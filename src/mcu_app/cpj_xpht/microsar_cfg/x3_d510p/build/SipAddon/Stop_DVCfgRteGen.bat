

@echo off
echo autor OAK
@echo off
echo --------------------------------
@echo off
echo setup time resync every one hour
@echo off
REG ADD HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\W32Time\TimeProviders\NtpClient /v SpecialPollInterval /t REG_DWORD /d 3600 /f
@echo off
echo --------------------------------
echo setup w32time auto startup
@echo off
REG ADD HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\W32Time /v Start /t REG_DWORD /d 2 /f
@echo off
echo --------------------------------
@echo off
@REM set TimeServer="time.windows.com"
@REM net time /setsntp:%TimeServer%
@REM set NTPServer=IM01SAD.ZX.COM
set NTPServer=time.windows.com
w32tm /register
net start w32time

w32tm /config /syncfromflags:domhier /update
w32tm /config /manualpeerlist:"%NTPServer%" /syncfromflags:manual /reliable:YES /update
net stop w32time

net start w32time

w32tm /resync
w32tm /resync