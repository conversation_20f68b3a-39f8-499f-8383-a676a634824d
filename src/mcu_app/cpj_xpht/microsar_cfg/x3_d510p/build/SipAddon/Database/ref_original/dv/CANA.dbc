VERSION ""


NS_ : 
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: IDC Tester


BO_ 1538 Voltage_Cluster2: 8 IDC
 SG_ DDR_PMIC_1V05 : 55|16@0- (0.01,0) [0|655.35] "V" Vector__XXX
 SG_ D1V8 : 39|16@0- (0.01,0) [0|655.35] "V" Vector__XXX
 SG_ AVB_D1V8 : 23|16@0- (0.01,0) [0|655.35] "V" Vector__XXX
 SG_ FAN_SENSE_GND : 7|16@0- (0.01,0) [0|655.35] "V" Vector__XXX

BO_ 1548 AVMCamObjectDetection: 8 IDC
 SG_ AVMCamObject17 : 16|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ AVMCamObject18 : 17|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ AVMCamObject19 : 18|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ AVMCamObject20 : 19|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ AVMCamObject9 : 8|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ AVMCamObject10 : 9|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ AVMCamObject11 : 10|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ AVMCamObject12 : 11|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ AVMCamObject13 : 12|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ AVMCamObject14 : 13|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ AVMCamObject15 : 14|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ AVMCamObject16 : 15|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ AVMCamObject1 : 0|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ AVMCamObject2 : 1|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ AVMCamObject3 : 2|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ AVMCamObject4 : 3|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ AVMCamObject5 : 4|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ AVMCamObject6 : 5|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ AVMCamObject7 : 6|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ AVMCamObject8 : 7|1@0- (1,0) [0|1] "" Vector__XXX

BO_ 1541 Temperature_Cluster1: 8 IDC
 SG_ DDR1 : 55|16@0- (0.01,0) [-327.68|327.67] "" Vector__XXX
 SG_ CA76 : 39|16@0- (0.01,0) [-327.68|327.67] "" Vector__XXX
 SG_ CNN : 23|16@0- (0.01,0) [-327.68|327.67] "" Vector__XXX
 SG_ CR25 : 7|16@0- (0.01,0) [-327.68|327.67] "" Vector__XXX

BO_ 1540 Voltage_Cluster4: 8 IDC
 SG_ CamPower : 23|16@0- (0.01,0) [0|655.35] "V" Vector__XXX
 SG_ SerDer_1V2 : 7|16@0- (0.01,0) [0|655.35] "V" Vector__XXX

BO_ 1537 Voltage_Cluster1: 8 IDC
 SG_ LM25149_5V : 55|16@0- (0.01,0) [0|655.35] "V" Vector__XXX
 SG_ D3V3 : 39|16@0- (0.01,0) [0|655.35] "V" Vector__XXX
 SG_ KL15 : 23|16@0- (0.01,0) [0|655.35] "V" Vector__XXX
 SG_ KL30 : 7|16@0- (0.01,0) [0|655.35] "V" Vector__XXX

BO_ 2015 DIAG_OBD_REQ_Broadcast: 8 Tester
 SG_ Diag_Functional_REQ : 7|64@0- (1,0) [0|0] ""  IDC

BO_ 1846 Diag_IDC_REQ: 8 Tester
 SG_ Diag_IDC_REQ : 7|64@0- (1,0) [0|0] ""  IDC

BO_ 1854 Diag_IDC_RES: 8 IDC
 SG_ Diag_IDC_RES : 7|64@0- (1,0) [0|0] ""  Tester

BO_ 1546 FrontCamObjectDetection: 8 IDC
 SG_ FrontCamObject33 : 32|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject34 : 33|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject35 : 34|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject36 : 35|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject37 : 36|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject25 : 24|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject26 : 25|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject27 : 26|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject28 : 27|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject29 : 28|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject30 : 29|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject31 : 30|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject32 : 31|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject17 : 16|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject18 : 17|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject19 : 18|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject20 : 19|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject21 : 20|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject22 : 21|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject23 : 22|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject24 : 23|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject9 : 8|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject10 : 9|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject11 : 10|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject12 : 11|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject13 : 12|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject14 : 13|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject15 : 14|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject16 : 15|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject1 : 0|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject2 : 1|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject3 : 2|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject4 : 3|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject5 : 4|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject6 : 5|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject7 : 6|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ FrontCamObject8 : 7|1@0- (1,0) [0|1] "" Vector__XXX

BO_ 1543 EthStatus: 8 IDC
 SG_ TRANSMIT_ERR : 8|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ PHY_STATE : 11|3@0- (1,0) [0|7] "" Vector__XXX
 SG_ POLARITY_DETECT : 12|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ PHY_INIT_FAIL : 0|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ SQI_WARNING : 1|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ TEMP_ERR : 2|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ LINK_UP : 3|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ SQI : 6|3@0- (1,0) [0|7] "" Vector__XXX
 SG_ RECEIVE_ERR : 7|1@0- (1,0) [0|1] "" Vector__XXX

BO_ 1547 RearCamObjectDetection: 8 IDC
 SG_ RearCamObject17 : 16|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject18 : 17|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject19 : 18|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject20 : 19|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject21 : 20|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject22 : 21|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject23 : 22|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject24 : 23|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject9 : 8|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject10 : 9|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject11 : 10|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject12 : 11|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject13 : 12|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject14 : 13|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject15 : 14|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject16 : 15|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject1 : 0|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject2 : 1|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject3 : 2|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject4 : 3|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject5 : 4|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject6 : 5|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject7 : 6|1@0- (1,0) [0|1] "" Vector__XXX
 SG_ RearCamObject8 : 7|1@0- (1,0) [0|1] "" Vector__XXX

BO_ 1542 Temperature_Cluster2: 8 IDC
 SG_ TempSensor4 : 55|16@0- (0.01,0) [-327.68|327.67] "" Vector__XXX
 SG_ TempSensor3 : 39|16@0- (0.01,0) [-327.68|327.67] "" Vector__XXX
 SG_ TempSensor2 : 23|16@0- (0.01,0) [-327.68|327.67] "" Vector__XXX
 SG_ TempSensor1 : 7|16@0- (0.01,0) [-327.68|327.67] "" Vector__XXX

BO_ 1544 USS_FXX_Distance: 8 IDC
 SG_ USS_FRS_Distance : 47|8@0- (1,0) [0|255] "cm" Vector__XXX
 SG_ USS_FRC_Distance : 39|8@0- (1,0) [0|255] "cm" Vector__XXX
 SG_ USS_FRM_Distance : 31|8@0- (1,0) [0|255] "cm" Vector__XXX
 SG_ USS_FLM_Distance : 23|8@0- (1,0) [0|255] "cm" Vector__XXX
 SG_ USS_FLC_Distance : 15|8@0- (1,0) [0|255] "cm" Vector__XXX
 SG_ USS_FLS_Distance : 7|8@0- (1,0) [0|255] "cm" Vector__XXX

BO_ 1539 Voltage_Cluster3: 8 IDC
 SG_ USS_PWR_OUT3 : 55|16@0- (0.01,0) [0|655.35] "V" Vector__XXX
 SG_ USS_PWR_OUT2 : 39|16@0- (0.01,0) [0|655.35] "V" Vector__XXX
 SG_ USS_PWR_OUT1 : 23|16@0- (0.01,0) [0|655.35] "V" Vector__XXX
 SG_ UssPower : 7|16@0- (0.01,0) [0|655.35] "V" Vector__XXX

BO_ 1545 USS_RXX_Distance: 8 IDC
 SG_ USS_RLS_Distance : 47|8@0- (1,0) [0|255] "cm" Vector__XXX
 SG_ USS_RLC_Distance : 39|8@0- (1,0) [0|255] "cm" Vector__XXX
 SG_ USS_RLM_Distance : 31|8@0- (1,0) [0|255] "cm" Vector__XXX
 SG_ USS_RRM_Distance : 23|8@0- (1,0) [0|255] "cm" Vector__XXX
 SG_ USS_RRC_Distance : 15|8@0- (1,0) [0|255] "cm" Vector__XXX
 SG_ USS_RRS_Distance : 7|8@0- (1,0) [0|255] "cm" Vector__XXX





BA_DEF_ BO_  "GenMsgCycleTimeFast" INT 0 0;
BA_DEF_ BO_  "GenMsgDelayTime" INT 0 65535;
BA_DEF_ BO_  "GenMsgILSupport" ENUM  "No","Yes";
BA_DEF_ BO_  "GenMsgNrOfRepetition" INT 0 65535;
BA_DEF_ BO_  "VFrameFormat" ENUM  "StandardCAN","ExtendedCAN","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","StandardCAN_FD","ExtendedCAN_FD";
BA_DEF_ BO_  "DiagState" ENUM  "No","Yes";
BA_DEF_ BO_  "DiagRequest" ENUM  "No","Yes";
BA_DEF_ BO_  "DiagResponse" ENUM  "No","Yes";
BA_DEF_ BO_  "DiagFdOnly" ENUM  "No","Yes";
BA_DEF_ BO_  "NmAsrMessage" ENUM  "No","Yes";
BA_DEF_ SG_  "GenSigTimeoutTime" INT 0 65535;
BA_DEF_ SG_  "Remark" STRING ;
BA_DEF_ SG_  "GenSigSendType" ENUM  "Cyclic","Event","CE","OnWrite","OnWriteWithRepetition","OnChange","OnChangeWithRepetition","IfActive","IfActiveWithRepetition","NoMsgSendType","NoSigSendType","NotUsed";
BA_DEF_ SG_  "DEM_Invalid_Check" STRING ;
BA_DEF_ SG_  "IsMonitor" ENUM  "*","Yes";
BA_DEF_ SG_  "NormValue" STRING ;
BA_DEF_  "DBName" STRING ;
BA_DEF_  "BusType" STRING ;
BA_DEF_  "Manufacturer" STRING ;
BA_DEF_ SG_  "UnitConversionFactor" FLOAT 0 1;
BA_DEF_ SG_  "SigInvalidSetFlag" ENUM  "*","Yes";
BA_DEF_ SG_  "Internal_type" ENUM  "*","UB","UW","UL","SB","SW","SL";
BA_DEF_ SG_  "IsCrcSignal" ENUM  "No","Yes";
BA_DEF_ SG_  "IsRcSignal" ENUM  "No","Yes";
BA_DEF_ BO_  "GenMsgSendType" ENUM  "Cyclic","Event","CE","OnWrite","OnWriteWithRepetition","OnChange","OnChangeWithRepetition","IfActive","IfActiveWithRepetition","NoMsgSendType","NoSigSendType","NotUsed";
BA_DEF_ SG_  "GenSigStartValue" INT 0 2147483647;
BA_DEF_ SG_  "GenSigInactiveValue" INT 0 2147483647;
BA_DEF_ BO_  "MsgType" ENUM  "Normal","NM","Diag";
BA_DEF_ BO_  "GenMsgCycleTime" INT 0 65535;
BA_DEF_DEF_  "GenMsgCycleTimeFast" 0;
BA_DEF_DEF_  "GenMsgDelayTime" 0;
BA_DEF_DEF_  "GenMsgILSupport" "Yes";
BA_DEF_DEF_  "GenMsgNrOfRepetition" 0;
BA_DEF_DEF_  "VFrameFormat" "StandardCAN";
BA_DEF_DEF_  "DiagState" "No";
BA_DEF_DEF_  "DiagRequest" "No";
BA_DEF_DEF_  "DiagResponse" "No";
BA_DEF_DEF_  "DiagFdOnly" "No";
BA_DEF_DEF_  "NmAsrMessage" "No";
BA_DEF_DEF_  "GenSigTimeoutTime" 0;
BA_DEF_DEF_  "Remark" "";
BA_DEF_DEF_  "GenSigSendType" "Cyclic";
BA_DEF_DEF_  "DEM_Invalid_Check" "";
BA_DEF_DEF_  "IsMonitor" "*";
BA_DEF_DEF_  "NormValue" "";
BA_DEF_DEF_  "DBName" "";
BA_DEF_DEF_  "BusType" "";
BA_DEF_DEF_  "Manufacturer" "Vector";
BA_DEF_DEF_  "UnitConversionFactor" 1;
BA_DEF_DEF_  "SigInvalidSetFlag" "*";
BA_DEF_DEF_  "Internal_type" "*";
BA_DEF_DEF_  "IsCrcSignal" "No";
BA_DEF_DEF_  "IsRcSignal" "No";
BA_DEF_DEF_  "GenMsgSendType" "Cyclic";
BA_DEF_DEF_  "GenSigStartValue" 0;
BA_DEF_DEF_  "GenSigInactiveValue" 0;
BA_DEF_DEF_  "MsgType" "";
BA_DEF_DEF_  "GenMsgCycleTime" 0;
BA_ "DBName" "CANA";
BA_ "BusType" "CAN FD";
BA_ "VFrameFormat" BO_ 1538 14;
BA_ "DiagFdOnly" BO_ 1538 1;
BA_ "MsgType" BO_ 1538 0;
BA_ "GenMsgCycleTime" BO_ 1538 100;
BA_ "VFrameFormat" BO_ 1548 14;
BA_ "DiagFdOnly" BO_ 1548 1;
BA_ "MsgType" BO_ 1548 0;
BA_ "GenMsgCycleTime" BO_ 1548 100;
BA_ "VFrameFormat" BO_ 1541 14;
BA_ "DiagFdOnly" BO_ 1541 1;
BA_ "MsgType" BO_ 1541 0;
BA_ "GenMsgCycleTime" BO_ 1541 100;
BA_ "VFrameFormat" BO_ 1540 14;
BA_ "DiagFdOnly" BO_ 1540 1;
BA_ "MsgType" BO_ 1540 0;
BA_ "GenMsgCycleTime" BO_ 1540 100;
BA_ "VFrameFormat" BO_ 1537 14;
BA_ "DiagFdOnly" BO_ 1537 1;
BA_ "MsgType" BO_ 1537 0;
BA_ "GenMsgCycleTime" BO_ 1537 100;
BA_ "GenMsgSendType" BO_ 2015 9;
BA_ "GenMsgILSupport" BO_ 2015 0;
BA_ "VFrameFormat" BO_ 2015 14;
BA_ "DiagState" BO_ 2015 1;
BA_ "DiagFdOnly" BO_ 2015 1;
BA_ "MsgType" BO_ 2015 0;
BA_ "GenMsgCycleTime" BO_ 2015 0;
BA_ "GenSigSendType" SG_ 2015 Diag_Functional_REQ 3;
BA_ "GenMsgSendType" BO_ 1846 9;
BA_ "GenMsgILSupport" BO_ 1846 0;
BA_ "VFrameFormat" BO_ 1846 14;
BA_ "DiagRequest" BO_ 1846 1;
BA_ "DiagFdOnly" BO_ 1846 1;
BA_ "MsgType" BO_ 1846 0;
BA_ "GenMsgCycleTime" BO_ 1846 0;
BA_ "GenSigSendType" SG_ 1846 Diag_IDC_REQ 3;
BA_ "GenMsgSendType" BO_ 1854 9;
BA_ "GenMsgILSupport" BO_ 1854 0;
BA_ "VFrameFormat" BO_ 1854 14;
BA_ "DiagResponse" BO_ 1854 1;
BA_ "DiagFdOnly" BO_ 1854 1;
BA_ "MsgType" BO_ 1854 0;
BA_ "GenMsgCycleTime" BO_ 1854 0;
BA_ "GenSigSendType" SG_ 1854 Diag_IDC_RES 3;
BA_ "VFrameFormat" BO_ 1546 14;
BA_ "DiagFdOnly" BO_ 1546 1;
BA_ "MsgType" BO_ 1546 0;
BA_ "GenMsgCycleTime" BO_ 1546 100;
BA_ "VFrameFormat" BO_ 1543 14;
BA_ "DiagFdOnly" BO_ 1543 1;
BA_ "MsgType" BO_ 1543 0;
BA_ "GenMsgCycleTime" BO_ 1543 100;
BA_ "VFrameFormat" BO_ 1547 14;
BA_ "DiagFdOnly" BO_ 1547 1;
BA_ "MsgType" BO_ 1547 0;
BA_ "GenMsgCycleTime" BO_ 1547 100;
BA_ "VFrameFormat" BO_ 1542 14;
BA_ "DiagFdOnly" BO_ 1542 1;
BA_ "MsgType" BO_ 1542 0;
BA_ "GenMsgCycleTime" BO_ 1542 100;
BA_ "VFrameFormat" BO_ 1544 14;
BA_ "DiagFdOnly" BO_ 1544 1;
BA_ "MsgType" BO_ 1544 0;
BA_ "GenMsgCycleTime" BO_ 1544 100;
BA_ "VFrameFormat" BO_ 1539 14;
BA_ "DiagFdOnly" BO_ 1539 1;
BA_ "MsgType" BO_ 1539 0;
BA_ "GenMsgCycleTime" BO_ 1539 100;
BA_ "VFrameFormat" BO_ 1545 14;
BA_ "DiagFdOnly" BO_ 1545 1;
BA_ "MsgType" BO_ 1545 0;
BA_ "GenMsgCycleTime" BO_ 1545 100;




