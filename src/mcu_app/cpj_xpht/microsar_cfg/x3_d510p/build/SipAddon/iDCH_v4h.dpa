<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ProjectAssistant Version="5.23.30 SP1">
    <General>
        <Name>iDCH_v4h</Name>
        <Version>1.1</Version>
        <Author><PERSON><PERSON></Author>
    </General>
    <Environment>
        <Platform>Canoeemu</Platform>
        <Derivative UUID="35C34EEE-6A21-4D8B-8B60-1CBFC45B32FC">Canoeemu</Derivative>
        <Compiler></Compiler>
        <SipIds>
            <SipId>CBD2100679_D00</SipId>
        </SipIds>
        <TargetType>Real Target</TargetType>
        <UseCases/>
        <PostBuildLoadableSupport>false</PostBuildLoadableSupport>
        <PostBuildSelectableSupport>false</PostBuildSelectableSupport>
        <ProjectType>
            <Type>Standard</Type>
        </ProjectType>
    </Environment>
    <Folders>
        <ECUC>.\Config\ECUC</ECUC>
        <GenData>.\..\..\src\GenData</GenData>
        <GenDataVtt>.\Appl\GenDataVtt</GenDataVtt>
        <Source>.\..\..\src\GenData</Source>
        <ServiceComponents>.\Config\ServiceComponents</ServiceComponents>
        <Logs>.\Log</Logs>
        <SIP>.\..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\CBD2100679_D00</SIP>
        <StartMenu></StartMenu>
        <ApplicationComponentFolders>
            <ApplicationComponentFolder>.\Config\ApplicationComponents</ApplicationComponentFolder>
        </ApplicationComponentFolders>
        <BswInternalBehaviour>.\Config\InternalBehavior</BswInternalBehaviour>
        <McData>.\Config\McData</McData>
        <DefinitionRestriction>.\DefRestrict</DefinitionRestriction>
        <AUTOSAR>.\Config\AUTOSAR</AUTOSAR>
    </Folders>
    <Tools/>
    <Input>
        <Options>
            <IgnoreUuidsSystemDescriptionFiles>false</IgnoreUuidsSystemDescriptionFiles>
            <IgnoreUuidsStandardConfigurationFiles>false</IgnoreUuidsStandardConfigurationFiles>
            <GenerateUpdateReport>true</GenerateUpdateReport>
            <GenerateXmlUpdateReport>false</GenerateXmlUpdateReport>
        </Options>
    </Input>
    <References/>
    <EcucSplitter>
        <Configuration>.\Config\ECUC\iDCH_v4h.ecuc.arxml</Configuration>
        <Splitter File=".\Config\ECUC\iDCH_v4h_Can_Can_ecuc.arxml">
            <Module Name="Can"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\iDCH_v4h_Port_Port_ecuc.arxml">
            <Module Name="Port"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\iDCH_v4h_Dem_Dem_ecuc.arxml">
            <Module Name="Dem"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\iDCH_v4h_Cdd_CRC_cdd_ecuc.arxml">
            <Module Name="CRC_cdd"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\iDCH_v4h_Spi_Spi_ecuc.arxml">
            <Module Name="Spi"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\iDCH_v4h_Dio_Dio_ecuc.arxml">
            <Module Name="Dio"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\iDCH_v4h_Mcu_Mcu_ecuc.arxml">
            <Module Name="Mcu"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\iDCH_v4h_Gpt_Gpt_ecuc.arxml">
            <Module Name="Gpt"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\iDCH_v4h_Cdd_Cddiic_ecuc.arxml">
            <Module Name="Cddiic"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\iDCH_v4h_Cdd_Cddths_ecuc.arxml">
            <Module Name="Cddths"/>
        </Splitter>
        <OwnFolderForEachSplitter>false</OwnFolderForEachSplitter>
        <OwnFileForEachInstance>true</OwnFileForEachInstance>
    </EcucSplitter>
    <ECUC>
        <Active RootPackageName="/ActiveEcuC/ActiveEcuC">.\Config\ECUC\iDCH_v4h.ecuc.arxml</Active>
    </ECUC>
    <PostBuildLoadable RTEDataFreezeChecksum="" CurrentConfigurationPhase="PRE_COMPILE"/>
    <ToolSettings>
        <Generators>
            <Settings Name="com.vector.cfg.gui.core.generators.ExtGenStepOrder">
                <Setting Value="INTERNAL_GENERATION|Dio_Generate|Port_Generate" Name="Order"/>
            </Settings>
            <Settings Name="com.vector.cfg.gui.core.generators.ExtGenSteps">
                <Settings Name="Can_Generate">
                    <Setting Value="true" Name="Active"/>
                    <Setting Value="..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\common\generic\generator\MCALConfGen.exe" Name="CommandLine"/>
                    <Setting Value="UTF-8" Name="CommandLineEncoding"/>
                    <Setting Value="" Name="Comment"/>
                    <Setting Value="Serial" Name="ExecutionType"/>
                    <Setting Value="-m can -o  ..\..\src\GenData Config\ECUC\iDCH_v4h_Can_Can_ecuc.arxml  ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\modules\can\generator\V4H\R1911_CAN_V4H_BSWMDT.arxml  ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\V4H\common_family\generator\arm\Sample_Application_V4H.trxml ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\common\generic\stubs\19_11\Dem\xml\Dem_Can.arxml ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\common\generic\stubs\19_11\Os\xml\Os_Can.arxml ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\common\generic\stubs\19_11\EcuM\xml\EcuM_Can.arxml ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\V4H\common_family\config\V4H\19_11\MCU_CAN_V4H.arxml" Name="GenerationParameters"/>
                    <Setting Value="4.0.3" Name="RequiredAsVersion"/>
                    <Setting Value="true" Name="SpecificAsVersionRequired"/>
                    <Setting Value="false" Name="SupportsStandAloneValidation"/>
                    <Setting Value="" Name="TargetType"/>
                    <Setting Value="" Name="TransformationOutput"/>
                    <Setting Value="false" Name="TransformationRequired"/>
                    <Setting Value="" Name="TransformationXsltFile"/>
                    <Setting Value="" Name="ValidationParameters"/>
                    <Setting Value="$(DpaProjectFolder)" Name="WorkingDir"/>
                </Settings>
                <Settings Name="Cddiic_Generate">
                    <Setting Value="true" Name="Active"/>
                    <Setting Value="..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\common\generic\generator\MCALConfGen.exe" Name="CommandLine"/>
                    <Setting Value="UTF-8" Name="CommandLineEncoding"/>
                    <Setting Value="" Name="Comment"/>
                    <Setting Value="Parallel" Name="ExecutionType"/>
                    <Setting Value="-m cddiic -o  ..\..\src\GenData  Config\ECUC\iDCH_v4h_Cdd_Cddiic_ecuc.arxml  ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\V4H\common_family\generator\arm\Sample_Application_V4H.trxml ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\modules\cddiic\generator\V4H\R1911_CDD_IIC_V4H_BSWMDT.arxml  ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\common\generic\stubs\19_11\Dem\xml\Dem_CDD_Iic.arxml" Name="GenerationParameters"/>
                    <Setting Value="" Name="RequiredAsVersion"/>
                    <Setting Value="false" Name="SpecificAsVersionRequired"/>
                    <Setting Value="false" Name="SupportsStandAloneValidation"/>
                    <Setting Value="" Name="TargetType"/>
                    <Setting Value="" Name="TransformationOutput"/>
                    <Setting Value="false" Name="TransformationRequired"/>
                    <Setting Value="" Name="TransformationXsltFile"/>
                    <Setting Value="" Name="ValidationParameters"/>
                    <Setting Value="$(DpaProjectFolder)" Name="WorkingDir"/>
                </Settings>
                <Settings Name="Cddths_Generate">
                    <Setting Value="true" Name="Active"/>
                    <Setting Value="..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\common\generic\generator\MCALConfGen.exe" Name="CommandLine"/>
                    <Setting Value="UTF-8" Name="CommandLineEncoding"/>
                    <Setting Value="" Name="Comment"/>
                    <Setting Value="Parallel" Name="ExecutionType"/>
                    <Setting Value="-m cddths -o  ..\..\src\GenData  Config\ECUC\iDCH_v4h_Cdd_Cddths_ecuc.arxml  ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\V4H\common_family\generator\arm\Sample_Application_V4H.trxml ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\modules\cddths\generator\V4H\R1911_CDD_THS_V4H_BSWMDT.arxml  ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\common\generic\stubs\19_11\Dem\xml\Dem_CDD_Ths.arxml ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\common\generic\stubs\19_11\Os\xml\Os_CDD_ths.arxml" Name="GenerationParameters"/>
                    <Setting Value="" Name="RequiredAsVersion"/>
                    <Setting Value="false" Name="SpecificAsVersionRequired"/>
                    <Setting Value="false" Name="SupportsStandAloneValidation"/>
                    <Setting Value="" Name="TargetType"/>
                    <Setting Value="" Name="TransformationOutput"/>
                    <Setting Value="false" Name="TransformationRequired"/>
                    <Setting Value="" Name="TransformationXsltFile"/>
                    <Setting Value="" Name="ValidationParameters"/>
                    <Setting Value="$(DpaProjectFolder)" Name="WorkingDir"/>
                </Settings>
                <Settings Name="Dio_Generate">
                    <Setting Value="true" Name="Active"/>
                    <Setting Value="..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\common\generic\generator\MCALConfGen.exe" Name="CommandLine"/>
                    <Setting Value="UTF-8" Name="CommandLineEncoding"/>
                    <Setting Value="" Name="Comment"/>
                    <Setting Value="Parallel" Name="ExecutionType"/>
                    <Setting Value="-m dio -o  ..\..\src\GenData  Config\ECUC\iDCH_v4h_Dio_Dio_ecuc.arxml ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\V4H\common_family\generator\arm\Sample_Application_V4H.trxml  ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\modules\dio\generator\V4H\R1911_DIO_V4H_BSWMDT.arxml  ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\common\generic\stubs\19_11\Dem\xml\Dem_dio.arxml" Name="GenerationParameters"/>
                    <Setting Value="" Name="RequiredAsVersion"/>
                    <Setting Value="false" Name="SpecificAsVersionRequired"/>
                    <Setting Value="false" Name="SupportsStandAloneValidation"/>
                    <Setting Value="" Name="TargetType"/>
                    <Setting Value="" Name="TransformationOutput"/>
                    <Setting Value="false" Name="TransformationRequired"/>
                    <Setting Value="" Name="TransformationXsltFile"/>
                    <Setting Value="" Name="ValidationParameters"/>
                    <Setting Value="$(DpaProjectFolder)" Name="WorkingDir"/>
                </Settings>
                <Settings Name="Mcu_Generate">
                    <Setting Value="true" Name="Active"/>
                    <Setting Value="..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\common\generic\generator\MCALConfGen.exe" Name="CommandLine"/>
                    <Setting Value="UTF-8" Name="CommandLineEncoding"/>
                    <Setting Value="" Name="Comment"/>
                    <Setting Value="Parallel" Name="ExecutionType"/>
                    <Setting Value="-m mcu -o  ..\..\src\GenData  Config\ECUC\iDCH_v4h_Mcu_Mcu_ecuc.arxml ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\V4H\common_family\generator\arm\Sample_Application_V4H.trxml ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\common\generic\stubs\19_11\Dem\xml\Dem_mcu.arxml ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\modules\mcu\generator\V4H\R1911_MCU_V4H_BSWMDT.arxml" Name="GenerationParameters"/>
                    <Setting Value="" Name="RequiredAsVersion"/>
                    <Setting Value="false" Name="SpecificAsVersionRequired"/>
                    <Setting Value="false" Name="SupportsStandAloneValidation"/>
                    <Setting Value="" Name="TargetType"/>
                    <Setting Value="" Name="TransformationOutput"/>
                    <Setting Value="false" Name="TransformationRequired"/>
                    <Setting Value="" Name="TransformationXsltFile"/>
                    <Setting Value="" Name="ValidationParameters"/>
                    <Setting Value="$(DpaProjectFolder)" Name="WorkingDir"/>
                </Settings>
                <Settings Name="Port_Generate">
                    <Setting Value="true" Name="Active"/>
                    <Setting Value="..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\common\generic\generator\MCALConfGen.exe" Name="CommandLine"/>
                    <Setting Value="UTF-8" Name="CommandLineEncoding"/>
                    <Setting Value="" Name="Comment"/>
                    <Setting Value="Parallel" Name="ExecutionType"/>
                    <Setting Value="-m port -o  ..\..\src\GenData Config\ECUC\iDCH_v4h_Port_Port_ecuc.arxml ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\V4H\common_family\generator\arm\Sample_Application_V4H.trxml ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\modules\port\generator\V4H\R1911_PORT_V4H_BSWMDT.arxml ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\common\generic\stubs\19_11\Dem\xml\Dem_port.arxml" Name="GenerationParameters"/>
                    <Setting Value="" Name="RequiredAsVersion"/>
                    <Setting Value="false" Name="SpecificAsVersionRequired"/>
                    <Setting Value="false" Name="SupportsStandAloneValidation"/>
                    <Setting Value="" Name="TargetType"/>
                    <Setting Value="" Name="TransformationOutput"/>
                    <Setting Value="false" Name="TransformationRequired"/>
                    <Setting Value="" Name="TransformationXsltFile"/>
                    <Setting Value="" Name="ValidationParameters"/>
                    <Setting Value="$(DpaProjectFolder)" Name="WorkingDir"/>
                </Settings>
                <Settings Name="Spi_Generate">
                    <Setting Value="true" Name="Active"/>
                    <Setting Value="..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\common\generic\generator\MCALConfGen.exe" Name="CommandLine"/>
                    <Setting Value="UTF-8" Name="CommandLineEncoding"/>
                    <Setting Value="" Name="Comment"/>
                    <Setting Value="Parallel" Name="ExecutionType"/>
                    <Setting Value="-m spi -o  ..\..\src\GenData   Config\ECUC\iDCH_v4h_Spi_Spi_ecuc.arxml  ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\V4H\common_family\generator\arm\Sample_Application_V4H.trxml  ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\modules\spi\generator\V4H\R1911_SPI_V4H_BSWMDT.arxml  ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\V4H\common_family\config\V4H\19_11\MCU_SPI_V4H.arxml  ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\common\generic\stubs\19_11\Dem\xml\Dem_spi.arxml  ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\common\generic\stubs\19_11\Os\xml\Os_SPI.arxml" Name="GenerationParameters"/>
                    <Setting Value="" Name="RequiredAsVersion"/>
                    <Setting Value="false" Name="SpecificAsVersionRequired"/>
                    <Setting Value="false" Name="SupportsStandAloneValidation"/>
                    <Setting Value="" Name="TargetType"/>
                    <Setting Value="" Name="TransformationOutput"/>
                    <Setting Value="false" Name="TransformationRequired"/>
                    <Setting Value="" Name="TransformationXsltFile"/>
                    <Setting Value="" Name="ValidationParameters"/>
                    <Setting Value="$(DpaProjectFolder)" Name="WorkingDir"/>
                </Settings>
                <Settings Name="gpt_Generate">
                    <Setting Value="true" Name="Active"/>
                    <Setting Value="..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\common\generic\generator\MCALConfGen.exe" Name="CommandLine"/>
                    <Setting Value="UTF-8" Name="CommandLineEncoding"/>
                    <Setting Value="" Name="Comment"/>
                    <Setting Value="Parallel" Name="ExecutionType"/>
                    <Setting Value="-m gpt -o  ..\..\src\GenData   Config\ECUC\iDCH_v4h_Gpt_Gpt_ecuc.arxml ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\modules\gpt\sample_application\V4H\19_11\config\App_GPT_V4H_Sample.arxml  ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\common\generic\stubs\19_11\EcuM\xml\EcuM_GPT.arxml  ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\V4H\common_family\generator\arm\Sample_Application_V4H.trxml        ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\modules\gpt\generator\V4H\R1911_GPT_V4H_BSWMDT.arxml   ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\V4H\common_family\config\V4H\19_11\MCU_GPT_V4H.arxml  ..\..\..\..\..\components\microsar\src\_AR_ThirdParty\ThirdParty\RTM8RC779GCMCL5QA0JCDRE\rel\common\generic\stubs\19_11\Dem\xml\Dem_gpt.arxml" Name="GenerationParameters"/>
                    <Setting Value="" Name="RequiredAsVersion"/>
                    <Setting Value="false" Name="SpecificAsVersionRequired"/>
                    <Setting Value="false" Name="SupportsStandAloneValidation"/>
                    <Setting Value="" Name="TargetType"/>
                    <Setting Value="" Name="TransformationOutput"/>
                    <Setting Value="false" Name="TransformationRequired"/>
                    <Setting Value="" Name="TransformationXsltFile"/>
                    <Setting Value="" Name="ValidationParameters"/>
                    <Setting Value="$(DpaProjectFolder)" Name="WorkingDir"/>
                </Settings>
            </Settings>
            <Settings Name="com.vector.cfg.gui.core.generators.GeneratedModules"/>
            <Settings Name="com.vector.cfg.gui.core.generators.GenerationTarget">
                <Setting Value="Real Target" Name="TargetType"/>
            </Settings>
            <Settings Name="com.vector.cfg.gui.core.generators.GeneratorsWithoutModuleDef">
                <Setting Value="false" Name="/MICROSAR/McDataConv"/>
            </Settings>
        </Generators>
        <Misc>
            <Settings Name="com.vector.cfg.gen.core.bswmdmigration.internal.service.BswImplVersionPersistor">
                <Settings Name="|Renesas|EcucDefs_Can|Can">
                    <Setting Value="1.1.12" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|Renesas|EcucDefs_CddCrc|Cdd">
                    <Setting Value="1.0.8" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|Renesas|EcucDefs_CddEmm|Cdd">
                    <Setting Value="1.0.9" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|Renesas|EcucDefs_CddIccom|Cdd">
                    <Setting Value="1.1.8" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|Renesas|EcucDefs_CddIic|Cdd">
                    <Setting Value="1.0.11" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|Renesas|EcucDefs_CddIpmmu|Cdd">
                    <Setting Value="1.0.8" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|Renesas|EcucDefs_CddRfso|Cdd">
                    <Setting Value="1.0.7" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|Renesas|EcucDefs_CddThs|Cdd">
                    <Setting Value="1.0.8" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|Renesas|EcucDefs_Dio|Dio">
                    <Setting Value="1.3.8" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|Renesas|EcucDefs_Eth|Eth">
                    <Setting Value="1.4.9" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|Renesas|EcucDefs_Fls|Fls">
                    <Setting Value="1.1.9" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|Renesas|EcucDefs_Gpt|Gpt">
                    <Setting Value="1.7.10" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|Renesas|EcucDefs_Mcu|Mcu">
                    <Setting Value="1.1.12" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|Renesas|EcucDefs_Port|Port">
                    <Setting Value="1.1.11" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|Renesas|EcucDefs_Spi|Spi">
                    <Setting Value="1.5.10" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|Renesas|EcucDefs_Wdg|Wdg">
                    <Setting Value="1.4.5" Name="BswImplSwVersion"/>
                </Settings>
            </Settings>
            <Settings Name="com.vector.cfg.gen.core.gencore.internal.GeneratorConfigurationService">
                <Setting Value="Yes" Name="SaveAfterGeneration"/>
            </Settings>
            <Settings Name="com.vector.cfg.gui.pse.pages.generators">
                <Settings Name="BuildVipProject"/>
                <Settings Name="CustomWorkflowExecution"/>
            </Settings>
            <Settings Name="com.vector.cfg.interop.tats"/>
            <Settings Name="com.vector.cfg.persistency.internal.folder">
                <Settings Name="TargetFolderManager"/>
            </Settings>
            <Settings Name="generationReportSettings"/>
        </Misc>
    </ToolSettings>
    <TopDownServiceConfiguration>
        <NvM>true</NvM>
    </TopDownServiceConfiguration>
    <Miscellaneous>
        <AmdGenerateDebugData>false</AmdGenerateDebugData>
        <AutomaticSyncSystemDescription>false</AutomaticSyncSystemDescription>
    </Miscellaneous>
    <SwctGeneration/>
</ProjectAssistant>
