import json
import os.path
from lxml import etree

current_path = os.path.dirname(os.path.abspath(__file__))
json_path = os.path.join(current_path,"vetor_can_objects.json")
json_demo_path = os.path.join(current_path,"j6e_obj_demo.json")
xdm_file = os.path.join(current_path, "Template.xdm")
out_file_path = r'../../../../../../components/microsar/src/_AR_ThirdParty/ThirdParty/hobot_j6e_v0.8.0_mcu/Config/McalCdd/project_matrix_sip_A/j6m_matrix_sip_A/config/Can.xdm'
modified_file = os.path.join(current_path, out_file_path)

nsmap = {
    None: "http://www.tresos.de/_projects/DataModel2/16/root.xsd",
    "a": "http://www.tresos.de/_projects/DataModel2/18/attribute.xsd",
    "v": "http://www.tresos.de/_projects/DataModel2/06/schema.xsd",
    "d": "http://www.tresos.de/_projects/DataModel2/06/data.xsd"
}

class vector_objects_to_mcal:
    def __init__(self,json_path):
        with open(json_path, "r") as file:
            self.vector_objects = json.load(file)
    def format_hw_obj(self):
        self.hw_obj_list = list()
        for channel_name, channel_info in self.vector_objects.items():
            if len(channel_info['tx']):
                for hw_obj in channel_info['tx']:
                    hw_obj_info = dict()
                    hw_obj_info['name'] = hw_obj['name']
                    hw_obj_info['CanObjectId'] = hw_obj['CanObjectId']
                    hw_obj_info['CanObjectType'] = 'TRANSMIT'
                    hw_obj_info['CanIdValue'] = hw_obj['CanIdValue']
                    hw_obj_info['channel_name'] = channel_name
                    self.hw_obj_list.append(hw_obj_info)
            if len(channel_info['rx']):
                hw_rx_obj_info = dict()
                for hw_obj in channel_info['rx']:
                    hw_rx_obj_info['name'] = hw_obj['name']
                    hw_rx_obj_info['CanObjectId'] = hw_obj['CanObjectId']
                    hw_rx_obj_info['CanObjectType'] = 'RECEIVE'
                filters = list()
                for rx_info in channel_info['rx_filters']:
                    filter = dict()
                    filter['name'] = rx_info['name'] + '_fliter'
                    filter['type'] = 'IDENTIFIABLE'
                    filter['CanHwFilterCode'] = {
                        'value': rx_info['CanIfRxPduCanId'],
                        'type': 'INTEGER'
                    }
                    filter['CanHwFilterMask'] = {
                        'value': '2047',
                        'type': 'INTEGER'
                    }
                    filters.append(filter)
                hw_rx_obj_info['filters'] = filters
                hw_rx_obj_info['channel_name'] = channel_name
                self.hw_obj_list.append(hw_rx_obj_info)

        return self.hw_obj_list

def vector_cfg_to_j6e_cfg(vector_cfg):
    with open(json_demo_path, 'r', encoding='utf-8') as file:
        j6e_obj_demo = json.load(file)

    j6e_obj_demo['channel_name'] = vector_cfg['channel_name']
    j6e_obj_demo['name'] = vector_cfg['name']
    j6e_obj_demo['CanObjectId']['value'] = vector_cfg['CanObjectId']
    j6e_obj_demo['CanObjectType']['value'] = vector_cfg['CanObjectType']
    if vector_cfg['CanObjectType'] == 'RECEIVE':
        j6e_obj_demo['CanHandleType']['value'] = 'BASIC'
        out_filter_list = list()
        for rx_filter in vector_cfg['filters']:
            filter_info = dict()
            filter_info['name'] = rx_filter['name']
            filter_info['CanFilterCode'] = rx_filter['CanHwFilterCode']
            filter_info['CanFilterMask'] = rx_filter['CanHwFilterMask']
            out_filter_list.append(filter_info)
        j6e_obj_demo['filter_list'] = out_filter_list

    return j6e_obj_demo

def add_hardware_element(data):
    hardware_tag = "{http://www.tresos.de/_projects/DataModel2/06/data.xsd}lst"

    hardware_object = etree.Element(hardware_tag,name="CanHardwareObject",type="MAP")

    etree.SubElement(hardware_object,'{http://www.tresos.de/_projects/DataModel2/06/data.xsd}ctr', name=data['name'], type=data['type'])

    return hardware_object


def add_var_element(parent, var_name, var_info):
    if var_name == 'CanControllerRef' or var_name == 'CanMainFunctionRWPeriodRef':
        var_elem_tag = '{http://www.tresos.de/_projects/DataModel2/06/data.xsd}ref'
    else:
        var_elem_tag = '{http://www.tresos.de/_projects/DataModel2/06/data.xsd}var'

    var_elem = etree.SubElement(parent, var_elem_tag,
                             {'name': var_name, 'type': var_info['type'], 'value': var_info['value']})
    # 添加 a:a 子元素
    if 'a' in var_info:
        tag = '{http://www.tresos.de/_projects/DataModel2/18/attribute.xsd}a'
        if isinstance(var_info['a'], list):  # 需要检查是否为列表
            for a in var_info['a']:
                etree.SubElement(var_elem, tag, {'name': a['name'], 'value': a['value']})
        else:
            a = var_info['a']
            etree.SubElement(var_elem, tag, {'name': a['name'], 'value': a['value']})

    return var_elem

def generate_xdm_config(data):

    hardware_elem = etree.Element('{http://www.tresos.de/_projects/DataModel2/06/data.xsd}ctr', name=data['name'],
                 type=data['type'])

    filter_elem = etree.Element('{http://www.tresos.de/_projects/DataModel2/06/data.xsd}lst', name='CanHwFilter', type='MAP')

    for key,value in data.items():
        if isinstance(value, dict):
            add_var_element(hardware_elem, key, value)
        hardware_elem.append(filter_elem)
        if key == 'filter_list':
            for filter_item in value:
                ctr_elem = etree.SubElement(filter_elem, '{http://www.tresos.de/_projects/DataModel2/06/data.xsd}ctr', {'name': filter_item['name'], 'type': 'IDENTIFIABLE'})
                etree.SubElement(ctr_elem, '{http://www.tresos.de/_projects/DataModel2/06/data.xsd}var', {'name': 'CanHwFilterCode', 'type': 'INTEGER','value': filter_item['CanFilterCode']['value']})
                etree.SubElement(ctr_elem, '{http://www.tresos.de/_projects/DataModel2/06/data.xsd}var', {'name': 'CanHwFilterMask', 'type': 'INTEGER', 'value': filter_item['CanFilterMask']['value']})
        else:
            for item in value:
                if isinstance(item,dict):
                    add_var_element(hardware_elem, item['name'], item)

    return hardware_elem


if __name__ == '__main__':

    hardware_tag = "{http://www.tresos.de/_projects/DataModel2/06/data.xsd}lst"

    hardware_object = etree.Element(hardware_tag, name="CanHardwareObject", type="MAP")

    mcal_info = vector_objects_to_mcal(json_path)
    tree = etree.parse(xdm_file)
    root = tree.getroot()

    for hw_obj in mcal_info.format_hw_obj():
        if len(hw_obj):
            j6e_cfg = vector_cfg_to_j6e_cfg(hw_obj)
            hardware_object.append(generate_xdm_config(j6e_cfg))

    for elem in tree.iter('{http://www.tresos.de/_projects/DataModel2/06/data.xsd}ctr'):
        if 'name' in elem.attrib and elem.attrib['name'] == 'CanConfigSet':
            elem.append(hardware_object)

    etree.indent(hardware_object, space="               ")
    tree.write(modified_file, encoding="utf-8",xml_declaration=False,pretty_print=True, method="xml")


