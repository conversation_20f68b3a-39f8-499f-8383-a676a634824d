<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:ce="http://www.tresos.de/_projects/DataModel2/18/childenable.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Can" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Can" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/Hobot_Can/Can"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="true"/>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuild"/>
              <d:ctr name="CanConfigSet" type="IDENTIFIABLE">
                <d:lst name="CanController" type="MAP">
                  <d:ctr name="CanController_0" type="IDENTIFIABLE">
                    <d:var name="CanHwChannel" type="ENUMERATION" 
                           value="FLEXCAN_0"/>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="592969728">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerId" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false"/>
                    <d:var name="CanAutoBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="CanControllerPrExcEn" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanControllerEdgeFilter" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanControllerFdISO" type="BOOLEAN" value="true"/>
                    <d:var name="CanClockFromBus" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuRfclkSettingConf_6"/>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can/Can/CanConfigSet/CanController_0/CanControllerBaudrateConfig_1"/>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection_0/EcucPartition_0">
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:var name="CanErrorNotification" type="FUNCTION-NAME" 
                           value="Can_ErrorNotif">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="CanFDErrorNotification" type="FUNCTION-NAME" 
                           value="CanFd_ErrorNotif"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="ManuallyMbIdxCfg" type="BOOLEAN" value="true"/>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <d:ctr name="CanControllerBaudrateConfig_0" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="1000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="1">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_1" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="9">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="2">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_2" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="2"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="2"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="1000.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="9">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="2">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_3" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="3"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="2"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="1000.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="5000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="6">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="1">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:chc name="CanRamBlock" type="IDENTIFIABLE" 
                           value="CanRamBlockSpecified">
                      <a:a name="ENABLE" value="true"/>
                      <d:ctr name="CanRamBlockSpecified" type="IDENTIFIABLE">
                        <d:var name="CanBlock0PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock1PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock2PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock3PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                      </d:ctr>
                    </d:chc>
                    <d:ctr name="CanRxFiFo" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:ctr name="CanEnhanceFiFo" type="IDENTIFIABLE">
                        <d:var name="CanEnhancedSchemeType" type="ENUMERATION" 
                               value="RANGE_FILTER_SCHEME"/>
                        <d:var name="CanFiFoOverflowNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CanController_1" type="IDENTIFIABLE">
                    <d:var name="CanHwChannel" type="ENUMERATION" 
                           value="FLEXCAN_1"/>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="592969728">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerId" type="INTEGER" value="1"/>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false"/>
                    <d:var name="CanAutoBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="CanControllerPrExcEn" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanControllerEdgeFilter" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerFdISO" type="BOOLEAN" value="true"/>
                    <d:var name="CanClockFromBus" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuRfclkSettingConf_6"/>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can/Can/CanConfigSet/CanController_1/CanControllerBaudrateConfig_1"/>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection_0/EcucPartition_0">
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:var name="CanErrorNotification" type="FUNCTION-NAME" 
                           value="Can_ErrorNotif">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="CanFDErrorNotification" type="FUNCTION-NAME" 
                           value="CanFd_ErrorNotif"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="ManuallyMbIdxCfg" type="BOOLEAN" value="true"/>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <d:ctr name="CanControllerBaudrateConfig_0" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="1000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="1">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_1" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="2">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_2" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="2"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="2"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="1000.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="2">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_3" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="3"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="2"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="1000.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="5000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="6">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="1">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:chc name="CanRamBlock" type="IDENTIFIABLE" 
                           value="CanRamBlockSpecified">
                      <a:a name="ENABLE" value="true"/>
                      <d:ctr name="CanRamBlockSpecified" type="IDENTIFIABLE">
                        <d:var name="CanBlock0PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock1PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock2PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock3PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                      </d:ctr>
                    </d:chc>
                    <d:ctr name="CanRxFiFo" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:ctr name="CanEnhanceFiFo" type="IDENTIFIABLE">
                        <d:var name="CanEnhancedSchemeType" type="ENUMERATION" 
                               value="RANGE_FILTER_SCHEME"/>
                        <d:var name="CanFiFoOverflowNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CanController_2" type="IDENTIFIABLE">
                    <d:var name="CanHwChannel" type="ENUMERATION" 
                           value="FLEXCAN_2"/>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="592969728">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerId" type="INTEGER" value="2"/>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false"/>
                    <d:var name="CanAutoBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="CanControllerPrExcEn" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanControllerEdgeFilter" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerFdISO" type="BOOLEAN" value="true"/>
                    <d:var name="CanClockFromBus" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuRfclkSettingConf_6"/>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can/Can/CanConfigSet/CanController_2/CanControllerBaudrateConfig_1"/>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection_0/EcucPartition_0">
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:var name="CanErrorNotification" type="FUNCTION-NAME" 
                           value="Can_ErrorNotif">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="CanFDErrorNotification" type="FUNCTION-NAME" 
                           value="CanFd_ErrorNotif"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="ManuallyMbIdxCfg" type="BOOLEAN" value="true"/>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <d:ctr name="CanControllerBaudrateConfig_0" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="1000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="1">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_1" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="2">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_2" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="2"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="2"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="1000.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="2">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_3" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="3"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="2"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="1000.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="5000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="6">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="1">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:chc name="CanRamBlock" type="IDENTIFIABLE" 
                           value="CanRamBlockSpecified">
                      <a:a name="ENABLE" value="true"/>
                      <d:ctr name="CanRamBlockSpecified" type="IDENTIFIABLE">
                        <d:var name="CanBlock0PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock1PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock2PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock3PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                      </d:ctr>
                    </d:chc>
                    <d:ctr name="CanRxFiFo" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:ctr name="CanEnhanceFiFo" type="IDENTIFIABLE">
                        <d:var name="CanEnhancedSchemeType" type="ENUMERATION" 
                               value="RANGE_FILTER_SCHEME"/>
                        <d:var name="CanFiFoOverflowNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CanController_3" type="IDENTIFIABLE">
                    <d:var name="CanHwChannel" type="ENUMERATION" 
                           value="FLEXCAN_3"/>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="592969728">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerId" type="INTEGER" value="3"/>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false"/>
                    <d:var name="CanAutoBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="CanControllerPrExcEn" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanControllerEdgeFilter" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerFdISO" type="BOOLEAN" value="true"/>
                    <d:var name="CanClockFromBus" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuRfclkSettingConf_6"/>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can/Can/CanConfigSet/CanController_3/CanControllerBaudrateConfig_1"/>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection_0/EcucPartition_0">
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:var name="CanErrorNotification" type="FUNCTION-NAME" 
                           value="Can_ErrorNotif">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="CanFDErrorNotification" type="FUNCTION-NAME" 
                           value="CanFd_ErrorNotif"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="ManuallyMbIdxCfg" type="BOOLEAN" value="true"/>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <d:ctr name="CanControllerBaudrateConfig_0" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="1000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="1">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_1" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="2">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_2" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="2"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="2"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="1000.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="2">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_3" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="3"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="2"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="1000.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="5000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="6">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="1">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:chc name="CanRamBlock" type="IDENTIFIABLE" 
                           value="CanRamBlockSpecified">
                      <a:a name="ENABLE" value="true"/>
                      <d:ctr name="CanRamBlockSpecified" type="IDENTIFIABLE">
                        <d:var name="CanBlock0PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock1PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock2PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock3PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                      </d:ctr>
                    </d:chc>
                    <d:ctr name="CanRxFiFo" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:ctr name="CanEnhanceFiFo" type="IDENTIFIABLE">
                        <d:var name="CanEnhancedSchemeType" type="ENUMERATION" 
                               value="RANGE_FILTER_SCHEME"/>
                        <d:var name="CanFiFoOverflowNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CanController_4" type="IDENTIFIABLE">
                    <d:var name="CanHwChannel" type="ENUMERATION" 
                           value="FLEXCAN_4"/>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="592969728">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerId" type="INTEGER" value="4"/>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false"/>
                    <d:var name="CanAutoBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="CanControllerPrExcEn" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanControllerEdgeFilter" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerFdISO" type="BOOLEAN" value="true"/>
                    <d:var name="CanClockFromBus" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuRfclkSettingConf_6"/>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can/Can/CanConfigSet/CanController_4/CanControllerBaudrateConfig_1"/>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection_0/EcucPartition_0">
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:var name="CanErrorNotification" type="FUNCTION-NAME" 
                           value="Can_ErrorNotif">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="CanFDErrorNotification" type="FUNCTION-NAME" 
                           value="CanFd_ErrorNotif"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="ManuallyMbIdxCfg" type="BOOLEAN" value="true"/>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <d:ctr name="CanControllerBaudrateConfig_0" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="1000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="1">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_1" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="2">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_2" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="2"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="2"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="1000.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="2">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_3" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="3"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="2"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="1000.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="5000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="6">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="1">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:chc name="CanRamBlock" type="IDENTIFIABLE" 
                           value="CanRamBlockSpecified">
                      <a:a name="ENABLE" value="true"/>
                      <d:ctr name="CanRamBlockSpecified" type="IDENTIFIABLE">
                        <d:var name="CanBlock0PayloadLength" type="ENUMERATION" 
                               value="CAN_8_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock1PayloadLength" type="ENUMERATION" 
                               value="CAN_8_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock2PayloadLength" type="ENUMERATION" 
                               value="CAN_16_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock3PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                      </d:ctr>
                    </d:chc>
                    <d:ctr name="CanRxFiFo" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:ctr name="CanEnhanceFiFo" type="IDENTIFIABLE">
                        <d:var name="CanEnhancedSchemeType" type="ENUMERATION" 
                               value="RANGE_FILTER_SCHEME"/>
                        <d:var name="CanFiFoOverflowNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CanController_5" type="IDENTIFIABLE">
                    <d:var name="CanHwChannel" type="ENUMERATION" 
                           value="FLEXCAN_5"/>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="592969728">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerId" type="INTEGER" value="5"/>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false"/>
                    <d:var name="CanAutoBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="CanControllerPrExcEn" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanControllerEdgeFilter" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerFdISO" type="BOOLEAN" value="true"/>
                    <d:var name="CanClockFromBus" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuRfclkSettingConf_6"/>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can/Can/CanConfigSet/CanController_5/CanControllerBaudrateConfig_1"/>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection_0/EcucPartition_0">
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:var name="CanErrorNotification" type="FUNCTION-NAME" 
                           value="Can_ErrorNotif">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="CanFDErrorNotification" type="FUNCTION-NAME" 
                           value="CanFd_ErrorNotif"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="ManuallyMbIdxCfg" type="BOOLEAN" value="true"/>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <d:ctr name="CanControllerBaudrateConfig_0" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="1000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="1">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_1" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="1000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="2">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_2" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="2"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="1000.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="1000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="2">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_3" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="3"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="1000.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="1000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="6">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="1">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:chc name="CanRamBlock" type="IDENTIFIABLE" 
                           value="CanRamBlockSpecified">
                      <a:a name="ENABLE" value="true"/>
                      <d:ctr name="CanRamBlockSpecified" type="IDENTIFIABLE">
                        <d:var name="CanBlock0PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock1PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock2PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock3PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                      </d:ctr>
                    </d:chc>
                    <d:ctr name="CanRxFiFo" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:ctr name="CanEnhanceFiFo" type="IDENTIFIABLE">
                        <d:var name="CanEnhancedSchemeType" type="ENUMERATION" 
                               value="RANGE_FILTER_SCHEME"/>
                        <d:var name="CanFiFoOverflowNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CanController_6" type="IDENTIFIABLE">
                    <d:var name="CanHwChannel" type="ENUMERATION" 
                           value="FLEXCAN_6"/>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="592969728">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerId" type="INTEGER" value="6"/>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false"/>
                    <d:var name="CanAutoBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="CanControllerPrExcEn" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanControllerEdgeFilter" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerFdISO" type="BOOLEAN" value="true"/>
                    <d:var name="CanClockFromBus" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuRfclkSettingConf_6"/>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can/Can/CanConfigSet/CanController_6/CanControllerBaudrateConfig_1"/>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection_0/EcucPartition_0">
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:var name="CanErrorNotification" type="FUNCTION-NAME" 
                           value="Can_ErrorNotif">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="CanFDErrorNotification" type="FUNCTION-NAME" 
                           value="CanFd_ErrorNotif"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="ManuallyMbIdxCfg" type="BOOLEAN" value="true"/>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <d:ctr name="CanControllerBaudrateConfig_0" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="1000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="1">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_1" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="2">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_2" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="2"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="2"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="1000.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="2">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_3" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="3"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="2"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="1000.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="5000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="6">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="1">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:chc name="CanRamBlock" type="IDENTIFIABLE" 
                           value="CanRamBlockSpecified">
                      <a:a name="ENABLE" value="true"/>
                      <d:ctr name="CanRamBlockSpecified" type="IDENTIFIABLE">
                        <d:var name="CanBlock0PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock1PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock2PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock3PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                      </d:ctr>
                    </d:chc>
                    <d:ctr name="CanRxFiFo" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:ctr name="CanEnhanceFiFo" type="IDENTIFIABLE">
                        <d:var name="CanEnhancedSchemeType" type="ENUMERATION" 
                               value="RANGE_FILTER_SCHEME"/>
                        <d:var name="CanFiFoOverflowNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CanController_7" type="IDENTIFIABLE">
                    <d:var name="CanHwChannel" type="ENUMERATION" 
                           value="FLEXCAN_7"/>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="592969728">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerId" type="INTEGER" value="7"/>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false"/>
                    <d:var name="CanAutoBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="CanControllerPrExcEn" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanControllerEdgeFilter" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerFdISO" type="BOOLEAN" value="true"/>
                    <d:var name="CanClockFromBus" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuRfclkSettingConf_6"/>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can/Can/CanConfigSet/CanController_7/CanControllerBaudrateConfig_1"/>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection_0/EcucPartition_0">
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:var name="CanErrorNotification" type="FUNCTION-NAME" 
                           value="Can_ErrorNotif">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="CanFDErrorNotification" type="FUNCTION-NAME" 
                           value="CanFd_ErrorNotif"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="ManuallyMbIdxCfg" type="BOOLEAN" value="true"/>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <d:ctr name="CanControllerBaudrateConfig_0" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="1000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="1">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_1" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="1000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="2">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_2" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="2"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="2"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="1000.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="1000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="2">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_3" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="3"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="2"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="1000.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="5000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="6">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="1">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:chc name="CanRamBlock" type="IDENTIFIABLE" 
                           value="CanRamBlockSpecified">
                      <a:a name="ENABLE" value="true"/>
                      <d:ctr name="CanRamBlockSpecified" type="IDENTIFIABLE">
                        <d:var name="CanBlock0PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock1PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock2PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock3PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                      </d:ctr>
                    </d:chc>
                    <d:ctr name="CanRxFiFo" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:ctr name="CanEnhanceFiFo" type="IDENTIFIABLE">
                        <d:var name="CanEnhancedSchemeType" type="ENUMERATION" 
                               value="RANGE_FILTER_SCHEME"/>
                        <d:var name="CanFiFoOverflowNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CanController_8" type="IDENTIFIABLE">
                    <d:var name="CanHwChannel" type="ENUMERATION" 
                           value="FLEXCAN_8"/>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="592969728">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerId" type="INTEGER" value="8"/>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false"/>
                    <d:var name="CanAutoBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="CanControllerPrExcEn" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanControllerEdgeFilter" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerFdISO" type="BOOLEAN" value="true"/>
                    <d:var name="CanClockFromBus" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuRfclkSettingConf_6"/>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can/Can/CanConfigSet/CanController_8/CanControllerBaudrateConfig_1"/>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection_0/EcucPartition_0">
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:var name="CanErrorNotification" type="FUNCTION-NAME" 
                           value="Can_ErrorNotif">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="CanFDErrorNotification" type="FUNCTION-NAME" 
                           value="CanFd_ErrorNotif"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="ManuallyMbIdxCfg" type="BOOLEAN" value="true"/>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <d:ctr name="CanControllerBaudrateConfig_0" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="1000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="1">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_1" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="2">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_2" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="2"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="2"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="1000.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="2">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_3" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="3"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="2"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="1000.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="5000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="6">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="1">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:chc name="CanRamBlock" type="IDENTIFIABLE" 
                           value="CanRamBlockSpecified">
                      <a:a name="ENABLE" value="true"/>
                      <d:ctr name="CanRamBlockSpecified" type="IDENTIFIABLE">
                        <d:var name="CanBlock0PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock1PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock2PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock3PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                      </d:ctr>
                    </d:chc>
                    <d:ctr name="CanRxFiFo" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:ctr name="CanEnhanceFiFo" type="IDENTIFIABLE">
                        <d:var name="CanEnhancedSchemeType" type="ENUMERATION" 
                               value="RANGE_FILTER_SCHEME"/>
                        <d:var name="CanFiFoOverflowNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="CanController_9" type="IDENTIFIABLE">
                    <d:var name="CanHwChannel" type="ENUMERATION" 
                           value="FLEXCAN_9"/>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="592969728">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerId" type="INTEGER" value="9"/>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="false"/>
                    <d:var name="CanAutoBusOffRecovery" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="CanControllerPrExcEn" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanControllerEdgeFilter" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerFdISO" type="BOOLEAN" value="true"/>
                    <d:var name="CanClockFromBus" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuRfclkSettingConf_6"/>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can/Can/CanConfigSet/CanController_9/CanControllerBaudrateConfig_1"/>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection_0/EcucPartition_0">
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:var name="CanErrorNotification" type="FUNCTION-NAME" 
                           value="Can_ErrorNotif">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="CanFDErrorNotification" type="FUNCTION-NAME" 
                           value="CanFd_ErrorNotif"/>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="ManuallyMbIdxCfg" type="BOOLEAN" value="true"/>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <d:ctr name="CanControllerBaudrateConfig_0" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="1000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="1">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_1" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="1"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="4"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="2">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_2" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="2"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="2"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="1000.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="2">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="CanControllerBaudrateConfig_3" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="ENHANCE_CBT"/>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="3"/>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="2"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="1000.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="7"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="4"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="2"/>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="true"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="5000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="6">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="1">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:chc name="CanRamBlock" type="IDENTIFIABLE" 
                           value="CanRamBlockSpecified">
                      <a:a name="ENABLE" value="true"/>
                      <d:ctr name="CanRamBlockSpecified" type="IDENTIFIABLE">
                        <d:var name="CanBlock0PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock1PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock2PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock3PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                      </d:ctr>
                    </d:chc>
                    <d:ctr name="CanRxFiFo" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:ctr name="CanEnhanceFiFo" type="IDENTIFIABLE">
                        <d:var name="CanEnhancedSchemeType" type="ENUMERATION" 
                               value="RANGE_FILTER_SCHEME"/>
                        <d:var name="CanFiFoOverflowNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                  </d:ctr>
                </d:lst>
                <d:ctr name="CanIcom" type="IDENTIFIABLE">
                  <a:a name="ENABLE" value="false"/>
                  <d:lst name="CanIcomConfig" type="MAP"/>
                </d:ctr>
              </d:ctr>
              <d:ctr name="CanGeneral" type="IDENTIFIABLE">
                <d:var name="CanDevErrorDetect" type="BOOLEAN" value="true"/>
                <d:var name="CanMulticoreSupport" type="BOOLEAN" value="false"/>
                <d:var name="CanVersionInfoApi" type="BOOLEAN" value="true"/>
                <d:var name="CanIndex" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanMainFunctionBusoffPeriod" type="FLOAT" 
                       value="0.001">
                  <a:a name="ENABLE" value="true"/>
                </d:var>
                <d:var name="CanMainFunctionWakeupPeriod" type="FLOAT" 
                       value="0.001">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanMainFunctionModePeriod" type="FLOAT" 
                       value="0.002"/>
                <d:var name="CanMultiplexedTransmission" type="BOOLEAN" 
                       value="true"/>
                <d:var name="CanTimeoutMethod" type="ENUMERATION" 
                       value="OSHAL_HORIZON"/>
                <d:var name="CanTimeoutDuration" type="FLOAT" value="1.0"/>
                <d:var name="CanLPduReceiveCalloutFunction" 
                       type="FUNCTION-NAME" value="CanLPduReceiveCallout">
                  <a:a name="ENABLE" value="true"/>
                </d:var>
                <d:lst name="CanEcucPartitionRef"/>
                <d:ref name="CanOsCounterRef" type="REFERENCE" >
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:ref>
                <d:var name="CanSetBaudrateApi" type="BOOLEAN" value="true">
                  <a:a name="ENABLE" value="true"/>
                </d:var>
                <d:var name="CanListenOnlyModeApi" type="BOOLEAN" value="false">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:ref name="CanSupportTTCANRef_" type="REFERENCE" >
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:ref>
                <d:ctr name="CanTimeStamp" type="IDENTIFIABLE">
                  <a:a name="ENABLE" value="false"/>
                  <d:var name="CanEnableHRCounter" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="CanRxTimestampNotification" type="FUNCTION-NAME" 
                         value="CanRxTimestampNotification"/>
                  <d:var name="CanTxTimestampNotification" type="FUNCTION-NAME" 
                         value="CanTxTimestampNotification"/>
                </d:ctr>
                <d:lst name="CanMainFunctionRWPeriods" type="MAP">
                  <d:ctr name="CanMainFunctionRWPeriods_0" type="IDENTIFIABLE">
                    <d:var name="CanMainFunctionPeriod" type="FLOAT" 
                           value="0.002"/>
                  </d:ctr>
                </d:lst>
                <d:var name="CanPublicIcomSupport" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:ctr name="CanIcomGeneral" type="IDENTIFIABLE">
                  <a:a name="ENABLE" value="false"/>
                  <d:var name="CanIcomLevel" type="ENUMERATION" 
                         value="CAN_ICOM_LEVEL_ONE">
                    <a:a name="ENABLE" value="false"/>
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="CanIcomVariant" type="ENUMERATION" 
                         value="CAN_ICOM_VARIANT_NONE">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="80">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="1"/>
                <d:var name="SwMinorVersion" type="INTEGER" value="0"/>
                <d:var name="SwPatchVersion" type="INTEGER" value="0"/>
                <d:var name="VendorId" type="INTEGER" value="189">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
