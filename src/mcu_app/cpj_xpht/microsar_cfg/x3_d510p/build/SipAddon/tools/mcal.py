from lxml import etree
import os
import json
import uuid

class xml_element:
    def __init__(self, tag, text=None, parameter_list=None):
        self.tag = tag
        self.text = text
        self.parameter_list = list()
        if parameter_list != None:
            self.parameter_list = list(parameter_list)
    def add_parameter(self, parameter_name, parameter_value):
        parameter = dict()
        parameter['name'] = parameter_name
        parameter['value'] = parameter_value
        if self.parameter_list is None:
            self.parameter_list = list()
        self.parameter_list.append(parameter)
    def set_text(self, text):
        self.text = text
    def set_tag(self, tag):
        self.tag = tag
    def create_xml_elem(self):
        elem = etree.Element(self.tag)
        elem.tail = '\n'
        for parameter in self.parameter_list:
            elem.set(parameter['name'], parameter['value'])
        if  self.text is not None:
            elem.text = self.text
        else:
            elem.text = '\n'
        return elem
    def xml_append_to_parent(self, parent):
        parent.append(self.create_xml_elem())

class parm_base_xml:
    def __init__(self,tag, val_dest = None, ref = None, value = None):
        self.ref = ref
        self.value = value
        self.tag = tag
        self.val_dest = val_dest

    def set_ref(self, ref):
        self.ref = ref
    def set_value(self, value):
        self.value = value
    def create_xml_elem(self):
        elem = xml_element(self.tag)
        elem_xml = elem.create_xml_elem()
        ref = xml_element('DEFINITION-REF', self.ref)
        ref.add_parameter('DEST', self.val_dest)
        ref.xml_append_to_parent(elem_xml)

        val = xml_element('VALUE', self.value)
        val.xml_append_to_parent(elem_xml)
        return elem_xml
    def xml_append_to_parent(self, parent):
            elem_xml = self.create_xml_elem()
            parent.append(elem_xml)
        
class parm_enum_xml(parm_base_xml):
    def __init__(self, ref=None, value=None):
        super().__init__('ECUC-TEXTUAL-PARAM-VALUE', 'ECUC-ENUMERATION-PARAM-DEF', ref, value)
class parm_int_xml(parm_base_xml):
    def __init__(self, ref=None, value=None):
        super().__init__('ECUC-NUMERICAL-PARAM-VALUE', 'ECUC-INTEGER-PARAM-DEF', ref, value)
class parm_bool_xml(parm_base_xml):
    def __init__(self, ref=None, value=None):
        super().__init__('ECUC-NUMERICAL-PARAM-VALUE', 'ECUC-BOOLEAN-PARAM-DEF', ref, value)
class parm_float_xml(parm_base_xml):
    def __init__(self, ref=None, value=None):
        super().__init__('ECUC-NUMERICAL-PARAM-VALUE', 'ECUC-FLOAT-PARAM-DEF', ref, value)

class ref_val_xml:
    def __init__(self, definition=None, value=None):
        self.definition = definition
        self.value = value
    def create_xml_elem(self):
        child = xml_element('ECUC-REFERENCE-VALUE')
        xml_child = child.create_xml_elem()

        definition_ref = xml_element('DEFINITION-REF', self.definition)
        definition_ref.add_parameter('DEST', 'ECUC-REFERENCE-DEF')
        definition_ref.xml_append_to_parent(xml_child)

        value_ref = xml_element('VALUE-REF', self.value)
        value_ref.add_parameter('DEST', 'ECUC-CONTAINER-VALUE')
        value_ref.xml_append_to_parent(xml_child)
        return xml_child
    def xml_append_to_parent(self, parent):
        elem_xml = self.create_xml_elem()
        parent.append(elem_xml)


class can_hardware_obj:
    def __init__(self, can_obj):
        self.can_obj = can_obj
    
    def create_parameter_values(self):
        parameter_values = xml_element('PARAMETER-VALUES')
        xml_parameters = parameter_values.create_xml_elem()

        CanHandleType = parm_enum_xml('/Renesas/EcucDefs_Can/Can/CanConfigSet/CanHardwareObject/CanHandleType', self.can_obj['CanHandleType'])
        CanHandleType.xml_append_to_parent(xml_parameters)

        CanHwObjectCount = parm_int_xml('/Renesas/EcucDefs_Can/Can/CanConfigSet/CanHardwareObject/CanHwObjectCount', self.can_obj['CanHwObjectCount'])
        CanHwObjectCount.xml_append_to_parent(xml_parameters)

        CanIdType = parm_enum_xml('/Renesas/EcucDefs_Can/Can/CanConfigSet/CanHardwareObject/CanIdType', self.can_obj['CanIdType'])
        CanIdType.xml_append_to_parent(xml_parameters)

        CanObjectId = parm_int_xml('/Renesas/EcucDefs_Can/Can/CanConfigSet/CanHardwareObject/CanObjectId', self.can_obj['CanObjectId'])
        CanObjectId.xml_append_to_parent(xml_parameters)

        CanObjectType = parm_enum_xml('/Renesas/EcucDefs_Can/Can/CanConfigSet/CanHardwareObject/CanObjectType', self.can_obj['CanObjectType'])
        CanObjectType.xml_append_to_parent(xml_parameters)

        CanMemoryMode = parm_enum_xml('/Renesas/EcucDefs_Can/Can/CanConfigSet/CanHardwareObject/CanMemoryMode', self.can_obj['CanMemoryMode'])
        CanMemoryMode.xml_append_to_parent(xml_parameters)
        # parameter add finish
        return xml_parameters
    def _change_can_channel_name(self, ref):
        out_ref = ref
        if ref['definition'] == '/Renesas/EcucDefs_Can/Can/CanConfigSet/CanHardwareObject/CanControllerRef':
            # if 'CANA' in self.can_obj['name']:
            #     out_ref['value'] = ref['value'][:ref['value'].rfind('/')] + '/CANA'
            # elif 'CANB' in self.can_obj['name']:
            #     out_ref['value'] = ref['value'][:ref['value'].rfind('/')] + '/CANB'
            out_ref['value'] = ref['value'][:ref['value'].rfind('/')] + '/'+ self.can_obj['channel_name']
        return out_ref

    def create_reference_values(self):
        reference_values = xml_element('REFERENCE-VALUES')
        xml_reference_values = reference_values.create_xml_elem()

        for ref in self.can_obj['reference_values']:
            ref = self._change_can_channel_name(ref)
            ref_xml = ref_val_xml(ref['definition'], ref['value'])
            ref_xml.xml_append_to_parent(xml_reference_values)
        return xml_reference_values
    def create_sub_container_values_CanReceiveFIFOConfiguration(self):  
        container_value = xml_element('ECUC-CONTAINER-VALUE')
        container_value.add_parameter('UUID', str(uuid.uuid4()))
        xml_container_value = container_value.create_xml_elem()
        
        short_name = xml_element('SHORT-NAME', 'CanReceiveFIFOConfiguration')
        short_name.xml_append_to_parent(xml_container_value)

        definition_ref = xml_element('DEFINITION-REF', '/Renesas/EcucDefs_Can/Can/CanConfigSet/CanHardwareObject/CanReceiveFIFOConfiguration')
        definition_ref.add_parameter('DEST', 'ECUC-PARAM-CONF-CONTAINER-DEF')
        definition_ref.xml_append_to_parent(xml_container_value)

        parameter_values = xml_element('PARAMETER-VALUES')
        xml_parameter_values = parameter_values.create_xml_elem()
        # 
        filter_parameters =  {
            'CanReceiveFIFOBufferDepth' : 'BUFFER_128',
            'CanReceiveFIFOPayloadLength' : 'PAYLOAD_64',
            'CanReceiveFIFOInterruptRatioSel' : 'FIFO_8_BY_8',
            'CanReceiveFIFOInterruptSrcSel' : 'SPECIFIED_FIFO_RATIO'
        }
        for key, value in filter_parameters.items():
            can_filter_code = parm_enum_xml('/Renesas/EcucDefs_Can/Can/CanConfigSet/CanHardwareObject/CanReceiveFIFOConfiguration/' + key, value)
            can_filter_code.xml_append_to_parent(xml_parameter_values)
        # bool parameter
        can_filter_code = parm_bool_xml('/Renesas/EcucDefs_Can/Can/CanConfigSet/CanHardwareObject/CanReceiveFIFOConfiguration/' + 'CanEnableReceiveFIFOInterrupt', 'false')
        can_filter_code.xml_append_to_parent(xml_parameter_values)
        xml_container_value.append(xml_parameter_values)
        return xml_container_value

    def create_sub_container_values_filters(self, filter_list):
        sub_container_values = xml_element('SUB-CONTAINERS')
        xml_sub_container_values = sub_container_values.create_xml_elem()
        for filter in filter_list: 
            container_value = xml_element('ECUC-CONTAINER-VALUE')
            container_value.add_parameter('UUID', filter['UUID'])
            xml_container_value = container_value.create_xml_elem()


            short_name = xml_element('SHORT-NAME', filter['name'])
            short_name.xml_append_to_parent(xml_container_value)

            definition_ref = xml_element('DEFINITION-REF', '/Renesas/EcucDefs_Can/Can/CanConfigSet/CanHardwareObject/CanHwFilter')
            definition_ref.add_parameter('DEST', 'ECUC-PARAM-CONF-CONTAINER-DEF')
            definition_ref.xml_append_to_parent(xml_container_value)

            parameter_values = xml_element('PARAMETER-VALUES')
            xml_parameter_values = parameter_values.create_xml_elem()

            can_filter_code = parm_int_xml('/Renesas/EcucDefs_Can/Can/CanConfigSet/CanHardwareObject/CanHwFilter/CanHwFilterCode', filter['CanFilterCode'])
            can_filter_code.xml_append_to_parent(xml_parameter_values)

            can_filter_mask = parm_int_xml('/Renesas/EcucDefs_Can/Can/CanConfigSet/CanHardwareObject/CanHwFilter/CanHwFilterMask', filter['CanFilterMask'])
            can_filter_mask.xml_append_to_parent(xml_parameter_values)

            can_hw_filter_receive_id_type = parm_enum_xml('/Renesas/EcucDefs_Can/Can/CanConfigSet/CanHardwareObject/CanHwFilter/CanHwFilterReceiveIdType', filter['CanHwFilterReceiveIdType'])
            can_hw_filter_receive_id_type.xml_append_to_parent(xml_parameter_values)

            can_hw_filter_source_node = parm_enum_xml('/Renesas/EcucDefs_Can/Can/CanConfigSet/CanHardwareObject/CanHwFilter/CanHwFilterSourceNode', filter['CanHwFilterSourceNode'])
            can_hw_filter_source_node.xml_append_to_parent(xml_parameter_values)

            can_hw_filter_dlc_check_value = parm_int_xml('/Renesas/EcucDefs_Can/Can/CanConfigSet/CanHardwareObject/CanHwFilter/CanHwFilterDLCCheckValue', filter['CanHwFilterDlcCheckValue'])
            can_hw_filter_dlc_check_value.xml_append_to_parent(xml_parameter_values)

            xml_container_value.append(xml_parameter_values)
            xml_sub_container_values.append(xml_container_value)
        CanReceiveFIFOConfiguration =  self.create_sub_container_values_CanReceiveFIFOConfiguration()
        xml_sub_container_values.append(CanReceiveFIFOConfiguration)
        return xml_sub_container_values
        

    def create_can_object(self):
        can_obj_root = xml_element("ECUC-CONTAINER-VALUE")
        # if 'UUID' in self.can_obj:
        #     can_obj_root.add_parameter('UUID', self.can_obj['UUID'])
        # else:
        #     can_obj_root.add_parameter('UUID', uuid.uuid4())
        xml_can_obj = can_obj_root.create_xml_elem()
        short_name = xml_element('SHORT-NAME', self.can_obj['name'])
        short_name.xml_append_to_parent(xml_can_obj)

        definition_ref = xml_element('DEFINITION-REF', '/Renesas/EcucDefs_Can/Can/CanConfigSet/CanHardwareObject')
        definition_ref.add_parameter('DEST', 'ECUC-PARAM-CONF-CONTAINER-DEF')
        definition_ref.xml_append_to_parent(xml_can_obj)

        xml_parameters = self.create_parameter_values()
        xml_can_obj.append(xml_parameters)

        xml_reference_values = self.create_reference_values()
        xml_can_obj.append(xml_reference_values)
        if self.can_obj['CanObjectType'] == 'RECEIVE':
            filter_list = self.can_obj['filter_list']
            # print(type(filter_list))
            # print(filter_list)
            xml_sub_container_values = self.create_sub_container_values_filters(filter_list)
            xml_can_obj.append(xml_sub_container_values)

        return xml_can_obj      
            


def vector_cfg_to_renesas_cfg(vector_cfg):
    renesas_obj_demo = {
        'UUID': 'XXX',
        'name': 'XXX',
        'CanHandleType': 'FULL',
        'CanHwObjectCount': '1',
        'CanIdType': 'STANDARD',
        'CanObjectId': '1',
        'CanObjectType': 'RECEIVE',
        'CanMemoryMode': 'BUFFER_MODE',
        'reference_values': [
            {
                'definition': '/Renesas/EcucDefs_Can/Can/CanConfigSet/CanHardwareObject/CanControllerRef',
                'value': '/ActiveEcuC/Can/CanConfigSet0/CanController0'
            },
            {
                'definition': '/Renesas/EcucDefs_Can/Can/CanConfigSet/CanHardwareObject/CanMainFunctionRWPeriodRef',
                'value': '/ActiveEcuC/Can/CanGeneral/CanMainFunctionRWPeriods'
            }
        ]
    }

    renesas_obj_demo['channel_name'] = vector_cfg['channel_name']
    renesas_obj_demo['UUID'] = str(uuid.uuid4())
    renesas_obj_demo['name'] = vector_cfg['name']
    renesas_obj_demo['CanObjectId'] = vector_cfg['CanObjectId']
    renesas_obj_demo['CanObjectType'] = vector_cfg['CanObjectType']
    renesas_obj_demo['CanMemoryMode'] = vector_cfg['CanMemoryMode']
    if vector_cfg['CanObjectType'] == 'RECEIVE':
        renesas_obj_demo['CanHandleType'] = 'BASIC'
        out_filter_list = list()
        for rx_filter in vector_cfg['filters']:
            filter_info = dict()
            filter_info['UUID'] = str(uuid.uuid4())
            filter_info['name'] = rx_filter['name']
            filter_info['CanFilterCode'] = rx_filter['CanHwFilterCode']
            filter_info['CanFilterMask'] =  '2047'
            filter_info['CanHwFilterReceiveIdType'] = 'STANDARD'
            filter_info['CanHwFilterSourceNode'] =  'FROM_OTHER_NODE'
            filter_info['CanHwFilterDlcCheckValue'] =  '0'
            out_filter_list.append(filter_info)
        renesas_obj_demo['filter_list'] = out_filter_list
        
    return renesas_obj_demo

def  create_can_base_rx_fifo_obj(name):
    vector_cfg = {'name': name, 
                    'CanHandleType': 'BASIC', 
                    'CanObjectHwSize': '1', 
                    'CanIdType': 'STANDARD', 
                    'CanObjectId': '1', 
                    'CanObjectType': 'RECEIVE',
                    'CanIdValue' : '0x7FF',}
    return vector_cfg_to_renesas_cfg(vector_cfg)

out_file_path = r'../Config/ECUC/iDCH_v4h_Can_Can_ecuc.arxml'
current_path = os.path.dirname(os.path.abspath(__file__))
modified_file = os.path.join(current_path, out_file_path)
arxml_file = os.path.join(current_path, "Template.arxml")
# arxml_file = os.path.join(current_path, "StartApplication_Can_Can_ecuc.arxml")

class vector_info_to_mcal:
    def __init__(self, json_path):
        # 从文件加载 JSON 数据
        with open(json_path , "r") as file:
            self.vector_info = json.load(file)
    def format_hw_obj(self):
        self.hw_obj_list = list()
        for channel_name, channel_info in self.vector_info.items():
            if len(channel_info['tx']):
                for hw_obj in channel_info['tx']:
                    hw_obj_info = dict()
                    hw_obj_info['name'] = hw_obj['name']
                    hw_obj_info['CanObjectId'] = hw_obj['CanObjectId']
                    hw_obj_info['CanObjectType'] = 'TRANSMIT'
                    hw_obj_info['CanMemoryMode'] = 'BUFFER_MODE'
                    hw_obj_info['CanIdValue'] = hw_obj['CanIdValue']
                    hw_obj_info['channel_name'] = channel_name
                    self.hw_obj_list.append(hw_obj_info)
            if len(channel_info['rx']):
                hw_rx_obj_info = dict()
                for hw_obj in channel_info['rx']:
                    hw_rx_obj_info['name'] = hw_obj['name']
                    hw_rx_obj_info['CanObjectId'] = hw_obj['CanObjectId']
                    hw_rx_obj_info['CanObjectType'] = 'RECEIVE'
                    hw_rx_obj_info['CanMemoryMode'] = 'RECEIVE_FIFO_MODE'
                filters = list()
                for rx_info in channel_info['rx_filters']:
                    filter = dict()
                    filter['name'] = rx_info['name'] + '_filter'
                    filter['CanHwFilterCode'] = rx_info['CanIfRxPduCanId']
                    filter['CanHwFilterMask'] = '2047'
                    filters.append(filter)
                hw_rx_obj_info['filters'] = filters
                hw_rx_obj_info['channel_name'] = channel_name
                self.hw_obj_list.append(hw_rx_obj_info)

        return self.hw_obj_list



json_path = os.path.join(current_path, "vetor_can_objects.json")

fix_vector_obj_info = list()
if __name__ == "__main__":
    
    # 解析ARXML文件
    tree = etree.parse(arxml_file)
    root = tree.getroot()


    # 定义命名空间映射
    namespaces = {'ar': 'http://autosar.org/schema/r4.0',
                'xsi': 'http://www.w3.org/2001/XMLSchema-instance'}

    # 查找所有ECU实例
    can_config_instances = root.xpath('//ar:ECUC-CONTAINER-VALUE[ar:SHORT-NAME="CanConfigSet0"]/ar:SUB-CONTAINERS', namespaces=namespaces)
    ecu_instances = can_config_instances[0].xpath('./ar:ECUC-CONTAINER-VALUE', namespaces=namespaces)

    mcal_info = vector_info_to_mcal(json_path)
    for hw_obj in mcal_info.format_hw_obj():
        # print(hw_obj)
        if len(hw_obj):
            renesas_cfg = vector_cfg_to_renesas_cfg(hw_obj)
            obj_handle = can_hardware_obj(renesas_cfg)
            new_element = obj_handle.create_can_object()
            can_config_instances[0].append(new_element)

    etree.indent(root, space="    ")
    tree.write(modified_file, encoding="utf-8", xml_declaration=True, method="xml")

