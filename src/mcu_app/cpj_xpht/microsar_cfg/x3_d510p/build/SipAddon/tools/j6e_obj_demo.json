{"name": "XXX", "type": "IDENTIFIABLE", "CanObjectId": {"type": "INTEGER", "value": "1"}, "CanObjectType": {"type": "ENUMERATION", "value": "RECEIVE", "a": {"name": "IMPORTER_INFO", "value": "@DEF"}}, "CanHandleType": {"type": "ENUMERATION", "value": "FULL"}, "CanIdType": {"type": "ENUMERATION", "value": "STANDARD"}, "CanFdPaddingValue": {"type": "INTEGER", "value": "0", "a": [{"tag": "a", "name": "ENABLE", "value": "false"}, {"name": "IMPORTER_INFO", "value": "@DEF"}]}, "CanHardwareObjectUsesPolling": {"type": "BOOLEAN", "value": "false", "a": {"name": "ENABLE", "value": "false"}}, "CanTriggerTransmitEnable": {"type": "BOOLEAN", "value": "false", "a": {"name": "ENABLE", "value": "false"}}, "CanHwObjectUsesBlock": {"type": "ENUMERATION", "value": "CAN_RAM_BLOCK_0", "a": {"name": "ENABLE", "value": "true"}}, "CanHwObjectCount": {"type": "INTEGER", "value": "1"}, "CanHthMBIdx": {"type": "INTEGER", "value": "255", "a": {"name": "IMPORTER_INFO", "value": "@DEF"}}, "CanTimeStampEnable": {"type": "BOOLEAN", "value": "false"}, "CanControllerRef": {"type": "REFERENCE", "value": "ASPath:/Can/Can/CanConfigSet/CanController_0"}, "CanMainFunctionRWPeriodRef": {"type": "REFERENCE", "value": "ASPath:/Can/Can/CanGeneral/CanMainFunctionRWPeriods_0", "a": {"name": "ENABLE", "value": "true"}}}