from lxml import etree
import os
import json


def get_arxml_root(file_path):
    current_path = os.path.dirname(os.path.abspath(__file__))
    print("Current file path:", current_path)

    # arxml_file = os.path.join(current_path, "Workspace123.arxml")
    arxml_file = os.path.join(current_path, file_path)


    # 解析ARXML文件
    tree = etree.parse(arxml_file)
    root = tree.getroot()
    return root
   


def get_rx_filter_objs(root , rx_filter_list, channel_name,):
    # 查找所有ECU实例
    can_config_instances = root.xpath('//ar:ECUC-CONTAINER-VALUE[ar:SHORT-NAME="CanIfInitCfg"]/ar:SUB-CONTAINERS', namespaces=namespaces)
    ecu_instances = can_config_instances[0].xpath('./ar:ECUC-CONTAINER-VALUE', namespaces=namespaces)
    # 遍历每个ECU实例并获取所需的配置信息
    for ecu_instance in ecu_instances:
        # 获取ECU实例的名称
        ecu_name  = ecu_instance.xpath('.//ar:SHORT-NAME', namespaces=namespaces)[0].text
        module = ecu_instance.xpath('.//ar:DEFINITION-REF', namespaces=namespaces)[0].text
        module_name = module[module.rfind('/')+1:]
        if 'CanIfBufferCfg'  in module_name:
            continue
        print('module_name', module_name)
        print('ECU Name:', ecu_name)
        can_obj = dict()
        can_obj['name'] = ecu_name
        # 获取ECU实例的参数
        parameters = ecu_instance.xpath('.//ar:PARAMETER-VALUES', namespaces=namespaces)

        # 遍历每个参数并打印名称和值
        for parameter in parameters:
            param_name_list = parameter.xpath('.//ar:DEFINITION-REF', namespaces=namespaces)
            param_value_list = parameter.xpath('.//ar:VALUE', namespaces=namespaces)
            for param_name, param_value in zip(param_name_list, param_value_list):
                name = param_name.text[param_name.text.rfind('/')+1:]
                # print('     ', name, ' : ', param_value.text)
                # print('             Parameter Name:', param_name.text)
                can_obj[name] = param_value.text
        if module_name == 'CanIfRxPduCfg':
            if channel_name in ecu_name:
                rx_filter_list.append(can_obj)

def get_can_objs(root, tx_obj_list, rx_obj_list, channel_name):
    # 查找所有ECU实例
    can_config_instances = root.xpath('//ar:ECUC-CONTAINER-VALUE[ar:SHORT-NAME="CanConfigSet"]/ar:SUB-CONTAINERS', namespaces=namespaces)
    ecu_instances = can_config_instances[0].xpath('./ar:ECUC-CONTAINER-VALUE', namespaces=namespaces)
    # 遍历每个ECU实例并获取所需的配置信息
    for ecu_instance in ecu_instances:
        # 获取ECU实例的名称
        ecu_name  = ecu_instance.xpath('.//ar:SHORT-NAME', namespaces=namespaces)[0].text
        module = ecu_instance.xpath('.//ar:DEFINITION-REF', namespaces=namespaces)[0].text
        module_name = module[module.rfind('/')+1:]
        if 'CanHardwareObject' not in  module_name:
            continue
        print('module_name', module_name)
        print('ECU Name:', ecu_name)
        can_obj = dict()
        can_obj['name'] = ecu_name
        # 获取ECU实例的参数
        parameters = ecu_instance.xpath('.//ar:PARAMETER-VALUES', namespaces=namespaces)

        # 遍历每个参数并打印名称和值
        for parameter in parameters:
            param_name_list = parameter.xpath('.//ar:DEFINITION-REF', namespaces=namespaces)
            param_value_list = parameter.xpath('.//ar:VALUE', namespaces=namespaces)
            for param_name, param_value in zip(param_name_list, param_value_list):
                name = param_name.text[param_name.text.rfind('/')+1:]
                # print('     ', name, ' : ', param_value.text)
                # print('             Parameter Name:', param_name.text)
                can_obj[name] = param_value.text

        if 'Tx' in ecu_name:
            if channel_name in ecu_name:
                tx_obj_list.append(can_obj)
        elif 'Rx' in ecu_name:
            if channel_name in ecu_name:
                rx_obj_list.append(can_obj)


def save_to_json(hw_objs):
    current_path = os.path.dirname(os.path.abspath(__file__))
    json_path = os.path.join(current_path, "vetor_can_objects.json")
    # 格式化输出到文件
    with open(json_path, "w") as file:
        json.dump(hw_objs, file, indent=4, sort_keys=True)
    hw_objs_json = json.dumps(hw_objs)
    # print(hw_objs)

StartApplication_Canif_ecuc_path = r'../../SipAddon/Config/ECUC/StartApplication_CanIf_CanIf_ecuc.arxml'
StartApplication_Can_ecuc_path = r'../../SipAddon/Config/ECUC/StartApplication_Can_Can_ecuc.arxml'
 # 定义命名空间映射
namespaces = {'ar': 'http://autosar.org/schema/r4.0',
            'xsi': 'http://www.w3.org/2001/XMLSchema-instance'}

if __name__ == "__main__":
    
    channel_list = ['CANA', 'CAN_PR1']
    hw_objs = dict()
    canif_root = get_arxml_root(StartApplication_Canif_ecuc_path)
    print(canif_root)
    can_root = get_arxml_root(StartApplication_Can_ecuc_path)
    for channel_name in channel_list:
        hw_objs[channel_name] = dict()
        hw_objs[channel_name]['rx_filters'] = list()
        hw_objs[channel_name]['tx'] = list()
        hw_objs[channel_name]['rx'] = list()
        get_rx_filter_objs(canif_root, hw_objs[channel_name]['rx_filters'], channel_name)
        get_can_objs(can_root, hw_objs[channel_name]['tx'], hw_objs[channel_name]['rx'], channel_name)

    save_to_json(hw_objs)
