{"CANA": {"rx": [{"CanFdPaddingValue": "0", "CanHandleType": "BASIC", "CanHwProcessing": "true", "CanIdType": "STANDARD", "CanIdValue": "0", "CanMaxDataLen": "64", "CanObjectHwFifo": "false", "CanObjectHwHandle": "0", "CanObjectHwSize": "6", "CanObjectId": "7", "CanObjectMultiplexedTransmission": "false", "CanObjectType": "RECEIVE", "name": "CN_CANA_2ccf3453_Rx"}], "rx_filters": [{"CanIfRxPduCanId": "642", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "16", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "21", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "ZCU_L_4_oCANA_36404d7d_Rx"}, {"CanIfRxPduCanId": "1212", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "8", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "6", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "ICM_SW2_oCANA_098251e7_Rx"}, {"CanIfRxPduCanId": "531", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "8", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "26", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "VCU_A_oCANA_f9326f64_Rx"}, {"CanIfRxPduCanId": "1218", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "48", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "5", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "VCU_9_oCANA_593fecdf_Rx"}, {"CanIfRxPduCanId": "1050", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "16", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "12", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "ZCU_2_oCANA_b2053824_Rx"}, {"CanIfRxPduCanId": "736", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "24", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "19", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "ICM_0x2E0_oCANA_b90fcaf4_Rx"}, {"CanIfRxPduCanId": "411", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "8", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "29", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "YRS_0x19B_oCANA_d57b75ea_Rx"}, {"CanIfRxPduCanId": "658", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "16", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "20", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "VCCM_11_oCANA_fc694ad3_Rx"}, {"CanIfRxPduCanId": "1062", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "8", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "10", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "VCC_2_oCANA_412e57f4_Rx"}, {"CanIfRxPduCanId": "307", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "8", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "34", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "SDM_1_oCANA_ddbdaf4a_Rx"}, {"CanIfRxPduCanId": "1047", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "16", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "14", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "ZCU_L_0x417_oCANA_09c3b234_Rx"}, {"CanIfRxPduCanId": "314", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "8", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "33", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "VCU_1_oCANA_9da1c5e8_Rx"}, {"CanIfRxPduCanId": "1063", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "32", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "9", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "VCC_3_oCANA_f9b770aa_Rx"}, {"CanIfRxPduCanId": "832", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "8", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "18", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "BMS_2_oCANA_b4d993e3_Rx"}, {"CanIfRxPduCanId": "271", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "64", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "38", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "IEBM_1_oCANA_2d41949e_Rx"}, {"CanIfRxPduCanId": "410", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "8", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "30", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "YRS_0x19A_oCANA_9fe938ab_Rx"}, {"CanIfRxPduCanId": "896", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "8", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "16", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "DKM_7_oCANA_d2ebb77a_Rx"}, {"CanIfRxPduCanId": "444", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "16", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "28", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "EPS_0x1BC_oCANA_860f8af2_Rx"}, {"CanIfRxPduCanId": "1152", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "16", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "8", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "TCP_2_oCANA_80c16d32_Rx"}, {"CanIfRxPduCanId": "288", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "24", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "37", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "IEBM_4_oCANA_f00bec13_Rx"}, {"CanIfRxPduCanId": "1183", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "8", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "7", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "DKM_9_oCANA_33c1410b_Rx"}, {"CanIfRxPduCanId": "546", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "8", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "24", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "ETRS_1_oCANA_8b70d6a2_Rx"}, {"CanIfRxPduCanId": "1360", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "8", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "3", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "IEBM_NM_oCANA_465b0a35_Rx"}, {"CanIfRxPduCanId": "298", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "16", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "35", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "IEBM_3_oCANA_32df5ad9_Rx"}, {"CanIfRxPduCanId": "267", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "64", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "39", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "IEBM_2_oCANA_d0a8beda_Rx"}, {"CanIfRxPduCanId": "497", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "8", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "27", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "VCCM_0x1F1_oCANA_803107cc_Rx"}, {"CanIfRxPduCanId": "544", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "8", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "25", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "ZCU_L_8_oCANA_8d03fa97_Rx"}, {"CanIfRxPduCanId": "297", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "24", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "36", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "EPS_0x129_oCANA_a40e67a4_Rx"}, {"CanIfRxPduCanId": "315", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "64", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "32", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "VCU_5_oCANA_12565253_Rx"}, {"CanIfRxPduCanId": "1847", "CanIfRxPduCanIdType": "STANDARD_FD_CAN", "CanIfRxPduDlc": "64", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "2", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "CanTp_RxIndication", "CanIfRxPduUserRxIndicationType": "PDUID_PDUINFOPTR", "CanIfRxPduUserRxIndicationUL": "CAN_TP", "name": "Diag_IDM_PhyReq_Tp_oCANA_c6229fde_Rx"}, {"CanIfRxPduCanId": "1296", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "8", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "4", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "VCCM_NM_oCANA_558bab75_Rx"}, {"CanIfRxPduCanId": "336", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "16", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "31", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "EPS_0x150_oCANA_3ffce6b2_Rx"}, {"CanIfRxPduCanId": "2015", "CanIfRxPduCanIdType": "STANDARD_FD_CAN", "CanIfRxPduDlc": "64", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "1", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "CanTp_RxIndication", "CanIfRxPduUserRxIndicationType": "PDUID_PDUINFOPTR", "CanIfRxPduUserRxIndicationUL": "CAN_TP", "name": "Diag_IDM_FuncReq_Tp_oCANA_660bd030_Rx"}, {"CanIfRxPduCanId": "1043", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "16", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "15", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "VCCM_13_oCANA_c8f60380_Rx"}, {"CanIfRxPduCanId": "641", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "16", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "22", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "ZCU_1_oCANA_a0df5787_Rx"}, {"CanIfRxPduCanId": "1048", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "8", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "13", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "ZCU_L_0x418_oCANA_41f1ca0d_Rx"}, {"CanIfRxPduCanId": "1052", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "32", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "11", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "ZCU_L_0x41C_oCANA_17b1e71e_Rx"}, {"CanIfRxPduCanId": "891", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "32", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "17", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "ECC_Sensor_oCANA_1b4e6d43_Rx"}, {"CanIfRxPduCanId": "548", "CanIfRxPduCanIdType": "STANDARD_CAN", "CanIfRxPduDlc": "8", "CanIfRxPduDlcCheck": "true", "CanIfRxPduId": "23", "CanIfRxPduReadData": "false", "CanIfRxPduReadNotifyStatus": "false", "CanIfRxPduUserRxIndicationName": "PduR_CanIfRxIndication", "CanIfRxPduUserRxIndicationUL": "PDUR", "name": "VCCM_0x224_oCANA_b7aa4b0c_Rx"}], "tx": [{"CanFdPaddingValue": "0", "CanHandleType": "BASIC", "CanHwProcessing": "true", "CanIdType": "STANDARD", "CanIdValue": "0", "CanMaxDataLen": "64", "CanObjectHwFifo": "false", "CanObjectHwHandle": "6", "CanObjectHwSize": "1", "CanObjectId": "6", "CanObjectMultiplexedTransmission": "false", "CanObjectType": "TRANSMIT", "name": "CN_CANA_973fcb74_Tx"}, {"CanCommonCANNode": "A", "CanFdPaddingValue": "0", "CanHandleType": "FULL", "CanHwProcessing": "true", "CanIdType": "STANDARD", "CanIdValue": "512", "CanMaxDataLen": "64", "CanObjectHwFifo": "false", "CanObjectHwHandle": "0", "CanObjectHwSize": "1", "CanObjectId": "0", "CanObjectMultiplexedTransmission": "false", "CanObjectType": "TRANSMIT", "name": "CanHardwareObject_IDM_1_oCANA_4092cfa6_Tx"}, {"CanCommonCANNode": "A", "CanFdPaddingValue": "0", "CanHandleType": "FULL", "CanHwProcessing": "true", "CanIdType": "STANDARD", "CanIdValue": "516", "CanMaxDataLen": "64", "CanObjectHwFifo": "false", "CanObjectHwHandle": "1", "CanObjectHwSize": "1", "CanObjectId": "1", "CanObjectMultiplexedTransmission": "false", "CanObjectType": "TRANSMIT", "name": "CanHardwareObject_IDM_2_oCANA_5248a005_Tx"}, {"CanCommonCANNode": "A", "CanFdPaddingValue": "0", "CanHandleType": "FULL", "CanHwProcessing": "true", "CanIdType": "STANDARD", "CanIdValue": "1537", "CanMaxDataLen": "64", "CanObjectHwFifo": "false", "CanObjectHwHandle": "2", "CanObjectHwSize": "1", "CanObjectId": "2", "CanObjectMultiplexedTransmission": "false", "CanObjectType": "TRANSMIT", "name": "CanHardwareObject_IDC_Temp_oCANA_78b59e5d_Tx"}, {"CanCommonCANNode": "A", "CanFdPaddingValue": "0", "CanHandleType": "FULL", "CanHwProcessing": "true", "CanIdType": "STANDARD", "CanIdValue": "1543", "CanMaxDataLen": "64", "CanObjectHwFifo": "false", "CanObjectHwHandle": "5", "CanObjectHwSize": "1", "CanObjectId": "5", "CanObjectMultiplexedTransmission": "false", "CanObjectType": "TRANSMIT", "name": "CanHardwareObject_IDC_Volt_1_oCANA_c03f7e4a_Tx"}, {"CanCommonCANNode": "A", "CanFdPaddingValue": "0", "CanHandleType": "FULL", "CanHwProcessing": "true", "CanIdType": "STANDARD", "CanIdValue": "1538", "CanMaxDataLen": "64", "CanObjectHwFifo": "false", "CanObjectHwHandle": "3", "CanObjectHwSize": "1", "CanObjectId": "3", "CanObjectMultiplexedTransmission": "false", "CanObjectType": "TRANSMIT", "name": "CanHardwareObject_IDC_Volt_2_oCANA_738a9f78_Tx"}, {"CanCommonCANNode": "A", "CanFdPaddingValue": "0", "CanHandleType": "FULL", "CanHwProcessing": "true", "CanIdType": "STANDARD", "CanIdValue": "1539", "CanMaxDataLen": "64", "CanObjectHwFifo": "false", "CanObjectHwHandle": "4", "CanObjectHwSize": "1", "CanObjectId": "4", "CanObjectMultiplexedTransmission": "false", "CanObjectType": "TRANSMIT", "name": "CanHardwareObject_IDC_Volt_3_oCANA_abc93da9_Tx"}]}, "CAN_PR1": {"rx": [], "rx_filters": [], "tx": []}}