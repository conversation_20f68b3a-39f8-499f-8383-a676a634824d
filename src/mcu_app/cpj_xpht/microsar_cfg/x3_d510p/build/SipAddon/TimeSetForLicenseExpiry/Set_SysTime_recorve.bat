@echo off
echo autor OA<PERSON>
@echo off
echo --------------------------------
@echo off
echo setup time resync every one hour
@echo off
REG ADD HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\W32Time\TimeProviders\NtpClient /v SpecialPollInterval /t REG_DWORD /d 3600 /f
@echo off
echo --------------------------------
echo setup w32time auto startup
@echo off
REG ADD HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\W32Time /v Start /t REG_DWORD /d 2 /f
@echo off
echo --------------------------------
@echo off
set TimeServer="time.windows.com"
net time /setsntp:%TimeServer%

net stop w32time

net start w32time

w32tm /resync