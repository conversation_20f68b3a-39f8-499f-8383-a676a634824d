<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
      <SHORT-NAME>MICROSAR</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="ac9253d0-b213-4c4b-a081-af764e2ab20c">
          <SHORT-NAME>Det_swc</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="68ea981d-3169-4bc4-bedd-e4193707862d">
              <SHORT-NAME>Interfaces</SHORT-NAME>
              <ELEMENTS>
                <CLIENT-SERVER-INTERFACE UUID="94e68041-3428-4210-9bf5-d5329a1f847e">
                  <SHORT-NAME>DETServiceLegacy</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>DEVELOPMENT-ERROR-TRACER</SERVICE-KIND>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="3db98f60-c4a2-4313-8882-62d127a84fea">
                      <SHORT-NAME>ReportError</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="f1ca00ef-68a0-449b-b58a-650610faefdb">
                          <SHORT-NAME>InstanceId</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="01319751-ee41-47a1-a92b-0657d3450c1a">
                          <SHORT-NAME>ApiId</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="b18d139a-fc21-4969-ae58-da9dad48d8cd">
                          <SHORT-NAME>ErrorId</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/Det_swc/Interfaces/DETServiceLegacy/E_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                  <POSSIBLE-ERRORS>
                    <APPLICATION-ERROR UUID="d1aed240-0015-4afa-a34b-c64b6cdc342a">
                      <SHORT-NAME>E_OK</SHORT-NAME>
                      <ERROR-CODE>0</ERROR-CODE>
                    </APPLICATION-ERROR>
                  </POSSIBLE-ERRORS>
                </CLIENT-SERVER-INTERFACE>
                <CLIENT-SERVER-INTERFACE UUID="c1a94f5f-b4a3-4abb-ba0c-c4a56f3685b2">
                  <SHORT-NAME>DETService</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>DEVELOPMENT-ERROR-TRACER</SERVICE-KIND>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="0b1fa460-a3ac-4832-aa49-9c854b171de1">
                      <SHORT-NAME>ReportError</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="3df40df7-6025-4f66-ad7b-38b402e90d42">
                          <SHORT-NAME>ApiId</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="dac05d2f-2bdf-46e6-b0d4-3236f2637f07">
                          <SHORT-NAME>ErrorId</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/Det_swc/Interfaces/DETService/E_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="ac03ecf3-1b6e-471f-ad79-bf97b9d1163d">
                      <SHORT-NAME>ReportRuntimeError</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="b270eeac-0b16-42d5-8b57-5031b1405a12">
                          <SHORT-NAME>ApiId</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="84686e57-55d6-4efa-b341-1a4265d8f7bf">
                          <SHORT-NAME>ErrorId</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/Det_swc/Interfaces/DETService/E_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                  <POSSIBLE-ERRORS>
                    <APPLICATION-ERROR UUID="fc407712-8384-4784-a8f7-1d695ecd8bc0">
                      <SHORT-NAME>E_OK</SHORT-NAME>
                      <ERROR-CODE>0</ERROR-CODE>
                    </APPLICATION-ERROR>
                  </POSSIBLE-ERRORS>
                </CLIENT-SERVER-INTERFACE>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="d3e0619e-9b29-4b96-aaf2-52470f5a4a44">
              <SHORT-NAME>ComponentTypes</SHORT-NAME>
              <ELEMENTS>
                <SERVICE-SW-COMPONENT-TYPE UUID="badc516f-19be-48c9-8072-2a79f619baa8">
                  <SHORT-NAME>Det</SHORT-NAME>
                  <CATEGORY>SERVICE_COMPONENT</CATEGORY>
                  <INTERNAL-BEHAVIORS>
                    <SWC-INTERNAL-BEHAVIOR UUID="afc28f84-8952-4bde-8604-972a00067891">
                      <SHORT-NAME>DetInternalBehavior</SHORT-NAME>
                      <HANDLE-TERMINATION-AND-RESTART>NO-SUPPORT</HANDLE-TERMINATION-AND-RESTART>
                      <SUPPORTS-MULTIPLE-INSTANTIATION>false</SUPPORTS-MULTIPLE-INSTANTIATION>
                    </SWC-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </SERVICE-SW-COMPONENT-TYPE>
                <SWC-IMPLEMENTATION UUID="a2b002f9-5297-4e76-9e00-7530aac957f1">
                  <SHORT-NAME>DetImplementation</SHORT-NAME>
                  <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
                  <BEHAVIOR-REF DEST="SWC-INTERNAL-BEHAVIOR">/MICROSAR/Det_swc/ComponentTypes/Det/DetInternalBehavior</BEHAVIOR-REF>
                </SWC-IMPLEMENTATION>
              </ELEMENTS>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
