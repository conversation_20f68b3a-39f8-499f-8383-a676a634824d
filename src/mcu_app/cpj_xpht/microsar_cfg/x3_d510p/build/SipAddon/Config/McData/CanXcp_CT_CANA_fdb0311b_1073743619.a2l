/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: Xcp
 *           Program: VAG AR4 (MSR_Vag_SLP5)
 *          Customer: SZ DJI Technologies Co., Ltd.
 *       Expiry Date: 2024-08-31
 *  Ordered Derivat.: TDA4VM
 *    License Scope : The usage is restricted to CBD2000702_D00
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: CanXcp_CT_CANA_fdb0311b_1073743619.a2l
 *   Generation Time: 2021-06-06 17:55:51
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000702_D00
 *      Tool Version: DaVinci Configurator (beta) 5.22.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/


    /begin XCP_ON_CAN
      0x0102 /* XCP on CAN version */
      CAN_ID_MASTER   1073743619 /* CMD/STIM */
      CAN_ID_SLAVE    1073743620 /* RES/ERR/EV/SERV/DAQ */
      BAUDRATE        500000 /* BAUDRATE */
      SAMPLE_POINT    80 /* SAMPLE_POINT */
      SAMPLE_RATE     SINGLE /* SAMPLE_RATE */
      BTL_CYCLES      10 /* BTL_CYCLES */
      SJW             1 /* length synchr. segment */
            /begin CAN_FD
        MAX_DLC                         64 /* DLC of CAN frame */
        CAN_FD_DATA_TRANSFER_BAUDRATE   500000 /* Baudrate [Hz] */
        SAMPLE_POINT                    80 /* Sample Point Receiver */
        BTL_CYCLES                      5 /* BTL_CYCLES */
        SJW                             1 /* length synchr. segment */
        
        SECONDARY_SAMPLE_POINT          0 /* Sender Sample Point */
        TRANSCEIVER_DELAY_COMPENSATION  OFF
      /end CAN_FD
      /begin PROTOCOL_LAYER 
        0x0101 
        2000
        10000
        2000
        2000
        2000
        2000
        0
        64 /* kXcpMaxCto */
        64 /* kXcpMaxDto */
        BYTE_ORDER_MSB_LAST /* Byte Order */
        ADDRESS_GRANULARITY_BYTE
        OPTIONAL_CMD SET_MTA
        OPTIONAL_CMD UPLOAD
        OPTIONAL_CMD SHORT_UPLOAD
        OPTIONAL_CMD DOWNLOAD
        OPTIONAL_CMD DOWNLOAD_MAX        
        OPTIONAL_CMD SET_DAQ_PTR
        OPTIONAL_CMD WRITE_DAQ
        OPTIONAL_CMD SET_DAQ_LIST_MODE
        OPTIONAL_CMD GET_DAQ_LIST_MODE
        OPTIONAL_CMD START_STOP_DAQ_LIST
        OPTIONAL_CMD START_STOP_SYNCH
        OPTIONAL_CMD GET_DAQ_PROCESSOR_INFO
        OPTIONAL_CMD FREE_DAQ
        OPTIONAL_CMD ALLOC_DAQ
        OPTIONAL_CMD ALLOC_ODT
        OPTIONAL_CMD ALLOC_ODT_ENTRY
      /end PROTOCOL_LAYER        
    /end XCP_ON_CAN

