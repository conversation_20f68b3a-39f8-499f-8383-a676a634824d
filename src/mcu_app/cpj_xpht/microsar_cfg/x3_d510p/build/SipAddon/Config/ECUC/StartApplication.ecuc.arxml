<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>ActiveEcuC</SHORT-NAME>
      <ELEMENTS>
        <ECUC-VALUE-COLLECTION>
          <SHORT-NAME>ActiveEcuC</SHORT-NAME>
          <ECU-EXTRACT-REF DEST="SYSTEM">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM</ECU-EXTRACT-REF>
          <ECUC-VALUES>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Com</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Rte</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/EcuM</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Os</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/PduR</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/CanSM</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/CanTp</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/CanIf</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Det</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/ComM</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/EcuC</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/BswM</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/vSet</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Crc</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/vLinkGen</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/vBRS</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/vBaseEnv</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Mcu</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Dem</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Dcm</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Dio</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/WdgM</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/WdgIf</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Wdg</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/IoHwAb</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Adc</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Spi</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Fls</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Gpt</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Pwm</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Cdd_Ipc</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/MemIf</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Fee</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/NvM</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/CanTSyn</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/StbM</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/EthIf</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/EthTrcv</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/EthSM</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/SoAd</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/TcpIp</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Xcp</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Eth</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Can</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
          </ECUC-VALUES>
        </ECUC-VALUE-COLLECTION>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
