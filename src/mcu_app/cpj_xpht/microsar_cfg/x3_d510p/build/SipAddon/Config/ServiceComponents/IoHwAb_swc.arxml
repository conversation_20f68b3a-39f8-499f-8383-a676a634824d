<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
      <SHORT-NAME>MICROSAR</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="1a8fc9ce-ad9b-41b0-b0bc-bc4ec7123585">
          <SHORT-NAME>IoHwAb_swc</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="180f2455-0b1a-47ba-9b7a-b722a7e2b5af">
              <SHORT-NAME>DataTypes</SHORT-NAME>
              <ELEMENTS>
                <IMPLEMENTATION-DATA-TYPE UUID="8894386b-84e5-45ee-b74e-08ba3411629a">
                  <SHORT-NAME>IOHWAB_UINT16</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/IoHwAb_swc/DataTypes/DataConstrs/IOHWAB_UINT16_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="519f866b-94c8-4a4e-ac9c-6ae535a386f4">
                  <SHORT-NAME>IOHWAB_UINT8</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/IoHwAb_swc/DataTypes/DataConstrs/IOHWAB_UINT8_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="b12ad783-a377-48d5-82f7-7d28b7ef4ce2">
                  <SHORT-NAME>IOHWAB_BOOL</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="3edc94a9-1291-4721-b40c-51434c7d72d0">
                  <SHORT-NAME>IoHwAb_AdcChannelType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/IoHwAb_swc/DataTypes/IOHWAB_UINT8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="1ef69758-4cbf-4e96-833e-f78e647584c3">
                  <SHORT-NAME>IoHwAb_AdcChannelValueType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/IoHwAb_swc/DataTypes/IOHWAB_UINT16</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="4b40a348-9b31-480d-8031-f82c061bb2fa">
                  <SHORT-NAME>IoHwAb_DioChannelType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/IoHwAb_swc/DataTypes/IOHWAB_UINT16</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="c26bbf2c-16f0-4d08-9193-ec9bf0223050">
                  <SHORT-NAME>IoHwAb_DioChannelValueType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/IoHwAb_swc/DataTypes/IOHWAB_BOOL</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
              </ELEMENTS>
              <AR-PACKAGES>
                <AR-PACKAGE UUID="0e249267-1ff3-4154-a27a-de8eab8bcc4c">
                  <SHORT-NAME>DataConstrs</SHORT-NAME>
                  <ELEMENTS>
                    <DATA-CONSTR UUID="b2da6a0e-bb44-4165-9cff-d249cfa3fb7e">
                      <SHORT-NAME>IOHWAB_UINT16_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">65535</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="b7150d66-119f-4ed4-8242-4d9c1810adf0">
                      <SHORT-NAME>IOHWAB_UINT8_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">255</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                  </ELEMENTS>
                </AR-PACKAGE>
              </AR-PACKAGES>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="45a70288-9ebb-4a18-8eaa-4de0f2e1c112">
              <SHORT-NAME>Interfaces</SHORT-NAME>
              <ELEMENTS>
                <CLIENT-SERVER-INTERFACE UUID="e39cef69-b4e4-43b3-86ba-bdcc085c89ef">
                  <SHORT-NAME>PiIoHwAbAdc</SHORT-NAME>
                  <IS-SERVICE>false</IS-SERVICE>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="0cffc458-f4cb-474b-afb2-4a62fb7056c6">
                      <SHORT-NAME>ReadChannel</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="5f7b604d-38f4-47b4-bb25-8d339566aa6a">
                          <SHORT-NAME>AdcChannel</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/IoHwAb_swc/DataTypes/IoHwAb_AdcChannelType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="7d82aeba-67f3-460f-912f-4d4084729347">
                          <SHORT-NAME>AdcValue</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/IoHwAb_swc/DataTypes/IoHwAb_AdcChannelValueType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/IoHwAb_swc/Interfaces/PiIoHwAbAdc/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                  <POSSIBLE-ERRORS>
                    <APPLICATION-ERROR UUID="15b0d7c1-1e03-49dc-a093-0341ac14b765">
                      <SHORT-NAME>E_NOT_OK</SHORT-NAME>
                      <ERROR-CODE>1</ERROR-CODE>
                    </APPLICATION-ERROR>
                  </POSSIBLE-ERRORS>
                </CLIENT-SERVER-INTERFACE>
                <CLIENT-SERVER-INTERFACE UUID="4a0e5e39-fbde-4ad7-9613-b683ddfbe2c5">
                  <SHORT-NAME>PiIoHwDio</SHORT-NAME>
                  <IS-SERVICE>false</IS-SERVICE>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="485abf4a-68da-4e26-93ad-5d73f1df09ae">
                      <SHORT-NAME>ReadChannel</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="63d2bb99-53cc-402f-a75c-6e9fd3615748">
                          <SHORT-NAME>DioChannel</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/IoHwAb_swc/DataTypes/IoHwAb_DioChannelType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="9bb0ffd4-b0bd-4bb3-94af-213ff272faf1">
                          <SHORT-NAME>DioValue</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/IoHwAb_swc/DataTypes/IoHwAb_DioChannelValueType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/IoHwAb_swc/Interfaces/PiIoHwDio/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="7975adf7-6012-4298-b52a-71c07626e01d">
                      <SHORT-NAME>WriteChannel</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="553e4b42-46f2-4950-b249-fa60b74cbe51">
                          <SHORT-NAME>DioChannel</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/IoHwAb_swc/DataTypes/IoHwAb_DioChannelType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="98cacb3b-94e4-4484-9c49-9ae373e2069f">
                          <SHORT-NAME>DioValue</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/IoHwAb_swc/DataTypes/IoHwAb_DioChannelValueType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/IoHwAb_swc/Interfaces/PiIoHwDio/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                  <POSSIBLE-ERRORS>
                    <APPLICATION-ERROR UUID="7d78418e-9d24-426d-8c50-72043124e956">
                      <SHORT-NAME>E_NOT_OK</SHORT-NAME>
                      <ERROR-CODE>1</ERROR-CODE>
                    </APPLICATION-ERROR>
                  </POSSIBLE-ERRORS>
                </CLIENT-SERVER-INTERFACE>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="65dde847-1575-4097-b395-f0144cabb580">
              <SHORT-NAME>ComponentTypes</SHORT-NAME>
              <ELEMENTS>
                <ECU-ABSTRACTION-SW-COMPONENT-TYPE UUID="0ae8befa-0c11-49d8-9ff2-450ba611bb25">
                  <SHORT-NAME>IoHwAb</SHORT-NAME>
                  <PORTS>
                    <P-PORT-PROTOTYPE UUID="2f4cc4ec-9eda-4a80-821f-8d754ff440c5">
                      <SHORT-NAME>PpIoHwAbAdc</SHORT-NAME>
                      <PROVIDED-INTERFACE-TREF DEST="CLIENT-SERVER-INTERFACE">/MICROSAR/IoHwAb_swc/Interfaces/PiIoHwAbAdc</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="34f8a58d-3b0e-4c19-b795-f6f8a4f74142">
                      <SHORT-NAME>PpIoHwAbDio</SHORT-NAME>
                      <PROVIDED-INTERFACE-TREF DEST="CLIENT-SERVER-INTERFACE">/MICROSAR/IoHwAb_swc/Interfaces/PiIoHwDio</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                  </PORTS>
                  <INTERNAL-BEHAVIORS>
                    <SWC-INTERNAL-BEHAVIOR UUID="85e9defb-c1d6-41d9-a60e-0603a06426c6">
                      <SHORT-NAME>IoHwAbInternalBehavior</SHORT-NAME>
                      <EVENTS>
                        <OPERATION-INVOKED-EVENT UUID="7e85ade5-f4de-4ffa-8a4d-59325e324c67">
                          <SHORT-NAME>OpEventIoHwAb_PpIoHwAbAdc_ReadChannel_ReadChannel_PpIoHwAbAdc</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/IoHwAb_swc/ComponentTypes/IoHwAb/IoHwAbInternalBehavior/IoHwAb_PpIoHwAbAdc_ReadChannel</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/IoHwAb_swc/ComponentTypes/IoHwAb/PpIoHwAbAdc</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/IoHwAb_swc/Interfaces/PiIoHwAbAdc/ReadChannel</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="4dc21820-b918-42ac-8ed9-ea676fe4e8f7">
                          <SHORT-NAME>OpEventIoHwAb_PpIoHwAbDio_ReadChannel_ReadChannel_PpIoHwAbDio</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/IoHwAb_swc/ComponentTypes/IoHwAb/IoHwAbInternalBehavior/IoHwAb_PpIoHwAbDio_ReadChannel</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/IoHwAb_swc/ComponentTypes/IoHwAb/PpIoHwAbDio</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/IoHwAb_swc/Interfaces/PiIoHwDio/ReadChannel</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="854d45ee-be72-44eb-85e4-28161092d217">
                          <SHORT-NAME>OpEventIoHwAb_PpIoHwAbDio_WriteChannel_WriteChannel_PpIoHwAbDio</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/IoHwAb_swc/ComponentTypes/IoHwAb/IoHwAbInternalBehavior/IoHwAb_PpIoHwAbDio_WriteChannel</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/IoHwAb_swc/ComponentTypes/IoHwAb/PpIoHwAbDio</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/IoHwAb_swc/Interfaces/PiIoHwDio/WriteChannel</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <TIMING-EVENT UUID="4a74d80f-d4f4-4c24-8afd-33e5b8c1692c">
                          <SHORT-NAME>Timer_IoHwAb_IoHwAbRunnable</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/IoHwAb_swc/ComponentTypes/IoHwAb/IoHwAbInternalBehavior/IoHwAb_IoHwAbRunnable</START-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </TIMING-EVENT>
                      </EVENTS>
                      <HANDLE-TERMINATION-AND-RESTART>NO-SUPPORT</HANDLE-TERMINATION-AND-RESTART>
                      <PORT-API-OPTIONS>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/IoHwAb_swc/ComponentTypes/IoHwAb/PpIoHwAbAdc</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/IoHwAb_swc/ComponentTypes/IoHwAb/PpIoHwAbDio</PORT-REF>
                        </PORT-API-OPTION>
                      </PORT-API-OPTIONS>
                      <RUNNABLES>
                        <RUNNABLE-ENTITY UUID="7a9ff2d1-1935-49d0-904b-b20c28efd164">
                          <SHORT-NAME>IoHwAb_PpIoHwAbAdc_ReadChannel</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Runnable Entity for calling the Operation 'ReadChannel' of the Client/Server Port Prototype 'PpIoHwAbAdc'.</L-2>
                          </DESC>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>IoHwAb_PpIoHwAbAdc_ReadChannel</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="ea452bdc-480f-4812-a60c-d56b15934336">
                          <SHORT-NAME>IoHwAb_PpIoHwAbDio_ReadChannel</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Runnable Entity for calling the Operation 'ReadChannel' of the Client/Server Port Prototype 'PpIoHwAbDio'.</L-2>
                          </DESC>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>IoHwAb_PpIoHwAbDio_ReadChannel</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="9c24121a-7c0e-4fb0-8bc9-b6b018816e83">
                          <SHORT-NAME>IoHwAb_PpIoHwAbDio_WriteChannel</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Runnable Entity for calling the Operation 'WriteChannel' of the Client/Server Port Prototype 'PpIoHwAbDio'.</L-2>
                          </DESC>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>IoHwAb_PpIoHwAbDio_WriteChannel</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="6a7ece0b-1a93-4248-a25e-ff329a262e7a">
                          <SHORT-NAME>IoHwAb_IoHwAbRunnable</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>IoHwAb_IoHwAbRunnable</SYMBOL>
                        </RUNNABLE-ENTITY>
                      </RUNNABLES>
                      <SUPPORTS-MULTIPLE-INSTANTIATION>false</SUPPORTS-MULTIPLE-INSTANTIATION>
                    </SWC-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </ECU-ABSTRACTION-SW-COMPONENT-TYPE>
                <SWC-IMPLEMENTATION UUID="6a66f852-4bc7-4390-88e9-b7cf3c00cff9">
                  <SHORT-NAME>IoHwAbImplementation</SHORT-NAME>
                  <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
                  <BEHAVIOR-REF DEST="SWC-INTERNAL-BEHAVIOR">/MICROSAR/IoHwAb_swc/ComponentTypes/IoHwAb/IoHwAbInternalBehavior</BEHAVIOR-REF>
                </SWC-IMPLEMENTATION>
              </ELEMENTS>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
