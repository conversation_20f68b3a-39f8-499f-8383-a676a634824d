<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
      <SHORT-NAME>MICROSAR</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="53c3a3f7-aad1-4b78-804e-634f8c429c94">
          <SHORT-NAME>IoHwAb_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="e6cca5e4-19ee-4579-8e4d-5514f8560b27">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="1c585300-68f2-4dee-901c-7efec8080276">
                  <SHORT-NAME>IoHwAb</SHORT-NAME>
                  <PROVIDED-ENTRYS>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/IoHwAb_ib_bswmd/BswModuleDescriptions/IoHwAb_MainFunction_IoHwAbSchedulable</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  </PROVIDED-ENTRYS>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="fd042bdb-a888-43ca-b1ef-b1e08e1a25a9">
                      <SHORT-NAME>IoHwAbBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="cd46dc7e-aa82-4232-869c-ce1f907d0085">
                          <SHORT-NAME>IOHWAB_EXCLUSIVE_AREA_0</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="8f10c116-4bae-44f1-a9cd-6d35fc034262">
                          <SHORT-NAME>IoHwAb_MainFunction_IoHwAbSchedulable</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/IoHwAb_ib_bswmd/BswModuleDescriptions/IoHwAb_MainFunction_IoHwAbSchedulable</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="521b2fb8-0c36-4060-beea-1baa144f5092">
                          <SHORT-NAME>IoHwAb_MainFunction_IoHwAbSchedulableTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/IoHwAb_ib_bswmd/BswModuleDescriptions/IoHwAb/IoHwAbBehavior/IoHwAb_MainFunction_IoHwAbSchedulable</STARTS-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="17eedd19-67c4-44ba-8f34-270d93fe4b41">
                  <SHORT-NAME>IoHwAb_MainFunction_IoHwAbSchedulable</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
