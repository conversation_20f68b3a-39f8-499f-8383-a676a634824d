<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
      <SHORT-NAME>MICROSAR</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="7871e510-6a60-4b3b-98bd-b11953af2362">
          <SHORT-NAME>TcpIp_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="c8750436-64aa-4259-8e16-4bbd5f2acb02">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="a875ae3c-f9ca-4c54-839f-b59f51319618">
                  <SHORT-NAME>TcpIp</SHORT-NAME>
                  <PROVIDED-ENTRYS>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/TcpIp_ib_bswmd/BswModuleDescriptions/TcpIp_MainFunction</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/TcpIp_ib_bswmd/BswModuleDescriptions/TcpIp_MainFunctionRx</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/TcpIp_ib_bswmd/BswModuleDescriptions/TcpIp_MainFunctionTx</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/TcpIp_ib_bswmd/BswModuleDescriptions/TcpIp_MainFunctionState</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  </PROVIDED-ENTRYS>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="20a27636-a493-4000-9cbb-3e9fbd1a97aa">
                      <SHORT-NAME>TcpIpBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="223a3f28-67f6-428b-b740-736520b99e95">
                          <SHORT-NAME>TCPIP_EXCLUSIVE_AREA_0</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="cc0cfbde-6e57-4008-a5a1-0267211ff75f">
                          <SHORT-NAME>TcpIp_MainFunctionRx</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/TcpIp_ib_bswmd/BswModuleDescriptions/TcpIp_MainFunctionRx</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                        <BSW-SCHEDULABLE-ENTITY UUID="ccf468c5-ba8c-44ec-9905-00d365419f67">
                          <SHORT-NAME>TcpIp_MainFunctionTx</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/TcpIp_ib_bswmd/BswModuleDescriptions/TcpIp_MainFunctionTx</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                        <BSW-SCHEDULABLE-ENTITY UUID="f7e31d24-9486-404a-9eb0-399bef7ddaac">
                          <SHORT-NAME>TcpIp_MainFunctionState</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/TcpIp_ib_bswmd/BswModuleDescriptions/TcpIp_MainFunctionState</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="7ff30c5e-6f0b-46af-9924-deac3e2f378c">
                          <SHORT-NAME>TcpIp_MainFunctionRxTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/TcpIp_ib_bswmd/BswModuleDescriptions/TcpIp/TcpIpBehavior/TcpIp_MainFunctionRx</STARTS-ON-EVENT-REF>
                          <PERIOD>0.005</PERIOD>
                        </BSW-TIMING-EVENT>
                        <BSW-TIMING-EVENT UUID="31834b1c-7425-4e24-84ef-a67940496eee">
                          <SHORT-NAME>TcpIp_MainFunctionTxTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/TcpIp_ib_bswmd/BswModuleDescriptions/TcpIp/TcpIpBehavior/TcpIp_MainFunctionTx</STARTS-ON-EVENT-REF>
                          <PERIOD>0.005</PERIOD>
                        </BSW-TIMING-EVENT>
                        <BSW-TIMING-EVENT UUID="98245791-180b-4907-acff-e22796df99bc">
                          <SHORT-NAME>TcpIp_MainFunctionStateTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/TcpIp_ib_bswmd/BswModuleDescriptions/TcpIp/TcpIpBehavior/TcpIp_MainFunctionState</STARTS-ON-EVENT-REF>
                          <PERIOD>0.005</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="35a6dea9-9e33-43c0-971a-f1cd4a8bf9e7">
                  <SHORT-NAME>TcpIp_MainFunction</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
                <BSW-MODULE-ENTRY UUID="7f6ad857-c844-401b-bf6e-d1d3ceca1270">
                  <SHORT-NAME>TcpIp_MainFunctionRx</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
                <BSW-MODULE-ENTRY UUID="eab0873d-8ba1-4fe9-9b07-d54cabe6f6ab">
                  <SHORT-NAME>TcpIp_MainFunctionTx</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
                <BSW-MODULE-ENTRY UUID="27b9f9a3-eb0b-4d09-86cd-7c473c8cd3f9">
                  <SHORT-NAME>TcpIp_MainFunctionState</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
