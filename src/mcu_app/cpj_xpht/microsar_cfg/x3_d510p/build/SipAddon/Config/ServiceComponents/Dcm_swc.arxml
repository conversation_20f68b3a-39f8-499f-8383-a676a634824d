<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
      <SHORT-NAME>MICROSAR</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="c921671d-8c5c-4021-b2a1-096420836db0">
          <SHORT-NAME>Dcm_swc</SHORT-NAME>
          <ELEMENTS>
            <MODE-DECLARATION-GROUP UUID="bbe44d1a-5fd8-4625-8baa-0e005a5c3fa4">
              <SHORT-NAME>DcmControlDtcSetting</SHORT-NAME>
              <CATEGORY>EXPLICIT_ORDER</CATEGORY>
              <INITIAL-MODE-REF DEST="MODE-DECLARATION">/MICROSAR/Dcm_swc/DcmControlDtcSetting/ENABLEDTCSETTING</INITIAL-MODE-REF>
              <MODE-DECLARATIONS>
                <MODE-DECLARATION UUID="e1ee6f6e-dce8-4ec5-a222-3964509a6ae7">
                  <SHORT-NAME>ENABLEDTCSETTING</SHORT-NAME>
                  <VALUE>0</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="a08f1cc0-6bc4-47c3-975b-596aaf021401">
                  <SHORT-NAME>DISABLEDTCSETTING</SHORT-NAME>
                  <VALUE>1</VALUE>
                </MODE-DECLARATION>
              </MODE-DECLARATIONS>
              <ON-TRANSITION-VALUE>2</ON-TRANSITION-VALUE>
            </MODE-DECLARATION-GROUP>
            <MODE-DECLARATION-GROUP UUID="19cac56d-8bbe-4060-94df-f3c3a7f8b47b">
              <SHORT-NAME>DcmCommunicationControl</SHORT-NAME>
              <CATEGORY>EXPLICIT_ORDER</CATEGORY>
              <INITIAL-MODE-REF DEST="MODE-DECLARATION">/MICROSAR/Dcm_swc/DcmCommunicationControl/DCM_ENABLE_RX_TX_NORM</INITIAL-MODE-REF>
              <MODE-DECLARATIONS>
                <MODE-DECLARATION UUID="b0ffdb1f-69ce-4e2e-937e-7e6cabb2111a">
                  <SHORT-NAME>DCM_ENABLE_RX_TX_NORM</SHORT-NAME>
                  <VALUE>0</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="337f2e79-6577-42c0-8c16-c3395114ee3c">
                  <SHORT-NAME>DCM_ENABLE_RX_DISABLE_TX_NORM</SHORT-NAME>
                  <VALUE>1</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="7831e4d3-e62d-42d7-b063-5114db4c77c7">
                  <SHORT-NAME>DCM_DISABLE_RX_ENABLE_TX_NORM</SHORT-NAME>
                  <VALUE>2</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="bf384581-85c3-49fd-ba30-bd469f3d68a9">
                  <SHORT-NAME>DCM_DISABLE_RX_TX_NORMAL</SHORT-NAME>
                  <VALUE>3</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="f57cf45f-505d-480e-9b6f-7bf6e0a79c68">
                  <SHORT-NAME>DCM_ENABLE_RX_TX_NM</SHORT-NAME>
                  <VALUE>4</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="4108d208-a440-4222-8454-7e1d07220fd4">
                  <SHORT-NAME>DCM_ENABLE_RX_DISABLE_TX_NM</SHORT-NAME>
                  <VALUE>5</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="5fd64cd3-034a-49fc-963b-0d8333b6434e">
                  <SHORT-NAME>DCM_DISABLE_RX_ENABLE_TX_NM</SHORT-NAME>
                  <VALUE>6</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="23d74b81-1495-4416-9db6-16baf3052997">
                  <SHORT-NAME>DCM_DISABLE_RX_TX_NM</SHORT-NAME>
                  <VALUE>7</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="0c46b95d-c774-43de-b295-726c928d9589">
                  <SHORT-NAME>DCM_ENABLE_RX_TX_NORM_NM</SHORT-NAME>
                  <VALUE>8</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="82990e0d-574e-4e6b-85ef-17700906f2aa">
                  <SHORT-NAME>DCM_ENABLE_RX_DISABLE_TX_NORM_NM</SHORT-NAME>
                  <VALUE>9</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="67b1e913-9f08-4d70-8dd4-04e29c664a8d">
                  <SHORT-NAME>DCM_DISABLE_RX_ENABLE_TX_NORM_NM</SHORT-NAME>
                  <VALUE>10</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="b5ebd90c-1026-4c9f-a5ee-199cf3553c7f">
                  <SHORT-NAME>DCM_DISABLE_RX_TX_NORM_NM</SHORT-NAME>
                  <VALUE>11</VALUE>
                </MODE-DECLARATION>
              </MODE-DECLARATIONS>
              <ON-TRANSITION-VALUE>12</ON-TRANSITION-VALUE>
            </MODE-DECLARATION-GROUP>
            <MODE-DECLARATION-GROUP UUID="ddc7a286-c0b7-4794-83c2-6c4ae717eacf">
              <SHORT-NAME>DcmDiagnosticSessionControl</SHORT-NAME>
              <CATEGORY>EXPLICIT_ORDER</CATEGORY>
              <INITIAL-MODE-REF DEST="MODE-DECLARATION">/MICROSAR/Dcm_swc/DcmDiagnosticSessionControl/DEFAULT_SESSION</INITIAL-MODE-REF>
              <MODE-DECLARATIONS>
                <MODE-DECLARATION UUID="ca1ded1b-e5a6-456b-82eb-5139ac578e6d">
                  <SHORT-NAME>DEFAULT_SESSION</SHORT-NAME>
                  <VALUE>1</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="a3cd8133-87c0-45de-a0d4-82900f9ef2d1">
                  <SHORT-NAME>PROGRAMMING_SESSION</SHORT-NAME>
                  <VALUE>2</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="6c546e0f-8bb9-414c-9793-3a823e22d1ff">
                  <SHORT-NAME>EXTENDED_SESSION</SHORT-NAME>
                  <VALUE>3</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="99aa872a-0262-406b-bb0c-9512dad212cc">
                  <SHORT-NAME>ZXExtended</SHORT-NAME>
                  <VALUE>96</VALUE>
                </MODE-DECLARATION>
              </MODE-DECLARATIONS>
              <ON-TRANSITION-VALUE>255</ON-TRANSITION-VALUE>
            </MODE-DECLARATION-GROUP>
            <DATA-TYPE-MAPPING-SET UUID="788c78de-ce7a-4b98-b607-c60b5a606909">
              <SHORT-NAME>DcmMappingSet</SHORT-NAME>
              <DESC>
                <L-2 L="FOR-ALL">Contains mapping for ModeDeclarationGroups which are provided by Dcm.</L-2>
              </DESC>
              <MODE-REQUEST-TYPE-MAPS>
                <MODE-REQUEST-TYPE-MAP>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/Dcm_swc/DataTypes/Dcm_ControlDtcSettingType</IMPLEMENTATION-DATA-TYPE-REF>
                  <MODE-GROUP-REF DEST="MODE-DECLARATION-GROUP">/MICROSAR/Dcm_swc/DcmControlDtcSetting</MODE-GROUP-REF>
                </MODE-REQUEST-TYPE-MAP>
                <MODE-REQUEST-TYPE-MAP>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/Dcm_swc/DataTypes/Dcm_DiagnosticSessionControlType</IMPLEMENTATION-DATA-TYPE-REF>
                  <MODE-GROUP-REF DEST="MODE-DECLARATION-GROUP">/MICROSAR/Dcm_swc/DcmDiagnosticSessionControl</MODE-GROUP-REF>
                </MODE-REQUEST-TYPE-MAP>
                <MODE-REQUEST-TYPE-MAP>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/Dcm_swc/DataTypes/Dcm_CommunicationModeType</IMPLEMENTATION-DATA-TYPE-REF>
                  <MODE-GROUP-REF DEST="MODE-DECLARATION-GROUP">/MICROSAR/Dcm_swc/DcmCommunicationControl</MODE-GROUP-REF>
                </MODE-REQUEST-TYPE-MAP>
              </MODE-REQUEST-TYPE-MAPS>
            </DATA-TYPE-MAPPING-SET>
          </ELEMENTS>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="5be8b8bf-0a1c-416e-bf42-62e036dc2197">
              <SHORT-NAME>DataTypes</SHORT-NAME>
              <ELEMENTS>
                <IMPLEMENTATION-DATA-TYPE UUID="34ef4360-45c4-454b-aa23-61eee0baefcf">
                  <SHORT-NAME>Dcm_ConfirmationStatusType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/MICROSAR/Dcm_swc/DataTypes/CompuMethods/Dcm_ConfirmationStatusType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/Dcm_swc/DataTypes/DataConstrs/Dcm_ConfirmationStatusType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="7b76f1ff-264a-48ad-bf98-ec880f10a9f4">
                  <SHORT-NAME>Dcm_OpStatusType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/MICROSAR/Dcm_swc/DataTypes/CompuMethods/Dcm_OpStatusType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/Dcm_swc/DataTypes/DataConstrs/Dcm_OpStatusType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="b3d42828-eb9c-4863-b762-87b2a7c20b35">
                  <SHORT-NAME>Dcm_NegativeResponseCodeType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/MICROSAR/Dcm_swc/DataTypes/CompuMethods/Dcm_NegativeResponseCodeType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/Dcm_swc/DataTypes/DataConstrs/Dcm_NegativeResponseCodeType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="1dd3bac5-f341-4505-ba46-53300b51d422">
                  <SHORT-NAME>Dcm_ProtocolType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/MICROSAR/Dcm_swc/DataTypes/CompuMethods/Dcm_ProtocolType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/Dcm_swc/DataTypes/DataConstrs/Dcm_ProtocolType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="8f566681-59dc-4595-9813-39ef20d72b34">
                  <SHORT-NAME>Dcm_RequestKindType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/MICROSAR/Dcm_swc/DataTypes/CompuMethods/Dcm_RequestKindType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/Dcm_swc/DataTypes/DataConstrs/Dcm_RequestKindType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="88d330fd-f090-43bf-945a-8eb2b0fce14e">
                  <SHORT-NAME>Dcm_ControlDtcSettingType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/MICROSAR/Dcm_swc/DataTypes/CompuMethods/Dcm_ControlDtcSettingType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/Dcm_swc/DataTypes/DataConstrs/Dcm_ControlDtcSettingType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="d7424cad-7c02-4d8b-a305-892136d32b78">
                  <SHORT-NAME>Dcm_CommunicationModeType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/MICROSAR/Dcm_swc/DataTypes/CompuMethods/Dcm_CommunicationModeType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/Dcm_swc/DataTypes/DataConstrs/Dcm_CommunicationModeType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="3ffe8071-bb1a-439e-a9bf-d161830d7171">
                  <SHORT-NAME>Dcm_Data4095ByteType</SHORT-NAME>
                  <CATEGORY>ARRAY</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <SW-CALIBRATION-ACCESS>READ-ONLY</SW-CALIBRATION-ACCESS>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                  <SUB-ELEMENTS>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="a5193416-22ab-431c-ac39-74c083d4917a">
                      <SHORT-NAME>Dcm_Data4095ByteTypeElement</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <ARRAY-SIZE>4095</ARRAY-SIZE>
                      <ARRAY-SIZE-SEMANTICS>FIXED-SIZE</ARRAY-SIZE-SEMANTICS>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  </SUB-ELEMENTS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="b7c888cb-0791-48f3-8a71-2a889694ae56">
                  <SHORT-NAME>Dcm_SecLevelType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/MICROSAR/Dcm_swc/DataTypes/CompuMethods/Dcm_SecLevelType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/Dcm_swc/DataTypes/DataConstrs/Dcm_SecLevelType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="821069e6-8639-45cb-8481-a4d736deae3c">
                  <SHORT-NAME>Dcm_SesCtrlType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/MICROSAR/Dcm_swc/DataTypes/CompuMethods/Dcm_SesCtrlType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/Dcm_swc/DataTypes/DataConstrs/Dcm_SesCtrlType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="0f6bcf5c-f646-4120-a086-6ae8de2a3016">
                  <SHORT-NAME>Dcm_DiagnosticSessionControlType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/MICROSAR/Dcm_swc/DataTypes/CompuMethods/Dcm_DiagnosticSessionControlType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/Dcm_swc/DataTypes/DataConstrs/Dcm_DiagnosticSessionControlType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
              </ELEMENTS>
              <AR-PACKAGES>
                <AR-PACKAGE UUID="bbfb34fe-6a84-48b3-a106-24865e208c35">
                  <SHORT-NAME>DataConstrs</SHORT-NAME>
                  <ELEMENTS>
                    <DATA-CONSTR UUID="eedaeb33-bdb5-474b-a093-01c152958e13">
                      <SHORT-NAME>Dcm_ConfirmationStatusType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">3</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="2bd5408f-2ec6-4fc9-b27b-b34c242cac4b">
                      <SHORT-NAME>Dcm_OpStatusType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">64</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="6f4f38e0-0b2b-40cb-a23f-0323ee87e734">
                      <SHORT-NAME>Dcm_NegativeResponseCodeType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">255</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="ee49b1ee-965f-461f-af92-f738e8fbd188">
                      <SHORT-NAME>Dcm_ProtocolType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">254</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="576c4afb-1b9f-4c6c-a2b7-84c435fec217">
                      <SHORT-NAME>Dcm_RequestKindType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="1a33e7f4-9327-497a-85fb-e9b10e42224b">
                      <SHORT-NAME>Dcm_ControlDtcSettingType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">255</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="81691b72-5c31-40bc-96fa-488a0b377ce8">
                      <SHORT-NAME>Dcm_CommunicationModeType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">255</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="72f4ceb0-25c3-4232-ab52-cf0172593677">
                      <SHORT-NAME>Dcm_SecLevelType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">49</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="e632b3f0-562f-4e63-8dc8-767060956ee3">
                      <SHORT-NAME>Dcm_SesCtrlType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">96</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="2a88e48e-6f26-48eb-b4a9-6976033ce8c7">
                      <SHORT-NAME>Dcm_DiagnosticSessionControlType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">255</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                  </ELEMENTS>
                </AR-PACKAGE>
                <AR-PACKAGE UUID="900152a0-3f41-4891-9f69-91baea95f5fe">
                  <SHORT-NAME>CompuMethods</SHORT-NAME>
                  <ELEMENTS>
                    <COMPU-METHOD UUID="681837ec-4417-47b1-bad3-6e3d3f759f99">
                      <SHORT-NAME>Dcm_ConfirmationStatusType</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_RES_POS_OK</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_RES_POS_OK</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_RES_POS_NOT_OK</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_RES_POS_NOT_OK</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_RES_NEG_OK</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_RES_NEG_OK</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_RES_NEG_NOT_OK</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">3</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">3</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_RES_NEG_NOT_OK</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                    <COMPU-METHOD UUID="80da14bf-4950-48b8-af20-6e0cb97fc539">
                      <SHORT-NAME>Dcm_OpStatusType</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_INITIAL</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_INITIAL</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_PENDING</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_PENDING</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_CANCEL</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_CANCEL</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_FORCE_RCRRP_OK</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">3</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">3</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_FORCE_RCRRP_OK</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_FORCE_RCRRP_NOT_OK</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">64</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">64</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_FORCE_RCRRP_NOT_OK</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                    <COMPU-METHOD UUID="b22eac6a-1dbf-437d-9916-be49901a24b5">
                      <SHORT-NAME>Dcm_NegativeResponseCodeType</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_POSITIVERESPONSE</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_POSITIVERESPONSE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_GENERALREJECT</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">16</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">16</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_GENERALREJECT</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_SERVICENOTSUPPORTED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">17</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">17</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_SERVICENOTSUPPORTED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_SUBFUNCTIONNOTSUPPORTED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">18</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">18</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_SUBFUNCTIONNOTSUPPORTED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_INCORRECTMESSAGELENGTHORINVALIDFORMAT</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">19</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">19</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_INCORRECTMESSAGELENGTHORINVALIDFORMAT</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_RESPONSETOOLONG</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">20</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">20</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_RESPONSETOOLONG</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_BUSYREPEATREQUEST</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">33</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">33</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_BUSYREPEATREQUEST</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_CONDITIONSNOTCORRECT</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">34</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">34</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_CONDITIONSNOTCORRECT</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_REQUESTSEQUENCEERROR</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">36</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">36</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_REQUESTSEQUENCEERROR</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_NORESPONSEFROMSUBNETCOMPONENT</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">37</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">37</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_NORESPONSEFROMSUBNETCOMPONENT</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_FAILUREPREVENTSEXECUTIONOFREQUESTEDACTION</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">38</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">38</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_FAILUREPREVENTSEXECUTIONOFREQUESTEDACTION</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_REQUESTOUTOFRANGE</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">49</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">49</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_REQUESTOUTOFRANGE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_SECURITYACCESSDENIED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">51</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">51</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_SECURITYACCESSDENIED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_AUTHENTICATIONREQUIRED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">52</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">52</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_AUTHENTICATIONREQUIRED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_INVALIDKEY</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">53</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">53</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_INVALIDKEY</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_EXCEEDNUMBEROFATTEMPTS</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">54</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">54</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_EXCEEDNUMBEROFATTEMPTS</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_REQUIREDTIMEDELAYNOTEXPIRED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">55</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">55</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_REQUIREDTIMEDELAYNOTEXPIRED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_CVF_INVALIDTIMEPERIOD</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">80</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">80</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_CVF_INVALIDTIMEPERIOD</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_CVF_INVALIDSIGNATURE</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">81</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">81</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_CVF_INVALIDSIGNATURE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_CVF_INVALIDCHAINOFTRUST</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">82</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">82</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_CVF_INVALIDCHAINOFTRUST</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_CVF_INVALIDTYPE</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">83</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">83</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_CVF_INVALIDTYPE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_CVF_INVALIDFORMAT</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">84</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">84</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_CVF_INVALIDFORMAT</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_CVF_INVALIDCONTENT</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">85</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">85</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_CVF_INVALIDCONTENT</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_CVF_INVALIDSCOPE</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">86</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">86</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_CVF_INVALIDSCOPE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_CVF_INVALIDCERTIFICATEREVOKED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">87</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">87</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_CVF_INVALIDCERTIFICATEREVOKED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_OWNERSHIPVERIFICATIONFAILED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">88</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">88</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_OWNERSHIPVERIFICATIONFAILED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_CHALLENGECALCULATIONFAILED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">89</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">89</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_CHALLENGECALCULATIONFAILED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_UPLOADDOWNLOADNOTACCEPTED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">112</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">112</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_UPLOADDOWNLOADNOTACCEPTED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_TRANSFERDATASUSPENDED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">113</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">113</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_TRANSFERDATASUSPENDED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_GENERALPROGRAMMINGFAILURE</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">114</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">114</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_GENERALPROGRAMMINGFAILURE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_WRONGBLOCKSEQUENCECOUNTER</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">115</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">115</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_WRONGBLOCKSEQUENCECOUNTER</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_REQUESTCORRECTLYRECEIVEDRESPONSEPENDING</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">120</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">120</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_REQUESTCORRECTLYRECEIVEDRESPONSEPENDING</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_SUBFUNCTIONNOTSUPPORTEDINACTIVESESSION</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">126</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">126</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_SUBFUNCTIONNOTSUPPORTEDINACTIVESESSION</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_SERVICENOTSUPPORTEDINACTIVESESSION</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">127</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">127</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_SERVICENOTSUPPORTEDINACTIVESESSION</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_RPMTOOHIGH</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">129</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">129</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_RPMTOOHIGH</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_RPMTOOLOW</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">130</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">130</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_RPMTOOLOW</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_ENGINEISRUNNING</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">131</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">131</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_ENGINEISRUNNING</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_ENGINEISNOTRUNNING</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">132</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">132</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_ENGINEISNOTRUNNING</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_ENGINERUNTIMETOOLOW</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">133</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">133</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_ENGINERUNTIMETOOLOW</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_TEMPERATURETOOHIGH</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">134</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">134</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_TEMPERATURETOOHIGH</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_TEMPERATURETOOLOW</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">135</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">135</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_TEMPERATURETOOLOW</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VEHICLESPEEDTOOHIGH</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">136</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">136</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VEHICLESPEEDTOOHIGH</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VEHICLESPEEDTOOLOW</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">137</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">137</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VEHICLESPEEDTOOLOW</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_THROTTLE_PEDALTOOHIGH</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">138</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">138</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_THROTTLE_PEDALTOOHIGH</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_THROTTLE_PEDALTOOLOW</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">139</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">139</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_THROTTLE_PEDALTOOLOW</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_TRANSMISSIONRANGENOTINNEUTRAL</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">140</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">140</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_TRANSMISSIONRANGENOTINNEUTRAL</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_TRANSMISSIONRANGENOTINGEAR</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">141</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">141</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_TRANSMISSIONRANGENOTINGEAR</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_BRAKESWITCH_NOTCLOSED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">143</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">143</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_BRAKESWITCH_NOTCLOSED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_SHIFTERLEVERNOTINPARK</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">144</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">144</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_SHIFTERLEVERNOTINPARK</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_TORQUECONVERTERCLUTCHLOCKED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">145</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">145</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_TORQUECONVERTERCLUTCHLOCKED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VOLTAGETOOHIGH</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">146</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">146</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VOLTAGETOOHIGH</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VOLTAGETOOLOW</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">147</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">147</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VOLTAGETOOLOW</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VMSCNC_0</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">240</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">240</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VMSCNC_0</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VMSCNC_1</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">241</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">241</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VMSCNC_1</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VMSCNC_2</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">242</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">242</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VMSCNC_2</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VMSCNC_3</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">243</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">243</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VMSCNC_3</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VMSCNC_4</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">244</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">244</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VMSCNC_4</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VMSCNC_5</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">245</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">245</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VMSCNC_5</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VMSCNC_6</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">246</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">246</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VMSCNC_6</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VMSCNC_7</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">247</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">247</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VMSCNC_7</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VMSCNC_8</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">248</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">248</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VMSCNC_8</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VMSCNC_9</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">249</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">249</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VMSCNC_9</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VMSCNC_A</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">250</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">250</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VMSCNC_A</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VMSCNC_B</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">251</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">251</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VMSCNC_B</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VMSCNC_C</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">252</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">252</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VMSCNC_C</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VMSCNC_D</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">253</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">253</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VMSCNC_D</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_E_VMSCNC_E</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">254</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">254</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_E_VMSCNC_E</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                    <COMPU-METHOD UUID="4fc131e0-b2f1-484f-b171-390f85f6b619">
                      <SHORT-NAME>Dcm_ProtocolType</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_OBD_ON_CAN</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_OBD_ON_CAN</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_OBD_ON_FLEXRAY</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_OBD_ON_FLEXRAY</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_OBD_ON_IP</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_OBD_ON_IP</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_UDS_ON_CAN</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">3</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">3</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_UDS_ON_CAN</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_UDS_ON_FLEXRAY</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">4</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">4</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_UDS_ON_FLEXRAY</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_UDS_ON_IP</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">5</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">5</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_UDS_ON_IP</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_NO_ACTIVE_PROTOCOL</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">12</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">12</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_NO_ACTIVE_PROTOCOL</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_SUPPLIER_1</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">240</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">240</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_SUPPLIER_1</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_SUPPLIER_2</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">241</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">241</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_SUPPLIER_2</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_SUPPLIER_3</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">242</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">242</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_SUPPLIER_3</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_SUPPLIER_4</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">243</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">243</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_SUPPLIER_4</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_SUPPLIER_5</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">244</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">244</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_SUPPLIER_5</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_SUPPLIER_6</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">245</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">245</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_SUPPLIER_6</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_SUPPLIER_7</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">246</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">246</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_SUPPLIER_7</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_SUPPLIER_8</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">247</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">247</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_SUPPLIER_8</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_SUPPLIER_9</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">248</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">248</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_SUPPLIER_9</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_SUPPLIER_10</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">249</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">249</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_SUPPLIER_10</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_SUPPLIER_11</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">250</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">250</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_SUPPLIER_11</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_SUPPLIER_12</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">251</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">251</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_SUPPLIER_12</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_SUPPLIER_13</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">252</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">252</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_SUPPLIER_13</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_SUPPLIER_14</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">253</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">253</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_SUPPLIER_14</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_SUPPLIER_15</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">254</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">254</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_SUPPLIER_15</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                    <COMPU-METHOD UUID="b919b853-602e-4bf5-9d78-4348e1899198">
                      <SHORT-NAME>Dcm_RequestKindType</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_REQ_KIND_NONE</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_REQ_KIND_NONE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_REQ_KIND_EXTERNAL</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_REQ_KIND_EXTERNAL</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_REQ_KIND_ROE</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_REQ_KIND_ROE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                    <COMPU-METHOD UUID="72747cde-9dff-4f8c-b2c0-ee981369d29b">
                      <SHORT-NAME>Dcm_SecLevelType</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_SEC_LEV_LOCKED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_SEC_LEV_LOCKED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_SEC_LEV_L1</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_SEC_LEV_L1</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_SEC_LEV_L9</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">9</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">9</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_SEC_LEV_L9</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_SEC_LEV_L49</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">49</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">49</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_SEC_LEV_L49</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                    <COMPU-METHOD UUID="557de128-64f8-4508-9118-e498f9978a24">
                      <SHORT-NAME>Dcm_SesCtrlType</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_DEFAULT_SESSION</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_DEFAULT_SESSION</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_PROGRAMMING_SESSION</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_PROGRAMMING_SESSION</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_EXTENDED_DIAGNOSTIC_SESSION</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">3</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">3</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_EXTENDED_DIAGNOSTIC_SESSION</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_ZXExtended</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">96</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">96</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_ZXExtended</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                    <COMPU-METHOD UUID="3fb2da3e-26c5-486c-b5d8-c34d7806e4df">
                      <SHORT-NAME>Dcm_ControlDtcSettingType</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_ENUM_ENABLEDTCSETTING</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_ENUM_ENABLEDTCSETTING</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_ENUM_DISABLEDTCSETTING</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_ENUM_DISABLEDTCSETTING</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                    <COMPU-METHOD UUID="f016d44a-9caa-49bb-b643-11d4c7cbe848">
                      <SHORT-NAME>Dcm_DiagnosticSessionControlType</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_ENUM_DEFAULT_SESSION</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_ENUM_DEFAULT_SESSION</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_ENUM_PROGRAMMING_SESSION</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_ENUM_PROGRAMMING_SESSION</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_ENUM_EXTENDED_SESSION</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">3</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">3</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_ENUM_EXTENDED_SESSION</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_ENUM_ZXExtended</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">96</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">96</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_ENUM_ZXExtended</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                    <COMPU-METHOD UUID="79deedef-0265-445b-8100-3c83271d8542">
                      <SHORT-NAME>Dcm_CommunicationModeType</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_ENABLE_RX_TX_NORM</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_ENABLE_RX_TX_NORM</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_ENABLE_RX_DISABLE_TX_NORM</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_ENABLE_RX_DISABLE_TX_NORM</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_DISABLE_RX_ENABLE_TX_NORM</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_DISABLE_RX_ENABLE_TX_NORM</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_DISABLE_RX_TX_NORMAL</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">3</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">3</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_DISABLE_RX_TX_NORMAL</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_ENABLE_RX_TX_NM</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">4</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">4</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_ENABLE_RX_TX_NM</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_ENABLE_RX_DISABLE_TX_NM</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">5</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">5</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_ENABLE_RX_DISABLE_TX_NM</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_DISABLE_RX_ENABLE_TX_NM</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">6</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">6</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_DISABLE_RX_ENABLE_TX_NM</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_DISABLE_RX_TX_NM</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">7</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">7</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_DISABLE_RX_TX_NM</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_ENABLE_RX_TX_NORM_NM</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">8</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">8</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_ENABLE_RX_TX_NORM_NM</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_ENABLE_RX_DISABLE_TX_NORM_NM</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">9</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">9</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_ENABLE_RX_DISABLE_TX_NORM_NM</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_DISABLE_RX_ENABLE_TX_NORM_NM</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">10</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">10</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_DISABLE_RX_ENABLE_TX_NORM_NM</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>DCM_DISABLE_RX_TX_NORM_NM</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">11</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">11</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>DCM_DISABLE_RX_TX_NORM_NM</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                  </ELEMENTS>
                </AR-PACKAGE>
              </AR-PACKAGES>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="84be8b99-d44f-426e-b9de-a3d4b126e3d9">
              <SHORT-NAME>Interfaces</SHORT-NAME>
              <ELEMENTS>
                <MODE-SWITCH-INTERFACE UUID="f8d976df-fbed-4577-b77b-49917b4c8226">
                  <SHORT-NAME>DcmControlDtcSetting</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>DIAGNOSTIC-COMMUNICATION-MANAGER</SERVICE-KIND>
                  <MODE-GROUP UUID="104598f8-0660-4312-94c2-c43a67ebf7b0">
                    <SHORT-NAME>DcmControlDtcSetting</SHORT-NAME>
                    <TYPE-TREF DEST="MODE-DECLARATION-GROUP">/MICROSAR/Dcm_swc/DcmControlDtcSetting</TYPE-TREF>
                  </MODE-GROUP>
                </MODE-SWITCH-INTERFACE>
                <MODE-SWITCH-INTERFACE UUID="b11c06e4-45b9-4f4a-ae60-e3f44697530f">
                  <SHORT-NAME>DcmDiagnosticSessionControl</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>DIAGNOSTIC-COMMUNICATION-MANAGER</SERVICE-KIND>
                  <MODE-GROUP UUID="3c9b9feb-60ad-4854-ace2-52e159cd661e">
                    <SHORT-NAME>DcmDiagnosticSessionControl</SHORT-NAME>
                    <TYPE-TREF DEST="MODE-DECLARATION-GROUP">/MICROSAR/Dcm_swc/DcmDiagnosticSessionControl</TYPE-TREF>
                  </MODE-GROUP>
                </MODE-SWITCH-INTERFACE>
                <CLIENT-SERVER-INTERFACE UUID="63398e55-9b4a-4604-ac92-90ebf3f1cd9c">
                  <SHORT-NAME>DCMServices</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>DIAGNOSTIC-COMMUNICATION-MANAGER</SERVICE-KIND>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="9cfb4f20-2b3f-4462-a393-e5bf3f705a3b">
                      <SHORT-NAME>GetActiveProtocol</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="6170b561-afda-4838-b797-a0e070d69438">
                          <SHORT-NAME>ActiveProtocol</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/Dcm_swc/DataTypes/Dcm_ProtocolType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/Dcm_swc/Interfaces/DCMServices/E_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="efb6fa69-7785-49ac-a2d1-aa2a83ff9132">
                      <SHORT-NAME>GetSesCtrlType</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="9cd7b344-ed88-418a-80ca-5be61d6ed86e">
                          <SHORT-NAME>SesCtrlType</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/Dcm_swc/DataTypes/Dcm_SesCtrlType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/Dcm_swc/Interfaces/DCMServices/E_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="cd2a084f-d0f0-4d57-86c1-0d263f796e54">
                      <SHORT-NAME>ResetToDefaultSession</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/Dcm_swc/Interfaces/DCMServices/E_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="5cb91e67-28b7-435d-a5eb-bd7b1a56f5c0">
                      <SHORT-NAME>GetSecurityLevel</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="bd317b2e-f78f-4207-ad2d-1599a64c45a8">
                          <SHORT-NAME>SecLevel</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/Dcm_swc/DataTypes/Dcm_SecLevelType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/Dcm_swc/Interfaces/DCMServices/E_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="24ccf0db-ce55-4557-8301-8dfb206ff1f8">
                      <SHORT-NAME>SetActiveDiagnostic</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="40c7b34c-ba5c-4765-ac99-c58a68a418fe">
                          <SHORT-NAME>active</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/Dcm_swc/Interfaces/DCMServices/E_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="8580e2a1-950a-4faf-abe7-f6b8108177f2">
                      <SHORT-NAME>GetRequestKind</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="0abc0977-8f77-4d28-bc69-93bd769462a1">
                          <SHORT-NAME>TesterSourceAddress</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="6bec515d-e57b-4240-bdfd-c6b64632848f">
                          <SHORT-NAME>RequestKind</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/Dcm_swc/DataTypes/Dcm_RequestKindType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/Dcm_swc/Interfaces/DCMServices/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                  <POSSIBLE-ERRORS>
                    <APPLICATION-ERROR UUID="dea9987c-528f-4a0e-9dde-c18c2ecb19b9">
                      <SHORT-NAME>E_OK</SHORT-NAME>
                      <ERROR-CODE>0</ERROR-CODE>
                    </APPLICATION-ERROR>
                    <APPLICATION-ERROR UUID="517eb4e6-3050-4914-a1d2-f897b24e34e1">
                      <SHORT-NAME>E_NOT_OK</SHORT-NAME>
                      <ERROR-CODE>1</ERROR-CODE>
                    </APPLICATION-ERROR>
                  </POSSIBLE-ERRORS>
                </CLIENT-SERVER-INTERFACE>
                <CLIENT-SERVER-INTERFACE UUID="32c473f3-cf2d-48a0-8492-416b00785618">
                  <SHORT-NAME>ServiceRequestNotification</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>DIAGNOSTIC-COMMUNICATION-MANAGER</SERVICE-KIND>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="e71892d9-54a2-45c4-b90f-b234d8634a8a">
                      <SHORT-NAME>Indication</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="f5f864fe-694f-44fc-8e37-16f6b3dbe5fa">
                          <SHORT-NAME>SID</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="582f867a-2c07-47ac-a055-e6ac3ed23a6e">
                          <SHORT-NAME>RequestData</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/Dcm_swc/DataTypes/Dcm_Data4095ByteType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="7444cb18-c8d7-4067-b47e-3b200126f312">
                          <SHORT-NAME>DataSize</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="ba17b0d9-c323-4675-8766-6e5e4b2f8330">
                          <SHORT-NAME>ReqType</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="dc7dd520-1066-41d1-bd58-8b2759d15b7a">
                          <SHORT-NAME>SourceAddress</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="b97a7bc7-f543-438b-aa14-af609183cdb7">
                          <SHORT-NAME>ErrorCode</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/Dcm_swc/DataTypes/Dcm_NegativeResponseCodeType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/Dcm_swc/Interfaces/ServiceRequestNotification/E_NOT_OK</POSSIBLE-ERROR-REF>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/Dcm_swc/Interfaces/ServiceRequestNotification/E_REQUEST_NOT_ACCEPTED</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="a2fa6679-81b6-4bd2-a59b-1b926108a5c0">
                      <SHORT-NAME>Confirmation</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="1a23f596-02a5-4be8-9ddc-32257d03fd68">
                          <SHORT-NAME>SID</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="2d702472-3a2e-4bae-b117-befde01cbc2b">
                          <SHORT-NAME>ReqType</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="8d88cb6e-f76f-4349-ae5c-2ea458577789">
                          <SHORT-NAME>SourceAddress</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="72525aad-bf67-4795-8147-d91a9d174e09">
                          <SHORT-NAME>ConfirmationStatus</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/Dcm_swc/DataTypes/Dcm_ConfirmationStatusType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/Dcm_swc/Interfaces/ServiceRequestNotification/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                  <POSSIBLE-ERRORS>
                    <APPLICATION-ERROR UUID="d92c7631-bd6e-4901-9f9b-9e6d3557ed6f">
                      <SHORT-NAME>E_NOT_OK</SHORT-NAME>
                      <ERROR-CODE>1</ERROR-CODE>
                    </APPLICATION-ERROR>
                    <APPLICATION-ERROR UUID="9ed7df3b-73cb-4a2b-bf69-78f48abc7077">
                      <SHORT-NAME>E_REQUEST_NOT_ACCEPTED</SHORT-NAME>
                      <ERROR-CODE>8</ERROR-CODE>
                    </APPLICATION-ERROR>
                  </POSSIBLE-ERRORS>
                </CLIENT-SERVER-INTERFACE>
                <MODE-SWITCH-INTERFACE UUID="41c55800-19b8-427f-869e-4af30a7e1bdd">
                  <SHORT-NAME>DcmCommunicationControl_ComMConf_ComMChannel_CN_CANA_c11a1184</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>DIAGNOSTIC-COMMUNICATION-MANAGER</SERVICE-KIND>
                  <MODE-GROUP UUID="861f5740-032f-4ec1-aa7a-6412ddc8f583">
                    <SHORT-NAME>DcmCommunicationControl_ComMConf_ComMChannel_CN_CANA_c11a1184</SHORT-NAME>
                    <TYPE-TREF DEST="MODE-DECLARATION-GROUP">/MICROSAR/Dcm_swc/DcmCommunicationControl</TYPE-TREF>
                  </MODE-GROUP>
                </MODE-SWITCH-INTERFACE>
                <MODE-SWITCH-INTERFACE UUID="22f67bf0-91e2-4899-b421-ee53ea161d6e">
                  <SHORT-NAME>DcmCommunicationControl_ComMConf_ComMChannel_CN_CANB_5813403e</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>DIAGNOSTIC-COMMUNICATION-MANAGER</SERVICE-KIND>
                  <MODE-GROUP UUID="b4023a3c-07c8-4440-8ce9-a63c9d82af16">
                    <SHORT-NAME>DcmCommunicationControl_ComMConf_ComMChannel_CN_CANB_5813403e</SHORT-NAME>
                    <TYPE-TREF DEST="MODE-DECLARATION-GROUP">/MICROSAR/Dcm_swc/DcmCommunicationControl</TYPE-TREF>
                  </MODE-GROUP>
                </MODE-SWITCH-INTERFACE>
                <MODE-SWITCH-INTERFACE UUID="e1b2e506-0a61-4715-a73c-409235d332e9">
                  <SHORT-NAME>DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI1_82921327</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>DIAGNOSTIC-COMMUNICATION-MANAGER</SERVICE-KIND>
                  <MODE-GROUP UUID="c7cac191-909f-49ee-a4e5-cf98cd094fb7">
                    <SHORT-NAME>DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI1_82921327</SHORT-NAME>
                    <TYPE-TREF DEST="MODE-DECLARATION-GROUP">/MICROSAR/Dcm_swc/DcmCommunicationControl</TYPE-TREF>
                  </MODE-GROUP>
                </MODE-SWITCH-INTERFACE>
                <MODE-SWITCH-INTERFACE UUID="7dbadf25-66e3-4c28-a835-89248c38f153">
                  <SHORT-NAME>DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI2_1b9b429d</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>DIAGNOSTIC-COMMUNICATION-MANAGER</SERVICE-KIND>
                  <MODE-GROUP UUID="b673eac2-cfe4-4f2f-93b5-2a5e769cc5a9">
                    <SHORT-NAME>DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI2_1b9b429d</SHORT-NAME>
                    <TYPE-TREF DEST="MODE-DECLARATION-GROUP">/MICROSAR/Dcm_swc/DcmCommunicationControl</TYPE-TREF>
                  </MODE-GROUP>
                </MODE-SWITCH-INTERFACE>
                <MODE-SWITCH-INTERFACE UUID="3de58f7b-ac4b-40d2-8b04-4c4c5db3dd07">
                  <SHORT-NAME>DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI3_6c9c720b</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>DIAGNOSTIC-COMMUNICATION-MANAGER</SERVICE-KIND>
                  <MODE-GROUP UUID="819d5306-378a-46ea-ad18-2303be82ee81">
                    <SHORT-NAME>DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI3_6c9c720b</SHORT-NAME>
                    <TYPE-TREF DEST="MODE-DECLARATION-GROUP">/MICROSAR/Dcm_swc/DcmCommunicationControl</TYPE-TREF>
                  </MODE-GROUP>
                </MODE-SWITCH-INTERFACE>
                <MODE-SWITCH-INTERFACE UUID="31f126e8-2ad3-4399-9024-318afd1c133d">
                  <SHORT-NAME>DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI4_f2f8e7a8</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>DIAGNOSTIC-COMMUNICATION-MANAGER</SERVICE-KIND>
                  <MODE-GROUP UUID="1627fa0e-9d43-49ef-a3ff-206b619162b0">
                    <SHORT-NAME>DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI4_f2f8e7a8</SHORT-NAME>
                    <TYPE-TREF DEST="MODE-DECLARATION-GROUP">/MICROSAR/Dcm_swc/DcmCommunicationControl</TYPE-TREF>
                  </MODE-GROUP>
                </MODE-SWITCH-INTERFACE>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="002aed2c-003d-40d1-8747-da64cdd38637">
              <SHORT-NAME>ComponentTypes</SHORT-NAME>
              <ELEMENTS>
                <SERVICE-SW-COMPONENT-TYPE UUID="060aba7b-ad27-46bc-9aaa-8901cf3766ac">
                  <SHORT-NAME>Dcm</SHORT-NAME>
                  <CATEGORY>SERVICE_COMPONENT</CATEGORY>
                  <PORTS>
                    <P-PORT-PROTOTYPE UUID="e9e6b696-8cd7-4acc-ab17-196ca9ab0936">
                      <SHORT-NAME>DcmControlDtcSetting</SHORT-NAME>
                      <PROVIDED-INTERFACE-TREF DEST="MODE-SWITCH-INTERFACE">/MICROSAR/Dcm_swc/Interfaces/DcmControlDtcSetting</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="b3ffd5ad-5b06-40bd-a1ea-b23d0da07f08">
                      <SHORT-NAME>DcmDiagnosticSessionControl</SHORT-NAME>
                      <PROVIDED-INTERFACE-TREF DEST="MODE-SWITCH-INTERFACE">/MICROSAR/Dcm_swc/Interfaces/DcmDiagnosticSessionControl</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="0d9ac875-b7a2-4627-a292-c2587e7a2b3c">
                      <SHORT-NAME>DCMServices</SHORT-NAME>
                      <PROVIDED-INTERFACE-TREF DEST="CLIENT-SERVER-INTERFACE">/MICROSAR/Dcm_swc/Interfaces/DCMServices</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="ec788884-dfa6-4e1c-9ee0-46b66617c184">
                      <SHORT-NAME>DcmCommunicationControl_ComMConf_ComMChannel_CN_CANA_c11a1184</SHORT-NAME>
                      <PROVIDED-INTERFACE-TREF DEST="MODE-SWITCH-INTERFACE">/MICROSAR/Dcm_swc/Interfaces/DcmCommunicationControl_ComMConf_ComMChannel_CN_CANA_c11a1184</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="f92f1db7-4dd2-4a46-a8d7-d8161a874e97">
                      <SHORT-NAME>DcmCommunicationControl_ComMConf_ComMChannel_CN_CANB_5813403e</SHORT-NAME>
                      <PROVIDED-INTERFACE-TREF DEST="MODE-SWITCH-INTERFACE">/MICROSAR/Dcm_swc/Interfaces/DcmCommunicationControl_ComMConf_ComMChannel_CN_CANB_5813403e</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="d698fa09-4f4f-4278-8d8b-eb749940c834">
                      <SHORT-NAME>DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI1_82921327</SHORT-NAME>
                      <PROVIDED-INTERFACE-TREF DEST="MODE-SWITCH-INTERFACE">/MICROSAR/Dcm_swc/Interfaces/DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI1_82921327</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="9c413342-db70-48b3-ab61-99853404b8c1">
                      <SHORT-NAME>DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI2_1b9b429d</SHORT-NAME>
                      <PROVIDED-INTERFACE-TREF DEST="MODE-SWITCH-INTERFACE">/MICROSAR/Dcm_swc/Interfaces/DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI2_1b9b429d</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="8bc04c06-a6b8-4f68-aa53-7142ff1479c9">
                      <SHORT-NAME>DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI3_6c9c720b</SHORT-NAME>
                      <PROVIDED-INTERFACE-TREF DEST="MODE-SWITCH-INTERFACE">/MICROSAR/Dcm_swc/Interfaces/DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI3_6c9c720b</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="8fb7fa50-e00d-447f-bd94-acdfe94a75e2">
                      <SHORT-NAME>DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI4_f2f8e7a8</SHORT-NAME>
                      <PROVIDED-INTERFACE-TREF DEST="MODE-SWITCH-INTERFACE">/MICROSAR/Dcm_swc/Interfaces/DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI4_f2f8e7a8</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                  </PORTS>
                  <INTERNAL-BEHAVIORS>
                    <SWC-INTERNAL-BEHAVIOR UUID="8315477c-268c-4246-875c-6d04b140648b">
                      <SHORT-NAME>DcmInternalBehavior</SHORT-NAME>
                      <DATA-TYPE-MAPPING-REFS>
                        <DATA-TYPE-MAPPING-REF DEST="DATA-TYPE-MAPPING-SET">/MICROSAR/Dcm_swc/DcmMappingSet</DATA-TYPE-MAPPING-REF>
                      </DATA-TYPE-MAPPING-REFS>
                      <EVENTS>
                        <TIMING-EVENT UUID="0a0588d5-fb5a-42d8-843d-ba5b81e06cee">
                          <SHORT-NAME>Timer_Dcm_MainFunction</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmInternalBehavior/Dcm_MainFunction</START-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </TIMING-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="08e8348b-17ef-4a6c-823b-f39c78893d99">
                          <SHORT-NAME>OpEventGetActiveProtocol_GetActiveProtocol_DCMServices</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmInternalBehavior/GetActiveProtocol</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DCMServices</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/Dcm_swc/Interfaces/DCMServices/GetActiveProtocol</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="ecce8c2e-d5d4-4d5f-845d-84da8ef499a8">
                          <SHORT-NAME>OpEventGetSesCtrlType_GetSesCtrlType_DCMServices</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmInternalBehavior/GetSesCtrlType</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DCMServices</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/Dcm_swc/Interfaces/DCMServices/GetSesCtrlType</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="4b587fca-02bc-40d1-b515-755806d85a78">
                          <SHORT-NAME>OpEventResetToDefaultSession_ResetToDefaultSession_DCMServices</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmInternalBehavior/ResetToDefaultSession</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DCMServices</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/Dcm_swc/Interfaces/DCMServices/ResetToDefaultSession</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="83755366-7b85-459c-8a27-be13ca331b66">
                          <SHORT-NAME>OpEventGetSecurityLevel_GetSecurityLevel_DCMServices</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmInternalBehavior/GetSecurityLevel</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DCMServices</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/Dcm_swc/Interfaces/DCMServices/GetSecurityLevel</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="e2fb621a-4aad-4137-9c98-b5e30cec4697">
                          <SHORT-NAME>OpEventSetActiveDiagnostic_SetActiveDiagnostic_DCMServices</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmInternalBehavior/SetActiveDiagnostic</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DCMServices</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/Dcm_swc/Interfaces/DCMServices/SetActiveDiagnostic</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="df64158c-f0e8-432b-8f9d-6474df3148a4">
                          <SHORT-NAME>OpEventGetRequestKind_GetRequestKind_DCMServices</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmInternalBehavior/GetRequestKind</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DCMServices</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/Dcm_swc/Interfaces/DCMServices/GetRequestKind</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                      </EVENTS>
                      <HANDLE-TERMINATION-AND-RESTART>NO-SUPPORT</HANDLE-TERMINATION-AND-RESTART>
                      <INCLUDED-DATA-TYPE-SETS>
                        <INCLUDED-DATA-TYPE-SET>
                          <DATA-TYPE-REFS>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/Dcm_swc/DataTypes/Dcm_ConfirmationStatusType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/Dcm_swc/DataTypes/Dcm_OpStatusType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/Dcm_swc/DataTypes/Dcm_NegativeResponseCodeType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/Dcm_swc/DataTypes/Dcm_ProtocolType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/Dcm_swc/DataTypes/Dcm_RequestKindType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/Dcm_swc/DataTypes/Dcm_SecLevelType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/Dcm_swc/DataTypes/Dcm_SesCtrlType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/Dcm_swc/DataTypes/Dcm_ControlDtcSettingType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/Dcm_swc/DataTypes/Dcm_DiagnosticSessionControlType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/Dcm_swc/DataTypes/Dcm_CommunicationModeType</DATA-TYPE-REF>
                          </DATA-TYPE-REFS>
                        </INCLUDED-DATA-TYPE-SET>
                      </INCLUDED-DATA-TYPE-SETS>
                      <PORT-API-OPTIONS>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmControlDtcSetting</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmDiagnosticSessionControl</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DCMServices</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmCommunicationControl_ComMConf_ComMChannel_CN_CANA_c11a1184</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmCommunicationControl_ComMConf_ComMChannel_CN_CANB_5813403e</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI1_82921327</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI2_1b9b429d</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI3_6c9c720b</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI4_f2f8e7a8</PORT-REF>
                        </PORT-API-OPTION>
                      </PORT-API-OPTIONS>
                      <RUNNABLES>
                        <RUNNABLE-ENTITY UUID="ae1e4037-2387-4b25-8c97-d94a8890d259">
                          <SHORT-NAME>Dcm_MainFunction</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>false</CAN-BE-INVOKED-CONCURRENTLY>
                          <MODE-SWITCH-POINTS>
                            <MODE-SWITCH-POINT UUID="a60f84e7-6006-4d70-ab35-6253573d70e7">
                              <SHORT-NAME>ModeSwitchPointDcm_MainFunction_DcmControlDtcSetting_DcmControlDtcSetting</SHORT-NAME>
                              <MODE-GROUP-IREF>
                                <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmControlDtcSetting</CONTEXT-P-PORT-REF>
                                <TARGET-MODE-GROUP-REF DEST="MODE-DECLARATION-GROUP-PROTOTYPE">/MICROSAR/Dcm_swc/Interfaces/DcmControlDtcSetting/DcmControlDtcSetting</TARGET-MODE-GROUP-REF>
                              </MODE-GROUP-IREF>
                            </MODE-SWITCH-POINT>
                            <MODE-SWITCH-POINT UUID="18601f63-1ff1-440c-b483-0e8c228df41a">
                              <SHORT-NAME>ModeSwitchPointDcm_MainFunction_DcmDiagnosticSessionControl_DcmDiagnosticSessionControl</SHORT-NAME>
                              <MODE-GROUP-IREF>
                                <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmDiagnosticSessionControl</CONTEXT-P-PORT-REF>
                                <TARGET-MODE-GROUP-REF DEST="MODE-DECLARATION-GROUP-PROTOTYPE">/MICROSAR/Dcm_swc/Interfaces/DcmDiagnosticSessionControl/DcmDiagnosticSessionControl</TARGET-MODE-GROUP-REF>
                              </MODE-GROUP-IREF>
                            </MODE-SWITCH-POINT>
                            <MODE-SWITCH-POINT UUID="7688be32-924f-42f8-a3ea-1a29ce9cdf9d">
                              <SHORT-NAME>ModeSwitchPointDcm_MainFunction_DcmCommunicationControl_ComMConf_ComMChannel_CN_CANA_c11a1184_DcmCommunicationControl_C_c8c89084</SHORT-NAME>
                              <MODE-GROUP-IREF>
                                <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmCommunicationControl_ComMConf_ComMChannel_CN_CANA_c11a1184</CONTEXT-P-PORT-REF>
                                <TARGET-MODE-GROUP-REF DEST="MODE-DECLARATION-GROUP-PROTOTYPE">/MICROSAR/Dcm_swc/Interfaces/DcmCommunicationControl_ComMConf_ComMChannel_CN_CANA_c11a1184/DcmCommunicationControl_ComMConf_ComMChannel_CN_CANA_c11a1184</TARGET-MODE-GROUP-REF>
                              </MODE-GROUP-IREF>
                            </MODE-SWITCH-POINT>
                            <MODE-SWITCH-POINT UUID="40ddef08-2217-46e4-8c33-ed942075f89a">
                              <SHORT-NAME>ModeSwitchPointDcm_MainFunction_DcmCommunicationControl_ComMConf_ComMChannel_CN_CANB_5813403e_DcmCommunicationControl_C_b930627d</SHORT-NAME>
                              <MODE-GROUP-IREF>
                                <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmCommunicationControl_ComMConf_ComMChannel_CN_CANB_5813403e</CONTEXT-P-PORT-REF>
                                <TARGET-MODE-GROUP-REF DEST="MODE-DECLARATION-GROUP-PROTOTYPE">/MICROSAR/Dcm_swc/Interfaces/DcmCommunicationControl_ComMConf_ComMChannel_CN_CANB_5813403e/DcmCommunicationControl_ComMConf_ComMChannel_CN_CANB_5813403e</TARGET-MODE-GROUP-REF>
                              </MODE-GROUP-IREF>
                            </MODE-SWITCH-POINT>
                            <MODE-SWITCH-POINT UUID="62f4d6aa-cb0a-424c-aea6-fb4ca56f9916">
                              <SHORT-NAME>ModeSwitchPointDcm_MainFunction_DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI1_82921327_DcmCommunicationControl_C_6ac440af</SHORT-NAME>
                              <MODE-GROUP-IREF>
                                <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI1_82921327</CONTEXT-P-PORT-REF>
                                <TARGET-MODE-GROUP-REF DEST="MODE-DECLARATION-GROUP-PROTOTYPE">/MICROSAR/Dcm_swc/Interfaces/DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI1_82921327/DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI1_82921327</TARGET-MODE-GROUP-REF>
                              </MODE-GROUP-IREF>
                            </MODE-SWITCH-POINT>
                            <MODE-SWITCH-POINT UUID="e1c06514-a605-4911-aacf-6d6a5f9e7c99">
                              <SHORT-NAME>ModeSwitchPointDcm_MainFunction_DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI2_1b9b429d_DcmCommunicationControl_C_87e8e645</SHORT-NAME>
                              <MODE-GROUP-IREF>
                                <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI2_1b9b429d</CONTEXT-P-PORT-REF>
                                <TARGET-MODE-GROUP-REF DEST="MODE-DECLARATION-GROUP-PROTOTYPE">/MICROSAR/Dcm_swc/Interfaces/DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI2_1b9b429d/DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI2_1b9b429d</TARGET-MODE-GROUP-REF>
                              </MODE-GROUP-IREF>
                            </MODE-SWITCH-POINT>
                            <MODE-SWITCH-POINT UUID="90f28260-c718-4cfd-a84a-785e21acfbb1">
                              <SHORT-NAME>ModeSwitchPointDcm_MainFunction_DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI3_6c9c720b_DcmCommunicationControl_C_bfbaa807</SHORT-NAME>
                              <MODE-GROUP-IREF>
                                <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI3_6c9c720b</CONTEXT-P-PORT-REF>
                                <TARGET-MODE-GROUP-REF DEST="MODE-DECLARATION-GROUP-PROTOTYPE">/MICROSAR/Dcm_swc/Interfaces/DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI3_6c9c720b/DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI3_6c9c720b</TARGET-MODE-GROUP-REF>
                              </MODE-GROUP-IREF>
                            </MODE-SWITCH-POINT>
                            <MODE-SWITCH-POINT UUID="d482fce5-6201-49c7-9e20-17124c26b83e">
                              <SHORT-NAME>ModeSwitchPointDcm_MainFunction_DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI4_f2f8e7a8_DcmCommunicationControl_C_d8d6462e</SHORT-NAME>
                              <MODE-GROUP-IREF>
                                <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI4_f2f8e7a8</CONTEXT-P-PORT-REF>
                                <TARGET-MODE-GROUP-REF DEST="MODE-DECLARATION-GROUP-PROTOTYPE">/MICROSAR/Dcm_swc/Interfaces/DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI4_f2f8e7a8/DcmCommunicationControl_ComMConf_ComMChannel_CN_PRI4_f2f8e7a8</TARGET-MODE-GROUP-REF>
                              </MODE-GROUP-IREF>
                            </MODE-SWITCH-POINT>
                          </MODE-SWITCH-POINTS>
                          <SYMBOL>Dcm_MainFunction</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="fc68b7eb-a6c3-4165-ba22-9da39f01a8aa">
                          <SHORT-NAME>GetActiveProtocol</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>Dcm_GetActiveProtocol</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="8c4d2b36-b3cf-46b6-b64a-6e2dc93e4643">
                          <SHORT-NAME>GetSesCtrlType</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>Dcm_GetSesCtrlType</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="597e2000-6637-4ccd-a26b-adbfad990bdc">
                          <SHORT-NAME>ResetToDefaultSession</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>Dcm_ResetToDefaultSession</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="0c69a5ce-6cf9-4d88-b965-6cc93c8e2503">
                          <SHORT-NAME>GetSecurityLevel</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>Dcm_GetSecurityLevel</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="6c2beaf7-2de7-42b4-8158-98fd3eb14476">
                          <SHORT-NAME>SetActiveDiagnostic</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>Dcm_SetActiveDiagnostic</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="67a244e8-da25-4562-b022-13a852dc9deb">
                          <SHORT-NAME>GetRequestKind</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>Dcm_GetRequestKind</SYMBOL>
                        </RUNNABLE-ENTITY>
                      </RUNNABLES>
                      <SUPPORTS-MULTIPLE-INSTANTIATION>false</SUPPORTS-MULTIPLE-INSTANTIATION>
                    </SWC-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </SERVICE-SW-COMPONENT-TYPE>
                <SWC-IMPLEMENTATION UUID="a4d3cc4d-9424-46d1-bfa4-9de43287abb4">
                  <SHORT-NAME>DcmImplementation</SHORT-NAME>
                  <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
                  <BEHAVIOR-REF DEST="SWC-INTERNAL-BEHAVIOR">/MICROSAR/Dcm_swc/ComponentTypes/Dcm/DcmInternalBehavior</BEHAVIOR-REF>
                </SWC-IMPLEMENTATION>
              </ELEMENTS>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
