<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00049.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>ActiveEcuC</SHORT-NAME>
      <ELEMENTS>
        <ECUC-MODULE-CONFIGURATION-VALUES UUID="01a1950a-41b1-4edd-a168-4c70d7129884">
          <SHORT-NAME>Mcu</SHORT-NAME>
          <ADMIN-DATA>
            <SDGS>
              <SDG GID="DV:CfgPostBuild">
                <SD GID="DV:postBuildVariantSupport">false</SD>
              </SDG>
            </SDGS>
          </ADMIN-DATA>
          <DEFINITION-REF DEST="ECUC-MODULE-DEF">/Renesas/EcucDefs_Mcu/Mcu</DEFINITION-REF>
          <IMPLEMENTATION-CONFIG-VARIANT>VARIANT-POST-BUILD</IMPLEMENTATION-CONFIG-VARIANT>
          <MODULE-DESCRIPTION-REF DEST="BSW-IMPLEMENTATION">/Renesas/BswModuleDescriptions_Mcu/Mcu_Impl</MODULE-DESCRIPTION-REF>
          <CONTAINERS>
            <ECUC-CONTAINER-VALUE UUID="c3b71ecc-d0cc-462d-9e45-750eaa0a62c4">
              <SHORT-NAME>McuGeneralConfiguration</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuGeneralConfiguration</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuGeneralConfiguration/McuDevErrorDetect</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuGeneralConfiguration/McuGetRamStateApi</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuGeneralConfiguration/McuInitClock</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuGeneralConfiguration/McuNoPll</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuGeneralConfiguration/McuPerformResetApi</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuGeneralConfiguration/McuVersionInfoApi</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuGeneralConfiguration/McuVersionCheckExternalModules</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuGeneralConfiguration/McuCriticalSectionProtection</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuGeneralConfiguration/McuSwResetCall</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuGeneralConfiguration/McuDeviceName</DEFINITION-REF>
                  <VALUE>V4H</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuGeneralConfiguration/McuIsrCategory</DEFINITION-REF>
                  <VALUE>CAT1</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE UUID="b8bcdbb3-a177-4b18-a2de-e445f0a11a14">
              <SHORT-NAME>McuModuleConfiguration</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSrcFailureNotification</DEFINITION-REF>
                  <VALUE>DISABLED</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuNumberOfMcuModes</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuRamSectors</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuResetSetting</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE UUID="237446a9-58b5-49b4-829d-428d4c14cc2f">
                  <SHORT-NAME>McuModeSettingConf</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuModeSettingConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuModeSettingConf/McuModeType</DEFINITION-REF>
                      <VALUE>MCU_RUN_MODE</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuModeSettingConf/McuMode</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="2b6917db-abe6-43e3-9ef1-e08300b8550b">
                  <SHORT-NAME>McuClockSettingConfig</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockSettingId</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockStabilityWaitingTime</DEFINITION-REF>
                      <VALUE>0.0001</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE UUID="ba126345-3f6f-423e-ac23-78b0f9a35334">
                      <SHORT-NAME>McuSystemClockSetting</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuSystemClockSetting</DEFINITION-REF>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE UUID="38fac0e0-cf36-418f-a25e-5551f6b330e4">
                          <SHORT-NAME>McuCpuMainSysClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuSystemClockSetting/McuCpuMainSysClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuSystemClockSetting/McuCpuMainSysClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_CPU</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuSystemClockSetting/McuCpuMainSysClk/McuClockValue</DEFINITION-REF>
                              <VALUE>1400000000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE UUID="9b81f439-e1fe-4f28-a469-e96f50b4a72d">
                      <SHORT-NAME>McuModuleClockSetting</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting</DEFINITION-REF>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE UUID="3fe8dc80-207d-4196-8028-67f9b9bcb396">
                          <SHORT-NAME>McuDebugTracePortClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuDebugTracePortClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuDebugTracePortClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_ZTR</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuDebugTracePortClk/McuClockSelection</DEFINITION-REF>
                              <VALUE>PLL1CLK_DIV3_ID_1</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuDebugTracePortClk/McuClockValue</DEFINITION-REF>
                              <VALUE>533333333.3</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="29d9964b-adf6-48dc-9836-0e3ce30e761d">
                          <SHORT-NAME>McuDebugTraceBusClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuDebugTraceBusClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuDebugTraceBusClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_ZT</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuDebugTraceBusClk/McuClockSelection</DEFINITION-REF>
                              <VALUE>PLL1CLK_DIV3_ID_1</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuDebugTraceBusClk/McuClockValue</DEFINITION-REF>
                              <VALUE>533333333.3</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="bfb32ef3-6bd1-4f5e-933a-45f5c1a9d8c5">
                          <SHORT-NAME>McuDebugClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuDebugClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuDebugClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_ZS</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuDebugClk/McuClockSelection</DEFINITION-REF>
                              <VALUE>PLL1CLK_DIV6_ID_3</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuDebugClk/McuClockValue</DEFINITION-REF>
                              <VALUE>266666666.7</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="7409bd66-d251-404d-9b7c-58fd3f58826b">
                          <SHORT-NAME>McuZR0Clk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZR0Clk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZR0Clk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_CPU</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZR0Clk/McuClockDivider</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZR0Clk/McuClockValue</DEFINITION-REF>
                              <VALUE>1399440000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="4e206f35-7ec6-4b69-b54e-d9cfe12eef77">
                          <SHORT-NAME>McuZR1Clk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZR1Clk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZR1Clk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_CPU</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZR1Clk/McuClockDivider</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZR1Clk/McuClockValue</DEFINITION-REF>
                              <VALUE>1399440000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="3332829a-0ded-4376-b824-03b8a5301392">
                          <SHORT-NAME>McuZR2Clk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZR2Clk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZR2Clk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_CPU</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZR2Clk/McuClockDivider</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZR2Clk/McuClockValue</DEFINITION-REF>
                              <VALUE>1399440000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="fa373b8a-5783-4954-bee3-de1dd082af54">
                          <SHORT-NAME>McuZB3Clk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZB3Clk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZB3Clk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_ZB3</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZB3Clk/McuClockSelection</DEFINITION-REF>
                              <VALUE>PLL3VCO_DIV2_ID_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZB3Clk/McuClockValue</DEFINITION-REF>
                              <VALUE>799680000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="e678871f-723e-41da-9661-e9f13efda017">
                          <SHORT-NAME>McuCA76Clk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuCA76Clk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuCA76Clk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_Z0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuCA76Clk/McuClockDivider</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuCA76Clk/McuClockValue</DEFINITION-REF>
                              <VALUE>1699320000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="72af171c-ab8b-43e5-8cdf-ccf73ea4b824">
                          <SHORT-NAME>McuZGClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZGClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZGClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_ZG</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZGClk/McuClockDivider</DEFINITION-REF>
                              <VALUE>16</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZGClk/McuClockValue</DEFINITION-REF>
                              <VALUE>599760000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="3cd1fe8f-cd4f-4059-80c1-b3e5eaca1b0e">
                          <SHORT-NAME>McuSDSRCClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSDSRCClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSDSRCClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_SDSRC</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSDSRCClk/McuClockSelection</DEFINITION-REF>
                              <VALUE>PLL5VCOD2_DIV2_ID_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSDSRCClk/McuClockValue</DEFINITION-REF>
                              <VALUE>799680000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="93f91e28-1363-4538-a10a-d661e151b484">
                          <SHORT-NAME>McuSD0HClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSD0HClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSD0HClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_SD0H</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSD0HClk/McuClockCtrlSel</DEFINITION-REF>
                              <VALUE>ACTIVATE_ID_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSD0HClk/McuClockSelection</DEFINITION-REF>
                              <VALUE>CLK_SDSRC_DIV1_ID_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSD0HClk/McuClockValue</DEFINITION-REF>
                              <VALUE>799680000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="74d0e600-3104-4a3d-a1e6-99d577ed3fe7">
                          <SHORT-NAME>McuSD0Clk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSD0Clk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSD0Clk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_SD0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSD0Clk/McuClockCtrlSel</DEFINITION-REF>
                              <VALUE>ACTIVATE_ID_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSD0Clk/McuClockSelection</DEFINITION-REF>
                              <VALUE>CLK_SD0H_DIV4_ID_1</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSD0Clk/McuClockValue</DEFINITION-REF>
                              <VALUE>199920000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="f7dd370d-66e6-4c05-9962-93baa49962da">
                          <SHORT-NAME>McuRPCClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuRPCClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuRPCClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_RPC</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuRPCClk/McuClockCtrlSel</DEFINITION-REF>
                              <VALUE>ACTIVATE_ID_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuRPCClk/McuClockSelection</DEFINITION-REF>
                              <VALUE>PLL5VCOD5_DIV2_ID_17</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuRPCClk/McuClockValue</DEFINITION-REF>
                              <VALUE>319872000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="fda12912-2dfb-4ab5-b32e-c179b3caafe6">
                          <SHORT-NAME>McuRPCD2Clk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuRPCD2Clk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuRPCD2Clk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_RPCD2</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuRPCD2Clk/McuClockCtrlSel</DEFINITION-REF>
                              <VALUE>ACTIVATE_ID_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuRPCD2Clk/McuClockValue</DEFINITION-REF>
                              <VALUE>159936000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="6fa023a6-9107-486c-b954-0b455b8cbffb">
                          <SHORT-NAME>McuMSOClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuMSOClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuMSOClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_MSO</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuMSOClk/McuClockCtrlSel</DEFINITION-REF>
                              <VALUE>ACTIVATE_ID_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuMSOClk/McuClockDivider</DEFINITION-REF>
                              <VALUE>5</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuMSOClk/McuClockValue</DEFINITION-REF>
                              <VALUE>133280000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="03e6be9b-4a0c-455a-ab3f-918fd64fa4a1">
                          <SHORT-NAME>McuCANFDClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuCANFDClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuCANFDClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_CANFD</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuCANFDClk/McuClockCtrlSel</DEFINITION-REF>
                              <VALUE>ACTIVATE_ID_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuCANFDClk/McuClockDivider</DEFINITION-REF>
                              <VALUE>9</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuCANFDClk/McuClockValue</DEFINITION-REF>
                              <VALUE>79968000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="ccd1bec5-daf5-4c83-99c0-dae9159558ca">
                          <SHORT-NAME>McuCSIClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuCSIClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuCSIClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_CSI</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuCSIClk/McuClockCtrlSel</DEFINITION-REF>
                              <VALUE>ACTIVATE_ID_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuCSIClk/McuClockDivider</DEFINITION-REF>
                              <VALUE>31</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuCSIClk/McuClockValue</DEFINITION-REF>
                              <VALUE>24990000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="3377acd4-b89d-4da7-99e9-e1024272567f">
                          <SHORT-NAME>McuPOSTClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuPOSTClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuPOSTClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_POST</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuPOSTClk/McuClockCtrlSel</DEFINITION-REF>
                              <VALUE>ACTIVATE_ID_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuPOSTClk/McuClockDivider</DEFINITION-REF>
                              <VALUE>11</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuPOSTClk/McuClockValue</DEFINITION-REF>
                              <VALUE>66640000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="f2c3f16b-1b92-4477-acf6-dc86747da238">
                          <SHORT-NAME>McuPOST2Clk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuPOST2Clk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuPOST2Clk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_POST2</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuPOST2Clk/McuClockCtrlSel</DEFINITION-REF>
                              <VALUE>ACTIVATE_ID_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuPOST2Clk/McuClockDivider</DEFINITION-REF>
                              <VALUE>5</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuPOST2Clk/McuClockValue</DEFINITION-REF>
                              <VALUE>133280000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="347c8f55-8ebe-4a63-8ad0-aee602af42e4">
                          <SHORT-NAME>McuPOST3Clk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuPOST3Clk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuPOST3Clk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_POST3</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuPOST3Clk/McuClockCtrlSel</DEFINITION-REF>
                              <VALUE>ACTIVATE_ID_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuPOST3Clk/McuClockDivider</DEFINITION-REF>
                              <VALUE>11</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuPOST3Clk/McuClockValue</DEFINITION-REF>
                              <VALUE>66640000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="b41659bd-3639-4847-ae3e-da59cd3a884e">
                          <SHORT-NAME>McuPOST4Clk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuPOST4Clk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuPOST4Clk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_POST4</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuPOST4Clk/McuClockCtrlSel</DEFINITION-REF>
                              <VALUE>ACTIVATE_ID_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuPOST4Clk/McuClockDivider</DEFINITION-REF>
                              <VALUE>11</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuPOST4Clk/McuClockValue</DEFINITION-REF>
                              <VALUE>66640000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="ca988192-7737-44e6-9d57-7bd3883afb85">
                          <SHORT-NAME>McuDSIEXTClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuDSIEXTClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuDSIEXTClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_DSIEXT</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuDSIEXTClk/McuClockCtrlSel</DEFINITION-REF>
                              <VALUE>ACTIVATE_ID_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuDSIEXTClk/McuClockDivider</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuDSIEXTClk/McuClockValue</DEFINITION-REF>
                              <VALUE>799680000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="95a41507-21b5-4069-a19c-4f5e2a472674">
                          <SHORT-NAME>McuSASYNCRTClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSASYNCRTClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSASYNCRTClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_SASYNCRT</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSASYNCRTClk/McuClockValue</DEFINITION-REF>
                              <VALUE>16666667</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="b1784cbb-40b0-4f0f-8ee3-709aae371402">
                          <SHORT-NAME>McuSASYNCPERD1Clk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSASYNCPERD1Clk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSASYNCPERD1Clk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_SASYNCPERD1</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSASYNCPERD1Clk/McuClockValue</DEFINITION-REF>
                              <VALUE>266666667</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="91e49e91-768d-4d40-997c-8fe23a3b8a14">
                          <SHORT-NAME>McuSASYNCPERD2Clk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSASYNCPERD2Clk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSASYNCPERD2Clk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_SASYNCPERD2</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSASYNCPERD2Clk/McuClockValue</DEFINITION-REF>
                              <VALUE>133333333</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="541f3e9d-5963-4473-be7b-6140e1f3612b">
                          <SHORT-NAME>McuSASYNCPERD4Clk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSASYNCPERD4Clk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSASYNCPERD4Clk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_SASYNCPERD4</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSASYNCPERD4Clk/McuClockValue</DEFINITION-REF>
                              <VALUE>66666667</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="eb6a6625-71fb-44e2-8d4b-bbe57d2611da">
                          <SHORT-NAME>McuIMPAClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuIMPAClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuIMPAClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_IMPA</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuIMPAClk/McuClockSelection</DEFINITION-REF>
                              <VALUE>PLL5_ID_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuIMPAClk/McuClockValue</DEFINITION-REF>
                              <VALUE>800000000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="*************-48ef-b889-5e69d8cd3baa">
                          <SHORT-NAME>McuZXClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZXClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZXClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_ZX</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuZXClk/McuClockValue</DEFINITION-REF>
                              <VALUE>800000000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="4f1f4be4-37b1-4944-bc48-0ecec556580f">
                          <SHORT-NAME>McuSVVIPClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSVVIPClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSVVIPClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_SV_VIP</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSVVIPClk/McuClockValue</DEFINITION-REF>
                              <VALUE>640000000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="dd6cdc40-28f9-4d58-97be-820b6535f8e1">
                          <SHORT-NAME>McuSVIRClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSVIRClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSVIRClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_SV_IR</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuSVIRClk/McuClockValue</DEFINITION-REF>
                              <VALUE>640000000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="1e3ce1c0-291f-41f6-8a5d-4c7a0aa296c2">
                          <SHORT-NAME>McuS0HSCClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuS0HSCClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuS0HSCClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_S0_HSC</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuS0HSCClk/McuClockValue</DEFINITION-REF>
                              <VALUE>800000000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="03428138-0790-4816-b3c4-4df07fbc12d7">
                          <SHORT-NAME>McuS0VCClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuS0VCClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuS0VCClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_S0_VC</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuS0VCClk/McuClockValue</DEFINITION-REF>
                              <VALUE>800000000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="9d841714-004b-44e8-8cf3-8afcb27623d3">
                          <SHORT-NAME>McuS0VIOClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuS0VIOClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuS0VIOClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_S0_VIO</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuS0VIOClk/McuClockValue</DEFINITION-REF>
                              <VALUE>800000000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="9f47a0a0-6f5b-413f-9d64-bf196104351b">
                          <SHORT-NAME>McuIMPA1Clk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuIMPA1Clk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuIMPA1Clk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_IMPA1</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuIMPA1Clk/McuClockValue</DEFINITION-REF>
                              <VALUE>800000000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="ea31663b-661a-42b1-96ce-7fbf36146700">
                          <SHORT-NAME>McuVCBUSClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuVCBUSClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuVCBUSClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_VC</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuVCBUSClk/McuClockValue</DEFINITION-REF>
                              <VALUE>533330000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="3f6e36aa-ad44-45b9-909d-24c5d8d8817e">
                          <SHORT-NAME>McuVIOBUSClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuVIOBUSClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuVIOBUSClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_VIO</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuVIOBUSClk/McuClockValue</DEFINITION-REF>
                              <VALUE>533330000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="87710be9-3091-46c1-bdf7-5fd0104fe721">
                          <SHORT-NAME>McuRCLKClk</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuRCLKClk</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuRCLKClk/McuClockName</DEFINITION-REF>
                              <VALUE>CLK_RCLK</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuRCLKClk/McuClockSelection</DEFINITION-REF>
                              <VALUE>CLK_OCO</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSetting/McuRCLKClk/McuClockValue</DEFINITION-REF>
                              <VALUE>32800</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE UUID="634915dc-55b5-448b-8d74-5dfe70906bd5">
                      <SHORT-NAME>McuPllClockSetting</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuMainOsc</DEFINITION-REF>
                          <VALUE>CLOCK_FREQUENCY_16_66_MHZ</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE UUID="533814fa-1e55-414a-9419-be93610ea132">
                          <SHORT-NAME>McuPll1ClockSetting</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll1ClockSetting</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll1ClockSetting/McuPllCircuitEnable</DEFINITION-REF>
                              <VALUE>true</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll1ClockSetting/McuPllFrequency</DEFINITION-REF>
                              <VALUE>3200000000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE UUID="7657b130-4b6e-4eb6-83db-57a6bfd6a90a">
                              <SHORT-NAME>McuPllStopConditions</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll1ClockSetting/McuPllStopConditions</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll1ClockSetting/McuPllStopConditions/McuPllStopByA3IR</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll1ClockSetting/McuPllStopConditions/McuPllStopBy3DGB</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll1ClockSetting/McuPllStopConditions/McuPllStopByA3ISP0</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll1ClockSetting/McuPllStopConditions/McuPllStopByA3ISP1</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll1ClockSetting/McuPllStopConditions/McuPllStopByA3DUL</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll1ClockSetting/McuPllStopConditions/McuPllStopByA3VIP0</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll1ClockSetting/McuPllStopConditions/McuPllStopByA3VIP1</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll1ClockSetting/McuPllStopConditions/McuPllStopByA3VIP2</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll1ClockSetting/McuPllStopConditions/McuPllStopByA2E0D1</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll1ClockSetting/McuPllStopConditions/McuPllStopByA2E0D0</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="fbbe6a0f-fc2b-4c34-bacc-a24f78b3cb0b">
                          <SHORT-NAME>McuPll2ClockSetting</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll2ClockSetting</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll2ClockSetting/McuPllCircuitEnable</DEFINITION-REF>
                              <VALUE>true</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll2ClockSetting/McuMultiplicationRatio</DEFINITION-REF>
                              <VALUE>101</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll2ClockSetting/McuFreqDitherMode</DEFINITION-REF>
                              <VALUE>INTEGER_FIXED_FREQUENCY_MODE_ID_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll2ClockSetting/McuDownSpreadModuleDepth</DEFINITION-REF>
                              <VALUE>SSDEPT_VALUE_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll2ClockSetting/McuFractionalMultiplication</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll2ClockSetting/McuSSCGModulationFreq</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll2ClockSetting/McuPllFrequency</DEFINITION-REF>
                              <VALUE>3398640000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE UUID="a7898996-9803-4f4c-b78a-08728c72aa40">
                              <SHORT-NAME>McuPllStopConditions</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll2ClockSetting/McuPllStopConditions</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll2ClockSetting/McuPllStopConditions/McuPllStopByA3IR</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll2ClockSetting/McuPllStopConditions/McuPllStopBy3DGB</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll2ClockSetting/McuPllStopConditions/McuPllStopByA3ISP1</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll2ClockSetting/McuPllStopConditions/McuPllStopByA3ISP0</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll2ClockSetting/McuPllStopConditions/McuPllStopByA3DUL</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll2ClockSetting/McuPllStopConditions/McuPllStopByA3VIP0</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll2ClockSetting/McuPllStopConditions/McuPllStopByA3VIP1</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll2ClockSetting/McuPllStopConditions/McuPllStopByA3VIP2</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="66af972a-190f-4b65-b89f-71ddc711a9d1">
                          <SHORT-NAME>McuPll3ClockSetting</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll3ClockSetting</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll3ClockSetting/McuMultiplicationRatio</DEFINITION-REF>
                              <VALUE>95</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll3ClockSetting/McuFreqDitherMode</DEFINITION-REF>
                              <VALUE>INTEGER_FIXED_FREQUENCY_MODE_ID_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll3ClockSetting/McuFractionalMultiplication</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll3ClockSetting/McuPllFrequency</DEFINITION-REF>
                              <VALUE>3198720000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll3ClockSetting/McuPllCircuitEnable</DEFINITION-REF>
                              <VALUE>true</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE UUID="b49d242f-15c7-4414-a8cc-d1994430bd1d">
                              <SHORT-NAME>McuPllStopConditions</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll3ClockSetting/McuPllStopConditions</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll3ClockSetting/McuPllStopConditions/McuPllStopByA3IR</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll3ClockSetting/McuPllStopConditions/McuPllStopBy3DGB</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll3ClockSetting/McuPllStopConditions/McuPllStopByA3ISP1</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll3ClockSetting/McuPllStopConditions/McuPllStopByA3ISP0</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll3ClockSetting/McuPllStopConditions/McuPllStopByA3DUL</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll3ClockSetting/McuPllStopConditions/McuPllStopByA3VIP0</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll3ClockSetting/McuPllStopConditions/McuPllStopByA3VIP1</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll3ClockSetting/McuPllStopConditions/McuPllStopByA3VIP2</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll3ClockSetting/McuPllStopConditions/McuPllStopByA2E0D1</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll3ClockSetting/McuPllStopConditions/McuPllStopByA2E0D0</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="4b173010-fe52-49da-b4ec-df2d7f563521">
                          <SHORT-NAME>McuPll4ClockSetting</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll4ClockSetting</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll4ClockSetting/McuPllCircuitEnable</DEFINITION-REF>
                              <VALUE>true</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll4ClockSetting/McuMultiplicationRatio</DEFINITION-REF>
                              <VALUE>71</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll4ClockSetting/McuFreqDitherMode</DEFINITION-REF>
                              <VALUE>INTEGER_FIXED_FREQUENCY_MODE_ID_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll4ClockSetting/McuDownSpreadModuleDepth</DEFINITION-REF>
                              <VALUE>SSDEPT_VALUE_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll4ClockSetting/McuFractionalMultiplication</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll4ClockSetting/McuSSCGModulationFreq</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll4ClockSetting/McuPllFrequency</DEFINITION-REF>
                              <VALUE>2399040000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE UUID="e72a3b2c-560e-4df4-9125-35670a808a18">
                              <SHORT-NAME>McuPllStopConditions</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll4ClockSetting/McuPllStopConditions</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll4ClockSetting/McuPllStopConditions/McuPllStopByA3IR</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll4ClockSetting/McuPllStopConditions/McuPllStopBy3DGB</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll4ClockSetting/McuPllStopConditions/McuPllStopByA3ISP1</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll4ClockSetting/McuPllStopConditions/McuPllStopByA3ISP0</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll4ClockSetting/McuPllStopConditions/McuPllStopByA3DUL</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll4ClockSetting/McuPllStopConditions/McuPllStopByA3VIP0</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll4ClockSetting/McuPllStopConditions/McuPllStopByA3VIP1</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll4ClockSetting/McuPllStopConditions/McuPllStopByA3VIP2</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll4ClockSetting/McuPllStopConditions/McuPllStopByA2E0D1</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll4ClockSetting/McuPllStopConditions/McuPllStopByA2E0D0</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="2cc6f6a7-eb86-4b24-95ed-dbc2613de681">
                          <SHORT-NAME>McuPll5ClockSetting</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll5ClockSetting</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll5ClockSetting/McuPllCircuitEnable</DEFINITION-REF>
                              <VALUE>true</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll5ClockSetting/McuPllFrequency</DEFINITION-REF>
                              <VALUE>3198720000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll5ClockSetting/McuMultiplicationRatio</DEFINITION-REF>
                              <VALUE>95</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll5ClockSetting/McuFreqDitherMode</DEFINITION-REF>
                              <VALUE>INTEGER_FIXED_FREQUENCY_MODE_ID_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE UUID="93c59c11-d1d2-4b5a-b520-5fc45fe95dc1">
                              <SHORT-NAME>McuPllStopConditions</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll5ClockSetting/McuPllStopConditions</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll5ClockSetting/McuPllStopConditions/McuPllStopByA3IR</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll5ClockSetting/McuPllStopConditions/McuPllStopBy3DGB</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll5ClockSetting/McuPllStopConditions/McuPllStopByA3ISP1</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll5ClockSetting/McuPllStopConditions/McuPllStopByA3ISP0</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll5ClockSetting/McuPllStopConditions/McuPllStopByA3DUL</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll5ClockSetting/McuPllStopConditions/McuPllStopByA3VIP0</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll5ClockSetting/McuPllStopConditions/McuPllStopByA3VIP1</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll5ClockSetting/McuPllStopConditions/McuPllStopByA3VIP2</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll5ClockSetting/McuPllStopConditions/McuPllStopByA2E0D1</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll5ClockSetting/McuPllStopConditions/McuPllStopByA2E0D0</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="6be1f6cd-f945-4995-8329-66928fa1d439">
                          <SHORT-NAME>McuPll6ClockSetting</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll6ClockSetting</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll6ClockSetting/McuPllCircuitEnable</DEFINITION-REF>
                              <VALUE>true</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll6ClockSetting/McuMultiplicationRatio</DEFINITION-REF>
                              <VALUE>83</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll6ClockSetting/McuFreqDitherMode</DEFINITION-REF>
                              <VALUE>INTEGER_FIXED_FREQUENCY_MODE_ID_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll6ClockSetting/McuDownSpreadModuleDepth</DEFINITION-REF>
                              <VALUE>SSDEPT_VALUE_0</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll6ClockSetting/McuFractionalMultiplication</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll6ClockSetting/McuSSCGModulationFreq</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll6ClockSetting/McuPllFrequency</DEFINITION-REF>
                              <VALUE>2798880000</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE UUID="3ebf6236-dde6-487d-9cc6-edb9838fc7de">
                              <SHORT-NAME>McuPllStopConditions</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll6ClockSetting/McuPllStopConditions</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll6ClockSetting/McuPllStopConditions/McuPllStopByA3IR</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll6ClockSetting/McuPllStopConditions/McuPllStopBy3DGB</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll6ClockSetting/McuPllStopConditions/McuPllStopByA3ISP1</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll6ClockSetting/McuPllStopConditions/McuPllStopByA3ISP0</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll6ClockSetting/McuPllStopConditions/McuPllStopByA3DUL</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll6ClockSetting/McuPllStopConditions/McuPllStopByA3VIP0</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll6ClockSetting/McuPllStopConditions/McuPllStopByA3VIP1</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll6ClockSetting/McuPllStopConditions/McuPllStopByA3VIP2</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll6ClockSetting/McuPllStopConditions/McuPllStopByA2E0D1</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuPllClockSetting/McuPll6ClockSetting/McuPllStopConditions/McuPllStopByA2E0D0</DEFINITION-REF>
                                  <VALUE>false</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE UUID="6912a944-55aa-464b-9572-5f85b7930112">
                      <SHORT-NAME>McuClockReferencePoint</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint/McuClockReferencePointFrequency</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE UUID="a971f6af-6694-4af4-b139-08f3742b9f75">
                      <SHORT-NAME>McuModuleClockSupplySetting</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuIMPPSCClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuIMPDMAC0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuIMP1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuIMP0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuSPMCClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuIMPCNNClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuISP1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuISP0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuUMFL0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuSMPS0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuSMPO0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuRGXClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuADGClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuSPMIClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuIMPSLVClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuIMPDTAClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuIMPDMAC1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuIMP3ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuIMP2ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuAVB2ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuAVB1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuAVB0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuCSITOP0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuCANFDClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuDSITXLINK1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuDSITXLINK0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuDOC2CHClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuDIS0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuCSITOP1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuIMS1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuIMS0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuIMR2ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuIMR1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuIMR0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuFRAY00ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuFCPVD1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuFCPVD0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuFCPCSClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuPWMClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuMSI5ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuMSI4ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuIVCP1EClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuISPCS1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuISPCS0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuINTTPClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuIPCClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVIN01ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVIN00ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVCPL4ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuTPU0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVSPD1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVSPD0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVIN17ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVIN16ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVIN15ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVIN14ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVIN13ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVIN12ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVIN11ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVIN10ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVIN07ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVIN06ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVIN05ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVIN04ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVIN03ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVIN02ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuPFC3ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuPFC2ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuPFC1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVSPX1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVSPX0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuFCPVX1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuFCPVX0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuMTIClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuCSBRGIRA2ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuCSBRGIRA3ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuTSNClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuIMPSDMAC1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuIMPSDMAC0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuCVE3ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuCVE2ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuCVE1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuCVE0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuDSCClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuFCPRCClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuSSIClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuSSIUClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuADVFSCClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuCR0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuINTAPClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuI2C5ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuI2C4ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuI2C3ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuI2C2ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuI2C1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuI2C0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuHSCIF3ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuHSCIF2ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuHSCIF1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuHSCIF0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuRTDM1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuRTDM0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuRPCClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuPCIE1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuPCIE0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuMSI3ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuMSI2ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuMSI1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuMSI0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuIRQCClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuTMU4ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuTMU3ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuTMU2ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuTMU1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuTMU0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuSYDM2ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuSYDM1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuSECROMClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuSDHI0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuSCIF4ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuSCIF3ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuSCIF1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuSCIF0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuRTDM3ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuRTDM2ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuUCMTClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuPFC0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuCMT3ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuCMT2ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuCMT1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuCMT0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuWDTClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuWCRC3ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuWCRC2ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuWCRC1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuWCRC0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuKCRC4ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuFSOClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuCRC3ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuCRC2ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuCRC1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuCRC0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuADVFSClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuKCRC7ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuKCRC6ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuKCRC5ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuAESACCWrapperClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuAESACC0ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuAESACC1ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuAESACC2ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuAESACC3ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuAESACC4ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuAESACC5ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuAESACC6ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuAESACC7ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuPAPDEBUGClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuTSCClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuLPDDRMSTPCR4b09ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuLPDDRMSTPCR4b10ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP1MSTPCR28b31ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP1MSTPCR28b30ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP0MSTPCR28b28ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP0MSTPCR28b27ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP0MSTPCR28b26ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP0MSTPCR28b25ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP0MSTPCR28b24ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP0MSTPCR28b23ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP0MSTPCR28b22ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP0MSTPCR28b21ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuPAPSDMACClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuPAPTOPClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuPAPBUSClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP3MSTPCR28b04ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP2MSTPCR28b03ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP1MSTPCR28b02ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP0MSTPCR28b01ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP3MSTPCR29b23ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP3MSTPCR29b22ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP3MSTPCR29b21ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP3MSTPCR29b20ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP3MSTPCR29b19ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP3MSTPCR29b18ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP3MSTPCR29b17ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP3MSTPCR29b16ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP2MSTPCR29b14ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP2MSTPCR29b13ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP2MSTPCR29b12ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP2MSTPCR29b11ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP2MSTPCR29b10ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP2MSTPCR29b09ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP2MSTPCR29b08ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP2MSTPCR29b07ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP1MSTPCR29b05ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP1MSTPCR29b04ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP1MSTPCR29b03ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP1MSTPCR29b02ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP1MSTPCR29b01ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuModuleClockSupplySetting/McuVDSP1MSTPCR29b00ClockSupplyEnable</DEFINITION-REF>
                          <VALUE>true</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="45566a30-ec3e-438a-b49a-14223c19e856">
                  <SHORT-NAME>McuDemEventParameterRefs</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuDemEventParameterRefs</DEFINITION-REF>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuDemEventParameterRefs/MCU_E_CLOCK_FAILURE</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dem/DemConfigSet/DemEventParameter</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="86515f96-0656-445a-a184-51544e0962a6">
                  <SHORT-NAME>McuRamSectorSettingConf</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuRamSectorSettingConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuRamSectorSettingConf/McuRamDefaultValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuRamSectorSettingConf/McuRamSectionBaseAddress</DEFINITION-REF>
                      <VALUE>3861905408</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuRamSectorSettingConf/McuRamSectionSize</DEFINITION-REF>
                      <VALUE>1016</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuRamSectorSettingConf/McuRamSectionWriteSize</DEFINITION-REF>
                      <VALUE>4</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="0890dbea-28f7-4a7d-b350-ab099e635e32">
                  <SHORT-NAME>McuRamSectorSettingConf_001</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuRamSectorSettingConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuRamSectorSettingConf/McuRamDefaultValue</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuRamSectorSettingConf/McuRamSectionBaseAddress</DEFINITION-REF>
                      <VALUE>3944742912</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuRamSectorSettingConf/McuRamSectionSize</DEFINITION-REF>
                      <VALUE>4</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuRamSectorSettingConf/McuRamSectionWriteSize</DEFINITION-REF>
                      <VALUE>4</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="bdb9b572-5c2b-40c5-8e1d-27d2538f2c7a">
                  <SHORT-NAME>McuRamSectorSettingConf_002</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuRamSectorSettingConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuRamSectorSettingConf/McuRamDefaultValue</DEFINITION-REF>
                      <VALUE>2</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuRamSectorSettingConf/McuRamSectionBaseAddress</DEFINITION-REF>
                      <VALUE>3794796544</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuRamSectorSettingConf/McuRamSectionSize</DEFINITION-REF>
                      <VALUE>4</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuModuleConfiguration/McuRamSectorSettingConf/McuRamSectionWriteSize</DEFINITION-REF>
                      <VALUE>4</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE UUID="7670e4fb-e8d1-48ed-9cc2-2ac367a7f9dc">
              <SHORT-NAME>McuPublishedInformation</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuPublishedInformation</DEFINITION-REF>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE UUID="909212fe-c6e3-411a-8436-452c8a6589ba">
                  <SHORT-NAME>McuResetReasonConf</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuPublishedInformation/McuResetReasonConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuPublishedInformation/McuResetReasonConf/McuResetReason</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="9cf6d7cd-4d1c-453e-90c6-6655b930104d">
                  <SHORT-NAME>McuPowerOnResetConf</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuPublishedInformation/McuPowerOnResetConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuPublishedInformation/McuPowerOnResetConf/McuResetReason</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="b425e1e3-9847-4ae6-9490-b782bc6d81d6">
                  <SHORT-NAME>McuSWDTResetConf</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuPublishedInformation/McuSWDTResetConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuPublishedInformation/McuSWDTResetConf/McuResetReason</DEFINITION-REF>
                      <VALUE>2</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="29186db6-a2c6-474b-a2fa-92c76f3bb7af">
                  <SHORT-NAME>McuRWDTResetConf</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuPublishedInformation/McuRWDTResetConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuPublishedInformation/McuRWDTResetConf/McuResetReason</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="68d879bb-3236-4677-8b9c-1a8cc8fb6dda">
                  <SHORT-NAME>McuMultiResetConf</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuPublishedInformation/McuMultiResetConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuPublishedInformation/McuMultiResetConf/McuResetReason</DEFINITION-REF>
                      <VALUE>5</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="d08824d3-4ff6-49b7-b4ce-fc0298bf0625">
                  <SHORT-NAME>McuResetUndefinedConf</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuPublishedInformation/McuResetUndefinedConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuPublishedInformation/McuResetUndefinedConf/McuResetReason</DEFINITION-REF>
                      <VALUE>7</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="d3f8f28e-8289-40f8-87f0-9437a213610c">
                  <SHORT-NAME>McuNoneResetConf</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuPublishedInformation/McuNoneResetConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuPublishedInformation/McuNoneResetConf/McuResetReason</DEFINITION-REF>
                      <VALUE>6</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="a798a8c2-d643-46ca-947a-fc202ad1c50a">
                  <SHORT-NAME>McuSwResetConf</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuPublishedInformation/McuSwResetConf</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/Renesas/EcucDefs_Mcu/Mcu/McuPublishedInformation/McuSwResetConf/McuResetReason</DEFINITION-REF>
                      <VALUE>3</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
          </CONTAINERS>
        </ECUC-MODULE-CONFIGURATION-VALUES>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
