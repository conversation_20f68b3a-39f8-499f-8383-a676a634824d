/*********************************************************************************************************************
 *  COPYRIGHT                                                                                                        *
 *  ---------------------------------------------------------------------------------------------------------------- *
 *  Copyright (c) 2013 - 2021 by Vector Informatik GmbH.                                        All rights reserved. *
 *                                                                                                                   *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.                    *
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.       *
 *                All other rights remain with Vector Informatik GmbH.                                               *
 *  ---------------------------------------------------------------------------------------------------------------- *
 *  FILE DESCRIPTION                                                                                                 *
 *  ---------------------------------------------------------------------------------------------------------------- *
 *             File: McData.a2l                                                                                      *
 *      Description: MICROSAR A2L file                                                                               *
 *     Generated at: 2021-10-24 10:22:16                                                                             *
 *        Generator: MICROSAR McDataConverter, Version ********                                                      *
 *  ---------------------------------------------------------------------------------------------------------------- *
 *  CUSTOMER INFORMATION                                                                                             *
 *  ---------------------------------------------------------------------------------------------------------------- *
 *         Customer: SZ DJI Technologies Co., Ltd.                                                                   *
 *    Serial Number: CBD2000702                                                                                      *
 *  Delivery Number: 0                                                                                               *
 *      Expiry Date: 2023-05-31                                                                                      *
 *********************************************************************************************************************/


/*****************************************************************************
 * RECORD_LAYOUT                                                             *
 *****************************************************************************/

/begin RECORD_LAYOUT 
    VECTOR_RL_BOOLEAN                             /* Name */
    FNC_VALUES                              /* Fnc Values */
        1                                     /* Position */
        UBYTE                                /* Data Type */
        ROW_DIR                             /* Index Mode */
        DIRECT                            /* Address Type */
/end RECORD_LAYOUT

/begin RECORD_LAYOUT 
    VECTOR_RL_UBYTE                               /* Name */
    FNC_VALUES                              /* Fnc Values */
        1                                     /* Position */
        UBYTE                                /* Data Type */
        ROW_DIR                             /* Index Mode */
        DIRECT                            /* Address Type */
/end RECORD_LAYOUT

/begin RECORD_LAYOUT 
    VECTOR_RL_UWORD                               /* Name */
    FNC_VALUES                              /* Fnc Values */
        1                                     /* Position */
        UWORD                                /* Data Type */
        ROW_DIR                             /* Index Mode */
        DIRECT                            /* Address Type */
/end RECORD_LAYOUT

/begin RECORD_LAYOUT 
    VECTOR_RL_ULONG                               /* Name */
    FNC_VALUES                              /* Fnc Values */
        1                                     /* Position */
        ULONG                                /* Data Type */
        ROW_DIR                             /* Index Mode */
        DIRECT                            /* Address Type */
/end RECORD_LAYOUT

/begin RECORD_LAYOUT 
    VECTOR_RL_SBYTE                               /* Name */
    FNC_VALUES                              /* Fnc Values */
        1                                     /* Position */
        SBYTE                                /* Data Type */
        ROW_DIR                             /* Index Mode */
        DIRECT                            /* Address Type */
/end RECORD_LAYOUT

/begin RECORD_LAYOUT 
    VECTOR_RL_SWORD                               /* Name */
    FNC_VALUES                              /* Fnc Values */
        1                                     /* Position */
        SWORD                                /* Data Type */
        ROW_DIR                             /* Index Mode */
        DIRECT                            /* Address Type */
/end RECORD_LAYOUT

/begin RECORD_LAYOUT 
    VECTOR_RL_SLONG                               /* Name */
    FNC_VALUES                              /* Fnc Values */
        1                                     /* Position */
        SLONG                                /* Data Type */
        ROW_DIR                             /* Index Mode */
        DIRECT                            /* Address Type */
/end RECORD_LAYOUT

/begin RECORD_LAYOUT 
    VECTOR_RL_FLOAT32_IEEE                        /* Name */
    FNC_VALUES                              /* Fnc Values */
        1                                     /* Position */
        FLOAT32_IEEE                         /* Data Type */
        ROW_DIR                             /* Index Mode */
        DIRECT                            /* Address Type */
/end RECORD_LAYOUT

/begin RECORD_LAYOUT 
    VECTOR_RL_FLOAT64_IEEE                        /* Name */
    FNC_VALUES                              /* Fnc Values */
        1                                     /* Position */
        FLOAT64_IEEE                         /* Data Type */
        ROW_DIR                             /* Index Mode */
        DIRECT                            /* Address Type */
/end RECORD_LAYOUT


/*****************************************************************************
 * CHARACTERISTIC                                                            *
 *****************************************************************************/


/*****************************************************************************
 * MEASUREMENT                                                               *
 *****************************************************************************/


/*****************************************************************************
 * COMPU_METHOD                                                              *
 *****************************************************************************/


/*****************************************************************************
 * COMPU_VTAB_RANGE                                                          *
 *****************************************************************************/


/*****************************************************************************
 * GROUP                                                                     *
 *****************************************************************************/

/begin GROUP 
    BswM                                          /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    CanIf                                         /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    CanNm                                         /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    CanSM                                         /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    CanTp                                         /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    Com                                           /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    ComM                                          /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    EcuM                                          /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    Nm                                            /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

/begin GROUP 
    PduR                                          /* Name */
    ""                                 /* Long Identifier */
    /begin REF_CHARACTERISTIC 
    /end REF_CHARACTERISTIC
    /begin REF_MEASUREMENT 
    /end REF_MEASUREMENT
/end GROUP

