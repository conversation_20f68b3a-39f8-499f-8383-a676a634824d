<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
      <SHORT-NAME>MICROSAR</SHORT-NAME>
      <ELEMENTS>
        <BSW-IMPLEMENTATION UUID="487e2e0d-92c2-4469-a75f-21e4b5b270a5">
          <SHORT-NAME>EcuM_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>10.00.02</SW-VERSION>
          <USED-CODE-GENERATOR>Da<PERSON><PERSON><PERSON> Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
        </BSW-IMPLEMENTATION>
      </ELEMENTS>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="2452f37c-822d-4ea2-9c5a-df7089e4511a">
          <SHORT-NAME>EcuM_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="2d1b5292-4d7e-48e9-8f70-453887eb1879">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="b7a49603-a6d0-4725-bc7f-5daa929d7035">
                  <SHORT-NAME>EcuM</SHORT-NAME>
                  <PROVIDED-ENTRYS>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/EcuM_ib_bswmd/BswModuleDescriptions/EcuM_MainFunction</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  </PROVIDED-ENTRYS>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="5dbcc7f9-698c-4eaa-b976-fcf930dd63c0">
                      <SHORT-NAME>EcuMBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="b1c32a6b-b2e8-487a-b8aa-c39e2b6c50b6">
                          <SHORT-NAME>ECUM_EXCLUSIVE_AREA_0</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">This exclusive area shall disable all wake up interrupts to prevent overwriting of some global variables.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="82c680a3-9fb5-455f-91ee-03909bd60b10">
                          <SHORT-NAME>ECUM_EXCLUSIVE_AREA_1</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">This exclusive area is only used in case of multi core and if the the EcuM is configured to handle also the slave cores. It must be configured as a spinlock to allow access to some variables simultaneously from multiple cores.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="ce9176ed-54f5-4d66-8e24-495cc4505842">
                          <SHORT-NAME>ECUM_EXCLUSIVE_AREA_2</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">This exclusive area is used for accessing the clock variable. It shall disable GPT interrupts and task changes. Only necessary if 32 Bit access is not atomic and Alarm Clock is present.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="28e2cbb3-7506-4106-92e3-2b7bdf7ca406">
                          <SHORT-NAME>ECUM_EXCLUSIVE_AREA_3</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">This This exclusive area must be configured as a spin lock to protect access on global shutdown target variables.</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="c20137e6-4aa1-41b2-8579-8e978fe6a6e1">
                          <SHORT-NAME>EcuM_MainFunction</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/EcuM_ib_bswmd/BswModuleDescriptions/EcuM_MainFunction</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="62387681-f175-4945-b9f5-acc77393df34">
                          <SHORT-NAME>EcuM_MainFunctionTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/EcuM_ib_bswmd/BswModuleDescriptions/EcuM/EcuMBehavior/EcuM_MainFunction</STARTS-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </BSW-TIMING-EVENT>
                        <BSW-TIMING-EVENT UUID="7fa16219-1cc9-4b78-a476-4e2e58bcb324">
                          <SHORT-NAME>EcuM_MainFunctionTimingEvent1</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/EcuM_ib_bswmd/BswModuleDescriptions/EcuM/EcuMBehavior/EcuM_MainFunction</STARTS-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="9bb5c099-cfc2-4be8-b5a2-0ceebaa65eeb">
                  <SHORT-NAME>EcuM_MainFunction</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="d722b4f1-85c4-4324-b704-4e797289801b">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
