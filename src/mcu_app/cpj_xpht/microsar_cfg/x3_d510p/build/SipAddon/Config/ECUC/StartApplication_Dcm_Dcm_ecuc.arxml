<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <ADMIN-DATA/>
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>ActiveEcuC</SHORT-NAME>
      <ELEMENTS>
        <ECUC-MODULE-CONFIGURATION-VALUES UUID="93c545c7-3f20-4a87-adb5-2bf44ac1c91f">
          <SHORT-NAME>Dcm</SHORT-NAME>
          <DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/Dcm</DEFINITION-REF>
          <IMPLEMENTATION-CONFIG-VARIANT>VARIANT-PRE-COMPILE</IMPLEMENTATION-CONFIG-VARIANT>
          <MODULE-DESCRIPTION-REF DEST="BSW-IMPLEMENTATION">/MICROSAR/Dcm_Impl</MODULE-DESCRIPTION-REF>
          <CONTAINERS>
            <ECUC-CONTAINER-VALUE UUID="cfb0b45b-45c0-4159-91b3-315958361c48">
              <SHORT-NAME>DcmConfigSet</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet</DEFINITION-REF>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE UUID="99e1ff9b-4928-431d-a166-9edd55bd5eb6">
                  <SHORT-NAME>DcmDsl</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl</DEFINITION-REF>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE UUID="36eb0af5-26f8-46b0-bd4c-88754f2d4817">
                      <SHORT-NAME>DcmDslProtocol</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol</DEFINITION-REF>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE UUID="6074c348-bc84-4716-a3e2-23d4c18432c0">
                          <SHORT-NAME>DcmDslProtocolRow_29812fbf</SHORT-NAME>
                          <ANNOTATIONS>
                            <ANNOTATION>
                              <LABEL>
                                <L-4 L="FOR-ALL">DcmDslConnection_53df21b8</L-4>
                              </LABEL>
                              <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                            </ANNOTATION>
                            <ANNOTATION>
                              <LABEL>
                                <L-4 L="FOR-ALL">DcmDslConnection_501c1b3a</L-4>
                              </LABEL>
                              <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                            </ANNOTATION>
                          </ANNOTATIONS>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslProtocolEndiannessConvEnabled</DEFINITION-REF>
                              <VALUE>false</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslProtocolID</DEFINITION-REF>
                              <VALUE>DCM_UDS_ON_CAN</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslProtocolPreemptTimeout</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslProtocolPriority</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmSendRespPendOnTransToBoot</DEFINITION-REF>
                              <VALUE>true</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmTimStrP2ServerAdjust</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmTimStrP2StarServerAdjust</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslProtocolMaximumResponseSize</DEFINITION-REF>
                              <VALUE>4095</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslProtocolRxBufferID</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsl/DcmDslBuffer</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslProtocolSIDTable</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslProtocolTxBufferID</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsl/DcmDslBuffer</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-SYMBOLIC-NAME-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDemClientRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dem/DemGeneral/DemClient</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE UUID="f62d295d-b64a-4258-9f8f-1ba77e8137af">
                              <SHORT-NAME>DcmDslConnection_55b73075</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-CHOICE-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection</DEFINITION-REF>
                              <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE UUID="1f252796-8ac7-4325-b0d1-db9070423b8b">
                                  <SHORT-NAME>DcmDslMainConnection</SHORT-NAME>
                                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection</DEFINITION-REF>
                                  <PARAMETER-VALUES>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRxTesterSourceAddr</DEFINITION-REF>
                                      <VALUE>0</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRxConnectionId</DEFINITION-REF>
                                      <VALUE>0</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                  </PARAMETER-VALUES>
                                  <SUB-CONTAINERS>
                                    <ECUC-CONTAINER-VALUE UUID="d9ae3c7a-6896-43d6-96a6-d82e77c66467">
                                      <SHORT-NAME>DiagReq_oCANB_56e1ec3e_Rx_8105b5ac</SHORT-NAME>
                                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx</DEFINITION-REF>
                                      <PARAMETER-VALUES>
                                        <ECUC-TEXTUAL-PARAM-VALUE>
                                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx/DcmDslProtocolRxAddrType</DEFINITION-REF>
                                          <VALUE>DCM_PHYSICAL_TYPE</VALUE>
                                        </ECUC-TEXTUAL-PARAM-VALUE>
                                        <ECUC-NUMERICAL-PARAM-VALUE>
                                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx/DcmDslProtocolRxPduId</DEFINITION-REF>
                                          <VALUE>2</VALUE>
                                        </ECUC-NUMERICAL-PARAM-VALUE>
                                        <ECUC-NUMERICAL-PARAM-VALUE>
                                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx/DcmDslProtocolRxChannelId</DEFINITION-REF>
                                          <VALUE>0</VALUE>
                                        </ECUC-NUMERICAL-PARAM-VALUE>
                                      </PARAMETER-VALUES>
                                      <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                          <DEFINITION-REF DEST="ECUC-SYMBOLIC-NAME-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx/DcmDslProtocolRxComMChannelRef</DEFINITION-REF>
                                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/ComM/ComMConfigSet/CN_CANB_5813403e</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx/DcmDslProtocolRxPduRef</DEFINITION-REF>
                                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/EcuC/EcucPduCollection/DiagReq_oCANB_56e1ec3e_Rx</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                      </REFERENCE-VALUES>
                                    </ECUC-CONTAINER-VALUE>
                                    <ECUC-CONTAINER-VALUE UUID="fa9af641-f30d-411c-8ad3-7f387d386f6f">
                                      <SHORT-NAME>Diag_FuncReq_7DF_oCANB_9ddc7b37_Rx_c097ee87</SHORT-NAME>
                                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx</DEFINITION-REF>
                                      <PARAMETER-VALUES>
                                        <ECUC-TEXTUAL-PARAM-VALUE>
                                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx/DcmDslProtocolRxAddrType</DEFINITION-REF>
                                          <VALUE>DCM_FUNCTIONAL_TYPE</VALUE>
                                        </ECUC-TEXTUAL-PARAM-VALUE>
                                        <ECUC-NUMERICAL-PARAM-VALUE>
                                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx/DcmDslProtocolRxPduId</DEFINITION-REF>
                                          <VALUE>3</VALUE>
                                        </ECUC-NUMERICAL-PARAM-VALUE>
                                        <ECUC-NUMERICAL-PARAM-VALUE>
                                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx/DcmDslProtocolRxChannelId</DEFINITION-REF>
                                          <VALUE>0</VALUE>
                                        </ECUC-NUMERICAL-PARAM-VALUE>
                                      </PARAMETER-VALUES>
                                      <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                          <DEFINITION-REF DEST="ECUC-SYMBOLIC-NAME-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx/DcmDslProtocolRxComMChannelRef</DEFINITION-REF>
                                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/ComM/ComMConfigSet/CN_CANB_5813403e</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx/DcmDslProtocolRxPduRef</DEFINITION-REF>
                                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/EcuC/EcucPduCollection/Diag_FuncReq_7DF_oCANB_9ddc7b37_Rx</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                      </REFERENCE-VALUES>
                                    </ECUC-CONTAINER-VALUE>
                                    <ECUC-CONTAINER-VALUE UUID="20c66514-16f6-3076-a67c-c906e60e86af">
                                      <SHORT-NAME>DiagResp_oCANB_fd9c786c_Tx_5f14cb23</SHORT-NAME>
                                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolTx</DEFINITION-REF>
                                      <PARAMETER-VALUES>
                                        <ECUC-NUMERICAL-PARAM-VALUE>
                                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolTx/DcmDslTxConfirmationPduId</DEFINITION-REF>
                                          <VALUE>1</VALUE>
                                        </ECUC-NUMERICAL-PARAM-VALUE>
                                      </PARAMETER-VALUES>
                                      <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolTx/DcmDslProtocolTxPduRef</DEFINITION-REF>
                                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/EcuC/EcucPduCollection/DiagResp_oCANB_fd9c786c_Tx</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                      </REFERENCE-VALUES>
                                    </ECUC-CONTAINER-VALUE>
                                  </SUB-CONTAINERS>
                                </ECUC-CONTAINER-VALUE>
                              </SUB-CONTAINERS>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE UUID="fe7b5c78-63e8-4a60-8ee0-76f33ee0e0b7">
                              <SHORT-NAME>DcmDslConnection_6a53ee01</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-CHOICE-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection</DEFINITION-REF>
                              <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE UUID="31fd871f-40e3-4fba-9cbf-86e988fb8d04">
                                  <SHORT-NAME>DcmDslMainConnection</SHORT-NAME>
                                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection</DEFINITION-REF>
                                  <PARAMETER-VALUES>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRxTesterSourceAddr</DEFINITION-REF>
                                      <VALUE>0</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRxConnectionId</DEFINITION-REF>
                                      <VALUE>1</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                  </PARAMETER-VALUES>
                                  <SUB-CONTAINERS>
                                    <ECUC-CONTAINER-VALUE UUID="b7ca73a0-386c-41a8-aad2-48f6f221f326">
                                      <SHORT-NAME>DiagReq_oCANB_20_583b7280_Rx_d691e0b8</SHORT-NAME>
                                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx</DEFINITION-REF>
                                      <PARAMETER-VALUES>
                                        <ECUC-TEXTUAL-PARAM-VALUE>
                                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx/DcmDslProtocolRxAddrType</DEFINITION-REF>
                                          <VALUE>DCM_PHYSICAL_TYPE</VALUE>
                                        </ECUC-TEXTUAL-PARAM-VALUE>
                                        <ECUC-NUMERICAL-PARAM-VALUE>
                                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx/DcmDslProtocolRxPduId</DEFINITION-REF>
                                          <VALUE>0</VALUE>
                                        </ECUC-NUMERICAL-PARAM-VALUE>
                                        <ECUC-NUMERICAL-PARAM-VALUE>
                                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx/DcmDslProtocolRxChannelId</DEFINITION-REF>
                                          <VALUE>0</VALUE>
                                        </ECUC-NUMERICAL-PARAM-VALUE>
                                      </PARAMETER-VALUES>
                                      <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                          <DEFINITION-REF DEST="ECUC-SYMBOLIC-NAME-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx/DcmDslProtocolRxComMChannelRef</DEFINITION-REF>
                                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/ComM/ComMConfigSet/CN_CANB_5813403e</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx/DcmDslProtocolRxPduRef</DEFINITION-REF>
                                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/EcuC/EcucPduCollection/DiagReq_oCANB_20_583b7280_Rx</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                      </REFERENCE-VALUES>
                                    </ECUC-CONTAINER-VALUE>
                                    <ECUC-CONTAINER-VALUE UUID="7b5333da-1ae3-32f5-b3b9-97734f0a8bbf">
                                      <SHORT-NAME>DiagResp_oCANB_20_0c9aca6a_Tx_c9240a38</SHORT-NAME>
                                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolTx</DEFINITION-REF>
                                      <PARAMETER-VALUES>
                                        <ECUC-NUMERICAL-PARAM-VALUE>
                                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolTx/DcmDslTxConfirmationPduId</DEFINITION-REF>
                                          <VALUE>0</VALUE>
                                        </ECUC-NUMERICAL-PARAM-VALUE>
                                      </PARAMETER-VALUES>
                                      <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolTx/DcmDslProtocolTxPduRef</DEFINITION-REF>
                                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/EcuC/EcucPduCollection/DiagResp_oCANB_20_0c9aca6a_Tx</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                      </REFERENCE-VALUES>
                                    </ECUC-CONTAINER-VALUE>
                                    <ECUC-CONTAINER-VALUE UUID="db661f72-a764-409a-928a-95a39d033b4c">
                                      <SHORT-NAME>Diag_FuncReq_7DF_oCANB_20_31821908_Rx_7c43947f</SHORT-NAME>
                                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx</DEFINITION-REF>
                                      <PARAMETER-VALUES>
                                        <ECUC-TEXTUAL-PARAM-VALUE>
                                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx/DcmDslProtocolRxAddrType</DEFINITION-REF>
                                          <VALUE>DCM_FUNCTIONAL_TYPE</VALUE>
                                        </ECUC-TEXTUAL-PARAM-VALUE>
                                        <ECUC-NUMERICAL-PARAM-VALUE>
                                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx/DcmDslProtocolRxPduId</DEFINITION-REF>
                                          <VALUE>1</VALUE>
                                        </ECUC-NUMERICAL-PARAM-VALUE>
                                        <ECUC-NUMERICAL-PARAM-VALUE>
                                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx/DcmDslProtocolRxChannelId</DEFINITION-REF>
                                          <VALUE>0</VALUE>
                                        </ECUC-NUMERICAL-PARAM-VALUE>
                                      </PARAMETER-VALUES>
                                      <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                          <DEFINITION-REF DEST="ECUC-SYMBOLIC-NAME-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx/DcmDslProtocolRxComMChannelRef</DEFINITION-REF>
                                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/ComM/ComMConfigSet/CN_CANB_5813403e</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslProtocol/DcmDslProtocolRow/DcmDslConnection/DcmDslMainConnection/DcmDslProtocolRx/DcmDslProtocolRxPduRef</DEFINITION-REF>
                                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/EcuC/EcucPduCollection/Diag_FuncReq_7DF_oCANB_20_31821908_Rx</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                      </REFERENCE-VALUES>
                                    </ECUC-CONTAINER-VALUE>
                                  </SUB-CONTAINERS>
                                </ECUC-CONTAINER-VALUE>
                              </SUB-CONTAINERS>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE UUID="cdfa4984-808f-40fd-a00e-ea3f1c5a9047">
                      <SHORT-NAME>DcmDslDiagResp</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslDiagResp</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslDiagResp/DcmDslDiagRespMaxNumRespPend</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslDiagResp/DcmDslDiagRespOnSecondDeclinedRequest</DEFINITION-REF>
                          <VALUE>false</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslDiagResp/DcmDslDiagRespMaxNumOfDeclinedRequests</DEFINITION-REF>
                          <VALUE>3</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE UUID="c8e1df49-fb17-4338-9411-a6c0b014c59b">
                      <SHORT-NAME>DcmDslBuffer</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslBuffer</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslBuffer/DcmDslBufferSize</DEFINITION-REF>
                          <VALUE>4095</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE UUID="4e9ae7f1-b458-4eb6-a80e-386d8a17cd61">
                      <SHORT-NAME>DcmDslServiceRequestSupplierNotification</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslServiceRequestSupplierNotification</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslServiceRequestSupplierNotification/DcmDslServiceRequestSupplierNotificationUsePort</DEFINITION-REF>
                          <VALUE>false</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslServiceRequestSupplierNotification/DcmDslServiceRequestSupplierNotificationConfirmationFnc</DEFINITION-REF>
                          <VALUE>ServiceRequestNotification_Confirmation</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsl/DcmDslServiceRequestSupplierNotification/DcmDslServiceRequestSupplierNotificationIndicationFnc</DEFINITION-REF>
                          <VALUE>ServiceRequestNotification_ConditionCheck_Indication</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="5e6d9b57-c011-42d4-96e6-58da5284f5c5">
                  <SHORT-NAME>DcmGeneral</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmGeneral</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmGeneral/DcmDevErrorDetect</DEFINITION-REF>
                      <VALUE>true</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmGeneral/DcmRequestManufacturerNotificationEnabled</DEFINITION-REF>
                      <VALUE>false</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmGeneral/DcmRequestSupplierNotificationEnabled</DEFINITION-REF>
                      <VALUE>true</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmGeneral/DcmRespondAllRequest</DEFINITION-REF>
                      <VALUE>true</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmGeneral/DcmTaskTime</DEFINITION-REF>
                      <VALUE>0.01</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmGeneral/DcmVersionInfoApi</DEFINITION-REF>
                      <VALUE>false</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmGeneral/DcmDspDataDefaultEndianness</DEFINITION-REF>
                      <VALUE>LITTLE_ENDIAN</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmGeneral/DcmSafeBswChecks</DEFINITION-REF>
                      <VALUE>false</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmGeneral/DcmSupportedIDCalculationSuppressionEnabled</DEFINITION-REF>
                      <VALUE>false</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmGeneral/DcmDtrDataProvisionViaDemEnabled</DEFINITION-REF>
                      <VALUE>true</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmGeneral/DcmForeignDiagnosticRequestDetectionEnabled</DEFINITION-REF>
                      <VALUE>false</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmGeneral/DcmSuppressResponseOnCanTpFuncMixedAddrRequests</DEFINITION-REF>
                      <VALUE>false</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmGeneral/DcmSenderReceiverPRPortsEnabled</DEFINITION-REF>
                      <VALUE>false</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmGeneral/DcmKeepAliveTime</DEFINITION-REF>
                      <VALUE>6</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="ebab7e86-38cd-4739-ab08-d15d47cf4a76">
                  <SHORT-NAME>DcmDsd</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd</DEFINITION-REF>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE UUID="f0437cfa-f692-4491-81f3-e8c0439f703a">
                      <SHORT-NAME>DcmDsdServiceTable</SHORT-NAME>
                      <ANNOTATIONS>
                        <ANNOTATION>
                          <LABEL>
                            <L-4 L="FOR-ALL">DynamicallyDefineDataId</L-4>
                          </LABEL>
                          <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                        </ANNOTATION>
                        <ANNOTATION>
                          <LABEL>
                            <L-4 L="FOR-ALL">IoControlById</L-4>
                          </LABEL>
                          <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                        </ANNOTATION>
                        <ANNOTATION>
                          <LABEL>
                            <L-4 L="FOR-ALL">ReadDataByPeriodicId</L-4>
                          </LABEL>
                          <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                        </ANNOTATION>
                        <ANNOTATION>
                          <LABEL>
                            <L-4 L="FOR-ALL">ReadMemoryByAddress</L-4>
                          </LABEL>
                          <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                        </ANNOTATION>
                        <ANNOTATION>
                          <LABEL>
                            <L-4 L="FOR-ALL">WriteMemoryByAddress</L-4>
                          </LABEL>
                          <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                        </ANNOTATION>
                      </ANNOTATIONS>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable</DEFINITION-REF>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE UUID="92a46c66-76e7-445a-9235-0ce080e2b74b">
                          <SHORT-NAME>ClearDiagInfo</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabServiceId</DEFINITION-REF>
                              <VALUE>20</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSubfuncAvail</DEFINITION-REF>
                              <VALUE>false</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSessionLevelRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Default</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSessionLevelRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Extended</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSessionLevelRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/ZXExtended</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="2d5c4738-20ab-4dd9-be86-dd1ee6603579">
                          <SHORT-NAME>EcuReset</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabServiceId</DEFINITION-REF>
                              <VALUE>17</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSubfuncAvail</DEFINITION-REF>
                              <VALUE>true</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabFnc</DEFINITION-REF>
                              <VALUE>idia_ecu_reset</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE UUID="ea0dd4ba-331e-4a4f-a349-313e54825f31">
                              <SHORT-NAME>Hard</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="36726832-a7b0-4ed7-afe0-87c86f06acb6">
                          <SHORT-NAME>ControlDtcSetting</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabServiceId</DEFINITION-REF>
                              <VALUE>133</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSubfuncAvail</DEFINITION-REF>
                              <VALUE>true</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSessionLevelRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Extended</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE UUID="2745da34-e73e-4504-ac7a-5dc47ea8f9d7">
                              <SHORT-NAME>Disable</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <VALUE>2</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE UUID="ba64d420-f11c-4ea3-b3cc-85abd403facf">
                              <SHORT-NAME>Enable</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="6aaaeaac-4175-413c-a865-80452b17de2e">
                          <SHORT-NAME>WriteDataById</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabServiceId</DEFINITION-REF>
                              <VALUE>46</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSubfuncAvail</DEFINITION-REF>
                              <VALUE>false</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabFnc</DEFINITION-REF>
                              <VALUE>idia_write_data_by_identifier</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSessionLevelRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Extended</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSessionLevelRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Programming_1</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSessionLevelRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/ZXExtended</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="694aa873-218d-45cc-b025-e20e8040a547">
                          <SHORT-NAME>ReadDataById</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabServiceId</DEFINITION-REF>
                              <VALUE>34</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSubfuncAvail</DEFINITION-REF>
                              <VALUE>false</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabFnc</DEFINITION-REF>
                              <VALUE>idia_read_data_by_identifier</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="cab06812-bfe0-48c2-b7ac-01132b4b7adf">
                          <SHORT-NAME>SecurityAccess</SHORT-NAME>
                          <ANNOTATIONS>
                            <ANNOTATION>
                              <LABEL>
                                <L-4 L="FOR-ALL">requestSeed_Request</L-4>
                              </LABEL>
                              <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                            </ANNOTATION>
                            <ANNOTATION>
                              <LABEL>
                                <L-4 L="FOR-ALL">sendKey_Send</L-4>
                              </LABEL>
                              <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                            </ANNOTATION>
                          </ANNOTATIONS>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabServiceId</DEFINITION-REF>
                              <VALUE>39</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSubfuncAvail</DEFINITION-REF>
                              <VALUE>true</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSessionLevelRef</DEFINITION-REF>
                              <ANNOTATIONS>
                                <ANNOTATION>
                                  <ANNOTATION-ORIGIN>DV:UserDefined</ANNOTATION-ORIGIN>
                                </ANNOTATION>
                              </ANNOTATIONS>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Programming_1</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSessionLevelRef</DEFINITION-REF>
                              <ANNOTATIONS>
                                <ANNOTATION>
                                  <ANNOTATION-ORIGIN>DV:UserDefined</ANNOTATION-ORIGIN>
                                </ANNOTATION>
                              </ANNOTATIONS>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Extended</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSessionLevelRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/ZXExtended</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE UUID="62266859-d77b-461e-80b4-452ce20f25dd">
                              <SHORT-NAME>KeyBoot_Send</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <VALUE>18</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                              <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Programming_1</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                              </REFERENCE-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE UUID="faec4198-92f1-4b5f-bd19-a1ae3ac65bc7">
                              <SHORT-NAME>SeedBoot_Request</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <VALUE>17</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                              <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Programming_1</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                              </REFERENCE-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE UUID="f475c61f-2c1a-4dcf-922e-ebcd9c811ea6">
                              <SHORT-NAME>KeyZX_Send</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <VALUE>98</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                              <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/ZXExtended</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                              </REFERENCE-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE UUID="6de8c512-fbe0-4cd9-9c53-f73f0763cf94">
                              <SHORT-NAME>SeedZX_Request</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <VALUE>97</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                              <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/ZXExtended</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                              </REFERENCE-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE UUID="3b548fa5-9b61-4efa-b5d5-b4ea30628a40">
                              <SHORT-NAME>SeedLevel1_Request</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:UserDefined</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                              <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:UserDefined</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:UserDefined</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Extended</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                              </REFERENCE-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE UUID="a6e5fba5-1b1d-412e-bf15-a1637e7088c0">
                              <SHORT-NAME>KeyLevel1_Send</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:UserDefined</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <VALUE>2</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                              <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:UserDefined</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Extended</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                              </REFERENCE-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="5e7bc7d1-348d-4511-bec5-671ec9c583bd">
                          <SHORT-NAME>ReadDtcInfo</SHORT-NAME>
                          <ANNOTATIONS>
                            <ANNOTATION>
                              <LABEL>
                                <L-4 L="FOR-ALL">RDTCBSMR</L-4>
                              </LABEL>
                              <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                            </ANNOTATION>
                            <ANNOTATION>
                              <LABEL>
                                <L-4 L="FOR-ALL">RDTCFDC</L-4>
                              </LABEL>
                              <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                            </ANNOTATION>
                            <ANNOTATION>
                              <LABEL>
                                <L-4 L="FOR-ALL">RDTCSSI</L-4>
                              </LABEL>
                              <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                            </ANNOTATION>
                            <ANNOTATION>
                              <LABEL>
                                <L-4 L="FOR-ALL">RFCDTC</L-4>
                              </LABEL>
                              <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                            </ANNOTATION>
                            <ANNOTATION>
                              <LABEL>
                                <L-4 L="FOR-ALL">RMRTFDTC</L-4>
                              </LABEL>
                              <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                            </ANNOTATION>
                            <ANNOTATION>
                              <LABEL>
                                <L-4 L="FOR-ALL">RNODTCBSMR</L-4>
                              </LABEL>
                              <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                            </ANNOTATION>
                            <ANNOTATION>
                              <LABEL>
                                <L-4 L="FOR-ALL">RSIODTC</L-4>
                              </LABEL>
                              <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                            </ANNOTATION>
                          </ANNOTATIONS>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabServiceId</DEFINITION-REF>
                              <VALUE>25</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSubfuncAvail</DEFINITION-REF>
                              <VALUE>true</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSessionLevelRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Default</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSessionLevelRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Extended</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSessionLevelRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/ZXExtended</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE UUID="42db49c0-c102-490f-9f1c-9b1fcacdf2f5">
                              <SHORT-NAME>RDTCEDRBDN</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <VALUE>6</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE UUID="a2b75a49-4c2c-4892-88fd-162d6bf02792">
                              <SHORT-NAME>RSUPDTC</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <VALUE>10</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE UUID="fd7c8b78-bc31-4be5-b38b-d315668ed371">
                              <SHORT-NAME>RDTCBSM</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <VALUE>2</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE UUID="c02009b9-1404-473a-a12c-0710eaa61460">
                              <SHORT-NAME>RNODTCBSM</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE UUID="101283a1-e016-43cd-b469-9cd36163a924">
                              <SHORT-NAME>RDTCSSBDTC</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <VALUE>4</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="731060c6-8214-4ed4-963f-4300676bd9ac">
                          <SHORT-NAME>TesterPresent</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabServiceId</DEFINITION-REF>
                              <VALUE>62</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSubfuncAvail</DEFINITION-REF>
                              <VALUE>true</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE UUID="3e80a379-3e2b-45ad-ae7e-e07db8dd6970">
                              <SHORT-NAME>TesterPresent</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="f4373205-e8af-4a11-a2d9-dcd318933fc7">
                          <SHORT-NAME>CommunicationControl</SHORT-NAME>
                          <ANNOTATIONS>
                            <ANNOTATION>
                              <LABEL>
                                <L-4 L="FOR-ALL">DisableRxAndEnableTx</L-4>
                              </LABEL>
                              <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                            </ANNOTATION>
                          </ANNOTATIONS>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabServiceId</DEFINITION-REF>
                              <VALUE>40</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSubfuncAvail</DEFINITION-REF>
                              <VALUE>true</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSessionLevelRef</DEFINITION-REF>
                              <ANNOTATIONS>
                                <ANNOTATION>
                                  <ANNOTATION-ORIGIN>DV:UserDefined</ANNOTATION-ORIGIN>
                                </ANNOTATION>
                              </ANNOTATIONS>
                            </ECUC-REFERENCE-VALUE>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSessionLevelRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Extended</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE UUID="747ac5db-cbbf-4c0d-a4bd-e18687f5550f">
                              <SHORT-NAME>EnableRxAndDisableTx</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                              <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:UserDefined</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Extended</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                              </REFERENCE-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE UUID="87813075-34f3-4553-83ad-8c083d10b577">
                              <SHORT-NAME>DisableRxAndTx</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <VALUE>3</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                              <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:UserDefined</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Extended</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                              </REFERENCE-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE UUID="2de6b146-9763-490c-9f6f-a10ca7353b95">
                              <SHORT-NAME>EnableRxAndTx</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                              <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:UserDefined</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Extended</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                              </REFERENCE-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="540bc636-8bf7-4f37-9e32-b47848967b23">
                          <SHORT-NAME>RoutineControl</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabServiceId</DEFINITION-REF>
                              <VALUE>49</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSubfuncAvail</DEFINITION-REF>
                              <VALUE>true</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabFnc</DEFINITION-REF>
                              <VALUE>idia_routine_control</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSessionLevelRef</DEFINITION-REF>
                              <ANNOTATIONS>
                                <ANNOTATION>
                                  <ANNOTATION-ORIGIN>DV:UserDefined</ANNOTATION-ORIGIN>
                                </ANNOTATION>
                              </ANNOTATIONS>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Programming_1</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSessionLevelRef</DEFINITION-REF>
                              <ANNOTATIONS>
                                <ANNOTATION>
                                  <ANNOTATION-ORIGIN>DV:UserDefined</ANNOTATION-ORIGIN>
                                </ANNOTATION>
                              </ANNOTATIONS>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Extended</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSessionLevelRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/ZXExtended</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="767fd40b-6ac9-4180-becf-9351d261bb1c">
                          <SHORT-NAME>DiagSessionControl</SHORT-NAME>
                          <ANNOTATIONS>
                            <ANNOTATION>
                              <LABEL>
                                <L-4 L="FOR-ALL">Programming</L-4>
                              </LABEL>
                              <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                            </ANNOTATION>
                          </ANNOTATIONS>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabServiceId</DEFINITION-REF>
                              <VALUE>16</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSubfuncAvail</DEFINITION-REF>
                              <VALUE>true</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE UUID="8feace08-8ae7-4599-b6cf-da53abcc3edb">
                              <SHORT-NAME>Programming1</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <VALUE>2</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                              <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Extended</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Programming_1</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                              </REFERENCE-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE UUID="817eb671-0abb-4a2f-be30-d3842fcaa7eb">
                              <SHORT-NAME>ZXExtended</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <VALUE>96</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                              <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Default</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/ZXExtended</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                              </REFERENCE-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE UUID="ad0d7db9-64cf-4387-b044-f81806401def">
                              <SHORT-NAME>Default</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                              <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Default</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Extended</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/ZXExtended</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Programming_1</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                              </REFERENCE-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE UUID="a736254d-04d0-442b-b021-cb50e8677b9a">
                              <SHORT-NAME>Extended</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceId</DEFINITION-REF>
                                  <VALUE>3</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                              <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Default</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSubService/DcmDsdSubServiceSessionLevelRef</DEFINITION-REF>
                                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/Extended</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                              </REFERENCE-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="a554c1ae-a06e-4a60-8125-046b07363e11">
                          <SHORT-NAME>InputOutputControlByIndentifier</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabServiceId</DEFINITION-REF>
                              <VALUE>47</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabFnc</DEFINITION-REF>
                              <VALUE>idia_ioutput_control_by_identifier</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsd/DcmDsdServiceTable/DcmDsdService/DcmDsdSidTabSubfuncAvail</DEFINITION-REF>
                              <VALUE>false</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="057e8679-33ed-4674-ac37-41a2a77250c3">
                  <SHORT-NAME>DcmDsp</SHORT-NAME>
                  <ANNOTATIONS>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">Boot_Software</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataDiagnosticIdentifier_DID</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DevelopmentData</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DID_0xF410</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DID_0xF412</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DID_0xF413</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">EcuIdentification</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">Example_ReadOnlyDID</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">Example_ReadWriteData</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">Example_WriteOnlyDID</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">PeriodicDataSample</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">SampleDynamicallyDefinedNonPeriodicDID</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">SampleIoControl</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">SerialNumber</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">Spare_Part_Number</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">Vehicle_Identification</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DidInfo_Boot_Software</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DidInfo_DataDiagnosticIdentifier_DID</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DidInfo_DevelopmentData</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DidInfo_DID_0xF410</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DidInfo_DID_0xF412</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DidInfo_DID_0xF413</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DidInfo_EcuIdentification</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DidInfo_Example_ReadOnlyDID</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DidInfo_Example_ReadWriteData</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DidInfo_Example_WriteOnlyDID</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DidInfo_PeriodicDataSample</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DidInfo_SampleDynamicallyDefinedNonPeriodicDID</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DidInfo_SampleIoControl</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DidInfo_SerialNumber</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DidInfo_Spare_Part_Number</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DidInfo_Vehicle_Identification</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">PeriodicDataSample_DataRecord</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">SerialNumber_SerialNumber</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">Spare_Part_Number_Spare_Part_Number</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">Vehicle_Identification_VIN</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">Boot_Software_Boot_Software_Identification</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">Boot_Software_NumberOfModules</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataDiagnosticIdentifier_DID_DataDiagnosticIdentifier</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DevelopmentData_CanDriverVersion</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DevelopmentData_DiagnosticModuleVersion</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DevelopmentData_NmVersion</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DevelopmentData_OperatingSystemVersion</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DevelopmentData_TransportLayerVersion</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DID_0xF410_DID_Data</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DID_0xF412_DID_Data</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DID_0xF413_DID_Data</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">EcuIdentification_Part_Number</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">Example_ReadOnlyDID_DID_Data_GlobalTime</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">Example_ReadWriteData_GlobalTime</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">Example_WriteOnlyDID_DID_DataObject</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataInfo_PeriodicDataSample_DataRecord</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataInfo_SerialNumber_SerialNumber</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataInfo_Spare_Part_Number_Spare_Part_Number</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataInfo_Vehicle_Identification_VIN</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataInfo_Boot_Software_Boot_Software_Identification</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataInfo_Boot_Software_NumberOfModules</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataInfo_DataDiagnosticIdentifier_DID_DataDiagnosticIdentifier</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataInfo_DevelopmentData_CanDriverVersion</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataInfo_DevelopmentData_DiagnosticModuleVersion</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataInfo_DevelopmentData_NmVersion</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataInfo_DevelopmentData_OperatingSystemVersion</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataInfo_DevelopmentData_TransportLayerVersion</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataInfo_DID_0xF410_DID_Data</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataInfo_DID_0xF412_DID_Data</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataInfo_DID_0xF413_DID_Data</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataInfo_EcuIdentification_Part_Number</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataInfo_Example_ReadOnlyDID_DID_Data_GlobalTime</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataInfo_Example_ReadWriteData_GlobalTime</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataInfo_Example_WriteOnlyDID_DID_DataObject</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">RoutineInfo_SampleRoutineControl_StartOnly</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">RoutineInfo_CheckProgrammingPreconditions</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">RoutineInfo_ForceBootMode</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">SampleRoutineControl_StartOnly</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">CheckProgrammingPreconditions</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">ForceBootMode</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DataInfo_Hardware_Version_Hardware_Version_Number</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">Hardware_Version_Hardware_Version_Number</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">DidInfo_Hardware_Version</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">Hardware_Version</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">SampleRoutineControl</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">RoutineInfo_SampleRoutineControl</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                    </ANNOTATION>
                  </ANNOTATIONS>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspMaxDidToRead</DEFINITION-REF>
                      <VALUE>5</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspMaxPeriodicDidToRead</DEFINITION-REF>
                      <VALUE>5</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspReadDIDReportsNODIByte</DEFINITION-REF>
                      <VALUE>false</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE UUID="560d52d0-4401-4f0b-8ed3-f3a820cb9558">
                      <SHORT-NAME>DcmDspSecurity</SHORT-NAME>
                      <ANNOTATIONS>
                        <ANNOTATION>
                          <LABEL>
                            <L-4 L="FOR-ALL">Level_3</L-4>
                          </LABEL>
                          <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                        </ANNOTATION>
                        <ANNOTATION>
                          <LABEL>
                            <L-4 L="FOR-ALL">UnlockedL1</L-4>
                          </LABEL>
                          <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                        </ANNOTATION>
                      </ANNOTATIONS>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity</DEFINITION-REF>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE UUID="3a1eb21d-a0b5-4200-b826-7a473e239fb7">
                          <SHORT-NAME>UnlockedBoot</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityDelayTime</DEFINITION-REF>
                              <VALUE>10</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityDelayTimeOnBoot</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityKeySize</DEFINITION-REF>
                              <VALUE>16</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityLevel</DEFINITION-REF>
                              <VALUE>9</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityNumAttDelay</DEFINITION-REF>
                              <VALUE>3</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecuritySeedSize</DEFINITION-REF>
                              <ANNOTATIONS>
                                <ANNOTATION>
                                  <ANNOTATION-ORIGIN>DV:UserDefined</ANNOTATION-ORIGIN>
                                </ANNOTATION>
                              </ANNOTATIONS>
                              <VALUE>16</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityAttemptCounterEnabled</DEFINITION-REF>
                              <VALUE>true</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityUsePort</DEFINITION-REF>
                              <VALUE>USE_ASYNCH_FNC</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityCompareKeyFnc</DEFINITION-REF>
                              <VALUE>Dcm_CompareKey_Boot</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityGetSeedFnc</DEFINITION-REF>
                              <VALUE>Dcm_GetSeed_Boot</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityGetAttemptCounterFnc</DEFINITION-REF>
                              <VALUE>GetBootAttemptCounter</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecuritySetAttemptCounterFnc</DEFINITION-REF>
                              <VALUE>SetBootAttemptCounter</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="cf9e89ec-cd44-408a-87e7-75eba38b11d1">
                          <SHORT-NAME>UnlockedApp</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityAttemptCounterEnabled</DEFINITION-REF>
                              <VALUE>true</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityDelayTime</DEFINITION-REF>
                              <VALUE>10</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityDelayTimeOnBoot</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityKeySize</DEFINITION-REF>
                              <VALUE>16</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityLevel</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityNumAttDelay</DEFINITION-REF>
                              <VALUE>3</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecuritySeedSize</DEFINITION-REF>
                              <VALUE>16</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityUsePort</DEFINITION-REF>
                              <VALUE>USE_ASYNCH_FNC</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityCompareKeyFnc</DEFINITION-REF>
                              <VALUE>Dcm_CompareKey</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityGetSeedFnc</DEFINITION-REF>
                              <VALUE>Dcm_GetSeed</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecuritySetAttemptCounterFnc</DEFINITION-REF>
                              <VALUE>SetAppAttemptCounter</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityGetAttemptCounterFnc</DEFINITION-REF>
                              <VALUE>GetAppAttemptCounter</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="97791e4e-9a8a-4df2-a3fe-472c92a1292c">
                          <SHORT-NAME>UnlockedZX</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityAttemptCounterEnabled</DEFINITION-REF>
                              <VALUE>true</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityDelayTime</DEFINITION-REF>
                              <VALUE>10</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityDelayTimeOnBoot</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityKeySize</DEFINITION-REF>
                              <VALUE>4</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityLevel</DEFINITION-REF>
                              <VALUE>49</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityNumAttDelay</DEFINITION-REF>
                              <VALUE>3</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecuritySeedSize</DEFINITION-REF>
                              <VALUE>4</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityUsePort</DEFINITION-REF>
                              <VALUE>USE_ASYNCH_FNC</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityCompareKeyFnc</DEFINITION-REF>
                              <VALUE>Dcm_CompareKey_Level_3</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityGetSeedFnc</DEFINITION-REF>
                              <VALUE>Dcm_GetSeed_Level_3</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecurityGetAttemptCounterFnc</DEFINITION-REF>
                              <VALUE>GetZxAttemptCounter</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSecurity/DcmDspSecurityRow/DcmDspSecuritySetAttemptCounterFnc</DEFINITION-REF>
                              <VALUE>SetZxAttemptCounter</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE UUID="6871317a-216f-44e0-9ac2-1cbc769d723f">
                      <SHORT-NAME>DcmDspControlDTCSetting</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspControlDTCSetting</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspControlDTCSetting/DcmSupportDTCSettingControlOptionRecord</DEFINITION-REF>
                          <ANNOTATIONS>
                            <ANNOTATION>
                              <ANNOTATION-ORIGIN>DV:UserDefined</ANNOTATION-ORIGIN>
                            </ANNOTATION>
                          </ANNOTATIONS>
                          <VALUE>false</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE UUID="f360e2d4-00bc-4c04-97cb-fb790be061e7">
                      <SHORT-NAME>ComControl</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspComControl</DEFINITION-REF>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE UUID="45786ea6-90c3-4096-96bd-2e545acc4360">
                          <SHORT-NAME>DspComContolAllChannel</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspComControl/DcmDspComControlAllChannel</DEFINITION-REF>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-SYMBOLIC-NAME-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspComControl/DcmDspComControlAllChannel/DcmDspAllComMChannelRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/ComM/ComMConfigSet/CN_CANA_c11a1184</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="cde98ce3-cae7-47e1-a473-74268c3285e1">
                          <SHORT-NAME>DcmDspComControlAllChannelCANB</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspComControl/DcmDspComControlAllChannel</DEFINITION-REF>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-SYMBOLIC-NAME-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspComControl/DcmDspComControlAllChannel/DcmDspAllComMChannelRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/ComM/ComMConfigSet/CN_CANB_5813403e</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="9d981d99-3197-4f53-b46d-505926918db7">
                          <SHORT-NAME>DcmDspComControlAllChannelPRI1</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspComControl/DcmDspComControlAllChannel</DEFINITION-REF>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-SYMBOLIC-NAME-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspComControl/DcmDspComControlAllChannel/DcmDspAllComMChannelRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/ComM/ComMConfigSet/CN_PRI1_82921327</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="2aa06e78-90d4-4f7f-a136-937b73d70600">
                          <SHORT-NAME>DcmDspComControlAllChannelPRI2</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspComControl/DcmDspComControlAllChannel</DEFINITION-REF>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-SYMBOLIC-NAME-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspComControl/DcmDspComControlAllChannel/DcmDspAllComMChannelRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/ComM/ComMConfigSet/CN_PRI2_1b9b429d</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="2fd207cb-4f03-4743-b31e-250116f518c2">
                          <SHORT-NAME>DcmDspComControlAllChannelPRI3</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspComControl/DcmDspComControlAllChannel</DEFINITION-REF>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-SYMBOLIC-NAME-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspComControl/DcmDspComControlAllChannel/DcmDspAllComMChannelRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/ComM/ComMConfigSet/CN_PRI3_6c9c720b</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="86b119e1-33f4-4680-be3d-8a8559054480">
                          <SHORT-NAME>DcmDspComControlAllChannelPRI4</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspComControl/DcmDspComControlAllChannel</DEFINITION-REF>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-SYMBOLIC-NAME-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspComControl/DcmDspComControlAllChannel/DcmDspAllComMChannelRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/ComM/ComMConfigSet/CN_PRI4_f2f8e7a8</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE UUID="fbe303c6-b689-4153-9686-1a17d5404257">
                      <SHORT-NAME>DcmDspSession</SHORT-NAME>
                      <ANNOTATIONS>
                        <ANNOTATION>
                          <LABEL>
                            <L-4 L="FOR-ALL">Programming</L-4>
                          </LABEL>
                          <ANNOTATION-ORIGIN>DV:RemovedDerivedContainer</ANNOTATION-ORIGIN>
                        </ANNOTATION>
                      </ANNOTATIONS>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession</DEFINITION-REF>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE UUID="7e73c8af-54a5-4870-b365-fea41cce8884">
                          <SHORT-NAME>ZXExtended</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionRow</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionRow/DcmDspSessionForBoot</DEFINITION-REF>
                              <VALUE>DCM_NO_BOOT</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionRow/DcmDspSessionLevel</DEFINITION-REF>
                              <VALUE>96</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionRow/DcmDspSessionP2ServerMax</DEFINITION-REF>
                              <VALUE>0.05</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionRow/DcmDspSessionP2StarServerMax</DEFINITION-REF>
                              <VALUE>2</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="cd2eb6f3-**************-6c901e68c095">
                          <SHORT-NAME>Default</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionRow</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionRow/DcmDspSessionLevel</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionRow/DcmDspSessionP2ServerMax</DEFINITION-REF>
                              <VALUE>0.05</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionRow/DcmDspSessionP2StarServerMax</DEFINITION-REF>
                              <ANNOTATIONS>
                                <ANNOTATION>
                                  <ANNOTATION-ORIGIN>DV:UserDefined</ANNOTATION-ORIGIN>
                                </ANNOTATION>
                              </ANNOTATIONS>
                              <VALUE>2</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionRow/DcmDspSessionForBoot</DEFINITION-REF>
                              <VALUE>DCM_NO_BOOT</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="d57e4542-3ae8-474d-b909-74c62e34ccb9">
                          <SHORT-NAME>Extended</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionRow</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionRow/DcmDspSessionLevel</DEFINITION-REF>
                              <VALUE>3</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionRow/DcmDspSessionP2ServerMax</DEFINITION-REF>
                              <VALUE>0.05</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionRow/DcmDspSessionP2StarServerMax</DEFINITION-REF>
                              <ANNOTATIONS>
                                <ANNOTATION>
                                  <ANNOTATION-ORIGIN>DV:UserDefined</ANNOTATION-ORIGIN>
                                </ANNOTATION>
                              </ANNOTATIONS>
                              <VALUE>2</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionRow/DcmDspSessionForBoot</DEFINITION-REF>
                              <VALUE>DCM_NO_BOOT</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="80a14269-fc6c-4bff-b13d-3374f8808692">
                          <SHORT-NAME>DcmDspSessionCallback</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionCallback</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionCallback/DcmDspSessionCallbackFnc</DEFINITION-REF>
                              <VALUE>idia_session_control_callback</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="dc00b086-d1fa-4fa7-903b-37b8cb6577d3">
                          <SHORT-NAME>Programming_1</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionRow</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-TEXTUAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionRow/DcmDspSessionForBoot</DEFINITION-REF>
                              <VALUE>DCM_NO_BOOT</VALUE>
                            </ECUC-TEXTUAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionRow/DcmDspSessionLevel</DEFINITION-REF>
                              <VALUE>2</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionRow/DcmDspSessionP2ServerMax</DEFINITION-REF>
                              <VALUE>0.05</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspSession/DcmDspSessionRow/DcmDspSessionP2StarServerMax</DEFINITION-REF>
                              <VALUE>2</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE UUID="db270059-e50a-4795-833d-c0b9875dbd0d">
                      <SHORT-NAME>DcmDspMemory</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspMemory</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspMemory/DcmDspUseMemoryId</DEFINITION-REF>
                          <VALUE>false</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE UUID="66474400-db5c-432c-9e50-e619da44517f">
                          <SHORT-NAME>DcmDspMemoryIdInfo</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspMemory/DcmDspMemoryIdInfo</DEFINITION-REF>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE UUID="e7fcacff-7eea-4024-8227-8f2aeaf36ba0">
                              <SHORT-NAME>DcmDspReadMemoryRangeInfo</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspMemory/DcmDspMemoryIdInfo/DcmDspReadMemoryRangeInfo</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspMemory/DcmDspMemoryIdInfo/DcmDspReadMemoryRangeInfo/DcmDspReadMemoryRangeHigh</DEFINITION-REF>
                                  <VALUE>4294967294</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspMemory/DcmDspMemoryIdInfo/DcmDspReadMemoryRangeInfo/DcmDspReadMemoryRangeLow</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE UUID="e90327bc-33b8-4538-b286-643eeb0b7b2f">
                              <SHORT-NAME>DcmDspWriteMemoryRangeInfo</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspMemory/DcmDspMemoryIdInfo/DcmDspWriteMemoryRangeInfo</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspMemory/DcmDspMemoryIdInfo/DcmDspWriteMemoryRangeInfo/DcmDspWriteMemoryRangeHigh</DEFINITION-REF>
                                  <VALUE>4294967294</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspMemory/DcmDspMemoryIdInfo/DcmDspWriteMemoryRangeInfo/DcmDspWriteMemoryRangeLow</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE UUID="947facb9-b58d-4187-9f19-6ccc2815bc91">
                      <SHORT-NAME>DcmDspPeriodicTransmission</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspPeriodicTransmission</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspPeriodicTransmission/DcmDspPeriodicTransmissionSlowRate</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspPeriodicTransmission/DcmDspPeriodicTransmissionMediumRate</DEFINITION-REF>
                          <VALUE>0.1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspPeriodicTransmission/DcmDspPeriodicTransmissionFastRate</DEFINITION-REF>
                          <VALUE>0.02</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE UUID="e5db26df-e7f4-4747-bbf7-5b8afe49a444">
                      <SHORT-NAME>SampleIoControl_CombinedDataElement</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspData</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspData/DcmDspDataConditionCheckReadFncUsed</DEFINITION-REF>
                          <VALUE>false</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspData/DcmDspDataSize</DEFINITION-REF>
                          <VALUE>32</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspData/DcmDspDataType</DEFINITION-REF>
                          <VALUE>UINT8_N</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspData/DcmDspDataUsePort</DEFINITION-REF>
                          <VALUE>USE_DATA_ASYNCH_CLIENT_SERVER</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspData/DcmDspDataInfoRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dcm/DcmConfigSet/DcmDsp/DataInfo_SampleIoControl_CombinedDataElement</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE UUID="716ff274-4a72-4fbb-98a3-80fbe8a9d1b4">
                      <SHORT-NAME>DataInfo_SampleIoControl_CombinedDataElement</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspDataInfo</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmDsp/DcmDspDataInfo/DcmDspDataFixedLength</DEFINITION-REF>
                          <VALUE>false</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="1ce83839-6be8-4b15-9b9b-05db624fafb9">
                  <SHORT-NAME>DcmPageBufferCfg</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmPageBufferCfg</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Dcm/DcmConfigSet/DcmPageBufferCfg/DcmPagedBufferEnabled</DEFINITION-REF>
                      <VALUE>true</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
          </CONTAINERS>
        </ECUC-MODULE-CONFIGURATION-VALUES>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
