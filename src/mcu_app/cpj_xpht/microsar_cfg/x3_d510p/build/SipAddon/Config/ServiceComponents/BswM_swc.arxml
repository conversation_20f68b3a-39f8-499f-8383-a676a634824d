<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
      <SHORT-NAME>MICROSAR</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="73f00816-6e97-4643-a8ef-3e84ec2e4630">
          <SHORT-NAME>BswM_swc</SHORT-NAME>
          <ELEMENTS>
            <MODE-DECLARATION-GROUP UUID="5cfa7356-5546-4062-97b1-143e6498c42b">
              <SHORT-NAME>ESH_Mode</SHORT-NAME>
              <CATEGORY>EXPLICIT_ORDER</CATEGORY>
              <INITIAL-MODE-REF DEST="MODE-DECLARATION">/MICROSAR/BswM_swc/ESH_Mode/STARTUP</INITIAL-MODE-REF>
              <MODE-DECLARATIONS>
                <MODE-DECLARATION UUID="ad35a3f4-9ecb-4408-a381-4e9fc8d14b21">
                  <SHORT-NAME>STARTUP</SHORT-NAME>
                  <VALUE>0</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="c385fc86-4f4d-4c30-84a0-7c1ba1a25736">
                  <SHORT-NAME>RUN</SHORT-NAME>
                  <VALUE>1</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="3caf2c9b-54d4-46ea-826e-8f5d6ba66595">
                  <SHORT-NAME>POSTRUN</SHORT-NAME>
                  <VALUE>2</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="5052ea7c-c2ea-4736-8403-e781d38fbdab">
                  <SHORT-NAME>WAKEUP</SHORT-NAME>
                  <VALUE>3</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="294f32b4-151f-4ff4-8527-42c470503f13">
                  <SHORT-NAME>SHUTDOWN</SHORT-NAME>
                  <VALUE>4</VALUE>
                </MODE-DECLARATION>
              </MODE-DECLARATIONS>
              <ON-TRANSITION-VALUE>5</ON-TRANSITION-VALUE>
            </MODE-DECLARATION-GROUP>
            <MODE-DECLARATION-GROUP UUID="585868ac-b97e-4e1c-a5bb-5f11c01baad4">
              <SHORT-NAME>ESH_RunRequest</SHORT-NAME>
              <CATEGORY>EXPLICIT_ORDER</CATEGORY>
              <INITIAL-MODE-REF DEST="MODE-DECLARATION">/MICROSAR/BswM_swc/ESH_RunRequest/RELEASED</INITIAL-MODE-REF>
              <MODE-DECLARATIONS>
                <MODE-DECLARATION UUID="c999637d-1fa0-466f-b47a-8ed60d1d7df6">
                  <SHORT-NAME>RELEASED</SHORT-NAME>
                  <VALUE>0</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="31d864ec-0482-4356-a7d9-7fff0135140d">
                  <SHORT-NAME>REQUESTED</SHORT-NAME>
                  <VALUE>1</VALUE>
                </MODE-DECLARATION>
              </MODE-DECLARATIONS>
              <ON-TRANSITION-VALUE>2</ON-TRANSITION-VALUE>
            </MODE-DECLARATION-GROUP>
            <DATA-TYPE-MAPPING-SET UUID="7207a381-0566-48e1-9de3-d64cfcbcb44b">
              <SHORT-NAME>BswMMappingSet</SHORT-NAME>
              <DESC>
                <L-2 L="FOR-ALL">Contains Mapping for ModeDeclarations Groups which are provided by BswM.</L-2>
              </DESC>
              <MODE-REQUEST-TYPE-MAPS>
                <MODE-REQUEST-TYPE-MAP>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/BswM_swc/DataTypes/BswM_ESH_Mode</IMPLEMENTATION-DATA-TYPE-REF>
                  <MODE-GROUP-REF DEST="MODE-DECLARATION-GROUP">/MICROSAR/BswM_swc/ESH_Mode</MODE-GROUP-REF>
                </MODE-REQUEST-TYPE-MAP>
                <MODE-REQUEST-TYPE-MAP>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/BswM_swc/DataTypes/BswM_ESH_RunRequest</IMPLEMENTATION-DATA-TYPE-REF>
                  <MODE-GROUP-REF DEST="MODE-DECLARATION-GROUP">/MICROSAR/BswM_swc/ESH_RunRequest</MODE-GROUP-REF>
                </MODE-REQUEST-TYPE-MAP>
              </MODE-REQUEST-TYPE-MAPS>
            </DATA-TYPE-MAPPING-SET>
          </ELEMENTS>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="0b37c6a8-3950-4b77-867c-6c39764264f5">
              <SHORT-NAME>ComponentTypes</SHORT-NAME>
              <ELEMENTS>
                <SERVICE-SW-COMPONENT-TYPE UUID="6967e7ac-92a4-4b49-8216-ea6d8c759daa">
                  <SHORT-NAME>BswM</SHORT-NAME>
                  <CATEGORY>SERVICE_COMPONENT</CATEGORY>
                  <PORTS>
                    <P-PORT-PROTOTYPE UUID="08eaebcd-4b6b-46f1-8a3f-a3d07531956b">
                      <SHORT-NAME>Switch_ESH_ModeSwitch</SHORT-NAME>
                      <ADMIN-DATA>
                        <SDGS>
                          <SDG GID="DV:DerivedFromReferrables">
                            <SDG GID="DV:DerivedFromReferrablesVariant">
                              <SD GID="DV:InvariantValuesView"/>
                              <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/BswM/BswMConfig/BswMModeControl/ESH_ModeSwitch</SDX-REF>
                              <SD GID="DV:DerivedFromReferrablesUUID">9fae9334-ad6b-4c41-aecf-4b47ebb95ec6</SD>
                            </SDG>
                          </SDG>
                        </SDGS>
                      </ADMIN-DATA>
                      <PROVIDED-INTERFACE-TREF DEST="MODE-SWITCH-INTERFACE">/MICROSAR/BswM_swc/Interfaces/BswM_MSI_ESH_Mode</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <R-PORT-PROTOTYPE UUID="8088f7d7-4ff4-4f0e-bef5-d59bd40f6bb6">
                      <SHORT-NAME>Notification_ESH_ModeNotification</SHORT-NAME>
                      <ADMIN-DATA>
                        <SDGS>
                          <SDG GID="DV:DerivedFromReferrables">
                            <SDG GID="DV:DerivedFromReferrablesVariant">
                              <SD GID="DV:InvariantValuesView"/>
                              <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/BswM/BswMConfig/BswMArbitration/ESH_ModeNotification</SDX-REF>
                              <SD GID="DV:DerivedFromReferrablesUUID">92e47bc5-88d2-43c3-8dfb-b746b6632f1f</SD>
                            </SDG>
                          </SDG>
                        </SDGS>
                      </ADMIN-DATA>
                      <REQUIRED-INTERFACE-TREF DEST="MODE-SWITCH-INTERFACE">/MICROSAR/BswM_swc/Interfaces/BswM_MSI_ESH_Mode</REQUIRED-INTERFACE-TREF>
                    </R-PORT-PROTOTYPE>
                    <R-PORT-PROTOTYPE UUID="16bbb677-4823-4af9-9ed6-177978f6a898">
                      <SHORT-NAME>Request_ESH_RunRequest_0</SHORT-NAME>
                      <ADMIN-DATA>
                        <SDGS>
                          <SDG GID="DV:DerivedFromReferrables">
                            <SDG GID="DV:DerivedFromReferrablesVariant">
                              <SD GID="DV:InvariantValuesView"/>
                              <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/BswM/BswMConfig/BswMArbitration/ESH_RunRequest_0</SDX-REF>
                              <SD GID="DV:DerivedFromReferrablesUUID">1961e065-492b-4722-b95b-06d1ab1426fd</SD>
                            </SDG>
                          </SDG>
                        </SDGS>
                      </ADMIN-DATA>
                      <REQUIRED-COM-SPECS>
                        <NONQUEUED-RECEIVER-COM-SPEC>
                          <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/MICROSAR/BswM_swc/Interfaces/BswM_SRI_BswM_MSI_ESH_RunRequest_BswM_MDGP_ESH_RunRequest/requestedMode</DATA-ELEMENT-REF>
                          <HANDLE-OUT-OF-RANGE>NONE</HANDLE-OUT-OF-RANGE>
                          <HANDLE-OUT-OF-RANGE-STATUS>SILENT</HANDLE-OUT-OF-RANGE-STATUS>
                          <INIT-VALUE>
                            <TEXT-VALUE-SPECIFICATION>
                              <VALUE>RELEASED</VALUE>
                            </TEXT-VALUE-SPECIFICATION>
                          </INIT-VALUE>
                        </NONQUEUED-RECEIVER-COM-SPEC>
                      </REQUIRED-COM-SPECS>
                      <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/MICROSAR/BswM_swc/Interfaces/BswM_SRI_BswM_MSI_ESH_RunRequest_BswM_MDGP_ESH_RunRequest</REQUIRED-INTERFACE-TREF>
                    </R-PORT-PROTOTYPE>
                    <R-PORT-PROTOTYPE UUID="7f4c7e87-f47a-439b-873b-253216c5beee">
                      <SHORT-NAME>Request_ESH_PostRunRequest_0</SHORT-NAME>
                      <ADMIN-DATA>
                        <SDGS>
                          <SDG GID="DV:DerivedFromReferrables">
                            <SDG GID="DV:DerivedFromReferrablesVariant">
                              <SD GID="DV:InvariantValuesView"/>
                              <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/BswM/BswMConfig/BswMArbitration/ESH_PostRunRequest_0</SDX-REF>
                              <SD GID="DV:DerivedFromReferrablesUUID">725f600e-3151-4900-8b9a-fa3c927a47de</SD>
                            </SDG>
                          </SDG>
                        </SDGS>
                      </ADMIN-DATA>
                      <REQUIRED-COM-SPECS>
                        <NONQUEUED-RECEIVER-COM-SPEC>
                          <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/MICROSAR/BswM_swc/Interfaces/BswM_SRI_BswM_MSI_ESH_RunRequest_BswM_MDGP_ESH_RunRequest/requestedMode</DATA-ELEMENT-REF>
                          <HANDLE-OUT-OF-RANGE>NONE</HANDLE-OUT-OF-RANGE>
                          <HANDLE-OUT-OF-RANGE-STATUS>SILENT</HANDLE-OUT-OF-RANGE-STATUS>
                          <INIT-VALUE>
                            <TEXT-VALUE-SPECIFICATION>
                              <VALUE>RELEASED</VALUE>
                            </TEXT-VALUE-SPECIFICATION>
                          </INIT-VALUE>
                        </NONQUEUED-RECEIVER-COM-SPEC>
                      </REQUIRED-COM-SPECS>
                      <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/MICROSAR/BswM_swc/Interfaces/BswM_SRI_BswM_MSI_ESH_RunRequest_BswM_MDGP_ESH_RunRequest</REQUIRED-INTERFACE-TREF>
                    </R-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="8440daea-2ffd-4b2f-b8f5-40c8f7669ac8">
                      <SHORT-NAME>Provide_BswMRtePostRunModeRequestPort</SHORT-NAME>
                      <ADMIN-DATA>
                        <SDGS>
                          <SDG GID="DV:DerivedFromReferrables">
                            <SDG GID="DV:DerivedFromReferrablesVariant">
                              <SD GID="DV:InvariantValuesView"/>
                              <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/BswM/BswMConfig/BswMModeControl/BswMRtePostRunModeRequestPort</SDX-REF>
                              <SD GID="DV:DerivedFromReferrablesUUID">34c868c5-ed0a-480f-b83a-a4c707d2e61f</SD>
                            </SDG>
                          </SDG>
                        </SDGS>
                      </ADMIN-DATA>
                      <PROVIDED-COM-SPECS>
                        <NONQUEUED-SENDER-COM-SPEC>
                          <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/MICROSAR/BswM_swc/Interfaces/BswM_SRI_BswM_MSI_ESH_RunRequest_BswM_MDGP_ESH_RunRequest/requestedMode</DATA-ELEMENT-REF>
                          <INIT-VALUE>
                            <TEXT-VALUE-SPECIFICATION>
                              <VALUE>REQUESTED</VALUE>
                            </TEXT-VALUE-SPECIFICATION>
                          </INIT-VALUE>
                        </NONQUEUED-SENDER-COM-SPEC>
                      </PROVIDED-COM-SPECS>
                      <PROVIDED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/MICROSAR/BswM_swc/Interfaces/BswM_SRI_BswM_MSI_ESH_RunRequest_BswM_MDGP_ESH_RunRequest</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="8b903b9e-58b9-435c-a1a4-0e5b966eb652">
                      <SHORT-NAME>Provide_BswMRteRunModeRequestPort</SHORT-NAME>
                      <ADMIN-DATA>
                        <SDGS>
                          <SDG GID="DV:DerivedFromReferrables">
                            <SDG GID="DV:DerivedFromReferrablesVariant">
                              <SD GID="DV:InvariantValuesView"/>
                              <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/BswM/BswMConfig/BswMModeControl/BswMRteRunModeRequestPort</SDX-REF>
                              <SD GID="DV:DerivedFromReferrablesUUID">12e40f22-c851-4d1f-8ce9-69b6e4806043</SD>
                            </SDG>
                          </SDG>
                        </SDGS>
                      </ADMIN-DATA>
                      <PROVIDED-COM-SPECS>
                        <NONQUEUED-SENDER-COM-SPEC>
                          <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/MICROSAR/BswM_swc/Interfaces/BswM_SRI_BswM_MSI_ESH_RunRequest_BswM_MDGP_ESH_RunRequest/requestedMode</DATA-ELEMENT-REF>
                        </NONQUEUED-SENDER-COM-SPEC>
                      </PROVIDED-COM-SPECS>
                      <PROVIDED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/MICROSAR/BswM_swc/Interfaces/BswM_SRI_BswM_MSI_ESH_RunRequest_BswM_MDGP_ESH_RunRequest</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                  </PORTS>
                  <INTERNAL-BEHAVIORS>
                    <SWC-INTERNAL-BEHAVIOR UUID="f8d1309e-edee-4f56-9dc8-472b79b906f5">
                      <SHORT-NAME>BswMInternalBehavior</SHORT-NAME>
                      <DATA-TYPE-MAPPING-REFS>
                        <DATA-TYPE-MAPPING-REF DEST="DATA-TYPE-MAPPING-SET">/MICROSAR/BswM_swc/BswMMappingSet</DATA-TYPE-MAPPING-REF>
                      </DATA-TYPE-MAPPING-REFS>
                      <EVENTS>
                        <TIMING-EVENT UUID="d736b955-34ce-48e3-8f9e-fd522df5385a">
                          <SHORT-NAME>Timer_BswM_MainFunction</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/BswM_swc/ComponentTypes/BswM/BswMInternalBehavior/BswM_MainFunction</START-ON-EVENT-REF>
                          <PERIOD>0.005</PERIOD>
                        </TIMING-EVENT>
                      </EVENTS>
                      <HANDLE-TERMINATION-AND-RESTART>NO-SUPPORT</HANDLE-TERMINATION-AND-RESTART>
                      <INCLUDED-DATA-TYPE-SETS>
                        <INCLUDED-DATA-TYPE-SET>
                          <DATA-TYPE-REFS>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/BswM_swc/DataTypes/BswM_ESH_Mode</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/BswM_swc/DataTypes/BswM_ESH_RunRequest</DATA-TYPE-REF>
                          </DATA-TYPE-REFS>
                        </INCLUDED-DATA-TYPE-SET>
                      </INCLUDED-DATA-TYPE-SETS>
                      <PORT-API-OPTIONS>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/BswM_swc/ComponentTypes/BswM/Switch_ESH_ModeSwitch</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="R-PORT-PROTOTYPE">/MICROSAR/BswM_swc/ComponentTypes/BswM/Notification_ESH_ModeNotification</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="R-PORT-PROTOTYPE">/MICROSAR/BswM_swc/ComponentTypes/BswM/Request_ESH_RunRequest_0</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="R-PORT-PROTOTYPE">/MICROSAR/BswM_swc/ComponentTypes/BswM/Request_ESH_PostRunRequest_0</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/BswM_swc/ComponentTypes/BswM/Provide_BswMRtePostRunModeRequestPort</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/BswM_swc/ComponentTypes/BswM/Provide_BswMRteRunModeRequestPort</PORT-REF>
                        </PORT-API-OPTION>
                      </PORT-API-OPTIONS>
                      <RUNNABLES>
                        <RUNNABLE-ENTITY UUID="3e8425ce-f2cd-4be1-8d58-5e274d8c533f">
                          <SHORT-NAME>BswM_MainFunction</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>false</CAN-BE-INVOKED-CONCURRENTLY>
                          <DATA-RECEIVE-POINT-BY-ARGUMENTS>
                            <VARIABLE-ACCESS UUID="2910f0ca-360a-45f5-8254-1fbd3f669645">
                              <SHORT-NAME>DataReceivePointBswM_MainFunction_Request_ESH_RunRequest_0_requestedMode</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <AUTOSAR-VARIABLE-IREF>
                                  <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/MICROSAR/BswM_swc/ComponentTypes/BswM/Request_ESH_RunRequest_0</PORT-PROTOTYPE-REF>
                                  <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/MICROSAR/BswM_swc/Interfaces/BswM_SRI_BswM_MSI_ESH_RunRequest_BswM_MDGP_ESH_RunRequest/requestedMode</TARGET-DATA-PROTOTYPE-REF>
                                </AUTOSAR-VARIABLE-IREF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                            <VARIABLE-ACCESS UUID="0e21b877-d767-4026-924a-868c76f22581">
                              <SHORT-NAME>DataReceivePointBswM_MainFunction_Request_ESH_PostRunRequest_0_requestedMode</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <AUTOSAR-VARIABLE-IREF>
                                  <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/MICROSAR/BswM_swc/ComponentTypes/BswM/Request_ESH_PostRunRequest_0</PORT-PROTOTYPE-REF>
                                  <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/MICROSAR/BswM_swc/Interfaces/BswM_SRI_BswM_MSI_ESH_RunRequest_BswM_MDGP_ESH_RunRequest/requestedMode</TARGET-DATA-PROTOTYPE-REF>
                                </AUTOSAR-VARIABLE-IREF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                          </DATA-RECEIVE-POINT-BY-ARGUMENTS>
                          <DATA-SEND-POINTS>
                            <VARIABLE-ACCESS UUID="a74ce7e5-02ca-4f58-ba54-e557394e78bc">
                              <SHORT-NAME>DataSendPointBswM_MainFunction_Provide_BswMRtePostRunModeRequestPort_requestedMode</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <AUTOSAR-VARIABLE-IREF>
                                  <PORT-PROTOTYPE-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/BswM_swc/ComponentTypes/BswM/Provide_BswMRtePostRunModeRequestPort</PORT-PROTOTYPE-REF>
                                  <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/MICROSAR/BswM_swc/Interfaces/BswM_SRI_BswM_MSI_ESH_RunRequest_BswM_MDGP_ESH_RunRequest/requestedMode</TARGET-DATA-PROTOTYPE-REF>
                                </AUTOSAR-VARIABLE-IREF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                            <VARIABLE-ACCESS UUID="524309ff-6ebd-4c7e-988b-320501b47637">
                              <SHORT-NAME>DataSendPointBswM_MainFunction_Provide_BswMRteRunModeRequestPort_requestedMode</SHORT-NAME>
                              <ACCESSED-VARIABLE>
                                <AUTOSAR-VARIABLE-IREF>
                                  <PORT-PROTOTYPE-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/BswM_swc/ComponentTypes/BswM/Provide_BswMRteRunModeRequestPort</PORT-PROTOTYPE-REF>
                                  <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/MICROSAR/BswM_swc/Interfaces/BswM_SRI_BswM_MSI_ESH_RunRequest_BswM_MDGP_ESH_RunRequest/requestedMode</TARGET-DATA-PROTOTYPE-REF>
                                </AUTOSAR-VARIABLE-IREF>
                              </ACCESSED-VARIABLE>
                            </VARIABLE-ACCESS>
                          </DATA-SEND-POINTS>
                          <MODE-ACCESS-POINTS>
                            <MODE-ACCESS-POINT>
                              <MODE-GROUP-IREF>
                                <R-MODE-GROUP-IN-ATOMIC-SWC-INSTANCE-REF>
                                  <CONTEXT-R-PORT-REF DEST="R-PORT-PROTOTYPE">/MICROSAR/BswM_swc/ComponentTypes/BswM/Notification_ESH_ModeNotification</CONTEXT-R-PORT-REF>
                                  <TARGET-MODE-GROUP-REF DEST="MODE-DECLARATION-GROUP-PROTOTYPE">/MICROSAR/BswM_swc/Interfaces/BswM_MSI_ESH_Mode/BswM_MDGP_ESH_Mode</TARGET-MODE-GROUP-REF>
                                </R-MODE-GROUP-IN-ATOMIC-SWC-INSTANCE-REF>
                              </MODE-GROUP-IREF>
                            </MODE-ACCESS-POINT>
                          </MODE-ACCESS-POINTS>
                          <MODE-SWITCH-POINTS>
                            <MODE-SWITCH-POINT UUID="3e0b7bd8-d091-4774-b961-0a95f93a07fa">
                              <SHORT-NAME>ModeSwitchPointBswM_MainFunction_Switch_ESH_ModeSwitch_BswM_MDGP_ESH_Mode</SHORT-NAME>
                              <MODE-GROUP-IREF>
                                <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/BswM_swc/ComponentTypes/BswM/Switch_ESH_ModeSwitch</CONTEXT-P-PORT-REF>
                                <TARGET-MODE-GROUP-REF DEST="MODE-DECLARATION-GROUP-PROTOTYPE">/MICROSAR/BswM_swc/Interfaces/BswM_MSI_ESH_Mode/BswM_MDGP_ESH_Mode</TARGET-MODE-GROUP-REF>
                              </MODE-GROUP-IREF>
                            </MODE-SWITCH-POINT>
                          </MODE-SWITCH-POINTS>
                          <SYMBOL>BswM_MainFunction</SYMBOL>
                        </RUNNABLE-ENTITY>
                      </RUNNABLES>
                      <SUPPORTS-MULTIPLE-INSTANTIATION>false</SUPPORTS-MULTIPLE-INSTANTIATION>
                    </SWC-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </SERVICE-SW-COMPONENT-TYPE>
                <SWC-IMPLEMENTATION UUID="5dae30ed-4d14-4dcc-8c43-243c758362ee">
                  <SHORT-NAME>BswMImplementation</SHORT-NAME>
                  <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
                  <BEHAVIOR-REF DEST="SWC-INTERNAL-BEHAVIOR">/MICROSAR/BswM_swc/ComponentTypes/BswM/BswMInternalBehavior</BEHAVIOR-REF>
                </SWC-IMPLEMENTATION>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="f270ebfa-8f9a-466c-b9ce-afbcfcef80f8">
              <SHORT-NAME>DataTypes</SHORT-NAME>
              <ELEMENTS>
                <IMPLEMENTATION-DATA-TYPE UUID="ed2e5a52-4c5d-4e49-a218-2abf970253c9">
                  <SHORT-NAME>BswM_ESH_Mode</SHORT-NAME>
                  <DESC>
                    <L-2 L="FOR-ALL">Unique value for each user</L-2>
                  </DESC>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/MICROSAR/BswM_swc/DataTypes/CompuMethods/BswM_ESH_Mode</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/BswM_swc/DataTypes/DataConstrs/BswM_ESH_Mode_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="6c4aac39-fecc-4a9b-8d04-6f4c6490783a">
                  <SHORT-NAME>BswM_ESH_RunRequest</SHORT-NAME>
                  <DESC>
                    <L-2 L="FOR-ALL">Unique value for each user</L-2>
                  </DESC>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/MICROSAR/BswM_swc/DataTypes/CompuMethods/BswM_ESH_RunRequest</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/BswM_swc/DataTypes/DataConstrs/BswM_ESH_RunRequest_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
              </ELEMENTS>
              <AR-PACKAGES>
                <AR-PACKAGE UUID="13c9d0c3-7a29-445c-b222-c2ec0e3166cf">
                  <SHORT-NAME>DataConstrs</SHORT-NAME>
                  <ELEMENTS>
                    <DATA-CONSTR UUID="9641ad5c-01cf-4f4d-82a7-452dfe9e2caa">
                      <SHORT-NAME>BswM_ESH_Mode_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">255</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="28b45c68-d1a9-400b-b24d-33ebc71791f0">
                      <SHORT-NAME>BswM_ESH_RunRequest_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">255</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                  </ELEMENTS>
                </AR-PACKAGE>
                <AR-PACKAGE UUID="fc543dbc-6ee4-49ee-bf04-e0cb06fb2580">
                  <SHORT-NAME>CompuMethods</SHORT-NAME>
                  <ELEMENTS>
                    <COMPU-METHOD UUID="b73d624e-ac71-422f-9628-175dcaa43462">
                      <SHORT-NAME>BswM_ESH_Mode</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>STARTUP</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>STARTUP</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>RUN</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>RUN</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>POSTRUN</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>POSTRUN</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>WAKEUP</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">3</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">3</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>WAKEUP</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>SHUTDOWN</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">4</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">4</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>SHUTDOWN</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                    <COMPU-METHOD UUID="0fa53210-9a20-43d3-b52a-b2c5e4b54a1a">
                      <SHORT-NAME>BswM_ESH_RunRequest</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>RELEASED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>RELEASED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>REQUESTED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>REQUESTED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                  </ELEMENTS>
                </AR-PACKAGE>
              </AR-PACKAGES>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="f5d53df7-6252-4980-b421-41ed5a4c8c99">
              <SHORT-NAME>Interfaces</SHORT-NAME>
              <ELEMENTS>
                <MODE-SWITCH-INTERFACE UUID="249d8ce5-2e3c-46b5-9905-c918eaa4939a">
                  <SHORT-NAME>BswM_MSI_ESH_Mode</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>BASIC-SOFTWARE-MODE-MANAGER</SERVICE-KIND>
                  <MODE-GROUP UUID="ba2f107e-4c7a-431d-8dc6-6e060c5e1351">
                    <SHORT-NAME>BswM_MDGP_ESH_Mode</SHORT-NAME>
                    <TYPE-TREF DEST="MODE-DECLARATION-GROUP">/MICROSAR/BswM_swc/ESH_Mode</TYPE-TREF>
                  </MODE-GROUP>
                </MODE-SWITCH-INTERFACE>
                <SENDER-RECEIVER-INTERFACE UUID="ecc48916-233b-47b9-b94d-c33c48226c8c">
                  <SHORT-NAME>BswM_SRI_BswM_MSI_ESH_Mode_BswM_MDGP_ESH_Mode</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>BASIC-SOFTWARE-MODE-MANAGER</SERVICE-KIND>
                  <DATA-ELEMENTS>
                    <VARIABLE-DATA-PROTOTYPE UUID="1fcf53d1-d107-41ed-8871-1f262371cf72">
                      <SHORT-NAME>requestedMode</SHORT-NAME>
                      <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/BswM_swc/DataTypes/BswM_ESH_Mode</TYPE-TREF>
                    </VARIABLE-DATA-PROTOTYPE>
                  </DATA-ELEMENTS>
                </SENDER-RECEIVER-INTERFACE>
                <MODE-SWITCH-INTERFACE UUID="a2c72637-4864-4801-84a5-d22bbb960f8a">
                  <SHORT-NAME>BswM_MSI_ESH_RunRequest</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>BASIC-SOFTWARE-MODE-MANAGER</SERVICE-KIND>
                  <MODE-GROUP UUID="a7c56abe-8ec9-4cf4-af5f-d479d463dd4a">
                    <SHORT-NAME>BswM_MDGP_ESH_RunRequest</SHORT-NAME>
                    <TYPE-TREF DEST="MODE-DECLARATION-GROUP">/MICROSAR/BswM_swc/ESH_RunRequest</TYPE-TREF>
                  </MODE-GROUP>
                </MODE-SWITCH-INTERFACE>
                <SENDER-RECEIVER-INTERFACE UUID="065a21ae-f46b-4d36-9460-917b914eebd7">
                  <SHORT-NAME>BswM_SRI_BswM_MSI_ESH_RunRequest_BswM_MDGP_ESH_RunRequest</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>BASIC-SOFTWARE-MODE-MANAGER</SERVICE-KIND>
                  <DATA-ELEMENTS>
                    <VARIABLE-DATA-PROTOTYPE UUID="6c234841-3d02-4f15-b218-49493d264310">
                      <SHORT-NAME>requestedMode</SHORT-NAME>
                      <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/BswM_swc/DataTypes/BswM_ESH_RunRequest</TYPE-TREF>
                    </VARIABLE-DATA-PROTOTYPE>
                  </DATA-ELEMENTS>
                </SENDER-RECEIVER-INTERFACE>
              </ELEMENTS>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
