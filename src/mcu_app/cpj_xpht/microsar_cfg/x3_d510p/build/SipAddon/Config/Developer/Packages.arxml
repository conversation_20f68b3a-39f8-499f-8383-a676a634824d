<?xml version="1.0" encoding="utf-8"?>
<!--This file was saved with a tool from Vector Informatik GmbH-->
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00046.xsd" xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="F24AA52A-BF17-4426-BAA3-************">
      <SHORT-NAME>AUTOSAR_Platform</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="20AF4FB5-305C-4279-A0D9-84ED9AD1C989">
          <SHORT-NAME>BaseTypes</SHORT-NAME>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="8305514E-20E9-46EA-A83A-CC087FBAED50">
          <SHORT-NAME>CompuMethods</SHORT-NAME>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="7F2FBB3F-1E22-4FF2-A033-E1863E98AEE8">
          <SHORT-NAME>DataConstrs</SHORT-NAME>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="586B49C0-386B-40CF-8387-91D8079E0F3C">
          <SHORT-NAME>ImplementationDataTypes</SHORT-NAME>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
      <SHORT-NAME>MICROSAR</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="9C917B2B-B2B1-4E7D-B692-28AF98C0E980">
          <SHORT-NAME>FiM_swc</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="2C34233B-7D49-4122-897A-AEC7A8E7B402">
              <SHORT-NAME>Interfaces</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="E82441F6-134B-4096-997B-D1FD034FE0F7">
      <SHORT-NAME>VehicleProject</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="6CEA7629-0355-41A4-85DA-56D581586676">
      <SHORT-NAME>NM</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="A1805A95-4D60-4EC5-A922-4293A68E5712">
      <SHORT-NAME>TP</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="551FF6D0-BE7B-4759-A405-800FA4876D96">
      <SHORT-NAME>Cluster</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="3858BA14-4600-4D12-A70D-6226D367DEE2">
      <SHORT-NAME>ECU</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="*************-416A-89E7-683056D9F5AA">
      <SHORT-NAME>IPDUGroup</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="45EF8FA4-7DC4-49C6-A7BA-42D8CFE32EB1">
      <SHORT-NAME>CanFrame</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="B141F1BA-54C9-4919-94E5-97E2C10217EA">
      <SHORT-NAME>PDU</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="F6EAC0C4-EB3D-4B49-A356-EABF22128789">
      <SHORT-NAME>ISignal</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="75244A8A-F04B-432D-8087-802B6F18102B">
      <SHORT-NAME>Signal</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="7C629F49-CD25-4485-8C04-03EAF602DE31">
      <SHORT-NAME>ComponentTypes</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="90558F38-9EB7-438A-A9F6-824EBF29FECC">
          <SHORT-NAME>DataTypeMappingSets</SHORT-NAME>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="0B7AE4A8-61F8-4B48-AA22-E120FC43A62D">
          <SHORT-NAME>ConstantMappingSets</SHORT-NAME>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="47BE0349-ECF4-447C-A4F6-54CC3F8DE337">
      <SHORT-NAME>Predefined_DEV</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="09AC2DB9-518E-416C-8E2B-1B2A5C489A63">
          <SHORT-NAME>DataConstraints</SHORT-NAME>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="C9E99801-E220-487C-A184-CD3967D823A6">
          <SHORT-NAME>CompuMethods</SHORT-NAME>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="3FC85877-11E4-4605-AA39-A445188D8EC5">
          <SHORT-NAME>ImplementationDataTypes</SHORT-NAME>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="3513F3A9-3075-4C36-AC30-C2BBE3230841">
          <SHORT-NAME>PortInterfaces</SHORT-NAME>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="A176463E-3C2E-4F75-94BC-9A06B842956F">
      <SHORT-NAME>DataType</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="461B0070-D6FE-4D69-B574-760D5958B64C">
          <SHORT-NAME>Constraint</SHORT-NAME>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="1CAF2582-A7A1-4468-A392-14E478DFD560">
          <SHORT-NAME>Semantics</SHORT-NAME>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="44C10548-70D2-4294-A8CB-92188BDD6467">
          <SHORT-NAME>Unit</SHORT-NAME>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="D2E376EE-8FE7-4405-9F4B-798D8460E1D0">
          <SHORT-NAME>DataTypeSemantics</SHORT-NAME>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="DAB11B89-0FEE-4DF9-B7AD-D8302D547B22">
      <SHORT-NAME>DataTypes</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="5ACF203A-3F4D-416B-89F3-A5AAAC7ADCA2">
          <SHORT-NAME>BaseTypes</SHORT-NAME>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="F73C699C-CFE8-4BA0-912F-9A7B47B22EDE">
          <SHORT-NAME>CompuMethods</SHORT-NAME>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="9DA461B6-72C1-4485-A5CE-C402F05BD1BC">
          <SHORT-NAME>DataConstrs</SHORT-NAME>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="AAE292FA-479E-40A5-9948-09A16F005672">
          <SHORT-NAME>Units</SHORT-NAME>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="0D9A0A65-0F77-478D-9B3C-13DF42E05DC9">
          <SHORT-NAME>ImplementationDataTypes_DEV</SHORT-NAME>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="8BFFB157-9E68-4FA8-8FCF-3E3580B433FD">
      <SHORT-NAME>ECUCompositionTypes</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="8094FC14-067A-4635-8341-A49A5E582693">
      <SHORT-NAME>EndToEndProtections</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="9E6A1175-3C25-419F-BDD6-BB2CBFDF5C7F">
      <SHORT-NAME>Systems</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="1D881A20-38AF-4AC8-95D8-8E523A5F2F94">
      <SHORT-NAME>Diagnostics</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="17AE77D8-EAEC-4912-9A57-B9438DB2EE9C">
      <SHORT-NAME>Constants</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="09A297C9-5019-4CCC-97B5-64355ED71B22">
      <SHORT-NAME>ECUCompositions</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="066D8F2E-634D-45CF-A826-88EE3DD465E8">
      <SHORT-NAME>ModeDclrGroups</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="AFF59301-03C0-4D39-8AFD-F8E71EA9A489">
      <SHORT-NAME>PortInterfaces</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="E384023E-FB1A-49D2-8DB1-5253301AD10A">
      <SHORT-NAME>SwAddrMethods</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="D35A3968-A3E0-4EE1-A99A-5189E8B2D356">
      <SHORT-NAME>Blueprints</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="DB771F2B-EB82-4F7C-B2FF-EAE0B34E5F1D">
          <SHORT-NAME>BlueprintMappingSets</SHORT-NAME>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="F2F50FE7-D8A2-4AD9-8564-FFAAD791756E">
          <SHORT-NAME>PortPrototypeBlueprints</SHORT-NAME>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="62AABE26-F911-4D85-88BD-482FAA08E9DA">
      <SHORT-NAME>Gateway</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="8F16D95C-4E6F-4DF6-8325-F28B0F47C193">
      <SHORT-NAME>LinFrame</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="CDCC5C20-2734-41C5-82DE-8AF5938BD82C">
      <SHORT-NAME>FlexrayFrame</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="3555F53B-D03D-4B5A-A5B8-C9BB36801C68">
      <SHORT-NAME>DataTransformations</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="3BCA9450-8E8B-45BE-A940-DB017E82FE76">
      <SHORT-NAME>SecureCommunicationPropsSet</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="2FB70DA8-4A40-4799-A696-8B76B21D8F90">
      <SHORT-NAME>SWC</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="F98E5A16-94E4-4599-A4DC-1AB939B01990">
      <SHORT-NAME>Constant</SHORT-NAME>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="73AC825A-F64C-4A41-B199-64DC632A1305">
      <SHORT-NAME>ADAS</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="4679A851-FA10-4E76-8D85-D82EECF2F340">
          <SHORT-NAME>BaseTypes</SHORT-NAME>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="27717DDD-E1CB-4DF2-AC7C-C1BCB16BEC84">
          <SHORT-NAME>Constants</SHORT-NAME>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="1FA9B355-0C5F-4884-911C-E9DB21F4924B">
          <SHORT-NAME>DataTypes</SHORT-NAME>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="095EDC1E-E5B2-49A0-9C3A-E9E022034E08">
          <SHORT-NAME>PortInterfaces</SHORT-NAME>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="2C13A6F7-9A85-44CF-B417-6C953CBF5774">
      <SHORT-NAME>AEB_pkg</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="A600D0C0-E9B1-470A-9186-AC10DA1F83BA">
          <SHORT-NAME>AEB_imp</SHORT-NAME>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="75034044-76B0-400A-A724-E1C88A346BA5">
          <SHORT-NAME>AEB_swc</SHORT-NAME>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="A4E3AC69-9213-424E-89FF-A49C1D688A4A">
      <SHORT-NAME>CSM_pkg</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="7BEC65CE-B1C3-4470-A86D-A9F88804BAF8">
          <SHORT-NAME>CSM_imp</SHORT-NAME>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="583E7E2C-A92C-4BCE-8C77-9C9C83B5FDCA">
          <SHORT-NAME>CSM_swc</SHORT-NAME>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
    <AR-PACKAGE UUID="86C20C90-D185-4227-B10B-627FADB54DE0">
      <SHORT-NAME>VehCtrl_pkg</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="A7C8FB00-2B57-498E-8002-C2DD6295E5E7">
          <SHORT-NAME>VehCtrl_imp</SHORT-NAME>
        </AR-PACKAGE>
        <AR-PACKAGE UUID="BF1A6726-7FFD-44C3-B8CA-CFB9A7D60918">
          <SHORT-NAME>VehCtrl_swc</SHORT-NAME>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>