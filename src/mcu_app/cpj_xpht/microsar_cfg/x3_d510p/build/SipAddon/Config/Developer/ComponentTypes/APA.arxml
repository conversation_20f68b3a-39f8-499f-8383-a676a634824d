<?xml version="1.0" encoding="utf-8"?>
<!--This file was saved with a tool from Vector Informatik GmbH-->
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00046.xsd" xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="ACED7121-8ADC-4153-87A1-877D30F8B208">
      <SHORT-NAME>APA_pkg</SHORT-NAME>
      <ELEMENTS>
        <APPLICATION-SW-COMPONENT-TYPE UUID="787AB497-8A40-4A48-97DA-64293BF82AEA">
          <SHORT-NAME>APA</SHORT-NAME>
          <ADMIN-DATA>
            <SDGS>
              <SDG GID="DV_SDG">
                <SD GID="DV_ReadOnly">1</SD>
              </SDG>
            </SDGS>
          </ADMIN-DATA>
          <PORTS>
            <P-PORT-PROTOTYPE UUID="3836B8EB-2C59-4EBE-8252-A332F29C64B6">
              <SHORT-NAME>PP_BlockFlag</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <PROVIDED-COM-SPECS>
                <NONQUEUED-SENDER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/BlockFlag/BlockFlag</DATA-ELEMENT-REF>
                  <INIT-VALUE>
                    <CONSTANT-REFERENCE>
                      <CONSTANT-REF DEST="CONSTANT-SPECIFICATION">/ADAS/Constants/DefaultInitValue_UInt8</CONSTANT-REF>
                    </CONSTANT-REFERENCE>
                  </INIT-VALUE>
                </NONQUEUED-SENDER-COM-SPEC>
              </PROVIDED-COM-SPECS>
              <PROVIDED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/BlockFlag</PROVIDED-INTERFACE-TREF>
            </P-PORT-PROTOTYPE>
            <P-PORT-PROTOTYPE UUID="D50EB6E6-BDCA-4077-A7A3-E5950AEE0065">
              <SHORT-NAME>PP_CollisionFlag</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <PROVIDED-COM-SPECS>
                <NONQUEUED-SENDER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/CollisionFlag/CollisionFlag</DATA-ELEMENT-REF>
                  <INIT-VALUE>
                    <NUMERICAL-VALUE-SPECIFICATION>
                      <VALUE>0</VALUE>
                    </NUMERICAL-VALUE-SPECIFICATION>
                  </INIT-VALUE>
                </NONQUEUED-SENDER-COM-SPEC>
              </PROVIDED-COM-SPECS>
              <PROVIDED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/CollisionFlag</PROVIDED-INTERFACE-TREF>
            </P-PORT-PROTOTYPE>
            <P-PORT-PROTOTYPE UUID="AA2D282A-6AC7-4E1A-AC64-40A6A83B848F">
              <SHORT-NAME>PP_EstimateAcceleration</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <PROVIDED-COM-SPECS>
                <NONQUEUED-SENDER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/EstimateAcceleration/EstimateAcceleration</DATA-ELEMENT-REF>
                  <INIT-VALUE>
                    <NUMERICAL-VALUE-SPECIFICATION>
                      <VALUE>0</VALUE>
                    </NUMERICAL-VALUE-SPECIFICATION>
                  </INIT-VALUE>
                </NONQUEUED-SENDER-COM-SPEC>
              </PROVIDED-COM-SPECS>
              <PROVIDED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/EstimateAcceleration</PROVIDED-INTERFACE-TREF>
            </P-PORT-PROTOTYPE>
            <P-PORT-PROTOTYPE UUID="81E00D04-EB06-4620-90EE-31B1E11020AB">
              <SHORT-NAME>PP_EstimateSpeed</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <PROVIDED-COM-SPECS>
                <NONQUEUED-SENDER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/EstimateSpeed/EstimateSpeed</DATA-ELEMENT-REF>
                  <INIT-VALUE>
                    <NUMERICAL-VALUE-SPECIFICATION>
                      <VALUE>0</VALUE>
                    </NUMERICAL-VALUE-SPECIFICATION>
                  </INIT-VALUE>
                </NONQUEUED-SENDER-COM-SPEC>
              </PROVIDED-COM-SPECS>
              <PROVIDED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/EstimateSpeed</PROVIDED-INTERFACE-TREF>
            </P-PORT-PROTOTYPE>
            <R-PORT-PROTOTYPE UUID="7A4A6B9E-C0E8-47F4-BB13-21B67812C6B3">
              <SHORT-NAME>PR_ACU_LongititudeAcc</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <REQUIRED-COM-SPECS>
                <NONQUEUED-RECEIVER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/ACU_LongititudeAcc/ACU_LongititudeAcc</DATA-ELEMENT-REF>
                  <ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
                  <ENABLE-UPDATE>false</ENABLE-UPDATE>
                  <FILTER>
                    <DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
                  </FILTER>
                  <HANDLE-NEVER-RECEIVED>false</HANDLE-NEVER-RECEIVED>
                  <INIT-VALUE>
                    <CONSTANT-REFERENCE>
                      <CONSTANT-REF DEST="CONSTANT-SPECIFICATION">/ADAS/Constants/DefaultInitValue_Float</CONSTANT-REF>
                    </CONSTANT-REFERENCE>
                  </INIT-VALUE>
                </NONQUEUED-RECEIVER-COM-SPEC>
              </REQUIRED-COM-SPECS>
              <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/ACU_LongititudeAcc</REQUIRED-INTERFACE-TREF>
            </R-PORT-PROTOTYPE>
            <R-PORT-PROTOTYPE UUID="47F94F06-17D0-48C2-85B3-1A54B230DE2B">
              <SHORT-NAME>PR_APA_HandshakeACC</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <REQUIRED-COM-SPECS>
                <NONQUEUED-RECEIVER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/APA_HandshakeACC/APA_HandshakeACC</DATA-ELEMENT-REF>
                  <ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
                  <ENABLE-UPDATE>false</ENABLE-UPDATE>
                  <FILTER>
                    <DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
                  </FILTER>
                  <HANDLE-NEVER-RECEIVED>false</HANDLE-NEVER-RECEIVED>
                </NONQUEUED-RECEIVER-COM-SPEC>
              </REQUIRED-COM-SPECS>
              <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/APA_HandshakeACC</REQUIRED-INTERFACE-TREF>
            </R-PORT-PROTOTYPE>
            <R-PORT-PROTOTYPE UUID="52A6B3D7-CD82-4E04-B9EC-CCA7A852DE71">
              <SHORT-NAME>PR_APA_HandshakeEPS</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <REQUIRED-COM-SPECS>
                <NONQUEUED-RECEIVER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/APA_HandshakeEPS/APA_HandshakeEPS</DATA-ELEMENT-REF>
                  <ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
                  <ENABLE-UPDATE>false</ENABLE-UPDATE>
                  <FILTER>
                    <DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
                  </FILTER>
                  <HANDLE-NEVER-RECEIVED>false</HANDLE-NEVER-RECEIVED>
                  <INIT-VALUE>
                    <NUMERICAL-VALUE-SPECIFICATION>
                      <VALUE>0</VALUE>
                    </NUMERICAL-VALUE-SPECIFICATION>
                  </INIT-VALUE>
                </NONQUEUED-RECEIVER-COM-SPEC>
              </REQUIRED-COM-SPECS>
              <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/APA_HandshakeEPS</REQUIRED-INTERFACE-TREF>
            </R-PORT-PROTOTYPE>
            <R-PORT-PROTOTYPE UUID="717EB14E-2F3F-40A4-9479-866674DE260A">
              <SHORT-NAME>PR_APA_HandshakeESP</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <REQUIRED-COM-SPECS>
                <NONQUEUED-RECEIVER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/APA_HandshakeESP/APA_HandshakeESP</DATA-ELEMENT-REF>
                  <ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
                  <ENABLE-UPDATE>false</ENABLE-UPDATE>
                  <FILTER>
                    <DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
                  </FILTER>
                  <HANDLE-NEVER-RECEIVED>false</HANDLE-NEVER-RECEIVED>
                  <INIT-VALUE>
                    <NUMERICAL-VALUE-SPECIFICATION>
                      <VALUE>0</VALUE>
                    </NUMERICAL-VALUE-SPECIFICATION>
                  </INIT-VALUE>
                </NONQUEUED-RECEIVER-COM-SPEC>
              </REQUIRED-COM-SPECS>
              <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/APA_HandshakeESP</REQUIRED-INTERFACE-TREF>
            </R-PORT-PROTOTYPE>
            <R-PORT-PROTOTYPE UUID="8067AB48-2134-45DA-949C-ABE7B3FDD988">
              <SHORT-NAME>PR_APA_IsLastPath</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <REQUIRED-COM-SPECS>
                <NONQUEUED-RECEIVER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/APA_IsLastPath/APA_IsLastPath</DATA-ELEMENT-REF>
                  <ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
                  <ENABLE-UPDATE>false</ENABLE-UPDATE>
                  <FILTER>
                    <DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
                  </FILTER>
                  <HANDLE-NEVER-RECEIVED>false</HANDLE-NEVER-RECEIVED>
                  <INIT-VALUE>
                    <CONSTANT-REFERENCE>
                      <CONSTANT-REF DEST="CONSTANT-SPECIFICATION">/ADAS/Constants/DefaultInitValue_UInt8</CONSTANT-REF>
                    </CONSTANT-REFERENCE>
                  </INIT-VALUE>
                </NONQUEUED-RECEIVER-COM-SPEC>
              </REQUIRED-COM-SPECS>
              <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/APA_IsLastPath</REQUIRED-INTERFACE-TREF>
            </R-PORT-PROTOTYPE>
            <R-PORT-PROTOTYPE UUID="E8D06140-C3A9-418D-949D-237926C9CFA4">
              <SHORT-NAME>PR_APA_RemainDistance</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <REQUIRED-COM-SPECS>
                <NONQUEUED-RECEIVER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/APA_RemainDistance/APA_RemainDistance</DATA-ELEMENT-REF>
                  <ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
                  <ENABLE-UPDATE>false</ENABLE-UPDATE>
                  <FILTER>
                    <DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
                  </FILTER>
                  <HANDLE-NEVER-RECEIVED>false</HANDLE-NEVER-RECEIVED>
                  <INIT-VALUE>
                    <CONSTANT-REFERENCE>
                      <CONSTANT-REF DEST="CONSTANT-SPECIFICATION">/ADAS/Constants/DefaultInitValue_Float</CONSTANT-REF>
                    </CONSTANT-REFERENCE>
                  </INIT-VALUE>
                </NONQUEUED-RECEIVER-COM-SPEC>
              </REQUIRED-COM-SPECS>
              <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/APA_RemainDistance</REQUIRED-INTERFACE-TREF>
            </R-PORT-PROTOTYPE>
            <R-PORT-PROTOTYPE UUID="B84E9B2F-6121-41FD-8CAF-F3D55F70C757">
              <SHORT-NAME>PR_Ctrl_st</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <REQUIRED-COM-SPECS>
                <NONQUEUED-RECEIVER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/Ctrl_st/Ctrl_st</DATA-ELEMENT-REF>
                  <ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
                  <ENABLE-UPDATE>false</ENABLE-UPDATE>
                  <FILTER>
                    <DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
                  </FILTER>
                  <HANDLE-NEVER-RECEIVED>false</HANDLE-NEVER-RECEIVED>
                  <INIT-VALUE>
                    <RECORD-VALUE-SPECIFICATION>
                      <FIELDS>
                        <RECORD-VALUE-SPECIFICATION>
                          <SHORT-LABEL>Header</SHORT-LABEL>
                          <FIELDS>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>seq</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>ts_high</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>ts_low</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </FIELDS>
                        </RECORD-VALUE-SPECIFICATION>
                        <RECORD-VALUE-SPECIFICATION>
                          <SHORT-LABEL>AccControl</SHORT-LABEL>
                          <FIELDS>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>enable</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>method</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>torque</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>acc</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>drvoffStop</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </FIELDS>
                        </RECORD-VALUE-SPECIFICATION>
                        <RECORD-VALUE-SPECIFICATION>
                          <SHORT-LABEL>SteerControl</SHORT-LABEL>
                          <FIELDS>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>enable</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>method</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>torque</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>angle</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>rate</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </FIELDS>
                        </RECORD-VALUE-SPECIFICATION>
                        <RECORD-VALUE-SPECIFICATION>
                          <SHORT-LABEL>GearControl</SHORT-LABEL>
                          <FIELDS>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>enable</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>gear</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </FIELDS>
                        </RECORD-VALUE-SPECIFICATION>
                        <RECORD-VALUE-SPECIFICATION>
                          <SHORT-LABEL>BcmControl</SHORT-LABEL>
                          <FIELDS>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>enable</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>turn_left_light</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>turn_right_light</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>emergency_light</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>brake_light</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>high_beam</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>low_beam</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>horn</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>wipe</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </FIELDS>
                        </RECORD-VALUE-SPECIFICATION>
                        <RECORD-VALUE-SPECIFICATION>
                          <SHORT-LABEL>SoundControl</SHORT-LABEL>
                          <FIELDS>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>enable</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>sound_id</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </FIELDS>
                        </RECORD-VALUE-SPECIFICATION>
                        <RECORD-VALUE-SPECIFICATION>
                          <SHORT-LABEL>DisplayControl</SHORT-LABEL>
                          <FIELDS>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>enable</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>set_speed</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>noa_status</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </FIELDS>
                        </RECORD-VALUE-SPECIFICATION>
                        <RECORD-VALUE-SPECIFICATION>
                          <SHORT-LABEL>VelocityControl</SHORT-LABEL>
                          <FIELDS>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>enable</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>vel</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>dist</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </FIELDS>
                        </RECORD-VALUE-SPECIFICATION>
                      </FIELDS>
                    </RECORD-VALUE-SPECIFICATION>
                  </INIT-VALUE>
                </NONQUEUED-RECEIVER-COM-SPEC>
              </REQUIRED-COM-SPECS>
              <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/Ctrl_st</REQUIRED-INTERFACE-TREF>
            </R-PORT-PROTOTYPE>
            <R-PORT-PROTOTYPE UUID="C3F74484-5C45-4BFE-8249-16DD3AE83DC8">
              <SHORT-NAME>PR_Ctrl_st_SPI</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <REQUIRED-COM-SPECS>
                <NONQUEUED-RECEIVER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/Ctrl_st_SPI/Ctrl_st_SPI</DATA-ELEMENT-REF>
                  <ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
                  <ENABLE-UPDATE>false</ENABLE-UPDATE>
                  <FILTER>
                    <DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
                  </FILTER>
                  <HANDLE-NEVER-RECEIVED>false</HANDLE-NEVER-RECEIVED>
                  <INIT-VALUE>
                    <RECORD-VALUE-SPECIFICATION>
                      <FIELDS>
                        <RECORD-VALUE-SPECIFICATION>
                          <SHORT-LABEL>Header</SHORT-LABEL>
                          <FIELDS>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>seq</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>ts_high</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>ts_low</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </FIELDS>
                        </RECORD-VALUE-SPECIFICATION>
                        <RECORD-VALUE-SPECIFICATION>
                          <SHORT-LABEL>AccControl</SHORT-LABEL>
                          <FIELDS>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>enable</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>method</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>torque</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>acc</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>drvoffStop</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </FIELDS>
                        </RECORD-VALUE-SPECIFICATION>
                        <RECORD-VALUE-SPECIFICATION>
                          <SHORT-LABEL>SteerControl</SHORT-LABEL>
                          <FIELDS>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>enable</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>method</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>torque</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>angle</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>rate</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </FIELDS>
                        </RECORD-VALUE-SPECIFICATION>
                        <RECORD-VALUE-SPECIFICATION>
                          <SHORT-LABEL>GearControl</SHORT-LABEL>
                          <FIELDS>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>enable</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>gear</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </FIELDS>
                        </RECORD-VALUE-SPECIFICATION>
                        <RECORD-VALUE-SPECIFICATION>
                          <SHORT-LABEL>BcmControl</SHORT-LABEL>
                          <FIELDS>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>enable</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>turn_left_light</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>turn_right_light</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>emergency_light</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>brake_light</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>high_beam</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>low_beam</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>horn</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>wipe</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </FIELDS>
                        </RECORD-VALUE-SPECIFICATION>
                        <RECORD-VALUE-SPECIFICATION>
                          <SHORT-LABEL>SoundControl</SHORT-LABEL>
                          <FIELDS>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>enable</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>sound_id</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </FIELDS>
                        </RECORD-VALUE-SPECIFICATION>
                        <RECORD-VALUE-SPECIFICATION>
                          <SHORT-LABEL>DisplayControl</SHORT-LABEL>
                          <FIELDS>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>enable</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>set_speed</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>noa_status</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </FIELDS>
                        </RECORD-VALUE-SPECIFICATION>
                        <RECORD-VALUE-SPECIFICATION>
                          <SHORT-LABEL>VelocityControl</SHORT-LABEL>
                          <FIELDS>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>enable</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>vel</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                            <NUMERICAL-VALUE-SPECIFICATION>
                              <SHORT-LABEL>dist</SHORT-LABEL>
                              <VALUE>0</VALUE>
                            </NUMERICAL-VALUE-SPECIFICATION>
                          </FIELDS>
                        </RECORD-VALUE-SPECIFICATION>
                      </FIELDS>
                    </RECORD-VALUE-SPECIFICATION>
                  </INIT-VALUE>
                </NONQUEUED-RECEIVER-COM-SPEC>
              </REQUIRED-COM-SPECS>
              <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/Ctrl_st_SPI</REQUIRED-INTERFACE-TREF>
            </R-PORT-PROTOTYPE>
            <R-PORT-PROTOTYPE UUID="1B271E3E-2F11-4790-86A7-6E6645CEA135">
              <SHORT-NAME>PR_ESP_WheelRotationDirectionFL</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <REQUIRED-COM-SPECS>
                <NONQUEUED-RECEIVER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/ESP_WheelRotationDirectionFL/ESP_WheelRotationDirectionFL</DATA-ELEMENT-REF>
                  <ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
                  <ENABLE-UPDATE>false</ENABLE-UPDATE>
                  <FILTER>
                    <DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
                  </FILTER>
                  <HANDLE-NEVER-RECEIVED>false</HANDLE-NEVER-RECEIVED>
                  <INIT-VALUE>
                    <NUMERICAL-VALUE-SPECIFICATION>
                      <VALUE>0</VALUE>
                    </NUMERICAL-VALUE-SPECIFICATION>
                  </INIT-VALUE>
                </NONQUEUED-RECEIVER-COM-SPEC>
              </REQUIRED-COM-SPECS>
              <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/ESP_WheelRotationDirectionFL</REQUIRED-INTERFACE-TREF>
            </R-PORT-PROTOTYPE>
            <R-PORT-PROTOTYPE UUID="433A93F5-D38B-4231-AE1D-2D25E9BE0E60">
              <SHORT-NAME>PR_ESP_WheelRotationDirectionFR</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <REQUIRED-COM-SPECS>
                <NONQUEUED-RECEIVER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/ESP_WheelRotationDirectionFR/ESP_WheelRotationDirectionFR</DATA-ELEMENT-REF>
                  <ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
                  <ENABLE-UPDATE>false</ENABLE-UPDATE>
                  <FILTER>
                    <DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
                  </FILTER>
                  <HANDLE-NEVER-RECEIVED>false</HANDLE-NEVER-RECEIVED>
                  <INIT-VALUE>
                    <NUMERICAL-VALUE-SPECIFICATION>
                      <VALUE>0</VALUE>
                    </NUMERICAL-VALUE-SPECIFICATION>
                  </INIT-VALUE>
                </NONQUEUED-RECEIVER-COM-SPEC>
              </REQUIRED-COM-SPECS>
              <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/ESP_WheelRotationDirectionFR</REQUIRED-INTERFACE-TREF>
            </R-PORT-PROTOTYPE>
            <R-PORT-PROTOTYPE UUID="********-91D3-4D87-A483-AEF9B633B851">
              <SHORT-NAME>PR_ESP_WheelRotationDirectionRL</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <REQUIRED-COM-SPECS>
                <NONQUEUED-RECEIVER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/ESP_WheelRotationDirectionRL/ESP_WheelRotationDirectionRL</DATA-ELEMENT-REF>
                  <ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
                  <ENABLE-UPDATE>false</ENABLE-UPDATE>
                  <FILTER>
                    <DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
                  </FILTER>
                  <HANDLE-NEVER-RECEIVED>false</HANDLE-NEVER-RECEIVED>
                </NONQUEUED-RECEIVER-COM-SPEC>
              </REQUIRED-COM-SPECS>
              <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/ESP_WheelRotationDirectionRL</REQUIRED-INTERFACE-TREF>
            </R-PORT-PROTOTYPE>
            <R-PORT-PROTOTYPE UUID="2FF44E67-1F24-4E7C-9A21-C377E1730BA4">
              <SHORT-NAME>PR_ESP_WheelRotationDirectionRR</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <REQUIRED-COM-SPECS>
                <NONQUEUED-RECEIVER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/ESP_WheelRotationDirectionRR/ESP_WheelRotationDirectionRR</DATA-ELEMENT-REF>
                  <ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
                  <ENABLE-UPDATE>false</ENABLE-UPDATE>
                  <FILTER>
                    <DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
                  </FILTER>
                  <HANDLE-NEVER-RECEIVED>false</HANDLE-NEVER-RECEIVED>
                  <INIT-VALUE>
                    <NUMERICAL-VALUE-SPECIFICATION>
                      <VALUE>0</VALUE>
                    </NUMERICAL-VALUE-SPECIFICATION>
                  </INIT-VALUE>
                </NONQUEUED-RECEIVER-COM-SPEC>
              </REQUIRED-COM-SPECS>
              <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/ESP_WheelRotationDirectionRR</REQUIRED-INTERFACE-TREF>
            </R-PORT-PROTOTYPE>
            <R-PORT-PROTOTYPE UUID="7499F35E-FD1F-415E-B07A-31F294518DFD">
              <SHORT-NAME>PR_ESP_WheelSpdPulseCounterFL</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <REQUIRED-COM-SPECS>
                <NONQUEUED-RECEIVER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/ESP_WheelSpdPulseCounterFL/ESP_WheelSpdPulseCounterFL</DATA-ELEMENT-REF>
                  <ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
                  <ENABLE-UPDATE>false</ENABLE-UPDATE>
                  <FILTER>
                    <DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
                  </FILTER>
                  <HANDLE-NEVER-RECEIVED>false</HANDLE-NEVER-RECEIVED>
                  <INIT-VALUE>
                    <NUMERICAL-VALUE-SPECIFICATION>
                      <VALUE>0</VALUE>
                    </NUMERICAL-VALUE-SPECIFICATION>
                  </INIT-VALUE>
                </NONQUEUED-RECEIVER-COM-SPEC>
              </REQUIRED-COM-SPECS>
              <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/ESP_WheelSpdPulseCounterFL</REQUIRED-INTERFACE-TREF>
            </R-PORT-PROTOTYPE>
            <R-PORT-PROTOTYPE UUID="92F4C5AB-535E-49B9-A406-C7F477295007">
              <SHORT-NAME>PR_ESP_WheelSpdPulseCounterFR</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <REQUIRED-COM-SPECS>
                <NONQUEUED-RECEIVER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/ESP_WheelSpdPulseCounterFR/ESP_WheelSpdPulseCounterFR</DATA-ELEMENT-REF>
                  <ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
                  <ENABLE-UPDATE>false</ENABLE-UPDATE>
                  <FILTER>
                    <DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
                  </FILTER>
                  <HANDLE-NEVER-RECEIVED>false</HANDLE-NEVER-RECEIVED>
                  <INIT-VALUE>
                    <NUMERICAL-VALUE-SPECIFICATION>
                      <VALUE>0</VALUE>
                    </NUMERICAL-VALUE-SPECIFICATION>
                  </INIT-VALUE>
                </NONQUEUED-RECEIVER-COM-SPEC>
              </REQUIRED-COM-SPECS>
              <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/ESP_WheelSpdPulseCounterFR</REQUIRED-INTERFACE-TREF>
            </R-PORT-PROTOTYPE>
            <R-PORT-PROTOTYPE UUID="DC91EAC6-1605-48DE-9A4F-4521F5802FB1">
              <SHORT-NAME>PR_ESP_WheelSpdPulseCounterRL</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <REQUIRED-COM-SPECS>
                <NONQUEUED-RECEIVER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/ESP_WheelSpdPulseCounterRL/ESP_WheelSpdPulseCounterRL</DATA-ELEMENT-REF>
                  <ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
                  <ENABLE-UPDATE>false</ENABLE-UPDATE>
                  <FILTER>
                    <DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
                  </FILTER>
                  <HANDLE-NEVER-RECEIVED>false</HANDLE-NEVER-RECEIVED>
                </NONQUEUED-RECEIVER-COM-SPEC>
              </REQUIRED-COM-SPECS>
              <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/ESP_WheelSpdPulseCounterRL</REQUIRED-INTERFACE-TREF>
            </R-PORT-PROTOTYPE>
            <R-PORT-PROTOTYPE UUID="ED713444-0DF7-4A18-B67D-3A312F5E2AA0">
              <SHORT-NAME>PR_ESP_WheelSpdPulseCounterRR</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <REQUIRED-COM-SPECS>
                <NONQUEUED-RECEIVER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/ESP_WheelSpdPulseCounterRR/ESP_WheelSpdPulseCounterRR</DATA-ELEMENT-REF>
                  <ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
                  <ENABLE-UPDATE>false</ENABLE-UPDATE>
                  <FILTER>
                    <DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
                  </FILTER>
                  <HANDLE-NEVER-RECEIVED>false</HANDLE-NEVER-RECEIVED>
                </NONQUEUED-RECEIVER-COM-SPEC>
              </REQUIRED-COM-SPECS>
              <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/ESP_WheelSpdPulseCounterRR</REQUIRED-INTERFACE-TREF>
            </R-PORT-PROTOTYPE>
            <R-PORT-PROTOTYPE UUID="073F19D9-E84D-4ACC-A0FD-FF5D0F9A5E7F">
              <SHORT-NAME>PR_IB_BrakePedalStatus</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <REQUIRED-COM-SPECS>
                <NONQUEUED-RECEIVER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/IB_BrakePedalStatus/IB_BrakePedalStatus</DATA-ELEMENT-REF>
                  <ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
                  <ENABLE-UPDATE>false</ENABLE-UPDATE>
                  <FILTER>
                    <DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
                  </FILTER>
                  <HANDLE-NEVER-RECEIVED>false</HANDLE-NEVER-RECEIVED>
                  <INIT-VALUE>
                    <CONSTANT-REFERENCE>
                      <CONSTANT-REF DEST="CONSTANT-SPECIFICATION">/ADAS/Constants/DefaultInitValue_UInt8</CONSTANT-REF>
                    </CONSTANT-REFERENCE>
                  </INIT-VALUE>
                </NONQUEUED-RECEIVER-COM-SPEC>
              </REQUIRED-COM-SPECS>
              <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/IB_BrakePedalStatus</REQUIRED-INTERFACE-TREF>
            </R-PORT-PROTOTYPE>
            <R-PORT-PROTOTYPE UUID="26E19233-4B15-4951-8442-B671D795BDC9">
              <SHORT-NAME>PR_RWPulse_RollingCounters</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <REQUIRED-COM-SPECS>
                <NONQUEUED-RECEIVER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/RWPulse_RollingCounters/RWPulse_RollingCounters</DATA-ELEMENT-REF>
                  <ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
                  <ENABLE-UPDATE>false</ENABLE-UPDATE>
                  <FILTER>
                    <DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
                  </FILTER>
                  <HANDLE-NEVER-RECEIVED>false</HANDLE-NEVER-RECEIVED>
                  <INIT-VALUE>
                    <ARRAY-VALUE-SPECIFICATION>
                      <ELEMENTS>
                        <NUMERICAL-VALUE-SPECIFICATION>
                          <VALUE>0</VALUE>
                        </NUMERICAL-VALUE-SPECIFICATION>
                        <NUMERICAL-VALUE-SPECIFICATION>
                          <VALUE>0</VALUE>
                        </NUMERICAL-VALUE-SPECIFICATION>
                        <NUMERICAL-VALUE-SPECIFICATION>
                          <VALUE>0</VALUE>
                        </NUMERICAL-VALUE-SPECIFICATION>
                      </ELEMENTS>
                    </ARRAY-VALUE-SPECIFICATION>
                  </INIT-VALUE>
                </NONQUEUED-RECEIVER-COM-SPEC>
              </REQUIRED-COM-SPECS>
              <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterfaces/RWPulse_RollingCounters</REQUIRED-INTERFACE-TREF>
            </R-PORT-PROTOTYPE>
            <R-PORT-PROTOTYPE UUID="C5950294-7FBC-4CF5-9022-A6DCC940D402">
              <SHORT-NAME>PR_SteerWhlAg</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <REQUIRED-COM-SPECS>
                <NONQUEUED-RECEIVER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/ADAS/PortInterfaces/SteerWhlAg/SteerWhlAg</DATA-ELEMENT-REF>
                  <ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
                  <ENABLE-UPDATE>false</ENABLE-UPDATE>
                  <FILTER>
                    <DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
                  </FILTER>
                  <HANDLE-NEVER-RECEIVED>false</HANDLE-NEVER-RECEIVED>
                  <INIT-VALUE>
                    <NUMERICAL-VALUE-SPECIFICATION>
                      <VALUE>0</VALUE>
                    </NUMERICAL-VALUE-SPECIFICATION>
                  </INIT-VALUE>
                </NONQUEUED-RECEIVER-COM-SPEC>
              </REQUIRED-COM-SPECS>
              <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/ADAS/PortInterfaces/SteerWhlAg</REQUIRED-INTERFACE-TREF>
            </R-PORT-PROTOTYPE>
            <R-PORT-PROTOTYPE UUID="5CADACF8-C6CF-4B7E-A796-F54BFC9E2128">
              <SHORT-NAME>PR_TrsmActGear</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <REQUIRED-COM-SPECS>
                <NONQUEUED-RECEIVER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/ADAS/PortInterfaces/TrsmActGear/TrsmActGear</DATA-ELEMENT-REF>
                  <ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
                  <ENABLE-UPDATE>false</ENABLE-UPDATE>
                  <FILTER>
                    <DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
                  </FILTER>
                  <HANDLE-NEVER-RECEIVED>false</HANDLE-NEVER-RECEIVED>
                  <INIT-VALUE>
                    <NUMERICAL-VALUE-SPECIFICATION>
                      <VALUE>0</VALUE>
                    </NUMERICAL-VALUE-SPECIFICATION>
                  </INIT-VALUE>
                </NONQUEUED-RECEIVER-COM-SPEC>
              </REQUIRED-COM-SPECS>
              <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/ADAS/PortInterfaces/TrsmActGear</REQUIRED-INTERFACE-TREF>
            </R-PORT-PROTOTYPE>
            <R-PORT-PROTOTYPE UUID="6ADA8B5C-FDC4-4E42-9D1F-BE273C45EF2D">
              <SHORT-NAME>PR_VehSpdMps</SHORT-NAME>
              <ADMIN-DATA>
                <SDGS>
                  <SDG GID="DV:DEV">
                    <SD GID="DV:ImportModePreset">Keep</SD>
                  </SDG>
                </SDGS>
              </ADMIN-DATA>
              <REQUIRED-COM-SPECS>
                <NONQUEUED-RECEIVER-COM-SPEC>
                  <DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/ADAS/PortInterfaces/VehSpdMps/VehSpdMps</DATA-ELEMENT-REF>
                  <ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
                  <ENABLE-UPDATE>false</ENABLE-UPDATE>
                  <FILTER>
                    <DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
                  </FILTER>
                  <HANDLE-NEVER-RECEIVED>false</HANDLE-NEVER-RECEIVED>
                  <INIT-VALUE>
                    <NUMERICAL-VALUE-SPECIFICATION>
                      <VALUE>0</VALUE>
                    </NUMERICAL-VALUE-SPECIFICATION>
                  </INIT-VALUE>
                </NONQUEUED-RECEIVER-COM-SPEC>
              </REQUIRED-COM-SPECS>
              <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/ADAS/PortInterfaces/VehSpdMps</REQUIRED-INTERFACE-TREF>
            </R-PORT-PROTOTYPE>
          </PORTS>
          <INTERNAL-BEHAVIORS>
            <SWC-INTERNAL-BEHAVIOR UUID="B834763C-006B-4338-941B-A54C6207151C">
              <SHORT-NAME>APA_InternalBehavior</SHORT-NAME>
              <DATA-TYPE-MAPPING-REFS>
                <DATA-TYPE-MAPPING-REF DEST="DATA-TYPE-MAPPING-SET">/ComponentTypes/DataTypeMappingSets/APATypeMappingSet</DATA-TYPE-MAPPING-REF>
                <DATA-TYPE-MAPPING-REF DEST="DATA-TYPE-MAPPING-SET">/ComponentTypes/DataTypeMappingSets/DataTypeMappings_DEV</DATA-TYPE-MAPPING-REF>
                <DATA-TYPE-MAPPING-REF DEST="DATA-TYPE-MAPPING-SET">/DataType/DataTypeMappingSet</DATA-TYPE-MAPPING-REF>
              </DATA-TYPE-MAPPING-REFS>
              <EVENTS>
                <INIT-EVENT UUID="9832D22B-0353-42C9-8076-B2DEC3DEFAD8">
                  <SHORT-NAME>APA_InitEvent</SHORT-NAME>
                  <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/APA_pkg/APA/APA_InternalBehavior/APA_Init</START-ON-EVENT-REF>
                </INIT-EVENT>
                <TIMING-EVENT UUID="9BDCB4F6-9C86-4BD4-859C-B7124647547A">
                  <SHORT-NAME>TMT_APA_Step</SHORT-NAME>
                  <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/APA_pkg/APA/APA_InternalBehavior/APA_Step</START-ON-EVENT-REF>
                  <PERIOD>0.025</PERIOD>
                </TIMING-EVENT>
              </EVENTS>
              <RUNNABLES>
                <RUNNABLE-ENTITY UUID="703A566D-1D5E-4E33-9054-039B330751A3">
                  <SHORT-NAME>APA_Init</SHORT-NAME>
                  <ADMIN-DATA>
                    <SDGS>
                      <SDG GID="edve:RunnableKind">
                        <SD>InitRunnable</SD>
                      </SDG>
                    </SDGS>
                  </ADMIN-DATA>
                  <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                  <CAN-BE-INVOKED-CONCURRENTLY>false</CAN-BE-INVOKED-CONCURRENTLY>
                  <SYMBOL>APA_Init</SYMBOL>
                </RUNNABLE-ENTITY>
                <RUNNABLE-ENTITY UUID="068B910E-AB17-4648-83AA-839EC0C3434B">
                  <SHORT-NAME>APA_Step</SHORT-NAME>
                  <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                  <CAN-BE-INVOKED-CONCURRENTLY>false</CAN-BE-INVOKED-CONCURRENTLY>
                  <DATA-READ-ACCESSS>
                    <VARIABLE-ACCESS UUID="9AA0317E-F715-48FD-A482-19CBBA91937A">
                      <SHORT-NAME>READ_PR_ACU_LongititudeAcc_ACU_LongititudeAcc</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/APA_pkg/APA/PR_ACU_LongititudeAcc</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/ACU_LongititudeAcc/ACU_LongititudeAcc</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="69F8CDB7-BE27-441D-9617-927102692E82">
                      <SHORT-NAME>READ_PR_APA_HandshakeACC_APA_HandshakeACC</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/APA_pkg/APA/PR_APA_HandshakeACC</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/APA_HandshakeACC/APA_HandshakeACC</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="ED44FCDE-050E-4958-9D49-6E27F96082D4">
                      <SHORT-NAME>READ_PR_APA_HandshakeEPS_EAPA_HandshakeEPS</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/APA_pkg/APA/PR_APA_HandshakeEPS</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/APA_HandshakeEPS/APA_HandshakeEPS</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="1CC6D0DE-73D1-412B-BD54-AD727975BF2E">
                      <SHORT-NAME>READ_PR_APA_HandshakeESP_APA_HandshakeESP</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/APA_pkg/APA/PR_APA_HandshakeESP</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/APA_HandshakeESP/APA_HandshakeESP</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="13575902-CCCF-43FA-888F-A921F2FC0920">
                      <SHORT-NAME>READ_PR_APA_IsLastPath_APA_IsLastPath</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/APA_pkg/APA/PR_APA_IsLastPath</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/APA_IsLastPath/APA_IsLastPath</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="F19264CB-4A4B-492E-AB60-BD9FB67AC6EC">
                      <SHORT-NAME>READ_PR_APA_RemainDistance_APA_RemainDistance</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/APA_pkg/APA/PR_APA_RemainDistance</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/APA_RemainDistance/APA_RemainDistance</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="EFFEC5AB-22B9-4DA8-9E5F-6A58060205EB">
                      <SHORT-NAME>READ_PR_Ctrl_st_Ctrl_st</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/APA_pkg/APA/PR_Ctrl_st</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/Ctrl_st/Ctrl_st</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="7DB46B22-2CE5-4694-8067-B5ED9E2E3AB5">
                      <SHORT-NAME>READ_PR_Ctrl_st_SPI_Ctrl_st_SPI</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/APA_pkg/APA/PR_Ctrl_st_SPI</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/Ctrl_st_SPI/Ctrl_st_SPI</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="BC0DE5AF-97D5-483F-9114-852119F17288">
                      <SHORT-NAME>READ_PR_ESP_WheelRotationDirectionFL_ESP_WheelRotationDirectionFL</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/APA_pkg/APA/PR_ESP_WheelRotationDirectionFL</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/ESP_WheelRotationDirectionFL/ESP_WheelRotationDirectionFL</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="C18C0F2A-413B-410C-B344-E3A68D8FBCFD">
                      <SHORT-NAME>READ_PR_ESP_WheelRotationDirectionFR_ESP_WheelRotationDirectionFR</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/APA_pkg/APA/PR_ESP_WheelRotationDirectionFR</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/ESP_WheelRotationDirectionFR/ESP_WheelRotationDirectionFR</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="08015305-B2DB-497D-8BA6-D336ACA2FB9B">
                      <SHORT-NAME>READ_PR_ESP_WheelRotationDirectionRL_ESP_WheelRotationDirectionRL</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/APA_pkg/APA/PR_ESP_WheelRotationDirectionRL</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/ESP_WheelRotationDirectionRL/ESP_WheelRotationDirectionRL</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="BBD413FE-F189-463D-A650-D7F356131B58">
                      <SHORT-NAME>READ_PR_ESP_WheelRotationDirectionRR_ESP_WheelRotationDirectionRR</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/APA_pkg/APA/PR_ESP_WheelRotationDirectionRR</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/ESP_WheelRotationDirectionRR/ESP_WheelRotationDirectionRR</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="11AA569D-4BEB-406B-9596-CB3BC412B162">
                      <SHORT-NAME>READ_PR_ESP_WheelSpdPulseCounterFL_ESP_WheelSpdPulseCounterFL</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/APA_pkg/APA/PR_ESP_WheelSpdPulseCounterFL</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/ESP_WheelSpdPulseCounterFL/ESP_WheelSpdPulseCounterFL</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="7E312D4C-C4CF-49E8-BA01-61C82463820B">
                      <SHORT-NAME>READ_PR_ESP_WheelSpdPulseCounterFR_ESP_WheelSpdPulseCounterFR</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/APA_pkg/APA/PR_ESP_WheelSpdPulseCounterFR</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/ESP_WheelSpdPulseCounterFR/ESP_WheelSpdPulseCounterFR</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="131E2F32-0E04-4560-9612-A1739FA5F538">
                      <SHORT-NAME>READ_PR_ESP_WheelSpdPulseCounterRL_ESP_WheelSpdPulseCounterRL</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/APA_pkg/APA/PR_ESP_WheelSpdPulseCounterRL</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/ESP_WheelSpdPulseCounterRL/ESP_WheelSpdPulseCounterRL</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="0AD2382C-D694-4E27-95D5-3943412171A5">
                      <SHORT-NAME>READ_PR_ESP_WheelSpdPulseCounterRR_ESP_WheelSpdPulseCounterRR</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/APA_pkg/APA/PR_ESP_WheelSpdPulseCounterRR</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/ESP_WheelSpdPulseCounterRR/ESP_WheelSpdPulseCounterRR</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="713F2325-93ED-4CB2-A71E-E15DBB4B3084">
                      <SHORT-NAME>READ_PR_IB_BrakePedalStatus_IB_BrakePedalStatus</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/APA_pkg/APA/PR_IB_BrakePedalStatus</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/IB_BrakePedalStatus/IB_BrakePedalStatus</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="0EF056DF-28E7-4A93-BB66-249D72F9B9DF">
                      <SHORT-NAME>READ_PR_RWPulse_RollingCounters_RWPulse_RollingCounters</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/APA_pkg/APA/PR_RWPulse_RollingCounters</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/RWPulse_RollingCounters/RWPulse_RollingCounters</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="A3F22224-CC25-4B90-B7E2-201D9A6216A6">
                      <SHORT-NAME>READ_PR_SteerWhlAg_SteerWhlAg</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/APA_pkg/APA/PR_SteerWhlAg</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/ADAS/PortInterfaces/SteerWhlAg/SteerWhlAg</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="4A2F65AF-E20C-42D9-B795-6CF9094183F1">
                      <SHORT-NAME>READ_PR_TrsmActGear_TrsmActGear</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/APA_pkg/APA/PR_TrsmActGear</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/ADAS/PortInterfaces/TrsmActGear/TrsmActGear</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="EE9B4013-241D-4262-97F9-D56918032371">
                      <SHORT-NAME>READ_PR_VehSpdMps_VehSpdMps</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="R-PORT-PROTOTYPE">/APA_pkg/APA/PR_VehSpdMps</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/ADAS/PortInterfaces/VehSpdMps/VehSpdMps</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                  </DATA-READ-ACCESSS>
                  <DATA-WRITE-ACCESSS>
                    <VARIABLE-ACCESS UUID="1F6A05C3-6BED-45E3-956A-37F209BB29EA">
                      <SHORT-NAME>WRITE_PP_BlockFlag_BlockFlag</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="P-PORT-PROTOTYPE">/APA_pkg/APA/PP_BlockFlag</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/BlockFlag/BlockFlag</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="9F1C8BE3-783B-47B3-89EC-980904FC38FA">
                      <SHORT-NAME>WRITE_PP_CollisionFlag_CollisionFlag</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="P-PORT-PROTOTYPE">/APA_pkg/APA/PP_CollisionFlag</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/CollisionFlag/CollisionFlag</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="D4F096BF-3781-45FF-83F3-B85910DDB574">
                      <SHORT-NAME>WRITE_PP_EstimateAcceleration_EstimateAcceleration</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="P-PORT-PROTOTYPE">/APA_pkg/APA/PP_EstimateAcceleration</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/EstimateAcceleration/EstimateAcceleration</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                    <VARIABLE-ACCESS UUID="EA22DA4E-0BC1-4555-AD97-8C2228C71BA3">
                      <SHORT-NAME>WRITE_PP_EstimateSpeed_EstimateSpeed</SHORT-NAME>
                      <ACCESSED-VARIABLE>
                        <AUTOSAR-VARIABLE-IREF>
                          <PORT-PROTOTYPE-REF DEST="P-PORT-PROTOTYPE">/APA_pkg/APA/PP_EstimateSpeed</PORT-PROTOTYPE-REF>
                          <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterfaces/EstimateSpeed/EstimateSpeed</TARGET-DATA-PROTOTYPE-REF>
                        </AUTOSAR-VARIABLE-IREF>
                      </ACCESSED-VARIABLE>
                    </VARIABLE-ACCESS>
                  </DATA-WRITE-ACCESSS>
                  <SYMBOL>APA_Step</SYMBOL>
                </RUNNABLE-ENTITY>
              </RUNNABLES>
              <SUPPORTS-MULTIPLE-INSTANTIATION>false</SUPPORTS-MULTIPLE-INSTANTIATION>
            </SWC-INTERNAL-BEHAVIOR>
          </INTERNAL-BEHAVIORS>
        </APPLICATION-SW-COMPONENT-TYPE>
        <SWC-IMPLEMENTATION UUID="86B7D91A-D3C7-4562-AC4A-20E6DCEE1955">
          <SHORT-NAME>APA_Implementation</SHORT-NAME>
          <CODE-DESCRIPTORS>
            <CODE UUID="C71208E4-B64F-4716-B0C1-79EB938F49A7">
              <SHORT-NAME>Default</SHORT-NAME>
              <ARTIFACT-DESCRIPTORS>
                <AUTOSAR-ENGINEERING-OBJECT>
                  <CATEGORY>SWSRC</CATEGORY>
                </AUTOSAR-ENGINEERING-OBJECT>
              </ARTIFACT-DESCRIPTORS>
            </CODE>
          </CODE-DESCRIPTORS>
          <BEHAVIOR-REF DEST="SWC-INTERNAL-BEHAVIOR">/APA_pkg/APA/APA_InternalBehavior</BEHAVIOR-REF>
        </SWC-IMPLEMENTATION>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>