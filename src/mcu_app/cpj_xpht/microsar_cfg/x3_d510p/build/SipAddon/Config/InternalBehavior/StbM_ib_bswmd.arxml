<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
      <SHORT-NAME>MICROSAR</SHORT-NAME>
      <ELEMENTS>
        <BSW-IMPLEMENTATION UUID="fe386218-4649-472f-97cd-8c56262c9005">
          <SHORT-NAME>StbM_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>8.02.02</SW-VERSION>
          <USED-CODE-GENERATOR>DaV<PERSON>ci Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.05.00</AR-RELEASE-VERSION>
        </BSW-IMPLEMENTATION>
      </ELEMENTS>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="3665fa87-f76a-4a20-97e3-c618ea69ddd0">
          <SHORT-NAME>StbM_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="d2a5ac1a-c2a8-490b-92ab-7d89a03d0183">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="6225df76-**************-e1f66846bc3b">
                  <SHORT-NAME>StbM</SHORT-NAME>
                  <PROVIDED-ENTRYS>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/StbM_ib_bswmd/BswModuleDescriptions/StbM_MainFunction</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  </PROVIDED-ENTRYS>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="1ea68d08-c5b9-4895-b616-71e4f06cd128">
                      <SHORT-NAME>StbMBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="03009d04-6a01-4891-b027-a1bcb051ef7e">
                          <SHORT-NAME>STBM_EXCLUSIVE_AREA_0</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="07a14f98-d09b-401b-b618-580bd1872848">
                          <SHORT-NAME>StbM_MainFunction</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/StbM_ib_bswmd/BswModuleDescriptions/StbM_MainFunction</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="faedfc26-97c4-4c55-91cf-9d062abac764">
                          <SHORT-NAME>StbM_MainFunctionTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/StbM_ib_bswmd/BswModuleDescriptions/StbM/StbMBehavior/StbM_MainFunction</STARTS-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="cf9d345d-1f1a-4f92-9c96-bf9764817851">
                  <SHORT-NAME>StbM_MainFunction</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="fe1d7d9e-396d-48e6-b505-0e15f72ff34e">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
