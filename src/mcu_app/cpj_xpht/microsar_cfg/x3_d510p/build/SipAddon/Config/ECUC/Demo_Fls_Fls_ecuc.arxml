<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>ActiveEcuC</SHORT-NAME>
      <ELEMENTS>
        <ECUC-MODULE-CONFIGURATION-VALUES UUID="555e08a7-dbe4-42fa-b0b7-9b339bef2c7e">
          <SHORT-NAME>Fls</SHORT-NAME>
          <ADMIN-DATA>
            <SDGS>
              <SDG GID="DV:CfgPostBuild">
                <SD GID="DV:postBuildVariantSupport">false</SD>
              </SDG>
            </SDGS>
          </ADMIN-DATA>
          <DEFINITION-REF DEST="ECUC-MODULE-DEF">/TI_DRA80X_J7/Fls</DEFINITION-REF>
          <IMPLEMENTATION-CONFIG-VARIANT>VARIANT-PRE-COMPILE</IMPLEMENTATION-CONFIG-VARIANT>
          <MODULE-DESCRIPTION-REF DEST="BSW-IMPLEMENTATION">/TI_DRA80X_J7/Fls_Impl</MODULE-DESCRIPTION-REF>
          <CONTAINERS>
            <ECUC-CONTAINER-VALUE UUID="1448fad8-513d-4435-b5c6-0c32994a2771">
              <SHORT-NAME>FlsConfigSet</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet/FlsAcErase</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet/FlsAcWrite</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet/FlsCallCycle</DEFINITION-REF>
                  <VALUE>0.005</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet/FlsDefaultMode</DEFINITION-REF>
                  <VALUE>MEMIF_MODE_SLOW</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet/FlsMaxReadFastMode</DEFINITION-REF>
                  <VALUE>256</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet/FlsMaxReadNormalMode</DEFINITION-REF>
                  <VALUE>512</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet/FlsMaxWriteFastMode</DEFINITION-REF>
                  <VALUE>512</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet/FlsMaxWriteNormalMode</DEFINITION-REF>
                  <VALUE>512</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet/FlsProtection</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet/FlsDacEnable</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet/FlsXipEnable</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet/FlsOspiClkSpeed</DEFINITION-REF>
                  <VALUE>133333333</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet/FlsDtrEnable</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet/FlsJobEndNotification</DEFINITION-REF>
                  <VALUE>Fee_JobEndNotification</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet/FlsJobErrorNotification</DEFINITION-REF>
                  <VALUE>Fee_JobErrorNotification</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
              </PARAMETER-VALUES>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE UUID="c8d3e690-226e-4874-9149-ef9389958d55">
                  <SHORT-NAME>FlsSectorList</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet/FlsSectorList</DEFINITION-REF>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE UUID="07b6a449-3dfd-4f49-9f68-462c36594943">
                      <SHORT-NAME>FlsSector</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet/FlsSectorList/FlsSector</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet/FlsSectorList/FlsSector/FlsNumberOfSectors</DEFINITION-REF>
                          <VALUE>2</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet/FlsSectorList/FlsSector/FlsPageSize</DEFINITION-REF>
                          <VALUE>8</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet/FlsSectorList/FlsSector/FlsSectorSize</DEFINITION-REF>
                          <VALUE>262144</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsConfigSet/FlsSectorList/FlsSector/FlsSectorStartaddress</DEFINITION-REF>
                          <VALUE>33030144</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE UUID="664b02b9-95c9-4890-a5df-12f97194562f">
              <SHORT-NAME>FlsGeneral</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Fls/FlsGeneral</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsGeneral/FlsAcLoadOnJobStart</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsGeneral/FlsBaseAddress</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsGeneral/FlsBlankCheckApi</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsGeneral/FlsCancelApi</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsGeneral/FlsCompareApi</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsGeneral/FlsDevErrorDetect</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsGeneral/FlsDriverIndex</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsGeneral/FlsEraseVerificationEnabled</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsGeneral/FlsGetJobResultApi</DEFINITION-REF>
                  <ANNOTATIONS>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">Annotation</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:User</ANNOTATION-ORIGIN>
                      <ANNOTATION-TEXT>
                        <P>
                          <L-1 L="FOR-ALL">PLAIN_TEXT</L-1>
                        </P>
                        <P>
                          <L-1 L="FOR-ALL">ryan:&#13;
The generated code has a problem, that will report function undefined symbol.&#13;
&#13;
example:&#13;
#define FLS_GET_JOB_RESULT_API(STD_ON)&#13;
&#13;
need change:&#13;
#define FLS_GET_JOB_RESULT_API         (STD_ON)</L-1>
                        </P>
                      </ANNOTATION-TEXT>
                    </ANNOTATION>
                  </ANNOTATIONS>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsGeneral/FlsGetStatusApi</DEFINITION-REF>
                  <ANNOTATIONS>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">Annotation</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:User</ANNOTATION-ORIGIN>
                      <ANNOTATION-TEXT>
                        <P>
                          <L-1 L="FOR-ALL">PLAIN_TEXT</L-1>
                        </P>
                        <P>
                          <L-1 L="FOR-ALL">ryan:&#13;
The generated code has a problem, that will report function undefined symbol.&#13;
&#13;
example:&#13;
#define FLS_GET_JOB_RESULT_API(STD_ON)&#13;
&#13;
need change:&#13;
#define FLS_GET_JOB_RESULT_API         (STD_ON)</L-1>
                        </P>
                      </ANNOTATION-TEXT>
                    </ANNOTATION>
                  </ANNOTATIONS>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsGeneral/FlsSetModeApi</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsGeneral/FlsTimeoutSupervisionEnabled</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsGeneral/FlsTotalSize</DEFINITION-REF>
                  <VALUE>2097152</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsGeneral/FlsUseInterrupts</DEFINITION-REF>
                  <ANNOTATIONS>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">Annotation</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:User</ANNOTATION-ORIGIN>
                      <ANNOTATION-TEXT>
                        <P>
                          <L-1 L="FOR-ALL">PLAIN_TEXT</L-1>
                        </P>
                        <P>
                          <L-1 L="FOR-ALL">ryan:&#13;
The generated code has a problem, that will report function undefined symbol.&#13;
&#13;
example:&#13;
#define FLS_GET_JOB_RESULT_API(STD_ON)&#13;
&#13;
need change:&#13;
#define FLS_GET_JOB_RESULT_API         (STD_ON)</L-1>
                        </P>
                      </ANNOTATION-TEXT>
                    </ANNOTATION>
                  </ANNOTATIONS>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsGeneral/FlsVersionInfoApi</DEFINITION-REF>
                  <ANNOTATIONS>
                    <ANNOTATION>
                      <LABEL>
                        <L-4 L="FOR-ALL">Annotation</L-4>
                      </LABEL>
                      <ANNOTATION-ORIGIN>DV:User</ANNOTATION-ORIGIN>
                      <ANNOTATION-TEXT>
                        <P>
                          <L-1 L="FOR-ALL">PLAIN_TEXT</L-1>
                        </P>
                        <P>
                          <L-1 L="FOR-ALL">ryan:&#13;
The generated code has a problem, that will report function undefined symbol.&#13;
&#13;
example:&#13;
#define FLS_GET_JOB_RESULT_API(STD_ON)&#13;
&#13;
need change:&#13;
#define FLS_GET_JOB_RESULT_API         (STD_ON)</L-1>
                        </P>
                      </ANNOTATION-TEXT>
                    </ANNOTATION>
                  </ANNOTATIONS>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsGeneral/FlsWriteVerificationEnabled</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE UUID="742af027-2c80-45db-84a4-8641cbdba1f9">
              <SHORT-NAME>FlsPublishedInformation</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Fls/FlsPublishedInformation</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsPublishedInformation/FlsAcLocationErase</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsPublishedInformation/FlsAcLocationWrite</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsPublishedInformation/FlsAcSizeErase</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsPublishedInformation/FlsAcSizeWrite</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsPublishedInformation/FlsErasedValue</DEFINITION-REF>
                  <VALUE>255</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsPublishedInformation/FlsEraseTime</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsPublishedInformation/FlsExpectedHwId</DEFINITION-REF>
                  <VALUE/>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsPublishedInformation/FlsSpecifiedEraseCycles</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TI_DRA80X_J7/Fls/FlsPublishedInformation/FlsWriteTime</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
          </CONTAINERS>
        </ECUC-MODULE-CONFIGURATION-VALUES>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
