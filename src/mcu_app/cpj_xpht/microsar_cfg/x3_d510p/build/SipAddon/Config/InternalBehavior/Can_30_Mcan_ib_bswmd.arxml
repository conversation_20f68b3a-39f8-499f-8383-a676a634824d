<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
      <SHORT-NAME>MICROSAR</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="44689d03-e8ed-4678-b2d0-d5ce51c8c535">
          <SHORT-NAME>Can_Mpc5700Mcan_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="ef1fccd3-23a4-4325-a8b0-cdea4c9e1eeb">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="6bf83f4c-9ccc-4b17-8e15-f09e3e0d0649">
                  <SHORT-NAME>Can</SHORT-NAME>
                  <PROVIDED-ENTRYS>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/Can_Mpc5700Mcan_ib_bswmd/BswModuleDescriptions/Can_30_Mcan_MainFunction_BusOff</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/Can_Mpc5700Mcan_ib_bswmd/BswModuleDescriptions/Can_30_Mcan_MainFunction_Wakeup</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/Can_Mpc5700Mcan_ib_bswmd/BswModuleDescriptions/Can_30_Mcan_MainFunction_Mode</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/Can_Mpc5700Mcan_ib_bswmd/BswModuleDescriptions/Can_30_Mcan_MainFunction_Read</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/Can_Mpc5700Mcan_ib_bswmd/BswModuleDescriptions/Can_30_Mcan_MainFunction_Write</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  </PROVIDED-ENTRYS>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="c833a392-e489-4321-ad03-fa11ab440733">
                      <SHORT-NAME>Can_Can_Mpc5700McanBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="3c354119-442d-4689-9605-be8a730b90dd">
                          <SHORT-NAME>CAN_30_MCAN_EXCLUSIVE_AREA_0</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="b0004f51-2df6-450f-91a9-2bd7ce3beeae">
                          <SHORT-NAME>CAN_30_MCAN_EXCLUSIVE_AREA_1</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="5d4b27a4-71ca-4b37-8829-e11c08a44200">
                          <SHORT-NAME>CAN_30_MCAN_EXCLUSIVE_AREA_2</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="1f46cc9b-632a-4cda-888b-456103bff23f">
                          <SHORT-NAME>CAN_30_MCAN_EXCLUSIVE_AREA_3</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="a4223775-db11-48cd-a66d-a8271dbc2e10">
                          <SHORT-NAME>CAN_30_MCAN_EXCLUSIVE_AREA_4</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="d867bf57-0356-4a09-9adb-33558272a468">
                          <SHORT-NAME>CAN_30_MCAN_EXCLUSIVE_AREA_5</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="f04101f0-6f57-47fd-aa0f-9104d03db4c4">
                          <SHORT-NAME>CAN_30_MCAN_EXCLUSIVE_AREA_6</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="c4cc6cc6-4fb2-495f-bd5e-0f29d8fe83b0">
                          <SHORT-NAME>CAN_30_MCAN_EXCLUSIVE_AREA_7</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="55788b2e-fd18-4780-9516-7f58f98ef708">
                          <SHORT-NAME>Can_30_Mcan_MainFunction_BusOff</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/Can_Mpc5700Mcan_ib_bswmd/BswModuleDescriptions/Can_30_Mcan_MainFunction_BusOff</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                        <BSW-SCHEDULABLE-ENTITY UUID="19cd0bcf-a8b5-4c6e-96a2-9d43dd0abd63">
                          <SHORT-NAME>Can_30_Mcan_MainFunction_Wakeup</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/Can_Mpc5700Mcan_ib_bswmd/BswModuleDescriptions/Can_30_Mcan_MainFunction_Wakeup</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                        <BSW-SCHEDULABLE-ENTITY UUID="fc26b1dc-8545-4740-bd34-168437ec6f52">
                          <SHORT-NAME>Can_30_Mcan_MainFunction_Mode</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/Can_Mpc5700Mcan_ib_bswmd/BswModuleDescriptions/Can_30_Mcan_MainFunction_Mode</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                        <BSW-SCHEDULABLE-ENTITY UUID="bf03c775-3abb-48f3-95d9-7772a782a153">
                          <SHORT-NAME>Can_30_Mcan_MainFunction_Read</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/Can_Mpc5700Mcan_ib_bswmd/BswModuleDescriptions/Can_30_Mcan_MainFunction_Read</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                        <BSW-SCHEDULABLE-ENTITY UUID="d7cbad10-02e6-44e8-86a3-5ffbfb1b453f">
                          <SHORT-NAME>Can_30_Mcan_MainFunction_Write</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/Can_Mpc5700Mcan_ib_bswmd/BswModuleDescriptions/Can_30_Mcan_MainFunction_Write</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="fc63befb-253e-44c8-acf1-da94920b3c29">
                          <SHORT-NAME>Can_30_Mcan_MainFunction_BusOffTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/Can_Mpc5700Mcan_ib_bswmd/BswModuleDescriptions/Can/Can_Can_Mpc5700McanBehavior/Can_30_Mcan_MainFunction_BusOff</STARTS-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </BSW-TIMING-EVENT>
                        <BSW-TIMING-EVENT UUID="67141c8c-c214-464c-ac8c-33ea7edb894f">
                          <SHORT-NAME>Can_30_Mcan_MainFunction_WakeupTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/Can_Mpc5700Mcan_ib_bswmd/BswModuleDescriptions/Can/Can_Can_Mpc5700McanBehavior/Can_30_Mcan_MainFunction_Wakeup</STARTS-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </BSW-TIMING-EVENT>
                        <BSW-TIMING-EVENT UUID="9f9518fe-f18c-49a0-bd57-754f469de997">
                          <SHORT-NAME>Can_30_Mcan_MainFunction_ModeTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/Can_Mpc5700Mcan_ib_bswmd/BswModuleDescriptions/Can/Can_Can_Mpc5700McanBehavior/Can_30_Mcan_MainFunction_Mode</STARTS-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </BSW-TIMING-EVENT>
                        <BSW-TIMING-EVENT UUID="2bc2db84-e587-4551-bc9b-6920b0530327">
                          <SHORT-NAME>Can_30_Mcan_MainFunction_ReadTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/Can_Mpc5700Mcan_ib_bswmd/BswModuleDescriptions/Can/Can_Can_Mpc5700McanBehavior/Can_30_Mcan_MainFunction_Read</STARTS-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </BSW-TIMING-EVENT>
                        <BSW-TIMING-EVENT UUID="976bc549-6ca5-4a1d-bae6-f14b9d9ab23e">
                          <SHORT-NAME>Can_30_Mcan_MainFunction_WriteTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/Can_Mpc5700Mcan_ib_bswmd/BswModuleDescriptions/Can/Can_Can_Mpc5700McanBehavior/Can_30_Mcan_MainFunction_Write</STARTS-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="b116c28b-28a4-42cd-b1d5-a94b303ca0d2">
                  <SHORT-NAME>Can_30_Mcan_MainFunction_BusOff</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
                <BSW-MODULE-ENTRY UUID="2370b71a-479e-4eb0-bcf6-d4f21a50e695">
                  <SHORT-NAME>Can_30_Mcan_MainFunction_Wakeup</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
                <BSW-MODULE-ENTRY UUID="b52f23f7-1655-43c7-b6a4-ab3a0a9f904e">
                  <SHORT-NAME>Can_30_Mcan_MainFunction_Mode</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
                <BSW-MODULE-ENTRY UUID="0a36638d-51cb-4110-a597-4426e9c78f81">
                  <SHORT-NAME>Can_30_Mcan_MainFunction_Read</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
                <BSW-MODULE-ENTRY UUID="33aa3ee6-2078-4957-9bb7-2cf2bd665b9c">
                  <SHORT-NAME>Can_30_Mcan_MainFunction_Write</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
