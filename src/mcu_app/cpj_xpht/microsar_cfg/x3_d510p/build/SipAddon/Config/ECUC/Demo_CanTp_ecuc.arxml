<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>ActiveEcuC</SHORT-NAME>
      <ELEMENTS>
        <ECUC-MODULE-CONFIGURATION-VALUES UUID="2a0ae53c-0a2f-4151-9a3b-e89744bc9244">
          <SHORT-NAME>CanTp</SHORT-NAME>
          <DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/CanTp</DEFINITION-REF>
          <IMPLEMENTATION-CONFIG-VARIANT>VARIANT-PRE-COMPILE</IMPLEMENTATION-CONFIG-VARIANT>
          <MODULE-DESCRIPTION-REF DEST="BSW-IMPLEMENTATION">/MICROSAR/CanTp_Impl</MODULE-DESCRIPTION-REF>
          <CONTAINERS>
            <ECUC-CONTAINER-VALUE UUID="c4fd0164-ab68-402b-b06f-8c0d1aaf4589">
              <SHORT-NAME>CanTpGeneral</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/CanTp/CanTpGeneral</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpChangeParameterApi</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpDevErrorDetect</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpPaddingByte</DEFINITION-REF>
                  <VALUE>204</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpReadParameterApi</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpVersionInfoApi</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpPaddingActive</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpHavePaddingByte</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpNumRxChannels</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpNumTxChannels</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpEnableSplitMainFunction</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpSupportStandardAddressing</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpSupportExtendedAddressing</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpSupportMixed11Addressing</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpSupportMixed29Addressing</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpSupportNormalFixedAddressing</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpOnlyNotifyInformedAppl</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpRc</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpTc</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpSafeBswChecks</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpEnableConstantBS</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpEnableTransmitQueue</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpUseOnlyFirstFc</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/CanTp/CanTpGeneral/CanTpEnableSynchronousTransmit</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE UUID="60d0110c-9e09-4083-9408-a5d452b624fb">
              <SHORT-NAME>CanTpConfig</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/CanTp/CanTpConfig</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpMainFunctionPeriod</DEFINITION-REF>
                  <VALUE>0.005</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE UUID="cfde26ec-5bf2-482e-a6c1-9d64af1ad301">
                  <SHORT-NAME>TPCH_51bcbe10</SHORT-NAME>
                  <LONG-NAME>
                    <L-4 L="FOR-ALL">TPCH</L-4>
                  </LONG-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpChannelMode</DEFINITION-REF>
                      <VALUE>CANTP_MODE_FULL_DUPLEX</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE UUID="6efd0066-b8ab-4726-b149-8ebadcea1a58">
                      <SHORT-NAME>CanTpRxNSdu_e5c8f1da</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpRxAddressingFormat</DEFINITION-REF>
                          <VALUE>CANTP_STANDARD</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpRxDl</DEFINITION-REF>
                          <VALUE>8</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpRxTaType</DEFINITION-REF>
                          <VALUE>CANTP_FUNCTIONAL</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpNbr</DEFINITION-REF>
                          <VALUE>0.1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpRxNSduId</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpRxPaddingActivation</DEFINITION-REF>
                          <VALUE>CANTP_ON</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpRxNSduRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/EcuC/EcucPduCollection/Func_DiagReq_oCAN00_44ede068_Rx</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE UUID="e0650c56-2c21-43e8-b7d6-6281b28d32ab">
                          <SHORT-NAME>CanTpRxNPdu_89cf1c15</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpRxNPdu</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpRxNPdu/CanTpRxNPduId</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpRxNPdu/CanTpRxNPduRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/EcuC/EcucPduCollection/Func_DiagReq_Tp_oCAN00_1be84631_Rx</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="d9c87dfd-3a2b-4a88-a40c-ac8ab1332863">
                  <SHORT-NAME>TPCH_0e69353a</SHORT-NAME>
                  <LONG-NAME>
                    <L-4 L="FOR-ALL">TPCH</L-4>
                  </LONG-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpChannelMode</DEFINITION-REF>
                      <VALUE>CANTP_MODE_FULL_DUPLEX</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE UUID="942d02b9-a0ac-43ef-ae57-4f2450caf61f">
                      <SHORT-NAME>CanTpRxNSdu_ae59ea06</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpRxAddressingFormat</DEFINITION-REF>
                          <VALUE>CANTP_STANDARD</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpRxDl</DEFINITION-REF>
                          <VALUE>8</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpRxTaType</DEFINITION-REF>
                          <VALUE>CANTP_PHYSICAL</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpNbr</DEFINITION-REF>
                          <VALUE>0.1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpRxNSduId</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpRxPaddingActivation</DEFINITION-REF>
                          <VALUE>CANTP_ON</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpBs</DEFINITION-REF>
                          <VALUE>8</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpNar</DEFINITION-REF>
                          <VALUE>0.1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpNcr</DEFINITION-REF>
                          <VALUE>0.5</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpRxWftMax</DEFINITION-REF>
                          <VALUE>15</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpSTmin</DEFINITION-REF>
                          <VALUE>0.02</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpRxNSduRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/EcuC/EcucPduCollection/IDC_DiagReq_oCAN00_2b203dab_Rx</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE UUID="5d894887-a5df-4efa-91e9-ce754ff34ee3">
                          <SHORT-NAME>CanTpRxNPdu_edd47b79</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpRxNPdu</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpRxNPdu/CanTpRxNPduId</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpRxNPdu/CanTpRxNPduRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/EcuC/EcucPduCollection/IDC_DiagReq_Tp_oCAN00_59426b18_Rx</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="820a573c-5f7f-4926-9587-fdae6a7436d0">
                          <SHORT-NAME>CanTpTxFcNPdu_edd47b79</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpTxFcNPdu</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpTxFcNPdu/CanTpTxFcNPduConfirmationPduId</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpRxNSdu/CanTpTxFcNPdu/CanTpTxFcNPduRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/EcuC/EcucPduCollection/IDC_DiagResp_Tp_oCAN00_8e9148c8_Tx</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE UUID="ff3dd143-4af0-4930-9236-2f81fafb105c">
                      <SHORT-NAME>CanTpTxNSdu_ae59ea06</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpTxNSdu</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpTxNSdu/CanTpTxAddressingFormat</DEFINITION-REF>
                          <VALUE>CANTP_STANDARD</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpTxNSdu/CanTpTxDl</DEFINITION-REF>
                          <VALUE>8</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpTxNSdu/CanTpTxTaType</DEFINITION-REF>
                          <VALUE>CANTP_PHYSICAL</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpTxNSdu/CanTpNas</DEFINITION-REF>
                          <VALUE>0.1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpTxNSdu/CanTpNcs</DEFINITION-REF>
                          <VALUE>0.2</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpTxNSdu/CanTpTc</DEFINITION-REF>
                          <VALUE>false</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpTxNSdu/CanTpTxNSduId</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpTxNSdu/CanTpTxPaddingActivation</DEFINITION-REF>
                          <VALUE>CANTP_ON</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpTxNSdu/CanTpNbs</DEFINITION-REF>
                          <VALUE>0.5</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpTxNSdu/CanTpTxNSduRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/EcuC/EcucPduCollection/IDC_DiagResp_oCAN00_54a191cf_Tx</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE UUID="e2a959f4-f8e4-44c4-895c-1ea5c843cffc">
                          <SHORT-NAME>CanTpRxFcNPdu_cf2eaf8b</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpTxNSdu/CanTpRxFcNPdu</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpTxNSdu/CanTpRxFcNPdu/CanTpRxFcNPduId</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpTxNSdu/CanTpRxFcNPdu/CanTpRxFcNPduRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/EcuC/EcucPduCollection/IDC_DiagReq_Tp_oCAN00_59426b18_Rx</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE UUID="e6825488-b121-47a7-907d-ad33e6541ce6">
                          <SHORT-NAME>CanTpTxNPdu_cf2eaf8b</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpTxNSdu/CanTpTxNPdu</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpTxNSdu/CanTpTxNPdu/CanTpTxNPduConfirmationPduId</DEFINITION-REF>
                              <VALUE>0</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <REFERENCE-VALUES>
                            <ECUC-REFERENCE-VALUE>
                              <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/CanTp/CanTpConfig/CanTpChannel/CanTpTxNSdu/CanTpTxNPdu/CanTpTxNPduRef</DEFINITION-REF>
                              <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/EcuC/EcucPduCollection/IDC_DiagResp_Tp_oCAN00_8e9148c8_Tx</VALUE-REF>
                            </ECUC-REFERENCE-VALUE>
                          </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
          </CONTAINERS>
        </ECUC-MODULE-CONFIGURATION-VALUES>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
