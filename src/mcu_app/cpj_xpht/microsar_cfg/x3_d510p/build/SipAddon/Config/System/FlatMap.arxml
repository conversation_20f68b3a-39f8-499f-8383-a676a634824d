<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="93780ecd-facd-43b7-bf2b-320a8790e7b9">
      <SHORT-NAME>COM</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="66efa641-af45-4ddb-a6f8-98ee4955e992">
          <SHORT-NAME>VECTOR</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="2354afb5-1d72-42c1-8603-65656f5bae84">
              <SHORT-NAME>CFG</SHORT-NAME>
              <AR-PACKAGES>
                <AR-PACKAGE UUID="b5d721c9-7e76-46a5-b800-70669d7b38ae">
                  <SHORT-NAME>WORKFLOW</SHORT-NAME>
                  <AR-PACKAGES>
                    <AR-PACKAGE UUID="d414708d-160d-42a6-87c9-66cdc518f13e">
                      <SHORT-NAME>SYSDESC</SHORT-NAME>
                      <AR-PACKAGES>
                        <AR-PACKAGE UUID="8bd57256-bb19-42a6-a467-092d4aab0c59">
                          <SHORT-NAME>SYNC</SHORT-NAME>
                          <ELEMENTS>
                            <FLAT-MAP UUID="4e6071cd-373b-472d-90a8-e1e9e0c775ae">
                              <SHORT-NAME>FLATMAP</SHORT-NAME>
                              <ADMIN-DATA>
                                <SDGS>
                                  <SDG GID="DV:CfgNamedRefs">
                                    <SDG GID="DV:CfgFlattener">
                                      <SD GID="DV:CfgFlattenerVersion">1</SD>
                                    </SDG>
                                  </SDG>
                                </SDGS>
                              </ADMIN-DATA>
                              <INSTANCES>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="9f8da87c-55ec-4aff-baf4-fd4e84a9d3b9">
                                  <SHORT-NAME>APP</SHORT-NAME>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:Flattener</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/VehicleProject/System/EcuSwComposition</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/ECUCompositionTypes/ECU_Composition/APP</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                  <ECU-EXTRACT-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/APP</TARGET-REF>
                                  </ECU-EXTRACT-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="d1e51f06-0d2d-4d03-8e21-8305f0766218">
                                  <SHORT-NAME>BSW</SHORT-NAME>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:Flattener</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/VehicleProject/System/EcuSwComposition</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/ECUCompositionTypes/ECU_Composition/BSW</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                  <ECU-EXTRACT-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/BSW</TARGET-REF>
                                  </ECU-EXTRACT-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="a12f9b0c-6ec6-44d4-aa0c-8e9018cbd275">
                                  <SHORT-NAME>NVM_SWC</SHORT-NAME>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:Flattener</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/VehicleProject/System/EcuSwComposition</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/ECUCompositionTypes/ECU_Composition/NVM_SWC</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                  <ECU-EXTRACT-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</TARGET-REF>
                                  </ECU-EXTRACT-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="1f30aa28-5ddc-48f3-b85f-54ba7aa140b5">
                                  <SHORT-NAME>APP2</SHORT-NAME>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:Flattener</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/VehicleProject/System/EcuSwComposition</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/ECUCompositionTypes/ECU_Composition/APP2</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                  <ECU-EXTRACT-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/APP2</TARGET-REF>
                                  </ECU-EXTRACT-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="509cde28-bf5e-433c-91ca-2b96442bb3a8">
                                  <SHORT-NAME>APP3</SHORT-NAME>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:Flattener</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/VehicleProject/System/EcuSwComposition</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/ECUCompositionTypes/ECU_Composition/APP3</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                  <ECU-EXTRACT-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/APP3</TARGET-REF>
                                  </ECU-EXTRACT-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="c5175041-74e0-4f11-9131-296f98ef6001">
                                  <SHORT-NAME>EVM_SWC</SHORT-NAME>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:Flattener</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/VehicleProject/System/EcuSwComposition</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/ECUCompositionTypes/ECU_Composition/EVM_SWC</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                  <ECU-EXTRACT-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/EVM_SWC</TARGET-REF>
                                  </ECU-EXTRACT-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="c9e0fff0-964b-4a42-98a6-3539503c9181">
                                  <SHORT-NAME>APP_C1</SHORT-NAME>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:Flattener</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/VehicleProject/System/EcuSwComposition</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/ECUCompositionTypes/ECU_Composition/APP_C1</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                  <ECU-EXTRACT-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/APP_C1</TARGET-REF>
                                  </ECU-EXTRACT-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="6f24260b-3585-4c1d-a3bd-f3da695284e0">
                                  <SHORT-NAME>NVM_SWC_PDM_DEM_AdminData</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_DEM_AdminData</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_DEM_AdminData</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="d3a31ada-e0d0-43a3-8a34-8d16dc64aa44">
                                  <SHORT-NAME>NVM_SWC_PDM_DEM_AgingData</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_DEM_AgingData</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_DEM_AgingData</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="2ce006dc-ca3b-4622-999a-8316e1757ed3">
                                  <SHORT-NAME>NVM_SWC_PDM_DEM_PrimaryEntry_0</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_DEM_PrimaryEntry_0</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_DEM_PrimaryEntry_0</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="2b01f5cc-ae9b-4239-942f-6823f787e57c">
                                  <SHORT-NAME>NVM_SWC_PDM_DEM_PrimaryEntry_1</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_DEM_PrimaryEntry_1</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_DEM_PrimaryEntry_1</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="078002df-c1c2-4851-a880-d5ba5d6731cb">
                                  <SHORT-NAME>NVM_SWC_PDM_DEM_PrimaryEntry_2</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_DEM_PrimaryEntry_2</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_DEM_PrimaryEntry_2</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="ed1d39b1-d80a-4423-a324-92907f002122">
                                  <SHORT-NAME>NVM_SWC_PDM_DEM_PrimaryEntry_3</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_DEM_PrimaryEntry_3</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_DEM_PrimaryEntry_3</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="08707793-58a4-417b-ac3b-559cc536eaab">
                                  <SHORT-NAME>NVM_SWC_PDM_DEM_PrimaryEntry_4</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_DEM_PrimaryEntry_4</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_DEM_PrimaryEntry_4</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="1a416fcc-d416-4d48-8bc1-27d5c5c4084a">
                                  <SHORT-NAME>NVM_SWC_PDM_DEM_PrimaryEntry_5</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_DEM_PrimaryEntry_5</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_DEM_PrimaryEntry_5</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="ab82cd07-6204-451a-a773-069527307758">
                                  <SHORT-NAME>NVM_SWC_PDM_DEM_PrimaryEntry_6</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_DEM_PrimaryEntry_6</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_DEM_PrimaryEntry_6</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="869b57c0-4a30-46ea-8c05-9d7c033bad72">
                                  <SHORT-NAME>NVM_SWC_PDM_DEM_PrimaryEntry_7</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_DEM_PrimaryEntry_7</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_DEM_PrimaryEntry_7</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="75b4e619-3039-4b36-9006-edf60c6b8f50">
                                  <SHORT-NAME>NVM_SWC_PDM_DEM_PrimaryEntry_8</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_DEM_PrimaryEntry_8</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_DEM_PrimaryEntry_8</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="9ac6dbf3-4ac5-460c-9bb8-3358caadfc53">
                                  <SHORT-NAME>NVM_SWC_PDM_DEM_PrimaryEntry_9</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_DEM_PrimaryEntry_9</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_DEM_PrimaryEntry_9</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="e1440a95-0f10-4919-b4f1-ddbb380aa3ce">
                                  <SHORT-NAME>NVM_SWC_PDM_DEM_StatusData</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_DEM_StatusData</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_DEM_StatusData</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="9df4f3b8-ca86-4546-bbf1-8f47dc3e23ac">
                                  <SHORT-NAME>NVM_SWC_PDM_DemHistoryStatus</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_DemHistoryStatus</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_DemHistoryStatus</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="395c0593-2170-497f-b713-bf1053065f97">
                                  <SHORT-NAME>NVM_SWC_PDM_ECUCalibrationDataVersionNumber</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ECUCalibrationDataVersionNumber</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ECUCalibrationDataVersionNumber</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="3e1961a2-debb-4cd7-9000-3aef68c213bd">
                                  <SHORT-NAME>NVM_SWC_PDM_ECUManufacturingDate</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ECUManufacturingDate</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ECUManufacturingDate</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="2bee31e2-0d44-4a10-a3c5-5d19e206d0fa">
                                  <SHORT-NAME>NVM_SWC_PDM_ECUSerialNumber</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ECUSerialNumber</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ECUSerialNumber</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="25959e98-3c0a-441b-9c52-9ff135087d63">
                                  <SHORT-NAME>NVM_SWC_PDM_VIN</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_VIN</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_VIN</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="9a23452a-dfec-46a2-b56e-edf88262548c">
                                  <SHORT-NAME>NVM_SWC_PDM_R52_CoreDump_1</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_R52_CoreDump_1</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_R52_CoreDump_1</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="cf20b8f4-a004-4e74-b78c-a7687e281815">
                                  <SHORT-NAME>NVM_SWC_PDM_R52_CoreDump_2</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_R52_CoreDump_2</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_R52_CoreDump_2</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="7d4c91f9-fc45-4017-8f69-f9454542dad6">
                                  <SHORT-NAME>NVM_SWC_PDM_R52_CoreDump_3</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_R52_CoreDump_3</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_R52_CoreDump_3</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="10af68f2-59f4-4bc3-b00c-f0ed508dcc41">
                                  <SHORT-NAME>NVM_SWC_PDM_R52_CoreDump_4</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_R52_CoreDump_4</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_R52_CoreDump_4</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="f7b24749-2bce-4503-9561-b79fea224d9b">
                                  <SHORT-NAME>NVM_SWC_PDM_R52_CoreDump_5</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_R52_CoreDump_5</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_R52_CoreDump_5</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="f3e7be51-1ad6-48fd-9002-65ff7dbaf5f0">
                                  <SHORT-NAME>AEB</SHORT-NAME>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:Flattener</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/VehicleProject/System/EcuSwComposition</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/ECUCompositionTypes/ECU_Composition/AEB</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                  <ECU-EXTRACT-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/AEB</TARGET-REF>
                                  </ECU-EXTRACT-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="49e4678a-9074-4524-9d2e-5bfd76545233">
                                  <SHORT-NAME>CSM</SHORT-NAME>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:Flattener</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/VehicleProject/System/EcuSwComposition</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/ECUCompositionTypes/ECU_Composition/CSM</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                  <ECU-EXTRACT-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/CSM</TARGET-REF>
                                  </ECU-EXTRACT-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="7be523eb-c806-4f55-99d9-38299b2bf184">
                                  <SHORT-NAME>Monitor</SHORT-NAME>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:Flattener</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/VehicleProject/System/EcuSwComposition</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/ECUCompositionTypes/ECU_Composition/Monitor</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                  <ECU-EXTRACT-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/Monitor</TARGET-REF>
                                  </ECU-EXTRACT-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="ef7b7585-4c14-42dd-9f21-3544333d25fa">
                                  <SHORT-NAME>PECP_IN</SHORT-NAME>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:Flattener</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/VehicleProject/System/EcuSwComposition</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/ECUCompositionTypes/ECU_Composition/PECP_IN</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                  <ECU-EXTRACT-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/PECP_IN</TARGET-REF>
                                  </ECU-EXTRACT-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="b3cfc43d-3f5f-40e7-b4b8-4a6f3309a9e4">
                                  <SHORT-NAME>VehCtrl</SHORT-NAME>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:Flattener</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/VehicleProject/System/EcuSwComposition</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/ECUCompositionTypes/ECU_Composition/VehCtrl</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                  <ECU-EXTRACT-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/VehCtrl</TARGET-REF>
                                  </ECU-EXTRACT-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="8276d375-e692-4103-90bd-9695e51876a7">
                                  <SHORT-NAME>VehInAdpr</SHORT-NAME>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:Flattener</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/VehicleProject/System/EcuSwComposition</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/ECUCompositionTypes/ECU_Composition/VehInAdpr</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                  <ECU-EXTRACT-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/VehInAdpr</TARGET-REF>
                                  </ECU-EXTRACT-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="0e69cf5b-d1d0-46c3-afae-6c47b5518ecf">
                                  <SHORT-NAME>VehOutAdpr</SHORT-NAME>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:Flattener</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/VehicleProject/System/EcuSwComposition</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/ECUCompositionTypes/ECU_Composition/VehOutAdpr</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                  <ECU-EXTRACT-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/VehOutAdpr</TARGET-REF>
                                  </ECU-EXTRACT-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="f45ba528-6a37-4f4c-8fea-fe425bc9e683">
                                  <SHORT-NAME>NET</SHORT-NAME>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:Flattener</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/VehicleProject/System/EcuSwComposition</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/ECUCompositionTypes/ECU_Composition/NET</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                  <ECU-EXTRACT-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NET</TARGET-REF>
                                  </ECU-EXTRACT-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="417f2c47-fc8c-438d-a19f-c7690d69c265">
                                  <SHORT-NAME>IpcAdpr</SHORT-NAME>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:Flattener</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/VehicleProject/System/EcuSwComposition</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/ECUCompositionTypes/ECU_Composition/IpcAdpr</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                  <ECU-EXTRACT-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/IpcAdpr</TARGET-REF>
                                  </ECU-EXTRACT-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="e1f54702-d2e8-475e-b7f4-3b5af75f584e">
                                  <SHORT-NAME>NVM_SWC_PDM_FingerPrint</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_FingerPrint</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_FingerPrint</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="776ede35-8b3f-4828-8fb0-32a63a8ebd38">
                                  <SHORT-NAME>NVM_SWC_PDM_PublicKeyStatus</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_PublicKeyStatus</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_PublicKeyStatus</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="6b4c90aa-13a5-46f3-ab7b-1ee17ff539c6">
                                  <SHORT-NAME>NVM_SWC_PDM_PublicKey</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_PublicKey</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_PublicKey</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="1efc900f-8c21-415c-b9fd-48e90941b27d">
                                  <SHORT-NAME>NVM_SWC_PDM_InternalCoding</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_InternalCoding</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_InternalCoding</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="34ea5c73-02bf-4a0c-bbfe-c8c15cc8a33d">
                                  <SHORT-NAME>NVM_SWC_PDM_E2eEnable</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_E2eEnable</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_E2eEnable</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="d7f0fd7c-7c55-40b3-b76f-755100af65f3">
                                  <SHORT-NAME>NVM_SWC_PDM_FunctionConfiguration</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_FunctionConfiguration</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_FunctionConfiguration</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="4bf151b4-6ab2-4d70-a4d8-e271ebd7e6bc">
                                  <SHORT-NAME>NVM_SWC_PDM_ECUProgrammingDate</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ECUProgrammingDate</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ECUProgrammingDate</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="cc1e6f4f-fa1d-48be-812d-51245f3605c2">
                                  <SHORT-NAME>NVM_SWC_PDM_RepairShopFingerprint</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_RepairShopFingerprint</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_RepairShopFingerprint</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="80afa227-ea53-434a-a1ca-d957bdf3fd8e">
                                  <SHORT-NAME>NVM_SWC_PDM_VehicleManufacturerCalibrationDataVersionNumber</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_VehicleManufacturerCalibrationDataVersionNumber</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_VehicleManufacturerCalibrationDataVersionNumber</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="40430181-bf8a-4c56-b436-f583fa192db0">
                                  <SHORT-NAME>NVM_SWC_PDM_RefreshInforField</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_RefreshInforField</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_RefreshInforField</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="65e00877-5745-45c1-a09c-82328599fdc1">
                                  <SHORT-NAME>NVM_SWC_PDM_ScalStaticCalibrationDataSigTest_Scal</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ScalStaticCalibrationDataSigTest_Scal</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ScalStaticCalibrationDataSigTest_Scal</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="7687b61a-d1bd-4811-b368-e1efa02208c4">
                                  <SHORT-NAME>NVM_SWC_PDM_ScalStaticCalibrationDataSigTest_Calcon</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ScalStaticCalibrationDataSigTest_Calcon</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ScalStaticCalibrationDataSigTest_Calcon</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="8d490d26-03f7-44b1-8ae6-a2efeeb01104">
                                  <SHORT-NAME>NVM_SWC_PDM_CalconInitialCalibrationStatusHistory</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_CalconInitialCalibrationStatusHistory</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_CalconInitialCalibrationStatusHistory</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="b5581c9c-8616-456f-8f60-1ed6361bebf3">
                                  <SHORT-NAME>NVM_SWC_PDM_EscimoPersistentData</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_EscimoPersistentData</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_EscimoPersistentData</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="95b5729c-b6da-4c4f-a1a5-e4aed0c34c7d">
                                  <SHORT-NAME>NVM_SWC_PDM_ImagerCalRoiLeft</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ImagerCalRoiLeft</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ImagerCalRoiLeft</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="e9aef6e8-0a68-42fb-b6ac-c4ba49058962">
                                  <SHORT-NAME>NVM_SWC_PDM_ImagerCalRoiRight</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ImagerCalRoiRight</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ImagerCalRoiRight</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="e68b4c30-8717-45dc-b543-a088b66d3c9b">
                                  <SHORT-NAME>NVM_SWC_PDM_ImagerCalRoiRightSimulation</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ImagerCalRoiRightSimulation</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ImagerCalRoiRightSimulation</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="069f2039-abe6-4b65-8b0e-b97f140be493">
                                  <SHORT-NAME>NVM_SWC_PDM_ImagerCalRoiLeftSimulation</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ImagerCalRoiLeftSimulation</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ImagerCalRoiLeftSimulation</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="c8594239-97a8-4e25-81d6-275ce98adfca">
                                  <SHORT-NAME>NVM_SWC_PDM_MocaInterfaceSteeringAngleOffset</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_MocaInterfaceSteeringAngleOffset</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_MocaInterfaceSteeringAngleOffset</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="990a924c-7290-43c2-a240-945d5750eb48">
                                  <SHORT-NAME>NVM_SWC_PDM_MocaInterfaceYawRateOffset</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_MocaInterfaceYawRateOffset</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_MocaInterfaceYawRateOffset</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="37f03c9f-fdd3-4c23-b2d7-9a788cc7c65d">
                                  <SHORT-NAME>NVM_SWC_PDM_MocaInterfaceYawRateDelay</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_MocaInterfaceYawRateDelay</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_MocaInterfaceYawRateDelay</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="3eb99e92-9ef2-4313-bc2f-bbf02fbd78f3">
                                  <SHORT-NAME>NVM_SWC_PDM_MocaInterfaceAbsoluteVelocityScale</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_MocaInterfaceAbsoluteVelocityScale</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_MocaInterfaceAbsoluteVelocityScale</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="87c16e18-d00e-4661-b013-55d7d2f72fe9">
                                  <SHORT-NAME>NVM_SWC_PDM_MocaInterfaceVelocityDelay</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_MocaInterfaceVelocityDelay</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_MocaInterfaceVelocityDelay</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="da0c5926-32cb-4720-a559-769dccd9e06c">
                                  <SHORT-NAME>NVM_SWC_PDM_MocaInterfaceRelativeVelocityScale</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_MocaInterfaceRelativeVelocityScale</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_MocaInterfaceRelativeVelocityScale</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="7e9f1245-ec42-4cfa-8c54-3704e769ccc1">
                                  <SHORT-NAME>NVM_SWC_PDM_MocaInterfaceLateralAccelerationOffset</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_MocaInterfaceLateralAccelerationOffset</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_MocaInterfaceLateralAccelerationOffset</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="4d9687d8-8154-4085-a45d-c9281a33c822">
                                  <SHORT-NAME>NVM_SWC_PDM_MocaInterfaceLongitudinalAccelerationOffset</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_MocaInterfaceLongitudinalAccelerationOffset</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_MocaInterfaceLongitudinalAccelerationOffset</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="0724fce8-9f20-470f-b625-b766dac773b4">
                                  <SHORT-NAME>NVM_SWC_PDM_OcalInitialCalibration_Ocal</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_OcalInitialCalibration_Ocal</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_OcalInitialCalibration_Ocal</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="4d520318-4719-4d02-aaee-a4020f917ca5">
                                  <SHORT-NAME>NVM_SWC_PDM_OcalInitialCalibration_Scal</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_OcalInitialCalibration_Scal</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_OcalInitialCalibration_Scal</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="3bd27140-ab9d-4d45-b146-3f2ec992b976">
                                  <SHORT-NAME>NVM_SWC_PDM_OcalInitialCalibration_Calcon</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_OcalInitialCalibration_Calcon</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_OcalInitialCalibration_Calcon</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="0bb4fc43-1c29-4eab-8147-297e3ccd0100">
                                  <SHORT-NAME>NVM_SWC_PDM_OcalDifferencesToInitialCalibration_Ocal</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_OcalDifferencesToInitialCalibration_Ocal</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_OcalDifferencesToInitialCalibration_Ocal</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="6efe3469-a94f-4396-a795-cecccb43802c">
                                  <SHORT-NAME>NVM_SWC_PDM_OcalDifferencesToInitialCalibration_Calcon</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_OcalDifferencesToInitialCalibration_Calcon</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_OcalDifferencesToInitialCalibration_Calcon</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="0927c60b-de21-4c57-9461-8d2f352775a0">
                                  <SHORT-NAME>NVM_SWC_PDM_ScePermBlindCounter</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ScePermBlindCounter</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ScePermBlindCounter</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="f74500e1-7075-44a8-a226-7c32a32b133d">
                                  <SHORT-NAME>NVM_SWC_PDM_ScePermNoBlindCounter</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ScePermNoBlindCounter</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ScePermNoBlindCounter</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="ba9529f0-463f-4ba3-969c-84ebbd6afe9c">
                                  <SHORT-NAME>NVM_SWC_PDM_WddStatus</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_WddStatus</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_WddStatus</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="50f4808e-0340-49b9-a309-866a05d03461">
                                  <SHORT-NAME>NVM_SWC_PDM_Uds0x27AttemptCounter</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_Uds0x27AttemptCounter</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_Uds0x27AttemptCounter</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="bbd0aefd-a7fa-43d3-b04b-20971397060f">
                                  <SHORT-NAME>NVM_SWC_PDM_AdasFunctionConfig</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_AdasFunctionConfig</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_AdasFunctionConfig</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="42608ca1-0799-4a98-8577-fa45305ea7fb">
                                  <SHORT-NAME>NVM_SWC_PDM_SvScalCalibrationStatusHistory</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_SvScalCalibrationStatusHistory</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_SvScalCalibrationStatusHistory</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="23911a9b-4bd5-47c0-ab23-aa0c3be503fd">
                                  <SHORT-NAME>NVM_SWC_PDM_SvScalFrontLeftExtrinsicData</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_SvScalFrontLeftExtrinsicData</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_SvScalFrontLeftExtrinsicData</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="47442527-76c1-4938-9cff-b4ac538e579b">
                                  <SHORT-NAME>NVM_SWC_PDM_SvScalFrontRightExtrinsicData</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_SvScalFrontRightExtrinsicData</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_SvScalFrontRightExtrinsicData</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="965716d7-0fa9-48eb-8dc4-6d822a6d040d">
                                  <SHORT-NAME>NVM_SWC_PDM_SvScalRearLeftExtrinsicData</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_SvScalRearLeftExtrinsicData</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_SvScalRearLeftExtrinsicData</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="93fcba64-ceed-400c-b0ce-2eaeafc78180">
                                  <SHORT-NAME>NVM_SWC_PDM_SvScalRearRightExtrinsicData</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_SvScalRearRightExtrinsicData</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_SvScalRearRightExtrinsicData</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="38c0a820-1cc7-4c7c-94c6-93a8a794df3e">
                                  <SHORT-NAME>NVM_SWC_PDM_RvScalCalibrationStatusHistory</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_RvScalCalibrationStatusHistory</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_RvScalCalibrationStatusHistory</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="946c0bcd-c9b5-4417-8be2-f476c131f6a2">
                                  <SHORT-NAME>NVM_SWC_PDM_RvScalRearExtrinsicData</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_RvScalRearExtrinsicData</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_RvScalRearExtrinsicData</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="14628ba6-bb25-4269-8530-65db9de2395c">
                                  <SHORT-NAME>NVM_SWC_PDM_TvScalCalibrationStatusHistory</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_TvScalCalibrationStatusHistory</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_TvScalCalibrationStatusHistory</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="f4812d16-485b-40fc-99c4-a8ece76060f9">
                                  <SHORT-NAME>NVM_SWC_PDM_TvScalRearExtrinsicData</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_TvScalRearExtrinsicData</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_TvScalRearExtrinsicData</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="0e07d81b-bfb6-4110-a04c-18b8665805c4">
                                  <SHORT-NAME>NVM_SWC_PDM_TvScalLeftExtrinsicData</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_TvScalLeftExtrinsicData</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_TvScalLeftExtrinsicData</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="40fccbf1-0c4a-4c4d-be09-0bf6efa0bc49">
                                  <SHORT-NAME>NVM_SWC_PDM_TvScalFrontExtrinsicData</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_TvScalFrontExtrinsicData</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_TvScalFrontExtrinsicData</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="6b96f7c8-a864-4616-88d9-639c3924997d">
                                  <SHORT-NAME>NVM_SWC_PDM_TvScalRightExtrinsicData</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_TvScalRightExtrinsicData</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_TvScalRightExtrinsicData</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="a277113c-93a2-400f-9f5c-406d4cbd4cf7">
                                  <SHORT-NAME>NVM_SWC_PDM_ApplicationSoftwareFingerprint</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ApplicationSoftwareFingerprint</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ApplicationSoftwareFingerprint</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="b4d182a2-0fe7-44cb-b84b-969e3bef5db0">
                                  <SHORT-NAME>NVM_SWC_PDM_ProgrammingDate</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ProgrammingDate</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ProgrammingDate</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="d66d21b5-5879-4e84-b3fb-9d05d439e067">
                                  <SHORT-NAME>NVM_SWC_PDM_ReprogrammingAttemptCounter</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ReprogrammingAttemptCounter</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ReprogrammingAttemptCounter</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="e4be6a6e-8e66-480a-a1b2-0eb83d5aeb66">
                                  <SHORT-NAME>NVM_SWC_PDM_ReprogrammingCounter</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ReprogrammingCounter</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ReprogrammingCounter</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="4e403f97-b4d5-4361-9bb1-69e73917f3f1">
                                  <SHORT-NAME>NVM_SWC_PDM_ECURunningInfo</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ECURunningInfo</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ECURunningInfo</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="1cd51fb7-b666-4c88-8d9c-02bbfc8360dd">
                                  <SHORT-NAME>NVM_SWC_PDM_StorageCounter</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_StorageCounter</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_StorageCounter</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="79e88732-fe52-48b3-87f9-246ae668d726">
                                  <SHORT-NAME>NVM_SWC_PDM_CalibrationResultDrivingFunction</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_CalibrationResultDrivingFunction</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_CalibrationResultDrivingFunction</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="8f2a716c-c8d0-466a-84ed-594775a4ea90">
                                  <SHORT-NAME>NVM_SWC_PDM_AVMCalibrationDataFactory</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_AVMCalibrationDataFactory</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_AVMCalibrationDataFactory</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="ee7baa49-bcd0-45b1-b155-c78290dd5170">
                                  <SHORT-NAME>NVM_SWC_PDM_VehicleMode</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_VehicleMode</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_VehicleMode</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="d3732fdf-7696-426b-8ffc-74c32e656e2c">
                                  <SHORT-NAME>NVM_SWC_PDM_SQIThreshold</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_SQIThreshold</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_SQIThreshold</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="996b4d4c-b95d-4a6a-ba0e-ea2b676a3ace">
                                  <SHORT-NAME>NVM_SWC_PDM_CarFeatureCode</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_CarFeatureCode</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_CarFeatureCode</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="2527697f-4775-44cd-9cc1-57ddad69f21a">
                                  <SHORT-NAME>NVM_SWC_PDM_SecOCKeyRaw</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_SecOCKeyRaw</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_SecOCKeyRaw</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="95def1ed-7278-4294-a587-ddde685a4bb8">
                                  <SHORT-NAME>NVM_SWC_PDM_SecOCFunctionConfig</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_SecOCFunctionConfig</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_SecOCFunctionConfig</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="0a4dc217-3866-48b6-9f7d-bc786c3aff32">
                                  <SHORT-NAME>NVM_SWC_PDM_ESKCode</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ESKCode</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ESKCode</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="22d125a2-0b95-46d3-9da4-a3e469481053">
                                  <SHORT-NAME>NVM_SWC_PDM_WheelHeaderHeight</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_WheelHeaderHeight</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_WheelHeaderHeight</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="ce8fa588-73ec-4fe6-a788-9e72d19edd4e">
                                  <SHORT-NAME>NVM_SWC_PDM_ServiceCalibrationResultDrivingFunction</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ServiceCalibrationResultDrivingFunction</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ServiceCalibrationResultDrivingFunction</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="9e9a5aa6-99fe-4772-a16e-aa7f3ec8ccc4">
                                  <SHORT-NAME>NVM_SWC_PDM_AVMCalibrationDataService</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_AVMCalibrationDataService</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_AVMCalibrationDataService</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="8e43ea3f-e306-4dbe-8103-4b0c3e8646bd">
                                  <SHORT-NAME>NVM_SWC_PDM_VehicleInfo</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_VehicleInfo</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_VehicleInfo</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="a210cb2f-23e2-40e8-977a-38d228d36106">
                                  <SHORT-NAME>NVM_SWC_PDM_SecOCKeyRawCntTime</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_SecOCKeyRawCntTime</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_SecOCKeyRawCntTime</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="2cac3195-1fb1-4124-9d8b-e669d444e373">
                                  <SHORT-NAME>NVM_SWC_PDM_ESKCntTime</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ESKCntTime</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ESKCntTime</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="584e3063-6ec0-49e6-8938-06babcd97836">
                                  <SHORT-NAME>NVM_SWC_PDM_AIN</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_AIN</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_AIN</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="6f0768d0-6f6d-4c9e-82f0-cc9018771d26">
                                  <SHORT-NAME>NVM_SWC_PDM_EOLCalibrationRearLocalizationCamera</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_EOLCalibrationRearLocalizationCamera</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_EOLCalibrationRearLocalizationCamera</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="b28c8b7f-1dcd-447d-b484-f267204ff040">
                                  <SHORT-NAME>NVM_SWC_PDM_ServiceCalibrationRearLocalizationCamera</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ServiceCalibrationRearLocalizationCamera</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ServiceCalibrationRearLocalizationCamera</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="bea0d21c-8b7f-426c-9fbc-009f97d117a8">
                                  <SHORT-NAME>NVM_SWC_PDM_EOLCalibrationLandFlightCombination</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_EOLCalibrationLandFlightCombination</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_EOLCalibrationLandFlightCombination</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                                <FLAT-INSTANCE-DESCRIPTOR UUID="aeae03a2-f5ab-4540-b095-952b93117519">
                                  <SHORT-NAME>NVM_SWC_PDM_ServiceCalibrationLandFlightCombination</SHORT-NAME>
                                  <ADMIN-DATA>
                                    <SDGS>
                                      <SDG GID="DV:NvBlockNeeds">
                                        <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/NvM/NVM_SWC_PDM_ServiceCalibrationLandFlightCombination</SDX-REF>
                                      </SDG>
                                    </SDGS>
                                  </ADMIN-DATA>
                                  <ANNOTATIONS>
                                    <ANNOTATION>
                                      <ANNOTATION-ORIGIN>DV:NvBlockNeeds</ANNOTATION-ORIGIN>
                                    </ANNOTATION>
                                  </ANNOTATIONS>
                                  <UPSTREAM-REFERENCE-IREF>
                                    <CONTEXT-ELEMENT-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/SYSTEM/COMPOSITIONTYPE</CONTEXT-ELEMENT-REF>
                                    <CONTEXT-ELEMENT-REF DEST="SW-COMPONENT-PROTOTYPE">/COM/VECTOR/CFG/WORKFLOW/SYSDESC/SYNC/COMPOSITIONTYPE/NVM_SWC</CONTEXT-ELEMENT-REF>
                                    <TARGET-REF DEST="NV-BLOCK-DESCRIPTOR">/ComponentTypes/NVM_SWC/PDM_ServiceCalibrationLandFlightCombination</TARGET-REF>
                                  </UPSTREAM-REFERENCE-IREF>
                                </FLAT-INSTANCE-DESCRIPTOR>
                              </INSTANCES>
                            </FLAT-MAP>
                          </ELEMENTS>
                        </AR-PACKAGE>
                      </AR-PACKAGES>
                    </AR-PACKAGE>
                  </AR-PACKAGES>
                </AR-PACKAGE>
              </AR-PACKAGES>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
