<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
      <SHORT-NAME>MICROSAR</SHORT-NAME>
      <ELEMENTS>
        <BSW-IMPLEMENTATION UUID="3caf2919-5d52-423f-9faf-7b78a8fcdb88">
          <SHORT-NAME>CanIf_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>6.25.00</SW-VERSION>
          <USED-CODE-GENERATOR>cMSR</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
        </BSW-IMPLEMENTATION>
      </ELEMENTS>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="db03fda0-1b5b-43ef-95e3-4248dfc1297e">
          <SHORT-NAME>CanIf_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="eaf0d871-3934-4f00-ab57-044d4fe8eb34">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="75298736-75c2-4296-850d-a2d7d9722e6b">
                  <SHORT-NAME>CanIf</SHORT-NAME>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="07422df8-f049-42e4-9cda-7db351d66e8e">
                      <SHORT-NAME>CanIf_Behavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="c2c3519d-f0db-466a-8048-21c24f0e8d74">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_0</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="55621f0e-df1b-47cf-beb7-99d297a40ccb">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_1</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="0fe026df-d6fc-45ee-a9de-e74515c680d4">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_2</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="3858bc95-a0c8-4bf3-a724-7b23ba69274d">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_3</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="b46fa3fe-9739-4de8-af13-b02c97764580">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_4</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="45a29a67-c9c7-46cb-976c-f66ed87e8a97">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_5</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="54d04747-a005-40fc-a015-c6027cc0b4f9">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_6</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="6054aaf1-1dcd-4f65-a5f2-2de4d8440d20">
                          <SHORT-NAME>CANIF_EXCLUSIVE_AREA_7</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="98bfce41-cf1a-4f0e-b285-6bf8cb4aed54">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
