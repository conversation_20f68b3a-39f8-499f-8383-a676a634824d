<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>ActiveEcuC</SHORT-NAME>
      <ELEMENTS>
        <ECUC-MODULE-CONFIGURATION-VALUES UUID="590bfb71-1d09-457d-9c4c-1399c3102d34">
          <SHORT-NAME>Cdd_Ipc</SHORT-NAME>
          <ADMIN-DATA>
            <SDGS>
              <SDG GID="DV:CfgPostBuild">
                <SD GID="DV:postBuildVariantSupport">false</SD>
              </SDG>
            </SDGS>
          </ADMIN-DATA>
          <DEFINITION-REF DEST="ECUC-MODULE-DEF">/TI_DRA80X_J7/Cdd_Ipc</DEFINITION-REF>
          <IMPLEMENTATION-CONFIG-VARIANT>VARIANT-PRE-COMPILE</IMPLEMENTATION-CONFIG-VARIANT>
          <MODULE-DESCRIPTION-REF DEST="BSW-IMPLEMENTATION">/TI_DRA80X_J7/Cdd_Ipc_Impl</MODULE-DESCRIPTION-REF>
          <CONTAINERS>
            <ECUC-CONTAINER-VALUE UUID="1f91decd-575e-4055-be38-c8a3c558b20e">
              <SHORT-NAME>CddIpcGeneral</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcGeneral</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcGeneral/CddDevErrorDetect</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcGeneral/CddIrqType</DEFINITION-REF>
                  <VALUE>CDD_IPC_ISR_CAT2</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcGeneral/NewMsgNtfyFunc</DEFINITION-REF>
                  <VALUE>Cdd_IpcNewMessageNotify</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcGeneral/NewControlMsgNtfyFunc</DEFINITION-REF>
                  <VALUE>Cdd_IpcNewCtrlMessageNotify</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcGeneral/CddIpcOSCounterId</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcGeneral/CddIpcDeviceVariant</DEFINITION-REF>
                  <VALUE>J721E</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcGeneral/CddVersionInfoApi</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcGeneral/CddDeinitApi</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcGeneral/CddIpcAnnounceApi</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcGeneral/CddRegisterReadBackApi</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcGeneral/CddIsInitDoneApi</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcGeneral/CddGetMsgMaxSizeApi</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
              <REFERENCE-VALUES>
                <ECUC-REFERENCE-VALUE>
                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcGeneral/CddIpcOsCounterRef</DEFINITION-REF>
                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Os/SystemTimer</VALUE-REF>
                </ECUC-REFERENCE-VALUE>
              </REFERENCE-VALUES>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE UUID="aba69235-753e-4287-8073-4960ed8618e8">
                  <SHORT-NAME>CddDemEventParameterRefs</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcGeneral/CddDemEventParameterRefs</DEFINITION-REF>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-SYMBOLIC-NAME-REFERENCE-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcGeneral/CddDemEventParameterRefs/CDD_IPC_E_HARDWARE_ERROR</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/Dem/DemConfigSet/DTC_0x000002</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE UUID="ebdda3ad-fec0-48a1-ba81-febc1eb646f0">
              <SHORT-NAME>CddIpcVirtIoConfig</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcVirtIoConfig</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcVirtIoConfig/VertIoRingAddr</DEFINITION-REF>
                  <VALUE>2852126720</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcVirtIoConfig/VertIoRingSize</DEFINITION-REF>
                  <VALUE>29360128</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcVirtIoConfig/VertIoObjSize</DEFINITION-REF>
                  <VALUE>4096</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE UUID="dd1544c1-edb9-4827-8dd5-f0eec8fb24f1">
              <SHORT-NAME>CddIpcProcIDs</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcProcIDs</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcProcIDs/OwnProcID</DEFINITION-REF>
                  <VALUE>CDD_IPC_CORE_MCU1_0</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
              </PARAMETER-VALUES>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE UUID="937bac3f-f8bb-4fbb-97b7-583a764a9048">
                  <SHORT-NAME>RemoteProcID</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcProcIDs/RemoteProcID</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcProcIDs/RemoteProcID/ProcID</DEFINITION-REF>
                      <VALUE>CDD_IPC_CORE_MPU1_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE UUID="f9eabb09-39a2-45b1-b15e-c605040cb562">
              <SHORT-NAME>CddIpcCommChannels</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels</DEFINITION-REF>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE UUID="9bc603e6-1bc7-4232-85c9-b5e5d8bd4be4">
                  <SHORT-NAME>CddIpcCommChs_MPU1_0_ch_0</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/CommId</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/LocalEp</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/RemoteEp</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/RemoteProcID</DEFINITION-REF>
                      <VALUE>CDD_IPC_CORE_MPU1_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/MaxNumMsgQueued</DEFINITION-REF>
                      <VALUE>256</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/MaxMsgSize</DEFINITION-REF>
                      <VALUE>512</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="1003de30-46e4-4c2c-a064-2dcf6d385881">
                  <SHORT-NAME>CddIpcCommChs_MPU1_0_ch_1</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/CommId</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/LocalEp</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/RemoteEp</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/RemoteProcID</DEFINITION-REF>
                      <VALUE>CDD_IPC_CORE_MPU1_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/MaxNumMsgQueued</DEFINITION-REF>
                      <VALUE>256</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/MaxMsgSize</DEFINITION-REF>
                      <VALUE>512</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="e480bb22-0dcf-45d4-b1f7-221c4fd7f952">
                  <SHORT-NAME>CddIpcCommChs_MPU1_0_ch_2</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/CommId</DEFINITION-REF>
                      <VALUE>2</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/LocalEp</DEFINITION-REF>
                      <VALUE>2</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/RemoteEp</DEFINITION-REF>
                      <VALUE>2</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/RemoteProcID</DEFINITION-REF>
                      <VALUE>CDD_IPC_CORE_MPU1_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/MaxNumMsgQueued</DEFINITION-REF>
                      <VALUE>256</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/MaxMsgSize</DEFINITION-REF>
                      <VALUE>512</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="3ca96acb-0cd0-4859-8d35-c64f338d526e">
                  <SHORT-NAME>CddIpcCommChs_MPU1_0_ch_3</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/CommId</DEFINITION-REF>
                      <VALUE>3</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/LocalEp</DEFINITION-REF>
                      <VALUE>3</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/RemoteEp</DEFINITION-REF>
                      <VALUE>3</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/RemoteProcID</DEFINITION-REF>
                      <VALUE>CDD_IPC_CORE_MPU1_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/MaxNumMsgQueued</DEFINITION-REF>
                      <VALUE>256</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/MaxMsgSize</DEFINITION-REF>
                      <VALUE>512</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="b056e490-e4b5-4a8e-a74a-2ca9db32fc20">
                  <SHORT-NAME>CddIpcCommChs_MPU1_0_ch_4</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/CommId</DEFINITION-REF>
                      <VALUE>4</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/LocalEp</DEFINITION-REF>
                      <VALUE>4</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/RemoteEp</DEFINITION-REF>
                      <VALUE>4</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/RemoteProcID</DEFINITION-REF>
                      <VALUE>CDD_IPC_CORE_MPU1_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/MaxNumMsgQueued</DEFINITION-REF>
                      <VALUE>256</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/MaxMsgSize</DEFINITION-REF>
                      <VALUE>512</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="57d5eb31-ecdc-4f1d-9279-e56290bc0884">
                  <SHORT-NAME>CddIpcCommChs_MPU1_0_ch_5</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/CommId</DEFINITION-REF>
                      <VALUE>5</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/LocalEp</DEFINITION-REF>
                      <VALUE>5</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/RemoteEp</DEFINITION-REF>
                      <VALUE>5</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/RemoteProcID</DEFINITION-REF>
                      <VALUE>CDD_IPC_CORE_MPU1_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/MaxNumMsgQueued</DEFINITION-REF>
                      <VALUE>256</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/MaxMsgSize</DEFINITION-REF>
                      <VALUE>512</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="7d280a35-90ad-4f4a-8f45-d7d5c2b729e0">
                  <SHORT-NAME>CddIpcCommChs_MPU1_0_ch_6</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/CommId</DEFINITION-REF>
                      <VALUE>6</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/LocalEp</DEFINITION-REF>
                      <VALUE>6</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/RemoteEp</DEFINITION-REF>
                      <VALUE>6</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/RemoteProcID</DEFINITION-REF>
                      <VALUE>CDD_IPC_CORE_MPU1_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/MaxNumMsgQueued</DEFINITION-REF>
                      <VALUE>256</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/MaxMsgSize</DEFINITION-REF>
                      <VALUE>512</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="349af272-bddd-4edf-8397-c94e41791e0f">
                  <SHORT-NAME>CddIpcCommChs_MPU1_0_ch_7</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/CommId</DEFINITION-REF>
                      <VALUE>7</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/LocalEp</DEFINITION-REF>
                      <VALUE>7</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/RemoteEp</DEFINITION-REF>
                      <VALUE>7</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/RemoteProcID</DEFINITION-REF>
                      <VALUE>CDD_IPC_CORE_MPU1_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/MaxNumMsgQueued</DEFINITION-REF>
                      <VALUE>256</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Cdd_Ipc/CddIpcCommChannels/CddIpcCommChs/MaxMsgSize</DEFINITION-REF>
                      <VALUE>512</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
          </CONTAINERS>
        </ECUC-MODULE-CONFIGURATION-VALUES>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
