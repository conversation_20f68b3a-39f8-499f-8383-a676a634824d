<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00049.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>ActiveEcuC</SHORT-NAME>
      <ELEMENTS>
        <ECUC-VALUE-COLLECTION>
          <SHORT-NAME>ActiveEcuC</SHORT-NAME>
          <ECUC-VALUES>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Can</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Port</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Dem</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/CRC_cdd</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/IIC_Cdd</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Spi</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Dio</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Mcu</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Gpt</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Cddiic</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
            <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
              <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/ActiveEcuC/Cddths</ECUC-MODULE-CONFIGURATION-VALUES-REF>
            </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
          </ECUC-VALUES>
        </ECUC-VALUE-COLLECTION>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
