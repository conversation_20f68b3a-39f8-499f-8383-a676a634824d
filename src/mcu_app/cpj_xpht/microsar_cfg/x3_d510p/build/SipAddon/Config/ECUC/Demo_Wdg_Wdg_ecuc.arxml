<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>ActiveEcuC</SHORT-NAME>
      <ELEMENTS>
        <ECUC-MODULE-CONFIGURATION-VALUES UUID="4168b759-90e0-4bd6-8cbb-82ecde4997e8">
          <SHORT-NAME>Wdg</SHORT-NAME>
          <ADMIN-DATA>
            <SDGS>
              <SDG GID="DV:CfgPostBuild">
                <SD GID="DV:postBuildVariantSupport">false</SD>
              </SDG>
            </SDGS>
          </ADMIN-DATA>
          <DEFINITION-REF DEST="ECUC-MODULE-DEF">/TI_DRA80X_J7/Wdg</DEFINITION-REF>
          <IMPLEMENTATION-CONFIG-VARIANT>VARIANT-PRE-COMPILE</IMPLEMENTATION-CONFIG-VARIANT>
          <MODULE-DESCRIPTION-REF DEST="BSW-IMPLEMENTATION">/TI_DRA80X_J7/Wdg_Impl</MODULE-DESCRIPTION-REF>
          <CONTAINERS>
            <ECUC-CONTAINER-VALUE UUID="66e102c0-a860-4bcc-9a40-9f42fd98e00e">
              <SHORT-NAME>WdgGeneral</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Wdg/WdgGeneral</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Wdg/WdgGeneral/WdgDevErrorDetect</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Wdg/WdgGeneral/WdgDisableAllowed</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Wdg/WdgGeneral/WdgIndex</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TI_DRA80X_J7/Wdg/WdgGeneral/WdgInitialTimeout</DEFINITION-REF>
                  <VALUE>5</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/TI_DRA80X_J7/Wdg/WdgGeneral/WdgMaxTimeout</DEFINITION-REF>
                  <VALUE>32</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TI_DRA80X_J7/Wdg/WdgGeneral/WdgRunArea</DEFINITION-REF>
                  <VALUE>ROM</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/TI_DRA80X_J7/Wdg/WdgGeneral/WdgTriggerLocation</DEFINITION-REF>
                  <VALUE>Wdg_Trigger</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Wdg/WdgGeneral/WdgVersionInfoApi</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/TI_DRA80X_J7/Wdg/WdgGeneral/WdgRegisterReadbackApi</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Wdg/WdgGeneral/WdgRtiFrequency</DEFINITION-REF>
                  <VALUE>12500000</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TI_DRA80X_J7/Wdg/WdgGeneral/WdgDeviceVariant</DEFINITION-REF>
                  <VALUE>J721E</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE UUID="26b6e1d5-09d2-4e45-b831-a2799b6d02df">
              <SHORT-NAME>WdgPublishedInformation</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Wdg/WdgPublishedInformation</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TI_DRA80X_J7/Wdg/WdgPublishedInformation/WdgTriggerMode</DEFINITION-REF>
                  <VALUE>WDG_WINDOW</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE UUID="3331d3de-d20c-4009-9fed-dc31dd12fd35">
              <SHORT-NAME>WdgSettingsConfig</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Wdg/WdgSettingsConfig</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TI_DRA80X_J7/Wdg/WdgSettingsConfig/WdgDefaultMode</DEFINITION-REF>
                  <VALUE>WDGIF_SLOW_MODE</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Wdg/WdgSettingsConfig/WdgInstanceId</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE UUID="df76f797-a4aa-410a-a9e1-451cf297ab13">
                  <SHORT-NAME>WdgSettingsFast</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Wdg/WdgSettingsConfig/WdgSettingsFast</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Wdg/WdgSettingsConfig/WdgSettingsFast/WdgTimeoutValue</DEFINITION-REF>
                      <VALUE>20</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TI_DRA80X_J7/Wdg/WdgSettingsConfig/WdgSettingsFast/WdgWindowSize</DEFINITION-REF>
                      <VALUE>WDG_WINDOW_100_PERCENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TI_DRA80X_J7/Wdg/WdgSettingsConfig/WdgSettingsFast/WdgReaction</DEFINITION-REF>
                      <VALUE>WDG_INTERRUPT_RXN</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="386e7ec2-717c-4616-89cb-e4c44d4cc47c">
                  <SHORT-NAME>WdgSettingsSlow</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TI_DRA80X_J7/Wdg/WdgSettingsConfig/WdgSettingsSlow</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/TI_DRA80X_J7/Wdg/WdgSettingsConfig/WdgSettingsSlow/WdgTimeoutValue</DEFINITION-REF>
                      <VALUE>100</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TI_DRA80X_J7/Wdg/WdgSettingsConfig/WdgSettingsSlow/WdgWindowSize</DEFINITION-REF>
                      <VALUE>WDG_WINDOW_100_PERCENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/TI_DRA80X_J7/Wdg/WdgSettingsConfig/WdgSettingsSlow/WdgReaction</DEFINITION-REF>
                      <VALUE>WDG_INTERRUPT_RXN</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
          </CONTAINERS>
        </ECUC-MODULE-CONFIGURATION-VALUES>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
