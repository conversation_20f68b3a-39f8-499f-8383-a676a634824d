<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
      <SHORT-NAME>MICROSAR</SHORT-NAME>
      <ELEMENTS>
        <BSW-IMPLEMENTATION UUID="e2b22e5d-7458-491d-9936-688488e6f364">
          <SHORT-NAME>SoAd_Impl</SHORT-NAME>
          <MC-SUPPORT/>
          <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
          <SW-VERSION>16.04.00</SW-VERSION>
          <USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
          <VENDOR-ID>30</VENDOR-ID>
          <AR-RELEASE-VERSION>4.02.02</AR-RELEASE-VERSION>
        </BSW-IMPLEMENTATION>
      </ELEMENTS>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="e875cc56-4097-4501-86f7-2cc713af92fc">
          <SHORT-NAME>SoAd_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="980c4ec7-6f3c-4336-8e9e-b975497aecef">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="62b23871-7fd6-4559-b532-99ae8a07377c">
                  <SHORT-NAME>SoAd</SHORT-NAME>
                  <PROVIDED-ENTRYS>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/SoAd_ib_bswmd/BswModuleDescriptions/SoAd_MainFunction</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/SoAd_ib_bswmd/BswModuleDescriptions/SoAd_MainFunctionRx</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/SoAd_ib_bswmd/BswModuleDescriptions/SoAd_MainFunctionTx</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/SoAd_ib_bswmd/BswModuleDescriptions/SoAd_MainFunctionState</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  </PROVIDED-ENTRYS>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="c856c33f-0ca3-4025-ba5a-9376586803fe">
                      <SHORT-NAME>SoAdBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="5df603c2-d238-43f2-86cc-8778c089aaba">
                          <SHORT-NAME>SOAD_EXCLUSIVE_AREA_0</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="095502f6-d1a3-463e-8b49-e67e12428759">
                          <SHORT-NAME>SOAD_EXCLUSIVE_AREA_1</SHORT-NAME>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="c64ce11c-dfc6-483a-a3ed-79f049edc671">
                          <SHORT-NAME>SoAd_MainFunctionRx</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/SoAd_ib_bswmd/BswModuleDescriptions/SoAd_MainFunctionRx</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                        <BSW-SCHEDULABLE-ENTITY UUID="7481c563-67c2-4e36-96bc-ab218400d7cb">
                          <SHORT-NAME>SoAd_MainFunctionTx</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/SoAd_ib_bswmd/BswModuleDescriptions/SoAd_MainFunctionTx</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                        <BSW-SCHEDULABLE-ENTITY UUID="5fca1b0b-5bed-49b6-8fe5-ed942ba1aa0e">
                          <SHORT-NAME>SoAd_MainFunctionState</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/SoAd_ib_bswmd/BswModuleDescriptions/SoAd_MainFunctionState</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="924af7de-5065-48cf-99a1-15c78653d3fa">
                          <SHORT-NAME>SoAd_MainFunctionRxTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/SoAd_ib_bswmd/BswModuleDescriptions/SoAd/SoAdBehavior/SoAd_MainFunctionRx</STARTS-ON-EVENT-REF>
                          <PERIOD>0.005</PERIOD>
                        </BSW-TIMING-EVENT>
                        <BSW-TIMING-EVENT UUID="2c4b37e6-8d2f-4e2e-a55e-************">
                          <SHORT-NAME>SoAd_MainFunctionTxTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/SoAd_ib_bswmd/BswModuleDescriptions/SoAd/SoAdBehavior/SoAd_MainFunctionTx</STARTS-ON-EVENT-REF>
                          <PERIOD>0.005</PERIOD>
                        </BSW-TIMING-EVENT>
                        <BSW-TIMING-EVENT UUID="64ff188a-4d83-412d-899f-d0d25896d3cb">
                          <SHORT-NAME>SoAd_MainFunctionStateTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/SoAd_ib_bswmd/BswModuleDescriptions/SoAd/SoAdBehavior/SoAd_MainFunctionState</STARTS-ON-EVENT-REF>
                          <PERIOD>0.005</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="6acb83dc-de68-4c3a-a05f-5639b1724619">
                  <SHORT-NAME>SoAd_MainFunction</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
                <BSW-MODULE-ENTRY UUID="a17968d8-c308-418e-857c-af998866e177">
                  <SHORT-NAME>SoAd_MainFunctionRx</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
                <BSW-MODULE-ENTRY UUID="d3f4bdc4-94cc-4bc9-ba9e-7feee536ceb3">
                  <SHORT-NAME>SoAd_MainFunctionTx</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
                <BSW-MODULE-ENTRY UUID="18722c62-9fe7-4bc5-94ed-423c2dfba11a">
                  <SHORT-NAME>SoAd_MainFunctionState</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="77a15aa3-60f1-4528-bd9f-a97baa4d5f8f">
              <SHORT-NAME>XcpEvents</SHORT-NAME>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
