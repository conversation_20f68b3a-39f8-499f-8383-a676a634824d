<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>ActiveEcuC</SHORT-NAME>
      <ELEMENTS>
        <ECUC-MODULE-CONFIGURATION-VALUES UUID="218e51e2-3888-4393-b675-7d14993439a0">
          <SHORT-NAME>IoHwAb</SHORT-NAME>
          <DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/IoHwAb</DEFINITION-REF>
          <IMPLEMENTATION-CONFIG-VARIANT>VARIANT-PRE-COMPILE</IMPLEMENTATION-CONFIG-VARIANT>
          <MODULE-DESCRIPTION-REF DEST="BSW-IMPLEMENTATION">/MICROSAR/IoHwAb_Impl</MODULE-DESCRIPTION-REF>
          <CONTAINERS>
            <ECUC-CONTAINER-VALUE UUID="89b1330a-6c35-4878-a1e6-fa5ef71bae3b">
              <SHORT-NAME>IoHwAbGeneral</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbGeneral</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbGeneral/IoHwAbDevErrorDetect</DEFINITION-REF>
                  <VALUE>false</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbGeneral/IoHwAbVersionInfoApi</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbGeneral/IoHwAbUseInitFunction</DEFINITION-REF>
                  <VALUE>true</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE UUID="597d45a1-7c68-4c63-aacf-5cc35ec89154">
              <SHORT-NAME>IoHwAbDatatypes</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes</DEFINITION-REF>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE UUID="c9def144-41c5-497b-805a-371498a99a15">
                  <SHORT-NAME>float64</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbBaseType</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbBaseType/IoHwAbBaseDataType</DEFINITION-REF>
                      <VALUE>IOHWAB_FLOAT64</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="fc4efc2f-a115-44a6-9699-331ed539a7cc">
                  <SHORT-NAME>float32</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbBaseType</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbBaseType/IoHwAbBaseDataType</DEFINITION-REF>
                      <VALUE>IOHWAB_FLOAT32</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="99ac79d6-a7b9-42f1-a9bd-068fe1e97f69">
                  <SHORT-NAME>sint32</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbBaseType</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbBaseType/IoHwAbBaseDataType</DEFINITION-REF>
                      <VALUE>IOHWAB_SINT32</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="1dc156a5-064a-4f12-94f9-36ce7bafc115">
                  <SHORT-NAME>sint16</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbBaseType</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbBaseType/IoHwAbBaseDataType</DEFINITION-REF>
                      <VALUE>IOHWAB_SINT16</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="395a0618-ec44-4de9-91d2-9df0ac8c0479">
                  <SHORT-NAME>sint8</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbBaseType</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbBaseType/IoHwAbBaseDataType</DEFINITION-REF>
                      <VALUE>IOHWAB_SINT8</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="18cf685e-00ca-4786-9eff-f3cdfc8a071b">
                  <SHORT-NAME>uint32</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbBaseType</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbBaseType/IoHwAbBaseDataType</DEFINITION-REF>
                      <VALUE>IOHWAB_UINT32</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="9f9297c6-**************-229299f024a8">
                  <SHORT-NAME>uint16</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbBaseType</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbBaseType/IoHwAbBaseDataType</DEFINITION-REF>
                      <VALUE>IOHWAB_UINT16</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="7515a411-5efe-45c7-9e38-126f88e3d8b6">
                  <SHORT-NAME>uint8</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbBaseType</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbBaseType/IoHwAbBaseDataType</DEFINITION-REF>
                      <VALUE>IOHWAB_UINT8</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="9754ea99-7216-4c1f-a929-716c5c65f69b">
                  <SHORT-NAME>boolean</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbBaseType</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbBaseType/IoHwAbBaseDataType</DEFINITION-REF>
                      <VALUE>IOHWAB_BOOL</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="32dcf3df-b7a2-472c-bb99-095d23b56fa4">
                  <SHORT-NAME>IoHwAbImplementationTypes</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbImplementationTypes</DEFINITION-REF>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE UUID="96849a16-9bf2-47eb-bac8-e904bc7cd79e">
                      <SHORT-NAME>IoHwAb_AdcChannelType</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbImplementationTypes/IoHwAbTypeReference</DEFINITION-REF>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbImplementationTypes/IoHwAbTypeReference/IoHwAbTypeReferenceTypeRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/IoHwAb/IoHwAbDatatypes/uint8</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE UUID="97304552-99fe-45d8-90ef-5088d4c018a2">
                      <SHORT-NAME>IoHwAb_AdcChannelValueType</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbImplementationTypes/IoHwAbTypeReference</DEFINITION-REF>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbImplementationTypes/IoHwAbTypeReference/IoHwAbTypeReferenceTypeRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/IoHwAb/IoHwAbDatatypes/uint16</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE UUID="e2c1bfe8-527c-4666-bd7c-e8f39b00697f">
                      <SHORT-NAME>IoHwAb_DioChannelType</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbImplementationTypes/IoHwAbTypeReference</DEFINITION-REF>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbImplementationTypes/IoHwAbTypeReference/IoHwAbTypeReferenceTypeRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/IoHwAb/IoHwAbDatatypes/uint16</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE UUID="c6392efc-78c6-41a9-aa50-4fcee09459db">
                      <SHORT-NAME>IoHwAb_DioChannelValueType</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbImplementationTypes/IoHwAbTypeReference</DEFINITION-REF>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/MICROSAR/IoHwAb/IoHwAbDatatypes/IoHwAbImplementationTypes/IoHwAbTypeReference/IoHwAbTypeReferenceTypeRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/IoHwAb/IoHwAbDatatypes/boolean</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE UUID="fa81cb94-c3df-40e4-98f2-3bde1efd1ffb">
              <SHORT-NAME>PiIoHwAbAdc</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbTemplate</DEFINITION-REF>
                  <VALUE>IoHwAb_Template_None</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
              </PARAMETER-VALUES>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE UUID="4f8271fb-5877-4055-9b3d-1a4fa734c58d">
                  <SHORT-NAME>ReadChannel</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation</DEFINITION-REF>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE UUID="0723a47e-649d-4d2e-aa54-cdf03201b020">
                      <SHORT-NAME>AdcChannel</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument/IoHwAbArgDirection</DEFINITION-REF>
                          <VALUE>IOHWAB_IN</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument/IoHwAbOperationArgumentPosition</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument/IoHwAbArgTypeRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/IoHwAb/IoHwAbDatatypes/IoHwAbImplementationTypes/IoHwAb_AdcChannelType</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE UUID="91bac484-3c38-49a6-ab7d-02e176c8f99c">
                      <SHORT-NAME>AdcValue</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument/IoHwAbArgDirection</DEFINITION-REF>
                          <VALUE>IOHWAB_OUT</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument/IoHwAbOperationArgumentPosition</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument/IoHwAbArgTypeRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/IoHwAb/IoHwAbDatatypes/IoHwAbImplementationTypes/IoHwAb_AdcChannelValueType</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="d05c6362-54dc-46c9-8340-5252826b6272">
                  <SHORT-NAME>E_NOT_OK</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbApplicationError</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbApplicationError/IoHwAbErrorCode</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE UUID="2f10f4af-dd5a-46a8-b308-e6ebffbe030d">
              <SHORT-NAME>PiIoHwDio</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbTemplate</DEFINITION-REF>
                  <VALUE>IoHwAb_Template_None</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
              </PARAMETER-VALUES>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE UUID="16b250be-0461-4532-b6f9-298e52d2f7ca">
                  <SHORT-NAME>ReadChannel</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation</DEFINITION-REF>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE UUID="55426ea9-d402-4e96-99dc-4f184b0e6fd5">
                      <SHORT-NAME>DioChannel</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument/IoHwAbArgDirection</DEFINITION-REF>
                          <VALUE>IOHWAB_IN</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument/IoHwAbOperationArgumentPosition</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument/IoHwAbArgTypeRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/IoHwAb/IoHwAbDatatypes/IoHwAbImplementationTypes/IoHwAb_DioChannelType</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE UUID="60bfb7d7-4d11-4d4b-81fe-16a941e3778b">
                      <SHORT-NAME>DioValue</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument/IoHwAbArgDirection</DEFINITION-REF>
                          <VALUE>IOHWAB_OUT</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument/IoHwAbOperationArgumentPosition</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument/IoHwAbArgTypeRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/IoHwAb/IoHwAbDatatypes/IoHwAbImplementationTypes/IoHwAb_DioChannelValueType</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="6318b9ab-bb54-4734-89ee-0df38fec1df5">
                  <SHORT-NAME>E_NOT_OK</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbApplicationError</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbApplicationError/IoHwAbErrorCode</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE UUID="6c08e2e5-f4f4-4fd5-8799-eb15eb3c1487">
                  <SHORT-NAME>WriteChannel</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation</DEFINITION-REF>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE UUID="885a3484-17d7-456f-9c5a-a56c3edda924">
                      <SHORT-NAME>DioChannel</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument/IoHwAbArgDirection</DEFINITION-REF>
                          <VALUE>IOHWAB_IN</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument/IoHwAbOperationArgumentPosition</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument/IoHwAbArgTypeRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/IoHwAb/IoHwAbDatatypes/IoHwAbImplementationTypes/IoHwAb_DioChannelType</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE UUID="a15795bd-0caa-4915-b305-cb87496b6be2">
                      <SHORT-NAME>DioValue</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument/IoHwAbArgDirection</DEFINITION-REF>
                          <VALUE>IOHWAB_IN</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument/IoHwAbOperationArgumentPosition</DEFINITION-REF>
                          <VALUE>1</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortInterface/IoHwAbOperation/IoHwAbOperationArgument/IoHwAbArgTypeRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/IoHwAb/IoHwAbDatatypes/IoHwAbImplementationTypes/IoHwAb_DioChannelValueType</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE UUID="64e44b4e-8343-4d5f-99a6-92865364053f">
              <SHORT-NAME>PpIoHwAbAdc</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortPrototype</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortPrototype/IoHwAbCSDirection</DEFINITION-REF>
                  <VALUE>IOHWAB_SERVER</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
              </PARAMETER-VALUES>
              <REFERENCE-VALUES>
                <ECUC-REFERENCE-VALUE>
                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortPrototype/IoHwAbCSPortInterfaceRef</DEFINITION-REF>
                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/IoHwAb/PiIoHwAbAdc</VALUE-REF>
                </ECUC-REFERENCE-VALUE>
              </REFERENCE-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE UUID="90fd7908-128c-42f0-bd2f-c24b8c50c2e2">
              <SHORT-NAME>PpIoHwAbDio</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortPrototype</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortPrototype/IoHwAbCSDirection</DEFINITION-REF>
                  <VALUE>IOHWAB_SERVER</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
              </PARAMETER-VALUES>
              <REFERENCE-VALUES>
                <ECUC-REFERENCE-VALUE>
                  <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/IoHwAb/IoHwAbCSPortPrototype/IoHwAbCSPortInterfaceRef</DEFINITION-REF>
                  <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/IoHwAb/PiIoHwDio</VALUE-REF>
                </ECUC-REFERENCE-VALUE>
              </REFERENCE-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE UUID="d997aef9-e96c-41a8-a86b-10c4ab5929cb">
              <SHORT-NAME>IoHwAbRunnable</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbRunnable</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbRunnable/IoHwAbTriggerPeriod</DEFINITION-REF>
                  <VALUE>10</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbRunnable/IoHwAbTimebase</DEFINITION-REF>
                  <VALUE>IoHwAbMSec</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE UUID="d0f8791e-4dde-4655-b180-2062a496fb44">
              <SHORT-NAME>IoHwAbSchedulable</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/IoHwAb/IoHwAbSchedulable</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbSchedulable/IoHwAbBswTriggerPeriod</DEFINITION-REF>
                  <VALUE>10</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/IoHwAb/IoHwAbSchedulable/IoHwAbBswTimebase</DEFINITION-REF>
                  <VALUE>IoHwAbMSec</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
          </CONTAINERS>
        </ECUC-MODULE-CONFIGURATION-VALUES>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
