<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
      <SHORT-NAME>MICROSAR</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="7c77ecd2-5e2c-4f1b-87b9-1c738fa7d989">
          <SHORT-NAME>Dem_ib_bswmd</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="1c076264-5d8a-44e2-bc72-91b06704245c">
              <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
              <ELEMENTS>
                <BSW-MODULE-DESCRIPTION UUID="e683ff62-2d3b-4b62-be3e-4750131a57cd">
                  <SHORT-NAME>Dem</SHORT-NAME>
                  <PROVIDED-ENTRYS>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/Dem_ib_bswmd/BswModuleDescriptions/Dem_MasterMainFunction</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/Dem_ib_bswmd/BswModuleDescriptions/Dem_SatelliteMainFunction</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  </PROVIDED-ENTRYS>
                  <INTERNAL-BEHAVIORS>
                    <BSW-INTERNAL-BEHAVIOR UUID="6b0a08bd-2879-444a-bc22-8a2855503182">
                      <SHORT-NAME>DemBehavior</SHORT-NAME>
                      <EXCLUSIVE-AREAS>
                        <EXCLUSIVE-AREA UUID="4e7380b3-2b86-421f-864a-4450a002566a">
                          <SHORT-NAME>DEM_EXCLUSIVE_AREA_0</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">(DiagMonitor) Ensures consistency and atomicity of event reports</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="a5d738a9-272f-4389-a773-c30452912352">
                          <SHORT-NAME>DEM_EXCLUSIVE_AREA_1</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">(StateManager) Ensures consistent status updates when receiving ECU states managed outside the Dem</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="7d5d71d8-5338-4d42-892c-adf04bc20071">
                          <SHORT-NAME>DEM_EXCLUSIVE_AREA_2</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">(DcmAPI) Ensures consistent status updates when receiving ECU states managed outside the Dem</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="c43b4980-37c1-487a-bb34-d2b2ce1f2814">
                          <SHORT-NAME>DEM_EXCLUSIVE_AREA_3</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">(CrossCoreComm) Ensures consistent prestorage when called from multiple clients. Ensures data consistency between satellites and master if the platform does not provide a specific compareAndSwap instruction</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                        <EXCLUSIVE-AREA UUID="e0520c89-0862-4653-b87e-e84a8e783a8e">
                          <SHORT-NAME>DEM_EXCLUSIVE_AREA_4</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">(NonAtomic32bit) Ensures consistency if the platform does not provide an atomic 32bit access</L-2>
                          </DESC>
                        </EXCLUSIVE-AREA>
                      </EXCLUSIVE-AREAS>
                      <ENTITYS>
                        <BSW-SCHEDULABLE-ENTITY UUID="627f8158-6d43-49bf-acf0-77d946b1cd98">
                          <SHORT-NAME>Dem_MasterMainFunction</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/Dem_ib_bswmd/BswModuleDescriptions/Dem_MasterMainFunction</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                        <BSW-SCHEDULABLE-ENTITY UUID="472ec1b8-**************-e647d0aa6da2">
                          <SHORT-NAME>Dem_SatelliteMainFunction</SHORT-NAME>
                          <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/Dem_ib_bswmd/BswModuleDescriptions/Dem_SatelliteMainFunction</IMPLEMENTED-ENTRY-REF>
                        </BSW-SCHEDULABLE-ENTITY>
                      </ENTITYS>
                      <EVENTS>
                        <BSW-TIMING-EVENT UUID="03e37896-c4e9-4c51-8860-eba54b55a344">
                          <SHORT-NAME>Dem_MasterMainFunctionTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/Dem_ib_bswmd/BswModuleDescriptions/Dem/DemBehavior/Dem_MasterMainFunction</STARTS-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </BSW-TIMING-EVENT>
                        <BSW-TIMING-EVENT UUID="6fe0b33c-0d10-4054-b4dc-c899b9cd9ad7">
                          <SHORT-NAME>Dem_SatelliteMainFunctionTimingEvent0</SHORT-NAME>
                          <STARTS-ON-EVENT-REF DEST="BSW-SCHEDULABLE-ENTITY">/MICROSAR/Dem_ib_bswmd/BswModuleDescriptions/Dem/DemBehavior/Dem_SatelliteMainFunction</STARTS-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </BSW-TIMING-EVENT>
                      </EVENTS>
                    </BSW-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </BSW-MODULE-DESCRIPTION>
                <BSW-MODULE-ENTRY UUID="cab030f1-e3ae-4e33-bedc-0b4e1c9bbf27">
                  <SHORT-NAME>Dem_MasterMainFunction</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
                <BSW-MODULE-ENTRY UUID="aa3a6883-02dd-4e57-adb2-c51f4bbef60b">
                  <SHORT-NAME>Dem_SatelliteMainFunction</SHORT-NAME>
                  <CALL-TYPE>SCHEDULED</CALL-TYPE>
                  <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
                </BSW-MODULE-ENTRY>
              </ELEMENTS>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
