<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
      <SHORT-NAME>MICROSAR</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="e842f986-**************-c426dd2cc64a">
          <SHORT-NAME>StbM_swc</SHORT-NAME>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="97506673-ef7c-423d-b47d-effb5b27f829">
              <SHORT-NAME>DataTypes</SHORT-NAME>
              <ELEMENTS>
                <IMPLEMENTATION-DATA-TYPE UUID="d1fc864a-5fe5-427d-82e2-16478914752e">
                  <SHORT-NAME>StbM_SynchronizedTimeBaseType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/StbM_swc/DataTypes/DataConstrs/StbM_SynchronizedTimeBaseType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="dbd6908c-62b5-4397-8410-a0e3dcef7e5b">
                  <SHORT-NAME>StbM_TimeBaseStatusType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/MICROSAR/StbM_swc/DataTypes/CompuMethods/StbM_TimeBaseStatusType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/StbM_swc/DataTypes/DataConstrs/StbM_TimeBaseStatusType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="1d2d0b7b-277b-487f-a357-f9a4f8af39c1">
                  <SHORT-NAME>StbM_TimeStampType</SHORT-NAME>
                  <CATEGORY>STRUCTURE</CATEGORY>
                  <SUB-ELEMENTS>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="c54036e9-0ccb-439a-abd7-0cbec637ec26">
                      <SHORT-NAME>timeBaseStatus</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_TimeBaseStatusType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="801e47ca-3b25-498e-a645-2ee6e7bf6d92">
                      <SHORT-NAME>nanoseconds</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="184369b7-ebc4-43c5-89e8-4afd5051c998">
                      <SHORT-NAME>seconds</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="8061bc8e-01b1-43dc-9613-67570cd1048b">
                      <SHORT-NAME>secondsHi</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  </SUB-ELEMENTS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="31d270c9-0faa-45f6-ab77-41c67b1286ee">
                  <SHORT-NAME>StbM_UserDataType</SHORT-NAME>
                  <CATEGORY>STRUCTURE</CATEGORY>
                  <SUB-ELEMENTS>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="16f08d2c-c2c8-47d7-9877-79eb4cfdce4a">
                      <SHORT-NAME>userDataLength</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="f11929c0-b84b-47c4-9453-156bdfc62d3d">
                      <SHORT-NAME>userByte0</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="969ec1d0-7d2b-46b0-98a3-f13659b3ccf9">
                      <SHORT-NAME>userByte1</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="b6f40481-bdfd-46aa-b58c-b807ae20f432">
                      <SHORT-NAME>userByte2</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  </SUB-ELEMENTS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="69366415-df20-4511-b7b8-7915a7683cf5">
                  <SHORT-NAME>StbM_RateDeviationType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/sint16</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="38ae7d63-2062-4c12-8382-d6a251e473cc">
                  <SHORT-NAME>StbM_SyncRecordTableHeadType</SHORT-NAME>
                  <CATEGORY>STRUCTURE</CATEGORY>
                  <SUB-ELEMENTS>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="51f5d52d-9cdb-4810-a056-d7dc9e3fc59a">
                      <SHORT-NAME>SynchronizedTimeDomain</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="32409b8c-0a83-4ddd-a3d3-991f9655ff80">
                      <SHORT-NAME>HWfrequency</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="b94d5163-5ab6-44fe-b1d9-1cf7e2b29006">
                      <SHORT-NAME>HWprescaler</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  </SUB-ELEMENTS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="1db5d6f0-edae-499d-a323-e0b9665c48b2">
                  <SHORT-NAME>StbM_SyncRecordTableBlockType</SHORT-NAME>
                  <CATEGORY>STRUCTURE</CATEGORY>
                  <SUB-ELEMENTS>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="aa9159c2-9d25-42d8-9ab6-da95738a1041">
                      <SHORT-NAME>GlbSeconds</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="43a550b2-f3b5-4859-914a-bcdeb71173a6">
                      <SHORT-NAME>GlbNanoSeconds</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="7fc18833-3a84-45d1-8acb-e201b06e7d7e">
                      <SHORT-NAME>TimeBaseStatus</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_TimeBaseStatusType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="d72ccced-2ae2-4c7a-9cb0-937b38f31af7">
                      <SHORT-NAME>VirtualLocalTimeLow</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="7c05a333-c57b-4a12-818b-dab334633f0d">
                      <SHORT-NAME>RateDeviation</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/sint16</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="ed59d4a1-ee0b-45ef-9866-3389e599b4ab">
                      <SHORT-NAME>LocSeconds</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="57bf361e-2602-427d-a80d-bab7b47b7439">
                      <SHORT-NAME>LocNanoSeconds</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="a6537dfb-d446-42d7-a154-d6fb310ca2a0">
                      <SHORT-NAME>PathDelay</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  </SUB-ELEMENTS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="60b8229a-31e3-4307-8623-1fce8b1ca1ba">
                  <SHORT-NAME>StbM_OffsetRecordTableHeadType</SHORT-NAME>
                  <CATEGORY>STRUCTURE</CATEGORY>
                  <SUB-ELEMENTS>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="6b73f57a-e0c8-43bc-86a8-8e8caebba3b5">
                      <SHORT-NAME>OffsetTimeDomain</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  </SUB-ELEMENTS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="e89bd648-24d9-40fa-99e3-f3cd4e12905d">
                  <SHORT-NAME>StbM_OffsetRecordTableBlockType</SHORT-NAME>
                  <CATEGORY>STRUCTURE</CATEGORY>
                  <SUB-ELEMENTS>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="5fdfacef-78e2-4e2a-8060-7d51f16c9a9a">
                      <SHORT-NAME>GlbSeconds</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="939e99e9-62fc-49ba-a03c-4255e23de403">
                      <SHORT-NAME>GlbNanoSeconds</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="977943cb-f0f8-4349-b3dd-20d8d99f559a">
                      <SHORT-NAME>TimeBaseStatus</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_TimeBaseStatusType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  </SUB-ELEMENTS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="e33d5c8b-6603-4164-ae9a-5685c660b419">
                  <SHORT-NAME>StbM_PortIdType</SHORT-NAME>
                  <CATEGORY>STRUCTURE</CATEGORY>
                  <SUB-ELEMENTS>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="0fb7811f-3d11-426c-b989-501e56a19e53">
                      <SHORT-NAME>clockIdentity</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint64</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="34dfd22a-8196-48b2-8c8c-a87624b63286">
                      <SHORT-NAME>portNumber</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  </SUB-ELEMENTS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="4ee6e482-d91f-42e5-bc1a-cb38fe4ee25a">
                  <SHORT-NAME>StbM_TimeStampShortType</SHORT-NAME>
                  <CATEGORY>STRUCTURE</CATEGORY>
                  <SUB-ELEMENTS>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="da3d1ea3-efb6-4543-b657-e342e46d7253">
                      <SHORT-NAME>nanoseconds</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="fb9fe466-9c06-4010-9b79-09233192d14a">
                      <SHORT-NAME>seconds</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  </SUB-ELEMENTS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="5b18f2b9-e70f-4ee6-adc8-76ca1eb0de75">
                  <SHORT-NAME>StbM_VirtualLocalTimeType</SHORT-NAME>
                  <CATEGORY>STRUCTURE</CATEGORY>
                  <SUB-ELEMENTS>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="8dcf579f-9aa3-4020-a58e-5511c7e907ff">
                      <SHORT-NAME>nanosecondsLo</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="dd2e7edd-1289-4d9f-875e-ebfbd12e4d97">
                      <SHORT-NAME>nanosecondsHi</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  </SUB-ELEMENTS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="381a0b19-b4a1-4243-8f8a-ae69f03090a9">
                  <SHORT-NAME>StbM_EthTimeMasterMeasurementType</SHORT-NAME>
                  <CATEGORY>STRUCTURE</CATEGORY>
                  <SUB-ELEMENTS>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="3db590a1-c0f0-4631-8e50-6cdf99309b11">
                      <SHORT-NAME>sequenceId</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="3fac2a58-ad0e-4d4b-b973-810c9e000524">
                      <SHORT-NAME>sourcePortId</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_PortIdType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="ddc0e3b0-1cb8-4cd0-a906-67e3429a9bf4">
                      <SHORT-NAME>syncEgressTimestamp</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_VirtualLocalTimeType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="5e799e6f-aec7-41e6-8bca-b9521bcd92e1">
                      <SHORT-NAME>preciseOriginTimestamp</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_TimeStampShortType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="b9f47e97-9377-44b1-a0a7-3cc0fc171941">
                      <SHORT-NAME>correctionField</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/sint64</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  </SUB-ELEMENTS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="53e03e10-046d-4741-b459-11965bb2235d">
                  <SHORT-NAME>StbM_EthTimeSlaveMeasurementType</SHORT-NAME>
                  <CATEGORY>STRUCTURE</CATEGORY>
                  <SUB-ELEMENTS>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="b4cf45ac-f21f-4d3f-889c-f48b418817cf">
                      <SHORT-NAME>sequenceId</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="72923ceb-39e6-4bda-bf65-3d21dd5d1d18">
                      <SHORT-NAME>sourcePortId</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_PortIdType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="d85b609c-f4e4-4550-b343-f870eb247847">
                      <SHORT-NAME>syncIngressTimestamp</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_VirtualLocalTimeType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="2c6d0a24-cdfd-4790-96aa-2ca5392371a8">
                      <SHORT-NAME>preciseOriginTimestamp</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_TimeStampShortType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="ce71b122-0808-4ffe-b8b4-719b165bd720">
                      <SHORT-NAME>correctionField</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/sint64</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="4b114c62-e9b3-4cc0-944a-c38787a346b8">
                      <SHORT-NAME>pDelay</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="1c713d65-fae8-443e-b0ce-a782c38b3c3c">
                      <SHORT-NAME>referenceLocalTimestamp</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_VirtualLocalTimeType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="88ca0378-52b1-4c0f-a895-65c804893bdf">
                      <SHORT-NAME>referenceGlobalTimestamp</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_TimeStampShortType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  </SUB-ELEMENTS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="d3bb3078-15d4-43a9-b4f0-aef69c78f1e0">
                  <SHORT-NAME>StbM_PdelayInitiatorMeasurementType</SHORT-NAME>
                  <CATEGORY>STRUCTURE</CATEGORY>
                  <SUB-ELEMENTS>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="18d1ef48-931e-49b9-961a-88dbe86dc689">
                      <SHORT-NAME>sequenceId</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="d750f1f9-c60e-4398-a94a-3affc071bf34">
                      <SHORT-NAME>requestPortId</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_PortIdType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="4009c392-c078-40b9-b969-b08d02e44d26">
                      <SHORT-NAME>responsePortId</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_PortIdType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="d4d1e9d0-e47e-43b0-9ce6-ebb144d0fef9">
                      <SHORT-NAME>requestOriginTimestamp</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_VirtualLocalTimeType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="c200394a-8a6a-4cda-97c5-7cae48ce41cb">
                      <SHORT-NAME>responseReceiptTimestamp</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_VirtualLocalTimeType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="49d2554b-4eb9-40a2-b805-402a07dbf5ce">
                      <SHORT-NAME>requestReceiptTimestamp</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_TimeStampShortType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="aedcd6e7-6db6-49e8-a73d-198cb2c68213">
                      <SHORT-NAME>responseOriginTimestamp</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_TimeStampShortType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="f0ae8f22-2d2a-4f1e-8927-e6f518470e88">
                      <SHORT-NAME>referenceLocalTimestamp</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_VirtualLocalTimeType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="de368642-960a-4438-95f3-98992f445c11">
                      <SHORT-NAME>referenceGlobalTimestamp</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_TimeStampShortType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="09c2ad90-1584-470f-a817-51019affb7eb">
                      <SHORT-NAME>pdelay</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  </SUB-ELEMENTS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="38ef92ac-8c5a-40b9-ad1a-a99f150a0c9f">
                  <SHORT-NAME>StbM_PdelayResponderMeasurementType</SHORT-NAME>
                  <CATEGORY>STRUCTURE</CATEGORY>
                  <SUB-ELEMENTS>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="4f92c6c9-7384-4b50-a3ad-6ae0fcb24669">
                      <SHORT-NAME>sequenceId</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="1645dcce-a1c4-45b6-840e-5b99def407de">
                      <SHORT-NAME>requestPortId</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_PortIdType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="8a64a038-814b-4cb3-91a8-4a1bc0653447">
                      <SHORT-NAME>responsePortId</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_PortIdType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="df80c77f-2e3d-4363-bec6-a12b6b82a620">
                      <SHORT-NAME>requestReceiptTimestamp</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_VirtualLocalTimeType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="c5a02253-c9cd-41fd-86e0-d5b0e0754a8e">
                      <SHORT-NAME>responseOriginTimestamp</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_VirtualLocalTimeType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="0ebcec35-7150-4901-8b82-1135301325cf">
                      <SHORT-NAME>referenceLocalTimestamp</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_VirtualLocalTimeType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT UUID="37ca1ffa-9d89-4ebe-976b-25c76a04dc3f">
                      <SHORT-NAME>referenceGlobalTimestamp</SHORT-NAME>
                      <CATEGORY>TYPE_REFERENCE</CATEGORY>
                      <SW-DATA-DEF-PROPS>
                        <SW-DATA-DEF-PROPS-VARIANTS>
                          <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_TimeStampShortType</IMPLEMENTATION-DATA-TYPE-REF>
                          </SW-DATA-DEF-PROPS-CONDITIONAL>
                        </SW-DATA-DEF-PROPS-VARIANTS>
                      </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  </SUB-ELEMENTS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="d3b30c37-6f48-44f3-bf8f-f104239e2a13">
                  <SHORT-NAME>StbM_TimeDiffType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/StbM_swc/DataTypes/DataConstrs/StbM_TimeDiffType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/sint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="2af982d8-ec7b-4ce2-9bb8-c34d213ff53c">
                  <SHORT-NAME>StbM_TimeBaseNotificationType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/MICROSAR/StbM_swc/DataTypes/CompuMethods/StbM_TimeBaseNotificationType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/StbM_swc/DataTypes/DataConstrs/StbM_TimeBaseNotificationType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="34f7cdf6-c765-4063-8a6b-4c6f59cc4b45">
                  <SHORT-NAME>StbM_CustomerIdType</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/StbM_swc/DataTypes/DataConstrs/StbM_CustomerIdType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="26e2ab93-e6dc-4afe-a00e-936811f48d8c">
                  <SHORT-NAME>StbM_MasterConfigType</SHORT-NAME>
                  <DESC>
                    <L-2 L="FOR-ALL">The value of this type is STBM_SYSTEM_WIDE_MASTER_ENABLED if and only if an ECU is configured as system wide master for a given timebase and STBM_SYSTEM_WIDE_MASTER_DISABLED otherwise.</L-2>
                  </DESC>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/MICROSAR/StbM_swc/DataTypes/CompuMethods/StbM_MasterConfigType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/StbM_swc/DataTypes/DataConstrs/StbM_MasterConfigType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
              </ELEMENTS>
              <AR-PACKAGES>
                <AR-PACKAGE UUID="71fcedc2-b281-44a2-93b8-************">
                  <SHORT-NAME>DataConstrs</SHORT-NAME>
                  <ELEMENTS>
                    <DATA-CONSTR UUID="8f1c2f80-b2be-4f5d-a2e2-3227144a506e">
                      <SHORT-NAME>StbM_SynchronizedTimeBaseType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">65535</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="0f8f04be-fa38-4f0a-8135-af548a956bb0">
                      <SHORT-NAME>StbM_TimeBaseStatusType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">63</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="94191ba0-43b4-461c-8445-cfbe1e7f57e6">
                      <SHORT-NAME>StbM_TimeDiffType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">-2147483648</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">2147483647</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="344f678e-0ac3-4ce5-b767-465b4e0dad86">
                      <SHORT-NAME>StbM_TimeBaseNotificationType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">4294967295</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="48f05345-6068-4204-a298-a70a811ca0bb">
                      <SHORT-NAME>StbM_CustomerIdType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">65535</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="2f1f7dff-672d-457d-a222-1850d8345425">
                      <SHORT-NAME>StbM_MasterConfigType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">255</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                  </ELEMENTS>
                </AR-PACKAGE>
                <AR-PACKAGE UUID="86736c7e-9d01-483d-9360-210a8ef0bde7">
                  <SHORT-NAME>CompuMethods</SHORT-NAME>
                  <ELEMENTS>
                    <COMPU-METHOD UUID="30d113dc-4f25-46ba-a102-8345e4a2212a">
                      <SHORT-NAME>StbM_TimeBaseStatusType</SHORT-NAME>
                      <CATEGORY>BITFIELD_TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>TIMEOUT</SHORT-LABEL>
                            <SYMBOL>TIMEOUT__FALSE</SYMBOL>
                            <MASK>1</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>FALSE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>TIMEOUT</SHORT-LABEL>
                            <SYMBOL>TIMEOUT</SYMBOL>
                            <MASK>1</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>TRUE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>Reserved</SHORT-LABEL>
                            <SYMBOL>Reserved__FALSE</SYMBOL>
                            <MASK>2</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>FALSE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>Reserved</SHORT-LABEL>
                            <SYMBOL>Reserved</SYMBOL>
                            <MASK>2</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>TRUE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>SYNC_TO_GATEWAY</SHORT-LABEL>
                            <SYMBOL>SYNC_TO_GATEWAY__FALSE</SYMBOL>
                            <MASK>4</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>FALSE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>SYNC_TO_GATEWAY</SHORT-LABEL>
                            <SYMBOL>SYNC_TO_GATEWAY</SYMBOL>
                            <MASK>4</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">4</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">4</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>TRUE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>GLOBAL_TIME_BASE</SHORT-LABEL>
                            <SYMBOL>GLOBAL_TIME_BASE__FALSE</SYMBOL>
                            <MASK>8</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>FALSE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>GLOBAL_TIME_BASE</SHORT-LABEL>
                            <SYMBOL>GLOBAL_TIME_BASE</SYMBOL>
                            <MASK>8</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">8</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">8</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>TRUE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>TIMELEAP_FUTURE</SHORT-LABEL>
                            <SYMBOL>TIMELEAP_FUTURE__FALSE</SYMBOL>
                            <MASK>16</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>FALSE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>TIMELEAP_FUTURE</SHORT-LABEL>
                            <SYMBOL>TIMELEAP_FUTURE</SYMBOL>
                            <MASK>16</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">16</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">16</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>TRUE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>TIMELEAP_PAST</SHORT-LABEL>
                            <SYMBOL>TIMELEAP_PAST__FALSE</SYMBOL>
                            <MASK>32</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>FALSE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>TIMELEAP_PAST</SHORT-LABEL>
                            <SYMBOL>TIMELEAP_PAST</SYMBOL>
                            <MASK>32</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">32</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">32</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>TRUE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                    <COMPU-METHOD UUID="70ae63a2-f40f-4aa3-b3ee-8c42c8ebc2bb">
                      <SHORT-NAME>StbM_TimeBaseNotificationType</SHORT-NAME>
                      <CATEGORY>BITFIELD_TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_GLOBAL_TIME</SHORT-LABEL>
                            <SYMBOL>EV_GLOBAL_TIME__FALSE</SYMBOL>
                            <MASK>1</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>FALSE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_GLOBAL_TIME</SHORT-LABEL>
                            <SYMBOL>EV_GLOBAL_TIME</SYMBOL>
                            <MASK>1</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>TRUE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_TIMEOUT_OCCURRED</SHORT-LABEL>
                            <SYMBOL>EV_TIMEOUT_OCCURRED__FALSE</SYMBOL>
                            <MASK>2</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>FALSE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_TIMEOUT_OCCURRED</SHORT-LABEL>
                            <SYMBOL>EV_TIMEOUT_OCCURRED</SYMBOL>
                            <MASK>2</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>TRUE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_TIMEOUT_REMOVED</SHORT-LABEL>
                            <SYMBOL>EV_TIMEOUT_REMOVED__FALSE</SYMBOL>
                            <MASK>4</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>FALSE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_TIMEOUT_REMOVED</SHORT-LABEL>
                            <SYMBOL>EV_TIMEOUT_REMOVED</SYMBOL>
                            <MASK>4</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">4</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">4</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>TRUE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_TIMELEAP_FUTURE</SHORT-LABEL>
                            <SYMBOL>EV_TIMELEAP_FUTURE__FALSE</SYMBOL>
                            <MASK>8</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>FALSE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_TIMELEAP_FUTURE</SHORT-LABEL>
                            <SYMBOL>EV_TIMELEAP_FUTURE</SYMBOL>
                            <MASK>8</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">8</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">8</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>TRUE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_TIMELEAP_FUTURE_REMOVED</SHORT-LABEL>
                            <SYMBOL>EV_TIMELEAP_FUTURE_REMOVED__FALSE</SYMBOL>
                            <MASK>16</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>FALSE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_TIMELEAP_FUTURE_REMOVED</SHORT-LABEL>
                            <SYMBOL>EV_TIMELEAP_FUTURE_REMOVED</SYMBOL>
                            <MASK>16</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">16</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">16</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>TRUE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_TIMELEAP_PAST</SHORT-LABEL>
                            <SYMBOL>EV_TIMELEAP_PAST__FALSE</SYMBOL>
                            <MASK>32</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>FALSE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_TIMELEAP_PAST</SHORT-LABEL>
                            <SYMBOL>EV_TIMELEAP_PAST</SYMBOL>
                            <MASK>32</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">32</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">32</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>TRUE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_TIMELEAP_PAST_REMOVED</SHORT-LABEL>
                            <SYMBOL>EV_TIMELEAP_PAST_REMOVED__FALSE</SYMBOL>
                            <MASK>64</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>FALSE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_TIMELEAP_PAST_REMOVED</SHORT-LABEL>
                            <SYMBOL>EV_TIMELEAP_PAST_REMOVED</SYMBOL>
                            <MASK>64</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">64</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">64</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>TRUE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_SYNC_TO_SUBDOMAIN</SHORT-LABEL>
                            <SYMBOL>EV_SYNC_TO_SUBDOMAIN__FALSE</SYMBOL>
                            <MASK>128</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>FALSE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_SYNC_TO_SUBDOMAIN</SHORT-LABEL>
                            <SYMBOL>EV_SYNC_TO_SUBDOMAIN</SYMBOL>
                            <MASK>128</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">128</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">128</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>TRUE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_SYNC_TO_GLOBAL_MASTER</SHORT-LABEL>
                            <SYMBOL>EV_SYNC_TO_GLOBAL_MASTER__FALSE</SYMBOL>
                            <MASK>256</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>FALSE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_SYNC_TO_GLOBAL_MASTER</SHORT-LABEL>
                            <SYMBOL>EV_SYNC_TO_GLOBAL_MASTER</SYMBOL>
                            <MASK>256</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">256</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">256</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>TRUE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_RESYNC</SHORT-LABEL>
                            <SYMBOL>EV_RESYNC__FALSE</SYMBOL>
                            <MASK>512</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>FALSE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_RESYNC</SHORT-LABEL>
                            <SYMBOL>EV_RESYNC</SYMBOL>
                            <MASK>512</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">512</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">512</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>TRUE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_RATECORRECTION</SHORT-LABEL>
                            <SYMBOL>EV_RATECORRECTION__FALSE</SYMBOL>
                            <MASK>1024</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>FALSE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>EV_RATECORRECTION</SHORT-LABEL>
                            <SYMBOL>EV_RATECORRECTION</SYMBOL>
                            <MASK>1024</MASK>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1024</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1024</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>TRUE</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                    <COMPU-METHOD UUID="3555271a-9e5a-423b-8647-40a8d93a877d">
                      <SHORT-NAME>StbM_MasterConfigType</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>STBM_SYSTEM_WIDE_MASTER_DISABLED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>STBM_SYSTEM_WIDE_MASTER_DISABLED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>STBM_SYSTEM_WIDE_MASTER_ENABLED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>STBM_SYSTEM_WIDE_MASTER_ENABLED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                  </ELEMENTS>
                </AR-PACKAGE>
              </AR-PACKAGES>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="08ad4a3a-6c80-47c8-b22f-28cf271832d2">
              <SHORT-NAME>Interfaces</SHORT-NAME>
              <ELEMENTS>
                <CLIENT-SERVER-INTERFACE UUID="d3c08ed3-a6a0-44d7-967c-4c872ba8b12e">
                  <SHORT-NAME>GlobalTime_Slave_StbMSynchronizedTimeBase</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>SYNC-BASE-TIME-MANAGER</SERVICE-KIND>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="230deb73-6c3c-41fc-b5a8-484962ff2cfd">
                      <SHORT-NAME>GetCurrentTime</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="6982d4ab-b7cf-4b6d-89c1-18ae63df9ee6">
                          <SHORT-NAME>timeStampPtr</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_TimeStampType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="03c9698f-cfb2-44a4-b31e-bc98e7dc97e9">
                          <SHORT-NAME>userDataPtr</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_UserDataType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Slave_StbMSynchronizedTimeBase/E_OK</POSSIBLE-ERROR-REF>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Slave_StbMSynchronizedTimeBase/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="30d2e7bb-5c84-4726-8c7d-3b4bc2102313">
                      <SHORT-NAME>GetRateDeviation</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="76254c5e-3f26-40fb-ace6-2a87cadf8a3f">
                          <SHORT-NAME>rateDeviation</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_RateDeviationType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Slave_StbMSynchronizedTimeBase/E_OK</POSSIBLE-ERROR-REF>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Slave_StbMSynchronizedTimeBase/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="6f706caf-4b48-4ca9-92d1-0e480cd031c9">
                      <SHORT-NAME>GetTimeLeap</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="05202a58-3d59-4c68-8ebf-f2a402adc45a">
                          <SHORT-NAME>timeJump</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_TimeDiffType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Slave_StbMSynchronizedTimeBase/E_OK</POSSIBLE-ERROR-REF>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Slave_StbMSynchronizedTimeBase/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="16347e68-9df2-47f9-bf21-4c5efa6014f0">
                      <SHORT-NAME>GetTimeBaseStatus</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="3fa03c24-26a9-47d1-8bf2-48f474794713">
                          <SHORT-NAME>syncTimeBaseStatus</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_TimeBaseStatusType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="75ba9542-b0c9-45a4-9280-23eaab9e7031">
                          <SHORT-NAME>offsetTimeBaseStatus</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_TimeBaseStatusType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Slave_StbMSynchronizedTimeBase/E_OK</POSSIBLE-ERROR-REF>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Slave_StbMSynchronizedTimeBase/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                  <POSSIBLE-ERRORS>
                    <APPLICATION-ERROR UUID="df2e490e-69f1-469e-9e70-533b7ef9bfbe">
                      <SHORT-NAME>E_OK</SHORT-NAME>
                      <ERROR-CODE>0</ERROR-CODE>
                    </APPLICATION-ERROR>
                    <APPLICATION-ERROR UUID="5e020a36-3150-47c8-86dd-c91180e9b589">
                      <SHORT-NAME>E_NOT_OK</SHORT-NAME>
                      <ERROR-CODE>1</ERROR-CODE>
                    </APPLICATION-ERROR>
                  </POSSIBLE-ERRORS>
                </CLIENT-SERVER-INTERFACE>
                <CLIENT-SERVER-INTERFACE UUID="96747b53-4e5b-4729-8173-dff73c036f04">
                  <SHORT-NAME>GlobalTime_Master_StbMSynchronizedTimeBase</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>SYNC-BASE-TIME-MANAGER</SERVICE-KIND>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="b990abae-8312-472a-b7de-b9d5f96b93c8">
                      <SHORT-NAME>SetGlobalTime</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="a1ee2b51-965b-437e-a749-fdb2f9ac25d1">
                          <SHORT-NAME>timeStampPtr</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_TimeStampType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="d8d00eef-5110-4bdc-8183-45d09b6e0b7e">
                          <SHORT-NAME>userDataPtr</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_UserDataType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Master_StbMSynchronizedTimeBase/E_OK</POSSIBLE-ERROR-REF>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Master_StbMSynchronizedTimeBase/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="cb7313e4-3e81-43f1-8a4e-c7ec2ad3aa4f">
                      <SHORT-NAME>SetUserData</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="c3d3ce55-68b4-42c8-bfe9-dce1e87c8c77">
                          <SHORT-NAME>userDataPtr</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_UserDataType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Master_StbMSynchronizedTimeBase/E_OK</POSSIBLE-ERROR-REF>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Master_StbMSynchronizedTimeBase/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="bb9cf5ed-9f6c-45cd-a8e0-645ee193f8b8">
                      <SHORT-NAME>SetRateCorrection</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="36ca53de-6cc0-45d1-b9cc-005391cb2c9d">
                          <SHORT-NAME>rateDeviation</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_RateDeviationType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Master_StbMSynchronizedTimeBase/E_OK</POSSIBLE-ERROR-REF>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Master_StbMSynchronizedTimeBase/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="535a3e7c-cd11-4bfd-941e-370d68a96f8e">
                      <SHORT-NAME>TriggerTimeTransmission</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Master_StbMSynchronizedTimeBase/E_OK</POSSIBLE-ERROR-REF>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Master_StbMSynchronizedTimeBase/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="f5ddc4bd-**************-88c27174ef99">
                      <SHORT-NAME>UpdateGlobalTime</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL"/>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="5d45b0cc-7686-41a8-b9e8-7346e5588722">
                          <SHORT-NAME>timeStamp</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_TimeStampType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="189df7f1-8601-4745-b4af-232e42870b11">
                          <SHORT-NAME>userData</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL"/>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_UserDataType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Master_StbMSynchronizedTimeBase/E_OK</POSSIBLE-ERROR-REF>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Master_StbMSynchronizedTimeBase/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                  <POSSIBLE-ERRORS>
                    <APPLICATION-ERROR UUID="461fce48-56d2-4284-8435-14bd370f3b60">
                      <SHORT-NAME>E_OK</SHORT-NAME>
                      <ERROR-CODE>0</ERROR-CODE>
                    </APPLICATION-ERROR>
                    <APPLICATION-ERROR UUID="7aa8fd83-b337-4785-9711-28e70dce605a">
                      <SHORT-NAME>E_NOT_OK</SHORT-NAME>
                      <ERROR-CODE>1</ERROR-CODE>
                    </APPLICATION-ERROR>
                  </POSSIBLE-ERRORS>
                </CLIENT-SERVER-INTERFACE>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="3ee23477-f555-4445-bdc1-008bb3ecb3a0">
              <SHORT-NAME>ComponentTypes</SHORT-NAME>
              <ELEMENTS>
                <SERVICE-SW-COMPONENT-TYPE UUID="a52da913-c135-41a6-9145-f8373526c83e">
                  <SHORT-NAME>StbM</SHORT-NAME>
                  <CATEGORY>SERVICE_COMPONENT</CATEGORY>
                  <PORTS>
                    <P-PORT-PROTOTYPE UUID="f67a555d-d5ea-4382-a2f3-b7291d3c02ce">
                      <SHORT-NAME>GlobalTime_Slave_StbMSynchronizedTimeBase</SHORT-NAME>
                      <PROVIDED-INTERFACE-TREF DEST="CLIENT-SERVER-INTERFACE">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Slave_StbMSynchronizedTimeBase</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="629c3e7d-dcd7-4baa-abf0-30083434c1c3">
                      <SHORT-NAME>GlobalTime_Master_StbMSynchronizedTimeBase</SHORT-NAME>
                      <PROVIDED-INTERFACE-TREF DEST="CLIENT-SERVER-INTERFACE">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Master_StbMSynchronizedTimeBase</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                  </PORTS>
                  <INTERNAL-BEHAVIORS>
                    <SWC-INTERNAL-BEHAVIOR UUID="6c8f8d6e-3dc8-4697-8634-5a7c5784018c">
                      <SHORT-NAME>StbMInternalBehavior</SHORT-NAME>
                      <EVENTS>
                        <TIMING-EVENT UUID="23033f46-3dd6-4839-be85-d5e4fd8da32a">
                          <SHORT-NAME>Timer_StbM_MainFunction</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/StbM_swc/ComponentTypes/StbM/StbMInternalBehavior/StbM_MainFunction</START-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </TIMING-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="88a6e371-ac8c-45e5-8206-0252bb7c5efb">
                          <SHORT-NAME>OpEventGetCurrentTime_GetCurrentTime_GlobalTime_Slave_StbMSynchronizedTimeBase</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/StbM_swc/ComponentTypes/StbM/StbMInternalBehavior/GetCurrentTime</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/StbM_swc/ComponentTypes/StbM/GlobalTime_Slave_StbMSynchronizedTimeBase</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Slave_StbMSynchronizedTimeBase/GetCurrentTime</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="ac2de6b9-2324-4fd4-8e3a-e461aa9bc9b1">
                          <SHORT-NAME>OpEventGetRateDeviation_GetRateDeviation_GlobalTime_Slave_StbMSynchronizedTimeBase</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/StbM_swc/ComponentTypes/StbM/StbMInternalBehavior/GetRateDeviation</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/StbM_swc/ComponentTypes/StbM/GlobalTime_Slave_StbMSynchronizedTimeBase</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Slave_StbMSynchronizedTimeBase/GetRateDeviation</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="2cca46dc-b48d-4cc8-b392-779372c287a9">
                          <SHORT-NAME>OpEventGetTimeLeap_GetTimeLeap_GlobalTime_Slave_StbMSynchronizedTimeBase</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/StbM_swc/ComponentTypes/StbM/StbMInternalBehavior/GetTimeLeap</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/StbM_swc/ComponentTypes/StbM/GlobalTime_Slave_StbMSynchronizedTimeBase</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Slave_StbMSynchronizedTimeBase/GetTimeLeap</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="9b248c75-ca2d-4d7d-a887-a463a8078ec8">
                          <SHORT-NAME>OpEventGetTimeBaseStatus_GetTimeBaseStatus_GlobalTime_Slave_StbMSynchronizedTimeBase</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/StbM_swc/ComponentTypes/StbM/StbMInternalBehavior/GetTimeBaseStatus</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/StbM_swc/ComponentTypes/StbM/GlobalTime_Slave_StbMSynchronizedTimeBase</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Slave_StbMSynchronizedTimeBase/GetTimeBaseStatus</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="5ac06699-8460-495a-8268-1423725dba09">
                          <SHORT-NAME>OpEventSetGlobalTime_SetGlobalTime_GlobalTime_Master_StbMSynchronizedTimeBase</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/StbM_swc/ComponentTypes/StbM/StbMInternalBehavior/SetGlobalTime</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/StbM_swc/ComponentTypes/StbM/GlobalTime_Master_StbMSynchronizedTimeBase</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Master_StbMSynchronizedTimeBase/SetGlobalTime</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="2c85123e-2f91-47db-8924-b0e295f5b305">
                          <SHORT-NAME>OpEventSetUserData_SetUserData_GlobalTime_Master_StbMSynchronizedTimeBase</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/StbM_swc/ComponentTypes/StbM/StbMInternalBehavior/SetUserData</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/StbM_swc/ComponentTypes/StbM/GlobalTime_Master_StbMSynchronizedTimeBase</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Master_StbMSynchronizedTimeBase/SetUserData</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="f32c8212-c59b-4530-8b64-dfcd2cff2fa7">
                          <SHORT-NAME>OpEventSetRateCorrection_SetRateCorrection_GlobalTime_Master_StbMSynchronizedTimeBase</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/StbM_swc/ComponentTypes/StbM/StbMInternalBehavior/SetRateCorrection</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/StbM_swc/ComponentTypes/StbM/GlobalTime_Master_StbMSynchronizedTimeBase</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Master_StbMSynchronizedTimeBase/SetRateCorrection</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="4ac370f0-43ac-4fac-b2e5-3a976ff6a89f">
                          <SHORT-NAME>OpEventTriggerTimeTransmission_TriggerTimeTransmission_GlobalTime_Master_StbMSynchronizedTimeBase</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/StbM_swc/ComponentTypes/StbM/StbMInternalBehavior/TriggerTimeTransmission</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/StbM_swc/ComponentTypes/StbM/GlobalTime_Master_StbMSynchronizedTimeBase</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Master_StbMSynchronizedTimeBase/TriggerTimeTransmission</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="0862c136-364c-43a3-8610-575e4b05182e">
                          <SHORT-NAME>OpEventUpdateGlobalTime_UpdateGlobalTime_GlobalTime_Master_StbMSynchronizedTimeBase</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/StbM_swc/ComponentTypes/StbM/StbMInternalBehavior/UpdateGlobalTime</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/StbM_swc/ComponentTypes/StbM/GlobalTime_Master_StbMSynchronizedTimeBase</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/StbM_swc/Interfaces/GlobalTime_Master_StbMSynchronizedTimeBase/UpdateGlobalTime</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                      </EVENTS>
                      <HANDLE-TERMINATION-AND-RESTART>NO-SUPPORT</HANDLE-TERMINATION-AND-RESTART>
                      <INCLUDED-DATA-TYPE-SETS>
                        <INCLUDED-DATA-TYPE-SET>
                          <DATA-TYPE-REFS>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_SynchronizedTimeBaseType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_TimeBaseStatusType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_TimeStampType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_UserDataType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_RateDeviationType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_SyncRecordTableHeadType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_SyncRecordTableBlockType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_OffsetRecordTableHeadType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_OffsetRecordTableBlockType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_PortIdType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_TimeStampShortType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_VirtualLocalTimeType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_EthTimeMasterMeasurementType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_EthTimeSlaveMeasurementType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_PdelayInitiatorMeasurementType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_PdelayResponderMeasurementType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_TimeDiffType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_TimeBaseNotificationType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_CustomerIdType</DATA-TYPE-REF>
                            <DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_MasterConfigType</DATA-TYPE-REF>
                          </DATA-TYPE-REFS>
                        </INCLUDED-DATA-TYPE-SET>
                      </INCLUDED-DATA-TYPE-SETS>
                      <PORT-API-OPTIONS>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-ARG-VALUES>
                            <PORT-DEFINED-ARGUMENT-VALUE>
                              <VALUE>
                                <NUMERICAL-VALUE-SPECIFICATION>
                                  <VALUE>0</VALUE>
                                </NUMERICAL-VALUE-SPECIFICATION>
                              </VALUE>
                              <VALUE-TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_SynchronizedTimeBaseType</VALUE-TYPE-TREF>
                            </PORT-DEFINED-ARGUMENT-VALUE>
                          </PORT-ARG-VALUES>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/StbM_swc/ComponentTypes/StbM/GlobalTime_Slave_StbMSynchronizedTimeBase</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-ARG-VALUES>
                            <PORT-DEFINED-ARGUMENT-VALUE>
                              <VALUE>
                                <NUMERICAL-VALUE-SPECIFICATION>
                                  <VALUE>0</VALUE>
                                </NUMERICAL-VALUE-SPECIFICATION>
                              </VALUE>
                              <VALUE-TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/StbM_swc/DataTypes/StbM_SynchronizedTimeBaseType</VALUE-TYPE-TREF>
                            </PORT-DEFINED-ARGUMENT-VALUE>
                          </PORT-ARG-VALUES>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/StbM_swc/ComponentTypes/StbM/GlobalTime_Master_StbMSynchronizedTimeBase</PORT-REF>
                        </PORT-API-OPTION>
                      </PORT-API-OPTIONS>
                      <RUNNABLES>
                        <RUNNABLE-ENTITY UUID="ca70ba56-0f69-4269-be52-df3236eb227c">
                          <SHORT-NAME>StbM_MainFunction</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>false</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>StbM_MainFunction</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="82dafd3b-b5fb-4595-abf9-df068d822a8b">
                          <SHORT-NAME>GetCurrentTime</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>StbM_GetCurrentTime</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="2e656b0e-88cd-4198-8a67-5ed6c72d523a">
                          <SHORT-NAME>GetRateDeviation</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>StbM_GetRateDeviation</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="823edd3b-bd0f-48e3-b71d-9ec360689f73">
                          <SHORT-NAME>GetTimeLeap</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>StbM_GetTimeLeap</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="4aef9b97-a9bc-4eb7-aa67-404f5cb322fe">
                          <SHORT-NAME>GetTimeBaseStatus</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>StbM_GetTimeBaseStatus</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="2c90ed06-924d-46ea-8717-f578f95d0d36">
                          <SHORT-NAME>SetGlobalTime</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>StbM_SetGlobalTime</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="1f8b464f-ea2a-4c14-b950-5d9693bdd90f">
                          <SHORT-NAME>SetUserData</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>StbM_SetUserData</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="e04df544-e915-48fa-9120-6c173bcb2ff5">
                          <SHORT-NAME>SetRateCorrection</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>StbM_SetRateCorrection</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="a695a639-81e9-4801-a1c4-9c192b2eeae0">
                          <SHORT-NAME>TriggerTimeTransmission</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>StbM_TriggerTimeTransmission</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="1f62f5fa-3f29-4bc9-a1da-516924675c78">
                          <SHORT-NAME>UpdateGlobalTime</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>StbM_UpdateGlobalTime</SYMBOL>
                        </RUNNABLE-ENTITY>
                      </RUNNABLES>
                      <SUPPORTS-MULTIPLE-INSTANTIATION>false</SUPPORTS-MULTIPLE-INSTANTIATION>
                    </SWC-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </SERVICE-SW-COMPONENT-TYPE>
                <SWC-IMPLEMENTATION UUID="0df95ae5-1f8b-4e35-8697-9087ef8aeb0c">
                  <SHORT-NAME>StbMImplementation</SHORT-NAME>
                  <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
                  <BEHAVIOR-REF DEST="SWC-INTERNAL-BEHAVIOR">/MICROSAR/StbM_swc/ComponentTypes/StbM/StbMInternalBehavior</BEHAVIOR-REF>
                </SWC-IMPLEMENTATION>
              </ELEMENTS>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
