<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00048.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
      <SHORT-NAME>MICROSAR</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE UUID="783e1bef-43da-41e3-940d-b65320f1772b">
          <SHORT-NAME>WdgM_swc</SHORT-NAME>
          <ELEMENTS>
            <MODE-DECLARATION-GROUP UUID="1aea0e28-3253-4265-9bc5-d81dd2938b58">
              <SHORT-NAME>WdgM_Mode</SHORT-NAME>
              <DESC>
                <L-2 L="FOR-ALL">The mode declaration group WdgMMode represents the modes of the Watchdog Manager module that will be notified to the SW-Cs / CDDs and the RTE.</L-2>
              </DESC>
              <CATEGORY>EXPLICIT_ORDER</CATEGORY>
              <INITIAL-MODE-REF DEST="MODE-DECLARATION">/MICROSAR/WdgM_swc/WdgM_Mode/SUPERVISION_OK</INITIAL-MODE-REF>
              <MODE-DECLARATIONS>
                <MODE-DECLARATION UUID="dd21bee5-9412-4f71-b635-a89f1a1d1d13">
                  <SHORT-NAME>SUPERVISION_OK</SHORT-NAME>
                  <VALUE>0</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="07b0a88d-9a3c-4d6d-9de9-f93cbb237d26">
                  <SHORT-NAME>SUPERVISION_FAILED</SHORT-NAME>
                  <VALUE>1</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="5dd741f1-d939-47bb-8884-73565efccf8b">
                  <SHORT-NAME>SUPERVISION_EXPIRED</SHORT-NAME>
                  <VALUE>2</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="f2be12d7-467d-40a6-8c0a-ff34c4bf699e">
                  <SHORT-NAME>SUPERVISION_STOPPED</SHORT-NAME>
                  <VALUE>3</VALUE>
                </MODE-DECLARATION>
                <MODE-DECLARATION UUID="84a221be-90ac-472f-aaec-4db39c000538">
                  <SHORT-NAME>SUPERVISION_DEACTIVATED</SHORT-NAME>
                  <VALUE>4</VALUE>
                </MODE-DECLARATION>
              </MODE-DECLARATIONS>
              <ON-TRANSITION-VALUE>255</ON-TRANSITION-VALUE>
            </MODE-DECLARATION-GROUP>
            <DATA-TYPE-MAPPING-SET UUID="e17248c6-4bc2-4500-9ed0-3dc98f142fb1">
              <SHORT-NAME>WdgMMappingSet</SHORT-NAME>
              <DESC>
                <L-2 L="FOR-ALL">Contains mapping for mode declaration groups which are provided by WdgM.</L-2>
              </DESC>
              <MODE-REQUEST-TYPE-MAPS>
                <MODE-REQUEST-TYPE-MAP>
                  <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/WdgM_swc/DataTypes/WdgMMode</IMPLEMENTATION-DATA-TYPE-REF>
                  <MODE-GROUP-REF DEST="MODE-DECLARATION-GROUP">/MICROSAR/WdgM_swc/WdgM_Mode</MODE-GROUP-REF>
                </MODE-REQUEST-TYPE-MAP>
              </MODE-REQUEST-TYPE-MAPS>
            </DATA-TYPE-MAPPING-SET>
          </ELEMENTS>
          <AR-PACKAGES>
            <AR-PACKAGE UUID="c2ac6431-f244-436d-9045-b8dbfd42788c">
              <SHORT-NAME>DataTypes</SHORT-NAME>
              <ELEMENTS>
                <IMPLEMENTATION-DATA-TYPE UUID="edd202a6-5990-4d54-9140-a6747a2e572d">
                  <SHORT-NAME>WdgM_SupervisedEntityIdType</SHORT-NAME>
                  <DESC>
                    <L-2 L="FOR-ALL">Identifier of a supervised entity</L-2>
                  </DESC>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/WdgM_swc/DataTypes/DataConstrs/WdgM_SupervisedEntityIdType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="bbf03b6d-55ba-4d5e-b599-a9309c75f1cc">
                  <SHORT-NAME>WdgM_CheckpointIdType</SHORT-NAME>
                  <DESC>
                    <L-2 L="FOR-ALL">Identifier of a checkpoint</L-2>
                  </DESC>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/WdgM_swc/DataTypes/DataConstrs/WdgM_CheckpointIdType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="1637b7a1-f24f-4c1e-b57f-3026b06324ce">
                  <SHORT-NAME>WdgM_ModeType</SHORT-NAME>
                  <DESC>
                    <L-2 L="FOR-ALL">Identifier of the Watchdog trigger mode</L-2>
                  </DESC>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/WdgM_swc/DataTypes/DataConstrs/WdgM_ModeType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="1208b34e-576d-49e3-9a15-344a8510d0f6">
                  <SHORT-NAME>WdgM_LocalStatusType</SHORT-NAME>
                  <DESC>
                    <L-2 L="FOR-ALL">Local Monitoring Status of a supervised entity.</L-2>
                  </DESC>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/MICROSAR/WdgM_swc/DataTypes/CompuMethods/WdgM_LocalStatusType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/WdgM_swc/DataTypes/DataConstrs/WdgM_LocalStatusType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="76905a56-9662-4bcc-a1bf-628d9dd5a4ba">
                  <SHORT-NAME>WdgM_GlobalStatusType</SHORT-NAME>
                  <DESC>
                    <L-2 L="FOR-ALL">Global Monitoring Status summarizes all local monitoring statuses of all supervised entities.</L-2>
                  </DESC>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/MICROSAR/WdgM_swc/DataTypes/CompuMethods/WdgM_GlobalStatusType</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/WdgM_swc/DataTypes/DataConstrs/WdgM_GlobalStatusType_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
                <IMPLEMENTATION-DATA-TYPE UUID="f40fbd74-17db-4661-aff3-e93c55fcd7fe">
                  <SHORT-NAME>WdgMMode</SHORT-NAME>
                  <DESC>
                    <L-2 L="FOR-ALL">The implementation data type to be mapped to the mode declaration group.</L-2>
                  </DESC>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <COMPU-METHOD-REF DEST="COMPU-METHOD">/MICROSAR/WdgM_swc/DataTypes/CompuMethods/WdgMMode</COMPU-METHOD-REF>
                        <DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR/WdgM_swc/DataTypes/DataConstrs/WdgMMode_Constr</DATA-CONSTR-REF>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE>
              </ELEMENTS>
              <AR-PACKAGES>
                <AR-PACKAGE UUID="09c2fb4c-04e0-4151-99b1-957ce9184482">
                  <SHORT-NAME>DataConstrs</SHORT-NAME>
                  <ELEMENTS>
                    <DATA-CONSTR UUID="18a2c88f-22c0-442f-bc01-b01eae81824c">
                      <SHORT-NAME>WdgM_SupervisedEntityIdType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">65535</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="30abb2fb-ac2f-4aae-a021-5a94ef857c7b">
                      <SHORT-NAME>WdgM_CheckpointIdType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">65535</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="6ed4d744-4b8f-4fe1-9cc4-6aeb9696d64d">
                      <SHORT-NAME>WdgM_ModeType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">255</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="8dca063a-c9fc-47b3-affa-eac0831ac40c">
                      <SHORT-NAME>WdgM_LocalStatusType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">4</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="c514aa10-6075-4a54-afd7-be98b7663638">
                      <SHORT-NAME>WdgM_GlobalStatusType_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">4</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                    <DATA-CONSTR UUID="72546b2a-4908-4c3c-a357-8f79bb5673f1">
                      <SHORT-NAME>WdgMMode_Constr</SHORT-NAME>
                      <DATA-CONSTR-RULES>
                        <DATA-CONSTR-RULE>
                          <INTERNAL-CONSTRS>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">255</UPPER-LIMIT>
                          </INTERNAL-CONSTRS>
                        </DATA-CONSTR-RULE>
                      </DATA-CONSTR-RULES>
                    </DATA-CONSTR>
                  </ELEMENTS>
                </AR-PACKAGE>
                <AR-PACKAGE UUID="fec00bba-d495-4d16-97c8-8334c3c71358">
                  <SHORT-NAME>CompuMethods</SHORT-NAME>
                  <ELEMENTS>
                    <COMPU-METHOD UUID="ee8cc221-9b9d-48ce-a73d-0aa83d88340a">
                      <SHORT-NAME>WdgM_LocalStatusType</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>WDGM_LOCAL_STATUS_OK</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>WDGM_LOCAL_STATUS_OK</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>WDGM_LOCAL_STATUS_FAILED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>WDGM_LOCAL_STATUS_FAILED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>WDGM_LOCAL_STATUS_EXPIRED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>WDGM_LOCAL_STATUS_EXPIRED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>WDGM_LOCAL_STATUS_DEACTIVATED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">4</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">4</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>WDGM_LOCAL_STATUS_DEACTIVATED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                    <COMPU-METHOD UUID="ff5ce4ae-d902-4550-8492-c69ba01f6142">
                      <SHORT-NAME>WdgM_GlobalStatusType</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>WDGM_GLOBAL_STATUS_OK</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>WDGM_GLOBAL_STATUS_OK</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>WDGM_GLOBAL_STATUS_FAILED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>WDGM_GLOBAL_STATUS_FAILED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>WDGM_GLOBAL_STATUS_EXPIRED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>WDGM_GLOBAL_STATUS_EXPIRED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>WDGM_GLOBAL_STATUS_STOPPED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">3</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">3</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>WDGM_GLOBAL_STATUS_STOPPED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>WDGM_GLOBAL_STATUS_DEACTIVATED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">4</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">4</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>WDGM_GLOBAL_STATUS_DEACTIVATED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                    <COMPU-METHOD UUID="a2a9bbce-8abb-43e2-9ae0-5558f917d8c7">
                      <SHORT-NAME>WdgMMode</SHORT-NAME>
                      <CATEGORY>TEXTTABLE</CATEGORY>
                      <COMPU-INTERNAL-TO-PHYS>
                        <COMPU-SCALES>
                          <COMPU-SCALE>
                            <SHORT-LABEL>SUPERVISION_OK</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>SUPERVISION_OK</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>SUPERVISION_FAILED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>SUPERVISION_FAILED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>SUPERVISION_EXPIRED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>SUPERVISION_EXPIRED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>SUPERVISION_STOPPED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">3</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">3</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>SUPERVISION_STOPPED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                          <COMPU-SCALE>
                            <SHORT-LABEL>SUPERVISION_DEACTIVATED</SHORT-LABEL>
                            <LOWER-LIMIT INTERVAL-TYPE="CLOSED">4</LOWER-LIMIT>
                            <UPPER-LIMIT INTERVAL-TYPE="CLOSED">4</UPPER-LIMIT>
                            <COMPU-CONST>
                              <VT>SUPERVISION_DEACTIVATED</VT>
                            </COMPU-CONST>
                          </COMPU-SCALE>
                        </COMPU-SCALES>
                      </COMPU-INTERNAL-TO-PHYS>
                    </COMPU-METHOD>
                  </ELEMENTS>
                </AR-PACKAGE>
              </AR-PACKAGES>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="7fd43089-a0e3-4748-8f37-d2b49048e882">
              <SHORT-NAME>Interfaces</SHORT-NAME>
              <ELEMENTS>
                <CLIENT-SERVER-INTERFACE UUID="3ea1ec1b-6f53-45ca-b9b3-69d86eecb843">
                  <SHORT-NAME>WdgM_AliveSupervision</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>WATCH-DOG-MANAGER</SERVICE-KIND>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="fb52d883-5674-4059-bcda-f6138d3da746">
                      <SHORT-NAME>CheckpointReached</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL">Indicates to the Watchdog Manager that a Checkpoint within a Supervised Entity has been reached.</L-2>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="81087773-fa7c-4678-b28c-1914e34255ad">
                          <SHORT-NAME>CPID</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Identifier of the checkpoint within a supervised entity that has been reached.</L-2>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/WdgM_swc/DataTypes/WdgM_CheckpointIdType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/WdgM_swc/Interfaces/WdgM_AliveSupervision/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                  <POSSIBLE-ERRORS>
                    <APPLICATION-ERROR UUID="4c1e7d68-e947-464e-9a9b-58e7c5f12cec">
                      <SHORT-NAME>E_NOT_OK</SHORT-NAME>
                      <ERROR-CODE>1</ERROR-CODE>
                    </APPLICATION-ERROR>
                  </POSSIBLE-ERRORS>
                </CLIENT-SERVER-INTERFACE>
                <CLIENT-SERVER-INTERFACE UUID="3b68ce1e-5b67-4fe2-80ad-09f59c9a4dc0">
                  <SHORT-NAME>WdgM_LocalStatus</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>WATCH-DOG-MANAGER</SERVICE-KIND>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="3365bf3a-c4b9-4210-b480-73bb1a4deac5">
                      <SHORT-NAME>GetLocalStatus</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL">Returns the supervision status of a supervised entity.</L-2>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="f28882a9-f26e-4a89-808f-73c063ac0d3d">
                          <SHORT-NAME>Status</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Supervision status of the given supervised entity.</L-2>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/WdgM_swc/DataTypes/WdgM_LocalStatusType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/WdgM_swc/Interfaces/WdgM_LocalStatus/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                  <POSSIBLE-ERRORS>
                    <APPLICATION-ERROR UUID="70bfb344-a534-4788-a209-08f56eb67536">
                      <SHORT-NAME>E_NOT_OK</SHORT-NAME>
                      <ERROR-CODE>1</ERROR-CODE>
                    </APPLICATION-ERROR>
                  </POSSIBLE-ERRORS>
                </CLIENT-SERVER-INTERFACE>
                <CLIENT-SERVER-INTERFACE UUID="01665f9a-4721-4ba3-9bac-8bf299184ec2">
                  <SHORT-NAME>WdgM_General</SHORT-NAME>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>WATCH-DOG-MANAGER</SERVICE-KIND>
                  <OPERATIONS>
                    <CLIENT-SERVER-OPERATION UUID="76cbd9ae-ca41-40a1-8b51-b480650eaecc">
                      <SHORT-NAME>GetMode</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL">Returns the current mode of the Watchdog Manager.</L-2>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="b511fb4f-ca7a-4ba6-9f46-99bd88004c32">
                          <SHORT-NAME>Mode</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Current mode of the Watchdog Manager.</L-2>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/WdgM_swc/DataTypes/WdgM_ModeType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/WdgM_swc/Interfaces/WdgM_General/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="0a6f0080-1d62-477a-b9ae-af777eda883c">
                      <SHORT-NAME>GetGlobalStatus</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL">Returns the global supervision status of the Watchdog Manager.</L-2>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="174aa294-47c5-48ad-a002-7e8deed4a962">
                          <SHORT-NAME>Status</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Global supervision status of the Watchdog Manager.</L-2>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/WdgM_swc/DataTypes/WdgM_GlobalStatusType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/WdgM_swc/Interfaces/WdgM_General/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="8de3939e-c8fb-4d2e-be61-5edefab8b1c3">
                      <SHORT-NAME>GetLocalStatus</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL">Returns the supervision status of a supervised entity.</L-2>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="7ffd3db6-da66-4fef-bd49-96b2802e59db">
                          <SHORT-NAME>SEID</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Identifier of the supervised entity whose supervision status shall be returned.</L-2>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/WdgM_swc/DataTypes/WdgM_SupervisedEntityIdType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="b67b8a57-71c3-41ee-b0f0-d9faf39e9287">
                          <SHORT-NAME>Status</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Supervision status of the given supervised entity.</L-2>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/WdgM_swc/DataTypes/WdgM_LocalStatusType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/WdgM_swc/Interfaces/WdgM_General/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="2fa2efa7-9d9d-40ae-94da-bbf27e854c7c">
                      <SHORT-NAME>PerformReset</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL">Instructs the Watchdog Manager to cause a watchdog reset.</L-2>
                      </DESC>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/WdgM_swc/Interfaces/WdgM_General/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="dd4f4869-f9b7-42a5-85da-93d7f0f62e70">
                      <SHORT-NAME>SetMode</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL">Requests a new trigger mode</L-2>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="5db24a53-e0d0-4157-9500-0579132a50d4">
                          <SHORT-NAME>Mode</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Desired new trigger mode id.</L-2>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/WdgM_swc/DataTypes/WdgM_ModeType</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                        <ARGUMENT-DATA-PROTOTYPE UUID="5b6a5617-55ff-4c03-9a45-435b29e346a0">
                          <SHORT-NAME>CallerID</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Module ID of the calling module.</L-2>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</TYPE-TREF>
                          <DIRECTION>IN</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/WdgM_swc/Interfaces/WdgM_General/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                    <CLIENT-SERVER-OPERATION UUID="355b83d6-a55b-493a-9bb8-30542147fcca">
                      <SHORT-NAME>GetFirstExpiredSEID</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL">Returns the id of that supervised entity which first reached the status WDGM_LOCAL_STATUS_EXPIRED.</L-2>
                      </DESC>
                      <ARGUMENTS>
                        <ARGUMENT-DATA-PROTOTYPE UUID="2b8df2ae-e95e-4e3e-ab62-1921f869e51d">
                          <SHORT-NAME>SEID</SHORT-NAME>
                          <DESC>
                            <L-2 L="FOR-ALL">Pointer to a variable that stores the id of the supervised entity.</L-2>
                          </DESC>
                          <TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/WdgM_swc/DataTypes/WdgM_SupervisedEntityIdType</TYPE-TREF>
                          <DIRECTION>OUT</DIRECTION>
                        </ARGUMENT-DATA-PROTOTYPE>
                      </ARGUMENTS>
                      <POSSIBLE-ERROR-REFS>
                        <POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/MICROSAR/WdgM_swc/Interfaces/WdgM_General/E_NOT_OK</POSSIBLE-ERROR-REF>
                      </POSSIBLE-ERROR-REFS>
                    </CLIENT-SERVER-OPERATION>
                  </OPERATIONS>
                  <POSSIBLE-ERRORS>
                    <APPLICATION-ERROR UUID="a122415e-cca0-4345-8df0-000780f4fd02">
                      <SHORT-NAME>E_NOT_OK</SHORT-NAME>
                      <ERROR-CODE>1</ERROR-CODE>
                    </APPLICATION-ERROR>
                  </POSSIBLE-ERRORS>
                </CLIENT-SERVER-INTERFACE>
                <MODE-SWITCH-INTERFACE UUID="2a96631d-c9a7-475e-b4c5-f548581d2174">
                  <SHORT-NAME>WdgM_IndividualMode</SHORT-NAME>
                  <DESC>
                    <L-2 L="FOR-ALL">The interface WdgM_IndividualMode is used to signal the local supervision status of a single supervised entity.</L-2>
                  </DESC>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>WATCH-DOG-MANAGER</SERVICE-KIND>
                  <MODE-GROUP UUID="82c59b52-a732-437d-aa12-f3650118db63">
                    <SHORT-NAME>currentMode</SHORT-NAME>
                    <TYPE-TREF DEST="MODE-DECLARATION-GROUP">/MICROSAR/WdgM_swc/WdgM_Mode</TYPE-TREF>
                  </MODE-GROUP>
                </MODE-SWITCH-INTERFACE>
                <MODE-SWITCH-INTERFACE UUID="e5c87a74-bcfe-4247-9f60-f76e4c1c5ea8">
                  <SHORT-NAME>WdgM_GlobalMode</SHORT-NAME>
                  <DESC>
                    <L-2 L="FOR-ALL">The interface WdgM_GlobalMode is used to signal the global supervision status that is combined from all individual supervised entities.</L-2>
                  </DESC>
                  <IS-SERVICE>true</IS-SERVICE>
                  <SERVICE-KIND>WATCH-DOG-MANAGER</SERVICE-KIND>
                  <MODE-GROUP UUID="e02d9847-4211-47a0-88dc-961b06ff5932">
                    <SHORT-NAME>currentMode</SHORT-NAME>
                    <TYPE-TREF DEST="MODE-DECLARATION-GROUP">/MICROSAR/WdgM_swc/WdgM_Mode</TYPE-TREF>
                  </MODE-GROUP>
                </MODE-SWITCH-INTERFACE>
              </ELEMENTS>
            </AR-PACKAGE>
            <AR-PACKAGE UUID="766d14b5-0724-44e7-b3fc-200b1bdef9b6">
              <SHORT-NAME>ComponentTypes</SHORT-NAME>
              <ELEMENTS>
                <SERVICE-SW-COMPONENT-TYPE UUID="b12fa6b3-75b5-41b4-8f65-cf2be8deeefe">
                  <SHORT-NAME>WdgM_SystemApplication_OsCore0</SHORT-NAME>
                  <CATEGORY>SERVICE_COMPONENT</CATEGORY>
                  <PORTS>
                    <P-PORT-PROTOTYPE UUID="1fbc1248-7a76-4079-a079-327b2f1f42fb">
                      <SHORT-NAME>alive_WdgMSupervisedEntity_SAF_MON</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL">Alive-P-Port Prototype for supervised entity 'WdgMSupervisedEntity_SAF_MON' with Id '0' for Application 'SystemApplication_OsCore0'.</L-2>
                      </DESC>
                      <ADMIN-DATA>
                        <SDGS>
                          <SDG GID="DV:DerivedFromReferrables">
                            <SDG GID="DV:DerivedFromReferrablesVariant">
                              <SD GID="DV:InvariantValuesView"/>
                              <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/WdgM/WdgMGeneral/WdgMSupervisedEntity_SAF_MON</SDX-REF>
                              <SD GID="DV:DerivedFromReferrablesUUID">db548eb4-a637-4767-a574-a1ae9b5e7d4e</SD>
                            </SDG>
                          </SDG>
                        </SDGS>
                      </ADMIN-DATA>
                      <PROVIDED-INTERFACE-TREF DEST="CLIENT-SERVER-INTERFACE">/MICROSAR/WdgM_swc/Interfaces/WdgM_AliveSupervision</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="a950b462-6c04-42f2-94d6-ebb54882dd06">
                      <SHORT-NAME>localStatus_WdgMSupervisedEntity_SAF_MON</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL">P-Port Prototype for supervised entity 'WdgMSupervisedEntity_SAF_MON' with Id '0' for Application 'SystemApplication_OsCore0' to request the local status.</L-2>
                      </DESC>
                      <ADMIN-DATA>
                        <SDGS>
                          <SDG GID="DV:DerivedFromReferrables">
                            <SDG GID="DV:DerivedFromReferrablesVariant">
                              <SD GID="DV:InvariantValuesView"/>
                              <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/WdgM/WdgMGeneral/WdgMSupervisedEntity_SAF_MON</SDX-REF>
                              <SD GID="DV:DerivedFromReferrablesUUID">db548eb4-a637-4767-a574-a1ae9b5e7d4e</SD>
                            </SDG>
                          </SDG>
                        </SDGS>
                      </ADMIN-DATA>
                      <PROVIDED-INTERFACE-TREF DEST="CLIENT-SERVER-INTERFACE">/MICROSAR/WdgM_swc/Interfaces/WdgM_LocalStatus</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="3bb70c2c-fc2b-446c-bed6-ab389c3d4ef8">
                      <SHORT-NAME>general_Core0</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL">General P-Port-Prototype for core 0 (SystemApplication_OsCore0)</L-2>
                      </DESC>
                      <ADMIN-DATA>
                        <SDGS>
                          <SDG GID="DV:DerivedFromReferrables">
                            <SDG GID="DV:DerivedFromReferrablesVariant">
                              <SD GID="DV:InvariantValuesView"/>
                              <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/WdgM/WdgMConfigSet/WdgMMode</SDX-REF>
                              <SD GID="DV:DerivedFromReferrablesUUID">b2259423-b14b-4fae-a08e-161e80ee09a5</SD>
                            </SDG>
                          </SDG>
                        </SDGS>
                      </ADMIN-DATA>
                      <PROVIDED-INTERFACE-TREF DEST="CLIENT-SERVER-INTERFACE">/MICROSAR/WdgM_swc/Interfaces/WdgM_General</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                    <P-PORT-PROTOTYPE UUID="28cdbafd-**************-42eb9ab07c4e">
                      <SHORT-NAME>mode_WdgMSupervisedEntity_SAF_MON</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL">Mode port for reporting the local supervision status of WdgMSupervisedEntity_SAF_MON</L-2>
                      </DESC>
                      <ADMIN-DATA>
                        <SDGS>
                          <SDG GID="DV:DerivedFromReferrables">
                            <SDG GID="DV:DerivedFromReferrablesVariant">
                              <SD GID="DV:InvariantValuesView"/>
                              <SDX-REF DEST="ECUC-CONTAINER-VALUE">/ActiveEcuC/WdgM/WdgMGeneral/WdgMSupervisedEntity_SAF_MON</SDX-REF>
                              <SD GID="DV:DerivedFromReferrablesUUID">db548eb4-a637-4767-a574-a1ae9b5e7d4e</SD>
                            </SDG>
                          </SDG>
                        </SDGS>
                      </ADMIN-DATA>
                      <PROVIDED-COM-SPECS>
                        <MODE-SWITCH-SENDER-COM-SPEC>
                          <MODE-GROUP-REF DEST="MODE-DECLARATION-GROUP-PROTOTYPE">/MICROSAR/WdgM_swc/Interfaces/WdgM_IndividualMode/currentMode</MODE-GROUP-REF>
                        </MODE-SWITCH-SENDER-COM-SPEC>
                      </PROVIDED-COM-SPECS>
                      <PROVIDED-INTERFACE-TREF DEST="MODE-SWITCH-INTERFACE">/MICROSAR/WdgM_swc/Interfaces/WdgM_IndividualMode</PROVIDED-INTERFACE-TREF>
                    </P-PORT-PROTOTYPE>
                  </PORTS>
                  <INTERNAL-BEHAVIORS>
                    <SWC-INTERNAL-BEHAVIOR UUID="87c44ff5-4888-49fc-91cf-1f1a24c15428">
                      <SHORT-NAME>WdgM_SystemApplication_OsCore0InternalBehavior</SHORT-NAME>
                      <DATA-TYPE-MAPPING-REFS>
                        <DATA-TYPE-MAPPING-REF DEST="DATA-TYPE-MAPPING-SET">/MICROSAR/WdgM_swc/WdgMMappingSet</DATA-TYPE-MAPPING-REF>
                      </DATA-TYPE-MAPPING-REFS>
                      <EVENTS>
                        <TIMING-EVENT UUID="8e92bc43-dbb5-4372-a511-7a1a6fb65650">
                          <SHORT-NAME>Timer_WdgM_MainFunction</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/WdgM_SystemApplication_OsCore0InternalBehavior/WdgM_MainFunction</START-ON-EVENT-REF>
                          <PERIOD>0.01</PERIOD>
                        </TIMING-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="32975d37-d558-4c77-9bb8-82a33e0f02d4">
                          <SHORT-NAME>OpEventCheckpointReached_CheckpointReached_alive_WdgMSupervisedEntity_SAF_MON</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/WdgM_SystemApplication_OsCore0InternalBehavior/CheckpointReached</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/alive_WdgMSupervisedEntity_SAF_MON</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/WdgM_swc/Interfaces/WdgM_AliveSupervision/CheckpointReached</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="2f9b37b6-0be4-453c-ac94-a9df46f0def3">
                          <SHORT-NAME>OpEventGetLocalStatus_GetLocalStatus_localStatus_WdgMSupervisedEntity_SAF_MON</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/WdgM_SystemApplication_OsCore0InternalBehavior/GetLocalStatus</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/localStatus_WdgMSupervisedEntity_SAF_MON</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/WdgM_swc/Interfaces/WdgM_LocalStatus/GetLocalStatus</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="744bbc86-913c-4c84-9193-c5a361c15d68">
                          <SHORT-NAME>OpEventGetLocalStatus_GetLocalStatus_general_Core0</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/WdgM_SystemApplication_OsCore0InternalBehavior/GetLocalStatus</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/general_Core0</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/WdgM_swc/Interfaces/WdgM_General/GetLocalStatus</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="1908ecf4-d51e-4a05-b617-a771475e00a3">
                          <SHORT-NAME>OpEventGetMode_GetMode_general_Core0</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/WdgM_SystemApplication_OsCore0InternalBehavior/GetMode</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/general_Core0</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/WdgM_swc/Interfaces/WdgM_General/GetMode</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="e12a690a-8477-41d2-8cf2-5fcd6301b361">
                          <SHORT-NAME>OpEventGetGlobalStatus_GetGlobalStatus_general_Core0</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/WdgM_SystemApplication_OsCore0InternalBehavior/GetGlobalStatus</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/general_Core0</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/WdgM_swc/Interfaces/WdgM_General/GetGlobalStatus</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="a16a656d-a3eb-4299-b1e9-8b02459a78ec">
                          <SHORT-NAME>OpEventPerformReset_PerformReset_general_Core0</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/WdgM_SystemApplication_OsCore0InternalBehavior/PerformReset</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/general_Core0</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/WdgM_swc/Interfaces/WdgM_General/PerformReset</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="efe1efe8-dc8d-44db-93f9-0526e79a5a4b">
                          <SHORT-NAME>OpEventSetMode_SetMode_general_Core0</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/WdgM_SystemApplication_OsCore0InternalBehavior/SetMode</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/general_Core0</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/WdgM_swc/Interfaces/WdgM_General/SetMode</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                        <OPERATION-INVOKED-EVENT UUID="14af107f-503a-4532-9aca-a8dcf7be17cb">
                          <SHORT-NAME>OpEventGetFirstExpiredSEID_GetFirstExpiredSEID_general_Core0</SHORT-NAME>
                          <START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/WdgM_SystemApplication_OsCore0InternalBehavior/GetFirstExpiredSEID</START-ON-EVENT-REF>
                          <OPERATION-IREF>
                            <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/general_Core0</CONTEXT-P-PORT-REF>
                            <TARGET-PROVIDED-OPERATION-REF DEST="CLIENT-SERVER-OPERATION">/MICROSAR/WdgM_swc/Interfaces/WdgM_General/GetFirstExpiredSEID</TARGET-PROVIDED-OPERATION-REF>
                          </OPERATION-IREF>
                        </OPERATION-INVOKED-EVENT>
                      </EVENTS>
                      <HANDLE-TERMINATION-AND-RESTART>NO-SUPPORT</HANDLE-TERMINATION-AND-RESTART>
                      <PORT-API-OPTIONS>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-ARG-VALUES>
                            <PORT-DEFINED-ARGUMENT-VALUE>
                              <VALUE>
                                <NUMERICAL-VALUE-SPECIFICATION>
                                  <SHORT-LABEL>SEID</SHORT-LABEL>
                                  <VALUE>0</VALUE>
                                </NUMERICAL-VALUE-SPECIFICATION>
                              </VALUE>
                              <VALUE-TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/WdgM_swc/DataTypes/WdgM_SupervisedEntityIdType</VALUE-TYPE-TREF>
                            </PORT-DEFINED-ARGUMENT-VALUE>
                          </PORT-ARG-VALUES>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/alive_WdgMSupervisedEntity_SAF_MON</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-ARG-VALUES>
                            <PORT-DEFINED-ARGUMENT-VALUE>
                              <VALUE>
                                <NUMERICAL-VALUE-SPECIFICATION>
                                  <VALUE>0</VALUE>
                                </NUMERICAL-VALUE-SPECIFICATION>
                              </VALUE>
                              <VALUE-TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR/WdgM_swc/DataTypes/WdgM_SupervisedEntityIdType</VALUE-TYPE-TREF>
                            </PORT-DEFINED-ARGUMENT-VALUE>
                          </PORT-ARG-VALUES>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/localStatus_WdgMSupervisedEntity_SAF_MON</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/general_Core0</PORT-REF>
                        </PORT-API-OPTION>
                        <PORT-API-OPTION>
                          <ENABLE-TAKE-ADDRESS>true</ENABLE-TAKE-ADDRESS>
                          <INDIRECT-API>false</INDIRECT-API>
                          <PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/mode_WdgMSupervisedEntity_SAF_MON</PORT-REF>
                        </PORT-API-OPTION>
                      </PORT-API-OPTIONS>
                      <RUNNABLES>
                        <RUNNABLE-ENTITY UUID="b67320c2-a6ec-4525-95b2-19c29490fafb">
                          <SHORT-NAME>WdgM_MainFunction</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <MODE-SWITCH-POINTS>
                            <MODE-SWITCH-POINT UUID="e81c63b2-7859-4531-a9c2-c590fd8b93e8">
                              <SHORT-NAME>ModeSwitchPointWdgM_MainFunction_mode_WdgMSupervisedEntity_SAF_MON_currentMode</SHORT-NAME>
                              <MODE-GROUP-IREF>
                                <CONTEXT-P-PORT-REF DEST="P-PORT-PROTOTYPE">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/mode_WdgMSupervisedEntity_SAF_MON</CONTEXT-P-PORT-REF>
                                <TARGET-MODE-GROUP-REF DEST="MODE-DECLARATION-GROUP-PROTOTYPE">/MICROSAR/WdgM_swc/Interfaces/WdgM_IndividualMode/currentMode</TARGET-MODE-GROUP-REF>
                              </MODE-GROUP-IREF>
                            </MODE-SWITCH-POINT>
                          </MODE-SWITCH-POINTS>
                          <SYMBOL>WdgM_MainFunction</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="3ca3b505-410d-4f94-a97d-ccb3c45cf4c8">
                          <SHORT-NAME>CheckpointReached</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>WdgM_CheckpointReached</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="1b202db4-5a6d-45c5-ab95-748846cd54e9">
                          <SHORT-NAME>GetLocalStatus</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>WdgM_GetLocalStatus</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="8cb9656a-ffb3-421f-904c-e3d1b62e511e">
                          <SHORT-NAME>GetMode</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>WdgM_GetMode</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="526133f8-23a6-4355-a23f-07c0b085323b">
                          <SHORT-NAME>GetGlobalStatus</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>WdgM_GetGlobalStatus</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="6b8d30c0-a0fa-4970-ac45-8fc98a473665">
                          <SHORT-NAME>PerformReset</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>WdgM_PerformReset</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="b03d4df3-7b40-4e2d-a5be-71c6f4741444">
                          <SHORT-NAME>SetMode</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>WdgM_SetMode</SYMBOL>
                        </RUNNABLE-ENTITY>
                        <RUNNABLE-ENTITY UUID="e7a9e8c9-fe84-4645-866a-d25c882e8cba">
                          <SHORT-NAME>GetFirstExpiredSEID</SHORT-NAME>
                          <MINIMUM-START-INTERVAL>0</MINIMUM-START-INTERVAL>
                          <CAN-BE-INVOKED-CONCURRENTLY>true</CAN-BE-INVOKED-CONCURRENTLY>
                          <SYMBOL>WdgM_GetFirstExpiredSEID</SYMBOL>
                        </RUNNABLE-ENTITY>
                      </RUNNABLES>
                      <SUPPORTS-MULTIPLE-INSTANTIATION>false</SUPPORTS-MULTIPLE-INSTANTIATION>
                    </SWC-INTERNAL-BEHAVIOR>
                  </INTERNAL-BEHAVIORS>
                </SERVICE-SW-COMPONENT-TYPE>
                <SWC-IMPLEMENTATION UUID="3eb74d11-1219-4a10-b9a7-8ab7cb5e111a">
                  <SHORT-NAME>WdgM_SystemApplication_OsCore0Implementation</SHORT-NAME>
                  <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
                  <BEHAVIOR-REF DEST="SWC-INTERNAL-BEHAVIOR">/MICROSAR/WdgM_swc/ComponentTypes/WdgM_SystemApplication_OsCore0/WdgM_SystemApplication_OsCore0InternalBehavior</BEHAVIOR-REF>
                </SWC-IMPLEMENTATION>
              </ELEMENTS>
            </AR-PACKAGE>
          </AR-PACKAGES>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
