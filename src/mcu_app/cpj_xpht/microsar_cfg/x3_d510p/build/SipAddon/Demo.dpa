<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ProjectAssistant Version="5.22.35 SP1">
    <General>
        <Name>Demo</Name>
        <Version>1.0</Version>
        <Author>Demo</Author>
    </General>
    <Environment>
        <Platform>Canoeemu</Platform>
        <Derivative UUID="67FD4360-1024-407C-BF57-DBC4FE388E53">TDA4VM88</Derivative>
        <Compiler UUID="B961F280-648C-45FF-A994-700EDE3B9F78">TexasInstruments</Compiler>
        <SipIds>
            <SipId>CBD2200362_D00</SipId>
        </SipIds>
        <TargetType>Real Target</TargetType>
        <UseCases/>
        <PostBuildLoadableSupport>false</PostBuildLoadableSupport>
        <PostBuildSelectableSupport>false</PostBuildSelectableSupport>
        <ModuleSpecificDerivatives/>
        <ProjectType>
            <Type>Standard</Type>
        </ProjectType>
    </Environment>
    <Folders>
        <ECUC>.\Config\ECUC</ECUC>
        <GenData>.\..\..\src\GenData</GenData>
        <GenDataVtt>.\Appl\GenDataVtt</GenDataVtt>
        <Source>.\..\..\src\GenData</Source>
        <ServiceComponents>.\Config\ServiceComponents</ServiceComponents>
        <Logs>.\Log</Logs>
        <SIP>.\..\..\..\..\..\components\microsar\src\_AR_ThirdParty</SIP>
        <StartMenu></StartMenu>
        <ApplicationComponentFolders>
            <ApplicationComponentFolder>.\Config\ApplicationComponents</ApplicationComponentFolder>
        </ApplicationComponentFolders>
        <BswInternalBehaviour>.\Config\InternalBehavior</BswInternalBehaviour>
        <McData>.\Config\McData</McData>
        <DefinitionRestriction>.\DefRestrict</DefinitionRestriction>
        <AUTOSAR>.\Config\AUTOSAR</AUTOSAR>
    </Folders>
    <Tools>
        <DEV>C:\Program Files (x86)\Vector DaVinci Developer 4.6 (SP1)\Bin\DaVinciDEV.exe</DEV>
        <LegacyConverter Version="********"/>
        <DDM Version="*********"/>
    </Tools>
    <Input>
        <ECUEX>Config\System\SystemExtract.arxml</ECUEX>
        <Options>
            <IgnoreUuidsSystemDescriptionFiles>false</IgnoreUuidsSystemDescriptionFiles>
            <IgnoreUuidsStandardConfigurationFiles>false</IgnoreUuidsStandardConfigurationFiles>
            <GenerateUpdateReport>true</GenerateUpdateReport>
            <GenerateXmlUpdateReport>false</GenerateXmlUpdateReport>
        </Options>
    </Input>
    <References>
        <DVWorkspace>.\Config\Developer\StartApplication.dcf</DVWorkspace>
        <FlatMap>Config\System\FlatMap.arxml</FlatMap>
        <FlatECUEX>Config\System\FlatExtract.arxml</FlatECUEX>
        <OEMCommunicationExtract>Config\System\Communication.arxml</OEMCommunicationExtract>
        <EcucFileReferences/>
        <DiagECUC>Config\System\DiagnosticsDescriptionData.arxml</DiagECUC>
    </References>
    <EcucSplitter>
        <Configuration>Config\ECUC\StartApplication.ecuc.arxml</Configuration>
        <Splitter File=".\Config\ECUC\StartApplication_Com_Com_ecuc.arxml">
            <Module Name="Com"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\StartApplication_Rte_Rte_ecuc.arxml">
            <Module Name="Rte"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\StartApplication_EcuM_EcuM_ecuc.arxml">
            <Module Name="EcuM"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\StartApplication_Os_Os_ecuc.arxml">
            <Module Name="Os"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\StartApplication_PduR_PduR_ecuc.arxml">
            <Module Name="PduR"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\StartApplication_CanSM_CanSM_ecuc.arxml">
            <Module Name="CanSM"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\StartApplication_CanTp_CanTp_ecuc.arxml">
            <Module Name="CanTp"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\StartApplication_CanIf_CanIf_ecuc.arxml">
            <Module Name="CanIf"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\StartApplication_Det_Det_ecuc.arxml">
            <Module Name="Det"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\StartApplication_ComM_ComM_ecuc.arxml">
            <Module Name="ComM"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\StartApplication_EcuC_EcuC_ecuc.arxml">
            <Module Name="EcuC"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\StartApplication_BswM_BswM_ecuc.arxml">
            <Module Name="BswM"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\StartApplication_vSet_vSet_ecuc.arxml">
            <Module Name="vSet"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\StartApplication_Crc_Crc_ecuc.arxml">
            <Module Name="Crc"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\StartApplication_vLinkGen_vLinkGen_ecuc.arxml">
            <Module Name="vLinkGen"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\StartApplication_vBRS_vBRS_ecuc.arxml">
            <Module Name="vBRS"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\StartApplication_vBaseEnv_vBaseEnv_ecuc.arxml">
            <Module Name="vBaseEnv"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\StartApplication_Mcu_Mcu_ecuc.arxml">
            <Module Name="Mcu"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\StartApplication_Dem_Dem_ecuc.arxml">
            <Module Name="Dem"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\StartApplication_Dcm_Dcm_ecuc.arxml">
            <Module Name="Dcm"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Dio_Dio_ecuc.arxml">
            <Module Name="Dio"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_WdgM_WdgM_ecuc.arxml">
            <Module Name="WdgM"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_WdgIf_WdgIf_ecuc.arxml">
            <Module Name="WdgIf"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Wdg_Wdg_ecuc.arxml">
            <Module Name="Wdg"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_IoHwAb_IoHwAb_ecuc.arxml">
            <Module Name="IoHwAb"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Adc_Adc_ecuc.arxml">
            <Module Name="Adc"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Spi_Spi_ecuc.arxml">
            <Module Name="Spi"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Fls_Fls_ecuc.arxml">
            <Module Name="Fls"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Gpt_Gpt_ecuc.arxml">
            <Module Name="Gpt"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Pwm_Pwm_ecuc.arxml">
            <Module Name="Pwm"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Cdd_Ipc_Cdd_Ipc_ecuc.arxml">
            <Module Name="Cdd_Ipc"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_MemIf_MemIf_ecuc.arxml">
            <Module Name="MemIf"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Fee_Fee_ecuc.arxml">
            <Module Name="Fee"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_NvM_NvM_ecuc.arxml">
            <Module Name="NvM"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_CanTSyn_CanTSyn_ecuc.arxml">
            <Module Name="CanTSyn"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_StbM_StbM_ecuc.arxml">
            <Module Name="StbM"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_EthIf_EthIf_ecuc.arxml">
            <Module Name="EthIf"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_EthTrcv_EthTrcv_ecuc.arxml">
            <Module Name="EthTrcv"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_EthSM_EthSM_ecuc.arxml">
            <Module Name="EthSM"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_SoAd_SoAd_ecuc.arxml">
            <Module Name="SoAd"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_TcpIp_TcpIp_ecuc.arxml">
            <Module Name="TcpIp"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Xcp_Xcp_ecuc.arxml">
            <Module Name="Xcp"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\Demo_Eth_Eth_ecuc.arxml">
            <Module Name="Eth"/>
        </Splitter>
        <Splitter File=".\Config\ECUC\StartApplication_Can_Can_ecuc.arxml">
            <Module Name="Can"/>
        </Splitter>
        <OwnFolderForEachSplitter>false</OwnFolderForEachSplitter>
        <OwnFileForEachInstance>true</OwnFileForEachInstance>
    </EcucSplitter>
    <Display LastVersion="5.22.40 SP2">
        <FileSet Id="" CS="873d6fa6">
            <File Order="1" EcuInstance="XPU" Hash="a563af9572212ebb5aad5003f58a323e" FileCategory="legacy_communication_data">$(DpaProjectFolder)\Database\Davused\XPU_CCAN_X3_Matrix_V301_20241115.dbc</File>
            <File Order="2" EcuInstance="XPU" Hash="4b66333d87ea1f4124e0683573404d83" FileCategory="legacy_communication_data">$(DpaProjectFolder)\Database\Davused\XPU_ICAN_X3_Matrix_V301_20241115.dbc</File>
            <File Order="3" EcuInstance="XPU" Hash="15b2a7a4864d00ead43bba6a7c95b290" FileCategory="legacy_communication_data">$(DpaProjectFolder)\Database\Davused\XPU_OTCAN_X3_Matrix_V301_20241115.dbc</File>
            <File Order="4" EcuInstance="XPU" Hash="3a1fcb1004280a28c9eee5282866a20a" FileCategory="legacy_communication_data">$(DpaProjectFolder)\Database\Davused\XPU_SCAN1_X3_Matrix_V211_20240630.dbc</File>
            <File Order="5" EcuInstance="XPU" Hash="21f4e510bbb93528bc632c6bcd9d9717" FileCategory="legacy_communication_data">$(DpaProjectFolder)\Database\Davused\XPU_SCAN2_X3_Matrix_V211_20240630.dbc</File>
            <File Order="6" EcuInstance="XPU" Hash="cddfe9a220bfd528a4967a2bded8ad81" FileCategory="legacy_communication_data">$(DpaProjectFolder)\Database\Davused\XPU_SCAN3_X3_Matrix_V211_20240630.dbc</File>
            <Diagnostic>
                <Description Order="0" Hash="20400a9165aca2a5d065e23f011dee8d">$(DpaProjectFolder)\Database\MyEcu.cdd</Description>
                <Ecu>MyEcu</Ecu>
                <Variant>CommonDiagnostics</Variant>
                <UseLegacySignalImport>false</UseLegacySignalImport>
            </Diagnostic>
            <ComControllerMappings>
                <ComControllerMapping ClusterPath="/Cluster/CANB" DefintionPath="/MICROSAR/Can_Mpc5700Mcan/Can"/>
                <ComControllerMapping ClusterPath="/Cluster/PRI1" DefintionPath="/MICROSAR/Can_Mpc5700Mcan/Can"/>
                <ComControllerMapping ClusterPath="/Cluster/CANA" DefintionPath="/MICROSAR/Can_Mpc5700Mcan/Can"/>
                <ComControllerMapping ClusterPath="/Cluster/PRI2" DefintionPath="/MICROSAR/Can_Mpc5700Mcan/Can"/>
                <ComControllerMapping ClusterPath="/Cluster/PRI3" DefintionPath="/MICROSAR/Can_Mpc5700Mcan/Can"/>
                <ComControllerMapping ClusterPath="/Cluster/PRI4" DefintionPath="/MICROSAR/Can_Mpc5700Mcan/Can"/>
            </ComControllerMappings>
        </FileSet>
        <Merge/>
        <SelectiveUpdate Active="false" CS="ecd6502d"/>
    </Display>
    <ECUC>
        <Active RootPackageName="/ActiveEcuC/ActiveEcuC">Config\ECUC\StartApplication.ecuc.arxml</Active>
        <Derived RootPackageName="/InitialEcuC/InitialEcuC">Config\ECUC\StartApplication.ecuc.Initial.arxml</Derived>
    </ECUC>
    <PostBuildLoadable RTEDataFreezeChecksum="" CurrentConfigurationPhase="PRE_COMPILE"/>
    <DEVSettings>
        <SelectiveImport>All</SelectiveImport>
        <ObjectLocking>true</ObjectLocking>
        <ImportModePreset>true</ImportModePreset>
    </DEVSettings>
    <ToolSettings>
        <Generators>
            <Settings Name="com.vector.cfg.gen.core.genusersettings">
                <Settings Name="com.vector.cfg.gen.Tresos_proxy">
                    <Settings Name="General">
                        <Setting Value="System" Name="forcearchitecture"/>
                        <Setting Value="1648,1055,1072" Name="ignoretresosmsgids"/>
                        <Setting Value="true" Name="performanceoptimization"/>
                        <Setting Value="false" Name="tresosverification"/>
                    </Settings>
                </Settings>
            </Settings>
            <Settings Name="com.vector.cfg.gui.core.generators.ExtGenStepOrder">
                <Setting Value="INTERNAL_GENERATION" Name="Order"/>
            </Settings>
            <Settings Name="com.vector.cfg.gui.core.generators.GeneratedModules">
                <Setting Value="false" Name="Adc"/>
                <Setting Value="false" Name="BswM"/>
                <Setting Value="false" Name="Can"/>
                <Setting Value="false" Name="CanIf"/>
                <Setting Value="false" Name="CanNm"/>
                <Setting Value="false" Name="CanSM"/>
                <Setting Value="false" Name="CanTSyn"/>
                <Setting Value="false" Name="CanTp"/>
                <Setting Value="false" Name="Cdd_Ipc"/>
                <Setting Value="false" Name="Com"/>
                <Setting Value="false" Name="ComM"/>
                <Setting Value="false" Name="Crc"/>
                <Setting Value="false" Name="Dcm"/>
                <Setting Value="false" Name="Dem"/>
                <Setting Value="false" Name="Det"/>
                <Setting Value="false" Name="Dio"/>
                <Setting Value="false" Name="EcuC"/>
                <Setting Value="false" Name="EcuM"/>
                <Setting Value="false" Name="Eth"/>
                <Setting Value="false" Name="EthIf"/>
                <Setting Value="false" Name="EthSM"/>
                <Setting Value="false" Name="EthTrcv"/>
                <Setting Value="false" Name="Fee"/>
                <Setting Value="false" Name="Fls"/>
                <Setting Value="false" Name="Gpt"/>
                <Setting Value="false" Name="IoHwAb"/>
                <Setting Value="false" Name="MemIf"/>
                <Setting Value="false" Name="Nm"/>
                <Setting Value="false" Name="NvM"/>
                <Setting Value="false" Name="Os"/>
                <Setting Value="false" Name="PduR"/>
                <Setting Value="false" Name="Pwm"/>
                <Setting Value="false" Name="Rte"/>
                <Setting Value="false" Name="SoAd"/>
                <Setting Value="false" Name="Spi"/>
                <Setting Value="false" Name="StbM"/>
                <Setting Value="false" Name="TcpIp"/>
                <Setting Value="false" Name="Wdg"/>
                <Setting Value="false" Name="WdgIf"/>
                <Setting Value="false" Name="WdgM"/>
                <Setting Value="false" Name="Xcp"/>
                <Setting Value="false" Name="vBRS"/>
                <Setting Value="false" Name="vLinkGen"/>
                <Setting Value="false" Name="vSet"/>
            </Settings>
            <Settings Name="com.vector.cfg.gui.core.generators.GenerationTarget">
                <Setting Value="Real Target" Name="TargetType"/>
            </Settings>
            <Settings Name="com.vector.cfg.gui.core.generators.GeneratorsWithoutModuleDef">
                <Setting Value="false" Name="/MICROSAR/E2EPW"/>
                <Setting Value="false" Name="/MICROSAR/McDataConv"/>
            </Settings>
        </Generators>
        <Misc>
            <Settings Name="com.vector.cfg.consistency">
                <Settings Name="ACK_65ea2c58-8a74-333c-b8a9-9648dd298b92">
                    <Settings Name="DESCRIPTION">
                        <Setting Value="WDGIF10007" Name="a ResultId"/>
                        <Setting Value="Info" Name="b Severity"/>
                        <Setting Value="Acknowledged by Ryan.Xing" Name="c AcknowledgementComment"/>
                        <Setting Value="false" Name="d IsOnDemand"/>
                        <Setting Value="The value of /ActiveEcuC/WdgIf/WdgIfDevice[0:WdgIfDeviceSetMode](value=NULL_PTR) of /ActiveEcuC/WdgIf/WdgIfDevice was initally set and may be invalid now. Please check if another include file should be configured." Name="e ResultDescription"/>
                    </Settings>
                    <Settings Name="Encoded">
                        <Setting Value="rO0ABXNyAF1jb20udmVjdG9yLmNmZy5jb25zaXN0ZW5jeS5pbnRlcm5hbC5yZXN1bHRwcm92aXNp&#xA;b25pbmcuYWNrbm93bGVkZ2UuUmVzdWx0MkFja1N0b3JlJFNlcmlhbEl0ZW3ogsXzdjXw9wIACkkA&#xA;E2hhc2hlZEVyckRlc2NyQ291bnRJAAhoYXNoZWRJZEkACmhhc2hlZFRleHRaABBpc09uRGVtYW5k&#xA;UmVzdWx0SQAOcmVzdWx0SWROdW1iZXJMAAdjb21tZW50dAASTGphdmEvbGFuZy9TdHJpbmc7TAAD&#xA;bWQ1cQB+AAFMABFyZXN1bHREZXNjcmlwdGlvbnEAfgABTAAOcmVzdWx0SWRPcmlnaW5xAH4AAUwA&#xA;CHNldmVyaXR5cQB+AAF4cAAAAAEE7rlB6kb5jQAAACcXdAAZQWNrbm93bGVkZ2VkIGJ5IFJ5YW4u&#xA;WGluZ3QAIDA5MWY1MTQ1N2U2YmE0NTc4MGM3NDFkNDJhYmIwMDY3dADVVGhlIHZhbHVlIG9mIC9B&#xA;Y3RpdmVFY3VDL1dkZ0lmL1dkZ0lmRGV2aWNlWzA6V2RnSWZEZXZpY2VTZXRNb2RlXSh2YWx1ZT1O&#xA;VUxMX1BUUikgb2YgL0FjdGl2ZUVjdUMvV2RnSWYvV2RnSWZEZXZpY2Ugd2FzIGluaXRhbGx5IHNl&#xA;dCBhbmQgbWF5IGJlIGludmFsaWQgbm93LiBQbGVhc2UgY2hlY2sgaWYgYW5vdGhlciBpbmNsdWRl&#xA;IGZpbGUgc2hvdWxkIGJlIGNvbmZpZ3VyZWQudAAFV0RHSUZ0AARJbmZv" Name="AcknowledgementDataV3Encoding"/>
                    </Settings>
                </Settings>
            </Settings>
            <Settings Name="com.vector.cfg.gen.core.bswmdmigration.internal.service.BswImplVersionPersistor">
                <Settings Name="|MICROSAR|BswM">
                    <Setting Value="14.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|CanIf">
                    <Setting Value="6.25.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|CanNm">
                    <Setting Value="10.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|CanSM">
                    <Setting Value="3.0.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|CanTSyn">
                    <Setting Value="5.3.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|CanTp">
                    <Setting Value="4.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|CanTrcv_Tja1043|CanTrcv">
                    <Setting Value="4.4.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Can_Mpc5700Mcan|Can">
                    <Setting Value="5.8.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Com">
                    <Setting Value="19.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|ComM">
                    <Setting Value="13.0.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|ComXf">
                    <Setting Value="1.15.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Crc">
                    <Setting Value="6.0.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|CryIf">
                    <Setting Value="5.1.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Crypto_30_LibCv|Crypto">
                    <Setting Value="9.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Csm">
                    <Setting Value="5.1.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Dcm">
                    <Setting Value="14.5.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Dem">
                    <Setting Value="19.7.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Det">
                    <Setting Value="12.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|DiagXf">
                    <Setting Value="1.11.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|DoIP">
                    <Setting Value="9.0.2" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|E2EPW">
                    <Setting Value="1.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|E2EXf">
                    <Setting Value="1.10.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Ea">
                    <Setting Value="3.3.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|EcuC">
                    <Setting Value="1.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|EcuM">
                    <Setting Value="10.0.2" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|EthIf">
                    <Setting Value="14.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|EthSM">
                    <Setting Value="4.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|EthTrcv_Tja1100|EthTrcv">
                    <Setting Value="5.0.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Eth_Generic|Eth">
                    <Setting Value="3.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Eth_Tc3xx|Eth">
                    <Setting Value="20.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Etm">
                    <Setting Value="8.0.5" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Fee">
                    <Setting Value="8.6.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|FiM">
                    <Setting Value="8.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|IoHwAb">
                    <Setting Value="3.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|IpduM">
                    <Setting Value="9.1.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|LdCom">
                    <Setting Value="3.1.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|MemIf">
                    <Setting Value="3.4.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Nm">
                    <Setting Value="13.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|NvM">
                    <Setting Value="7.1.2" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Os">
                    <Setting Value="2.49.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|PduR">
                    <Setting Value="15.4.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Rte">
                    <Setting Value="4.23.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Sd">
                    <Setting Value="9.2.2" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|SecOC">
                    <Setting Value="10.3.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|SoAd">
                    <Setting Value="16.4.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|SomeIpTp">
                    <Setting Value="4.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|SomeIpXf">
                    <Setting Value="1.15.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|StbM">
                    <Setting Value="8.2.2" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|TcpIp">
                    <Setting Value="14.1.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|UdpNm">
                    <Setting Value="5.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|WdgIf">
                    <Setting Value="5.4.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|WdgM">
                    <Setting Value="5.5.3" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Xcp">
                    <Setting Value="6.0.1" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|Xlock">
                    <Setting Value="5.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|vBRS">
                    <Setting Value="1.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|vBaseEnv">
                    <Setting Value="1.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|vDem42">
                    <Setting Value="1.2.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|vItaSip">
                    <Setting Value="6.0.0" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|vLinkGen">
                    <Setting Value="2.1.4" Name="BswImplSwVersion"/>
                </Settings>
                <Settings Name="|MICROSAR|vSecPrim">
                    <Setting Value="4.2.0" Name="BswImplSwVersion"/>
                </Settings>
            </Settings>
            <Settings Name="com.vector.cfg.gen.core.gencore.internal.aov.AutomaticOnDemandValidationConfigurationService">
                <Setting Value="rO0ABXNyAGpjb20udmVjdG9yLmNmZy5nZW4uY29yZS5nZW5jb3JlLmludGVybmFsLmFvdi5BdXRv&#xA;bWF0aWNPbkRlbWFuZFZhbGlkYXRpb25Db25maWd1cmF0aW9uU2VydmljZSRQZXJzaXN0ZWREYXRh&#xA;pcjrmQ6cuakCAAJaAAlhY3RpdmF0ZWRMABJkZWFjdGl2YXRlZE1vZHVsZXN0ABZMamF2YS91dGls&#xA;L0NvbGxlY3Rpb247eHABc3IANmNvbS5nb29nbGUuY29tbW9uLmNvbGxlY3QuSW1tdXRhYmxlTGlz&#xA;dCRTZXJpYWxpemVkRm9ybQAAAAAAAAAAAgABWwAIZWxlbWVudHN0ABNbTGphdmEvbGFuZy9PYmpl&#xA;Y3Q7eHB1cgATW0xqYXZhLmxhbmcuT2JqZWN0O5DOWJ8QcylsAgAAeHAAAAAXc3IANWNvbS52ZWN0&#xA;b3IuY2ZnLm1vZGVsLmFjY2Vzcy5EZWZSZWYkU2VyaWFsaXphdGlvblByb3h5NnUB4bS2W7ACAAJM&#xA;AB5kZWZSZWZTdHJpbmdXaXRob3V0UGFja2FnZVBhcnR0ABJMamF2YS9sYW5nL1N0cmluZztMABlw&#xA;YWNrYWdlUGFydE9mRGVmUmVmU3RyaW5ncQB+AAl4cHQAA1B3bXQADS9USV9EUkE4MFhfSjdzcQB+&#xA;AAh0AARXZGdNdAAJL01JQ1JPU0FSc3EAfgAIdAAFRXRoU01xAH4AD3NxAH4ACHQAA0V0aHQAEy9N&#xA;SUNST1NBUi9FdGhfVGMzeHhzcQB+AAh0AAVFMkVQV3EAfgAPc3EAfgAIdAAHQ2RkX0lwY3EAfgAM&#xA;c3EAfgAIdAADV2RncQB+AAxzcQB+AAh0AANDcmNxAH4AD3NxAH4ACHQABUV0aElmcQB+AA9zcQB+&#xA;AAh0AANTcGlxAH4ADHNxAH4ACHQABEVjdUNxAH4AD3NxAH4ACHQAA0Zsc3EAfgAMc3EAfgAIdAAD&#xA;Q2FudAAZL01JQ1JPU0FSL0Nhbl9NcGM1NzAwTWNhbnNxAH4ACHQAA0dwdHEAfgAMc3EAfgAIdAAE&#xA;dlNldHEAfgAPc3EAfgAIdAAHRXRoVHJjdnQAGS9NSUNST1NBUi9FdGhUcmN2X1RqYTExMDBzcQB+&#xA;AAh0AAZJb0h3QWJxAH4AD3NxAH4ACHQABVdkZ0lmcQB+AA9zcQB+AAh0AANBZGNxAH4ADHNxAH4A&#xA;CHQAA0Rpb3EAfgAMc3EAfgAIdAADRGNtcQB+AA9zcQB+AAh0AAVNZW1JZnEAfgAPc3EAfgAIdAAE&#xA;dkJSU3EAfgAP" Name="Data"/>
            </Settings>
            <Settings Name="com.vector.cfg.gui.pse.pages.generators">
                <Settings Name="BuildVipProject"/>
                <Settings Name="CustomWorkflowExecution"/>
            </Settings>
            <Settings Name="com.vector.cfg.interop.tats"/>
            <Settings Name="com.vector.cfg.persistency.internal.folder">
                <Settings Name="TargetFolderManager"/>
            </Settings>
            <Settings Name="generationReportSettings"/>
        </Misc>
    </ToolSettings>
    <TopDownServiceConfiguration>
        <NvM>true</NvM>
    </TopDownServiceConfiguration>
    <Miscellaneous>
        <AmdGenerateDebugData>false</AmdGenerateDebugData>
        <AutomaticSyncSystemDescription>false</AutomaticSyncSystemDescription>
    </Miscellaneous>
    <SwctGeneration generationMode="SWCT_ONLY">
        <Component Name="StartApplication" GenerationEnabled="false"/>
        <Component Name="IoHwAb" GenerationEnabled="false"/>
        <Component Name="APP" GenerationEnabled="false"/>
        <Component Name="CtApSciserverHigh" GenerationEnabled="false"/>
        <Component Name="CtApSciserverLow" GenerationEnabled="false"/>
        <Component Name="BSW" GenerationEnabled="false"/>
        <Component Name="APP2" GenerationEnabled="false"/>
        <Component Name="APP3" GenerationEnabled="false"/>
        <Component Name="EVM_SWC" GenerationEnabled="false"/>
        <Component Name="APP_C1" GenerationEnabled="false"/>
        <Component Name="AEB" GenerationEnabled="false"/>
        <Component Name="Monitor" GenerationEnabled="false"/>
        <Component Name="PECP_IN" GenerationEnabled="false"/>
        <Component Name="VehInAdpr" GenerationEnabled="true"/>
        <Component Name="VehOutAdpr" GenerationEnabled="true"/>
        <Component Name="CSM" GenerationEnabled="false"/>
        <Component Name="VehCtrl" GenerationEnabled="false"/>
        <Component Name="NET" GenerationEnabled="true"/>
        <Component Name="CAN" GenerationEnabled="true"/>
        <Component Name="IpcAdpr" GenerationEnabled="true"/>
    </SwctGeneration>
</ProjectAssistant>
