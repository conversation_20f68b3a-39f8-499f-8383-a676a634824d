/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte_Type.h
 *           Config:  E:/09_IDCHIGH/J6E_demo/03_idc_mcu_for_j6e/cpj_xpht/microsar_cfg/m3xh_d500/build/SipAddon/Demo.dpa
 *        SW-C Type:  APP2
 *  Generation Time:  2024-12-04 11:21:15
 *
 *        Generator:  MICROSAR RTE Generator Version 4.23.0
 *                    RTE Core Version 1.23.0
 *          License:  CBD2000702
 *
 *      Description:  Header file containing user defined AUTOSAR types and RTE structures (Contract Phase)
 *********************************************************************************************************************/

/* double include prevention */
#ifndef RTE_TYPE_H
# define RTE_TYPE_H

# include "Rte.h"


# ifndef RTE_SUPPRESS_UNUSED_DATATYPES
/**********************************************************************************************************************
 * Unused Data type definitions
 *********************************************************************************************************************/

#  define Rte_TypeDef_Boolean
typedef boolean Boolean;

#  define Rte_TypeDef_Float
typedef float32 Float;

#  define Rte_TypeDef_SInt32
typedef sint32 SInt32;

#  define Rte_TypeDef_SInt8
typedef sint8 SInt8;

#  define Rte_TypeDef_UInt16
typedef uint16 UInt16;

#  define Rte_TypeDef_UInt32
typedef uint32 UInt32;

#  define Rte_TypeDef_UInt64
typedef uint64 UInt64;

#  define Rte_TypeDef_UInt8
typedef uint8 UInt8;

#  define Rte_TypeDef_dtRef_VOID
typedef void * dtRef_VOID;

#  define Rte_TypeDef_dtRef_const_VOID
typedef const void * dtRef_const_VOID;

#  define Rte_TypeDef_ACCDebug
typedef UInt8 ACCDebug[1636];

#  define Rte_TypeDef_AEBDebug
typedef UInt8 AEBDebug[2304];

#  define Rte_TypeDef_AESDebug
typedef UInt8 AESDebug[2304];

#  define Rte_TypeDef_Dcm_Data4095ByteType
typedef uint8 Dcm_Data4095ByteType[4095];

#  define Rte_TypeDef_DebugDataAeb
typedef uint8 DebugDataAeb[18432];

#  define Rte_TypeDef_Dem_MaxDataValueType
typedef uint8 Dem_MaxDataValueType[12];

#  define Rte_TypeDef_LssDebug
typedef UInt8 LssDebug[1024];

#  define Rte_TypeDef_PDM_CalconInitialCalibrationStatusHistory_size
typedef uint8 PDM_CalconInitialCalibrationStatusHistory_size[6];

#  define Rte_TypeDef_PDM_CameraParam_size
typedef uint8 PDM_CameraParam_size[360];

#  define Rte_TypeDef_PDM_DEM_AdminData_size
typedef uint8 PDM_DEM_AdminData_size[13];

#  define Rte_TypeDef_PDM_DEM_AgingData_size
typedef uint8 PDM_DEM_AgingData_size[104];

#  define Rte_TypeDef_PDM_DEM_PrimaryEntry_0_size
typedef uint8 PDM_DEM_PrimaryEntry_0_size[34];

#  define Rte_TypeDef_PDM_DEM_PrimaryEntry_1_size
typedef uint8 PDM_DEM_PrimaryEntry_1_size[34];

#  define Rte_TypeDef_PDM_DEM_PrimaryEntry_2_size
typedef uint8 PDM_DEM_PrimaryEntry_2_size[34];

#  define Rte_TypeDef_PDM_DEM_PrimaryEntry_3_size
typedef uint8 PDM_DEM_PrimaryEntry_3_size[34];

#  define Rte_TypeDef_PDM_DEM_PrimaryEntry_4_size
typedef uint8 PDM_DEM_PrimaryEntry_4_size[34];

#  define Rte_TypeDef_PDM_DEM_PrimaryEntry_5_size
typedef uint8 PDM_DEM_PrimaryEntry_5_size[34];

#  define Rte_TypeDef_PDM_DEM_PrimaryEntry_6_size
typedef uint8 PDM_DEM_PrimaryEntry_6_size[34];

#  define Rte_TypeDef_PDM_DEM_PrimaryEntry_7_size
typedef uint8 PDM_DEM_PrimaryEntry_7_size[34];

#  define Rte_TypeDef_PDM_DEM_PrimaryEntry_8_size
typedef uint8 PDM_DEM_PrimaryEntry_8_size[34];

#  define Rte_TypeDef_PDM_DEM_PrimaryEntry_9_size
typedef uint8 PDM_DEM_PrimaryEntry_9_size[34];

#  define Rte_TypeDef_PDM_DEM_StatusData_size
typedef uint8 PDM_DEM_StatusData_size[1810];

#  define Rte_TypeDef_PDM_Date_size
typedef uint8 PDM_Date_size[3];

#  define Rte_TypeDef_PDM_DemHistoryStatus_size
typedef uint8 PDM_DemHistoryStatus_size[225];

#  define Rte_TypeDef_PDM_ECUCalibrationDataVersionNumber_size
typedef uint8 PDM_ECUCalibrationDataVersionNumber_size[9];

#  define Rte_TypeDef_PDM_ECUProgrammingDate_size
typedef uint8 PDM_ECUProgrammingDate_size[3];

#  define Rte_TypeDef_PDM_ECUSerialNumber_size
typedef uint8 PDM_ECUSerialNumber_size[32];

#  define Rte_TypeDef_PDM_FingerPrint_size
typedef uint8 PDM_FingerPrint_size[15];

#  define Rte_TypeDef_PDM_FunctionConfiguration_size
typedef uint8 PDM_FunctionConfiguration_size[5];

#  define Rte_TypeDef_PDM_GarageCode_size
typedef uint8 PDM_GarageCode_size[12];

#  define Rte_TypeDef_PDM_ImagerCalRoiLeftSimulation_size
typedef uint8 PDM_ImagerCalRoiLeftSimulation_size[32];

#  define Rte_TypeDef_PDM_ImagerCalRoiLeft_size
typedef uint8 PDM_ImagerCalRoiLeft_size[32];

#  define Rte_TypeDef_PDM_ImagerCalRoiRightSimulation_size
typedef uint8 PDM_ImagerCalRoiRightSimulation_size[32];

#  define Rte_TypeDef_PDM_ImagerCalRoiRight_size
typedef uint8 PDM_ImagerCalRoiRight_size[32];

#  define Rte_TypeDef_PDM_InternalCoding_size
typedef uint8 PDM_InternalCoding_size[200];

#  define Rte_TypeDef_PDM_MocaInterfaceAbsoluteVelocityScale_size
typedef uint8 PDM_MocaInterfaceAbsoluteVelocityScale_size[8];

#  define Rte_TypeDef_PDM_MocaInterfaceLateralAccelerationOffset_size
typedef uint8 PDM_MocaInterfaceLateralAccelerationOffset_size[8];

#  define Rte_TypeDef_PDM_MocaInterfaceLongitudinalAccelerationOffset_size
typedef uint8 PDM_MocaInterfaceLongitudinalAccelerationOffset_size[8];

#  define Rte_TypeDef_PDM_MocaInterfaceRelativeVelocityScale_size
typedef uint8 PDM_MocaInterfaceRelativeVelocityScale_size[20];

#  define Rte_TypeDef_PDM_MocaInterfaceSteeringAngleOffset_size
typedef uint8 PDM_MocaInterfaceSteeringAngleOffset_size[8];

#  define Rte_TypeDef_PDM_MocaInterfaceVelocityDelay_size
typedef uint8 PDM_MocaInterfaceVelocityDelay_size[8];

#  define Rte_TypeDef_PDM_MocaInterfaceYawRateDelay_size
typedef uint8 PDM_MocaInterfaceYawRateDelay_size[8];

#  define Rte_TypeDef_PDM_MocaInterfaceYawRateOffset_size
typedef uint8 PDM_MocaInterfaceYawRateOffset_size[8];

#  define Rte_TypeDef_PDM_OcalDifferencesToInitialCalibration_Calcon_size
typedef uint8 PDM_OcalDifferencesToInitialCalibration_Calcon_size[32];

#  define Rte_TypeDef_PDM_OcalDifferencesToInitialCalibration_Ocal_size
typedef uint8 PDM_OcalDifferencesToInitialCalibration_Ocal_size[32];

#  define Rte_TypeDef_PDM_OcalInitialCalibration_Calcon_size
typedef uint8 PDM_OcalInitialCalibration_Calcon_size[32];

#  define Rte_TypeDef_PDM_OcalInitialCalibration_Ocal_size
typedef uint8 PDM_OcalInitialCalibration_Ocal_size[32];

#  define Rte_TypeDef_PDM_OcalInitialCalibration_Scal_size
typedef uint8 PDM_OcalInitialCalibration_Scal_size[32];

#  define Rte_TypeDef_PDM_PublicKey_size
typedef uint8 PDM_PublicKey_size[387];

#  define Rte_TypeDef_PDM_R52_CoreDump_1_size
typedef uint8 PDM_R52_CoreDump_1_size[500];

#  define Rte_TypeDef_PDM_R52_CoreDump_2_size
typedef uint8 PDM_R52_CoreDump_2_size[500];

#  define Rte_TypeDef_PDM_R52_CoreDump_3_size
typedef uint8 PDM_R52_CoreDump_3_size[500];

#  define Rte_TypeDef_PDM_R52_CoreDump_4_size
typedef uint8 PDM_R52_CoreDump_4_size[500];

#  define Rte_TypeDef_PDM_R52_CoreDump_5_size
typedef uint8 PDM_R52_CoreDump_5_size[500];

#  define Rte_TypeDef_PDM_RefreshInforField_size
typedef uint8 PDM_RefreshInforField_size[264];

#  define Rte_TypeDef_PDM_RepairShopFingerprint_size
typedef uint8 PDM_RepairShopFingerprint_size[29];

#  define Rte_TypeDef_PDM_RvScalRearExtrinsicData_size
typedef uint8 PDM_RvScalRearExtrinsicData_size[24];

#  define Rte_TypeDef_PDM_ScalStaticCalibrationDataSigTest_Calcon_size
typedef uint8 PDM_ScalStaticCalibrationDataSigTest_Calcon_size[192];

#  define Rte_TypeDef_PDM_ScalStaticCalibrationDataSigTest_Scal_size
typedef uint8 PDM_ScalStaticCalibrationDataSigTest_Scal_size[192];

#  define Rte_TypeDef_PDM_StaticCalibrationParam_size
typedef uint8 PDM_StaticCalibrationParam_size[150];

#  define Rte_TypeDef_PDM_SvScalFrontLeftExtrinsicData_size
typedef uint8 PDM_SvScalFrontLeftExtrinsicData_size[24];

#  define Rte_TypeDef_PDM_SvScalFrontRightExtrinsicData_size
typedef uint8 PDM_SvScalFrontRightExtrinsicData_size[24];

#  define Rte_TypeDef_PDM_SvScalRearLeftExtrinsicData_size
typedef uint8 PDM_SvScalRearLeftExtrinsicData_size[24];

#  define Rte_TypeDef_PDM_SvScalRearRightExtrinsicData_size
typedef uint8 PDM_SvScalRearRightExtrinsicData_size[24];

#  define Rte_TypeDef_PDM_TvScalFrontExtrinsicData_size
typedef uint8 PDM_TvScalFrontExtrinsicData_size[24];

#  define Rte_TypeDef_PDM_TvScalLeftExtrinsicData_size
typedef uint8 PDM_TvScalLeftExtrinsicData_size[24];

#  define Rte_TypeDef_PDM_TvScalRearExtrinsicData_size
typedef uint8 PDM_TvScalRearExtrinsicData_size[24];

#  define Rte_TypeDef_PDM_TvScalRightExtrinsicData_size
typedef uint8 PDM_TvScalRightExtrinsicData_size[24];

#  define Rte_TypeDef_PDM_VIN_size
typedef uint8 PDM_VIN_size[17];

#  define Rte_TypeDef_PDM_VehicleConfig_size
typedef uint8 PDM_VehicleConfig_size[8];

#  define Rte_TypeDef_PDM_VehicleManufacturerCalibrationDataVersionNumber_size
typedef uint8 PDM_VehicleManufacturerCalibrationDataVersionNumber_size[8];

#  define Rte_TypeDef_RWPulse_RollingCounters
typedef UInt8 RWPulse_RollingCounters[3];

#  define Rte_TypeDef_Rte_DT_LineParamsType_0
typedef Float Rte_DT_LineParamsType_0[4];

#  define Rte_TypeDef_objectAxArray_11
typedef Float objectAxArray_11[11];

#  define Rte_TypeDef_objectAyArray_11
typedef Float objectAyArray_11[11];

#  define Rte_TypeDef_objectDxArray_11
typedef Float objectDxArray_11[11];

#  define Rte_TypeDef_objectDyArray_11
typedef Float objectDyArray_11[11];

#  define Rte_TypeDef_objectVxArray_11
typedef Float objectVxArray_11[11];

#  define Rte_TypeDef_objectVyArray_11
typedef Float objectVyArray_11[11];

#  define Rte_TypeDef_rt_Array_Float_3
typedef Float rt_Array_Float_3[3];

#  define Rte_TypeDef_rt_Array_Float_4
typedef Float rt_Array_Float_4[4];

#  define Rte_TypeDef_AEBDiagInfoType
typedef struct
{
  Boolean FCWCarEnbl;
  Boolean FCWVruEnbl;
  UInt8 FCWSnstvtLvl;
  Boolean AEBBrakeJerkEnbl;
  Boolean AEBCarEnbl;
  Boolean AEBVruEnbl;
  Boolean AEBEbaEnbl;
  Boolean AEBFactReset;
  UInt8 AEBSysStaNVM;
  UInt8 FCWSnstvtLvlNVM;
  UInt8 FCWSysStaNVM;
} AEBDiagInfoType;

#  define Rte_TypeDef_AEBPrimTarInfo
typedef struct
{
  Float objectDxRel;
  Float objectDyRel;
  Float objectVxRel;
  Float objectVyRel;
  UInt8 objectType;
  Float objectHeadAng;
  Float objectAxRel;
  Float objectAyRel;
} AEBPrimTarInfo;

#  define Rte_TypeDef_AccControlType
typedef struct
{
  UInt8 enable;
  UInt32 method;
  Float torque;
  Float acc;
  UInt8 drvoffStop;
} AccControlType;

#  define Rte_TypeDef_AccObjDispSubTyp
typedef struct
{
  UInt8 AccDetdObjId;
  UInt8 AccDetdObjTyp;
  UInt8 AccDetdObjColr;
  Float AccDetdObjLgtDst;
  Float AccDetdObjLatDst;
  Float AccDetdObjLgtSpd;
  Float AccDetdObjLatSpd;
  Float AccDetdObjHeadingAg;
  Float AccDetdObjExistPrblty;
  UInt8 AccDetdObjSyncCtr;
  UInt8 AccDetdObjRiskLvl;
  SInt32 Reserve1;
  SInt32 Reserve2;
} AccObjDispSubTyp;

#  define Rte_TypeDef_AlcStateMsgType
typedef struct
{
  UInt8 AlcLaneChgMainSt;
  UInt8 AlcChgDir;
  Boolean ALCActvFlg;
  UInt8 AutoLaneChgLaneStsLe;
  UInt8 AutoLaneChgLaneStsRi;
  UInt8 ALCActvnDirDsp;
  UInt32 ALCDangerObjFrntId;
  UInt32 ALCDangerObjRearId;
  UInt32 timeStamp;
  UInt32 HighTimeStamp;
  float32 ALCFinalYPosn;
} AlcStateMsgType;

#  define Rte_TypeDef_BCM_CFCAN_FrP04_adt
typedef struct
{
  uint8 BCM_100ms_PDU04_CRC;
  uint8 BCM_100ms_PDU04_RC;
  uint8 BntOpenSts;
  uint8 DrvrDoorOpenSts;
  uint8 FrtPsngDoorOpenSts;
  uint8 FrtWiperMdSts;
  uint8 FrtWiperOpReq;
  uint8 FrtWiperParkPosA;
  uint8 FrtWiperSwSts;
  uint8 FrtWshrPumpA;
  uint8 LdspcOpenSts;
  uint8 LowWiperWshrFludLvlSwA;
  uint8 RLDoorOpenSts;
  uint8 RRDoorOpenSts;
  uint8 RrDoorsOpenSts;
  uint8 RrWiperMdSts;
  uint8 RrWshrPumpA;
  uint8 ScurtAlrmSts;
  uint8 ScurtAlrmTrigd;
  float32 SolarStrngLSide;
  float32 SolarStrngRSide;
  uint8 TrlrHitchSwA;
  uint8 VehLckngSta;
} BCM_CFCAN_FrP04_adt;

#  define Rte_TypeDef_BcmControlType
typedef struct
{
  UInt8 enable;
  UInt8 turn_left_light;
  UInt8 turn_right_light;
  UInt8 emergency_light;
  UInt8 brake_light;
  UInt8 high_beam;
  UInt8 low_beam;
  UInt8 horn;
  SInt32 wipe;
} BcmControlType;

#  define Rte_TypeDef_Brake_request
typedef struct
{
  Float velocity;
  Float distance;
  UInt8 BreakMode;
} Brake_request;

#  define Rte_TypeDef_CCP_025ms_ECM_PDU04_adt
typedef struct
{
  uint8 AutocTrCmddGear;
  uint8 CCP_025ms_ECM_PDU04_CRC;
  uint8 CCP_025ms_ECM_PDU04_RC;
  uint8 CCP_025ms_ECM_PDU04_Reserve01;
  uint8 CCP_025ms_ECM_PDU04_Reserve02;
  uint8 CCP_025ms_ECM_PDU04_Reserve03;
  uint8 CCP_025ms_ECM_PDU04_Reserve04;
  uint8 CCP_025ms_ECM_PDU04_Reserve05;
  float32 ClPos;
  uint8 ClPosV;
  uint8 ClStSwA;
  uint8 ClStSwAV;
  uint8 DrvrShftCtrlTrgtGear;
  uint8 TrEstdGear;
  uint8 TrEstdGearV;
  uint8 TrShftLvrPos;
  uint8 TrShftLvrPosV;
  uint8 TrShftPtrnASts;
  uint8 TrTapUpTapDwnMdSts;
} CCP_025ms_ECM_PDU04_adt;

#  define Rte_TypeDef_CmrFsPoint
typedef struct
{
  UInt8 type;
  Float long_pos;
  Float lat_pos;
} CmrFsPoint;

#  define Rte_TypeDef_DisplayControlType
typedef struct
{
  UInt8 enable;
  Float set_speed;
  UInt32 noa_status;
} DisplayControlType;

#  define Rte_TypeDef_DiversionInfo
typedef struct
{
  UInt8 valid_flag;
  Float diversion_dist;
} DiversionInfo;

#  define Rte_TypeDef_DriverSettingType
typedef struct
{
  Float SetSpeed;
  Float SetTimeGap;
} DriverSettingType;

#  define Rte_TypeDef_ECM_050ms_PDU09_adt
typedef struct
{
  uint8 ECM_050ms_PDU09_CRC;
  uint8 ECM_050ms_PDU09_RC;
  uint32 ECM_050ms_PDU09_Reserve01;
  uint8 EcoDrvngAIO;
  uint8 EcoDrvngDspStsGearShftIndSts;
  uint8 EcoDrvngDspStsRcmndFwdGear;
  uint8 ECODrvngSpdRutA;
  float32 EnToqMaxExtdRng;
  uint8 EnToqMaxExtdRngV;
  float32 EnToqMinExtdRng;
  uint8 EnToqMinExtdRngV;
} ECM_050ms_PDU09_adt;

#  define Rte_TypeDef_ECM_PTCAN_FrP00_adt
typedef struct
{
  float32 AccelActuPos;
  uint8 AccelActuPosV;
  float32 AccelEfctvPos;
  uint8 CCA;
  uint8 CCEnbd;
  uint8 ClOpenReqA;
  uint8 ECM_010ms_PDU00_CRC;
  uint8 ECM_010ms_PDU00_RC;
  uint8 ECM_010ms_PDU00_Reserve01;
  uint8 En12VoltStrMotCmddOn;
  uint8 EnCtrlrRunCrkTrmlSts;
  uint8 EnIdleCtrlA;
  uint8 EnRunA;
  float32 EnSpd;
  uint8 EnSpdSts;
  uint8 EnSpReqToTr;
  uint8 IntlckRelayAuth;
  uint8 PtBrkPdlDscrtInptSts;
  uint8 PtBrkPdlDscrtInptStsV;
  uint8 PtCrkAbotd;
  uint8 RVSEnRunning;
  uint8 RVSPtCrkAbotd;
  uint8 RVSPtRunAbotd;
} ECM_PTCAN_FrP00_adt;

#  define Rte_TypeDef_ECM_PTCAN_FrP03_adt
typedef struct
{
  uint8 ECM_020ms_PDU03_CRC;
  uint8 ECM_020ms_PDU03_RC;
  uint16 ECM_020ms_PDU03_Reserve01;
  uint8 EnASSFrngDet;
  uint8 EnASSFuelReqdOn;
  uint8 EnASSSta;
  uint8 EnASSStallDet;
  uint8 EnAutoSpNotProb;
  float32 EnToqActuExtdRng;
  uint8 EnToqActuExtdRngV;
  float32 EnToqDrvrReqdExtdRng;
  uint8 EnToqDrvrReqdExtdRngV;
  uint8 EnToqRducnFlrSts;
  uint8 PropSysA;
} ECM_PTCAN_FrP03_adt;

#  define Rte_TypeDef_ECM_PTCAN_FrP27_adt
typedef struct
{
  uint8 AccelOvrd;
  uint8 ECM_020ms_PDU27_CRC;
  uint8 ECM_020ms_PDU27_RC;
  uint32 ECM_020ms_PDU27_Reserve01;
  uint8 PtACCToqReqResp;
  uint8 PtAPAToqReqResp;
  uint8 SLIFSts_ECM;
  uint8 SLIFWrngStsResp;
  uint8 SpdAstMdECM;
  uint8 SpdAstSysStsECM;
  float32 SpdAstSysTrgtSpd;
  uint8 TrgtSpdSrcSts;
  uint8 TrWarmUpMdReq;
} ECM_PTCAN_FrP27_adt;

#  define Rte_TypeDef_EPS_010ms_PDU00_adt
typedef struct
{
  uint8 EPS_010ms_PDU00_CRC;
  uint8 EPS_010ms_PDU00_RC;
  uint32 EPS_010ms_PDU00_Reserve01;
  float32 StrgWhlAng;
  float32 StrgWhlAngGrd;
  uint8 StrgWhlAngSnsrCalSts;
  uint8 StrgWhlAngSnsrFlt;
  uint8 StrgWhlAngSnsrInid;
  uint8 StrgWhlAngSnsrMultCapb;
  uint8 StrgWhlAngV;
} EPS_010ms_PDU00_adt;

#  define Rte_TypeDef_EPS_010ms_PDU03_adt
typedef struct
{
  float32 ActuPnonAng;
  uint8 ActuPnonAngV;
  float32 ActuRoadWhlAng;
  uint8 ActuRoadWhlAngV;
  uint8 ChLKAAngReqResp;
  uint8 ChLKAHptResp;
  uint8 ChLKAToqReqResp;
  uint8 ChLtrlCtrlInhRsn;
  uint8 EPS_010ms_PDU03_CRC;
  uint8 EPS_010ms_PDU03_RC;
  uint8 EPS_010ms_PDU03_Reserve01;
} EPS_010ms_PDU03_adt;

#  define Rte_TypeDef_EPS_010ms_PDU04_adt
typedef struct
{
  float32 ADASStrgDlvrdToq;
  uint8 ADASStrgDlvrdToqV;
  uint8 EPS_010ms_PDU04_CRC;
  uint8 EPS_010ms_PDU04_RC;
  uint8 EPS_010ms_PDU04_Reserve01;
  uint8 EPSBrkJrkLvReq;
  float32 EPSToqValAldMax;
  uint8 EPSToqValAldMaxV;
  float32 EPSToqValAldMin;
  uint8 EPSToqValAldMinV;
  float32 FrntWhlStrgMotToq;
  uint8 FrntWhlStrgMotToqV;
} EPS_010ms_PDU04_adt;

#  define Rte_TypeDef_EPS_010ms_PDU07_adt
typedef struct
{
  float32 ActuRoadWhlAngGrd;
  uint8 ActuRoadWhlAngGrdV;
  uint8 ChToqInhRsn;
  float32 DrvrStrgDlvrdToq;
  uint8 DrvrStrgDlvrdToqV;
  uint8 EPS_010ms_PDU07_CRC;
  uint8 EPS_010ms_PDU07_RC;
  uint8 EPS_010ms_PDU07_Reserve01;
  uint8 EPSCurntDrvMd;
  uint8 EPSDrvngMdCtrlInh;
  uint8 EPSHFLmtrSts;
  uint8 EPSHFMontngResultSts;
  uint8 StrgCDMSSetngDspCmd;
  uint8 StrgCustSetngDspCmd;
} EPS_010ms_PDU07_adt;

#  define Rte_TypeDef_ESSFlag
typedef struct
{
  Float ESS_Angle_Req;
  UInt8 ESS_Control_flag;
  UInt8 Hazard_light_Req;
  UInt8 ProtocolMajorVersion;
  UInt8 ProtocolMinorVersion;
  UInt8 RollingCounter_ESS_Control;
  UInt16 CRCChecksum_ESS_Control;
  UInt8 ESS_debug_status;
  UInt8 ESS_Message_info;
  UInt8 ESS_Function_Status;
  UInt16 ESS_Obj_ID;
  UInt8 ESS_Obj_Type;
  float64 ESS_Obj_TTC;
  float64 C0;
  float64 C1;
  float64 C2;
  float64 C3;
  Float EndY;
  float64 C4;
  float64 C5;
  Float EndX;
} ESSFlag;

#  define Rte_TypeDef_FDR_020ms_PDU05_adt
typedef struct
{
  boolean ACCAccReqSts;
  float32 ACCAccReqVal;
  uint8 ACCGoReq;
  uint8 ACCSdslReq;
  uint8 ACCSysFltSts;
  uint8 ACCSysFltSts_SCS;
  uint8 ACCSysSts;
  uint8 FDR_020ms_PDU05_CRC;
  uint8 FDR_020ms_PDU05_RC;
  uint32 FDR_020ms_PDU05_Reserve01;
} FDR_020ms_PDU05_adt;

#  define Rte_TypeDef_FDR_020ms_PDU09_adt
typedef struct
{
  uint8 ACCToqReqSts;
  float32 ACCToqReqVal;
  uint8 FDR_020ms_PDU09_CRC;
  uint8 FDR_020ms_PDU09_RC;
  uint8 FDR_020ms_PDU09_Reserve01;
  uint16 FDR_020ms_PDU09_Reserve02;
  uint32 FDR_020ms_PDU09_Reserve03;
} FDR_020ms_PDU09_adt;

#  define Rte_TypeDef_FDR_020ms_PDU22_adt
typedef struct
{
  boolean ACCOtptShaftTotToqReqSts;
  float32 ACCOtptShaftTotToqReqVal;
  uint8 FDR_020ms_PDU22_CRC;
  uint8 FDR_020ms_PDU22_RC;
  uint8 FDR_020ms_PDU22_Reserve01;
  uint32 FDR_020ms_PDU22_Reserve02;
  uint16 FDR_020ms_PDU22_Reserve03;
} FDR_020ms_PDU22_adt;

#  define Rte_TypeDef_FVCM_010ms_PDU07_adt
typedef struct
{
  float32 EPSToqAppdCoefnt;
  uint8 FVCM_010ms_PDU07_CRC;
  uint8 FVCM_010ms_PDU07_RC;
  uint8 FVCM_010ms_PDU07_Reserve01;
  uint8 FVCM_010ms_PDU07_Reserve02;
  uint8 FVCM_010ms_PDU07_Reserve03;
  float32 LKAReqAng;
  uint8 LKAReqAngSts;
  float32 LKAReqToq;
  uint8 LKAReqToqSts;
} FVCM_010ms_PDU07_adt;

#  define Rte_TypeDef_GearControlType
typedef struct
{
  UInt8 enable;
  UInt8 gear;
} GearControlType;

#  define Rte_TypeDef_HbOdo_OdoType
typedef struct
{
  Float OdoVehSpd_f32;
  Float OdoVehAccLgt_f32;
  Float OdoVehAccLat_f32;
  Float OdoVehYawRate_f32;
  Boolean OdoVehSpdInvld_b;
  Boolean OdoVehAccLgtInvld_b;
  Boolean OdoVehAccLatInvld_b;
  Boolean OdoVehYawRateInvld_b;
  UInt32 OdoTimeStamp_u32;
} HbOdo_OdoType;

#  define Rte_TypeDef_HeaderType
typedef struct
{
  UInt32 seq;
  UInt32 ts_high;
  UInt32 ts_low;
} HeaderType;

#  define Rte_TypeDef_IHBCInfoTyp
typedef struct
{
  Boolean IHBC_Decision;
} IHBCInfoTyp;

#  define Rte_TypeDef_J5AEBFlag
typedef struct
{
  UInt8 AEB_Protocol_Version;
  UInt32 AEB_Vision_frame_ID;
  UInt32 AEB_CRC;
  Float AEB_Brake_Decel_Req;
  UInt32 FCW_Supp_Reason;
  UInt32 AEB_Supp_Reason;
  UInt8 LatentWarn;
  UInt8 AEB_Obj_ID_LatentWarn;
  UInt8 AEB_Obj_Type_LatentWarn;
  float64 AEB_Obj_TTC_LatentWarn;
  UInt8 AEB_PreWarn;
  UInt8 AEB_Obj_ID_PreWarn;
  UInt8 AEB_Obj_Type_PreWarn;
  float64 AEB_Obj_TTC_PreWarn;
  UInt8 AEB_Prefill;
  UInt8 AEB_Obj_ID_Prefill;
  UInt8 AEB_Obj_Type_Prefill;
  float64 AEB_Obj_TTC_Prefill;
  UInt8 AEB_Jerk;
  UInt8 AEB_Obj_ID_Jerk;
  UInt8 AEB_Obj_Type_Jerk;
  float64 AEB_Obj_TTC_Jerk;
  UInt8 AEB_PartialBrake;
  UInt8 AEB_Obj_ID_PartialBrake;
  UInt8 AEB_Obj_Type_PartialBrake;
  float64 AEB_Obj_TTC_PartialBrake;
  UInt8 AEB_FullBrake;
  UInt8 AEB_Obj_ID_FullBrake;
  UInt8 AEB_Obj_Type_FullBrake;
  float64 AEB_Obj_TTC_FullBrake;
  UInt8 AEB_EBA;
  UInt8 AEB_Obj_ID_EBA;
  UInt8 AEB_Obj_Type_EBA;
  float64 AEB_Obj_TTC_EBA;
  UInt8 AEB_Hold;
  UInt8 AEB_Obj_ID_Hold;
  UInt8 AEB_Obj_Type_Hold;
  float64 AEB_Obj_TTC_Hold;
} J5AEBFlag;

#  define Rte_TypeDef_KeyObstacleIndexType
typedef struct
{
  SInt32 index;
  UInt32 attribute;
  SInt32 priority;
} KeyObstacleIndexType;

#  define Rte_TypeDef_LADS_020ms_PDU04_adt
typedef struct
{
  uint8 APAEPBOPReq;
  uint8 APASCSBrkFn;
  uint8 APASCSBrkFnMd;
  uint8 APASCSDrvrReqSts;
  uint8 APASCSParkngPrflReq;
  uint64 APASCSParkngTrgtDist;
  float32 APASCSParkngVelMax;
  uint8 APASCSReqSts;
  uint8 LADS_020ms_PDU04_CRC;
  uint8 LADS_020ms_PDU04_RC;
  uint8 LADS_020ms_PDU04_Reserve01;
  uint32 LADS_020ms_PDU04_Reserve02;
  uint8 LADS_020ms_PDU04_Reserve03;
} LADS_020ms_PDU04_adt;

#  define Rte_TypeDef_LADS_020ms_PDU07_adt
typedef struct
{
  uint8 APACmdEPSSts;
  uint8 APAF;
  uint8 APAReq;
  float32 APATrgtEPSStrgWhlAng;
  uint8 LADS_020ms_PDU07_CRC;
  uint8 LADS_020ms_PDU07_RC;
  uint8 LADS_020ms_PDU07_Reserve01;
  uint8 LADS_020ms_PDU07_Reserve02;
  uint32 LADS_020ms_PDU07_Reserve03;
  uint8 LADS_020ms_PDU07_Reserve04;
} LADS_020ms_PDU07_adt;

#  define Rte_TypeDef_LADS_020ms_PDU12_adt
typedef struct
{
  uint8 APAInfoDispReq_LADS;
  uint8 APAMd_LADS;
  uint8 APAModeSts;
  float32 CmdVehSped;
  uint8 LADS_020ms_PDU12_CRC;
  uint8 LADS_020ms_PDU12_RC;
  uint8 LADS_020ms_PDU12_Reserve02;
  uint8 LADS_020ms_PDU12_Reserve03;
  uint8 LADSAFnInd;
  uint16 MaxTorqLimit;
  uint16 MinTorqLimit;
} LADS_020ms_PDU12_adt;

#  define Rte_TypeDef_LaneMkrTyp
typedef struct
{
  Float ConCoeff;
  Float FirstCoeff;
  Float SecCoeff;
  Float ThrdCoeff;
  Float LaneStrtRng;
  Float LaneEndRng;
  UInt8 LaneMkrTyp;
  Float LaneConf;
  UInt8 LaneQly;
  Float LaneMkrWidth;
  UInt8 LaneMkrColor;
  UInt16 LaneMkrIds;
  UInt8 LaneCrossing;
  UInt8 LaneMeaStatus;
  UInt8 ObstacleFlag;
  UInt8 LaneExtraNearPointType;
  UInt8 LaneExtraFarPointType;
  Float LaneExtraFarPointPosX;
  Float LaneExtraFarPointPosY;
  Float LaneExtraNearPointPosX;
  Float LaneExtraNearPointPosY;
  UInt8 LaneParsingConfidence;
} LaneMkrTyp;

#  define Rte_TypeDef_LaneSpdLmtValType
typedef struct
{
  Float s;
  Float max_v;
} LaneSpdLmtValType;

#  define Rte_TypeDef_LineBorderTyp
typedef struct
{
  Float LineOffs;
  Float LineHdgAg;
  Float LineCrvt;
  Float LineCrvtRate;
  Float StrtRng;
  Float ViewRng;
  UInt8 LineTyp;
  Boolean LineQly;
  Float ExistProblty;
  Float LineWidth;
  UInt8 LineColor;
  UInt16 LineIds;
} LineBorderTyp;

#  define Rte_TypeDef_LssFctDiagType
typedef struct
{
  Boolean ElkReDiagEnbl;
  Boolean ElkOcDiagEnbl;
  Boolean ElkOvDiagEnbl;
  Boolean LdwOvDiagEnbl;
  Boolean LdpOvDiagEnbl;
} LssFctDiagType;

#  define Rte_TypeDef_LssKeyObj
typedef struct
{
  UInt16 ID;
  UInt32 Age;
  UInt8 MotionStatus;
  UInt8 MeasuringStatus;
  UInt8 Type;
  UInt8 SubType;
  UInt8 PedSubType;
  float64 TTC;
  Float PosX;
  Float PosY;
  Float VelX;
  Float VelY;
  Float AccX;
} LssKeyObj;

#  define Rte_TypeDef_NET_SIG_BOOL_ST
typedef struct
{
  uint8 update_ub;
  uint8 status_ub;
  boolean value_b;
} NET_SIG_BOOL_ST;

#  define Rte_TypeDef_NET_SIG_SB_ST
typedef struct
{
  uint8 update_ub;
  uint8 status_ub;
  sint8 value_sb;
} NET_SIG_SB_ST;

#  define Rte_TypeDef_NET_SIG_SL_ST
typedef struct
{
  uint8 update_ub;
  uint8 status_ub;
  sint32 value_sl;
} NET_SIG_SL_ST;

#  define Rte_TypeDef_NET_SIG_STATUS_ST
typedef struct
{
  boolean initial_b;
  boolean counterError_b;
  boolean checksumError_b;
  boolean signalInvalid_b;
  boolean signalTimeOut_b;
} NET_SIG_STATUS_ST;

#  define Rte_TypeDef_NET_SIG_SW_ST
typedef struct
{
  uint8 update_ub;
  uint8 status_ub;
  sint16 value_sw;
} NET_SIG_SW_ST;

#  define Rte_TypeDef_NET_SIG_UB_ST
typedef struct
{
  uint8 update_ub;
  uint8 status_ub;
  uint8 value_ub;
} NET_SIG_UB_ST;

#  define Rte_TypeDef_NET_SIG_ULL_ST
typedef struct
{
  uint8 update_ub;
  uint8 status_ub;
  uint64 value_ull;
} NET_SIG_ULL_ST;

#  define Rte_TypeDef_NET_SIG_UL_ST
typedef struct
{
  uint8 update_ub;
  uint8 status_ub;
  uint32 value_ul;
} NET_SIG_UL_ST;

#  define Rte_TypeDef_NET_SIG_UW_ST
typedef struct
{
  uint8 update_ub;
  uint8 status_ub;
  uint16 value_uw;
} NET_SIG_UW_ST;

#  define Rte_TypeDef_ObstacleType
typedef struct
{
  UInt16 ID;
  SInt32 VsnObjIdx_s32;
  SInt32 VsnObjOutIdx_s32;
  UInt32 TimeStamp;
  UInt16 AgeCntr;
  UInt16 LifeCntr;
  UInt8 Conf;
  Float ObstacleProb;
  Float Width;
  Float Length;
  Float Height;
  UInt8 TrackStatus;
  UInt32 UpdatedSensor;
  UInt8 CutInFlag;
  Float CutInDist;
  Float CutInSpd;
  Boolean CutInSpdValid;
  Float PosnLgt;
  Float PosnLat;
  Float SpdLgt;
  Float SpdLat;
  Float AccLgt;
  Float AccLat;
  Float Heading;
  Float YawRate;
  UInt8 ObjType;
  UInt8 SubType;
  UInt8 MotionType;
  UInt8 MotionCategory;
  UInt8 VisLaneAssign;
  UInt8 PosDirectionAssign;
  UInt16 RefPoint;
} ObstacleType;

#  define Rte_TypeDef_RFSDA_020ms_PDU00_adt
typedef struct
{
  uint8 FCRSysSts;
  uint8 FCTASysFltSts;
  uint8 FCTBPrflReq;
  float32 FCTBReqDcl;
  uint8 FCTBReqDclSts;
  uint8 FCTBReqDclV;
  uint8 FCTBSysFltSts;
  uint8 FCTSysSelSts;
  uint8 FrtCrnRdrCalSts;
  uint8 LFCTAWrnng;
  uint8 RFCTAWrnng;
  uint8 RFSDA_020ms_PDU00_CRC;
  uint8 RFSDA_020ms_PDU00_RC;
  uint8 RFSDA_020ms_PDU00_Reserve01;
  uint16 RFSDA_020ms_PDU00_Reserve03;
} RFSDA_020ms_PDU00_adt;

#  define Rte_TypeDef_SCS_020ms_PDU05_adt
typedef struct
{
  uint8 LDrvnWhlRotlDircn;
  uint8 RDrvnWhlRotlDircn;
  uint8 SCS_020ms_PDU05_CRC;
  uint8 SCS_020ms_PDU05_RC;
  uint32 SCS_020ms_PDU05_Reserve01;
  float32 WhlGndVelLDrvn;
  uint8 WhlGndVelLDrvnV;
  float32 WhlGndVelRDrvn;
  uint8 WhlGndVelRDrvnV;
} SCS_020ms_PDU05_adt;

#  define Rte_TypeDef_SCS_020ms_PDU06_adt
typedef struct
{
  uint8 BrkDiscTemSts;
  uint8 BrkFludLvlLow;
  uint8 BrkFludLvlLowV;
  float32 BrkPdlDrvrAppdPrs;
  uint8 BrkPdlDrvrAppdPrsDet;
  uint8 BrkPdlDrvrAppdPrsDetV;
  uint8 BrkPdlDrvrAppdPrsV;
  float32 ChSysTotBrkAxleToqVal;
  uint8 ChSysTotBrkAxleToqValV;
  uint8 SCS_020ms_PDU06_CRC;
  uint8 SCS_020ms_PDU06_RC;
  uint16 SCS_020ms_PDU06_Reserve01;
  uint8 VehSdslSts;
  uint8 WhlBrkPrsSts;
  float32 WhlRghRoad;
  uint8 WhlRghRoadV;
} SCS_020ms_PDU06_adt;

#  define Rte_TypeDef_SCS_020ms_PDU08_adt
typedef struct
{
  uint8 SCS_020ms_PDU08_CRC;
  uint8 SCS_020ms_PDU08_RC;
  uint16 SCS_020ms_PDU08_Reserve01;
  uint8 SCS_020ms_PDU08_Reserve02;
  float32 VehDynYawRate;
  uint8 VehDynYawRateV;
  float32 VSELatAcc;
  uint8 VSELatAccV;
  float32 VSELongtAcc;
  uint8 VSELongtAccV;
} SCS_020ms_PDU08_adt;

#  define Rte_TypeDef_SCS_020ms_PDU15_adt
typedef struct
{
  uint8 SCS_020ms_PDU15_CRC;
  uint8 SCS_020ms_PDU15_RC;
  uint8 SCS_020ms_PDU15_Reserve01;
  float32 VehSpdAvg;
  float32 VehSpdAvgDrvn;
  uint8 VehSpdAvgDrvnSrc;
  uint8 VehSpdAvgDrvnV;
  float32 VehSpdAvgNonDrvn;
  uint8 VehSpdAvgNonDrvnV;
  uint8 VehSpdAvgV;
} SCS_020ms_PDU15_adt;

#  define Rte_TypeDef_SCS_020ms_PDU16_adt
typedef struct
{
  uint8 LNonDrvnWhlRotlDircn;
  uint8 RNonDrvnWhlRotlDircn;
  uint8 SCS_020ms_PDU16_CRC;
  uint8 SCS_020ms_PDU16_RC;
  uint32 SCS_020ms_PDU16_Reserve01;
  float32 WhlGndVelLNonDrvn;
  uint8 WhlGndVelLNonDrvnV;
  float32 WhlGndVelRNonDrvn;
  uint8 WhlGndVelRNonDrvnV;
} SCS_020ms_PDU16_adt;

#  define Rte_TypeDef_SCS_020ms_PDU22_adt
typedef struct
{
  uint8 APAEnToqReqSts;
  float32 APAEnToqReqVal;
  uint8 SCS_020ms_PDU22_CRC;
  uint8 SCS_020ms_PDU22_RC;
  uint8 SCS_020ms_PDU22_Reserve01;
  uint16 SCS_020ms_PDU22_Reserve02;
  uint32 SCS_020ms_PDU22_Reserve03;
} SCS_020ms_PDU22_adt;

#  define Rte_TypeDef_SG_CR_RL_ObjID01
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_1;
  uint8 CR_RL_Obj_MsgCounter_1;
  float32 CR_RL_ObjAccelX_1;
  float32 CR_RL_ObjAccelY_1;
  float32 CR_RL_ObjDistX_1;
  float32 CR_RL_ObjDistY_1;
  float32 CR_RL_ObjExistProb_1;
  uint8 CR_RL_ObjID_1;
  float32 CR_RL_ObjObstacleProb_1;
  float32 CR_RL_ObjRelVelX_1;
  float32 CR_RL_ObjRelVelY_1;
} SG_CR_RL_ObjID01;

#  define Rte_TypeDef_SG_CR_RL_ObjID02
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_2;
  uint8 CR_RL_Obj_MsgCounter_2;
  float32 CR_RL_ObjAccelX_2;
  float32 CR_RL_ObjAccelY_2;
  float32 CR_RL_ObjDistX_2;
  float32 CR_RL_ObjDistY_2;
  float32 CR_RL_ObjExistProb_2;
  uint8 CR_RL_ObjID_2;
  float32 CR_RL_ObjObstacleProb_2;
  float32 CR_RL_ObjRelVelX_2;
  float32 CR_RL_ObjRelVelY_2;
} SG_CR_RL_ObjID02;

#  define Rte_TypeDef_SG_CR_RL_ObjID03
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_3;
  uint8 CR_RL_Obj_MsgCounter_3;
  float32 CR_RL_ObjAccelX_3;
  float32 CR_RL_ObjAccelY_3;
  float32 CR_RL_ObjDistX_3;
  float32 CR_RL_ObjDistXStd_1;
  float32 CR_RL_ObjDistXStd_2;
  float32 CR_RL_ObjDistXStd_3;
  float32 CR_RL_ObjDistY_3;
  float32 CR_RL_ObjDistYStd_1;
  float32 CR_RL_ObjDistYStd_2;
  float32 CR_RL_ObjDistYStd_3;
  float32 CR_RL_ObjExistProb_3;
  uint8 CR_RL_ObjID_3;
  float32 CR_RL_ObjObstacleProb_3;
  float32 CR_RL_ObjRAccelXStd_1;
  float32 CR_RL_ObjRAccelXStd_2;
  float32 CR_RL_ObjRAccelXStd_3;
  float32 CR_RL_ObjRAccelYStd_1;
  float32 CR_RL_ObjRAccelYStd_2;
  float32 CR_RL_ObjRAccelYStd_3;
  float32 CR_RL_ObjRelVelX_3;
  float32 CR_RL_ObjRelVelXStd_1;
  float32 CR_RL_ObjRelVelXStd_2;
  float32 CR_RL_ObjRelVelXStd_3;
  float32 CR_RL_ObjRelVelY_3;
  float32 CR_RL_ObjRelVelYStd_1;
  float32 CR_RL_ObjRelVelYStd_2;
  float32 CR_RL_ObjRelVelYStd_3;
} SG_CR_RL_ObjID03;

#  define Rte_TypeDef_SG_CR_RL_ObjID04
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_4;
  uint8 CR_RL_Obj_MsgCounter_4;
  float32 CR_RL_ObjAccelX_4;
  float32 CR_RL_ObjAccelY_4;
  float32 CR_RL_ObjDistX_4;
  float32 CR_RL_ObjDistY_4;
  float32 CR_RL_ObjExistProb_4;
  uint8 CR_RL_ObjID_4;
  float32 CR_RL_ObjObstacleProb_4;
  float32 CR_RL_ObjRelVelX_4;
  float32 CR_RL_ObjRelVelY_4;
} SG_CR_RL_ObjID04;

#  define Rte_TypeDef_SG_CR_RL_ObjID05
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_5;
  uint8 CR_RL_Obj_MsgCounter_5;
  float32 CR_RL_ObjAccelX_5;
  float32 CR_RL_ObjAccelY_5;
  float32 CR_RL_ObjDistX_5;
  float32 CR_RL_ObjDistY_5;
  float32 CR_RL_ObjExistProb_5;
  uint8 CR_RL_ObjID_5;
  float32 CR_RL_ObjObstacleProb_5;
  float32 CR_RL_ObjRelVelX_5;
  float32 CR_RL_ObjRelVelY_5;
} SG_CR_RL_ObjID05;

#  define Rte_TypeDef_SG_CR_RL_ObjID06
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_6;
  uint8 CR_RL_Obj_MsgCounter_6;
  float32 CR_RL_ObjAccelX_6;
  float32 CR_RL_ObjAccelY_6;
  float32 CR_RL_ObjDistX_6;
  float32 CR_RL_ObjDistXStd_4;
  float32 CR_RL_ObjDistXStd_5;
  float32 CR_RL_ObjDistXStd_6;
  float32 CR_RL_ObjDistY_6;
  float32 CR_RL_ObjDistYStd_4;
  float32 CR_RL_ObjDistYStd_5;
  float32 CR_RL_ObjDistYStd_6;
  float32 CR_RL_ObjExistProb_6;
  uint8 CR_RL_ObjID_6;
  float32 CR_RL_ObjObstacleProb_6;
  float32 CR_RL_ObjRAccelXStd_4;
  float32 CR_RL_ObjRAccelXStd_5;
  float32 CR_RL_ObjRAccelXStd_6;
  float32 CR_RL_ObjRAccelYStd_4;
  float32 CR_RL_ObjRAccelYStd_5;
  float32 CR_RL_ObjRAccelYStd_6;
  float32 CR_RL_ObjRelVelX_6;
  float32 CR_RL_ObjRelVelXStd_4;
  float32 CR_RL_ObjRelVelXStd_5;
  float32 CR_RL_ObjRelVelXStd_6;
  float32 CR_RL_ObjRelVelY_6;
  float32 CR_RL_ObjRelVelYStd_4;
  float32 CR_RL_ObjRelVelYStd_5;
  float32 CR_RL_ObjRelVelYStd_6;
} SG_CR_RL_ObjID06;

#  define Rte_TypeDef_SG_CR_RL_ObjID07
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_7;
  uint8 CR_RL_Obj_MsgCounter_7;
  float32 CR_RL_ObjAccelX_7;
  float32 CR_RL_ObjAccelY_7;
  float32 CR_RL_ObjDistX_7;
  float32 CR_RL_ObjDistY_7;
  float32 CR_RL_ObjExistProb_7;
  uint8 CR_RL_ObjID_7;
  float32 CR_RL_ObjObstacleProb_7;
  float32 CR_RL_ObjRelVelX_7;
  float32 CR_RL_ObjRelVelY_7;
} SG_CR_RL_ObjID07;

#  define Rte_TypeDef_SG_CR_RL_ObjID08
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_8;
  uint8 CR_RL_Obj_MsgCounter_8;
  float32 CR_RL_ObjAccelX_8;
  float32 CR_RL_ObjAccelY_8;
  float32 CR_RL_ObjDistX_8;
  float32 CR_RL_ObjDistY_8;
  float32 CR_RL_ObjExistProb_8;
  uint8 CR_RL_ObjID_8;
  float32 CR_RL_ObjObstacleProb_8;
  float32 CR_RL_ObjRelVelX_8;
  float32 CR_RL_ObjRelVelY_8;
} SG_CR_RL_ObjID08;

#  define Rte_TypeDef_SG_CR_RL_ObjID09
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_9;
  uint8 CR_RL_Obj_MsgCounter_9;
  float32 CR_RL_ObjAccelX_9;
  float32 CR_RL_ObjAccelY_9;
  float32 CR_RL_ObjDistX_9;
  float32 CR_RL_ObjDistXStd_7;
  float32 CR_RL_ObjDistXStd_8;
  float32 CR_RL_ObjDistXStd_9;
  float32 CR_RL_ObjDistY_9;
  float32 CR_RL_ObjDistYStd_7;
  float32 CR_RL_ObjDistYStd_8;
  float32 CR_RL_ObjDistYStd_9;
  float32 CR_RL_ObjExistProb_9;
  uint8 CR_RL_ObjID_9;
  float32 CR_RL_ObjObstacleProb_9;
  float32 CR_RL_ObjRAccelXStd_7;
  float32 CR_RL_ObjRAccelXStd_8;
  float32 CR_RL_ObjRAccelXStd_9;
  float32 CR_RL_ObjRAccelYStd_7;
  float32 CR_RL_ObjRAccelYStd_8;
  float32 CR_RL_ObjRAccelYStd_9;
  float32 CR_RL_ObjRelVelX_9;
  float32 CR_RL_ObjRelVelXStd_7;
  float32 CR_RL_ObjRelVelXStd_8;
  float32 CR_RL_ObjRelVelXStd_9;
  float32 CR_RL_ObjRelVelY_9;
  float32 CR_RL_ObjRelVelYStd_7;
  float32 CR_RL_ObjRelVelYStd_8;
  float32 CR_RL_ObjRelVelYStd_9;
} SG_CR_RL_ObjID09;

#  define Rte_TypeDef_SG_CR_RL_ObjID10
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_10;
  uint8 CR_RL_Obj_MsgCounter_10;
  float32 CR_RL_ObjAccelX_10;
  float32 CR_RL_ObjAccelY_10;
  float32 CR_RL_ObjDistX_10;
  float32 CR_RL_ObjDistY_10;
  float32 CR_RL_ObjExistProb_10;
  uint8 CR_RL_ObjID_10;
  float32 CR_RL_ObjObstacleProb_10;
  float32 CR_RL_ObjRelVelX_10;
  float32 CR_RL_ObjRelVelY_10;
} SG_CR_RL_ObjID10;

#  define Rte_TypeDef_SG_CR_RL_ObjID11
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_11;
  uint8 CR_RL_Obj_MsgCounter_11;
  float32 CR_RL_ObjAccelX_11;
  float32 CR_RL_ObjAccelY_11;
  float32 CR_RL_ObjDistX_11;
  float32 CR_RL_ObjDistY_11;
  float32 CR_RL_ObjExistProb_11;
  uint8 CR_RL_ObjID_11;
  float32 CR_RL_ObjObstacleProb_11;
  float32 CR_RL_ObjRelVelX_11;
  float32 CR_RL_ObjRelVelY_11;
} SG_CR_RL_ObjID11;

#  define Rte_TypeDef_SG_CR_RL_ObjID12
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_12;
  uint8 CR_RL_Obj_MsgCounter_12;
  float32 CR_RL_ObjAccelX_12;
  float32 CR_RL_ObjAccelY_12;
  float32 CR_RL_ObjDistX_12;
  float32 CR_RL_ObjDistXStd_10;
  float32 CR_RL_ObjDistXStd_11;
  float32 CR_RL_ObjDistXStd_12;
  float32 CR_RL_ObjDistY_12;
  float32 CR_RL_ObjDistYStd_10;
  float32 CR_RL_ObjDistYStd_11;
  float32 CR_RL_ObjDistYStd_12;
  float32 CR_RL_ObjExistProb_12;
  uint8 CR_RL_ObjID_12;
  float32 CR_RL_ObjObstacleProb_12;
  float32 CR_RL_ObjRAccelXStd_10;
  float32 CR_RL_ObjRAccelXStd_11;
  float32 CR_RL_ObjRAccelXStd_12;
  float32 CR_RL_ObjRAccelYStd_10;
  float32 CR_RL_ObjRAccelYStd_11;
  float32 CR_RL_ObjRAccelYStd_12;
  float32 CR_RL_ObjRelVelX_12;
  float32 CR_RL_ObjRelVelXStd_10;
  float32 CR_RL_ObjRelVelXStd_11;
  float32 CR_RL_ObjRelVelXStd_12;
  float32 CR_RL_ObjRelVelY_12;
  float32 CR_RL_ObjRelVelYStd_10;
  float32 CR_RL_ObjRelVelYStd_11;
  float32 CR_RL_ObjRelVelYStd_12;
} SG_CR_RL_ObjID12;

#  define Rte_TypeDef_SG_CR_RL_ObjID13
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_13;
  uint8 CR_RL_Obj_MsgCounter_13;
  float32 CR_RL_ObjAccelX_13;
  float32 CR_RL_ObjAccelY_13;
  float32 CR_RL_ObjDistX_13;
  float32 CR_RL_ObjDistY_13;
  float32 CR_RL_ObjExistProb_13;
  uint8 CR_RL_ObjID_13;
  float32 CR_RL_ObjObstacleProb_13;
  float32 CR_RL_ObjRelVelX_13;
  float32 CR_RL_ObjRelVelY_13;
} SG_CR_RL_ObjID13;

#  define Rte_TypeDef_SG_CR_RL_ObjID14
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_14;
  uint8 CR_RL_Obj_MsgCounter_14;
  float32 CR_RL_ObjAccelX_14;
  float32 CR_RL_ObjAccelY_14;
  float32 CR_RL_ObjDistX_14;
  float32 CR_RL_ObjDistY_14;
  float32 CR_RL_ObjExistProb_14;
  uint8 CR_RL_ObjID_14;
  float32 CR_RL_ObjObstacleProb_14;
  float32 CR_RL_ObjRelVelX_14;
  float32 CR_RL_ObjRelVelY_14;
} SG_CR_RL_ObjID14;

#  define Rte_TypeDef_SG_CR_RL_ObjID15
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_15;
  uint8 CR_RL_Obj_MsgCounter_15;
  float32 CR_RL_ObjAccelX_15;
  float32 CR_RL_ObjAccelY_15;
  float32 CR_RL_ObjDistX_15;
  float32 CR_RL_ObjDistXStd_13;
  float32 CR_RL_ObjDistXStd_14;
  float32 CR_RL_ObjDistXStd_15;
  float32 CR_RL_ObjDistY_15;
  float32 CR_RL_ObjDistYStd_13;
  float32 CR_RL_ObjDistYStd_14;
  float32 CR_RL_ObjDistYStd_15;
  float32 CR_RL_ObjExistProb_15;
  uint8 CR_RL_ObjID_15;
  float32 CR_RL_ObjObstacleProb_15;
  float32 CR_RL_ObjRAccelXStd_13;
  float32 CR_RL_ObjRAccelXStd_14;
  float32 CR_RL_ObjRAccelXStd_15;
  float32 CR_RL_ObjRAccelYStd_13;
  float32 CR_RL_ObjRAccelYStd_14;
  float32 CR_RL_ObjRAccelYStd_15;
  float32 CR_RL_ObjRelVelX_15;
  float32 CR_RL_ObjRelVelXStd_13;
  float32 CR_RL_ObjRelVelXStd_14;
  float32 CR_RL_ObjRelVelXStd_15;
  float32 CR_RL_ObjRelVelY_15;
  float32 CR_RL_ObjRelVelYStd_13;
  float32 CR_RL_ObjRelVelYStd_14;
  float32 CR_RL_ObjRelVelYStd_15;
} SG_CR_RL_ObjID15;

#  define Rte_TypeDef_SG_CR_RL_ObjID16
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_16;
  uint8 CR_RL_Obj_MsgCounter_16;
  float32 CR_RL_ObjAccelX_16;
  float32 CR_RL_ObjAccelY_16;
  float32 CR_RL_ObjDistX_16;
  float32 CR_RL_ObjDistY_16;
  float32 CR_RL_ObjExistProb_16;
  uint8 CR_RL_ObjID_16;
  float32 CR_RL_ObjObstacleProb_16;
  float32 CR_RL_ObjRelVelX_16;
  float32 CR_RL_ObjRelVelY_16;
} SG_CR_RL_ObjID16;

#  define Rte_TypeDef_SG_CR_RL_ObjID17
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_17;
  uint8 CR_RL_Obj_MsgCounter_17;
  float32 CR_RL_ObjAccelX_17;
  float32 CR_RL_ObjAccelY_17;
  float32 CR_RL_ObjDistX_17;
  float32 CR_RL_ObjDistY_17;
  float32 CR_RL_ObjExistProb_17;
  uint8 CR_RL_ObjID_17;
  float32 CR_RL_ObjObstacleProb_17;
  float32 CR_RL_ObjRelVelX_17;
  float32 CR_RL_ObjRelVelY_17;
} SG_CR_RL_ObjID17;

#  define Rte_TypeDef_SG_CR_RL_ObjID18
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_18;
  uint8 CR_RL_Obj_MsgCounter_18;
  float32 CR_RL_ObjAccelX_18;
  float32 CR_RL_ObjAccelY_18;
  float32 CR_RL_ObjDistX_18;
  float32 CR_RL_ObjDistXStd_16;
  float32 CR_RL_ObjDistXStd_17;
  float32 CR_RL_ObjDistXStd_18;
  float32 CR_RL_ObjDistY_18;
  float32 CR_RL_ObjDistYStd_16;
  float32 CR_RL_ObjDistYStd_17;
  float32 CR_RL_ObjDistYStd_18;
  float32 CR_RL_ObjExistProb_18;
  uint8 CR_RL_ObjID_18;
  float32 CR_RL_ObjObstacleProb_18;
  float32 CR_RL_ObjRAccelXStd_16;
  float32 CR_RL_ObjRAccelXStd_17;
  float32 CR_RL_ObjRAccelXStd_18;
  float32 CR_RL_ObjRAccelYStd_16;
  float32 CR_RL_ObjRAccelYStd_17;
  float32 CR_RL_ObjRAccelYStd_18;
  float32 CR_RL_ObjRelVelX_18;
  float32 CR_RL_ObjRelVelXStd_16;
  float32 CR_RL_ObjRelVelXStd_17;
  float32 CR_RL_ObjRelVelXStd_18;
  float32 CR_RL_ObjRelVelY_18;
  float32 CR_RL_ObjRelVelYStd_16;
  float32 CR_RL_ObjRelVelYStd_17;
  float32 CR_RL_ObjRelVelYStd_18;
} SG_CR_RL_ObjID18;

#  define Rte_TypeDef_SG_CR_RL_ObjID19
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_19;
  uint8 CR_RL_Obj_MsgCounter_19;
  float32 CR_RL_ObjAccelX_19;
  float32 CR_RL_ObjAccelY_19;
  float32 CR_RL_ObjDistX_19;
  float32 CR_RL_ObjDistY_19;
  float32 CR_RL_ObjExistProb_19;
  uint8 CR_RL_ObjID_19;
  float32 CR_RL_ObjObstacleProb_19;
  float32 CR_RL_ObjRelVelX_19;
  float32 CR_RL_ObjRelVelY_19;
} SG_CR_RL_ObjID19;

#  define Rte_TypeDef_SG_CR_RL_ObjID20
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_20;
  uint8 CR_RL_Obj_MsgCounter_20;
  float32 CR_RL_ObjAccelX_20;
  float32 CR_RL_ObjAccelY_20;
  float32 CR_RL_ObjDistX_20;
  float32 CR_RL_ObjDistY_20;
  float32 CR_RL_ObjExistProb_20;
  uint8 CR_RL_ObjID_20;
  float32 CR_RL_ObjObstacleProb_20;
  float32 CR_RL_ObjRelVelX_20;
  float32 CR_RL_ObjRelVelY_20;
} SG_CR_RL_ObjID20;

#  define Rte_TypeDef_SG_CR_RL_ObjID21
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_21;
  uint8 CR_RL_Obj_MsgCounter_21;
  float32 CR_RL_ObjAccelX_21;
  float32 CR_RL_ObjAccelY_21;
  float32 CR_RL_ObjDistX_21;
  float32 CR_RL_ObjDistXStd_19;
  float32 CR_RL_ObjDistXStd_20;
  float32 CR_RL_ObjDistXStd_21;
  float32 CR_RL_ObjDistY_21;
  float32 CR_RL_ObjDistYStd_19;
  float32 CR_RL_ObjDistYStd_20;
  float32 CR_RL_ObjDistYStd_21;
  float32 CR_RL_ObjExistProb_21;
  uint8 CR_RL_ObjID_21;
  float32 CR_RL_ObjObstacleProb_21;
  float32 CR_RL_ObjRAccelXStd_19;
  float32 CR_RL_ObjRAccelXStd_20;
  float32 CR_RL_ObjRAccelXStd_21;
  float32 CR_RL_ObjRAccelYStd_19;
  float32 CR_RL_ObjRAccelYStd_20;
  float32 CR_RL_ObjRAccelYStd_21;
  float32 CR_RL_ObjRelVelX_21;
  float32 CR_RL_ObjRelVelXStd_19;
  float32 CR_RL_ObjRelVelXStd_20;
  float32 CR_RL_ObjRelVelXStd_21;
  float32 CR_RL_ObjRelVelY_21;
  float32 CR_RL_ObjRelVelYStd_19;
  float32 CR_RL_ObjRelVelYStd_20;
  float32 CR_RL_ObjRelVelYStd_21;
} SG_CR_RL_ObjID21;

#  define Rte_TypeDef_SG_CR_RL_ObjID22
typedef struct
{
  uint8 CR_RL_Obj_CRC8Checksum_22;
  uint8 CR_RL_Obj_MsgCounter_22;
  float32 CR_RL_ObjAccelX_22;
  float32 CR_RL_ObjAccelY_22;
  float32 CR_RL_ObjDistX_22;
  float32 CR_RL_ObjDistXStd_22;
  float32 CR_RL_ObjDistY_22;
  float32 CR_RL_ObjDistYStd_22;
  float32 CR_RL_ObjExistProb_22;
  uint8 CR_RL_ObjID_22;
  float32 CR_RL_ObjObstacleProb_22;
  float32 CR_RL_ObjRAccelXStd_22;
  float32 CR_RL_ObjRAccelYStd_22;
  float32 CR_RL_ObjRelVelX_22;
  float32 CR_RL_ObjRelVelXStd_22;
  float32 CR_RL_ObjRelVelY_22;
  float32 CR_RL_ObjRelVelYStd_22;
} SG_CR_RL_ObjID22;

#  define Rte_TypeDef_SG_CR_RR_ObjID01
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_1;
  uint8 CR_RR_Obj_MsgCounter_1;
  float32 CR_RR_ObjAccelX_1;
  float32 CR_RR_ObjAccelY_1;
  float32 CR_RR_ObjDistX_1;
  float32 CR_RR_ObjDistY_1;
  float32 CR_RR_ObjExistProb_1;
  uint8 CR_RR_ObjID_1;
  float32 CR_RR_ObjObstacleProb_1;
  float32 CR_RR_ObjRelVelX_1;
  float32 CR_RR_ObjRelVelY_1;
} SG_CR_RR_ObjID01;

#  define Rte_TypeDef_SG_CR_RR_ObjID02
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_2;
  uint8 CR_RR_Obj_MsgCounter_2;
  float32 CR_RR_ObjAccelX_2;
  float32 CR_RR_ObjAccelY_2;
  float32 CR_RR_ObjDistX_2;
  float32 CR_RR_ObjDistY_2;
  float32 CR_RR_ObjExistProb_2;
  uint8 CR_RR_ObjID_2;
  float32 CR_RR_ObjObstacleProb_2;
  float32 CR_RR_ObjRelVelX_2;
  float32 CR_RR_ObjRelVelY_2;
} SG_CR_RR_ObjID02;

#  define Rte_TypeDef_SG_CR_RR_ObjID03
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_3;
  uint8 CR_RR_Obj_MsgCounter_3;
  float32 CR_RR_ObjAccelX_3;
  float32 CR_RR_ObjAccelY_3;
  float32 CR_RR_ObjDistX_3;
  float32 CR_RR_ObjDistXStd_1;
  float32 CR_RR_ObjDistXStd_2;
  float32 CR_RR_ObjDistXStd_3;
  float32 CR_RR_ObjDistY_3;
  float32 CR_RR_ObjDistYStd_1;
  float32 CR_RR_ObjDistYStd_2;
  float32 CR_RR_ObjDistYStd_3;
  float32 CR_RR_ObjExistProb_3;
  uint8 CR_RR_ObjID_3;
  float32 CR_RR_ObjObstacleProb_3;
  float32 CR_RR_ObjRAccelXStd_1;
  float32 CR_RR_ObjRAccelXStd_2;
  float32 CR_RR_ObjRAccelXStd_3;
  float32 CR_RR_ObjRAccelYStd_1;
  float32 CR_RR_ObjRAccelYStd_2;
  float32 CR_RR_ObjRAccelYStd_3;
  float32 CR_RR_ObjRelVelX_3;
  float32 CR_RR_ObjRelVelXStd_1;
  float32 CR_RR_ObjRelVelXStd_2;
  float32 CR_RR_ObjRelVelXStd_3;
  float32 CR_RR_ObjRelVelY_3;
  float32 CR_RR_ObjRelVelYStd_1;
  float32 CR_RR_ObjRelVelYStd_2;
  float32 CR_RR_ObjRelVelYStd_3;
} SG_CR_RR_ObjID03;

#  define Rte_TypeDef_SG_CR_RR_ObjID04
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_4;
  uint8 CR_RR_Obj_MsgCounter_4;
  float32 CR_RR_ObjAccelX_4;
  float32 CR_RR_ObjAccelY_4;
  float32 CR_RR_ObjDistX_4;
  float32 CR_RR_ObjDistY_4;
  float32 CR_RR_ObjExistProb_4;
  uint8 CR_RR_ObjID_4;
  float32 CR_RR_ObjObstacleProb_4;
  float32 CR_RR_ObjRelVelX_4;
  float32 CR_RR_ObjRelVelY_4;
} SG_CR_RR_ObjID04;

#  define Rte_TypeDef_SG_CR_RR_ObjID05
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_5;
  uint8 CR_RR_Obj_MsgCounter_5;
  float32 CR_RR_ObjAccelX_5;
  float32 CR_RR_ObjAccelY_5;
  float32 CR_RR_ObjDistX_5;
  float32 CR_RR_ObjDistY_5;
  float32 CR_RR_ObjExistProb_5;
  uint8 CR_RR_ObjID_5;
  float32 CR_RR_ObjObstacleProb_5;
  float32 CR_RR_ObjRelVelX_5;
  float32 CR_RR_ObjRelVelY_5;
} SG_CR_RR_ObjID05;

#  define Rte_TypeDef_SG_CR_RR_ObjID06
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_6;
  uint8 CR_RR_Obj_MsgCounter_6;
  float32 CR_RR_ObjAccelX_6;
  float32 CR_RR_ObjAccelY_6;
  float32 CR_RR_ObjDistX_6;
  float32 CR_RR_ObjDistXStd_4;
  float32 CR_RR_ObjDistXStd_5;
  float32 CR_RR_ObjDistXStd_6;
  float32 CR_RR_ObjDistY_6;
  float32 CR_RR_ObjDistYStd_4;
  float32 CR_RR_ObjDistYStd_5;
  float32 CR_RR_ObjDistYStd_6;
  float32 CR_RR_ObjExistProb_6;
  uint8 CR_RR_ObjID_6;
  float32 CR_RR_ObjObstacleProb_6;
  float32 CR_RR_ObjRAccelXStd_4;
  float32 CR_RR_ObjRAccelXStd_5;
  float32 CR_RR_ObjRAccelXStd_6;
  float32 CR_RR_ObjRAccelYStd_4;
  float32 CR_RR_ObjRAccelYStd_5;
  float32 CR_RR_ObjRAccelYStd_6;
  float32 CR_RR_ObjRelVelX_6;
  float32 CR_RR_ObjRelVelXStd_4;
  float32 CR_RR_ObjRelVelXStd_5;
  float32 CR_RR_ObjRelVelXStd_6;
  float32 CR_RR_ObjRelVelY_6;
  float32 CR_RR_ObjRelVelYStd_4;
  float32 CR_RR_ObjRelVelYStd_5;
  float32 CR_RR_ObjRelVelYStd_6;
} SG_CR_RR_ObjID06;

#  define Rte_TypeDef_SG_CR_RR_ObjID07
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_7;
  uint8 CR_RR_Obj_MsgCounter_7;
  float32 CR_RR_ObjAccelX_7;
  float32 CR_RR_ObjAccelY_7;
  float32 CR_RR_ObjDistX_7;
  float32 CR_RR_ObjDistY_7;
  float32 CR_RR_ObjExistProb_7;
  uint8 CR_RR_ObjID_7;
  float32 CR_RR_ObjObstacleProb_7;
  float32 CR_RR_ObjRelVelX_7;
  float32 CR_RR_ObjRelVelY_7;
} SG_CR_RR_ObjID07;

#  define Rte_TypeDef_SG_CR_RR_ObjID08
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_8;
  uint8 CR_RR_Obj_MsgCounter_8;
  float32 CR_RR_ObjAccelX_8;
  float32 CR_RR_ObjAccelY_8;
  float32 CR_RR_ObjDistX_8;
  float32 CR_RR_ObjDistY_8;
  float32 CR_RR_ObjExistProb_8;
  uint8 CR_RR_ObjID_8;
  float32 CR_RR_ObjObstacleProb_8;
  float32 CR_RR_ObjRelVelX_8;
  float32 CR_RR_ObjRelVelY_8;
} SG_CR_RR_ObjID08;

#  define Rte_TypeDef_SG_CR_RR_ObjID09
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_9;
  uint8 CR_RR_Obj_MsgCounter_9;
  float32 CR_RR_ObjAccelX_9;
  float32 CR_RR_ObjAccelY_9;
  float32 CR_RR_ObjDistX_9;
  float32 CR_RR_ObjDistXStd_7;
  float32 CR_RR_ObjDistXStd_8;
  float32 CR_RR_ObjDistXStd_9;
  float32 CR_RR_ObjDistY_9;
  float32 CR_RR_ObjDistYStd_7;
  float32 CR_RR_ObjDistYStd_8;
  float32 CR_RR_ObjDistYStd_9;
  float32 CR_RR_ObjExistProb_9;
  uint8 CR_RR_ObjID_9;
  float32 CR_RR_ObjObstacleProb_9;
  float32 CR_RR_ObjRAccelXStd_7;
  float32 CR_RR_ObjRAccelXStd_8;
  float32 CR_RR_ObjRAccelXStd_9;
  float32 CR_RR_ObjRAccelYStd_7;
  float32 CR_RR_ObjRAccelYStd_8;
  float32 CR_RR_ObjRAccelYStd_9;
  float32 CR_RR_ObjRelVelX_9;
  float32 CR_RR_ObjRelVelXStd_7;
  float32 CR_RR_ObjRelVelXStd_8;
  float32 CR_RR_ObjRelVelXStd_9;
  float32 CR_RR_ObjRelVelY_9;
  float32 CR_RR_ObjRelVelYStd_7;
  float32 CR_RR_ObjRelVelYStd_8;
  float32 CR_RR_ObjRelVelYStd_9;
} SG_CR_RR_ObjID09;

#  define Rte_TypeDef_SG_CR_RR_ObjID10
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_10;
  uint8 CR_RR_Obj_MsgCounter_10;
  float32 CR_RR_ObjAccelX_10;
  float32 CR_RR_ObjAccelY_10;
  float32 CR_RR_ObjDistX_10;
  float32 CR_RR_ObjDistY_10;
  float32 CR_RR_ObjExistProb_10;
  uint8 CR_RR_ObjID_10;
  float32 CR_RR_ObjObstacleProb_10;
  float32 CR_RR_ObjRelVelX_10;
  float32 CR_RR_ObjRelVelY_10;
} SG_CR_RR_ObjID10;

#  define Rte_TypeDef_SG_CR_RR_ObjID11
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_11;
  uint8 CR_RR_Obj_MsgCounter_11;
  float32 CR_RR_ObjAccelX_11;
  float32 CR_RR_ObjAccelY_11;
  float32 CR_RR_ObjDistX_11;
  float32 CR_RR_ObjDistY_11;
  float32 CR_RR_ObjExistProb_11;
  uint8 CR_RR_ObjID_11;
  float32 CR_RR_ObjObstacleProb_11;
  float32 CR_RR_ObjRelVelX_11;
  float32 CR_RR_ObjRelVelY_11;
} SG_CR_RR_ObjID11;

#  define Rte_TypeDef_SG_CR_RR_ObjID12
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_12;
  uint8 CR_RR_Obj_MsgCounter_12;
  float32 CR_RR_ObjAccelX_12;
  float32 CR_RR_ObjAccelY_12;
  float32 CR_RR_ObjDistX_12;
  float32 CR_RR_ObjDistXStd_10;
  float32 CR_RR_ObjDistXStd_11;
  float32 CR_RR_ObjDistXStd_12;
  float32 CR_RR_ObjDistY_12;
  float32 CR_RR_ObjDistYStd_10;
  float32 CR_RR_ObjDistYStd_11;
  float32 CR_RR_ObjDistYStd_12;
  float32 CR_RR_ObjExistProb_12;
  uint8 CR_RR_ObjID_12;
  float32 CR_RR_ObjObstacleProb_12;
  float32 CR_RR_ObjRAccelXStd_10;
  float32 CR_RR_ObjRAccelXStd_11;
  float32 CR_RR_ObjRAccelXStd_12;
  float32 CR_RR_ObjRAccelYStd_10;
  float32 CR_RR_ObjRAccelYStd_11;
  float32 CR_RR_ObjRAccelYStd_12;
  float32 CR_RR_ObjRelVelX_12;
  float32 CR_RR_ObjRelVelXStd_10;
  float32 CR_RR_ObjRelVelXStd_11;
  float32 CR_RR_ObjRelVelXStd_12;
  float32 CR_RR_ObjRelVelY_12;
  float32 CR_RR_ObjRelVelYStd_10;
  float32 CR_RR_ObjRelVelYStd_11;
  float32 CR_RR_ObjRelVelYStd_12;
} SG_CR_RR_ObjID12;

#  define Rte_TypeDef_SG_CR_RR_ObjID13
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_13;
  uint8 CR_RR_Obj_MsgCounter_13;
  float32 CR_RR_ObjAccelX_13;
  float32 CR_RR_ObjAccelY_13;
  float32 CR_RR_ObjDistX_13;
  float32 CR_RR_ObjDistY_13;
  float32 CR_RR_ObjExistProb_13;
  uint8 CR_RR_ObjID_13;
  float32 CR_RR_ObjObstacleProb_13;
  float32 CR_RR_ObjRelVelX_13;
  float32 CR_RR_ObjRelVelY_13;
} SG_CR_RR_ObjID13;

#  define Rte_TypeDef_SG_CR_RR_ObjID14
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_14;
  uint8 CR_RR_Obj_MsgCounter_14;
  float32 CR_RR_ObjAccelX_14;
  float32 CR_RR_ObjAccelY_14;
  float32 CR_RR_ObjDistX_14;
  float32 CR_RR_ObjDistY_14;
  float32 CR_RR_ObjExistProb_14;
  uint8 CR_RR_ObjID_14;
  float32 CR_RR_ObjObstacleProb_14;
  float32 CR_RR_ObjRelVelX_14;
  float32 CR_RR_ObjRelVelY_14;
} SG_CR_RR_ObjID14;

#  define Rte_TypeDef_SG_CR_RR_ObjID15
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_15;
  uint8 CR_RR_Obj_MsgCounter_15;
  float32 CR_RR_ObjAccelX_15;
  float32 CR_RR_ObjAccelY_15;
  float32 CR_RR_ObjDistX_15;
  float32 CR_RR_ObjDistXStd_13;
  float32 CR_RR_ObjDistXStd_14;
  float32 CR_RR_ObjDistXStd_15;
  float32 CR_RR_ObjDistY_15;
  float32 CR_RR_ObjDistYStd_13;
  float32 CR_RR_ObjDistYStd_14;
  float32 CR_RR_ObjDistYStd_15;
  float32 CR_RR_ObjExistProb_15;
  uint8 CR_RR_ObjID_15;
  float32 CR_RR_ObjObstacleProb_15;
  float32 CR_RR_ObjRAccelXStd_13;
  float32 CR_RR_ObjRAccelXStd_14;
  float32 CR_RR_ObjRAccelXStd_15;
  float32 CR_RR_ObjRAccelYStd_13;
  float32 CR_RR_ObjRAccelYStd_14;
  float32 CR_RR_ObjRAccelYStd_15;
  float32 CR_RR_ObjRelVelX_15;
  float32 CR_RR_ObjRelVelXStd_13;
  float32 CR_RR_ObjRelVelXStd_14;
  float32 CR_RR_ObjRelVelXStd_15;
  float32 CR_RR_ObjRelVelY_15;
  float32 CR_RR_ObjRelVelYStd_13;
  float32 CR_RR_ObjRelVelYStd_14;
  float32 CR_RR_ObjRelVelYStd_15;
} SG_CR_RR_ObjID15;

#  define Rte_TypeDef_SG_CR_RR_ObjID16
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_16;
  uint8 CR_RR_Obj_MsgCounter_16;
  float32 CR_RR_ObjAccelX_16;
  float32 CR_RR_ObjAccelY_16;
  float32 CR_RR_ObjDistX_16;
  float32 CR_RR_ObjDistY_16;
  float32 CR_RR_ObjExistProb_16;
  uint8 CR_RR_ObjID_16;
  float32 CR_RR_ObjObstacleProb_16;
  float32 CR_RR_ObjRelVelX_16;
  float32 CR_RR_ObjRelVelY_16;
} SG_CR_RR_ObjID16;

#  define Rte_TypeDef_SG_CR_RR_ObjID17
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_17;
  uint8 CR_RR_Obj_MsgCounter_17;
  float32 CR_RR_ObjAccelX_17;
  float32 CR_RR_ObjAccelY_17;
  float32 CR_RR_ObjDistX_17;
  float32 CR_RR_ObjDistY_17;
  float32 CR_RR_ObjExistProb_17;
  uint8 CR_RR_ObjID_17;
  float32 CR_RR_ObjObstacleProb_17;
  float32 CR_RR_ObjRelVelX_17;
  float32 CR_RR_ObjRelVelY_17;
} SG_CR_RR_ObjID17;

#  define Rte_TypeDef_SG_CR_RR_ObjID18
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_18;
  uint8 CR_RR_Obj_MsgCounter_18;
  float32 CR_RR_ObjAccelX_18;
  float32 CR_RR_ObjAccelY_18;
  float32 CR_RR_ObjDistX_18;
  float32 CR_RR_ObjDistXStd_16;
  float32 CR_RR_ObjDistXStd_17;
  float32 CR_RR_ObjDistXStd_18;
  float32 CR_RR_ObjDistY_18;
  float32 CR_RR_ObjDistYStd_16;
  float32 CR_RR_ObjDistYStd_17;
  float32 CR_RR_ObjDistYStd_18;
  float32 CR_RR_ObjExistProb_18;
  uint8 CR_RR_ObjID_18;
  float32 CR_RR_ObjObstacleProb_18;
  float32 CR_RR_ObjRAccelXStd_16;
  float32 CR_RR_ObjRAccelXStd_17;
  float32 CR_RR_ObjRAccelXStd_18;
  float32 CR_RR_ObjRAccelYStd_16;
  float32 CR_RR_ObjRAccelYStd_17;
  float32 CR_RR_ObjRAccelYStd_18;
  float32 CR_RR_ObjRelVelX_18;
  float32 CR_RR_ObjRelVelXStd_16;
  float32 CR_RR_ObjRelVelXStd_17;
  float32 CR_RR_ObjRelVelXStd_18;
  float32 CR_RR_ObjRelVelY_18;
  float32 CR_RR_ObjRelVelYStd_16;
  float32 CR_RR_ObjRelVelYStd_17;
  float32 CR_RR_ObjRelVelYStd_18;
} SG_CR_RR_ObjID18;

#  define Rte_TypeDef_SG_CR_RR_ObjID19
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_19;
  uint8 CR_RR_Obj_MsgCounter_19;
  float32 CR_RR_ObjAccelX_19;
  float32 CR_RR_ObjAccelY_19;
  float32 CR_RR_ObjDistX_19;
  float32 CR_RR_ObjDistY_19;
  float32 CR_RR_ObjExistProb_19;
  uint8 CR_RR_ObjID_19;
  float32 CR_RR_ObjObstacleProb_19;
  float32 CR_RR_ObjRelVelX_19;
  float32 CR_RR_ObjRelVelY_19;
} SG_CR_RR_ObjID19;

#  define Rte_TypeDef_SG_CR_RR_ObjID20
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_20;
  uint8 CR_RR_Obj_MsgCounter_20;
  float32 CR_RR_ObjAccelX_20;
  float32 CR_RR_ObjAccelY_20;
  float32 CR_RR_ObjDistX_20;
  float32 CR_RR_ObjDistY_20;
  float32 CR_RR_ObjExistProb_20;
  uint8 CR_RR_ObjID_20;
  float32 CR_RR_ObjObstacleProb_20;
  float32 CR_RR_ObjRelVelX_20;
  float32 CR_RR_ObjRelVelY_20;
} SG_CR_RR_ObjID20;

#  define Rte_TypeDef_SG_CR_RR_ObjID21
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_21;
  uint8 CR_RR_Obj_MsgCounter_21;
  float32 CR_RR_ObjAccelX_21;
  float32 CR_RR_ObjAccelY_21;
  float32 CR_RR_ObjDistX_21;
  float32 CR_RR_ObjDistXStd_19;
  float32 CR_RR_ObjDistXStd_20;
  float32 CR_RR_ObjDistXStd_21;
  float32 CR_RR_ObjDistY_21;
  float32 CR_RR_ObjDistYStd_19;
  float32 CR_RR_ObjDistYStd_20;
  float32 CR_RR_ObjDistYStd_21;
  float32 CR_RR_ObjExistProb_21;
  uint8 CR_RR_ObjID_21;
  float32 CR_RR_ObjObstacleProb_21;
  float32 CR_RR_ObjRAccelXStd_19;
  float32 CR_RR_ObjRAccelXStd_20;
  float32 CR_RR_ObjRAccelXStd_21;
  float32 CR_RR_ObjRAccelYStd_19;
  float32 CR_RR_ObjRAccelYStd_20;
  float32 CR_RR_ObjRAccelYStd_21;
  float32 CR_RR_ObjRelVelX_21;
  float32 CR_RR_ObjRelVelXStd_19;
  float32 CR_RR_ObjRelVelXStd_20;
  float32 CR_RR_ObjRelVelXStd_21;
  float32 CR_RR_ObjRelVelY_21;
  float32 CR_RR_ObjRelVelYStd_19;
  float32 CR_RR_ObjRelVelYStd_20;
  float32 CR_RR_ObjRelVelYStd_21;
} SG_CR_RR_ObjID21;

#  define Rte_TypeDef_SG_CR_RR_ObjID22
typedef struct
{
  uint8 CR_RR_Obj_CRC8Checksum_22;
  uint8 CR_RR_Obj_MsgCounter_22;
  float32 CR_RR_ObjAccelX_22;
  float32 CR_RR_ObjAccelY_22;
  float32 CR_RR_ObjDistX_22;
  float32 CR_RR_ObjDistXStd_22;
  float32 CR_RR_ObjDistY_22;
  float32 CR_RR_ObjDistYStd_22;
  float32 CR_RR_ObjExistProb_22;
  uint8 CR_RR_ObjID_22;
  float32 CR_RR_ObjObstacleProb_22;
  float32 CR_RR_ObjRAccelXStd_22;
  float32 CR_RR_ObjRAccelYStd_22;
  float32 CR_RR_ObjRelVelX_22;
  uint8 CR_RR_ObjRelVelXStd_22;
  uint16 CR_RR_ObjRelVelY_22;
  uint8 CR_RR_ObjRelVelYStd_22;
} SG_CR_RR_ObjID22;

#  define Rte_TypeDef_SoundControlType
typedef struct
{
  UInt8 enable;
  UInt32 sound_id;
} SoundControlType;

#  define Rte_TypeDef_SsmAlcType
typedef struct
{
  Boolean stopline_valid;
  Float stopline_dst;
  Boolean AlcSafe_Le;
  Boolean AlcSafe_Host;
  Boolean AlcSafe_Ri;
  SInt32 Alcunsafeobj_id_LF;
  SInt32 Alcunsafeobj_id_LR;
  SInt32 Alcunsafeobj_id_RF;
  SInt32 Alcunsafeobj_id_RR;
} SsmAlcType;

#  define Rte_TypeDef_SsmCurvatureType
typedef struct
{
  Float s;
  Float kappa;
} SsmCurvatureType;

#  define Rte_TypeDef_SsmHeaderType
typedef struct
{
  UInt32 ts_high;
  UInt32 ts_low;
} SsmHeaderType;

#  define Rte_TypeDef_SsmObsType
typedef struct
{
  UInt32 id;
  UInt8 type;
  UInt8 sub_type;
  Float conf;
  Float pos_x;
  Float pos_y;
  Float pos_yaw;
  Float speed_x;
  Float speed_y;
  Float acc_x;
  Float acc_y;
  Float length;
  Float width;
  Float height;
  UInt8 source;
  UInt8 static_flag;
  UInt8 cut_in_flag;
  UInt8 pre_cut_in_flag;
  UInt8 cipv_flag;
  UInt8 valid_flag;
  SInt8 cut_in_line_index;
  Float cut_in_dist;
  UInt8 lane_index;
  UInt8 motion_state;
  UInt8 age;
  UInt8 meas_state;
  UInt8 lane_behavior;
  Float yaw_rate;
  Float pos_std_dev_x;
  Float pos_std_dev_y;
  Float acc_std_dev_x;
  Float acc_std_dev_y;
  Float yaw_cov_var;
  Float cutin_speed;
  UInt32 motion_categroy;
  UInt8 fusion_source;
} SsmObsType;

#  define Rte_TypeDef_SsmRefPtType
typedef struct
{
  Float s;
  Float x;
  Float y;
} SsmRefPtType;

#  define Rte_TypeDef_SsmSpeedRangeType
typedef struct
{
  Float s;
  Float max_v;
} SsmSpeedRangeType;

#  define Rte_TypeDef_StbM_OffsetRecordTableHeadType
typedef struct
{
  uint8 OffsetTimeDomain;
} StbM_OffsetRecordTableHeadType;

#  define Rte_TypeDef_StbM_PortIdType
typedef struct
{
  uint64 clockIdentity;
  uint16 portNumber;
} StbM_PortIdType;

#  define Rte_TypeDef_StbM_SyncRecordTableHeadType
typedef struct
{
  uint8 SynchronizedTimeDomain;
  uint32 HWfrequency;
  uint32 HWprescaler;
} StbM_SyncRecordTableHeadType;

#  define Rte_TypeDef_StbM_TimeStampShortType
typedef struct
{
  uint32 nanoseconds;
  uint32 seconds;
} StbM_TimeStampShortType;

#  define Rte_TypeDef_StbM_UserDataType
typedef struct
{
  uint8 userDataLength;
  uint8 userByte0;
  uint8 userByte1;
  uint8 userByte2;
} StbM_UserDataType;

#  define Rte_TypeDef_StbM_VirtualLocalTimeType
typedef struct
{
  uint32 nanosecondsLo;
  uint32 nanosecondsHi;
} StbM_VirtualLocalTimeType;

#  define Rte_TypeDef_SteerControlType
typedef struct
{
  UInt8 enable;
  UInt32 method;
  Float torque;
  Float angle;
  Float rate;
} SteerControlType;

#  define Rte_TypeDef_TCM_PTCAN_FrP03_adt
typedef struct
{
  uint8 AutocTrGearShftDircn;
  uint8 AutocTrGearShftDircnF;
  uint8 DrvlnToqStabSts;
  uint8 TCM_025ms_PDU03_CRC;
  uint8 TCM_025ms_PDU03_RC;
  uint8 TCM_025ms_PDU03_Reserve01;
  uint8 TrCCCanclReq;
  uint8 TrCrkPermsnSts;
  uint8 TrCrkPermsnStsV;
  float32 TrCrkshftToqLd;
  uint8 TrCrkshftToqLdV;
  uint8 TrCustSetngDspCmd;
  uint8 TrEnASSOvrd;
  uint8 TrInptClSts;
  uint8 TrInptClStsV;
  float32 TrMinVolReq;
  uint8 TrRngInhSts;
  uint8 TrWrnngDsp;
} TCM_PTCAN_FrP03_adt;

#  define Rte_TypeDef_TSRObjTyp
typedef struct
{
  UInt8 TSR_ID;
  UInt8 TSR_Confidence;
  UInt16 TSRTrafficSignType;
  Float Sign_Position_X;
  Float Sign_Position_Y;
  Float Sign_Position_Z;
  UInt8 TSR_Relevancy;
} TSRObjTyp;

#  define Rte_TypeDef_TsObjTyp
typedef struct
{
  Float PosnLgt;
  Float PosnLat;
  Float Spd;
  Float A;
  UInt8 ObjType;
  UInt8 MotionType;
  UInt16 Idn;
  Float headingAg;
  UInt8 trackStatus;
  UInt32 timeStamp;
  Float LatSpd;
  Float LatAcc;
  Float ExistPrblty;
  UInt8 SyncCtr;
  UInt8 Risk;
  Float Ttc;
  SInt32 Reserve1;
  SInt32 Reserve2;
  UInt32 HighTimeStamp;
  SInt32 SsmObjectId;
} TsObjTyp;

#  define Rte_TypeDef_TsrNavInfo
typedef struct
{
  Float TsrNavSpdLmtVal;
  UInt8 TsrNavSpdLmtValSts;
  UInt8 TsrNavCrntRoadType;
  Float TsrNavNewIconDist;
  UInt8 TsrNavNewIcon;
  UInt8 TsrNavSts;
  Float TsrNavItvalSpdDetnRetDist;
} TsrNavInfo;

#  define Rte_TypeDef_UssObstacleData_st
typedef struct
{
  Float x;
  Float y;
} UssObstacleData_st;

#  define Rte_TypeDef_UssRawData_st
typedef struct
{
  UInt64 timestamp;
  Float distance;
  uint8 status;
} UssRawData_st;

#  define Rte_TypeDef_VCU_010ms_PDU00_adt
typedef struct
{
  uint8 ElecMotEmgcShutDwn;
  float32 EPTAccelActuPos;
  uint8 EPTAccelActuPosV;
  float32 EPTAccelEfctvPos;
  uint8 EPTBrkPdlDscrtInptSts;
  uint8 EPTBrkPdlDscrtInptStsV;
  uint8 EPTCrkAbotd;
  uint8 EPTHVDCDCMdReq;
  uint8 EPTHVEmgcPwrOffReq;
  uint8 EPTMainRelayDrvReq;
  uint8 EPTRdy;
  uint8 EPTStCmdOn;
  uint8 VCU_010ms_PDU00_CRC;
  uint8 VCU_010ms_PDU00_RC;
  uint16 VCU_010ms_PDU00_Reserve01;
  uint8 VCU_010ms_PDU00_Reserve02;
  uint8 VCU_010ms_PDU00_Reserve03;
  uint8 VCU_010ms_PDU00_Reserve04;
} VCU_010ms_PDU00_adt;

#  define Rte_TypeDef_VCU_010ms_PDU19_adt
typedef struct
{
  uint8 AccelOvrd_VCU;
  float32 EPTTrOtptShaftTotMaxAvlblToq;
  uint8 EPTTrOtptShaftTotMaxAvlblToqV;
  float32 EPTTrOtptShaftTotMinAvlblToq;
  uint8 EPTTrOtptShaftTotMinAvlblToqV;
  float32 EPTTrOtptShaftTotToq;
  uint8 EPTTrOtptShaftTotToqV;
  uint8 IMCU_010ms_PDU19_CRC;
  uint8 IMCU_010ms_PDU19_RC;
  uint8 IMCU_010ms_PDU19_Reserve01;
  uint8 IMCU_010ms_PDU19_Reserve02;
  uint8 PtACCToqReqResp_VCU;
} VCU_010ms_PDU19_adt;

#  define Rte_TypeDef_VCU_100ms_PDU20_adt
typedef struct
{
  uint8 IMCU_100ms_PDU20_CRC;
  uint8 IMCU_100ms_PDU20_RC;
  uint8 IMCU_100ms_PDU20_Reserve01;
  uint8 IMCU_100ms_PDU20_Reserve02;
  uint8 IMCU_100ms_PDU20_Reserve03;
  uint16 IMCU_100ms_PDU20_Reserve04;
  uint8 SLIFSts;
  boolean SpdAstCondStsInd;
  uint8 SpdAstMdECM_VCU;
  uint8 SpdAstSysStsECM_VCU;
  float32 SpdAstSysTrgtSpd_VCU;
  uint8 TrgtSpdSrcSts_VCU;
} VCU_100ms_PDU20_adt;

#  define Rte_TypeDef_VelocityControlType
typedef struct
{
  uint8 enable;
  float32 vel;
  Float dist;
} VelocityControlType;

#  define Rte_TypeDef_VisObjStsType
typedef struct
{
  UInt32 FrameID_u32;
  UInt32 TimeStamp_top32_u32;
  UInt32 TimeStamp_last32_u32;
  UInt8 ObjNum_u8;
} VisObjStsType;

#  define Rte_TypeDef_VisObjType
typedef struct
{
  UInt8 ObjLaneAssign_u8;
  UInt8 ObstacleClass_u8;
  UInt8 VehSubTyp_u8;
  UInt8 PedSubTyp_u8;
  UInt8 ObjMeasSts_u8;
  UInt8 MotionMode_u8;
  UInt8 MotionCategory_u8;
  UInt8 ID_u8;
  Float CoverLineValue_f32;
  Float PosX_f32;
  Float PosY_f32;
  Float RelVelX_f32;
  Float RelVelY_f32;
  Float RelAccX_f32;
  Float RelAccY_f32;
  Float PosXStd_f32;
  Float PosYStd_f32;
  Float RelVelXStd_f32;
  Float RelVelYStd_f32;
  Float ObjWidth_f32;
  Float ObjLength_f32;
  Float ObjHeight_f32;
  Float ObjHeading_f32;
  Float ObjHeadingSTD_f32;
  Float AgeConsecutive_f32;
  UInt8 ObjConfidence_u8;
  Boolean ObstacleReplaced_b;
  Boolean MCPFlag_b;
  UInt8 IsObjValid_u8;
  UInt8 CoverLineSts_u8;
  Float CutInLongSpd_f32;
  Boolean CutInLongSpd_b;
  Boolean IsCIPV_b;
  UInt32 InternalID_u32;
  UInt16 Reserved_u16;
  Float AbsVelX_f32;
  Float AbsVelY_f32;
} VisObjType;

#  define Rte_TypeDef_struct_BrakeCmd
typedef struct
{
  Float velocity;
  Float distance;
  UInt8 BreakMode;
} struct_BrakeCmd;

#  define Rte_TypeDef_ABA_available
typedef Boolean ABA_available;

#  define Rte_TypeDef_ABS_Fail
typedef Boolean ABS_Fail;

#  define Rte_TypeDef_ABS_Sts
typedef Boolean ABS_Sts;

#  define Rte_TypeDef_ACCDetObj1ExistPrblty
typedef float32 ACCDetObj1ExistPrblty;

#  define Rte_TypeDef_ACCDetObj1Id
typedef uint8 ACCDetObj1Id;

#  define Rte_TypeDef_ACCDetObj1LatRltvDist
typedef float32 ACCDetObj1LatRltvDist;

#  define Rte_TypeDef_ACCDetObj1LongtRltvDist
typedef float32 ACCDetObj1LongtRltvDist;

#  define Rte_TypeDef_ACCDetObj1LongtRltvSpd
typedef float32 ACCDetObj1LongtRltvSpd;

#  define Rte_TypeDef_ACCDetObj1SyncCtr
typedef uint8 ACCDetObj1SyncCtr;

#  define Rte_TypeDef_ACCDetObj2ExistPrblty
typedef float32 ACCDetObj2ExistPrblty;

#  define Rte_TypeDef_ACCDetObj2Id
typedef uint8 ACCDetObj2Id;

#  define Rte_TypeDef_ACCDetObj2LatRltvDist
typedef float32 ACCDetObj2LatRltvDist;

#  define Rte_TypeDef_ACCDetObj2LongtRltvDist
typedef float32 ACCDetObj2LongtRltvDist;

#  define Rte_TypeDef_ACCDetObj2LongtRltvSpd
typedef float32 ACCDetObj2LongtRltvSpd;

#  define Rte_TypeDef_ACCDetObj2SyncCtr
typedef uint8 ACCDetObj2SyncCtr;

#  define Rte_TypeDef_ACCDetObj3ExistPrblty
typedef float32 ACCDetObj3ExistPrblty;

#  define Rte_TypeDef_ACCDetObj3Id
typedef uint8 ACCDetObj3Id;

#  define Rte_TypeDef_ACCDetObj3LatRltvDist
typedef float32 ACCDetObj3LatRltvDist;

#  define Rte_TypeDef_ACCDetObj3LongtRltvDist
typedef float32 ACCDetObj3LongtRltvDist;

#  define Rte_TypeDef_ACCDetObj3LongtRltvSpd
typedef float32 ACCDetObj3LongtRltvSpd;

#  define Rte_TypeDef_ACCDetObj3SyncCtr
typedef uint8 ACCDetObj3SyncCtr;

#  define Rte_TypeDef_ACCDetObjExistPrblty
typedef float32 ACCDetObjExistPrblty;

#  define Rte_TypeDef_ACCDetObjId
typedef uint8 ACCDetObjId;

#  define Rte_TypeDef_ACCDetObjLatRltvDist
typedef float32 ACCDetObjLatRltvDist;

#  define Rte_TypeDef_ACCDetObjLongtRltvDist
typedef float32 ACCDetObjLongtRltvDist;

#  define Rte_TypeDef_ACCDetObjLongtRltvSpd
typedef float32 ACCDetObjLongtRltvSpd;

#  define Rte_TypeDef_ACCDetObjSyncCtr
typedef uint8 ACCDetObjSyncCtr;

#  define Rte_TypeDef_ACCDetObs1ExistPrblty
typedef float32 ACCDetObs1ExistPrblty;

#  define Rte_TypeDef_ACCDetObs2ExistPrblty
typedef float32 ACCDetObs2ExistPrblty;

#  define Rte_TypeDef_ACCDetObs3ExistPrblty
typedef float32 ACCDetObs3ExistPrblty;

#  define Rte_TypeDef_ACCDetObsExistPrblty
typedef float32 ACCDetObsExistPrblty;

#  define Rte_TypeDef_ACCDrvrSelTrgtSpd
typedef float32 ACCDrvrSelTrgtSpd;

#  define Rte_TypeDef_ACU_LongititudeAcc
typedef float32 ACU_LongititudeAcc;

#  define Rte_TypeDef_ADAS_APBrakeFuncMode
typedef uint8 ADAS_APBrakeFuncMode;

#  define Rte_TypeDef_ADAS_AP_BrakeModeStatus
typedef uint8 ADAS_AP_BrakeModeStatus;

#  define Rte_TypeDef_ADAS_AP_FailureBrakeMode
typedef uint8 ADAS_AP_FailureBrakeMode;

#  define Rte_TypeDef_ADAS_AP_MaxVelocityLimit
typedef float32 ADAS_AP_MaxVelocityLimit;

#  define Rte_TypeDef_ADAS_AP_StopDistance
typedef float32 ADAS_AP_StopDistance;

#  define Rte_TypeDef_ADAS_EPS_APCtrlReq
typedef uint8 ADAS_EPS_APCtrlReq;

#  define Rte_TypeDef_ADAS_EPS_APFuncReq
typedef uint8 ADAS_EPS_APFuncReq;

#  define Rte_TypeDef_ADAS_GearsCtrlReq
typedef uint8 ADAS_GearsCtrlReq;

#  define Rte_TypeDef_ADAS_GearsTarget
typedef uint8 ADAS_GearsTarget;

#  define Rte_TypeDef_ADAS_VLCBS_ShutdownReq
typedef uint8 ADAS_VLCBS_ShutdownReq;

#  define Rte_TypeDef_ADAS_VLCECGPOvrd
typedef uint8 ADAS_VLCECGPOvrd;

#  define Rte_TypeDef_AD_Request
typedef UInt8 AD_Request;

#  define Rte_TypeDef_AEBDclReqVal
typedef Float AEBDclReqVal;

#  define Rte_TypeDef_AEB_available
typedef Boolean AEB_available;

#  define Rte_TypeDef_AESActvCtrl
typedef UInt8 AESActvCtrl;

#  define Rte_TypeDef_AESAsmSts
typedef UInt8 AESAsmSts;

#  define Rte_TypeDef_AESExitReason
typedef UInt8 AESExitReason;

#  define Rte_TypeDef_AESSteerWhlAgReq
typedef Float AESSteerWhlAgReq;

#  define Rte_TypeDef_APA_HandshakeACC
typedef UInt8 APA_HandshakeACC;

#  define Rte_TypeDef_APA_HandshakeACC_Spec
typedef uint8 APA_HandshakeACC_Spec;

#  define Rte_TypeDef_APA_HandshakeEPS
typedef UInt8 APA_HandshakeEPS;

#  define Rte_TypeDef_APA_HandshakeEPS_Spec
typedef uint8 APA_HandshakeEPS_Spec;

#  define Rte_TypeDef_APA_HandshakeESP
typedef UInt8 APA_HandshakeESP;

#  define Rte_TypeDef_APA_HandshakeESP_Spec
typedef uint8 APA_HandshakeESP_Spec;

#  define Rte_TypeDef_APA_IsLastPath
typedef uint8 APA_IsLastPath;

#  define Rte_TypeDef_APA_RemainDistance
typedef float32 APA_RemainDistance;

#  define Rte_TypeDef_AP_Request
typedef UInt8 AP_Request;

#  define Rte_TypeDef_AP_Status
typedef UInt8 AP_Status;

#  define Rte_TypeDef_AP_Status_Actual
typedef UInt8 AP_Status_Actual;

#  define Rte_TypeDef_AP_Status_Allow
typedef UInt8 AP_Status_Allow;

#  define Rte_TypeDef_AccPedalOvrd
typedef Boolean AccPedalOvrd;

#  define Rte_TypeDef_AswSysFltStsBSW
typedef UInt8 AswSysFltStsBSW;

#  define Rte_TypeDef_AswSysFltStsDow
typedef UInt8 AswSysFltStsDow;

#  define Rte_TypeDef_AvoidDistance
typedef Float AvoidDistance;

#  define Rte_TypeDef_AvoidStatus
typedef UInt8 AvoidStatus;

#  define Rte_TypeDef_AvoidUssID
typedef UInt8 AvoidUssID;

#  define Rte_TypeDef_BlockFlag
typedef uint8 BlockFlag;

#  define Rte_TypeDef_Braking_status
typedef UInt8 Braking_status;

#  define Rte_TypeDef_BswSysFltStsBSW
typedef UInt8 BswSysFltStsBSW;

#  define Rte_TypeDef_BswSysFltStsDow
typedef UInt8 BswSysFltStsDow;

#  define Rte_TypeDef_BswSysFltStsTsr
typedef UInt8 BswSysFltStsTsr;

#  define Rte_TypeDef_CCSpdVcCmd
typedef uint8 CCSpdVcCmd;

#  define Rte_TypeDef_CalendarDay
typedef uint8 CalendarDay;

#  define Rte_TypeDef_CalendarYear
typedef float32 CalendarYear;

#  define Rte_TypeDef_CamrTmsp
typedef uint32 CamrTmsp;

#  define Rte_TypeDef_ClstrDspdVehSpd
typedef uint8 ClstrDspdVehSpd;

#  define Rte_TypeDef_CluActTorq
typedef uint16 CluActTorq;

#  define Rte_TypeDef_ComM_InhibitionStatusType
typedef uint8 ComM_InhibitionStatusType;

#  define Rte_TypeDef_ComM_UserHandleType
typedef uint16 ComM_UserHandleType;

#  define Rte_TypeDef_CrusAndSpdLmtrDrvrSeldSpd
typedef float32 CrusAndSpdLmtrDrvrSeldSpd;

#  define Rte_TypeDef_CsmAccActiveReq
typedef Boolean CsmAccActiveReq;

#  define Rte_TypeDef_Dem_DTCGroupType
typedef uint32 Dem_DTCGroupType;

#  define Rte_TypeDef_Dem_DTCStatusMaskType
typedef uint8 Dem_DTCStatusMaskType;

#  define Rte_TypeDef_Dem_EventIdType
typedef uint16 Dem_EventIdType;

#  define Rte_TypeDef_Dem_OperationCycleIdType
typedef uint8 Dem_OperationCycleIdType;

#  define Rte_TypeDef_Dem_RatioIdType
typedef uint16 Dem_RatioIdType;

#  define Rte_TypeDef_DistSinceTrgtCamr
typedef float32 DistSinceTrgtCamr;

#  define Rte_TypeDef_DrivingParkingSysState
typedef uint8 DrivingParkingSysState;

#  define Rte_TypeDef_DrvrWndDclnSpc
typedef float32 DrvrWndDclnSpc;

#  define Rte_TypeDef_EBD_Fail
typedef Boolean EBD_Fail;

#  define Rte_TypeDef_EBD_Sts
typedef Boolean EBD_Sts;

#  define Rte_TypeDef_EPS_APInhibitCode
typedef uint8 EPS_APInhibitCode;

#  define Rte_TypeDef_EPS_APStatus
typedef uint8 EPS_APStatus;

#  define Rte_TypeDef_EPS_ErrorFlag
typedef uint8 EPS_ErrorFlag;

#  define Rte_TypeDef_ESC_OFF
typedef Boolean ESC_OFF;

#  define Rte_TypeDef_ESC_Sts
typedef UInt8 ESC_Sts;

#  define Rte_TypeDef_ESP_APLCFailureStatus
typedef uint8 ESP_APLCFailureStatus;

#  define Rte_TypeDef_ESP_APLCStatus
typedef uint8 ESP_APLCStatus;

#  define Rte_TypeDef_ESP_ESPFault
typedef uint8 ESP_ESPFault;

#  define Rte_TypeDef_ESP_VLCCtrlActive
typedef uint8 ESP_VLCCtrlActive;

#  define Rte_TypeDef_ESP_VLC_CDDVehiHoldStatus
typedef uint8 ESP_VLC_CDDVehiHoldStatus;

#  define Rte_TypeDef_ESP_WheelRotationDirectionFL
typedef UInt8 ESP_WheelRotationDirectionFL;

#  define Rte_TypeDef_ESP_WheelRotationDirectionFR
typedef UInt8 ESP_WheelRotationDirectionFR;

#  define Rte_TypeDef_ESP_WheelRotationDirectionRL
typedef UInt8 ESP_WheelRotationDirectionRL;

#  define Rte_TypeDef_ESP_WheelRotationDirectionRR
typedef UInt8 ESP_WheelRotationDirectionRR;

#  define Rte_TypeDef_ESP_WheelSpdPulseCounterFL
typedef UInt16 ESP_WheelSpdPulseCounterFL;

#  define Rte_TypeDef_ESP_WheelSpdPulseCounterFR
typedef UInt16 ESP_WheelSpdPulseCounterFR;

#  define Rte_TypeDef_ESP_WheelSpdPulseCounterRL
typedef UInt16 ESP_WheelSpdPulseCounterRL;

#  define Rte_TypeDef_ESP_WheelSpdPulseCounterRR
typedef UInt16 ESP_WheelSpdPulseCounterRR;

#  define Rte_TypeDef_EcuM_TimeType
typedef uint32 EcuM_TimeType;

#  define Rte_TypeDef_EcuM_UserType
typedef uint8 EcuM_UserType;

#  define Rte_TypeDef_EstimateSpeed
typedef Float EstimateSpeed;

#  define Rte_TypeDef_FICMPosngSysDircn
typedef uint16 FICMPosngSysDircn;

#  define Rte_TypeDef_FICMPosngSysLatd
typedef float32 FICMPosngSysLatd;

#  define Rte_TypeDef_FICMPosngSysLongd
typedef float32 FICMPosngSysLongd;

#  define Rte_TypeDef_FLObsDist
typedef uint16 FLObsDist;

#  define Rte_TypeDef_FRObsDist
typedef uint16 FRObsDist;

#  define Rte_TypeDef_FaultInfor
typedef UInt8 FaultInfor;

#  define Rte_TypeDef_Fault_Prompt
typedef UInt8 Fault_Prompt;

#  define Rte_TypeDef_FreespaceAvoid
typedef UInt8 FreespaceAvoid;

#  define Rte_TypeDef_FrtMidLObsDist
typedef uint16 FrtMidLObsDist;

#  define Rte_TypeDef_FrtMidRObsDist
typedef uint16 FrtMidRObsDist;

#  define Rte_TypeDef_FrtPsngWndDclnSpc
typedef uint8 FrtPsngWndDclnSpc;

#  define Rte_TypeDef_HADS_NKI
typedef uint16 HADS_NKI;

#  define Rte_TypeDef_HDC_Fault
typedef UInt8 HDC_Fault;

#  define Rte_TypeDef_HDC_Sts
typedef UInt8 HDC_Sts;

#  define Rte_TypeDef_HDMLicVPrd
typedef uint16 HDMLicVPrd;

#  define Rte_TypeDef_HourOfDay
typedef uint8 HourOfDay;

#  define Rte_TypeDef_IB_BrakePedalStatus
typedef uint8 IB_BrakePedalStatus;

#  define Rte_TypeDef_ICAPlusSuppBit
typedef UInt32 ICAPlusSuppBit;

#  define Rte_TypeDef_IOHWAB_BOOL
typedef boolean IOHWAB_BOOL;

#  define Rte_TypeDef_IOHWAB_UINT16
typedef uint16 IOHWAB_UINT16;

#  define Rte_TypeDef_IOHWAB_UINT8
typedef uint8 IOHWAB_UINT8;

#  define Rte_TypeDef_IcaPlusHwaMod
typedef UInt8 IcaPlusHwaMod;

#  define Rte_TypeDef_IcaPlusHwaSts
typedef UInt8 IcaPlusHwaSts;

#  define Rte_TypeDef_IoHwAb_AdcChannelType
typedef IOHWAB_UINT8 IoHwAb_AdcChannelType;

#  define Rte_TypeDef_IoHwAb_AdcChannelValueType
typedef IOHWAB_UINT16 IoHwAb_AdcChannelValueType;

#  define Rte_TypeDef_IoHwAb_DioChannelType
typedef IOHWAB_UINT16 IoHwAb_DioChannelType;

#  define Rte_TypeDef_IoHwAb_DioChannelValueType
typedef IOHWAB_BOOL IoHwAb_DioChannelValueType;

#  define Rte_TypeDef_LADSUsrSelSlotID
typedef uint8 LADSUsrSelSlotID;

#  define Rte_TypeDef_LBSWWrning
typedef Boolean LBSWWrning;

#  define Rte_TypeDef_LBSWWrnng
typedef UInt8 LBSWWrnng;

#  define Rte_TypeDef_LDOWWrnng
typedef UInt8 LDOWWrnng;

#  define Rte_TypeDef_LDrvnWhlRotlDistPlsCtr
typedef uint16 LDrvnWhlRotlDistPlsCtr;

#  define Rte_TypeDef_LDrvnWhlRotlDistTmsp
typedef uint16 LDrvnWhlRotlDistTmsp;

#  define Rte_TypeDef_LDrvnWhlRotlDistTmspRlovCtr
typedef uint8 LDrvnWhlRotlDistTmspRlovCtr;

#  define Rte_TypeDef_LDrvnWhlRotlSeqNum
typedef uint8 LDrvnWhlRotlSeqNum;

#  define Rte_TypeDef_LKAToqReqValMax
typedef float32 LKAToqReqValMax;

#  define Rte_TypeDef_LKAToqReqValMin
typedef float32 LKAToqReqValMin;

#  define Rte_TypeDef_LNonDrvnWhlRotlDistPC
typedef uint16 LNonDrvnWhlRotlDistPC;

#  define Rte_TypeDef_LNonDrvnWhlRotlDistT
typedef uint16 LNonDrvnWhlRotlDistT;

#  define Rte_TypeDef_LNonDrvnWhlRotlDistTRC
typedef uint8 LNonDrvnWhlRotlDistTRC;

#  define Rte_TypeDef_LNonDrvnWhlRotlSeqNum
typedef uint8 LNonDrvnWhlRotlSeqNum;

#  define Rte_TypeDef_LRCWWrnng
typedef UInt8 LRCWWrnng;

#  define Rte_TypeDef_LRoadPrfShckDetnCfdc
typedef float32 LRoadPrfShckDetnCfdc;

#  define Rte_TypeDef_LRoadPrfShckDetnHght
typedef float32 LRoadPrfShckDetnHght;

#  define Rte_TypeDef_LRoadPrfShckLeadEdgeDist
typedef float32 LRoadPrfShckLeadEdgeDist;

#  define Rte_TypeDef_LSSLCtrlMod
typedef UInt8 LSSLCtrlMod;

#  define Rte_TypeDef_Lane0_C0
typedef float32 Lane0_C0;

#  define Rte_TypeDef_Lane0_C1
typedef float32 Lane0_C1;

#  define Rte_TypeDef_Lane0_C2
typedef float32 Lane0_C2;

#  define Rte_TypeDef_Lane0_C3
typedef float32 Lane0_C3;

#  define Rte_TypeDef_Lane0_ExistPrblty
typedef float32 Lane0_ExistPrblty;

#  define Rte_TypeDef_Lane0_Part1_SyncFrameIndex
typedef uint8 Lane0_Part1_SyncFrameIndex;

#  define Rte_TypeDef_Lane0_ViewRangeEnd
typedef float32 Lane0_ViewRangeEnd;

#  define Rte_TypeDef_Lane0_ViewRangeStart
typedef float32 Lane0_ViewRangeStart;

#  define Rte_TypeDef_Lane1_C0
typedef float32 Lane1_C0;

#  define Rte_TypeDef_Lane1_C1
typedef float32 Lane1_C1;

#  define Rte_TypeDef_Lane1_C2
typedef float32 Lane1_C2;

#  define Rte_TypeDef_Lane1_C3
typedef float32 Lane1_C3;

#  define Rte_TypeDef_Lane1_ExistPrblty
typedef float32 Lane1_ExistPrblty;

#  define Rte_TypeDef_Lane1_ID
typedef uint8 Lane1_ID;

#  define Rte_TypeDef_Lane1_Part0_SyncFrameIndex
typedef uint8 Lane1_Part0_SyncFrameIndex;

#  define Rte_TypeDef_Lane1_ViewRangeEnd
typedef float32 Lane1_ViewRangeEnd;

#  define Rte_TypeDef_Lane1_ViewRangeStart
typedef float32 Lane1_ViewRangeStart;

#  define Rte_TypeDef_Lane2_C0
typedef float32 Lane2_C0;

#  define Rte_TypeDef_Lane2_C1
typedef float32 Lane2_C1;

#  define Rte_TypeDef_Lane2_C2
typedef float32 Lane2_C2;

#  define Rte_TypeDef_Lane2_C3
typedef float32 Lane2_C3;

#  define Rte_TypeDef_Lane2_ExistPrblty
typedef float32 Lane2_ExistPrblty;

#  define Rte_TypeDef_Lane2_ID
typedef uint8 Lane2_ID;

#  define Rte_TypeDef_Lane2_Part0_SyncFrameIndex
typedef uint8 Lane2_Part0_SyncFrameIndex;

#  define Rte_TypeDef_Lane2_Part1_SyncFrameIndex
typedef uint8 Lane2_Part1_SyncFrameIndex;

#  define Rte_TypeDef_Lane2_ViewRangeEnd
typedef float32 Lane2_ViewRangeEnd;

#  define Rte_TypeDef_Lane2_ViewRangeStart
typedef float32 Lane2_ViewRangeStart;

#  define Rte_TypeDef_Lane3_C0
typedef float32 Lane3_C0;

#  define Rte_TypeDef_Lane3_C1
typedef float32 Lane3_C1;

#  define Rte_TypeDef_Lane3_C2
typedef float32 Lane3_C2;

#  define Rte_TypeDef_Lane3_C3
typedef float32 Lane3_C3;

#  define Rte_TypeDef_Lane3_ExistPrblty
typedef float32 Lane3_ExistPrblty;

#  define Rte_TypeDef_Lane3_ID
typedef uint8 Lane3_ID;

#  define Rte_TypeDef_Lane3_Part0_SyncFrameIndex
typedef uint8 Lane3_Part0_SyncFrameIndex;

#  define Rte_TypeDef_Lane3_Part1_SyncFrameIndex
typedef uint8 Lane3_Part1_SyncFrameIndex;

#  define Rte_TypeDef_Lane3_ViewRangeEnd
typedef float32 Lane3_ViewRangeEnd;

#  define Rte_TypeDef_Lane3_ViewRangeStart
typedef float32 Lane3_ViewRangeStart;

#  define Rte_TypeDef_LineLAlvCtr0
typedef uint8 LineLAlvCtr0;

#  define Rte_TypeDef_LineLAlvCtr1
typedef uint8 LineLAlvCtr1;

#  define Rte_TypeDef_LineLBlkCtr0
typedef uint8 LineLBlkCtr0;

#  define Rte_TypeDef_LineLBlkCtr1
typedef uint8 LineLBlkCtr1;

#  define Rte_TypeDef_LineLC0
typedef float32 LineLC0;

#  define Rte_TypeDef_LineLC1
typedef float32 LineLC1;

#  define Rte_TypeDef_LineLC2
typedef float32 LineLC2;

#  define Rte_TypeDef_LineLC3
typedef float32 LineLC3;

#  define Rte_TypeDef_LineLChksm0
typedef uint8 LineLChksm0;

#  define Rte_TypeDef_LineLChksm1
typedef uint8 LineLChksm1;

#  define Rte_TypeDef_LineLExistPrblty
typedef float32 LineLExistPrblty;

#  define Rte_TypeDef_LineLLAlvCtr
typedef uint8 LineLLAlvCtr;

#  define Rte_TypeDef_LineLLAlvCtr0
typedef uint8 LineLLAlvCtr0;

#  define Rte_TypeDef_LineLLBlkCtr0
typedef uint8 LineLLBlkCtr0;

#  define Rte_TypeDef_LineLLBlkCtr1
typedef uint8 LineLLBlkCtr1;

#  define Rte_TypeDef_LineLLC0
typedef float32 LineLLC0;

#  define Rte_TypeDef_LineLLC1
typedef float32 LineLLC1;

#  define Rte_TypeDef_LineLLC2
typedef float32 LineLLC2;

#  define Rte_TypeDef_LineLLC3
typedef float32 LineLLC3;

#  define Rte_TypeDef_LineLLChksm0
typedef uint8 LineLLChksm0;

#  define Rte_TypeDef_LineLLChksm1
typedef uint8 LineLLChksm1;

#  define Rte_TypeDef_LineLLExistPrblty
typedef float32 LineLLExistPrblty;

#  define Rte_TypeDef_LineLLViewRngEnd
typedef float32 LineLLViewRngEnd;

#  define Rte_TypeDef_LineLLViewRngSt
typedef float32 LineLLViewRngSt;

#  define Rte_TypeDef_LineLViewRngEnd
typedef float32 LineLViewRngEnd;

#  define Rte_TypeDef_LineLViewRngSt
typedef float32 LineLViewRngSt;

#  define Rte_TypeDef_LineRAlvCtr0
typedef uint8 LineRAlvCtr0;

#  define Rte_TypeDef_LineRAlvCtr1
typedef uint8 LineRAlvCtr1;

#  define Rte_TypeDef_LineRBlkCtr0
typedef uint8 LineRBlkCtr0;

#  define Rte_TypeDef_LineRBlkCtr1
typedef uint8 LineRBlkCtr1;

#  define Rte_TypeDef_LineRC0
typedef float32 LineRC0;

#  define Rte_TypeDef_LineRC1
typedef float32 LineRC1;

#  define Rte_TypeDef_LineRC2
typedef float32 LineRC2;

#  define Rte_TypeDef_LineRC3
typedef float32 LineRC3;

#  define Rte_TypeDef_LineRChksm0
typedef uint8 LineRChksm0;

#  define Rte_TypeDef_LineRChksm1
typedef uint8 LineRChksm1;

#  define Rte_TypeDef_LineRExistPrblty
typedef float32 LineRExistPrblty;

#  define Rte_TypeDef_LineRRAlvCtr0
typedef uint8 LineRRAlvCtr0;

#  define Rte_TypeDef_LineRRAlvCtr1
typedef uint8 LineRRAlvCtr1;

#  define Rte_TypeDef_LineRRBlkCtr0
typedef uint8 LineRRBlkCtr0;

#  define Rte_TypeDef_LineRRBlkCtr1
typedef uint8 LineRRBlkCtr1;

#  define Rte_TypeDef_LineRRC0
typedef float32 LineRRC0;

#  define Rte_TypeDef_LineRRC1
typedef float32 LineRRC1;

#  define Rte_TypeDef_LineRRC2
typedef float32 LineRRC2;

#  define Rte_TypeDef_LineRRC3
typedef float32 LineRRC3;

#  define Rte_TypeDef_LineRRChksm0
typedef uint8 LineRRChksm0;

#  define Rte_TypeDef_LineRRChksm1
typedef uint8 LineRRChksm1;

#  define Rte_TypeDef_LineRRExistPrblty
typedef float32 LineRRExistPrblty;

#  define Rte_TypeDef_LineRRViewRngEnd
typedef float32 LineRRViewRngEnd;

#  define Rte_TypeDef_LineRRViewRngSt
typedef float32 LineRRViewRngSt;

#  define Rte_TypeDef_LineRViewRngEnd
typedef float32 LineRViewRngEnd;

#  define Rte_TypeDef_LineRViewRngSt
typedef float32 LineRViewRngSt;

#  define Rte_TypeDef_MinuteOfHour
typedef uint8 MinuteOfHour;

#  define Rte_TypeDef_MoF_AEBDclReqSts
typedef UInt8 MoF_AEBDclReqSts;

#  define Rte_TypeDef_MoF_AEBDclReqVal
typedef AEBDclReqVal MoF_AEBDclReqVal;

#  define Rte_TypeDef_MoF_AESActvCtrl
typedef UInt8 MoF_AESActvCtrl;

#  define Rte_TypeDef_MoF_BSDSysFltSts
typedef UInt8 MoF_BSDSysFltSts;

#  define Rte_TypeDef_MoF_BrakeSts
typedef UInt8 MoF_BrakeSts;

#  define Rte_TypeDef_MoF_BrkMainCylPres_VD
typedef Boolean MoF_BrkMainCylPres_VD;

#  define Rte_TypeDef_MoF_ChESSReqResp
typedef UInt8 MoF_ChESSReqResp;

#  define Rte_TypeDef_MoF_DOWSysFltSts
typedef UInt8 MoF_DOWSysFltSts;

#  define Rte_TypeDef_MoF_ESSSwReq
typedef UInt8 MoF_ESSSwReq;

#  define Rte_TypeDef_MoF_ESSSysFltSts
typedef UInt8 MoF_ESSSysFltSts;

#  define Rte_TypeDef_MoF_EnDragToqRducMonitor
typedef Boolean MoF_EnDragToqRducMonitor;

#  define Rte_TypeDef_MoF_FLWhlPulCnt
typedef UInt16 MoF_FLWhlPulCnt;

#  define Rte_TypeDef_MoF_FLWhlPulse
typedef UInt32 MoF_FLWhlPulse;

#  define Rte_TypeDef_MoF_FLWhlPulseDir
typedef UInt8 MoF_FLWhlPulseDir;

#  define Rte_TypeDef_MoF_FRWhlPulCnt
typedef UInt16 MoF_FRWhlPulCnt;

#  define Rte_TypeDef_MoF_FRWhlPulse
typedef UInt32 MoF_FRWhlPulse;

#  define Rte_TypeDef_MoF_FRWhlPulseDir
typedef UInt8 MoF_FRWhlPulseDir;

#  define Rte_TypeDef_MoF_HDCF
typedef Boolean MoF_HDCF;

#  define Rte_TypeDef_MoF_HDC_sts
typedef UInt8 MoF_HDC_sts;

#  define Rte_TypeDef_MoF_LDrvnWhlRotlDircn
typedef UInt8 MoF_LDrvnWhlRotlDircn;

#  define Rte_TypeDef_MoF_LNonDrvnWhlRotlDircn
typedef UInt8 MoF_LNonDrvnWhlRotlDircn;

#  define Rte_TypeDef_MoF_MSPSwReq
typedef UInt8 MoF_MSPSwReq;

#  define Rte_TypeDef_MoF_MSPSysFltSts
typedef UInt8 MoF_MSPSysFltSts;

#  define Rte_TypeDef_MoF_RAEBSysFltSts
typedef UInt8 MoF_RAEBSysFltSts;

#  define Rte_TypeDef_MoF_RCWSnstvtLvlReq
typedef UInt8 MoF_RCWSnstvtLvlReq;

#  define Rte_TypeDef_MoF_RCWSwReq
typedef UInt8 MoF_RCWSwReq;

#  define Rte_TypeDef_MoF_RCWSysFltSts
typedef UInt8 MoF_RCWSysFltSts;

#  define Rte_TypeDef_MoF_RDrvnWhlRotlDircn
typedef UInt8 MoF_RDrvnWhlRotlDircn;

#  define Rte_TypeDef_MoF_RLWhlPulCnt
typedef UInt16 MoF_RLWhlPulCnt;

#  define Rte_TypeDef_MoF_RLWhlPulse
typedef UInt32 MoF_RLWhlPulse;

#  define Rte_TypeDef_MoF_RLWhlPulseDir
typedef UInt8 MoF_RLWhlPulseDir;

#  define Rte_TypeDef_MoF_RNonDrvnWhlRotlDircn
typedef UInt8 MoF_RNonDrvnWhlRotlDircn;

#  define Rte_TypeDef_MoF_RRWhlPulCnt
typedef UInt16 MoF_RRWhlPulCnt;

#  define Rte_TypeDef_MoF_RRWhlPulse
typedef UInt32 MoF_RRWhlPulse;

#  define Rte_TypeDef_MoF_SteerWhlAgSign
typedef Boolean MoF_SteerWhlAgSign;

#  define Rte_TypeDef_MoF_SteerWhlSpdSign
typedef Boolean MoF_SteerWhlSpdSign;

#  define Rte_TypeDef_MoF_SteerWhlSpdVld
typedef Boolean MoF_SteerWhlSpdVld;

#  define Rte_TypeDef_MoF_TCSF
typedef Boolean MoF_TCSF;

#  define Rte_TypeDef_Module_status
typedef UInt8 Module_status;

#  define Rte_TypeDef_NOSLnChngSts
typedef UInt8 NOSLnChngSts;

#  define Rte_TypeDef_NavCtryCode
typedef uint8 NavCtryCode;

#  define Rte_TypeDef_NavSpdLmtVal
typedef float32 NavSpdLmtVal;

#  define Rte_TypeDef_NvM_BlockIdType
typedef uint16 NvM_BlockIdType;

#  define Rte_TypeDef_Obj0ExistPrblty
typedef float32 Obj0ExistPrblty;

#  define Rte_TypeDef_Obj0Id
typedef uint8 Obj0Id;

#  define Rte_TypeDef_Obj0LatRltvDist
typedef float32 Obj0LatRltvDist;

#  define Rte_TypeDef_Obj0LongtRltvDist
typedef float32 Obj0LongtRltvDist;

#  define Rte_TypeDef_Obj0LongtRltvSpd
typedef float32 Obj0LongtRltvSpd;

#  define Rte_TypeDef_Obj0SyncCtr
typedef uint8 Obj0SyncCtr;

#  define Rte_TypeDef_Obj1ExistPrblty
typedef float32 Obj1ExistPrblty;

#  define Rte_TypeDef_Obj1Id
typedef uint8 Obj1Id;

#  define Rte_TypeDef_Obj1LatRltvDist
typedef float32 Obj1LatRltvDist;

#  define Rte_TypeDef_Obj1LongtRltvDist
typedef float32 Obj1LongtRltvDist;

#  define Rte_TypeDef_Obj1LongtRltvSpd
typedef float32 Obj1LongtRltvSpd;

#  define Rte_TypeDef_Obj1SyncCtr
typedef uint8 Obj1SyncCtr;

#  define Rte_TypeDef_Obj2ExistPrblty
typedef float32 Obj2ExistPrblty;

#  define Rte_TypeDef_Obj2Id
typedef uint8 Obj2Id;

#  define Rte_TypeDef_Obj2LatRltvDist
typedef float32 Obj2LatRltvDist;

#  define Rte_TypeDef_Obj2LongtRltvDist
typedef float32 Obj2LongtRltvDist;

#  define Rte_TypeDef_Obj2LongtRltvSpd
typedef float32 Obj2LongtRltvSpd;

#  define Rte_TypeDef_Obj2SyncCtr
typedef uint8 Obj2SyncCtr;

#  define Rte_TypeDef_Obj3ExistPrblty
typedef float32 Obj3ExistPrblty;

#  define Rte_TypeDef_Obj3Id
typedef uint8 Obj3Id;

#  define Rte_TypeDef_Obj3LatRltvDist
typedef float32 Obj3LatRltvDist;

#  define Rte_TypeDef_Obj3LongtRltvDist
typedef float32 Obj3LongtRltvDist;

#  define Rte_TypeDef_Obj3LongtRltvSpd
typedef float32 Obj3LongtRltvSpd;

#  define Rte_TypeDef_Obj3SyncCtr
typedef uint8 Obj3SyncCtr;

#  define Rte_TypeDef_Obj4ExistPrblty
typedef float32 Obj4ExistPrblty;

#  define Rte_TypeDef_Obj4Id
typedef uint8 Obj4Id;

#  define Rte_TypeDef_Obj4LatRltvDist
typedef float32 Obj4LatRltvDist;

#  define Rte_TypeDef_Obj4LongtRltvDist
typedef float32 Obj4LongtRltvDist;

#  define Rte_TypeDef_Obj4LongtRltvSpd
typedef float32 Obj4LongtRltvSpd;

#  define Rte_TypeDef_Obj4SyncCtr
typedef uint8 Obj4SyncCtr;

#  define Rte_TypeDef_Obs0ExistPrblty
typedef float32 Obs0ExistPrblty;

#  define Rte_TypeDef_Obs1ExistPrblty
typedef float32 Obs1ExistPrblty;

#  define Rte_TypeDef_Obs2ExistPrblty
typedef float32 Obs2ExistPrblty;

#  define Rte_TypeDef_Obs3ExistPrblty
typedef float32 Obs3ExistPrblty;

#  define Rte_TypeDef_Obs4ExistPrblty
typedef float32 Obs4ExistPrblty;

#  define Rte_TypeDef_OtsdAirTemCrVal
typedef float32 OtsdAirTemCrVal;

#  define Rte_TypeDef_Out_AEBDclReqSts
typedef UInt8 Out_AEBDclReqSts;

#  define Rte_TypeDef_Out_AEBDclReqVal
typedef AEBDclReqVal Out_AEBDclReqVal;

#  define Rte_TypeDef_Out_AESActvCtrl
typedef UInt8 Out_AESActvCtrl;

#  define Rte_TypeDef_RBSWWrnng
typedef UInt8 RBSWWrnng;

#  define Rte_TypeDef_RDOWWrnng
typedef UInt8 RDOWWrnng;

#  define Rte_TypeDef_RDrvnWhlRotlDistPlsCtr
typedef uint16 RDrvnWhlRotlDistPlsCtr;

#  define Rte_TypeDef_RDrvnWhlRotlDistTmsp
typedef uint16 RDrvnWhlRotlDistTmsp;

#  define Rte_TypeDef_RDrvnWhlRotlDistTmspRlovCtr
typedef uint8 RDrvnWhlRotlDistTmspRlovCtr;

#  define Rte_TypeDef_RDrvnWhlRotlSeqNum
typedef uint8 RDrvnWhlRotlSeqNum;

#  define Rte_TypeDef_RLObsDist
typedef uint16 RLObsDist;

#  define Rte_TypeDef_RLWndDclnSpc
typedef float32 RLWndDclnSpc;

#  define Rte_TypeDef_RNonDrvnWhlRotlDistPC
typedef uint16 RNonDrvnWhlRotlDistPC;

#  define Rte_TypeDef_RNonDrvnWhlRotlDistT
typedef uint16 RNonDrvnWhlRotlDistT;

#  define Rte_TypeDef_RNonDrvnWhlRotlDistTRC
typedef uint8 RNonDrvnWhlRotlDistTRC;

#  define Rte_TypeDef_RNonDrvnWhlRotlSeqNum
typedef uint8 RNonDrvnWhlRotlSeqNum;

#  define Rte_TypeDef_RRCWWrnng
typedef UInt8 RRCWWrnng;

#  define Rte_TypeDef_RRObsDist
typedef uint16 RRObsDist;

#  define Rte_TypeDef_RRWndDclnSpc
typedef float32 RRWndDclnSpc;

#  define Rte_TypeDef_RRoadPrfShckDetnCfdc
typedef float32 RRoadPrfShckDetnCfdc;

#  define Rte_TypeDef_RRoadPrfShckDetnHght
typedef float32 RRoadPrfShckDetnHght;

#  define Rte_TypeDef_RRoadPrfShckLeadEdgeDist
typedef float32 RRoadPrfShckLeadEdgeDist;

#  define Rte_TypeDef_RdgeLAlvCtr0
typedef uint8 RdgeLAlvCtr0;

#  define Rte_TypeDef_RdgeLAlvCtr1
typedef uint8 RdgeLAlvCtr1;

#  define Rte_TypeDef_RdgeLBlkCtr0
typedef uint8 RdgeLBlkCtr0;

#  define Rte_TypeDef_RdgeLBlkCtr1
typedef uint8 RdgeLBlkCtr1;

#  define Rte_TypeDef_RdgeLC0
typedef float32 RdgeLC0;

#  define Rte_TypeDef_RdgeLC1
typedef float32 RdgeLC1;

#  define Rte_TypeDef_RdgeLC2
typedef float32 RdgeLC2;

#  define Rte_TypeDef_RdgeLC3
typedef float32 RdgeLC3;

#  define Rte_TypeDef_RdgeLChksm0
typedef uint8 RdgeLChksm0;

#  define Rte_TypeDef_RdgeLChksm1
typedef uint8 RdgeLChksm1;

#  define Rte_TypeDef_RdgeLExistPrblty
typedef float32 RdgeLExistPrblty;

#  define Rte_TypeDef_RdgeLViewRngEnd
typedef float32 RdgeLViewRngEnd;

#  define Rte_TypeDef_RdgeLViewRngSt
typedef float32 RdgeLViewRngSt;

#  define Rte_TypeDef_RdgeRAlvCtr0
typedef uint8 RdgeRAlvCtr0;

#  define Rte_TypeDef_RdgeRAlvCtr1
typedef uint8 RdgeRAlvCtr1;

#  define Rte_TypeDef_RdgeRBlkCtr0
typedef uint8 RdgeRBlkCtr0;

#  define Rte_TypeDef_RdgeRBlkCtr1
typedef uint8 RdgeRBlkCtr1;

#  define Rte_TypeDef_RdgeRC0
typedef float32 RdgeRC0;

#  define Rte_TypeDef_RdgeRC1
typedef float32 RdgeRC1;

#  define Rte_TypeDef_RdgeRC2
typedef float32 RdgeRC2;

#  define Rte_TypeDef_RdgeRC3
typedef float32 RdgeRC3;

#  define Rte_TypeDef_RdgeRChksm0
typedef uint8 RdgeRChksm0;

#  define Rte_TypeDef_RdgeRChksm1
typedef uint8 RdgeRChksm1;

#  define Rte_TypeDef_RdgeRExistPrblty
typedef float32 RdgeRExistPrblty;

#  define Rte_TypeDef_RdgeRViewRngEnd
typedef float32 RdgeRViewRngEnd;

#  define Rte_TypeDef_RdgeRViewRngSt
typedef float32 RdgeRViewRngSt;

#  define Rte_TypeDef_RrMidLObsDist
typedef uint16 RrMidLObsDist;

#  define Rte_TypeDef_RrMidRObsDist
typedef uint16 RrMidRObsDist;

#  define Rte_TypeDef_SCS_010ms_PDU00_RC
typedef uint8 SCS_010ms_PDU00_RC;

#  define Rte_TypeDef_SCUAPAAlvRC_TCM
typedef uint8 SCUAPAAlvRC_TCM;

#  define Rte_TypeDef_SecsOfMinute
typedef uint8 SecsOfMinute;

#  define Rte_TypeDef_Sign0AlvCtr
typedef uint8 Sign0AlvCtr;

#  define Rte_TypeDef_Sign0BlkCtr
typedef uint8 Sign0BlkCtr;

#  define Rte_TypeDef_Sign0Chksm
typedef uint8 Sign0Chksm;

#  define Rte_TypeDef_Sign0ExistPrblty
typedef float32 Sign0ExistPrblty;

#  define Rte_TypeDef_Sign0Hght
typedef float32 Sign0Hght;

#  define Rte_TypeDef_Sign0PosX
typedef float32 Sign0PosX;

#  define Rte_TypeDef_Sign0PosY
typedef float32 Sign0PosY;

#  define Rte_TypeDef_Sign0PosZ
typedef float32 Sign0PosZ;

#  define Rte_TypeDef_Sign0Wdth
typedef float32 Sign0Wdth;

#  define Rte_TypeDef_Sign1AlvCtr
typedef uint8 Sign1AlvCtr;

#  define Rte_TypeDef_Sign1BlkCtr
typedef uint8 Sign1BlkCtr;

#  define Rte_TypeDef_Sign1Chksm
typedef uint8 Sign1Chksm;

#  define Rte_TypeDef_Sign1ExistPrblty
typedef float32 Sign1ExistPrblty;

#  define Rte_TypeDef_Sign1Hght
typedef float32 Sign1Hght;

#  define Rte_TypeDef_Sign1PosX
typedef float32 Sign1PosX;

#  define Rte_TypeDef_Sign1PosY
typedef float32 Sign1PosY;

#  define Rte_TypeDef_Sign1PosZ
typedef float32 Sign1PosZ;

#  define Rte_TypeDef_Sign1Wdth
typedef float32 Sign1Wdth;

#  define Rte_TypeDef_Sign2AlvCtr
typedef uint8 Sign2AlvCtr;

#  define Rte_TypeDef_Sign2BlkCtr
typedef uint8 Sign2BlkCtr;

#  define Rte_TypeDef_Sign2Chksm
typedef uint8 Sign2Chksm;

#  define Rte_TypeDef_Sign2ExistPrblty
typedef float32 Sign2ExistPrblty;

#  define Rte_TypeDef_Sign2Hght
typedef float32 Sign2Hght;

#  define Rte_TypeDef_Sign2PosX
typedef float32 Sign2PosX;

#  define Rte_TypeDef_Sign2PosY
typedef float32 Sign2PosY;

#  define Rte_TypeDef_Sign2PosZ
typedef float32 Sign2PosZ;

#  define Rte_TypeDef_Sign2Wdth
typedef float32 Sign2Wdth;

#  define Rte_TypeDef_Sign3AlvCtr
typedef uint8 Sign3AlvCtr;

#  define Rte_TypeDef_Sign3BlkCtr
typedef uint8 Sign3BlkCtr;

#  define Rte_TypeDef_Sign3Chksm
typedef uint8 Sign3Chksm;

#  define Rte_TypeDef_Sign3ExistPrblty
typedef float32 Sign3ExistPrblty;

#  define Rte_TypeDef_Sign3Hght
typedef float32 Sign3Hght;

#  define Rte_TypeDef_Sign3PosX
typedef float32 Sign3PosX;

#  define Rte_TypeDef_Sign3PosY
typedef float32 Sign3PosY;

#  define Rte_TypeDef_Sign3PosZ
typedef float32 Sign3PosZ;

#  define Rte_TypeDef_Sign3Wdth
typedef float32 Sign3Wdth;

#  define Rte_TypeDef_SrfSlidePosSts
typedef float32 SrfSlidePosSts;

#  define Rte_TypeDef_StbM_CustomerIdType
typedef uint16 StbM_CustomerIdType;

#  define Rte_TypeDef_StbM_RateDeviationType
typedef sint16 StbM_RateDeviationType;

#  define Rte_TypeDef_StbM_SynchronizedTimeBaseType
typedef uint16 StbM_SynchronizedTimeBaseType;

#  define Rte_TypeDef_StbM_TimeDiffType
typedef sint32 StbM_TimeDiffType;

#  define Rte_TypeDef_TCS_Fail
typedef Boolean TCS_Fail;

#  define Rte_TypeDef_TCS_Sts
typedef Boolean TCS_Sts;

#  define Rte_TypeDef_TimeInMicrosecondsType
typedef uint32 TimeInMicrosecondsType;

#  define Rte_TypeDef_TrGear
typedef float32 TrGear;

#  define Rte_TypeDef_TrgtSpdReqCamr
typedef float32 TrgtSpdReqCamr;

#  define Rte_TypeDef_UsrDfnSlotPosA
typedef uint16 UsrDfnSlotPosA;

#  define Rte_TypeDef_UsrDfnSlotPosX
typedef uint16 UsrDfnSlotPosX;

#  define Rte_TypeDef_UsrDfnSlotPosY
typedef uint16 UsrDfnSlotPosY;

#  define Rte_TypeDef_VCU_CH_TorqOverrideReq
typedef uint8 VCU_CH_TorqOverrideReq;

#  define Rte_TypeDef_VehAccLat_VD
typedef Boolean VehAccLat_VD;

#  define Rte_TypeDef_VehAccLgt_VD
typedef Boolean VehAccLgt_VD;

#  define Rte_TypeDef_VehOdo
typedef uint32 VehOdo;

#  define Rte_TypeDef_VehYawRate_VD
typedef Boolean VehYawRate_VD;

#  define Rte_TypeDef_WdgM_CheckpointIdType
typedef uint16 WdgM_CheckpointIdType;

#  define Rte_TypeDef_WdgM_ModeType
typedef uint8 WdgM_ModeType;

#  define Rte_TypeDef_WdgM_SupervisedEntityIdType
typedef uint16 WdgM_SupervisedEntityIdType;

#  define Rte_TypeDef_functional_status
typedef UInt8 functional_status;

#  define Rte_TypeDef_lowbeam_sts
typedef UInt8 lowbeam_sts;

#  define Rte_TypeDef_AEBDspCmd
typedef uint8 AEBDspCmd;

#  define Rte_TypeDef_AutoLaneChngstyle
typedef uint8 AutoLaneChngstyle;

#  define Rte_TypeDef_BswM_ESH_Mode
typedef uint8 BswM_ESH_Mode;

#  define Rte_TypeDef_BswM_ESH_RunRequest
typedef uint8 BswM_ESH_RunRequest;

#  define Rte_TypeDef_ComM_ModeType
typedef uint8 ComM_ModeType;

#  define Rte_TypeDef_Dcm_CommunicationModeType
typedef uint8 Dcm_CommunicationModeType;

#  define Rte_TypeDef_Dcm_ConfirmationStatusType
typedef uint8 Dcm_ConfirmationStatusType;

#  define Rte_TypeDef_Dcm_ControlDtcSettingType
typedef uint8 Dcm_ControlDtcSettingType;

#  define Rte_TypeDef_Dcm_DiagnosticSessionControlType
typedef uint8 Dcm_DiagnosticSessionControlType;

#  define Rte_TypeDef_Dcm_NegativeResponseCodeType
typedef uint8 Dcm_NegativeResponseCodeType;

#  define Rte_TypeDef_Dcm_OpStatusType
typedef uint8 Dcm_OpStatusType;

#  define Rte_TypeDef_Dcm_ProtocolType
typedef uint8 Dcm_ProtocolType;

#  define Rte_TypeDef_Dcm_RequestKindType
typedef uint8 Dcm_RequestKindType;

#  define Rte_TypeDef_Dcm_SecLevelType
typedef uint8 Dcm_SecLevelType;

#  define Rte_TypeDef_Dcm_SesCtrlType
typedef uint8 Dcm_SesCtrlType;

#  define Rte_TypeDef_Dem_DTCFormatType
typedef uint8 Dem_DTCFormatType;

#  define Rte_TypeDef_Dem_DTCKindType
typedef uint8 Dem_DTCKindType;

#  define Rte_TypeDef_Dem_DTCOriginType
typedef uint16 Dem_DTCOriginType;

#  define Rte_TypeDef_Dem_DTCSeverityType
typedef uint8 Dem_DTCSeverityType;

#  define Rte_TypeDef_Dem_DTRControlType
typedef uint8 Dem_DTRControlType;

#  define Rte_TypeDef_Dem_DebounceResetStatusType
typedef uint8 Dem_DebounceResetStatusType;

#  define Rte_TypeDef_Dem_DebouncingStateType
typedef uint8 Dem_DebouncingStateType;

#  define Rte_TypeDef_Dem_EventStatusType
typedef uint8 Dem_EventStatusType;

#  define Rte_TypeDef_Dem_IndicatorStatusType
typedef uint8 Dem_IndicatorStatusType;

#  define Rte_TypeDef_Dem_InitMonitorReasonType
typedef uint8 Dem_InitMonitorReasonType;

#  define Rte_TypeDef_Dem_IumprDenomCondIdType
typedef uint8 Dem_IumprDenomCondIdType;

#  define Rte_TypeDef_Dem_IumprDenomCondStatusType
typedef uint8 Dem_IumprDenomCondStatusType;

#  define Rte_TypeDef_Dem_IumprReadinessGroupType
typedef uint8 Dem_IumprReadinessGroupType;

#  define Rte_TypeDef_Dem_MonitorStatusType
typedef uint8 Dem_MonitorStatusType;

#  define Rte_TypeDef_Dem_OperationCycleStateType
typedef uint8 Dem_OperationCycleStateType;

#  define Rte_TypeDef_Dem_UdsStatusByteType
typedef uint8 Dem_UdsStatusByteType;

#  define Rte_TypeDef_EHCSwDspCmd
typedef uint8 EHCSwDspCmd;

#  define Rte_TypeDef_EcuM_BootTargetType
typedef uint8 EcuM_BootTargetType;

#  define Rte_TypeDef_EcuM_ModeType
typedef uint8 EcuM_ModeType;

#  define Rte_TypeDef_EcuM_ShutdownCauseType
typedef uint8 EcuM_ShutdownCauseType;

#  define Rte_TypeDef_EcuM_StateType
typedef uint8 EcuM_StateType;

#  define Rte_TypeDef_ILCSwReq
typedef uint8 ILCSwReq;

#  define Rte_TypeDef_LDircnIndLghtF
typedef boolean LDircnIndLghtF;

#  define Rte_TypeDef_NOPSwReq
typedef uint8 NOPSwReq;

#  define Rte_TypeDef_NOPVcRmndrSwReq
typedef uint8 NOPVcRmndrSwReq;

#  define Rte_TypeDef_NvM_RequestResultType
typedef uint8 NvM_RequestResultType;

#  define Rte_TypeDef_NvM_ServiceIdType
typedef uint8 NvM_ServiceIdType;

#  define Rte_TypeDef_RDircnIndLghtF
typedef boolean RDircnIndLghtF;

#  define Rte_TypeDef_RstrFctryDeftsReq
typedef boolean RstrFctryDeftsReq;

#  define Rte_TypeDef_Rte_DT_DMS_050ms_PDU00_10
typedef uint8 Rte_DT_DMS_050ms_PDU00_10;

#  define Rte_TypeDef_Rte_DT_DMS_050ms_PDU00_11
typedef uint8 Rte_DT_DMS_050ms_PDU00_11;

#  define Rte_TypeDef_Rte_DT_DMS_050ms_PDU00_12
typedef uint8 Rte_DT_DMS_050ms_PDU00_12;

#  define Rte_TypeDef_Rte_DT_DMS_050ms_PDU00_13
typedef uint8 Rte_DT_DMS_050ms_PDU00_13;

#  define Rte_TypeDef_Rte_DT_DMS_050ms_PDU00_14
typedef uint8 Rte_DT_DMS_050ms_PDU00_14;

#  define Rte_TypeDef_Rte_DT_DMS_050ms_PDU00_15
typedef uint8 Rte_DT_DMS_050ms_PDU00_15;

#  define Rte_TypeDef_Rte_DT_DMS_050ms_PDU00_16
typedef uint8 Rte_DT_DMS_050ms_PDU00_16;

#  define Rte_TypeDef_Rte_DT_DMS_050ms_PDU00_17
typedef uint8 Rte_DT_DMS_050ms_PDU00_17;

#  define Rte_TypeDef_Rte_DT_DMS_050ms_PDU00_18
typedef uint8 Rte_DT_DMS_050ms_PDU00_18;

#  define Rte_TypeDef_Rte_DT_DMS_050ms_PDU00_5
typedef uint8 Rte_DT_DMS_050ms_PDU00_5;

#  define Rte_TypeDef_Rte_DT_DMS_050ms_PDU00_6
typedef uint8 Rte_DT_DMS_050ms_PDU00_6;

#  define Rte_TypeDef_Rte_DT_DMS_050ms_PDU00_7
typedef uint8 Rte_DT_DMS_050ms_PDU00_7;

#  define Rte_TypeDef_Rte_DT_DMS_050ms_PDU00_8
typedef uint8 Rte_DT_DMS_050ms_PDU00_8;

#  define Rte_TypeDef_Rte_DT_DMS_050ms_PDU00_9
typedef uint8 Rte_DT_DMS_050ms_PDU00_9;

#  define Rte_TypeDef_Rte_DT_EPS_020ms_PDU13_5
typedef uint8 Rte_DT_EPS_020ms_PDU13_5;

#  define Rte_TypeDef_Rte_DT_EPS_020ms_PDU13_6
typedef uint8 Rte_DT_EPS_020ms_PDU13_6;

#  define Rte_TypeDef_Rte_DT_HADS_020ms_PDU00_0
typedef uint8 Rte_DT_HADS_020ms_PDU00_0;

#  define Rte_TypeDef_Rte_DT_HADS_020ms_PDU00_10
typedef uint8 Rte_DT_HADS_020ms_PDU00_10;

#  define Rte_TypeDef_Rte_DT_HADS_020ms_PDU00_11
typedef uint8 Rte_DT_HADS_020ms_PDU00_11;

#  define Rte_TypeDef_Rte_DT_HADS_020ms_PDU00_12
typedef uint8 Rte_DT_HADS_020ms_PDU00_12;

#  define Rte_TypeDef_Rte_DT_HADS_020ms_PDU00_13
typedef uint8 Rte_DT_HADS_020ms_PDU00_13;

#  define Rte_TypeDef_Rte_DT_HADS_020ms_PDU00_14
typedef uint8 Rte_DT_HADS_020ms_PDU00_14;

#  define Rte_TypeDef_Rte_DT_HADS_020ms_PDU00_15
typedef uint8 Rte_DT_HADS_020ms_PDU00_15;

#  define Rte_TypeDef_Rte_DT_HADS_020ms_PDU00_6
typedef uint8 Rte_DT_HADS_020ms_PDU00_6;

#  define Rte_TypeDef_Rte_DT_HADS_020ms_PDU00_7
typedef uint8 Rte_DT_HADS_020ms_PDU00_7;

#  define Rte_TypeDef_Rte_DT_HADS_020ms_PDU00_8
typedef uint8 Rte_DT_HADS_020ms_PDU00_8;

#  define Rte_TypeDef_Rte_DT_HADS_020ms_PDU00_9
typedef uint8 Rte_DT_HADS_020ms_PDU00_9;

#  define Rte_TypeDef_Rte_DT_HOD_050ms_PDU00_10
typedef uint8 Rte_DT_HOD_050ms_PDU00_10;

#  define Rte_TypeDef_Rte_DT_HOD_050ms_PDU00_12
typedef uint8 Rte_DT_HOD_050ms_PDU00_12;

#  define Rte_TypeDef_Rte_DT_HOD_050ms_PDU00_5
typedef uint8 Rte_DT_HOD_050ms_PDU00_5;

#  define Rte_TypeDef_Rte_DT_HOD_050ms_PDU00_6
typedef uint8 Rte_DT_HOD_050ms_PDU00_6;

#  define Rte_TypeDef_Rte_DT_HOD_050ms_PDU00_7
typedef uint8 Rte_DT_HOD_050ms_PDU00_7;

#  define Rte_TypeDef_Rte_DT_HOD_050ms_PDU00_8
typedef uint8 Rte_DT_HOD_050ms_PDU00_8;

#  define Rte_TypeDef_Rte_DT_IBS_010ms_PDU12_0
typedef uint8 Rte_DT_IBS_010ms_PDU12_0;

#  define Rte_TypeDef_Rte_DT_IBS_010ms_PDU12_1
typedef uint8 Rte_DT_IBS_010ms_PDU12_1;

#  define Rte_TypeDef_Rte_DT_IPK_050ms_PDU31_0
typedef uint8 Rte_DT_IPK_050ms_PDU31_0;

#  define Rte_TypeDef_Rte_DT_IPK_050ms_PDU31_1
typedef uint8 Rte_DT_IPK_050ms_PDU31_1;

#  define Rte_TypeDef_Rte_DT_LADS_050ms_PDU02_0
typedef uint8 Rte_DT_LADS_050ms_PDU02_0;

#  define Rte_TypeDef_Rte_DT_LADS_050ms_PDU02_1
typedef uint8 Rte_DT_LADS_050ms_PDU02_1;

#  define Rte_TypeDef_Rte_DT_LADS_050ms_PDU02_2
typedef uint8 Rte_DT_LADS_050ms_PDU02_2;

#  define Rte_TypeDef_Rte_DT_LADS_050ms_PDU05_0
typedef uint8 Rte_DT_LADS_050ms_PDU05_0;

#  define Rte_TypeDef_Rte_DT_LADS_050ms_PDU05_1
typedef uint8 Rte_DT_LADS_050ms_PDU05_1;

#  define Rte_TypeDef_Rte_DT_PGM_050ms_PDU00_0
typedef uint8 Rte_DT_PGM_050ms_PDU00_0;

#  define Rte_TypeDef_Rte_DT_PGM_050ms_PDU00_10
typedef uint8 Rte_DT_PGM_050ms_PDU00_10;

#  define Rte_TypeDef_Rte_DT_PGM_050ms_PDU00_11
typedef uint8 Rte_DT_PGM_050ms_PDU00_11;

#  define Rte_TypeDef_Rte_DT_PGM_050ms_PDU00_12
typedef uint8 Rte_DT_PGM_050ms_PDU00_12;

#  define Rte_TypeDef_Rte_DT_PGM_050ms_PDU00_13
typedef uint8 Rte_DT_PGM_050ms_PDU00_13;

#  define Rte_TypeDef_Rte_DT_PGM_050ms_PDU00_7
typedef uint8 Rte_DT_PGM_050ms_PDU00_7;

#  define Rte_TypeDef_Rte_DT_PGM_050ms_PDU00_8
typedef uint8 Rte_DT_PGM_050ms_PDU00_8;

#  define Rte_TypeDef_Rte_DT_PGM_050ms_PDU00_9
typedef uint8 Rte_DT_PGM_050ms_PDU00_9;

#  define Rte_TypeDef_Rte_DT_RHRDA_050ms_PDU04_0
typedef uint8 Rte_DT_RHRDA_050ms_PDU04_0;

#  define Rte_TypeDef_Rte_DT_RHRDA_050ms_PDU04_1
typedef uint8 Rte_DT_RHRDA_050ms_PDU04_1;

#  define Rte_TypeDef_Rte_DT_RHRDA_050ms_PDU04_14
typedef uint8 Rte_DT_RHRDA_050ms_PDU04_14;

#  define Rte_TypeDef_Rte_DT_RHRDA_050ms_PDU04_15
typedef uint8 Rte_DT_RHRDA_050ms_PDU04_15;

#  define Rte_TypeDef_Rte_DT_RHRDA_050ms_PDU04_16
typedef uint8 Rte_DT_RHRDA_050ms_PDU04_16;

#  define Rte_TypeDef_Rte_DT_RHRDA_050ms_PDU04_2
typedef uint8 Rte_DT_RHRDA_050ms_PDU04_2;

#  define Rte_TypeDef_Rte_DT_RHRDA_050ms_PDU04_3
typedef uint8 Rte_DT_RHRDA_050ms_PDU04_3;

#  define Rte_TypeDef_Rte_DT_RHRDA_050ms_PDU04_4
typedef uint8 Rte_DT_RHRDA_050ms_PDU04_4;

#  define Rte_TypeDef_Rte_DT_RHRDA_050ms_PDU04_5
typedef uint8 Rte_DT_RHRDA_050ms_PDU04_5;

#  define Rte_TypeDef_Rte_DT_RHRDA_050ms_PDU04_6
typedef uint8 Rte_DT_RHRDA_050ms_PDU04_6;

#  define Rte_TypeDef_Rte_DT_RHRDA_050ms_PDU04_7
typedef uint8 Rte_DT_RHRDA_050ms_PDU04_7;

#  define Rte_TypeDef_Rte_DT_RHRDA_050ms_PDU04_8
typedef uint8 Rte_DT_RHRDA_050ms_PDU04_8;

#  define Rte_TypeDef_Rte_DT_RHRDA_050ms_PDU04_9
typedef uint8 Rte_DT_RHRDA_050ms_PDU04_9;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_0
typedef boolean Rte_DT_SCS_020ms_PDU03_0;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_1
typedef boolean Rte_DT_SCS_020ms_PDU03_1;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_10
typedef uint8 Rte_DT_SCS_020ms_PDU03_10;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_11
typedef boolean Rte_DT_SCS_020ms_PDU03_11;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_12
typedef boolean Rte_DT_SCS_020ms_PDU03_12;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_13
typedef uint8 Rte_DT_SCS_020ms_PDU03_13;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_14
typedef boolean Rte_DT_SCS_020ms_PDU03_14;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_15
typedef uint8 Rte_DT_SCS_020ms_PDU03_15;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_16
typedef uint8 Rte_DT_SCS_020ms_PDU03_16;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_17
typedef uint8 Rte_DT_SCS_020ms_PDU03_17;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_2
typedef boolean Rte_DT_SCS_020ms_PDU03_2;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_21
typedef uint8 Rte_DT_SCS_020ms_PDU03_21;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_22
typedef boolean Rte_DT_SCS_020ms_PDU03_22;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_23
typedef uint8 Rte_DT_SCS_020ms_PDU03_23;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_24
typedef boolean Rte_DT_SCS_020ms_PDU03_24;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_25
typedef uint8 Rte_DT_SCS_020ms_PDU03_25;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_26
typedef uint8 Rte_DT_SCS_020ms_PDU03_26;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_27
typedef uint8 Rte_DT_SCS_020ms_PDU03_27;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_28
typedef uint8 Rte_DT_SCS_020ms_PDU03_28;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_29
typedef boolean Rte_DT_SCS_020ms_PDU03_29;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_3
typedef boolean Rte_DT_SCS_020ms_PDU03_3;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_4
typedef uint8 Rte_DT_SCS_020ms_PDU03_4;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_5
typedef uint8 Rte_DT_SCS_020ms_PDU03_5;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_6
typedef boolean Rte_DT_SCS_020ms_PDU03_6;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_7
typedef boolean Rte_DT_SCS_020ms_PDU03_7;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_8
typedef uint8 Rte_DT_SCS_020ms_PDU03_8;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU03_9
typedef boolean Rte_DT_SCS_020ms_PDU03_9;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU04_0
typedef uint8 Rte_DT_SCS_020ms_PDU04_0;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU04_1
typedef uint8 Rte_DT_SCS_020ms_PDU04_1;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU04_10
typedef uint8 Rte_DT_SCS_020ms_PDU04_10;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU04_2
typedef boolean Rte_DT_SCS_020ms_PDU04_2;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU04_3
typedef uint8 Rte_DT_SCS_020ms_PDU04_3;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU04_4
typedef uint8 Rte_DT_SCS_020ms_PDU04_4;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU04_5
typedef uint8 Rte_DT_SCS_020ms_PDU04_5;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU04_6
typedef uint8 Rte_DT_SCS_020ms_PDU04_6;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU04_7
typedef uint8 Rte_DT_SCS_020ms_PDU04_7;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU04_8
typedef uint8 Rte_DT_SCS_020ms_PDU04_8;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU04_9
typedef uint8 Rte_DT_SCS_020ms_PDU04_9;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU18_0
typedef uint8 Rte_DT_SCS_020ms_PDU18_0;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU18_1
typedef uint8 Rte_DT_SCS_020ms_PDU18_1;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU18_2
typedef uint8 Rte_DT_SCS_020ms_PDU18_2;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU18_3
typedef uint8 Rte_DT_SCS_020ms_PDU18_3;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU18_4
typedef uint8 Rte_DT_SCS_020ms_PDU18_4;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU18_5
typedef uint8 Rte_DT_SCS_020ms_PDU18_5;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU21_0
typedef uint8 Rte_DT_SCS_020ms_PDU21_0;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU21_1
typedef boolean Rte_DT_SCS_020ms_PDU21_1;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU21_10
typedef uint8 Rte_DT_SCS_020ms_PDU21_10;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU21_15
typedef boolean Rte_DT_SCS_020ms_PDU21_15;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU21_16
typedef boolean Rte_DT_SCS_020ms_PDU21_16;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU21_17
typedef uint8 Rte_DT_SCS_020ms_PDU21_17;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU21_18
typedef uint8 Rte_DT_SCS_020ms_PDU21_18;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU21_19
typedef uint8 Rte_DT_SCS_020ms_PDU21_19;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU21_2
typedef uint8 Rte_DT_SCS_020ms_PDU21_2;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU21_3
typedef boolean Rte_DT_SCS_020ms_PDU21_3;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU21_4
typedef uint8 Rte_DT_SCS_020ms_PDU21_4;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU21_5
typedef uint8 Rte_DT_SCS_020ms_PDU21_5;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU21_6
typedef uint8 Rte_DT_SCS_020ms_PDU21_6;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU21_7
typedef uint8 Rte_DT_SCS_020ms_PDU21_7;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU21_8
typedef uint8 Rte_DT_SCS_020ms_PDU21_8;

#  define Rte_TypeDef_Rte_DT_SCS_020ms_PDU21_9
typedef uint8 Rte_DT_SCS_020ms_PDU21_9;

#  define Rte_TypeDef_Rte_DT_SCU_010ms_PDU00_10
typedef uint8 Rte_DT_SCU_010ms_PDU00_10;

#  define Rte_TypeDef_Rte_DT_SCU_010ms_PDU00_11
typedef uint8 Rte_DT_SCU_010ms_PDU00_11;

#  define Rte_TypeDef_Rte_DT_SCU_010ms_PDU00_12
typedef uint8 Rte_DT_SCU_010ms_PDU00_12;

#  define Rte_TypeDef_Rte_DT_SCU_010ms_PDU00_13
typedef uint8 Rte_DT_SCU_010ms_PDU00_13;

#  define Rte_TypeDef_Rte_DT_SCU_010ms_PDU00_14
typedef uint8 Rte_DT_SCU_010ms_PDU00_14;

#  define Rte_TypeDef_Rte_DT_SCU_010ms_PDU00_15
typedef uint8 Rte_DT_SCU_010ms_PDU00_15;

#  define Rte_TypeDef_Rte_DT_SCU_010ms_PDU00_16
typedef uint8 Rte_DT_SCU_010ms_PDU00_16;

#  define Rte_TypeDef_Rte_DT_SCU_010ms_PDU00_17
typedef uint8 Rte_DT_SCU_010ms_PDU00_17;

#  define Rte_TypeDef_Rte_DT_SCU_010ms_PDU00_18
typedef uint8 Rte_DT_SCU_010ms_PDU00_18;

#  define Rte_TypeDef_Rte_DT_SCU_010ms_PDU00_19
typedef uint8 Rte_DT_SCU_010ms_PDU00_19;

#  define Rte_TypeDef_Rte_DT_SCU_010ms_PDU00_3
typedef uint8 Rte_DT_SCU_010ms_PDU00_3;

#  define Rte_TypeDef_Rte_DT_SCU_010ms_PDU00_4
typedef uint8 Rte_DT_SCU_010ms_PDU00_4;

#  define Rte_TypeDef_Rte_DT_SCU_010ms_PDU00_5
typedef uint8 Rte_DT_SCU_010ms_PDU00_5;

#  define Rte_TypeDef_Rte_DT_SCU_010ms_PDU00_6
typedef uint8 Rte_DT_SCU_010ms_PDU00_6;

#  define Rte_TypeDef_Rte_DT_SCU_010ms_PDU00_7
typedef uint8 Rte_DT_SCU_010ms_PDU00_7;

#  define Rte_TypeDef_Rte_DT_SCU_010ms_PDU00_8
typedef uint8 Rte_DT_SCU_010ms_PDU00_8;

#  define Rte_TypeDef_Rte_DT_SCU_010ms_PDU00_9
typedef uint8 Rte_DT_SCU_010ms_PDU00_9;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_0
typedef boolean Rte_DT_SDM_SFCANFD_FrP00_0;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_1
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_1;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_10
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_10;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_11
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_11;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_12
typedef boolean Rte_DT_SDM_SFCANFD_FrP00_12;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_13
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_13;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_14
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_14;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_15
typedef boolean Rte_DT_SDM_SFCANFD_FrP00_15;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_16
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_16;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_17
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_17;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_18
typedef boolean Rte_DT_SDM_SFCANFD_FrP00_18;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_19
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_19;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_2
typedef boolean Rte_DT_SDM_SFCANFD_FrP00_2;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_20
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_20;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_21
typedef boolean Rte_DT_SDM_SFCANFD_FrP00_21;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_22
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_22;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_23
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_23;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_24
typedef boolean Rte_DT_SDM_SFCANFD_FrP00_24;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_25
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_25;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_26
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_26;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_27
typedef boolean Rte_DT_SDM_SFCANFD_FrP00_27;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_28
typedef boolean Rte_DT_SDM_SFCANFD_FrP00_28;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_29
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_29;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_3
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_3;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_30
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_30;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_31
typedef boolean Rte_DT_SDM_SFCANFD_FrP00_31;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_32
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_32;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_33
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_33;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_36
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_36;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_37
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_37;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_4
typedef boolean Rte_DT_SDM_SFCANFD_FrP00_4;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_5
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_5;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_6
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_6;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_7
typedef uint8 Rte_DT_SDM_SFCANFD_FrP00_7;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_8
typedef boolean Rte_DT_SDM_SFCANFD_FrP00_8;

#  define Rte_TypeDef_Rte_DT_SDM_SFCANFD_FrP00_9
typedef boolean Rte_DT_SDM_SFCANFD_FrP00_9;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID10_10
typedef boolean Rte_DT_SG_MRR_ObjID10_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID10_19
typedef uint8 Rte_DT_SG_MRR_ObjID10_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID11_10
typedef boolean Rte_DT_SG_MRR_ObjID11_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID11_19
typedef uint8 Rte_DT_SG_MRR_ObjID11_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID12_10
typedef boolean Rte_DT_SG_MRR_ObjID12_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID12_19
typedef uint8 Rte_DT_SG_MRR_ObjID12_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID13_10
typedef boolean Rte_DT_SG_MRR_ObjID13_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID13_19
typedef uint8 Rte_DT_SG_MRR_ObjID13_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID14_10
typedef boolean Rte_DT_SG_MRR_ObjID14_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID14_19
typedef uint8 Rte_DT_SG_MRR_ObjID14_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID15_10
typedef boolean Rte_DT_SG_MRR_ObjID15_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID15_19
typedef uint8 Rte_DT_SG_MRR_ObjID15_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID16_10
typedef boolean Rte_DT_SG_MRR_ObjID16_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID16_19
typedef uint8 Rte_DT_SG_MRR_ObjID16_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID17_10
typedef boolean Rte_DT_SG_MRR_ObjID17_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID17_19
typedef uint8 Rte_DT_SG_MRR_ObjID17_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID18_10
typedef boolean Rte_DT_SG_MRR_ObjID18_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID18_19
typedef uint8 Rte_DT_SG_MRR_ObjID18_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID19_10
typedef boolean Rte_DT_SG_MRR_ObjID19_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID19_19
typedef uint8 Rte_DT_SG_MRR_ObjID19_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID1_10
typedef boolean Rte_DT_SG_MRR_ObjID1_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID1_19
typedef uint8 Rte_DT_SG_MRR_ObjID1_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID20_10
typedef boolean Rte_DT_SG_MRR_ObjID20_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID20_19
typedef uint8 Rte_DT_SG_MRR_ObjID20_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID21_10
typedef boolean Rte_DT_SG_MRR_ObjID21_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID21_19
typedef uint8 Rte_DT_SG_MRR_ObjID21_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID22_10
typedef boolean Rte_DT_SG_MRR_ObjID22_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID22_19
typedef uint8 Rte_DT_SG_MRR_ObjID22_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID23_10
typedef boolean Rte_DT_SG_MRR_ObjID23_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID23_19
typedef uint8 Rte_DT_SG_MRR_ObjID23_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID24_10
typedef boolean Rte_DT_SG_MRR_ObjID24_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID24_19
typedef uint8 Rte_DT_SG_MRR_ObjID24_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID25_10
typedef boolean Rte_DT_SG_MRR_ObjID25_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID25_19
typedef uint8 Rte_DT_SG_MRR_ObjID25_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID26_10
typedef boolean Rte_DT_SG_MRR_ObjID26_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID26_19
typedef uint8 Rte_DT_SG_MRR_ObjID26_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID27_10
typedef boolean Rte_DT_SG_MRR_ObjID27_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID27_19
typedef uint8 Rte_DT_SG_MRR_ObjID27_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID28_10
typedef boolean Rte_DT_SG_MRR_ObjID28_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID28_19
typedef uint8 Rte_DT_SG_MRR_ObjID28_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID29_10
typedef boolean Rte_DT_SG_MRR_ObjID29_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID29_19
typedef uint8 Rte_DT_SG_MRR_ObjID29_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID2_10
typedef boolean Rte_DT_SG_MRR_ObjID2_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID2_19
typedef uint8 Rte_DT_SG_MRR_ObjID2_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID30_10
typedef boolean Rte_DT_SG_MRR_ObjID30_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID30_19
typedef uint8 Rte_DT_SG_MRR_ObjID30_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID31_10
typedef boolean Rte_DT_SG_MRR_ObjID31_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID31_19
typedef uint8 Rte_DT_SG_MRR_ObjID31_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID32_10
typedef boolean Rte_DT_SG_MRR_ObjID32_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID32_19
typedef uint8 Rte_DT_SG_MRR_ObjID32_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID3_10
typedef boolean Rte_DT_SG_MRR_ObjID3_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID3_19
typedef uint8 Rte_DT_SG_MRR_ObjID3_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID4_10
typedef boolean Rte_DT_SG_MRR_ObjID4_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID4_19
typedef uint8 Rte_DT_SG_MRR_ObjID4_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID5_10
typedef boolean Rte_DT_SG_MRR_ObjID5_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID5_19
typedef uint8 Rte_DT_SG_MRR_ObjID5_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID6_10
typedef boolean Rte_DT_SG_MRR_ObjID6_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID6_19
typedef uint8 Rte_DT_SG_MRR_ObjID6_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID7_10
typedef boolean Rte_DT_SG_MRR_ObjID7_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID7_19
typedef uint8 Rte_DT_SG_MRR_ObjID7_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID8_10
typedef boolean Rte_DT_SG_MRR_ObjID8_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID8_19
typedef uint8 Rte_DT_SG_MRR_ObjID8_19;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID9_10
typedef boolean Rte_DT_SG_MRR_ObjID9_10;

#  define Rte_TypeDef_Rte_DT_SG_MRR_ObjID9_19
typedef uint8 Rte_DT_SG_MRR_ObjID9_19;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_0
typedef boolean Rte_DT_SWS_050ms_PDU00_0;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_1
typedef boolean Rte_DT_SWS_050ms_PDU00_1;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_10
typedef uint8 Rte_DT_SWS_050ms_PDU00_10;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_11
typedef uint8 Rte_DT_SWS_050ms_PDU00_11;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_12
typedef uint8 Rte_DT_SWS_050ms_PDU00_12;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_13
typedef boolean Rte_DT_SWS_050ms_PDU00_13;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_14
typedef uint8 Rte_DT_SWS_050ms_PDU00_14;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_15
typedef uint8 Rte_DT_SWS_050ms_PDU00_15;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_16
typedef uint8 Rte_DT_SWS_050ms_PDU00_16;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_2
typedef boolean Rte_DT_SWS_050ms_PDU00_2;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_22
typedef boolean Rte_DT_SWS_050ms_PDU00_22;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_23
typedef boolean Rte_DT_SWS_050ms_PDU00_23;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_24
typedef boolean Rte_DT_SWS_050ms_PDU00_24;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_25
typedef uint8 Rte_DT_SWS_050ms_PDU00_25;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_26
typedef boolean Rte_DT_SWS_050ms_PDU00_26;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_27
typedef boolean Rte_DT_SWS_050ms_PDU00_27;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_28
typedef boolean Rte_DT_SWS_050ms_PDU00_28;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_29
typedef boolean Rte_DT_SWS_050ms_PDU00_29;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_3
typedef boolean Rte_DT_SWS_050ms_PDU00_3;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_30
typedef boolean Rte_DT_SWS_050ms_PDU00_30;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_31
typedef boolean Rte_DT_SWS_050ms_PDU00_31;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_4
typedef boolean Rte_DT_SWS_050ms_PDU00_4;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_6
typedef boolean Rte_DT_SWS_050ms_PDU00_6;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_7
typedef boolean Rte_DT_SWS_050ms_PDU00_7;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_8
typedef boolean Rte_DT_SWS_050ms_PDU00_8;

#  define Rte_TypeDef_Rte_DT_SWS_050ms_PDU00_9
typedef boolean Rte_DT_SWS_050ms_PDU00_9;

#  define Rte_TypeDef_Rte_DT_TCM_025ms_PDU20_6
typedef uint8 Rte_DT_TCM_025ms_PDU20_6;

#  define Rte_TypeDef_Rte_DT_TCM_PTCAN_FrP02_0
typedef uint8 Rte_DT_TCM_PTCAN_FrP02_0;

#  define Rte_TypeDef_Rte_DT_TCM_PTCAN_FrP02_1
typedef boolean Rte_DT_TCM_PTCAN_FrP02_1;

#  define Rte_TypeDef_Rte_DT_TCM_PTCAN_FrP02_10
typedef uint8 Rte_DT_TCM_PTCAN_FrP02_10;

#  define Rte_TypeDef_Rte_DT_TCM_PTCAN_FrP02_11
typedef boolean Rte_DT_TCM_PTCAN_FrP02_11;

#  define Rte_TypeDef_Rte_DT_TCM_PTCAN_FrP02_12
typedef uint8 Rte_DT_TCM_PTCAN_FrP02_12;

#  define Rte_TypeDef_Rte_DT_TCM_PTCAN_FrP02_13
typedef uint8 Rte_DT_TCM_PTCAN_FrP02_13;

#  define Rte_TypeDef_Rte_DT_TCM_PTCAN_FrP02_14
typedef uint8 Rte_DT_TCM_PTCAN_FrP02_14;

#  define Rte_TypeDef_Rte_DT_TCM_PTCAN_FrP02_15
typedef boolean Rte_DT_TCM_PTCAN_FrP02_15;

#  define Rte_TypeDef_Rte_DT_TCM_PTCAN_FrP02_16
typedef uint8 Rte_DT_TCM_PTCAN_FrP02_16;

#  define Rte_TypeDef_Rte_DT_TCM_PTCAN_FrP02_17
typedef boolean Rte_DT_TCM_PTCAN_FrP02_17;

#  define Rte_TypeDef_Rte_DT_TCM_PTCAN_FrP02_18
typedef uint8 Rte_DT_TCM_PTCAN_FrP02_18;

#  define Rte_TypeDef_Rte_DT_TCM_PTCAN_FrP02_19
typedef uint8 Rte_DT_TCM_PTCAN_FrP02_19;

#  define Rte_TypeDef_Rte_DT_TCM_PTCAN_FrP02_2
typedef uint8 Rte_DT_TCM_PTCAN_FrP02_2;

#  define Rte_TypeDef_Rte_DT_TCM_PTCAN_FrP02_20
typedef uint8 Rte_DT_TCM_PTCAN_FrP02_20;

#  define Rte_TypeDef_Rte_DT_TCM_PTCAN_FrP02_21
typedef uint8 Rte_DT_TCM_PTCAN_FrP02_21;

#  define Rte_TypeDef_Rte_DT_TCM_PTCAN_FrP02_22
typedef uint8 Rte_DT_TCM_PTCAN_FrP02_22;

#  define Rte_TypeDef_Rte_DT_TCM_PTCAN_FrP02_8
typedef boolean Rte_DT_TCM_PTCAN_FrP02_8;

#  define Rte_TypeDef_Rte_DT_TCM_PTCAN_FrP02_9
typedef uint8 Rte_DT_TCM_PTCAN_FrP02_9;

#  define Rte_TypeDef_StbM_MasterConfigType
typedef uint8 StbM_MasterConfigType;

#  define Rte_TypeDef_StbM_TimeBaseNotificationType
typedef uint32 StbM_TimeBaseNotificationType;

#  define Rte_TypeDef_StbM_TimeBaseStatusType
typedef uint8 StbM_TimeBaseStatusType;

#  define Rte_TypeDef_StbM_TimeBaseStatusType_1
typedef uint8 StbM_TimeBaseStatusType_1;

#  define Rte_TypeDef_StbM_TimeBaseStatusType_2
typedef uint8 StbM_TimeBaseStatusType_2;

#  define Rte_TypeDef_WdgMMode
typedef uint8 WdgMMode;

#  define Rte_TypeDef_WdgM_GlobalStatusType
typedef uint8 WdgM_GlobalStatusType;

#  define Rte_TypeDef_WdgM_LocalStatusType
typedef uint8 WdgM_LocalStatusType;

#  define Rte_TypeDef_Rte_DT_FObstaclesType_0
typedef ObstacleType Rte_DT_FObstaclesType_0[32];

#  define Rte_TypeDef_Rte_DT_FObstaclesType_1
typedef KeyObstacleIndexType Rte_DT_FObstaclesType_1[9];

#  define Rte_TypeDef_UssObstacleData
typedef UssObstacleData_st UssObstacleData[50];

#  define Rte_TypeDef_UssRawData
typedef UssRawData_st UssRawData[12];

#  define Rte_TypeDef_rt_Array_CmrFsPoint_64
typedef CmrFsPoint rt_Array_CmrFsPoint_64[64];

#  define Rte_TypeDef_rt_Array_SsmCurvatureType_10
typedef SsmCurvatureType rt_Array_SsmCurvatureType_10[10];

#  define Rte_TypeDef_rt_Array_SsmObsType_32
typedef SsmObsType rt_Array_SsmObsType_32[32];

#  define Rte_TypeDef_rt_Array_SsmRefPtType_20
typedef SsmRefPtType rt_Array_SsmRefPtType_20[20];

#  define Rte_TypeDef_rt_Array_SsmSpeedRangeType_2
typedef SsmSpeedRangeType rt_Array_SsmSpeedRangeType_2[2];

#  define Rte_TypeDef_rt_Array_VisObjType_10
typedef VisObjType rt_Array_VisObjType_10[20];

#  define Rte_TypeDef_AEBEdrType
typedef struct
{
  Boolean AEBEdrValidFlag;
  UInt8 AEBEdrType;
  UInt8 objectStatus;
  UInt8 objectClass;
  Float tEventDuration;
  UInt8 espBrakeJerkActive;
  UInt8 espAutomatedBrakingActive;
  Float dyvAtdxvMin;
  Float RecordElement;
  Float maxDzv;
  Float minDzv;
  Float startDzv;
  Float endDzv;
  Float startProb1Obstacle;
  Float endProb1Obstacle;
  Float maxProb1Obstacle;
  Float minProb1Obstacle;
  Float startwExistProb;
  Float endwExistProb;
  Float maxwExistProb;
  float32 minwExistProb;
  Float wConstElem;
  Float prob1Moving;
  Float prob1HasBeenObservedMoving;
  UInt8 videoConfirmed;
  UInt8 endDriverOverride;
  Float endDriveby;
  Float endOverlap;
  Float cntAlive;
  Float dVarXv;
  Float vVarXv;
  Float aVarXv;
  Float vVarYv;
  objectDxArray_11 objectDxArray;
  objectDyArray_11 objectDyArray;
  objectVxArray_11 objectVxArray;
  objectVyArray_11 objectVyArray;
  objectAxArray_11 objectAxArray;
  objectAyArray_11 objectAyArray;
} AEBEdrType;

#  define Rte_TypeDef_AccObjDispTyp
typedef struct
{
  AccObjDispSubTyp CipObjDisp;
  AccObjDispSubTyp SipObjDisp;
  AccObjDispSubTyp FrntLeObjDisp;
  AccObjDispSubTyp FrntRiObjDisp;
  AccObjDispSubTyp SecFrntLeObjDisp;
  AccObjDispSubTyp SecFrntRiObjDisp;
  AccObjDispSubTyp FrontPed1ObjDisp;
  AccObjDispSubTyp FrontPed2ObjDisp;
  AccObjDispSubTyp BackLeftObj;
  AccObjDispSubTyp BackRightObj;
  AccObjDispSubTyp BackCipvObj;
  AccObjDispSubTyp SideLeftObj;
  AccObjDispSubTyp SideRightObj;
  AccObjDispSubTyp AnyFrontLocationObj0;
  AccObjDispSubTyp AnyFrontLocationObj1;
  AccObjDispSubTyp AnyFrontLocationObj2;
  AccObjDispSubTyp AnyFrontLocationObj3;
  AccObjDispSubTyp AnyFrontLocationObj4;
  AccObjDispSubTyp AnyFrontLocationObj5;
  AccObjDispSubTyp AnyFrontLocationObj6;
  AccObjDispSubTyp AnyLocation0;
  AccObjDispSubTyp AnyLocation1;
  AccObjDispSubTyp AnyLocation2;
  AccObjDispSubTyp AnyLocation3;
  AccObjDispSubTyp AnyLocation4;
  AccObjDispSubTyp AnyLocation5;
  AccObjDispSubTyp AnyLocation6;
  AccObjDispSubTyp AnyLocation7;
  AccObjDispSubTyp AnyLocation8;
  AccObjDispSubTyp AnyLocation9;
  AccObjDispSubTyp AnyLocation10;
  AccObjDispSubTyp AnyLocation11;
  AccObjDispSubTyp AnyLocation12;
  AccObjDispSubTyp AnyLocation13;
  AccObjDispSubTyp AnyLocation14;
  AccObjDispSubTyp AnyLocation15;
} AccObjDispTyp;

#  define Rte_TypeDef_CmrFsType
typedef struct
{
  UInt8 points_num;
  rt_Array_CmrFsPoint_64 fs_pt;
} CmrFsType;

#  define Rte_TypeDef_CmrLaneTyp
typedef struct
{
  LaneMkrTyp LaneMkrLe;
  LaneMkrTyp LaneMkrRi;
  LaneMkrTyp LaneMkrNxtLe;
  LaneMkrTyp LaneMkrNxtRi;
  Float StopLineDis;
  UInt8 StopLineExist;
} CmrLaneTyp;

#  define Rte_TypeDef_ConftPathTyp
typedef struct
{
  Float c0;
  Float c1;
  Float c2;
  rt_Array_Float_3 c3_array;
  rt_Array_Float_3 segment_length_array;
  Float lane_width;
  UInt8 valid;
  UInt8 change_to_left;
  UInt8 change_to_right;
  UInt8 on_sliproad_left;
  UInt8 on_sliproad_right;
} ConftPathTyp;

#  define Rte_TypeDef_Ctrl_stType
typedef struct
{
  HeaderType Header;
  AccControlType AccControl;
  SteerControlType SteerControl;
  GearControlType GearControl;
  BcmControlType BcmControl;
  SoundControlType SoundControl;
  DisplayControlType DisplayControl;
  VelocityControlType VelocityControl;
} Ctrl_stType;

#  define Rte_TypeDef_DMS_050ms_PDU00
typedef struct
{
  uint8 DMS_050ms_PDU00_CRC;
  uint8 DMS_050ms_PDU00_RC;
  uint8 DMS_050ms_PDU00_Reserve01;
  uint8 DMS_050ms_PDU00_Reserve02;
  uint8 DMS_050ms_PDU00_Reserve03;
  Rte_DT_DMS_050ms_PDU00_5 DMSAdoWrnngDspCmd;
  Rte_DT_DMS_050ms_PDU00_6 DMSAudReq;
  Rte_DT_DMS_050ms_PDU00_7 DMSDspCmd;
  Rte_DT_DMS_050ms_PDU00_8 DMSHapticWrnngDspCmd;
  Rte_DT_DMS_050ms_PDU00_9 DMSSts;
  Rte_DT_DMS_050ms_PDU00_10 DMSVbnLvlReq;
  Rte_DT_DMS_050ms_PDU00_11 DMSWrnngReq;
  Rte_DT_DMS_050ms_PDU00_12 DrvrAbnormBehvor;
  Rte_DT_DMS_050ms_PDU00_13 DrvrDstcnSts;
  Rte_DT_DMS_050ms_PDU00_14 DrvrFtgLvl;
  Rte_DT_DMS_050ms_PDU00_15 DrvrGazeRgn;
  Rte_DT_DMS_050ms_PDU00_16 DrvrPrst;
  Rte_DT_DMS_050ms_PDU00_17 EyeOnRoad;
  Rte_DT_DMS_050ms_PDU00_18 SpfngDet;
} DMS_050ms_PDU00;

#  define Rte_TypeDef_EPS_020ms_PDU13
typedef struct
{
  uint8 EPS_020ms_PDU13_CRC;
  uint8 EPS_020ms_PDU13_RC;
  uint16 EPS_020ms_PDU13_Reserve01;
  uint8 EPS_020ms_PDU13_Reserve02;
  uint16 EPS_020ms_PDU13_Reserve03;
  Rte_DT_EPS_020ms_PDU13_5 EPSAPAInh;
  Rte_DT_EPS_020ms_PDU13_6 EPSAPASts;
} EPS_020ms_PDU13;

#  define Rte_TypeDef_FObstaclesType
typedef struct
{
  Rte_DT_FObstaclesType_0 obstacle;
  Rte_DT_FObstaclesType_1 key_obstacle;
  UInt8 obstacle_num;
} FObstaclesType;

#  define Rte_TypeDef_FunctionStateToHmiMsgType
typedef struct
{
  HeaderType Header;
  UInt8 functionState;
  UInt32 inhibitReason;
  UInt32 handsoffLevel;
  DriverSettingType DriverSetting;
  UInt8 isLatOverride;
  UInt8 isLonOverride;
} FunctionStateToHmiMsgType;

#  define Rte_TypeDef_HADS_020ms_PDU00
typedef struct
{
  Rte_DT_HADS_020ms_PDU00_0 DistOutODD;
  uint8 HADS_020ms_PDU00_CRC;
  uint8 HADS_020ms_PDU00_RC;
  uint8 HADS_020ms_PDU00_Reserve03;
  uint8 HADS_020ms_PDU00_Reserve04;
  uint8 HADS_020ms_PDU00_Reserve05;
  Rte_DT_HADS_020ms_PDU00_6 NOPMsgReq;
  Rte_DT_HADS_020ms_PDU00_7 NOPSysFltSts;
  Rte_DT_HADS_020ms_PDU00_8 NOPSysSts;
  Rte_DT_HADS_020ms_PDU00_9 SHWAEPBAppdReq;
  Rte_DT_HADS_020ms_PDU00_10 SHWAIndSts;
  Rte_DT_HADS_020ms_PDU00_11 SHWASysFltSts;
  Rte_DT_HADS_020ms_PDU00_12 SHWASysMsg;
  Rte_DT_HADS_020ms_PDU00_13 SHWASysReqHzrdLghtReqSts;
  Rte_DT_HADS_020ms_PDU00_14 SHWASysSts;
  Rte_DT_HADS_020ms_PDU00_15 SHWASysTakeOver;
} HADS_020ms_PDU00;

#  define Rte_TypeDef_HOD_050ms_PDU00
typedef struct
{
  uint8 HOD_050ms_PDU00_CRC;
  uint8 HOD_050ms_PDU00_RC;
  uint8 HOD_050ms_PDU00_Reserve01;
  uint8 HOD_050ms_PDU00_Reserve02;
  uint8 HOD_050ms_PDU00_Reserve03;
  Rte_DT_HOD_050ms_PDU00_5 HODDetnSts_l;
  Rte_DT_HOD_050ms_PDU00_6 HODdiagSts_l;
  Rte_DT_HOD_050ms_PDU00_7 HODSysSts_l;
  Rte_DT_HOD_050ms_PDU00_8 HODTchZone1Sts_l;
  uint8 HODTchZone1Val_l;
  Rte_DT_HOD_050ms_PDU00_10 HODTchZone2Sts_l;
  uint8 HODTchZone2Val_l;
  Rte_DT_HOD_050ms_PDU00_12 HODTchZone3Sts_l;
  uint8 HODTchZone3Val_l;
} HOD_050ms_PDU00;

#  define Rte_TypeDef_HmiTarsTypFor1V
typedef struct
{
  TsObjTyp FrontPedObj0;
  TsObjTyp FrontPedObj1;
} HmiTarsTypFor1V;

#  define Rte_TypeDef_HmiTarsTypForMV
typedef struct
{
  TsObjTyp BackLeftObj;
  TsObjTyp BackRightObj;
  TsObjTyp BackCipvObj;
  TsObjTyp SideLeftObj;
  TsObjTyp SideRightObj;
  TsObjTyp AnyFrontLocationObj0;
  TsObjTyp AnyFrontLocationObj1;
  TsObjTyp AnyFrontLocationObj2;
  TsObjTyp AnyFrontLocationObj3;
  TsObjTyp AnyFrontLocationObj4;
  TsObjTyp AnyFrontLocationObj5;
  TsObjTyp AnyFrontLocationObj6;
  TsObjTyp AnyLocation0;
  TsObjTyp AnyLocation1;
  TsObjTyp AnyLocation2;
  TsObjTyp AnyLocation3;
  TsObjTyp AnyLocation4;
  TsObjTyp AnyLocation5;
  TsObjTyp AnyLocation6;
  TsObjTyp AnyLocation7;
  TsObjTyp AnyLocation8;
  TsObjTyp AnyLocation9;
  TsObjTyp AnyLocation10;
  TsObjTyp AnyLocation11;
  TsObjTyp AnyLocation12;
  TsObjTyp AnyLocation13;
  TsObjTyp AnyLocation14;
  TsObjTyp AnyLocation15;
} HmiTarsTypForMV;

#  define Rte_TypeDef_IBS_010ms_PDU12
typedef struct
{
  Rte_DT_IBS_010ms_PDU12_0 BrkPdlAppd;
  Rte_DT_IBS_010ms_PDU12_1 BrkPdlAppdV;
  uint8 IBS_010ms_PDU12_CRC;
  uint8 IBS_010ms_PDU12_RC;
  uint8 IBS_010ms_PDU12_Reserve01;
  uint32 IBS_010ms_PDU12_Reserve02;
  uint32 IBS_010ms_PDU12_Reserve03;
} IBS_010ms_PDU12;

#  define Rte_TypeDef_IPK_050ms_PDU31
typedef struct
{
  Rte_DT_IPK_050ms_PDU31_0 ADASDspdErInHMI;
  Rte_DT_IPK_050ms_PDU31_1 ADASErDetcByHMI;
  uint8 IPK_050ms_PDU31_CRC;
  uint8 IPK_050ms_PDU31_RC;
  uint32 IPK_050ms_PDU31_Reserve01;
  uint32 IPK_050ms_PDU31_Reserve02;
} IPK_050ms_PDU31;

#  define Rte_TypeDef_LADS_050ms_PDU02
typedef struct
{
  Rte_DT_LADS_050ms_PDU02_0 APAAFnInd;
  Rte_DT_LADS_050ms_PDU02_1 APAEnRunReq;
  Rte_DT_LADS_050ms_PDU02_2 APASts;
  uint8 LADS_050ms_PDU02_CRC;
  uint8 LADS_050ms_PDU02_RC;
  uint8 LADS_050ms_PDU02_Reserve01;
  uint8 LADS_050ms_PDU02_Reserve02;
  uint16 LADS_050ms_PDU02_Reserve03;
  uint16 LADS_050ms_PDU02_Reserve04;
} LADS_050ms_PDU02;

#  define Rte_TypeDef_LADS_050ms_PDU05
typedef struct
{
  Rte_DT_LADS_050ms_PDU05_0 APASCUReqSts;
  Rte_DT_LADS_050ms_PDU05_1 APAShifterPosReqd;
  uint8 LADS_050ms_PDU05_CRC;
  uint8 LADS_050ms_PDU05_RC;
  uint32 LADS_050ms_PDU05_Reserve01;
  uint32 LADS_050ms_PDU05_Reserve02;
} LADS_050ms_PDU05;

#  define Rte_TypeDef_LaneBordrForDsp
typedef struct
{
  LineBorderTyp LineBorderLe;
  LineBorderTyp LineBorderRi;
  LineBorderTyp LineBorderNxtLe;
  LineBorderTyp LineBorderNxtRi;
} LaneBordrForDsp;

#  define Rte_TypeDef_LineParamsType
typedef struct
{
  Rte_DT_LineParamsType_0 coeffs;
  Float start_x;
  Float end_x;
} LineParamsType;

#  define Rte_TypeDef_NOACtrl_stType
typedef struct
{
  HeaderType Header;
  AccControlType AccControl;
  SteerControlType SteerControl;
  GearControlType GearControl;
  BcmControlType BcmControl;
  SoundControlType SoundControl;
  DisplayControlType DisplayControl;
  VelocityControlType VelocityControl;
} NOACtrl_stType;

#  define Rte_TypeDef_NOAStateOutMsg_stType
typedef struct
{
  HeaderType Header;
  UInt8 state;
  UInt32 inhibitReason;
  DriverSettingType DriverSetting;
  UInt8 NOSLnChngSts;
  UInt8 nos_state;
  UInt8 soft_switch;
} NOAStateOutMsg_stType;

#  define Rte_TypeDef_PGM_050ms_PDU00
typedef struct
{
  Rte_DT_PGM_050ms_PDU00_0 MainPwrFltRsn;
  uint8 PGM_050ms_PDU00_CRC;
  uint8 PGM_050ms_PDU00_RC;
  uint8 PGM_050ms_PDU00_Reserve01;
  uint16 PGM_050ms_PDU00_Reserve02;
  uint16 PGM_050ms_PDU00_Reserve03;
  uint8 PGM_050ms_PDU00_Reserve04;
  Rte_DT_PGM_050ms_PDU00_7 PGMDiags;
  Rte_DT_PGM_050ms_PDU00_8 PGMFltRsn;
  Rte_DT_PGM_050ms_PDU00_9 PGMSts;
  Rte_DT_PGM_050ms_PDU00_10 PGMSwSts;
  Rte_DT_PGM_050ms_PDU00_11 PwrSysStsInfoToAutoDrvng;
  Rte_DT_PGM_050ms_PDU00_12 PwrSysStsToAutoDrvng;
  Rte_DT_PGM_050ms_PDU00_13 RednPwrFltRsn;
} PGM_050ms_PDU00;

#  define Rte_TypeDef_RHRDA_050ms_PDU04
typedef struct
{
  Rte_DT_RHRDA_050ms_PDU04_0 DOWSelSts;
  Rte_DT_RHRDA_050ms_PDU04_1 LBSDAndLCAWrnng;
  Rte_DT_RHRDA_050ms_PDU04_2 LDOWWrnng;
  Rte_DT_RHRDA_050ms_PDU04_3 LRCTAWrnng;
  Rte_DT_RHRDA_050ms_PDU04_4 RBSDAndLCAWrnng;
  Rte_DT_RHRDA_050ms_PDU04_5 RCTSysSelSts;
  Rte_DT_RHRDA_050ms_PDU04_6 RCWSelSts;
  Rte_DT_RHRDA_050ms_PDU04_7 RCWWrnng;
  Rte_DT_RHRDA_050ms_PDU04_8 RDASysSta;
  Rte_DT_RHRDA_050ms_PDU04_9 RDOWWrnng;
  uint8 RHRDA_050ms_PDU04_CRC;
  uint8 RHRDA_050ms_PDU04_RC;
  uint8 RHRDA_050ms_PDU04_Reserve01;
  uint32 RHRDA_050ms_PDU04_Reserve02;
  Rte_DT_RHRDA_050ms_PDU04_14 RLRdrEmgcLineChngWrnng;
  Rte_DT_RHRDA_050ms_PDU04_15 RRCTAWrnng;
  Rte_DT_RHRDA_050ms_PDU04_16 RRRdrEmgcLineChngWrnng;
} RHRDA_050ms_PDU04;

#  define Rte_TypeDef_SCS_020ms_PDU03
typedef struct
{
  Rte_DT_SCS_020ms_PDU03_0 ABSA;
  Rte_DT_SCS_020ms_PDU03_1 ABSF;
  Rte_DT_SCS_020ms_PDU03_2 ABSIO;
  Rte_DT_SCS_020ms_PDU03_3 ACCBrkngA;
  Rte_DT_SCS_020ms_PDU03_4 AutoHoldMsg;
  Rte_DT_SCS_020ms_PDU03_5 AutoHoldSysSts;
  Rte_DT_SCS_020ms_PDU03_6 BrkSysBrkLghtsReqd;
  Rte_DT_SCS_020ms_PDU03_7 BrkSysHillRlbkCtrlAvlbl;
  Rte_DT_SCS_020ms_PDU03_8 BrkSysHillRlbkCtrlSts;
  Rte_DT_SCS_020ms_PDU03_9 BrkSysHillStAstAvlbl;
  Rte_DT_SCS_020ms_PDU03_10 BrkSysHillStAstSts;
  Rte_DT_SCS_020ms_PDU03_11 BrkSysRedBrkTlltReq;
  Rte_DT_SCS_020ms_PDU03_12 EmgcBrkA;
  Rte_DT_SCS_020ms_PDU03_13 EmgcBrkAV;
  Rte_DT_SCS_020ms_PDU03_14 EnDragToqRducnA;
  Rte_DT_SCS_020ms_PDU03_15 HDCSysSts;
  Rte_DT_SCS_020ms_PDU03_16 MulColBrkA;
  Rte_DT_SCS_020ms_PDU03_17 MultColBrkAvlblySts;
  uint8 SCS_020ms_PDU03_CRC;
  uint8 SCS_020ms_PDU03_RC;
  uint8 SCS_020ms_PDU03_Reserve01;
  Rte_DT_SCS_020ms_PDU03_21 SCSMdSts;
  Rte_DT_SCS_020ms_PDU03_22 TCSA;
  Rte_DT_SCS_020ms_PDU03_23 TCSDrvrItnt;
  Rte_DT_SCS_020ms_PDU03_24 TCSEnbd;
  Rte_DT_SCS_020ms_PDU03_25 TCSOpngMd;
  Rte_DT_SCS_020ms_PDU03_26 TCSOpngSts;
  Rte_DT_SCS_020ms_PDU03_27 VSEMd;
  Rte_DT_SCS_020ms_PDU03_28 VSESts;
  Rte_DT_SCS_020ms_PDU03_29 VSESysA;
} SCS_020ms_PDU03;

#  define Rte_TypeDef_SCS_020ms_PDU04
typedef struct
{
  Rte_DT_SCS_020ms_PDU04_0 EPBAppcnSts;
  Rte_DT_SCS_020ms_PDU04_1 EPBAvlblySts;
  Rte_DT_SCS_020ms_PDU04_2 EPBCCCanclReqd;
  Rte_DT_SCS_020ms_PDU04_3 EPBFlrSts;
  Rte_DT_SCS_020ms_PDU04_4 EPBSts;
  Rte_DT_SCS_020ms_PDU04_5 EPBSwSts;
  Rte_DT_SCS_020ms_PDU04_6 EPBSwStsV;
  Rte_DT_SCS_020ms_PDU04_7 EPBSysAudWrnngReq;
  Rte_DT_SCS_020ms_PDU04_8 EPBSysDspMsgReq;
  Rte_DT_SCS_020ms_PDU04_9 EPBSysStsIndReq;
  Rte_DT_SCS_020ms_PDU04_10 EPBSysWrnngIndReq;
  uint8 SCS_020ms_PDU04_CRC;
  uint8 SCS_020ms_PDU04_RC;
  uint32 SCS_020ms_PDU04_Reserve01;
} SCS_020ms_PDU04;

#  define Rte_TypeDef_SCS_020ms_PDU18
typedef struct
{
  Rte_DT_SCS_020ms_PDU18_0 ChACCAccReqResp;
  Rte_DT_SCS_020ms_PDU18_1 ChACCReqFlrSts;
  Rte_DT_SCS_020ms_PDU18_2 ChAEBBrkJerkReqResp;
  Rte_DT_SCS_020ms_PDU18_3 ChAEBDclReqResp;
  Rte_DT_SCS_020ms_PDU18_4 ChAEBHydBrkAstReqResp;
  Rte_DT_SCS_020ms_PDU18_5 ChAEBPrflReqResp;
  uint8 SCS_020ms_PDU18_CRC;
  uint8 SCS_020ms_PDU18_RC;
  uint16 SCS_020ms_PDU18_Reserve01;
  uint8 SCS_020ms_PDU18_Reserve02;
  uint8 SCS_020ms_PDU18_Reserve03;
  uint16 SCS_020ms_PDU18_Reserve04;
} SCS_020ms_PDU18;

#  define Rte_TypeDef_SCS_020ms_PDU21
typedef struct
{
  Rte_DT_SCS_020ms_PDU21_0 EBDActiveSt;
  Rte_DT_SCS_020ms_PDU21_1 EBDFaultSt;
  Rte_DT_SCS_020ms_PDU21_2 EPBAPAAvlbl;
  Rte_DT_SCS_020ms_PDU21_3 HBAActiveSt;
  Rte_DT_SCS_020ms_PDU21_4 HBAValidSt;
  Rte_DT_SCS_020ms_PDU21_5 HBBValidSt;
  Rte_DT_SCS_020ms_PDU21_6 HydBrkBostrA;
  Rte_DT_SCS_020ms_PDU21_7 LDrvnWhlRotlDircnSdslSts;
  Rte_DT_SCS_020ms_PDU21_8 LNonDrvnWhlRotlDircnSdslSts;
  Rte_DT_SCS_020ms_PDU21_9 RDrvnWhlRotlDircnSdslSts;
  Rte_DT_SCS_020ms_PDU21_10 RNonDrvnWhlRotlDircnSdslSts;
  uint8 SCS_020ms_PDU21_CRC;
  uint8 SCS_020ms_PDU21_RC;
  uint16 SCS_020ms_PDU21_Reserve01;
  uint32 SCS_020ms_PDU21_Reserve02;
  Rte_DT_SCS_020ms_PDU21_15 SCSActvnOfVehHoldFlag;
  Rte_DT_SCS_020ms_PDU21_16 SCSAPADrvrSts;
  Rte_DT_SCS_020ms_PDU21_17 SCSLongtCtrlrAvlbly;
  Rte_DT_SCS_020ms_PDU21_18 SCSLongtCtrlrCtrlSts;
  Rte_DT_SCS_020ms_PDU21_19 SCSLongtCtrlrFlrSts;
} SCS_020ms_PDU21;

#  define Rte_TypeDef_SCU_010ms_PDU00
typedef struct
{
  uint8 SCU_010ms_PDU00_CRC;
  uint8 SCU_010ms_PDU00_RC;
  uint8 SCU_010ms_PDU00_Reserve01;
  Rte_DT_SCU_010ms_PDU00_3 SCUActrMvmntSts;
  Rte_DT_SCU_010ms_PDU00_4 SCUAPAUnderCtrlA;
  Rte_DT_SCU_010ms_PDU00_5 SCUDrvnIntrvnPos;
  Rte_DT_SCU_010ms_PDU00_6 SCUDrvrIntrvnA;
  Rte_DT_SCU_010ms_PDU00_7 SCUExtdShifterActrPos;
  Rte_DT_SCU_010ms_PDU00_8 SCUExtdShifterFlr;
  Rte_DT_SCU_010ms_PDU00_9 SCUFltLvlWrnng;
  Rte_DT_SCU_010ms_PDU00_10 SCUParkActrSts;
  Rte_DT_SCU_010ms_PDU00_11 SCUParkLckReq;
  Rte_DT_SCU_010ms_PDU00_12 SCUParkLckReqV;
  Rte_DT_SCU_010ms_PDU00_13 SCUShftLvrPosIncnstntFlag;
  Rte_DT_SCU_010ms_PDU00_14 SCUShftLvrPosTrgt;
  Rte_DT_SCU_010ms_PDU00_15 SCUShifterLvrPos;
  Rte_DT_SCU_010ms_PDU00_16 SCUShifterOpngRmndr;
  Rte_DT_SCU_010ms_PDU00_17 SCUShifterPosInvsn;
  Rte_DT_SCU_010ms_PDU00_18 SCUTapUpDwnSwSta;
  Rte_DT_SCU_010ms_PDU00_19 SCUTapUpDwnSwStaInvsn;
} SCU_010ms_PDU00;

#  define Rte_TypeDef_SDM_SFCANFD_FrP00
typedef struct
{
  Rte_DT_SDM_SFCANFD_FrP00_0 AirbagDpl;
  Rte_DT_SDM_SFCANFD_FrP00_1 AirbagDplInvsn;
  Rte_DT_SDM_SFCANFD_FrP00_2 AirbagSysFlt;
  Rte_DT_SDM_SFCANFD_FrP00_3 AirbagSysFltIndCmd;
  Rte_DT_SDM_SFCANFD_FrP00_4 DrvrSbltAtc;
  Rte_DT_SDM_SFCANFD_FrP00_5 DrvrSbltAtcV;
  Rte_DT_SDM_SFCANFD_FrP00_6 FasnDrvrSbltIndCmd;
  Rte_DT_SDM_SFCANFD_FrP00_7 FasnFrtPsngSbltIndCmd;
  Rte_DT_SDM_SFCANFD_FrP00_8 FasnSbltAudRmndr;
  Rte_DT_SDM_SFCANFD_FrP00_9 FasnSecRowLSbltAtc;
  Rte_DT_SDM_SFCANFD_FrP00_10 FasnSecRowLSbltAtcV;
  Rte_DT_SDM_SFCANFD_FrP00_11 FasnSecRowLSbltIndCmd;
  Rte_DT_SDM_SFCANFD_FrP00_12 FasnSecRowMidSbltAtc;
  Rte_DT_SDM_SFCANFD_FrP00_13 FasnSecRowMidSbltAtcV;
  Rte_DT_SDM_SFCANFD_FrP00_14 FasnSecRowMidSbltIndCmd;
  Rte_DT_SDM_SFCANFD_FrP00_15 FasnSecRowRSbltAtc;
  Rte_DT_SDM_SFCANFD_FrP00_16 FasnSecRowRSbltAtcV;
  Rte_DT_SDM_SFCANFD_FrP00_17 FasnSecRowRSbltIndCmd;
  Rte_DT_SDM_SFCANFD_FrP00_18 FasnThrdRowLSbltAtc;
  Rte_DT_SDM_SFCANFD_FrP00_19 FasnThrdRowLSbltAtcV;
  Rte_DT_SDM_SFCANFD_FrP00_20 FasnThrdRowLSbltIndCmd;
  Rte_DT_SDM_SFCANFD_FrP00_21 FasnThrdRowMidSbltAtc;
  Rte_DT_SDM_SFCANFD_FrP00_22 FasnThrdRowMidSbltAtcV;
  Rte_DT_SDM_SFCANFD_FrP00_23 FasnThrdRowMidSbltIndCmd;
  Rte_DT_SDM_SFCANFD_FrP00_24 FasnThrdRowRSbltAtc;
  Rte_DT_SDM_SFCANFD_FrP00_25 FasnThrdRowRSbltAtcV;
  Rte_DT_SDM_SFCANFD_FrP00_26 FasnThrdRowRSbltIndCmd;
  Rte_DT_SDM_SFCANFD_FrP00_27 FrtPsngAirbagDsblIndF;
  Rte_DT_SDM_SFCANFD_FrP00_28 FrtPsngAirbagEnbIndF;
  Rte_DT_SDM_SFCANFD_FrP00_29 FrtPsngAirbagIndReq;
  Rte_DT_SDM_SFCANFD_FrP00_30 FrtPsngAirbagSwSts;
  Rte_DT_SDM_SFCANFD_FrP00_31 FrtPsngSbltAtc;
  Rte_DT_SDM_SFCANFD_FrP00_32 FrtPsngSbltAtcV;
  Rte_DT_SDM_SFCANFD_FrP00_33 PedtrnProtnSysIndrCmd;
  uint8 SDM_020ms_PDU00_CRC;
  uint8 SDM_020ms_PDU00_RC;
  Rte_DT_SDM_SFCANFD_FrP00_36 SeatOccptnNum;
  Rte_DT_SDM_SFCANFD_FrP00_37 VehCrshTyp;
} SDM_SFCANFD_FrP00;

#  define Rte_TypeDef_SG_MRR_ObjID1
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_1;
  uint8 MRR_Obj_MsgCounter_1;
  float32 MRR_ObjAccelX_1;
  float32 MRR_ObjAccelY_1;
  float32 MRR_ObjDistX_1;
  float32 MRR_ObjDistXStd_1;
  float32 MRR_ObjDistY_1;
  float32 MRR_ObjDistYStd_1;
  float32 MRR_ObjExistProb_1;
  uint8 MRR_ObjFusionCamID_1;
  Rte_DT_SG_MRR_ObjID1_10 MRR_ObjFusionSig_1;
  uint8 MRR_ObjID_1;
  float32 MRR_ObjObstacleProb_1;
  float32 MRR_ObjRAccelXStd_1;
  float32 MRR_ObjRAccelYStd_1;
  float32 MRR_ObjRelVelX_1;
  float32 MRR_ObjRelVelXStd_1;
  float32 MRR_ObjRelVelY_1;
  float32 MRR_ObjRelVelYStd_1;
  Rte_DT_SG_MRR_ObjID1_19 MRR_ObjType_1;
} SG_MRR_ObjID1;

#  define Rte_TypeDef_SG_MRR_ObjID10
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_10;
  uint8 MRR_Obj_MsgCounter_10;
  float32 MRR_ObjAccelX_10;
  float32 MRR_ObjAccelY_10;
  float32 MRR_ObjDistX_10;
  float32 MRR_ObjDistXStd_10;
  float32 MRR_ObjDistY_10;
  float32 MRR_ObjDistYStd_10;
  float32 MRR_ObjExistProb_10;
  uint8 MRR_ObjFusionCamID_10;
  Rte_DT_SG_MRR_ObjID10_10 MRR_ObjFusionSig_10;
  uint8 MRR_ObjID_10;
  float32 MRR_ObjObstacleProb_10;
  float32 MRR_ObjRAccelXStd_10;
  float32 MRR_ObjRAccelYStd_10;
  float32 MRR_ObjRelVelX_10;
  float32 MRR_ObjRelVelXStd_10;
  float32 MRR_ObjRelVelY_10;
  float32 MRR_ObjRelVelYStd_10;
  Rte_DT_SG_MRR_ObjID10_19 MRR_ObjType_10;
} SG_MRR_ObjID10;

#  define Rte_TypeDef_SG_MRR_ObjID11
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_11;
  uint8 MRR_Obj_MsgCounter_11;
  float32 MRR_ObjAccelX_11;
  float32 MRR_ObjAccelY_11;
  float32 MRR_ObjDistX_11;
  float32 MRR_ObjDistXStd_11;
  float32 MRR_ObjDistY_11;
  float32 MRR_ObjDistYStd_11;
  float32 MRR_ObjExistProb_11;
  uint8 MRR_ObjFusionCamID_11;
  Rte_DT_SG_MRR_ObjID11_10 MRR_ObjFusionSig_11;
  uint8 MRR_ObjID_11;
  float32 MRR_ObjObstacleProb_11;
  float32 MRR_ObjRAccelXStd_11;
  float32 MRR_ObjRAccelYStd_11;
  float32 MRR_ObjRelVelX_11;
  float32 MRR_ObjRelVelXStd_11;
  float32 MRR_ObjRelVelY_11;
  float32 MRR_ObjRelVelYStd_11;
  Rte_DT_SG_MRR_ObjID11_19 MRR_ObjType_11;
} SG_MRR_ObjID11;

#  define Rte_TypeDef_SG_MRR_ObjID12
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_12;
  uint8 MRR_Obj_MsgCounter_12;
  float32 MRR_ObjAccelX_12;
  float32 MRR_ObjAccelY_12;
  float32 MRR_ObjDistX_12;
  float32 MRR_ObjDistXStd_12;
  float32 MRR_ObjDistY_12;
  float32 MRR_ObjDistYStd_12;
  float32 MRR_ObjExistProb_12;
  uint8 MRR_ObjFusionCamID_12;
  Rte_DT_SG_MRR_ObjID12_10 MRR_ObjFusionSig_12;
  uint8 MRR_ObjID_12;
  float32 MRR_ObjObstacleProb_12;
  float32 MRR_ObjRAccelXStd_12;
  float32 MRR_ObjRAccelYStd_12;
  float32 MRR_ObjRelVelX_12;
  float32 MRR_ObjRelVelXStd_12;
  float32 MRR_ObjRelVelY_12;
  float32 MRR_ObjRelVelYStd_12;
  Rte_DT_SG_MRR_ObjID12_19 MRR_ObjType_12;
} SG_MRR_ObjID12;

#  define Rte_TypeDef_SG_MRR_ObjID13
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_13;
  uint8 MRR_Obj_MsgCounter_13;
  float32 MRR_ObjAccelX_13;
  float32 MRR_ObjAccelY_13;
  float32 MRR_ObjDistX_13;
  float32 MRR_ObjDistXStd_13;
  float32 MRR_ObjDistY_13;
  float32 MRR_ObjDistYStd_13;
  float32 MRR_ObjExistProb_13;
  uint8 MRR_ObjFusionCamID_13;
  Rte_DT_SG_MRR_ObjID13_10 MRR_ObjFusionSig_13;
  uint8 MRR_ObjID_13;
  float32 MRR_ObjObstacleProb_13;
  float32 MRR_ObjRAccelXStd_13;
  float32 MRR_ObjRAccelYStd_13;
  float32 MRR_ObjRelVelX_13;
  float32 MRR_ObjRelVelXStd_13;
  float32 MRR_ObjRelVelY_13;
  float32 MRR_ObjRelVelYStd_13;
  Rte_DT_SG_MRR_ObjID13_19 MRR_ObjType_13;
} SG_MRR_ObjID13;

#  define Rte_TypeDef_SG_MRR_ObjID14
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_14;
  uint8 MRR_Obj_MsgCounter_14;
  float32 MRR_ObjAccelX_14;
  float32 MRR_ObjAccelY_14;
  float32 MRR_ObjDistX_14;
  float32 MRR_ObjDistXStd_14;
  float32 MRR_ObjDistY_14;
  float32 MRR_ObjDistYStd_14;
  float32 MRR_ObjExistProb_14;
  uint8 MRR_ObjFusionCamID_14;
  Rte_DT_SG_MRR_ObjID14_10 MRR_ObjFusionSig_14;
  uint8 MRR_ObjID_14;
  float32 MRR_ObjObstacleProb_14;
  float32 MRR_ObjRAccelXStd_14;
  float32 MRR_ObjRAccelYStd_14;
  float32 MRR_ObjRelVelX_14;
  float32 MRR_ObjRelVelXStd_14;
  float32 MRR_ObjRelVelY_14;
  float32 MRR_ObjRelVelYStd_14;
  Rte_DT_SG_MRR_ObjID14_19 MRR_ObjType_14;
} SG_MRR_ObjID14;

#  define Rte_TypeDef_SG_MRR_ObjID15
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_15;
  uint8 MRR_Obj_MsgCounter_15;
  float32 MRR_ObjAccelX_15;
  float32 MRR_ObjAccelY_15;
  float32 MRR_ObjDistX_15;
  float32 MRR_ObjDistXStd_15;
  float32 MRR_ObjDistY_15;
  float32 MRR_ObjDistYStd_15;
  float32 MRR_ObjExistProb_15;
  uint8 MRR_ObjFusionCamID_15;
  Rte_DT_SG_MRR_ObjID15_10 MRR_ObjFusionSig_15;
  uint8 MRR_ObjID_15;
  float32 MRR_ObjObstacleProb_15;
  float32 MRR_ObjRAccelXStd_15;
  float32 MRR_ObjRAccelYStd_15;
  float32 MRR_ObjRelVelX_15;
  float32 MRR_ObjRelVelXStd_15;
  float32 MRR_ObjRelVelY_15;
  float32 MRR_ObjRelVelYStd_15;
  Rte_DT_SG_MRR_ObjID15_19 MRR_ObjType_15;
} SG_MRR_ObjID15;

#  define Rte_TypeDef_SG_MRR_ObjID16
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_16;
  uint8 MRR_Obj_MsgCounter_16;
  float32 MRR_ObjAccelX_16;
  float32 MRR_ObjAccelY_16;
  float32 MRR_ObjDistX_16;
  float32 MRR_ObjDistXStd_16;
  float32 MRR_ObjDistY_16;
  float32 MRR_ObjDistYStd_16;
  float32 MRR_ObjExistProb_16;
  uint8 MRR_ObjFusionCamID_16;
  Rte_DT_SG_MRR_ObjID16_10 MRR_ObjFusionSig_16;
  uint8 MRR_ObjID_16;
  float32 MRR_ObjObstacleProb_16;
  float32 MRR_ObjRAccelXStd_16;
  float32 MRR_ObjRAccelYStd_16;
  float32 MRR_ObjRelVelX_16;
  float32 MRR_ObjRelVelXStd_16;
  float32 MRR_ObjRelVelY_16;
  float32 MRR_ObjRelVelYStd_16;
  Rte_DT_SG_MRR_ObjID16_19 MRR_ObjType_16;
} SG_MRR_ObjID16;

#  define Rte_TypeDef_SG_MRR_ObjID17
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_17;
  uint8 MRR_Obj_MsgCounter_17;
  float32 MRR_ObjAccelX_17;
  float32 MRR_ObjAccelY_17;
  float32 MRR_ObjDistX_17;
  float32 MRR_ObjDistXStd_17;
  float32 MRR_ObjDistY_17;
  float32 MRR_ObjDistYStd_17;
  float32 MRR_ObjExistProb_17;
  uint8 MRR_ObjFusionCamID_17;
  Rte_DT_SG_MRR_ObjID17_10 MRR_ObjFusionSig_17;
  uint8 MRR_ObjID_17;
  float32 MRR_ObjObstacleProb_17;
  float32 MRR_ObjRAccelXStd_17;
  float32 MRR_ObjRAccelYStd_17;
  float32 MRR_ObjRelVelX_17;
  float32 MRR_ObjRelVelXStd_17;
  float32 MRR_ObjRelVelY_17;
  float32 MRR_ObjRelVelYStd_17;
  Rte_DT_SG_MRR_ObjID17_19 MRR_ObjType_17;
} SG_MRR_ObjID17;

#  define Rte_TypeDef_SG_MRR_ObjID18
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_18;
  uint8 MRR_Obj_MsgCounter_18;
  float32 MRR_ObjAccelX_18;
  float32 MRR_ObjAccelY_18;
  float32 MRR_ObjDistX_18;
  float32 MRR_ObjDistXStd_18;
  float32 MRR_ObjDistY_18;
  float32 MRR_ObjDistYStd_18;
  float32 MRR_ObjExistProb_18;
  uint8 MRR_ObjFusionCamID_18;
  Rte_DT_SG_MRR_ObjID18_10 MRR_ObjFusionSig_18;
  uint8 MRR_ObjID_18;
  float32 MRR_ObjObstacleProb_18;
  float32 MRR_ObjRAccelXStd_18;
  float32 MRR_ObjRAccelYStd_18;
  float32 MRR_ObjRelVelX_18;
  float32 MRR_ObjRelVelXStd_18;
  float32 MRR_ObjRelVelY_18;
  float32 MRR_ObjRelVelYStd_18;
  Rte_DT_SG_MRR_ObjID18_19 MRR_ObjType_18;
} SG_MRR_ObjID18;

#  define Rte_TypeDef_SG_MRR_ObjID19
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_19;
  uint8 MRR_Obj_MsgCounter_19;
  float32 MRR_ObjAccelX_19;
  float32 MRR_ObjAccelY_19;
  float32 MRR_ObjDistX_19;
  float32 MRR_ObjDistXStd_19;
  float32 MRR_ObjDistY_19;
  float32 MRR_ObjDistYStd_19;
  float32 MRR_ObjExistProb_19;
  uint8 MRR_ObjFusionCamID_19;
  Rte_DT_SG_MRR_ObjID19_10 MRR_ObjFusionSig_19;
  uint8 MRR_ObjID_19;
  float32 MRR_ObjObstacleProb_19;
  float32 MRR_ObjRAccelXStd_19;
  float32 MRR_ObjRAccelYStd_19;
  float32 MRR_ObjRelVelX_19;
  float32 MRR_ObjRelVelXStd_19;
  float32 MRR_ObjRelVelY_19;
  float32 MRR_ObjRelVelYStd_19;
  Rte_DT_SG_MRR_ObjID19_19 MRR_ObjType_19;
} SG_MRR_ObjID19;

#  define Rte_TypeDef_SG_MRR_ObjID2
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_2;
  uint8 MRR_Obj_MsgCounter_2;
  float32 MRR_ObjAccelX_2;
  float32 MRR_ObjAccelY_2;
  float32 MRR_ObjDistX_2;
  float32 MRR_ObjDistXStd_2;
  float32 MRR_ObjDistY_2;
  float32 MRR_ObjDistYStd_2;
  float32 MRR_ObjExistProb_2;
  uint8 MRR_ObjFusionCamID_2;
  Rte_DT_SG_MRR_ObjID2_10 MRR_ObjFusionSig_2;
  uint8 MRR_ObjID_2;
  float32 MRR_ObjObstacleProb_2;
  float32 MRR_ObjRAccelXStd_2;
  float32 MRR_ObjRAccelYStd_2;
  float32 MRR_ObjRelVelX_2;
  float32 MRR_ObjRelVelXStd_2;
  float32 MRR_ObjRelVelY_2;
  float32 MRR_ObjRelVelYStd_2;
  Rte_DT_SG_MRR_ObjID2_19 MRR_ObjType_2;
} SG_MRR_ObjID2;

#  define Rte_TypeDef_SG_MRR_ObjID20
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_20;
  uint8 MRR_Obj_MsgCounter_20;
  float32 MRR_ObjAccelX_20;
  float32 MRR_ObjAccelY_20;
  float32 MRR_ObjDistX_20;
  float32 MRR_ObjDistXStd_20;
  float32 MRR_ObjDistY_20;
  float32 MRR_ObjDistYStd_20;
  float32 MRR_ObjExistProb_20;
  uint8 MRR_ObjFusionCamID_20;
  Rte_DT_SG_MRR_ObjID20_10 MRR_ObjFusionSig_20;
  uint8 MRR_ObjID_20;
  float32 MRR_ObjObstacleProb_20;
  float32 MRR_ObjRAccelXStd_20;
  float32 MRR_ObjRAccelYStd_20;
  float32 MRR_ObjRelVelX_20;
  float32 MRR_ObjRelVelXStd_20;
  float32 MRR_ObjRelVelY_20;
  float32 MRR_ObjRelVelYStd_20;
  Rte_DT_SG_MRR_ObjID20_19 MRR_ObjType_20;
} SG_MRR_ObjID20;

#  define Rte_TypeDef_SG_MRR_ObjID21
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_21;
  uint8 MRR_Obj_MsgCounter_21;
  float32 MRR_ObjAccelX_21;
  float32 MRR_ObjAccelY_21;
  float32 MRR_ObjDistX_21;
  float32 MRR_ObjDistXStd_21;
  float32 MRR_ObjDistY_21;
  float32 MRR_ObjDistYStd_21;
  float32 MRR_ObjExistProb_21;
  uint8 MRR_ObjFusionCamID_21;
  Rte_DT_SG_MRR_ObjID21_10 MRR_ObjFusionSig_21;
  uint8 MRR_ObjID_21;
  float32 MRR_ObjObstacleProb_21;
  float32 MRR_ObjRAccelXStd_21;
  float32 MRR_ObjRAccelYStd_21;
  float32 MRR_ObjRelVelX_21;
  float32 MRR_ObjRelVelXStd_21;
  float32 MRR_ObjRelVelY_21;
  float32 MRR_ObjRelVelYStd_21;
  Rte_DT_SG_MRR_ObjID21_19 MRR_ObjType_21;
} SG_MRR_ObjID21;

#  define Rte_TypeDef_SG_MRR_ObjID22
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_22;
  uint8 MRR_Obj_MsgCounter_22;
  float32 MRR_ObjAccelX_22;
  float32 MRR_ObjAccelY_22;
  float32 MRR_ObjDistX_22;
  float32 MRR_ObjDistXStd_22;
  float32 MRR_ObjDistY_22;
  float32 MRR_ObjDistYStd_22;
  float32 MRR_ObjExistProb_22;
  uint8 MRR_ObjFusionCamID_22;
  Rte_DT_SG_MRR_ObjID22_10 MRR_ObjFusionSig_22;
  uint8 MRR_ObjID_22;
  float32 MRR_ObjObstacleProb_22;
  float32 MRR_ObjRAccelXStd_22;
  float32 MRR_ObjRAccelYStd_22;
  float32 MRR_ObjRelVelX_22;
  float32 MRR_ObjRelVelXStd_22;
  float32 MRR_ObjRelVelY_22;
  float32 MRR_ObjRelVelYStd_22;
  Rte_DT_SG_MRR_ObjID22_19 MRR_ObjType_22;
} SG_MRR_ObjID22;

#  define Rte_TypeDef_SG_MRR_ObjID23
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_23;
  uint8 MRR_Obj_MsgCounter_23;
  float32 MRR_ObjAccelX_23;
  float32 MRR_ObjAccelY_23;
  float32 MRR_ObjDistX_23;
  float32 MRR_ObjDistXStd_23;
  float32 MRR_ObjDistY_23;
  float32 MRR_ObjDistYStd_23;
  float32 MRR_ObjExistProb_23;
  uint8 MRR_ObjFusionCamID_23;
  Rte_DT_SG_MRR_ObjID23_10 MRR_ObjFusionSig_23;
  uint8 MRR_ObjID_23;
  float32 MRR_ObjObstacleProb_23;
  float32 MRR_ObjRAccelXStd_23;
  float32 MRR_ObjRAccelYStd_23;
  float32 MRR_ObjRelVelX_23;
  float32 MRR_ObjRelVelXStd_23;
  float32 MRR_ObjRelVelY_23;
  float32 MRR_ObjRelVelYStd_23;
  Rte_DT_SG_MRR_ObjID23_19 MRR_ObjType_23;
} SG_MRR_ObjID23;

#  define Rte_TypeDef_SG_MRR_ObjID24
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_24;
  uint8 MRR_Obj_MsgCounter_24;
  float32 MRR_ObjAccelX_24;
  float32 MRR_ObjAccelY_24;
  float32 MRR_ObjDistX_24;
  float32 MRR_ObjDistXStd_24;
  float32 MRR_ObjDistY_24;
  float32 MRR_ObjDistYStd_24;
  float32 MRR_ObjExistProb_24;
  uint8 MRR_ObjFusionCamID_24;
  Rte_DT_SG_MRR_ObjID24_10 MRR_ObjFusionSig_24;
  uint8 MRR_ObjID_24;
  float32 MRR_ObjObstacleProb_24;
  float32 MRR_ObjRAccelXStd_24;
  float32 MRR_ObjRAccelYStd_24;
  float32 MRR_ObjRelVelX_24;
  float32 MRR_ObjRelVelXStd_24;
  float32 MRR_ObjRelVelY_24;
  float32 MRR_ObjRelVelYStd_24;
  Rte_DT_SG_MRR_ObjID24_19 MRR_ObjType_24;
} SG_MRR_ObjID24;

#  define Rte_TypeDef_SG_MRR_ObjID25
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_25;
  uint8 MRR_Obj_MsgCounter_25;
  float32 MRR_ObjAccelX_25;
  float32 MRR_ObjAccelY_25;
  float32 MRR_ObjDistX_25;
  float32 MRR_ObjDistXStd_25;
  float32 MRR_ObjDistY_25;
  float32 MRR_ObjDistYStd_25;
  float32 MRR_ObjExistProb_25;
  uint8 MRR_ObjFusionCamID_25;
  Rte_DT_SG_MRR_ObjID25_10 MRR_ObjFusionSig_25;
  uint8 MRR_ObjID_25;
  float32 MRR_ObjObstacleProb_25;
  float32 MRR_ObjRAccelXStd_25;
  float32 MRR_ObjRAccelYStd_25;
  float32 MRR_ObjRelVelX_25;
  float32 MRR_ObjRelVelXStd_25;
  float32 MRR_ObjRelVelY_25;
  float32 MRR_ObjRelVelYStd_25;
  Rte_DT_SG_MRR_ObjID25_19 MRR_ObjType_25;
} SG_MRR_ObjID25;

#  define Rte_TypeDef_SG_MRR_ObjID26
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_26;
  uint8 MRR_Obj_MsgCounter_26;
  float32 MRR_ObjAccelX_26;
  float32 MRR_ObjAccelY_26;
  float32 MRR_ObjDistX_26;
  float32 MRR_ObjDistXStd_26;
  float32 MRR_ObjDistY_26;
  float32 MRR_ObjDistYStd_26;
  float32 MRR_ObjExistProb_26;
  uint8 MRR_ObjFusionCamID_26;
  Rte_DT_SG_MRR_ObjID26_10 MRR_ObjFusionSig_26;
  uint8 MRR_ObjID_26;
  float32 MRR_ObjObstacleProb_26;
  float32 MRR_ObjRAccelXStd_26;
  float32 MRR_ObjRAccelYStd_26;
  float32 MRR_ObjRelVelX_26;
  float32 MRR_ObjRelVelXStd_26;
  float32 MRR_ObjRelVelY_26;
  float32 MRR_ObjRelVelYStd_26;
  Rte_DT_SG_MRR_ObjID26_19 MRR_ObjType_26;
} SG_MRR_ObjID26;

#  define Rte_TypeDef_SG_MRR_ObjID27
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_27;
  uint8 MRR_Obj_MsgCounter_27;
  float32 MRR_ObjAccelX_27;
  float32 MRR_ObjAccelY_27;
  float32 MRR_ObjDistX_27;
  float32 MRR_ObjDistXStd_27;
  float32 MRR_ObjDistY_27;
  float32 MRR_ObjDistYStd_27;
  float32 MRR_ObjExistProb_27;
  uint8 MRR_ObjFusionCamID_27;
  Rte_DT_SG_MRR_ObjID27_10 MRR_ObjFusionSig_27;
  uint8 MRR_ObjID_27;
  float32 MRR_ObjObstacleProb_27;
  float32 MRR_ObjRAccelXStd_27;
  float32 MRR_ObjRAccelYStd_27;
  float32 MRR_ObjRelVelX_27;
  float32 MRR_ObjRelVelXStd_27;
  float32 MRR_ObjRelVelY_27;
  float32 MRR_ObjRelVelYStd_27;
  Rte_DT_SG_MRR_ObjID27_19 MRR_ObjType_27;
} SG_MRR_ObjID27;

#  define Rte_TypeDef_SG_MRR_ObjID28
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_28;
  uint8 MRR_Obj_MsgCounter_28;
  float32 MRR_ObjAccelX_28;
  float32 MRR_ObjAccelY_28;
  float32 MRR_ObjDistX_28;
  float32 MRR_ObjDistXStd_28;
  float32 MRR_ObjDistY_28;
  float32 MRR_ObjDistYStd_28;
  float32 MRR_ObjExistProb_28;
  uint8 MRR_ObjFusionCamID_28;
  Rte_DT_SG_MRR_ObjID28_10 MRR_ObjFusionSig_28;
  uint8 MRR_ObjID_28;
  float32 MRR_ObjObstacleProb_28;
  float32 MRR_ObjRAccelXStd_28;
  float32 MRR_ObjRAccelYStd_28;
  float32 MRR_ObjRelVelX_28;
  float32 MRR_ObjRelVelXStd_28;
  float32 MRR_ObjRelVelY_28;
  float32 MRR_ObjRelVelYStd_28;
  Rte_DT_SG_MRR_ObjID28_19 MRR_ObjType_28;
} SG_MRR_ObjID28;

#  define Rte_TypeDef_SG_MRR_ObjID29
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_29;
  uint8 MRR_Obj_MsgCounter_29;
  float32 MRR_ObjAccelX_29;
  float32 MRR_ObjAccelY_29;
  float32 MRR_ObjDistX_29;
  float32 MRR_ObjDistXStd_29;
  float32 MRR_ObjDistY_29;
  float32 MRR_ObjDistYStd_29;
  float32 MRR_ObjExistProb_29;
  uint8 MRR_ObjFusionCamID_29;
  Rte_DT_SG_MRR_ObjID29_10 MRR_ObjFusionSig_29;
  uint8 MRR_ObjID_29;
  float32 MRR_ObjObstacleProb_29;
  float32 MRR_ObjRAccelXStd_29;
  float32 MRR_ObjRAccelYStd_29;
  float32 MRR_ObjRelVelX_29;
  float32 MRR_ObjRelVelXStd_29;
  float32 MRR_ObjRelVelY_29;
  float32 MRR_ObjRelVelYStd_29;
  Rte_DT_SG_MRR_ObjID29_19 MRR_ObjType_29;
} SG_MRR_ObjID29;

#  define Rte_TypeDef_SG_MRR_ObjID3
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_3;
  uint8 MRR_Obj_MsgCounter_3;
  float32 MRR_ObjAccelX_3;
  float32 MRR_ObjAccelY_3;
  float32 MRR_ObjDistX_3;
  float32 MRR_ObjDistXStd_3;
  float32 MRR_ObjDistY_3;
  float32 MRR_ObjDistYStd_3;
  float32 MRR_ObjExistProb_3;
  uint8 MRR_ObjFusionCamID_3;
  Rte_DT_SG_MRR_ObjID3_10 MRR_ObjFusionSig_3;
  uint8 MRR_ObjID_3;
  float32 MRR_ObjObstacleProb_3;
  float32 MRR_ObjRAccelXStd_3;
  float32 MRR_ObjRAccelYStd_3;
  float32 MRR_ObjRelVelX_3;
  float32 MRR_ObjRelVelXStd_3;
  float32 MRR_ObjRelVelY_3;
  float32 MRR_ObjRelVelYStd_3;
  Rte_DT_SG_MRR_ObjID3_19 MRR_ObjType_3;
} SG_MRR_ObjID3;

#  define Rte_TypeDef_SG_MRR_ObjID30
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_30;
  uint8 MRR_Obj_MsgCounter_30;
  float32 MRR_ObjAccelX_30;
  float32 MRR_ObjAccelY_30;
  float32 MRR_ObjDistX_30;
  float32 MRR_ObjDistXStd_30;
  float32 MRR_ObjDistY_30;
  float32 MRR_ObjDistYStd_30;
  float32 MRR_ObjExistProb_30;
  uint8 MRR_ObjFusionCamID_30;
  Rte_DT_SG_MRR_ObjID30_10 MRR_ObjFusionSig_30;
  uint8 MRR_ObjID_30;
  float32 MRR_ObjObstacleProb_30;
  float32 MRR_ObjRAccelXStd_30;
  float32 MRR_ObjRAccelYStd_30;
  float32 MRR_ObjRelVelX_30;
  float32 MRR_ObjRelVelXStd_30;
  float32 MRR_ObjRelVelY_30;
  float32 MRR_ObjRelVelYStd_30;
  Rte_DT_SG_MRR_ObjID30_19 MRR_ObjType_30;
} SG_MRR_ObjID30;

#  define Rte_TypeDef_SG_MRR_ObjID31
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_31;
  uint8 MRR_Obj_MsgCounter_31;
  float32 MRR_ObjAccelX_31;
  float32 MRR_ObjAccelY_31;
  float32 MRR_ObjDistX_31;
  float32 MRR_ObjDistXStd_31;
  float32 MRR_ObjDistY_31;
  float32 MRR_ObjDistYStd_31;
  float32 MRR_ObjExistProb_31;
  uint8 MRR_ObjFusionCamID_31;
  Rte_DT_SG_MRR_ObjID31_10 MRR_ObjFusionSig_31;
  uint8 MRR_ObjID_31;
  float32 MRR_ObjObstacleProb_31;
  float32 MRR_ObjRAccelXStd_31;
  float32 MRR_ObjRAccelYStd_31;
  float32 MRR_ObjRelVelX_31;
  float32 MRR_ObjRelVelXStd_31;
  float32 MRR_ObjRelVelY_31;
  float32 MRR_ObjRelVelYStd_31;
  Rte_DT_SG_MRR_ObjID31_19 MRR_ObjType_31;
} SG_MRR_ObjID31;

#  define Rte_TypeDef_SG_MRR_ObjID32
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_32;
  uint8 MRR_Obj_MsgCounter_32;
  float32 MRR_ObjAccelX_32;
  float32 MRR_ObjAccelY_32;
  float32 MRR_ObjDistX_32;
  float32 MRR_ObjDistXStd_32;
  float32 MRR_ObjDistY_32;
  float32 MRR_ObjDistYStd_32;
  float32 MRR_ObjExistProb_32;
  uint8 MRR_ObjFusionCamID_32;
  Rte_DT_SG_MRR_ObjID32_10 MRR_ObjFusionSig_32;
  uint8 MRR_ObjID_32;
  float32 MRR_ObjObstacleProb_32;
  float32 MRR_ObjRAccelXStd_32;
  float32 MRR_ObjRAccelYStd_32;
  float32 MRR_ObjRelVelX_32;
  float32 MRR_ObjRelVelXStd_32;
  float32 MRR_ObjRelVelY_32;
  float32 MRR_ObjRelVelYStd_32;
  Rte_DT_SG_MRR_ObjID32_19 MRR_ObjType_32;
} SG_MRR_ObjID32;

#  define Rte_TypeDef_SG_MRR_ObjID4
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_4;
  uint8 MRR_Obj_MsgCounter_4;
  float32 MRR_ObjAccelX_4;
  float32 MRR_ObjAccelY_4;
  float32 MRR_ObjDistX_4;
  float32 MRR_ObjDistXStd_4;
  float32 MRR_ObjDistY_4;
  float32 MRR_ObjDistYStd_4;
  float32 MRR_ObjExistProb_4;
  uint8 MRR_ObjFusionCamID_4;
  Rte_DT_SG_MRR_ObjID4_10 MRR_ObjFusionSig_4;
  uint8 MRR_ObjID_4;
  float32 MRR_ObjObstacleProb_4;
  float32 MRR_ObjRAccelXStd_4;
  float32 MRR_ObjRAccelYStd_4;
  float32 MRR_ObjRelVelX_4;
  float32 MRR_ObjRelVelXStd_4;
  float32 MRR_ObjRelVelY_4;
  float32 MRR_ObjRelVelYStd_4;
  Rte_DT_SG_MRR_ObjID4_19 MRR_ObjType_4;
} SG_MRR_ObjID4;

#  define Rte_TypeDef_SG_MRR_ObjID5
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_5;
  uint8 MRR_Obj_MsgCounter_5;
  float32 MRR_ObjAccelX_5;
  float32 MRR_ObjAccelY_5;
  float32 MRR_ObjDistX_5;
  float32 MRR_ObjDistXStd_5;
  float32 MRR_ObjDistY_5;
  float32 MRR_ObjDistYStd_5;
  float32 MRR_ObjExistProb_5;
  uint8 MRR_ObjFusionCamID_5;
  Rte_DT_SG_MRR_ObjID5_10 MRR_ObjFusionSig_5;
  uint8 MRR_ObjID_5;
  float32 MRR_ObjObstacleProb_5;
  float32 MRR_ObjRAccelXStd_5;
  float32 MRR_ObjRAccelYStd_5;
  float32 MRR_ObjRelVelX_5;
  float32 MRR_ObjRelVelXStd_5;
  float32 MRR_ObjRelVelY_5;
  float32 MRR_ObjRelVelYStd_5;
  Rte_DT_SG_MRR_ObjID5_19 MRR_ObjType_5;
} SG_MRR_ObjID5;

#  define Rte_TypeDef_SG_MRR_ObjID6
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_6;
  uint8 MRR_Obj_MsgCounter_6;
  float32 MRR_ObjAccelX_6;
  float32 MRR_ObjAccelY_6;
  float32 MRR_ObjDistX_6;
  float32 MRR_ObjDistXStd_6;
  float32 MRR_ObjDistY_6;
  float32 MRR_ObjDistYStd_6;
  float32 MRR_ObjExistProb_6;
  uint8 MRR_ObjFusionCamID_6;
  Rte_DT_SG_MRR_ObjID6_10 MRR_ObjFusionSig_6;
  uint8 MRR_ObjID_6;
  float32 MRR_ObjObstacleProb_6;
  float32 MRR_ObjRAccelXStd_6;
  float32 MRR_ObjRAccelYStd_6;
  float32 MRR_ObjRelVelX_6;
  float32 MRR_ObjRelVelXStd_6;
  float32 MRR_ObjRelVelY_6;
  float32 MRR_ObjRelVelYStd_6;
  Rte_DT_SG_MRR_ObjID6_19 MRR_ObjType_6;
} SG_MRR_ObjID6;

#  define Rte_TypeDef_SG_MRR_ObjID7
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_7;
  uint8 MRR_Obj_MsgCounter_7;
  float32 MRR_ObjAccelX_7;
  float32 MRR_ObjAccelY_7;
  float32 MRR_ObjDistX_7;
  float32 MRR_ObjDistXStd_7;
  float32 MRR_ObjDistY_7;
  float32 MRR_ObjDistYStd_7;
  float32 MRR_ObjExistProb_7;
  uint8 MRR_ObjFusionCamID_7;
  Rte_DT_SG_MRR_ObjID7_10 MRR_ObjFusionSig_7;
  uint8 MRR_ObjID_7;
  float32 MRR_ObjObstacleProb_7;
  float32 MRR_ObjRAccelXStd_7;
  float32 MRR_ObjRAccelYStd_7;
  float32 MRR_ObjRelVelX_7;
  float32 MRR_ObjRelVelXStd_7;
  float32 MRR_ObjRelVelY_7;
  float32 MRR_ObjRelVelYStd_7;
  Rte_DT_SG_MRR_ObjID7_19 MRR_ObjType_7;
} SG_MRR_ObjID7;

#  define Rte_TypeDef_SG_MRR_ObjID8
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_8;
  uint8 MRR_Obj_MsgCounter_8;
  float32 MRR_ObjAccelX_8;
  float32 MRR_ObjAccelY_8;
  float32 MRR_ObjDistX_8;
  float32 MRR_ObjDistXStd_8;
  float32 MRR_ObjDistY_8;
  float32 MRR_ObjDistYStd_8;
  float32 MRR_ObjExistProb_8;
  uint8 MRR_ObjFusionCamID_8;
  Rte_DT_SG_MRR_ObjID8_10 MRR_ObjFusionSig_8;
  uint8 MRR_ObjID_8;
  float32 MRR_ObjObstacleProb_8;
  float32 MRR_ObjRAccelXStd_8;
  float32 MRR_ObjRAccelYStd_8;
  float32 MRR_ObjRelVelX_8;
  float32 MRR_ObjRelVelXStd_8;
  float32 MRR_ObjRelVelY_8;
  float32 MRR_ObjRelVelYStd_8;
  Rte_DT_SG_MRR_ObjID8_19 MRR_ObjType_8;
} SG_MRR_ObjID8;

#  define Rte_TypeDef_SG_MRR_ObjID9
typedef struct
{
  uint8 MRR_Obj_CRC8Checksum_9;
  uint8 MRR_Obj_MsgCounter_9;
  float32 MRR_ObjAccelX_9;
  float32 MRR_ObjAccelY_9;
  float32 MRR_ObjDistX_9;
  float32 MRR_ObjDistXStd_9;
  float32 MRR_ObjDistY_9;
  float32 MRR_ObjDistYStd_9;
  float32 MRR_ObjExistProb_9;
  uint8 MRR_ObjFusionCamID_9;
  Rte_DT_SG_MRR_ObjID9_10 MRR_ObjFusionSig_9;
  uint8 MRR_ObjID_9;
  float32 MRR_ObjObstacleProb_9;
  float32 MRR_ObjRAccelXStd_9;
  float32 MRR_ObjRAccelYStd_9;
  float32 MRR_ObjRelVelX_9;
  float32 MRR_ObjRelVelXStd_9;
  float32 MRR_ObjRelVelY_9;
  float32 MRR_ObjRelVelYStd_9;
  Rte_DT_SG_MRR_ObjID9_19 MRR_ObjType_9;
} SG_MRR_ObjID9;

#  define Rte_TypeDef_SWS_050ms_PDU00
typedef struct
{
  Rte_DT_SWS_050ms_PDU00_0 CCSwStsCanclSwA_l;
  Rte_DT_SWS_050ms_PDU00_1 CCSwStsCCASwA_l;
  Rte_DT_SWS_050ms_PDU00_2 CCSwStsDistDecSwA_l;
  Rte_DT_SWS_050ms_PDU00_3 CCSwStsDistIncSwA_l;
  Rte_DT_SWS_050ms_PDU00_4 CCSwStsOnSwA_l;
  uint8 CCSwStsPV;
  Rte_DT_SWS_050ms_PDU00_6 CCSwStsRsmSwA_l;
  Rte_DT_SWS_050ms_PDU00_7 CCSwStsSetSwA_l;
  Rte_DT_SWS_050ms_PDU00_8 CCSwStsSpdDecSwA_l;
  Rte_DT_SWS_050ms_PDU00_9 CCSwStsSpdIncSwA_l;
  Rte_DT_SWS_050ms_PDU00_10 CCSwStsSwDataIntgty_l;
  Rte_DT_SWS_050ms_PDU00_11 PfTrTapUpDwnSecySwSta_l;
  Rte_DT_SWS_050ms_PDU00_12 PfTrTapUpDwnSwSta_l;
  Rte_DT_SWS_050ms_PDU00_13 StrgWhlDrvngMdSwA_l;
  Rte_DT_SWS_050ms_PDU00_14 StrgWhlDrvngMdSwDataIntgty_l;
  Rte_DT_SWS_050ms_PDU00_15 StrgWhlEntrtnSwDataIntgty_l;
  Rte_DT_SWS_050ms_PDU00_16 StrgWhlTipcSwDataIntgty_l;
  uint8 SWS_050ms_PDU000_CRC;
  uint8 SWS_050ms_PDU00_RC;
  uint8 SWS_050ms_PDU00_Reserve01;
  uint8 SWS_050ms_PDU00_Reserve02;
  uint8 SWS_050ms_PDU00_Reserve03;
  Rte_DT_SWS_050ms_PDU00_22 SWSCnfmSwA_l;
  Rte_DT_SWS_050ms_PDU00_23 SWSEntrtnUserSwA_l;
  Rte_DT_SWS_050ms_PDU00_24 SWSFastrUserSwA_l;
  Rte_DT_SWS_050ms_PDU00_25 SWSFnChngSwA_l;
  Rte_DT_SWS_050ms_PDU00_26 SWSSelDwnSwA_l;
  Rte_DT_SWS_050ms_PDU00_27 SWSSelLSwA_l;
  Rte_DT_SWS_050ms_PDU00_28 SWSSelRSwA_l;
  Rte_DT_SWS_050ms_PDU00_29 SWSSelUpSwA_l;
  Rte_DT_SWS_050ms_PDU00_30 SWSSocContSwA_l;
  Rte_DT_SWS_050ms_PDU00_31 SWSVcSwA_l;
} SWS_050ms_PDU00;

#  define Rte_TypeDef_SeldTarsTyp
typedef struct
{
  TsObjTyp CipObj;
  TsObjTyp SipObj;
  TsObjTyp FrntLeObj;
  TsObjTyp FrntRiObj;
  TsObjTyp SecFrntLeObj;
  TsObjTyp SecFrntRiObj;
} SeldTarsTyp;

#  define Rte_TypeDef_SsmLaneType
typedef struct
{
  SInt8 index;
  rt_Array_SsmSpeedRangeType_2 speed_ranges_st;
  rt_Array_SsmCurvatureType_10 curvature_st;
  rt_Array_SsmRefPtType_20 ref_line_pts;
  UInt8 lane_type;
} SsmLaneType;

#  define Rte_TypeDef_SsmMapSignType
typedef struct
{
  UInt8 is_map_valid;
  UInt8 lane_type;
  DiversionInfo line_diversion;
  LaneSpdLmtValType speed_ranges_st;
} SsmMapSignType;

#  define Rte_TypeDef_SsmObjType
typedef struct
{
  UInt8 obj_num;
  UInt32 ts_high;
  UInt32 ts_low;
  UInt8 source;
  rt_Array_SsmObsType_32 obj_lists;
} SsmObjType;

#  define Rte_TypeDef_SsmParamType
typedef struct
{
  UInt8 type;
  Float start_x;
  Float end_x;
  rt_Array_Float_4 coeffs;
} SsmParamType;

#  define Rte_TypeDef_StateManagerOutMsgType
typedef struct
{
  HeaderType Header;
  DriverSettingType DriverSetting;
  UInt8 isUserALCConfirm;
  UInt8 ICAMode;
  UInt8 NOAMode;
  UInt8 AEBMode;
  UInt8 inhibitReason;
} StateManagerOutMsgType;

#  define Rte_TypeDef_StbM_EthTimeMasterMeasurementType
typedef struct
{
  uint16 sequenceId;
  StbM_PortIdType sourcePortId;
  StbM_VirtualLocalTimeType syncEgressTimestamp;
  StbM_TimeStampShortType preciseOriginTimestamp;
  sint64 correctionField;
} StbM_EthTimeMasterMeasurementType;

#  define Rte_TypeDef_StbM_EthTimeSlaveMeasurementType
typedef struct
{
  uint16 sequenceId;
  StbM_PortIdType sourcePortId;
  StbM_VirtualLocalTimeType syncIngressTimestamp;
  StbM_TimeStampShortType preciseOriginTimestamp;
  sint64 correctionField;
  uint32 pDelay;
  StbM_VirtualLocalTimeType referenceLocalTimestamp;
  StbM_TimeStampShortType referenceGlobalTimestamp;
} StbM_EthTimeSlaveMeasurementType;

#  define Rte_TypeDef_StbM_OffsetRecordTableBlockType
typedef struct
{
  uint32 GlbSeconds;
  uint32 GlbNanoSeconds;
  StbM_TimeBaseStatusType TimeBaseStatus;
} StbM_OffsetRecordTableBlockType;

#  define Rte_TypeDef_StbM_PdelayInitiatorMeasurementType
typedef struct
{
  uint16 sequenceId;
  StbM_PortIdType requestPortId;
  StbM_PortIdType responsePortId;
  StbM_VirtualLocalTimeType requestOriginTimestamp;
  StbM_VirtualLocalTimeType responseReceiptTimestamp;
  StbM_TimeStampShortType requestReceiptTimestamp;
  StbM_TimeStampShortType responseOriginTimestamp;
  StbM_VirtualLocalTimeType referenceLocalTimestamp;
  StbM_TimeStampShortType referenceGlobalTimestamp;
  uint32 pdelay;
} StbM_PdelayInitiatorMeasurementType;

#  define Rte_TypeDef_StbM_PdelayResponderMeasurementType
typedef struct
{
  uint16 sequenceId;
  StbM_PortIdType requestPortId;
  StbM_PortIdType responsePortId;
  StbM_VirtualLocalTimeType requestReceiptTimestamp;
  StbM_VirtualLocalTimeType responseOriginTimestamp;
  StbM_VirtualLocalTimeType referenceLocalTimestamp;
  StbM_TimeStampShortType referenceGlobalTimestamp;
} StbM_PdelayResponderMeasurementType;

#  define Rte_TypeDef_StbM_SyncRecordTableBlockType
typedef struct
{
  uint32 GlbSeconds;
  uint32 GlbNanoSeconds;
  StbM_TimeBaseStatusType TimeBaseStatus;
  uint32 VirtualLocalTimeLow;
  sint16 RateDeviation;
  uint32 LocSeconds;
  uint32 LocNanoSeconds;
  uint32 PathDelay;
} StbM_SyncRecordTableBlockType;

#  define Rte_TypeDef_StbM_TimeStampType
typedef struct
{
  StbM_TimeBaseStatusType timeBaseStatus;
  uint32 nanoseconds;
  uint32 seconds;
  uint16 secondsHi;
} StbM_TimeStampType;

#  define Rte_TypeDef_StbM_TimeStampType_1
typedef struct
{
  StbM_TimeBaseStatusType_1 timeBaseStatus;
  uint32 nanoseconds;
  uint32 seconds;
  uint16 secondsHi;
} StbM_TimeStampType_1;

#  define Rte_TypeDef_StbM_TimeStampType_2
typedef struct
{
  StbM_TimeBaseStatusType_2 timeBaseStatus;
  uint32 nanoseconds;
  uint32 seconds;
  uint16 secondsHi;
} StbM_TimeStampType_2;

#  define Rte_TypeDef_TCM_025ms_PDU20
typedef struct
{
  uint8 TCM_025ms_PDU20_CRC;
  uint8 TCM_025ms_PDU20_RC;
  uint16 TCM_025ms_PDU20_Reserve01;
  uint16 TCM_025ms_PDU20_Reserve02;
  uint8 TCM_025ms_PDU20_Reserve03;
  uint16 TCM_025ms_PDU20_Reserve04;
  Rte_DT_TCM_025ms_PDU20_6 TrACCToqReqResp;
} TCM_025ms_PDU20;

#  define Rte_TypeDef_TCM_PTCAN_FrP02
typedef struct
{
  Rte_DT_TCM_PTCAN_FrP02_0 AutocTrCmddGear_TCM;
  Rte_DT_TCM_PTCAN_FrP02_1 DrvrShftCtrlReqDndIO;
  Rte_DT_TCM_PTCAN_FrP02_2 DrvrShftCtrlTrgtGear_TCM;
  uint8 TCM_025ms_PDU02_CRC;
  uint8 TCM_025ms_PDU02_RC;
  uint8 TCM_025ms_PDU02_Reserve03;
  uint8 TCM_025ms_PDU02_Reserve04;
  uint8 TCM_025ms_PDU02_Reserve05;
  Rte_DT_TCM_PTCAN_FrP02_8 TrCrpMdA_TCM;
  Rte_DT_TCM_PTCAN_FrP02_9 TrEngdSta_TCM;
  Rte_DT_TCM_PTCAN_FrP02_10 TrEngdStaV_TCM;
  Rte_DT_TCM_PTCAN_FrP02_11 TrEPBAppReq;
  Rte_DT_TCM_PTCAN_FrP02_12 TrEstdGear_TCM;
  Rte_DT_TCM_PTCAN_FrP02_13 TrEstdGearV_TCM;
  Rte_DT_TCM_PTCAN_FrP02_14 TrIntnlSwPos;
  Rte_DT_TCM_PTCAN_FrP02_15 TrIntnlSwPosV;
  Rte_DT_TCM_PTCAN_FrP02_16 TrPrkPos;
  Rte_DT_TCM_PTCAN_FrP02_17 TrPrkSysF;
  Rte_DT_TCM_PTCAN_FrP02_18 TrShftLvrPos_TCM;
  Rte_DT_TCM_PTCAN_FrP02_19 TrShftLvrPosV_TCM;
  Rte_DT_TCM_PTCAN_FrP02_20 TrShftPtrnASts_TCM;
  Rte_DT_TCM_PTCAN_FrP02_21 TrTapUpTapDwnMdSts_TCM;
  Rte_DT_TCM_PTCAN_FrP02_22 TrTrgtShftLvrPos;
} TCM_PTCAN_FrP02;

#  define Rte_TypeDef_TSRInfoTyp
typedef struct
{
  TSRObjTyp TSR0;
  TSRObjTyp TSR1;
  TSRObjTyp TSR2;
  TSRObjTyp TSR3;
  TSRObjTyp TSR4;
  TSRObjTyp TSR5;
  TSRObjTyp TSR6;
  TSRObjTyp TSR7;
} TSRInfoTyp;

#  define Rte_TypeDef_VisObjListType
typedef struct
{
  VisObjStsType SensorStatusFlags_st;
  rt_Array_VisObjType_10 SensorObject_st;
} VisObjListType;

#  define Rte_TypeDef_Rte_DT_FDriveLaneType_1
typedef LineParamsType Rte_DT_FDriveLaneType_1[3];

#  define Rte_TypeDef_rt_Array_SsmLaneType_3
typedef SsmLaneType rt_Array_SsmLaneType_3[3];

#  define Rte_TypeDef_rt_Array_SsmParamType_3
typedef SsmParamType rt_Array_SsmParamType_3[3];

#  define Rte_TypeDef_CamInputType
typedef struct
{
  CmrFsType CmrFreeSpace;
  VisObjListType CamObjListsInput_st;
  CmrLaneTyp CmrLaneRaw;
} CamInputType;

#  define Rte_TypeDef_FDriveLaneType
typedef struct
{
  Boolean valid;
  Rte_DT_FDriveLaneType_1 refline_params;
  Float high_conf_length;
  Float lane_width;
  SsmAlcType road_safety_status;
  UInt8 change_lane_status;
} FDriveLaneType;

#  define Rte_TypeDef_FPerceptionType
typedef struct
{
  HeaderType header;
  FObstaclesType obstacles;
  FDriveLaneType driveline;
} FPerceptionType;

#  define Rte_TypeDef_SsmTrafficLineType
typedef struct
{
  SInt32 index;
  UInt8 color;
  UInt8 line_source;
  UInt32 id;
  UInt8 make_up;
  UInt32 ts_high;
  UInt32 ts_low;
  UInt8 side_type;
  rt_Array_SsmParamType_3 params;
  UInt8 conf;
} SsmTrafficLineType;

#  define Rte_TypeDef_rt_Array_SsmTrafficLineType_6
typedef SsmTrafficLineType rt_Array_SsmTrafficLineType_6[6];

#  define Rte_TypeDef_SsmFrameType
typedef struct
{
  UInt32 seq;
  UInt8 ssm_source;
  SsmHeaderType Ssm_Header_st;
  rt_Array_SsmLaneType_3 Ssm_Lanes_st;
  rt_Array_SsmTrafficLineType_6 Ssm_TrafficLine_st;
  SsmObjType Ssm_Objs_Frame_st;
  SsmMapSignType Ssm_map_sign_st;
  SsmAlcType Ssm_Alc_st;
} SsmFrameType;

# endif

#endif /* RTE_TYPE_H */
