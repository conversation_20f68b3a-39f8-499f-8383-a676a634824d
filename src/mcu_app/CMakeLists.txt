#=============================================================================
#  C O P Y R I G H T
#-----------------------------------------------------------------------------
#  Copyright (c) 2021 by iMotion AI. All rights reserved.
#-----------------------------------------------------------------------------
#  SPDX-License-Identifier: BIOSLv4
#=============================================================================

cmake_minimum_required(VERSION 3.8.2)

# -------------------------------------------------------------------------------
# Setting up cmake scripts
# -------------------------------------------------------------------------------
set(ZX_IDC_PRJ_ROOT "${CMAKE_CURRENT_LIST_DIR}")
set(ZX_PROJECT_CMAKE_DIR "${CMAKE_CURRENT_LIST_DIR}/idc_rte/tools/flight/Modules")
set(ZX_PROJECT_JOBS 8)
# set(CMAKE_BUILD_TYPE Debug)
# set(CMAKE_VERBOSE_MAKEFILE ON)
# Get git-status
include(gitstatus.cmake)

# -------------------------------------------------------------------------------
# Setup module path for further cmake includes
# -------------------------------------------------------------------------------
list(
    APPEND
    CMAKE_MODULE_PATH
    "${CMAKE_CURRENT_LIST_DIR}/idc_rte/tools/flight/Modules"
    "${CMAKE_CURRENT_LIST_DIR}/idc_infrastructure/version/modules"
    "${CMAKE_CURRENT_LIST_DIR}/idc_infrastructure/tial/Modules"
    "${CMAKE_CURRENT_LIST_DIR}/components/cmake_tools"
)

list(APPEND CMAKE_PREFIX_PATH
    ${ZX_SOC_SYSROOT}/usr/lib/cmake
    ${ZX_SOC_SYSROOT}/usr/ext/lib/cmake
)

set(ZX_RTE_ROOT "${CMAKE_CURRENT_LIST_DIR}/idc_rte")

# -------------------------------------------------------------------------------
# Linking path and lenght relevant
# -------------------------------------------------------------------------------
# Increasing the full path length of object files to 300
set(CMAKE_OBJECT_PATH_MAX 300)

# Solves problem for long linking/compiling and archiving commands
SET(CMAKE_CXX_USE_RESPONSE_FILE_FOR_OBJECTS 1)
SET(CMAKE_C_USE_RESPONSE_FILE_FOR_OBJECTS 1)

# -------------------------------------------------------------------------------
# Set CMAKE_INSTALL_PREFIX
# -------------------------------------------------------------------------------
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
    if(NOT CMAKE_TOOLCHAIN_FILE)
        if(WIN32)
            set(CMAKE_INSTALL_PREFIX "${CMAKE_BINARY_DIR}/install" CACHE PATH "Installation Directory")
        else()
            set(CMAKE_INSTALL_PREFIX "/usr/local" CACHE PATH "Installation Directory")
        endif()
    else(NOT CMAKE_TOOLCHAIN_FILE)
        # any crosscompiling
    endif(NOT CMAKE_TOOLCHAIN_FILE)
endif()

# -------------------------------------------------------------------------------
# Root project
# -------------------------------------------------------------------------------
project(J6R52 LANGUAGES C CXX ASM 
    VERSION 1.0.0
    DESCRIPTION "J6R52 MCU Application"
) 

set(ZX_PROJECT_DIR ${CMAKE_CURRENT_SOURCE_DIR}
    CACHE INTERNAL "Root folder of project"
)

set(ZX_PROJECT_VARIATIONPOINT_app_ACTIVATION "mcu" CACHE INTERNAL "project config app activation")

include(zx_variation)

# -------------------------------------------------------------------------------
# Retrieve target specification
# -------------------------------------------------------------------------------
# Make sure the target file is defined
if(ZX_PROJECT_TARGET_CMAKE STREQUAL "")
    message(FATAL_ERROR "ZX_PROJECT_TARGET_CMAKE must be defined, please specify a target cmake file")
endif()

# Make sure the target file is available
if(NOT EXISTS "${ZX_PROJECT_TARGET_CMAKE}")
    set(ZX_PROJECT_TARGET_CMAKE "${CMAKE_HOME_DIRECTORY}/${CPJ_FOLDER_NAME}/cmake_build/${ZX_PROJECT_DEVICE}/${ZX_PROJECT_TARGET}.cmake" CACHE STRING "Path to CMake build target file" FORCE)
    message(STATUS "ZX_PROJECT_TARGET_CMAKE is not defined, using default path: ${ZX_PROJECT_TARGET_CMAKE}")
    if (NOT EXISTS "${ZX_PROJECT_TARGET_CMAKE}")
        set(ZX_PROJECT_TARGET "" CACHE STRING "Path to CMake build target file")
        message(FATAL_ERROR "ZX_PROJECT_TARGET_CMAKE points to the non existent file: ${ZX_PROJECT_TARGET_CMAKE}")
    endif()
endif()

# Get components from the target file
# Info: build directory is different from the directory from where the file is included later on
get_filename_component(
    ZX_PROJECT_TARGET_CMAKE
    "${ZX_PROJECT_TARGET_CMAKE}"
    REALPATH
    BASE_DIR "${CMAKE_BINARY_DIR}"
)

# include the variation_cfg.prebuild.cmake file
string(REGEX REPLACE "\\.cmake$" ".prebuild.cmake" ZX_PROJECT_TARGET_PREBUILD "${ZX_PROJECT_TARGET_CMAKE}")
    message(STATUS "Including ${ZX_PROJECT_TARGET_PREBUILD}")
include("${ZX_PROJECT_TARGET_PREBUILD}" OPTIONAL)

# include the variation_cfg file  
#include(${CPJ_FOLDER_NAME}/cmake_build/${ZX_PROJECT_DEVICE}/rpu.cmake)
include(${ZX_PROJECT_TARGET_CMAKE} RESULT_VARIABLE l_result)
if(l_result STREQUAL NOTFOUND)
    message(FATAL_ERROR "Cannot find cmake build target file: ${ZX_PROJECT_TARGET_CMAKE}")
endif()

# include platform target file
if ("linux" STREQUAL ZX_PROJECT_VARIATIONPOINT_os_ACTIVATION)
    list(APPEND CMAKE_PREFIX_PATH "${ZX_SDK_EXT_DIR}/platform/${ZX_PROJECT_VARIATIONPOINT_platform_ACTIVATION}" CACHE INTERNAL "")
    # include ("${CMAKE_PREFIX_PATH}/target.cmake")s
endif()
find_package(version_did REQUIRED)
version_did_generate_cpp(VERSION_DID_SOURCES VERSION_DID_HEADERS DESTINATION generated)
find_package(imo_com_micro REQUIRED)
link_libraries(imo_com_micro::imo_com_ddk)
# -----------------------------------------------------------------------------
# Includes (ZX)
# -----------------------------------------------------------------------------
include(zx_component)
include(zx_version)
include(zx_install)
include(zx_compilerExtra)
include(zx_platformExtra)
include(zx_doc)
include(zx_dota)
include(zx_param)
include(zx_option)
include(zx_device)
include(zx_sensors)

# -------------------------------------------------------------------------------
# Include compile options
# -------------------------------------------------------------------------------
if(ZX_PROJECT_OPTIONS_CMAKE)
    include(${ZX_PROJECT_OPTIONS_CMAKE} OPTIONAL)
    message(STATUS "ZX_PROJECT_OPTIONS_CMAKE ${ZX_PROJECT_OPTIONS_CMAKE}")
endif()

# -------------------------------------------------------------------------------
# Fetch public interfaces
# -------------------------------------------------------------------------------
set(ZX_ZX_PROJECT_PUBLIC_INCLUDE_DIRS "" CACHE INTERNAL "")
foreach (l_INTERFACE_DIR ${ZX_PROJECT_PUBLIC_INTERFACE_DIRS})
    list(APPEND ZX_ZX_PROJECT_PUBLIC_INCLUDE_DIRS
    ${CMAKE_CURRENT_LIST_DIR}/idc_interfaces/${l_INTERFACE_DIR})
endforeach()

include_directories(${ZX_ZX_PROJECT_PUBLIC_INCLUDE_DIRS})
# no param generator for mcu for now

add_library(prbif INTERFACE IMPORTED GLOBAL)

target_link_libraries(
    prbif
    INTERFACE
        prbif::pf
        prbif::cpj
)
# -------------------------------------------------------------------------------
# Setup Dota Build Generator
# -------------------------------------------------------------------------------
zx_dota_setup_build_generator(
    GENERATOR_COMMAND_VARIABLE  l_DOTA_BUILD_GENERATOR_COMMAND
    PRJ_DIR                     ${CMAKE_CURRENT_LIST_DIR}
    CPJ_DIR                     ${CPJ_FOLDER_NAME}
    BUILD_DIR                   ${ZX_PROJECT_BUILD_DIR}
    DOTA_FILE                   ${ZX_PROJECT_DOTA_FILE}
)

zx_dota_generate(
    DOTA_BUILD_GENERATOR_COMMAND    ${l_DOTA_BUILD_GENERATOR_COMMAND}
)

if ("rpu" STREQUAL ZX_PROJECT_VARIATIONPOINT_target_ACTIVATION)

    set(CMAKE_EXECUTABLE_SUFFIX ".elf") 
    add_executable(
        ${ZX_TARGET_BIN_NAME}
        ${ZX_PROJECT_STARTUP_CXX}
        ${VERSION_DID_SOURCES}
        )
    target_link_components(${ZX_TARGET_BIN_NAME})

    # add_custom_command(TARGET ${ZX_TARGET_BIN_NAME}
    #     POST_BUILD
    #     COMMAND ${CMAKE_COMMAND} -E make_directory ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/log
    #     COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_BINARY_DIR}/generated_include/version/version_idc_mcu_app.json ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/log/
    #     COMMENT "copy vesion for mcu app"
    #     VERBATIM
    # )


    zx_customerBuild_elf_to_bin (
        TARGET_PATH "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}"
        TARGET_NAME ${ZX_TARGET_BIN_NAME}
    )

elseif("sil" STREQUAL ZX_PROJECT_VARIATIONPOINT_target_ACTIVATION)   

endif()

# -------------------------------------------------------------------------------
# Documentation
# -------------------------------------------------------------------------------
if(ENABLE_DOC_BUILD AND "t2x" STREQUAL ZX_PROJECT_VARIATIONPOINT_vehicle_ACTIVATION)
    zx_doc_project(
        PROJECT_NAME mcu_app
        TARGET_LIST
            net_cfg
            NetM
            WDG
            HSI
            ESM 
            evm_cfg       
            dia_cfg
    )
endif()
# -------------------------------------------------------------------------------
# Target exports & packaging
# -------------------------------------------------------------------------------
# install executable file
# install(TARGETS ${ZX_TARGET_BIN_NAME} 
#         RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
#         # LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
#         # ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
# )

# # install bin files
# install(
#     DIRECTORY ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/
#     DESTINATION ${CMAKE_INSTALL_BINDIR}
#     FILES_MATCHING 
#     PATTERN "*.bin"
#     PATTERN "*.img"
#     PATTERN "*.hex"
#     PATTERN "*.s19"
#     PATTERN "*.map"
#     PATTERN "*.a" EXCLUDE
# )
# install_zx_libs(${ZX_TARGET_BIN_NAME})


# -------------------------------------------------------------------------------
# 安装 CMake 配置文件
configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/cmake/zx-config.cmake.in
    ${CMAKE_CURRENT_BINARY_DIR}/zx-config.cmake
    @ONLY
)

# 配置组合库的 CMake 配置文件
configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/cmake/zx_combined-config.cmake.in
    ${CMAKE_CURRENT_BINARY_DIR}/zx_combined-config.cmake
    @ONLY
)

install(FILES
    ${CMAKE_CURRENT_BINARY_DIR}/zx-config.cmake
    ${CMAKE_CURRENT_BINARY_DIR}/zx_combined-config.cmake
    DESTINATION lib/cmake/mcu_app
)

include(cmake/install_func.cmake)
install_zx_libs(${ZX_TARGET_BIN_NAME})
create_zx_combined_library(${ZX_TARGET_BIN_NAME})

# 安装单独的库文件（向后兼容）
install(FILES ${ZX_TARGET_LIBRARIES}
    DESTINATION lib
    COMPONENT libraries
)

# 安装组合库
if(EXISTS "${ZX_COMBINED_LIBRARY_PATH}")
    install(FILES "${ZX_COMBINED_LIBRARY_PATH}"
        DESTINATION lib
        COMPONENT combined_library
    )
endif()

install(TARGETS ${ZX_TARGET_BIN_NAME} 
    EXPORT zx-targets
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    INCLUDES DESTINATION include
)

# 导出编译目标
install(EXPORT zx-targets
    FILE zx-targets.cmake
    NAMESPACE zx::
    DESTINATION lib/cmake/mcu_app
)

include(InstallRequiredSystemLibraries)
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_GENERATOR "TGZ")  
include(CPack)
# -------------------------------------------------------------------------------
# Build Tool Chain Testing
# -------------------------------------------------------------------------------



