#//=============================================================================
#//  C O P Y R I G H T
#//-----------------------------------------------------------------------------
#//  Copyright (c) 2021 by iMotion AI. All rights reserved.
#//=============================================================================
#//  P R O J E C T   I N F O R M A T I O N
#//-----------------------------------------------------------------------------
#//       Projectname: iMotion Driving Computer
#//  Target system(s):
#//       Compiler(s):
#//=============================================================================
#//  N O T E S
#//-----------------------------------------------------------------------------
#//  Notes:
#//=============================================================================
#//  I N I T I A L   A U T H O R   I D E N T I T Y
#//-----------------------------------------------------------------------------
#//        Name:
#//  Department: Xu Ronnie
#//=============================================================================

include_guard(GLOBAL)

add_subdirectory(stm_rtos)
# add_subdirectory(pmasensl)
add_subdirectory(usssensl)

target_link_libraries(
    prbif::pf
    INTERFACE
    stm_rtos
    # pmasensl
    usssensl
    )

add_subdirectory(idc_asf)
add_subdirectory(idc_dia)
add_subdirectory(idc_evm)
add_subdirectory(idc_prm)
add_subdirectory(idc_ssm)
add_subdirectory(idia)
add_subdirectory(iNet)
add_subdirectory(iPdm)
add_subdirectory(iTrace)
add_subdirectory(microsar)


target_link_libraries(
    prbif::pf
    INTERFACE
    idc_dia
    idc_evm
    idc_prm
    idc_ssm
    idia
    iNet
    iPdm
	iTrace
    zx_microsar
    )

function(target_link_components target_name)
    message(STATUS "target_link_components: ${target_name}")
        target_link_libraries(
            ${target_name}
            
            PUBLIC
            -Wl,--start-group
            -Wl,--whole-archive
            pdm_cfg
            ipc_cfg
            pmasensl_cfg
            idia
            net_cfg
            os_cfg
            scom
            -Wl,--no-whole-archive
            ddk
            cool
            valin_cfg
            valout_cfg
            cool_cfg
            dia_cfg
            evm_cfg
            stm_rtos
            ipc
            vfc
            app_cfg
            
            # pmasensl
            usssensl
            zx_microsar
            -Wl,--end-group
        )

endfunction()
