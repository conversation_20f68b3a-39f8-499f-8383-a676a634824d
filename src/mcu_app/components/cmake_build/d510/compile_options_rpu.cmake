message("Setting compile options for d510")

add_compile_definitions(ZX_IDC_SOC_HOBOT_J6E=1)
add_link_options( 
    "-Wl,--wrap=memcpy"
    "--specs=nano.specs"
)
add_compile_options(
    "-fno-omit-frame-pointer"
)
if("Release" STREQUAL CMAKE_BUILD_TYPE)
    add_link_options("--specs=nosys.specs")
    add_compile_options("-O3" )
else()
    add_link_options("--specs=rdimon.specs")
    add_compile_options("-O0" "-g")
endif()
add_compile_options(
    # "-march=armv8-r"
    "-g"
    "-ffreestanding"
    "-ffunction-sections"
    "-fdata-sections"
    "-mfloat-abi=softfp"
    "-mfpu=fpv4-sp-d16"
)
set(l_werror_flags -Wall  -Werror)
set(l_ignore_flags )
list(APPEND l_common_ignore_flags 
    #########################################################
    # Ignore warnings #
    #########################################################
    "-Wno-unused-variable"
    "-Wno-comment"
    "-Wno-unused-but-set-variable"
    "-Wno-unused-function"
    "-Wno-unused-parameter"
)
# add_compile_options(${l_werror_flags}) 
add_compile_options(${l_common_ignore_flags})
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}  -Wno-reorder")
add_compile_options(
    -fno-exceptions 
    -fno-unwind-tables 
    -fno-asynchronous-unwind-tables
)
set(ZX_J6E_MCAL_BUILD_PATH ${CMAKE_HOME_DIRECTORY}/components/microsar/src/_AR_ThirdParty/ThirdParty/hobot_j6e_v0.8.0_mcu/zx_build)

set(ZX_J6E_IPC_LINK_PATH  ${ZX_J6E_MCAL_BUILD_PATH}/J6_MCU_Mcal_MemMap.ld)
set(CMAKE_EXE_LINKER_FLAGS  "-T ${ZX_J6E_IPC_LINK_PATH} ${CMAKE_EXE_LINKER_FLAGS}")
set(CMAKE_EXE_LINKER_FLAGS "-marm -mcpu=cortex-r52 ${CMAKE_EXE_LINKER_FLAGS}")
add_link_options(-T ${CMAKE_CURRENT_LIST_DIR}/rcores.ld.S)
