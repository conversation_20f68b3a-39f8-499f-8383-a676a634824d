/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: ReadMe.txt
 *
 *********************************************************************************************************************/

 /**********************************************************************************************************************
  AUTHOR IDENTITY
-----------------------------------------------------------------------------------------------------------------------
  Name                          Initials      Company
-----------------------------------------------------------------------------------------------------------------------
  Sven Hesselmann               vissvh        Vector Informatik GmbH
-----------------------------------------------------------------------------------------------------------------------
  REVISION HISTORY
-----------------------------------------------------------------------------------------------------------------------
  Version   Date        Author  Change Id     Description
-----------------------------------------------------------------------------------------------------------------------
  01.00.00  2018-04-06  vissvh  -             Initial creation
**********************************************************************************************************************/



The file MSR4_R20_Static_ServicePortInterfaces.arxml provides a list of Service Port Interfaces of the MICROSAR modules in R20.
The list only contains Service Port Interfaces that are (mostly) static and not depending on the configuration of the Service Compoment.
The provided Service Port Interfaces can be used to setup application SWCs without having a configured Service Compoment.

