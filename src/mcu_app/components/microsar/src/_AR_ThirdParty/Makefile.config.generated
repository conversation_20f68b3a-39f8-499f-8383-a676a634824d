
#------------------------------------------------------------------------------
# Version of generated hardware configuration
#------------------------------------------------------------------------------
BRS_GENERATED_HW_CONFIG_VERSION = 345

#------------------------------------------------------------------------------
# Clock frequency of main oscillator (Hz)
#------------------------------------------------------------------------------
MAIN_OSC_CLK = 19200000

#------------------------------------------------------------------------------
# BRS time base clock (MHz)
# This frequency is the CPU clock of the cores.
#------------------------------------------------------------------------------
TIMEBASE_CLOCK = 1000

#------------------------------------------------------------------------------
# Peripheral clock (MHz)
# This frequency is the clock for the on-chip timer used for BRS ms flag.
#------------------------------------------------------------------------------
PERIPH_CLOCK = 250

#------------------------------------------------------------------------------
# Tested Derivative: TI TDA4VM88
#-------------------------------v-----------------------------------------------
DERIVATIVE = TDA4VM88

#------------------------------------------------------------------------------
# CPU Core
#------------------------------------------------------------------------------
CPU_CORE = CORTEX_R5F

#------------------------------------------------------------------------------
# Instruction Set
#------------------------------------------------------------------------------
INSTRUCTION_SET = ARM

#------------------------------------------------------------------------------
# Floating Point Unit (FPU)
#------------------------------------------------------------------------------
FPU_USED = 1

#------------------------------------------------------------------------------
# Evaluation Board: No Led Support
#------------------------------------------------------------------------------
EVA_BOARD = J721EVM

#------------------------------------------------------------------------------
# Support of Hardware Security Module (HSM)
#------------------------------------------------------------------------------
BRS_ENABLE_HSM_SUPPORT = 0

#------------------------------------------------------------------------------
# Support of FlashBootLoader (FBL)
#------------------------------------------------------------------------------
BRS_ENABLE_FBL_SUPPORT = 0

ADDITIONAL_INCLUDES += .
#ADDITIONAL_LDFLAGS_FROM_VBRS = -i ti/drvlib
#ADDITIONAL_LDFLAGS_FROM_VBRS += --library=ti.csl.aer5f
#ADDITIONAL_LDFLAGS_FROM_VBRS += --library=sciclient.aer5f
#ADDITIONAL_LDFLAGS_FROM_VBRS += --library=ti.osal.aer5f
#ADDITIONAL_LDFLAGS_FROM_VBRS += --library=ti.board.aer5f
#ADDITIONAL_LDFLAGS_FROM_VBRS += --library=ti.drv.uart.aer5f
#ADDITIONAL_LDFLAGS_FROM_VBRS += --library=ti.drv.i2c.aer5f
#ADDITIONAL_LDFLAGS_FROM_VBRS += --library=ti.csl.init.aer5f
#ADDITIONAL_LDFLAGS_FROM_VBRS += --library=rm_pm_hal.aer5f
#ADDITIONAL_LDFLAGS_FROM_VBRS += --library=sciclient_direct.aer5f
#ADDITIONAL_LDFLAGS_FROM_VBRS += --library=sciserver_tirtos.aer5f
#ADDITIONAL_LDFLAGS_FROM_VBRS += --library=sciserver_baremetal.aer5f


