<?xml version="1.0" encoding="utf-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<xs:schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:ns1="http://www.vector.com/MIPv2" targetNamespace="http://www.vector.com/MIPv2" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<!-- 
		Type for ActionDefVar
	
		 The ActionDefVarType always contains the element 'Name'. 
		 The ActionDefVarType always contains the element 'ValueBOP' element (that can be combined with other elements) or a 'Value' element.
		 The element 'ValueBOP' must be combined with one of the following elements: 
		-'LookForPathPattern' 
		-'LookForPatternInFile' AND 'PathRel'
		-'BaseValue' AND 'BasePattern'
	 -->
	<xs:complexType name="ActionDefVarType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string"/>
			<xs:choice>
				<xs:element name="Value" type="xs:string"/>
				<xs:sequence>
					<xs:choice>
						<xs:element name="LookForPathPattern" type="xs:string"/>
						<xs:sequence>
							<xs:element name="LookForPatternInFile" type="xs:string"/>
							<xs:element name="PathRel" type="xs:string"/>
						</xs:sequence>
						<xs:sequence>
							<xs:element name="BaseValue" type="xs:string"/>
							<xs:element name="BasePattern" type="xs:string"/>
						</xs:sequence>
					</xs:choice>
					<xs:element name="ValueBOP" type="xs:string"/>
				</xs:sequence>
			</xs:choice>
		</xs:sequence>
		<xs:attribute name="Summary" type="xs:string" use="required"/>
		<xs:attribute name="RelatedPackage" type="xs:unsignedByte" use="required"/>
		<xs:attribute name="ExecuteHidden" type="xs:boolean" use="optional"/>
		<xs:attribute name="ActionId" type="xs:unsignedByte" use="optional"/>
		<!-- ExecuteHidden specifies if the content of 'Summary' attribute shall be displayed in Helper Tool or not. Default value = false, which means that the Summary is always displayed in tool if this attribute is not specified. -->
	</xs:complexType>
	<!-- 
		Type for Condition 
	-->
	<xs:complexType name="ConditionType">
		<xs:attribute name="Pattern" type="xs:string" use="required"/>
		<xs:attribute name="Exist" type="xs:string" use="optional"/>
		<xs:attribute name="Match" type="xs:string" use="optional"/>
		<xs:attribute name="NotMatch" type="xs:string" use="optional"/>
		<xs:attribute name="Equal" type="xs:string" use="optional"/>
		<xs:attribute name="NotEqual" type="xs:string" use="optional"/>
		<xs:attribute name="Greater" type="xs:string" use="optional"/>
		<xs:attribute name="GreaterEqual" type="xs:string" use="optional"/>
		<xs:attribute name="Lower" type="xs:string" use="optional"/>
		<xs:attribute name="LowerEqual" type="xs:string" use="optional"/>
	</xs:complexType>
	<!-- 
		Type for ActionRenameFile
	-->
	<xs:complexType name="ActionRenameFileType">
		<xs:all>
			<xs:element name="PathRel" type="xs:string"/>
			<xs:element name="NewName" type="xs:string"/>
			<xs:element name="ExcludePattern" type="xs:string" minOccurs="0"/>
			<xs:element name="Condition" type="ns1:ConditionType" minOccurs="0"/>
		</xs:all>
		<xs:attribute name="Summary" type="xs:string" use="required"/>
		<xs:attribute name="RelatedPackage" type="xs:unsignedByte" use="required"/>
		<xs:attribute name="ActionId" type="xs:unsignedByte" use="optional"/>
		<xs:attribute name="MultiFile" type="xs:boolean" use="optional"/>
	</xs:complexType>
	<!-- 
		Type for ActionPatchFile
	-->
	<xs:complexType name="ActionPatchFileType">
		<xs:all>
			<xs:element name="PathRel" type="xs:string"/>
			<xs:element name="Pattern" type="xs:string"/>
			<xs:element name="ReplaceBOP" type="xs:string"/>
			<xs:element name="Condition" type="ns1:ConditionType" minOccurs="0"/>
			<xs:element name="SkipPattern" type="xs:string" minOccurs="0"/>
		</xs:all>
		<xs:attribute name="Summary" type="xs:string" use="required"/>
		<xs:attribute name="RelatedPackage" type="xs:unsignedByte" use="required"/>
		<xs:attribute name="MultiFile" type="xs:boolean" use="optional"/>
		<xs:attribute name="ActionId" type="xs:unsignedByte" use="optional"/>
	</xs:complexType>
	<!-- 
		Type for ActionCopyPattern 
	-->
	<xs:complexType name="ActionCopyPatternType">
		<xs:all>
			<xs:element name="SourceRootRel" type="xs:string"/>
			<xs:element name="TargetRootRel" type="xs:string"/>
			<xs:element name="SourcePattern" type="xs:string"/>
			<xs:element name="TargetBOP" type="xs:string"/>
			<xs:element name="ExcludePattern" type="xs:string" minOccurs="0"/>
			<xs:element name="Condition" type="ns1:ConditionType" minOccurs="0"/>
		</xs:all>
		<xs:attribute name="Summary" type="xs:string" use="required"/>
		<xs:attribute name="RelatedPackage" type="xs:unsignedByte" use="required"/>
		<xs:attribute name="ActionId" type="xs:unsignedByte" use="required"/>
		<xs:attribute name="Undo" type="xs:boolean" use="optional"/>
	</xs:complexType>
	<!-- 
		Type for ActionRemove 
	-->
	<xs:complexType name="ActionRemoveType">
		<xs:all>
			<xs:element name="PathRel" type="xs:string"/>
			<xs:element name="Condition" type="ns1:ConditionType" minOccurs="0"/>
		</xs:all>
		<xs:attribute name="Summary" type="xs:string" use="required"/>
		<xs:attribute name="RelatedPackage" type="xs:short" use="required"/>
		<xs:attribute name="MultiFile" type="xs:boolean"/>
		<xs:attribute name="ActionId" type="xs:unsignedByte" use="optional"/>
		<!-- Set RelatedPackage="-1" if all installed packages shall be removed at once  -->
	</xs:complexType>
	<!-- 
		Type for ActionRemovePackage
	-->
	<xs:complexType name="ActionRemovePackageType">
		<xs:attribute name="Summary" type="xs:string" use="required"/>
		<xs:attribute name="RelatedPackage" type="xs:unsignedByte" use="required"/>
	</xs:complexType>
	<!-- 
		Type for ActionCopyPackage
	-->
	<xs:complexType name="ActionCopyPackageType">
		<xs:all>
			<xs:element name="Condition" type="ns1:ConditionType" minOccurs="0"/>
		</xs:all>
		<xs:attribute name="Summary" type="xs:string" use="required"/>
		<xs:attribute name="RelatedPackage" type="xs:unsignedByte" use="required"/>
		<xs:attribute name="ActionId" type="xs:unsignedByte" use="optional"/>
	</xs:complexType>
	<!--
		Type for the Folder of ExpectedStructure
	-->
	<xs:element name="Folder" type="ns1:FolderType"/>
	<xs:complexType name="FolderType">
		<xs:sequence>
			<xs:element ref="ns1:Folder" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="Name" type="xs:string" use="required"/>
		<xs:attribute name="Files" type="xs:string" use="optional"/>
		<xs:attribute name="Mandatory" type="xs:boolean" use="optional"/>
	</xs:complexType>
	<!--
		The 'MipConfig' element specifies the order in which the elements underneath appear. 
		There is exactly ONE 'GeneralInformation' element. After that, there are ONE OR MORE 'Packages' elements. 
		After that, there is ONE 'Install', ONE 'Prepare', ONE 'UndoPrepare' and ONE 'Remove' element.
		Last there CAN be ONE 'Bswmd' and ONE 'Finalize' element. 
	-->
	<xs:element name="MipConfig">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="GeneralInformation">
					<xs:complexType>
						<xs:all>
							<xs:element name="SipBase_RelPath" type="xs:string"/>
							<xs:element name="McalSupply_RelPath" type="xs:string"/>
						</xs:all>
					</xs:complexType>
				</xs:element>
				<xs:element name="Packages">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="Package" maxOccurs="unbounded">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="NameExpected" type="xs:string"/>
										<xs:element name="StructureExpected">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="Folder" type="ns1:FolderType" maxOccurs="unbounded"/>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="Description" type="xs:string"/>
										<xs:element name="Mandatory" type="xs:boolean"/>
									</xs:sequence>
									<xs:attribute name="Summary" type="xs:string" use="required"/>
									<xs:attribute name="Id" type="xs:unsignedByte" use="required"/>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
						<xs:attribute name="TechRef" type="xs:string" use="required"/>
					</xs:complexType>
				</xs:element>
				<xs:element name="Install">
					<xs:complexType>
						<xs:choice maxOccurs="unbounded">
							<xs:element name="ActionCopyPackage" type="ns1:ActionCopyPackageType" maxOccurs="unbounded"/>
						</xs:choice>
					</xs:complexType>
				</xs:element>
				<xs:element name="Prepare">
					<xs:complexType>
						<xs:choice maxOccurs="unbounded">
							<!-- Single Actions may be summarized in an action group -->
							<xs:element name="ActionGroup" minOccurs="0" maxOccurs="unbounded">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="ActionDefVar" type="ns1:ActionDefVarType" minOccurs="0" maxOccurs="unbounded"/>
										<xs:element name="ActionPatchFile" type="ns1:ActionPatchFileType" minOccurs="0" maxOccurs="unbounded"/>
										<xs:element name="ActionCopyPattern" type="ns1:ActionCopyPatternType" minOccurs="0" maxOccurs="unbounded"/>
										<xs:element name="ActionRenameFile" type="ns1:ActionRenameFileType" minOccurs="0" maxOccurs="unbounded"/>
										<xs:element name="ActionRemove" type="ns1:ActionRemoveType" minOccurs="0" maxOccurs="unbounded"/>
									</xs:sequence>
									<xs:attribute name="Summary" type="xs:string" use="required"/>
								</xs:complexType>
							</xs:element>
							<!-- Single Actions without action group -->
							<xs:element name="ActionDefVar" type="ns1:ActionDefVarType" minOccurs="0" maxOccurs="unbounded"/>
							<xs:element name="ActionPatchFile" type="ns1:ActionPatchFileType" minOccurs="0" maxOccurs="unbounded"/>
							<xs:element name="ActionCopyPattern" type="ns1:ActionCopyPatternType" minOccurs="0" maxOccurs="unbounded"/>
							<xs:element name="ActionRenameFile" type="ns1:ActionRenameFileType" minOccurs="0" maxOccurs="unbounded"/>
							<xs:element name="ActionRemove" type="ns1:ActionRemoveType" minOccurs="0" maxOccurs="unbounded"/>
						</xs:choice>
					</xs:complexType>
				</xs:element>
				<xs:element name="UndoPrepare">
					<xs:complexType>
						<xs:choice maxOccurs="unbounded">
							<xs:element name="Undo" maxOccurs="unbounded">
								<xs:complexType>
									<xs:attribute name="ActionId" type="xs:unsignedByte" use="required"/>
									<xs:attribute name="Summary" type="xs:string" use="required"/>
								</xs:complexType>
							</xs:element>
							<xs:element name="ActionDefVar" type="ns1:ActionDefVarType" minOccurs="0" maxOccurs="unbounded"/>
							<xs:element name="ActionPatchFile" type="ns1:ActionPatchFileType" minOccurs="0" maxOccurs="unbounded"/>
							<xs:element name="ActionCopyPattern" type="ns1:ActionCopyPatternType" minOccurs="0" maxOccurs="unbounded"/>
							<xs:element name="ActionRenameFile" type="ns1:ActionRenameFileType" minOccurs="0" maxOccurs="unbounded"/>
							<xs:element name="ActionRemove" type="ns1:ActionRemoveType" minOccurs="0" maxOccurs="unbounded"/>
						</xs:choice>
					</xs:complexType>
				</xs:element>
				<xs:element name="Remove" minOccurs="0">
					<xs:complexType>
						<xs:choice maxOccurs="unbounded">
							<xs:element name="ActionRemovePackage" type="ns1:ActionRemovePackageType" minOccurs="0" maxOccurs="unbounded"/>
							<xs:element name="ActionRemove" type="ns1:ActionRemoveType" minOccurs="0" maxOccurs="unbounded"/>
							<xs:element name="ActionDefVar" type="ns1:ActionDefVarType" minOccurs="0" maxOccurs="unbounded"/>
						</xs:choice>
					</xs:complexType>
				</xs:element>
				<xs:element name="Bswmd" minOccurs="0">
					<!--
						The 'Bswmd' element specifies the order in which the elements underneath appear.
						The 'Bswmd' element always contains ONE 'ActionDefVarBswmd' element at first position. After that, there can be some 'ActionDefVar' elements. 
						Next there can be either the elements ('Conditions' and 'Derivatives') or ('DerivativeScan' and 'ActionDefVarBeforeCopy').
						After that there has to be at least one 'Copy' element.
					-->
					<xs:complexType>
						<xs:sequence>
							<xs:element name="ActionDefVarBswmd" minOccurs="0">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="Name" type="xs:string" fixed="BswmdMcalFolder"/>
										<xs:choice>
											<xs:element name="Value" type="xs:string"/>
											<xs:sequence>
												<xs:choice>
													<xs:element name="LookForPathPattern" type="xs:string"/>
													<xs:choice maxOccurs="unbounded">
														<xs:element name="LookForPatternInFile" type="xs:string"/>
														<xs:element name="PathRel" type="xs:string"/>
													</xs:choice>
													<xs:choice maxOccurs="unbounded">
														<xs:element name="BaseValue" type="xs:string"/>
														<xs:element name="BasePattern" type="xs:string"/>
													</xs:choice>
												</xs:choice>
												<xs:element name="ValueBOP" type="xs:string"/>
											</xs:sequence>
										</xs:choice>
									</xs:sequence>
									<xs:attribute name="Summary" type="xs:string" use="required"/>
									<xs:attribute name="RelatedPackage" type="xs:unsignedByte" use="required"/>
									<xs:attribute name="ExecuteHidden" type="xs:boolean" use="optional"/>
									<xs:attribute name="ActionId" type="xs:unsignedByte" use="optional"/>
								</xs:complexType>
							</xs:element>
							<xs:element name="ActionDefVar" type="ns1:ActionDefVarType" minOccurs="0" maxOccurs="unbounded"/>
							<xs:choice>
								<xs:choice maxOccurs="unbounded">
									<xs:element name="Conditions" minOccurs="0">
										<xs:complexType>
											<xs:choice maxOccurs="unbounded">
												<xs:element name="Condition" maxOccurs="unbounded">
													<xs:complexType>
														<xs:sequence>
															<xs:element name="LookForExistingPathPattern" type="xs:string"/>
														</xs:sequence>
														<xs:attribute name="Name" type="xs:string" use="required"/>
													</xs:complexType>
												</xs:element>
											</xs:choice>
										</xs:complexType>
									</xs:element>
									<xs:element name="Derivatives" minOccurs="0">
										<xs:complexType>
											<xs:choice maxOccurs="unbounded">
												<xs:element name="Derivative" maxOccurs="unbounded">
													<xs:complexType>
														<xs:attribute name="ConditionName" type="xs:string" use="optional"/>
														<xs:attribute name="DisplayName" type="xs:string" use="required"/>
														<xs:attribute name="IdentifyingFileFragment" type="xs:string" use="required"/>
													</xs:complexType>
												</xs:element>
											</xs:choice>
										</xs:complexType>
									</xs:element>
								</xs:choice>
								<xs:sequence>
									<xs:element name="DerivativeScan" minOccurs="0">
										<xs:complexType>
											<xs:all>
												<xs:element name="Pattern" type="xs:string"/>
												<xs:element name="DisplayNameBOP" type="xs:string"/>
												<xs:element name="IdentifyingFileFragmentBOP" type="xs:string"/>
											</xs:all>
										</xs:complexType>
									</xs:element>
									<xs:element name="ActionDefVarBeforeCopy" type="ns1:ActionDefVarType" minOccurs="0" maxOccurs="unbounded"/>
								</xs:sequence>
							</xs:choice>
							<xs:element name="Copy" maxOccurs="unbounded">
								<xs:complexType>
									<xs:all>
										<xs:element name="Pattern" type="xs:string"/>
										<xs:element name="TargetBOP" type="xs:string"/>
										<xs:element name="ExcludePattern" type="xs:string" minOccurs="0"/>
										<xs:element name="Condition" type="ns1:ConditionType" minOccurs="0"/>
										<xs:element name="RemovePattern" type="xs:string"/>
									</xs:all>
									<xs:attribute name="RelatedPackage" type="xs:unsignedByte" use="required"/>
									<xs:attribute name="Renesas" type="xs:boolean" use="optional"/>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="Finalize" minOccurs="0">
					<xs:complexType>
						<xs:choice maxOccurs="unbounded">
							<!-- Single Actions may be summarized in an action group -->
							<xs:element name="ActionGroup" minOccurs="0" maxOccurs="unbounded">
								<xs:complexType>
									<xs:choice maxOccurs="unbounded">
										<xs:element name="ActionRemove" type="ns1:ActionRemoveType" minOccurs="0" maxOccurs="unbounded"/>
										<xs:element name="ActionPatchFile" type="ns1:ActionPatchFileType" minOccurs="0" maxOccurs="unbounded"/>
										<xs:element name="ActionDefVar" type="ns1:ActionDefVarType" minOccurs="0" maxOccurs="unbounded"/>
										<xs:element name="ActionRenameFile" type="ns1:ActionRenameFileType" minOccurs="0" maxOccurs="unbounded"/>
										<xs:element name="ActionCopyPattern" type="ns1:ActionCopyPatternType" minOccurs="0" maxOccurs="unbounded"/>
									</xs:choice>
									<xs:attribute name="Summary" type="xs:string" use="required"/>
								</xs:complexType>
							</xs:element>
							<!-- Single Actions without action group -->
							<xs:element name="ActionRemove" type="ns1:ActionRemoveType" minOccurs="0" maxOccurs="unbounded"/>
							<xs:element name="ActionPatchFile" type="ns1:ActionPatchFileType" minOccurs="0" maxOccurs="unbounded"/>
							<xs:element name="ActionDefVar" type="ns1:ActionDefVarType" minOccurs="0" maxOccurs="unbounded"/>
							<xs:element name="ActionRenameFile" type="ns1:ActionRenameFileType" minOccurs="0" maxOccurs="unbounded"/>
							<xs:element name="ActionCopyPattern" type="ns1:ActionCopyPatternType" minOccurs="0" maxOccurs="unbounded"/>
						</xs:choice>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
