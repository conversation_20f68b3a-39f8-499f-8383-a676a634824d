<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<html>
	<head>
		<title>DaVinci Configurator Pro Release Notes</title>
		<meta name="vs_showGrid" content="False"/>
		<style type="text/css">
			<!-- body { font-size:12pt;font-family:arial,sans-serif; color:#000000; background-color: #E5E5E5;}
      h1 {font-size:18pt; font-family:arial,sans-serif}
      h2 {font-size:16pt; font-family:arial,sans-serif}
      h3 {font-size:12pt; font-family:arial,sans-serif}
      h4 {font-size:10pt; font-family:arial,sans-serif}
      p,td,th,address,ul,ol,li {font-size:10pt; font-family:arial,sans-serif}
      th {background-color: #D0D0E0;}
      pre {font-size:10pt;}
      p.small {font-size:8pt; font-family:arial,sans-serif}
      a {font-family:arial,sans-serif}
      /* from W3-REC.css: */ @media screen { /* hide from IE3 */ a:hover { background: #FE0 }}
      //-->
		</style>
	</head>
	<body>
		<h1>
			<a name="_top">DaVinci Configurator Pro Release Notes</a>
		</h1>
		<h2>Table of contents</h2>
		<table border="0">
			<tr>
				<td>
					<a href="#5.22 SP1">DaVinci Configurator Pro 5.22 (SP1)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.22">DaVinci Configurator Pro 5.22</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.21 SP3">DaVinci Configurator Pro 5.21 (SP3)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.21 SP2">DaVinci Configurator Pro 5.21 (SP2)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.21 SP1">DaVinci Configurator Pro 5.21 (SP1)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.21">DaVinci Configurator Pro 5.21</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.20 SP4">DaVinci Configurator Pro 5.20 (SP4)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.20 SP3">DaVinci Configurator Pro 5.20 (SP3)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.20 SP2">DaVinci Configurator Pro 5.20 (SP2)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.20 SP1">DaVinci Configurator Pro 5.20 (SP1)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.20">DaVinci Configurator Pro 5.20</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.19 SP6">DaVinci Configurator Pro 5.19 (SP6)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.19 SP5">DaVinci Configurator Pro 5.19 (SP5)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.19 SP4">DaVinci Configurator Pro 5.19 (SP4)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.19 SP3">DaVinci Configurator Pro 5.19 (SP3)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.19 SP2">DaVinci Configurator Pro 5.19 (SP2)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.19 SP1">DaVinci Configurator Pro 5.19 (SP1)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.19">DaVinci Configurator Pro 5.19</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.18 SP7">DaVinci Configurator Pro 5.18 (SP7)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.18 SP6">DaVinci Configurator Pro 5.18 (SP6)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.18 SP5">DaVinci Configurator Pro 5.18 (SP5)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.18 SP4">DaVinci Configurator Pro 5.18 (SP4)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.18 SP3">DaVinci Configurator Pro 5.18 (SP3)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.18 SP2">DaVinci Configurator Pro 5.18 (SP2)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.18 SP1">DaVinci Configurator Pro 5.18 (SP1)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.18">DaVinci Configurator Pro 5.18</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.17 SP10">DaVinci Configurator Pro 5.17 (SP10)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.17 SP9">DaVinci Configurator Pro 5.17 (SP9)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.17 SP8">DaVinci Configurator Pro 5.17 (SP8)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.17 SP7">DaVinci Configurator Pro 5.17 (SP7)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.17 SP6">DaVinci Configurator Pro 5.17 (SP6)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.17 SP5">DaVinci Configurator Pro 5.17 (SP5)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.17 SP4">DaVinci Configurator Pro 5.17 (SP4)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.17 SP3">DaVinci Configurator Pro 5.17 (SP3)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.17 SP2">DaVinci Configurator Pro 5.17 (SP2)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.17 SP1">DaVinci Configurator Pro 5.17 (SP1)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.17">DaVinci Configurator Pro 5.17</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.16 SP7">DaVinci Configurator Pro 5.16 (SP7)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.16 SP6">DaVinci Configurator Pro 5.16 (SP6)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.16 SP5">DaVinci Configurator Pro 5.16 (SP5)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.16 SP4">DaVinci Configurator Pro 5.16 (SP4)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.16 SP3">DaVinci Configurator Pro 5.16 (SP3)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.16 SP2">DaVinci Configurator Pro 5.16 (SP2)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.16 SP1">DaVinci Configurator Pro 5.16 (SP1)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.16">DaVinci Configurator Pro 5.16</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.15 SP8">DaVinci Configurator Pro 5.15 (SP8)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.15 SP7">DaVinci Configurator Pro 5.15 (SP7)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.15 SP6">DaVinci Configurator Pro 5.15 (SP6)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.15 SP5">DaVinci Configurator Pro 5.15 (SP5)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.15 SP4">DaVinci Configurator Pro 5.15 (SP4)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.15 SP3">DaVinci Configurator Pro 5.15 (SP3)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.15 SP2">DaVinci Configurator Pro 5.15 (SP2)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.15 SP1">DaVinci Configurator Pro 5.15 (SP1)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.15">DaVinci Configurator Pro 5.15</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.14 SP5">DaVinci Configurator Pro 5.14 (SP5)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.14 SP4">DaVinci Configurator Pro 5.14 (SP4)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.14 SP3">DaVinci Configurator Pro 5.14 (SP3)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.14 SP2">DaVinci Configurator Pro 5.14 (SP2)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.14 SP1">DaVinci Configurator Pro 5.14 (SP1)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.14">DaVinci Configurator Pro 5.14</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.13 SP7">DaVinci Configurator Pro 5.13 (SP7)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.13 SP6">DaVinci Configurator Pro 5.13 (SP6)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.13 SP5">DaVinci Configurator Pro 5.13 (SP5)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.13 SP4">DaVinci Configurator Pro 5.13 (SP4)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.13 SP3">DaVinci Configurator Pro 5.13 (SP3)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.13 SP2">DaVinci Configurator Pro 5.13 (SP2)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.13 SP1">DaVinci Configurator Pro 5.13 (SP1)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.13">DaVinci Configurator Pro 5.13</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.12 SP4">DaVinci Configurator Pro 5.12 (SP4)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.12 SP3">DaVinci Configurator Pro 5.12 (SP3)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.12 SP2">DaVinci Configurator Pro 5.12 (SP2)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.12 SP1">DaVinci Configurator Pro 5.12 (SP1)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.12">DaVinci Configurator Pro 5.12</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.11 SP5">DaVinci Configurator Pro 5.11 (SP5)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.11 SP4">DaVinci Configurator Pro 5.11 (SP4)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.11 SP3">DaVinci Configurator Pro 5.11 (SP3)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.11 SP2">DaVinci Configurator Pro 5.11 (SP2)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.11 SP1">DaVinci Configurator Pro 5.11 (SP1)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.11">DaVinci Configurator Pro 5.11</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.10 SP9">DaVinci Configurator Pro 5.10 (SP9)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.10 SP8">DaVinci Configurator Pro 5.10 (SP8)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.10 SP7">DaVinci Configurator Pro 5.10 (SP7)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.10 SP6">DaVinci Configurator Pro 5.10 (SP6)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.10 SP5">DaVinci Configurator Pro 5.10 (SP5)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.10 SP4">DaVinci Configurator Pro 5.10 (SP4)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.10 SP3">DaVinci Configurator Pro 5.10 (SP3)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.10 SP2">DaVinci Configurator Pro 5.10 (SP2)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.10 SP1">DaVinci Configurator Pro 5.10 (SP1)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.10">DaVinci Configurator Pro 5.10</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.9 SP7">DaVinci Configurator Pro 5.9 (SP7)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.9 SP6">DaVinci Configurator Pro 5.9 (SP6)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.9 SP5">DaVinci Configurator Pro 5.9 (SP5)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.9 SP4">DaVinci Configurator Pro 5.9 (SP4)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.9 SP3">DaVinci Configurator Pro 5.9 (SP3)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.9 SP2">DaVinci Configurator Pro 5.9 (SP2)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.9 SP1">DaVinci Configurator Pro 5.9 (SP1)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.9">DaVinci Configurator Pro 5.9</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.8 SP5">DaVinci Configurator Pro 5.8 (SP5)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.8 SP4">DaVinci Configurator Pro 5.8 (SP4)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.8 SP3">DaVinci Configurator Pro 5.8 (SP3)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.8 SP2">DaVinci Configurator Pro 5.8 (SP2)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.8 SP1">DaVinci Configurator Pro 5.8 (SP1)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.8">DaVinci Configurator Pro 5.8</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.7 SP4">DaVinci Configurator Pro 5.7 (SP4)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.7 SP3">DaVinci Configurator Pro 5.7 (SP3)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.7 SP2">DaVinci Configurator Pro 5.7 (SP2)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.7 SP1">DaVinci Configurator Pro 5.7 (SP1)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.7">DaVinci Configurator Pro 5.7</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.6 SP11">DaVinci Configurator Pro 5.6 (SP11)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.6 SP10">DaVinci Configurator Pro 5.6 (SP10)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.6 SP9">DaVinci Configurator Pro 5.6 (SP9)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.6 SP8">DaVinci Configurator Pro 5.6 (SP8)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.6 SP7">DaVinci Configurator Pro 5.6 (SP7)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.6 SP6">DaVinci Configurator Pro 5.6 (SP6)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.6 SP5">DaVinci Configurator Pro 5.6 (SP5)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.6 SP4">DaVinci Configurator Pro 5.6 (SP4)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.6 SP3">DaVinci Configurator Pro 5.6 (SP3)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.6 SP2">DaVinci Configurator Pro 5.6 (SP2)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.6 SP1">DaVinci Configurator Pro 5.6 (SP1)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.6">DaVinci Configurator Pro 5.6</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.5 SP9">DaVinci Configurator Pro 5.5 (SP9)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.5 SP8">DaVinci Configurator Pro 5.5 (SP8)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.5 SP7">DaVinci Configurator Pro 5.5 (SP7)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.5 SP6">DaVinci Configurator Pro 5.5 (SP6)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.5 SP5">DaVinci Configurator Pro 5.5 (SP5)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.5 SP4">DaVinci Configurator Pro 5.5 (SP4)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.5 SP3">DaVinci Configurator Pro 5.5 (SP3)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.5 SP2">DaVinci Configurator Pro 5.5 (SP2)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.5 SP1">DaVinci Configurator Pro 5.5 (SP1)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.5">DaVinci Configurator Pro 5.5</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.4 SP7">DaVinci Configurator Pro 5.4 (SP7)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.4 SP6">DaVinci Configurator Pro 5.4 (SP6)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.4 SP5">DaVinci Configurator Pro 5.4 (SP5)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.4 SP4">DaVinci Configurator Pro 5.4 (SP4)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.4 SP3">DaVinci Configurator Pro 5.4 (SP3)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.4 SP2">DaVinci Configurator Pro 5.4 (SP2)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.4 SP1">DaVinci Configurator Pro 5.4 (SP1)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.4">DaVinci Configurator Pro 5.4</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.3">DaVinci Configurator Pro 5.3</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.2">DaVinci Configurator Pro 5.2</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.0 SP1">DaVinci Configurator Pro 5.0 (SP1)</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#5.0">DaVinci Configurator Pro 5.0</a>
				</td>
			</tr>
			<tr>
				<td>
					<a href="#info">Additional Information</a>
				</td>
			</tr>
		</table>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.22 SP1">DaVinci Configurator Pro 5.22 (SP1)</a>
		</h2>
		<h3>Miscellaneous Tool Features</h3>
		<ul>
			<li>TopDownService Config - Improve Evaluation of Support Dirty Flag Value at NvBlockDescriptors</li>
			<li>VTT_Gen7 support in OsIsrService</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Derivation aborts if CAN ID is not defined at CanFrameTriggering</li>
			<li>&apos;Generate Module&apos; generates whole project</li>
			<li>Default selection of &apos;Configuration of a CryptoKeyElement&apos; during creation of new Key entry at Security Comfort view</li>
			<li>Non-AUTOSAR Modules have the wrong icon</li>
			<li>Default value in &apos;Select Alternate Module Definition&apos; dialog</li>
			<li>Differences View doesn&apos;t display elements correctly</li>
			<li>Adapt DET Comfort View to AUTOSAR 19-11</li>
			<li>DiagnosticParameterIdentifier may cause Update Workflow to abort</li>
			<li>Routing Path Form Editor is not displayed correctly</li>
			<li>Data Type Check - Native Declaration of Platform Types</li>
			<li>NullPointerException is thrown in BswM Editor</li>
			<li>Missing EcuC-RuleEvents for ModelTraverserValidator in Initial Backgroundvalidation</li>
			<li>Differences View: Context menu entry &apos;Copy&apos; is missing</li>
			<li>Apply SoftDerived Parameter to restored derived container</li>
			<li>Missing modules in updater list</li>
			<li>Copy menu is missing in the context menu</li>
			<li>Show OsApplications in Component Prototype Properties</li>
			<li>Ignore index of multi instance parameter on DiffMerge</li>
			<li>PDU Graph shows wrong layout</li>
			<li>Derived the J1939TpRx- and -TxProtocolType</li>
			<li>Incorrect creation of DataType mappings for IOControl Ports</li>
			<li>For EIRA Rx ComMSignals no ComMPncComSignalChannelRef is needed</li>
			<li>Switching to the last editor throws an error message</li>
			<li>Not all Generator Warnings do get printed on console</li>
			<li>ConnectorValidationRule: Implement Timeout</li>
			<li>Support PBS variant MultiDriverMappings in the EcuC Updater (mapping of first file set is valid for all other file sets)</li>
			<li>Exceptions not handled in EVS Editor</li>
			<li>Support of LinIfProtocolType SAE_J2602_2012</li>
			<li>EVS Editor does not show final validation errors after EVS import</li>
			<li>Only one UseCase per Pre-/RecConfig is recognized</li>
			<li>Guarantee deterministic PDU Graph layout</li>
			<li>ComSignalGroup parameters are not derived for bi-directional ISignals</li>
			<li>MCSupportData is just written to Internal Behavior if &apos;ARMXL to A2L Generator&apos; is generated</li>
			<li>Exception pops up when configuring Update Workflow</li>
			<li>Change the data type of the log level parameter of the DiagnosticDataModifer</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.22">DaVinci Configurator Pro 5.22</a>
		</h2>
		<h3>Extensions</h3>
		<h4>New Comfort Configuration for the Crypto Stack</h4>
		<ul>
			<li>Comfort editor for the configuration of the Crypto services</li>
			<li>Assistant for the creation of CsmJobs</li>
		</ul>	
		<h4>Improvements and Extensions of PDU Diagram View in PDU Editor</h4>
		<ul>
			<li>Change color in PDU Graph</li>
			<li>Show infos and warnings on PDU nodes</li>
			<li>Show roles in PDU nodes</li>
			<li>Highlight the edges of the selected PDU</li>
			<li>Highlight selected edges</li>
			<li>Context menu entries are missing</li>
			<li>UdpNm and SoAd are changed in Com stack hierarchy</li>
		</ul>
		<h4>BaseEcuC Generator Features and Issue Fixes</h4>
		<ul>
			<li>If both TpPg.TpSdu and TpPg.Sdu list exist ignore TpPg.TpSdu</li>
			<li>CddPdurLowerLayerTxPdu and CddPdurLowerLayerRxPdu not derived for UserDefinedPdus</li>
			<li>NullPointerException if CommunicationDirection is missing in the FramePort</li>
			<li>Check if a SystemNode and Ecu-Instance exists at the end of FilePreprocessing</li>
			<li>Unnessary PduR API forwarding routing paths created for ContainedIPdus</li>
			<li>Implement new ASR 4.5 mapping rules for SoAd</li>
			<li>Derivation of DltTxPdu and DltRxPdu</li>
			<li>Extend mapping rule for ComMChannelPerPnc according to ASR 4.5</li>
			<li>Import internal debouncing from DEXT</li>
			<li>Incorrect derivation of ComMUsers for PNCs</li>
			<li>Derive only SoAdSocketRoutingGroups that are used by a service</li>
			<li>Change mapping rule for CanControllerPropSeg according to ASR 4.1.1</li>
			<li>Extend mapping rules for the SomeIpTpChannel container according to ASR 4.4.0</li>
			<li>Import immediateNvDataStorage from DEXT DTCProps</li>
			<li>Optimize the derivation of DcmDslProtocolRxTesterSourceAddr</li>
			<li>Derive non gateway routed rx J1939DcmIPdu should check NodeId</li>
			<li>Derivation of the FiM module in CFG5</li>
			<li>Implement support for new ASR 4.5 ComSignalType definitions</li>
			<li>Change NamingRule for DcmDspVehInfoData Container</li>
			<li>Derivation of IssmTxSignalExRef and IssmRxSignalExtRef</li>
			<li>Derive CddPdurLowerLayerTxPdu and CddPdurLowerLayerRxPdu</li>
			<li>Filter *RouteDest according PDU direction for multicast sockets</li>
			<li>Allow to disable UUID validation per file set</li>
			<li>Only one ComMUser is created for all PncMappings</li>
			<li>Change the way of merging the J1939NmNode and its NodeID</li>
			<li>Derive ComDataInvalidAction from SignalPort.handleInvalid</li>
			<li>Implement RX / TX aware requirements checks for LdCom</li>
		</ul>
		<h4>Miscellaneous Tool Features</h4>
		<ul>
			<li>Extend RTE59002 validator to consider DiagnosticDataElement of DiagnosticServiceSwMappings</li>
			<li>Allow Usage of ModeRequestPorts in Expressions of Different Partitions in the BswM Editor</li>
			<li>Adapt column filter behavior to Excel</li>
			<li>Support PostBuild-Selectable variant MultiDriverMappings (mapping of first file set is valid for all other file sets)</li>
			<li>Extend the module export assistant</li>
			<li>Input File Assistant: Implement a new page for selective update</li>
			<li>Support relative paths for &apos;Script File&apos; in Input File Assistant</li>
			<li>Support new NVRAM Block in Block Assistant</li>
			<li>Support BitfieldTextTable CompuMethods for ApplicationPrmitiveDataTypes</li>
			<li>Compare and Merge: Support base container for renamed containers</li>
			<li>Difference View: provide 3-way-merge classification</li>
			<li>Adjustment of &apos;Setup Memory Block&apos;-Assistant to the new DEM model</li>
			<li>VTT-activation/-deactivation on TargetType-Change</li>
			<li>Show DvDev path validation to Project Config Information Bar</li>
			<li>Create a command line switch that disables initial background validation</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Main window goes into the background (last active window appears) when clicking on &apos;clear filter&apos; from a grid</li>
			<li>File Supervision event on Win10 differs to Win7</li>
			<li>Task Mapping Grid Does not Scroll to Mapped Runnable</li>
			<li>Flattener: Missing Connectors in Case of Multi Instantiation</li>
			<li>Exception in Application Components Part With Variant Data Mappings</li>
			<li>E2EProtectionValidator Exception Removing Multiple Receivers</li>
			<li>CompuMethods with illegal number format causes file analysis to abort</li>
			<li>Java deadlock in File Supervision</li>
			<li>Solving action cannot be sorted when using result iterator</li>
			<li>Filter does not work in combination with sorting</li>
			<li>Task Mapping Assistant: ExecutionOrderConstraints Cannot be Selected</li>
			<li>Changing number format for parameter has no effect</li>
			<li>Exception &apos;Unhandled event loop exception: String index out of range: -1&apos; occurs</li>
			<li>BSWM Editor does not show variant BswMActionListItem</li>
			<li>&lt;Multiple&gt; value indication broken for shortnames and dropdown controls</li>
			<li>UnitOfWork is started twice when adding elements</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.21 SP3">DaVinci Configurator Pro 5.21 (SP3)</a>
		</h2>
		<h3>Miscellaneous Tool Features</h3>
		<ul>
			<li>TopDownService Config - Improve Evaluation of Support Dirty Flag Value at NvBlockDescriptors</li>
			<li>Change the way of merging the J1939NmNode and its NodeID</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Flattener: Missing Connectors in case of Multi Instantiation</li>
			<li>AutomationInterface: AccessControlException when opening a parameterized project </li>
			<li>PDU Graph: Context menu entries are missing</li>
			<li>Derivation aborts if CAN ID is not defined at CanFrameTriggering</li>
			<li>NullPointerException if CommunicationDirection is missing in the FramePort</li>
			<li>PDU Graph: UdpNm and SoAd are changed in com stack hierarchy</li>
			<li>DiagnosticParameterIdentifier may cause Update Workflow to abort</li>
			<li>Data Type Check - Native Declaration of Platform Types</li>
			<li>Exception in Application Components part with Variant Data Mappings</li>
			<li>AutomationInterface: ProvisionException when opening a project loaded via arxml files</li>
			<li>NVM Need Priority Selection</li>
			<li>Support PBS variant MultiDriverMappings (mapping of first file set is valid for all other file sets)</li>
			<li>Differences View: Context menu entry &apos;Copy&apos; is missing</li>
			<li>Apply SoftDerived Parameter to restored derived container</li>
			<li>Missing modules in updater list</li>
			<li>Copy menu is missing in the context menu</li>
			<li>Show OsApplications in Component Prototype Properties</li>
			<li>Ignore index of multi instance parameter on DiffMerge</li>
			<li>Derived the J1939TpRx- and -TxProtocolType</li>
			<li>Incorrect creation of DataType mappings for IOControl Ports</li>
			<li>For EIRA Rx ComMSignals no ComMPncComSignalChannelRef is needed</li>
			<li>ConnectorValidationRule: Implement Timeout</li>
			<li>Support PBS variant MultiDriverMappings in the EcuC Updater (mapping of first file set is valid for all other file sets)</li>
			<li>Only one UseCase per Pre-/RecConfig is recognized</li>
			<li>ComSignalGroup parameters are not dervied for bi-directional ISignals</li>
			<li>MCSupportData is just written to Internal Behavior if &apos;ARMXL to A2L Generator&apos; is generated</li>
			<li>Exception pops up when configuring Update Workflow</li>
			<li>Change the data type of the log level parameter of the DiagnosticDataModifer</li>
			<li>Grids ignore unit/number format settings</li>
			<li>Changing number format for parameter has no effect</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.21 SP2">DaVinci Configurator Pro 5.21 (SP2)</a>
		</h2>	
		<h3>Miscellaneous Tool Features</h3>
		<ul>
			<li>Update StdDef of EthIf</li>
			<li>VTT-Activation/-Deactivation on TargetType-Change (in Project Settings Editor)</li>
			<li>Filter *RouteDest according PDU direction for multicast sockets</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Incorrect derivation of ComMUsers for PNCs</li>
			<li>StringIndexOutOfBoundsException by base validator StringParameterLength</li>
			<li>Derivation of DltTxPdu and DltRxPdu</li>
			<li>ConnectorValidation - Performance Issue for Large Arrays</li>
			<li>Only one ComMUser is created for all PncMappings</li>
			<li>Exception When Connecting Ports</li>
			<li>E2EProtectionValidator Exception Removing Multiple Receivers</li>
			<li>TaskMappingAssistant: ExecutionOrderConstraints Cannot be Selected</li>
			<li>Application Ports Grid Errors For Invalid Connectors</li>
			<li>BswmdModelGenerator doesn&apos;t support whitespaces in paths</li>
			<li>Unnessary PduR API forwarding routing paths created for ContainedIPdus</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>		
		<!-- #################### -->
		<h2>
			<a name="5.21 SP1">DaVinci Configurator Pro 5.21 (SP1)</a>
		</h2>	
		<h3>Fixed Issues</h3>
		<ul>
			<li>Automation Interface: Incorrect AccessPermission in Workflow Update</li>
			<li>Automation Interface: Unnecessary multiple project load in Workflow</li>
			<li>Exception when following Shortname-Link in a table</li>
			<li>Extend RTE59002 validator to consider DiagnosticDataElement of DiagnosticServiceSwMappings</li>
			<li>Error when importing variants from an .arxml file</li>
			<li>Derivation of DltTxPdu and DltRxPdu</li>
			<li>ConnectorValidation: Exception for SubElementMappings</li>
			<li>Data Mapping Validation: Check Lin Slave Response Error Signals</li>
			<li>Do not enter Pending Update when only missing input files are replaced</li>
			<li>Exception in MemBlockEditor</li>
			<li>Selection of generation steps does not work</li>
			<li>IllegalArgumentException in EcucBswInitConfigPtrNameValidation</li>
			<li>New ExtGenStep does not appear in &apos;Code Generation&apos; tree</li>
			<li>Exception &apos;Unhandled event loop exception: String index out of range: -1&apos; occurs</li>
			<li>Workflow - GeneratorLoading Warning dialog pops up during update</li>
			<li>AUTOSAR 19-11 files are rejected in Input Files Assistant</li>
			<li>CFG5 crashes when changing focus</li>
			<li>Support relative paths for &apos;Script File&apos; in IFA</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>		
		<!-- #################### -->
		<h2>
			<a name="5.21">DaVinci Configurator Pro 5.21</a>
		</h2>
		<h3>Extensions</h3>
		<h4>Automation Interface Extensions</h4>
		<ul>
			<li>Provide an API to check for existing input files</li>
			<li>Connecting Two Ports of the Same Component</li>
			<li>Provide an API to check if a project update is required</li>
		</ul>	
		<h4>Improvements and Extensions of PDU Diagram View in PDU Editor</h4>
		<ul>
			<li>Show PDU names in PDU graphs</li>
			<li>Support multiselection for PDU Graph</li>
			<li>Support different layouts for the PDU Graph</li>
			<li>Provide zoom buttons for the PDU Graph</li>
			<li>Extend Context menu of PDUs for &apos;show in PDUs (Graph)&apos;</li>
			<li>Context menu entry &apos;Route PDU&apos; is missing in the PDU Graph</li>
			<li>Improve edge layout</li>
			<li>Support of additional BSW Modules and their PDU modelling: CDD modules, DoIp, LinTP, FrArTp, J1939Rm, J1939Tp</li>
		</ul>
		<h4>BaseEcuC Generator Features and Issue Fixes</h4>
		<ul>
			<li>Derive PdelayLatencyThreshold acc. to ASR 4.4</li>
			<li>FrIfUserTxUL for gateway routed contained PDUs in ContainerIPdus is not derived to PDUR</li>
			<li>J1939DcmDiagnosticMessageSupport is not derived when J1939DcmIPdu.Length is 0</li>
			<li>Adapt UINT8_DYN mapping rules to ASR 4.4 description</li>
			<li>Performance Improvement: Load only files from old project needed by the updater</li>
			<li>Error occurs when read input file with CompuMethod with upperLimit &quot;INF&quot;</li>
			<li>NullPointerException thrown during mapping if J1939TpConfig has no cluster</li>
			<li>Direct Mapping between PNC and a ComM Channel does not work</li>
			<li>Derive SoAdTxUpperLayerType and SoAdRxUpperLayerType according to EthTpConfig</li>
			<li>GlobalTimeSubDomains not considered for ASR 4.4 Extracts</li>
			<li>Implement mapping rule for ComMManageReference</li>
			<li>Derive GlobalTime Synchronization according to ASR 4.4</li>
			<li>Implement new update rules for Container and ModuleConfiguraions</li>
			<li>Log error message during derivation of PduRRoutingPathGroup/PduRDestPduRef if [constr_3275] is violated</li>
			<li>Extend DEXT use-case validations</li>
			<li>Derive parameters J1939Tp[Tx|Rx]RetrySupport, J1939RmSupportRequest2 and J1939NmChannelUsesAddressArbitration</li>
			<li>Derive SoAdSocketRoutes for dynamic client use-case with identical HeaderIDs</li>
			<li>Derive FLOAT DataType for DID DataElements (DCM)</li>
			<li>Only one PduPort is considered for Payload of SecuredIPdu</li>
			<li>Derive all parameters of CanSMManagerNetwork of ASR 4.2.2</li>
			<li>Soft derived container is displayed as not derived</li>
			<li>DcmDslConnections are not correctly derived</li>
			<li>Implement ASR4.4 mapping rule for the parameters &lt;BusNm&gt;PnEnabled</li>
			<li>Wrong API forwarding routing path created for DcmIPdu</li>
			<li>Derive parameters in J1939Tp according to ASR 4.4</li>
			<li>Derive J1939TpRxPgDynLength/J1939TpTxPgDynLength</li>
			<li>Derive the parameter EcuCPduCollection/Pdu.DynamicLength</li>
			<li>Derive cluster name into the EcuC file for VttOnlya and for dual target</li>
			<li>Evaluate RoleBasedPortAssignment in DiagnosticEventPortMappings while auto deriving Dcm/Dem ports</li>
			<li>Deleted SoftDerived ReferenceParameter restored during update</li>
			<li>Derivation of J1939DcmDiagnosticMessageSupport should exclude gateway routed DcmIPdu</li>
			<li>Change derivation of CanControllerBaudrate to float</li>
			<li>The short name of J1939TpConnection should also consider its sdu list</li>
			<li>Support Routed J1939DcmIPdus </li>
			<li>Version constant for SD client service minor version wildcard is wrong</li>
			<li>Implement mapping rule for IpduMContainedTxPduPriority</li>
		</ul>
		<h4>Miscellaneous Tool Features</h4>
		<ul>
			<li>Create TA Tool Suite project in &apos;Project-new-Assistant&apos;</li>
			<li>ECU Software Components Editor: Add links to Task Mapping and Exclusive Area Grids</li>
			<li>Improve keyboard usability of quicktype filter control for NatTables</li>
			<li>Enhance configuration element state for Soft-Derived parameters and containers</li>
			<li>Support soft-derived configuration elements in the UI: Enhanced information in Properties View</li>
			<li>Provide detailed &apos;pending update&apos; information, including &apos;pending update reason&apos; for each missing input file</li>
			<li>General activation for grid modifications, Edit (F2) / (double) click</li>
			<li>Comfort Editor Extension - NVRAM Block Permanent Memory for WWHOBD</li>
			<li>Improvements of &apos;Add ECUC File References&apos; dialog</li>
			<li>Remove module: Improve validation message</li>
			<li>Support Request Package: Include Package Manager Manifest</li>
			<li>Exit comparison mode when module import is finished or no differences are left</li>
			<li>Differences View: Provide information about variation points</li>
			<li>Import of Module configurations containing Variation-Points for Postbuild-Selectable</li>
			<li>Activate CSS support in the GUI</li>
			<li>Add &apos;One ECUC file per module instance&apos; option</li>
			<li>Support queue length in properties for ECU Software Components Editor</li>
			<li>Sort modules for Diff&amp;Merge</li>
			<li>Support Data Mapping of Dynamic Arrays VSA_LINEAR</li>
			<li>Data mapping validation: inner-against-outer-mapping for SenderReceiverToSignalGroupMappings</li>
			<li>Add data mapping validation for multi-mapped signals</li>
			<li>Data Mapping Assistant: Open With Page Depending on Direction</li>
			<li>Support TriggerToSignalMappings With Transformer</li>
			<li>Find View: &apos;IsNotChangeable&apos; query for modules and container</li>
			<li>LdCOM/COM column for Data Mapping table</li>
			<li>Use NvMBlockUseSetRamBlockStatus parameter in DEM comfort view/ configuration wizard</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>TaskMappingEditor: sometimes missing Position column</li>
			<li>Comfort View &apos;Default Error Tracer&apos; is missing the module E2eXf and is not in sync with model</li>
			<li>Generation Result View stays empty</li>
			<li>NullPointerException in Diagnostic Event Data Comfort Editor</li>
			<li>Connect correct Ports for DiagnosticServiceDataMappings with IOControl</li>
			<li>FileSupervision CPU load issue on Windows10</li>
			<li>TaskMappingGrid Does not Scroll to Mapped Runnable</li>
			<li>No error reported when two variants have the same criterion-value-pairs</li>
			<li>Comparison Mode is not left when project is closed</li>
			<li>Differences View: Button for use Base is missing in three way merge</li>
			<li>Persist Size of Reference Selection Dialog</li>
			<li>Diff &amp; Merge assistant: Mapped CEs are not considered when evaluating added and removed module configurations</li>
			<li>Scroll position always jumps to right edge of the column</li>
			<li>Tooltip window is not refreshed after executing solving action</li>
			<li>Validate path in &apos;Remove Module&apos; assistant</li>
			<li>Automation Interface: faulty/missing exceptions during generator selection for module generation</li>
			<li>UnhandledEventLoopException when Scriptlocation given in the DPA file could not be located</li>
			<li>CFG5 does not finish closing, when BswM Editor is open</li>
			<li>Com PDU view empty in PDUs editor</li>
			<li>Basic Editor and Diagnostic Domain Editors are not available in ASR4 diagnostics in MSR3 use case</li>
			<li>Properties View doesn&apos;t show content for differences related to system description differences</li>
			<li>Report unparseable schema versions as warning </li>
			<li>NullPointerException at Default Error Tracer Editor</li>
			<li>Not all editors are closed if modules are removed</li>
			<li>Parameters not using horizontal space in IOHwAb editor</li>
			<li>Automation Interface: Dynamic targets are not resolved in IntelliJ IDEA 2019.2</li>
			<li>Performance Improvement of EVS model initialization</li>
			<li>Task Mapping grids of ECU SWC Editor do not show any error decoration icons</li>
			<li>Properties View: &apos;Preconfigured&apos; and &apos;Recommended&apos; values are not shown for container</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.20 SP4">DaVinci Configurator Pro 5.20 (SP4)</a>
		</h2>	
		<h3>Fixed Issues</h3>
		<ul>
			<li>Derive non gateway routed rx J1939DcmIPdu should check NodeId</li>
			<li>ConnectorValidation: Only One RTE51031 Shown for Each Operation</li>
			<li>Incorrect AccessPermission in Workflow Update API</li>
			<li>Grid filters incorrectly</li>
			<li>E2EProtectionValidator Exception Removing Multiple Receivers</li>
			<li>Exception &apos;Unhandled event loop exception: String index out of range: -1&apos; occurs</li>
			<li>Application Ports Grid Errors For Invalid Connectors</li>
			<li>SwcGeneration: Add API to create DataReceiveErrorEvents</li>
			<li>Unnessary PduR API forwarding routing paths created for ContainedIPdus</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>		
		<!-- #################### -->
		<h2>
			<a name="5.20 SP3">DaVinci Configurator Pro 5.20 (SP3)</a>
		</h2>	
		<h3>Fixed Issues</h3>
		<ul>
			<li>SoftDerived parameter was not created correctly during InputFileUpate</li>
			<li>Updating multi-driver mapping works incorrectly</li>
			<li>Extend RTE59002 validator to consider DiagnosticDataElement of DiagnosticServiceSwMappings</li>
			<li>J1939DcmDiagnosticMessageSupport is not derived when J1939DcmIPdu.Length is 0</li>
			<li>UnhandledEventLoopException when Scriptlocation given in the DPA file could not be located</li>
			<li>Multi selection context menu in &apos;Unresolved References&apos; view</li>
			<li>Unhandled Event Loop Exception in Generation Dialogue</li>
			<li>Allow Usage of ModeRequestPorts in Expressions of Different Partitions</li>
			<li>Adapt UINT8_DYN mapping rules to ASR 4.4 description</li>
			<li>Provide Drag&apos;n&apos;Drop inside Items List of Action List Control</li>
			<li>RuntimeSystemGeneralEditor: Support A2L Version 1.7.1</li>
			<li>Unhandled Event Loop Exception when selecting entries in &apos;Unresolved References&apos; view</li>
			<li>SdMulticastEventSoConRef is not properly derived</li>
			<li>NullPointerException thrown during mapping if J1939TpConfig has no cluster</li>
			<li>Variance Details View without content/not updated</li>
			<li>FrIfUserTxUL for gateway routed contained PDUs in ContainerIPdus is not derived to PDUR</li>
			<li>Comfort View &quot;Default Error Tracer&quot; is missing the module E2eXf and is not in sync with model</li>
			<li>Generation Result View stays empty</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>		
		<!-- #################### -->
		<h2>
			<a name="5.20 SP2">DaVinci Configurator Pro 5.20 (SP2)</a>
		</h2>	
		<h3>Fixed Issues</h3>
		<ul>
			<li>Do not restore SoftDerived ReferenceParameters during update</li>
			<li>Links into the BSW Mode Management editor can&apos;t be resolved</li>
			<li>Code Generation: ExtGenSteps dont accept round brackets in path</li>
			<li>FileSupervision CPU load issue on Windows10</li>
			<li>Watchdogs Editor: Jump from Watchdog in Grid to Tree not possible</li>
			<li>Automation Interface: Connecting two Ports of the same Component not possible</li>
			<li>Automation Interface: Dynamic targets are not resolved in IntelliJ IDEA 2019.2</li>
			<li>DataMappingValidation Inner against Outer Mapping for SenderReceiverToSignalGroupMappings</li>
			<li>Context menu entry &apos;Route PDU&apos; is missing in the PDU Graph</li>
			<li>Incorrect variant condition on deadline monitoring pdu group references</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.20 SP1">DaVinci Configurator Pro 5.20 (SP1)</a>
		</h2>	
		<h3>Extensions</h3>
		<h4>Communication Domain</h4>
		<ul>
			<li>New assistant for the configuration of the J1939RequestableFlag</li>
		</ul>			
		<h3>Fixed Issues</h3>
		<ul>
			<li>Exception when removing Mode Request Ports in Mode Management Editor</li>
			<li>DaVinci Configurator does not finish closing, when BswM Editor is open</li>
			<li>Derive parameters J1939Tp[Tx|Rx]RetrySupport, J1939RmSupportRequest2 and J1939NmChannelUsesAddressArbitration</li>
			<li>Error icons are not shown in PDUs Form</li>
			<li>Input Files Assistant: &apos;Select all&apos; button is active even if no input file is available</li>
			<li>Module Import: Decline import of not matching variant data</li>
			<li>Basic Editor and Diagnostic Domain Editors are not available in ASR4 diagnostics in MSR3 use case</li>
			<li>Only one PduPort is considered for Payload of SecuredIPdu</li>
			<li>Properties View doesn&apos;t show content for differences related to system description differences</li>
			<li>Flattener Does not Correctly Handle SubElementMappings for Arrays</li>
			<li>Input file paths are stored as absolute path after project update</li>
			<li>BswM Tooltip is not shown on Expressions</li>
			<li>Support QueueLength in Properties for ECU SWC Editor</li>
			<li>Differences View: CE is added when user clicks ignore in the toolbar of the Differences view</li>
			<li>Data Mapping Grids Selection When Change Grouping Mode</li>
			<li>NullPointerException during project load</li>
			<li>Derivation of J1939DcmDiagnosticMessageSupport should exclude gateway routed DcmIPdu</li>
			<li>Project Status Information Dialog Remains Open After Update</li>
			<li>Editors which does not support variance might be enabled in variant projects</li>
			<li>Rework of Hyperlinks in ECU Software Components Editor </li>
			<li>Internal error occurred during &apos;Update Unresolved Reference Information for status line&apos;</li>
			<li>Project state information for pending update shows different content</li>
			<li>Module Configuration Import Dialog doesn&apos;t allow to change import mode</li>
			<li>Connector Validation Exception For Missing Port</li>
			<li>Missing validation icons in the Memory Blocks Grid</li>
			<li>Support sub-function 0x55 of ReadDTCInformation</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.20">DaVinci Configurator Pro 5.20</a>
		</h2>
		<h3>License Handling</h3>
		<ul>
			<li>DaVinci Configurator SIP.Diag: Selection of Project Standard Configuration files in Input Files Assistant enabled now</li>
		</ul>			
		<h3>Extensions</h3>
		<h4>Automation Interface Extensions</h4>
		<ul>
			<li>Extension for Unmapping Component Ports</li>
			<li>Extension for Unmapping Signals and Communication Elements</li>
			<li>Enhance Simple Data Mapping API for Complex Mappings</li>
			<li>Implement Missing Getters for Runtime System Domain Model</li>
			<li>Support Creation of Delegation Ports at the Component Port Selection API</li>
			<li>Improve Performance of Origin Context API</li>
			<li>Extend Domain Com automation interface by FullCan use case</li>
			<li>Support Multi-Partition Auto Configuration</li>
			<li>Support new keywords for Search-API: Query and Count</li>
			<li>Offer ParameterDataPrototypes for DataMapping</li>
			<li>Extend generation API to support Generation Execution Report</li>
			<li>Select Origin Component Port API: Offer Origin Port for Naming Declaration of Delegation Port</li>
			<li>Extend mdfModel API with referable + relative path</li>
			<li>Create Custom Report</li>
			<li>Support of project merge via automation scripts</li>
		</ul>	
		<h4>BSW Management Editor Improvement</h4>
		<ul>
			<li>No more restrictions in case of multi-core systems</li>
			<li>Show/hide groups in the tree</li>
			<li>Filtering to display only elements related to a selected element</li>
			<li>Improved names of generated elements</li>
			<li>Special dialog to add, remove and order actions within an action list</li>
			<li>Ordering of an action list by drag&amp;drop</li>
			<li>Neutral project update for legacy diagnostic files</li>
		</ul>
		<h4>New view in PDUs Editor: PDU Diagram</h4>
		<ul>
			<li>Graphical display of PDU relations</li>
			<li>Limitation: not yet all PDU types displayed</li>
		</ul>
		<h4>BaseEcuC Generator Features</h4>
		<ul>
			<li>Support of IpduMContainerQueueSize (RFC 80408)</li>
			<li>Direct mapping between PNC and ComM channel (RFC 80169)</li>
			<li>Derivation of Bundle PDUs on Client Side for Dynamic Sockets</li>
			<li>Support of DEXT &apos;recoverableInSameOperationCycle&apos; flag</li>
			<li>Import DID Size into CFG5</li>
			<li>Derive J1939TpRxNSdu and J1939TpTxNSdu from j1939TpPg.sdu</li>
			<li>Derive the LIN Wakeup Source only as Soft-Derived parameter</li>
		</ul>
		<h4>Project Update and Multi-User Features</h4>
		<ul>
			<li>Neutral project update for legacy diagnostic files</li>
			<li>Harmonized UUID handling during  ARXML import and project merge</li>
			<li>Support of PB-S projects with DEXT input files</li>
			<li>Input Files Editor: Display detailed validation info</li>
			<li>Command line: indirect file for handling large number of input files</li>
		</ul>
		<h4>Miscellaneous Tool Features</h4>
		<ul>
			<li>Improved display of VASE script logs</li>
			<li>Support of component-specific scripts also in the new SIP structure</li>
			<li>Tresos proxy generator supports now two separate tresos installations</li>
			<li>Grid controls: Filtering and sorting by summary column</li>
			<li>Restore expansion and selection state of trees and grids when switching the displayed PB-S variant</li>
			<li>Grid controls: Filtering by multiple columns</li>
			<li>Find View: Check for deviation from recommended value</li>
			<li>ECU Software Components Editor: Signal-centric view for data mapping</li>
			<li>GenDevKit: Support QueueLength for ReceiverComSpec</li>
			<li>Support SubElementMappings for Bitfield Arrays in GUI and Validations</li>
			<li>Connecting Port Prototypes: Improve Matching Score of Origin Context</li>
			<li>Introduce Completed Flag For Origin Ports</li>
			<li>Show full ASR Path in ReferenceSelection Dialog</li>
			<li>Full Sub Element Mapping Support</li>
			<li>Allow &apos;Edit Variance&apos; for derived containers</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Selective merge of platform function: Parent is not created when only child is assigned</li>
			<li>ResolveDeleted difference action processes also added and modified if they are selected</li>
			<li>Different Dcf load behavior than DaVinci Developer</li>
			<li>Wrong derivation of Sporadic Lin Frames </li>
			<li>Data Mapping Assistant shows signals of other variants</li>
			<li>Input File Assistant: File replace does not work as expected</li>
			<li>Custom Workflow Steps cannot be saved and loaded</li>
			<li>Data Constraints not validated correctly if type emitter is not &apos;RTE&apos;</li>
			<li>Data Mapping Assistant does not filter non-zero-length signals for trigger </li>
			<li>Close button gets truncated in the Generate dialog</li>
			<li>No error message shown when trying to open a dual target project in a SIP without VTT license</li>
			<li>Edit Variance Dialog: Criterion value can be set to a non-integer value</li>
			<li>Unresolved References View updates very often in background</li>
			<li>Multiple IssmChannels stay in one column in the IssmIss Grid</li>
			<li>DvCfgCmd closes unexpectly when the path to the executable is too long</li>
			<li>Memory Mapping Assistant does not show any memory object</li>
			<li>Deleted soft-derived  reference parameter exists after update</li>
			<li>Data mapping between RX signal of NmPdu and DataElement/Operation shall be forbidden</li>
			<li>Prevent the execution of the Update Workflow for variant projects without input files</li>
			<li>Find View: Context menu action &apos;Reset to recommended value&apos; should always be available on multi selection</li>
			<li>Update Workflow aborts with error in Diagnostic-Only update mode only with diagnostic input files</li>
			<li>Script Task View not grayed out exec option</li>
			<li>Differences in Differences View are not sorted</li>
			<li>Properties View: Missing entries in Definition tab </li>
			<li>FileChangeListener does not recognize InputFile changes in InputFilesStateService with relativ paths</li>
			<li>Diff&Merge: Filters are not considered when starting ComparisonMode from Cfg5</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.19 SP6">DaVinci Configurator Pro 5.19 (SP6)</a>
		</h2>
		<h3>Miscellaneous Tool Features</h3>
		<ul>
			<li>TopDownService Config - Improve Evaluation of Support Dirty Flag Value at NvBlockDescriptors</li>
			<li>Compare and Merge: Support base container for renamed containers</li>
			<li>Change the way of merging the J1939NmNode and its NodeID</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Incorrect derivation of ComMUsers for PNCs</li>
			<li>ConnectorValidation: Only one RTE51031 shown for each Operation</li>
			<li>StringIndexOutOfBoundsException by basevalidator StringParameterLength</li>
			<li>Flattener: Missing Connectors in case of Multi Instantiation</li>
			<li>NullPointerException if CommunicationDirection is missing in the FramePort</li>
			<li>Only one ComMUser is created for all PncMappings</li>
			<li>Exception in Application Components part with Variant Data Mappings</li>
			<li>Exception when connecting Ports</li>
			<li>Problems with TempFile creation and deletion</li>
			<li>NVM Need Priority Selection</li>
			<li>Exporter modelTreeClosure doesn&apos;t export referenced objects as described in the headline</li>
			<li>Missing modules in updater list</li>
			<li>E2EProtectionValidator Exception when removing Multiple Receivers</li>
			<li>BswM - Provide Drag&apos;n&apos;Drop inside Items List of Action List Control</li>
			<li>Show OsApplications in Component Prototype Properties</li>
			<li>Execution Order Constraint cannot be applied</li>
			<li>Application Ports grid errors for invalid Connectors</li>
			<li>Derived the J1939TpRx- and -TxProtocolType</li>
			<li>Incorrect creation of DataType mappings for IOControl Ports</li>
			<li>For EIRA Rx ComMSignals no ComMPncComSignalChannelRef is needed</li>
			<li>ConnectorValidationRule: Implement Timeout</li>
			<li>ComSignalGroup parameters are not derived for bi-directional ISignals</li>
			<li>BSWM Editor does not show variant BswMActionListItem</li>
			<li>Support changed reference of DiagnosticServiceSwMapping in SwcPortMappingValidator</li>
			<li>Changing number format for parameter has no effect</li>
			<li>Unnessary PduR API forwarding routing paths created for ContainedIPdus</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.19 SP5">DaVinci Configurator Pro 5.19 (SP5)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Updating multi-driver mapping works incorrectly</li>
			<li>Extend RTE59002 validator to consider DiagnosticDataElement of DiagnosticServiceSwMappings</li>
			<li>E2Es out of Sync After Each Project Load</li>
			<li>Automation Interface: Timing problem with model load and model synchronization</li>
			<li>Automation Interface: Connecting Two Ports of the Same Component Not Possible</li>
			<li>Automation Interface: Faulty/missing exceptions during generator selection for module generation</li>
			<li>J1939DcmDiagnosticMessageSupport is not derived when J1939DcmIPdu.Length is 0</li>
			<li>Broken Display in Grid of Infrastructure Subset Assignments</li>
			<li>Allow Usage of ModeRequestPorts in Expressions of Different Partitions</li>
			<li>Input file: File type values are not deterministic</li>
			<li>Make AutomaticRteSetup-ASA deterministic</li>
			<li>Exception when selecting entries in &apos;Unresolved References&apos; view</li>
			<li>SdMulticastEventSoConRef is not properly derived</li>
			<li>NullPointerException thrown during mapping if J1939TpConfig has no cluster</li>
			<li>Variance Details View without content/not updated</li>
			<li>Comfort View &apos;Default Error Tracer&apos; is missing the module E2eXf and is not in sync with model</li>
			<li>Generation Result View stays empty</li>
			<li>E2EProtectionValidator Fails to Synchronize Multiple Receivers Scenarios</li>
			<li>ConnectorValidation: Only One RTE51031 Shown for Each Operation</li>
			<li>Deleted SoftDerived ReferenceParameter restored during update</li>
			<li>Exception when Scriptlocation given in the DPA file could not be located</li>
			<li>Exception &apos;Unhandled event loop exception: String index out of range: -1&apos; occurs</li>
			<li>Automation Interface: Dynamic targets are not resolved in IntelliJ IDEA 2019.2</li>
			<li>FrIfUserTxUL for gateway routed contained PDUs in ContainerIPdus is not derived to PDUR</li>
			<li>Support relative paths for &apos;Script File&apos; in IFA</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>			
		<!-- #################### -->
		<h2>
			<a name="5.19 SP4">DaVinci Configurator Pro 5.19 (SP4)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Unresolved References View Updates Very Often in Background</li>
			<li>FileSupervision CPU load issue on Windows10</li>
			<li>Performance problem on EVS model initialization</li>
			<li>EVS information is not loaded after VASE execution at file preprocessing</li>
			<li>BSW Management Editor: Internal Error about Multiple Reference Targets</li>
			<li>Provide an API to check for existing input files</li>
			<li>Support TriggerToSignalMappings With Transformer</li>
			<li>Base Validation of Arrays Does not Check Array Size Correctly</li>
			<li>Automation Interface: Timing problem with model load and model synchronization</li>
			<li>Connect correct Ports for DiagnosticServiceDataMappings with IOControl</li>
			<li>Mutliple messages in StatusLine for same topic</li>
			<li>Wrong API forwarding routing path created for DcmIPdu</li>
			<li>Difference Details View shows empty content</li>
			<li>AutomationInterface: Connecting Two Ports of the Same Component Not Possible</li>
			<li>DataMappingValidation Inner Against Outer Mapping for SenderReceiverToSignalGroupMappings</li>
			<li>Disable Meta Data Handling for Socket Connection PDUs</li>
			<li>Input file Assistant: File type values are not not deterministic</li>
			<li>Task Mapping grids of ECU SWC Editor do not show any error decoration icons</li>
			<li>Commandline Tooling uses wrong Garbage Collector</li>
			<li>E2EProtectionValidator Fails to Synchronize Multiple Receivers Scenarios</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>			
		<!-- #################### -->
		<h2>
			<a name="5.19 SP3">DaVinci Configurator Pro 5.19 (SP3)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Derive Routed J1939DcmIPdus </li>
			<li>Basic Editor and Diagnostic Domain Editors are not available in ASR4 diagnostics in MSR3 use case</li>
			<li>Only one PduPort is considered for Payload of SecuredIPdu</li>
			<li>Flattener Does not Correctly Handle SubElementMappings for Arrays</li>
			<li>Top-Down Service Configuration Shall See Port Interfaces Without isService Flag as Application Port Interfaces</li>
			<li>Input file paths are stored as absolute path after project update</li>
			<li>SIP Mismatch dialog does not scale correctly</li>
			<li>DvCfgCmd crashes when the path to the executable is too long</li>
			<li>Memory Domain: Null-Pointer-Exception in CreateHwAbBlockOperation</li>
			<li>Differences View: CE is added when user clicks ignore in the toolbar of the Differences view</li>
			<li>Derivation of J1939DcmDiagnosticMessageSupport should exclude gateway routed DcmIPdu</li>
			<li>Derive parameters J1939Tp[Tx|Rx]RetrySupport, J1939RmSupportRequest2 and J1939NmChannelUsesAddressArbitration</li>
			<li>Rework multi-threading locking of ProjectUpdateStateService</li>
			<li>Removed Soft-Derived container not recognized during update</li>
			<li>Parameter which is not instantiated doesnt show createable in properties view</li>
			<li>Internal error occurred during &apos;Update Unresolved Reference Information for status line&apos;</li>
			<li>Rework warning message of &apos;No installed VttCmdTool found.&apos;</li>
			<li>Derive requestable flag from j1939TpPg.requestable with tpSdu reference fails</li>
			<li>Project state information for pending update shows different content</li>
			<li>Missing validation icons in the Memory Blocks Grid</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.19 SP2">DaVinci Configurator Pro 5.19 (SP2)</a>
		</h2>
		<h3>License Handling</h3>
		<ul>
			<li>DaVinci Configurator SIP.Diag: Selection of Project Standard Configuration files in Input Files Assistant enabled now</li>
		</ul>			
		<h3>Fixed Issues</h3>
		<ul>
			<li>Some Comfort Editors do not work in postbuild-selectable configurations</li>
			<li>Generation is not possible without internal generators</li>
			<li>Switch -additionalSipPath does not select correct SIP License anymore</li>
			<li>Wrong message about closed editors on module deactivation</li>
			<li>Allow &apos;Edit Variance&apos; for derived containers</li>
			<li>Input Files Assistant: File replace does not work as expected</li>
			<li>Data Mapping Assistant does not filter non-zero length signals for trigger </li>
			<li>Application Ports Grid: Add additional column for origin port</li>
			<li>Diff/Merge Improvement for UUID and platform functions</li>
			<li>VTT setting is enabled in New Project Assistant even if VTT is missing</li>
			<li>OS Configuration Editor: remap functions of task throws exception</li>
			<li>Prevent the execution of the update workflow for variant projects without input files</li>
			<li>Custom Workflow Steps cannot be saved and loaded</li>
			<li>Dcf load behavior different to DaVinci Developer</li>
			<li>Data Mapping Assistant shows signals of other variants</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.19 SP1">DaVinci Configurator Pro 5.19 (SP1)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Flattener cannot synchronize &apos;derived&apos; state of inner data mappings</li>
			<li>Connecting port prototypes: improve matching score of origin context</li>
			<li>Origin context: ports connected by an invalid connector should be considered as unconnected</li>
			<li>Error message occurs when clicking on the SWT Template Generation button without having a RTE module</li>
			<li>Causing errors are not reported if file preprocessing fails</li>
			<li>Connector validation checks internal constraints for Application Data Types</li>
			<li>Inital content of the validation summary label is wrong</li>
			<li>Error icon is missing on conditional generation error message</li>
			<li>SwcPortMappingValidator error when removing Dcm</li>
			<li>File preprocessing model: do not add preprocessing script step if no ARXML result will be available</li>
			<li>Unnecessary routing paths for fan-out PDUs</li>
			<li>Report doesn&apos;t contain parameter annotations</li>
			<li>Wrong handling of selective update settings in DPA causes unnecessary file preprocessing</li>
			<li>Derive MessageId of AssignFrameId and UnassignFrameId</li>
			<li>Context menu actions in ComMUsers Editor-Tree fail to delete and create channel references</li>
			<li>Export to CSV in Task Mapping Editor does not keep the correct order of elements</li>
			<li>SecOCSecuredRxPduVerification and SecOCUseAuthDataFreshness are derived from wrong IPduPort</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.19">DaVinci Configurator Pro 5.19</a>
		</h2>
		<h3>NOTE</h3>
		<p>
        Only available as 64-bit executable. Windows 32-bit is no longer supported.
        </p>		
		<h3>Extensions</h3>
		<h4>Unresolved Reference View</h4>
		<ul>
			<li>New view with a list of all unresolved references in the configuration</li>
			<li>Convenient correction of the unresolved references</li>
		</ul>
		<h4>Improvements Diff&amp;Merge</h4>
		<ul>
			<li>Difference View: grouping of differences by modules</li>
			<li>Difference View: improved display of differences in task mapping</li>
			<li>Difference View: Improved filter to hide irrelevant diffs (e.g. AdminData)</li>
			<li>Filter to exclude elements from generic ECUC diff&amp;merge</li>
		</ul>	
		<h4>Automation Interface Extensions</h4>
		<ul>
			<li>Project creation and update for post-build selectable variants</li>
			<li>Selection of use-cases during project setup (to apply use-case preconfiguration)</li>
		</ul>	
		<h4>License Handling</h4>
		<ul>
			<li>DaVinci Configurator SIP.PB: all editors of domains &apos;Communication&apos; and &apos;Diagnostics&apos; now visible</li>
			<li>Special icon and message type for easier detection license violations that are reported by MICROSAR generators</li>
		</ul>	
		<h3>Miscellaneous Tool Features</h3>
		<ul>
			<li>Support for AUTOSAR 4.4</li>
			<li>Support for the component-based SIP/delivery structure</li>			
			<li>Support internal RTE Trigger (Interrunnable Trigger)</li>
			<li>Support new NvRAM Block in �Setup Event Memory Blocks� Editor</li>
			<li>Support for selective update of project</li>
			<li>Provide Global Snapshot Information in &apos;Diagnostic Event Data&apos; Editor</li>
			<li>Support Splitted Secured-IPDU in a Container-PDU</li>
			<li>Import ASR 4.3.0 DEXT J1939 DTC</li>
			<li>Extend the derivation of the CanTp&apos;s addressing formats</li>
			<li>Command Line: Support separate Ecu Instance for each input file</li>
			<li>Visualize acknowlegdements for top level elements in validation view</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Change SIP workflow script location handling and log messages within the Update Workflow</li>
			<li>RT00002 error in E2EProtectionValidator for E2Es without profile</li>
			<li>Extend the validation of UUIDs to support splitted Input files</li>
			<li>Update of validation view is broken</li>
			<li>Module activation rejected</li>
			<li>All ProjectStandardConfiguration files are ignored during the file preprocessing activity</li>
			<li>Correctly extract DataTypeMappingSets and referenced ApplicationDataTypes</li>
			<li>DataMappingValidation Exception For Mappings Without Signal Reference</li>
			<li>ComIPduGroupRef parameter not derived for nested ISignalIPduGroup</li>
			<li>The wrong Ecu Instance is chosen during the migration of the project from 5.16</li>
			<li>Remove mapping rule for SecOCProvideTxTruncatedFreshnessValue</li>
			<li>Derive Sd module only if service discovery is described in the System Extract</li>
			<li>Add parameter to AcceptanceFilter View</li>
			<li>Variant project switches into Update-Pending state after project update and reloading the project</li>
			<li>Adapt derivation of SecOCUseMessageLink</li>
			<li>The merge of J1939NmNode should consider the J1939NmClusters in all NmConfigs</li>
			<li>Some SignalMappings are not derived to ComGwMapping</li>
			<li>Initial configuration is applied to wrong module configuration</li>
			<li>Migration short name used for Ecuc migration is not forwarded to the EcucUpdater</li>
			<li>Get correct ComponentPrototype from DiagnosticServiceDataMappings for Connectors</li>
			<li>Adapt RTE51035 message if compu method cannot be applied</li>
			<li>Diff&amp;Merge: ReadOnly Flags in arxml-Files to import should be ignored</li>
			<li>PB-L update process stops: FibexElementRefs cannot be updated</li>
			<li>Temporary generation files are deleted although the option &apos;Keep temporary generation files&apos; is set</li>
			<li>Differences View: Filter has no effect on executed actions</li>
			<li>Difference Details View doesn&apos;t show values for BASE, MINE and OTHER</li>
			<li>Production Error Handling Editor cannot find the DemDebounceTimeBased Definition</li>
			<li>Consider the cluster for upperlayer Gateway routing target</li>
			<li>Correct channel is not considered for the SecuredIPdu upperlayer check</li>
			<li>Update Workflow gets aborted for vCanCcCdm configurations (CHAdeMO)</li>
			<li>Diff &amp; Merge Assistant - Performance: The evaluation of platform annotated CEs takes too long</li>
			<li>DaVinci Configurator doesn&apos;t respond anymore when creating DaVinci Developer Workspace in Project Settings Editor</li>
			<li>Exception when creating connectors on components</li>
			<li>NullPointerException is shown when loading a project</li>
			<li>Show RTE Validation messages only if RTE is active</li>
			<li>Differences View: &apos;Copy&apos; on non changeable elements returns incomplete AUTOSAR path</li>
			<li>Automatic selection of VTT modules doesn&apos;t work if the user presses &apos;Finish&apos;</li>
			<li>Check for dongle drivers not working with 32 Bit Windows</li>
			<li>RTE59001 is not solving all default actions</li>
			<li>RTE59000 appears always for duplicate fibex elements</li>
			<li>&apos;Synchronize now&apos; fails with Model Modification Exception or other failure</li>
			<li>SwcGeneration AR3: Symbol of runnables is not synchronized</li>
			<li>SwcGeneration: CanEnterExclusiveAreas are not fully synchronized</li>
			<li>Correct DataConstr Validation for type references</li>
			<li>Basic Editor: Multi selection of containers that contain instance references throws an exception</li>
			<li>Support explicit broadcast modelling for TcpIpLocalAddr</li>
			<li>GlobalTimeDomain is not derived if SubDomain contains FupDataIDList</li>
			<li>Parameter FrArTpGeneral/FrArTpHaveLm is not fully removed from mapping</li>
			<li>Validation error PDUR12500 shows up for gateway routed XCP PDUs</li>
			<li>Change the range of UUID validation from among variants to within variant.</li>
			<li>Improve robustness of flex-ray physical channel evaluation</li>
			<li>Exception thrown when deriving of ComGwDestination if there are no Signal oder SignalGroup in the SignalTriggering</li>
			<li>Derive new ASR 4.3 parameters UdpNmImmediateNmCycleTime and UdpNmImmediateNmTransmissions</li>
			<li>Removing a module causes an IllegalArgumentException</li>
			<li>Workflow log files are empty after update</li>
			<li>PostBuild-Selectable: ModelChangeMode wronlgy deletes non-multi-instance-parameters</li>
			<li>Adapt SdClientService mapping rule to support TPS_SYST_02096</li>
			<li>Module Import Merge: Numerical Values (Boolean, Integer, Float) are not resolved correctly</li>
			<li>Change DCF-Load merge behaviour depending on atpSplitable stereotype</li>
			<li>System extractor should filter out unnecessary GlobalTimeDomain elements</li>
			<li>Automation Interface: ArxmlFile loading fails the second time in the same task execution</li>
			<li>Add Information about atpSplitable stereotype to the IClassInfo</li>			
			<li>No stable UUID generate for CanHardwareObject in TX case</li>
			<li>Derive MICROSAR specific enumeration value TcpIpAddressType.TCPIP_IPV4_BROADCAST</li>	
			<li>It is not possible to use AUTOSAR_COMPLEMENTARY_DATA files via command line</li>
			<li>Show Input File name in Load Arxml File Activity</li>
			<li>Validation View: &apos;Link with Editor&apos; issues</li>
			<li>Move up and down buttons of action lists does not consider variants</li>
			<li>Show user-friendly validation messages in Task Mapping Assistant</li>
			<li>Missing &apos;not instantiated&apos;  - Decoration in multi instance parameter control</li>
			<li>Derivation of J1939SharedAddressCluster</li>
			<li>File Preprocessing Activity &apos;Load ARXML file&apos; does not report loading errors to the user</li>
			<li>TaskMappingAssistant does not show any icons</li>
			<li>Wrong validation result id in RtmMeasurementPointService and unsufficient logger outputs</li>
			<li>Do not derive NM module if no BusNm is derived</li>
			<li>Deactivate TaskMappingEditor and TaskMappingAssistant without OS</li>
			<li>Derive SecOCSecuredRxPduVerification</li>
			<li>FatalError during Update due to already removed object</li>
			<li>Do not derive XcpOnEthernetEnabled and XcpOnFlexRayEnabled</li>
			<li>Do not derive TcpIpStaticIpAddressConfig for &apos;ANY&apos; IP addresses and for runtime IP address configurations</li>
			<li>Link-Navigation from TaskMapping Editor doesn&apos;t work for all cases</li>
			<li>Wrong derivation of FrIfUserRxIndicationUL for ContainerIPdus</li>
			<li>The creation of a new NvBlock is not possible when using &apos;Automap Diagnostic Data Objects&apos; function</li>
			<li>RTE59004 Non Variant Validation Results are Shown in Variant Tabs</li>
			<li>Wrong derivation of DcmDsdSubServiceId of DiagnosticReadDTCInformation</li>
			<li>Exception when an ApplicationComponent is renamed in Developer</li>
			<li>Renaming of a container switches a reference value to the wrong variant</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.18 SP7">DaVinci Configurator Pro 5.18 (SP7)</a>
		</h2>
		<h3>Miscellaneous Tool Features</h3>
		<ul>
			<li>TopDownService Config - Improve Evaluation of Support Dirty Flag Value at NvBlockDescriptors</li>
			<li>Compare and Merge: Support base container for renamed containers</li>
			<li>Change the way of merging the J1939NmNode and its NodeID</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Exception when connecting Ports</li>
			<li>ConnectorValidation: Only one RTE51031 shown for each Operation</li>
			<li>Flattener: Missing Connectors in case of Multi Instantiation</li>
			<li>NVM Need Priority Selection</li>
			<li>Exporter modelTreeClosure doesn&apos;t export referenced objects as described in the headline</li>
			<li>E2EProtectionValidator Exception when removing Multiple Receivers</li>
			<li>NullPointerException if J1939TpProtocolType is missing in the J1939DcmIPdu</li>
			<li>Execution Order Constraint cannot be applied</li>
			<li>NullPointerException if CommunicationDirection is missing in the FramePort</li>
			<li>Application Ports grid errors for invalid Connectors</li>
			<li>Derived the J1939TpRx- and -TxProtocolType</li>
			<li>For EIRA Rx ComMSignals no ComMPncComSignalChannelRef is needed</li>
			<li>ConnectorValidationRule: Implement Timeout</li>
			<li>Exception in Application Components part with Variant Data Mappings</li>
			<li>BSWM Editor does not show variant BswMActionListItem</li>
			<li>Changing number format for parameter has no effect</li>	
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.18 SP6">DaVinci Configurator Pro 5.18 (SP6)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Extend RTE59002 validator to consider DiagnosticDataElement of DiagnosticServiceSwMappings</li>
			<li>E2Es out of Sync After Each Project Load</li>
			<li>Automation Interface: Timing problem with model load and model synchronization</li>
			<li>Automation Interface: Connecting Two Ports of the Same Component Not Possible</li>
			<li>Automation Interface: Faulty/missing exceptions during generator selection for module generation</li>
			<li>Make AutomaticRteSetup-ASA deterministic</li>
			<li>Exception &apos;Unhandled event loop exception: String index out of range: -1&apos; occurs</li>
			<li>SdMulticastEventSoConRef is not properly derived</li>
			<li>NullPointerException thrown during mapping if J1939TpConfig has no cluster</li>
			<li>Comfort View &apos;Default Error Tracer&apos; is missing the module E2eXf and is not in sync with model</li>
			<li>Generation Result View stays empty</li>
			<li>E2EProtectionValidator Fails to Synchronize Multiple Receivers Scenarios</li>
			<li>Change SwcPortMappingValidator to show only one SolvingAction</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.18 SP5">DaVinci Configurator Pro 5.18 (SP5)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Basic Editor and Diagnostic Domain Editors are not available in ASR4 diagnostics in MSR3 use case</li>
			<li>Only one PduPort is considered for Payload of SecuredIPdu</li>
			<li>Flattener Does not Correctly Handle SubElementMappings for Arrays</li>
			<li>TopDownServiceConfig: Shall See Port Interfaces Without isService Flag as Application Port Interfaces</li>
			<li>Allow &apos;Edit Variance&apos; for derived containers</li>
			<li>SIP Mismatch dialog does not scale correctly</li>
			<li>Wrong API forwarding routing path created for DcmIPdu</li>
			<li>Derivation of J1939DcmDiagnosticMessageSupport should exclude gateway routed DcmIPdu</li>
			<li>BSW Management Editor: Internal Error about Multiple Reference Targets</li>
			<li>Properties View Definition Tab Missing Entries</li>
			<li>Custom Workflow Step Names must be unique (as External Gen Steps)</li>
			<li>Removed SoftDerived container not recognized during update</li>
			<li>Prevent the execution of the Update Workflow for variant projects without input files</li>
			<li>Parameter which is not instantiated doesnt show createable in properties view</li>
			<li>[Warning] Rework warning message of &apos;No installed VttCmdTool found.&apos;</li>
			<li>Derive requestable flag from j1939TpPg.requestable with tpSdu reference fails</li>
			<li>Connector Validation Exception For Missing Port</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.18 SP4">DaVinci Configurator Pro 5.18 (SP4)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Generation is not possible without internal generators</li>
			<li>Input File Assistant: File replace does not work as expected</li>
			<li>Data Mapping Assistant does not filter non-zero length signals for trigger</li>
			<li>Diff&Merge: Filters are not considered when starting comparison mode from Cfg5</li>
			<li>Deleted soft-derived  reference parameter exists after update</li>
			<li>Custom Workflow Steps cannot be saved and loaded</li>
			<li>Different Dcf load behaviour to DaVinci Developer</li>
			<li>Project is not switched into &apos;Pending Update&apos; state after changing the content of an InputFile</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.18 SP3">DaVinci Configurator Pro 5.18 (SP3)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Flattener cannot synchronize &apos;derived&apos; state of inner data mappings</li>
			<li>Update fails if VASE-script does not create output file</li>
			<li>No error message shown when trying to open a dual target project in a SIP without VTT license</li>
			<li>PostBuildLoadable: Parameter is not deleteable even if ParentContainer was created in PostBuild-ConfigurationPhase</li>
			<li>Origin context: ports connected by an invalid connector should be considered as unconnected</li>
			<li>System Description Sync fails for arrays with data type reference</li>
			<li>Origin context predicate of FlatExtractPort does not support all component ports</li>
			<li>FullCan mailbox is created for the wrong controller</li>
			<li>Wrong derivation of DcmDsdSubServiceId of DiagnosticReadDTCInformation</li>
			<li>Calling ScriptTask twice with different arguments is not possible</li>
			<li>Not all ValidationResults will be shown in RTE59002</li>
			<li>Change mapping rule for CanNmMsgCycleOffset</li>
			<li>Update Workflow aborts with error in Diagnostic-Only update mode only with diagnostic input files</li>
			<li>Derive MessageId of AssignFrameId and UnassignFrameId</li>
			<li>Export to CSV in Task Mapping Editor does not keep the correct order of elements</li>
			<li>DynamicMissingMethodException throws AccessControlException in script context</li>
			
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.18 SP2">DaVinci Configurator Pro 5.18 (SP2)</a>
		</h2>
		<h3>Miscellaneous Tool Features</h3>
		<ul>
			<li>Visualize Acknowlegdements for top level elements in validation view</li>
			<li>CommandLine: Selction of separate Ecu Instance for each input file</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Change SIP workflow script location handling and log messages within the Update Workflow</li>
			<li>RT00002 error in E2EProtectionValidator for E2Es without profile</li>
			<li>Extend the validation of UUIDs to support splitted Input files</li>
			<li>Update of validation view is broken</li>
			<li>Module activation rejected</li>
			<li>All ProjectStandardConfiguration files are ignored during the file preprocessing activity</li>
			<li>Correctly extract DataTypeMappingSets and referenced ApplicationDataTypes</li>
			<li>DataMappingValidation Exception For Mappings Without Signal Reference</li>
			<li>ComIPduGroupRef parameter not derived for nested ISignalIPduGroup</li>
			<li>The wrong Ecu Instance is chosen during the migration of the project from 5.16</li>
			<li>Remove mapping rule for SecOCProvideTxTruncatedFreshnessValue</li>
			<li>Derive Sd module only if service discovery is described in the System Extract</li>
			<li>Add parameter to AcceptanceFilter View</li>
			<li>Variant project switches into Update-Pending state after project update and reloading the project</li>
			<li>Adapt derivation of SecOCUseMessageLink</li>
			<li>The merge of J1939NmNode should consider the J1939NmClusters in all NmConfigs</li>
			<li>Some SignalMappings are not derived to ComGwMapping</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.18 SP1">DaVinci Configurator Pro 5.18 (SP1)</a>
		</h2>
		<h3>Miscellaneous Tool Features</h3>
		<ul>
			<li>Support explicit broadcast modeling for TcpIpLocalAddr</li>
			<li>Change DCF load/merge behavior depending on atpSplitable stereotype</li>
			<li>Update workflow: Keep UUIDs stable in case of variant projects</li>
			<li>Add option to choose use case values when migrating an &apos;old&apos; project to &apos;new&apos; SIP</li>
			<li>Derive new ASR 4.3 parameters UdpNmImmediateNmCycleTime and UdpNmImmediateNmTransmissions</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Initial configuration is applied to wrong module configuration</li>
			<li>Migration short name used for Ecuc migration is not forwarded to the EcucUpdater</li>
			<li>Get correct ComponentPrototype from DiagnosticServiceDataMappings for Connectors</li>
			<li>Adapt RTE51035 message if compu method cannot be applied</li>
			<li>Diff&amp;Merge: ReadOnly Flags in arxml-Files to import should be ignored</li>
			<li>PB-L update process stops: FibexElementRefs cannot be updated</li>
			<li>Temporary generation files are deleted although the option &apos;Keep temporary generation files&apos; is set</li>
			<li>Differences View: Filter has no effect on executed actions</li>
			<li>Difference Details View doesn&apos;t show values for BASE, MINE and OTHER</li>
			<li>Production Error Handling Editor cannot find the DemDebounceTimeBased Definition</li>
			<li>Consider the cluster for upperlayer Gateway routing target</li>
			<li>Correct channel is not considered for the SecuredIPdu upperlayer check</li>
			<li>Update Workflow gets aborted for vCanCcCdm configurations (CHAdeMO)</li>
			<li>Diff &amp; Merge Assistant - Performance: The evaluation of platform annotated CEs takes too long</li>
			<li>DaVinci Configurator doesn&apos;t response anymore when creating DaVinci Developer Workspace in Project Settings Editor</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.18">DaVinci Configurator Pro 5.18</a>
		</h2>
		<h3>Extensions</h3>
		<h4>Improvements in BaseEcuC Workflow</h4>
		<ul>
			<li>Selective update of diagnostic module configurations (only in combination with CDD or PDX files)</li>
			<li>System Description modification with AutomationInterface during project update</li>
		</ul>
		<h4>Improvements in Identity Handling</h4>
		<ul>
			<li>Introduction of Variance Details View for a clearer overview of the configuration in different identities</li>
		</ul>
		<h3>Miscellaneous Tool Features</h3>
		<ul>
			<li>Improved reporting function</li>
			<li>Extension and Improvement of Diff&amp;Merge function</li>
			<li>Open the TechRef folder from the Help menu</li>
			<li>Display OriginContext in Properties and Overview tables in SWC Design</li>
			<li>Use the OriginContext as additional mapping criterion in Port mapping</li>
			<li>Derive SecOC-Parameter acc. to AR4.3.1</li>
			<li>Derive the SecOC PDU structure for a separated Authentication PDU</li>
			<li>Extend mapping of diagnostic data elements to NvBlocks</li>
			<li>Support ASR 4.3 System Extract requirements for LdCom</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Change the multiplicity of J1939RmMultiplexedPduRef in BaseEcuC base on BSWMD</li>
			<li>Duplicate keybinding for &apos;+&apos; in GCE tree may create unwanted elements</li>
			<li>Number format problem when duplicating entries in a grid</li>
			<li>Project Merge Assistant: CFG5 Diff &amp; Merge Modules existing in Base are marked as not existing in Base</li>
			<li>Project Settings Edtior should validate empty working directory for external generation Steps</li>
			<li>Derive DcmDspDataType as Array datatype for Arrays with variable size</li>
			<li>Project Merge Assistant: &apos;Dpa file locked&apos; error only occurs once</li>
			<li>Duplication of many elements takes forever</li>
			<li>Some parameters of ComSignal are missing after update with a new input file.</li>
			<li>Change derivation for ComMChannelRef in DcmDslConnections for DoIp</li>
			<li>Keyboard navigation leads to NPE</li>
			<li>Show RTE Validation messages only if RTE is active</li>
			<li>Duplication of empty SDG-Nodes on every project-load-save loop leeds to huge files</li>
			<li>Deactivate HW or VTT module partners when the user activates/deactivates modules</li>
			<li>Reference to ImplementationDataType cannot be resolved.</li>
			<li>InputFileType is not correctly detected if project is setup with an older version</li>
			<li>FrNmPnEraCalcEnabled is derived although PncMappingType does not exist</li>
			<li>Input File Assistant: ECU Instance can not be selected if the FileType is changed.</li>
			<li>SoAdTxIfTriggerTransmit should only be derived for IF PDUs</li>
			<li>Connector validation quits with exception</li>
			<li>Task Mapping Editor: Trigger Details disappear while scrolling</li>
			<li>Memory Block Editor not updated when block is assigned to partition</li>
			<li>Possible NPE in SwcPortMappingValidator for not accepted DiagnosticServiceSwMappings</li>	
			<li>Not possible to delete variant expression argument in BSW management editor</li>
			<li>EcucUpdater: UUID was changed during update without reason</li>
			<li>IllegalStateException in the Differences View after Arxml-Import</li>
			<li>Cell tooltip prevents the user from opening the context menu / doubleclicking</li>
			<li>Deleted SoftDerived Container recovered during DBUpdate</li>
			<li>No TcpIpIpFramePrioDefault parameter if EthernetCommunicationController is used in multiple VLANs</li>
			<li>Derive DemDidDataClassRefs and DemDidClassRefs in the correct order</li>	
			<li>Support 4.3.1 MAC/PHY configuration as additional SysEx for existing BSW implementation</li>
			<li>Project Settings Editor - NullPointerException when removing (not existing) external ECUC file</li>
			<li>Differences View: &apos;Copy&apos; on non changeable elements returns incomplete AUTOSAR path</li>
			<li>Derive J1939Requestable from CanFrameTriggering</li>
			<li>BSW Management Editor deletes auto configuration conditions</li>
			<li>EcucUpdater ignores modules with same MSN</li>
			<li>Derive parameter CanIfTxPduCanIdMask as soft-derived</li>	
			<li>Module configuration is deleted  at project save when a second MC with the same ShortName was added and removed by undo</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.17 SP10">DaVinci Configurator Pro 5.17 (SP10)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Exception When Connecting Ports</li>
			<li>ConnectorValidation: Only One RTE51031 Shown for Each Operation</li>
			<li>Extend RTE59002 validator to consider DiagnosticDataElement of DiagnosticServiceSwMappings</li>
			<li>E2EProtectionValidator Exception Removing Multiple Receivers</li>
			<li>Make AutomaticRteSetup-ASA deterministic</li>
			<li>E2Es out of Sync After Each Project Load</li>
			<li>SdMulticastEventSoConRef is not properly derived</li>
			<li>Change NamingRule for DcmDspVehInfoData Container</li>
			<li>Change check for SubDomains within a GlobalTimeDomain</li>
			<li>NullPointerException thrown during mapping if J1939TpConfig has no cluster</li>
			<li>FrIfUserTxUL for gateway routed contained PDUs in ContainerIPdus is not derived to PDUR</li>
			<li>Comfort View &apos;Default Error Tracer&apos; is missing the module E2eXf and is not in sync with model</li>
			<li>BSWM Editor does not show variant BswMActionListItem</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>	
		<!-- #################### -->
		<h2>
			<a name="5.17 SP9">DaVinci Configurator Pro 5.17 (SP9)</a>
		</h2>
		<h3>Miscellaneous Tool Features</h3>
		<ul>
			<li>Improved RTE51029 Validation Message</li>
			<li>Rework warning message of &apos;No installed VttCmdTool found.&apos;</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Base Validation of arrays does not check array size correctly</li>
			<li>Generation Result View stays empty</li>
			<li>Difference Details View shows empty content</li>
			<li>Derivation of J1939DcmDiagnosticMessageSupport should exclude gateway routed DcmIPdu</li>
			<li>Connector Validation Exception For Missing Port</li>
			<li>Automation Interface: Connecting two Ports of the same Component is not possible</li>
			<li>E2EProtectionValidator fails to synchronize multiple Receivers scenarios</li>
			<li>DataMappingValidation Inner Against Outer Mapping for SenderReceiverToSignalGroupMappings</li>
			<li>Automation Interface: Provide an API to check for existing input files</li>
			<li>Disable Meta Data Handling for Socket Connection PDUs</li>
			<li>BSW Management Editor: Internal Error about Multiple Reference Targets</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>	
		<!-- #################### -->
		<h2>
			<a name="5.17 SP8">DaVinci Configurator Pro 5.17 (SP8)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Custom Workflow Step Names must be unique (as External Gen Steps)</li>
			<li>Basic Editor and Diagnostic Domain Editors are not available in ASR4 diagnostics in MSR3 use case</li>
			<li>Only one PduPort is considered for Payload of SecuredIPdu</li>
			<li>Prevent the execution of the Update Workflow for variant projects without input files</li>
			<li>InputFileAssistant shows state description even if it is deleted</li>
			<li>Flattener Does not Correctly Handle SubElementMappings for Arrays</li>
			<li>TopDownServiceConfiguration Shall See Port Interfaces Without isService Flag as Application Port Interfaces</li>
			<li>Allow &apos;Edit Variance&apos; for derived containers</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>	
		<!-- #################### -->
		<h2>
			<a name="5.17 SP7">DaVinci Configurator Pro 5.17 (SP7)</a>
		</h2>
		<h3>Licensing</h3>
		<ul>
			<li>SIP.Diag tool license: Selection of Project Standard Configuration files in the Input File Assistant enabled now</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>VttSynchronizer: ModelCeAlreadyRemovedException if a element has already been removed</li>
			<li>Deleted soft-derived reference parameter exists after update</li>
			<li>Input File Assistant: File replace does not work as expected</li>
			<li>Data mapping validation does not fully validate child mappings</li>
			<li>Data Mapping Assistant does not filter non-zero length signals for trigger</li>
			<li>NullPointerException when loading project with non existing VTT tooling path</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.17 SP6">DaVinci Configurator Pro 5.17 (SP6)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>No error message shown when trying to open a dual target project in a SIP without VTT license</li>
			<li>Improve ImplicitMapping performance for PlatformDevelopmentFunction </li>
			<li>PostBuildLoadable: Parameter is not deleteable even ParentContainer was created in PostBuild-ConfigurationPhase</li>
			<li>Module activation rejected</li>
			<li>Allow Import of AR3 ProjectStandardConfiguration Files</li>
			<li>DataMappingValidation Exception For Mappings Without Signal Reference</li>
			<li>ComIPduGroupRef parameter not derived for nested ISignalIPduGroup</li>
			<li>Wrong derivation of FrIfUserRxIndicationUL for ContainerIPdus</li>
			<li>FullCan mailbox is created for the wrong controller</li>
			<li>Not all ValidationResults will be shown in RTE59002</li>
			<li>Change mapping rule for CanNmMsgCycleOffset</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.17 SP5">DaVinci Configurator Pro 5.17 (SP5)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Get correct ComponentPrototype from DiagnosticServiceDataMappings for Connectors</li>
			<li>Adapt RTE51035 message if compu method cannot be applied</li>
			<li>Correct DataConstr Validation for type references</li>
			<li>Basic Editor: Multi selection of containers that contain instance references throws an exception</li>
			<li>RT00002 error in E2EProtectionValidator for E2Es without profile</li>
			<li>Support explicit broadcast modelling for TcpIpLocalAddr</li>
			<li>GlobalTimeDomain is not derived if SubDomain contains FupDataIDList</li>
			<li>Parameter FrArTpGeneral/FrArTpHaveLm is not fully removed from mapping</li>
			<li>Validation error PDUR12500 shows up for gateway routed XCP PDUs</li>
			<li>Variant project switches into Update-Pending state after project update and reloading the project</li>
			<li>Change the range of UUID validation from among variants to within variant.</li>
			<li>Temporary generation files are deleted, although the option keep temporary generation files is set</li>
			<li>Correct channel is not considered for the SecuredIPdu UpperLayer check</li>
			<li>Improve robustness of flex-ray physical channel evaluation</li>
			<li>Exception thrown when deriving of ComGwDestination if there are no Signal oder SignalGroup in the SignalTriggering</li>
			<li>The wrong Ecu Instance is chosen during the migration of the project from 5.16</li>
			<li>Derive new ASR 4.3 parameters UdpNmImmediateNmCycleTime and UdpNmImmediateNmTransmissions</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.17 SP4">DaVinci Configurator Pro 5.17 (SP4)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Removing a module causes an IllegalArgumentException</li>
			<li>Workflow log files are empty after update</li>
			<li>PostBuild-Selectable: ModelChangeMode wronlgy deletes non-multi-instance-parameters</li>
			<li>Adapt SdClientService mapping rule to support TPS_SYST_02096</li>
			<li>Module Import Merge: Numerical Values (Boolean, Integer, Float) are not resolved correctly</li>
			<li>Change DCF-Load merge behaviour depending on atpSplitable stereotype</li>
			<li>System extractor should filter out unnecessary GlobalTimeDomain elements</li>
			<li>Automation Interface: ArxmlFile loading fails the second time in the same task execution</li>
			<li>Performance: Diff&amp;Merge Assistant: The evaluation of platform annotated CEs takes too long</li>
			<li>Add Information about atpSplitable stereotype to the IClassInfo</li>			
			<li>No stable UUID generate for CanHardwareObject in TX case</li>
			<li>Derive MICROSAR specific enumeration value TcpIpAddressType.TCPIP_IPV4_BROADCAST</li>
			<li>SwcGeneration: CanEnterExclusiveAreas are not fully synchronized</li>			
			<li>It is not possible to use AUTOSAR_COMPLEMENTARY_DATA files via command line</li>
			<li>&apos;Synchronize now&apos; fails with Model Modification Exception or other failure</li>
			<li>Get correct ComponentPrototype from DiagnosticServiceDataMappings for Connectors</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.17 SP3">DaVinci Configurator Pro 5.17 (SP3)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Change derivation for ComMChannelRef in DcmDslConnections for DoIp</li>
			<li>FrNmPnEraCalcEnabled is derived although PncMappingType does not exist</li>
			<li>Keyboard navigation leads to NullPointerException</li>
			<li>Platform Functions are no longer shown in the Properties View</li>
			<li>Lack of error message when violating the constr_3102 in J1939NmNode Mapping</li>
			<li>Memory Leak when reloading project after external changes</li>
			<li>RTE59000 appears always for duplicate fibex elements</li>
			<li>DataMappingValidation exception for variant signals</li>
			<li>Change Cfg5 model merge behavior for dcf-workspaces with different priority attributes</li>
			<li>Check open intervals with equal bounds for linear compu scales</li>
			<li>ValidationView is not updated when Acknowledgement is added</li>
			<li>Misleading error message when there are more than one signalPort referenced by a signalTriggering</li>
			<li>ConsistencyRT-Error for UniqueSymbolicNameValueValidation-Rule</li>
			<li>Reference target is not shown in reference selection dialog</li>
			<li>TaskMapping grid disappears</li>
			<li>Correct E2Ehandling in CFG5 to support DEV4 interaction</li>
			<li>No TcpIpIpFramePrioDefault parameter if EthernetCommunicationController is used in multiple VLANs</li>
			<li>Issues in Derivation of J1939NmNodes and SupportNameManagement attribute</li>
			<li>Consider DiagnosticParameterIdentifier in DemDataClass NamingRule</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p><!-- #################### -->
		<h2>
			<a name="5.17 SP2">DaVinci Configurator Pro 5.17 (SP2)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Project Settings Editor: External Generation Steps: Error message &apos;The name XY already exists&apos; appears wrongly</li>
			<li>Derive DcmDspVehInfoDataSize as Byte value</li>
			<li>Input File Assistant: InputFileType is not correctly detected if project is setup with an older version</li>
			<li>Input File Assistant: ECU Instance can not be selected if the FileType is changed.</li>
			<li>SoAdTxIfTriggerTransmit should only be derived for IF PDUs</li>
			<li>Overview page of Memory Blocks Editor isn&apos;t updated</li>
			<li>Merger does not report error to the calling context</li>
			<li>Add all missing DiagnosticServiceInstances to the DcmDsdService Namingrule</li>
			<li>Do not allow to map a data element multiple times to the same SignalGroup</li>
			<li>RTE51035: InitValues for Records and Arrays are not shown correctly</li>
			<li>Project Settings Editor - NullPointerExceptions when removing (not existing) external ECUC file</li>
			<li>Derive DcmDspDataType as Array datatype for Arrays with variable size</li>
			<li>Ecu configuration report doesn&apos;t contain annotations</li>
			<li>Header Type of ContainerIPdu is not derived correctly for value NO-HEADER</li>
			<li>Input File Assistant: Open Update Report doesnt work anymore</li>
			<li>Not possible to delete variant expression argument in BSW management editor</li>
			<li>Show RTE Validation messages only if RTE is active</li>
			<li>Do not allow to rename application ports of service components</li>
			<li>Deactivate HW or VTT module partners when the user activates/deactivates modules</li>
			<li>Derive parameter CanIfTxPduCanIdMask as soft-derived</li>
			<li>ConnectorValidationRule: Support record degradation for N:1 communication</li>
			<li>Derive DemDidDataClassRefs and DemDidClassRefs in the correct order</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.17 SP1">DaVinci Configurator Pro 5.17 (SP1)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Revert generic namning rules introduced within R20 for DCM and DEM.</li>
			<li>Project Settings Editor: External Generation Steps: Error message &apos;The name XY already exists&apos; appears wrongly</li>
			<li>Derive DcmDspVehInfoDataSize as Byte value</li>
			<li>Overview page of Memory Blocks Editor isn&apos;t updated</li>
			<li>Project Status Information: Hyperlinks don&apos;t work as expected</li>
			<li>Deactivate HW or VTT module partners when the user activates/deactivates modules</li>
			<li>Ecu configuration report doesn&apos;t contain annotations</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.17">DaVinci Configurator Pro 5.17</a>
		</h2>
		<h3>Extensions</h3>
		<h4>Input File Assistant</h4>
		<ul>
			<li>Easier selection and configuration of the project input files</li>
			<li>Separation of preprocessing to speed-up the workflow</li>
			<li>File type recognition</li>
		</ul>
		<h4>Validation View</h4>
		<ul>
			<li>New look similar to other grid views (usage of a tree-grid-control)</li>
			<li>Filtering by severity</li>
		</ul>
		<h3>Miscellaneous Tool Features</h3>
		<ul>
			<li>Extension of Automation API for task mapping</li>
			<li>Create connectors based on DiagnosticEventPortMapping for Dem (Event Monitor Ports and Event Info Ports)</li>
			<li>Speedup of applying pre/rec/init-config on large projects</li>
			<li>Create connectors based on DiagnosticEventPortMapping for Dem</li>
			<li>Roundup of selective Diff&amp;Merge</li>
			<li>Introduction of the Difference Details View</li>
			<li>New format of Ecu-Configuration Report</li>
			<li>Display of Element Usage within Reference Selection Dialog</li>
			<li>Restructuring of &apos;ECU Composition&apos; Editor</li>
			<li>Restore the tree expansion and selection state after switching the displayed variant (all Domain Editors)</li>
			<li>Increase compatibility index for connections if a port interface mapping exists</li>
			<li>Support of AUTOSAR 4.3.1 schema</li>
			<li>Extension of Automation API for importing module configurations</li>
			<li>DaVinci Developer workspace can be added later even after creating the DPA project</li>
			<li>ECU Software Design Editor: introduction of Port Terminators</li>
			<li>Support of IP dual-stack use case in ETH mapping rules</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Diff/Merge: Support Admin-Data elements in the ECUC</li>
			<li>Diff/Merge: Add-Action leads to InvalidModification-Error</li>
			<li>Too many IpduMContainedTxPdu / IpduMContainedRxPdu derived in a gateway</li>
			<li>Project Settings Editor: Path to DaVinci Developer Workspace not selectable via file dialog</li>
			<li>Remove derivation of FrArTpGeneral/FrArTpHaveGrpSeg and FrArTpGeneral/FrArTpHaveLm</li>
			<li>Data Mapping Assistant does not lock complex elements after removing root mapping</li>
			<li>Original parameter value not set after removing the user-defined flag of an ECU parameter</li>
			<li>Enabling top-down service configuration in a project without developer workspace raises errors</li>
			<li>Ethernet: Support &apos;ANY&apos; as IP address wildcard</li>
			<li>The user gets non-resolvable &apos;RTE01122 Invalid reference&apos; and &apos;RTE51029 Component prototype inconsistent.&apos; validation message</li>
			<li>Content Assist removes newlines in external generation steps editor</li>
			<li>Tree Control: Expansion of multiple elements at once by pressing &apos;+&apos; not possible</li>
			<li>EcucInitfctService 3rdPartyClient: solve automatically ConfigPtrName in case there is only one</li>
			<li>Update Workflow gets aborted if MultiplexedIPdu is not referencing a PartPdu</li>
			<li>FatalError during project update with an EcuExtract with a long file name</li>
			<li>ValidationRule Cfg00020 - &apos;Deviation from initial configuration&apos; validation message is missing some text</li>
			<li>Recent File List may contain same project twice</li>
			<li>Resolving links to the &apos;Mcu Frequency Calculation&apos; editor fails</li>
			<li>Remove derivation of FrArTpGeneral/FrArTpHaveGrpSeg and FrArTpGeneral/FrArTpHaveLm</li>
			<li>Creation of multiple VTT modules when activating multiple modules with same AUTOSAR base definition</li>
			<li>Support static IPvX and MAC multicast group address assignments</li>
			<li>Changing the number format on freshly changed values resets the values</li>
			<li>Support &apos;ANY&apos; as IP address wildcard</li>
			<li>Original parameter value not set after removing the user-defined flag of an ECU parameter</li>
			<li>Performance issues in SwcPortMapping Validator</li>
			<li>NullPointerException when Vlan is missing in a VlanMembership</li>
			<li>Support for PRIORITY attribute in DCF-Workspaces</li>
			<li>FindView does not find the type == PRPortPrototype</li>
			<li>Bus Controller Editor: acceptance filter optimization is disabled when no CanIfRxPduCanIdRange is available</li>
			<li>Disallowed variance at ComIPdus and ComSignals in Postbuild-Selctable &amp; Postbuild-Loadable projects</li>
			<li>NullPointerException in EcucBswInitfctService if %MULTICONFIGNAME% is one of several possible configurations</li>
			<li>Do not derive ComBitSize if SignalLength exceeds the maximum of 64</li>
			<li>PduRRoutingPath is missing for legacy communication files with PNC EIRA PDUs</li>
			<li>PDU in variant project cannot be configured as FullCan</li>
			<li>Derive SecOCTxPduSecuredArea/SecOCRxPduSecuredArea Container and its parameters</li>
			<li>Do not derive TcpIpStaticIpAddressConfig for IP addresses with the value &apos;ANY&apos;</li>
			<li>EIRA / ERA PDU assigned to wrong channels and bus system</li>
			<li>Errors during DaVinci Developer workspace synchronization are not fully reported to the user</li>
			<li>Only derive DemTImeSeriesSnapshot container if values from AdminData &apos;TimeSeriesSnapshot&apos; are available</li>
			<li>No Com is derived when there are only NM-Pdus in the extract</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.16 SP7">DaVinci Configurator Pro 5.16 (SP7)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Rework warning message of &apos;No installed VttCmdTool found.&apos;</li>
			<li>Base Validation of Arrays Does not Check Array Size Correctly</li>
			<li>DataMappingValidation Inner Against Outer Mapping for SenderReceiverToSignalGroupMappings</li>
			<li>AutomationAPI: Connecting Two Ports of the Same Component Not Possible</li>
			<li>E2EProtectionValidator Fails to Synchronize Multiple Receivers Scenarios</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.16 SP6">DaVinci Configurator Pro 5.16 (SP6)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>&apos;Access Denied&apos; exception by creating a new script project</li>
			<li>Export to CSV in Task Mapping Editor does not keep the correct order of elements</li>
			<li>ComIPduGroupRef parameter not derived for nested ISignalIPduGroup</li>
			<li>Temporary generation files are deleted, although the option keep temporary generation files is set</li>
			<li>E2EProtectionValidator: E2Es without context ports</li>
			<li>RT00002 error in E2EProtectionValidator for E2Es without profile</li>
			<li>E2EProtectionValidator does not remove inconsistent protections</li>
			<li>Data-Mapping validation exception for mappings without signal reference</li>
			<li>Top-Down Service Configuration: Creating connectors with duplicate shortnames</li>
			<li>Data-Mapping validation does not fully validate child mappings</li>
			<li>Flattener does not correctly handle ports of multi-instantiated compositions</li>
			<li>Post-Build Loadable: Parameter is not deleteable even ParentContainer was created in PostBuild-Configuration Phase</li>
			<li>Flattener should base-validate direction of connector&apos;s ports</li>
			<li>Flattener cannot synchronize &apos;derived&apos; state of inner data mappings</li>
			<li>Top-Down Service Configuration shall consider port interfaces without &apos;isService&apos; flag as application port interfaces</li>
			<li>Flattener does not correctly handle SubElementMappings for arrays</li>
			<li>DaVinci Configurator SIP.Diag license (ASR4 diagnostics in MSR3 use case): Basic Editor and Diagnostic Domain Editors are missing</li>
			<li>Only one PduPort is considered for payload of SecuredIPdu</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.16 SP5">DaVinci Configurator Pro 5.16 (SP5)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Memory leak when adding Project Standard Configuration Files</li>
			<li>System extractor should filter out unnecessary GlobalTimeDomain elements</li>
			<li>Memory leak when adding ecuc file references</li>
			<li>&apos;Synchronize now&apos; fails with Model Modification Exception or other failure</li>
			<li>DataMappingValidation exception for variant signals</li>
			<li>AUTOSAR version with new version schema location is not correctly parsed.</li>
			<li>Diff &amp; Merge Assistant: Improve determination of modules contained in project OTHER (and BASE)</li>
			<li>Variant merger removes post-build loadable state from containers</li>
			<li>No stable UUID generate for CanHardwareObject in TX case</li>
			<li>SwcGeneration: CanEnterExclusiveAreas are not fully synchronized</li>
			<li>AutomationInterface ArxmlFile loading fails the second time in the same task execution</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.16 SP4">DaVinci Configurator Pro 5.16 (SP4)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Project Settings Editor should validate empty working directory for external generation steps</li>
			<li>Show RTE Validation messages only if RTE is active</li>
			<li>Do not allow to map a data element multiple times to the same SignalGroup</li>
			<li>NullPointerException is shown when loading a project</li>
			<li>Acceptance Filter editor does not show all PDUs on channel</li>
			<li>Data Mapping Assistant offers connected trigger elements for manual mapping</li>
			<li>Initial configuration is applied to wrong container</li>
			<li>Consistency error for UniqueSymbolicNameValueValidation-Rule</li>
			<li>Differences View: &apos;Copy&apos; on non changeable elements returns incomplete AUTOSAR path</li>
			<li>Content assist is opened on disabled fields and changes the content of the field</li>
			<li>Automatic selection of VTT modules doesn&apos;t work if the user presses &apos;Finish&apos;</li>
			<li>Check for dongle drivers not working with 32bit Windows</li>
			<li>Top-down service configuration: RTE59001 is not solving all default actions</li>
			<li>RTE59000 appears always for duplicate fibex elements</li>
			<li>Support of &apos;Reload Project...&apos; option within file change dialog</li>
			<li>SWC generation: Synchronizing 847 runnables and 600 calibration parameter lasts too long</li>
			<li>Connectors with too long shortnames</li>
			<li>No comparison is done for ECUC elements</li>
			<li>No TcpIpIpFramePrioDefault parameter if EthernetCommunicationController is used in multiple VLANs</li>
			<li>SWC generation AUTOSAR3: Symbol of runnables is not synchronized</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.16 SP3">DaVinci Configurator Pro 5.16 (SP3)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Merge GlobalTimeSlaves based on their CommunicationConnector reference</li>
			<li>XCP PDUs are not derived if they are only modelled with FarmeTriggerings</li>
			<li>Consider Variance reference in AdminData for SwcPortMappingValidator</li>
			<li>Consider PRPortPrototypes for S/R-Interfaces in SwcPortMappingValidator RTE59002</li>
			<li>Consider J1939Cluster in Dcm Mapping</li>
			<li>Differences View: &apos;Filter not resolvable differences&apos; doesn&apos;t filter differences with an &apos;Ignore&apos; action</li>
			<li>Connect to new port does not allow to create application ports on NvBlockSWCs</li>
			<li>ComSpecs for composite (record/arrray) VariableDataPrototypes are not flattened out correctly</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.16 SP2">DaVinci Configurator Pro 5.16 (SP2)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Input Files Editor: DiagFilesetConfiguration Assistant states an Error &apos;Cannot create State Description Template&apos;</li>
			<li>Input file preprocessing does not start if the merger configuration is changed</li>
			<li>Gpt Validation: GptWakeupEventEmptyCheck reports an invalid descriptor which does not pass the filter</li>
			<li>Diff/Merge: Show error message if project OTHER or BASE cannot be loaded</li>
			<li>&apos;Make path relative&apos; doesn&apos;t consider relative paths with path variable correctly</li>
			<li>Application connectors grid: connected component prototype link not working for service components</li>
			<li>Cancel loading a configuration when referenced files are missing</li>
			<li>Frame priorirty not correctly derived for SoAdConnectionGroups</li>
			<li>Project Settings Editor: PostBuild-Loadable flag is not changeable with a PBLDiagOnly license</li>
			<li>Null-pointer-exception in EcucBswInitfctService if %MULTICONFIGNAME% is one of several possible configurations</li>
			<li>Do not derive ComBitSize if SignalLength exceeds the maximum of 64</li>
			<li>Code generation is not possible due to error RTE13068 - Insufficient data type to represent mode value</li>
			<li>Task Mapping Assistant throws null-pointer-execption for obsolete task mappings</li>
			<li>Do not derive LdCom Pdu if the Pdu is routed and not used</li>
			<li>BSWM Editor: Auto Configuration of Communication Control causes duplicate shortname errors and abort of transaction</li>
			<li>&apos;Show-in&apos;: Resolving a link into a currently closed editor does not scroll the target form control visible</li>
			<li>Description of exclusive area in BswInternalBehavior is not updated</li>
			<li>PDU in variant project cannot be configured as FullCan</li>
			<li>ConsistencyRT00002 error for an Swc description validator</li>
			<li>Context menu actions are not executed when using touchpad and enhanced screens and Windows 10</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.16 SP1">DaVinci Configurator Pro 5.16 (SP1)</a>
		</h2>
		<h3>Extensions</h3>
		<h4>Miscellaneous Tool Features</h4>
		<ul>
			<li>Extension of Automation API for task mapping</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Input Files Editor: needless state description can not be removed from configuration</li>
			<li>Input Files Editor: error during setup a new project with multiple driver SIP</li>
			<li>Update workflow does not retain variant order of EcuMDriverInitItems</li>
			<li>Resolving links to the &apos;Mcu Frequency Calculation&apos; editor fails</li>
			<li>GUI grids: prevent recalculation of row height when scrolling horizontally</li>
			<li>Basic Editor grids lose their selection during update</li>
			<li>Incorrect decorations are shown in empty cells</li>
			<li>Support static IPvX and MAC multicast group address assignments</li>
			<li>The module DiagXf is not shown in Generate Dialog/Project Settings Editor</li>
			<li>&apos;Create Parameter&apos; entry is missing in context menu for multiselection enum parameters</li>
			<li>SwcPortMappingValidator is slow on large projects and causes consistency/RTE validation error</li>
			<li>Support for PRIORITY attribute in DCF Workspaces</li>
			<li>&lt;SPACE&gt; cannot be used to toggle checkstate of items in assistants</li>
			<li>The user gets non-resolvable &apos;RTE01122 Invalid reference&apos; and &apos;RTE51029 Component prototype inconsistent.&apos; validation message</li>
			<li>Allow to configure the transformer reference of a port interface mapping</li>
			<li>Transformer modules are generated although they are not enabled for generation</li>
			<li>Bus Controller Editor: acceptance filter optimization is disabled when no CanIfRxPduCanIdRange is available</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.16">DaVinci Configurator Pro 5.16</a>
		</h2>
		<h3>Extensions</h3>
		<h4>Platform Development Support</h4>
		<ul>
			<li>Definition of platform function in the Project Settings Editor</li>
			<li>User-defined assignment of EcuC containers to a platform function (explicit assignment)</li>
			<li>Automatic inheritance of function assignment for child containers and referenced containers (implicit assignment)</li>
			<li>Implicit assignment of RTE and OS configuration based on function assignment of SWCs</li>
			<li>Selective import and merge of platform functions via the Project Merge Assistant</li>
		</ul>
		<h4>Usability Improvements</h4>
		<ul>
			<li>Copy &amp; paste of partial module configurations between DaVinci projects</li>
			<li>Central toolbar switch to display reference parameters either with full path or only as short name</li>
			<li>Support of multiple column filters in grid views</li>
			<li>New layout of the New Project Assistant</li>
			<li>Restore the tree expansion and selection state after switching the displayed variant (Basic Editor only)</li>
			<li>Bus Controller Editor: improved performance and usability of Bus Timing Configuration dialog for CAN</li>
			<li>Bus Controller Editor: improved CAN acceptance mask configuration</li>
			<li>Display of project status in the status line of the main window</li>
			<li>Change default action of &apos;Edit Variance&apos; assistant to &apos;Detailed configuration of variance&apos; and remember last choice</li>
			<li>Automatic selection of newly created elements in tree</li>
			<li>Activate additional VTT modules during update workflow</li>
			<li>Option to run the SWC template generation during the code generation</li>
		</ul>
		<h4>Automation</h4>
		<ul>
			<li>Extensions for task mapping and component prototype creation</li>
		</ul>
		<h4>Miscellaneous Tool Features</h4>
		<ul>
			<li>ECUC file granularity can be changed after project creation</li>
			<li>Schema validation of System Extract input files during the update workflow</li>
			<li>Derive CanTSyn, FrTSyn and StdM according AUTOSAR 4.3</li>
			<li>Derivative selection during SIP update</li>
			<li>Support of PRPortProtoypes for service SWCs with Sender/Receiver interfaces (used by Dem and Dcm)</li>
			<li>Validation of MetaDataLength for optimized Client/Server modelling according to AUTOSAR 4.3</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Text is clipped in &apos;Edit variance&apos; dialog</li>
			<li>Status tab in PropertiesView and CopyPath does not work on variant parameters</li>
			<li>Standard Create Parameter Action does not correctly consider parameters in non-existent chosen choice containers</li>
			<li>Last-Editor toolbar action does not work anymore for Overview-Pages and more</li>
			<li>Editor displays &apos;More than 5000 errors&apos; message even though it displays no error</li>
			<li>TP Editor does not update content after a relevant model change</li>
			<li>&apos;Show unconnected ports only&apos; does not have an effect on unmapped ports</li>
			<li>Invalid derivation of J1939DcmBusType; missing J1939DcmChannels</li>
			<li>Support Admin-Data elements in the ECUC in Diff&amp;Merge</li>
			<li>A solving action of a potentially invalid on-demand result should not be executable</li>
			<li>Diff&amp;Merge: Parameter is duplicated if it exists already without value</li>
			<li>Remove old outdated information from InternalBehavior</li>
			<li>No error icons are shown within the ClockReferencePoints grid</li>
			<li>Adjust FrTpConnection naming rule</li>
			<li>ComMPncComSignal for ERIA Rx pdu missing</li>
			<li>Change derivation of CanHardwareObject, CanIfHxhConfig container</li>
			<li>Create Parameter Operation is enabled in Context Menu on Parameters with 0:0 Multiplicity</li>
			<li>ECU Composition does not show RTE59000 in ECU Software Components Editor</li>
			<li>Grid of Task Mapping Editor disappears on switching grouping mode</li>
			<li>Change the derivation of CanTpRxTaType to AUTOSAR 4.3</li>
			<li>Derive CanNmVariant dependent of NmPassiveModeEnabled</li>
			<li>Provide SolvingActions for &apos;AR-ECUC02067 A choice-container must have exactly one choice child&apos;</li>
			<li>Validation View context menu is not resized correctly</li>
			<li>BSW Management Editor shows actions of inactive variants</li>
			<li>Error about multiple reference targets is reported when creating a logical expression</li>
			<li>MCU Editor not available in .MCAL tool version</li>
			<li>Automap does not consider delegation ports as potential targets</li>
			<li>Consider the signal group name for the Data Mapping Assistant</li>
			<li>Derive only from TpConnections that are used by the ECU instance of the System Extract</li>
			<li>The derivation of the SecOc Pdu and the contained AuthenticPdu is changed for the Gateway use case</li>
			<li>Remove derivation of parameter CanTpRxNSduId</li>
			<li>SignalGroups with Com Based Transformers are not shown as serialized</li>
			<li>Remove derivation of XcpOnCanEnabled</li>
			<li>LinTpNumberOfRxNSdu / LinTpNumberOfTxNSdu are not derived anymore</li>
			<li>Properties View does not show default value for parameters of type String</li>
			<li>Task Mapping: setting a selection makes grid column width non-resizeable</li>
			<li>Change mapping rule for SoAdRxUpperLayerType and SoAdTxUpperLayerType with DcmIPdus</li>
			<li>No Com and/or LdCom module are derived if no ISignalIPdus are used by the ECU</li>
			<li>FrIfUserRxIndicationUL and FrIfTxPduUserTxConfirmation is not set to XCP</li>
			<li>Data Mapping Assistant shows channel name instead of cluster name</li>
			<li>Change mapping rule for PDU type in SoAd and DoIP for DcmIPdus</li>
			<li>Derive DoIPLogicalAddress parameter for the DoIP module</li>
			<li>System Description cannot be synchronized when changing type of init value</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.15 SP8">DaVinci Configurator Pro 5.15 (SP8)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>BSW management editor deletes auto configuration conditions</li>
			<li>Save Project Via Automation Interface for PB Variant Configurations</li>
			<li>Error Dialog appears without warnings inside</li>
			<li>DataMappingValidation Exception For Mappings Without Signal Reference</li>
			<li>PostBuildLoadable: Parameter is not deleteable even ParentContainer was created in PostBuild-ConfigurationPhase</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.15 SP7">DaVinci Configurator Pro 5.15 (SP7)</a>
		</h2>
		<h3>Extensions</h3>
		<h4>Miscellaneous Tool Features</h4>
		<ul>
			<li>SwcGeneration: improved performance</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Exception when creating connectors on components</li>
			<li>NullPointerException is shown when loading a project</li>
			<li>Show RTE Validation messages only if RTE is active</li>
			<li>Differences View: &apos;Copy&apos; on non changeable elements returns incomplete AUTOSAR path</li>
			<li>Automatic selection of VTT modules doesn&apos;t work if the user presses &apos;Finish&apos;</li>
			<li>Check for dongle drivers not working with 32 Bit Windows</li>
			<li>RTE59001 is not solving all default actions</li>
			<li>RTE59000 appears always for duplicate fibex elements</li>
			<li>&apos;Synchronize now&apos; fails with Model Modification Exception or other failure</li>
			<li>SwcGeneration AR3: Symbol of runnables is not synchronized</li>
			<li>SwcGeneration: CanEnterExclusiveAreas are not fully synchronized</li>
			<li>Temporary generation files are deleted, although the option keep temporary generation files is set</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.15 SP6">DaVinci Configurator Pro 5.15 (SP6)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Using TypeReferences for PortDefinedArgument data types leads to non-synchable ServiceComponent</li>
			<li>Input file preprocessing does not start if the merger configuration is changed</li>
			<li>EcucInitfctService 3rdPartyClient: solve automatically ConfigPtrName in case there is only one</li>
			<li>Update Workflow gets aborted if MultiplexedIPdu is not referencing a PartPdu</li>
			<li>Merge GlobalTimeSlaves based on their CommunicationConnector reference</li>
			<li>NPE in EcucBswInitfctService if %MULTICONFIGNAME% is one of several possible configurations</li>
			<li>Do not derive ComBitSize if SignalLength exceeds the maximum of 64</li>
			<li>Do not derive LdCom Pdu if the Pdu is routed and not used.</li>
			<li>Context menu actions are not executed when using touchpad and enhanced screens and Windows 10</li>
			<li>Errors during Dev-Workspace-Synchronization are not fully reported to the user</li>
			<li>Consider PRPortPrototypes for S/R-Interfaces in SwcPortMappingValidator RTE59002</li>
			<li>Context menu commands are not post-build selectable aware</li>
			<li>Consider J1939Cluster in Dcm Mapping</li>
			<li>Connect to new port does not allow to create application ports on NvBlockSWCs</li>
			<li>ConnectorValidationRule: Support record degradation for N:1 communication</li>
			<li>ComSpecs for composite (record/arrray) VariableDataPrototypes are not flattened out correctly</li>
			<li>Flattener not always flattening out correct multi instantiated components</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.15 SP5">DaVinci Configurator Pro 5.15 (SP5)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Add more Null-Pointer checks into service functions</li>
			<li>Input Files Editor: DiagFilesetConfiguration Assistant states an Error</li>
			<li>Gpt Validation: GptWakeupEventEmptyCheck reports an invalid descriptor which does not pass the filter</li>
			<li>CanIfRxPduUserRxIndicationUL and CanIfTxPduUserTxConfirmationUL is derived as NULL_PTR for ContainerIPdus</li>
			<li>Enabling top-down service configuration in a project without developer workspace raises errors</li>
			<li>Support for PRIORITY attribute in DCF-Workspaces</li>
			<li>Cancel loading a configuration when referenced files are missing</li>
			<li>Memoryleak in Validation View context menu</li>
			<li>Project Settings Editor: PostBuild-Loadable flag is not changeable with a PBLDiagOnly license</li>
			<li>+, ++ and x toolbar buttons are missing in action list grid in BSW Management Editor</li>
			<li>Task Mapping Assistant throws Null-Pointer-Exception for obsolete task mappings</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.15 SP4">DaVinci Configurator Pro 5.15 (SP4)</a>
		</h2>
		<h3>Fixed Issues</h3>
		<ul>
			<li>TP editor does not update content after a relevant model change</li>
			<li>Improvement of ECU selection in input files editor</li>
			<li>Error tooltip isn&apos;t updated correctly when hovering on another error</li>
			<li>Properties view does not show &apos;Variance&apos; properties in many comfort editors</li>
			<li>Too much ComPduGroups are derived in variant projects</li>
			<li>Smoother auto-scrolling in task mapping editor grid</li>
			<li>Change mapping rule for SoAdRxUpperLayerType and SoAdTxUpperLayerType with DcmIPdus</li>
			<li>SIP.DIAG license flags doesn&apos;t activate the input files editor for diagnostic files</li>
			<li>UnhandledEventLoopException in task mapping editor </li>
			<li>CanIfTxPduUserTxConfirmation and CanIfRxPduUserRxIndication is not set to XCP</li>
			<li>FrIfUserRxIndicationUL and FrIfTxPduUserTxConfirmation is not set to XCP</li>
			<li>Gateway Routings: Received source PDU is resent on receiving channel</li>
			<li>DataMappingAssistant shows channel name instead of cluster name</li>
			<li>Ethernet: Change mapping rule for PDU type in SoAd and DoIP for DcmIPdus</li>
			<li>Change mapping rule for EthIfCtrlMtu</li>
			<li>SystemDescription cannot be synchronized when changing type of init value</li>
			<li>Too many IpduMContainedTxPdu / IpduMContainedRxPdu derived in a Gateway</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.15 SP3">DaVinci Configurator Pro 5.15 (SP3)</a>
		</h2>
		<h3>Extensions</h3>
		<h4>Miscellaneous Tool Features</h4>
		<ul>
			<li>CAN Bustiming Dialog: improved performance and usability</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Connector check for ApplicationDataTypes should not rely on ImplementationDataTypes</li>
			<li>Derive CanNmVariant dependent of NmPassiveModeEnabled</li>
			<li>Differences are not cleaned up before another import / compare is started</li>
			<li>Validation View context menu is not resized correctly</li>
			<li>Multiselection editing doesn&apos;t allow to set empty string</li>
			<li>Consider the SignalGroup name for the DataMapping Assistant</li>
			<li>SWC generation in AUTOSAR3 use case: SWC is out-of-synch after each project load</li>
			<li>LdComApiType LDCOM_TP is never derived</li>
			<li>Enhance the derivation of SecuredIPdu for PDU fan-out</li>
			<li>The BSW Management Editor&apos;s tree shows elements of non-active variants</li>
			<li>Long duration of generation when Custom-Workflow-Steps are configured</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.15 SP2">DaVinci Configurator Pro 5.15 (SP2)</a>
		</h2>
		<h3>Note</h3>
		<li>Ensure to install the latest External-Components Setup from: <a href="https://vector.com/vi_servicepacks_davinci_en.html">Vector Download Area</a>
		</li>
		<h3>Extensions</h3>
		<h4>Miscellaneous Tool Features</h4>
		<ul>
			<li>Update pool license management to version 2.0 and to Flexnet version 11.13.03</li>
			<li>Property view status tab shows preconfigured status with value relation</li>
			<li>Provide Option to integrate SWC Templates into the generation</li>
			<li>Improved automatic selection in &apos;Edit variance&apos; dialog</li>
			<li>Improved CAN Acceptance Mask Configuration</li>
			<li>Change default action of &apos;Edit Variance&apos; assistant to &apos;Detailed configuration of variance&apos; and remember last choice</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>&apos;Connect to new port&apos; doesn&apos;t fully validate new port names and runnable prefix/postfix</li>
			<li>Cfg5 fails to refresh propertly, when a module is activated/deavtivated via the undo/redo operations</li>
			<li>Change derivation of CanHardwareObject, CanIfHxhConfig container</li>
			<li>&apos;Setup Event Memory Blocks&apos; assistant changes the BlockId name unexpectedly</li>
			<li>In a variant configuration Parameter is created without recommended/default value</li>
			<li>New project assistant: Disable VTT settings if VTT is not enabled</li>
			<li>Wrong order of actions in the context menu in communication users editor</li>
			<li>IllegalStateException when there pre ext gen steps, post ext gen steps and ARXML to A2L conv is activated</li>
			<li>Missing ComControllerRef in CanCommunicationConnector aborts the Update Workflow</li>
			<li>Automap does not consider delegation ports as potential targets</li>
			<li>Displaying the RTMMeasurementPoints grid in the BasicEditor takes too long</li>
			<li>Status tab displays only location in properties view for non-existance parameter, display also Createable state</li>
			<li>Derive only from TpConnections that are used by the ECU instance of the System Extract</li>
			<li>The derivation of the SecOc Pdu and the contained AuthenticPdu is changed for the Gateway use case</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.15 SP1">DaVinci Configurator Pro 5.15 (SP1)</a>
		</h2>
		<h3>Extensions</h3>
		<h4>Miscellaneous Tool Features</h4>
		<ul>
			<li>Diff&amp;Merge of system description elements in variant projects</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>ECU Software Components Editor: Unhandled Event Loop Exception when preselecting the child element in the Data Mapping Page</li>
			<li>Combined Events: DTC id set for BSW Events cannot be cleared</li>
			<li>Validation message &apos;RTE51017 Port prototype inconsistent&apos; shown for a port in a CompositionType</li>
			<li>Create New Port Assistant: Two connections are created instead of one</li>
			<li>For nested data mappings the channel and cluster is displayed as &apos;Unmapped&apos;</li>
			<li>No error icons are shown within the ClockReferencePoints grid</li>
			<li>ComMPncComSignal for ERIA Rx pdu missing</li>
			<li>FrTpConnectionMapping - LA / RA Address consider correct communication direction</li>
			<li>Allow the removal of the DT-VTT setting</li>
			<li>BSW Management Editor: disable &apos;Configure...&apos; hyperlinks for disabled auto-configuration domains</li>
			<li>&apos;Create Parameter&apos; operation is enabled in context menu of parameters with 0:0 multiplicity</li>
			<li>User annotation for parameter  is added to parent container</li>
			<li>Grid of Task Mapping Editor disappears on switching the grouping mode</li>
			<li>Hide &apos;Copy value&apos; entry in context menu for configuration elements without value</li>
			<li>Change the derivation of CanTpRxTaType to ASR4.3</li>
			<li>UUID validation does not consider variant input files</li>
			<li>Derive the parameters ComGwDestinationDescription/ComUpdateBitPosition and ComGwSourceDescription/ComUpdateBitPosition</li>
			<li>Performance issue during system description synchronization</li>
			<li>Null Pointer Exception when choosing &apos;Deselect All&apos; in import assistant</li>
			<li>The vertical size of the &apos;Module Import&apos; assistant is too small</li>
			<li>Path length check of DaVinci Configurator launcher executables should include length of license DLLs</li>
			<li>Error about multiple reference targets is reported when creating a logical expression</li>
			<li>Execution order constraint: ComponentRef expects BaseComposition as context</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.15">DaVinci Configurator Pro 5.15</a>
		</h2>
		<h3>Extensions</h3>
		<h4>Miscellaneous Tool Features</h4>
		<ul>
			<li>Support ASR4.3 Schema</li>
			<li>Support DaVinci Developer 4.0</li>
			<li>Improved vVIRTUALtarget configuration</li>
			<li>Reworked and homogeneous context menues</li>
			<li>Support of two Flexray communication controllers</li>
			<li>Simplification of the Delete Module Assistant</li>
			<li>Display CanTpNSa, CanTpNTa and CanTpNAe in Transport Protocol Editor</li>
			<li>Postbuild-Selectable: Support of variance in Diagnostic Data Identifiers Editor</li>
			<li>Performance: Optional deactivation of auto-solving actions to avoid GUI blocking time</li>
			<li>Find View: Support of system description elements</li>
			<li>Improved BSWM logical expression assistant for postbuild-selectable use cases</li>
			<li>RTE configuration: support of execution order constraints and timing constraints</li>
			<li>Support of description-based signal routing</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Exception is shown when using &apos;- Show all -&apos; after project close</li>
			<li>Postbuild-Selectable: Variant specific renaming of container isn&apos;t possible</li>
			<li>Configurable option of silent update of DaVinci Developer workspace</li>
			<li>Unhandled Event Loop Exception when updating Properties View after disconnecting connectors in the ECU Software Components editor</li>
			<li>Wrong derivation of PnFilterMask</li>
			<li>Display error annotations in the grid of the Task Mapping Editor</li>
			<li>Wrong row selected during refresh of application connectors grid after adding/removing connection</li>
			<li>Support multi-selection of Standard Configuration Files in Input File Editor</li>
			<li>Derive CanIfHrh container for NmRangePdus</li>
			<li>Stop file supervision during project update</li>
			<li>Derive parameter NmCoordinator only if the container NmCoordinator exists and refers to a NmNode</li>
			<li>Derive ComFirstTimeout for AUTOSAR 4.3</li>
			<li>Derive the parameter SecOCFreshnessValueId</li>
			<li>Error annotations are displayed on unexpected mode ports</li>
			<li>SoAdRxUpperLayerType and SoAdTxUpperLayerType as specified in AUTOSAR 4.3</li>
			<li>Missing ComSignalLength for ComGwSourceDescription</li>
			<li>Do not derive routed SecOc Tx and Rx Pdus</li>
			<li>Derive CanIfRxPduUserRxIndicationUL and CanIfTxPduUserTxConfirmationUL for SecOc PDUs</li>
			<li>Derive FrIfUserRxIndicationUL and FrIfUserTxUL for SecOc PDUs</li>
			<li>Missing [Can/Fr]IfUserTxConfirmation and [Can/Fr]IfUserRxIndication for SecOc Pdus contained in ContainerIPdus</li>
			<li>SWC template generation GUI does not support cancellation</li>
			<li>Do not derive FrIf/FrIfConfig/FrIfCluster/FrIfController/FrIfCtrlIdx</li>
			<li>Move up and move down buttons of generator steps are always disabled</li>
			<li>Update of a variant project fails if criterion names and variant names don&apos;t match</li>
			<li>Derive the parameters ComGwDestinationDescription/ComUpdateBitPosition and ComGwSourceDescription/ComUpdateBitPosition</li>
			<li>SoAdRxUpperLayerType and SoAdTxUpperLayerType not derived correct for DcmIPdus</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
		</p>
		<!-- #################### -->
		<h2>
			<a name="5.14 SP5">DaVinci Configurator Pro 5.14 (SP5)</a>
		</h2>
		<h3>Miscellaneous</h3>
		<ul>
			<li>Support of new Vector Licenses</li>
		</ul>
		<h3>Fixed Issues</h3>
		<ul>
			<li>Using TypeReferences for PortDefinedArgument data types leads to non-synchable ServiceComponent</li>
			<li>Error tooltip is shown correct by hovering an error but is not changing if hovering another error </li>
			<li>Input file preprocessing does not start if the merger configuration is changed</li>
			<li>Update Workflow gets aborted if MultiplexedIPdu is not referencing a PartPdu</li>
			<li>Input Files Editor: needless state description can not be removed from configuration</li>
			<li>Merge GlobalTimeSlaves based on their CommunicationConnector reference</li>
			<li>Exception when closing project after creating Internal Behavior</li>
			<li>Do not allow to connect service ports to NV-SWCs</li>
			<li>Project Settings Editor: Path to DaVinci Developer Workspace not selectable via file dialog</li>
			<li>BuildVtt-Button is not visible in toolbar</li>
			<li>Incorrect decorations are shown in empty cells</li>
			<li>Data Mapping Assistant does not lock complex elements after removing root mapping</li>
			<li>Application connectors grid: connected component prototype link not working for service components</li>
			<li>Update Workflow: NullPointerException when Vlan is missing in a VlanMembership</li>
			<li>NullPointerException in EcucBswInitfctService if %MULTICONFIGNAME% is one of several possible configurations</li>
			<li>Context menu actions are not executed when using touchpad and enhanced screens and Windows 10</li>
			<li>Properties View Table: Initial resize columns depending on the displayed data</li>
		</ul>
		<p>
			<a href="#_top">(top)</a>
			<!-- #################### -->
			<h2>
				<a name="5.14 SP4">DaVinci Configurator Pro 5.14 (SP4)</a>
			</h2>
			<h3>Note</h3>
			<li>Ensure to install the latest External-Components Setup from: <a href="https://vector.com/vi_servicepacks_davinci_en.html">Vector Download Area</a>
			</li>
			<h3>Extensions</h3>
			<h4>Miscellaneous Tool Features</h4>
			<ul>
				<li>Update pool license management to version 2.0 and to Flexnet version 11.13.03</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Text is clipped in &apos;Edit variance&apos; dialog</li>
				<li>&apos;Connect to new port&apos; doesn&apos;t fully validate new port names and runnable prefix/postfix</li>
				<li>PDUs Editor: J1939 networks are displayed like regular CAN networks</li>
				<li>Derive CanNmVariant dependent of NmPassiveModeEnabled</li>
				<li>Change default action of &apos;Edit Variance&apos; assistant to &apos;Detailed configuration of variance&apos; and remember last choice</li>
				<li>Too much ComPduGroups are derived in variant projects</li>
				<li>Derive only from TpConnections that are used by the ECU instance of the System Extract</li>
				<li>The derivation of the SecOc Pdu and the contained AuthenticPdu is changed for the Gateway use case</li>
				<li>Too many IpduMContainedTxPdu / IpduMContainedRxPdu derived in a Gateway</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.14 SP3">DaVinci Configurator Pro 5.14 (SP3)</a>
			</h2>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Selection changes are ignored in Memory Mapping assistant</li>
				<li>Combined Events: DTC ID set for BSW Events cannot be cleared</li>
				<li>OS Configuration Editor (Gen7 OS): OsApplication-Labels do not contain assigned core number anymore</li>
				<li>Error about multiple reference targets is reported when creating a logical expression</li>
				<li>Saving backup while removing a module doesn&apos;t work</li>
				<li>ECU Software Components Editor: Allow multiselection when selecting target ports for manual connection creation</li>
				<li>Task Mapping Editor: Scrolling to tree-selected mapped runnable in grid does not work anymore</li>
				<li>Diff&amp;Merge: Parameter is duplicated if it exists already without value</li>
				<li>Change derivation of XNmComUserDataSupport parameter for CanNm, FrNm, UdpNm and Nm</li>
				<li>Consistency message RT00002 is created for connector validation</li>
				<li>For nested data mappings the channel and cluster is displayed as &apos;Unmapped&apos;</li>
				<li>Automation Interface: solving action execution may cause an InvalidObjectException</li>
				<li>Adjust FrTpConnection naming rule</li>
				<li>ComMPncComSignal for EIRA Rx PDU missing</li>
				<li>FrTpConnectionMapping - LA / RA Address consider correct communication direction</li>
				<li>Link of unused global PDU should not be resolved into the PDUs Editor</li>
				<li>Grid of Task Mapping Editor disappears on switching grouping mode</li>
				<li>Derivation of PN Filter Masks do not consider only requestor PNC Mappings</li>
				<li>Derive the parameters ComGwDestinationDescription/ComUpdateBitPosition and ComGwSourceDescription/ComUpdateBitPosition</li>
				<li>Support concurrent local license access for multiple clients</li>
				<li>Update workflow fails if project folder path ends with &apos;.\&apos;</li>
				<li>Data Mapping: Automapper does not map root data elements of ArrayOfUint8 to non-dynamic-length signals</li>
				<li>Path length check of DaVinci Configurator launcher executables should include length of license DLLs</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.14 SP2">DaVinci Configurator Pro 5.14 (SP2)</a>
			</h2>
			<h3>Extensions</h3>
			<h4>Miscellaneous Tool Features</h4>
			<ul>
				<li>Support of two Flexray communication controllers</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Postbuild-Selectable: Variant specific renaming of container isn&apos;t possible</li>
				<li>Enable state of a supervised entities individual supervision cycle in the Watchdogs editor</li>
				<li>&apos;Element Usage&apos; command does not work in &apos;Memory Blocks&apos; editor</li>
				<li>Correct &apos;Virtual target&apos; usage state within DaVinci Configurator GUI</li>
				<li>Show recommended and preconfigured information for containers</li>
				<li>Show the loading location of an element</li>
				<li>PDUs editor shows &apos;Com&apos; section for NM CanIf PDU</li>
				<li>Derive the parameter SecOCFreshnessValueId</li>
				<li>Derive additional parameters for SecOC</li>
				<li>Error annotations are displayed on unexpected mode ports</li>
				<li>Input file preprocessing is not executed if the LegacyConverter is changed</li>
				<li>&apos;New Project&apos; Assistant checks for existence of VTT tool even though the VTT target is not enabled in the project</li>
				<li>Prevent creation of duplicate EcuC InitFunctions</li>
				<li>Creating BswMModeConditions for BswMUserConditionRequests is not possible</li>
				<li>Edit variance command is offered for conditions in non-post-build-selectable projects</li>
				<li>Missing [Can/Fr]IfUserTxConfirmation and [Can/Fr]IfUserRxIndication for SecOc Pdus contained in ContainerIPdus</li>
				<li>Swct-Generation GUI does not support cancelation</li>
				<li>Duplicate DoIPConnection names might be generated</li>
				<li>Wrong detected IPv4 broadcast address leads to multiple DoIPUdpVehicleAnnouncementConnections</li>
				<li>A choice container can not be created with bswmdModel() in the automation interface</li>
				<li>Do not derive FrIf/FrIfConfig/FrIfCluster/FrIfController/FrIfCtrlIdx</li>
				<li>BSW management editor: newly created elements are not automatically selected in the tree</li>
				<li>FrNmChannelIdentifiersMapping - consider correct FrNmCluster</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.14 SP1">DaVinci Configurator Pro 5.14 (SP1)</a>
			</h2>
			<h3>Extensions</h3>
			<h4>Miscellaneous Tool Features</h4>
			<ul>
				<li>DaVinci Configurator Lib: no more UUIDs in generated EcuC</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Development Errors Editor throws exception, if DetGeneral container is not present</li>
				<li>Do not derive routed SecOC Pdus</li>
				<li>CreateMemBlockOnDefaultPartition throws an IllegalArgumentException.</li>
				<li>Derive CanIfRxPduUserRxIndicationUL and CanIfTxPduUserTxConfirmationUL for SecOc PDUs</li>
				<li>Derive FrIfUserRxIndicationUL and FrIfUserTxUL for SecOc PDUs</li>
				<li>Missing ComIPduSignalGroupRef in ComIPdu</li>
				<li>Missing ComSignalLength for ComGwSourceDescription</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.14">DaVinci Configurator Pro 5.14</a>
			</h2>
			<h3>Note</h3>
			<li>
				<a href="http://kb.vector.com/128/">Vector Knowledge Base</a> extended with &apos;Troubleshooting&apos; section for the DaVinci tools  </li>
			<h3>Extensions</h3>
			<h4>Automation Interface</h4>
			<ul>
				<li>Automation API for various purposes like editing EcuC values, access of validation results, execution of solving actions</li>
				<li>Integrated scripting host for executing Groovy scripts</li>
				<li>Selection of script files and script projects</li>
				<li>Execution of script tasks via the GUI and via command line</li>
			</ul>
			<h4>BSW Management Editor: improved editing of expressions</h4>
			<ul>
				<li>Drag and drop support</li>
				<li>Reuse of expressions</li>
			</ul>
			<h4>Miscellaneous Tool Features</h4>
			<ul>
				<li>Verify compatibility of vVIRTUALtarget basic version and DaVinci Configurator version</li>
				<li>Support hex, binary, octal format for init values in system description</li>
				<li>Allow export module configuration in read-only projects</li>
				<li>Diagnostic extract processing: auto-connect of Dcm routine ports</li>
				<li>Support of sender/receiver communication for Dem</li>
				<li>Support of memory ranges from cdd file</li>
				<li>Support 64-bit Signal Types for COM according to AUTOSAR 4.2.2</li>
				<li>Support of CAN-FD request types within Transport Protocol Editor</li>
				<li>Split the file preprocessing from the update workflow into an own workflow</li>
				<li>Create separate EIRA TX IPDUGroups and EIRA RX/ERA RX IPDU Groups</li>
				<li>Improve pool license handling within DaVinci Configurator</li>
				<li>Support the replacement of variant module configurations</li>
				<li>Provide new generation setting &apos;Tresos performance optimization&apos;</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Alt+Click doesn&apos;t work in grids in the IOHwAb editor</li>
				<li>PDUs Editor: sub-nodes of a module (i.e. the possible types of module PDUs) shall be sorted alphabetically</li>
				<li>Not all EIRA TX Signals are mapped as ComMPncSignal</li>
				<li>ComMPncSignal for EIRA Tx signal refers to the wrong channel</li>
				<li>Do not map a EraSignal to a ComMPnc if it is only assinged to one channel</li>
				<li>Duplicate log entries in Update Workflow log</li>
				<li>Commandline option -m (--modulesToGenerate) does not work with an empty argument (&quot;&quot;)</li>
				<li>Preferred solving action marker not displayed within Validation View context menu</li>
				<li>Update workflow gets aborted for FrNm with missing FrCommunicationCluster reference</li>
				<li>PDUs Editor: routing path form not displayed correctly when selecting multiple destination PDUs</li>
				<li>Mapping rule for FrIfByteOrder does not use definition in IPduToFrameMapping.packingByteOrder</li>
				<li>PduRRoutingPath created twice for EIRA TX PDU</li>
				<li>The creation of new projects with invalid identifier names shall be rejected</li>
				<li>Project Settings Editor: EcuC File Reference File is not shown after adding a file</li>
				<li>Derive parameter ComSignalGroupArrayAccess</li>
				<li>Derive XNmComUserDataSupport parameter for CanNm, FrNm and UdpNm</li>
				<li>Diff&amp;Merge: tooltip in tree does not show all differences</li>
				<li>Derivation of reference EthTSyn/EthTSynGlobalTimeDomain/EthTSynPortConfig/EthTSynGlobalTimeEthIfRef</li>
				<li>Change mapping rules for TcpIpLocalAddr and TcpIpAddrAssignment to allow multiple assignment methods</li>
				<li>FrTp connection mappings gets aborted at TpConnections without receivers</li>
				<li>Change Generate-Directory in CommandLine-Generation without dpa-File</li>
				<li>Derive parameter UdpNmComUserDataSupport</li>
				<li>Add SecuredIPdus to PduR</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.13 SP7">DaVinci Configurator Pro 5.13 (SP7)</a>
			</h2>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Support of &apos;Reload Project...&apos; option within file change dialog</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Exception when creating Connectors on Components</li>
				<li>Consistency error for UniqueSymbolicNameValueValidation-Rule</li>
				<li>Check for dongle drivers not working with 32bit Windows</li>
				<li>SWC generation AUTOSAR3: Symbol of runnables is not synchronized</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.13 SP6">DaVinci Configurator Pro 5.13 (SP6)</a>
			</h2>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Support of new Vector Licenses</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Using TypeReferences for PortDefinedArgument data types leads to non-synchable ServiceComponent</li>
				<li>Update Workflow gets aborted if MultiplexedIPdu is not referencing a PartPdu</li>
				<li>NullPointerException in EcucBswInitfctService if %MULTICONFIGNAME% is one of several possible configurations</li>
				<li>Context menu actions are not executed when using touchpad and enhanced screens and Windows 10</li>
				<li>Do not sort SolvingActions in Validation View</li>
				<li>TP editor does not update content after a relevant model change</li>
				<li>Parameter is not shown on &apos;Lin&apos; node of Communication General Editor</li>
				<li>Building Vtt-Project Dialog shall not show the Workflow-Tab</li>
				<li>CAN Bustiming dialog performance and usability</li>
				<li>FrTpConnectionMapping - LA / RA Address consider correct communication direction</li>
				<li>Task Mapping Editor: smoother auto-scrolling</li>
				<li>Ranges grid on Flexray controller page (FIFO section) in Bus Controller Editor does not display error decorations</li>
				<li>Bus Controller Editor shows no PDUs on second channel</li>
				<li>Error when unmapping multiple elements in the Task Mapping Editor</li>
				<li>Exception when closing project after creating Internal Behavior</li>
				<li>Too many IpduMContainedTxPdu / IpduMContainedRxPdu derived in a Gateway</li>
				<li>Data Mapping Assistant does not lock complex elements after removing root mapping</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.13 SP5">DaVinci Configurator Pro 5.13 (SP5)</a>
			</h2>
			<h3>Fixed Issues</h3>
			<ul>
				<li>FrTpConnectionMapping - LA / RA Address consider correct communication direction</li>
				<li>Task Mapping Editor: smoother auto-scrolling</li>
				<li>Ranges grid on Flexray controller page (FIFO section) in bus controller editor does not display error decorations</li>
				<li>Bus Controller Editor shows no PDUs on second channel</li>
				<li>Error when unmapping multiple elements in the Task Mapping Editor</li>
				<li>Do not allow to connect service ports to NV-SWCs</li>
				<li>Too many IpduMContainedTxPdu / IpduMContainedRxPdu derived in a gateway</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.13 SP4">DaVinci Configurator Pro 5.13 (SP4)</a>
			</h2>
			<h3>Fixed Issues</h3>
			<ul>
				<li>ECU Software Components Editor: &apos;Connect to new port&apos; doesn&apos;t fully validate new port names and runnable prefix/postfix</li>
				<li>Workflow is cancelled if the schema validation of an Extract file fails during the update workflow</li>
				<li>Update pool license management to version 2.0 and to Flexnet version 11.13.03</li>
				<li>Create New Port Assistant: Two connections are created instead of one</li>
				<li>Error message box is shown if the update workflow was cancelled</li>
				<li>Signal groups with com-based transformers are not shown as serialized</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.13 SP3">DaVinci Configurator Pro 5.13 (SP3)</a>
			</h2>
			<h3>Extensions</h3>
			<h4>Miscellaneous Tool Features</h4>
			<ul>
				<li>Simplifications in the &apos;Delete Module Assistant&apos;</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>GPT Validators and RAMTST Validator shall only be active for MICROSAR definitions</li>
				<li>Adding variance to a non-variant project leads to project update with UNDEFINED file set</li>
				<li>Rounding error in Bustiming editor</li>
				<li>SwcGeneration stays in sync even annotated variant derived-from-referrables change</li>
				<li>Derive CanIfTxPduUserTxConfirmationULType and CanIfRxPduUserIndicationUL for GeneralPurposePdu with category &apos;XCP&apos;</li>
				<li>Variant merger should support post-build-selectable variance in DiagnosticConnections</li>
				<li>RTE59001 appears after execution of RTE59000</li>
				<li>Missing DataTypeMappingSet after project creation</li>
				<li>Changes in ProjectStandardConfiguration Input Files are not notified by the FileSupervision</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.13 SP2">DaVinci Configurator Pro 5.13 (SP2)</a>
			</h2>
			<h3>Extensions</h3>
			<h4>Miscellaneous Tool Features</h4>
			<ul>
				<li>Support MICROSAR OS Gen7 in configuration editors</li>
				<li>Allow module configuration export in read-only projects</li>
				<li>Task Mapping Editor: add a link to create new tasks</li>
				<li>Change mapping rules for TcpIpLocalAddr and TcpIpAddrAssignment to allow multiple assignment methods</li>
				<li>Support dynamic IP multicast address configuration</li>
				<li>Support of CAN-FD request types within Transport Protocol Editor</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Diff&amp;Merge: Improve error message when project &apos;OTHER&apos; is locked by another application</li>
				<li>Duplicate log entries in Update Workflow log</li>
				<li>Commandline option -m (--modulesToGenerate) does not work with an empty argument (&quot;&quot;)</li>
				<li>Exception is shown when closing a project while &apos;Link with editor&apos; is active</li>
				<li>Derive XNmComUserDataSupport parameter for CanNm, FrNm and UdpNm</li>
				<li>Derive parameter UdpNmComUserDataSupport</li>
				<li>Persistency reload doesn&apos;t remove child objects of a removed subtree contained in several files</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.13 SP1">DaVinci Configurator Pro 5.13 (SP1)</a>
			</h2>
			<h3>Extensions</h3>
			<h4>Diff&amp;Merge</h4>
			<ul>
				<li>Introduction of 3-way-merge including an auto-merge functionality</li>
				<li>Diff&amp;merge for SystemDescription elements</li>
				<li>Provide filter mechnism for Diff&amp;Merge results</li>
			</ul>
			<h4>Miscellaneous Tool Features</h4>
			<ul>
				<li>Support MICROSAR SafeWdgM in Watchdogs Editor</li>
				<li>Task Mapping Editor: unmap functionality</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Alt+Click doesn&apos;t work in grids in the IOHwAb editor</li>
				<li>DcmDslConnections incomplete for DoIp + CAN</li>
				<li>Value of System Extract property SocketConnection.clientPortFromConnectionRequest is ignored</li>
				<li>Changing selection of tree nodes in Input Files Editor freezes the application</li>
				<li>Error annotation does not finish in acceptable time in ECU Components editor</li>
				<li>NullPointerException when switching number format</li>
				<li>Project Settings Editor: EcuC File Reference File is not shown after add</li>
				<li>The creation of new projects with invalid identifier names shall be rejected</li>
				<li>PduRRoutingPath created twice for EIRA TX PDU</li>
				<li>Update Workflow gets aborted for FrNm with missing FrCommunicationCluster reference</li>
				<li>Rename of symbolic name value containers is denied even if the symbolic name parameters have equal values</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.13">DaVinci Configurator Pro 5.13</a>
			</h2>
			<h3>Extensions</h3>
			<h4>New editor: Task Mapping</h4>
			<ul>
				<li>Overview of all OS applications, tasks and the mapped runnables</li>
				<li>Map the runnables by drag-and-drop</li>
				<li>Group the RTE events by trigger type or by component</li>
			</ul>
			<h4>BSW Management Editor improved</h4>
			<ul>
				<li>Hide auto-config-sub-nodes without content</li>
				<li>Provide support for post-build variance in &apos;Select Existing Action&apos; assistant</li>
				<li>Provide create commands in auto configuration groups</li>
			</ul>
			<h4>Miscellaneous Tool Features</h4>
			<ul>
				<li>Global number format selection</li>
				<li>Auto-connect Dcm ports with application SWCs based on mappings defined in diagnostic extract</li>
				<li>Support Data Transformation for Dcm</li>
				<li>Derivation of System Extract attributes for EthTSyn</li>
				<li>Support of Bitfield data types for ServiceSWCs</li>
				<li>UUID-based object identification during project update</li>
				<li>Use CMS garbage collector to improve memory performance</li>
				<li>Support of Windows 10</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>DaVinci Configurator freezes when many elements are selected from the Validation View</li>
				<li>Basic Editor performs several updates after executing a find query</li>
				<li>Generation dialog scroll position changes if generation fails</li>
				<li>Element Usage View: Show-In on Multi-Instance-Reference-Parameter does only show Basic Editor as target</li>
				<li>Performance Improvement for multi selection case</li>
				<li>Find view content isn&apos;t cleared on project close / open</li>
				<li>Prevent that PNC EIRA Pdu is derived multiple times</li>
				<li>CanNmRxPdus are missing</li>
				<li>Error when loading a dpa-file from a location containing a #-character</li>
				<li>Do not derive the ComSignal HandleId</li>
				<li>Null Pointer Exception in validation view when filtering out acknowledged validation results</li>
				<li>Shortname change isn&apos;t refelected in the editor tree</li>
				<li>Acknowledging a validationresult that reports a missing module causes an IllegalArgumentException</li>
				<li>Do not derive PduR routing paths and IPduM path ways for Gateway MultiplexedIPdus</li>
				<li>OsIsr Service: a Cfg95301 error is not solvable in case of Mojito Os</li>
				<li>Wrong DcmDslConnection derivation based on DBC DiagConnection attribute</li>
				<li>Setup crashes if path length of the sip to update is to long</li>
				<li>Derive EthSwtConfig from CouplingElements only for the selected ECU instance</li>
				<li>Derive User Rx Indication also for ContainerIPdus</li>
				<li>Unhandled Event loop exception when selecting a schedulable entity in the Events page in Internal Behavior Editor</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.12 SP4">DaVinci Configurator Pro 5.12 (SP4)</a>
			</h2>
			<h3>Miscellaneous Tool Features</h3>
			<ul>
				<li>Prevent loading of AUTOSAR 3.x files at the beginning of the update workflow</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>&apos;Connect to new port&apos; doesn&apos;t fully validate new port names and runnable prefix/postfix</li>
				<li>Do not sort Solving Actions in the Validation View</li>
				<li>Connector Check for Application Datatypes should not rely on ImplDTs</li>
				<li>Parameter &apos;LinIfScheduleEndNotification&apos; is not shown on &apos;Lin&apos; node of Communication General editor</li>
				<li>Deviation of PnFilterMask is wrong</li>
				<li>Diff/Merge/Import: Elements are duplicated after choosing &apos;Use OTHER all&apos;</li>
				<li>Update Workflow gets aborted due to incomplete Gateway routing</li>
				<li>Project Settings Editor - External Generation Steps DetailsPart initially missing</li>
				<li>FrTpConnectionMapping - LA / RA Address has to consider correct communication direction</li>
				<li>Error message box is shown if the Update Workflow was cancelled</li>
				<li>Update Workflow is aborted due to internal error in Flattener</li>
				<li>User Annotations cannot be removed</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.12 SP3">DaVinci Configurator Pro 5.12 (SP3)</a>
			</h2>
			<h3>Miscellaneous Tool Features</h3>
			<ul>
				<li>Detailed SIP license state information</li>
				<li>Derive IpduM/IpduMGeneral/IpduMHeaderByteOrder</li>
				<li>CanNmMsgRepeatMsgInd is now derived from NmEcu.nmRepeatMsgIndEnabled</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>GPT Validators and RAMTST Validator shall only be active for MICROSAR definitions</li>
				<li>Display errors in tooltips with long texts</li>
				<li>Tool freezes when many elements are selected from the Validation View</li>
				<li>Exception when selecting DemEventParameter in Basic Editor</li>
				<li>Not all EIRA TX Signals are mapped as ComMPncSignal</li>
				<li>Commandline generation does not detect missing system description synchronization (RTE59000)</li>
				<li>[V] - annotation at tree node labels does not reflect same state as editors</li>
				<li>Enable the GUI to support correctly the Array mapping to primitive signals within a Record</li>
				<li>Commandline update ends always with command error code 0</li>
				<li>Unhandled event loop exception when starting update with a write protected &apos;Log&apos; folder</li>
				<li>NullPointerException during FrTpMapping</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.12 SP2">DaVinci Configurator Pro 5.12 (SP2)</a>
			</h2>
			<h3>Miscellaneous Tool Features</h3>
			<ul>
				<li>Support multi-selection in Differences View</li>
				<li>Derive the parameters SoAdRxUpperLayerType and SoAdRxUpperLayerType</li>
				<li>Inform user when configuration is updated in Generation - Calculation phase</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>IronPython is not supported on 32 Bit systems</li>
				<li>Naming collisions for ComIPdu, ComSignalGroup and global Pdus when there are distributed in several AR-PACKAGES</li>
				<li>Allow usage of pool version license greater than current tool version</li>
				<li>&apos;Edit Project Settings&apos; button is not disabled in read-only projects</li>
				<li>Grids in Comfort Editors: Editing of boolean values works not as expected</li>
				<li>Confirmation callbacks are not set for GeneralPurposePdu</li>
				<li>Calculation of Ea- and FeeBlockNumber doesn&apos;t work any more</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.12 SP1">DaVinci Configurator Pro 5.12 (SP1)</a>
			</h2>
			<h3>Extensions</h3>
			<ul>
				<li>MCAL update 4.0.7 for derivatives RH850 (D1x)</li>
				<li>Ethernet: Derive remote IP address also when dynamic configuration is enabled</li>
			</ul>
			<h4>Miscellaneous Tool Features</h4>
			<ul>
				<li>DataMapping-Validation and Assistant: Add check for ISignals/ISignalGroups not referenced by the System</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>NullPointerException in validation view when filtering out acknowledged validation results</li>
				<li>Error when loading a dpa-file from a location containing a #-character</li>
				<li>Warning Cfg00024 &apos;Missing reference target&apos; occurs</li>
				<li>FrNmPnEiraCalcEnabled derived with wrong value</li>
				<li>Prevent that PNC EIRA Pdu is derived multiple times</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.12">DaVinci Configurator Pro 5.12</a>
			</h2>
			<h3>Extensions</h3>
			<ul>
				<li>Introduction of DaVinci Configurator LTD</li>
				<li>Validation result acknowledgment for warnings and information</li>
			</ul>
			<h4>Miscellaneous Tool Features</h4>
			<ul>
				<li>Memory Block Editor: parameters re-arranged in form view and grid</li>
				<li>Element usage view: support &apos;Copy Path&apos; to copy the AUTOSAR path to clipboard</li>
				<li>EcucUpdater must be able to process containers/parameters without definition.</li>
				<li>Support LDF Files according to J2602</li>
				<li>Support of AUTOSAR schema validation for input files during project update workflow</li>
				<li>Do not derive /MICROSAR/PduR/PduRBswModules to InitialEcuC</li>
				<li>SoAdSocketRemoteAddress derivation according to ASR 4.2 upstream mapping rules</li>
				<li>CompuScales as init values and conditions in BswM </li>
				<li>Derivation of diagnostic connections out of system description / diagnostic extract</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Input Files Editor: &apos;Variant Merge&apos; relevant folders should be displayed with relative path</li>
				<li>Generation Dialog: Generation target should be saved</li>
				<li>Editing existing annotation text is not possible</li>
				<li>&apos;Open in explorer&apos; command opens wrong directory</li>
				<li>Memory General Editor: Fls sectors do not show error icons</li>
				<li>Menu entry &apos;Switch configuration phase&apos; onyl becomes active after saving the project</li>
				<li>Exception when editing multiple boolean parameters in grid</li>
				<li>PDU Editor: Channel node shows too many PDUs</li>
				<li>New Project Assistant: Vtt-Project-Path and MC-Data show old values even though changed by user</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.11 SP5">DaVinci Configurator Pro 5.11 (SP5)</a>
			</h2>
			<h3>Extensions</h3>
			<h4>Miscellaneous Tool Features</h4>
			<ul>
				<li>Provide possibility to import differences regarding derived Configuration Elements</li>
				<li>Improve pool license handling within DaVinci Configurator</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>User Annotations are not considered by diff and merge feature</li>
				<li>SWC Generation creates non AR conform swCalibrationAccess-Properties for Type-References</li>
				<li>Filtered validation view displays resuls twice and faulty</li>
				<li>Commandline generator in Asr3 use case reports a SIP update warning</li>
				<li>Postbuild Loadable should be defined during Project Setup (Diagnostic-Only support)</li>
				<li>Differences Views don&apos;t display value for objects of type MIReferenceValue</li>
				<li>Clicking on &apos;+&apos; does not expand the node in the DifferencesView</li>
				<li>Instance reference cannot be edited on Japanese Windows systems</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.11 SP4">DaVinci Configurator Pro 5.11 (SP4)</a>
			</h2>
			<h3>Extensions</h3>
			<h4>Diff&amp;Merge</h4>
			<ul>
				<li>Introduction of 3-way-merge including an auto-merge functionality</li>
				<li>Diff&amp;Merge for SystemDescription elements</li>
				<li>Provide filter mechnism for Diff&amp;Merge results</li>
			</ul>
			<h4>Miscellaneous Tool Features</h4>
			<ul>
				<li>Improve Keyboard-Handling in &apos;New module assistant&apos;</li>
				<li>Support list of &apos;ApplicationComponents&apos; folders</li>
				<li>Support post build selectable variance in Issm editor</li>
				<li>Use CMS garbage collector to improve memory performanc</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Inappropriate [V]-decoration in multi instance parameter control</li>
				<li>&apos;Show parent in&apos; item in context menu of differences view fails</li>
				<li>Exception after editing VFB trace function name</li>
				<li>&apos;Memory General&apos; editor overview page does not work correctly on multiple module configurations of &apos;Fls&apos;</li>
				<li>Remove derivation of parameter NmChannelId</li>
				<li>Prevent the import of a module via Module Import if a module is imported by the standard configuration</li>
				<li>Derive the parameters SoAdRxUpperLayerType and SoAdRxUpperLayerType</li>
				<li>Bus Controller: Bus Timing page empty when CanBaudratePrescaler parameter is missing</li>
				<li>Pdus are not multiplied if no PduToFrameMapping exists</li>
				<li>Derive ComTransferProperty only for Tx ComSignals/ComGroupSignals</li>
				<li>Do not start the update workflow if the EcuC file splitter are not reside within .\Config\EcuC</li>
				<li>DaVinci Developer pool license is not recognized as .RTE option for DaVinci Configurator</li>
				<li>Switching the projects configuration phase shall be denied if PostBuildLoadableSupport == false</li>
				<li>Improve performance of ComponentType validation rule</li>
				<li>Handling of empty System Extracts (with ECU instance only)</li>
				<li>Bus Timing editor does not handle time values with non-terminating decimal expansion correctly</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.11 SP3">DaVinci Configurator Pro 5.11 (SP3)</a>
			</h2>
			<h3>Extensions</h3>
			<ul>
				<li>Component Connection Assistant: Improve Automapping-Algorithm</li>
				<li>VTT components are grouped in new domain &apos;Virtual&apos;</li>
				<li>Provide a &apos;Reset column order&apos; command in Grid</li>
				<li>DataMapping Assistant: display the cluster additional to the channel</li>
				<li>Support MCAL for derivatives Rh850F1K </li>
				<li>Support post build selectable variance in Issm editor</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>ComSignalNaming Rule reports erroneously not unique shortname</li>
				<li>BaseEcuc Mappings gets aborted due to missing Controller reference in CommunicationConnector</li>
				<li>NullPointerException in PropertyView when closing variant project</li>
				<li>NmChannelConfig and ComMChannel for Ethernet VLAN derived which does not contain any Nm configuration</li>
				<li>Could not load ARXML files, which contains SHORT-NAME-FRAGMENTS from ASR4.2.2</li>
				<li>Opening the context menu of validation result Cfg00024 results in a &apos;NoSuchElement&apos; exception</li>
				<li>Switching the active variant results in an out-of-synch grid view</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.11 SP2">DaVinci Configurator Pro 5.11 (SP2)</a>
			</h2>
			<h3>Extensions</h3>
			<ul>
				<li>MCAL update 4.0.6  for derivatives RH850_1407 and RH850_1404 (D1M)</li>
			</ul>
			<h3>Performance</h3>
			<ul>
				<li>Reduce memory consumption during background validation.</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Correct calculation of IsoRscan, IsoRlin and IsoVi0pixclk within validation rules for RH850D1x.</li>
				<li>The project last opened is not listed in the recent file list</li>
				<li>Data Mapping Assistant assigns variation point of non-selected variant.</li>
				<li>If the SIP defines no valid derivative, the &apos;Project New Assistant&apos; sets the &apos;Virtual Target&apos; option</li>
				<li>Don&apos;t roll back the SIP migration of all modules in case of an error at a single module.</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.11 SP1">DaVinci Configurator Pro 5.11 (SP1)</a>
			</h2>
			<h3>Note</h3>
			<ul>
				<li>Aladdin dongles used on 32-bit Windows systems are no longer supported. Please use Keyman-dongles instead (<a href="#info">contact support</a>).</li>
			</ul>
			<h3>Extensions</h3>
			<ul>
				<li>Create report from find view result</li>
				<li>&apos;Element usage&apos; view supports &apos;Copy Path&apos;</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>License management: keyman dongle licenses are displayed duplicated within the overview </li>
				<li>Element usage view breaks if an expanded root element is removed from the model </li>
				<li>Do not derive the container DcmDslProtocolRow if all DcmConnections are routed </li>
				<li>Validation for unmapped port should check the mapping in other Variants  </li>
				<li>DataMapping-Assistant: Automapped complex data elements are not expanded and not automapped </li>
				<li>IoHwAb Comfort Editor shows the wrong IoHwAbInitValueType </li>
				<li>SdServer-/SdClientTimers are not derived for SdEventHandlers and SdConsumedEventGroups </li>
				<li>Project Standard Configuration input file cannot be added to two different file sets </li>
				<li>Support post-build-selectable-variance in PncMappings </li>
				<li>Assistant for mapping of production errors to diagnostic events does not work</li>
				<li>Do not derive the parameter HandleIDs for IpduM, LdCom, PduR and FrArTp</li>
				<li>Derive UdpNmMsgCycleTime indepent from Nm Passive Mode</li>
				<li>Wrong calculation of PnFilterMask for large values </li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.11">DaVinci Configurator Pro 5.11</a>
			</h2>
			<h3>Extensions</h3>
			<h4>PDUs Editor redesigned</h4>
			<ul>
				<li>Changed tree structure to display all kinds of PDUs</li>
				<li>Improved overview of PduR routing paths</li>
				<li>Routing Path Assistant to create new routing paths</li>
			</ul>
			<h4>Miscellaneous Tool Features</h4>
			<ul>
				<li>Task Mapping Assistant: filters for easy identification of mapping candidates</li>
				<li>BSW Management Editor: improved display of BswM rules</li>
				<li>Diff&amp;Merge: Filtering of difference results</li>
				<li>Improved behavior of column filter dialog in grid views</li>
				<li>Improved behavior of find view: tolerant search without keywords, harmonized keywords</li>
				<li>Explicit enabling of postbuild support in the project settings</li>
				<li>Postbuild-Selectable support for Project Standard Configurations</li>
				<li>MCU comfort Integration for RH850D1x </li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Module configuration import: values of references are not correctly displayed</li>
				<li>Tree filter isn&apos;t updated when changing shortnames </li>
				<li>&apos;Add Modules&apos; Assistant shows error text but no error has occurred</li>
				<li>Alt+Click doesn&apos;t work in grids in the IOHwAb editor</li>
				<li>Mapping of implementation data types to application data types is not considered for NvBlockDescriptors of NvBlock SWCs</li>
				<li>Partial Networking Editor: tree update missing on module activation/deactivation</li>
				<li>&apos;Element usage&apos; view breaks if an expanded root element is removed from the model </li>
				<li>Endless loop in &apos;Element usage view&apos;</li>
				<li>&apos;Select all&apos; toolbar button in &apos;Remove ECUC File Reference...&apos; assistant does not work</li>
				<li>AsrVersion of split ecuc files do not match the version from the main ecuc file</li>
				<li>Top-down service configuration: wrong activation state of NvM module determined</li>
				<li>Error is reported when creating a new parameter in the multi-instance parameter control</li>
				<li>Module Internal Behavior Editor:  Overview page is not updated when creating a new internal behavior as first action</li>
				<li>NullPointerException in PropertyView when closing variant project</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.10 SP9">DaVinci Configurator Pro 5.10 (SP9)</a>
			</h2>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Module import should notify if an according module does already exist</li>
				<li>Update cannot be started due to missing ECU instance</li>
				<li>Improve pool license handling within DaVinci Configurator</li>
				<li>Instance reference cannot be edited on Japanese Windows systems</li>
				<li>A dongle option license incorrectly activates a DaVinci Configurator PRO license</li>
				<li>Ctrl+Alt+Del is handled as delete in List Views</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.10 SP8">DaVinci Configurator Pro 5.10 (SP8)</a>
			</h2>
			<h3>Fixed Issues</h3>
			<ul>
				<li>User Annotations are not considered by diff and merge feature</li>
				<li>SWCGeneration creates non AR conform swCalibrationAccess-Properties for Type-References</li>
				<li>Input Files Editor: List of found ECU instances is no longer sorted</li>
				<li>Allow usage of pool version license greater than current tool version</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.10 SP7">DaVinci Configurator Pro 5.10 (SP7)</a>
			</h2>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Auto-Merge operation only works on current selection</li>
				<li>DaVinci workspace merge doesn&apos;t start DaVinci Developer with correct projects</li>
				<li>Cannot load project if application SWCs are not disjunct in Application Components folder and Developer workspace</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.10 SP6">DaVinci Configurator Pro 5.10 (SP6)</a>
			</h2>
			<h3>Extensions</h3>
			<h4>Diff&amp;Merge</h4>
			<ul>
				<li>Introduction of 3-way-merge including an auto-merge functionality</li>
				<li>Diff&amp;merge for SystemDescription elements</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Criterion value can not be removed</li>
				<li>Prevent the import of a module via Module Import if a module is imported as Project Standard Configuration</li>
				<li>Edit Variance Assistant throws NullPointerException</li>
				<li>Invalid RTE51028 validation warning occurs</li>
				<li>Project update: Missing error message and abort if EcuC split files are located outside of .\Config\EcuC</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.10 SP5">DaVinci Configurator Pro 5.10 (SP5)</a>
			</h2>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Within an ECU Extract of Systemdescription dangling references pointing into the AUTOSAR_Platform (PlatformTypes in PlatformTypes_AR4.arxml) should not removed during update</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.10 SP4">DaVinci Configurator Pro 5.10 (SP4)</a>
			</h2>
			<h3>Usability Features</h3>
			<ul>
				<li>Task Mapping Editor:display function triggers separated by &apos;Category&apos; and &apos;Condition&apos;</li>
			</ul>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Validation Rules for RH850F1H, RH850F1X, RH850F1M, RH850F1L  - MCAL-Update to 4.1.0 </li>
				<li>Difference Details View: Improved display of added and removed elements </li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Compare and Merge: multiple instances of single-instance parameters created</li>
				<li>Element usage view breaks if an expanded root element is removed from the model </li>
				<li>Values of a variant signal are shown as &apos;multiple&apos; in the ComSignal grid</li>
				<li>DataMapping-Assistant: Automapped complex data elements are not expanded and not automapped </li>
				<li>If at least two NmTxPdus in different clusters / nodes refer an ISignal with the same name, only one ComSignal is created.</li>
				<li>Update workflow aborts with invalid PduTriggering used by an Ethernet Cluster</li>
				<li>IoHwAb Editor shows the wrong IoHwAbInitValueType</li>
				<li>Support Request Package doesn&apos;t consider external file references</li>
				<li>NmChannelConfig and ComMChannel for Ethernet VLAN derived which does not contain any Nm configuration </li>
				<li>Update fails if path to ECU extract file is not relative to project directory or if it is not absolute</li>
				<li>Implement new AR 4.2.1 mapping rule for SoAdPduHeaderEnable</li>
				<li>Bsw Module Internal Behavior Editor: Renaming of schedulable entity leads to wrong RTE code generation </li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.10 SP3">DaVinci Configurator Pro 5.10 (SP3)</a>
			</h2>
			<h3>Usability Features</h3>
			<ul>
				<li>Diff&amp;Merge: Improve filtering of differences</li>
				<li>Improve grid quick filter definition</li>
			</ul>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Validation rules for RH850D1x</li>
				<li>DataMappings derived from upstream system description are not read-only any more</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Acceptance filter optimization creates extended id filters for standard id channel</li>
				<li>ComSignalNaming Validation Rule reports erroneously not unique shortname</li>
				<li>External ECUC file reference is lost when performing an update</li>
				<li>ClassCastException when expanding &apos;Show referenced item in&apos; sub menu in element usage view</li>
				<li>Top down service config unnecessarily activates NvM module</li>
				<li>Correction of BlockNumber calculation etc in 3rd-party-Fee/-Ea</li>
				<li>VTT: Rename of Container in HW-Module will not be synchronized</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.10 SP2">DaVinci Configurator Pro 5.10 (SP2)</a>
			</h2>
			<h3>Breaking Changes</h3>
			<ul>
				<li>Relevant for projects with RTE configuration: Projects saved with CFG PRO 5.10 (SP2) lead to error message &apos;RTE01053 Invalid SwComponentInstance container&apos; when being generated with an older version of CFG PRO 5.10</li>
			</ul>
			<h3>Extensions</h3>
			<h4>Tool Features</h4>
			<ul>
				<li>Postbuild-Selectable support for project standard configurations</li>
				<li>New &apos;Element Usage&apos; view allows tracking references to configuration elements (see &apos;Element Usage&apos; command within context menu of configuration elements)</li>
			</ul>
			<h3>Usability Features</h3>
			<ul>
				<li>Activate multiple modules at once within project settings editors module page</li>
				<li>Import Assistant: change default value from &apos;Replace&apos; to &apos;Merge&apos;</li>
				<li>Decorate the name of PduRRoutingPaths with the direction</li>
				<li>Input Files Editor: display absolute paths in diagnostic file set configuration dialog</li>
				<li>Improve &apos;Create New Service Port&apos; assistant: detect modified DaVinci Developer workspace in advance</li>
			</ul>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Validation rules for RH850F1x - update for MCAL Ver4.01.06.001</li>
				<li>Derive CanTp from CanTpConfig which refers to J1939Cluster</li>
				<li>Derive SdInstanceUnicastRxPdu and SdInstanceMulticastRxPdu</li>
				<li>Support the ServiceKind attribute instead of Admin-Data tag</li>
				<li>New validation rule: set the FeeDeviceIndex of each FeeBlock</li>
				<li>Remove EcuC split file directory when according module configuration is deleted</li>
				<li>Automatic creation of schema-valid shortnames for new containers</li>
				<li>A message dialog is shown when custom workflow step or SWC-T generation failed</li>
				<li>DVCfgCmd.exe: command line switch to support a cdd file as input</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Top-down service configuration determines wrong activation state of NvM module</li>
				<li>ECUC_BswM_00814: BswMLogicalExpressions with a single nested element must not have a BswMLogicalOperator</li>
				<li>Tool exception when handling Soad socket connection group mapping</li>
				<li>Update of FrTrcv internal behavior fails if BswModuleEntry already exists</li>
				<li>File PlatformTypes.arxml is missing in support request package</li>
				<li>Breadcrumb in basic editor isn&apos;t filtered for active variant</li>
				<li>Prevent error message RTE49999 during software component template generation</li>
				<li>Reference to VTT-project in .dpa file is lost during update workflow</li>
				<li>&apos;Select all&apos; toolbar button in &apos;File&apos; &gt; &apos;Remove ECUC File Reference...&apos; assistant does not work</li>
				<li>Update workflow fails when diagnostic state description file is removed</li>
				<li>UdpNm NmUserDataPdu missing in Com configuration</li>
				<li>Incorrect derivation of the parameter NmRemoteSleepIndEnabled and NmBusSynchronizationEnabled</li>
				<li>Packed NvBlockDataMappings are not displayed correctly in Ecu Software Components editor</li>
				<li>Parameter which exists in two choices of a choice container doesn&apos;t show multiple definitions in &apos;properties view&apos;</li>
				<li>Exception when setting &apos;Target type&apos; in existing project from &quot;&quot; to &apos;Real Target&apos;</li>
				<li>Removing the developer workspace path in the PSE breaks the update functionality</li>
				<li>Fee Optimization Assistant: page not updated when back button was used before</li>
				<li>Partial networking editor doesn&apos;t update tree on module activation/deactivation</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.10 SP1">DaVinci Configurator Pro 5.10 (SP1)</a>
			</h2>
			<h3>Extensions</h3>
			<h4>Tool Features</h4>
			<ul>
				<li>BSW Management Editor: Added general settings page</li>
				<li>Global switch for editor specific number format switching</li>
				<li>top down service config must parse RoleBasedPortAssignements of NvBlockNeeds at NvBlockDescriptors</li>
			</ul>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Validation Rules for RH850F1x - Update for MCAL Ver4.01.06.001 </li>
				<li>Do not derive parameter CanTpTxNSduId, PduRDestPduHandleId and PduRSourcePduHandleId in InitialEcuC</li>
				<li>Support 3rd party modules within Project Standard Configuration </li>
				<li>Set the UpperlayerPDU for CanIfXxPdus to PDUR for ContainerIPdus which are part of MultiplexedIPdus </li>
				<li>Support of EiraRxPdus for passive NM nodes</li>
				<li>Project Settings Editor:  ECUC file references add and remove functionality </li>
				<li>Command line generation module selection according .dpa selection</li>
				<li>FeeDeviceIndex is set automatically for each FeeBlock</li>
				<li>Do not derive the parameters PduRDestPduHandleId and PduRSourcePduHandleId in InitialEcuC</li>
				<li>Insert Path Selection Button for &apos;vtt project&apos; in Project Settings Editor </li>
				<li>Reuse manually created SwConnectorPrototypes in top down service config </li>
				<li>Insert Path Selection Button for &apos;vtt project&apos; in Project Settings Editor </li>
			</ul>
			<h3>Performance</h3>
			<ul>
				<li>Speed up closing projects even if they have a huge amount of validation results</li>
				<li>Improved validation performance in the command line use case </li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>BaseEcucGenerator can fail when CommunicationConnector is not connected to a PhysicalChannel</li>
				<li>Postbuild-Selectable: For variant NmUserDataPdus the global PDUs are derived invariant</li>
				<li>Validation error AR-ECUC02008 occurs for IpduM Pdu without dynamic part</li>
				<li>Reset to derived value is not working for multi instance reference parameter </li>
				<li>CanNmRange is not completely derived</li>
				<li>Null Pointer Exception in DataMappingValidationRule when determining signal direction for nested data mappings with invalid root mappings </li>
				<li>Fix calculation for FrNmUserDataTxPdu length for AR 4.2.1 System Extracts</li>
				<li>SignalTriggerings without Signal reference aborts the Update Workflow</li>
				<li>Gateway PduMappings without source or target PduTriggering references aborts the Update Workflow</li>
				<li>Console Application: The commandline arg --modulesToGenerate shall accept an empty &quot;&quot; string, like --extGenStepsToGenerate</li>
				<li>Validation Error RTE59000 does not disappear after solving</li>
				<li>Read-Only projects cannot be opened due to a Null Pointer Exception</li>
				<li>Null Pointer Exception in SocketConnection mapping</li>
				<li>Update of diagnostic data fails when SIP is located within project folder </li>
				<li>Report an error when Sip License cannot be found </li>
				<li>Projects with .dev workspace cannot be loaded</li>
				<li>Null Pointer Exception when deriving DcmDslProtocol for DcmDslConnections </li>
				<li>Too many PNC EIRA/ERA Pdus are derived</li>
				<li>Enabling FullCan on a variant message creates a non-variant hardware object </li>
				<li>Null Pointer Exception when deleting an OsTask with previous opened FormPage with ResourceLocks </li>
				<li>Automatically created string parameters have no value if the BSWMD specifies no default </li>
				<li>Modules without MICROSAR or AUTOSAR definition reference should be displayed with their definition reference  </li>
				<li>Postbuild-Selectable: Shortnames of symbolic name value containers should be reused over variants</li>
				<li>DataTypeMappingSet support for ParameterSoftwareComponents (Calibration) is missing </li>
				<li>Tree filter isn&apos;t updated when changing shortnames</li>
				<li>Incorrect derived SoAdPduRoutes and SoAdSocketRoute in AR 4.2.1 System Extracts</li>
				<li>&apos;Auto-resize columns&apos; causes exceptions if columns are hidden in a GridView</li>
				<li>SoAdSocketConnection(-Group)s not derivied if the SocketConnection(-Bundle) is defined as multicast connection</li>
				<li>Internal Behavior-Editor: Refresh the module list automatically </li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.10">DaVinci Configurator Pro 5.10</a>
			</h2>
			<h3>Extensions</h3>
			<h4>Tool Features</h4>
			<ul>
				<li>New editor: Module Internal Behavior Editor</li>
				<li>Support hyperlinks in text fields such as descriptions or annotations</li>
				<li>Improved import of ECUC files (import mode &apos;Replace&apos;)</li>
				<li>Simplified access to the RTE SWC template generation</li>
				<li>Configuration of the TCP/IP stack according to AR 4.2.1</li>
				<li>ECU Software Components Editor: support of data prototype mapping</li>
				<li>Bottom-up service port design and service mapping</li>
				<li>Allow split ECUC files to be stored in individual folders</li>
				<li>&apos;Edit variance&apos; supported for data mappings</li>
				<li>Support for directory supervision as extension of the file change supervision</li>
				<li>Support of different strategies for writing NV data in Nv Block SWCs</li>
				<li>Conditional generation supported for external generation steps</li>
				<li>Project Settings Editor: Mass selection/deselection of all external generation steps</li>
				<li>BSW Management Editor: auto configuration GUI improvement</li>
				<li>BSW Management Editor: support new types of mode request ports and new types of actions</li>
				<li>Diagnostic Events Editor and Diagnostic Event Data Editor: support of WWH-OBD parameters</li>
				<li>Bulk execution of specific solving actions (solving action groups)</li>
				<li>Command line support for system description as input file</li>
				<li>Grid views: status row showing current selection state and filter setting</li>
				<li>Grid views: persistent grid settings (e.g. column width)</li>
				<li>Updated support of Freescale i.MX6SLX</li>
				<li>Generate Dialog displays the current target type (VIRTUAL, REAL)</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Validation error &apos;Duplicate short names&apos; does not display concerned configuration element</li>
				<li>Editor tree filters in BSW Management Editor and Watchdogs Editor does not work for newly created items</li>
				<li>For variant NmUserDataPdus the global PDUs are derived invariant</li>
				<li>Invalid ComPncSignals can be derived in a variant project</li>
				<li>Allow post-build-selectable configuration variant without evaluated variants for non-MICROSAR modules</li>
				<li>Support ADMIN-DATA for UDS connection</li>
				<li>Generation of BaseEcuC can fail when CommunicationConnector is not connected to a PhysicalChannel</li>
				<li>Cannot set manual reference target if no reference targets are available</li>
				<li>CFG5 doesn&apos;t support the ECUC-MULTILINE-STRING-PARAM-DEF properly</li>
				<li>Change of active variant causes exception in CAN bus timing configuration</li>
				<li>ComM is not derived for J1939Cluster without ISignalIPdus</li>
				<li>Commandline parser not working when -i option is used</li>
				<li>The commandline arg --modulesToGenerate shall accept and empty &quot;&quot; string, like --extGenStepsToGenerate</li>
				<li>Fixed calculation for FrNmUserDataTxPdu lenght for AR 4.2.1 System Extracts</li>
				<li>Gateway PduMappings without source or target PduTriggering references aborts the Update Workflow</li>
				<li>Null Pointer Exception when deriving DcmDslProtocol for DcmDslConnections</li>
				<li>Set the upperlayer for CanIfXxPdus to PDUR for ContainerIPdus which are part of MultiplexedIPdus</li>
				<li>Validation error AR-ECUC02008 occurs for IpduM Pdu without dynamic part</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.9 SP7">DaVinci Configurator Pro 5.9 (SP7)</a>
			</h2>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Access to JNA library not granted on PCs with local JNA versions</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.9 SP6">DaVinci Configurator Pro 5.9 (SP6)</a>
			</h2>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Extend the command line Exporter to combine post-build variant export with exporter IDs</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>NullPointerException when SipLicense.lic is missing</li>
				<li>AUTOSAR files merger incorrectly redirects references when automatically refactoring elements</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.9 SP5">DaVinci Configurator Pro 5.9 (SP5)</a>
			</h2>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Improve resolving of differences in Diff&amp;Merge</li>
				<li>Support of Hierarchical grid filter </li>
				<li>Comfort Integration for RH850F1H - MCAL-Update to 4.1.0  </li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Module Configuration Import: Values of references are not correctly displayed</li>
				<li>Validation rule RTE59000 appears but does not disappear after solve</li>
				<li>Element usage view breaks if an expanded root element is removed from the model </li>
				<li>Acceptance filter optimization creates extended id filters for standard id channel </li>
				<li>External ECUC file reference is lost when perfomring an update</li>
				<li>Difference decorations not shown in tree</li>
				<li>SdServer-/SdClientTimers are not derived for SdEventHandlers and SdConsumedEventGroups </li>
				<li>NmChannelConfig and ComMChannel for Ethernet VLAN derived which does not contain any Nm configuration  </li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.9 SP4">DaVinci Configurator Pro 5.9 (SP4)</a>
			</h2>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Filter inconsistent empty rows from task mapping pages (assistant and editor)</li>
				<li>Support EIRA/ERA Pdus in a merged system extract and legacy extract use case</li>
				<li>Support multi selection for &apos;Edit Variance&apos; command</li>
				<li>Support merge of non-variant module configurations in variant projects</li>
			</ul>
			<h3>Usability Improvements</h3>
			<ul>
				<li>Disclaimer dialog does not block loading of the project</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>UdpNm UserData missing in Com</li>
				<li>Module activation causes an OutOfBoundsException</li>
				<li>Nm parameters not correctly derived for Ethernet-clusters with multiple Connectors</li>
				<li>Incorrect handling of CanIdRanges defined in CanFrameTriggerings</li>
				<li>CDD-file is duplicated after performing input data update workflow</li>
				<li>Validation error PDUR07030 occurs for ComIPdu which is created instead of a IpduMXxPathway with only static part</li>
				<li>Ecu-Extract elements are removed during DCF-Reload</li>
				<li>AUTOSAR version of split EcuC files do not match the version of the main EcuC file</li>
				<li>GenDataVtt, Variance, Pending-Update is missing in support request package</li>
				<li>Reference to VTT-Project is lost during update workflow</li>
				<li>&apos;Align differences&apos; multiplies the existing project standard configuration entries in the .dpa-file</li>
				<li>CanNmUserData Pdu is missing when a CanNmRange is defined for the CanNmNode</li>
				<li>Incorrect calculation of NmUserDataPdu length and ComSignal bit position</li>
				<li>Variant merger creates conflicting variation point configuration</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.9 SP3">DaVinci Configurator Pro 5.9 (SP3)</a>
			</h2>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Validation Rules for RH850F1x - Update for MCAL Ver4.01.06.001 </li>
				<li>Do not derive parameter CanTpTxNSduId, PduRDestPduHandleId and PduRSourcePduHandleId in InitialEcuC</li>
				<li>Support 3rd party modules within Project Standard Configuration </li>
				<li>Set the UpperlayerPDU for CanIfXxPdus to PDUR for ContainerIPdus which are part of MultiplexedIPdus </li>
				<li>Support of EiraRxPdus for passive NM nodes</li>
				<li>Project Settings Editor:  ECUC file references add and remove functionality </li>
				<li>Command line generation module selection according .dpa selection</li>
			</ul>
			<h3>Performance</h3>
			<ul>
				<li>Speed up closing projects even if they have a huge amount of validation results</li>
				<li>Improved validation performance in the command line use case </li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>BaseEcucGenerator can fail when CommunicationConnector is not connected to a PhysicalChannel</li>
				<li>Postbuild-Selectable: For variant NmUserDataPdus the global PDUs are derived invariant</li>
				<li>Validation error AR-ECUC02008 occurs for IpduM Pdu without dynamic part</li>
				<li>Reset to derived value is not working for multi instance reference parameter </li>
				<li>CanNmRange is not completely derived</li>
				<li>Null Pointer Exception in DataMappingValidationRule when determining signal direction for nested data mappings with invalid root mappings </li>
				<li>Fix calculation for FrNmUserDataTxPdu length for AR 4.2.1 System Extracts</li>
				<li>SignalTriggerings without Signal reference aborts the Update Workflow</li>
				<li>Gateway PduMappings without source or target PduTriggering references aborts the Update Workflow</li>
				<li>Console Application: The commandline arg --modulesToGenerate shall accept an empty &quot;&quot; string, like --extGenStepsToGenerate</li>
				<li>Validation Error RTE59000 does not disappear after solving</li>
				<li>Read-Only projects cannot be opened due to a Null Pointer Exception</li>
				<li>Null Pointer Exception in SocketConnection mapping</li>
				<li>Update of diagnostic data fails when SIP is located within project folder </li>
				<li>Report an error when Sip License cannot be found </li>
				<li>Projects with .dev workspace cannot be loaded</li>
				<li>Null Pointer Exception when deriving DcmDslProtocol for DcmDslConnections </li>
				<li>Too many PNC EIRA/ERA Pdus are derived</li>
				<li>Enabling FullCan on a variant message creates a non-variant hardware object </li>
				<li>Null Pointer Exception when deleting an OsTask with previous opened FormPage with ResourceLocks </li>
				<li>Automatically created string parameters have no value if the BSWMD specifies no default </li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.9 SP2">DaVinci Configurator Pro 5.9 (SP2)</a>
			</h2>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Support of Pool-license model</li>
				<li>Support Projects without communication</li>
				<li>Support of Project Standard Configuration via command line</li>
				<li>Automatic execution of custom workflow steps after code generation</li>
				<li>Command line support for SystemDescription as input file</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Error Icons for MultiInstance Parameter are either shown in Basic Editor or Comfort Editor, not both</li>
				<li>Removing data mappings for deleted Software Components via AutoSolve does not work</li>
				<li>Persistency raises an error for duplicate content in &apos;SW-DATA-DEF-PROPS&apos;</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.9 SP1">DaVinci Configurator Pro 5.9 (SP1)</a>
			</h2>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Derive new AR 4.2.1 parameter in IpVx module</li>
				<li>Derive Pdu references for the Sd module</li>
				<li>Quick enable/disable of external generator steps in generation sequence lists</li>
				<li>Postbuild selectable variance assistant. Supporting the &apos;Edit Variance...&apos; command functionality</li>
			</ul>
			<h3>Domain Runtime System</h3>
			<ul>
				<li>Editor for Data Prototype Mapping</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Expand generation sequence tree when using typing filter</li>
				<li>Validator error CAN02012 occurs after update</li>
				<li>Improve error message for project load errors during SIP update</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.9">DaVinci Configurator Pro 5.9</a>
			</h2>
			<h3>Extensions</h3>
			<h4>Tool Features</h4>
			<ul>
				<li>Support references to EcuC file fragments</li>
				<li>Scripting interface workflow integration</li>
				<li>Comfort Integration RH850P1x-C</li>
				<li>Support for multiple CAN driver</li>
				<li>MultiSelection in ValidationView</li>
				<li>Add CommandLine Option -x to exculde module configurations from generation</li>
				<li>Support for Diagnostic configuration via ECUC standard configuration</li>
				<li>Support CAN-FD Mode 2 for communication and diagnostics</li>
				<li>Support of GeneralPurposeIPdu for Xcp</li>
				<li>Support of Union Datatypes in the Rte</li>
				<li>Support of J1939Rm</li>
				<li>Optimized workflow between DaVinci DEV and DaVinci Configurator</li>
				<li>TaskMapping Assistant: option to combine function triggers</li>
				<li>Support making a non-variant project variant without using input files</li>
				<li>Support enhanced configuration of service-oriented communication according to ASR 4.2.1</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>External Generation Steps lost after close and reload of project</li>
				<li>Rendering issues with [V] marker in grids in Postbuild-Selectable projects</li>
				<li>Watchdogs editor doesn&apos;t show new instances if a new Wdg module instance is activated</li>
				<li>ComSignals not created correct for bi-directional Signals</li>
				<li>DPA file shall not automatically be saved during project load</li>
				<li>Loading projects with missing folders (specified in dpa file) lead to a pop-up dialog</li>
				<li>&apos;Edit variance&apos; is not displayed for elements with variation points in projects without postbuild selectable support</li>
				<li>ECU management editor: EcuMWakeupSourceMask references are not set correctly</li>
				<li>Slave-to-Slave LinIfFrame is not derived on Master Node</li>
				<li>Property view should display if an object supports variance</li>
				<li>CanIfRxPduRef not derived for Pdus with multiple FrameTriggerings</li>
				<li>DaVinci Developer executable cannot be selected in Project Settings Editor</li>
				<li>J1939DcmIPdus with SA/DA 0xFE are not bound to J1939Dcm/J1939Rm</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.8 SP5">DaVinci Configurator Pro 5.8 (SP5)</a>
			</h2>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Difference decorations not shown in tree</li>
				<li>Update via CommandLine: AdditionalBSWMD files are not supported for update with commandline</li>
				<li>Read-Only projects cannot be opened due to a NullPointerException</li>
				<li>RTE59000 appears but does not disappear after solve</li>
				<li>EcucUpdater does not consider the index attribute &lt;INDEX&gt; of a container</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.8 SP4">DaVinci Configurator Pro 5.8 (SP4)</a>
			</h2>
			<h3>Extensions</h3>
			<h4>Tool Features</h4>
			<ul>
				<li>Provide an .ini-switch for CFG5.PRO floating licenses itself</li>
				<li>Support of Project Standard Configuration via command line (option: --psc)</li>
			</ul>
			<h3>Performance</h3>
			<ul>
				<li>Duplicating Dem Events allocates too much heap</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Missing Ethernet signal routings after project update</li>
				<li>Incorrect derivation of ComSignalGroups created for MultiplexedPdus with multiple triggerings</li>
				<li>Invalid short names generated when the System Extract contains elements ending with a underscore</li>
				<li>Removing data mappings for deleted Software Components via AutoSolve does not work. RTE54002-Validation Errors appears in Validation view</li>
				<li>RTE generator output doesn&apos;t reflect all model changes when re-generating a 2nd time</li>
				<li>Incompatible CanTp and PduR due to different AR-versions in BSWMD- and InternalBehavior-files during SIP upgrade</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.8 SP3">DaVinci Configurator Pro 5.8 (SP3)</a>
			</h2>
			<h3>Extensions</h3>
			<h4>Tool Features</h4>
			<ul>
				<li>BSW Management Editor: Support new mode request ports of type BswMLinScheduleEndNotification</li>
				<li>Extended calculation for CanNmUserDataTxPdu lenght for AR 4.2.1 System Extracts</li>
				<li>Comfort Integration RH850/F1H</li>
			</ul>
			<h3>Performance</h3>
			<ul>
				<li>Decouple decoration calculation from validation view display for large configuration</li>
				<li>Improve performance of grid base implementation</li>
				<li>Improve performance when solving actions</li>
				<li>Decrease memory allocation</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Unexpected RTE51001 Validation error in Cfg5 after syncing changes in DEV Workspace files</li>
				<li>Generation Result Dialog filters out too many generators</li>
				<li>CanIdRange is not derived correct for CanNm PDUs</li>
				<li>Exception when opening context menu of multiselected lines in filtered Grids</li>
				<li>False negative validation warning in base validation of bitfield texttable compu method</li>
				<li>Solving Rte59000 fails with an IllegalArgumentException</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.8 SP2">DaVinci Configurator Pro 5.8 (SP2)</a>
			</h2>
			<h3>Extensions</h3>
			<h4>Tool Features</h4>
			<ul>
				<li>Separate generation step for VTT generators</li>
				<li>Postbuild-Loadable: Remove hash values as postfix from signal groups and group signals</li>
				<li>Task Mapping Assistant: option to combine function triggers</li>
				<li>Mcu Editor &amp; validation: support update of RH850 P1x - new version E403</li>
				<li>Mcu Editor &amp; validation: support update of Mpc560xB - new version 1.0.1</li>
				<li>Change algorithm for assigning synthetic user data ComIPdu derived from a NmPdu to a ComIPduGroup</li>
				<li>New editor for VAB projects: Infrastructure Subset Manager Editor</li>
				<li>Support of bitfield data types</li>
				<li>Full schema support of AUTOSAR 4.2.1</li>
				<li>Project Update: execution of workflow scripts for system extract modification</li>
			</ul>
			<h4>Usability Enhancements</h4>
			<ul>
				<li>Validation View: multiselection of Preferred Solving Actions</li>
				<li>Change detection and reload of DCF workspace</li>
			</ul>
			<h3>Information</h3>
			<ul>
				<li>Tool title changed in case of OEM Post-Build Update SIP license: displayed as &apos;DaVinci Configurator SIP.PB&apos;</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Postbuild-Selectable: Rendering issues with [V] marker in grids</li>
				<li>Runtime System Settings Editor: Missing OsHooks Container does not show &apos;Container is missing...&apos; Message</li>
				<li>Wrong RTE51035 validation error about init values for enumerations</li>
				<li>NullPointerException in Reference Selection Control in &apos;Manual Only&apos; mode</li>
				<li>Widget-is-disposed error after undo</li>
				<li>OsIsrUseSpecialFunctionName is greyed by OsIsrConfigurationService, even if not created by the service</li>
				<li>ExtGen-Steps lost after close and reload of project</li>
				<li>Display ComMChannel name instead of CanSMManagerNetwork name (added missing definitions).</li>
				<li>NullPointerException when closing the &apos;Configure Mode Request Ports&apos; assistant two times</li>
				<li>Duplicating entries in an action list shows an error dialog</li>
				<li>BaseEcuC: CanNmRange is not derived if no CanNmRxPdu exists</li>
				<li>DPA file always saved during project load</li>
				<li>NullPointerException occurs for only LdCom projects</li>
				<li>Validation Rules for Mpc560xp (Stm Variante) - Correct calculation for Mcu_CMU1_CLOCK - New Utils contain function to distinguish &apos;AUX1 supported&apos; and &apos;AUX1 equals System Clock&apos; - CMU1 Frequency calculation corrected</li>
				<li>COM90034 occurs, ComGwMappings not derived correct for SignalGroup mappings</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.8 SP1">DaVinci Configurator Pro 5.8 (SP1)</a>
			</h2>
			<h3>Features</h3>
			<ul>
				<li>Implement Resource change supervision: detailed action feedback</li>
				<li>Derive ProtocolType for ISOBUS TPs</li>
				<li>Comfort editor for Transport Protocol LIN</li>
				<li>Support of project update with external file references within dpa and DaVinci Developer workspace</li>
				<li>Basic schema support of AUTOSAR 4.2.1</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Tree-Tooltip: When children errors exist, own errors are not fully displayed in tooltip</li>
				<li>Create only one DemEvent for variant DemEventParameterRefs</li>
				<li>ECU extractor: extract ISignalIPdus of static and dynamic parts of extracted MultiplexedIPdus</li>
				<li>RTE freeze file contains FIBEX-ELEMENT-REF-CONDITIONAL and UUID diffs</li>
				<li>Multi instance parameter part: in place editing causes NullPointerException</li>
				<li>Project new assistant uses incorrect VTT project extension</li>
				<li>LIN Controller editor does not display &apos;Channel Wakeup Support&apos; parameter correctly</li>
				<li>Modifications on DCF file are not reported to the user</li>
				<li>Workflow Diagnostic Import: LegacyImportSignal flag is not correctly evaluated</li>
				<li>RTE freeze checksum calculation differs for ignored objects</li>
				<li>Validation Rules for Mpc560xp (Stm Variante) - Correct calculation for Mcu_CMU1_CLOCK</li>
				<li>Inplace Editing does not work for Multi-Instance Callback Parameters</li>
				<li>Link-Issues in Signals-Editor and PDUs-Editor</li>
				<li>Show [V] within grid in status column instead of first column</li>
				<li>Exception when creating new EcuMSleepModes using ECU Management editor</li>
				<li>GUI issues in mode management domain editors</li>
				<li>Wrong persistency export filter used for the RTE freeze file (comm contained)</li>
				<li>Developer executable cannot be selected in Project Settings</li>
				<li>Validation Rule DET80000 is not enabled if non-MICROSAR Det module is activated</li>
				<li>Communication Editor does not work without activated COM-Module</li>
				<li>Name of parent truncated in reference parameter control</li>
				<li>Tooltip in trees: When children errors exist, own errors are not fully displayed in tooltip</li>
				<li>ExclusiveArea always shows EaBlockId</li>
				<li>Multi-Editing Grid: Entering non-alphanumerical hides input field in multi-editing dialog</li>
				<li>CFG5 cannot create new projects on network shares</li>
				<li>Modules are not considered by &apos;DetActivationCheck&apos;</li>
				<li>Displayed paths to definition files</li>
				<li>Nondeterministic display of Non-Existent-Parameter Icon</li>
				<li>Update Utility hides some update errors</li>
				<li>Undo-Toolbar-Button is not updated after Configuration Phase switch</li>
				<li>Invalid Connector Validation is ignored by consistency for PR-Ports</li>
				<li>The --genArg parameter of DVCfgCmd overwrites previous values without warning </li>
				<li>The --genArg parameter of DVCfgCmd doesn&apos;t report errors when argument &amp; value are missing</li>
				<li>Wrong order of Ref-Paramter after Cdd Update</li>
				<li>Implement usage of new naming rule in Com mappings</li>
				<li>&apos;ReadOnly&apos; parameter can be deleted, however it cannot be created again.</li>
				<li>ProjectStandardConfiguration: Adaptation of ComMChannel name</li>
				<li>WakeUp triggers not filtered from &apos;Create WakeUp source&apos; dialog</li>
				<li>Watchdogs editor doesn&apos;t show new instances if a new Wdg module instance is activated</li>
				<li>Choice reference controls are not updated after changing reference target</li>
				<li>Display of strange error if .dpa Derivative tag contains blanks</li>
				<li>ComSignals not created correct for bi-directional Signals</li>
				<li>Wrong editor representation of more than one instance of 1:1 parameters</li>
				<li>Gateway routing paths not derived for Ethernet Pdus</li>
				<li>Properties view isn&apos;t updated if variance state of the currently selected element changes</li>
				<li>CommunicationElements of nested complex data elements are not completely expanded even root is mapped to SignalGroup</li>
				<li>Removing the last delegation port data mapping does not revalidate open delegation ports</li>
				<li>Report generation overwrites existing reports without warning</li>
				<li>Report cannot be generated if SIP path contains special characters</li>
				<li>Report generates .xml file as well</li>
				<li>Unhandeld Event loop exception when deleting a ScheduleTable container in Os Configuration Editor</li>
				<li>Mcu Comfort Editor for Mpc560xp platforms: enumeration values do not match definition</li>
				<li>Initialization editor: provide possibility for creating InitFunctions for outdated or non-MICROSAR modules</li>
				<li>Project update fails when using &apos;Ecu-C only&apos; update option with existing project</li>
				<li>State Description field is not grayed when specifying a CDD file</li>
				<li>EcuMWakeupSourceMask references are not set correctly</li>
				<li>BaseEcucGenerator ignores project standard configuration files if no System Extract is used</li>
				<li>PDUs editor: configuration of a different value in each variant for a parameter is not possible</li>
				<li>Commandline: Enable &apos;OnlyEcuc&apos; Update mode for cmdUpdate</li>
				<li>Copy of files with space characters in path doesn&apos;t work correctly</li>
				<li>Modifying the access rights of project file leads to corrupted user message</li>
				<li>Update Workflow fails if input file is read-only and is used more then once per variant</li>
				<li>Multi-Triggering: too much Global Pdus are derived for Pdus with multiple FrameTriggerings</li>
				<li>Watchdogs editor&apos;s configuration tab tree control is not updated</li>
				<li>Allow GUI thread access during workbench startup</li>
				<li>MemoryLeak in UI Update-Workflow</li>
				<li>Value change in post-build phase fails for PB-L/S parameter with variant-multiplicity == false</li>
				<li>&apos;Make path relativ&apos; operation doesn&apos;t work for files on other drives</li>
				<li>Slave-to-Slave LinIfFrame is not derived on Master Node</li>
				<li>SWCGeneration: All DEM Ports are removed after SIP-Update</li>
				<li>Validation Error PDUR04001 occurs</li>
				<li>Multi-Triggering: CanIfRxPduRef not derived for Pdus with multiple FrameTriggerings</li>
				<li>Misleading error message during phase switch when a container definition is missing</li>
				<li>Create new state description dialog displays the wrong file path</li>
				<li>Assistants &apos;Create support request package&apos; &amp; &apos;Compare and Merge Project&apos;</li>
				<li>Link-Issues in Signals-Editor and PDUs-Editor</li>
			</ul>
			<h3>Performance</h3>
			<ul>
				<li>Increase performance of validation rules of Base Services Domain in variant projects</li>
				<li>Fixed slow performance during update</li>
				<li>Improve model access performance of invariant projects</li>
				<li>Improve performance of PostBuildSelectable data model handling</li>
				<li>Improve scaling on large uuid updates</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.8">DaVinci Configurator Pro 5.8</a>
			</h2>
			<h3>Postbuild-Selectable ECUs</h3>
			<ul>
				<li>Definition of variation criteria and variants</li>
				<li>Configuration of several input file sets per variant</li>
				<li>Pre-defined variant criteria for handling communication variants and diagnostic variants</li>
				<li>Central selection of the variant to be displayed</li>
				<li>Annotation of variance in the Basic Editor and Configuration Editors</li>
				<li>Display of validation messages per variant</li>
			</ul>
			<h3>Workflow</h3>
			<ul>
				<li>Ecu instance can be changed during update of input files</li>
				<li>Update DEV workspace without user interaction</li>
				<li>Project Standard Configuration: support of annoted system description references</li>
				<li>Support of CAN FD input files</li>
			</ul>
			<h3>Option VTT</h3>
			<ul>
				<li>Support of parallel configuration of real target MCAL and vVIRTUALtarget MCAL</li>
				<li>Project setup for convenient generation of vVIRTUALtarget project</li>
				<li>Installation of vVIRTUALtarget Basic via DaVinci Configurator External Components Setup</li>
			</ul>
			<h3>Domain Mode Management</h3>
			<ul>
				<li>Initialization Editor: configuration of BswM init action list</li>
				<li>ECU Management Editor: sleep/wakeup configuration integrated</li>
				<li>BSW Managemet Editor: usability improvements</li>
			</ul>
			<h3>Domain Runtime System</h3>
			<ul>
				<li>ECU Software Components Editor: display of service needs</li>
			</ul>
			<h3>Domain Diagnostics</h3>
			<ul>
				<li>Diagnostic Data Identifiers Editor: new editor for displaying and editing the Dcm DIDs and data objects</li>
			</ul>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Project Settings Editor: modification of all project settings after project creation</li>
				<li>Improved performance of report generator</li>
				<li>Option MD: SDK for developing module plug-ins</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.7 SP4">DaVinci Configurator Pro 5.7 (SP4)</a>
			</h2>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Memory General Editor: Not existing Fls module leeds to an empty details part</li>
				<li>NPE during project update when LdCom module is active</li>
				<li>NoSuchElementException in case of dangling references in system description during update</li>
				<li>Message dialog after project open shows allways unresolved file and directory</li>
				<li>Multi instance parameter control: in place editing causes NullPointerException</li>
				<li>Multiline text control doesn&apos;t accept line separators</li>
				<li>NullPointerException when closing the &apos;Configure Mode Request Ports&apos; assistant two times</li>
				<li>Watchdogs editor&apos;s configuration tab tree control is not updated</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.7 SP3">DaVinci Configurator Pro 5.7 (SP3)</a>
			</h2>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Do not change files if content is not changed</li>
				<li>AUTOSAR standard module activation shall use BSW-implementation if available</li>
				<li>Support file extension epd und bmd for BSWMD-Files</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.7 SP2">DaVinci Configurator Pro 5.7 (SP2)</a>
			</h2>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Support manufacturer specific legacy formats</li>
				<li>Support change of project settings in DPA file</li>
			</ul>
			<h3>Domain Base Services</h3>
			<ul>
				<li>Update Comfort Integration TC27xB</li>
				<li>Comfort Integration TC26xBA</li>
				<li>Comfort Integration Mpc560xp (Stm Variante)</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>The switch --extGenStepsToGenerate &quot;&quot; does not disable all ExtGenSteps during generation</li>
				<li>Mapping rule for FrTpConnection can cause a NPE</li>
				<li>NullPointerException with Tx/Rx-PDU parameter twisty menu of TP editor</li>
				<li>Provide a (Log) Message when .dpa directories are not valid</li>
				<li>NullPointerException when opening TransportProtocolEditor</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.7 SP1">DaVinci Configurator Pro 5.7 (SP1)</a>
			</h2>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Performance improvements</li>
			</ul>
			<h3>Domain Communication</h3>
			<ul>
				<li>Comfort Integration J1939</li>
			</ul>
			<h3>Domain Runtime System</h3>
			<ul>
				<li>Support ServiceSWCs with non-ServicePorts</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>PDUs Editor: &apos;Full CAN&apos; Parameter is not displayed/updated correctly</li>
				<li>Cfg5 does not support display of Pdu when NmPdu is used instead of ISignalIPdu</li>
				<li>No PduRRoutingPath is derived for PNC EIRA/ERA PDUs</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.7">DaVinci Configurator Pro 5.7</a>
			</h2>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Top-Down Service configuration</li>
			</ul>
			<h3>Domain Communication</h3>
			<ul>
				<li>Comfort Integration Ethernet</li>
			</ul>
			<h3>Domain Runtime System</h3>
			<ul>
				<li>Support Combined PR Ports</li>
				<li>Support Service Needs</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.6 SP11">DaVinci Configurator Pro 5.6 (SP11)</a>
			</h2>
			<h3>Extensions</h3>
			<h4>Tool Features</h4>
			<ul>
				<li>Mcu Editor &amp; validation: support update of Mpc560xB</li>
				<li>Support of SystemDescription as input file.</li>
			</ul>
			<h3>Fixed Issued</h3>
			<ul>
				<li>Module Configuration Import: Values of references are not correctly displayed</li>
				<li>The configurator pro deletes the active ECUC file when saving after using a duplicate shortname </li>
				<li> Application cannot be launched when SIP folder &apos;./Doc/DeliveryInformation&apos; contains a sub-directory </li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.6 SP10">DaVinci Configurator Pro 5.6 (SP10)</a>
			</h2>
			<h3>Features</h3>
			<ul>
				<li>Support for hierarchical grid filter</li>
				<li>Improve grid quick filter definition</li>
				<li>Filtering in Difference view</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Module Configuration Import: Values of references are not correctly displayed</li>
				<li>Module Configuration Import: Modified elements shown after re-import</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.6 SP9">DaVinci Configurator Pro 5.6 (SP9)</a>
			</h2>
			<h3>Features</h3>
			<ul>
				<li>Improvements were done in the mapping rules of the Ethernet stack</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Executing a diff/merge action within the postbuild project phase results in errors when non-postbuild loadable parameters/containers are modified</li>
				<li>Cannot activate modules with definition &apos;/AUTOSAR_Os/...&apos;</li>
				<li>OS-Generator is called with incorrect AUTOSAR schema version after updating a project</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.6 SP8">DaVinci Configurator Pro 5.6 (SP8)</a>
			</h2>
			<h3>Extensions</h3>
			<h4>Tool Features</h4>
			<ul>
				<li>Mcu Editor &amp; validation: support update of Mpc560xB - new version 1.0.1</li>
			</ul>
			<h3>Fixed Issued</h3>
			<ul>
				<li>AdditionalBSWMD files are not supported for update with commandline</li>
				<li>Dcm sends unexpected service response on a valid request for routine control</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.6 SP7">DaVinci Configurator Pro 5.6 (SP7)</a>
			</h2>
			<h3>Fixed Issued</h3>
			<ul>
				<li>Project Settings Editor: Code Generation CheckBox Inconsistency</li>
				<li>Locked filters for CAN are changed</li>
				<li>Acceptance Filter Editor shows wrong filter for PDU</li>
				<li>Reporting: Parameter with multiple values is not displayed correctly</li>
				<li>ECU instance not available when setting up a project with only legacy files</li>
				<li>Validation Rules for MB91520 - Bugfix for check of SscgFrequency</li>
				<li>Validation Rules &amp; Comfort Editor for RH850F1x - Update for new MCAL-Version</li>
				<li>Prevent endless loop in link calculation</li>
				<li>Error message when leaving the Diff&amp;Merge mode</li>
				<li>Ethernet mappings could generate invalid short names</li>
				<li>Derived short names in the Ecuc can exceed the maximum allowed length</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.6 SP6">DaVinci Configurator Pro 5.6 (SP6)</a>
			</h2>
			<h3>Fixed Issued</h3>
			<ul>
				<li>EcucUpdater: Wrong order of Ref-Paramter after Cdd Update</li>
				<li>CFG5 doesn&apos;t start on Windows 8.1</li>
				<li>Null pointer exception in bsw management editor</li>
				<li>Watchdogs editor&apos;s configuration tab tree control is not updated</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.6 SP5">DaVinci Configurator Pro 5.6 (SP5)</a>
			</h2>
			<h3>Domain Base Services</h3>
			<ul>
				<li>Update Comfort Integration TC27xB</li>
				<li>Comfort Integration TC26xBA</li>
				<li>Update Comfort Integration R850F1x - MCAL-Update E4.13</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.6 SP4">DaVinci Configurator Pro 5.6 (SP4)</a>
			</h2>
			<h3>Domain Runtime System</h3>
			<ul>
				<li>Support ServiceSWCs with non-ServicePorts</li>
			</ul>
			<h3>Domain Base Services</h3>
			<ul>
				<li>Comfort Integration iMX6SLX</li>
				<li>Comfort Integration MPC574xP - MCAL-Update (V0.91 HF2)</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.6 SP3">DaVinci Configurator Pro 5.6 (SP3)</a>
			</h2>
			<h3>Domain Base Services</h3>
			<ul>
				<li>Comfort Integration iMX6SLX</li>
				<li>Comfort Integration MPC574xP</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.6 SP2">DaVinci Configurator Pro 5.6 (SP2)</a>
			</h2>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Bug Fixes</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.6 SP1">DaVinci Configurator Pro 5.6 (SP1)</a>
			</h2>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Licensing: OEM PostBuild Update</li>
			</ul>
			<h3>Domain Base Services</h3>
			<ul>
				<li>Comfort Integration RH850F1x</li>
				<li>Comfort Integration RH850P1x</li>
				<li>Comfort Integration Aurix EP</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.6">DaVinci Configurator Pro 5.6</a>
			</h2>
			<h3>Project Update/Base EcuC Generation</h3>
			<ul>
				<li>EcuC as input files (project standard configuration)</li>
				<li>System descriptions as input files (system extract generator)</li>
				<li>Support of J1939 and ETH via AUTOSAR 4.1.2</li>
				<li>Support of partial networks on ETH</li>
			</ul>
			<h3>Domain Communication</h3>
			<ul>
				<li>Bus Controller Editor: baud rate calculation dialog improved</li>
			</ul>
			<h3>Domain Network Management</h3>
			<ul>
				<li>Partial Networking Editor: support of UdpNm</li>
			</ul>
			<h3>Domain Mode Management</h3>
			<ul>
				<li>Watchdogs Editor: support of SafeWdgM</li>
			</ul>
			<h3>Domain Runtime System</h3>
			<ul>
				<li>Validation rules for data type compatibility</li>
				<li>Enhanced validation rules for connector compatibility</li>
				<li>Automatic setup of service SWC prototypes</li>
			</ul>
			<h3>Miscellaneous</h3>
			<ul>
				<li>64-bit variant of DaVinci Configurator Pro</li>
				<li>Command line: new switch --extGenStepsToGenerate</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.5 SP9">DaVinci Configurator Pro 5.5 (SP9)</a>
			</h2>
			<h3>Extensions</h3>
			<h4>Tool Features</h4>
			<ul>
				<li>Support of SystemDescription as input file</li>
				<li>Ignore LinSlave EcuInstances in the input files editor.</li>
			</ul>
			<h3>Fixed Issues</h3>
			<ul>
				<li>XML-Files cannot be opened if the path contains special characters </li>
				<li>Application cannot be launched when SIP folder &apos;./Doc/DeliveryInformation&apos; contains a sub-directory.</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.5 SP8">DaVinci Configurator Pro 5.5 (SP8)</a>
			</h2>
			<h3>Extensions</h3>
			<h4>Tool Features</h4>
			<ul>
				<li>Mcu Editor &amp; validation: support update of Mpc560xB - new version 1.0.1</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.5 SP7">DaVinci Configurator Pro 5.5 (SP7)</a>
			</h2>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Multiline text control doesn&apos;t accept line separators.</li>
				<li>Exception when restoring derived subcontainers.</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.5 SP6">DaVinci Configurator Pro 5.5 (SP6)</a>
			</h2>
			<h3>Domain I/O</h3>
			<ul>
				<li>Ids (e.g. DioChannelId) need no longer be sorted in ascending order. An unique Id is sufficient now.</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.5 SP5">DaVinci Configurator Pro 5.5 (SP5)</a>
			</h2>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Error during Update of InputFiles in AUTOSAR3.2.2 and CommandLine enviroment </li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.5 SP4">DaVinci Configurator Pro 5.5 (SP4)</a>
			</h2>
			<h3>Fixed Issues</h3>
			<ul>
				<li>Error message when leaving the Diff&amp;Merge mode</li>
				<li>No automatic conversion from AUTOSAR module configurations during loading</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.5 SP3">DaVinci Configurator Pro 5.5 (SP3)</a>
			</h2>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Licensing: OEM PostBuild Update</li>
				<li>Performance improvements</li>
			</ul>
			<h3>Domain Base Services</h3>
			<ul>
				<li>Comfort Integration RH850F1x for new MCAL version</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.5 SP2">DaVinci Configurator Pro 5.5 (SP2)</a>
			</h2>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Support of Diff&amp;Merge</li>
				<li>Performance improvements</li>
			</ul>
			<h3>Domain Base Services</h3>
			<ul>
				<li>Comfort Integration Aurix-HE (TC26x, TC27x, TC29x)</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.5 SP1">DaVinci Configurator Pro 5.5 (SP1)</a>
			</h2>
			<h3>Support of Vector Virtual Integration Platform</h3>
			<ul>
				<li>Project setup: selection of target type real, virtual or real+virtual </li>
				<li>Code generation: Option for generating VIP target</li>
			</ul>
			<h3>Domain Base Services</h3>
			<ul>
				<li>Comfort Integration RH850F1X</li>
				<li>Comfort Integration RH850P1X</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.5">DaVinci Configurator Pro 5.5</a>
			</h2>
			<h3>User Annotations</h3>
			<ul>
				<li>Add textual annotations for each configuration element</li>
				<li>Multiple annotation per configuration element possible</li>
				<li>Each annotation has an own label</li>
				<li>Support in FindView and Reporting</li>
			</ul>
			<h3>Automatic Generator Validation</h3>
			<ul>
				<li>Validation messages of external generation steps are shown during configuration process</li>
			</ul>
			<h3>BSWMD Update</h3>
			<ul>
				<li>Selection of new BSWMD if Short-Name of module definition is changed</li>
			</ul>
			<h3>Domain IO</h3>
			<ul>
				<li>Comfort editor for IoHwAbstraction</li>
			</ul>
			<h3>Domain Mode Management</h3>
			<ul>
				<li>BSW Management Editor: Automatic configuration of ECU state handling</li>
				<li>BSW Management Editor: Automatic configuration of Module initialization</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.4 SP7">DaVinci Configurator Pro 5.4 (SP7)</a>
			</h2>
			<h3>Miscellaneous</h3>
			<ul>
				<li> Diagnostic input file cannot be found with relative path</li>
				<li> RTE checkum error: AutosarComparator does not sort non-referrables Objects in each case</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.4 SP6">DaVinci Configurator Pro 5.4 (SP6)</a>
			</h2>
			<h3>Miscellaneous</h3>
			<ul>
				<li> Support an ECUExtract splitted in several Files</li>
				<li> Performance improvements</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<h2>
				<a name="5.4 SP5">DaVinci Configurator Pro 5.4 (SP5)</a>
			</h2>
			<h3>Miscellaneous</h3>
			<ul>
				<li> Performance improvements</li>
				<li> Support Floating licences</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<h2>
				<a name="5.4 SP4">DaVinci Configurator Pro 5.4 (SP4)</a>
			</h2>
			<h3>Domain Base Services</h3>
			<ul>
				<li>Comfort Integration MPC574xM</li>
				<li>Comfort Integration MPC5606B</li>
			</ul>
			<h3>Miscellaneous</h3>
			<ul>
				<li> Performance improvements</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<h2>
				<a name="5.4 SP3">DaVinci Configurator Pro 5.4 (SP3)</a>
			</h2>
			<h3>Domain Base Services</h3>
			<ul>
				<li>Comfort Integration TC27xx</li>
				<li>Comfort Integration MPC564xC ( Bolero )</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.4 SP2">DaVinci Configurator Pro 5.4 (SP2)</a>
			</h2>
			<ul>
				<li>Performance improvement: Launcher tool to start DaVinci Configurator executable with maximum available memorys</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<h2>
				<a name="5.4 SP1">DaVinci Configurator Pro 5.4 (SP1)</a>
			</h2>
			<h3>Domain Network Management</h3>
			<ul>
				<li>Partial Networking Editor: new editor for comfort configuration of partial networks</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.4">DaVinci Configurator Pro 5.4</a>
			</h2>
			<h3>Post-build loadable ECUs</h3>
			<ul>
				<li>Project Setings Editor: Selection of module implementation variant</li>
				<li>Properties View: display effective configuration class of a parameter</li>
				<li>Project configuraton phase displayed in the status bar</li>
				<li>Assistant for switching the project configuraton phase</li>
			</ul>
			<h3>Domain Communication</h3>
			<ul>
				<li>Signals Editor: editing of signal gateway routings</li>
				<li>Bus Controller Editor: Display the CAN ID of the PDUs passing an acceptance filter</li>
			</ul>
			<h3>Domain Mode Management</h3>
			<ul>
				<li>BSW Management Editor: Separate definition of mode switch ports and mode request ports</li>
				<li>BSW Management Editor: Logical expressions can be shared by several rules</li>
				<li>BSW Management Editor: Integrated help text</li>
			</ul>
			<h3>Domain Runtime System</h3>
			<ul>
				<li>Task Mapping Assistant: automatic calculation of position-in-task</li>
				<li>Data Mapping Assistant: support of complex data types/signals groups</li>
				<li>OS Configuration Editor: generic display of vendor parameters of tasks, alarms, etc.</li>
				<li>OS Configuration Editor: Display of core assignment of OS applications</li>
			</ul>
			<h3>Miscellaneous</h3>
			<ul>
				<li>New look-and-feel of grid-based editors: easier in-place editing, multi-operations, sorting, filtering</li>
				<li>Reworked validation view, allows e.g. multi-line messages</li>
				<li>Assistants renamed</li>
				<li>File change detection: warning displayed if a file is changed outside DaVinci Configurator</li>
				<li>Change of project settings after creation of the project (administrative info, path to DaVinci Developer)</li>
			</ul>
			<h3>A2L generation based on McSupportData</h3>
			<ul>
				<li>Generation of McSupportData by the MSR generators</li>
				<li>Conversion of the McSupportData to an A2L file</li>
			</ul>
			<h3>Service Pack Update Utility Tool</h3>
			<ul>
				<li>Distribution of service packs via Vector Download Center</li>
				<li>Update of existing SIPs with the new tool service pack</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.3">DaVinci Configurator Pro 5.3</a>
			</h2>
			<h3>ECUC Import Function</h3>
			<ul>
				<li>Import of module configurations from ECUC files</li>
				<li>AUTOSAR 4.0 and AUTOSAR 3.x format supported (without semantical conversion)</li>
			</ul>
			<h3>Search Function</h3>
			<ul>
				<li>New view: Find View displays the search result</li>
				<li>Simple search strings or complex search queries with logical expressions supported</li>
				<li>Various syntax keywords for filtering object types or parameter states</li>
				<li>Editing support and syntax highlighting of the search queries</li>
				<li>Navigation from the Find View to the configuration editors</li>
			</ul>
			<h3>Code Generation</h3>
			<ul>
				<li>Partial code generation by deselecting individual code generation steps</li>
				<li>Generation Result View displays the status of the last code generation process</li>
			</ul>
			<h3>Input Files Editor</h3>
			<ul>
				<li>Improved handling of input files (copy to project folder)</li>
				<li>Support for defining relative paths with placeholders</li>
				<li>Simplified workflow to update the diagnostic state description</li>
				<li>Selectable diagnostic description patch file</li>
				<li>Improved generation of Base EcuC file</li>
			</ul>
			<h3>Project Assistant</h3>
			<ul>
				<li>Support of split EcuC projects</li>
			</ul>
			<h3>GUI General</h3>
			<ul>
				<li>Display options for the number format of integer parameters</li>
				<li>Display options for the physical units of parameters representing a memory, time or frequency value</li>
				<li>Property View: display of parameter path</li>
				<li>Copy-to-clipboard of parameter path</li>
				<li>Context menu displayed in the editor tree</li>
				<li>Editor address line (bread crumb) displays preview of next level in context tree</li>
				<li>Window configuration restored when opening the tool</li>
			</ul>
			<h3>Domain Communication</h3>
			<ul>
				<li>Bus Controller Editor: support of extended and mixed IDs in CAN acceptance filter configuration</li>
				<li>Communication General Editor restructured</li>
				<li>PDUs Editor: display of PDU gateway routing paths and internal routing paths</li>
				<li>Signals Editor: display of signal gateway routing paths</li>
				<li>Transport Protocol Editor: Support of CanTp connections</li>
				<li>Gateway Routing Editor removed (content moved to PDUs Editor and Signals Editor)</li>
			</ul>
			<h3>Domain Runtime System</h3>
			<ul>
				<li>ECU Components Editor: display of SWC port prototype list, including mapping information</li>
				<li>Component Connection Assistant: improved algorithm for automatic service mapping</li>
				<li>Runtime System General Editor: Display of StbM</li>
				<li>Improved live validation of connectors for C/S ports</li>
			</ul>
			<h3>Domain Diagnostics</h3>
			<ul>
				<li>OBDII support in Diagnostic Data Editor, Diagnostic Event Editor and Setup Diagnostic Memory Blocks Assistant</li>
				<li>Schema validation of ODX files</li>
			</ul>
			<h3>Domain Memory</h3>
			<ul>
				<li>Repair function for missing links between NvM and Fee blocks</li>
			</ul>
			<h3>Miscellaneous</h3>
			<ul>
				<li>Product option .MD (Module Development)</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.2">DaVinci Configurator Pro 5.2</a>
			</h2>
			<h3>AUTOSAR 4.0.3 support</h3>
			<ul>
				<li>Support of AUTOSAR 4.0.3 files (ECUC, BSWMD, SYSEX)</li>
				<li>Adaptation of comfort editors to AUTOSR 4.0.3</li>
			</ul>
			<h3>New Project Assistant</h3>
			<ul>
				<li>Workflow support for creation of new projects</li>
				<li>Separation of project creation and project update</li>
			</ul>
			<h3>Input Files Editor</h3>
			<ul>
				<li>Selection of system description files and diagnostic data files</li>
				<li>Creation and update of diagnostic state descriptions</li>
				<li>Creation of Base EcuC based on the input files</li>
				<li>Project update after changes in the input files</li>
			</ul>
			<h3>Project Settings Editor</h3>
			<ul>
				<li>Editor structure reworked</li>
				<li>Configuration of custom workflow steps</li>
			</ul>
			<h3>GUI General</h3>
			<ul>
				<li>Simplified navigation view: &apos;Basic&apos; tab obsolete, one Basic Editor for all modules</li>
				<li>Context tree in comfort editors now initially displayed</li>
				<li>Optional context tree in Basic Editor</li>
				<li>&apos;Show Details&apos; mode for grid views with hidden context tree</li>
			</ul>
			<h3>Domain Base Services</h3>
			<ul>
				<li>Editor re-organization: Development Errors, Microcontroller Unit, General Purpose Timer, RAM Test</li>
			</ul>
			<h3>Domain Communication</h3>
			<ul>
				<li>Gateway Routing Editor: support of 1:n routings</li>
				<li>Editor reorganization: Bus Controller, PDUs, Signals</li>
				<li>Bus Controller Editor: support for bus timing configuration (hardware-specific)</li>
				<li>PDUs Editor: support for handling of Full-CAN objects</li>
			</ul>
			<h3>Domain Diagnostics</h3>
			<ul>
				<li>New comfort editors: Diagnostic Data, Diagnostic Events, Production Error Handling</li>
				<li>New assistant: Setup Diagnostic Memory Blocks</li>
			</ul>
			<h3>Domain Memory</h3>
			<ul>
				<li>Editor reorganization: Memory partition editing moved to Memory Blocks Editor</li>
			</ul>
			<h3>Domain Mode Management</h3>
			<ul>
				<li>Editor rework to support AUTOSAR 4 EcuMFlex</li>
			</ul>
			<h3>Domain Runtime System</h3>
			<ul>
				<li>Support of AUTOSAR 4 workflow (SYSEX with flattened ECUEX)</li>
				<li>ECU Software Components Editor: Selection of service SWCs created by MICROSAR 4 generators</li>
				<li>Task Mapping Assistant: display the current mapping status of the runnables</li>
				<li>Runtime System General Editor: reworked structure</li>
				<li>Basic validation of SYSEX and ECUEX content via automatic validation rules</li>
			</ul>
			<h3>Reporting</h3>
			<ul>
				<li>Creation of HTML report of the configuration</li>
				<li>Supported reports: complete configuration report, report of user-defined parameters</li>
			</ul>
			<h3>Miscellaneous</h3>
			<ul>
				<li>License dialog with detailed display of tool license</li>
				<li>Support of Vector Keyman USB dongle</li>
				<li>Automation interface</li>
				<li>Installer for external components available in Download Center</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.0 SP1">DaVinci Configurator Pro 5.0 SP1</a>
			</h2>
			<h3>Domain Communication</h3>
			<ul>
				<li>Support of FlexRay in the comfort editors</li>
				<li>Comfort editor FlexRay Job Lists</li>
				<li>Comfort editor Transport Protocol</li>
			</ul>
			<h3>Domain Mode Management</h3>
			<ul>
				<li>Comfort editor BSW Management</li>
				<li>Comfort editor Initialization</li>
				<li>Comfort editor Sleep and Wakeup</li>
				<li>Comfort editor Watchdogs</li>
			</ul>
			<h3>Domain Runtime System</h3>
			<ul>
				<li>Comfort editor Timing Protection</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="5.0">DaVinci Configurator Pro 5.0</a>
			</h2>
			<h3>Eclipse-based tool</h3>
			<ul>
				<li>Re-implementation of DaVinci Configurator Pro based on the Eclipse platform</li>
				<li>Parameter state management</li>
				<li>Solving actions for validation errors</li>
				<li>Auto-solving actions for automatic consistency</li>
			</ul>
			<h3>Domain Base Services</h3>
			<ul>
				<li>Comfort editor Base Services General</li>
				<li>Comfort editor Development Error Handling</li>
				<li>Comfort editor General Purpose Timer</li>
				<li>Comfort editor Mcu Clocks</li>
				<li>Comfort editor Mcu Power Modes</li>
				<li>Comfort editor RAM Test</li>
			</ul>
			<h3>Domain Communication</h3>
			<ul>
				<li>Comfort editor Communication General</li>
				<li>Comfort editor Communication Controller</li>
				<li>Comfort editor Gateway Routing</li>
				<li>Comfort editor PDU Groups</li>
				<li>Comfort editor PDUs and Signals</li>
			</ul>
			<h3>Domain Memory</h3>
			<ul>
				<li>Comfort editor Memory General</li>
				<li>Comfort editor Memory Blocks</li>
				<li>Comfort editor Memory Partitions</li>
			</ul>
			<h3>Domain Mode Management</h3>
			<ul>
				<li>Comfort editor EcuM Users</li>
			</ul>
			<h3>Domain Network Management</h3>
			<ul>
				<li>Comfort editor Network Management General</li>
				<li>Comfort editor Communication Users</li>
				<li>Comfort editor NM Coordinator</li>
			</ul>
			<h3>Domain Runtime System</h3>
			<ul>
				<li>Comfort editor Runtime System General</li>
				<li>Comfort editor OS Configuration</li>
			</ul>
			<p>
				<a href="#_top">(top)</a>
			</p>
			<!-- #################### -->
			<h2>
				<a name="info">Additional Information</a>
			</h2>
			<h3>Copyright</h3>
			<dl>
				<dd>
					<p>&copy; Vector Informatik GmbH</p>
				</dd>
			</dl>
			<h3>Certified Quality Management System</h3>
			<dl>
				<dd>
					<p>The Quality/Process Management of Vector Informatik GmbH is being certified
           according to DIN EN ISO 9001:2000-12 (formerly DIN EN ISO 9001:1994-08)
           throughout since 1998-08-19.</p>
				</dd>
			</dl>
			<h3>Vector Informatik GmbH - Addresses</h3>
			<dl>
				<dd>
					<p>
						<b>Vector Informatik GmbH</b>
						<br/>
						Ingersheimer Str. 24<br/>
						D-70499 Stuttgart, Germany<br/>
						Tel.: +49 (711) 80670-0<br/>
						Fax: +49 (711) 80670-100<br/>
						<a href="mailto:<EMAIL>"><EMAIL></a>
						<br/>
						<a href="http://www.vector-informatik.com">http://www.vector-informatik.com</a>
					</p>
				</dd>
			</dl>
			<h3>Subsidiaries</h3>
			<dl>
				<dd>
					<p>
						<b>Vector France SAS</b>
						<br/>
						168, Boulevard Cam&eacute;linat<br/>
						92240 Malakoff<br/>
						France<br/>
						Tel.: +33 1 4231 4000<br/>
						Fax: +33 1 4231 4009<br/>
						<a href="mailto:<EMAIL>"><EMAIL></a>
						<br/>
						<a href="http://www.vector-france.com">http://www.vector-france.com</a>
					</p>
				</dd>
				<dd>
					<p>
						<b>Vector Japan Co., Ltd.</b>
						<br/>
						Seafort Square Center Bld.<br/>
						18F, 2-3-12,<br/>
						Higashi-shinagawa, Shinagawa-ku<br/>
						Tokyo 140-0002<br/>
						Japan<br/>
						Tel.: +81 3 5769 6970<br/>
						Fax: +81 3 5769 6975<br/>
						<a href="mailto:<EMAIL>"><EMAIL></a>
						<br/>
						<a href="http://www.vector-japan.co.jp">http://www.vector-japan.co.jp</a>
					</p>
				</dd>
				<dd>
					<p>
						<b>VecScan AB</b>
						<br/>
						Theres Svenssons Gata 9<br/>
						417 55 Gothenburg<br/>
						Sweden<br/>
						Tel.: +46 (31) 76476 00<br/>
						Fax: +46 (31) 76476 19<br/>
						<a href="mailto:<EMAIL>"><EMAIL></a>
						<br/>
						<a href="http://www.vecscan.com">http://www.vecscan.com</a>
					</p>
				</dd>
				<dd>
					<p>
						<b>Vector CANtech, Inc.</b>
						<br/>
						39500 Orchard Hill Place<br/>
						Suite 550<br/>
						Novi, Michigan 48375<br/>
						USA<br/>
						Tel.: +****************<br/>
						Fax: +****************<br/>
						<a href="mailto:<EMAIL>"><EMAIL></a>
						<br/>
						<a href="http://www.vector-cantech.com">http://www.vector-cantech.com</a>
					</p>
				</dd>
				<dd>
					<p>
						<b>Vector Korea IT Inc.</b>
						<br/>
						Daerung Post Tower III, 508<br/>
						182-4 Guro-dong, Guro-gu<br/>
						Seoul 152-790<br/>
						Republic of Korea<br/>
						Tel.: +82(0)2 2028 0600<br/>
						Fax: +82(0)2 2028 0604<br/>
						<a href="mailto:<EMAIL>"><EMAIL></a>
						<br/>
						<a href="http://www.vector-korea.com">http://www.vector-korea.com</a>
					</p>
				</dd>
				<dd>
					<p>
						<b>Vector GB Ltd.</b>
						<br/>
						Rhodium<br/>
						Central Boulevard<br/>
						Blythe Valley Park<br/>
						Solihull, Birmingham<br/>
						West Midlands B90 8AS<br/>
						United Kingdom<br/>
						Tel.: +44 (0) 7530 264701<br/>
						<a href="mailto:<EMAIL>"><EMAIL></a>
						<br/>
						<a href="http://www.vector-gb.co.uk">http://www.vector-gb.co.uk</a>
					</p>
				</dd>
			</dl>
			<p>
				<a href="#_top">(top)</a>
			</p>
		</p>
		<!-- ##################### -->
	</body>
</html>
