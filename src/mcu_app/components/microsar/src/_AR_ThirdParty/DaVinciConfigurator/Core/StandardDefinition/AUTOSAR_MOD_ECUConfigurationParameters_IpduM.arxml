<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: IpduM -->
						<ECUC-MODULE-DEF UUID="ECUC:01c1cba1-1af2-40e0-a551-2417ba71f3fb">
							<SHORT-NAME>IpduM</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the IpduM (Ipdu Multiplexer) module.</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.2.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
										<DATE>2011-11-09</DATE>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-LINK-TIME</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-POST-BUILD</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: IpduMConfig -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:7af0ef33-3531-4c92-8769-330c3db2f19b">
									<SHORT-NAME>IpduMConfig</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container contains the sub containers of the IpduM module. The IpduMTxPathway subcontainer includes information about sent I-PDUs. The IpduMRxPathway includes information about received I-PDUs.</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">This container is a MultipleConfigurationContainer, i.e. this container and its sub-containers exist once per configuration set.</L-1>
										</P>
									</INTRODUCTION>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>true</MULTIPLE-CONFIGURATION-CONTAINER>
									<SUB-CONTAINERS>
										<!-- Container Definition: IpduMRxPathway -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:7bedc87c-e833-4e9a-8ca3-ac8d731e7639">
											<SHORT-NAME>IpduMRxPathway</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Contains the configuration parameters received I-PDUs by the IpduM module.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<SUB-CONTAINERS>
												<!-- Container Definition: IpduMRxIndication -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:79391c0f-2367-4aaa-9fe1-0499addc12a4">
													<SHORT-NAME>IpduMRxIndication</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Contains the configuration for incoming RxIndication calls.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: IpduMByteOrder -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:0a091ae2-be9b-9515-59e3-e1ccee37c364">
															<SHORT-NAME>IpduMByteOrder</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines the ByteOrder for all IpduMSegments (static and dynamic part) and for the selectorField within the MultiplexedPdu.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">The absolute position of a segment in the MultiplexedIPdu is
                                                        determined by the definition of the ByteOrder parameter:
                                                        If BIG_ENDIAN is specified, the SegmentPosition indicates the bit position of the most significant bit in an IPDU.
                                                        If LITTLE_ENDIAN is specified, the SegmentPosition indicates the bit position of the least significant bit in an IPDU.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:d7f8e728-c919-dd0f-1576-a308c87e54df">
																	<SHORT-NAME>BIG_ENDIAN</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:135e4d5c-1d65-dc04-ec9f-db72db3e19f4">
																	<SHORT-NAME>LITTLE_ENDIAN</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: IpduMRxHandleId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:0033a86b-82e6-4de0-ba8f-63fb49ed2d56">
															<SHORT-NAME>IpduMRxHandleId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This is the I-PDU ID of the incoming I-PDU. If an incoming RxIndication&apos;s I-PDU ID matches this value then it is unpacked according to the specification in this container.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: IpduMRxIndicationPduRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:1072e012-067c-4a46-a7d3-c732cc72e5fb">
															<SHORT-NAME>IpduMRxIndicationPduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the received Pdu representation in the ECU Configuration Description exchange file.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
													<SUB-CONTAINERS>
														<!-- Container Definition: IpduMRxDynamicPart -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:f1397a3c-d592-43f4-b7c2-f27fa8343387">
															<SHORT-NAME>IpduMRxDynamicPart</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This container contains the configuration for the dynamic part of incoming RxIndication calls. When an incoming received I-PDU&apos;s selector field matches the IpduM_Selector_Value, the new outgoing I-PDU for the dynamic part is constructed as defined by the segments of this container and sent out with the I-PDU ID referenced by IpduMOutgoingDynamicPduRef.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: IpduMRxSelectorValue -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:3f6485c6-3099-46d6-8c29-8472e98b8156">
																	<SHORT-NAME>IpduMRxSelectorValue</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This is the selector value that this container refers to.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
															<REFERENCES>
																<!-- Reference Definition: IpduMOutgoingDynamicPduRef -->
																<ECUC-REFERENCE-DEF UUID="ECUC:4e7f0043-612c-4d35-b62a-db8642482b3e">
																	<SHORT-NAME>IpduMOutgoingDynamicPduRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">When the new I-PDU is sent out it is sent with this I-PDU ID. Reference to the sent PDU representation in the ECU Configuration Description exchange file.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
															</REFERENCES>
															<SUB-CONTAINERS>
																<!-- Container Definition: IpduMSegment -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:d5fbfafb-4179-84a3-5b98-49b555b0dcf8">
																	<SHORT-NAME>IpduMSegment</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This contains the location and the length of a segment. A segment must fit inside the I-PDU. The segment in the source I-PDU that is located at the IpduMSegmentPosition is copied to the same position in the destination I-PDU.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: IpduMSegmentLength -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:71341c72-5251-d1df-199a-529813c95762">
																			<SHORT-NAME>IpduMSegmentLength</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Length of the segment in bits.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<MAX>2032</MAX>
																			<MIN>1</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: IpduMSegmentPosition -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:bcca77f4-d678-cded-10e1-da764dd064cb">
																			<SHORT-NAME>IpduMSegmentPosition</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Segments bit position in the multiplexed Pdu.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<MAX>2031</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
															</SUB-CONTAINERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: IpduMRxStaticPart -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:4928f0b1-4227-4382-a2e5-c6dc41caf280">
															<SHORT-NAME>IpduMRxStaticPart</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This container contains the configuration for the static part of incoming RxIndication calls. On reception, the new outgoing I-PDU for the static part is constructed as defined by the segments of this container and sent out with the I-PDU ID referenced by IpduMOutgoingStaticPduRef.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<REFERENCES>
																<!-- Reference Definition: IpduMOutgoingStaticPduRef -->
																<ECUC-REFERENCE-DEF UUID="ECUC:0e3f6906-b529-4773-b5dc-18f321d17d07">
																	<SHORT-NAME>IpduMOutgoingStaticPduRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">When the new I-PDU is sent out it is sent with this I-PDU ID. Reference to the sent Pdu representation in the ECU Configuration Description exchange file.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
															</REFERENCES>
															<SUB-CONTAINERS>
																<!-- Container Definition: IpduMSegment -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:2deb716f-ae0e-8431-46bb-1e11ef479bf1">
																	<SHORT-NAME>IpduMSegment</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This contains the location and the length of a segment. A segment must fit inside the I-PDU. The segment in the source I-PDU that is located at the IpduMSegmentPosition is copied to the same position in the destination I-PDU.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: IpduMSegmentLength -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:c92392e6-bee6-d16d-04bd-26f4ad60165b">
																			<SHORT-NAME>IpduMSegmentLength</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Length of the segment in bits.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<MAX>2032</MAX>
																			<MIN>1</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: IpduMSegmentPosition -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:14b9ee69-430d-cd7b-fc04-aed2e76723c4">
																			<SHORT-NAME>IpduMSegmentPosition</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Segments bit position in the multiplexed Pdu.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<MAX>2031</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
															</SUB-CONTAINERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: IpduMSelectorFieldPosition -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:7812a762-87eb-986f-2dc8-b97e52a6cd82">
															<SHORT-NAME>IpduMSelectorFieldPosition</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This contains the location and the length of the selector field.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: IpduMSelectorFieldLength -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:f25ae367-aac9-e609-e486-87a52b031fdd">
																	<SHORT-NAME>IpduMSelectorFieldLength</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Length of the selector field in bits.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>8</MAX>
																	<MIN>1</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: IpduMSelectorFieldPosition -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:b542e6ee-166c-e294-bddc-23c4350a221e">
																	<SHORT-NAME>IpduMSelectorFieldPosition</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Selector field bit position in the multiplexed Pdu.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>2031</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: IpduMTxPathway -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:9cdee255-853a-4712-9486-6c862bb07011">
											<SHORT-NAME>IpduMTxPathway</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Contains the configuration parameters transmitted I-PDUs by the IpduM module.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<SUB-CONTAINERS>
												<!-- Container Definition: IpduMTxRequest -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:0d77339e-affa-4b47-9dbc-2b2b999e0703">
													<SHORT-NAME>IpduMTxRequest</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This is used to specify the configuration for Transmit requests.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">There will one instance of this container for each I-PDU that can be requested for transmission (the outgoing I-PDUs) by the IpduM.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: IpduMByteOrder -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:9e473272-4b2e-95b2-57bf-085ed9f9b7c3">
															<SHORT-NAME>IpduMByteOrder</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines the ByteOrder for all IpduMSegments (static and dynamic part) and for the selectorField within the MultiplexedPdu.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">The absolute position of a segment in the MultiplexedIPdu is
                                                        determined by the definition of the ByteOrder parameter:
                                                        If BIG_ENDIAN is specified, the SegmentPosition indicates the bit position of the most significant bit in an IPDU.
                                                        If LITTLE_ENDIAN is specified, the SegmentPosition indicates the bit position of the least significant bit in an IPDU.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:6c36feb8-55ac-ddac-1351-c99ab440493e">
																	<SHORT-NAME>BIG_ENDIAN</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:a79c64eb-a9f8-dca1-ea7b-0204c7000e53">
																	<SHORT-NAME>LITTLE_ENDIAN</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: IpduMIPduUnusedAreasDefault -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:69073676-9fa7-4288-b2e0-3026caff1032">
															<SHORT-NAME>IpduMIPduUnusedAreasDefault</SHORT-NAME>
															<DESC>
																<L-2 L="EN">IpduM module fills not used areas of an I-PDU with this bit-pattern</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">If this attribute is omitted the IpduM module does not fill the I-PDU.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: IpduMTxConfirmationPduId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:258cd7c4-f9ec-4228-9e80-8c7b5065c561">
															<SHORT-NAME>IpduMTxConfirmationPduId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">The handle Id to be used by the PduR to confirm the transmission of this Pdu.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">The existence of this parameter is essential for the PduR generation tool to actually find a symbolicNameValue for the OutgoingPdu.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: IpduMTxConfirmationTimeout -->
														<ECUC-FLOAT-PARAM-DEF UUID="ECUC:8773e2ec-6b2b-4eca-b47a-aba1d3576810">
															<SHORT-NAME>IpduMTxConfirmationTimeout</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This timeout (in seconds) defines the timeout period for monitoring the reception of the TxConfirmation.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">It is not used when an I-PDU is requested using the trigger transmit API.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>3600</MAX>
															<MIN>0</MIN>
														</ECUC-FLOAT-PARAM-DEF>
														<!-- PARAMETER DEFINITION: IpduMTxTriggerMode -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:69612ecd-3531-4049-b5c0-7524610ef2bc">
															<SHORT-NAME>IpduMTxTriggerMode</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Selects whether to send the multiplexed I-PDU immediately or at some later date.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:4e8c28fa-eb7e-9235-325a-8b831f72a0f0">
																	<SHORT-NAME>DYNAMIC_PART_TRIGGER</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:7d26ddd8-138a-94ff-4a76-600c267b408c">
																	<SHORT-NAME>NONE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:5a8b456d-ef90-9a16-5b76-e2bc9cc8a6b3">
																	<SHORT-NAME>STATIC_OR_DYNAMIC_PART_TRIGGER</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:b3a2006f-44e9-9367-3206-d90ed6431caa">
																	<SHORT-NAME>STATIC_PART_TRIGGER</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: IpduMInitialDynamicPart -->
														<ECUC-REFERENCE-DEF UUID="ECUC:360b8673-3660-4296-bce3-e0c4f605a64e">
															<SHORT-NAME>IpduMInitialDynamicPart</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the dynamic part that shall be used to initialize this multiplexed TX-I-PDU.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/IpduM/IpduMConfig/IpduMTxPathway/IpduMTxRequest/IpduMTxDynamicPart</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
														<!-- Reference Definition: IpduMOutgoingPduRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:9c9ad385-0306-44ac-a575-b5c32377b397">
															<SHORT-NAME>IpduMOutgoingPduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the PDU defining the outgoing I-PDU.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">When the outgoing I-PDU is sent this is the I-PDU ID to give it. It is the IpduM I-PDU ID of the assembled I-PDU.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
													<SUB-CONTAINERS>
														<!-- Container Definition: IpduMSelectorFieldPosition -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:0c50bef2-147e-990c-2ba3-e0103e68c1e1">
															<SHORT-NAME>IpduMSelectorFieldPosition</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This contains the location and the length of the selector field.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: IpduMSelectorFieldLength -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:8698faf7-375c-e6a6-e261-ae3716c5143c">
																	<SHORT-NAME>IpduMSelectorFieldLength</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Length of the selector field in bits.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>8</MAX>
																	<MIN>1</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: IpduMSelectorFieldPosition -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:4980fe7d-a2ff-e331-bbb7-4a5620cc167d">
																	<SHORT-NAME>IpduMSelectorFieldPosition</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Selector field bit position in the multiplexed Pdu.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>2031</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: IpduMTxDynamicPart -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:3796101d-e7a3-4b52-98c6-f1127db9a143">
															<SHORT-NAME>IpduMTxDynamicPart</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Configuration parameters for an instance of a TxRequest call into the IpduM. When a Tx Request with the IpduMTxDynamicHandleId is received by the IpduM, all segments as defined by this container are copied from the incoming I-PDU into the outgoing I-PDU buffer and then the send mode honoured. This container is used by the dynamic part of a TxRequest configuration. Therefore, for each outgoing I-PDU there will be one instance of this container for the dynamic part.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: IpduMJitUpdate -->
																<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:d09eccc9-8102-9927-19b6-15b5f18d8936">
																	<SHORT-NAME>IpduMJitUpdate</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">If configured to true fetch the data of this part Just-In-Time via the triggerTransmit API of the PduR.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>false</DEFAULT-VALUE>
																</ECUC-BOOLEAN-PARAM-DEF>
																<!-- PARAMETER DEFINITION: IpduMTxDynamicConfirmation -->
																<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:b6ce8ccd-e819-4c1d-82a5-1a795a79cdf2">
																	<SHORT-NAME>IpduMTxDynamicConfirmation</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">A transmit request can be confirmed by the lower layer. If this parameter is set to true a confirmation of the I-PDU in COM representing the dynamic part is generated.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																</ECUC-BOOLEAN-PARAM-DEF>
																<!-- PARAMETER DEFINITION: IpduMTxDynamicHandleId -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:6387c164-c4f0-42c4-b7ef-3d03708e8e86">
																	<SHORT-NAME>IpduMTxDynamicHandleId</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This is an incoming handle id. When the handle of an incoming Tx Request matches this, the bits fields (see IpduMSegment) are copied and the IpduMTxTriggerMode is honored.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
																	<MAX>65535</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
															<REFERENCES>
																<!-- Reference Definition: IpduMTxDynamicPduRef -->
																<ECUC-REFERENCE-DEF UUID="ECUC:ccb1bc43-ddaa-499b-a564-619481758ef8">
																	<SHORT-NAME>IpduMTxDynamicPduRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to the Pdu representation in the ECU Configuration Description exchange file to be transmitted.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
															</REFERENCES>
															<SUB-CONTAINERS>
																<!-- Container Definition: IpduMSegment -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:1c5890dc-538a-8c01-3c9c-48482b364ab4">
																	<SHORT-NAME>IpduMSegment</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This contains the location and the length of a segment. A segment must fit inside the I-PDU. The segment in the source I-PDU that is located at the IpduMSegmentPosition is copied to the same position in the destination I-PDU.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: IpduMSegmentLength -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:b790b253-6462-d93d-fa9e-512ae94ec51e">
																			<SHORT-NAME>IpduMSegmentLength</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Length of the segment in bits.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<MAX>2032</MAX>
																			<MIN>1</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: IpduMSegmentPosition -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:03270dd5-e889-d54b-f1e5-d9092355d287">
																			<SHORT-NAME>IpduMSegmentPosition</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Segments bit position in the multiplexed Pdu.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<MAX>2031</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
															</SUB-CONTAINERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: IpduMTxStaticPart -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:2e0c7ae8-2ee8-4e7b-8199-63de477c9b32">
															<SHORT-NAME>IpduMTxStaticPart</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Configuration parameters for an instance of a Tx_Request call into the IpduM. When a Tx Request with the IpduMTxStaticHandleId is received by the IpduM, all segments as defined by this container are copied from the incoming I-PDU into the outgoing I-PDU buffer and then the send mode honoured. This container is used for the static part of a TxRequest configuration. Therefore, for each outgoing I-PDU there will be one instance of this container for the static part if it exists.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: IpduMJitUpdate -->
																<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:c7153793-c847-9c50-0288-8881bb508325">
																	<SHORT-NAME>IpduMJitUpdate</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">If configured to true fetch the data of this part Just-In-Time via the triggerTransmit API of the PduR.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>false</DEFAULT-VALUE>
																</ECUC-BOOLEAN-PARAM-DEF>
																<!-- PARAMETER DEFINITION: IpduMTxStaticConfirmation -->
																<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:4ce07ee5-1ee2-4cb7-a88e-a1a5fbc52dcb">
																	<SHORT-NAME>IpduMTxStaticConfirmation</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">A transmit request can be confirmed by the lower layer. If this parameter is set to true a confirmation of the I-PDU in COM representing the static part is generated.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																</ECUC-BOOLEAN-PARAM-DEF>
																<!-- PARAMETER DEFINITION: IpduMTxStaticHandleId -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:70025d88-e703-48fd-bf16-be6df3b135db">
																	<SHORT-NAME>IpduMTxStaticHandleId</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This is an incoming handle id. When the handle of an incoming Tx Request matches this, the segments are copied (IPduMSegment) and the IpduMTxTriggerMode is honored.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
																	<MAX>65535</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
															<REFERENCES>
																<!-- Reference Definition: IpduMTxStaticPduRef -->
																<ECUC-REFERENCE-DEF UUID="ECUC:1e7ba123-6e03-4f9c-b63c-cb74d7b31a9c">
																	<SHORT-NAME>IpduMTxStaticPduRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to the Pdu representation in the ECU Configuration Description exchange file to be transmitted.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
															</REFERENCES>
															<SUB-CONTAINERS>
																<!-- Container Definition: IpduMSegment -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:12cefba6-9acf-8f2a-256e-bb13f4f944a3">
																	<SHORT-NAME>IpduMSegment</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This contains the location and the length of a segment. A segment must fit inside the I-PDU. The segment in the source I-PDU that is located at the IpduMSegmentPosition is copied to the same position in the destination I-PDU.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: IpduMSegmentLength -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:ae071d1d-aba7-dc66-e370-c3f6b311bf0d">
																			<SHORT-NAME>IpduMSegmentLength</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Length of the segment in bits.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<MAX>2032</MAX>
																			<MIN>1</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: IpduMSegmentPosition -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:f99d78a0-2fce-d874-dab8-4bd4ed18cc76">
																			<SHORT-NAME>IpduMSegmentPosition</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Segments bit position in the multiplexed Pdu.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<MAX>2031</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
															</SUB-CONTAINERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: IpduMGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:315d58cd-393b-43c9-9743-757f43d5e78e">
									<SHORT-NAME>IpduMGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Contains the general configuration parameters of IpduM.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: IpduMConfigurationTimeBase -->
										<ECUC-FLOAT-PARAM-DEF UUID="ECUC:f674624b-eb02-4426-bda6-e01d27e1c4f2">
											<SHORT-NAME>IpduMConfigurationTimeBase</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The cycle time with which IpduM_MainFunction should  be invoked (in seconds).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>3600</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: IpduMDevErrorDetect -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:2ab08891-5dd2-4861-8432-8aedd0b61dfe">
											<SHORT-NAME>IpduMDevErrorDetect</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Active/Deactivate the detection of development errors, for production code this parameter has to be False.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">True: error detection activated
                                        False: error detection deactivated</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: IpduMStaticPartExists -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:9974a5d3-b6a8-4d53-98a8-415803a91d78">
											<SHORT-NAME>IpduMStaticPartExists</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This is to allow optimizations in the case the IpduM will never be used with a static part.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Note that this is a pre-compile option. If this is set to False then it will not be possible to add static parts after compilation.

                                        True: A static part may exist.
                                        False: A static part will never exist.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: IpduMVersionInfoApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:c3f380d8-b5fb-4fe4-a1ef-fc16979aaed6">
											<SHORT-NAME>IpduMVersionInfoApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Active/Deactivate the version information API.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: version information activated
                                        false: version information deactivated</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: IpduMPublishedInformation -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e49398c4-0f28-425e-8177-a0d5623eccfe">
									<SHORT-NAME>IpduMPublishedInformation</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Additional published parameters not covered by</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">CommonPublishedInformation container. Note that these parameters do not have any configuration class setting, since they are published information.</L-1>
										</P>
									</INTRODUCTION>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: IpduMRxDirectComInvocation -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:7624ef7a-7214-43a6-96a5-a30a54b6af64">
											<SHORT-NAME>IpduMRxDirectComInvocation</SHORT-NAME>
											<DESC>
												<L-2 L="EN">If set to TRUE the COM invocation optimization as defined in</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">IPDUM140 is  implemented.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
