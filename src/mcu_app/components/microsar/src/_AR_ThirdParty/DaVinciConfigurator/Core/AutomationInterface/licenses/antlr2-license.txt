ANTLR 2 License

We reserve no legal rights to the ANTLR--it is fully in the public domain. An individual or company may do
whatever they wish with source code distributed with ANTLR or the code generated by ANTLR, including the
incorporation of ANTLR, or its output, into commerical software.

We encourage users to develop software with ANTLR. However, we do ask that credit is given to us for
developing ANTLR. By "credit", we mean that if you use ANTLR or incorporate any source code into one of your
programs (commercial product, research project, or otherwise) that you acknowledge this fact somewhere in
the documentation, research report, etc... If you like ANTLR and have developed a nice tool with the output,
please mention that you developed it using ANTLR. In addition, we ask that the headers remain intact in our
source code. As long as these guidelines are kept, we expect to continue enhancing this system and expect to
make other tools available as they are completed.

In countries where the Public Domain status of the work may not be valid, the author grants a copyright
licence to the general public to deal in the work without restriction and permission to sublicence derivates
under the terms of any (OSI approved) Open Source licence.

The Python parser generator code under antlr/actions/python/ is covered by the 3-clause BSD licence (this
part is included in the binary JAR files); the run-time part under lib/python/ is covered by the GNU GPL,
version 3 or later (this part is not included in the binary JAR files). See [1] for the full details.

https://bugs.debian.org/cgi-bin/bugreport.cgi?bug=750643#80%22
