/*******************************************************************************
 * Copyright (c) 2010, 2017 IBM Corporation and others.
 *
 * This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License 2.0
 * which accompanies this distribution, and is available at
 * https://www.eclipse.org/legal/epl-2.0/
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *     <PERSON> <<EMAIL>> - Bug 420836
 *     <PERSON><PERSON> <<EMAIL>> - Bug 497591, 501250, 512385
 *******************************************************************************/

@import url("platform:/plugin/org.eclipse.ui.themes/css/e4_basestyle.css");

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_START {
	color: #F6F6F6;
}

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_END {
	color: #D6D6D6;
}

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_TAB_OUTER_KEYLINE_COLOR {
	color: #C4C5C1;
}

.MTrimmedWindow {
	background-color: #E8E8E8;
}

.MPartStack {
	swt-simple: false;
	swt-mru-visible: false;
	swt-unselected-tabs-color: #E8E8E8 #FFFFFF 100%;
	swt-outer-keyline-color: #FFFFFF;
	swt-inner-keyline-color: #FFFFFF;
	swt-tab-outline: #B6BCCC;
}

.MPartStack.active {
	swt-tab-outline: #B6BCCC;
	swt-shadow-visible: false;
	swt-outer-keyline-color: #C4C5C1;
}

.MTrimBar {
	background-color: #E8E8E8;
}

.MTrimBar#org-eclipse-ui-main-toolbar {
	background-color: #F0F0F0 #E8E8E8 100%;
}

.MToolControl.TrimStack {
	frame-image: url(./macTSFrame.png);
	handle-image: url(./macHandle.png);
	frame-image-rotated: url(./macTSFrame-rotated.png);
	handle-image-rotated: url(./macHandle-rotated.png);
	frame-cuts: 5px 1px 5px 16px;
}

.MToolBar.Draggable {
	handle-image: url(./macHandle.png);
}

.MTrimmedWindow {
	background-color: #E8E8E8;
}

#PerspectiveSwitcher {
	background-color: #F0F0F0 #E8E8E8 100%;
	eclipse-perspective-keyline-color: #E8E8E8 #E8E8E8;
	handle-image: url(./macHandle.png);
}

#org-eclipse-ui-editorss {
	swt-tab-height: 8px;
	padding: 0px 5px 7px;
	swt-unselected-tabs-color: #D6DDE5 #D6DDE5 #D6DDE5 100% 100%;
	swt-outer-keyline-color: #D6DDE5;
	swt-inner-keyline-color: #D6DDE5;
	swt-tab-outline: #D6DDE5;
	color: #D6DDE5;
}

CTabFolder.MArea .MPartStack, CTabFolder.MArea .MPartStack.active {
	swt-shadow-visible: false;
}

CTabFolder Canvas {
	background-color: #FFFFFF;
}

Form, FormHeading {
	background-color: #ffffff;
	background: #ffffff;
	background-image: #ffffff;
	color: #505050;
}

Form {
	/* Bug 465148: Additional styling for the Form */
	text-background-color: #ffffff;

	tb-toggle-hover-color: #505050;
	tb-toggle-color: #505050;
	h-hover-full-color: #505050;
	h-hover-light-color: #505050;
	h-bottom-keyline-2-color: #eaeaea;
	h-bottom-keyline-1-color: #eaeaea;
}


Section {
	background-color: #ffffff;
  	color: #505050;
  	background-color-titlebar: #eaeaea;
  	background-color-gradient-titlebar: #eaeaea;
  	border-color-titlebar: #ffffff;
}

CTabFolder {
  swt-selected-tabs-background: #CCE0F6 #3F97F9 100%;
}

CTabFolder CTabItem:selected {
  background-color: #E9E8E9;
}

TabbedPropertyTitle > CLabel{
	color: #505050;
}

TabbedPropertyTitle {
	swt-backgroundGradientStart-color:  #eaeaea;
	swt-backgroundGradientEnd-color:    #eaeaea;
	swt-backgroundBottomKeyline1-color: #eaeaea;
	swt-backgroundBottomKeyline2-color: #eaeaea;
}

TabbedPropertyList {
	swt-tabAreaBackground-color : #ffffff;
	swt-tabBackground-color     : '#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_BG_START';
	swt-tabNormalShadow-color   : '#org-eclipse-ui-workbench-INACTIVE_TAB_OUTLINE_COLOR';             /* color of shadow lines around the tabs */
	swt-tabDarkShadow-color     : '#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_SELECTED_TEXT_COLOR'; /* line color of the tiny scroll triangle (at top / at bottom) */
	color                       : #505050; /* text color in the tab / tab area */
}