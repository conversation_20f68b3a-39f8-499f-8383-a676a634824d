<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta name="generator" content=
"HTML Tidy for Linux/x86 (vers 25 March 2009), see www.w3.org" />
<meta http-equiv="Content-Type" content=
"text/html; charset=utf-8" />
<title>SAX License</title>

<style type="text/css">
/*<![CDATA[*/
<!--
        h1, h2, h3, h4, h5, h6 { color: maroon; }
        /* make sure that goes OK with the nav column background
         * net-friendly colors include: yellow/ffffcc, blue/ccccff
         */
    -->
/*]]>*/
</style>
</head>
<body color="#000000">
<h2>Origin</h2>
<p>This page was originally taken from: <a href=
"http://www.saxproject.org/copying.html">http://www.saxproject.org/copying.html</a>
with the navigation links remove from the left-hand-side of the
page.</p>
<h2>Copyright Status</h2>
<div>
<p><em>SAX is free!</em></p>
<p>In fact, it's not possible to own a license to SAX, since it's
been placed in the public domain.</p>
<h2>No Warranty</h2>
<p>Because SAX is released to the public domain, there is no
warranty for the design or for the software implementation, to the
extent permitted by applicable law. Except when otherwise stated in
writing the copyright holders and/or other parties provide SAX "as
is" without warranty of any kind, either expressed or implied,
including, but not limited to, the implied warranties of
merchantability and fitness for a particular purpose. The entire
risk as to the quality and performance of SAX is with you. Should
SAX prove defective, you assume the cost of all necessary
servicing, repair or correction.</p>
<p>In no event unless required by applicable law or agreed to in
writing will any copyright holder, or any other party who may
modify and/or redistribute SAX, be liable to you for damages,
including any general, special, incidental or consequential damages
arising out of the use or inability to use SAX (including but not
limited to loss of data or data being rendered inaccurate or losses
sustained by you or third parties or a failure of the SAX to
operate with any other programs), even if such holder or other
party has been advised of the possibility of such damages.</p>
<h2>Copyright Disclaimers</h2>
<p>This page includes statements to that effect by David Megginson,
who would have been able to claim copyright for the original
work.</p>
<!-- MAYBE:  link to archived copies of the messages? -->
<h3>SAX 1.0</h3>
<p>Version 1.0 of the Simple API for XML (SAX), created
collectively by the membership of the XML-DEV mailing list, is
hereby released into the public domain.</p>
<p>No one owns SAX: you may use it freely in both commercial and
non-commercial applications, bundle it with your software
distribution, include it on a CD-ROM, list the source code in a
book, mirror the documentation at your own web site, or use it in
any other way you see fit.</p>
<p><em>David Megginson, <a href=
"http://www.megginson.com/">Megginson Technologies Ltd.</a><br />
1998-05-11</em></p>
<h3>SAX 2.0</h3>
<p>I hereby abandon any property rights to SAX 2.0 (the Simple API
for XML), and release all of the SAX 2.0 source code, compiled
code, and documentation contained in this distribution into the
Public Domain. SAX comes with NO WARRANTY or guarantee of fitness
for any purpose.</p>
<p><em>David Megginson, <a href=
"http://www.megginson.com/">Megginson Technologies Ltd.</a><br />
2000-05-05</em></p>
</div>
<br />
</body>
</html>
