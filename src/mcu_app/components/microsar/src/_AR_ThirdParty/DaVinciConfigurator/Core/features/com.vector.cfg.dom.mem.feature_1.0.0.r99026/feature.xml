<?xml version="1.0" encoding="UTF-8"?>
<feature
      id="com.vector.cfg.dom.mem.feature"
      label="DaVinci Cfg Memory Domain"
      version="1.0.0.r99026"
      provider-name="Vector Informatik GmbH">

   <description>
      DaVinci Configurator memory domain.
   </description>

   <copyright>
      (c) Copyright Vector Informatik GmbH. All Rights Reserved.
   </copyright>

   <license url="http://www.vector.com">
      Vector Group License Agreement

This software is licensed under the terms and conditions of „Delivery and maintenance of software products” of Vector Group, see www.vector.com
   </license>

   <requires>
      <import plugin="org.eclipse.ui"/>
      <import plugin="org.eclipse.core.runtime"/>
      <import plugin="com.vector.cfg.util"/>
      <import plugin="com.vector.cfg.model"/>
      <import plugin="com.vector.cfg.model.query"/>
      <import plugin="com.vector.cfg.gui.core"/>
      <import plugin="com.vector.cfg.gui.core.ctrl"/>
      <import plugin="com.vector.cfg.business"/>
      <import plugin="com.vector.cfg.core"/>
      <import plugin="org.eclipse.ui.forms"/>
      <import plugin="com.vector.cfg.gui.gce"/>
      <import plugin="org.eclipse.core.databinding"/>
      <import plugin="org.eclipse.core.databinding.observable"/>
   </requires>

   <plugin
         id="com.vector.cfg.dom.mem.ui"
         download-size="0"
         install-size="0"
         version="1.0.0.99026"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.dom.mem.shared"
         download-size="0"
         install-size="0"
         version="2.0.0.99026"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.dom.mem"
         download-size="0"
         install-size="0"
         version="1.0.0.99026"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.dom.mem.validations"
         download-size="0"
         install-size="0"
         version="1.0.0.99026"
         unpack="false"/>

</feature>
