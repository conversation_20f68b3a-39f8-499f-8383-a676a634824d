import static com.vector.cfg.automation.api.ScriptApi.*

//daVin<PERSON> enables the IDE code completion support
da<PERSON><PERSON><PERSON> {
    scriptDescription "Creates a new Dpa Project and updates it with a communication extract"

    /* 
     * Task: UpdateProjectTask
     * Type: DV_APPLICATION
     * -------------------------------------------------------------------------------------------------------
     * Creates a project and updates it with a communication extract
     * -------------------------------------------------------------------------------------------------------
     */
    scriptTask("UpdateProjectTask", DV_APPLICATION) {
        taskDescription 'Creates a new Dpa Project and updates it with a communication extract'

        taskHelp '''CreateNewProject script task
    The task creates a project in the Script temp folder with the name "updateProjectFolder".
    Adds the communication extract 'e_Rx_simple_AR4.arxml'.
    executes an update.
    '''

        code {
            // Create a new Project
            def theProject = projects.createProject {
                projectName 'TheProject'
                projectFolder paths.resolveTempPath("updateProjectFolder")
                
                general {
                    createStartMenuEntries false
                }
                
                daVinciDeveloper{
                    createDaVinciDeveloperWorkspace false
                }
            }
            
            def extractPath = paths.resolveScriptPath("SupportFiles/e_Rx_simple_AR4.arxml")
            workflow {
                update(theProject){
                    input{
                        communication{
                           extract{
                                extractFiles{exFilePathList->
                                    exFilePathList.add(extractPath.asPersistablePath())
                                }

                                ecuInstanceSelection{
                                    // select the first ecu instance in the communication extract.
                                    // Note: this closure is deferred executed.
                                    return availableEcuInstances[0]
                                }
                           }
                        }
                    }
                }
            }
            scriptLogger.info("Project created and update, location: $theProject")
        }
    }

}
