<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: EthSM -->
						<ECUC-MODULE-DEF UUID="ECUC:6f8c16f8-2680-4ddd-9b38-31fd814152b8">
							<SHORT-NAME>EthSM</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the Ethernet State Manager</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.4.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
										<DATE>2013-10-31</DATE>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: EthSMGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:78fcf290-0b0b-492a-af4e-69b20bc58c9f">
									<SHORT-NAME>EthSMGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container contains the global parameter of the Ethernet State Manager.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: EthSMDevErrorDetect -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:8d8442fe-424d-4bf4-b0b0-a6cc37dee29b">
											<SHORT-NAME>EthSMDevErrorDetect</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables and disables the development error detection and notification mechanism.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthSMDummyMode -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:f081696f-77a7-4ebd-9df9-400a656ab75f">
											<SHORT-NAME>EthSMDummyMode</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Disables the API to the EthIf. The API to the ComM is available but the functionality is deactivated. The function calls from the ComM will be answered with the return value E_OK.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthSMMainFunctionPeriod -->
										<ECUC-FLOAT-PARAM-DEF UUID="ECUC:e46ee864-8ae6-4a07-8e2f-817073cbecb6">
											<SHORT-NAME>EthSMMainFunctionPeriod</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the period in seconds that the MainFunction has to be triggered with.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>1</MAX>
											<MIN>0.005</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthSMVersionInfoApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:0cd3155a-3f2b-4ad8-bcc7-36205264a3f4">
											<SHORT-NAME>EthSMVersionInfoApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables and disables the version info API.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: EthSMNetwork -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:b14ff5a3-8d2d-4f9b-aed4-508801a04f62">
									<SHORT-NAME>EthSMNetwork</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container contains the Ethernet network-specific parameters of each Ethernet network. It also contains the controller and transceiver IDs assigned to a Ethernet network.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: EthSMConfirmationTimeout -->
										<ECUC-FLOAT-PARAM-DEF UUID="ECUC:721b10c3-a34d-4b9c-8249-306c48c324fa">
											<SHORT-NAME>EthSMConfirmationTimeout</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Timeout in seconds for the calls to EthIf:</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">EthIf_ControllerInit
                                        EthIf_TransceiverInit
                                        EthIf_SetControllerMode
                                        EthIf_SetTransceiverMode</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>Inf</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Symbolic Name Reference Definition: EthSMComMNetworkHandleRef -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:90fd482d-e9f2-436b-8dd2-ca69eccc3407">
											<SHORT-NAME>EthSMComMNetworkHandleRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Unique handle to identify one certain Ethernet network. Reference to one of the network handles configured for the ComM.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/ComM/ComMConfigSet/ComMChannel</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
										<!-- Symbolic Name Reference Definition: EthSMEthIfControllerRef -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:ca206552-a24a-42d9-a7a4-222ecba045e6">
											<SHORT-NAME>EthSMEthIfControllerRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to EthIfCtrl container where a ETH controller and</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">transceiver (optional) combination is configured.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EthIf/EthIfConfigSet/EthIfController</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<!-- Container Definition: EthSMDemEventParameterRefs -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:afdeaabf-81fe-4291-b326-292ae18ea6ff">
											<SHORT-NAME>EthSMDemEventParameterRefs</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for the references to DemEventParameter elements which shall be invoked using the API Dem_ReportErrorStatus API in case the corresponding error occurs. The EventId is taken from the referenced DemEventParameter&apos;s DemEventId value. The standardized errors are provided in the container and can be extended by vendor specific error references.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<REFERENCES>
												<!-- Symbolic Name Reference Definition: ETHSM_E_LINK_DOWN -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:402b4fad-2686-4096-a2fd-aeab95f3b91a">
													<SHORT-NAME>ETHSM_E_LINK_DOWN</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to configured DEM event to report bus off errors for this Eth network.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
