#encoding=UTF-8
#version=1
aquintos.auth.log,9.0.0.************,plugins/aquintos.auth.log_9.0.0.************.jar,4,false
aquintos.authentication,9.0.0.************,plugins/aquintos.authentication_9.0.0.************.jar,4,false
aquintos.commonresources,9.0.0.************,plugins/aquintos.commonresources_9.0.0.************.jar,4,false
aquintos.deltamodel,9.0.0.************,plugins/aquintos.deltamodel_9.0.0.************.jar,4,false
aquintos.log,9.0.0.************,plugins/aquintos.log_9.0.0.************.jar,4,false
aquintos.mdf,9.0.0.************,plugins/aquintos.mdf_9.0.0.************.jar,4,false
aquintos.mmregistry,9.0.0.************,plugins/aquintos.mmregistry_9.0.0.************.jar,4,false
aquintos.modelbridge,9.0.0.************,plugins/aquintos.modelbridge_9.0.0.************.jar,4,false
aquintos.operationsframework,9.0.0.************,plugins/aquintos.operationsframework_9.0.0.************.jar,4,false
aquintos.utilities,9.0.0.************,plugins/aquintos.utilities_9.0.0.************.jar,4,false
aquintos.uuid,9.0.0.************,plugins/aquintos.uuid_9.0.0.************.jar,4,false
ca.odell.glazedlists,1.9.0.v201303080712,plugins/ca.odell.glazedlists_1.9.0.v201303080712.jar,4,false
com.codemeter,6.40.2405.************,plugins/com.codemeter_6.40.2405.************.jar,4,false
com.google.guava,27.1.0.v20190517-1946,plugins/com.google.guava_27.1.0.v20190517-1946.jar,4,false
com.google.guava,25.1.0.jre,plugins/com.google.guava_25.1.0.jre.jar,4,false
com.google.guava,18.0.0.p1,plugins/com.google.guava_18.0.0.p1.jar,4,false
com.google.inject,4.2.2,plugins/com.google.inject_4.2.2.jar,4,false
com.google.inject.assistedinject,4.2.2,plugins/com.google.inject.assistedinject_4.2.2.jar,4,false
com.ibm.icu,64.2.0.v20190507-1337,plugins/com.ibm.icu_64.2.0.v20190507-1337.jar,4,false
com.lmax.disruptor,3.4.2,plugins/com.lmax.disruptor_3.4.2.jar,4,false
com.ning.compress-lzf,1.0.5.************,plugins/com.ning.compress-lzf_1.0.5.************.jar,4,false
com.sun.el,2.2.0.v201303151357,plugins/com.sun.el_2.2.0.v201303151357.jar,4,false
com.vector.annotations.obfuscation,1.0.1,plugins/com.vector.annotations.obfuscation_1.0.1.jar,4,false
com.vector.annotations.publishedapi,1.0.0.99026,plugins/com.vector.annotations.publishedapi_1.0.0.99026.jar,4,false
com.vector.cfg.app.changenotification,1.0.0.99026,plugins/com.vector.cfg.app.changenotification_1.0.0.99026.jar,4,false
com.vector.cfg.app.workspace,1.0.0.99026,plugins/com.vector.cfg.app.workspace_1.0.0.99026.jar,3,true
com.vector.cfg.automation,1.0.0.99026,plugins/com.vector.cfg.automation_1.0.0.99026.jar,4,false
com.vector.cfg.automation.api,1.0.0.99026,plugins/com.vector.cfg.automation.api_1.0.0.99026.jar,4,false
com.vector.cfg.automation.api.testinfrastructure.published,1.0.0.99026,plugins/com.vector.cfg.automation.api.testinfrastructure.published_1.0.0.99026.jar,4,false
com.vector.cfg.automation.app,1.0.0.99026,plugins/com.vector.cfg.automation.app_1.0.0.99026.jar,4,false
com.vector.cfg.automation.classloading,1.0.0.99026,plugins/com.vector.cfg.automation.classloading_1.0.0.99026.jar,4,false
com.vector.cfg.automation.classloading.msr,1.0.0.99026,plugins/com.vector.cfg.automation.classloading.msr_1.0.0.99026.jar,4,false
com.vector.cfg.automation.console,1.0.0.99026,plugins/com.vector.cfg.automation.console_1.0.0.99026.jar,4,false
com.vector.cfg.automation.scripting.api,1.0.0.99026,plugins/com.vector.cfg.automation.scripting.api_1.0.0.99026.jar,4,false
com.vector.cfg.automation.scripting.base,1.0.0.99026,plugins/com.vector.cfg.automation.scripting.base_1.0.0.99026.jar,4,false
com.vector.cfg.automation.scripting.groovy,1.0.0.99026,plugins/com.vector.cfg.automation.scripting.groovy_1.0.0.99026.jar,4,false
com.vector.cfg.business,1.0.0.99026,plugins/com.vector.cfg.business_1.0.0.99026.jar,4,false
com.vector.cfg.business.defrefs,1.0.0.99026,plugins/com.vector.cfg.business.defrefs_1.0.0.99026.jar,4,false
com.vector.cfg.business.msr,1.0.0.99026,plugins/com.vector.cfg.business.msr_1.0.0.99026.jar,4,false
com.vector.cfg.business.usecases,1.0.0.99026,plugins/com.vector.cfg.business.usecases_1.0.0.99026.jar,4,false
com.vector.cfg.business.vtt,1.0.0.99026,plugins/com.vector.cfg.business.vtt_1.0.0.99026.jar,4,false
com.vector.cfg.business.vtt.msr,1.0.0.99026,plugins/com.vector.cfg.business.vtt.msr_1.0.0.99026.jar,4,false
com.vector.cfg.consistency.contribution.fw,1.0.0.99026,plugins/com.vector.cfg.consistency.contribution.fw_1.0.0.99026.jar,4,false
com.vector.cfg.consistency.contribution.if,1.0.0.99026,plugins/com.vector.cfg.consistency.contribution.if_1.0.0.99026.jar,4,false
com.vector.cfg.consistency.contribution.msr.impl,1.0.0.99026,plugins/com.vector.cfg.consistency.contribution.msr.impl_1.0.0.99026.jar,4,false
com.vector.cfg.consistency.contribution.pai.impl,1.0.0.99026,plugins/com.vector.cfg.consistency.contribution.pai.impl_1.0.0.99026.jar,4,false
com.vector.cfg.consistency.contribution.util,1.0.0.99026,plugins/com.vector.cfg.consistency.contribution.util_1.0.0.99026.jar,4,false
com.vector.cfg.consistency.core.if,1.0.0.99026,plugins/com.vector.cfg.consistency.core.if_1.0.0.99026.jar,4,false
com.vector.cfg.consistency.core.impl,1.0.0.99026,plugins/com.vector.cfg.consistency.core.impl_1.0.0.99026.jar,4,false
com.vector.cfg.consistency.core.internal.if,1.0.0.99026,plugins/com.vector.cfg.consistency.core.internal.if_1.0.0.99026.jar,4,false
com.vector.cfg.consistency.core.msr.impl,1.0.0.99026,plugins/com.vector.cfg.consistency.core.msr.impl_1.0.0.99026.jar,4,false
com.vector.cfg.consistency.core.util,1.0.0.99026,plugins/com.vector.cfg.consistency.core.util_1.0.0.99026.jar,4,false
com.vector.cfg.consistency.gencore.legacy,1.0.0.99026,plugins/com.vector.cfg.consistency.gencore.legacy_1.0.0.99026.jar,4,false
com.vector.cfg.consistency.publishedapi.gencorevalidators.legacy,1.0.0.99026,plugins/com.vector.cfg.consistency.publishedapi.gencorevalidators.legacy_1.0.0.99026.jar,4,false
com.vector.cfg.consistency.publishedapi.legacy,1.0.0.99026,plugins/com.vector.cfg.consistency.publishedapi.legacy_1.0.0.99026.jar,4,false
com.vector.cfg.consistency.publishedapi.pai.legacy,1.0.0.99026,plugins/com.vector.cfg.consistency.publishedapi.pai.legacy_1.0.0.99026.jar,4,false
com.vector.cfg.consistency.requester.if,1.0.0.99026,plugins/com.vector.cfg.consistency.requester.if_1.0.0.99026.jar,4,false
com.vector.cfg.consistency.requester.impl,1.0.0.99026,plugins/com.vector.cfg.consistency.requester.impl_1.0.0.99026.jar,4,false
com.vector.cfg.consistency.requester.pai.if,1.0.0.99026,plugins/com.vector.cfg.consistency.requester.pai.if_1.0.0.99026.jar,4,false
com.vector.cfg.consistency.requester.pai.impl,1.0.0.99026,plugins/com.vector.cfg.consistency.requester.pai.impl_1.0.0.99026.jar,4,false
com.vector.cfg.consistency.requester.util,1.0.0.99026,plugins/com.vector.cfg.consistency.requester.util_1.0.0.99026.jar,4,false
com.vector.cfg.console.app,5.22.40.99026,plugins/com.vector.cfg.console.app_5.22.40.99026.jar,4,false
com.vector.cfg.console.app.base,1.0.0.99026,plugins/com.vector.cfg.console.app.base_1.0.0.99026.jar,4,false
com.vector.cfg.console.base,1.0.0.99026,plugins/com.vector.cfg.console.base_1.0.0.99026.jar,4,false
com.vector.cfg.console.converter,1.0.0.99026,plugins/com.vector.cfg.console.converter_1.0.0.99026.jar,4,false
com.vector.cfg.console.customer,1.0.0.99026,plugins/com.vector.cfg.console.customer_1.0.0.99026.jar,4,false
com.vector.cfg.console.exporter,1.0.0.99026,plugins/com.vector.cfg.console.exporter_1.0.0.99026.jar,4,false
com.vector.cfg.console.project,1.0.0.99026,plugins/com.vector.cfg.console.project_1.0.0.99026.jar,4,false
com.vector.cfg.core,5.28.0.99026,plugins/com.vector.cfg.core_5.28.0.99026.jar,4,false
com.vector.cfg.core.app,1.0.0.99026,plugins/com.vector.cfg.core.app_1.0.0.99026.jar,4,false
com.vector.cfg.core.license.dvcfg,1.0.0.99026,plugins/com.vector.cfg.core.license.dvcfg_1.0.0.99026.jar,4,false
com.vector.cfg.core.operation,1.0.0.99026,plugins/com.vector.cfg.core.operation_1.0.0.99026.jar,4,false
com.vector.cfg.core.sip,1.0.0.99026,plugins/com.vector.cfg.core.sip_1.0.0.99026.jar,4,false
com.vector.cfg.core.sip.msr,1.0.0.99026,plugins/com.vector.cfg.core.sip.msr_1.0.0.99026.jar,4,false
com.vector.cfg.core.tooldefinition,5.22.40.99026,plugins/com.vector.cfg.core.tooldefinition_5.22.40.99026.jar,4,false
com.vector.cfg.customersupport.srp,1.0.0.99026,plugins/com.vector.cfg.customersupport.srp_1.0.0.99026.jar,4,false
com.vector.cfg.customersupport.srp.extension.rte,1.0.0.99026,plugins/com.vector.cfg.customersupport.srp.extension.rte_1.0.0.99026.jar,4,false
com.vector.cfg.customersupport.srp.pai.if,1.0.0.99026,plugins/com.vector.cfg.customersupport.srp.pai.if_1.0.0.99026.jar,4,false
com.vector.cfg.customersupport.srp.pai.impl,1.0.0.99026,plugins/com.vector.cfg.customersupport.srp.pai.impl_1.0.0.99026.jar,4,false
com.vector.cfg.datamining,1.0.0.99026,plugins/com.vector.cfg.datamining_1.0.0.99026.jar,4,false
com.vector.cfg.datamining.pai.if,1.0.0.99026,plugins/com.vector.cfg.datamining.pai.if_1.0.0.99026.jar,4,false
com.vector.cfg.datamining.pai.impl,1.0.0.99026,plugins/com.vector.cfg.datamining.pai.impl_1.0.0.99026.jar,4,false
com.vector.cfg.dom.base.ui,1.0.0.99026,plugins/com.vector.cfg.dom.base.ui_1.0.0.99026.jar,4,false
com.vector.cfg.dom.com,1.0.0.99026,plugins/com.vector.cfg.dom.com_1.0.0.99026.jar,4,false
com.vector.cfg.dom.com.groovy,1.0.0.99026,plugins/com.vector.cfg.dom.com.groovy_1.0.0.99026.jar,4,false
com.vector.cfg.dom.com.model.gui,1.0.0.99026,plugins/com.vector.cfg.dom.com.model.gui_1.0.0.99026.jar,4,false
com.vector.cfg.dom.com.ui,1.0.0.99026,plugins/com.vector.cfg.dom.com.ui_1.0.0.99026.jar,4,false
com.vector.cfg.dom.comctrl.ui,1.0.0.99026,plugins/com.vector.cfg.dom.comctrl.ui_1.0.0.99026.jar,4,false
com.vector.cfg.dom.diagnostics,1.0.0.99026,plugins/com.vector.cfg.dom.diagnostics_1.0.0.99026.jar,4,false
com.vector.cfg.dom.diagnostics.groovy,1.0.0.99026,plugins/com.vector.cfg.dom.diagnostics.groovy_1.0.0.99026.jar,4,false
com.vector.cfg.dom.diagnostics.ui,1.0.0.99026,plugins/com.vector.cfg.dom.diagnostics.ui_1.0.0.99026.jar,4,false
com.vector.cfg.dom.io.ui,1.0.0.99026,plugins/com.vector.cfg.dom.io.ui_1.0.0.99026.jar,4,false
com.vector.cfg.dom.measurecalib,1.0.0.99026,plugins/com.vector.cfg.dom.measurecalib_1.0.0.99026.jar,4,false
com.vector.cfg.dom.mem,1.0.0.99026,plugins/com.vector.cfg.dom.mem_1.0.0.99026.jar,4,false
com.vector.cfg.dom.mem.shared,2.0.0.99026,plugins/com.vector.cfg.dom.mem.shared_2.0.0.99026.jar,4,false
com.vector.cfg.dom.mem.ui,1.0.0.99026,plugins/com.vector.cfg.dom.mem.ui_1.0.0.99026.jar,4,false
com.vector.cfg.dom.mem.validations,1.0.0.99026,plugins/com.vector.cfg.dom.mem.validations_1.0.0.99026.jar,4,false
com.vector.cfg.dom.modemgt,1.0.0.99026,plugins/com.vector.cfg.dom.modemgt_1.0.0.99026.jar,4,false
com.vector.cfg.dom.modemgt.groovy,1.0.0.99026,plugins/com.vector.cfg.dom.modemgt.groovy_1.0.0.99026.jar,4,false
com.vector.cfg.dom.modemgt.ui,1.0.0.99026,plugins/com.vector.cfg.dom.modemgt.ui_1.0.0.99026.jar,4,false
com.vector.cfg.dom.runtimesys,1.0.0.99026,plugins/com.vector.cfg.dom.runtimesys_1.0.0.99026.jar,4,false
com.vector.cfg.dom.runtimesys.groovy,1.0.0.99026,plugins/com.vector.cfg.dom.runtimesys.groovy_1.0.0.99026.jar,4,false
com.vector.cfg.dom.runtimesys.memmap,1.0.0.99026,plugins/com.vector.cfg.dom.runtimesys.memmap_1.0.0.99026.jar,4,false
com.vector.cfg.dom.runtimesys.ui,1.0.0.99026,plugins/com.vector.cfg.dom.runtimesys.ui_1.0.0.99026.jar,4,false
com.vector.cfg.dom.ui.image,1.0.0.99026,plugins/com.vector.cfg.dom.ui.image_1.0.0.99026.jar,4,false
com.vector.cfg.gen.bswmdmodelgenerator,1.6.7.99026,plugins/com.vector.cfg.gen.bswmdmodelgenerator_1.6.7.99026.jar,4,false
com.vector.cfg.gen.bswmdmodelgenerator.app,1.0.0.99026,plugins/com.vector.cfg.gen.bswmdmodelgenerator.app_1.0.0.99026.jar,4,false
com.vector.cfg.gen.bswmdmodelgenerator.common,1.0.0.99026,plugins/com.vector.cfg.gen.bswmdmodelgenerator.common_1.0.0.99026.jar,4,false
com.vector.cfg.gen.bswmdmodelgenerator.generatorconfiguration,1.0.0.99026,plugins/com.vector.cfg.gen.bswmdmodelgenerator.generatorconfiguration_1.0.0.99026.jar,4,false
com.vector.cfg.gen.bswmdmodelgenerator.model,1.0.0.99026,plugins/com.vector.cfg.gen.bswmdmodelgenerator.model_1.0.0.99026.jar,4,false
com.vector.cfg.gen.bswmdmodelgenerator.target.gen,1.0.0.99026,plugins/com.vector.cfg.gen.bswmdmodelgenerator.target.gen_1.0.0.99026.jar,4,false
com.vector.cfg.gen.core.bswmdmigration,1.0.0.99026,plugins/com.vector.cfg.gen.core.bswmdmigration_1.0.0.99026.jar,4,false
com.vector.cfg.gen.core.bswmdmigration.groovy,1.0.0.99026,plugins/com.vector.cfg.gen.core.bswmdmigration.groovy_1.0.0.99026.jar,4,false
com.vector.cfg.gen.core.bswmdmodel.groovy,1.0.0.99026,plugins/com.vector.cfg.gen.core.bswmdmodel.groovy_1.0.0.99026.jar,4,false
com.vector.cfg.gen.core.contributions,1.0.0.99026,plugins/com.vector.cfg.gen.core.contributions_1.0.0.99026.jar,4,false
com.vector.cfg.gen.core.contributions.dom.base,1.0.0.99026,plugins/com.vector.cfg.gen.core.contributions.dom.base_1.0.0.99026.jar,4,false
com.vector.cfg.gen.core.genclassloading,1.0.0.99026,plugins/com.vector.cfg.gen.core.genclassloading_1.0.0.99026.jar,4,false
com.vector.cfg.gen.core.genclassloading.msr,1.0.0.99026,plugins/com.vector.cfg.gen.core.genclassloading.msr_1.0.0.99026.jar,4,false
com.vector.cfg.gen.core.gencommon,1.0.0.99026,plugins/com.vector.cfg.gen.core.gencommon_1.0.0.99026.jar,4,false
com.vector.cfg.gen.core.gencore,1.0.0.99026,plugins/com.vector.cfg.gen.core.gencore_1.0.0.99026.jar,4,false
com.vector.cfg.gen.core.gencore.msr,1.0.0.99026,plugins/com.vector.cfg.gen.core.gencore.msr_1.0.0.99026.jar,4,false
com.vector.cfg.gen.core.genusage,1.0.0.99026,plugins/com.vector.cfg.gen.core.genusage_1.0.0.99026.jar,4,false
com.vector.cfg.gen.core.genusage.console,1.0.0.99026,plugins/com.vector.cfg.gen.core.genusage.console_1.0.0.99026.jar,4,false
com.vector.cfg.gen.core.genusage.groovy,1.0.0.99026,plugins/com.vector.cfg.gen.core.genusage.groovy_1.0.0.99026.jar,4,false
com.vector.cfg.gen.core.moduleInterface,1.0.0.99026,plugins/com.vector.cfg.gen.core.moduleInterface_1.0.0.99026.jar,4,false
com.vector.cfg.gen.core.signing.console,1.0.0.99026,plugins/com.vector.cfg.gen.core.signing.console_1.0.0.99026.jar,4,false
com.vector.cfg.gen.core.testinfrastructure.published,1.0.0.99026,plugins/com.vector.cfg.gen.core.testinfrastructure.published_1.0.0.99026.jar,4,false
com.vector.cfg.gen.core.utils,1.0.0.99026,plugins/com.vector.cfg.gen.core.utils_1.0.0.99026.jar,4,false
com.vector.cfg.gen.vtt.published,1.0.0.99026,plugins/com.vector.cfg.gen.vtt.published_1.0.0.99026.jar,4,false
com.vector.cfg.gui.activity,1.0.0.99026,plugins/com.vector.cfg.gui.activity_1.0.0.99026.jar,4,false
com.vector.cfg.gui.app,1.0.0.99026,plugins/com.vector.cfg.gui.app_1.0.0.99026.jar,4,false
com.vector.cfg.gui.app.dvcfg,1.0.0.99026,plugins/com.vector.cfg.gui.app.dvcfg_1.0.0.99026.jar,4,false
com.vector.cfg.gui.branding.dvcfg,5.22.40.r99026,plugins/com.vector.cfg.gui.branding.dvcfg_5.22.40.r99026.jar,4,false
com.vector.cfg.gui.core,1.0.0.99026,plugins/com.vector.cfg.gui.core_1.0.0.99026.jar,4,false
com.vector.cfg.gui.core.ctrl,1.0.0.99026,plugins/com.vector.cfg.gui.core.ctrl_1.0.0.99026.jar,4,false
com.vector.cfg.gui.core.error,1.0.0.99026,plugins/com.vector.cfg.gui.core.error_1.0.0.99026.jar,4,false
com.vector.cfg.gui.core.image.resource,1.0.0.99026,plugins/com.vector.cfg.gui.core.image.resource_1.0.0.99026.jar,4,false
com.vector.cfg.gui.core.image.service,1.0.0.99026,plugins/com.vector.cfg.gui.core.image.service_1.0.0.99026.jar,4,false
com.vector.cfg.gui.danglingreference,1.0.0.99026,plugins/com.vector.cfg.gui.danglingreference_1.0.0.99026.jar,4,false
com.vector.cfg.gui.errorlog,1.0.0.99026,plugins/com.vector.cfg.gui.errorlog_1.0.0.99026.jar,4,false
com.vector.cfg.gui.findview,1.0.0.99026,plugins/com.vector.cfg.gui.findview_1.0.0.99026.jar,4,false
com.vector.cfg.gui.gce,1.0.0.99026,plugins/com.vector.cfg.gui.gce_1.0.0.99026.jar,4,false
com.vector.cfg.gui.gen,1.0.0.99026,plugins/com.vector.cfg.gui.gen_1.0.0.99026.jar,4,false
com.vector.cfg.gui.graphframework.fw,1.0.0.99026,plugins/com.vector.cfg.gui.graphframework.fw_1.0.0.99026.jar,4,false
com.vector.cfg.gui.persistency.msr,1.0.0.99026,plugins/com.vector.cfg.gui.persistency.msr_1.0.0.99026.jar,4,false
com.vector.cfg.gui.pse.dvcfg,1.0.0.99026,plugins/com.vector.cfg.gui.pse.dvcfg_1.0.0.99026.jar,4,false
com.vector.cfg.gui.scripting,1.0.0.99026,plugins/com.vector.cfg.gui.scripting_1.0.0.99026.jar,4,false
com.vector.cfg.gui.sip.msr,1.0.0.99026,plugins/com.vector.cfg.gui.sip.msr_1.0.0.99026.jar,4,false
com.vector.cfg.gui.vtt.msr,1.0.0.99026,plugins/com.vector.cfg.gui.vtt.msr_1.0.0.99026.jar,4,false
com.vector.cfg.gui.workflow.diff,1.0.0.99026,plugins/com.vector.cfg.gui.workflow.diff_1.0.0.99026.jar,4,false
com.vector.cfg.gui.workflow.vase,1.0.0.99026,plugins/com.vector.cfg.gui.workflow.vase_1.0.0.99026.jar,4,false
com.vector.cfg.installation.msr,1.0.0.99026,plugins/com.vector.cfg.installation.msr_1.0.0.99026.jar,4,false
com.vector.cfg.installation.msr.win32,1.0.0.99026,plugins/com.vector.cfg.installation.msr.win32_1.0.0.99026.jar,4,false
com.vector.cfg.interop.dvdev.impl,1.0.0.99026,plugins/com.vector.cfg.interop.dvdev.impl_1.0.0.99026.jar,4,false
com.vector.cfg.interop.if,1.0.0.99026,plugins/com.vector.cfg.interop.if_1.0.0.99026.jar,4,false
com.vector.cfg.interop.impl,1.0.0.99026,plugins/com.vector.cfg.interop.impl_1.0.0.99026.jar,4,false
com.vector.cfg.interop.tats.if,1.0.0.99026,plugins/com.vector.cfg.interop.tats.if_1.0.0.99026.jar,4,false
com.vector.cfg.interop.tats.impl,1.0.0.99026,plugins/com.vector.cfg.interop.tats.impl_1.0.0.99026.jar,4,false
com.vector.cfg.license.appservice,1.0.0.99026,plugins/com.vector.cfg.license.appservice_1.0.0.99026.jar,4,false
com.vector.cfg.mdf.meta,1.0.0.99026,plugins/com.vector.cfg.mdf.meta_1.0.0.99026.jar,4,false
com.vector.cfg.model,1.0.0.99026,plugins/com.vector.cfg.model_1.0.0.99026.jar,4,false
com.vector.cfg.model.abstraction,1.0.0.99026,plugins/com.vector.cfg.model.abstraction_1.0.0.99026.jar,4,false
com.vector.cfg.model.asr.annotation,1.0.0.99026,plugins/com.vector.cfg.model.asr.annotation_1.0.0.99026.jar,4,false
com.vector.cfg.model.asr.annotation.api,1.0.0.99026,plugins/com.vector.cfg.model.asr.annotation.api_1.0.0.99026.jar,4,false
com.vector.cfg.model.asr.api,1.0.0.99026,plugins/com.vector.cfg.model.asr.api_1.0.0.99026.jar,4,false
com.vector.cfg.model.asr.ecuc,1.0.0.99026,plugins/com.vector.cfg.model.asr.ecuc_1.0.0.99026.jar,4,false
com.vector.cfg.model.asr.ecuc.api,1.0.0.99026,plugins/com.vector.cfg.model.asr.ecuc.api_1.0.0.99026.jar,4,false
com.vector.cfg.model.asr.ecuc.groovy,1.0.0.99026,plugins/com.vector.cfg.model.asr.ecuc.groovy_1.0.0.99026.jar,4,false
com.vector.cfg.model.asr.formula.api,1.0.0.99026,plugins/com.vector.cfg.model.asr.formula.api_1.0.0.99026.jar,4,false
com.vector.cfg.model.asr.variance,1.0.0.99026,plugins/com.vector.cfg.model.asr.variance_1.0.0.99026.jar,4,false
com.vector.cfg.model.asr.variance.api,1.0.0.99026,plugins/com.vector.cfg.model.asr.variance.api_1.0.0.99026.jar,4,false
com.vector.cfg.model.asr.view.api,1.0.0.99026,plugins/com.vector.cfg.model.asr.view.api_1.0.0.99026.jar,4,false
com.vector.cfg.model.base,1.0.0.99026,plugins/com.vector.cfg.model.base_1.0.0.99026.jar,4,false
com.vector.cfg.model.base.api,1.0.0.99026,plugins/com.vector.cfg.model.base.api_1.0.0.99026.jar,4,false
com.vector.cfg.model.formula,1.0.0.99026,plugins/com.vector.cfg.model.formula_1.0.0.99026.jar,4,false
com.vector.cfg.model.formula.asr,1.0.0.99026,plugins/com.vector.cfg.model.formula.asr_1.0.0.99026.jar,4,false
com.vector.cfg.model.groovy,1.0.0.99026,plugins/com.vector.cfg.model.groovy_1.0.0.99026.jar,4,false
com.vector.cfg.model.location.mapping.contribution.if,1.0.0.99026,plugins/com.vector.cfg.model.location.mapping.contribution.if_1.0.0.99026.jar,4,false
com.vector.cfg.model.location.mapping.if,1.0.0.99026,plugins/com.vector.cfg.model.location.mapping.if_1.0.0.99026.jar,4,false
com.vector.cfg.model.location.mapping.impl,1.0.0.99026,plugins/com.vector.cfg.model.location.mapping.impl_1.0.0.99026.jar,4,false
com.vector.cfg.model.location.mapping.internal.if,1.0.0.99026,plugins/com.vector.cfg.model.location.mapping.internal.if_1.0.0.99026.jar,4,false
com.vector.cfg.model.mdf,1.0.0.99026,plugins/com.vector.cfg.model.mdf_1.0.0.99026.jar,4,false
com.vector.cfg.model.mdf.asr.base,1.0.0.99026,plugins/com.vector.cfg.model.mdf.asr.base_1.0.0.99026.jar,4,false
com.vector.cfg.model.mdf.asr.latest,1.0.0.99026,plugins/com.vector.cfg.model.mdf.asr.latest_1.0.0.99026.jar,4,false
com.vector.cfg.model.mdf.asr.latest.api,1.0.0.99026,plugins/com.vector.cfg.model.mdf.asr.latest.api_1.0.0.99026.jar,4,false
com.vector.cfg.model.mdf.authentication,1.0.0.99026,plugins/com.vector.cfg.model.mdf.authentication_1.0.0.99026.jar,4,false
com.vector.cfg.model.query,1.0.0.99026,plugins/com.vector.cfg.model.query_1.0.0.99026.jar,4,false
com.vector.cfg.model.recorder,1.0.0.99026,plugins/com.vector.cfg.model.recorder_1.0.0.99026.jar,4,false
com.vector.cfg.model.rules,1.0.0.99026,plugins/com.vector.cfg.model.rules_1.0.0.99026.jar,4,false
com.vector.cfg.model.services,1.0.0.99026,plugins/com.vector.cfg.model.services_1.0.0.99026.jar,4,false
com.vector.cfg.model.services.client,1.0.0.99026,plugins/com.vector.cfg.model.services.client_1.0.0.99026.jar,4,false
com.vector.cfg.model.services.msr,1.0.0.99026,plugins/com.vector.cfg.model.services.msr_1.0.0.99026.jar,4,false
com.vector.cfg.model.swcTemplates,1.0.0.99026,plugins/com.vector.cfg.model.swcTemplates_1.0.0.99026.jar,4,false
com.vector.cfg.model.sysdesc,1.0.0.99026,plugins/com.vector.cfg.model.sysdesc_1.0.0.99026.jar,4,false
com.vector.cfg.model.sysdesc.validation,1.0.0.99026,plugins/com.vector.cfg.model.sysdesc.validation_1.0.0.99026.jar,4,false
com.vector.cfg.model.testinfrastructure.published,1.0.0.99026,plugins/com.vector.cfg.model.testinfrastructure.published_1.0.0.99026.jar,4,false
com.vector.cfg.model.unit,1.0.0.99026,plugins/com.vector.cfg.model.unit_1.0.0.99026.jar,4,false
com.vector.cfg.model.uow,1.0.0.99026,plugins/com.vector.cfg.model.uow_1.0.0.99026.jar,4,false
com.vector.cfg.model.uow.api,1.0.0.99026,plugins/com.vector.cfg.model.uow.api_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.addons.contribution,1.0.0.99026,plugins/com.vector.cfg.persistency.addons.contribution_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.addons.copypaste.msr,1.0.0.99026,plugins/com.vector.cfg.persistency.addons.copypaste.msr_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.addons.exportfilter,1.0.0.99026,plugins/com.vector.cfg.persistency.addons.exportfilter_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.addons.filesupervision.msr,1.0.0.99026,plugins/com.vector.cfg.persistency.addons.filesupervision.msr_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.addons.msr,1.0.0.99026,plugins/com.vector.cfg.persistency.addons.msr_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.addons.published,1.0.0.99026,plugins/com.vector.cfg.persistency.addons.published_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.annotations.copypaste,1.0.0.99026,plugins/com.vector.cfg.persistency.annotations.copypaste_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.arxml.resources.msr,1.0.0.99026,plugins/com.vector.cfg.persistency.arxml.resources.msr_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.base,1.0.0.99026,plugins/com.vector.cfg.persistency.base_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.base.contribution,1.0.0.99026,plugins/com.vector.cfg.persistency.base.contribution_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.dpa,1.0.0.99026,plugins/com.vector.cfg.persistency.dpa_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.exporter,1.0.0.99026,plugins/com.vector.cfg.persistency.exporter_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.groovy,1.0.0.99026,plugins/com.vector.cfg.persistency.groovy_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.jaxb,1.0.0.99026,plugins/com.vector.cfg.persistency.jaxb_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.jaxb.locations,1.0.0.99026,plugins/com.vector.cfg.persistency.jaxb.locations_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.json,1.0.0.99026,plugins/com.vector.cfg.persistency.json_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.model.traverser,1.0.0.99026,plugins/com.vector.cfg.persistency.model.traverser_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.parser,1.0.0.99026,plugins/com.vector.cfg.persistency.parser_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.parser.contribution,1.0.0.99026,plugins/com.vector.cfg.persistency.parser.contribution_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.parser.latest,1.0.0.99026,plugins/com.vector.cfg.persistency.parser.latest_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.preferences,1.0.0.99026,plugins/com.vector.cfg.persistency.preferences_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.project,1.0.0.99026,plugins/com.vector.cfg.persistency.project_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.project.contribution,1.0.0.99026,plugins/com.vector.cfg.persistency.project.contribution_1.0.0.99026.jar,4,false
com.vector.cfg.persistency.project.operations.msr,1.0.0.99026,plugins/com.vector.cfg.persistency.project.operations.msr_1.0.0.99026.jar,4,false
com.vector.cfg.project,1.0.0.99026,plugins/com.vector.cfg.project_1.0.0.99026.jar,4,false
com.vector.cfg.project.activity,1.0.0.99026,plugins/com.vector.cfg.project.activity_1.0.0.99026.jar,4,false
com.vector.cfg.project.creation,1.0.0.99026,plugins/com.vector.cfg.project.creation_1.0.0.99026.jar,4,false
com.vector.cfg.project.creation.msr,1.0.0.99026,plugins/com.vector.cfg.project.creation.msr_1.0.0.99026.jar,4,false
com.vector.cfg.project.creation.msr.groovy,1.0.0.99026,plugins/com.vector.cfg.project.creation.msr.groovy_1.0.0.99026.jar,4,false
com.vector.cfg.project.evs.contribution.if,1.0.0.99026,plugins/com.vector.cfg.project.evs.contribution.if_1.0.0.99026.jar,4,false
com.vector.cfg.project.evs.if,1.0.0.99026,plugins/com.vector.cfg.project.evs.if_1.0.0.99026.jar,4,false
com.vector.cfg.project.evs.impl,1.0.0.99026,plugins/com.vector.cfg.project.evs.impl_1.0.0.99026.jar,4,false
com.vector.cfg.project.evs.pai.if,1.0.0.99026,plugins/com.vector.cfg.project.evs.pai.if_1.0.0.99026.jar,4,false
com.vector.cfg.project.evs.pai.impl,1.0.0.99026,plugins/com.vector.cfg.project.evs.pai.impl_1.0.0.99026.jar,4,false
com.vector.cfg.project.loader,1.0.0.99026,plugins/com.vector.cfg.project.loader_1.0.0.99026.jar,4,false
com.vector.cfg.project.msr,1.0.0.99026,plugins/com.vector.cfg.project.msr_1.0.0.99026.jar,4,false
com.vector.cfg.project.operations.msr,1.0.0.99026,plugins/com.vector.cfg.project.operations.msr_1.0.0.99026.jar,4,false
com.vector.cfg.project.settings.msr.groovy,1.0.0.99026,plugins/com.vector.cfg.project.settings.msr.groovy_1.0.0.99026.jar,4,false
com.vector.cfg.reporting,1.0.0.99026,plugins/com.vector.cfg.reporting_1.0.0.99026.jar,4,false
com.vector.cfg.reporting.ecuc,1.0.0.99026,plugins/com.vector.cfg.reporting.ecuc_1.0.0.99026.jar,4,false
com.vector.cfg.reporting.ecuc.groovy,1.0.0.99026,plugins/com.vector.cfg.reporting.ecuc.groovy_1.0.0.99026.jar,4,false
com.vector.cfg.reporting.gen.execution,1.0.0.99026,plugins/com.vector.cfg.reporting.gen.execution_1.0.0.99026.jar,4,false
com.vector.cfg.reporting.ui,1.0.0.99026,plugins/com.vector.cfg.reporting.ui_1.0.0.99026.jar,4,false
com.vector.cfg.reporting.workflow.base,1.0.0.99026,plugins/com.vector.cfg.reporting.workflow.base_1.0.0.99026.jar,4,false
com.vector.cfg.reporting.workflow.diff,1.0.0.99026,plugins/com.vector.cfg.reporting.workflow.diff_1.0.0.99026.jar,4,false
com.vector.cfg.reporting.workflow.update,1.0.0.99026,plugins/com.vector.cfg.reporting.workflow.update_1.0.0.99026.jar,4,false
com.vector.cfg.testInfrastructure.published,1.0.0.99026,plugins/com.vector.cfg.testInfrastructure.published_1.0.0.99026.jar,4,false
com.vector.cfg.testinfrastructure.published.scripting.groovy,1.0.0.99026,plugins/com.vector.cfg.testinfrastructure.published.scripting.groovy_1.0.0.99026.jar,4,false
com.vector.cfg.util,1.0.0.99026,plugins/com.vector.cfg.util_1.0.0.99026.jar,4,false
com.vector.cfg.util.activity,1.0.0.99026,plugins/com.vector.cfg.util.activity_1.0.0.99026.jar,4,false
com.vector.cfg.util.args,1.0.0.99026,plugins/com.vector.cfg.util.args_1.0.0.99026.jar,4,false
com.vector.cfg.util.base,1.0.0.99026,plugins/com.vector.cfg.util.base_1.0.0.99026.jar,4,false
com.vector.cfg.util.classloading,1.0.0.99026,plugins/com.vector.cfg.util.classloading_1.0.0.99026.jar,4,false
com.vector.cfg.util.davincideveloper,1.0.0.99026,plugins/com.vector.cfg.util.davincideveloper_1.0.0.99026.jar,4,false
com.vector.cfg.util.feature.dev,1.0.0.99026,plugins/com.vector.cfg.util.feature.dev_1.0.0.99026.jar,4,false
com.vector.cfg.util.jna,4.5.1.99026,plugins/com.vector.cfg.util.jna_4.5.1.99026,4,false
com.vector.cfg.util.log,1.0.0.99026,plugins/com.vector.cfg.util.log_1.0.0.99026.jar,4,false
com.vector.cfg.util.log4j2.plugin,1.0.0.99026,plugins/com.vector.cfg.util.log4j2.plugin_1.0.0.99026.jar,4,false
com.vector.cfg.util.platform,1.0.0.99026,plugins/com.vector.cfg.util.platform_1.0.0.99026.jar,4,false
com.vector.cfg.util.platform.msr,1.0.0.99026,plugins/com.vector.cfg.util.platform.msr_1.0.0.99026.jar,4,false
com.vector.cfg.util.platform.win32.win32,1.0.0.99026,plugins/com.vector.cfg.util.platform.win32.win32_1.0.0.99026.jar,4,false
com.vector.cfg.util.scripting.dynamic,1.0.0.99026,plugins/com.vector.cfg.util.scripting.dynamic_1.0.0.99026.jar,4,false
com.vector.cfg.util.scripting.groovy,1.0.0.99026,plugins/com.vector.cfg.util.scripting.groovy_1.0.0.99026.jar,4,false
com.vector.cfg.util.scripting.groovy.lib,1.0.0.99026,plugins/com.vector.cfg.util.scripting.groovy.lib_1.0.0.99026.jar,4,false
com.vector.cfg.util.servicecontext,1.0.0.99026,plugins/com.vector.cfg.util.servicecontext_1.0.0.99026.jar,4,false
com.vector.cfg.util.services,1.0.0.99026,plugins/com.vector.cfg.util.services_1.0.0.99026.jar,4,false
com.vector.cfg.util.services.if,1.0.0.99026,plugins/com.vector.cfg.util.services.if_1.0.0.99026.jar,4,false
com.vector.cfg.util.text,1.0.0.99026,plugins/com.vector.cfg.util.text_1.0.0.99026.jar,4,false
com.vector.cfg.util.win32.win32.x86_64,1.0.0.99026,plugins/com.vector.cfg.util.win32.win32.x86_64_1.0.0.99026,4,false
com.vector.cfg.validation.basicrules.impl,1.0.0.99026,plugins/com.vector.cfg.validation.basicrules.impl_1.0.0.99026.jar,4,false
com.vector.cfg.validation.basicrules.msr.impl,1.0.0.99026,plugins/com.vector.cfg.validation.basicrules.msr.impl_1.0.0.99026.jar,4,false
com.vector.cfg.workflow,1.0.0.99026,plugins/com.vector.cfg.workflow_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.annotations.diff,1.0.0.99026,plugins/com.vector.cfg.workflow.annotations.diff_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.annotations.update,1.0.0.99026,plugins/com.vector.cfg.workflow.annotations.update_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.baseecuc.generator,1.0.0.99026,plugins/com.vector.cfg.workflow.baseecuc.generator_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.baseecuc.mapping,1.0.0.99026,plugins/com.vector.cfg.workflow.baseecuc.mapping_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.baseecuc.model.ecuc,1.0.0.99026,plugins/com.vector.cfg.workflow.baseecuc.model.ecuc_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.baseecuc.model.extract,1.0.0.99026,plugins/com.vector.cfg.workflow.baseecuc.model.extract_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.baseecuc.model.relations,1.0.0.99026,plugins/com.vector.cfg.workflow.baseecuc.model.relations_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.baseecuc.util,1.0.0.99026,plugins/com.vector.cfg.workflow.baseecuc.util_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.common.if,1.0.0.99026,plugins/com.vector.cfg.workflow.common.if_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.common.impl,1.0.0.99026,plugins/com.vector.cfg.workflow.common.impl_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.common.internal.if,1.0.0.99026,plugins/com.vector.cfg.workflow.common.internal.if_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.common.util,1.0.0.99026,plugins/com.vector.cfg.workflow.common.util_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.derival,1.0.0.99026,plugins/com.vector.cfg.workflow.derival_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.diagnostics,1.0.0.99026,plugins/com.vector.cfg.workflow.diagnostics_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.diff,1.0.0.99026,plugins/com.vector.cfg.workflow.diff_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.diff.groovy,1.0.0.99026,plugins/com.vector.cfg.workflow.diff.groovy_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.ecucupdater,1.0.0.99026,plugins/com.vector.cfg.workflow.ecucupdater_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.extract,1.0.0.99026,plugins/com.vector.cfg.workflow.extract_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.flat,1.0.0.99026,plugins/com.vector.cfg.workflow.flat_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.groovy,1.0.0.99026,plugins/com.vector.cfg.workflow.groovy_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.legacyconverter,1.0.0.99026,plugins/com.vector.cfg.workflow.legacyconverter_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.merge,1.0.0.99026,plugins/com.vector.cfg.workflow.merge_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.project,1.0.0.99026,plugins/com.vector.cfg.workflow.project_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.sysdesc.sync,1.0.0.99026,plugins/com.vector.cfg.workflow.sysdesc.sync_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.tdscfg,1.0.0.99026,plugins/com.vector.cfg.workflow.tdscfg_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.update,1.0.0.99026,plugins/com.vector.cfg.workflow.update_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.update.console,1.0.0.99026,plugins/com.vector.cfg.workflow.update.console_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.vase,1.0.0.99026,plugins/com.vector.cfg.workflow.vase_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.vase.console,1.0.0.99026,plugins/com.vector.cfg.workflow.vase.console_1.0.0.99026.jar,4,false
com.vector.cfg.workflow.vtt,1.0.0.99026,plugins/com.vector.cfg.workflow.vtt_1.0.0.99026.jar,4,false
com.vector.ctrl,1.0.0.99026,plugins/com.vector.ctrl_1.0.0.99026.jar,4,false
com.vector.ctrl.grid,1.0.0.99026,plugins/com.vector.ctrl.grid_1.0.0.99026.jar,4,false
com.vector.davinci.ctrl.fw,1.0.0.99026,plugins/com.vector.davinci.ctrl.fw_1.0.0.99026.jar,4,false
com.vector.davinci.util,1.0.3,plugins/com.vector.davinci.util_1.0.3.jar,4,false
com.vector.davinci.util.collections,1.0.1,plugins/com.vector.davinci.util.collections_1.0.1.jar,4,false
com.vector.license,1.0.0.99026,plugins/com.vector.license_1.0.0.99026.jar,4,false
com.vector.licensing.vlal,1.1.0.************,plugins/com.vector.licensing.vlal_1.1.0.************.jar,4,false
com.vector.severities,1.0.0.99026,plugins/com.vector.severities_1.0.0.99026.jar,4,false
javax.annotation,1.2.0.v201602091430,plugins/javax.annotation_1.2.0.v201602091430.jar,4,false
javax.el,2.2.0.v201303151357,plugins/javax.el_2.2.0.v201303151357.jar,4,false
javax.inject,1.0.0.v20091030,plugins/javax.inject_1.0.0.v20091030.jar,4,false
javax.servlet,3.1.0.v201410161800,plugins/javax.servlet_3.1.0.v201410161800.jar,4,false
javax.servlet.jsp,2.2.0.v201112011158,plugins/javax.servlet.jsp_2.2.0.v201112011158.jar,4,false
joda-time,2.9.3,plugins/joda-time_2.9.3.jar,4,false
net.sf.trove4j,3.0.3,plugins/net.sf.trove4j_3.0.3.jar,4,false
org.aopalliance,1.0.0.v201105210816,plugins/org.aopalliance_1.0.0.v201105210816.jar,4,false
org.apache.ant,1.10.6.v20190516-0412,plugins/org.apache.ant_1.10.6.v20190516-0412,4,false
org.apache.batik.constants,1.11.0.v20190515-0436,plugins/org.apache.batik.constants_1.11.0.v20190515-0436.jar,4,false
org.apache.batik.css,1.11.0.v20190515-0436,plugins/org.apache.batik.css_1.11.0.v20190515-0436.jar,4,false
org.apache.batik.i18n,1.11.0.v20190515-0436,plugins/org.apache.batik.i18n_1.11.0.v20190515-0436.jar,4,false
org.apache.batik.util,1.11.0.v20190515-0436,plugins/org.apache.batik.util_1.11.0.v20190515-0436.jar,4,false
org.apache.commons.cli,1.4.0,plugins/org.apache.commons.cli_1.4.0.jar,4,false
org.apache.commons.codec,1.10.0.v20180409-1845,plugins/org.apache.commons.codec_1.10.0.v20180409-1845.jar,4,false
org.apache.commons.io,2.6.0.v20190123-2029,plugins/org.apache.commons.io_2.6.0.v20190123-2029.jar,4,false
org.apache.commons.jxpath,1.3.0.v200911051830,plugins/org.apache.commons.jxpath_1.3.0.v200911051830.jar,4,false
org.apache.commons.lang,2.6.0.v201404270220,plugins/org.apache.commons.lang_2.6.0.v201404270220.jar,4,false
org.apache.commons.logging,1.2.0.v20180409-1502,plugins/org.apache.commons.logging_1.2.0.v20180409-1502.jar,4,false
org.apache.felix.gogo.command,1.0.2.v20170914-1324,plugins/org.apache.felix.gogo.command_1.0.2.v20170914-1324.jar,4,false
org.apache.felix.gogo.runtime,1.1.0.v20180713-1646,plugins/org.apache.felix.gogo.runtime_1.1.0.v20180713-1646.jar,4,false
org.apache.felix.gogo.shell,1.1.0.v20180713-1646,plugins/org.apache.felix.gogo.shell_1.1.0.v20180713-1646.jar,4,false
org.apache.felix.scr,2.1.14.v20190123-1619,plugins/org.apache.felix.scr_2.1.14.v20190123-1619.jar,1,true
org.apache.jasper.glassfish,2.2.2.v201501141630,plugins/org.apache.jasper.glassfish_2.2.2.v201501141630.jar,4,false
org.apache.logging.log4j.api,2.13.1,plugins/org.apache.logging.log4j.api_2.13.1.jar,4,false
org.apache.logging.log4j.core,2.13.1,plugins/org.apache.logging.log4j.core_2.13.1.jar,4,false
org.apache.lucene.analyzers-common,8.0.0.v20190404-1858,plugins/org.apache.lucene.analyzers-common_8.0.0.v20190404-1858.jar,4,false
org.apache.lucene.analyzers-smartcn,8.0.0.v20190404-1858,plugins/org.apache.lucene.analyzers-smartcn_8.0.0.v20190404-1858.jar,4,false
org.apache.lucene.core,8.0.0.v20190404-1858,plugins/org.apache.lucene.core_8.0.0.v20190404-1858.jar,4,false
org.apache.servicemix.bundles.jdom,*******,plugins/org.apache.servicemix.bundles.jdom_*******.jar,4,false
org.apache.xmlgraphics,2.3.0.v20190515-0436,plugins/org.apache.xmlgraphics_2.3.0.v20190515-0436.jar,4,false
org.eclipse.ant.core,3.5.500.v20190701-1953,plugins/org.eclipse.ant.core_3.5.500.v20190701-1953.jar,4,false
org.eclipse.compare,3.7.700.v20190802-1838,plugins/org.eclipse.compare_3.7.700.v20190802-1838.jar,4,false
org.eclipse.compare.core,3.6.600.v20190615-1517,plugins/org.eclipse.compare.core_3.6.600.v20190615-1517.jar,4,false
org.eclipse.core.commands,3.9.500.v20190805-1157,plugins/org.eclipse.core.commands_3.9.500.v20190805-1157.jar,4,false
org.eclipse.core.contenttype,3.7.400.v20190624-1144,plugins/org.eclipse.core.contenttype_3.7.400.v20190624-1144.jar,4,false
org.eclipse.core.databinding,1.7.500.v20190624-2109,plugins/org.eclipse.core.databinding_1.7.500.v20190624-2109.jar,4,false
org.eclipse.core.databinding.beans,1.5.100.v20190624-2109,plugins/org.eclipse.core.databinding.beans_1.5.100.v20190624-2109.jar,4,false
org.eclipse.core.databinding.observable,1.8.0.v20190805-1157,plugins/org.eclipse.core.databinding.observable_1.8.0.v20190805-1157.jar,4,false
org.eclipse.core.databinding.property,1.7.100.v20190805-1157,plugins/org.eclipse.core.databinding.property_1.7.100.v20190805-1157.jar,4,false
org.eclipse.core.expressions,3.6.500.v20190617-1926,plugins/org.eclipse.core.expressions_3.6.500.v20190617-1926.jar,4,false
org.eclipse.core.filebuffers,3.6.700.v20190614-0928,plugins/org.eclipse.core.filebuffers_3.6.700.v20190614-0928.jar,4,false
org.eclipse.core.filesystem,1.7.500.v20190620-1312,plugins/org.eclipse.core.filesystem_1.7.500.v20190620-1312.jar,4,false
org.eclipse.core.filesystem.win32.x86_64,1.4.200.v20190812-0909,plugins/org.eclipse.core.filesystem.win32.x86_64_1.4.200.v20190812-0909.jar,4,false
org.eclipse.core.jobs,3.10.500.v20190620-1426,plugins/org.eclipse.core.jobs_3.10.500.v20190620-1426.jar,4,false
org.eclipse.core.net,1.3.600.v20190619-1613,plugins/org.eclipse.core.net_1.3.600.v20190619-1613.jar,4,false
org.eclipse.core.resources,3.13.500.v20190819-0800,plugins/org.eclipse.core.resources_3.13.500.v20190819-0800.jar,4,false
org.eclipse.core.resources.win32.x86_64,3.5.400.v20190812-0909,plugins/org.eclipse.core.resources.win32.x86_64_3.5.400.v20190812-0909.jar,4,false
org.eclipse.core.runtime,3.16.0.v20190823-1314,plugins/org.eclipse.core.runtime_3.16.0.v20190823-1314.jar,3,true
org.eclipse.core.variables,3.4.600.v20190614-1239,plugins/org.eclipse.core.variables_3.4.600.v20190614-1239.jar,4,false
org.eclipse.debug.core,3.14.0.v20190812-1404,plugins/org.eclipse.debug.core_3.14.0.v20190812-1404.jar,4,false
org.eclipse.draw2d,3.10.100.201606061308,plugins/org.eclipse.draw2d_3.10.100.201606061308.jar,4,false
org.eclipse.e4.core.commands,0.12.700.v20190621-1412,plugins/org.eclipse.e4.core.commands_0.12.700.v20190621-1412.jar,4,false
org.eclipse.e4.core.contexts,1.8.200.v20190620-0649,plugins/org.eclipse.e4.core.contexts_1.8.200.v20190620-0649.jar,4,false
org.eclipse.e4.core.di,1.7.400.v20190903-1311,plugins/org.eclipse.e4.core.di_1.7.400.v20190903-1311.jar,4,false
org.eclipse.e4.core.di.annotations,1.6.400.v20190518-1217,plugins/org.eclipse.e4.core.di.annotations_1.6.400.v20190518-1217.jar,4,false
org.eclipse.e4.core.di.extensions,0.15.300.v20190213-1308,plugins/org.eclipse.e4.core.di.extensions_0.15.300.v20190213-1308.jar,4,false
org.eclipse.e4.core.di.extensions.supplier,0.15.400.v20190709-0707,plugins/org.eclipse.e4.core.di.extensions.supplier_0.15.400.v20190709-0707.jar,4,false
org.eclipse.e4.core.services,2.2.0.v20190630-2019,plugins/org.eclipse.e4.core.services_2.2.0.v20190630-2019.jar,4,false
org.eclipse.e4.emf.xpath,0.2.400.v20190621-1946,plugins/org.eclipse.e4.emf.xpath_0.2.400.v20190621-1946.jar,4,false
org.eclipse.e4.ui.bindings,0.12.600.v20190625-0735,plugins/org.eclipse.e4.ui.bindings_0.12.600.v20190625-0735.jar,4,false
org.eclipse.e4.ui.css.core,0.12.800.v20190805-1157,plugins/org.eclipse.e4.ui.css.core_0.12.800.v20190805-1157.jar,4,false
org.eclipse.e4.ui.css.swt,0.13.600.v20190805-1157,plugins/org.eclipse.e4.ui.css.swt_0.13.600.v20190805-1157.jar,4,false
org.eclipse.e4.ui.css.swt.theme,0.12.400.v20190812-0413,plugins/org.eclipse.e4.ui.css.swt.theme_0.12.400.v20190812-0413.jar,4,false
org.eclipse.e4.ui.di,1.2.600.v20190510-1100,plugins/org.eclipse.e4.ui.di_1.2.600.v20190510-1100.jar,4,false
org.eclipse.e4.ui.dialogs,1.1.600.v20190814-0636,plugins/org.eclipse.e4.ui.dialogs_1.1.600.v20190814-0636.jar,4,false
org.eclipse.e4.ui.model.workbench,2.1.500.v20190824-1021,plugins/org.eclipse.e4.ui.model.workbench_2.1.500.v20190824-1021.jar,4,false
org.eclipse.e4.ui.services,1.3.600.v20190716-1245,plugins/org.eclipse.e4.ui.services_1.3.600.v20190716-1245.jar,4,false
org.eclipse.e4.ui.widgets,1.2.500.v20190624-0808,plugins/org.eclipse.e4.ui.widgets_1.2.500.v20190624-0808.jar,4,false
org.eclipse.e4.ui.workbench,1.10.100.v20190810-0814,plugins/org.eclipse.e4.ui.workbench_1.10.100.v20190810-0814.jar,4,false
org.eclipse.e4.ui.workbench.addons.swt,1.3.600.v20190716-1245,plugins/org.eclipse.e4.ui.workbench.addons.swt_1.3.600.v20190716-1245.jar,4,false
org.eclipse.e4.ui.workbench.renderers.swt,0.14.800.v20190716-1245,plugins/org.eclipse.e4.ui.workbench.renderers.swt_0.14.800.v20190716-1245.jar,4,false
org.eclipse.e4.ui.workbench.swt,0.14.700.v20190807-1716,plugins/org.eclipse.e4.ui.workbench.swt_0.14.700.v20190807-1716.jar,4,false
org.eclipse.e4.ui.workbench3,0.15.200.v20190621-1448,plugins/org.eclipse.e4.ui.workbench3_0.15.200.v20190621-1448.jar,4,false
org.eclipse.elk.alg.common,0.5.0,plugins/org.eclipse.elk.alg.common_0.5.0.jar,4,false
org.eclipse.elk.alg.layered,0.5.0,plugins/org.eclipse.elk.alg.layered_0.5.0.jar,4,false
org.eclipse.elk.core,0.5.0,plugins/org.eclipse.elk.core_0.5.0.jar,4,false
org.eclipse.elk.graph,0.5.0,plugins/org.eclipse.elk.graph_0.5.0.jar,4,false
org.eclipse.emf.common,2.16.0.v20190625-1131,plugins/org.eclipse.emf.common_2.16.0.v20190625-1131.jar,4,false
org.eclipse.emf.ecore,2.19.0.v20190822-1451,plugins/org.eclipse.emf.ecore_2.19.0.v20190822-1451.jar,4,false
org.eclipse.emf.ecore.change,2.14.0.v20190528-0725,plugins/org.eclipse.emf.ecore.change_2.14.0.v20190528-0725.jar,4,false
org.eclipse.emf.ecore.xmi,2.16.0.v20190528-0725,plugins/org.eclipse.emf.ecore.xmi_2.16.0.v20190528-0725.jar,4,false
org.eclipse.equinox.app,1.4.300.v20190815-1535,plugins/org.eclipse.equinox.app_1.4.300.v20190815-1535.jar,4,false
org.eclipse.equinox.bidi,1.2.100.v20190815-1535,plugins/org.eclipse.equinox.bidi_1.2.100.v20190815-1535.jar,4,false
org.eclipse.equinox.common,3.10.500.v20190815-1535,plugins/org.eclipse.equinox.common_3.10.500.v20190815-1535.jar,2,true
org.eclipse.equinox.console,1.4.0.v20190819-1430,plugins/org.eclipse.equinox.console_1.4.0.v20190819-1430.jar,4,false
org.eclipse.equinox.ds,1.6.0.v20190122-0806,plugins/org.eclipse.equinox.ds_1.6.0.v20190122-0806.jar,1,true
org.eclipse.equinox.event,1.5.200.v20190814-0953,plugins/org.eclipse.equinox.event_1.5.200.v20190814-0953.jar,2,true
org.eclipse.equinox.http.jetty,3.7.200.v20190714-1849,plugins/org.eclipse.equinox.http.jetty_3.7.200.v20190714-1849.jar,4,false
org.eclipse.equinox.http.registry,1.1.700.v20190214-1948,plugins/org.eclipse.equinox.http.registry_1.1.700.v20190214-1948.jar,4,false
org.eclipse.equinox.http.servlet,1.6.200.v20190823-1423,plugins/org.eclipse.equinox.http.servlet_1.6.200.v20190823-1423.jar,4,false
org.eclipse.equinox.jsp.jasper,1.1.300.v20190714-1850,plugins/org.eclipse.equinox.jsp.jasper_1.1.300.v20190714-1850.jar,4,false
org.eclipse.equinox.jsp.jasper.registry,1.1.300.v20190714-1850,plugins/org.eclipse.equinox.jsp.jasper.registry_1.1.300.v20190714-1850.jar,4,false
org.eclipse.equinox.launcher,1.5.500.v20190715-1310,plugins/org.eclipse.equinox.launcher_1.5.500.v20190715-1310.jar,4,false
org.eclipse.equinox.launcher.win32.win32.x86_64,1.1.1100.v20190907-0426,plugins/org.eclipse.equinox.launcher.win32.win32.x86_64_1.1.1100.v20190907-0426,4,false
org.eclipse.equinox.p2.core,2.6.100.v20190705-1223,plugins/org.eclipse.equinox.p2.core_2.6.100.v20190705-1223.jar,4,false
org.eclipse.equinox.p2.engine,2.6.400.v20190716-0825,plugins/org.eclipse.equinox.p2.engine_2.6.400.v20190716-0825.jar,4,false
org.eclipse.equinox.p2.metadata,2.4.500.v20190807-0737,plugins/org.eclipse.equinox.p2.metadata_2.4.500.v20190807-0737.jar,4,false
org.eclipse.equinox.p2.metadata.repository,1.3.200.v20190808-0702,plugins/org.eclipse.equinox.p2.metadata.repository_1.3.200.v20190808-0702.jar,4,false
org.eclipse.equinox.p2.repository,2.4.500.v20190716-0939,plugins/org.eclipse.equinox.p2.repository_2.4.500.v20190716-0939.jar,4,false
org.eclipse.equinox.preferences,3.7.500.v20190815-1535,plugins/org.eclipse.equinox.preferences_3.7.500.v20190815-1535.jar,4,false
org.eclipse.equinox.registry,3.8.500.v20190714-1850,plugins/org.eclipse.equinox.registry_3.8.500.v20190714-1850.jar,4,false
org.eclipse.equinox.security,1.3.300.v20190714-1851,plugins/org.eclipse.equinox.security_1.3.300.v20190714-1851.jar,4,false
org.eclipse.equinox.simpleconfigurator,1.3.300.v20190716-0825,plugins/org.eclipse.equinox.simpleconfigurator_1.3.300.v20190716-0825.jar,1,true
org.eclipse.equinox.util,1.1.300.v20190714-1852,plugins/org.eclipse.equinox.util_1.1.300.v20190714-1852.jar,4,false
org.eclipse.help,3.8.500.v20190624-2105,plugins/org.eclipse.help_3.8.500.v20190624-2105.jar,4,false
org.eclipse.help.base,4.2.700.v20190916-1045,plugins/org.eclipse.help.base_4.2.700.v20190916-1045.jar,4,false
org.eclipse.help.ui,4.1.600.v20190814-0936,plugins/org.eclipse.help.ui_4.1.600.v20190814-0936.jar,4,false
org.eclipse.help.webapp,3.9.600.v20190814-0635,plugins/org.eclipse.help.webapp_3.9.600.v20190814-0635.jar,4,false
org.eclipse.jdt.annotation,2.2.300.v20190328-1431,plugins/org.eclipse.jdt.annotation_2.2.300.v20190328-1431.jar,4,false
org.eclipse.jdt.annotation,2.2.200.v20180921-1416,plugins/org.eclipse.jdt.annotation_2.2.200.v20180921-1416.jar,4,false
org.eclipse.jdt.compiler.apt,1.3.700.v20190704-1731,plugins/org.eclipse.jdt.compiler.apt_1.3.700.v20190704-1731.jar,4,false
org.eclipse.jdt.compiler.tool,1.2.600.v20190322-0450,plugins/org.eclipse.jdt.compiler.tool_1.2.600.v20190322-0450.jar,4,false
org.eclipse.jdt.core,3.19.0.v20190903-0936,plugins/org.eclipse.jdt.core_3.19.0.v20190903-0936.jar,4,false
org.eclipse.jdt.debug,3.13.100.v20190902-1050,plugins/org.eclipse.jdt.debug_3.13.100.v20190902-1050,4,false
org.eclipse.jdt.launching,3.15.0.v20190826-1639,plugins/org.eclipse.jdt.launching_3.15.0.v20190826-1639.jar,4,false
org.eclipse.jetty.continuation,9.4.20.v20190813,plugins/org.eclipse.jetty.continuation_9.4.20.v20190813.jar,4,false
org.eclipse.jetty.http,9.4.20.v20190813,plugins/org.eclipse.jetty.http_9.4.20.v20190813.jar,4,false
org.eclipse.jetty.io,9.4.20.v20190813,plugins/org.eclipse.jetty.io_9.4.20.v20190813.jar,4,false
org.eclipse.jetty.security,9.4.20.v20190813,plugins/org.eclipse.jetty.security_9.4.20.v20190813.jar,4,false
org.eclipse.jetty.server,9.4.20.v20190813,plugins/org.eclipse.jetty.server_9.4.20.v20190813.jar,4,false
org.eclipse.jetty.servlet,9.4.20.v20190813,plugins/org.eclipse.jetty.servlet_9.4.20.v20190813.jar,4,false
org.eclipse.jetty.util,9.4.20.v20190813,plugins/org.eclipse.jetty.util_9.4.20.v20190813.jar,4,false
org.eclipse.jface,3.17.0.v20190820-1444,plugins/org.eclipse.jface_3.17.0.v20190820-1444.jar,4,false
org.eclipse.jface.databinding,1.9.100.v20190805-1255,plugins/org.eclipse.jface.databinding_1.9.100.v20190805-1255.jar,4,false
org.eclipse.jface.text,3.15.300.v20190819-0725,plugins/org.eclipse.jface.text_3.15.300.v20190819-0725.jar,4,false
org.eclipse.ltk.core.refactoring,3.10.200.v20190814-1719,plugins/org.eclipse.ltk.core.refactoring_3.10.200.v20190814-1719.jar,4,false
org.eclipse.ltk.ui.refactoring,3.10.0.v20190819-2110,plugins/org.eclipse.ltk.ui.refactoring_3.10.0.v20190819-2110.jar,4,false
org.eclipse.nebula.widgets.nattable.core,1.6.0.201909181823,plugins/org.eclipse.nebula.widgets.nattable.core_1.6.0.201909181823.jar,4,false
org.eclipse.nebula.widgets.nattable.extension.e4,1.2.0.201909181823,plugins/org.eclipse.nebula.widgets.nattable.extension.e4_1.2.0.201909181823.jar,4,false
org.eclipse.nebula.widgets.nattable.extension.glazedlists,1.6.0.201909181823,plugins/org.eclipse.nebula.widgets.nattable.extension.glazedlists_1.6.0.201909181823.jar,4,false
org.eclipse.nebula.widgets.tablecombo,1.2.0.201907151344,plugins/org.eclipse.nebula.widgets.tablecombo_1.2.0.201907151344.jar,4,false
org.eclipse.osgi,3.15.0.v20190830-1434,plugins/org.eclipse.osgi_3.15.0.v20190830-1434.jar,4,false
org.eclipse.osgi.compatibility.state,1.1.600.v20190814-1451,plugins/org.eclipse.osgi.compatibility.state_1.1.600.v20190814-1451.jar,4,false
org.eclipse.osgi.services,3.8.0.v20190206-2147,plugins/org.eclipse.osgi.services_3.8.0.v20190206-2147.jar,4,false
org.eclipse.osgi.util,3.5.300.v20190708-1141,plugins/org.eclipse.osgi.util_3.5.300.v20190708-1141.jar,4,false
org.eclipse.swt,3.112.0.v20200310-0942-543747,plugins/org.eclipse.swt_3.112.0.v20200310-0942-543747.jar,4,false
org.eclipse.swt.win32.win32.x86_64,3.112.0.v20200310-0942-543747,plugins/org.eclipse.swt.win32.win32.x86_64_3.112.0.v20200310-0942-543747.jar,4,false
org.eclipse.team.core,3.8.700.v20190619-1613,plugins/org.eclipse.team.core_3.8.700.v20190619-1613.jar,4,false
org.eclipse.team.ui,3.8.600.v20190819-1553,plugins/org.eclipse.team.ui_3.8.600.v20190819-1553.jar,4,false
org.eclipse.text,3.9.0.v20190826-1019,plugins/org.eclipse.text_3.9.0.v20190826-1019.jar,4,false
org.eclipse.ui,3.114.0.v20190808-1317,plugins/org.eclipse.ui_3.114.0.v20190808-1317.jar,4,false
org.eclipse.ui.console,3.8.600.v20190815-2020,plugins/org.eclipse.ui.console_3.8.600.v20190815-2020.jar,4,false
org.eclipse.ui.editors,3.12.0.v20190730-1840,plugins/org.eclipse.ui.editors_3.12.0.v20190730-1840.jar,4,false
org.eclipse.ui.forms,3.8.100.v20190625-1825,plugins/org.eclipse.ui.forms_3.8.100.v20190625-1825.jar,4,false
org.eclipse.ui.ide,3.16.0.v20190916-1323,plugins/org.eclipse.ui.ide_3.16.0.v20190916-1323.jar,4,false
org.eclipse.ui.ide.application,1.3.400.v20190818-1234,plugins/org.eclipse.ui.ide.application_1.3.400.v20190818-1234.jar,4,false
org.eclipse.ui.navigator,3.9.0.v20190807-2204,plugins/org.eclipse.ui.navigator_3.9.0.v20190807-2204.jar,4,false
org.eclipse.ui.navigator.resources,3.7.0.v20190820-1649,plugins/org.eclipse.ui.navigator.resources_3.7.0.v20190820-1649.jar,4,false
org.eclipse.ui.themes,1.2.700.v20190826-0816,plugins/org.eclipse.ui.themes_1.2.700.v20190826-0816,4,false
org.eclipse.ui.views,3.10.0.v20190805-1157,plugins/org.eclipse.ui.views_3.10.0.v20190805-1157.jar,4,false
org.eclipse.ui.views.properties.tabbed,3.8.600.v20190713-1021,plugins/org.eclipse.ui.views.properties.tabbed_3.8.600.v20190713-1021.jar,4,false
org.eclipse.ui.workbench,3.116.0.v20190826-1428,plugins/org.eclipse.ui.workbench_3.116.0.v20190826-1428.jar,4,false
org.eclipse.ui.workbench.texteditor,3.13.0.v20190903-0631,plugins/org.eclipse.ui.workbench.texteditor_3.13.0.v20190903-0631.jar,4,false
org.eclipse.update.configurator,3.4.300.v20190518-1030,plugins/org.eclipse.update.configurator_3.4.300.v20190518-1030.jar,4,false
org.eclipse.urischeme,1.0.400.v20190621-1448,plugins/org.eclipse.urischeme_1.0.400.v20190621-1448.jar,4,false
org.eclipse.xtext.xbase.lib,2.19.0.v20190902-0728,plugins/org.eclipse.xtext.xbase.lib_2.19.0.v20190902-0728.jar,4,false
org.eclipse.zest.core,1.5.300.201606061308,plugins/org.eclipse.zest.core_1.5.300.201606061308.jar,4,false
org.eclipse.zest.layouts,1.1.300.201606061308,plugins/org.eclipse.zest.layouts_1.1.300.201606061308.jar,4,false
org.hamcrest.core,1.3.0.v20180420-1519,plugins/org.hamcrest.core_1.3.0.v20180420-1519.jar,4,false
org.hamcrest.library,1.3.0.v20180524-2246,plugins/org.hamcrest.library_1.3.0.v20180524-2246.jar,4,false
org.hsqldb,2.2.5.201804101419,plugins/org.hsqldb_2.2.5.201804101419.jar,4,false
org.junit,4.12.0.v201504281640,plugins/org.junit_4.12.0.v201504281640,4,false
org.scala-lang.scala-library,2.11.7.v20150622-112736-1fbce4612c,plugins/org.scala-lang.scala-library_2.11.7.v20150622-112736-1fbce4612c.jar,4,false
org.tukaani.xz,1.8.0.v20180207-1613,plugins/org.tukaani.xz_1.8.0.v20180207-1613.jar,4,false
org.w3c.css.sac,1.3.1.v200903091627,plugins/org.w3c.css.sac_1.3.1.v200903091627.jar,4,false
org.w3c.dom.events,3.0.0.draft20060413_v201105210656,plugins/org.w3c.dom.events_3.0.0.draft20060413_v201105210656.jar,4,false
org.w3c.dom.smil,1.0.1.v200903091627,plugins/org.w3c.dom.smil_1.0.1.v200903091627.jar,4,false
org.w3c.dom.svg,1.1.0.v201011041433,plugins/org.w3c.dom.svg_1.1.0.v201011041433.jar,4,false
sun.misc,1.0.0.201906171301,plugins/sun.misc_1.0.0.201906171301,4,false
vi.annotations.nodsl.base.if,9.0.0.************,plugins/vi.annotations.nodsl.base.if_9.0.0.************.jar,4,false
vi.api,9.0.0.************,plugins/vi.api_9.0.0.************.jar,4,false
vi.auth.api,9.0.0.************,plugins/vi.auth.api_9.0.0.************.jar,4,false
vi.authentication.nodsl.base.if,9.0.0.************,plugins/vi.authentication.nodsl.base.if_9.0.0.************.jar,4,false
vi.collections,9.0.0.************,plugins/vi.collections_9.0.0.************.jar,4,false
vi.common.nodsl.base.if,9.0.0.************,plugins/vi.common.nodsl.base.if_9.0.0.************.jar,4,false
vi.concurrency.api,9.0.0.************,plugins/vi.concurrency.api_9.0.0.************.jar,4,false
vi.deltamodel.nodsl.modeling.if,9.0.0.************,plugins/vi.deltamodel.nodsl.modeling.if_9.0.0.************.jar,4,false
vi.globalevent.nodsl.modeling.if,9.5.0.************,plugins/vi.globalevent.nodsl.modeling.if_9.5.0.************.jar,4,false
vi.info,9.0.0.************,plugins/vi.info_9.0.0.************.jar,4,false
vi.info.mdf,9.0.0.************,plugins/vi.info.mdf_9.0.0.************.jar,4,false
vi.mdf.nodsl.modeling.if,9.0.0.************,plugins/vi.mdf.nodsl.modeling.if_9.0.0.************.jar,4,false
vi.mdf.nodsl.modeling.impl,9.0.0.************,plugins/vi.mdf.nodsl.modeling.impl_9.0.0.************.jar,4,false
vi.mdf.nodsl.modeling.internal.if,9.0.0.************,plugins/vi.mdf.nodsl.modeling.internal.if_9.0.0.************.jar,4,false
vi.mdf.nodsl.modeling.legacy.util,9.0.0.************,plugins/vi.mdf.nodsl.modeling.legacy.util_9.0.0.************.jar,4,false
vi.mm.mof.modeling.impl,9.0.0.************,plugins/vi.mm.mof.modeling.impl_9.0.0.************.jar,4,false
vi.mmregistry.nodsl.modeling.if,9.0.0.************,plugins/vi.mmregistry.nodsl.modeling.if_9.0.0.************.jar,4,false
vi.modelbridge.nodsl.modeling.if,9.0.0.************,plugins/vi.modelbridge.nodsl.modeling.if_9.0.0.************.jar,4,false
vi.monitoring.nodsl.base.if,9.0.0.************,plugins/vi.monitoring.nodsl.base.if_9.0.0.************.jar,4,false
vi.status.nodsl.base.if,9.0.0.************,plugins/vi.status.nodsl.base.if_9.0.0.************.jar,4,false
vi.testenv.nodsl.base.util,9.0.0.************,plugins/vi.testenv.nodsl.base.util_9.0.0.************.jar,4,false
