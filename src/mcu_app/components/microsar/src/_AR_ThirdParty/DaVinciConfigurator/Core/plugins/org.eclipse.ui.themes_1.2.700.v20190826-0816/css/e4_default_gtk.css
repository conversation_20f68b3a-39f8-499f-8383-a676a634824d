/*******************************************************************************
 * Copyright (c) 2010, 2014 IBM Corporation and others.
 *
 * This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License 2.0
 * which accompanies this distribution, and is available at
 * https://www.eclipse.org/legal/epl-2.0/
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *     <PERSON> <<EMAIL>> - Bug 420836
 *     <PERSON><PERSON> <<EMAIL>> - 325937
 *     <PERSON><PERSON> <<EMAIL>> - Bug 501250
 *******************************************************************************/

@import url("platform:/plugin/org.eclipse.ui.themes/css/e4_basestyle.css");

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_START {
	color: #EEEEEE;
}

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_END {
	color: #EEEEEE;
}
/* Higher contrast keylines for selected stack */
ColorDefinition#org-eclipse-ui-workbench-ACTIVE_TAB_OUTER_KEYLINE_COLOR {
	color: #A0A0A0;
}
ColorDefinition#org-eclipse-ui-workbench-ACTIVE_TAB_INNER_KEYLINE_COLOR {
	color: #A0A0A0;
}
ColorDefinition#org-eclipse-ui-workbench-ACTIVE_TAB_OUTLINE_COLOR {
	color: #A0A0A0;
}


ColorDefinition#org-eclipse-ui-workbench-INACTIVE_UNSELECTED_TABS_COLOR_START {
	color: #DCDCDC;
}

ColorDefinition#org-eclipse-ui-workbench-INACTIVE_UNSELECTED_TABS_COLOR_END {
	color: #DCDCDC;
}

ColorDefinition#org-eclipse-ui-workbench-INACTIVE_TAB_OUTER_KEYLINE_COLOR {
	color: #B4B4B4;
}

ColorDefinition#org-eclipse-ui-workbench-INACTIVE_TAB_INNER_KEYLINE_COLOR {
	color: #B4B4B4;
}

ColorDefinition#org-eclipse-ui-workbench-INACTIVE_TAB_BG_START{
	color: #E2E2E2;
}

ColorDefinition#org-eclipse-ui-workbench-INACTIVE_TAB_BG_END {
	color: #E2E2E2;
}

ColorDefinition#org-eclipse-ui-workbench-INACTIVE_NOFOCUS_TAB_BG_START {
	color: #E2E2E2;
}

ColorDefinition#org-eclipse-ui-workbench-INACTIVE_NOFOCUS_TAB_BG_END {
	color: #E2E2E2;
}

ColorDefinition#org-eclipse-ui-workbench-INACTIVE_TAB_OUTLINE_COLOR {
	color: #B4B4B4;
}


.MTrimmedWindow {
	background-color: #E2E2E2;
}

.MPartStack {
	swt-simple: false;
	swt-mru-visible: false;
}

.MTrimBar {
	background-color: #E2E2E2;
}

.MTrimBar#org-eclipse-ui-main-toolbar  {
	background-color: COLOR-WIDGET-BACKGROUND #E2E2E2 100%;
}

.MToolControl.TrimStack {
	frame-image:  url(./gtkTSFrame.png);
	handle-image:  url(./gtkHandle.png);
	frame-image-rotated:  url(./gtkTSFrame-rotated.png);
	handle-image-rotated:  url(./gtkHandle-rotated.png);
}

#PerspectiveSwitcher {
	background-color: COLOR-WIDGET-BACKGROUND #E2E2E2 100%;
	eclipse-perspective-keyline-color: #E2E2E2 #B4B4B4;
}

#org-eclipse-ui-editorss {
	swt-tab-height: 8px;
	padding: 0px 5px 7px;
}

CTabFolder.MArea .MPartStack, CTabFolder.MArea .MPartStack.active {
	swt-shadow-visible: false;
}

CTabFolder Canvas {
	background-color: #F8F8F8;
}

Form, FormHeading {
	background-color: #ffffff;
	background: #ffffff;
	background-image: #ffffff;
	color: #505050;
}

Form {
	/* Bug 465148: Additional styling for the Form */
	text-background-color: #ffffff;

	tb-toggle-hover-color: #505050;
	tb-toggle-color: #505050;
	h-hover-full-color: #505050;
	h-hover-light-color: #505050;
	h-bottom-keyline-2-color: #eaeaea;
	h-bottom-keyline-1-color: #eaeaea;

}



Section {
	background-color: #ffffff;
  	color: #505050;
  	background-color-titlebar: #eaeaea;
  	background-color-gradient-titlebar: #eaeaea;
  	border-color-titlebar: #ffffff;
}

TabbedPropertyTitle > CLabel{
	color: #505050;
}

TabbedPropertyTitle {
	swt-backgroundGradientStart-color:  #eaeaea;
	swt-backgroundGradientEnd-color:    #eaeaea;
	swt-backgroundBottomKeyline1-color: #eaeaea;
	swt-backgroundBottomKeyline2-color: #eaeaea;
}

TabbedPropertyList {
	swt-tabAreaBackground-color : #ffffff;
	swt-tabBackground-color     : '#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_BG_START';
	swt-tabNormalShadow-color   : '#org-eclipse-ui-workbench-INACTIVE_TAB_OUTLINE_COLOR';             /* color of shadow lines around the tabs */
	swt-tabDarkShadow-color     : '#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_SELECTED_TEXT_COLOR'; /* line color of the tiny scroll triangle (at top / at bottom) */
	color                       : #505050; /* text color in the tab / tab area */
}