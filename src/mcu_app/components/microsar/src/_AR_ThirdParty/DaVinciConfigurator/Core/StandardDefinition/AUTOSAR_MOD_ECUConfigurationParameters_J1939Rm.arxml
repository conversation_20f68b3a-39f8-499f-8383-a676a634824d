<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: J1939Rm -->
						<ECUC-MODULE-DEF UUID="ECUC:27255541-a605-4c97-b9be-d05032a8b461">
							<SHORT-NAME>J1939Rm</SHORT-NAME>
							<DESC>
								<L-2 L="EN">The SAE J1939 Nm module</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.4.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
										<DATE>2013-10-31</DATE>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-LINK-TIME</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-POST-BUILD</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: J1939RmConfigSet -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:5cf1a263-c0ee-4a24-a485-704ec78413a7">
									<SHORT-NAME>J1939RmConfigSet</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container is a MultipleConfigurationContainer, i.e. this container and its sub-containers exist once per configuration set.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>true</MULTIPLE-CONFIGURATION-CONTAINER>
									<SUB-CONTAINERS>
										<!-- Container Definition: J1939RmChannel -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:7e0aa11e-4b45-4a32-9b33-2fa5e2907640">
											<SHORT-NAME>J1939RmChannel</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Contains the parameters for a CAN channel supported by the J1939 Request Manager.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: J1939RmAckQueueSize -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:5dcee440-f4bb-46d6-a787-4a19d98560f2">
													<SHORT-NAME>J1939RmAckQueueSize</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Number of transmitted acknowledgements that can be stored.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: J1939RmRequestQueueSize -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:32b3c018-dd5e-462d-9f5c-8a88a11af231">
													<SHORT-NAME>J1939RmRequestQueueSize</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Number of transmitted requests that can be stored.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: J1939RmRequestTimeoutMonitors -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:a829bbce-af3a-476b-bac4-727fedfc827c">
													<SHORT-NAME>J1939RmRequestTimeoutMonitors</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Number of transmitted requests that can be monitored for timeout.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Symbolic Name Reference Definition: J1939RmComMNetworkHandleRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:17cfd20c-d369-473a-800b-2010c8a5be28">
													<SHORT-NAME>J1939RmComMNetworkHandleRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the channel defined by the ComMChannel providing access to the unique channel index ComMChannelId.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/ComM/ComMConfigSet/ComMChannel</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
											<SUB-CONTAINERS>
												<!-- Container Definition: J1939RmAckmRxPdu -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:c29fc910-2ae7-4d23-b3d6-471a6a664ece">
													<SHORT-NAME>J1939RmAckmRxPdu</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Contains the configuration of the I-PDU used to receive the Acknowledgement PG.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: J1939RmAckmRxPduId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:384018fd-c035-4758-b3ce-3588af856fe3">
															<SHORT-NAME>J1939RmAckmRxPduId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">The I-PDU identifier used for RxIndication from PduR.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: J1939RmAckmRxPduRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:df89fcd4-edb0-45d2-874e-2c632c2981e8">
															<SHORT-NAME>J1939RmAckmRxPduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the Pdu object representing the I-PDU.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: J1939RmAckmTxPdu -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:3dc55eb3-e8d0-4c21-98bf-5e34edce9502">
													<SHORT-NAME>J1939RmAckmTxPdu</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Contains the configuration of the I-PDU used to transmit the Acknowledgement PG.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: J1939RmAckmTxPduId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:b31cb398-8dd5-4b8c-b239-63e5c1adf9a0">
															<SHORT-NAME>J1939RmAckmTxPduId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">The I-PDU identifier used for TxConfirmation from PduR.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: J1939RmAckmTxPduRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:1d59d5a3-b579-4d8f-abd9-81307a195a58">
															<SHORT-NAME>J1939RmAckmTxPduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the Pdu object representing the I-PDU.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: J1939RmRqstRxPdu -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:479965fe-361f-48f3-8cb4-093697565b3f">
													<SHORT-NAME>J1939RmRqstRxPdu</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Contains the configuration of the I-PDU used to receive the Request PG.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: J1939RmRqstRxPduId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:a54ac773-f61b-4092-bf95-94fa8249e93a">
															<SHORT-NAME>J1939RmRqstRxPduId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">The I-PDU identifier used for RxIndication from PduR.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: J1939RmRqstRxPduRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:a1b2b442-997d-4e4e-9785-137bbba46565">
															<SHORT-NAME>J1939RmRqstRxPduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the Pdu object representing the I-PDU.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: J1939RmRqstTxPdu -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:58f0f75c-0764-429f-80d7-83187edbee73">
													<SHORT-NAME>J1939RmRqstTxPdu</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Contains the configuration of the I-PDU used to transmit the Request PG.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: J1939RmRqstTxPduId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:c837bbfe-c073-44d7-9b6e-30fe89bdb70d">
															<SHORT-NAME>J1939RmRqstTxPduId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">The I-PDU identifier used for TxConfirmation from PduR.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: J1939RmRqstTxPduRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:5efc2cae-3d15-4cff-b893-ba702661b8e7">
															<SHORT-NAME>J1939RmRqstTxPduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the Pdu object representing the I-PDU.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: J1939RmNode -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:d453e997-a0a0-403c-aa6d-34845c74418b">
											<SHORT-NAME>J1939RmNode</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Contains the parameters for the support of a logical J1939 node (identified by an ECU address).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<REFERENCES>
												<!-- Reference Definition: J1939RmNodeChannelRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:aa122285-1c57-41e0-99da-41c9602f34ba">
													<SHORT-NAME>J1939RmNodeChannelRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the channels this node has access to.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/J1939Rm/J1939RmConfigSet/J1939RmChannel</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<!-- Symbolic Name Reference Definition: J1939RmNmNodeRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:07bed225-fddd-4e71-96ed-cd22d87229fc">
													<SHORT-NAME>J1939RmNmNodeRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the corresponding J1939Nm node.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/J1939Nm/J1939NmConfigSet/J1939NmNode</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
											<SUB-CONTAINERS>
												<!-- Container Definition: J1939RmUser -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e534c545-1a58-4f2f-8e41-95d1106733a8">
													<SHORT-NAME>J1939RmUser</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Contains the configuration of a module that uses the request and acknowledgement interfaces of J1939Rm.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: J1939RmUserAckIndication -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:67fed1f0-296d-4d40-9387-9481dc72a908">
															<SHORT-NAME>J1939RmUserAckIndication</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Enable AckIndication for this module. In case of CDD, the name is &lt;apiServicePrefix&gt;_AckIndication. In case of RTE, the port is defined as J1939RmRPort&lt;J1939RmUserId&gt;, and the operation is called AckIndication. This parameter shall not be set for J1939RmUserType J1939RM_USER_J1939NM, J1939RM_USER_J1939DCM, or J1939RM_USER_COM.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: J1939RmUserAckPGN -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:37997ea0-3065-4aaa-a9e2-40bc6b88a3bc">
															<SHORT-NAME>J1939RmUserAckPGN</SHORT-NAME>
															<DESC>
																<L-2 L="EN">PGN supported to be acknowledged to this module. The PGNs supported by different modules should usually be disjunctive. This parameter shall not be set for J1939RmUserType J1939RM_USER_J1939NM, J1939RM_USER_J1939DCM, and J1939RM_USER_COM.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>262143</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: J1939RmUserComIPduRequestQueueSize -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:b386ec3b-587c-4d51-ba58-98c6387c910c">
															<SHORT-NAME>J1939RmUserComIPduRequestQueueSize</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Number of received requests that can be stored for COM I-PDUs of this user.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: J1939RmUserId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:1fe9bba9-b2d6-43c3-affc-43271705f1b7">
															<SHORT-NAME>J1939RmUserId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Identifier used by a module using J1939Rm. This parameter is only required when the module uses transmission of requests.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: J1939RmUserPGN -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:0f89743c-31e4-49da-ad13-a85775c09d1b">
															<SHORT-NAME>J1939RmUserPGN</SHORT-NAME>
															<DESC>
																<L-2 L="EN">PGN supported to be requested from this module. The PGNs supported by different modules should usually be disjunctive. This parameter is predefined to AC (0x0EE00) for J1939RmUserType J1939RM_USER_J1939NM and is derived from the J1939Dcm PDUs in the system description for J1939RmUserType J1939RM_USER_J1939DCM. It shall not be set for J1939RmUserType J1939RM_USER_COM.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>262143</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: J1939RmUserRequestIndication -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:d4306c2d-71d4-41e5-b6c0-bc1354a535df">
															<SHORT-NAME>J1939RmUserRequestIndication</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Enable RequestIndication for this module. In case of J1939Nm or J1939Dcm, the name is fixed. In case of CDD, the name is &lt;apiServicePrefix&gt;_RequestIndication. In case of RTE, the port is defined as J1939RmRPort&lt;J1939RmUserId&gt;, and the operation is called RequestIndication. This parameter shall not be set for J1939RmUserType J1939RM_USER_COM.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: J1939RmUserSendAck -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:8311a6fb-8b7b-41ff-8de9-c1d692af5ca4">
															<SHORT-NAME>J1939RmUserSendAck</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Enable the SendAck API for this module. In case of RTE, the port is defined as J1939RmPPort&lt;J1939RmUserId&gt;, and the operation is called SendAck. This parameter shall not be set for J1939RmUserType J1939RM_USER_J1939NM or J1939RM_USER_COM.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: J1939RmUserSendRequest -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:1466d83f-619c-4bc9-9e6f-cc6c27153c4b">
															<SHORT-NAME>J1939RmUserSendRequest</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Enable the SendRequest API for this module. In case of RTE, the port is defined as J1939RmPPort&lt;J1939RmUserId&gt;, and the operation is called SendRequest. This parameter shall not be set for J1939RmUserType J1939RM_USER_J1939NM, J1939RM_USER_J1939DCM, or J1939RM_USER_COM.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: J1939RmUserTimeoutSupervision -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:a5425ede-c411-4f83-af88-3a68127893d3">
															<SHORT-NAME>J1939RmUserTimeoutSupervision</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Enable RequestTimeoutIndication and CancelRequestTimeout for this module. RequestTimeoutIndication: In case of CDD, the name is &lt;apiServicePrefix&gt;_RequestTimeoutIndication.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">In case of RTE, the port is defined as J1939RmRPort&lt;J1939RmUserId&gt;, and the operation is called RequestTimeoutIndication. CancelRequestTimeout: In case of RTE, the port is defined as J1939RmPPort&lt;J1939RmUserId&gt;, and the operation is called CancelRequestTimeout. 
                                                    This parameter shall not be set for J1939RmUserType J1939RM_USER_J1939NM, J1939RM_USER_J1939DCM, or J1939RM_USER_COM.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: J1939RmUserType -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:ee94106e-ef27-4982-a16f-8380a6eb1794">
															<SHORT-NAME>J1939RmUserType</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Type of module using J1939Rm.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:c4233afa-fe46-949e-178e-419f96abaa3b">
																	<SHORT-NAME>J1939RM_CDD</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:db382a68-322c-94b7-11eb-e1195ae28cc8">
																	<SHORT-NAME>J1939RM_RTE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:3e8cb92d-3302-92a4-2144-a4a02cc5a92b">
																	<SHORT-NAME>J1939RM_USER_COM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:f68eb2d0-cf95-8f71-20b1-384f87ef6527">
																	<SHORT-NAME>J1939RM_USER_J1939DCM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:9b6b7a5d-83a9-9af1-358a-a2568598980b">
																	<SHORT-NAME>J1939RM_USER_J1939NM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Foreign Reference Definition: J1939RmUserCddRef -->
														<ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:074f9e81-f250-420f-b47c-2fd7c9430c42">
															<SHORT-NAME>J1939RmUserCddRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the CDD module description. This parameter is only required for J1939RmUserType J1939RM_USER_CDD.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-TYPE>ECUC-MODULE-CONFIGURATION-VALUES</DESTINATION-TYPE>
														</ECUC-FOREIGN-REFERENCE-DEF>
													</REFERENCES>
													<SUB-CONTAINERS>
														<!-- Container Definition: J1939RmComIPdu -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:895c7730-bd1b-4c1a-819d-d5a5c83a0001">
															<SHORT-NAME>J1939RmComIPdu</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Contains the configuration of an I-PDU that is to be transmitted on request by COM. This configuration container is only relevant for J1939RmUserType J1939RM_USER_COM.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: J1939RmComIPduDA -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:319fe995-75c2-4673-9548-d6615a3911e1">
																	<SHORT-NAME>J1939RmComIPduDA</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Destination address of the COM I-PDU.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>253</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: J1939RmComIPduPGN -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:e25ddae6-8a07-4ede-9340-dd08d4a54df3">
																	<SHORT-NAME>J1939RmComIPduPGN</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">PGN of the COM I-PDU.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>262143</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: J1939RmComIPduSA -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:203a04b4-8064-46f2-9f2a-be9f3ce6391d">
																	<SHORT-NAME>J1939RmComIPduSA</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Source address of the COM I-PDU.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>253</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
															<SUB-CONTAINERS>
																<!-- Container Definition: J1939RmLowerComIPdu -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:7934f63c-95a7-4b79-8d06-02be6e0ea367">
																	<SHORT-NAME>J1939RmLowerComIPdu</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Contains the configuration of the I-PDU that is sent from J1939Rm to CanIf or J1939Tp.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: J1939RmLowerComIPduId -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:7338e5b5-35e0-491e-9aef-c4dffdf83796">
																			<SHORT-NAME>J1939RmLowerComIPduId</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">The I-PDU identifier used for communication with PduR.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
																			<MAX>65535</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																	</PARAMETERS>
																	<REFERENCES>
																		<!-- Reference Definition: J1939RmLowerComIPduRef -->
																		<ECUC-REFERENCE-DEF UUID="ECUC:9f97cfd5-beb1-4cac-8b4f-fddb4554023b">
																			<SHORT-NAME>J1939RmLowerComIPduRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Reference to the Pdu object representing the I-PDU.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
																		</ECUC-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: J1939RmUpperComIPdu -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:a8bbbdfa-1c66-4428-ad89-272b41945433">
																	<SHORT-NAME>J1939RmUpperComIPdu</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Contains the configuration of the I-PDU that is sent from COM to J1939Rm.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: J1939RmUpperComIPduId -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:bc462154-84f8-45c6-bb6d-924ddc6947c6">
																			<SHORT-NAME>J1939RmUpperComIPduId</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">The I-PDU identifier used for communication with PduR.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
																			<MAX>65535</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																	</PARAMETERS>
																	<REFERENCES>
																		<!-- Reference Definition: J1939RmUpperComIPduRef -->
																		<ECUC-REFERENCE-DEF UUID="ECUC:e1ebaf35-cb54-438c-8ee7-68c218ac6c1d">
																			<SHORT-NAME>J1939RmUpperComIPduRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Reference to the Pdu object representing the I-PDU.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
																		</ECUC-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
															</SUB-CONTAINERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: J1939RmGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:5b511604-71f4-4fcc-855b-e14dfcf98b78">
									<SHORT-NAME>J1939RmGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Contains the general configuration parameters of the module.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: J1939RmDevErrorDetect -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:ceb44cc8-860f-44f9-bd1a-ffa67730e875">
											<SHORT-NAME>J1939RmDevErrorDetect</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Pre-processor switch for enabling development error detection support.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: J1939RmMainFunctionPeriod -->
										<ECUC-FLOAT-PARAM-DEF UUID="ECUC:d465d8dd-0f52-4781-976e-90c74758483a">
											<SHORT-NAME>J1939RmMainFunctionPeriod</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Execution cycle of J1939Rm_MainFunction in seconds.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>0.255</MAX>
											<MIN>0.001</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: J1939RmSupportAckIndication -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:3604d400-de3f-4289-8096-b362ad169b62">
											<SHORT-NAME>J1939RmSupportAckIndication</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Pre-processor switch for enabling support of acknowledgement indications.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: J1939RmSupportAckTransmission -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:bb26d6d7-9043-4363-b73e-e4831744d154">
											<SHORT-NAME>J1939RmSupportAckTransmission</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Pre-processor switch for enabling support of acknowledgement transmission.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: J1939RmSupportRequestIndication -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:dfb17ab4-18dd-4573-9991-b5542658f1cf">
											<SHORT-NAME>J1939RmSupportRequestIndication</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Pre-processor switch for enabling support of request indications.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: J1939RmSupportRequestTransmission -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:49750482-096b-4af9-a961-9239859cfb16">
											<SHORT-NAME>J1939RmSupportRequestTransmission</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Pre-processor switch for enabling support of request transmission.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: J1939RmSupportTimeoutSupervision -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:a598ff50-095c-4c40-bd21-79db6bf6a85a">
											<SHORT-NAME>J1939RmSupportTimeoutSupervision</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Pre-processor switch for enabling support of request timeout supervision.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: J1939RmTxConfirmationTimeout -->
										<ECUC-FLOAT-PARAM-DEF UUID="ECUC:4d1b62b1-1dc5-42d1-96ab-2108c6b5374f">
											<SHORT-NAME>J1939RmTxConfirmationTimeout</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Time in seconds to wait for a confirmation after transmission of a message. The behaviour when the time elapses depends on the transmitted message.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65.535</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: J1939RmVersionInfoApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:48e29e97-ddfd-4ada-b709-5f4891496e07">
											<SHORT-NAME>J1939RmVersionInfoApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Pre-processor switch for enabling version info API support.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
