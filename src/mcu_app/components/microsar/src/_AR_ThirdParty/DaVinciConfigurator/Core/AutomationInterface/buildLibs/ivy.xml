<?xml version="1.0" encoding="UTF-8"?>
<ivy-module version="2.0">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to <PERSON><PERSON><PERSON> or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <info organisation="com.vector.cfg.build.gradle" module="DVCfgAutomationInterfaceGradle" revision="1.0.9" status="integration" publication="20201121201945"/>
  <configurations>
    <conf name="compile" visibility="public"/>
    <conf name="default" visibility="public" extends="runtime"/>
    <conf name="runtime" visibility="public"/>
  </configurations>
  <publications>
    <artifact name="DVCfgAutomationInterfaceGradle" type="jar" ext="jar" conf="compile,runtime"/>
  </publications>
  <dependencies/>
</ivy-module>
