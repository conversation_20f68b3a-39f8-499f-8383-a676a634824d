<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: EthTSyn -->
						<ECUC-MODULE-DEF UUID="627dd85d-46ff-4fcb-9dae-b2bfd55de964">
							<SHORT-NAME>EthTSyn</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the Synchronized Time-base Manager (StbM) module with respect to global time handling on Ethernet.</L-2>
							</DESC>
							<CATEGORY>VENDOR_SPECIFIC_MODULE_DEFINITION</CATEGORY>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>1.00.00</REVISION-LABEL>
										<ISSUED-BY>vislje</ISSUED-BY>
										<DATE>2014-09-24</DATE>
									</DOC-REVISION>
									<DOC-REVISION>
										<REVISION-LABEL>1.00.01</REVISION-LABEL>
										<ISSUED-BY>vislje</ISSUED-BY>
										<DATE>2014-11-11</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">SW version update</L-2>
												</CHANGE>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
									<DOC-REVISION>
										<REVISION-LABEL>1.00.02</REVISION-LABEL>
										<ISSUED-BY>vissem</ISSUED-BY>
										<DATE>2015-01-08</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">changed multiplicity of EthTSynSynchronizedTimeBaseRef from 1:1 to 0:1</L-2>
												</CHANGE>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
									<DOC-REVISION>
										<REVISION-LABEL>1.00.03</REVISION-LABEL>
										<ISSUED-BY>vissem</ISSUED-BY>
										<DATE>2015-02-18</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">SW version update</L-2>
												</CHANGE>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
									<DOC-REVISION>
										<REVISION-LABEL>1.00.04</REVISION-LABEL>
										<ISSUED-BY>vissem</ISSUED-BY>
										<DATE>2015-02-19</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">changed multiplicity of EthTSynSynchronizedTimeBaseRef back to 1:1</L-2>
												</CHANGE>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
									<DOC-REVISION>
										<REVISION-LABEL>1.00.05</REVISION-LABEL>
										<ISSUED-BY>vissem</ISSUED-BY>
										<DATE>2015-03-26</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">SW version update</L-2>
												</CHANGE>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
									<DOC-REVISION>
										<REVISION-LABEL>1.01.00</REVISION-LABEL>
										<ISSUED-BY>vissem</ISSUED-BY>
										<DATE>2015-02-27</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">FEAT-1268: Add timestamp support (STBM) in software for EthTSyn</L-2>
												</CHANGE>
												<REASON>
													<L-2 L="EN">ESCAN00081418</L-2>
												</REASON>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
									<DOC-REVISION>
										<REVISION-LABEL>01.01.01</REVISION-LABEL>
										<ISSUED-BY>vissem</ISSUED-BY>
										<DATE>2015-07-10</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">Added description to EthTSynGlobalTimeFollowUpTimeout parameter</L-2>
												</CHANGE>
												<REASON>
													<L-2 L="EN">ESCAN00082229</L-2>
												</REASON>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
									<DOC-REVISION>
										<REVISION-LABEL>01.01.02</REVISION-LABEL>
										<ISSUED-BY>vissem</ISSUED-BY>
										<DATE>2015-07-08</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">Change type of  EthTSynUserCalloutNsPerTick from Integer to Float</L-2>
												</CHANGE>
												<REASON>
													<L-2 L="EN">ESCAN00083935</L-2>
												</REASON>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
									<DOC-REVISION>
										<REVISION-LABEL>01.02.00</REVISION-LABEL>
										<ISSUED-BY>vissem</ISSUED-BY>
										<DATE>2015-07-09</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">Change multiplicity of EthTSynSynchronizedTimeBaseRef to 0:1, Changed Multiplicity of EthTSynUserConfigFile from mandatory (1:1) to optional (0:1)</L-2>
												</CHANGE>
												<REASON>
													<L-2 L="EN">ESCAN00081412, ESCAN00084015</L-2>
												</REASON>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
									<DOC-REVISION>
										<REVISION-LABEL>01.02.01</REVISION-LABEL>
										<ISSUED-BY>vissem</ISSUED-BY>
										<DATE>2015-09-01</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">SW version update</L-2>
												</CHANGE>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
									<DOC-REVISION>
										<REVISION-LABEL>02.00.00</REVISION-LABEL>
										<ISSUED-BY>vissem</ISSUED-BY>
										<DATE>2015-09-01</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">FEAT-1529: Support Ethernet Switches for Ethernet Time Sync</L-2>
												</CHANGE>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
									<DOC-REVISION>
										<REVISION-LABEL>02.00.01</REVISION-LABEL>
										<ISSUED-BY>vissem</ISSUED-BY>
										<DATE>2016-01-26</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">SW version update</L-2>
												</CHANGE>
												<REASON>
													<L-2 L="EN">ESCAN00087452</L-2>
												</REASON>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
									<DOC-REVISION>
										<REVISION-LABEL>02.00.02</REVISION-LABEL>
										<ISSUED-BY>vissem</ISSUED-BY>
										<DATE>2016-02-22</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">SW version update</L-2>
												</CHANGE>
												<REASON>
													<L-2 L="EN">ESCAN00088420</L-2>
												</REASON>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
									<DOC-REVISION>
										<REVISION-LABEL>02.01.00</REVISION-LABEL>
										<ISSUED-BY>vissem</ISSUED-BY>
										<DATE>2016-02-22</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">Added Parameters: EthTSynCorrActionInSync, EthTSynCorrActionOutOfSync, EthTSynGlobalTimeSyncTimeout, EthTSynGmCapable, EthTSynEnableAnnounce, EthTSynBridgeKeepSourcePortIdentity.Type of the parameters EthTSynSyncSentCallBackFunction and EthTSynFollowUpSentCallBackFunction changed from String to FunctionName</L-2>
												</CHANGE>
												<REASON>
													<L-2 L="EN">ESCAN00087688, ESCAN00088494, ESCAN00088616, ESCAN00088618, WORKITEM3092</L-2>
												</REASON>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: EthTSynGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="a258f9cf-c87e-481e-ac20-324c15f93fef">
									<SHORT-NAME>EthTSynGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container holds the general parameters of the Ethernet-specific Synchronized Time-base Manager</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">true</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: EthTSynDevErrorDetect -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="7d918638-3b1d-4dce-ae12-0baf3c4ca5f4">
											<SHORT-NAME>EthTSynDevErrorDetect</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter switches the Development Error Detection and Notification ON or OFF.
true:	Development error detection is enabled.
false:	Development error detection is disabled.

Note: In general, the development error detection is recommended during pre-test phase. It is not recommended to enable the development error detection in production code due to increased runtime and ROM needs.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthTSynHardwareTimestampSupport -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="8c6d0fcd-aafe-437f-833d-d87f363de28d">
											<SHORT-NAME>EthTSynHardwareTimestampSupport</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Activate/Deactivate the hardware time stamping functionality of the Ethernet hardware. 
True: Clock is administrated by EthIf-Lower layer (e.g. Eth). Timestamps are requested from EthIf (e.g. EthIf_GetIngressTimestamp()) 
False: Clock is administrated by StbM, therefore timestamps are provided by the StbM.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">True: Timestamp is retrieved from the Ethernet hardware
                                        False: Timestamp is retrieved from the StbM</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthTSynMainFunctionPeriod -->
										<ECUC-FLOAT-PARAM-DEF UUID="9fd56f11-928d-4310-96d3-e2031da6391a">
											<SHORT-NAME>EthTSynMainFunctionPeriod</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Schedule period of the main function EthTSyn_MainFunction. Unit: [s].</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">SEC</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0.005</DEFAULT-VALUE>
											<MAX>1.7976931348623157E+308</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthTSynVersionInfo -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ef73f771-f5de-4b97-a8ba-96ce3d616b91">
											<SHORT-NAME>EthTSynVersionInfo</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Activate/Deactivate the version information API (EthTSyn_GetVersionInfo) to get major, minor and patch version information.
True: version information API activated 
False: version information API deactivated.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-STRING-PARAM-DEF UUID="7c67e6b3-ddbd-42fa-b641-cfa90a68a311">
											<SHORT-NAME>EthTSynDestPhyAddr</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Destination Physical Address (MAC-Address)</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Destination Physical Hardware Address (MAC-Address) of EthTSyn-gPTP Frames. Input format has to match xx::xx::xx::xx::xx::xx, where x stands for a hex value between 0 and F.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-STRING-PARAM-DEF-VARIANTS>
												<ECUC-STRING-PARAM-DEF-CONDITIONAL>
													<DEFAULT-VALUE>01:80:C2:00:00:0E</DEFAULT-VALUE>
												</ECUC-STRING-PARAM-DEF-CONDITIONAL>
											</ECUC-STRING-PARAM-DEF-VARIANTS>
										</ECUC-STRING-PARAM-DEF>
										<ECUC-STRING-PARAM-DEF UUID="bc661a29-f070-464a-b45f-46e08859377a">
											<SHORT-NAME>EthTSynUserConfigFile</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to an external user configuration file that will be included during generation.
The content of the user configuration file will be added at the end of the generated module configuration file and allows altering or extending the generated code.
Caution: User configuration files can cause the software module to malfunction and must only be used with great care!</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-STRING-PARAM-DEF>
										<ECUC-STRING-PARAM-DEF UUID="a438cb59-e7ed-4df1-b671-7357add8c814">
											<SHORT-NAME>EthTSynPublicCddHeaderFile</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Defines header files for call-back functions (e.g. Sync / FollowUp sent) which shall be included in case of CDDs. Range of characters is 1.. 32.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-STRING-PARAM-DEF-VARIANTS>
												<ECUC-STRING-PARAM-DEF-CONDITIONAL>
													<MAX-LENGTH>32</MAX-LENGTH>
													<MIN-LENGTH>1</MIN-LENGTH>
												</ECUC-STRING-PARAM-DEF-CONDITIONAL>
											</ECUC-STRING-PARAM-DEF-VARIANTS>
										</ECUC-STRING-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="7548b071-a37e-49e1-9461-fdb4ca4fd77d">
											<SHORT-NAME>EthTSynGmCapable</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Grand Master Capable</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Select whether the EthTSyn can act as Grand Master. In a bridge configuration, the ECU will take over the role of the GM in case a Sync-Timeout is detected on the Slave-Por</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>Vector Informatik</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<ECUC-REFERENCE-DEF UUID="fb7b6805-4434-4706-bb86-adf0aa63b1f4">
											<SHORT-NAME>EthTSynEthIfFrameType</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Frame Type</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">The chosen frame owner determines which frames (in respect to ethertype) are received.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/EthIf/EthIfConfigSet/EthIfFrameOwnerConfig</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: EthTSynGlobalTimeDomain -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ea7c3071-1841-4467-b7c8-7bcc761329f6">
									<SHORT-NAME>EthTSynGlobalTimeDomain</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This represents the existence of a global time domain on Ethernet. The EthTSyn module can administrate several global time domains at the same time that in itself form a hierarchy of domains and sub-domains.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">true</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">If the EthTSyn exists it is assumed that at least one global time domain exists.</L-1>
										</P>
									</INTRODUCTION>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: EthTSynGlobalTimeDomainId -->
										<ECUC-INTEGER-PARAM-DEF UUID="17baeae0-c720-4952-ba54-cb53c7467639">
											<SHORT-NAME>EthTSynGlobalTimeDomainId</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The global time domain ID.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>31</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthTSynGlobalTimeFollowUpTimeout -->
										<ECUC-FLOAT-PARAM-DEF UUID="6c55b223-1b24-4988-849c-b273172e0a34">
											<SHORT-NAME>EthTSynGlobalTimeFollowUpTimeout</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the time waited for the reception of a FollowUp message after receiving a Sync message. After the Timeout the Sync message will be discarded. Unit: seconds</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">SEC</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0.125</DEFAULT-VALUE>
											<MAX>1.7976931348623157E+308</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<ECUC-FLOAT-PARAM-DEF UUID="f84c9461-8e4e-4717-b89f-c529908cd5cf">
											<SHORT-NAME>EthTSynGlobalTimeSyncTimeout</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter is only used in a Bridge configuration. It specifies the time, the bridge is waiting for the reception of a new Sync message on its slave port before a Grand Master timeout is assumed and the Bridge begins to generate its own Sync and FollowUp messages.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">SEC</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>Vector Informatik</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0.25</DEFAULT-VALUE>
											<MAX>255</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="ea0d5919-e83e-4b60-b61c-3e63f5e58cb9">
											<SHORT-NAME>EthTSynBridgeKeepSourcePortIdentity</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter is only used in a bridge configuration.

When this parameter is enabled, the source port identity of forwarded Sync/FollowUp messages is not modified.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>Vector Informatik</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Symbolic Name Reference Definition: EthTSynSynchronizedTimeBaseRef -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="1b6287ba-bceb-43e3-b897-f8f3bb03edca">
											<SHORT-NAME>EthTSynSynchronizedTimeBaseRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Optional reference to the synchronized time-base.
The refernece is only used when a TimeSource with STBM_TIMESTAMPING as TimestampMethdo is used.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/StbM/StbMSynchronizedTimeBase</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
										<ECUC-REFERENCE-DEF UUID="5ad6b517-af0d-4d2a-a549-6148f661a47e">
											<SHORT-NAME>EthTSynTimeSourceRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Refernece to the Time Source which will be used for the TimeDomain.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/EthTSyn/EthTSynTimeSourceConfig</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<!-- Container Definition: EthTSynGlobalTimeMaster -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:20d1e409-b2eb-4086-bbe6-0525aff7c21c">
											<SHORT-NAME>EthTSynGlobalTimeMaster</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configuration of the global time master. Each global time domain is required to have exactly one global time master. This master may or may not exist on the configured ECU.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: EthTSynGlobalTimeTxFollowUpOffset -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:144256e5-4ef8-8799-714a-f1273c3b0ee6">
													<SHORT-NAME>EthTSynGlobalTimeTxFollowUpOffset</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This represents configuration of the TX offset for Follow_Up / Pdelay_Resp_Follow_Up messages. Unit: seconds</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>Inf</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: EthTSynGlobalTimeTxPdelayReqPeriod -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:20432b32-7ee2-81fc-6476-e8f0ef2d1ef4">
													<SHORT-NAME>EthTSynGlobalTimeTxPdelayReqPeriod</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This represents configuration of the TX period for Pdelay_Req messages. Unit: seconds</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>Inf</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: EthTSynGlobalTimeTxPeriod -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:895a28b4-2c4e-4b59-ac47-70aadc1de237">
													<SHORT-NAME>EthTSynGlobalTimeTxPeriod</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This represents configuration of the TX period. Unit: seconds</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>Inf</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: EthTSynGlobalTimeEthIfRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:c6496c14-1aaf-8d15-615b-86889b88b373">
													<SHORT-NAME>EthTSynGlobalTimeEthIfRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This represents the reference to the Ethernet interface taken to fetch the global time information.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EthIf/EthIfConfigSet/EthIfController</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: EthTSynGlobalTimeSlave -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:06ad6036-fb6a-450e-a8c1-7509babd6012">
											<SHORT-NAME>EthTSynGlobalTimeSlave</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configuration of a time slave. Each global time domain is required to have at least one time slave. The configured ECU may or may not represent a time slave.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: EthTSynGlobalTimeTxFollowUpOffset -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:fa1dd312-9777-8c21-5e26-610b4700acdc">
													<SHORT-NAME>EthTSynGlobalTimeTxFollowUpOffset</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This represents configuration of the TX offset for Follow_Up / Pdelay_Resp_Follow_Up messages. Unit: seconds</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>Inf</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: EthTSynGlobalTimeTxPdelayReqPeriod -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:061ea75f-c761-8684-5152-58d4f9f2bcea">
													<SHORT-NAME>EthTSynGlobalTimeTxPdelayReqPeriod</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This represents configuration of the TX period for Pdelay_Req messages. Unit: seconds</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>Inf</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: EthTSynTimeHardwareCorrectionThreshold -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:b7b5155b-b081-440e-9987-7ec76ab8e9d9">
													<SHORT-NAME>EthTSynTimeHardwareCorrectionThreshold</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter can be taken to define the maximum deviation between the local time and the time obtained from SYNC message that triggers a correction of the time drift caused by the hardware on the local node.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FLOAT-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: EthTSynGlobalTimeEthIfRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:ac24e841-632e-919d-4e36-f66ca64e5169">
													<SHORT-NAME>EthTSynGlobalTimeEthIfRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This represents the reference to the Ethernet interface taken to fetch the global time information.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EthIf/EthIfConfigSet/EthIfController</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="233c922e-e57a-4479-8c57-4a710e54102b">
									<SHORT-NAME>EthTSynTimeSourceConfig</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Configuration of the Time Sources used by the EthTSyn module</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">true</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<PARAMETERS>
										<ECUC-ENUMERATION-PARAM-DEF UUID="e98bd81f-ccdd-476b-a502-af659ed3c16e">
											<SHORT-NAME>EthTSynTimestampMethod</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Select which timestamping method is used.
HW_TIMESTAMPING:
- Clock is managed in HW

STBM_TIMESTAMPING:
- Clock is managed in StbM

OS/GPT/CALLOUT_TIMESTAMPING:
- Clock is managed in EthTSyn
- Local Time is derived from Os/Gpt Counter or from UserCallout</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>HW_TIMESTAMPING</DEFAULT-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="857b8cfb-06dc-4945-ad5c-d39130a7163b">
													<SHORT-NAME>HW_TIMESTAMPING</SHORT-NAME>
													<ORIGIN>FEAT-1268: SW-Timestamp support</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="9b2fe288-2c21-4eef-926a-4cc3d1de817e">
													<SHORT-NAME>STBM_TIMESTAMPING</SHORT-NAME>
													<ORIGIN>FEAT-1268: SW-Timestamp support</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="4472e888-727d-4362-a636-cf97344f9136">
													<SHORT-NAME>GPT_TIMESTAMPING</SHORT-NAME>
													<ORIGIN>FEAT-1268: SW-Timestamp support</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="896d6ea3-d9b7-4b84-bca1-0d7c127095f2">
													<SHORT-NAME>OS_TIMESTAMPING</SHORT-NAME>
													<ORIGIN>FEAT-1268: SW-Timestamp support</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="9a20ec89-bca4-4451-a368-02ec0800ff25">
													<SHORT-NAME>CALLOUT_TIMESTAMPING</SHORT-NAME>
													<ORIGIN>FEAT-1268: SW-Timestamp support</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<ECUC-CHOICE-REFERENCE-DEF UUID="accdb8c3-3244-4eba-aa83-059df00d703c">
											<SHORT-NAME>EthTSynTimeBaseRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to the Counter/EthIfController used as Time base.

This parameter is only used if EthTSynTImestampMethod is set to:
GPT_TIMESTAMPING
OS_TIMESTAMPING</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<DESTINATION-REFS>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsCounter</DESTINATION-REF>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Gpt/GptChannelConfigSet/GptChannelConfiguration</DESTINATION-REF>
											</DESTINATION-REFS>
										</ECUC-CHOICE-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="884a38a6-cc65-41b0-9a37-43644eb67560">
											<SHORT-NAME>EthTSynTimestampCalloutConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configuration of Timestamp Usercallout.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<PARAMETERS>
												<ECUC-FUNCTION-NAME-DEF UUID="ec1ead39-ad3f-4ea5-96dd-2b26deb5b5c0">
													<SHORT-NAME>EthTSynTimestampUserCallout</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the User-Callout function when Timestamp Method is set to "CALLOUT-TIMESTAMPING".
This Function is called when EthTSyn_GetCurrentTimeRaw is invoked.
The Callout-function has the following signature:

Std_ReturnType TimestampUserCallout(EthTSyn_TimestampTickType *CurrentTickTime)

The function shall return "E_OK" when the retrieval of the Time was successful and "E_NOT_OK" otherwise.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="056b41cf-1e7b-4372-9c8c-695d90ac37a3">
													<SHORT-NAME>EthTSynUserCalloutMaxValue</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Max Tick Value returned by the Usercallout.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>1000000000</DEFAULT-VALUE>
													<MAX>4294967296</MAX>
													<MIN>1</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-STRING-PARAM-DEF UUID="e1e1bab4-7bdc-4d7f-8917-37df3a25176b">
													<SHORT-NAME>EthTSynUserIncludeFile</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines a header file name which shall be included by EthTSyn for the Timestamp UserCallout.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<ECUC-FLOAT-PARAM-DEF UUID="a4cc02f0-c5d7-4e79-a54c-6c77ab82ba6c">
													<SHORT-NAME>EthTSynUserCalloutNsPerTick</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Time of one "Tick" (returned by the Usercallout) in Nanoseconds.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>1</DEFAULT-VALUE>
													<MAX>1000000000</MAX>
													<MIN>1</MIN>
												</ECUC-FLOAT-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
