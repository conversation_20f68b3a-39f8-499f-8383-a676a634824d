<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.eclipse.orbit.bundles</groupId>
    <artifactId>ant</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

  <artifactId>org.apache.ant</artifactId>
  <version>1.10.6-SNAPSHOT</version>
  <packaging>eclipse-bundle-recipe</packaging>

  <name>Apache Ant (all-in-one)</name>

  <properties>
    <recipe.excludes>META-INF/maven/**/*.*,images/</recipe.excludes>
    <recipe.unpackdependencies>false</recipe.unpackdependencies>
    <recipe.stripversion>true</recipe.stripversion>
    <recipe.excludedotfolder>true</recipe.excludedotfolder>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-antlr</artifactId>
      <version>1.10.6</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-apache-bcel</artifactId>
      <version>1.10.6</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-apache-bsf</artifactId>
      <version>1.10.6</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-apache-log4j</artifactId>
      <version>1.10.6</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-apache-oro</artifactId>
      <version>1.10.6</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-apache-regexp</artifactId>
      <version>1.10.6</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-apache-resolver</artifactId>
      <version>1.10.6</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-apache-xalan2</artifactId>
      <version>1.10.6</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-commons-logging</artifactId>
      <version>1.10.6</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-commons-net</artifactId>
      <version>1.10.6</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-imageio</artifactId>
      <version>1.10.6</version>
    </dependency>
    
    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-jai</artifactId>
      <version>1.10.6</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-javamail</artifactId>
      <version>1.10.6</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-jdepend</artifactId>
      <version>1.10.6</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-jmf</artifactId>
      <version>1.10.6</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-jsch</artifactId>
      <version>1.10.6</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-junit</artifactId>
      <version>1.10.6</version>
    </dependency>
    
    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-junitlauncher</artifactId>
      <version>1.10.6</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-junit4</artifactId>
      <version>1.10.6</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-launcher</artifactId>
      <version>1.10.6</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-netrexx</artifactId>
      <version>1.10.6</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-swing</artifactId>
      <version>1.10.6</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-testutil</artifactId>
      <version>1.10.6</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant</artifactId>
      <version>1.10.6</version>
    </dependency>
    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant-xz</artifactId>
      <version>1.10.6</version>
    </dependency>
  </dependencies>

</project>
