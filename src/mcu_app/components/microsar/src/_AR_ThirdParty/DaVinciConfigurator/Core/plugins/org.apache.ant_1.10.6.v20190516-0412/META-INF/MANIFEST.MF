Manifest-Version: 1.0
Automatic-Module-Name: org.apache.ant
Bundle-SymbolicName: org.apache.ant
Built-By: genie.orbit
Bundle-ManifestVersion: 2
Bundle-Vendor: %bundleVendor
Import-Package: COM.ibm.netrexx.process;resolution:=optional,com.jcraf
 t.jsch;resolution:=optional,com.sun.media.jai.codec;resolution:=optio
 nal,javax.activation;resolution:=optional,javax.imageio;resolution:=o
 ptional,javax.imageio.stream;resolution:=optional,javax.mail;resoluti
 on:=optional;version="[1.6,2)",javax.mail.internet;resolution:=option
 al;version="[1.6,2)",javax.media.jai;resolution:=optional,javax.scrip
 t;resolution:=optional,javax.sound.sampled;resolution:=optional,javax
 .swing;resolution:=optional,javax.swing.border;resolution:=optional,j
 avax.xml.namespace;resolution:=optional,javax.xml.parsers;resolution:
 =optional,javax.xml.stream;resolution:=optional,javax.xml.transform;r
 esolution:=optional,javax.xml.transform.sax;resolution:=optional,java
 x.xml.transform.stream;resolution:=optional,javax.xml.xpath;resolutio
 n:=optional,jdepend.textui;resolution:=optional,jdepend.xmlui;resolut
 ion:=optional,junit.framework;resolution:=optional,netrexx.lang;resol
 ution:=optional,org.apache.bcel.classfile;resolution:=optional;versio
 n="[6.2,7)",org.apache.bsf;resolution:=optional,org.apache.commons.lo
 gging;resolution:=optional,org.apache.commons.net.bsd;resolution:=opt
 ional;version="[3.6,4)",org.apache.commons.net.ftp;resolution:=option
 al;version="[3.6,4)",org.apache.commons.net.telnet;resolution:=option
 al;version="[3.6,4)",org.apache.log4j;resolution:=optional;version="[
 1.2,2)",org.apache.oro.text.regex;resolution:=optional,org.apache.reg
 exp;resolution:=optional,org.apache.tools.ant;resolution:=optional;ve
 rsion="[1.10,2)",org.apache.tools.ant.attribute;resolution:=optional;
 version="[1.10,2)",org.apache.tools.ant.dispatch;resolution:=optional
 ;version="[1.10,2)",org.apache.tools.ant.filters;resolution:=optional
 ;version="[1.10,2)",org.apache.tools.ant.filters.util;resolution:=opt
 ional;version="[1.10,2)",org.apache.tools.ant.helper;resolution:=opti
 onal;version="[1.10,2)",org.apache.tools.ant.input;resolution:=option
 al;version="[1.10,2)",org.apache.tools.ant.launch;resolution:=optiona
 l;version="[1.10,2)",org.apache.tools.ant.listener;resolution:=option
 al;version="[1.10,2)",org.apache.tools.ant.property;resolution:=optio
 nal;version="[1.10,2)",org.apache.tools.ant.taskdefs;resolution:=opti
 onal;version="[1.10,2)",org.apache.tools.ant.taskdefs.compilers;resol
 ution:=optional;version="[1.10,2)",org.apache.tools.ant.taskdefs.cond
 ition;resolution:=optional;version="[1.10,2)",org.apache.tools.ant.ta
 skdefs.email;resolution:=optional;version="[1.10,2)",org.apache.tools
 .ant.taskdefs.launcher;resolution:=optional;version="[1.10,2)",org.ap
 ache.tools.ant.taskdefs.optional;resolution:=optional;version="[1.10,
 2)",org.apache.tools.ant.taskdefs.optional.depend.constantpool;resolu
 tion:=optional;version="[1.10,2)",org.apache.tools.ant.taskdefs.optio
 nal.extension;resolution:=optional;version="[1.10,2)",org.apache.tool
 s.ant.taskdefs.optional.extension.resolvers;resolution:=optional;vers
 ion="[1.10,2)",org.apache.tools.ant.taskdefs.optional.javah;resolutio
 n:=optional;version="[1.10,2)",org.apache.tools.ant.taskdefs.optional
 .jsp;resolution:=optional;version="[1.10,2)",org.apache.tools.ant.tas
 kdefs.optional.jsp.compilers;resolution:=optional;version="[1.10,2)",
 org.apache.tools.ant.taskdefs.optional.junitlauncher.confined;resolut
 ion:=optional;version="[1.10,2)",org.apache.tools.ant.taskdefs.option
 al.native2ascii;resolution:=optional;version="[1.10,2)",org.apache.to
 ols.ant.taskdefs.optional.net;resolution:=optional;version="[1.10,2)"
 ,org.apache.tools.ant.taskdefs.rmic;resolution:=optional;version="[1.
 10,2)",org.apache.tools.ant.types;resolution:=optional;version="[1.10
 ,2)",org.apache.tools.ant.types.optional.image;resolution:=optional;v
 ersion="[1.10,2)",org.apache.tools.ant.types.optional.imageio;resolut
 ion:=optional;version="[1.10,2)",org.apache.tools.ant.types.resources
 ;resolution:=optional;version="[1.10,2)",org.apache.tools.ant.types.r
 esources.comparators;resolution:=optional;version="[1.10,2)",org.apac
 he.tools.ant.types.resources.selectors;resolution:=optional;version="
 [1.10,2)",org.apache.tools.ant.types.selectors;resolution:=optional;v
 ersion="[1.10,2)",org.apache.tools.ant.types.selectors.modifiedselect
 or;resolution:=optional;version="[1.10,2)",org.apache.tools.ant.types
 .spi;resolution:=optional;version="[1.10,2)",org.apache.tools.ant.uti
 l;resolution:=optional;version="[1.10,2)",org.apache.tools.ant.util.d
 epend;resolution:=optional;version="[1.10,2)",org.apache.tools.ant.ut
 il.facade;resolution:=optional;version="[1.10,2)",org.apache.tools.an
 t.util.java15;resolution:=optional;version="[1.10,2)",org.apache.tool
 s.ant.util.optional;resolution:=optional;version="[1.10,2)",org.apach
 e.tools.ant.util.regexp;resolution:=optional;version="[1.10,2)",org.a
 pache.tools.bzip2;resolution:=optional;version="[1.10,2)",org.apache.
 tools.mail;resolution:=optional;version="[1.10,2)",org.apache.tools.t
 ar;resolution:=optional;version="[1.10,2)",org.apache.tools.zip;resol
 ution:=optional;version="[1.10,2)",org.apache.xalan.trace;resolution:
 =optional,org.apache.xalan.transformer;resolution:=optional,org.apach
 e.xml.resolver;resolution:=optional,org.apache.xml.resolver.helpers;r
 esolution:=optional,org.apache.xml.resolver.tools;resolution:=optiona
 l,org.junit;resolution:=optional,org.junit.platform.engine;resolution
 :=optional,org.junit.platform.engine.discovery;resolution:=optional,o
 rg.junit.platform.engine.reporting;resolution:=optional,org.junit.pla
 tform.engine.support.descriptor;resolution:=optional,org.junit.platfo
 rm.launcher;resolution:=optional,org.junit.platform.launcher.core;res
 olution:=optional,org.junit.platform.launcher.listeners;resolution:=o
 ptional,org.junit.rules;resolution:=optional,org.junit.runner;resolut
 ion:=optional,org.junit.runner.manipulation;resolution:=optional,org.
 junit.runner.notification;resolution:=optional,org.tukaani.xz;resolut
 ion:=optional,org.w3c.dom;resolution:=optional,org.xml.sax;resolution
 :=optional,org.xml.sax.helpers;resolution:=optional
Require-Capability: osgi.ee;filter:="(&(osgi.ee=JavaSE)(version=1.8))"
Export-Package: org.apache.tools.ant;version="1.10.6";uses:="junit.fra
 mework,org.apache.tools.ant.input,org.apache.tools.ant.launch,org.apa
 che.tools.ant.property,org.apache.tools.ant.taskdefs.condition,org.ap
 ache.tools.ant.types,org.apache.tools.ant.types.selectors,org.junit.r
 ules,org.xml.sax",org.apache.tools.ant.attribute;version="1.10.6";use
 s:="org.apache.tools.ant",org.apache.tools.ant.dispatch;version="1.10
 .6";uses:="org.apache.tools.ant",org.apache.tools.ant.filters;version
 ="1.10.6";uses:="org.apache.tools.ant,org.apache.tools.ant.types,org.
 apache.tools.ant.util",org.apache.tools.ant.filters.util;version="1.1
 0.6";uses:="org.apache.tools.ant,org.apache.tools.ant.types",org.apac
 he.tools.ant.helper;version="1.10.6";uses:="org.apache.tools.ant,org.
 apache.tools.ant.types,org.xml.sax,org.xml.sax.helpers",org.apache.to
 ols.ant.input;version="1.10.6";uses:="org.apache.tools.ant",org.apach
 e.tools.ant.launch;version="1.10.6",org.apache.tools.ant.listener;ver
 sion="1.10.6";uses:="org.apache.tools.ant",org.apache.tools.ant.loade
 r;version="1.10.6";uses:="org.apache.tools.ant,org.apache.tools.ant.t
 ypes",org.apache.tools.ant.property;version="1.10.6";uses:="org.apach
 e.tools.ant",org.apache.tools.ant.taskdefs;version="1.10.6";uses:="ja
 vax.xml.namespace,org.apache.tools.ant,org.apache.tools.ant.filters,o
 rg.apache.tools.ant.taskdefs.compilers,org.apache.tools.ant.taskdefs.
 condition,org.apache.tools.ant.taskdefs.email,org.apache.tools.ant.ta
 skdefs.launcher,org.apache.tools.ant.taskdefs.rmic,org.apache.tools.a
 nt.types,org.apache.tools.ant.types.resources,org.apache.tools.ant.ty
 pes.resources.selectors,org.apache.tools.ant.types.selectors,org.apac
 he.tools.ant.types.selectors.modifiedselector,org.apache.tools.ant.ty
 pes.spi,org.apache.tools.ant.util,org.apache.tools.ant.util.facade,or
 g.apache.tools.tar,org.apache.tools.zip,org.w3c.dom,org.xml.sax",org.
 apache.tools.ant.taskdefs.compilers;version="1.10.6";uses:="org.apach
 e.tools.ant,org.apache.tools.ant.taskdefs,org.apache.tools.ant.types"
 ,org.apache.tools.ant.taskdefs.condition;version="1.10.6";uses:="org.
 apache.tools.ant,org.apache.tools.ant.taskdefs,org.apache.tools.ant.t
 ypes,org.apache.tools.ant.types.selectors",org.apache.tools.ant.taskd
 efs.cvslib;version="1.10.6";uses:="org.apache.tools.ant,org.apache.to
 ols.ant.taskdefs,org.apache.tools.ant.types",org.apache.tools.ant.tas
 kdefs.email;version="1.10.6";uses:="org.apache.tools.ant,org.apache.t
 ools.ant.types",org.apache.tools.ant.taskdefs.launcher;version="1.10.
 6";uses:="org.apache.tools.ant,org.apache.tools.ant.util",org.apache.
 tools.ant.taskdefs.modules;version="1.10.6";uses:="org.apache.tools.a
 nt,org.apache.tools.ant.types",org.apache.tools.ant.taskdefs.optional
 ;version="1.10.6";uses:="javax.xml.transform,org.apache.tools.ant,org
 .apache.tools.ant.taskdefs,org.apache.tools.ant.taskdefs.optional.jav
 ah,org.apache.tools.ant.taskdefs.optional.native2ascii,org.apache.too
 ls.ant.types,org.apache.tools.ant.util,org.apache.tools.ant.util.faca
 de,org.junit,org.junit.rules,org.w3c.dom,org.xml.sax",org.apache.tool
 s.ant.taskdefs.optional.ccm;version="1.10.6";uses:="org.apache.tools.
 ant,org.apache.tools.ant.taskdefs,org.apache.tools.ant.types",org.apa
 che.tools.ant.taskdefs.optional.clearcase;version="1.10.6";uses:="org
 .apache.tools.ant,org.apache.tools.ant.types",org.apache.tools.ant.ta
 skdefs.optional.depend;version="1.10.6";uses:="org.apache.tools.ant,o
 rg.apache.tools.ant.taskdefs,org.apache.tools.ant.types,org.apache.to
 ols.ant.util.depend",org.apache.tools.ant.taskdefs.optional.depend.co
 nstantpool;version="1.10.6",org.apache.tools.ant.taskdefs.optional.ej
 b;version="1.10.6";uses:="javax.xml.parsers,org.apache.tools.ant,org.
 apache.tools.ant.taskdefs,org.apache.tools.ant.types,org.xml.sax",org
 .apache.tools.ant.taskdefs.optional.extension;version="1.10.6";uses:=
 "org.apache.tools.ant,org.apache.tools.ant.taskdefs.optional.extensio
 n.resolvers,org.apache.tools.ant.types,org.apache.tools.ant.util",org
 .apache.tools.ant.taskdefs.optional.extension.resolvers;version="1.10
 .6";uses:="org.apache.tools.ant,org.apache.tools.ant.taskdefs.optiona
 l.extension",org.apache.tools.ant.taskdefs.optional.i18n;version="1.1
 0.6";uses:="org.apache.tools.ant,org.apache.tools.ant.taskdefs,org.ap
 ache.tools.ant.types",org.apache.tools.ant.taskdefs.optional.image;ve
 rsion="1.10.6";uses:="org.apache.tools.ant,org.apache.tools.ant.taskd
 efs,org.apache.tools.ant.types,org.apache.tools.ant.types.optional.im
 age,org.apache.tools.ant.types.optional.imageio,org.apache.tools.ant.
 util",org.apache.tools.ant.taskdefs.optional.j2ee;version="1.10.6";us
 es:="org.apache.tools.ant,org.apache.tools.ant.taskdefs,org.apache.to
 ols.ant.types",org.apache.tools.ant.taskdefs.optional.javacc;version=
 "1.10.6";uses:="org.apache.tools.ant,org.apache.tools.ant.types",org.
 apache.tools.ant.taskdefs.optional.javah;version="1.10.6";uses:="org.
 apache.tools.ant,org.apache.tools.ant.taskdefs.optional,org.apache.to
 ols.ant.types",org.apache.tools.ant.taskdefs.optional.jdepend;version
 ="1.10.6";uses:="org.apache.tools.ant,org.apache.tools.ant.taskdefs,o
 rg.apache.tools.ant.types",org.apache.tools.ant.taskdefs.optional.jli
 nk;version="1.10.6";uses:="org.apache.tools.ant,org.apache.tools.ant.
 taskdefs,org.apache.tools.ant.types",org.apache.tools.ant.taskdefs.op
 tional.jsp;version="1.10.6";uses:="org.apache.tools.ant,org.apache.to
 ols.ant.taskdefs,org.apache.tools.ant.types",org.apache.tools.ant.tas
 kdefs.optional.jsp.compilers;version="1.10.6";uses:="org.apache.tools
 .ant,org.apache.tools.ant.taskdefs.optional.jsp,org.apache.tools.ant.
 types",org.apache.tools.ant.taskdefs.optional.junit;version="1.10.6";
 uses:="javax.xml.parsers,junit.framework,org.apache.tools.ant,org.apa
 che.tools.ant.taskdefs,org.apache.tools.ant.types,org.junit.runner,or
 g.junit.runner.notification,org.w3c.dom",org.apache.tools.ant.taskdef
 s.optional.junit.xsl;version="1.10.6",org.apache.tools.ant.taskdefs.o
 ptional.junitlauncher;version="1.10.6";uses:="org.apache.tools.ant,or
 g.apache.tools.ant.taskdefs.optional.junitlauncher.confined,org.junit
 .platform.launcher",org.apache.tools.ant.taskdefs.optional.junitlaunc
 her.confined;version="1.10.6";uses:="javax.xml.stream,org.apache.tool
 s.ant,org.apache.tools.ant.types",org.apache.tools.ant.taskdefs.optio
 nal.native2ascii;version="1.10.6";uses:="org.apache.tools.ant,org.apa
 che.tools.ant.taskdefs.optional,org.apache.tools.ant.types",org.apach
 e.tools.ant.taskdefs.optional.net;version="1.10.6";uses:="org.apache.
 commons.net.bsd,org.apache.commons.net.ftp,org.apache.commons.net.tel
 net,org.apache.tools.ant,org.apache.tools.ant.taskdefs.email,org.apac
 he.tools.ant.types,org.apache.tools.ant.util",org.apache.tools.ant.ta
 skdefs.optional.pvcs;version="1.10.6";uses:="org.apache.tools.ant,org
 .apache.tools.ant.taskdefs,org.apache.tools.ant.types",org.apache.too
 ls.ant.taskdefs.optional.script;version="1.10.6";uses:="org.apache.to
 ols.ant,org.apache.tools.ant.taskdefs,org.apache.tools.ant.types",org
 .apache.tools.ant.taskdefs.optional.sos;version="1.10.6";uses:="org.a
 pache.tools.ant,org.apache.tools.ant.types",org.apache.tools.ant.task
 defs.optional.sound;version="1.10.6";uses:="javax.sound.sampled,org.a
 pache.tools.ant",org.apache.tools.ant.taskdefs.optional.splash;versio
 n="1.10.6";uses:="org.apache.tools.ant",org.apache.tools.ant.taskdefs
 .optional.ssh;version="1.10.6";uses:="com.jcraft.jsch,org.apache.tool
 s.ant,org.apache.tools.ant.types",org.apache.tools.ant.taskdefs.optio
 nal.testing;version="1.10.6";uses:="org.apache.tools.ant,org.apache.t
 ools.ant.taskdefs,org.apache.tools.ant.taskdefs.condition",org.apache
 .tools.ant.taskdefs.optional.unix;version="1.10.6";uses:="org.apache.
 tools.ant,org.apache.tools.ant.dispatch,org.apache.tools.ant.taskdefs
 ,org.apache.tools.ant.types",org.apache.tools.ant.taskdefs.optional.v
 ss;version="1.10.6";uses:="org.apache.tools.ant,org.apache.tools.ant.
 types",org.apache.tools.ant.taskdefs.optional.windows;version="1.10.6
 ";uses:="org.apache.tools.ant.taskdefs",org.apache.tools.ant.taskdefs
 .optional.xz;version="1.10.6";uses:="org.apache.tools.ant.taskdefs",o
 rg.apache.tools.ant.taskdefs.rmic;version="1.10.6";uses:="org.apache.
 tools.ant,org.apache.tools.ant.taskdefs,org.apache.tools.ant.types,or
 g.apache.tools.ant.util",org.apache.tools.ant.types;version="1.10.6";
 uses:="javax.xml.transform,org.apache.tools.ant,org.apache.tools.ant.
 filters,org.apache.tools.ant.taskdefs,org.apache.tools.ant.types.sele
 ctors,org.apache.tools.ant.types.selectors.modifiedselector,org.apach
 e.tools.ant.util,org.apache.tools.ant.util.regexp,org.junit,org.junit
 .rules,org.xml.sax",org.apache.tools.ant.types.conditions;version="1.
 10.6",org.apache.tools.ant.types.mappers;version="1.10.6";uses:="org.
 apache.tools.ant.types,org.apache.tools.ant.util",org.apache.tools.an
 t.types.optional;version="1.10.6";uses:="org.apache.tools.ant,org.apa
 che.tools.ant.filters,org.apache.tools.ant.taskdefs.condition,org.apa
 che.tools.ant.types,org.apache.tools.ant.types.selectors,org.apache.t
 ools.ant.util",org.apache.tools.ant.types.optional.depend;version="1.
 10.6";uses:="org.apache.tools.ant,org.apache.tools.ant.types",org.apa
 che.tools.ant.types.optional.image;version="1.10.6";uses:="javax.medi
 a.jai,org.apache.tools.ant.types",org.apache.tools.ant.types.optional
 .imageio;version="1.10.6";uses:="org.apache.tools.ant.types",org.apac
 he.tools.ant.types.optional.xz;version="1.10.6";uses:="org.apache.too
 ls.ant.types,org.apache.tools.ant.types.resources",org.apache.tools.a
 nt.types.resolver;version="1.10.6";uses:="org.apache.tools.ant.types,
 org.apache.xml.resolver,org.apache.xml.resolver.tools",org.apache.too
 ls.ant.types.resources;version="1.10.6";uses:="org.apache.tools.ant,o
 rg.apache.tools.ant.types,org.apache.tools.ant.types.resources.compar
 ators,org.apache.tools.ant.types.resources.selectors,org.apache.tools
 .ant.types.selectors,org.apache.tools.ant.util,org.apache.tools.tar,o
 rg.apache.tools.zip",org.apache.tools.ant.types.resources.comparators
 ;version="1.10.6";uses:="org.apache.tools.ant,org.apache.tools.ant.ty
 pes",org.apache.tools.ant.types.resources.selectors;version="1.10.6";
 uses:="org.apache.tools.ant,org.apache.tools.ant.types,org.apache.too
 ls.ant.types.resources.comparators",org.apache.tools.ant.types.select
 ors;version="1.10.6";uses:="org.apache.tools.ant,org.apache.tools.ant
 .types,org.apache.tools.ant.types.resources.selectors,org.apache.tool
 s.ant.types.selectors.modifiedselector,org.apache.tools.ant.util",org
 .apache.tools.ant.types.selectors.modifiedselector;version="1.10.6";u
 ses:="org.apache.tools.ant,org.apache.tools.ant.types,org.apache.tool
 s.ant.types.resources.selectors,org.apache.tools.ant.types.selectors"
 ,org.apache.tools.ant.types.spi;version="1.10.6";uses:="org.apache.to
 ols.ant",org.apache.tools.ant.util;version="1.10.6";uses:="javax.xml.
 parsers,org.apache.tools.ant,org.apache.tools.ant.types,org.apache.to
 ols.ant.types.resources,org.apache.tools.ant.types.resources.selector
 s,org.apache.tools.ant.util.optional,org.apache.tools.ant.util.regexp
 ,org.w3c.dom,org.xml.sax",org.apache.tools.ant.util.depend;version="1
 .10.6";uses:="org.apache.tools.ant.types",org.apache.tools.ant.util.d
 epend.bcel;version="1.10.6";uses:="org.apache.bcel.classfile,org.apac
 he.tools.ant.util.depend",org.apache.tools.ant.util.facade;version="1
 .10.6";uses:="org.apache.tools.ant,org.apache.tools.ant.types",org.ap
 ache.tools.ant.util.java15;version="1.10.6",org.apache.tools.ant.util
 .optional;version="1.10.6";uses:="org.apache.tools.ant,org.apache.too
 ls.ant.util",org.apache.tools.ant.util.regexp;version="1.10.6";uses:=
 "org.apache.oro.text.regex,org.apache.regexp,org.apache.tools.ant,org
 .junit",org.apache.tools.bzip2;version="1.10.6",org.apache.tools.mail
 ;version="1.10.6",org.apache.tools.tar;version="1.10.6";uses:="org.ap
 ache.tools.zip",org.apache.tools.zip;version="1.10.6"
Bundle-Name: %bundleName
Bundle-Version: 1.10.6.v20190516-0412
Bundle-ClassPath: lib/ant-antlr.jar,lib/ant-apache-bcel.jar,lib/ant-ap
 ache-bsf.jar,lib/ant-apache-log4j.jar,lib/ant-apache-oro.jar,lib/ant-
 apache-regexp.jar,lib/ant-apache-resolver.jar,lib/ant-apache-xalan2.j
 ar,lib/ant-commons-logging.jar,lib/ant-commons-net.jar,lib/ant-imagei
 o.jar,lib/ant-jai.jar,lib/ant-javamail.jar,lib/ant-jdepend.jar,lib/an
 t-jmf.jar,lib/ant-jsch.jar,lib/ant-junit.jar,lib/ant-junit4.jar,lib/a
 nt-junitlauncher.jar,lib/ant-launcher.jar,lib/ant-netrexx.jar,lib/ant
 -swing.jar,lib/ant-testutil.jar,lib/ant-xz.jar,lib/ant.jar
Created-By: Eclipse Bundle Recipe Maven Plug-in
Build-Jdk: 1.8.0_192
Eclipse-BundleShape: dir

Name: lib/ant-xz.jar
SHA-256-Digest: MneJBb59Pn82O8/EbIqfz8hEqvC7rvJJPUd1JIPrALs=

Name: etc/mmetrics-frames.xsl
SHA-256-Digest: Ne2FgaRkm56aU+wLS2zIDW+4XJEOsG62heOCbmkYzbg=

Name: about_files/SAX-LICENSE.html
SHA-256-Digest: FQRgO0nRSZCPug2AudAK07PSB4DJJspXsT1adPMxeuo=

Name: lib/ant.jar
SHA-256-Digest: dEpkjNjiVRnVOId5axguWpsHX/W9AYD39wYjMlTIYV0=

Name: lib/ant-jdepend.jar
SHA-256-Digest: KnGb8aV3MAz1KQk47Pb3lFJNQP+gUBTGpFcAyE1RwiU=

Name: etc/checkstyle/checkstyle-frames.xsl
SHA-256-Digest: hfIfwqoPqhvdzOvLjZZXSUl5PaDuLTfsznu7WKVsQAA=

Name: lib/ant-junit4.jar
SHA-256-Digest: mbUYHwT/YoM47qBsibBokhITLAkIGMPn3cTUCiFkaEA=

Name: lib/ant-antlr.jar
SHA-256-Digest: pjWkG2YLcu8+Y12CRAMOevBQXLxVYABefgv/J0pgJ8w=

Name: etc/coverage-frames.xsl
SHA-256-Digest: c83olZ+UFMPX1wZQ3ta1EC8gFNsA85LPC09mTl4PWt0=

Name: lib/ant-apache-regexp.jar
SHA-256-Digest: oZyrifQY7J51+LdCH+STwc3Ft77TwQAzFuCB6ak27AU=

Name: etc/junit-frames.xsl
SHA-256-Digest: w50U7iQCp1SLuXziW9ASS18uhkY9xsvX7GGg47RwVSk=

Name: lib/ant-apache-log4j.jar
SHA-256-Digest: XkSNvYWoEAqP1C7MyCET/FfZlp+nBjJlGToJmmmXY8k=

Name: lib/ant-launcher.jar
SHA-256-Digest: t4TzIOJGx/0iXytH+1EnXdzqqdokIp/lO6ciW8SYMPg=

Name: bin/antRun.bat
SHA-256-Digest: RvkoqxA5T4asT7tZvjcmQ7BTeoITcVcTcLN0mVFg0vA=

Name: etc/checkstyle/checkstyle-text.xsl
SHA-256-Digest: vQnaLw2nAseue+n/3IZRIhcRFfcRxgeKHwRcVz2UPe0=

Name: lib/ant-jai.jar
SHA-256-Digest: 1u3DVh9rSmuVXTEQdIgB8nLSb4+78g4dHvDCY7ce8T0=

Name: about_files/LICENSE
SHA-256-Digest: 1FfXdo4jghuHwEdZrLKqqy4iAxlzegYhXRYlew32/2A=

Name: lib/ant-apache-xalan2.jar
SHA-256-Digest: aONZ8Xqfs7KUS6fjemPwNMOWhtK8FX0WTnJkE087pwI=

Name: bin/antenv.cmd
SHA-256-Digest: Z389kGjE2ZTaiXn40M1w37UhbMwNsHJ7CVjJeomD/MI=

Name: lib/ant-junit.jar
SHA-256-Digest: 6gTqbQvNUTbXurZAKemk6JDhDT4MemLreJYdX3inq0c=

Name: lib/ant-junitlauncher.jar
SHA-256-Digest: VCdRGptxwHtzj20r/0YUcEi8G3WidffNb11H+Fyc9xQ=

Name: lib/ant-imageio.jar
SHA-256-Digest: yvdKfBCKf9ZTCowGQIeqiOYxRkA57DsXqLhAe9Nuoa0=

Name: lib/ant-commons-logging.jar
SHA-256-Digest: /uu0DPhiEGY883GAAHmiqj1SgYSGApJDpfYSRr8vbew=

Name: about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt
SHA-256-Digest: z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=

Name: bin/antRun.pl
SHA-256-Digest: 1JQv8vLQnDpkd3JDqU8wMHfWMYet0MAoqEV2hn9Ytf8=

Name: lib/ant-apache-resolver.jar
SHA-256-Digest: HJ5rd//F7pGgDxGfU++886d/qunVwCGwfQkSjjIA3/U=

Name: bin/ant
SHA-256-Digest: 0rLHqmh8F9XG8Jpi0dkpezPlyROE/UywOPopAZlHmsA=

Name: lib/ant-netrexx.jar
SHA-256-Digest: tk05DFx2BmcIboLEm+spWmYtN8BeEaHr4Dx/v9vnsoQ=

Name: etc/checkstyle/checkstyle-xdoc.xsl
SHA-256-Digest: X18mSthJgMvSZtDm8mc21xjBnQqPl4Dxk4xEVFJB86g=

Name: etc/log.xsl
SHA-256-Digest: XxidzipQrOM/Y7eRTcHrkqCqIrO7xMNoPfTeKDeqGbI=

Name: OSGI-INF/l10n/bundle.properties
SHA-256-Digest: x7cEMojylK91VoPCj5D/qXFeGFoHnvUj+Tp8su7fgMk=

Name: lib/ant-swing.jar
SHA-256-Digest: O1o4kazOSzrkFz//uoKAt6aSG6j1BSe/aixolfiLRU0=

Name: lib/ant-apache-bsf.jar
SHA-256-Digest: bQ9xY9fZnRKfOq0rQLMEO4lgwR4XeNsSGWacz/w8xhI=

Name: about_files/DOM-LICENSE.html
SHA-256-Digest: 0eDSRxw93sR6pC5tpA0nM9Z4SgrpIeo2p/xuvXYcJtY=

Name: bin/complete-ant-cmd.pl
SHA-256-Digest: RfqL8c4TIFUau4BCxEuP4En4JZvZRKfUUTdxdT1J2YU=

Name: lib/ant-testutil.jar
SHA-256-Digest: /0CGCnsnzd28g40ZkCFusMleBIWqpcU3Ul2YCVdUAjQ=

Name: lib/ant-jsch.jar
SHA-256-Digest: EIFnWUp16msEQ90nhz23KY4jU6Gv9SSI4gYnAiH4mag=

Name: bin/lcp.bat
SHA-256-Digest: xE0FZ7y+qCT/bGz9CY264KBYaVAVjjFvwP93JqnPP9g=

Name: etc/jdepend-frames.xsl
SHA-256-Digest: gDJkA5Cx99H+xc28I/fTm04Dunxx3P9ix4stDcJUfSs=

Name: bin/envset.cmd
SHA-256-Digest: /RdqIs+QwZkuWZuoH5Gk6g73MJF8pJJBhq66Ki+cc14=

Name: etc/maudit-frames.xsl
SHA-256-Digest: aTCc05yj08Ji3hp38WvuPkPxf87k6iqan82BxsDeLes=

Name: bin/ant.bat
SHA-256-Digest: W9p7+W5vj5wAJ/3ockMbPZmMgE0viRCcOdFWOtK9G7I=

Name: bin/antRun
SHA-256-Digest: 4iN2O/MIkCDRxAb8GJk31duEE5VjP1d0BV/QsoYg7VA=

Name: etc/changelog.xsl
SHA-256-Digest: jSxGvTssHGKi1RU09qXna1/+38/Ycq6kOlqbBso75Hk=

Name: lib/ant-commons-net.jar
SHA-256-Digest: Wb6aqkURwG+LzmIgdaCl1LN6EayXlpXdUxux2eME94c=

Name: etc/jdepend.xsl
SHA-256-Digest: 8PUeIOCdYbgOSnnxTIa3rJ56AiYBwfopdJkddQNyu74=

Name: etc/checkstyle/checkstyle-frames-sortby-check.xsl
SHA-256-Digest: T2w6z5pv5dEG1DQH9bsHc6uODqEfF0hv1t1QxsmuqgM=

Name: about.html
SHA-256-Digest: TArCm5N3tBK2UEHK3ienpP3HvX0uGs4x8FeHGasvnC4=

Name: etc/junit-noframes.xsl
SHA-256-Digest: TmMoRI6AIa1quc2VUc4whT3MQ9KhYovy/vo+e/ep0TA=

Name: lib/ant-apache-oro.jar
SHA-256-Digest: wJVzAKpJOP6BoBWBoP4P7up6yT7+UrXPtlvzxtjICoo=

Name: lib/ant-javamail.jar
SHA-256-Digest: qEnP6NJC9gI/s4cOyhE1CbOFCiIdmbbRz2Or4w+mTvE=

Name: lib/ant-jmf.jar
SHA-256-Digest: rv/VDl/X3iCkW1ncXnSSAXvCM81Hr+EssdcHMOzO1Os=

Name: about_files/NOTICE
SHA-256-Digest: aUpJbqt/eBp9ctb4gCCQ2J6m53Hx8IvjcZkCzj5WYM4=

Name: bin/runrc.cmd
SHA-256-Digest: hFYL7KlOh0juA4dRiv5P0qI/YJXCKtjSkJKHb7M6uQM=

Name: bin/runant.py
SHA-256-Digest: jEfmrld/lA4WIPolE3bQst/8zUzBQHV1xhYu8wsiaqY=

Name: etc/junit-frames-xalan1.xsl
SHA-256-Digest: +emCiY5vQQn2MLe1b9rwNcb1c/ZFVilDNGvIVEc8na4=

Name: lib/ant-apache-bcel.jar
SHA-256-Digest: GoFCvbnTgzmbA3xzZ/cs4GWG5CWXf4u8UF4CVmkarJY=

Name: bin/runant.pl
SHA-256-Digest: SV+Rk09rIHVnFDPZri0HmjXPZti/AgduRx5e3sn1hLQ=

Name: etc/tagdiff.xsl
SHA-256-Digest: RE9uuObEoffBHHlJH7AUkCNXSuK6hlDhZaqJ/ffAc0w=

Name: META-INF/maven/org.eclipse.orbit.bundles/org.apache.ant/pom.xml
SHA-256-Digest: MfYOBCIm3MaGtCvPjP+naduqJFkz+1F10yj3rtL4hhs=

Name: META-INF/maven/org.eclipse.orbit.bundles/org.apache.ant/pom.prop
 erties
SHA-256-Digest: IQSn+DonmlBUdjnzAwUDliG22RMfgpXZEZVmYFIh8Ec=

Name: bin/ant.cmd
SHA-256-Digest: kR6RZ7/AJkuK6AhLS7w+t1NUnXHd2Q20AXB0H0DaC8U=

