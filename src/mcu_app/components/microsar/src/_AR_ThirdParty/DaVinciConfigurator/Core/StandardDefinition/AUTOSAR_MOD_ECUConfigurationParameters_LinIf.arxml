<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: LinIf -->
						<ECUC-MODULE-DEF UUID="ECUC:ac2ac1e0-b9bd-4f49-bfe2-3ce03cc8061d">
							<SHORT-NAME>LinIf</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the LinIf (LIN Interface) module.</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.2.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
										<DATE>2011-11-09</DATE>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-LINK-TIME</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-POST-BUILD</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: LinIfGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:dc9a773a-d7ca-42ff-a751-8f35fc1171e3">
									<SHORT-NAME>LinIfGeneral</SHORT-NAME>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: LinIfCancelTransmitSupported -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:d6679d00-fe67-48c5-9a7a-ffaeb82ebc57">
											<SHORT-NAME>LinIfCancelTransmitSupported</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Global Pre-Compile Switch to reliably prevent the generation of the dummy LinIf_CancelTransmit API.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: LinIfDevErrorDetect -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:1786d1df-f161-4bd4-be94-d671b4335008">
											<SHORT-NAME>LinIfDevErrorDetect</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Switches the Development Error Detection and Notification ON or OFF.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: LinIfMultipleDriversSupported -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:c3f20a68-c3db-4a68-b9e8-254e8873f238">
											<SHORT-NAME>LinIfMultipleDriversSupported</SHORT-NAME>
											<DESC>
												<L-2 L="EN">States if multiple drivers are included in the LIN Interface or not. The reason for this parameter is to reduce the size of LIN Interface if multiple drivers are not used.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: LinIfMultipleTrcvDriverSupported -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:2265c857-1c3c-48fc-9e10-adeaa218d4d3">
											<SHORT-NAME>LinIfMultipleTrcvDriverSupported</SHORT-NAME>
											<DESC>
												<L-2 L="EN">States if multiple transceiver drivers are included in the LIN Interface or not. The reason for this parameter is to reduce the size of LIN Interface if multiple transceiver drivers are not used.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: LinIfNcOptionalRequestSupported -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:43093996-93e3-461d-b50f-58021a6b42bc">
											<SHORT-NAME>LinIfNcOptionalRequestSupported</SHORT-NAME>
											<DESC>
												<L-2 L="EN">States if the node configuration commands Assign NAD and Conditional Change NAD are supported.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: LinIfPublicCddHeaderFile -->
										<ECUC-STRING-PARAM-DEF UUID="ECUC:90188127-bfd2-42e2-aacc-20d2a3283e45">
											<SHORT-NAME>LinIfPublicCddHeaderFile</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Defines header files for callback functions which shall be included in case of CDDs. Range of characters is 1.. 32.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-STRING-PARAM-DEF-VARIANTS>
												<ECUC-STRING-PARAM-DEF-CONDITIONAL>
													<MAX-LENGTH>32</MAX-LENGTH>
													<MIN-LENGTH>1</MIN-LENGTH>
												</ECUC-STRING-PARAM-DEF-CONDITIONAL>
											</ECUC-STRING-PARAM-DEF-VARIANTS>
										</ECUC-STRING-PARAM-DEF>
										<!-- PARAMETER DEFINITION: LinIfTpSupported -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:395fa338-8613-4555-b427-29807f920ac3">
											<SHORT-NAME>LinIfTpSupported</SHORT-NAME>
											<DESC>
												<L-2 L="EN">States if the TP is included in the LIN Interface or not. The reason for this parameter is to reduce the size of LIN Interface if the TP is not used.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: LinIfTrcvDriverSupported -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:021681e4-7dd7-48b9-a0ae-f46ba162c147">
											<SHORT-NAME>LinIfTrcvDriverSupported</SHORT-NAME>
											<DESC>
												<L-2 L="EN">States if transceiver drivers are included in the LIN Interface or not. The reason for this parameter is to reduce the size of LIN Interface if transceiver drivers are not used.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: LinIfVersionInfoApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:8e091b33-0f2b-41ea-bdcd-4e7f273f4746">
											<SHORT-NAME>LinIfVersionInfoApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Switches the LinIf_GetVersionInfo function ON or OFF.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: LinIfGlobalConfig -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:25d2741f-e24d-4278-923e-13bb5b219004">
									<SHORT-NAME>LinIfGlobalConfig</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container contains the global configuration parameter of the LinIf.</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">It is a MultipleConfigurationContainer, i.e. this container and its sub-containers exit once per configuration set.</L-1>
										</P>
									</INTRODUCTION>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>true</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: LinIfTimeBase -->
										<ECUC-FLOAT-PARAM-DEF UUID="ECUC:d3d4848c-709e-4d4b-b219-c54ea93f1156">
											<SHORT-NAME>LinIfTimeBase</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The delay between processing two frames is a multiple of the LIN Interface time-base in seconds.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>0.255</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: LinIfChannel -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:acb64525-220b-48db-b6f0-5bb8fdb46ff1">
											<SHORT-NAME>LinIfChannel</SHORT-NAME>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: LinIfChannelId -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:0f6b0bda-545e-4531-baa3-9a231615e2f1">
													<SHORT-NAME>LinIfChannelId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter holds the unique channel index value. The value shall be the same as the ComMChannelId of the ComMChannel referenced by LinIfComMNetworkHandleRef.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">Implementation Type: NetworkHandleType</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: LinIfGotoSleepConfirmationUL -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:40dc162a-06e8-4aa7-846f-43facbc0272d">
													<SHORT-NAME>LinIfGotoSleepConfirmationUL</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines the upper layer (UL) module to which the confirmation of the goto-sleep command shall be sent.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:ca065e19-b247-8fa4-5228-11792ec917cb">
															<SHORT-NAME>CDD</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:99c0f01a-ce8b-9268-4c33-7275058b6a32">
															<SHORT-NAME>LIN_SM</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: LinIfScheduleRequestConfirmationUL -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:8ed4be31-285b-4f4c-b944-2e349fe7185b">
													<SHORT-NAME>LinIfScheduleRequestConfirmationUL</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines the upper layer (UL) module to which the confirmation of the successfully performed schedule table change.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:61c16980-f5aa-8bf9-54dd-1566c57d4187">
															<SHORT-NAME>CDD</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:ab3d3623-1c3e-93f8-6bab-f797cb6d27d6">
															<SHORT-NAME>LIN_SM</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: LinIfStartupState -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:59a7fb27-4bdc-49b5-9d06-fe139cf39575">
													<SHORT-NAME>LinIfStartupState</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Defines the state of each LIN channel after startup</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:6574ef8c-0c98-8c88-721c-ad53139c93cd">
															<SHORT-NAME>NORMAL</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:55272573-41e3-8e73-68cb-b1a908707f65">
															<SHORT-NAME>SLEEP</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: LinIfWakeupConfirmationUL -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:6eef5240-87ec-4625-bcd3-e7462d2b305c">
													<SHORT-NAME>LinIfWakeupConfirmationUL</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines the upper layer (UL) module to which the confirmation of the wake-up shall be sent.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:f05e44f5-e062-8ea3-46dd-9e9385a0eaf5">
															<SHORT-NAME>CDD</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:f580eb91-b6f0-9335-3a13-88ea1bf02749">
															<SHORT-NAME>LIN_SM</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Symbolic Name Reference Definition: LinIfChannelRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:8715c30d-96ef-473d-bad1-16e8692c606c">
													<SHORT-NAME>LinIfChannelRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the used channel in Lin.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">Replaces LINIF_CHANNEL_INDEX</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Lin/LinGlobalConfig/LinChannel</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
												<!-- Symbolic Name Reference Definition: LinIfComMNetworkHandleRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:d5db781e-486d-447c-93ff-e6a9cb25adcf">
													<SHORT-NAME>LinIfComMNetworkHandleRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Unique handle to identify one certain LIN network. Reference to one of the network handles configured for the ComM.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/ComM/ComMConfigSet/ComMChannel</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
											<SUB-CONTAINERS>
												<!-- Container Definition: LinIfFrame -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:1c7be285-63c0-4176-87e9-7af9e12e9593">
													<SHORT-NAME>LinIfFrame</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Generic container for all types of LIN frames. The shortName of this container is used as LinIfFrameName.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: LinIfChecksumType -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:1c4bccbb-94d9-47a9-8818-ab3f131f67a4">
															<SHORT-NAME>LinIfChecksumType</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Type of checksum that the frame is using.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:06960102-7ffd-8656-47c3-d6c57341668f">
																	<SHORT-NAME>CLASSIC</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:887a1b4e-a626-8418-303b-70d241aa50ae">
																	<SHORT-NAME>ENHANCED</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: LinIfFrameType -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:b9735872-2db9-41ef-9047-01d69eb83851">
															<SHORT-NAME>LinIfFrameType</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Type of frame that is described (e.g. sporadic frame).</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">The sporadic slot is not found among the frame types. A sporadic slot is a set of sporadic frames.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:730bd87c-8b4d-8eb8-4198-ddf2ecef97bf">
																	<SHORT-NAME>ASSIGN</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:be5cfcf2-f127-90e5-0c77-446bccb6fed4">
																	<SHORT-NAME>ASSIGN_FRAME_ID_RANGE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:735c4525-0719-910c-19dc-6b08a4f624d0">
																	<SHORT-NAME>ASSIGN_NAD</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:8d187355-496a-8435-316e-9269fdb06ea1">
																	<SHORT-NAME>CONDITIONAL</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:1ae77594-c587-8372-1247-3b1f35c383dd">
																	<SHORT-NAME>EVENT_TRIGGERED</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:43dfcea0-0129-9090-332b-0af9b20f7fa3">
																	<SHORT-NAME>FREE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:979cf678-dd66-887e-420f-20ca0ae37ee7">
																	<SHORT-NAME>MRF</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:7ea23d21-4d28-8924-2d04-f043e6840f96">
																	<SHORT-NAME>SAVE_CONFIGURATION</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:6e24a765-cf02-8974-1664-2595c8081304">
																	<SHORT-NAME>SPORADIC</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:e785f036-d53a-844e-2a88-35c7f8d55637">
																	<SHORT-NAME>SRF</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:b27f8876-9e2e-8b7d-4571-a462131e812b">
																	<SHORT-NAME>UNASSIGN</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:b9b0b87d-3f28-90f4-2a2b-e93b6333e702">
																	<SHORT-NAME>UNCONDITIONAL</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: LinIfLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:9d278703-8bd9-4c02-b84a-82dbbe4347b2">
															<SHORT-NAME>LinIfLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Length of the LIN SDU in bytes.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>8</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: LinIfPid -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:5ea9c5f4-9012-4f2c-925b-348b6fb424ea">
															<SHORT-NAME>LinIfPid</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Protected ID of the LIN frame. There is no reason to calculate the Parity in run-time.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<SUB-CONTAINERS>
														<!-- Container Definition: LinIfFixedFrameSdu -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:839c724c-3ce5-4a4f-8258-f9473b61cbd0">
															<SHORT-NAME>LinIfFixedFrameSdu</SHORT-NAME>
															<DESC>
																<L-2 L="EN">In case this is a fixed frame this is the SDU (response).</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">This container represent an eight byte array. The Byte order
                                                        shall be MSB first.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<SUB-CONTAINERS>
																<!-- Container Definition: LinIfFixedFrameSduByte -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:0aa8d449-7336-45d1-acf2-a79b1451a9ff">
																	<SHORT-NAME>LinIfFixedFrameSduByte</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container represents a byte within the 8 byte array.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>8</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>8</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: LinIfFixedFrameSduBytePos -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:0ab4ee67-172e-439e-bc01-1b107c277c05">
																			<SHORT-NAME>LinIfFixedFrameSduBytePos</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Index of the Byte in the SDU (response) 8 byte array.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<MAX>7</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: LinIfFixedFrameSduByteVal -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:76133907-c78a-453d-bff1-c84964c755e6">
																			<SHORT-NAME>LinIfFixedFrameSduByteVal</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Byte value in the SDU (response) 8-byte array.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<MAX>255</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
															</SUB-CONTAINERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: LinIfPduDirection -->
														<ECUC-CHOICE-CONTAINER-DEF UUID="ECUC:ad560145-2b51-46ad-91c3-219c5c976f4c">
															<SHORT-NAME>LinIfPduDirection</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Direction of the frame</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<CHOICES>
																<!-- Container Definition: LinIfInternalPdu -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:c126513a-cf29-4918-bb7e-aea26cd3f5df">
																	<SHORT-NAME>LinIfInternalPdu</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Represents a Diagnostic or Configuration frame : no Message ID (no PduId).</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: LinIfRxPdu -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:13322c3b-51f2-40eb-b615-dfc3e665e5e8">
																	<SHORT-NAME>LinIfRxPdu</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">represents a received PDU/frame</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: LinIfRxIndicationUL -->
																		<ECUC-FUNCTION-NAME-DEF UUID="ECUC:29e68358-16ae-4dc8-b4da-1173c87ca2a4">
																			<SHORT-NAME>LinIfRxIndicationUL</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines the name of the &lt;User_RxIndication&gt;. This parameter depends on the parameter LinIfUserRxIndicationUL.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">If LinIfUserRxIndicationUL equals PDUR, the name of the &lt;User_RxIndication&gt; is fixed. 
                                                                        If LinIfUserRxIndicationUL equals CDD, the name of the &lt;User_RxIndication&gt; is selectable.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<ECUC-FUNCTION-NAME-DEF-VARIANTS>
																				<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
																			</ECUC-FUNCTION-NAME-DEF-VARIANTS>
																		</ECUC-FUNCTION-NAME-DEF>
																		<!-- PARAMETER DEFINITION: LinIfUserRxIndicationUL -->
																		<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:27972d9b-34b5-4115-bee3-e76a7f66edb5">
																			<SHORT-NAME>LinIfUserRxIndicationUL</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines the upper layer (UL) module to which the indication of the successfully received LINRXPDUID has to be routed via &lt;User_RxIndication&gt;.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">This &lt;User_RxIndication&gt; has to be invoked when the indication of the configured LINRXPDUID will be received by a Rx indication event from the LIN Driver module.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<LITERALS>
																				<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:3a45e2fa-7ee8-815a-66fa-6cf3db8b3f75">
																					<SHORT-NAME>CDD</SHORT-NAME>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																				</ECUC-ENUMERATION-LITERAL-DEF>
																				<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:c268d370-18d8-8f9a-3f04-d150af44b857">
																					<SHORT-NAME>PDUR</SHORT-NAME>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																				</ECUC-ENUMERATION-LITERAL-DEF>
																			</LITERALS>
																		</ECUC-ENUMERATION-PARAM-DEF>
																	</PARAMETERS>
																	<REFERENCES>
																		<!-- Reference Definition: LinIfRxPduRef -->
																		<ECUC-REFERENCE-DEF UUID="ECUC:a4b0259f-0d05-4a0e-b86a-89c2558bb7ba">
																			<SHORT-NAME>LinIfRxPduRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Reference to the PDU that is received in this frame.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
																		</ECUC-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: LinIfSlaveToSlavePdu -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:01088ba1-2498-4a9a-ab0c-a5fa045e5621">
																	<SHORT-NAME>LinIfSlaveToSlavePdu</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">represents a slave-to-slave PDU/frame. Master does only send the header but doesn&apos;t receive the response.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">Added for completeness</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: LinIfTxPdu -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:7640ebc8-a9e9-4673-9ccd-ce63404a29ce">
																	<SHORT-NAME>LinIfTxPdu</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">represents a transmitted PDU/frame</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: LinIfTxConfirmationUL -->
																		<ECUC-FUNCTION-NAME-DEF UUID="ECUC:5ffc9a8f-527a-4206-9862-d45ec09056df">
																			<SHORT-NAME>LinIfTxConfirmationUL</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines the name of the &lt;User_TxConfirmation&gt;. This parameter depends on the parameter LinIfUserTxUL.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">If LinIfUserTxUL equals  PDUR, the name of the &lt;User_TxConfirmation&gt; is fixed. 
                                                                        If LinIfUserTxUL equals CDD, the name of the &lt;User_TxConfirmation&gt; is selectable.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<ECUC-FUNCTION-NAME-DEF-VARIANTS>
																				<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
																			</ECUC-FUNCTION-NAME-DEF-VARIANTS>
																		</ECUC-FUNCTION-NAME-DEF>
																		<!-- PARAMETER DEFINITION: LinIfTxPduId -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:dd139fd4-fcb7-4f6e-8311-f18bb2cfe5c3">
																			<SHORT-NAME>LinIfTxPduId</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Identifier of the Pdu for the upper layer.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
																			<MAX>65535</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: LinIfTxTriggerTransmitUL -->
																		<ECUC-FUNCTION-NAME-DEF UUID="ECUC:02bbc963-0110-4f5b-8693-b62cab0c86af">
																			<SHORT-NAME>LinIfTxTriggerTransmitUL</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines the name of the &lt;User_TriggerTransmit&gt;. This parameter  depends on the parameter LinIfUserTxUL. If LinIfUserTxUL equals PDUR, the name  of the &lt;User_TriggerTransmit&gt; is fixed.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">If LinIfUserTxUL equals CDD, the name of the &lt;User_TriggerTransmit&gt; is  selectable.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<ECUC-FUNCTION-NAME-DEF-VARIANTS>
																				<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
																			</ECUC-FUNCTION-NAME-DEF-VARIANTS>
																		</ECUC-FUNCTION-NAME-DEF>
																		<!-- PARAMETER DEFINITION: LinIfUserTxUL -->
																		<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:fe2c0d05-82ea-4af2-84db-989658a33d89">
																			<SHORT-NAME>LinIfUserTxUL</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines the upper layer (UL) module to which the trigger of the transmitted LinTxPdu (via the &lt;User_TriggerTransmit&gt;) or the confirmation of the successfully transmitted LinTxPdu has to be routed (via the &lt;User_TxConfirmation&gt;).</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<LITERALS>
																				<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:e6e8eb78-80ba-88d1-2814-5b465782b48e">
																					<SHORT-NAME>CDD</SHORT-NAME>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																				</ECUC-ENUMERATION-LITERAL-DEF>
																				<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:a94a1a3a-f9f3-9001-2ec9-f41aec973d5d">
																					<SHORT-NAME>PDUR</SHORT-NAME>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																				</ECUC-ENUMERATION-LITERAL-DEF>
																			</LITERALS>
																		</ECUC-ENUMERATION-PARAM-DEF>
																	</PARAMETERS>
																	<REFERENCES>
																		<!-- Reference Definition: LinIfTxPduRef -->
																		<ECUC-REFERENCE-DEF UUID="ECUC:78cf5840-3ad7-412d-ac03-c32aec40298b">
																			<SHORT-NAME>LinIfTxPduRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Reference to the PDU that is transmitted in this frame.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
																		</ECUC-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
															</CHOICES>
														</ECUC-CHOICE-CONTAINER-DEF>
														<!-- Container Definition: LinIfSubstitutionFrames -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:c9f2d77f-3e93-450d-b8d7-c9e1940c044d">
															<SHORT-NAME>LinIfSubstitutionFrames</SHORT-NAME>
															<DESC>
																<L-2 L="EN">List of unconditional Frames that can be sent in a sporadic Frame slot.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: LinIfFramePriority -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:52c7ca27-7e7c-41f9-8a29-9660a446d4f7">
																	<SHORT-NAME>LinIfFramePriority</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Priority of an unconditional frame if used as a sporadic frame.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
															<REFERENCES>
																<!-- Reference Definition: LinIfSubstitutionFrameRef -->
																<ECUC-REFERENCE-DEF UUID="ECUC:d92f4f29-dbe9-4834-b3dd-031a34675f81">
																	<SHORT-NAME>LinIfSubstitutionFrameRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to an unconditional Frame that can be sent in a sporadic Frame slot.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/LinIf/LinIfGlobalConfig/LinIfChannel/LinIfFrame</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
															</REFERENCES>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: LinIfMaster -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:4f684fcc-d115-41c2-be8a-62a09a31f5e5">
													<SHORT-NAME>LinIfMaster</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Each Master can only be connected to one physical channel. This could be compared to the Node parameter in a LDF file.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: LinIfClusterTimeBase -->
														<ECUC-FLOAT-PARAM-DEF UUID="ECUC:cc38fa34-ef1b-4471-b680-edb4c6116888">
															<SHORT-NAME>LinIfClusterTimeBase</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines a time-base for one LIN cluster in seconds (normally 0.002, 0.005 or 0.010s).</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>0.255</MAX>
															<MIN>0</MIN>
														</ECUC-FLOAT-PARAM-DEF>
														<!-- PARAMETER DEFINITION: LinIfJitter -->
														<ECUC-FLOAT-PARAM-DEF UUID="ECUC:120714ea-419a-4e88-aa67-01082afdc636">
															<SHORT-NAME>LinIfJitter</SHORT-NAME>
															<DESC>
																<L-2 L="EN">The jitter specifies the differences between the maximum and minimum delay from time base tick to the header sending start point in seconds.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>0.255</MAX>
															<MIN>0</MIN>
														</ECUC-FLOAT-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: LinIfScheduleTable -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:36bc9661-3231-4789-92b9-add11de9e32d">
													<SHORT-NAME>LinIfScheduleTable</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Describes a schedule table. Each LinIfChannel may have several schedule tables. Each schedule table can only be connected to one channel.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: LinIfResumePosition -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:1241c21b-2b43-4b02-a0a5-d95aca248cfb">
															<SHORT-NAME>LinIfResumePosition</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines, where a schedule table shall be proceeded in case if it has been interrupted by a RUN-ONCE table.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:6c2edccf-9358-9589-38de-5fc3674fbf7a">
																	<SHORT-NAME>CONTINUE_AT_IT_POINT</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:6110e570-9fa1-896f-4af5-203ae2be2ae4">
																	<SHORT-NAME>START_FROM_BEGINNING</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: LinIfRunMode -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:1281d31b-3c9b-41a9-96cf-c05155bc3364">
															<SHORT-NAME>LinIfRunMode</SHORT-NAME>
															<DESC>
																<L-2 L="EN">The schedule table can be executed in two different modes.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:4638c8d6-7642-928b-3103-f228862f701e">
																	<SHORT-NAME>RUN_CONTINUOUS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:5c377f4b-b026-893b-41c1-7902722eff4e">
																	<SHORT-NAME>RUN_ONCE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: LinIfScheduleMode -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:86bbb55b-c6e6-4a39-be37-418f827cc427">
															<SHORT-NAME>LinIfScheduleMode</SHORT-NAME>
															<DESC>
																<L-2 L="EN">The schedule table can be executed in three different modes.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:7b593ad6-4267-9541-42e0-d1cdf8be3d02">
																	<SHORT-NAME>LINTP_APPLICATIVE_SCHEDULE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:5ba36a71-b2b6-94aa-1fab-67f122dd2fb0">
																	<SHORT-NAME>LINTP_DIAG_REQUEST</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:c229a45e-1431-8984-34ae-b80c7c3fa41a">
																	<SHORT-NAME>LINTP_DIAG_RESPONSE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: LinIfScheduleTableIndex -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:fd890a6a-8cf9-4e03-b6dd-058f8d7dd274">
															<SHORT-NAME>LinIfScheduleTableIndex</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This is the unique index used by upper layers to identify a schedule. Note that the NULL_SCHEDULE for each channel has index 0.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: LinIfScheduleTableName -->
														<ECUC-STRING-PARAM-DEF UUID="ECUC:413fe6ac-3350-4a4d-896d-728cf69160a3">
															<SHORT-NAME>LinIfScheduleTableName</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Optional schedule name used to cross-reference with a LDF. This parameter shall always be accompanied by LIN_IF_SCHEDULE_INDEX.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<ECUC-STRING-PARAM-DEF-VARIANTS>
																<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
															</ECUC-STRING-PARAM-DEF-VARIANTS>
														</ECUC-STRING-PARAM-DEF>
													</PARAMETERS>
													<SUB-CONTAINERS>
														<!-- Container Definition: LinIfEntry -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:5856d22c-f08e-4597-aa05-a2fa836b7ec8">
															<SHORT-NAME>LinIfEntry</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Describes an entry in the schedule table (also known as Frame Slot).</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: LinIfDelay -->
																<ECUC-FLOAT-PARAM-DEF UUID="ECUC:d4db710c-4063-4b8d-81e4-d8cea455b14a">
																	<SHORT-NAME>LinIfDelay</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Delay to next entry in schedule table in seconds.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>0.255</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<!-- PARAMETER DEFINITION: LinIfEntryIndex -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:f0b28166-a295-4302-8662-4774593acd65">
																	<SHORT-NAME>LinIfEntryIndex</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Position of the Frame Entry in the Schedule Table. The first entry index in the schedule table is 0.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
															<REFERENCES>
																<!-- Reference Definition: LinIfCollisionResolvingRef -->
																<ECUC-REFERENCE-DEF UUID="ECUC:b0729f03-6459-4a96-b816-9e8aa89cab6e">
																	<SHORT-NAME>LinIfCollisionResolvingRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to the schedule table, which resolves the collision.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/LinIf/LinIfGlobalConfig/LinIfChannel/LinIfScheduleTable</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
																<!-- Reference Definition: LinIfFrameRef -->
																<ECUC-REFERENCE-DEF UUID="ECUC:5be22eba-daf3-4d41-b2ab-d9fe94e681a3">
																	<SHORT-NAME>LinIfFrameRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to the frames that belong to this schedule table entry.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/LinIf/LinIfGlobalConfig/LinIfChannel/LinIfFrame</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
															</REFERENCES>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: LinIfSlave -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e071b12c-44e9-4260-b1db-d54fc2e02a8f">
													<SHORT-NAME>LinIfSlave</SHORT-NAME>
													<DESC>
														<L-2 L="EN">The Node attributes of the Slaves are provided with these parameter. The ShortName of this container is used as LinIfNodeName.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: LinIfConfiguredNad -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:12ae340b-bce8-417e-8c8b-8fd78f073d91">
															<SHORT-NAME>LinIfConfiguredNad</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Definition of the initial node address</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: LinIfFunctionId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:d42320ca-495a-4d03-8f65-51a1513c7bc5">
															<SHORT-NAME>LinIfFunctionId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">LIN function ID</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: LinIfProtocolVersion -->
														<ECUC-STRING-PARAM-DEF UUID="ECUC:cdaf5b4c-6111-4a97-a09e-614be235bd91">
															<SHORT-NAME>LinIfProtocolVersion</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines the LIN Protocol version which is used by the slave.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<ECUC-STRING-PARAM-DEF-VARIANTS>
																<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
															</ECUC-STRING-PARAM-DEF-VARIANTS>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: LinIfSupplierId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:58478360-77ad-41dc-a149-5196162b1f53">
															<SHORT-NAME>LinIfSupplierId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">LIN Supplier ID</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: LinIfVariant -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:1bec9005-853b-46dc-8607-091f91a537b5">
															<SHORT-NAME>LinIfVariant</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Specifies the Variant ID</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: LinIfTransceiverDrvConfig -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:3ddbe282-a769-4c24-b63c-ec4af3e5b96c">
													<SHORT-NAME>LinIfTransceiverDrvConfig</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container contains the configuration (parameters) of all addressed LIN transceivers by each underlying LIN Transceiver Driver.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: LinIfTrcvWakeupNotification -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:2cf0df6b-b5bc-4f50-b33f-2faf58ad5174">
															<SHORT-NAME>LinIfTrcvWakeupNotification</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Selects whether wakeup indication notification is supported.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">True: Enabled
                                                        False: Disabled</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Symbolic Name Reference Definition: LinIfTrcvIdRef -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:df3c8eae-155e-48a0-bdac-7438e0cdd781">
															<SHORT-NAME>LinIfTrcvIdRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Logical handle of the underlying LIN transceiver to be served by the LIN Interface.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/LinTrcv/LinTrcvChannel</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
