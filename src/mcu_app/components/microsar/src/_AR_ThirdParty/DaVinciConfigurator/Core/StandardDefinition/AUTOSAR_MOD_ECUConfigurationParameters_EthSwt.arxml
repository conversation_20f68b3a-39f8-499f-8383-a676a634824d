<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: EthSwt -->
						<ECUC-MODULE-DEF UUID="ECUC:79dfb782-ae0e-4458-acea-4fd1bf19c6d8">
							<SHORT-NAME>EthSwt</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the EthSwt (Ethernet Switch Driver) module.</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.6.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
										<DATE>2014-10-31</DATE>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-LINK-TIME</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-POST-BUILD</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: EthSwtConfig -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:022b64c0-dcee-45d9-8dab-0ec9632c50ec">
									<SHORT-NAME>EthSwtConfig</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Configuration of one Ethernet Switch.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: EthSwtIdx -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:666187aa-c9d0-4ff8-9b55-50a3526b0ced">
											<SHORT-NAME>EthSwtIdx</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the instance ID of the configured Ethernet Switch.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
											<MAX>255</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: EthSwtDemEventParameterRefs -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:d9439cbe-74df-469f-9869-1e59b2158ca8">
											<SHORT-NAME>EthSwtDemEventParameterRefs</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for the references to DemEventParameter elements which shall be invoked using the API Dem_ReportErrorStatus in case the corresponding error occurs. The EventId is taken from the referenced DemEventParameter&apos;s DemEventId value. The standardized errors are provided in the container and can be extended by vendor specific error references.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<REFERENCES>
												<!-- Symbolic Name Reference Definition: ETHSWT_E_ACCESS -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:6d0e97ed-01b9-4f47-b005-2742bb9752b8">
													<SHORT-NAME>ETHSWT_E_ACCESS</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the DemEventParameter which shall be issued when the error &quot;Ethernet Switch Access Failure&quot; has occurred.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: EthSwtNvm -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:d7833215-cb74-44be-9b61-ac433690ccf7">
											<SHORT-NAME>EthSwtNvm</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configuration of one Ethernet Switch Nvm usage in case the module requires non volatile memory in the Ecu to store switch configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<REFERENCES>
												<!-- Symbolic Name Reference Definition: EthSwtNvmBlockDescriptorRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:d80a068c-a983-4a55-9329-88e4e7488c1f">
													<SHORT-NAME>EthSwtNvmBlockDescriptorRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the Nvm block description in the Nvm module configuration.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/NvM/NvMBlockDescriptor</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: EthSwtPort -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:60c612bd-d595-4d5e-80cc-0b360b27db11">
											<SHORT-NAME>EthSwtPort</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configuration of one Ethernet Switch Port.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: EthSwtPortEnableLinkDownCallback -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:ef9ae984-adc2-43f9-bb45-398f6a12e6f2">
													<SHORT-NAME>EthSwtPortEnableLinkDownCallback</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables the callback &lt;User&gt;_LinkDown for this EthSwtPort if an IEEE802.1X link loss is detected.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">&lt;User&gt; is defined by EthSwtLinkDownUser.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: EthSwtPortIdx -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:01e9df96-9db2-47ba-9086-9aa958cf08c4">
													<SHORT-NAME>EthSwtPortIdx</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Specifies the instance ID of the configured Ethernet Switch Port.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: EthSwtPortPhysicalLayerType -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:0ba2ef58-bb99-4b33-a847-bd2c6dfa503b">
													<SHORT-NAME>EthSwtPortPhysicalLayerType</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Defines the physical layer type on this EthSwtPort.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:3440769c-a450-9a1b-0eab-64101ea3cc5f">
															<SHORT-NAME>ETHSWT_PORT_BASE_T</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:e76b53ef-5f6b-98e7-3841-428277cbfdee">
															<SHORT-NAME>ETHSWT_PORT_BROAD_R_REACH</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:85875dd5-d288-9223-1eba-462cb58c4b8f">
															<SHORT-NAME>ETHSWT_PORT_RTPGE</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:dce82c80-9882-93eb-1022-155f43cbdf3b">
															<SHORT-NAME>ETHSWT_PORT_X_MII</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: EthSwtPortPredefinedMacAddresses -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:144e0e11-79ac-4473-9457-b4203cc98786">
													<SHORT-NAME>EthSwtPortPredefinedMacAddresses</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Specifies a list of 48-bit physical addresses (MAC addresses) which can be reached via this port in network byte order.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">Note that further addresses can be learned during runtime.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL>
															<REGULAR-EXPRESSION>[0-9a-fA-F]{2}[[:-][0-9a-fA-F]{2}]{5}</REGULAR-EXPRESSION>
														</ECUC-STRING-PARAM-DEF-CONDITIONAL>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: EthSwtPortSpeed -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:d91d6ae3-d025-4366-a127-fe31196c8183">
													<SHORT-NAME>EthSwtPortSpeed</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Defines the communication speed in Mbit per second on this EthSwtPort in case no EthSwtPortTrcvRef is defined.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">Is optional if EthSwtPortTrcvRef is defined.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:ef078f69-c928-9cd9-2ccc-37b160cc1689">
															<SHORT-NAME>ETHSWT_PORT_SPEED_10</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:e23e7ead-f910-9cd2-3a31-f1a1a68a6254">
															<SHORT-NAME>ETHSWT_PORT_SPEED_100</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:2444c734-a086-9b00-2cb0-d71585507dde">
															<SHORT-NAME>ETHSWT_PORT_SPEED_1000</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Symbolic Name Reference Definition: EthSwtPortTrcvRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:87a2557d-4030-4760-b6ad-713aad1cfcca">
													<SHORT-NAME>EthSwtPortTrcvRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the ethernet transceiver driver this EthSwtPort is connected with.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EthTrcv/EthTrcvConfigSet/EthTrcvConfig</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
											<SUB-CONTAINERS>
												<!-- Container Definition: EthSwtPortEgress -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:c6580740-35c3-4d5f-89a3-e3e5f39d23ef">
													<SHORT-NAME>EthSwtPortEgress</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Configuration of one Ethernet Switch Port Egress behavior.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<REFERENCES>
														<!-- Reference Definition: EthSwtPortEgressLastSchedulerRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:cea09fae-5a0f-486e-b7cc-4f3db96b65fa">
															<SHORT-NAME>EthSwtPortEgressLastSchedulerRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the port scheduler which is the last in the egress port structure.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EthSwt/EthSwtConfig/EthSwtPort/EthSwtPortEgress/EthSwtPortScheduler</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
													<SUB-CONTAINERS>
														<!-- Container Definition: EthSwtPortFifo -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:79e247c0-ddf3-45c7-b51a-fefa41629204">
															<SHORT-NAME>EthSwtPortFifo</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Represents a Fifo in the egress port.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: EthSwtPortFifoTrafficClassAssignment -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:68706570-2b3c-4981-b961-8cc4c6d1c36d">
																	<SHORT-NAME>EthSwtPortFifoTrafficClassAssignment</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Defines which traffic classes are assigned to this Fifo.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>8</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>7</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: EthSwtPortScheduler -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:bbb43a98-8b9c-4764-acbc-cc9802fab490">
															<SHORT-NAME>EthSwtPortScheduler</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Represents a Scheduler in the egress port.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: EthSwtPortSchedulerAlgorithm -->
																<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:d39b10ea-dd5f-4ceb-ab57-145f22eec37c">
																	<SHORT-NAME>EthSwtPortSchedulerAlgorithm</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Defines the scheduler algorithm.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<LITERALS>
																		<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:403e008e-a57e-964c-5e92-0fea650e4c34">
																			<SHORT-NAME>ETHSWT_SCHEDULER_DEFICIT_ROUND_ROBIN</SHORT-NAME>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																		</ECUC-ENUMERATION-LITERAL-DEF>
																		<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:70ac70f2-7ed0-970d-417a-ab73a782d4ea">
																			<SHORT-NAME>ETHSWT_SCHEDULER_STRICT_PRIORITY</SHORT-NAME>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																		</ECUC-ENUMERATION-LITERAL-DEF>
																		<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:e8bac5aa-0394-92a5-3469-be3859967e7c">
																			<SHORT-NAME>ETHSWT_SCHEDULER_WEIGHTED_ROUND_ROBIN</SHORT-NAME>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																		</ECUC-ENUMERATION-LITERAL-DEF>
																	</LITERALS>
																</ECUC-ENUMERATION-PARAM-DEF>
															</PARAMETERS>
															<SUB-CONTAINERS>
																<!-- Container Definition: EthSwtPortSchedulerPredecessor -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:6d84c3e2-51aa-4c9d-b19b-7d4244143ba4">
																	<SHORT-NAME>EthSwtPortSchedulerPredecessor</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Defines an ordered list of predecessors for this scheduler.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: EthSwtPortSchedulerPredecessorOrder -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:452dd519-f0b5-4a80-a860-f7d0dd679034">
																			<SHORT-NAME>EthSwtPortSchedulerPredecessorOrder</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Defines the order of the scheduler predecessors.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<MAX>18446744073709551615</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																	</PARAMETERS>
																	<REFERENCES>
																		<!-- Choice Reference Definition: EthSwtPortEgressPredecessorRef -->
																		<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:15fd9ee4-6e23-44a6-9243-51481779d06c">
																			<SHORT-NAME>EthSwtPortEgressPredecessorRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Choice reference to the scheduler predecessor.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REFS>
																				<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EthSwt/EthSwtConfig/EthSwtPort/EthSwtPortEgress/EthSwtPortFifo</DESTINATION-REF>
																				<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EthSwt/EthSwtConfig/EthSwtPort/EthSwtPortEgress/EthSwtPortScheduler</DESTINATION-REF>
																				<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EthSwt/EthSwtConfig/EthSwtPort/EthSwtPortEgress/EthSwtPortShaper</DESTINATION-REF>
																			</DESTINATION-REFS>
																		</ECUC-CHOICE-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
															</SUB-CONTAINERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: EthSwtPortShaper -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:dde897f6-7f5d-4d5d-aa27-3a650416db47">
															<SHORT-NAME>EthSwtPortShaper</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Represents a Shaper in the egress port.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: EthSwtPortShaperIdleSlope -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:c3e7b5a1-91a9-4e1d-882c-ab494f6fcac6">
																	<SHORT-NAME>EthSwtPortShaperIdleSlope</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Defines the increase of credit in bits per second for the AVB shaper.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>18446744073709551615</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
															<REFERENCES>
																<!-- Reference Definition: EthSwtPortEgressPredecessorFifoRef -->
																<ECUC-REFERENCE-DEF UUID="ECUC:cbe06380-bb22-4f68-b4fe-ab4a25574793">
																	<SHORT-NAME>EthSwtPortEgressPredecessorFifoRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to the fifo which is the predecessor for this shaper.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EthSwt/EthSwtConfig/EthSwtPort/EthSwtPortEgress/EthSwtPortFifo</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
															</REFERENCES>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: EthSwtPortVlanForwarding -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e2b2252c-6874-4b5b-a5f7-3fe9b374750f">
															<SHORT-NAME>EthSwtPortVlanForwarding</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines how messages with a specific VLAN Id shall be handled at this egress port wrt. their VLAN Id.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: EthSwtPortVlanDefaultPriority -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:bd79d7ea-4554-4ea8-854f-3be9beea261e">
																	<SHORT-NAME>EthSwtPortVlanDefaultPriority</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Determines the standard output-priority outgoing messages will be tagged with.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>7</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: EthSwtPortVlanForwardingId -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:da2b6dd7-513d-451c-b6a7-b652af979ace">
																	<SHORT-NAME>EthSwtPortVlanForwardingId</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Determines the VLAN Id the VlanForwarding shall apply to.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>4094</MAX>
																	<MIN>1</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: EthSwtPortVlanForwardingType -->
																<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:4e0b90c2-142f-4599-9ac1-b77f78c77b4c">
																	<SHORT-NAME>EthSwtPortVlanForwardingType</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Defines how the message with a specific VLAN Id shall be handled.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<LITERALS>
																		<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:7056b0ee-d84b-8ed3-633b-ac7a9bc9a504">
																			<SHORT-NAME>ETHSWT_NOT_SENT</SHORT-NAME>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																		</ECUC-ENUMERATION-LITERAL-DEF>
																		<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:46b150fa-3997-93b1-3c23-d71d22dd9e46">
																			<SHORT-NAME>ETHSWT_SENT_TAGGED</SHORT-NAME>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																		</ECUC-ENUMERATION-LITERAL-DEF>
																		<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:99a9ec39-5421-9412-3fe5-63ae1a04d99b">
																			<SHORT-NAME>ETHSWT_SENT_UNTAGGED</SHORT-NAME>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																		</ECUC-ENUMERATION-LITERAL-DEF>
																	</LITERALS>
																</ECUC-ENUMERATION-PARAM-DEF>
															</PARAMETERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: EthSwtPortIngress -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:6201a610-faee-41a6-b33f-1055b2dc404e">
													<SHORT-NAME>EthSwtPortIngress</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Configuration of one Ethernet Switch Port ingress behavior.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: EthSwtPortIngressVlanModification -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:fb3ff4fc-7e38-4211-a5ef-e4de2de24e19">
															<SHORT-NAME>EthSwtPortIngressVlanModification</SHORT-NAME>
															<DESC>
																<L-2 L="EN">If this parameter is defined all messages which arrive at this ingress port will be tagged with this VLAN Id. This tagging happen also if the arriving message already has a VLAN Id, it will be overwritten by the defined one.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">If this parameter is not defined no changes to the VLAN Id shall happen at this ingress port.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4094</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: EthSwtPortTrafficClassAssignment -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:dfae6cad-011a-422c-b718-6c61c19a5607">
															<SHORT-NAME>EthSwtPortTrafficClassAssignment</SHORT-NAME>
															<DESC>
																<L-2 L="EN">If this parameter is defined all arriving messages at this ingress port shall be assigned this traffic class.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">If this parameter is not defined no general port based traffic class assignment is done.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>7</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<SUB-CONTAINERS>
														<!-- Container Definition: EthSwtPriorityRegeneration -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:8a1a555a-1e95-45e3-bc8f-e9414c3363d1">
															<SHORT-NAME>EthSwtPriorityRegeneration</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines a priority regeneration where the EthSwtPriorityRegenerationIngressPriority is replaced by EthSwtPriorityRegenerationRegeneratedPriority.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">The EthSwtPriorityRegeneration is optional in case no priority regeneration shall be performed.

                                                        In case a EthSwtPriorityRegeneration is defined it shall have 8 mappings, one for each priority.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>8</UPPER-MULTIPLICITY>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: EthSwtPriorityRegenerationIngressPriority -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:f991d5ac-24d3-43a6-81e1-5368c7df2b7f">
																	<SHORT-NAME>EthSwtPriorityRegenerationIngressPriority</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Message priority of the incoming message.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>7</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: EthSwtPriorityRegenerationRegeneratedPriority -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:ed4e5a31-7f83-4c6a-9725-e59f7a500303">
																	<SHORT-NAME>EthSwtPriorityRegenerationRegeneratedPriority</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Message priority the incoming message will be tagged with.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>7</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: EthSwtPriorityTrafficClassAssignment -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:ebce6c0b-f226-42e1-82eb-f26ad54d1a75">
															<SHORT-NAME>EthSwtPriorityTrafficClassAssignment</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines a priority based traffic class assignment. All messages with a specific priority (EthSwtPriorityTrafficClassAssignmentPriority) arriving at this ingress port or, if enabled regenerated priorities (EthSwtPriorityRegeneration), shall be assigned to a traffic class (EthSwtPriorityTrafficClassAssignmentTrafficClass).</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>8</UPPER-MULTIPLICITY>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: EthSwtPriorityTrafficClassAssignmentPriority -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:806b5fe1-238e-4332-aeca-a209cc150a6a">
																	<SHORT-NAME>EthSwtPriorityTrafficClassAssignmentPriority</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Message priority.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>7</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: EthSwtPriorityTrafficClassAssignmentTrafficClass -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:8e1ada3b-fe7e-4632-b7a5-40284b38abb3">
																	<SHORT-NAME>EthSwtPriorityTrafficClassAssignmentTrafficClass</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Traffic Class value.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>7</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: EthSwtSpi -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:c613caa3-1ff6-49c8-99cc-5883e9aeaef5">
											<SHORT-NAME>EthSwtSpi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configuration of one Ethernet Switch SPI access (if SPI is used).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SUB-CONTAINERS>
												<!-- Container Definition: EthSwtSpiSequence -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:b08a8fff-5509-479d-b072-f6441b4c9aaf">
													<SHORT-NAME>EthSwtSpiSequence</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container gives EthSwt driver information about one SPI sequence.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">One SPI sequence used by EthSwt driver is in exclusive use for it. No other driver is allowed to access this sequence. EthSwt driver may use one sequence to access n EthSwt hardware chips of the same type or n sequences are used to access one single EthSwt hardware chip.
                                                If a EthSwt hardware has no SPI interface, there is no instance of this container.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: EthSwtSpiAccessSynchronous -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:d73351bc-6c36-4ba9-8fc7-b907d1ee6787">
															<SHORT-NAME>EthSwtSpiAccessSynchronous</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter is used to define whether the access to the Spi sequence is synchronous or asynchronous.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">true: SPI access is synchronous. 
                                                        false: SPI access is asynchronous.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Symbolic Name Reference Definition: EthSwtSpiSequenceName -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:203cb639-e275-46ff-ac61-574e4e9d48cc">
															<SHORT-NAME>EthSwtSpiSequenceName</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to a Spi sequence configuration container.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Spi/SpiDriver/SpiSequence</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: EthSwtGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:3d96a192-1204-4873-9177-991e498850f6">
									<SHORT-NAME>EthSwtGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">General configuration of Ethernet Switch Driver module.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: EthSwtDevErrorDetect -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:df00580a-6987-4b2d-a90f-46da57fbc3a8">
											<SHORT-NAME>EthSwtDevErrorDetect</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables / Disables development error detection.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthSwtEnableVlanApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:bd96990d-3af9-4d53-b41e-b6d34720b860">
											<SHORT-NAME>EthSwtEnableVlanApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables / Disables EthSwt_EnableVLAN API.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthSwtGetArlTableApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:ddea1320-b490-4d2d-810b-1f04b314d7ff">
											<SHORT-NAME>EthSwtGetArlTableApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables / Disables EthSwt_GetArlTable API.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthSwtGetBufferLevelApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:90b58238-c1c1-4ce9-8d14-dc4fd778da98">
											<SHORT-NAME>EthSwtGetBufferLevelApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables / Disables API to fetch the switch buffer utilization.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthSwtGetDropCountApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:de712ddb-6adf-4518-9e40-b39f61b00f8d">
											<SHORT-NAME>EthSwtGetDropCountApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables / Disables EthSwt_GetDropCount API.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthSwtGetEtherStatsApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:b5dd0d07-c466-4867-8913-e86738cbd1c4">
											<SHORT-NAME>EthSwtGetEtherStatsApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables / Disables EthSwt_GetEtherStats API.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthSwtGetMacLearningModeApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:34b67e5b-3aff-48ba-87dc-4a6977cf12b0">
											<SHORT-NAME>EthSwtGetMacLearningModeApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables / Disables EthSwt_GetMacLearningMode API.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthSwtGetPortMacAddrApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:cc4070df-e3d1-48f8-81a8-d51c9a7b84e3">
											<SHORT-NAME>EthSwtGetPortMacAddrApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables / Disables EthSwt_GetPortMacAddr API.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthSwtGetSwitchRegApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:73bf4d9d-c9bb-42bb-b602-cc4923f629e6">
											<SHORT-NAME>EthSwtGetSwitchRegApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables / Disables EthSwt_GetSwitchReg API.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthSwtIndex -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:1cb88db2-2bb7-4324-bc58-84083b1e22ed">
											<SHORT-NAME>EthSwtIndex</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the InstanceId of this module instance. If only one instance is present it shall have the Id 0.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>255</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthSwtLinkDownUser -->
										<ECUC-FUNCTION-NAME-DEF UUID="ECUC:d84f11d1-8ece-4319-b463-9153fb20709c">
											<SHORT-NAME>EthSwtLinkDownUser</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Defines the &lt;User&gt; function name for the &lt;User&gt;_LinkDown callback.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
										<!-- PARAMETER DEFINITION: EthSwtLinkUpUser -->
										<ECUC-FUNCTION-NAME-DEF UUID="ECUC:6231011e-406a-4302-ad0e-635210f97a84">
											<SHORT-NAME>EthSwtLinkUpUser</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Defines the &lt;User&gt; function name for the &lt;User&gt;_LinkUp callback.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
										<!-- PARAMETER DEFINITION: EthSwtPersistentConfigurationResult -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:f8450fe6-4d29-4e15-bd03-cf5ae09526f8">
											<SHORT-NAME>EthSwtPersistentConfigurationResult</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables / Disables the callback API &lt;User&gt;_PersistentConfigurationResult.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthSwtPersistentConfigurationResultUser -->
										<ECUC-FUNCTION-NAME-DEF UUID="ECUC:04d72fa0-52c3-4588-84af-e897ba51d7ba">
											<SHORT-NAME>EthSwtPersistentConfigurationResultUser</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Defines the &lt;User&gt; function name for the &lt;User&gt;_PersistentConfigurationResult callback.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
										<!-- PARAMETER DEFINITION: EthSwtPublicCddHeaderFile -->
										<ECUC-STRING-PARAM-DEF UUID="ECUC:6e2b80fe-5da2-48cb-a5c6-df33a57a4438">
											<SHORT-NAME>EthSwtPublicCddHeaderFile</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Defines header files for callback functions which shall be included in case of CDDs.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-STRING-PARAM-DEF-VARIANTS>
												<ECUC-STRING-PARAM-DEF-CONDITIONAL>
													<MAX-LENGTH>32</MAX-LENGTH>
													<MIN-LENGTH>1</MIN-LENGTH>
												</ECUC-STRING-PARAM-DEF-CONDITIONAL>
											</ECUC-STRING-PARAM-DEF-VARIANTS>
										</ECUC-STRING-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthSwtReadTrcvRegisterApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:e1967e31-054e-4403-a7b9-b8c88de81c56">
											<SHORT-NAME>EthSwtReadTrcvRegisterApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables / Disables EthSwt_ReadTrcvRegister API.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthSwtResetConfigurationApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:16af361b-4414-4cf5-aa22-08737cdb74b4">
											<SHORT-NAME>EthSwtResetConfigurationApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables / Disables EthSwt_ResetConfiguration API.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthSwtSetMacLearningModeApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:f419814f-256a-4219-8b31-f12ec7f7372a">
											<SHORT-NAME>EthSwtSetMacLearningModeApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables / Disables EthSwt_SetMacLearningMode API.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthSwtSetSwitchRegApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:0425871a-a7ea-4b22-93ec-75e00a77a5bf">
											<SHORT-NAME>EthSwtSetSwitchRegApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables / Disables EthSwt_SetSwitchReg API.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthSwtStoreConfigurationApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:9012e251-d823-4ec7-ab36-04c58eef5efc">
											<SHORT-NAME>EthSwtStoreConfigurationApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables / Disables EthSwt_StoreConfiguration API.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthSwtVersionInfoApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:e884d0d0-8bb9-472b-ae56-eca768f1a331">
											<SHORT-NAME>EthSwtVersionInfoApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables / Disables version info API.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthSwtWriteTrcvRegisterApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:c1518a91-3da5-4941-a1ce-b7ca69fb86eb">
											<SHORT-NAME>EthSwtWriteTrcvRegisterApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables / Disables EthSwt_WriteTrcvRegister API.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
