<?xml version="1.0" encoding="utf-8"?>
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd" xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<AR-PACKAGES>
		<AR-PACKAGE>
			<SHORT-NAME>ComponentType</SHORT-NAME>
			<ELEMENTS>
				<APPLICATION-SW-COMPONENT-TYPE>
					<SHORT-NAME>CT_App</SHORT-NAME>
					<PORTS>
						<R-PORT-PROTOTYPE>
							<SHORT-NAME>Signal_Bool</SHORT-NAME>
							<REQUIRED-COM-SPECS>
								<NONQUEUED-RECEIVER-COM-SPEC>
									<DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterface/Signal_Bool/Signal_Bool</DATA-ELEMENT-REF>
									<ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
									<FILTER>
										<DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
									</FILTER>
									<INIT-VALUE>
										<CONSTANT-REFERENCE>
											<CONSTANT-REF DEST="CONSTANT-SPECIFICATION">/Constant/cInit</CONSTANT-REF>
										</CONSTANT-REFERENCE>
									</INIT-VALUE>
								</NONQUEUED-RECEIVER-COM-SPEC>
							</REQUIRED-COM-SPECS>
							<REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterface/Signal_Bool</REQUIRED-INTERFACE-TREF>
						</R-PORT-PROTOTYPE>
					</PORTS>
					<INTERNAL-BEHAVIORS>
						<SWC-INTERNAL-BEHAVIOR>
							<SHORT-NAME>CT_App_InternalBehavior</SHORT-NAME>
							<EVENTS>
								<DATA-RECEIVED-EVENT>
									<SHORT-NAME>DRT_Runnable_Signal_Bool_Signal_Bool</SHORT-NAME>
									<START-ON-EVENT-REF DEST="RUNNABLE-ENTITY">/ComponentType/CT_App/CT_App_InternalBehavior/Runnable</START-ON-EVENT-REF>
									<DATA-IREF>
										<CONTEXT-R-PORT-REF DEST="R-PORT-PROTOTYPE">/ComponentType/CT_App/Signal_Bool</CONTEXT-R-PORT-REF>
										<TARGET-DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterface/Signal_Bool/Signal_Bool</TARGET-DATA-ELEMENT-REF>
									</DATA-IREF>
								</DATA-RECEIVED-EVENT>
							</EVENTS>
							<PORT-API-OPTIONS>
								<PORT-API-OPTION>
									<ENABLE-TAKE-ADDRESS>false</ENABLE-TAKE-ADDRESS>
									<INDIRECT-API>false</INDIRECT-API>
									<PORT-REF DEST="R-PORT-PROTOTYPE">/ComponentType/CT_App/Signal_Bool</PORT-REF>
								</PORT-API-OPTION>
							</PORT-API-OPTIONS>
							<RUNNABLES>
								<RUNNABLE-ENTITY>
									<SHORT-NAME>Runnable</SHORT-NAME>
									<MINIMUM-START-INTERVAL>0.000000000</MINIMUM-START-INTERVAL>
									<CAN-BE-INVOKED-CONCURRENTLY>false</CAN-BE-INVOKED-CONCURRENTLY>
									<SYMBOL>Runnable</SYMBOL>
								</RUNNABLE-ENTITY>
							</RUNNABLES>
							<SUPPORTS-MULTIPLE-INSTANTIATION>false</SUPPORTS-MULTIPLE-INSTANTIATION>
						</SWC-INTERNAL-BEHAVIOR>
					</INTERNAL-BEHAVIORS>
				</APPLICATION-SW-COMPONENT-TYPE>
				<SWC-IMPLEMENTATION>
					<SHORT-NAME>CT_App_Implementation</SHORT-NAME>
					<CODE-DESCRIPTORS>
						<CODE>
							<SHORT-NAME>Code</SHORT-NAME>
							<ARTIFACT-DESCRIPTORS>
								<AUTOSAR-ENGINEERING-OBJECT>
									<CATEGORY>SWSRC</CATEGORY>
								</AUTOSAR-ENGINEERING-OBJECT>
							</ARTIFACT-DESCRIPTORS>
						</CODE>
					</CODE-DESCRIPTORS>
					<BEHAVIOR-REF DEST="SWC-INTERNAL-BEHAVIOR">/ComponentType/CT_App/CT_App_InternalBehavior</BEHAVIOR-REF>
				</SWC-IMPLEMENTATION>
			</ELEMENTS>
		</AR-PACKAGE>
		<AR-PACKAGE>
			<SHORT-NAME>Constant</SHORT-NAME>
			<ELEMENTS>
				<CONSTANT-SPECIFICATION>
					<SHORT-NAME>cInit</SHORT-NAME>
					<VALUE-SPEC>
						<TEXT-VALUE-SPECIFICATION>
							<VALUE>1</VALUE>
						</TEXT-VALUE-SPECIFICATION>
					</VALUE-SPEC>
				</CONSTANT-SPECIFICATION>
			</ELEMENTS>
		</AR-PACKAGE>
		<AR-PACKAGE>
			<SHORT-NAME>DataType</SHORT-NAME>
			<ELEMENTS>
				<IMPLEMENTATION-DATA-TYPE>
					<SHORT-NAME>Signal_Bool</SHORT-NAME>
					<CATEGORY>VALUE</CATEGORY>
					<SW-DATA-DEF-PROPS>
						<SW-DATA-DEF-PROPS-VARIANTS>
							<SW-DATA-DEF-PROPS-CONDITIONAL>
								<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AR4_TemporaryElements/BaseType/Signal_Bool</BASE-TYPE-REF>
								<DATA-CONSTR-REF DEST="DATA-CONSTR">/AR4_TemporaryElements/DataConstrain/Signal_Bool_DataConstrain</DATA-CONSTR-REF>
							</SW-DATA-DEF-PROPS-CONDITIONAL>
						</SW-DATA-DEF-PROPS-VARIANTS>
					</SW-DATA-DEF-PROPS>
				</IMPLEMENTATION-DATA-TYPE>
			</ELEMENTS>
		</AR-PACKAGE>
		<AR-PACKAGE>
			<SHORT-NAME>DaVinci</SHORT-NAME>
			<ELEMENTS/>
			<AR-PACKAGES>
				<AR-PACKAGE>
					<SHORT-NAME>ABC_ClusterCANCluster</SHORT-NAME>
					<ELEMENTS>
						<CAN-CLUSTER>
							<SHORT-NAME>CANCluster</SHORT-NAME>
							<CAN-CLUSTER-VARIANTS>
								<CAN-CLUSTER-CONDITIONAL>
									<PHYSICAL-CHANNELS>
										<CAN-PHYSICAL-CHANNEL>
											<SHORT-NAME>New_CAN_Network_1</SHORT-NAME>
											<COMM-CONNECTORS>
												<COMMUNICATION-CONNECTOR-REF-CONDITIONAL>
													<COMMUNICATION-CONNECTOR-REF DEST="CAN-COMMUNICATION-CONNECTOR">/DaVinci/ABC_ECU/e_Rx_simple/CANCluster_e_Rx_simple</COMMUNICATION-CONNECTOR-REF>
												</COMMUNICATION-CONNECTOR-REF-CONDITIONAL>
											</COMM-CONNECTORS>
											<FRAME-TRIGGERINGS>
												<CAN-FRAME-TRIGGERING>
													<SHORT-NAME>Msg_Bool_F</SHORT-NAME>
													<FRAME-PORT-REFS>
														<FRAME-PORT-REF DEST="FRAME-PORT">/DaVinci/ABC_ECU/e_Rx_simple/CANCluster_e_Rx_simple/FP_Msg_Bool_IN</FRAME-PORT-REF>
													</FRAME-PORT-REFS>
													<FRAME-REF DEST="CAN-FRAME">/DaVinci/ABC_ClusterCANCluster/ABC_Frame/Msg_Bool</FRAME-REF>
													<CAN-ADDRESSING-MODE>STANDARD</CAN-ADDRESSING-MODE>
													<IDENTIFIER>0</IDENTIFIER>
												</CAN-FRAME-TRIGGERING>
											</FRAME-TRIGGERINGS>
											<I-SIGNAL-TRIGGERINGS>
												<I-SIGNAL-TRIGGERING>
													<SHORT-NAME>Signal_Bool_S_Msg_Bool</SHORT-NAME>
													<I-SIGNAL-PORT-REFS>
														<I-SIGNAL-PORT-REF DEST="I-SIGNAL-PORT">/DaVinci/ABC_ECU/e_Rx_simple/CANCluster_e_Rx_simple/SP_Msg_Bool_Signal_Bool_IN</I-SIGNAL-PORT-REF>
													</I-SIGNAL-PORT-REFS>
													<I-SIGNAL-REF DEST="I-SIGNAL">/DaVinci/ABC_ClusterCANCluster/ABC_ISignal/Signal_Bool_Msg_Bool</I-SIGNAL-REF>
												</I-SIGNAL-TRIGGERING>
											</I-SIGNAL-TRIGGERINGS>
											<PDU-TRIGGERINGS>
												<PDU-TRIGGERING>
													<SHORT-NAME>Msg_Bool_P</SHORT-NAME>
													<I-PDU-PORT-REFS>
														<I-PDU-PORT-REF DEST="I-PDU-PORT">/DaVinci/ABC_ECU/e_Rx_simple/CANCluster_e_Rx_simple/PP_Msg_Bool_IN</I-PDU-PORT-REF>
													</I-PDU-PORT-REFS>
													<I-PDU-REF DEST="I-SIGNAL-I-PDU">/DaVinci/ABC_ClusterCANCluster/ABC_PDU/Msg_Bool</I-PDU-REF>
												</PDU-TRIGGERING>
											</PDU-TRIGGERINGS>
										</CAN-PHYSICAL-CHANNEL>
									</PHYSICAL-CHANNELS>
									<SPEED>0</SPEED>
								</CAN-CLUSTER-CONDITIONAL>
							</CAN-CLUSTER-VARIANTS>
						</CAN-CLUSTER>
					</ELEMENTS>
					<AR-PACKAGES>
						<AR-PACKAGE>
							<SHORT-NAME>ABC_Frame</SHORT-NAME>
							<ELEMENTS>
								<CAN-FRAME>
									<SHORT-NAME>Msg_Bool</SHORT-NAME>
									<FRAME-LENGTH>8</FRAME-LENGTH>
									<PDU-TO-FRAME-MAPPINGS>
										<PDU-TO-FRAME-MAPPING>
											<SHORT-NAME>Msg_Bool</SHORT-NAME>
											<PACKING-BYTE-ORDER>MOST-SIGNIFICANT-BYTE-LAST</PACKING-BYTE-ORDER>
											<PDU-REF DEST="I-SIGNAL-I-PDU">/DaVinci/ABC_ClusterCANCluster/ABC_PDU/Msg_Bool</PDU-REF>
											<START-POSITION>0</START-POSITION>
										</PDU-TO-FRAME-MAPPING>
									</PDU-TO-FRAME-MAPPINGS>
								</CAN-FRAME>
							</ELEMENTS>
						</AR-PACKAGE>
						<AR-PACKAGE>
							<SHORT-NAME>ABC_ISignal</SHORT-NAME>
							<ELEMENTS>
								<I-SIGNAL>
									<SHORT-NAME>Signal_Bool_Msg_Bool</SHORT-NAME>
									<LENGTH>1</LENGTH>
									<SYSTEM-SIGNAL-REF DEST="SYSTEM-SIGNAL">/Signal/Signal_Bool</SYSTEM-SIGNAL-REF>
								</I-SIGNAL>
							</ELEMENTS>
						</AR-PACKAGE>
						<AR-PACKAGE>
							<SHORT-NAME>ABC_PDU</SHORT-NAME>
							<ELEMENTS>
								<I-SIGNAL-I-PDU>
									<SHORT-NAME>Msg_Bool</SHORT-NAME>
									<LENGTH>64</LENGTH>
									<I-SIGNAL-TO-PDU-MAPPINGS>
										<I-SIGNAL-TO-I-PDU-MAPPING>
											<SHORT-NAME>Signal_Bool_S</SHORT-NAME>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV_SDG">
														<SD GID="DV_NetSigCT">100</SD>
														<SD GID="DV_NetSigCTA">100</SD>
														<SD GID="DV_NetSigInactiveVal">0</SD>
														<SD GID="DV_NetSigInitVal">0</SD>
														<SD GID="DV_NetSigSTI">0</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<I-SIGNAL-REF DEST="I-SIGNAL">/DaVinci/ABC_ClusterCANCluster/ABC_ISignal/Signal_Bool_Msg_Bool</I-SIGNAL-REF>
											<PACKING-BYTE-ORDER>MOST-SIGNIFICANT-BYTE-LAST</PACKING-BYTE-ORDER>
											<START-POSITION>0</START-POSITION>
										</I-SIGNAL-TO-I-PDU-MAPPING>
									</I-SIGNAL-TO-PDU-MAPPINGS>
								</I-SIGNAL-I-PDU>
							</ELEMENTS>
						</AR-PACKAGE>
					</AR-PACKAGES>
				</AR-PACKAGE>
				<AR-PACKAGE>
					<SHORT-NAME>ABC_ECU</SHORT-NAME>
					<ELEMENTS>
						<ECU-INSTANCE>
							<SHORT-NAME>e_Rx_simple</SHORT-NAME>
							<ASSOCIATED-PDUR-I-PDU-GROUP-REFS>
								<ASSOCIATED-PDUR-I-PDU-GROUP-REF DEST="PDUR-I-PDU-GROUP">/DaVinci/ABC_PDUGroup/e_Rx_simple_CANCluster_Rx</ASSOCIATED-PDUR-I-PDU-GROUP-REF>
								<ASSOCIATED-PDUR-I-PDU-GROUP-REF DEST="PDUR-I-PDU-GROUP">/DaVinci/ABC_PDUGroup/e_Rx_simple_CANCluster_Tx</ASSOCIATED-PDUR-I-PDU-GROUP-REF>
							</ASSOCIATED-PDUR-I-PDU-GROUP-REFS>
							<COMM-CONTROLLERS>
								<CAN-COMMUNICATION-CONTROLLER>
									<SHORT-NAME>CANCluster</SHORT-NAME>
								</CAN-COMMUNICATION-CONTROLLER>
							</COMM-CONTROLLERS>
							<CONNECTORS>
								<CAN-COMMUNICATION-CONNECTOR>
									<SHORT-NAME>CANCluster_e_Rx_simple</SHORT-NAME>
									<COMM-CONTROLLER-REF DEST="CAN-COMMUNICATION-CONTROLLER">/DaVinci/ABC_ECU/e_Rx_simple/CANCluster</COMM-CONTROLLER-REF>
									<ECU-COMM-PORT-INSTANCES>
										<FRAME-PORT>
											<SHORT-NAME>FP_Msg_Bool_IN</SHORT-NAME>
											<COMMUNICATION-DIRECTION>IN</COMMUNICATION-DIRECTION>
										</FRAME-PORT>
										<I-PDU-PORT>
											<SHORT-NAME>PP_Msg_Bool_IN</SHORT-NAME>
											<COMMUNICATION-DIRECTION>IN</COMMUNICATION-DIRECTION>
										</I-PDU-PORT>
										<I-SIGNAL-PORT>
											<SHORT-NAME>SP_Msg_Bool_Signal_Bool_IN</SHORT-NAME>
											<COMMUNICATION-DIRECTION>IN</COMMUNICATION-DIRECTION>
											<TIMEOUT>0</TIMEOUT>
										</I-SIGNAL-PORT>
									</ECU-COMM-PORT-INSTANCES>
								</CAN-COMMUNICATION-CONNECTOR>
							</CONNECTORS>
							<DIAGNOSTIC-ADDRESS>0</DIAGNOSTIC-ADDRESS>
							<SLEEP-MODE-SUPPORTED>false</SLEEP-MODE-SUPPORTED>
							<WAKE-UP-OVER-BUS-SUPPORTED>false</WAKE-UP-OVER-BUS-SUPPORTED>
						</ECU-INSTANCE>
					</ELEMENTS>
				</AR-PACKAGE>
				<AR-PACKAGE>
					<SHORT-NAME>ABC_PDUGroup</SHORT-NAME>
					<ELEMENTS>
						<PDUR-I-PDU-GROUP>
							<SHORT-NAME>e_Rx_simple_CANCluster_Rx</SHORT-NAME>
						</PDUR-I-PDU-GROUP>
						<PDUR-I-PDU-GROUP>
							<SHORT-NAME>e_Rx_simple_CANCluster_Tx</SHORT-NAME>
						</PDUR-I-PDU-GROUP>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
		<AR-PACKAGE>
			<SHORT-NAME>ECUComposition</SHORT-NAME>
			<ELEMENTS>
				<COMPOSITION-SW-COMPONENT-TYPE>
					<SHORT-NAME>EcuSwComposition</SHORT-NAME>
				</COMPOSITION-SW-COMPONENT-TYPE>
			</ELEMENTS>
		</AR-PACKAGE>
		<AR-PACKAGE>
			<SHORT-NAME>ECUCompositionType</SHORT-NAME>
			<ELEMENTS>
				<COMPOSITION-SW-COMPONENT-TYPE>
					<SHORT-NAME>EcuSwComposition</SHORT-NAME>
					<PORTS>
						<R-PORT-PROTOTYPE>
							<SHORT-NAME>Signal_Bool</SHORT-NAME>
							<REQUIRED-COM-SPECS>
								<NONQUEUED-RECEIVER-COM-SPEC>
									<DATA-ELEMENT-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterface/Signal_Bool/Signal_Bool</DATA-ELEMENT-REF>
									<ALIVE-TIMEOUT>0</ALIVE-TIMEOUT>
									<FILTER>
										<DATA-FILTER-TYPE>ALWAYS</DATA-FILTER-TYPE>
									</FILTER>
								</NONQUEUED-RECEIVER-COM-SPEC>
							</REQUIRED-COM-SPECS>
							<REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">/PortInterface/Signal_Bool</REQUIRED-INTERFACE-TREF>
						</R-PORT-PROTOTYPE>
					</PORTS>
					<COMPONENTS>
						<SW-COMPONENT-PROTOTYPE>
							<SHORT-NAME>CP_App</SHORT-NAME>
							<TYPE-TREF DEST="APPLICATION-SW-COMPONENT-TYPE">/ComponentType/CT_App</TYPE-TREF>
						</SW-COMPONENT-PROTOTYPE>
					</COMPONENTS>
					<CONNECTORS>
						<DELEGATION-SW-CONNECTOR>
							<SHORT-NAME>Signal_Bool_CP_App_Signal_Bool</SHORT-NAME>
							<INNER-PORT-IREF>
								<R-PORT-IN-COMPOSITION-INSTANCE-REF>
									<CONTEXT-COMPONENT-REF DEST="SW-COMPONENT-PROTOTYPE">/ECUCompositionType/EcuSwComposition/CP_App</CONTEXT-COMPONENT-REF>
									<TARGET-R-PORT-REF DEST="R-PORT-PROTOTYPE">/ComponentType/CT_App/Signal_Bool</TARGET-R-PORT-REF>
								</R-PORT-IN-COMPOSITION-INSTANCE-REF>
							</INNER-PORT-IREF>
							<OUTER-PORT-REF DEST="PORT-PROTOTYPE">/ECUCompositionType/EcuSwComposition/Signal_Bool</OUTER-PORT-REF>
						</DELEGATION-SW-CONNECTOR>
					</CONNECTORS>
				</COMPOSITION-SW-COMPONENT-TYPE>
			</ELEMENTS>
		</AR-PACKAGE>
		<AR-PACKAGE>
			<SHORT-NAME>PortInterface</SHORT-NAME>
			<ELEMENTS>
				<SENDER-RECEIVER-INTERFACE>
					<SHORT-NAME>Signal_Bool</SHORT-NAME>
					<IS-SERVICE>false</IS-SERVICE>
					<DATA-ELEMENTS>
						<VARIABLE-DATA-PROTOTYPE>
							<SHORT-NAME>Signal_Bool</SHORT-NAME>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
							<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/DataType/Signal_Bool</TYPE-TREF>
						</VARIABLE-DATA-PROTOTYPE>
					</DATA-ELEMENTS>
				</SENDER-RECEIVER-INTERFACE>
			</ELEMENTS>
		</AR-PACKAGE>
		<AR-PACKAGE>
			<SHORT-NAME>Signal</SHORT-NAME>
			<ELEMENTS>
				<SYSTEM-SIGNAL>
					<SHORT-NAME>Signal_Bool</SHORT-NAME>
				</SYSTEM-SIGNAL>
			</ELEMENTS>
		</AR-PACKAGE>
		<AR-PACKAGE>
			<SHORT-NAME>VehicleProject</SHORT-NAME>
			<ELEMENTS>
				<SYSTEM>
					<SHORT-NAME>e_Rx_simple</SHORT-NAME>
					<FIBEX-ELEMENTS>
						<FIBEX-ELEMENT-REF-CONDITIONAL>
							<FIBEX-ELEMENT-REF DEST="CAN-CLUSTER">/DaVinci/ABC_ClusterCANCluster/CANCluster</FIBEX-ELEMENT-REF>
						</FIBEX-ELEMENT-REF-CONDITIONAL>
						<FIBEX-ELEMENT-REF-CONDITIONAL>
							<FIBEX-ELEMENT-REF DEST="ECU-INSTANCE">/DaVinci/ABC_ECU/e_Rx_simple</FIBEX-ELEMENT-REF>
						</FIBEX-ELEMENT-REF-CONDITIONAL>
						<FIBEX-ELEMENT-REF-CONDITIONAL>
							<FIBEX-ELEMENT-REF DEST="CAN-FRAME">/DaVinci/ABC_ClusterCANCluster/ABC_Frame/Msg_Bool</FIBEX-ELEMENT-REF>
						</FIBEX-ELEMENT-REF-CONDITIONAL>
						<FIBEX-ELEMENT-REF-CONDITIONAL>
							<FIBEX-ELEMENT-REF DEST="I-SIGNAL">/DaVinci/ABC_ClusterCANCluster/ABC_ISignal/Signal_Bool_Msg_Bool</FIBEX-ELEMENT-REF>
						</FIBEX-ELEMENT-REF-CONDITIONAL>
						<FIBEX-ELEMENT-REF-CONDITIONAL>
							<FIBEX-ELEMENT-REF DEST="I-SIGNAL-I-PDU">/DaVinci/ABC_ClusterCANCluster/ABC_PDU/Msg_Bool</FIBEX-ELEMENT-REF>
						</FIBEX-ELEMENT-REF-CONDITIONAL>
					</FIBEX-ELEMENTS>
					<MAPPINGS>
						<SYSTEM-MAPPING>
							<SHORT-NAME>e_Rx_simple_MPPNG</SHORT-NAME>
							<DATA-MAPPINGS>
								<SENDER-RECEIVER-TO-SIGNAL-MAPPING>
									<DATA-ELEMENT-IREF>
										<CONTEXT-COMPONENT-REF DEST="SW-COMPONENT-PROTOTYPE">/ECUCompositionType/EcuSwComposition/CP_App</CONTEXT-COMPONENT-REF>
										<CONTEXT-COMPOSITION-REF DEST="ROOT-SW-COMPOSITION-PROTOTYPE">/VehicleProject/e_Rx_simple/EcuSwComposition</CONTEXT-COMPOSITION-REF>
										<CONTEXT-PORT-REF DEST="R-PORT-PROTOTYPE">/ComponentType/CT_App/Signal_Bool</CONTEXT-PORT-REF>
										<TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">/PortInterface/Signal_Bool/Signal_Bool</TARGET-DATA-PROTOTYPE-REF>
									</DATA-ELEMENT-IREF>
									<SYSTEM-SIGNAL-REF DEST="SYSTEM-SIGNAL">/Signal/Signal_Bool</SYSTEM-SIGNAL-REF>
								</SENDER-RECEIVER-TO-SIGNAL-MAPPING>
							</DATA-MAPPINGS>
						</SYSTEM-MAPPING>
					</MAPPINGS>
					<ROOT-SOFTWARE-COMPOSITIONS>
						<ROOT-SW-COMPOSITION-PROTOTYPE>
							<SHORT-NAME>EcuSwComposition</SHORT-NAME>
							<SOFTWARE-COMPOSITION-TREF DEST="COMPOSITION-SW-COMPONENT-TYPE">/ECUCompositionType/EcuSwComposition</SOFTWARE-COMPOSITION-TREF>
						</ROOT-SW-COMPOSITION-PROTOTYPE>
					</ROOT-SOFTWARE-COMPOSITIONS>
				</SYSTEM>
			</ELEMENTS>
		</AR-PACKAGE>
		<AR-PACKAGE>
			<SHORT-NAME>AR4_TemporaryElements</SHORT-NAME>
			<ELEMENTS/>
			<AR-PACKAGES>
				<AR-PACKAGE>
					<SHORT-NAME>DataConstrain</SHORT-NAME>
					<ELEMENTS>
						<DATA-CONSTR>
							<SHORT-NAME>Signal_Bool_DataConstrain</SHORT-NAME>
							<DATA-CONSTR-RULES>
								<DATA-CONSTR-RULE>
									<INTERNAL-CONSTRS>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
									</INTERNAL-CONSTRS>
								</DATA-CONSTR-RULE>
							</DATA-CONSTR-RULES>
						</DATA-CONSTR>
					</ELEMENTS>
					<AR-PACKAGES/>
				</AR-PACKAGE>
				<AR-PACKAGE>
					<SHORT-NAME>BaseType</SHORT-NAME>
					<ELEMENTS>
						<SW-BASE-TYPE>
							<SHORT-NAME>Signal_Bool</SHORT-NAME>
							<BASE-TYPE-ENCODING>NONE</BASE-TYPE-ENCODING>
							<NATIVE-DECLARATION>uint8</NATIVE-DECLARATION>
						</SW-BASE-TYPE>
					</ELEMENTS>
					<AR-PACKAGES/>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
