<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: Can -->
						<ECUC-MODULE-DEF UUID="ECUC:f7ca73a3-b05b-4f6a-a64d-9a6e8da3314e">
							<SHORT-NAME>Can</SHORT-NAME>
							<DESC>
								<L-2 L="EN">This container holds the configuration of a single CAN Driver.</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.2.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
										<DATE>2011-11-09</DATE>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-POST-BUILD</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: CanConfigSet -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e28c636a-f5f1-4503-b0ad-aed7fbed80d7">
									<SHORT-NAME>CanConfigSet</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This is the multiple configuration set container for CAN Driver</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>true</MULTIPLE-CONFIGURATION-CONTAINER>
									<SUB-CONTAINERS>
										<!-- Container Definition: CanController -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:a79e276f-cf20-496c-8587-edf4d9d2f01b">
											<SHORT-NAME>CanController</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the configuration parameters of the CAN controller(s).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CanBusoffProcessing -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:1ac4345e-22de-49c3-8bb0-053a8e121758">
													<SHORT-NAME>CanBusoffProcessing</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables / disables API Can_MainFunction_BusOff() for handling busoff events in polling mode.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:c7e3c917-84b8-dc84-9f56-c016f043b9ad">
															<SHORT-NAME>INTERRUPT</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:cb6d0b10-0a36-e2dc-bc29-ae297cee4673">
															<SHORT-NAME>POLLING</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CanControllerActivation -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:e12d871d-e196-46d8-a5a5-454f8475061f">
													<SHORT-NAME>CanControllerActivation</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Defines if a CAN controller is used in the configuration.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CanControllerBaseAddress -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:69afaa0c-2dc7-4390-a7c9-c605ae01c22f">
													<SHORT-NAME>CanControllerBaseAddress</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Specifies the CAN controller base address.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CanControllerId -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:efc9b48f-271a-4833-9ba0-aa53aa4cf278">
													<SHORT-NAME>CanControllerId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter provides the controller ID which is unique in a given CAN Driver. The value for this parameter starts with 0 and continue without any gaps.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CanRxProcessing -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:60718366-d647-4c8c-95ea-bc42b9536786">
													<SHORT-NAME>CanRxProcessing</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables / disables API Can_MainFunction_Read() for handling PDU reception events in polling mode.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:0d911820-3821-df4d-a991-771f1b8509db">
															<SHORT-NAME>INTERRUPT</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:111a5a18-bd9f-e5a5-c664-6531a82f96a1">
															<SHORT-NAME>POLLING</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CanTxProcessing -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:87199697-72c8-46ee-b7c7-276cc811d697">
													<SHORT-NAME>CanTxProcessing</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables / disables API Can_MainFunction_Write() for handling PDU transmission events in polling mode.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:34392b50-d4a2-d9af-cb6d-e2492a4378ec">
															<SHORT-NAME>INTERRUPT</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:37c26d49-5a20-e007-e840-d05bb6ee05b2">
															<SHORT-NAME>POLLING</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CanWakeupProcessing -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:826d181c-d6ad-4d64-93a2-bf529c8481e4">
													<SHORT-NAME>CanWakeupProcessing</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables / disables API Can_MainFunction_Wakeup() for handling wakeup events in polling mode.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:2f8cacd6-3887-e025-a749-7a2efeb62439">
															<SHORT-NAME>INTERRUPT</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:3315eece-be05-e67d-c41c-68418b60b0ff">
															<SHORT-NAME>POLLING</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CanWakeupSupport -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:48e0b1e7-b421-4388-ac6c-04b471380664">
													<SHORT-NAME>CanWakeupSupport</SHORT-NAME>
													<DESC>
														<L-2 L="EN">CAN driver support for wakeup over CAN Bus.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: CanControllerDefaultBaudrate -->
												<ECUC-REFERENCE-DEF UUID="ECUC:aef302e1-1821-4e87-8041-e542f92cb963">
													<SHORT-NAME>CanControllerDefaultBaudrate</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to baudrate configuration container configured for the Can Controller.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Can/CanConfigSet/CanController/CanControllerBaudrateConfig</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<!-- Reference Definition: CanCpuClockRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:2e20d271-b4b3-47cc-a0ee-a6e49b8373ec">
													<SHORT-NAME>CanCpuClockRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the CPU clock configuration, which is set in the MCU driver configuration</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<!-- Symbolic Name Reference Definition: CanWakeupSourceRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:61a02a48-1812-4052-8e57-b48bc38ce491">
													<SHORT-NAME>CanWakeupSourceRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter contains a reference to the Wakeup Source for this controller as defined in the ECU State Manager.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">Implementation Type: reference to EcuM_WakeupSourceType</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMWakeupSource</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
											<SUB-CONTAINERS>
												<!-- Container Definition: CanControllerBaudrateConfig -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:7380e54a-e891-4fb8-88e8-459dc7c59aaf">
													<SHORT-NAME>CanControllerBaudrateConfig</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container contains bit timing related configuration parameters of the CAN controller(s).</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CanControllerBaudRate -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:e5ae7b80-e6c2-4e56-8ccd-5dce07379977">
															<SHORT-NAME>CanControllerBaudRate</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Specifies the baudrate of the controller in kbps.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>2000</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanControllerPropSeg -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:669827f7-fac2-42e8-870b-8003e83a0cbc">
															<SHORT-NAME>CanControllerPropSeg</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Specifies propagation delay in time quantas.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanControllerSeg1 -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:2ff9d8a8-d32b-4a08-85d7-8cc51effa452">
															<SHORT-NAME>CanControllerSeg1</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Specifies phase segment 1 in time quantas.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanControllerSeg2 -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:755cccd2-d9a8-4df1-9cdb-6a9c5f850a12">
															<SHORT-NAME>CanControllerSeg2</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Specifies phase segment 2 in time quantas.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanControllerSyncJumpWidth -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:70cfe32e-1160-408d-be3c-9e8a789a5bb9">
															<SHORT-NAME>CanControllerSyncJumpWidth</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Specifies the synchronization jump width for the controller in time quantas.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: CanFilterMask -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:be9f285a-0eda-475e-9def-ef440e3c84c2">
													<SHORT-NAME>CanFilterMask</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container contains the configuration (parameters) of the CAN Filter Mask(s).</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CanFilterMaskValue -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:0b97bc36-d3d1-4d14-af96-e57e293fc3e1">
															<SHORT-NAME>CanFilterMaskValue</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Describes a mask for hardware-based filtering of CAN identifiers. The CAN identifiers of incoming messages are masked with the appropriate CanFilterMaskValue. Bits holding a 0 mean don&apos;t care, i.e. do not compare the message&apos;s identifier in the respective bit position.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">The mask shall be build by filling with leading 0. In case of CanIdType 
                                                        EXTENDED or MIXED a 29 bit mask shall be build. In case of CanIdType 
                                                        STANDARD a 11 bit mask shall be build</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: CanTTController -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:03522dfa-716f-4cae-b216-c32f5ff584ce">
													<SHORT-NAME>CanTTController</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container is only included and valid if TTCAN SWS is used and TTCAN is enabled.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This container contains the configuration parameters of the TTCAN controller(s) (which are needed in addition to the configuration parameters of the CAN controller(s)).

                                                CanTTController is only included, if the controller supports TTCAN.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CanTTControllerApplWatchdogLimit -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:58bdc478-e74c-42df-8d3d-0ba19ae24008">
															<SHORT-NAME>CanTTControllerApplWatchdogLimit</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines the maximum time period (unit is 256 times NTU) after which the application has to serve the watchdog.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTTControllerCycleCountMax -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:fafa672b-b0c5-4c09-9035-06e2461576cf">
															<SHORT-NAME>CanTTControllerCycleCountMax</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines the value for cycle_count_max.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">Allowed values:
                                                        0x00: 1 basic cycle
                                                        0x01: 2 basic cycles
                                                        0x03: 4 basic cycles
                                                        0x07: 8 basic cycles
                                                        0x0F: 16 basic cycles
                                                        0x1F: 32 basic cycles
                                                        0x3F: 64 basic cycles</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>63</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTTControllerExpectedTxTrigger -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:91b067db-e57d-432f-9093-262d567242ec">
															<SHORT-NAME>CanTTControllerExpectedTxTrigger</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Number of expected_tx_trigger.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTTControllerExternalClockSynchronisation -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:1f319887-2617-4190-b5d6-95b9e555b180">
															<SHORT-NAME>CanTTControllerExternalClockSynchronisation</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Enables/disables the external clock synchronization.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">TRUE:
                                                        External clock synchronization enabled.
                                                        FALSE:
                                                        External clock synchronization disabled.

                                                        This parameter shall only be configurable if parameter CanTTControllerLevel2 equals TRUE.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTTControllerGlobalTimeFiltering -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:ced20e88-c426-42a9-bce0-ed1a2a5f3797">
															<SHORT-NAME>CanTTControllerGlobalTimeFiltering</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Enables/disables the global time filtering.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">TRUE:
                                                        Global time filtering enabled.
                                                        FALSE:
                                                        Global time filtering disabled.

                                                        This parameter shall only be configurable if parameter CanTTControllerLevel2 equals TRUE.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTTControllerInitialRefOffset -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:25750102-31df-4bed-91cf-01253dc78faf">
															<SHORT-NAME>CanTTControllerInitialRefOffset</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines the initial value for ref trigger offset.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>127</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTTControllerInterruptEnable -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:6d752b5f-31aa-4e11-915c-cbe1e1a0f01a">
															<SHORT-NAME>CanTTControllerInterruptEnable</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Enables/disables the respective interrupts.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">Bit Position set to 1: Enable respective interrupt.
                                                        Bit Position set to 0: Disable respective interrupt.

                                                        Bit Position / Interrupt Source: 
                                                        10: Application Watchdog. 
                                                        9: Watch Trigger reached. 
                                                        8: Initialization Watch Trigger reached. 
                                                        7: Change of Error Level. 
                                                        6: Tx Overflow. 
                                                        5: Tx Underflow. 
                                                        4: Global Time Error. 
                                                        3: Gap. 
                                                        2: Start of Cycle. 
                                                        1: Time Discontinuity. 
                                                        0: Master State Change. 

                                                        Bit position &quot;1: Time Discontinuity&quot; and &quot;4: Global Time Error&quot; shall only be configurable if parameter CanTTControllerLevel2 equals TRUE.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>1023</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTTControllerLevel2 -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:*************-42da-8b89-dad7c7ddeb2c">
															<SHORT-NAME>CanTTControllerLevel2</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines whether Level 2 or Level 1 is used.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">TRUE: Level 2.
                                                        FALSE: Level 1.

                                                        If this parameter is set to FALSE then all parameters with dependency to CanTTControllerLevel2 need not be configured.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTTControllerNTUConfig -->
														<ECUC-FLOAT-PARAM-DEF UUID="ECUC:fc8b7d0e-677b-4c04-b547-ef6602271c33">
															<SHORT-NAME>CanTTControllerNTUConfig</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines the config value for NTU (network time unit).</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">Value given in microseconds. The value configured shall be greater than 0.
                                                        Together with the local oscillator period, the TUR (time unit ratio) can be derived from the NTU. This parameter shall only be configurable if parameter CanTTControllerLevel2 equals TRUE.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>100</MAX>
															<MIN>0</MIN>
														</ECUC-FLOAT-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTTControllerOperationMode -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:fd04f084-a5cc-4c27-9d75-7e1a46e86bfa">
															<SHORT-NAME>CanTTControllerOperationMode</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines the operation mode.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:3fd64a8a-7d9a-9c65-715c-caf19978e54c">
																	<SHORT-NAME>CAN_TT_EVENT_SYNC_TIME_TRIGGERED</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:3fef5410-240b-9c34-51d9-eae0a402d47f">
																	<SHORT-NAME>CAN_TT_EVENT_TRIGGERED</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:2f4bd692-b267-90cd-41cb-11f197c247c6">
																	<SHORT-NAME>CAN_TT_TIME_TRIGGERED</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTTControllerSyncDeviation -->
														<ECUC-FLOAT-PARAM-DEF UUID="ECUC:26f778c8-15de-4c4a-b05e-fff49e20b233">
															<SHORT-NAME>CanTTControllerSyncDeviation</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines the maximum synchronization deviation:</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">Given as a percentage value of the NTU (network time unit). The value configured shall be greater than 0.
                                                        This parameter shall only be configurable if parameter CanTTControllerLevel2 equals TRUE.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>100</MAX>
															<MIN>0</MIN>
														</ECUC-FLOAT-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTTControllerTURRestore -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:e5b66746-4e6e-47f5-b62b-ab6452a49082">
															<SHORT-NAME>CanTTControllerTURRestore</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Enables/disables the TUR restore.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">Note that the value configured for TUR can be derived from the value configured for NTU and the local oscillator preriod.
                                                        TRUE:
                                                        TUR restore enabled.  
                                                        FALSE:
                                                        TUR restore disabled. 

                                                        This parameter shall only be configurable if parameter CanTTControllerLevel2 equals TRUE.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTTControllerTimeMaster -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:68c24e85-bb75-4b38-b9af-05947b2f0f27">
															<SHORT-NAME>CanTTControllerTimeMaster</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines whether the controller acts as a potential time master.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">TRUE: Potential time master. 
                                                        FALSE: Time slave.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTTControllerTimeMasterPriority -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:64837877-30b7-48a4-9f87-a7e4e959010f">
															<SHORT-NAME>CanTTControllerTimeMasterPriority</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines the time master priority.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>7</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTTControllerTxEnableWindowLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:432b3c85-542c-48e3-ab51-3944e1d6e421">
															<SHORT-NAME>CanTTControllerTxEnableWindowLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Length of the tx enable window given in CAN bit times.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">Definition parameter &quot;CanTTControllerTxEnableWindowlength&quot; is used such that:
                                                        Length of enable window = CanTTControllerTxEnableWindowLength + 1</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>16</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTTControllerWatchTriggerGapTimeMark -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:a880dad8-6591-4dbc-a3d4-8057ff9b6931">
															<SHORT-NAME>CanTTControllerWatchTriggerGapTimeMark</SHORT-NAME>
															<DESC>
																<L-2 L="EN">watch trigger time mark after a gap</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTTControllerWatchTriggerTimeMark -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:496f1799-9365-4e5b-878a-dd8cab58f338">
															<SHORT-NAME>CanTTControllerWatchTriggerTimeMark</SHORT-NAME>
															<DESC>
																<L-2 L="EN">watch trigger time mark</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTTIRQProcessing -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:089059db-bfe6-43a7-9fa7-99b4b583a2bf">
															<SHORT-NAME>CanTTIRQProcessing</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Enables / disables API Can_MainFunction_BusOff() for handling busoff events in polling mode.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:7bc27e44-c968-9123-5842-4a8300d5dc4a">
																	<SHORT-NAME>INTERRUPT</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:c242e16b-c373-998e-333f-0e295c508c28">
																	<SHORT-NAME>POLLING</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: CanHardwareObject -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:ee3d6577-0017-42fc-b4d6-8d27c2e66563">
											<SHORT-NAME>CanHardwareObject</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the configuration (parameters) of CAN Hardware Objects.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CanHandleType -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:6c58c09a-9aa2-4e4d-8ae3-8eba40a77aaf">
													<SHORT-NAME>CanHandleType</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Specifies the type (Full-CAN or Basic-CAN) of a hardware object.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:50af5435-1308-8ebd-3bcc-ba3b63b3ca28">
															<SHORT-NAME>BASIC</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:307cbc95-796f-8689-5e97-22a1120b1381">
															<SHORT-NAME>FULL</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CanIdType -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:f8442a9b-7752-416c-ac68-8cd3080589c0">
													<SHORT-NAME>CanIdType</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Specifies whether the IdValue is of type</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">- standard identifier
                                                  - extended identifier
                                                  - mixed mode

                                                ImplementationType: Can_IdType</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:b0d87c30-8808-863d-7394-ca932296bf20">
															<SHORT-NAME>EXTENDED</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:78dcd470-c4dc-8a3d-5a2d-3c4c9d1b6b41">
															<SHORT-NAME>MIXED</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:8c7ad8bb-450e-8b44-4a05-57c77c6eafd5">
															<SHORT-NAME>STANDARD</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CanIdValue -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:615fef00-eb96-4bbb-afd7-cef66a67db46">
													<SHORT-NAME>CanIdValue</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Specifies (together with the filter mask) the identifiers range that passes the hardware filter.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CanObjectId -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:5049e195-870c-490c-ac6d-745aae833d04">
													<SHORT-NAME>CanObjectId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Holds the handle ID of HRH or HTH. The value of this parameter is unique in a given CAN Driver, and it should start with 0 and continue without any gaps.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">The HRH and HTH Ids are defined under two different name-spaces. 

                                                Example: HRH0-0, HRH1-1, HTH0-2, HTH1-3</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CanObjectType -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:b8abf5c9-4e6a-46bf-a1ae-63b191e72d04">
													<SHORT-NAME>CanObjectType</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Specifies if the HardwareObject is used as Transmit or as Receive object</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:df6672f4-**************-95c9fec6b750">
															<SHORT-NAME>RECEIVE</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:693a8ec4-7de5-85d1-4188-999f27e876cc">
															<SHORT-NAME>TRANSMIT</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: CanControllerRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:e2b98fcd-3e29-4041-a480-aed31e706d97">
													<SHORT-NAME>CanControllerRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to CAN Controller to which the HOH is associated to.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Can/CanConfigSet/CanController</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<!-- Reference Definition: CanFilterMaskRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:d0683493-3c90-46c3-815f-5a3dabb20a7a">
													<SHORT-NAME>CanFilterMaskRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the filter mask that is used for hardware filtering together with the CAN_ID_VALUE.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">Different CanHardwareObjects with different CanIdTypes (STANDARD, MIXED,  EXTENDED)
                                                can share the same CanFilterMask (i.e., the CanFilterMaskRef parameters of  these
                                                CanHardwareObjects reference the very same CanFilterMask container). This  shall be allowed and must be
                                                supported by the configuration generators.

                                                The CanFilterMaskRef is omitted for
                                                1) CanHardwareObjects with CanObjectType set to TRANSMIT
                                                2) CanHardwareObjects with CanObjectType set to RECEIVE if only a single Can ID
                                                   shall be received via this CanHardwareObjects (i.e., exact match with CanIdValue)</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Can/CanConfigSet/CanController/CanFilterMask</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<!-- Reference Definition: CanMainFunctionRWPeriodRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:185477b7-2192-42b1-9dad-c72942a94b7c">
													<SHORT-NAME>CanMainFunctionRWPeriodRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to CAN Controller to which the HOH is associated to.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Can/CanGeneral/CanMainFunctionRWPeriods</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
											<SUB-CONTAINERS>
												<!-- Container Definition: CanTTHardwareObjectTrigger -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:5c10353d-3ba8-4140-adf6-b5c02d28c9f0">
													<SHORT-NAME>CanTTHardwareObjectTrigger</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container is only included and valid if TTCAN SWS is used and TTCAN is enabled.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This container contains the configuration (parameters) of TTCAN triggers for Hardware Objects, which are additional to the configuration (parameters) of CAN Hardware Objects.

                                                CanTTHardwareObjectTrigger is only included, if the controller supports TTCAN.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CanTTHardwareObjectBaseCycle -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:bbb7fda0-3f86-4144-b471-33793652290e">
															<SHORT-NAME>CanTTHardwareObjectBaseCycle</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines the cycle_offset.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">CanTTHardwareObjectBaseCycle must be not greater than cycle_count_max.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>63</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTTHardwareObjectCycleRepetition -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:6f58e444-b305-47d4-899b-38971bc77aa6">
															<SHORT-NAME>CanTTHardwareObjectCycleRepetition</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines the repeat_factor.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">CanTTHardwareObjectCycleRepetition shall be a power of two (2), greater than cycle_offset but not greater than cycle_count_max + 1.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>64</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTTHardwareObjectTimeMark -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:737166e8-39c4-4d60-ac3f-00672a8ebf66">
															<SHORT-NAME>CanTTHardwareObjectTimeMark</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines the point in time, when the trigger will be activated.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">Value is given in cycle time.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTTHardwareObjectTriggerId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:0da05573-613f-45fd-94f7-68688e7bcf18">
															<SHORT-NAME>CanTTHardwareObjectTriggerId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Sequential number which allows separation of different TTCAN triggers configured for one and the same hardware object.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>63</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTTHardwareObjectTriggerType -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:b15710a4-a4ef-45a8-830a-33d47a8b386c">
															<SHORT-NAME>CanTTHardwareObjectTriggerType</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines the type of the trigger associated with the hardware object. This parameter depends on plain CAN parameter CAN_OBJECT_TYPE.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">If CAN_OBJECT_TYPE equals RECEIVE than this parameter is fixed to  CAN_TT_RX_TRIGGER.
                                                        If CAN_OBJECT_TYPE equals TRANSMIT than one of the following literals is configurable:
                                                        CAN_TT_TX_REF_TRIGGER,
                                                        CAN_TT_TX_REF_TRIGGER_GAP,
                                                        CAN_TT_TX_TRIGGER_MERGED,
                                                        CAN_TT_TX_TRIGGER_SINGLE,
                                                        CAN_TT_TX_TRIGGER_EXCLUSIVE.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:68bf5289-34ee-89d3-5d65-19d42c07e68a">
																	<SHORT-NAME>CAN_TT_RX_TRIGGER</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:dd5be928-40aa-90b7-51c4-1a505ca77627">
																	<SHORT-NAME>CAN_TT_TX_REF_TRIGGER</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:653170d7-c6ef-8b76-6b84-e446fec6810a">
																	<SHORT-NAME>CAN_TT_TX_REF_TRIGGER_GAP</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:67f9446c-4224-8ee6-4d65-4c58f35031eb">
																	<SHORT-NAME>CAN_TT_TX_TRIGGER_EXCLUSIVE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:5c22b2be-4eff-8fdc-3546-615ee8b62e5c">
																	<SHORT-NAME>CAN_TT_TX_TRIGGER_MERGED</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:314b236a-f035-85d0-5aca-52042b178e82">
																	<SHORT-NAME>CAN_TT_TX_TRIGGER_SINGLE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CanGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:856dd730-a715-4e74-89f9-cd03e465c206">
									<SHORT-NAME>CanGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container contains the parameters related each CAN Driver Unit.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CanChangeBaudrateApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:68aba675-**************-4911b27827d4">
											<SHORT-NAME>CanChangeBaudrateApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The support of the Can_ChangeBaudrate API is optional.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">If this parameter is set to true the Can_ChangeBaudrate API shall be supported. Otherwise the API is not supported.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CanDevErrorDetection -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:00beaaf9-8ba3-448a-aa81-a27661c11c8b">
											<SHORT-NAME>CanDevErrorDetection</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Switches the Development Error Detection and Notification ON or OFF.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CanHardwareCancellation -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:ddb2d203-fc56-4784-98de-edbdb00d90ed">
											<SHORT-NAME>CanHardwareCancellation</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies if hardware cancellation shall be supported.ON or OFF</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CanIdenticalIdCancellation -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:33156f66-0274-4236-b05b-8cca84e9844f">
											<SHORT-NAME>CanIdenticalIdCancellation</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables/disables cancellation of pending PDUs with identical ID.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CanIndex -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:18b93eb8-b1dc-4779-9372-277b674dd618">
											<SHORT-NAME>CanIndex</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the InstanceId of this module instance. If only one instance is present it shall have the Id 0.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>255</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CanLPduReceiveCalloutFunction -->
										<ECUC-FUNCTION-NAME-DEF UUID="ECUC:0bf48002-3ae8-405d-8e51-63cbb66a3f2a">
											<SHORT-NAME>CanLPduReceiveCalloutFunction</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the existence and the name of a callout function that is called after a successful</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">reception of a received CAN Rx L-PDU. If this parameter is omitted no callout shall take place.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
										<!-- PARAMETER DEFINITION: CanMainFunctionBusoffPeriod -->
										<ECUC-FLOAT-PARAM-DEF UUID="ECUC:12306b16-f8b2-418a-9262-e10b8222f967">
											<SHORT-NAME>CanMainFunctionBusoffPeriod</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter describes the period for cyclic call to Can_MainFunction_Busoff. Unit is seconds.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65.535</MAX>
											<MIN>0.001</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CanMainFunctionModePeriod -->
										<ECUC-FLOAT-PARAM-DEF UUID="ECUC:a4a82737-e923-4dd2-a0c3-8e56b5c0ce7c">
											<SHORT-NAME>CanMainFunctionModePeriod</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter describes the period for cyclic call to Can_MainFunction_Mode. Unit is seconds.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65.535</MAX>
											<MIN>0.001</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CanMainFunctionWakeupPeriod -->
										<ECUC-FLOAT-PARAM-DEF UUID="ECUC:c25c3355-86db-4423-a9cb-3f081c873488">
											<SHORT-NAME>CanMainFunctionWakeupPeriod</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter describes the period for cyclic call to Can_MainFunction_Wakeup. Unit is seconds.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65.535</MAX>
											<MIN>0.001</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CanMultiplexedTransmission -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:2eb95d32-92e6-407d-bae9-abac34896d07">
											<SHORT-NAME>CanMultiplexedTransmission</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies if multiplexed transmission shall be supported.ON or OFF</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CanTimeoutDuration -->
										<ECUC-FLOAT-PARAM-DEF UUID="ECUC:b395c86f-3f7f-42d0-8b8a-94cdc17d2246">
											<SHORT-NAME>CanTimeoutDuration</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the maximum time for blocking function until a timeout is detected. Unit is seconds.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65.535</MAX>
											<MIN>0.001</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CanVersionInfoApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:52d00b4f-174c-453b-8884-271b69795d80">
											<SHORT-NAME>CanVersionInfoApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Switches the Can_GetVersionInfo() API ON or OFF.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Reference Definition: CanCounterRef -->
										<ECUC-REFERENCE-DEF UUID="ECUC:0f0c8933-6ce9-4a53-90cc-31b61ca90e25">
											<SHORT-NAME>CanCounterRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter contains a reference to the counter, which is used by the CAN driver.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsCounter</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<!-- Reference Definition: CanSupportTTCANRef -->
										<ECUC-REFERENCE-DEF UUID="ECUC:3fc9831b-0db6-41a2-8700-1b0dbcf79da4">
											<SHORT-NAME>CanSupportTTCANRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The parameter refers to CanIfSupportTTCAN parameter in the CAN Interface Module configuration.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The CanIfSupportTTCAN parameter defines whether TTCAN is supported.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/CanIf/CanIfPrivateCfg</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<!-- Container Definition: CanMainFunctionRWPeriods -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e19259ca-56e6-476f-8828-152be61457d0">
											<SHORT-NAME>CanMainFunctionRWPeriods</SHORT-NAME>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CanMainFunctionReadPeriod -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:e168d193-1b8a-4de0-a93b-0993b8972e22">
													<SHORT-NAME>CanMainFunctionReadPeriod</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter describes the period for cyclic call to Can_MainFunction_Read. Unit is seconds. Different poll-cycles will be configurable if more than one CanMainFunctionReadPeriod is configured. In this case multiple Can_MainFunction_Read() will be provided by the CAN Driver module.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>65.535</MAX>
													<MIN>0.001</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CanMainFunctionWritePeriod -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:3144d1f4-d927-4535-88fe-584a942a90c4">
													<SHORT-NAME>CanMainFunctionWritePeriod</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter describes the period for cyclic call to Can_MainFunction_Write. Unit is seconds. Different poll-cycles will be configurable if more than one CanMainFunctionWritePeriod is configured. In this case multiple Can_MainFunction_Write() will be provided by the CAN Driver module.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>65.535</MAX>
													<MIN>0.001</MIN>
												</ECUC-FLOAT-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
