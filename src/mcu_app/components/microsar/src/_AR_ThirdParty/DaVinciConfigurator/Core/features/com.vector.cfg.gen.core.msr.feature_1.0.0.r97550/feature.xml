<?xml version="1.0" encoding="UTF-8"?>
<feature
      id="com.vector.cfg.gen.core.msr.feature"
      label="DaVinci Cfg Generator Core"
      version="1.0.0.r97550"
      provider-name="Vector Informatik GmbH">

   <description>
      DaVinci configurator generator core.
   </description>

   <copyright>
      (c) Copyright Vector Informatik GmbH. All Rights Reserved.
   </copyright>

   <license url="http://www.vector.com">
      Vector Group License Agreement

This software is licensed under the terms and conditions of „Delivery and maintenance of software products” of Vector Group, see www.vector.com
   </license>

   <includes
         id="com.vector.cfg.gen.core.feature"
         version="1.0.0.r97550"/>

   <plugin
         id="com.vector.cfg.gen.core.bswmdmigration"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.gen.core.bswmdmigration.groovy"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.gen.core.bswmdmodel.groovy"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.gen.core.contributions"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.gen.core.contributions.dom.base"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.gen.vtt.published"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.gen.core.genclassloading.msr"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.gen.core.gencore.msr"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

</feature>
