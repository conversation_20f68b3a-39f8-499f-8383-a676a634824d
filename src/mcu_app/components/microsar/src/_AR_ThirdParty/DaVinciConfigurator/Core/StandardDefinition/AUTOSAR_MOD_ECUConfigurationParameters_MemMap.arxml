<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: MemMap -->
						<ECUC-MODULE-DEF UUID="ECUC:aa31b7a8-b524-4c87-be84-43fca3822c12">
							<SHORT-NAME>MemMap</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the MemMap module.</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.2.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
										<DATE>2011-11-09</DATE>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: MemMapAddressingModeSet -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:b9a29699-350e-4955-b78e-47f569142ad4">
									<SHORT-NAME>MemMapAddressingModeSet</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Defines a set of addressing modes which might apply to a SwAddrMethod.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: MemMapSupportedAddressingMethodOption -->
										<ECUC-STRING-PARAM-DEF UUID="ECUC:6d1ca67a-194a-4573-9f95-2fb64da2384c">
											<SHORT-NAME>MemMapSupportedAddressingMethodOption</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This constrains the usage of this addressing mode set for Generic Mappings to swAddrMethods.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The attribute option of a swAddrMethod mapped via MemMapGenericMapping to this MemMapAddressingModeSet shall be equal to one of the configured MemMapSupportedAddressMethodOption&apos;s</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-STRING-PARAM-DEF-VARIANTS>
												<ECUC-STRING-PARAM-DEF-CONDITIONAL>
													<REGULAR-EXPRESSION>[a-zA-Z]([a-zA-Z0-9]|_[a-zA-Z0-9])*_?</REGULAR-EXPRESSION>
												</ECUC-STRING-PARAM-DEF-CONDITIONAL>
											</ECUC-STRING-PARAM-DEF-VARIANTS>
										</ECUC-STRING-PARAM-DEF>
										<!-- PARAMETER DEFINITION: MemMapSupportedMemoryAllocationKeywordPolicy -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:f71b072d-046d-478f-9ccb-e3e9316d5d3c">
											<SHORT-NAME>MemMapSupportedMemoryAllocationKeywordPolicy</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This constrains the usage of this addressing mode set for Generic Mappings to swAddrMethods.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The attribute MemoryAllocationKeywordPolicy of a swAddrMethod mapped via MemMapGenericMapping to this MemMapAddressingModeSet shall be equal to one of the configured MemMapSupportedMemoryAllocationKeywordPolicy&apos;s</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:3d236827-e879-8fd7-6b32-9dc13ae2b831">
													<SHORT-NAME>MEMMAP_ALLOCATION_KEYWORD_POLICY_ADDR_METHOD_SHORT_NAME</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:4ee09fa8-3526-96fe-40be-d82461c03036">
													<SHORT-NAME>MEMMAP_ALLOCATION_KEYWORD_POLICY_ADDR_METHOD_SHORT_NAME_AND_ALIGNMENT</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: MemMapSupportedSectionInitializationPolicy -->
										<ECUC-STRING-PARAM-DEF UUID="ECUC:e907ccf6-062e-43b1-babf-99bb412f2a9e">
											<SHORT-NAME>MemMapSupportedSectionInitializationPolicy</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This constrains the usage of this addressing mode set for Generic Mappings to swAddrMethods.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The sectionIntializationPolicy attribute value of a swAddrMethod mapped via MemMapGenericMapping to this MemMapAddressingModeSet shall be equal to one of the configured MemMapSupportedSectionIntializationPolicy&apos;s 

                                        Please note that SectionInitializationPolicyType describes the intended initialization of MemorySections. 

                                        The following values are standardized in AUTOSAR Methodology:

                                        * &apos;&apos;&apos;NO-INIT&apos;&apos;&apos;: No initialization and no clearing is performed. Such data elements must not be read before one has written a value into it.
                                        * &apos;&apos;&apos;INIT&apos;&apos;&apos;: To be used for data that are initialized by every reset to the specified value (initValue). 
                                        * &apos;&apos;&apos;POWER-ON-INIT&apos;&apos;&apos;: To be used for data that are initialized by &quot;Power On&quot; to the specified value (initValue). Note: there might be several resets between power on resets. 
                                        * &apos;&apos;&apos;CLEARED&apos;&apos;&apos;: To be used for data that are initialized by every reset to zero. 
                                        * &apos;&apos;&apos;POWER-ON-CLEARED&apos;&apos;&apos;: To be used for data that are initialized by &quot;Power On&quot; to zero. Note: there might be several resets between power on resets.

                                        Please note that the values are defined similar to the representation of enumeration types in the XML schema to ensure backward compatibility.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-STRING-PARAM-DEF-VARIANTS>
												<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
											</ECUC-STRING-PARAM-DEF-VARIANTS>
										</ECUC-STRING-PARAM-DEF>
										<!-- PARAMETER DEFINITION: MemMapSupportedSectionType -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:3c77ff1c-dee1-49ea-a4d9-33f3f3f2805a">
											<SHORT-NAME>MemMapSupportedSectionType</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This constrains the usage of this addressing mode set for Generic Mappings to swAddrMethods.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The attribute sectionType of a swAddrMethod mapped via MemMapGenericMapping or MemMapSectionSpecificMapping to this MemMapAddressingModeSet shall be equal to one of the configured MemMapSupportedSectionType&apos;s.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:bd466957-be22-967d-72cc-31c181fa7109">
													<SHORT-NAME>MEMMAP_SECTION_TYPE_CALIBRATION_OFFLINE</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:762544a8-97e4-90b0-73bf-6dd7bbb6005e">
													<SHORT-NAME>MEMMAP_SECTION_TYPE_CALIBRATION_ONLINE</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:7ee6e927-17f6-94e1-5617-aeff85863c6f">
													<SHORT-NAME>MEMMAP_SECTION_TYPE_CAL_PRM</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:2bd973d8-c9d3-8d77-7653-7e7ae351d202">
													<SHORT-NAME>MEMMAP_SECTION_TYPE_CODE</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:b3338e06-f25d-9559-717f-fb0b6b42e210">
													<SHORT-NAME>MEMMAP_SECTION_TYPE_CONFIG_DATA</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:d3ea7541-37a2-8e64-5bfa-62daba4446d5">
													<SHORT-NAME>MEMMAP_SECTION_TYPE_CONST</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:1613750d-f431-9618-60d5-e358074c9d76">
													<SHORT-NAME>MEMMAP_SECTION_TYPE_EXCLUDE_FROM_FLASH</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:b3f373d1-3794-9063-49f1-90d4160922cf">
													<SHORT-NAME>MEMMAP_SECTION_TYPE_USER_DEFINED</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:6147d9aa-7192-9431-52a1-3ef06deb2188">
													<SHORT-NAME>MEMMAP_SECTION_TYPE_VAR</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:27883fef-206f-92a3-4c11-f089be5cd3ac">
													<SHORT-NAME>MEMMAP_SECTION_TYPE_VAR_FAST</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:8e58c3cb-891f-98a3-5ace-27345622addf">
													<SHORT-NAME>MEMMAP_SECTION_TYPE_VAR_NO_INIT</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:0f3265de-8ed0-98d5-7024-ecbde1b8cb11">
													<SHORT-NAME>MEMMAP_SECTION_TYPE_VAR_POWER_ON_INIT</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: MemMapAddressingMode -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:80bfe9ef-b7fc-4b51-8dd1-dab1ae3fbeed">
											<SHORT-NAME>MemMapAddressingMode</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Defines a addressing mode with a set of #pragma statements implementing the start and the stop of a section.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: MemMapAddressingModeStart -->
												<ECUC-MULTILINE-STRING-PARAM-DEF UUID="ECUC:9cd47499-3661-462d-81fb-beb77b4eda67">
													<SHORT-NAME>MemMapAddressingModeStart</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Defines a set of #pragma statements implementing the start of a section.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-MULTILINE-STRING-PARAM-DEF-VARIANTS>
														<ECUC-MULTILINE-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-MULTILINE-STRING-PARAM-DEF-VARIANTS>
												</ECUC-MULTILINE-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: MemMapAddressingModeStop -->
												<ECUC-MULTILINE-STRING-PARAM-DEF UUID="ECUC:0976b2a0-8e41-4457-9636-eace58400036">
													<SHORT-NAME>MemMapAddressingModeStop</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Defines a set of #pragma statements implementing the start of a section.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-MULTILINE-STRING-PARAM-DEF-VARIANTS>
														<ECUC-MULTILINE-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-MULTILINE-STRING-PARAM-DEF-VARIANTS>
												</ECUC-MULTILINE-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: MemMapAlignmentSelector -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:cc847254-326c-445f-9df8-7548068d871c">
													<SHORT-NAME>MemMapAlignmentSelector</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Defines a the alignments for which the MemMapAddressingMode applies.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">The to be used alignment is defined in the alignment attribute of the MemorySection. If the MemMapAlignmentSelector fits to alignment attribute of the MemorySection the set of #pragmas of the related MemMapAddressingMode shall be used to implement the start and the stop of a section.

                                                Please note that the same MemMapAddressingMode can be applicable for several alignments, e.g. &quot;8&quot; bit and &quot;UNSPECIFIED&quot;.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL>
															<REGULAR-EXPRESSION>[1-9][0-9]*|0x[0-9a-f]*|0[0-7]*|0b[0-1]*|UNSPECIFIED|UNKNOWN|BOOLEAN|</REGULAR-EXPRESSION>
														</ECUC-STRING-PARAM-DEF-CONDITIONAL>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: MemMapAllocation -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:db0cbfa5-d86b-407d-acfa-64a436b1f94d">
									<SHORT-NAME>MemMapAllocation</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Defines a set of addressing modes which might apply to a SwAddrMethod.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<SUB-CONTAINERS>
										<!-- Container Definition: MemMapGenericMapping -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:ab992de3-97d5-42b4-8493-2f5be2bc7a30">
											<SHORT-NAME>MemMapGenericMapping</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Defines which SwAddrMethod is implemented with which MemMapAddressingModeSet.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The pragmas for the implementation of the MemorySelectorKeywords are taken from the MemMapAddressingModeStart and MemMapAddressingModeStop parameters of the MemMapAddressingModeSet for the individual alignments.

                                        That this mapping becomes valid requires matching MemMapSupportedSectionType&apos;s, MemMapSupportedSectionInitializationPolicy&apos;s and MemMapSupportedAddressingMethodOption&apos;s.

                                        The MemMapGenericMapping applies only if it is not overruled by an MemMapSectionSpecificMapping</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<REFERENCES>
												<!-- Foreign Reference Definition: MemMapSwAddressMethodRef -->
												<ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:ce703d4e-b826-4ecf-a296-9f53d2a913eb">
													<SHORT-NAME>MemMapSwAddressMethodRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the SwAddrMethod which applies to the MemMapGenericMapping.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-TYPE>SW-ADDR-METHOD</DESTINATION-TYPE>
												</ECUC-FOREIGN-REFERENCE-DEF>
												<!-- Reference Definition: MemMapAddressingModeSetRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:9b2ed32c-df97-4ae7-8430-3b3adf225003">
													<SHORT-NAME>MemMapAddressingModeSetRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the MemMapAddressingModeSet which applies to the MemMapGenericMapping.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/MemMap/MemMapAddressingModeSet</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: MemMapSectionSpecificMapping -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:b1dc433b-fdcc-4d9e-8c1b-2b185aeeab21">
											<SHORT-NAME>MemMapSectionSpecificMapping</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Defines which MemorySection of a BSW Module or a Software Component is implemented with which MemMapAddressingModeSet.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The pragmas for the implementation of the MemorySelectorKeywords are taken from the MemMapAddressingModeStart and MemMapAddressingModeStop parameters of the MemMapAddressingModeSet for the specific alignment of the MemorySection.

                                        The MemMapSectionSpecificMapping precedes a mapping defined by MemMapGenericMapping.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<REFERENCES>
												<!-- Foreign Reference Definition: MemMapMemorySectionRef -->
												<ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:028cc5c2-4681-4e77-8873-03c4af2327b9">
													<SHORT-NAME>MemMapMemorySectionRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the MemorySection which applies to the MemMapSectionSpecificMapping.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-TYPE>MEMORY-SECTION</DESTINATION-TYPE>
												</ECUC-FOREIGN-REFERENCE-DEF>
												<!-- Reference Definition: MemMapAddressingModeSetRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:557c3f43-7697-4644-a538-eca0f577ef55">
													<SHORT-NAME>MemMapAddressingModeSetRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the MemMapAddressingModeSet which applies to the MemMapModuleSectionSpecificMapping.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/MemMap/MemMapAddressingModeSet</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
