<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: Fee -->
						<ECUC-MODULE-DEF UUID="ECUC:25e46613-7e63-4604-a81c-33867a9d91f7">
							<SHORT-NAME>Fee</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the Fee (Flash EEPROM Emulation) module.</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.2.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
										<DATE>2011-11-09</DATE>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: FeeBlockConfiguration -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:3e3f6a41-1274-4d00-b94d-504e69110cfa">
									<SHORT-NAME>FeeBlockConfiguration</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Configuration of block specific parameters for the Flash EEPROM Emulation module.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: FeeBlockNumber -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:0a407883-2fc6-4823-a26e-ea079aa7e73f">
											<SHORT-NAME>FeeBlockNumber</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Block identifier (handle).</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">0x0000 and 0xFFFF shall not be used for block numbers (see FEE006).

                                        Range: 
                                        min = 2^NVM_DATASET_SELECTION_BITS 
                                        max = 0xFFFF -2^NVM_DATASET_SELECTION_BITS

                                        Note: Depending on the number of bits set aside for dataset selection several other block numbers shall also be left out to ease implementation.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
											<MAX>65534</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: FeeBlockSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:24bf18c3-**************-6c7937444d1d">
											<SHORT-NAME>FeeBlockSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Size of a logical block in bytes.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: FeeImmediateData -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:b1116e04-53cd-44a7-8056-f43434aa82c8">
											<SHORT-NAME>FeeImmediateData</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Marker for high priority data.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: 	Block contains immediate data.
                                        false:	Block does not contain immediate data.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: FeeNumberOfWriteCycles -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:b55e41d9-f6b5-4613-937e-2b83fe37a654">
											<SHORT-NAME>FeeNumberOfWriteCycles</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Number of write cycles required for this block.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Symbolic Name Reference Definition: FeeDeviceIndex -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:f3bfa469-e131-4f58-bb89-ca7fc4548d25">
											<SHORT-NAME>FeeDeviceIndex</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Device index (handle).</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Range: 0 .. 254 (0xFF reserved for broadcast call to GetStatus function).</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Fls/FlsGeneral</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: FeeGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:40da9e07-9e9a-4777-85e7-a111b37d8453">
									<SHORT-NAME>FeeGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for general parameters. These parameters are not specific to a block.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: FeeDevErrorDetect -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:f262f806-4d29-4ef6-b4c1-6167282e1f21">
											<SHORT-NAME>FeeDevErrorDetect</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Pre-processor switch to enable and disable development error detection.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true:	Development error detection enabled.
                                        false:	Development error detection disabled.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: FeeIndex -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:0de3db3d-02d0-4bbd-a4df-8e82cfa0ec48">
											<SHORT-NAME>FeeIndex</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the InstanceId of this module instance. If only one instance is present it shall have the Id 0.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>254</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: FeeNvmJobEndNotification -->
										<ECUC-FUNCTION-NAME-DEF UUID="ECUC:3b4f73c8-354a-4d0e-86e6-0e2d07ba5590">
											<SHORT-NAME>FeeNvmJobEndNotification</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Mapped to the job end notification routine provided by the upper layer module (NvM_JobEndNotification).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
										<!-- PARAMETER DEFINITION: FeeNvmJobErrorNotification -->
										<ECUC-FUNCTION-NAME-DEF UUID="ECUC:9a658f03-834b-4dd3-add7-58b968076e7d">
											<SHORT-NAME>FeeNvmJobErrorNotification</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Mapped to the job error notification routine provided by the upper layer module (NvM_JobErrorNotification).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
										<!-- PARAMETER DEFINITION: FeePollingMode -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:cbbd2e13-d937-4e0f-8546-6ea5cc3b7eeb">
											<SHORT-NAME>FeePollingMode</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Pre-processor switch to enable and disable the polling mode for this module.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true:	Polling mode enabled, callback functions (provided to FLS module) disabled.

                                        false:	Polling mode disabled, callback functions (provided to FLS module) enabled.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: FeeSetModeSupported -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:f064e794-edd8-4288-b518-84c07839383d">
											<SHORT-NAME>FeeSetModeSupported</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Compiler switch to enable/disable the &apos;SetMode&apos; functionality of the FEE module.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">TRUE: SetMode functionality supported / code present, 
                                        FALSE: SetMode functionality not supported / code not present. 

                                        Note: 
                                        This configuration setting has to be consistent with that of all 
                                        underlying flash device drivers (configuration parameter FlsSetModeApi).</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: FeeVersionInfoApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:e66783a5-0bb8-4060-83ed-985d387af3a5">
											<SHORT-NAME>FeeVersionInfoApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Pre-processor switch to enable / disable the API to read out the modules version information.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true:	Version info API enabled.
                                        false:	Version info API disabled.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: FeeVirtualPageSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:cfc06841-4637-4082-9f2e-57de66655c6f">
											<SHORT-NAME>FeeVirtualPageSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The size in bytes to which logical blocks shall be aligned.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: FeePublishedInformation -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:c99d74b3-e634-4c09-b512-87583d22c249">
									<SHORT-NAME>FeePublishedInformation</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Additional published parameters not covered by CommonPublishedInformation container.</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">Note that these parameters do not have any configuration class setting, since they are published information.</L-1>
										</P>
									</INTRODUCTION>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: FeeBlockOverhead -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:05f4fdfa-6b05-4960-973e-f62746494b3d">
											<SHORT-NAME>FeeBlockOverhead</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Management overhead per logical block in bytes.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Note: If the management overhead depends on the block size or block location a formula has to be provided that allows the configurator to calculate the management overhead correctly.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: FeeMaximumBlockingTime -->
										<ECUC-FLOAT-PARAM-DEF UUID="ECUC:4c8210b6-42e3-416f-a8cc-f2c6b95f3131">
											<SHORT-NAME>FeeMaximumBlockingTime</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum time the FEE module&apos;s API routines shall be blocked (delayed) by internal operations.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Note: Internal operations in that case means operations that are not explicitly invoked from the upper layer module but need to be handled for proper operation of this module or the underlying memory driver.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>Inf</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: FeePageOverhead -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:ff042c2b-4ea3-4cc9-b210-6b4956da10d9">
											<SHORT-NAME>FeePageOverhead</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Management overhead per page in bytes.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Note: If the management overhead depends on the block size or block location a formula has to be provided that allows the configurator to calculate the management overhead correctly.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
