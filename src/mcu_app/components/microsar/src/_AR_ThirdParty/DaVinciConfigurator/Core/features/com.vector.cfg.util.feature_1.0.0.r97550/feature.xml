<?xml version="1.0" encoding="UTF-8"?>
<feature
      id="com.vector.cfg.util.feature"
      label="Utilities for DaVinci Products"
      version="1.0.0.r97550"
      provider-name="Vector Informatik GmbH">

   <description>
      Utilities for DaVinci Products
   </description>

   <copyright>
      (c) Copyright Vector Informatik GmbH. All Rights Reserved.
   </copyright>

   <license url="http://www.vector.com">
      Vector Group License Agreement

This software is licensed under the terms and conditions of „Delivery and maintenance of software products” of Vector Group, see www.vector.com
   </license>

   <plugin
         id="com.vector.annotations.publishedapi"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.annotations.obfuscation"
         download-size="0"
         install-size="0"
         version="1.0.1"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.util"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.util.text"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.util.log4j2.plugin"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.util.scripting.dynamic"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.util.base"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.util.log"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.util.servicecontext"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.davinci.util"
         download-size="0"
         install-size="0"
         version="1.0.3"
         unpack="false"/>

   <plugin
         id="com.vector.davinci.util.collections"
         download-size="0"
         install-size="0"
         version="1.0.1"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.util.services"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.util.classloading"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.core.app"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.core"
         download-size="0"
         install-size="0"
         version="5.28.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.project.loader"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.util.activity"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.util.args"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.util.jna"
         download-size="0"
         install-size="0"
         version="4.5.1.97550"/>

   <plugin
         id="com.vector.cfg.util.platform"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.util.feature.dev"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.util.platform.linux.gtk"
         os="linux"
         ws="gtk"
         download-size="0"
         install-size="0"
         version="0.0.0"
         fragment="true"
         unpack="false"/>
         
   <plugin
         id="com.vector.cfg.util.platform.win32.win32"
         os="win32"
         ws="win32"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         fragment="true"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.util.win32.win32.x86_64"
         os="win32"
         ws="win32"
         arch="x86_64"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         fragment="true"/>

   <plugin
         id="com.vector.cfg.util.services.if"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

</feature>
