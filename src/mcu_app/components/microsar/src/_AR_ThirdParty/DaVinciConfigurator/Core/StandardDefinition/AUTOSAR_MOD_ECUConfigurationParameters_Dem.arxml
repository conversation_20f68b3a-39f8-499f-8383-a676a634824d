<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2017 rel. 3 sp1 (x64) (http://www.altova.com) by  (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: Dem -->
						<ECUC-MODULE-DEF UUID="ECUC:a72ca44c-5093-4cd1-b6f1-e197d31fdd75">
							<SHORT-NAME>Dem</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the Dem (Diagnostic Event Manager) module.</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.4.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
										<DATE>2013-10-31</DATE>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-POST-BUILD</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: DemConfigSet -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e85440ff-d0a6-4001-bcfc-aa36b340c94a">
									<SHORT-NAME>DemConfigSet</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container contains the configuration parameters and sub containers of the Dem module supporting multiple configuration sets. This container is a MultipleConfigurationContainer, i.e. this container and its sub-containers exist once per configuration set.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>true</MULTIPLE-CONFIGURATION-CONTAINER>
									<SUB-CONTAINERS>
										<!-- Container Definition: DemDTCClass -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:10f37378-2b66-4089-aec4-382cb9316830">
											<SHORT-NAME>DemDTCClass</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the configuration (parameters) for DTCClass.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>65535</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DemDTCFunctionalUnit -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:467cf2b5-284e-4cd6-9004-831fb0599388">
													<SHORT-NAME>DemDTCFunctionalUnit</SHORT-NAME>
													<DESC>
														<L-2 L="EN">DTCFuncitonalUnit is a 1-byte value which identifies the corresponding basic vehicle / system function which reports the DTC. This parameter is necessary for the report of severity information.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">If this parameter is configured for no DTC, the Dem provides no DTC functional unit information.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemDTCSeverity -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:ec5f19c2-2f59-4a05-a5a7-21fc4e8b49e3">
													<SHORT-NAME>DemDTCSeverity</SHORT-NAME>
													<DESC>
														<L-2 L="EN">DTC severity according to ISO 14229-1. This parameter depends on the automotive manufacturer.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">If it is not configured, the value is counted as &apos;no severity&apos;. 
                                                If this parameter is configured for no DTC, the Dem provides no DTC severity information.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>DEM_DTC_SEV_NO_SEVERITY</DEFAULT-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:93216985-f644-8761-6d87-fa7b1c03d385">
															<SHORT-NAME>DEM_DTC_SEV_CHECK_AT_NEXT_HALT</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:5dbb8ec8-3f61-8a04-4848-dbf0cba5ee51">
															<SHORT-NAME>DEM_DTC_SEV_IMMEDIATELY</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:ec3e0ecb-7831-87a8-48c1-55a53c5d592a">
															<SHORT-NAME>DEM_DTC_SEV_MAINTENANCE_ONLY</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:f81916b0-bcc4-8fec-4154-f817276e12cf">
															<SHORT-NAME>DEM_DTC_SEV_NO_SEVERITY</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemImmediateNvStorage -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:09b420be-542a-4655-beaa-bc967dfcd3e8">
													<SHORT-NAME>DemImmediateNvStorage</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Switch to enable immediate storage triggering of an according event memory entriy persistently to NVRAM.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">true: immediate non-volatile storage triggering enabled 
                                                false: immediate non-volatile storage triggering disabled</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemJ1939DTC -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:d62b52a5-97bd-4f04-a64d-e88dda34eb15">
													<SHORT-NAME>DemJ1939DTC</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Unique Diagnostic Trouble Code value for J1939 (consiting of SPN and FMI)</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>16777214</MAX>
													<MIN>1</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemObdDTC -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:e979a29e-4aef-405e-aee7-2dc1c41b6c04">
													<SHORT-NAME>DemObdDTC</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Unique Diagnostic Trouble Code value for OBD</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>65535</MAX>
													<MIN>1</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemUdsDTC -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:c1fc7bad-0617-4667-8f03-ef4094b29060">
													<SHORT-NAME>DemUdsDTC</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Unique Diagnostic Trouble Code value for UDS</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">(Range: 0x000000 and 0xFFFFFF are reserved for DTC groups by ISO 14229-1)</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>16777214</MAX>
													<MIN>1</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: DemJ1939DTC_NodeAddressRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:399fa3b9-2fc9-44ab-a7b2-25b1a2c7f40b">
													<SHORT-NAME>DemJ1939DTC_NodeAddressRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to a J1939 NodeAddress</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemJ1939NodeAddress</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
											<SUB-CONTAINERS>
												<!-- Container Definition: DemCallbackInitMForF -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:d73df317-2911-4d4c-8d81-466df02d7e55">
													<SHORT-NAME>DemCallbackInitMForF</SHORT-NAME>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">In case the container has a DemCallbackInitMForFFnc, this parameter defines the name of the function that the Dem will call. 

                                                In case there is no DemCallbackInitMForFFnc, the Dem will have an R-Port requiring the interface CallbackInitMonitorForFunction, whose name is generated by using the unique callback-prefix followed by the event name.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: DemCallbackInitMForFFnc -->
														<ECUC-FUNCTION-NAME-DEF UUID="ECUC:2415de0a-e9e7-45e0-b2fd-c955bc96b0ea">
															<SHORT-NAME>DemCallbackInitMForFFnc</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Function name of prototype &quot;InitMonitorForFunction&quot;.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<ECUC-FUNCTION-NAME-DEF-VARIANTS>
																<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
															</ECUC-FUNCTION-NAME-DEF-VARIANTS>
														</ECUC-FUNCTION-NAME-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemDtr -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:048c7ac5-d3b3-4277-91af-bbdb75a2bd3b">
											<SHORT-NAME>DemDtr</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container holds the configuration of one individual DTR.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DemDtrCompuDenominator0 -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:5c85b81d-2f61-4fbd-b25f-e128ee47865a">
													<SHORT-NAME>DemDtrCompuDenominator0</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Part of the conversion between the binary representation and the physical meaning analogous to the SW-C Template conversion CompuRationalCoeffs with 2 numerator coefficients and 1 denominator coefficient in the direction compuInternalToPhys.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This is the only one supported denominator value, a constant divisor.

                                                The value 0 is not allowed.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemDtrCompuNumerator0 -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:2a37b812-08e6-4de4-8ebb-4268f02b47a3">
													<SHORT-NAME>DemDtrCompuNumerator0</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Part of the conversion between the binary representation and the physical meaning analogous to the SW-C Template conversion CompuRationalCoeffs with 2 numerator coefficients and 1 denominator coefficient in the direction compuInternalToPhys.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This is the first numerator value, which is multiplied with x^0, i.e., the offset.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemDtrCompuNumerator1 -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:ac631547-516f-4d48-8a71-1fe3c0d49638">
													<SHORT-NAME>DemDtrCompuNumerator1</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Part of the conversion between the binary representation and the physical meaning analogous to the SW-C Template conversion CompuRationalCoeffs with 2 numerator coefficients and 1 denominator coefficient in the direction compuInternalToPhys.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This is the second numerator value, which is multiplied with x^1, i.e., the factor.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemDtrId -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:9c14e03c-5be7-4392-b637-ad7293e74d5f">
													<SHORT-NAME>DemDtrId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">The index identifier value assigned to this DTR. The value is generated during the Dem configuration process.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemDtrMid -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:2cb52e21-b358-4631-82d4-ec342e69d3b8">
													<SHORT-NAME>DemDtrMid</SHORT-NAME>
													<DESC>
														<L-2 L="EN">The OBDMID of the DTR.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">The values 0x00, 0x20, 0x40, 0x60, 0x80, 0xA0, 0xC0, 0xE0 are reserved.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemDtrTid -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:4da5ac1a-81b6-454e-9da7-222ef76ea52d">
													<SHORT-NAME>DemDtrTid</SHORT-NAME>
													<DESC>
														<L-2 L="EN">The OBDTID of the DTR.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemDtrUasid -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:b29dfc82-e531-4ccd-a648-97a890536902">
													<SHORT-NAME>DemDtrUasid</SHORT-NAME>
													<DESC>
														<L-2 L="EN">The UaSId the DTR data shall be scaled to, and reported together with the rescaled DTR data.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemDtrUpdateKind -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:f58baecb-daa8-449b-b8b7-ddbe112c024d">
													<SHORT-NAME>DemDtrUpdateKind</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Update conditions applied by the Dem to reports of DTR values. Only supported if a related Event is configured. If no related Event is configured, the Dem behaves as if DemDtrUpdateKind is configured to &quot;DEM_DTR_UPDATE_ALWAYS&quot;.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:fa5c8b8f-c386-8ad8-1df8-ddc89b353203">
															<SHORT-NAME>DEM_DTR_UPDATE_ALWAYS</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:7633447d-aaea-8783-4259-3ef3ba070e7a">
															<SHORT-NAME>DEM_DTR_UPDATE_STEADY</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: DemDtrEventRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:b5eb778a-d83b-4f33-9432-a212ca1caa0c">
													<SHORT-NAME>DemDtrEventRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the DemEventParameter this DTR is related to. If the related event is not configured, the Dem cannot ensure consistency between the DTR and the event.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemEventParameter -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:17edd206-934c-420b-a50a-75aef6269ac7">
											<SHORT-NAME>DemEventParameter</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the configuration (parameters) for events.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Note that this container definition does not explicitly define a symbolic name parameter. Instead, the short name of the container will be used in the Ecu Configuration Description to specify the symbolic name of the diagnostic event.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>65535</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DemEventAvailable -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:6b0b03bd-1d0c-47c8-b723-0bca376ea53a">
													<SHORT-NAME>DemEventAvailable</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter configures an Event as unavailable.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">It is treated by Dem as if it does not exist.
                                                true =  Event is available
                                                false = Event is not available</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemEventId -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:a42e4864-6ec3-4459-ae25-2a314d4a0e42">
													<SHORT-NAME>DemEventId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Unique identifier of a diagnostic event.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This parameter should not be changeable by user, because the Id should be generated by Dem itself to prevent gaps and multiple use of an Id. The events should be sequentially ordered beginning with 1 and no gaps in between.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<MAX>65535</MAX>
													<MIN>1</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemEventKind -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:44e8a61c-a95b-487d-908b-0ca87be434d4">
													<SHORT-NAME>DemEventKind</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter is used to distinguish between SW-C and BSW events. SW-C events are reported by Dem_SetEventStatus API and BSW events are reported by Dem_ReportErrorStatus API.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:ef61fc42-79d3-8cfd-3747-f7e994eea520">
															<SHORT-NAME>DEM_EVENT_KIND_BSW</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:1a38d2f8-aa83-843f-5295-de7cb009fee9">
															<SHORT-NAME>DEM_EVENT_KIND_SWC</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemMaxNumberFreezeFrameRecords -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:0c06d2a8-5789-4416-8c13-f331b2f5d081">
													<SHORT-NAME>DemMaxNumberFreezeFrameRecords</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines the number of according freeze frame records, which can maximal be stored for this event. Therefore all these freeze frame records have the same freeze frame class.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This parameter is only required for calculated record numeration (refer to DemTypeOfFreezeFrameRecordNumeration).</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: DemDTCClassRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:89db0687-72d2-47f9-9e6a-6bcf25fde654">
													<SHORT-NAME>DemDTCClassRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines the DTC configuration associated with the diagnostic event.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">It is allowed to have events without a DTC (e.g. for ECU-internal events triggering safety reactions without being reported via diagnostic communication). The same DemDTCClass can be used from several events, to combine these (refer to chapter &quot;Combination of diagnostic event&quot;).</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemDTCClass</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<!-- Reference Definition: DemExtendedDataClassRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:aad40479-8bd6-451a-86f6-d42e15b8bd26">
													<SHORT-NAME>DemExtendedDataClassRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This reference defines the link to an extended data class sampler.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemExtendedDataClass</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<!-- Reference Definition: DemFreezeFrameClassRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:b980679e-9569-4f3b-8558-841069092eb2">
													<SHORT-NAME>DemFreezeFrameClassRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">These references define the links to a freeze frame class sampler.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemFreezeFrameClass</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<!-- Reference Definition: DemFreezeFrameRecNumClassRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:37ada416-5e4f-4e2d-82f8-f8f010feb2f4">
													<SHORT-NAME>DemFreezeFrameRecNumClassRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines the list of dedicated freeze frame record numbers associated with the diagnostic event. These record numbers are assigned to the freeze frame records (instead of calculated record numbers).</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This parameter is only required for configured record numeration (refer to DemTypeOfFreezeFrameRecordNumeration).</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemFreezeFrameRecNumClass</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<!-- Reference Definition: DemJ1939ExpandedFreezeFrameClassRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:f9add2f1-9447-4a3b-a021-8152d0c37857">
													<SHORT-NAME>DemJ1939ExpandedFreezeFrameClassRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">These references define the links to a J1939 freeze frame class sampler.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemGeneralJ1939/DemJ1939FreezeFrameClass</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<!-- Reference Definition: DemJ1939FreezeFrameClassRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:66c9848c-d60b-4263-80dd-d247536f8d13">
													<SHORT-NAME>DemJ1939FreezeFrameClassRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">These references define the links to a J1939 freeze frame class sampler.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemGeneralJ1939/DemJ1939FreezeFrameClass</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<!-- Reference Definition: DemOBDGroupingAssociativeEventsRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:e6a5765e-f9e2-43d8-9b3e-23b46ec06e7a">
													<SHORT-NAME>DemOBDGroupingAssociativeEventsRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines a reference which points to a representative event of one group of associate events.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">The &quot;reverence event&quot; must refer to it self.
                                                Note: One event is only allowed to be reverenced to only one group of associate events.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
											<SUB-CONTAINERS>
												<!-- Container Definition: DemCallbackClearEventAllowed -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:81830ba7-c118-43dd-baf9-d89bcec71b90">
													<SHORT-NAME>DemCallbackClearEventAllowed</SHORT-NAME>
													<DESC>
														<L-2 L="EN">The presence of this container indicates that the Dem has access to a &quot;ClearEventAllowed&quot; callback.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">In case there is a DemCallbackClearEventAllowedFnc, this parameter defines the name of the function that the Dem will call. 

                                                In case there is no DemCallbackClearEventAllowedFnc, the Dem will have an R-Port requiring the interface CallbackClearEventAllowed whose name is generated by using the unique callback-prefix followed by the event name.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: DemCallbackClearEventAllowedFnc -->
														<ECUC-FUNCTION-NAME-DEF UUID="ECUC:f57a2f55-06b0-438d-9a41-d2626089d446">
															<SHORT-NAME>DemCallbackClearEventAllowedFnc</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Function name of prototype &quot;ClearEventAllowed&quot;.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<ECUC-FUNCTION-NAME-DEF-VARIANTS>
																<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
															</ECUC-FUNCTION-NAME-DEF-VARIANTS>
														</ECUC-FUNCTION-NAME-DEF>
														<!-- PARAMETER DEFINITION: DemClearEventAllowedBehavior -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:7411934a-7525-44dd-b02b-e0681e08b4c2">
															<SHORT-NAME>DemClearEventAllowedBehavior</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines the resulting UDS status byte for the related event, which must not be cleared according to the ClearEventAllowed callback.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>DEM_NO_STATUS_BYTE_CHANGE</DEFAULT-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:fcbbe3da-65c8-93a4-4d21-04949539cced">
																	<SHORT-NAME>DEM_NO_STATUS_BYTE_CHANGE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:c86d9fde-c4ac-8940-7643-d40958c6ef49">
																	<SHORT-NAME>DEM_ONLY_THIS_CYCLE_AND_READINESS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: DemCallbackEventDataChanged -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:48673ef9-0ee2-44e2-a4da-c56610dd6792">
													<SHORT-NAME>DemCallbackEventDataChanged</SHORT-NAME>
													<DESC>
														<L-2 L="EN">The presence of this container indicates that the Dem has access to an &quot;EventDataChanged&quot; callback.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">In case there is a DemCallbackEventDataChangedFnc, this parameter defines the name of the function that the Dem will call. 

                                                In case there is no DemCallbackEventDataChangedFnc, the Dem will have an R-Port requiring the interface CallbackEventDataChanged whose name is generated by using the unique callback-prefix followed by the event name.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: DemCallbackEventDataChangedFnc -->
														<ECUC-FUNCTION-NAME-DEF UUID="ECUC:620931a3-319d-4e30-8896-df7694afa222">
															<SHORT-NAME>DemCallbackEventDataChangedFnc</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Function name of prototype &quot;EventDataChanged&quot;</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<ECUC-FUNCTION-NAME-DEF-VARIANTS>
																<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
															</ECUC-FUNCTION-NAME-DEF-VARIANTS>
														</ECUC-FUNCTION-NAME-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: DemCallbackEventStatusChanged -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:505d125c-04db-4d5e-92f4-d73e664f7174">
													<SHORT-NAME>DemCallbackEventStatusChanged</SHORT-NAME>
													<DESC>
														<L-2 L="EN">The presence of this container indicates, that the Dem has access to an &quot;EventStatusChanged&quot; callback, which the Dem will call to notify other components about the change in the status of an event.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">In case there is a DemCallbackEvenStatusChangedFnc, this parameter defines the name of the function that the Dem will call. 

                                                In case there is no DemCallbackEvenStatusChangedFnc, the Dem will have an R-Port requiring the interface CallbackEventStatusChanged, whose name is generated by using the unique callback-prefix followed by the event name.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: DemCallbackEventStatusChangedFnc -->
														<ECUC-FUNCTION-NAME-DEF UUID="ECUC:0a1de324-a6d1-4b23-ba12-631c41dbd8cc">
															<SHORT-NAME>DemCallbackEventStatusChangedFnc</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Function name of prototype &quot;EventStatusChanged&quot;</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<ECUC-FUNCTION-NAME-DEF-VARIANTS>
																<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
															</ECUC-FUNCTION-NAME-DEF-VARIANTS>
														</ECUC-FUNCTION-NAME-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: DemCallbackInitMForE -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:11b1adbe-fd9a-49a2-a43e-01bed9346c47">
													<SHORT-NAME>DemCallbackInitMForE</SHORT-NAME>
													<DESC>
														<L-2 L="EN">The presence of this container indicates, that the Dem has access to an &quot;InitMonitorForEvent&quot; callback, which the Dem will call to initialize a monitor.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">In case the container has a DemCallbackInitMForEFnc, this parameter defines the name of the function that the Dem will call. 

                                                In case there is no DemCallbackInitMForEFnc, the Dem will have an R-Port requiring the interface CallbackInitMonitorForEvent, whose name is generated by using the unique callback-prefix followed by the event name.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: DemCallbackInitMForEFnc -->
														<ECUC-FUNCTION-NAME-DEF UUID="ECUC:46ba0efd-c45e-4400-a6f9-0441ffe9df5b">
															<SHORT-NAME>DemCallbackInitMForEFnc</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Function name of prototype &quot;InitMonitorForEvent&quot;.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<ECUC-FUNCTION-NAME-DEF-VARIANTS>
																<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
															</ECUC-FUNCTION-NAME-DEF-VARIANTS>
														</ECUC-FUNCTION-NAME-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: DemEventClass -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:5127af6d-f63f-46f2-9279-2ddfa6850162">
													<SHORT-NAME>DemEventClass</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container contains the configuration (parameters) for EventClass</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: DemAgingAllowed -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:13bf7d23-8cc0-4b57-8911-92658b0cb328">
															<SHORT-NAME>DemAgingAllowed</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Switch to allow aging/unlearning of the event or not.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">true: aging allowed
                                                        false: aging not allowed</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: DemAgingCycleCounterThreshold -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:c005de82-ee68-46ba-8069-ed248a20b11b">
															<SHORT-NAME>DemAgingCycleCounterThreshold</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Number of aging cycles needed to unlearn/delete the event.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>256</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: DemConsiderPtoStatus -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:ade2dfbd-edda-4dba-82cf-889cca141ba3">
															<SHORT-NAME>DemConsiderPtoStatus</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter is TRUE, when the event is affected by the Dem PTO handling.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: DemEventDestination -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:3d77d2e5-cd5d-463c-a083-aca98fec267d">
															<SHORT-NAME>DemEventDestination</SHORT-NAME>
															<DESC>
																<L-2 L="EN">The event destination assigns events to none, one or multiple origins.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">If no event destination is assigned to a specific event, the event is handled internally and is not visible externally to the Dcm.
                                                        If more than one event destination is assigned to a specific event, the event can be present in the corresponding origins.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>4</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:a03e4f82-643d-8779-1d86-318e8896bc8f">
																	<SHORT-NAME>DEM_DTC_ORIGIN_MIRROR_MEMORY</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:eef2bde8-d198-8cfb-3b62-************">
																	<SHORT-NAME>DEM_DTC_ORIGIN_PRIMARY_MEMORY</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:237a2988-b8ec-882a-4abc-99c07d550020">
																	<SHORT-NAME>DEM_DTC_ORIGIN_SECONDARY_MEMORY</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: DemEventFailureCycleCounterThreshold -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:6506cb9d-36d0-4f67-89f7-266b9f4cbc3a">
															<SHORT-NAME>DemEventFailureCycleCounterThreshold</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines the number of failure cycles for the event based fault confirmation.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">If this parameter is enabled, fault confirmation of the event is enabled accordingly.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>256</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: DemEventMemoryEntryFdcThresholdStorageValue -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:8dd83213-1223-4eff-ac82-a316ef9d215d">
															<SHORT-NAME>DemEventMemoryEntryFdcThresholdStorageValue</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Threshold to allocate an event memory entry and to capture the Freeze Frame.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: DemEventOBDReadinessGroup -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:bd389f92-bcb8-4921-acc3-d96cb78371b6">
															<SHORT-NAME>DemEventOBDReadinessGroup</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter specifies the Event OBD Readiness group for PID $01 and PID $41 computation.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">This parameter is only applicable for emission-related ECUs.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:c8a0f871-dd27-905b-2879-572db8e3cf4e">
																	<SHORT-NAME>DEM_OBD_RDY_AC</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:9a86d2f9-5c7d-8922-18be-4a79826b0a0d">
																	<SHORT-NAME>DEM_OBD_RDY_BOOSTPR</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:67c0eb8c-9b75-8fd3-41dd-2c6b3da65fe7">
																	<SHORT-NAME>DEM_OBD_RDY_CAT</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:590c5855-ba71-889e-1dfe-d089d8c3e99e">
																	<SHORT-NAME>DEM_OBD_RDY_CMPRCMPT</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:24406652-28a2-8c98-22ab-14b165c1710f">
																	<SHORT-NAME>DEM_OBD_RDY_EGSENS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:4814a24f-ba27-8e30-4460-dad6236a541c">
																	<SHORT-NAME>DEM_OBD_RDY_ERG</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:3522472a-e6b0-8fff-19e8-2698cb82efe3">
																	<SHORT-NAME>DEM_OBD_RDY_EVAP</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:5f657eb9-ca7e-8709-4d25-867796f640a1">
																	<SHORT-NAME>DEM_OBD_RDY_FLSYS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:e8520c53-3a66-8c9a-1432-a51a209ba087">
																	<SHORT-NAME>DEM_OBD_RDY_FLSYS_NONCONT</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:57d6de23-bfa2-8f29-35e6-744ef693e713">
																	<SHORT-NAME>DEM_OBD_RDY_HCCAT</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:112d66a3-63f0-95f8-2a65-d852605b6538">
																	<SHORT-NAME>DEM_OBD_RDY_HTCAT</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:caeee2be-aee6-9455-27eb-3162635713be">
																	<SHORT-NAME>DEM_OBD_RDY_MISF</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:93cb2d69-e5fc-8886-34b6-4a0177156aa4">
																	<SHORT-NAME>DEM_OBD_RDY_NONE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:3f1bae63-abca-8801-398c-9f5897a4b2d2">
																	<SHORT-NAME>DEM_OBD_RDY_NOXCAT</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:c67a245f-ccb7-8a0c-45db-6fb3ca5220b3">
																	<SHORT-NAME>DEM_OBD_RDY_O2SENS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:1f54136a-d23c-8c53-2bdf-4bbe0e1b58cb">
																	<SHORT-NAME>DEM_OBD_RDY_O2SENSHT</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:e09df06a-a9de-9226-3cab-f5bd3db29009">
																	<SHORT-NAME>DEM_OBD_RDY_PMFLT</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:70ebb189-51b7-91dd-22e4-9001674709d2">
																	<SHORT-NAME>DEM_OBD_RDY_SECAIR</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: DemEventPriority -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:3ef7c809-cd40-4824-b0cd-da79a594c75d">
															<SHORT-NAME>DemEventPriority</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Priority of the event, in view of full event buffer.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>256</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: DemEventSignificance -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:73be02e4-de26-4bf9-87d8-8f6d3b15f532">
															<SHORT-NAME>DemEventSignificance</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Significance of the event, which indicates additional information concerning fault classification and resolution.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">It can be mapped as Dem-internal data element. It shall be configured, if it is a part of event related data.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:fa8dfd7a-7d54-908e-2512-063626db83c1">
																	<SHORT-NAME>DEM_EVENT_SIGNIFICANCE_FAULT</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:b8389afd-2112-8f54-1689-ee6c4413023f">
																	<SHORT-NAME>DEM_EVENT_SIGNIFICANCE_OCCURRENCE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: DemFFPrestorageSupported -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:998abc59-21d6-4f48-98d2-f84c83c31b9e">
															<SHORT-NAME>DemFFPrestorageSupported</SHORT-NAME>
															<DESC>
																<L-2 L="EN">If this parameter is set to true, then the Prestorage of FreezeFrames is supported by the assigned event. This parameter is useful to calculate the buffer size.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Choice Reference Definition: DemAgingCycleRef -->
														<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:2c17db4a-8035-4b38-bca3-093e53f7d34d">
															<SHORT-NAME>DemAgingCycleRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the cycle which is triggering the aging of the event. This can either be the same as the operation cycle of the event, or a separate aging cycle reported via API Dem_SetAgingCycleState. If external aging is configured (refer to DemAgingCycleCounterProcessing), this parameter is not used.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC V1.0.0</ORIGIN>
															<DESTINATION-REFS>
																<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemAgingCycle</DESTINATION-REF>
																<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemOperationCycle</DESTINATION-REF>
															</DESTINATION-REFS>
														</ECUC-CHOICE-REFERENCE-DEF>
														<!-- Reference Definition: DemEnableConditionGroupRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:6953a02d-6999-4d84-8c1d-daede62dfc77">
															<SHORT-NAME>DemEnableConditionGroupRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">References an enable condition group.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC V1.0.0</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemEnableConditionGroup</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
														<!-- Reference Definition: DemEventFailureCycleRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:2f31cb98-c767-407b-82e7-fe6fcdf86076">
															<SHORT-NAME>DemEventFailureCycleRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Kind of failure cycle for the event based fault confirmation.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">If fault confirmation of an event is enabled, but this parameter is disabled, the operation cycle (refer to DemOperationCycleRef) is used for fault confirmation.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC V1.0.0</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemOperationCycle</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
														<!-- Reference Definition: DemOperationCycleRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:fc68bc01-d147-4b6d-95a2-36cfa577cacf">
															<SHORT-NAME>DemOperationCycleRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Kind of operation cycle for the event (e.g. power cycle, driving cycle, ...)</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC V1.0.0</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemOperationCycle</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
														<!-- Reference Definition: DemStorageConditionGroupRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:92052d72-8af4-4f08-9d1f-2a901238caf9">
															<SHORT-NAME>DemStorageConditionGroupRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">References a storage condition group.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC V1.0.0</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemStorageConditionGroup</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
													<SUB-CONTAINERS>
														<!-- Container Definition: DemDebounceAlgorithmClass -->
														<ECUC-CHOICE-CONTAINER-DEF UUID="ECUC:98a5d79e-8975-48e4-bbc8-a174596e2dbb">
															<SHORT-NAME>DemDebounceAlgorithmClass</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Debounce algorithm class: counter based, time based, or monitor internal.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<CHOICES>
																<!-- Container Definition: DemDebounceCounterBased -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:c20d7e84-9b93-4776-a3db-24113379f5f4">
																	<SHORT-NAME>DemDebounceCounterBased</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container contains the configuration (parameters) for counter based debouncing.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: DemDebounceBehavior -->
																		<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:6fd7cbcb-976b-46c5-bd9c-2ac97686f52d">
																			<SHORT-NAME>DemDebounceBehavior</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines how the event debounce algorithm will behave, if a related enable condition is not fulfilled or ControlDTCSetting of the related event is disabled.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<LITERALS>
																				<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:a277b67f-c1e1-8c4e-444e-e0df7539a03b">
																					<SHORT-NAME>DEM_DEBOUNCE_FREEZE</SHORT-NAME>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																				</ECUC-ENUMERATION-LITERAL-DEF>
																				<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:0f3c06cc-8a2d-8a68-4b9b-925b50c83898">
																					<SHORT-NAME>DEM_DEBOUNCE_RESET</SHORT-NAME>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																				</ECUC-ENUMERATION-LITERAL-DEF>
																			</LITERALS>
																		</ECUC-ENUMERATION-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: DemDebounceCounterDecrementStepSize -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:07c58e53-a31c-421d-a6eb-910f2d3cd2d8">
																			<SHORT-NAME>DemDebounceCounterDecrementStepSize</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Defines the step size for decrementation of the internal debounce counter (PREPASSED).</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<MAX>32768</MAX>
																			<MIN>1</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: DemDebounceCounterFailedThreshold -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:aff278be-**************-212582caf8f2">
																			<SHORT-NAME>DemDebounceCounterFailedThreshold</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Defines the value of the internal debounce counter, which indicates the failed status.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<MAX>32767</MAX>
																			<MIN>1</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: DemDebounceCounterIncrementStepSize -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:7cde5c15-c87f-4eed-b0f5-38313a32efe2">
																			<SHORT-NAME>DemDebounceCounterIncrementStepSize</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Defines the step size for incrementation of the internal debounce counter (PREFAILED).</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<MAX>32767</MAX>
																			<MIN>1</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: DemDebounceCounterJumpDown -->
																		<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:aa2d713a-ec74-4c4c-93a9-bd83b0c107b8">
																			<SHORT-NAME>DemDebounceCounterJumpDown</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Switch for the activation of Jump-Down.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">true: Jump-Down activated
                                                                        false: Jump-Down deactivated</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: DemDebounceCounterJumpDownValue -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:7baa87e8-87d0-45bb-9937-a8c3a4ebc2b6">
																			<SHORT-NAME>DemDebounceCounterJumpDownValue</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Jump-Down value of the internal debounce counter which is taken as initialization value for the counter when the respective step-down occurs.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>0</DEFAULT-VALUE>
																			<MAX>32767</MAX>
																			<MIN>-32768</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: DemDebounceCounterJumpUp -->
																		<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:6f82b9d4-c3e1-4f00-8196-1f85c7be1562">
																			<SHORT-NAME>DemDebounceCounterJumpUp</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Switch for the activation of Jump-Up.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">true: Jump-Up activated
                                                                        false: Jump-Up deactivated</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: DemDebounceCounterJumpUpValue -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:86693810-0a0a-4927-91ae-5d3723761724">
																			<SHORT-NAME>DemDebounceCounterJumpUpValue</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Jump-Up value of the internal debounce counter which is taken as initialization value for the counter when the respective step-up occurs.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>0</DEFAULT-VALUE>
																			<MAX>32767</MAX>
																			<MIN>-32768</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: DemDebounceCounterPassedThreshold -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:2c4aba4f-9fc9-4572-a226-9562516ffac3">
																			<SHORT-NAME>DemDebounceCounterPassedThreshold</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Defines the value of the internal debounce counter, which indicates the passed status.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<MAX>-1</MAX>
																			<MIN>-32768</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: DemDebounceCounterStorage -->
																		<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:33babe76-7e6b-44dc-8233-e33933cf87e7">
																			<SHORT-NAME>DemDebounceCounterStorage</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Switch to store the debounce counter value non-volatile or not.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">true: debounce counter value shall be stored non-volatile
                                                                        false: debounce counter value is volatile</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>false</DEFAULT-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: DemDebounceMonitorInternal -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:cc0801d6-d390-4989-80bf-e5bf1d2832b6">
																	<SHORT-NAME>DemDebounceMonitorInternal</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container contains the configuration (parameters) for monitor internal debouncing.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<SUB-CONTAINERS>
																		<!-- Container Definition: DemCallbackGetFDC -->
																		<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:82f362d0-6c6f-47ea-b760-c561850bcfa2">
																			<SHORT-NAME>DemCallbackGetFDC</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">The presence of this container indicates, that the Dem has access to a &quot;GetFaultDetectionCounter&quot; callback, which the Dem will call to obtain the value of the fault detection counter.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">In case the container has a DemCallbackGetFDCFnc, this parameter defines the name of the function that the Dem will call.

                                                                        In case there is no DemCallbackGetFDCFnc, the Dem will have a R-Port requiring the interface CallbackGetFaultDetectionCounter, whose name is generated by using the unique callback-prefix followed by the event name.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																			<PARAMETERS>
																				<!-- PARAMETER DEFINITION: DemCallbackGetFDCFnc -->
																				<ECUC-FUNCTION-NAME-DEF UUID="ECUC:fc99fc77-96af-446e-9167-2db5d56b6f50">
																					<SHORT-NAME>DemCallbackGetFDCFnc</SHORT-NAME>
																					<DESC>
																						<L-2 L="EN">Function name of prototype &quot;GetFaultDetectionCounter&quot;.</L-2>
																					</DESC>
																					<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																					<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																					<IMPLEMENTATION-CONFIG-CLASSES>
																						<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																							<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																							<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																						</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																						<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																							<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																							<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																						</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					</IMPLEMENTATION-CONFIG-CLASSES>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																					<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																					<ECUC-FUNCTION-NAME-DEF-VARIANTS>
																						<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
																					</ECUC-FUNCTION-NAME-DEF-VARIANTS>
																				</ECUC-FUNCTION-NAME-DEF>
																			</PARAMETERS>
																		</ECUC-PARAM-CONF-CONTAINER-DEF>
																	</SUB-CONTAINERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: DemDebounceTimeBase -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:498c5d2e-12d2-43c7-8892-9bb11701a6aa">
																	<SHORT-NAME>DemDebounceTimeBase</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container contains the configuration (parameters) for time based debouncing.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: DemDebounceBehavior -->
																		<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:79236e41-97f0-418c-b2f2-241d6595589b">
																			<SHORT-NAME>DemDebounceBehavior</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines how the event debounce algorithm will</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">behave, if a related enable condition is not fulfilled or ControlDTCSetting of the related event is disabled.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<LITERALS>
																				<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:c920c736-025f-8859-3752-2e8164029522">
																					<SHORT-NAME>DEM_DEBOUNCE_FREEZE</SHORT-NAME>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																				</ECUC-ENUMERATION-LITERAL-DEF>
																				<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:f294965b-3c71-8867-2889-2af81735c0a4">
																					<SHORT-NAME>DEM_DEBOUNCE_RESET</SHORT-NAME>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																				</ECUC-ENUMERATION-LITERAL-DEF>
																			</LITERALS>
																		</ECUC-ENUMERATION-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: DemDebounceTimeFailedThreshold -->
																		<ECUC-FLOAT-PARAM-DEF UUID="ECUC:4579dd3e-fa1d-404c-a743-9b236697409b">
																			<SHORT-NAME>DemDebounceTimeFailedThreshold</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Defines the time out duration for &quot;Event Failed&quot; qualification.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">The AUTOSAR configuration standard is to use SI units, so this parameter is defined as float value in seconds. Dem configuration tools must convert this float value to the appropriate value format for the use in the software implementation of Dem.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<MAX>3600</MAX>
																			<MIN>0.001</MIN>
																		</ECUC-FLOAT-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: DemDebounceTimePassedThreshold -->
																		<ECUC-FLOAT-PARAM-DEF UUID="ECUC:e3952bac-0313-43a6-ac7c-19204e3a2662">
																			<SHORT-NAME>DemDebounceTimePassedThreshold</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Defines the time out duration for &quot;Event Passed&quot; qualification.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">The AUTOSAR configuration standard is to use SI units, so this parameter is defined as float value in seconds. Dem configuration tools must convert this float value to the appropriate value format for the use in the software implementation of Dem.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<MAX>3600</MAX>
																			<MIN>0.001</MIN>
																		</ECUC-FLOAT-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
															</CHOICES>
														</ECUC-CHOICE-CONTAINER-DEF>
														<!-- Container Definition: DemIndicatorAttribute -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:38466ee0-81d6-4eb5-84ea-278631d94b99">
															<SHORT-NAME>DemIndicatorAttribute</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This container contains the event specific configuration of Indicators.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: DemIndicatorBehaviour -->
																<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:26582faa-e852-45d6-a68d-cf0cc279ca26">
																	<SHORT-NAME>DemIndicatorBehaviour</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Behaviour of the linked indicator</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<LITERALS>
																		<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:ed2bb1be-821a-8ebb-3920-9a6307d1730f">
																			<SHORT-NAME>DEM_INDICATOR_BLINKING</SHORT-NAME>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																		</ECUC-ENUMERATION-LITERAL-DEF>
																		<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:5daca4a6-e4a2-94da-42ad-81a2155b29aa">
																			<SHORT-NAME>DEM_INDICATOR_BLINK_CONT</SHORT-NAME>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																		</ECUC-ENUMERATION-LITERAL-DEF>
																		<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:47b58546-dc96-92ea-1ef8-c0763ee52973">
																			<SHORT-NAME>DEM_INDICATOR_CONTINUOUS</SHORT-NAME>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																		</ECUC-ENUMERATION-LITERAL-DEF>
																		<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:61d3b8f7-120a-96c1-3f12-b77182f842de">
																			<SHORT-NAME>DEM_INDICATOR_FAST_FLASH</SHORT-NAME>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																		</ECUC-ENUMERATION-LITERAL-DEF>
																		<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:bb6f8658-3f11-9b36-28aa-e5c201c3862d">
																			<SHORT-NAME>DEM_INDICATOR_SLOW_FLASH</SHORT-NAME>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																		</ECUC-ENUMERATION-LITERAL-DEF>
																	</LITERALS>
																</ECUC-ENUMERATION-PARAM-DEF>
																<!-- PARAMETER DEFINITION: DemIndicatorFailureCycleCounterThreshold -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:bc2a7554-29fe-4a08-a8a8-57087833e55c">
																	<SHORT-NAME>DemIndicatorFailureCycleCounterThreshold</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Defines the number of failure cycles for the WarningIndicatorOnCriteria.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: DemIndicatorFailureCycleSource -->
																<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:9224f6ca-d10a-4b8d-9b76-da543ec42a3a">
																	<SHORT-NAME>DemIndicatorFailureCycleSource</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This parameter defines, which failure cycle is used for the WarningIndicatorOnCriteria handling.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<LITERALS>
																		<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:4033c0dd-db8e-98eb-31ab-db058d3e2e93">
																			<SHORT-NAME>DEM_FAILURE_CYCLE_EVENT</SHORT-NAME>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																		</ECUC-ENUMERATION-LITERAL-DEF>
																		<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:213030d5-592b-91ca-2a6c-e4754e3a01c2">
																			<SHORT-NAME>DEM_FAILURE_CYCLE_INDICATOR</SHORT-NAME>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																		</ECUC-ENUMERATION-LITERAL-DEF>
																	</LITERALS>
																</ECUC-ENUMERATION-PARAM-DEF>
																<!-- PARAMETER DEFINITION: DemIndicatorHealingCycleCounterThreshold -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:4248526d-57f1-4519-bb2d-400a541280f5">
																	<SHORT-NAME>DemIndicatorHealingCycleCounterThreshold</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Defines the number of healing cycles for the WarningIndicatorOffCriteria.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
															<REFERENCES>
																<!-- Reference Definition: DemIndicatorFailureCycleRef -->
																<ECUC-REFERENCE-DEF UUID="ECUC:3fc5415b-ef0e-4616-84af-ac48fb00b4e7">
																	<SHORT-NAME>DemIndicatorFailureCycleRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Kind of failure cycle for the indicator controlled by the according event used for the WarningIndicatorOnCriteria.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemOperationCycle</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
																<!-- Reference Definition: DemIndicatorHealingCycleRef -->
																<ECUC-REFERENCE-DEF UUID="ECUC:01818b0e-d069-473b-bb27-f067edb1f453">
																	<SHORT-NAME>DemIndicatorHealingCycleRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Kind of healing cycle for the indicator controlled by the according event used for the WarningIndicatorOffCriteria.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemOperationCycle</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
																<!-- Reference Definition: DemIndicatorRef -->
																<ECUC-REFERENCE-DEF UUID="ECUC:328aa173-bf87-415a-a94c-8b48686226a6">
																	<SHORT-NAME>DemIndicatorRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to the used indicator.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemIndicator</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
															</REFERENCES>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemJ1939NodeAddress -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:27441308-61a5-44f9-8352-4d8afd5c2f68">
											<SHORT-NAME>DemJ1939NodeAddress</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Contains the parameters for the support of a logical J1939 node (identified by an ECU address).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<REFERENCES>
												<!-- Symbolic Name Reference Definition: DemJ1939NmNodeRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:a36d170a-9faa-439d-a951-d2bd5c8a6004">
													<SHORT-NAME>DemJ1939NmNodeRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the corresponding J1939Nm node.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/J1939Nm/J1939NmConfigSet/J1939NmNode</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemPidClass -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:44faa8c1-9430-4ce8-b572-a9033e48bdd5">
											<SHORT-NAME>DemPidClass</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the different PIDs for the single global OBD relevant freeze frame class. It is assembled out of one or several data elements.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DemPidIdentifier -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:18337b6d-c988-4e65-9896-c0814ac35f45">
													<SHORT-NAME>DemPidIdentifier</SHORT-NAME>
													<DESC>
														<L-2 L="EN">identifier of the PID</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<SUB-CONTAINERS>
												<!-- Container Definition: DemPidDataElement -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:08dbe78d-7b7b-4510-bfa9-3ce041b597d2">
													<SHORT-NAME>DemPidDataElement</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container contains the different data elements contained in the specific PID.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<REFERENCES>
														<!-- Choice Reference Definition: DemPidDataElementClassRef -->
														<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:e55a5a69-f687-4fd6-9b48-17ca02603d1d">
															<SHORT-NAME>DemPidDataElementClassRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This reference contains the link to a data element class.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REFS>
																<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</DESTINATION-REF>
																<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</DESTINATION-REF>
																<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemInternalDataElementClass</DESTINATION-REF>
															</DESTINATION-REFS>
														</ECUC-CHOICE-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: DemGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:6ca017f1-d32b-49c2-9624-9c1a628401c2">
									<SHORT-NAME>DemGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container contains the configuration (parameters) of the BSW Dem</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: DemAgingCycleCounterProcessing -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:cf65f678-2be0-4cd4-91bb-8023cce9e2ff">
											<SHORT-NAME>DemAgingCycleCounterProcessing</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This configuration switch defines, whether the aging counter is calculated Dem-internally or provided via Dem_SetAgingCycleCounterValue.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:74038486-efbf-8bb6-2a8c-1fc9b80dc914">
													<SHORT-NAME>DEM_PROCESS_AGINGCTR_EXTERN</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:b9f4e64d-628d-97bf-337b-3dc2b856102f">
													<SHORT-NAME>DEM_PROCESS_AGINGCTR_INTERN</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemAgingRequieresTestedCycle -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:a3bd8297-60fe-44db-9094-40808a08b5f4">
											<SHORT-NAME>DemAgingRequieresTestedCycle</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Defines if the aging cycle counter is processed every aging cycles or if only tested aging cycle are considered.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: only tested aging cycle are considered for aging cycle counter

                                        false: aging cycle counter is processed every aging cycle</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemAvailabilitySupport -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:04f071cf-22a9-4382-97a2-cecb921f771b">
											<SHORT-NAME>DemAvailabilitySupport</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This configuration switch defines, whether support for availability is enabled or not.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:47cbfa3b-9ff3-8dd3-22e7-8c35805e2017">
													<SHORT-NAME>DEM_EVENT_AVAILABILITY</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:94d22fd6-4a01-9883-1df3-7f674f14b66d">
													<SHORT-NAME>DEM_NO_AVAILABILITY</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemBswErrorBufferSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:6388fad6-ff27-41e9-bc9e-fa9d41faf0df">
											<SHORT-NAME>DemBswErrorBufferSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Maximum number of elements in buffer for handling of BSW errors (ref. to Dem107).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>255</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemClearDTCBehavior -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:e0481c61-58b6-42ce-af08-899ebe9c41d8">
											<SHORT-NAME>DemClearDTCBehavior</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Defines the clearing process of diagnostic information for volatile and non-volatile memory and the positive response handling for the Dcm module.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:b30a5dfd-99d1-93de-2c3c-2c2a14de704a">
													<SHORT-NAME>DEM_CLRRESP_NONVOLATILE_FINISH</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:1295f1d7-e2b2-8f3c-4dec-3ba7362a5ea1">
													<SHORT-NAME>DEM_CLRRESP_NONVOLATILE_TRIGGER</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:e1a1bc45-0de6-8c12-2b76-04da086cb364">
													<SHORT-NAME>DEM_CLRRESP_VOLATILE</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemClearDTCLimitation -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:27cbd13d-d1aa-4038-b93c-c83a0474cd1e">
											<SHORT-NAME>DemClearDTCLimitation</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Defines the supported Dem_&lt;...&gt;ClearDTC API scope.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>DEM_ALL_SUPPORTED_DTCS</DEFAULT-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:7e3fe85f-aad3-90f5-2c64-13f49844bc5a">
													<SHORT-NAME>DEM_ALL_SUPPORTED_DTCS</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:f515a1b0-a1ab-9419-501e-0f36f79f8be1">
													<SHORT-NAME>DEM_ONLY_CLEAR_ALL_DTCS</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemDataElementDefaultEndianness -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:2c01e075-aad4-4de0-9d3e-801945176c2c">
											<SHORT-NAME>DemDataElementDefaultEndianness</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Defines the default endianness of the data belonging to a data element which is applicable if the DemExternalSRDataElementClass does not define a endianness.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:cb8781ea-bf0e-9653-55e5-30e02cf7afcb">
													<SHORT-NAME>BIG_ENDIAN</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:af36f702-cf12-971b-4aec-53e6bc0af573">
													<SHORT-NAME>LITTLE_ENDIAN</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:9e032af5-1fc1-8e43-476d-4b2f0e27c72b">
													<SHORT-NAME>OPAQUE</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemDebounceCounterBasedSupport -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:6d308999-a425-4904-aef3-a55c5e5db5e6">
											<SHORT-NAME>DemDebounceCounterBasedSupport</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This configuration switch defines, whether support for counter based debouncing is enabled or not.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: counter based debouncing support is enabled
                                        false: counter based debouncing support is disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemDebounceTimeBasedSupport -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:306ac5ba-3e68-40cc-a248-b1bde6637e36">
											<SHORT-NAME>DemDebounceTimeBasedSupport</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This configuration switch defines, whether support for time based debouncing is enabled or not.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: time based debouncing support is enabled
                                        false: time based debouncing support is disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemDevErrorDetect -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:21da3508-a0a5-4ecb-994e-b8378d8bcc8a">
											<SHORT-NAME>DemDevErrorDetect</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Activate/Deactivate the Development Error Detection and Notification.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: Development Error Detection and Notification activated
                                        false: Development Error Detection and Notification deactivated</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemDtcStatusAvailabilityMask -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:4a2afd78-57fb-4c89-a156-eae8fc5dcf37">
											<SHORT-NAME>DemDtcStatusAvailabilityMask</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Mask for the supported DTC status bits by the Dem. This mask is used by UDS service 0x19.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>255</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemEnableConditionSupport -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:a47fcf61-b9f1-4ad7-ac32-11a0b471653f">
											<SHORT-NAME>DemEnableConditionSupport</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This configuration switch defines, whether support for enable conditions is enabled or not.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: support for enable conditions is enabled
                                        false: support for enable conditions is disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemEventCombinationSupport -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:f3fe6a87-b322-467a-aa77-e49d9ce66490">
											<SHORT-NAME>DemEventCombinationSupport</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the type of event combination supported by the Dem.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:d66c7d73-ea0b-8c7b-4c09-c4d084a311a0">
													<SHORT-NAME>DEM_EVCOMB_DISABLED</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:6367c8f4-ddd6-8d00-4cdd-88006f014768">
													<SHORT-NAME>DEM_EVCOMB_TYPE1</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:4161bcd0-55d7-9295-30e0-c979d4c2e77f">
													<SHORT-NAME>DEM_EVCOMB_TYPE2</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemEventDisplacementStrategy -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:6108bc87-5feb-439d-8100-cb20505e7e0e">
											<SHORT-NAME>DemEventDisplacementStrategy</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This configuration switch defines, whether support for event displacement is enabled or not, and which displacement strategy is followed.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:15933adf-4393-98a2-29cb-ee44717e29ea">
													<SHORT-NAME>DEM_DISPLACEMENT_FULL</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:7f48bfd1-2eac-95d3-2985-0a7f17fb4b2d">
													<SHORT-NAME>DEM_DISPLACEMENT_NONE</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:9cc9fd25-1c90-9668-436d-738ba763ae7b">
													<SHORT-NAME>DEM_DISPLACEMENT_PRIO_OCC</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemEventMemoryEntryStorageTrigger -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:b074dc99-92e8-425c-bfa2-d900a2a78862">
											<SHORT-NAME>DemEventMemoryEntryStorageTrigger</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configures the primary trigger to allocate an event memory entry.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>DEM_STORAGE_ON_TEST_FAILED</DEFAULT-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:40e35d68-3175-9653-458e-4ce35de8f7cc">
													<SHORT-NAME>DEM_STORAGE_ON_CONFIRMED</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:149d6fe2-ec53-8a48-2e50-f63cfaa7c572">
													<SHORT-NAME>DEM_STORAGE_ON_FDC_THRESHOLD</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:864ba2f6-1db9-9896-3baf-76db84fb324b">
													<SHORT-NAME>DEM_STORAGE_ON_PENDING</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:467006ce-28c4-9367-3dce-2b4dab2d2bb4">
													<SHORT-NAME>DEM_STORAGE_ON_TEST_FAILED</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemExtendedDataCapture -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:5e5bf7d4-5a8a-4bbf-aadd-d1072da3f2ed">
											<SHORT-NAME>DemExtendedDataCapture</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the point in time, when the extended data collection is done for the initial event memory entry.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:629e45b2-eed4-956c-457b-b2b68bb6e945">
													<SHORT-NAME>DEM_TRIGGER_EVENT_MEMORY_STORAGE</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:49136128-740e-9145-3596-7b151e98c86b">
													<SHORT-NAME>DEM_TRIGGER_TESTFAILED</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemFreezeFrameCapture -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:0cc73e66-4679-461b-929f-cd30f8ded7da">
											<SHORT-NAME>DemFreezeFrameCapture</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the point in time, when the freeze frame data collection is done for the initial event memory entry.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:2315ae8f-9143-90ad-3395-0be630b0cb51">
													<SHORT-NAME>DEM_TRIGGER_EVENT_MEMORY_STORAGE</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:193e6e4a-d134-92b4-55d0-6203dcd16f32">
													<SHORT-NAME>DEM_TRIGGER_TESTFAILED</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemGeneralInterfaceSupport -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:088bfcc2-3ffe-426c-9182-db21e7c3b259">
											<SHORT-NAME>DemGeneralInterfaceSupport</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The interfaces GeneralEvtInfo, GeneralCallbackEventDataChanged and GeneralCallbackEventStatusChange are provided if DemGeneralInterfaceSupport is equal to true.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemHeaderFileInclusion -->
										<ECUC-STRING-PARAM-DEF UUID="ECUC:0d15ce1a-85f5-49a6-8e19-d6ef5c19fbf9">
											<SHORT-NAME>DemHeaderFileInclusion</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Name of the header file(s) to be included by the Dem module containing the used C-callback declarations.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-STRING-PARAM-DEF-VARIANTS>
												<ECUC-STRING-PARAM-DEF-CONDITIONAL>
													<REGULAR-EXPRESSION>[a-zA-Z0-9_]([a-zA-Z0-9\._])*</REGULAR-EXPRESSION>
												</ECUC-STRING-PARAM-DEF-CONDITIONAL>
											</ECUC-STRING-PARAM-DEF-VARIANTS>
										</ECUC-STRING-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemImmediateNvStorageLimit -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:8a2b8a2b-cffb-4401-8732-5de196039921">
											<SHORT-NAME>DemImmediateNvStorageLimit</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the maximum number of occurrences, a specific event memory entry is allowed, to be stored in NVRAM immediately (refer to DemImmediateNvStorage).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>255</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemMaxNumberEventEntryMirror -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:884c0df3-c917-44ef-adf6-a7d6f4638e20">
											<SHORT-NAME>DemMaxNumberEventEntryMirror</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Maximum number of events which can be stored in the mirror memory</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>255</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemMaxNumberEventEntryPermanent -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:cd9f6fcb-2134-4e0b-95fb-833dfca769bb">
											<SHORT-NAME>DemMaxNumberEventEntryPermanent</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Maximum number of events which can be stored in the permanent memory.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The assignment of an event to this memory type is dynamic and used for emission-related events only.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>255</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemMaxNumberEventEntryPrimary -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:7fad4aa0-c3ce-4005-bb7f-dfb8b1a1172a">
											<SHORT-NAME>DemMaxNumberEventEntryPrimary</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Maximum number of events which can be stored in the primary memory</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>255</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemMaxNumberEventEntrySecondary -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:29cb4d86-003f-4d93-9e59-7105edd601f4">
											<SHORT-NAME>DemMaxNumberEventEntrySecondary</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Maximum number of events which can be stored in the secondary memory</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>255</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemMaxNumberPrestoredFF -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:a9b9d119-6ddf-46f6-93bd-5a990016cf70">
											<SHORT-NAME>DemMaxNumberPrestoredFF</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Defines the maximum number for prestored freeze frames.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">If set to 0, then freeze frame prestorage is not supported by the ECU.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>255</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemOBDSupport -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:8e668bd5-8855-4f56-a1b2-9dd2da07c076">
											<SHORT-NAME>DemOBDSupport</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This configuration switch defines OBD support and kind of OBD ECU.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:ec16f318-57d8-909f-2616-6d499a525ffa">
													<SHORT-NAME>DEM_OBD_DEP_SEC_ECU</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:52d64358-cd2c-8f23-4966-a2c13ccc6508">
													<SHORT-NAME>DEM_OBD_MASTER_ECU</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:2f0163e8-2714-8cbd-222c-1bbd95b1c770">
													<SHORT-NAME>DEM_OBD_NO_OBD_SUPPORT</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:974659a6-0e9b-8e0f-41af-99055582e0e2">
													<SHORT-NAME>DEM_OBD_PRIMARY_ECU</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemOccurrenceCounterProcessing -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:ce89a944-4a2a-4473-ad59-f307a8e9103e">
											<SHORT-NAME>DemOccurrenceCounterProcessing</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This configuration switch defines the consideration of the fault confirmation process for the occurrence counter. For OBD and mixed systems (OBD/non OBD, refer to DemOBDSupport) configuration switch shall always set to DEM_PROCESS_OCCCTR_TF.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:190f7f57-fb8e-8a14-4dc3-8ecaeeb313bc">
													<SHORT-NAME>DEM_PROCESS_OCCCTR_CDTC</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:59b08558-fc01-9026-3a00-3dc83ab1f38a">
													<SHORT-NAME>DEM_PROCESS_OCCCTR_TF</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemOperationCycleProcessing -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:c6300d57-88cb-4c33-b72e-18e346da8dfb">
											<SHORT-NAME>DemOperationCycleProcessing</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This configuration switch defines, whether the operation cycles are triggered by DEM_CYCLE_STATE_START or collecting an external counter value, which results in respective state changes.</L-2>
												<L-2 L="EN">Tags: atp.Status=obsolete, atp.StatusComment=This parameter is obsolete since the option DEM_PROCESS_OPCYC_COUNTER is not longer supported by AUTOSAR., atp.StatusRevisionBegin=4.1.1</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">This parameter is obsolete since the option DEM_PROCESS_OPCYC_COUNTER is not longer supported by AUTOSAR.</L-1>
												</P>
												<P>
													<L-1 L="EN">This element is obsolete and will be removed in a future revision.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>DEM_PROCESS_OPCYC_STATE</DEFAULT-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:6964e8c5-4a76-9364-207a-bb4d950422e8">
													<SHORT-NAME>DEM_PROCESS_OPCYC_COUNTER</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:469af532-61cc-9353-560a-e7f481e89e28">
													<SHORT-NAME>DEM_PROCESS_OPCYC_STATE</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemOperationCycleStatusStorage -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:06baac28-6a64-46c0-b468-2b6e5f2571bc">
											<SHORT-NAME>DemOperationCycleStatusStorage</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Defines if the operation cycle state is available over the power cycle (stored non-volatile) or not.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: the operation cycle state is stored non-volatile 
                                        false: the operation cycle state is only stored volatile</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemPTOSupport -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:40aaf4a3-811c-4ed4-b5ed-c364b32c4193">
											<SHORT-NAME>DemPTOSupport</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This configuration switch defines, whether PTO support (and therefore PID $1E support) is enabled or not.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemResetConfirmedBitOnOverflow -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:af5bbe02-f227-411a-b071-9b9a5b48bf52">
											<SHORT-NAME>DemResetConfirmedBitOnOverflow</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This configuration switch defines, whether the confirmed bit is reset or not while an event memory entry will be displaced.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemStatusBitHandlingTestFailedSinceLastClear -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:bcf344c1-e4b6-47b2-836b-93bda8616623">
											<SHORT-NAME>DemStatusBitHandlingTestFailedSinceLastClear</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This configuration switch defines, whether the aging and displacement mechanism shall be applied to the &quot;TestFailedSinceLastClear&quot; status bits.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:89e211a9-c14a-94d0-3ad7-a22af76dc4e9">
													<SHORT-NAME>DEM_STATUS_BIT_AGING_AND_DISPLACEMENT</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:ca5c64a2-ebf9-8d5a-2d04-8620708f3325">
													<SHORT-NAME>DEM_STATUS_BIT_NORMAL</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemStatusBitStorageTestFailed -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:b8232040-8309-438b-b16d-3bbf911191b0">
											<SHORT-NAME>DemStatusBitStorageTestFailed</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Activate/Deactivate the permanent storage of the &quot;TestFailed&quot; status bits.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: storage activated
                                        false: storage deactivated</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemStorageConditionSupport -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:dd8247a4-4a28-4326-ad0d-8d23978fbcbf">
											<SHORT-NAME>DemStorageConditionSupport</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This configuration switch defines, whether support for storage conditions is enabled or not.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: support for storage conditions is enabled
                                        false: support for storage conditions is disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemSuppressionSupport -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:b22c10e5-1781-4d5a-9320-781d8d6b2480">
											<SHORT-NAME>DemSuppressionSupport</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This configuration switch defines, whether support for suppression is enabled or not.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:c3c078c7-56f3-9971-1cdf-05ea118bee75">
													<SHORT-NAME>DEM_DTC_SUPPRESSION</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:cacd67e7-62d1-8ca3-436b-1b60d28aee77">
													<SHORT-NAME>DEM_EVENT_AND_DTC_SUPPRESSION</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:1266a2ee-2376-8eec-1990-db0bc3db4483">
													<SHORT-NAME>DEM_EVENT_SUPPRESSION</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:4ff99f43-c7ee-924a-2d6b-aa05c0a10824">
													<SHORT-NAME>DEM_NO_SUPPRESSION</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemTaskTime -->
										<ECUC-FLOAT-PARAM-DEF UUID="ECUC:2109a155-8685-4f62-9f8d-1df661889043">
											<SHORT-NAME>DemTaskTime</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Allow to configure the time for the periodic cyclic task. Please note: This configuration value shall be equal to the value in the Basic Software Scheduler configuration of the RTE module.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The AUTOSAR configuration standard is to use SI units, so this parameter is defined as float value in seconds. Dem configuration tools must convert this float value to the appropriate value format for the use in the software implementation of Dem.

                                        min:
                                        A negative value is not allowed.

                                        max:
                                        After event status was reported, processing shall be completed within 100ms in order to have the fault entry status information updated as soon as possible (e.g. for PID $01).

                                        upperMultiplicity:
                                        Exactly one TaskTime must be specified per configuration.

                                        lowerMultiplicity:
                                        Exactly one TaskTime must be specified per configuration.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC V1.0.0</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>0.1</MAX>
											<MIN>0.001</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemTriggerDcmReports -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:0b862943-7dc6-4a4e-9a7b-4b9cf27a0d19">
											<SHORT-NAME>DemTriggerDcmReports</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Activate/Deactivate the notification to the Diagnostic Communication Manager for ROE processing.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: Dcm ROE notification activated 
                                        false: Dcm ROE notification deactivated</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemTriggerDltReports -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:1aae8fc4-5b22-4a6c-8744-a125dbf06efe">
											<SHORT-NAME>DemTriggerDltReports</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Activate/Deactivate the notification to the Diagnostic Log and Trace.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: Dlt notification activated
                                        false: Dlt notification deactivated</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemTriggerFiMReports -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:ffa4e7ab-5a53-4aab-89d5-faab430e6007">
											<SHORT-NAME>DemTriggerFiMReports</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Activate/Deactivate the notification to the Function Inhibition Manager.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: FiM notification activated
                                        false: FiM notification deactivated</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemTriggerMonitorInitBeforeClearOk -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:5fcadaa0-df21-4e86-ab87-636545aafe14">
											<SHORT-NAME>DemTriggerMonitorInitBeforeClearOk</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Defines if the monitor re-initialization has to be triggered before or after the Dem module returns DEM_CLEAR_OK.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: trigger re-initialization before DEM_CLEAR_OK 
                                        false: trigger re-initialization after DEM_CLEAR_OK</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemTypeOfDTCSupported -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:2e118358-e52d-474d-95d0-6a4807a3bcbf">
											<SHORT-NAME>DemTypeOfDTCSupported</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the format returned by Dem_DcmGetTranslationType and does not relate to/influence the supported Dem functionality.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:4317f3d1-91f3-8a59-2f64-ee4c79c9adbc">
													<SHORT-NAME>DEM_DTC_TRANSLATION_ISO11992_4</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:6503ffd6-9788-9382-1cbf-20dd7143736f">
													<SHORT-NAME>DEM_DTC_TRANSLATION_ISO14229_1</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:370dc9f0-cd6c-97e9-42d8-3e7537b008d2">
													<SHORT-NAME>DEM_DTC_TRANSLATION_ISO15031_6</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:55e98c77-0fd1-8b68-5477-3af60bc82d0e">
													<SHORT-NAME>DEM_DTC_TRANSLATION_SAEJ1939_73</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemTypeOfFreezeFrameRecordNumeration -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:3855ce15-b738-467e-a342-7ae2499366bd">
											<SHORT-NAME>DemTypeOfFreezeFrameRecordNumeration</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the type of assigning freeze frame record numbers for event-specific freeze frame records.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:6ebe6f8d-79ce-904a-180b-fdc7a41bc841">
													<SHORT-NAME>DEM_FF_RECNUM_CALCULATED</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:4a1da08a-ddf4-99b2-54e1-c0dfaf9672eb">
													<SHORT-NAME>DEM_FF_RECNUM_CONFIGURED</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DemVersionInfoApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:2236d8b8-2712-47f4-9d0b-b77e5b5df6d7">
											<SHORT-NAME>DemVersionInfoApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Activate/Deactivate the version information API.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: version information activated
                                        false: version information deactivated</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Reference Definition: DemMILIndicatorRef -->
										<ECUC-REFERENCE-DEF UUID="ECUC:32b33d8d-7755-49f6-8db4-6b12faef15fe">
											<SHORT-NAME>DemMILIndicatorRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the indicator representing the MIL.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">This parameter is mandatory for ECUs supporting OBD (refer to DemOBDSupport).</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemIndicator</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<!-- Container Definition: DemAgingCycle -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:25f625cb-f3db-4091-922d-c554992647c4">
											<SHORT-NAME>DemAgingCycle</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Note that this container definition does not explicitly define a symbolic name parameter. Instead, the short name of the container will be used in the Ecu Configuration Description to specify the symbolic name of the aging cycle name. These aging cycles are reported via API Dem_SetAgingCycleState only.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>256</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemCallbackDTCStatusChanged -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:101cdd27-4d18-467d-a6d0-d9561d5424e7">
											<SHORT-NAME>DemCallbackDTCStatusChanged</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The presence of this container indicates, that the Dem has access to a &quot;DTCStatusChanged&quot; callback, which the Dem will call to notify other components about the change in the status of a DTC.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">In case there is a DemCallbackDTCStatusChangedFnc, this parameter defines the name of the function that the Dem will call. 

                                        In case there is no DemCallbackDTCStatusChangedFnc, the Dem will have an R-Port requiring the interface CallbackDTCStatusChanged whose name is generated by using the unique callback-prefix followed by the event name.

                                        Status change notifications are supported for DTCs in primary memory only.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DemCallbackDTCStatusChangedFnc -->
												<ECUC-FUNCTION-NAME-DEF UUID="ECUC:f95f691b-c363-4c11-9b04-852adad46b90">
													<SHORT-NAME>DemCallbackDTCStatusChangedFnc</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Function name of prototype &quot;DTCStatusChanged&quot;.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">Note: If the parameter DemTriggerDcmReports is enabled, this parameter shall not be &quot;Dcm_DemTriggerOnDTCStatus&quot;.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-FUNCTION-NAME-DEF-VARIANTS>
														<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
													</ECUC-FUNCTION-NAME-DEF-VARIANTS>
												</ECUC-FUNCTION-NAME-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemDataElementClass -->
										<ECUC-CHOICE-CONTAINER-DEF UUID="ECUC:6505a151-66bd-4283-b004-9f267f7ce44e">
											<SHORT-NAME>DemDataElementClass</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the configuration (parameters) for an internal/external data element class.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
											<CHOICES>
												<!-- Container Definition: DemExternalCSDataElementClass -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:731531ff-e865-4793-887a-8064686ae7d1">
													<SHORT-NAME>DemExternalCSDataElementClass</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container contains the configuration (parameters) for an external client/server based data element class.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">It defines, how the Dem can obtain the value of the data element from either a SW-C or another BSW module. Whether a client/server port or a C function-call is used, is defined by DemDataElementUsePort.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: DemDataElementDataSize -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:a23991f9-25de-4d38-8aca-a06d784b6697">
															<SHORT-NAME>DemDataElementDataSize</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines the size of the data element in bytes.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: DemDataElementReadFnc -->
														<ECUC-FUNCTION-NAME-DEF UUID="ECUC:fa9c6503-853e-4f03-b603-dfee41f57709">
															<SHORT-NAME>DemDataElementReadFnc</SHORT-NAME>
															<DESC>
																<L-2 L="EN">In case of DemDataElementUsePort is false, this parameter defines the prototype of the C function &quot;ReadDataElement&quot; used to get the according value.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<ECUC-FUNCTION-NAME-DEF-VARIANTS>
																<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
															</ECUC-FUNCTION-NAME-DEF-VARIANTS>
														</ECUC-FUNCTION-NAME-DEF>
														<!-- PARAMETER DEFINITION: DemDataElementUsePort -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:9cf1ca4d-69eb-4ba4-bfbc-6e3c027526d2">
															<SHORT-NAME>DemDataElementUsePort</SHORT-NAME>
															<DESC>
																<L-2 L="EN">If the parameter is set to True, a R-Port is generated, to obtain the data element (interface DataServices_&lt;SyncDataElement&gt;).</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">If the parameter is set to False, the information is obtained by C function-call on another BSW module specified by the parameter DemDataElementReadFnc.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: DemExternalSRDataElementClass -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:8e6c8d7e-2a1f-44a9-a5cf-728b1478f58d">
													<SHORT-NAME>DemExternalSRDataElementClass</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container contains the configuration (parameters) for an external sender/receiver based data element class. It defines, how the Dem can obtain the value of the data element from a SW-C, by using a sender/receiver port.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: DemDataElementDataSize -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:9d166994-cc93-44c6-9fa0-52f7bc28e02b">
															<SHORT-NAME>DemDataElementDataSize</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines the size of the data element in bits.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: DemDataElementDataType -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:25afee69-0327-4666-8baf-77218bb4a807">
															<SHORT-NAME>DemDataElementDataType</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Provide the implementation data type of data belonging to a external data.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:477413e0-87f1-8e8c-4eae-2569ffcd26bf">
																	<SHORT-NAME>BOOLEAN</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:5add6db0-af2c-8e7e-4756-c515411abda3">
																	<SHORT-NAME>SINT16</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:64a03839-7df7-88d5-4c3f-6a25c74d60f9">
																	<SHORT-NAME>SINT32</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:be74f199-1a04-86a7-4be9-0113923d1360">
																	<SHORT-NAME>SINT8</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:6563aa07-63e7-876a-496e-e29263b0b0ec">
																	<SHORT-NAME>UINT16</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:e1797f16-382f-9058-28ff-0863b0f58d0f">
																	<SHORT-NAME>UINT32</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:b0a0f5b5-750e-891e-48c2-f609c9a7d7f8">
																	<SHORT-NAME>UINT8</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: DemDataElementEndianness -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:ccdad70a-e417-49cb-b2f5-ba55fd055f02">
															<SHORT-NAME>DemDataElementEndianness</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines the endianness of the data belonging to an external data.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">If no DemDataElementEndianness is defined the value of DemDataElementDefaultEndianness is applicable.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:19b0fdc5-dade-85ed-3026-45ced58f97b8">
																	<SHORT-NAME>BIG_ENDIAN</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:56fb8c1c-c5b7-921a-5c18-8f00cfd76856">
																	<SHORT-NAME>LITTLE_ENDIAN</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:d8e767bc-814b-93a4-442e-3be4f78c30a2">
																	<SHORT-NAME>OPAQUE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
													</PARAMETERS>
													<SUB-CONTAINERS>
														<!-- Container Definition: DemDiagnosisScaling -->
														<ECUC-CHOICE-CONTAINER-DEF UUID="ECUC:1113d69e-4b70-4756-bea7-2f74de625fcf">
															<SHORT-NAME>DemDiagnosisScaling</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This container contains the configuration (parameters) of an alternative Diagnosis Representation. Out if this the scaling between Diagnosis and ECU internal representation and vice versa can be calculated.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<CHOICES>
																<!-- Container Definition: DemAlternativeDataInterface -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:c494d1bc-6807-4116-868f-139fc9d4056c">
																	<SHORT-NAME>DemAlternativeDataInterface</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container contains the configuration (parameters) of an alternative Diagnosis Representation by the means of a VariableDataPrototoype in a DataInterface.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">Additionally a reference to PortInterfaceMapping can be defined which provide already the mapping rules between the VariableDataPrototoype in a DataInterface used by the software component (DemSRDataElementClass) and the intended Diagnosis Representation defined by DemExternalSRDataElementClass.</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Foreign Reference Definition: DemDataElement -->
																		<ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:a976f624-cf39-4827-bd93-dee061b141ee">
																			<SHORT-NAME>DemDataElement</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Alternative Diagnosis Representation for the data defined by the means of a VariableDataPrototoype in a DataInterface.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-TYPE>VARIABLE-DATA-PROTOTYPE</DESTINATION-TYPE>
																		</ECUC-FOREIGN-REFERENCE-DEF>
																		<!-- Foreign Reference Definition: DemPortInterfaceMapping -->
																		<ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:2fa38569-4bc8-4f8a-be68-d40ed21fea34">
																			<SHORT-NAME>DemPortInterfaceMapping</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Optional reference to PortInterfaceMapping which defines the mapping rules.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-TYPE>PORT-INTERFACE-MAPPING</DESTINATION-TYPE>
																		</ECUC-FOREIGN-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: DemAlternativeDataProps -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:3b554f3d-00a7-4269-b499-bed18222a6e6">
																	<SHORT-NAME>DemAlternativeDataProps</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container contains the configuration (parameters) of an alternative Diagnosis Representation by the means of ECU configuration parameters.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">The physical unit of the alternative data representation is defined by the DataPrototype referenced by DemSRDataElementClass.

                                                                Additionally the definition of a text table mapping can be a defined for DemDataTypeCategory TEXTTABLE and SCALE_LINEAR_AND_TEXTTABLE</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: DemDataTypeCategory -->
																		<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:4583a595-9cec-4832-a50a-8a7f8adb04c2">
																			<SHORT-NAME>DemDataTypeCategory</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Data category of the alternative Diagnosis Representation.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<LITERALS>
																				<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:0a98a662-7d2d-84d7-6952-5bebe02e4a27">
																					<SHORT-NAME>SCALE_LINEAR_AND_TEXTTABLE</SHORT-NAME>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																				</ECUC-ENUMERATION-LITERAL-DEF>
																				<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:2d18cb6f-7cf5-888d-3b85-f82e4d0989b3">
																					<SHORT-NAME>TEXTTABLE</SHORT-NAME>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																				</ECUC-ENUMERATION-LITERAL-DEF>
																			</LITERALS>
																		</ECUC-ENUMERATION-PARAM-DEF>
																	</PARAMETERS>
																	<SUB-CONTAINERS>
																		<!-- Container Definition: DemLinearScale -->
																		<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:3543b2d3-c856-4976-815e-fd332f298fe3">
																			<SHORT-NAME>DemLinearScale</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This container contains the configuration (parameters) of an linear scale of the alternative Diagnosis Representation.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																			<PARAMETERS>
																				<!-- PARAMETER DEFINITION: DemDiagnosisRepresentationDataLowerRange -->
																				<ECUC-FLOAT-PARAM-DEF UUID="ECUC:33ff4aeb-6e0d-4f56-bb9d-d9cbcf0b8a82">
																					<SHORT-NAME>DemDiagnosisRepresentationDataLowerRange</SHORT-NAME>
																					<DESC>
																						<L-2 L="EN">Lower Range for this scale of the data in the alternative Diagnosis Representation.</L-2>
																					</DESC>
																					<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																					<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																					<IMPLEMENTATION-CONFIG-CLASSES>
																						<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																							<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																							<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																						</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																						<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																							<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																							<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																						</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					</IMPLEMENTATION-CONFIG-CLASSES>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																					<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																				</ECUC-FLOAT-PARAM-DEF>
																				<!-- PARAMETER DEFINITION: DemDiagnosisRepresentationDataOffset -->
																				<ECUC-FLOAT-PARAM-DEF UUID="ECUC:e493b157-9aa7-493c-a845-66d62947b562">
																					<SHORT-NAME>DemDiagnosisRepresentationDataOffset</SHORT-NAME>
																					<DESC>
																						<L-2 L="EN">Data offset of the alternative Diagnosis Representation for this scale.</L-2>
																					</DESC>
																					<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																					<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																					<IMPLEMENTATION-CONFIG-CLASSES>
																						<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																							<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																							<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																						</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																						<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																							<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																							<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																						</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					</IMPLEMENTATION-CONFIG-CLASSES>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																					<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																					<DEFAULT-VALUE>0</DEFAULT-VALUE>
																					<MAX>Inf</MAX>
																					<MIN>0</MIN>
																				</ECUC-FLOAT-PARAM-DEF>
																				<!-- PARAMETER DEFINITION: DemDiagnosisRepresentationDataResolution -->
																				<ECUC-FLOAT-PARAM-DEF UUID="ECUC:1110fe8f-72d4-452e-9e82-1cae64436142">
																					<SHORT-NAME>DemDiagnosisRepresentationDataResolution</SHORT-NAME>
																					<DESC>
																						<L-2 L="EN">Data resolution of the alternative Diagnosis Representation for this scale.</L-2>
																					</DESC>
																					<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																					<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																					<IMPLEMENTATION-CONFIG-CLASSES>
																						<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																							<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																							<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																						</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																						<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																							<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																							<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																						</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					</IMPLEMENTATION-CONFIG-CLASSES>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																					<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																					<MAX>Inf</MAX>
																					<MIN>0</MIN>
																				</ECUC-FLOAT-PARAM-DEF>
																				<!-- PARAMETER DEFINITION: DemDiagnosisRepresentationDataUpperRange -->
																				<ECUC-FLOAT-PARAM-DEF UUID="ECUC:b594bd37-222e-4a99-8d02-458c91b9a1e8">
																					<SHORT-NAME>DemDiagnosisRepresentationDataUpperRange</SHORT-NAME>
																					<DESC>
																						<L-2 L="EN">Upper Range for this scale of the data in the alternative Diagnosis Representation.</L-2>
																					</DESC>
																					<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																					<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																					<IMPLEMENTATION-CONFIG-CLASSES>
																						<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																							<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																							<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																						</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																						<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																							<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																							<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																						</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					</IMPLEMENTATION-CONFIG-CLASSES>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																					<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																				</ECUC-FLOAT-PARAM-DEF>
																			</PARAMETERS>
																		</ECUC-PARAM-CONF-CONTAINER-DEF>
																		<!-- Container Definition: DemTextTableMapping -->
																		<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e937ef32-d52e-83a0-35ca-28d3ad8b7723">
																			<SHORT-NAME>DemTextTableMapping</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This container contains the configuration (parameters) of the mapping a DataPrototype typed by AutosarDataType that refer to a CompuMethods of category TEXTTABLE or  SCALE_LINEAR_AND_TEXTTABLE.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">Each DemTextTableMapping defines a value pair which is used to map the ECU internal value (DemInternalDataValue) to the vale used in the diagnosis representation (DemDiagnosisRepresentationDataValue) and vice versa.

                                                                        The set of all DemTextTableMapping defines the whole mapping of an data.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
																			<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																			<PARAMETERS>
																				<!-- PARAMETER DEFINITION: DemDiagnosisRepresentationDataValue -->
																				<ECUC-INTEGER-PARAM-DEF UUID="ECUC:d9d82ed7-7e85-ce90-e005-f83904bb6b90">
																					<SHORT-NAME>DemDiagnosisRepresentationDataValue</SHORT-NAME>
																					<DESC>
																						<L-2 L="EN">The data value in the diagnosis representation.</L-2>
																					</DESC>
																					<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																					<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																					<IMPLEMENTATION-CONFIG-CLASSES>
																						<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																							<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																							<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																						</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																						<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																							<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																							<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																						</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					</IMPLEMENTATION-CONFIG-CLASSES>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																					<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																					<MAX>18446744073709551615</MAX>
																					<MIN>0</MIN>
																				</ECUC-INTEGER-PARAM-DEF>
																				<!-- PARAMETER DEFINITION: DemInternalDataValue -->
																				<ECUC-INTEGER-PARAM-DEF UUID="ECUC:642c2ec0-b782-d319-db3d-28112033f97e">
																					<SHORT-NAME>DemInternalDataValue</SHORT-NAME>
																					<DESC>
																						<L-2 L="EN">The ECU internal data value.</L-2>
																					</DESC>
																					<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																					<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																					<IMPLEMENTATION-CONFIG-CLASSES>
																						<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																							<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																							<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																						</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																						<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																							<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																							<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																						</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					</IMPLEMENTATION-CONFIG-CLASSES>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																					<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																					<MAX>18446744073709551615</MAX>
																					<MIN>0</MIN>
																				</ECUC-INTEGER-PARAM-DEF>
																			</PARAMETERS>
																		</ECUC-PARAM-CONF-CONTAINER-DEF>
																	</SUB-CONTAINERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: DemAlternativeDataType -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:f403e75d-f752-4e3d-aa66-4d086335d59f">
																	<SHORT-NAME>DemAlternativeDataType</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container contains the configuration (parameters) of an alternative Diagnosis Representation by the means of an ApplicationDataType.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">Additionally the definition of a text table mapping can be a defined for ApplicationDataTypes of category TEXTTABLE and SCALE_LINEAR_AND_TEXTTABLE.</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Foreign Reference Definition: DemApplicationDataType -->
																		<ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:438db0a6-b8c3-4e53-9e9f-fe7aa4530ed3">
																			<SHORT-NAME>DemApplicationDataType</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Alternative Diagnosis Representation for the data defined by the means of a ApplicationPrimitiveDataType of category VALUE or BOOLEAN.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-TYPE>APPLICATION-PRIMITIVE-DATA-TYPE</DESTINATION-TYPE>
																		</ECUC-FOREIGN-REFERENCE-DEF>
																	</REFERENCES>
																	<SUB-CONTAINERS>
																		<!-- Container Definition: DemTextTableMapping -->
																		<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:a1e68753-cbd9-8f74-2b96-b70a8e9ea5dc">
																			<SHORT-NAME>DemTextTableMapping</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This container contains the configuration (parameters) of the mapping a DataPrototype typed by AutosarDataType that refer to a CompuMethods of category TEXTTABLE or  SCALE_LINEAR_AND_TEXTTABLE.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">Each DemTextTableMapping defines a value pair which is used to map the ECU internal value (DemInternalDataValue) to the vale used in the diagnosis representation (DemDiagnosisRepresentationDataValue) and vice versa.

                                                                        The set of all DemTextTableMapping defines the whole mapping of an data.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
																			<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																			<PARAMETERS>
																				<!-- PARAMETER DEFINITION: DemDiagnosisRepresentationDataValue -->
																				<ECUC-INTEGER-PARAM-DEF UUID="ECUC:9286c6f8-7530-da64-d5d2-866fe5ce9a49">
																					<SHORT-NAME>DemDiagnosisRepresentationDataValue</SHORT-NAME>
																					<DESC>
																						<L-2 L="EN">The data value in the diagnosis representation.</L-2>
																					</DESC>
																					<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																					<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																					<IMPLEMENTATION-CONFIG-CLASSES>
																						<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																							<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																							<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																						</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																						<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																							<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																							<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																						</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					</IMPLEMENTATION-CONFIG-CLASSES>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																					<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																					<MAX>18446744073709551615</MAX>
																					<MIN>0</MIN>
																				</ECUC-INTEGER-PARAM-DEF>
																				<!-- PARAMETER DEFINITION: DemInternalDataValue -->
																				<ECUC-INTEGER-PARAM-DEF UUID="ECUC:1cdac6e1-ae2d-deed-d109-b64801472837">
																					<SHORT-NAME>DemInternalDataValue</SHORT-NAME>
																					<DESC>
																						<L-2 L="EN">The ECU internal data value.</L-2>
																					</DESC>
																					<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																					<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																					<IMPLEMENTATION-CONFIG-CLASSES>
																						<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																							<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																							<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																						</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																						<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																							<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																							<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																						</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					</IMPLEMENTATION-CONFIG-CLASSES>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																					<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																					<MAX>18446744073709551615</MAX>
																					<MIN>0</MIN>
																				</ECUC-INTEGER-PARAM-DEF>
																			</PARAMETERS>
																		</ECUC-PARAM-CONF-CONTAINER-DEF>
																	</SUB-CONTAINERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
															</CHOICES>
														</ECUC-CHOICE-CONTAINER-DEF>
														<!-- Container Definition: DemSRDataElementClass -->
														<ECUC-CHOICE-CONTAINER-DEF UUID="ECUC:cbfb18d6-1749-45e0-833d-34d9d30215d4">
															<SHORT-NAME>DemSRDataElementClass</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This container defines the source of data in a provided port which shall be read for a external data element</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">This container shall contain either one
                                                        DemSubElementInDataElementInstance
                                                        OR
                                                        DemDataElementInstance
                                                        OR
                                                        DemSubElementInImplDataElementInstance
                                                        reference.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<CHOICES>
																<!-- Container Definition: DemDataElementInstance -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:1da3b6b3-d899-4105-96f5-eee7ed1ed32f">
																	<SHORT-NAME>DemDataElementInstance</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Instance Reference to the primitive data in a port  where the data element is typed with an ApplicationPrimitveDataType or an ImplementationDataType.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Instance Reference Definition: DemDataElementInstanceRef -->
																		<ECUC-INSTANCE-REFERENCE-DEF UUID="ECUC:a420975a-5a17-48de-ad73-d7082549c962">
																			<SHORT-NAME>DemDataElementInstanceRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Instance Reference to the primitive data which shall be read or written.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">Supported are VariableDataPrototypes in SenderReceiverInterfaces and NvDataInterfaces and ParameterDataPrototypes in ParameterInterfaces (read only).
                                                                        This reference is applicable if the AutosarDataPrototype is typed with a ApplicationPrimitiveDataType of category VALUE or BOOLEAN or if the AutosarDataPrototype is typed with a ImplementationDataType of category VALUE or TYPE_REFERENCE that in turn boils down to VALUE</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-CONTEXT>ROOT-SW-COMPOSITION-PROTOTYPE SW-COMPONENT-PROTOTYPE PORT-PROTOTYPE</DESTINATION-CONTEXT>
																			<DESTINATION-TYPE>AUTOSAR-DATA-PROTOTYPE</DESTINATION-TYPE>
																		</ECUC-INSTANCE-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: DemSubElementInDataElementInstance -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:5afd11aa-5406-45d4-b56a-6ea45ba3d010">
																	<SHORT-NAME>DemSubElementInDataElementInstance</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Instance Reference to the primitve sub-element (at any level) of composite data in a port where the data element is typed with an ApplicationCompositeDataType.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Instance Reference Definition: DemSubElementInDataElementInstanceRef -->
																		<ECUC-INSTANCE-REFERENCE-DEF UUID="ECUC:6b0b0abb-b972-44f8-aae4-c32fcb78a982">
																			<SHORT-NAME>DemSubElementInDataElementInstanceRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Instance Reference to the primitve sub-element (at any level) of composite data in a port which shall be read or written.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">Supported are VariableDataPrototypes in SenderReceiverInterfaces and NvDataInterfaces and ParameterDataPrototypes in ParameterInterfaces (read only).
                                                                        This reference is applicable if the AutosarDataPrototype is typed with a ApplicationCompositeDataType.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-CONTEXT>ROOT-SW-COMPOSITION-PROTOTYPE SW-COMPONENT-PROTOTYPE PORT-PROTOTYPE AUTOSAR-DATA-PROTOTYPE APPLICATION-COMPOSITE-ELEMENT-DATA-PROTOTYPE*</DESTINATION-CONTEXT>
																			<DESTINATION-TYPE>AUTOSAR-DATA-PROTOTYPE</DESTINATION-TYPE>
																		</ECUC-INSTANCE-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: DemSubElementInImplDataElementInstance -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:c8bd6556-b5be-4c93-9be9-37a70a4d8424">
																	<SHORT-NAME>DemSubElementInImplDataElementInstance</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Instance Reference to the primitve sub-element (at any level) of composite data in a port where the data element is typed with an ImplementationDataType of category STRUCTURE or ARRAY.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Instance Reference Definition: DemSubElementInImplDataElementInstanceRef -->
																		<ECUC-INSTANCE-REFERENCE-DEF UUID="ECUC:ad862330-1bb1-40f6-816f-bf8b49e5c159">
																			<SHORT-NAME>DemSubElementInImplDataElementInstanceRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Instance Reference to the primitve sub-element (at any level) of composite data in a port which shall be read or written.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">Supported are VariableDataPrototypes in SenderReceiverInterfaces and NvDataInterfaces and ParameterDataPrototypes in ParameterInterfaces (read only).
                                                                        This reference is applicable if the AutosarDataPrototype is typed with a ImplementationDataType of category STRUCTURE or ARRAY.
                                                                        Please note that in case of ARRAY the index attribute in the target reference has to be set to select a single array element.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-CONTEXT>ROOT-SW-COMPOSITION-PROTOTYPE SW-COMPONENT-PROTOTYPE PORT-PROTOTYPE AUTOSAR-DATA-PROTOTYPE IMPLEMENTATION-DATA-TYPE-ELEMENT*</DESTINATION-CONTEXT>
																			<DESTINATION-TYPE>IMPLEMENTATION-DATA-TYPE-ELEMENT</DESTINATION-TYPE>
																		</ECUC-INSTANCE-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
															</CHOICES>
														</ECUC-CHOICE-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: DemInternalDataElementClass -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:3169b35f-f938-49a0-976c-91300ae5037d">
													<SHORT-NAME>DemInternalDataElementClass</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container contains the configuration (parameters) for an internal data element class.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: DemDataElementDataSize -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:2c592a33-d99b-45f2-9427-5f246a99524d">
															<SHORT-NAME>DemDataElementDataSize</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines the size of the data element in bytes.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: DemInternalDataElement -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:fe434fc3-1ebf-40e6-9b79-a20a65a9dcf8">
															<SHORT-NAME>DemInternalDataElement</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines the Dem-internal data value, which is mapped to the data element.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:7bbb5e80-0b9d-8a37-51c7-a453bd7319bc">
																	<SHORT-NAME>DEM_AGINGCTR_DOWNCNT</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:70356d9d-1e78-95a0-3b60-484f8c32512f">
																	<SHORT-NAME>DEM_AGINGCTR_UPCNT</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:5a1a45b9-8b40-8e3e-3108-0cd3d7279ab0">
																	<SHORT-NAME>DEM_CURRENT_FDC</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:e346b569-725d-8b34-4c46-bf0a2d6ecefd">
																	<SHORT-NAME>DEM_CYCLES_SINCE_FIRST_FAILED</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:19040cb0-12e7-9770-4b0f-71333aacea55">
																	<SHORT-NAME>DEM_CYCLES_SINCE_LAST_FAILED</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:df6fe1f7-d86c-914c-2506-895e7a08f821">
																	<SHORT-NAME>DEM_FAILED_CYCLES</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:de2ee53a-24e5-92ca-48d7-5ddc55563793">
																	<SHORT-NAME>DEM_MAX_FDC_DURING_CURRENT_CYCLE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:f7c97836-c929-90f5-293b-5a103e698510">
																	<SHORT-NAME>DEM_MAX_FDC_SINCE_LAST_CLEAR</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:632a7e19-b90f-8c52-3e6d-332a0beed12c">
																	<SHORT-NAME>DEM_OCCCTR</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:91fb9864-09bf-909b-2ddd-4e7792494e51">
																	<SHORT-NAME>DEM_OVFLIND</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:818b1f7f-a71f-9513-1862-b64f3ad159b2">
																	<SHORT-NAME>DEM_SIGNIFICANCE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</CHOICES>
										</ECUC-CHOICE-CONTAINER-DEF>
										<!-- Container Definition: DemDidClass -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e1cbc150-3dd5-4439-abf5-ccb4cde025c7">
											<SHORT-NAME>DemDidClass</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the configuration (parameters) for a data Id class. It is assembled out of one or several data elements.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DemDidIdentifier -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:6fb72160-e0f8-4592-831c-05d34fe8c839">
													<SHORT-NAME>DemDidIdentifier</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Identifier of the Data ID.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Choice Reference Definition: DemDidDataElementClassRef -->
												<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:30a0fb20-847a-43b2-ad30-0df10d7c2f68">
													<SHORT-NAME>DemDidDataElementClassRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This reference contains the link to a data element class.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REFS>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemInternalDataElementClass</DESTINATION-REF>
													</DESTINATION-REFS>
												</ECUC-CHOICE-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemEnableCondition -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:490f6445-7fad-4f11-9130-c78155a7fbe6">
											<SHORT-NAME>DemEnableCondition</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the configuration (parameters) for enable conditions.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DemEnableConditionId -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:8941da71-f206-4351-8887-40650a034533">
													<SHORT-NAME>DemEnableConditionId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Defines a unique enable condition Id.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This parameter should not be changeable by user, because the Id should be generated by Dem itself to prevent gaps and multiple use of an Id. The enable conditions should be sequentially ordered beginning with 0 and no gaps in between.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemEnableConditionStatus -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:7e66af51-7547-4ead-9cc8-9dd2c2f112fe">
													<SHORT-NAME>DemEnableConditionStatus</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Defines the initial status for enable or disable of acceptance of event reports of a diagnostic event.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">The value is the initialization after power up (before this condition is reported the first time). 
                                                true: acceptance of a diagnostic event enabled 
                                                false: acceptance of a diagnostic event disabled</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemEnableConditionGroup -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:32a02fa5-08ff-4668-b2a6-bcc8066eccd1">
											<SHORT-NAME>DemEnableConditionGroup</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the configuration (parameters) for enable condition groups.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<REFERENCES>
												<!-- Reference Definition: DemEnableConditionRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:2efef637-5844-4dfc-984b-a96a59d045f2">
													<SHORT-NAME>DemEnableConditionRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">References an enable condition.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC V1.0.0</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemEnableCondition</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemExtendedDataClass -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:9a005bc7-bfcc-476f-a2fa-1152a1793b12">
											<SHORT-NAME>DemExtendedDataClass</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This class contains the combinations of extended data records for an extended data class.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<REFERENCES>
												<!-- Reference Definition: DemExtendedDataRecordClassRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:76ed7707-7ef4-4a49-8502-1182d3997425">
													<SHORT-NAME>DemExtendedDataRecordClassRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This reference contains the link to an extended data class record.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>253</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemExtendedDataRecordClass</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemExtendedDataRecordClass -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:6821e454-6d75-4f8b-a6c1-c1e0fdc5f8d2">
											<SHORT-NAME>DemExtendedDataRecordClass</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the configuration (parameters) for an extended data record class.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">It is assembled out of one or several data elements.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>253</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DemExtendedDataRecordNumber -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:a403cf7b-c80e-4d4b-9e39-5805c8992866">
													<SHORT-NAME>DemExtendedDataRecordNumber</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This configuration parameter specifies an unique identifier for an extended data record.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">One or more extended data records can be assigned to one diagnostic event/DTC.

                                                0xFF and 0xFE are reserved by ISO (therefore the maximal value equals 253).</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>253</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemExtendedDataRecordTrigger -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:2c092a02-741b-4363-b677-051dfa118690">
													<SHORT-NAME>DemExtendedDataRecordTrigger</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Configures the primary trigger to allocate an event memory entry.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:4007958d-fb13-978d-6582-0ae401855972">
															<SHORT-NAME>DEM_EXTENDED_DATA_CAPTURE_ON_CONFIRMED</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:1e627406-0fe1-9e8e-4488-e5d84133ca88">
															<SHORT-NAME>DEM_EXTENDED_DATA_CAPTURE_ON_FDC_THRESHOLD</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:3c753bf8-c9b0-9224-39f7-d5b761408df2">
															<SHORT-NAME>DEM_EXTENDED_DATA_CAPTURE_ON_PENDING</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:35ba30f2-a7fc-9da2-45e6-0afcd20b2a0a">
															<SHORT-NAME>DEM_EXTENDED_DATA_CAPTURE_ON_TEST_FAILED</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemExtendedDataRecordUpdate -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:221029aa-5a23-40f4-b9e0-b01c16eb1ecb">
													<SHORT-NAME>DemExtendedDataRecordUpdate</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This extended data record is captured if the configured trigger condition in &quot;DemExtendedDataRecordTrigger&quot; is fulfilled.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:f5d11092-ac01-97db-579b-6138807cb61f">
															<SHORT-NAME>DEM_UPDATE_RECORD_NO</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:88c4c861-7dc7-9965-4b19-d8cc4db6109a">
															<SHORT-NAME>DEM_UPDATE_RECORD_YES</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Choice Reference Definition: DemDataElementClassRef -->
												<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:97567e6b-f870-4e8a-ba55-b05cfd081266">
													<SHORT-NAME>DemDataElementClassRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This reference contains the link to a data element class.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REFS>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemInternalDataElementClass</DESTINATION-REF>
													</DESTINATION-REFS>
												</ECUC-CHOICE-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemFreezeFrameClass -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:0dd242a0-90fd-4d71-9506-bcab60321bf8">
											<SHORT-NAME>DemFreezeFrameClass</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the combinations of DIDs for a non OBD relevant freeze frame class.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<REFERENCES>
												<!-- Reference Definition: DemDidClassRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:f641607d-0dcf-4d67-80a5-3fddd327023a">
													<SHORT-NAME>DemDidClassRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">For OBD relevant data</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">Multiple PIDs can be relevant per freeze frame.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDidClass</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemFreezeFrameRecNumClass -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e2e5fbe0-d229-4de6-a9db-3c1d6cf37722">
											<SHORT-NAME>DemFreezeFrameRecNumClass</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains a list of dedicated, different freeze frame record numbers assigned to an event. The order of record numbers in this list is assigned to the chronological order of the according freeze frame records.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">dependency: DemTypeOfFreezeFrameRecordNumeration = DEM_FF_RECNUM_CONFIGURED</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<REFERENCES>
												<!-- Reference Definition: DemFreezeFrameRecordClassRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:0c808701-e72e-45ab-9efe-233c0ea65198">
													<SHORT-NAME>DemFreezeFrameRecordClassRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter references record number(s) for a freeze frame record.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>254</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemFreezeFrameRecordClass</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemFreezeFrameRecordClass -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:243fd8af-15d0-4af1-99db-c610b201491b">
											<SHORT-NAME>DemFreezeFrameRecordClass</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains a list of dedicated, different freeze frame record numbers.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DemFreezeFrameRecordNumber -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:8577a18a-a211-451a-86ba-332c3c551648">
													<SHORT-NAME>DemFreezeFrameRecordNumber</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines a record number for a freeze frame record. This record number is unique per freeze frame record number class.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">The range of this value is defined by ISO 14229-1 (0x01 .. 0xFE).</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>254</MAX>
													<MIN>1</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemFreezeFrameRecordTrigger -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:4386a02e-28bd-4dc9-b99a-1829f34a1792">
													<SHORT-NAME>DemFreezeFrameRecordTrigger</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Configures the primary trigger to allocate an event memory entry.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:67315ee2-ed78-95e2-3c2c-ee789f42fa38">
															<SHORT-NAME>DEM_FREEZE_FRAME_CAPTURE_ON_CONFIRMED</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:6dddc0a2-0afa-8d86-2f17-77d0a45245a1">
															<SHORT-NAME>DEM_FREEZE_FRAME_CAPTURE_ON_FDC_THRESHOLD</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:f7339c37-a835-8e5e-2a04-90c1adb2aafb">
															<SHORT-NAME>DEM_FREEZE_FRAME_CAPTURE_ON_PENDING</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:1f7c4837-30ba-990f-45b7-7295088f9772">
															<SHORT-NAME>DEM_FREEZE_FRAME_CAPTURE_ON_TEST_FAILED</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemFreezeFrameRecordUpdate -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:b96da489-b522-43af-8733-2894c87542da">
													<SHORT-NAME>DemFreezeFrameRecordUpdate</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines the case, when the freeze frame record is stored/updated.</L-2>
														<L-2 L="EN">Tags: atp.Status=obsolete, atp.StatusRevisionBegin=4.1.1</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:fe0b2c3a-0b75-9708-2ed6-2528926cc701">
															<SHORT-NAME>DEM_UPDATE_RECORD_NO</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:*************-8ff5-23ff-2271ec035b17">
															<SHORT-NAME>DEM_UPDATE_RECORD_YES</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemGeneralJ1939 -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:b8f84dc2-a70a-4523-8c77-0cef2f837fbd">
											<SHORT-NAME>DemGeneralJ1939</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the general J1939-specific configuration (parameters) of the Dem module.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">If the container exists the J1939 support is enabled.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DemJ1939ClearDtcSupport -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:006b1cb5-b415-4364-a2de-9716bb0336eb">
													<SHORT-NAME>DemJ1939ClearDtcSupport</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This configuration switch defines whether clearing J1939 DTCs (DM3 und DM11) is supported or not.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This switches on and off the API Dem_J1939DcmClearDTC.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemJ1939Dm31Support -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:12b38ba3-011d-436c-bc42-997b08968c93">
													<SHORT-NAME>DemJ1939Dm31Support</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This configuration switch defines whether J1939 DM31 is supported or not.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This switches on and off the APIs Dem_J1939DcmFirstDTCwithLampStatus and Dem_J1939DcmGetNextDTCwithLampStatus.
                                                .</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemJ1939ExpandedFreezeFrameSupport -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:c3285bd5-b3d6-48de-a1a2-9d1e46424e94">
													<SHORT-NAME>DemJ1939ExpandedFreezeFrameSupport</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This configuration switch defines whether J1939 expanded freeze frames are supported or not.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This switches on and off the APIs Dem_J1939DcmSetFreezeFrameFilter, Dem_J1939DcmGetNextFreezeFrame and Dem_J1939DcmGetNextSPNInFreezeFrame.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemJ1939FreezeFrameSupport -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:6cbd5bf9-7d09-474b-93ac-870ea2b2c62d">
													<SHORT-NAME>DemJ1939FreezeFrameSupport</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This configuration switch defines whether J1939 freeze frames are supported or not.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This switches on and off the APIs Dem_J1939DcmSetFreezeFrameFilter and Dem_J1939DcmGetNextFreezeFrame.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemJ1939RatioSupport -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:60ea9ba9-0c2d-4cb4-8a29-0ef5482137fe">
													<SHORT-NAME>DemJ1939RatioSupport</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This configuration switch defines whether J1939 performance ratios are supported or not.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This switches on and off the APIs Dem_J1939DcmSetRatioFilter and Dem_J1939DcmGetNextFilteredRatio.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemJ1939Readiness1Support -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:7a872ae9-095f-42ca-8442-ab9448998fab">
													<SHORT-NAME>DemJ1939Readiness1Support</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This configuration switch defines whether J1939 diagnostic readiness 1 is supported or not.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This switches on and off the API Dem_J1939DcmReadDiagnosticReadiness1.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemJ1939Readiness2Support -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:081b4af4-1e48-412f-ac0b-bb549d8cdd64">
													<SHORT-NAME>DemJ1939Readiness2Support</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This configuration switch defines whether J1939 diagnostic readiness 2 is supported or not.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This switches on and off the API Dem_J1939DcmReadDiagnosticReadiness2.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemJ1939Readiness3Support -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:6f94320e-b403-48e1-9a7c-3c3c34518b32">
													<SHORT-NAME>DemJ1939Readiness3Support</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This configuration switch defines whether J1939 diagnostic readiness 3 is supported or not.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This switches on and off the API Dem_J1939DcmReadDiagnosticReadiness3.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemJ1939ReadingDtcSupport -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:3594bc2e-8726-44f8-b638-e5be99388147">
													<SHORT-NAME>DemJ1939ReadingDtcSupport</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This configuration switch defines whether J1939 DTC readout is supported or not.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This switches on and off the APIs Dem_J1939DcmSetDTCFilter, Dem_J1939DcmGetNumberOfFilteredDTC and Dem_J1939DcmGetNextFilteredDTC.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: DemAmberWarningLampIndicatorRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:cedf27e6-b9f5-4c36-800f-eab98917ec18">
													<SHORT-NAME>DemAmberWarningLampIndicatorRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines the indicator representing the AmberWarningLamp . This parameter may be used for ECUs supporting J1939.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemIndicator</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<!-- Reference Definition: DemProtectLampIndicatorRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:76cac18f-6be9-4b81-866c-5150aa610a5c">
													<SHORT-NAME>DemProtectLampIndicatorRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines the indicator representing the ProtectLamp. This parameter may be used for ECUs supporting J1939.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemIndicator</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<!-- Reference Definition: DemRedStopLampIndicatorRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:7f4fc974-67c0-418f-b4a7-d09d62b6c85e">
													<SHORT-NAME>DemRedStopLampIndicatorRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines the indicator representing the RedStopLamp. This parameter may be used for ECUs supporting J1939.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemIndicator</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
											<SUB-CONTAINERS>
												<!-- Container Definition: DemCallbackJ1939DTCStatusChanged -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:0d124437-dcbb-45fc-b7fb-75077766d0bf">
													<SHORT-NAME>DemCallbackJ1939DTCStatusChanged</SHORT-NAME>
													<DESC>
														<L-2 L="EN">The presence of this container indicates, that the Dem has access to a &quot;DTCStatusChanged&quot; callback, which the Dem will call to notify other components about the change in the status of a DTC.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">In case there is a DemCallbackDTCStatusChangedFnc, this parameter defines the name of the function that the Dem will call.

                                                In case there is no DemCallbackDTCStatusChangedFnc, the Dem will have an R-Port requiring the interface CallbackDTCStatusChanged whose name is generated by using the unique callback-prefix followed by the event name.

                                                Status change notifications are supported for DTCs in primary memory only.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: DemCallbackDTCStatusChangedFnc -->
														<ECUC-FUNCTION-NAME-DEF UUID="ECUC:51d9c5c0-bb7f-4566-be08-1f2181bda434">
															<SHORT-NAME>DemCallbackDTCStatusChangedFnc</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Function name of prototype &quot;DTCStatusChanged&quot;.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">Note: If the parameter DemTriggerDcmReports is enabled, this parameter shall not be &quot;Dcm_DemTriggerOnDTCStatus&quot;.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<ECUC-FUNCTION-NAME-DEF-VARIANTS>
																<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
															</ECUC-FUNCTION-NAME-DEF-VARIANTS>
														</ECUC-FUNCTION-NAME-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: DemJ1939FreezeFrameClass -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:86700f99-a606-494e-ae36-cee1fcd4331d">
													<SHORT-NAME>DemJ1939FreezeFrameClass</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container contains the combinations of SPNs s for a J1939 relevant freeze frame.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<REFERENCES>
														<!-- Reference Definition: DemSPNClassRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:eaead167-1f62-4a5c-bc75-25f40d834faa">
															<SHORT-NAME>DemSPNClassRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to an SPN. This reference defines requiresIndex = true since it represents a ordered list of references where the order describes the order of single SPNs in the J1939 Freeze Frame.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemGeneralJ1939/DemSPNClass</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: DemSPNClass -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:d77f4ee8-fef8-4b89-a725-1253893ad24a">
													<SHORT-NAME>DemSPNClass</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container contains the configuration (parameters) for a SPN.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: DemSPNId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:2716b2e8-64bd-491e-8fad-735bc797fcd2">
															<SHORT-NAME>DemSPNId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Suspect parameter number</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>524287</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Choice Reference Definition: DemSPNDataElementClassRef -->
														<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:1a039457-9359-433b-8fec-561253267c68">
															<SHORT-NAME>DemSPNDataElementClassRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This reference contains the link to a data element class.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REFS>
																<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</DESTINATION-REF>
																<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</DESTINATION-REF>
																<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemInternalDataElementClass</DESTINATION-REF>
															</DESTINATION-REFS>
														</ECUC-CHOICE-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemGeneralOBD -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:dc39405f-19df-4342-bf96-7955053c295f">
											<SHORT-NAME>DemGeneralOBD</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the general OBD-specific configuration (parameters) of the Dem module.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DemOBDCentralizedPID21Handling -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:dcb3a818-296a-471f-944a-382dbe24efa3">
													<SHORT-NAME>DemOBDCentralizedPID21Handling</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Switch to enable the centralized handling of PID $21.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">true: centralized handling of PID $21 enabled 

                                                false: centralized handling of PID $21 disabled</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemOBDCentralizedPID31Handling -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:fd5a0bb6-54d2-4124-85b4-316cc364f4f3">
													<SHORT-NAME>DemOBDCentralizedPID31Handling</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Switch to enable the centralized handling of PID $31.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">true: centralized handling of PID $31 enabled 

                                                false: centralized handling of PID $31 disabled</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemOBDCompliancy -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:cfcbf5d3-1309-4a84-ac7c-247c321b34f1">
													<SHORT-NAME>DemOBDCompliancy</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Configuration value to define  the appropriate value to PID$1C &quot;OBD requirements to which vehicle or engine is certified.&quot; according to the respective standards, e.g. OBD, OBDII, JOBD etc.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">Notice as well J1979 or the &quot;DiagnosticReadiness 1&quot; DM5 message of J1939-73</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemOBDDestinationOfEvents -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:6fe71c89-ac19-4ddb-91b7-39582c4f59c6">
													<SHORT-NAME>DemOBDDestinationOfEvents</SHORT-NAME>
													<DESC>
														<L-2 L="EN">The destination of events assigns where the OBD events shall be stored.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:37216445-dba2-8774-756e-6d0aa4cb0ec7">
															<SHORT-NAME>DEM_DTC_ORIGIN_PRIMARY_MEMORY</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:9384c630-c690-90bf-4247-be024409cef5">
															<SHORT-NAME>DEM_DTC_ORIGIN_SECONDARY_MEMORY</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemOBDEventDisplacement -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:2b92632d-3dbf-4fc6-b488-3786c582eb80">
													<SHORT-NAME>DemOBDEventDisplacement</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Activate/Deactivate a different displacement behavior for OBD events.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">OBD events with special Conditions (e.g. Pending, MIL_On…) shall not be displaced.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Choice Reference Definition: DemOBDInputAcceleratorPedalInformation -->
												<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:4e99443b-59a7-4f36-a2da-6bd7fca7143a">
													<SHORT-NAME>DemOBDInputAcceleratorPedalInformation</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Input variable for the accelerator padal information, which is assigned to a specific data element used as interface for the Dem-internal PID calculations.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REFS>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</DESTINATION-REF>
													</DESTINATION-REFS>
												</ECUC-CHOICE-REFERENCE-DEF>
												<!-- Choice Reference Definition: DemOBDInputAmbientPressure -->
												<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:e7436253-fc7b-40af-856e-3843edd48fc4">
													<SHORT-NAME>DemOBDInputAmbientPressure</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Input variable for the ambient pressure, which is assigned to a specific data element used as interface for the Dem-internal PID calculations.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REFS>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</DESTINATION-REF>
													</DESTINATION-REFS>
												</ECUC-CHOICE-REFERENCE-DEF>
												<!-- Choice Reference Definition: DemOBDInputAmbientTemperature -->
												<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:d21feba4-8962-41c4-b8a3-14bb9ee2b13e">
													<SHORT-NAME>DemOBDInputAmbientTemperature</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Input variable for the ambient temperature, which is assigned to a specific data element used as interface for the Dem-internal PID calculations.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REFS>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</DESTINATION-REF>
													</DESTINATION-REFS>
												</ECUC-CHOICE-REFERENCE-DEF>
												<!-- Choice Reference Definition: DemOBDInputDistanceInformation -->
												<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:e4405450-1973-437f-9a72-6b4f3c1d6f30">
													<SHORT-NAME>DemOBDInputDistanceInformation</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Input variable for the distance information, which is assigned to a specific data element used as interface for the Dem-internal PID calculations.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REFS>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</DESTINATION-REF>
													</DESTINATION-REFS>
												</ECUC-CHOICE-REFERENCE-DEF>
												<!-- Choice Reference Definition: DemOBDInputEngineSpeed -->
												<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:3c88edb4-d1bc-439d-a059-ae046bb4da25">
													<SHORT-NAME>DemOBDInputEngineSpeed</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Input variable for the engine speed, which is assigned to a specific data element used as interface for the Dem-internal PID calculations.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REFS>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</DESTINATION-REF>
													</DESTINATION-REFS>
												</ECUC-CHOICE-REFERENCE-DEF>
												<!-- Choice Reference Definition: DemOBDInputEngineTemperature -->
												<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:a77d2964-9355-4869-83f4-2f929f570dec">
													<SHORT-NAME>DemOBDInputEngineTemperature</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Input variable for the engine temperature, which is assigned to a specific data element used as interface for the Dem-internal PID calculations.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REFS>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</DESTINATION-REF>
													</DESTINATION-REFS>
												</ECUC-CHOICE-REFERENCE-DEF>
												<!-- Choice Reference Definition: DemOBDInputProgrammingEvent -->
												<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:855ef6a6-5c64-4498-b6e8-a8f2b2aacc5d">
													<SHORT-NAME>DemOBDInputProgrammingEvent</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Input variable for the programming event, which is assigned to a specific data element used as interface for the Dem-internal PID calculations.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REFS>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</DESTINATION-REF>
													</DESTINATION-REFS>
												</ECUC-CHOICE-REFERENCE-DEF>
												<!-- Choice Reference Definition: DemOBDInputVehicleSpeed -->
												<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:5256078f-f457-473b-945f-24f415eb398e">
													<SHORT-NAME>DemOBDInputVehicleSpeed</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Input variable for the vehicle speed, which is assigned to a specific data element used as interface for the Dem-internal PID calculations.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REFS>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</DESTINATION-REF>
													</DESTINATION-REFS>
												</ECUC-CHOICE-REFERENCE-DEF>
												<!-- Choice Reference Definition: DemOBDTimeSinceEngineStart -->
												<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:2edc7ce5-2f3c-4ce3-a381-1feb57738843">
													<SHORT-NAME>DemOBDTimeSinceEngineStart</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Input variable for the Time Since Engine Start information, which is assigned to a specific data element.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REFS>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemInternalDataElementClass</DESTINATION-REF>
													</DESTINATION-REFS>
												</ECUC-CHOICE-REFERENCE-DEF>
											</REFERENCES>
											<SUB-CONTAINERS>
												<!-- Container Definition: DemCallbackOBDDTCStatusChanged -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:bb68bdbd-d314-4379-a7f5-797f07f6fab0">
													<SHORT-NAME>DemCallbackOBDDTCStatusChanged</SHORT-NAME>
													<DESC>
														<L-2 L="EN">The presence of this container indicates, that the Dem has access to a &quot;DTCStatusChanged&quot; callback, which the Dem will call to notify other components about the change in the status of a DTC.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">In case there is a DemCallbackDTCStatusChangedFnc, this parameter defines the name of the function that the Dem will call.

                                                In case there is no DemCallbackDTCStatusChangedFnc, the Dem will have an R-Port requiring the interface CallbackDTCStatusChanged whose name is generated by using the unique callback-prefix followed by the event name.

                                                Status change notifications are supported for DTCs in primary memory only.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: DemCallbackDTCStatusChangedFnc -->
														<ECUC-FUNCTION-NAME-DEF UUID="ECUC:d8e3c461-3103-4e19-8243-a9ad31b83d65">
															<SHORT-NAME>DemCallbackDTCStatusChangedFnc</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Function name of prototype &quot;DTCStatusChanged&quot;.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">Note: If the parameter DemTriggerDcmReports is enabled, this parameter shall not be &quot;Dcm_DemTriggerOnDTCStatus&quot;.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<ECUC-FUNCTION-NAME-DEF-VARIANTS>
																<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
															</ECUC-FUNCTION-NAME-DEF-VARIANTS>
														</ECUC-FUNCTION-NAME-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemGroupOfDTC -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:c89875f9-acf6-428b-9a00-4c3f2c8afe19">
											<SHORT-NAME>DemGroupOfDTC</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the configuration (parameters) for DTC groups.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DemGroupDTCs -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:2e622411-01bb-411b-90b1-6c0947dbad48">
													<SHORT-NAME>DemGroupDTCs</SHORT-NAME>
													<DESC>
														<L-2 L="EN">DTC value of the selected group of DTC</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">(Range: 3 byte, 0x000000 is only available for the emission-related DTC group, 0xFFFFFF is reserved for &apos;all DTCs&apos;, according to ISO14229-1 Annex D.1,)
                                                The DTC group &apos;all DTCs&apos; is always available and will not be configured.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<MAX>16777214</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemIndicator -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:3d9ac4b7-4eb9-4ac6-b433-30b5b6fa9fe9">
											<SHORT-NAME>DemIndicator</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the configuration (parameters) for Indicators.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Note that this container definition does not explicitly define a symbolic name parameter. Instead, the short name of the container will be used in the Ecu Configuration Description to specify the symbolic name of the INDICATOR_NAME.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DemIndicatorID -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:7b5c78d8-ffb6-478c-a521-a8ebfeb21b00">
													<SHORT-NAME>DemIndicatorID</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Unique identifier of an indicator.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemNvRamBlockId -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:3ab8406f-9f71-44c9-8200-0c9baf37a90a">
											<SHORT-NAME>DemNvRamBlockId</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the configuration (parameters) for a non-volatile memory block, which is used from the Dem. If no permanent storage of event memory entries is required, no block needs to be configured.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The number of blocks which are necessary depends on the implementation and configuration
                                        (e.g. number of used event memories) of the Dem module.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<REFERENCES>
												<!-- Symbolic Name Reference Definition: DemNvRamBlockIdRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:3dfa5a10-bb40-4111-9e8d-653298123dfc">
													<SHORT-NAME>DemNvRamBlockIdRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This reference contains the link to a non-volatile memory block. For post build time configurations worst case scenario shall be used.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC V1.0.0</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/NvM/NvMBlockDescriptor</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemOperationCycle -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:edf2c803-842d-44ee-91cf-76c67f209935">
											<SHORT-NAME>DemOperationCycle</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Note that this container definition does not explicitly define a symbolic name parameter. Instead, the short name of the container will be used in the Ecu Configuration Description to specify the symbolic name of the operation cycle name.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>256</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DemOperationCycleAutomaticEnd -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:bd53f3b5-1f03-41df-b65a-5a892484e08c">
													<SHORT-NAME>DemOperationCycleAutomaticEnd</SHORT-NAME>
													<DESC>
														<L-2 L="EN">If DemOperationCycleAutomaticEnd is configured to TRUE, Dem shall automatically end the driving cycle at either Dem_Shutdown() or Dem_Init().</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemOperationCycleAutostart -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:99ec8223-f61d-498a-b636-fdd2b87159a9">
													<SHORT-NAME>DemOperationCycleAutostart</SHORT-NAME>
													<DESC>
														<L-2 L="EN">The autostart property defines if the operation cycles is automatically (re-)started during Dem_PreInit.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemOperationCycleType -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:7544f8ce-f35f-4335-b2da-a0144b7c0dce">
													<SHORT-NAME>DemOperationCycleType</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Operation cycles types for the Dem to be supported by cycle-state APIs.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">Further cycle types can be specified as part of the Dem delivery.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:6e292eb5-3d5d-87b4-4b52-60fbe29ba835">
															<SHORT-NAME>DEM_OPCYC_IGNITION</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:65e7ef08-9f0d-8582-29dd-f2fd39507d57">
															<SHORT-NAME>DEM_OPCYC_OBD_DCY</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:ea659649-4aeb-8e37-46e0-e244c89dc5c0">
															<SHORT-NAME>DEM_OPCYC_OTHER</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:4b0828e8-3017-86e5-420e-78575f662c4a">
															<SHORT-NAME>DEM_OPCYC_POWER</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:2b07258a-84a6-9321-1a92-84d6c2bad98c">
															<SHORT-NAME>DEM_OPCYC_TIME</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:f2dcb43b-0499-93d2-2462-56a9159d53df">
															<SHORT-NAME>DEM_OPCYC_WARMUP</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemRatio -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:bd1f9289-8c44-4164-9c8c-385ab2d911b4">
											<SHORT-NAME>DemRatio</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the OBD-specific in-use-monitor performance ratio configuration.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">It is related to a specific event, a FID, and an IUMPR group.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>65535</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DemIUMPRDenGroup -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:f97b47cb-864b-4aef-beae-173913703bd0">
													<SHORT-NAME>DemIUMPRDenGroup</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter specifies the assigned denominator type which is applied in addition to the General Denominator conditions.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:8a3c5b87-2190-8451-53f5-c8b1bce4bf6d">
															<SHORT-NAME>DEM_IUMPR_DEN_500MILL</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:d71bc231-c82a-8f89-1d4f-9be894202d74">
															<SHORT-NAME>DEM_IUMPR_DEN_COLDSTART</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:5a72e4ff-a961-87c9-20e8-d5d9df1219e5">
															<SHORT-NAME>DEM_IUMPR_DEN_EVAP</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:889ca1b8-f9d2-88a6-2e92-6f22928fb482">
															<SHORT-NAME>DEM_IUMPR_DEN_NONE</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:a81f2f64-ad19-8776-5bdc-cf0f76b7ee25">
															<SHORT-NAME>DEM_IUMPR_DEN_PHYS_API</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemIUMPRGroup -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:6210985c-4380-4b4b-adba-d12b8c40365f">
													<SHORT-NAME>DemIUMPRGroup</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter specifies the assigned IUMPR group of the ratio Id.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:*************-8831-41f4-1e916afea3db">
															<SHORT-NAME>DEM_IUMPR_BOOSTPRS</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:b4a2696c-6648-9002-4ddd-ab9a6f7dd32b">
															<SHORT-NAME>DEM_IUMPR_CAT1</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:11e67916-8de9-8f91-3f4c-3739b0a0aa1d">
															<SHORT-NAME>DEM_IUMPR_CAT2</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:52e0f73d-5b77-831d-505d-7ef9430937b3">
															<SHORT-NAME>DEM_IUMPR_EGR</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:1bd972ef-3a8c-8d4d-3b3f-8e12c1d19469">
															<SHORT-NAME>DEM_IUMPR_EGSENSOR</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:5131d5aa-12dd-868b-522c-023d5fb6c98e">
															<SHORT-NAME>DEM_IUMPR_EVAP</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:997a1a87-345e-8e33-5638-4e8aa01c4442">
															<SHORT-NAME>DEM_IUMPR_FLSYS</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:371a9c6a-e20e-8659-1f82-19319c3ec4e2">
															<SHORT-NAME>DEM_IUMPR_NMHCCAT</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:3b4080d3-10bb-8eb0-2c4b-ad179cb09901">
															<SHORT-NAME>DEM_IUMPR_NOXADSORB</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:e04a403c-9de1-8dab-5baa-829e0de2cada">
															<SHORT-NAME>DEM_IUMPR_NOXCAT</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:891e1303-6403-87e0-3670-d7bf72fdfd54">
															<SHORT-NAME>DEM_IUMPR_OXS1</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:7e28168f-5dbf-84db-50bd-b0d5e9b09001">
															<SHORT-NAME>DEM_IUMPR_OXS2</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:36a3812a-6a1a-860f-45c0-0e3fc34fdfd6">
															<SHORT-NAME>DEM_IUMPR_PMFILTER</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:6ca2f5fd-e5ff-81c8-56d1-d7c85ab882a8">
															<SHORT-NAME>DEM_IUMPR_PRIVATE</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:8d3951cd-13ab-8172-4bc0-71bff28f2a4e">
															<SHORT-NAME>DEM_IUMPR_SAIR</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:efacbc9b-b0a5-8556-5a7a-fe1cbdb5d825">
															<SHORT-NAME>DEM_IUMPR_SECOXS1</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:afcd92ce-aab1-8c88-4052-f723fa1059e2">
															<SHORT-NAME>DEM_IUMPR_SECOXS2</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemRatioId -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:25cf98a3-6dce-490d-a2f1-8a73b51507ad">
													<SHORT-NAME>DemRatioId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Defines a unique ratio Id.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This parameter should not be changeable by user, because the Id should be generated by Dem itself to prevent gaps and multiple use of an Id. The ratio Ids should be sequentially ordered beginning with 0 and no gaps in between.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemRatioKind -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:5b7f3369-b175-4235-bd33-5f46fe1078d9">
													<SHORT-NAME>DemRatioKind</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines whether the ratio will be calculated API or observer based.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:0f28b897-c6c4-8505-4063-4ea05dc5ff9d">
															<SHORT-NAME>DEM_RATIO_API</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:91f98f41-6f70-86df-49f0-d49d5dcd5199">
															<SHORT-NAME>DEM_RATIO_OBSERVER</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: DemDiagnosticEventRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:e4a17661-b438-44c2-b250-9d97e9cda20d">
													<SHORT-NAME>DemDiagnosticEventRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This reference contains the link to a diagnostic event.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<!-- Symbolic Name Reference Definition: DemFunctionIdRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:36c5505e-2322-474d-8979-f5ca259d394d">
													<SHORT-NAME>DemFunctionIdRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This reference contains the link to a function identifier within the FiM which is used as a primary FID.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/FiM/FiMConfigSet/FiMFID</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
												<!-- Symbolic Name Reference Definition: DemSecondaryFunctionIdRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:*************-412d-a803-72c0e418f233">
													<SHORT-NAME>DemSecondaryFunctionIdRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This reference contains the link to a function identifier within the FiM which is used as a secondary FID.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">The &quot;primary&quot; and all &quot;secondary&quot; FID inhibitions are combined by &quot;OR&quot;.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/FiM/FiMConfigSet/FiMFID</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemStorageCondition -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:5f232bac-fe27-4d30-b072-1795f9af7ef6">
											<SHORT-NAME>DemStorageCondition</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the configuration (parameters) for storage conditions.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DemStorageConditionId -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:67e309bf-b4b9-4de5-b0d1-47df01d36b84">
													<SHORT-NAME>DemStorageConditionId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Defines a unique storage condition Id. This parameter should not be changeable by user, because the Id should be generated by Dem itself to prevent gaps and multiple use of an Id. The storage conditions should be sequentially ordered beginning with 0 and no gaps in between.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DemStorageConditionStatus -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:57bb12c8-1d23-4314-a9fa-d0cb5116322f">
													<SHORT-NAME>DemStorageConditionStatus</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Defines the initial status for enable or disable of storage of a diagnostic event.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">The value is the initialization after power up (before this condition is reported the first time).
                                                true: storage of a diagnostic event enabled 
                                                false: storage of a diagnostic event disabled</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemStorageConditionGroup -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:713b95f5-b501-4a30-91c8-cedbe4ba692b">
											<SHORT-NAME>DemStorageConditionGroup</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the configuration (parameters) for storage condition groups.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<REFERENCES>
												<!-- Reference Definition: DemStorageConditionRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:94a93959-aae2-416d-a59a-bd0fae591f32">
													<SHORT-NAME>DemStorageConditionRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">References an enable condition.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC V1.0.0</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemGeneral/DemStorageCondition</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DemClient -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:5d3c6e4a-6c4c-4754-b120-000eb60702c2">
											<SHORT-NAME>DemClient</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains possible clients that are using the Dem APIs.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DemClientId -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:06c5d156-e093-420c-a281-d73b792ba3f9">
													<SHORT-NAME>DemClientId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Defines a unique identifier for a Dem client. This number is used by this client in the ClientID parameter in all API with this parameter.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
