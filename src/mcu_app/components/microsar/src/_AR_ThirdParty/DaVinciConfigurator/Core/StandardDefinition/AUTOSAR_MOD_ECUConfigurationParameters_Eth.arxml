<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: Eth -->
						<ECUC-MODULE-DEF UUID="ECUC:7330314e-e986-4763-8446-4fea1ec1f9db">
							<SHORT-NAME>Eth</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the Eth (Ethernet Driver) module.</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.4.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
										<DATE>2013-10-31</DATE>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-LINK-TIME</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-POST-BUILD</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: EthConfigSet -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:5038b196-90f1-4b1e-991a-59d361bf33c9">
									<SHORT-NAME>EthConfigSet</SHORT-NAME>
									<DESC>
										<L-2 L="EN">All included containers and parameters that may be part of a multiple configuration set.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>true</MULTIPLE-CONFIGURATION-CONTAINER>
									<SUB-CONTAINERS>
										<!-- Container Definition: EthCtrlConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:56cd8c81-2738-4e37-9187-eb8d6838c4c6">
											<SHORT-NAME>EthCtrlConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configuration of the individual controller</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: EthCtrlEnableMii -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:5292ca43-a021-40dc-ad36-08a935433941">
													<SHORT-NAME>EthCtrlEnableMii</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables / Disables Media Independent Interface (MII) for transceiver access</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: EthCtrlEnableRxInterrupt -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:1750d5cc-afcb-4d21-93b0-e29e9a41ea26">
													<SHORT-NAME>EthCtrlEnableRxInterrupt</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables / Disables receive interrupt</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: EthCtrlEnableTxInterrupt -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:064b39a9-dca2-4886-a425-973de461e3d7">
													<SHORT-NAME>EthCtrlEnableTxInterrupt</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables / Disables transmit interrupt</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: EthCtrlIdx -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:57bc491e-62f2-4d20-943a-0bf28cca2ac1">
													<SHORT-NAME>EthCtrlIdx</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Specifies the instance ID of the configured controller.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: EthCtrlPhyAddress -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:b7e156c0-3a45-42f1-b2f9-feb287415200">
													<SHORT-NAME>EthCtrlPhyAddress</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Specifies the unique 48-bit physical address (MAC address) of the controller in network byte order.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">Regular Expression: [0-9a-fA-F]{2}[[:-][0-9a-fA-F]{2}]{5}</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL>
															<MAX-LENGTH>17</MAX-LENGTH>
															<MIN-LENGTH>17</MIN-LENGTH>
														</ECUC-STRING-PARAM-DEF-CONDITIONAL>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: EthCtrlRxBufLenByte -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:6164a17c-2277-4d64-81a0-c03e219bdf8d">
													<SHORT-NAME>EthCtrlRxBufLenByte</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Limits the maximum receive buffer length (frame length) in bytes.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>1522</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: EthCtrlTxBufLenByte -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:f4b834a6-9fce-4cf9-9f80-98e4a6ad9eec">
													<SHORT-NAME>EthCtrlTxBufLenByte</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Limits the maximum transmit buffer length (frame length) in bytes.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>1522</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: EthRxBufTotal -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:139da546-28f2-4245-8429-9c05fa63efde">
													<SHORT-NAME>EthRxBufTotal</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Configures the number of receive buffers.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: EthTxBufTotal -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:3ea8a022-70b8-493a-9cf8-13ae8489d6b4">
													<SHORT-NAME>EthTxBufTotal</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Configures the number of transmit buffers.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<SUB-CONTAINERS>
												<!-- Container Definition: EthDemEventParameterRefs -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:6c163108-f8fb-4e94-aee0-19a511446ffc">
													<SHORT-NAME>EthDemEventParameterRefs</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container for the references to DemEventParameter elements which shall be invoked using the API Dem_ReportErrorStatus in case the corresponding error occurs. The EventId is taken from the referenced DemEventParameter&apos;s DemEventId value. The standardized errors are provided in the container and can be extended by vendor specific error references.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<REFERENCES>
														<!-- Symbolic Name Reference Definition: ETH_E_ACCESS -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:b4651d05-f83d-44c9-9d1e-21089a781293">
															<SHORT-NAME>ETH_E_ACCESS</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the DemEventParameter which shall be issued when the error &quot;Controller access failed&quot; has occured.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: EthGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:6f42d612-0b76-4acc-a377-9a35f3decf57">
									<SHORT-NAME>EthGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">General configuration of Ethernet Driver module</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: EthDevErrorDetect -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:e9f29dc0-4e38-4c7a-89f6-67505e301506">
											<SHORT-NAME>EthDevErrorDetect</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables / Disables development error detection.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthIndex -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:8f9f2801-a733-45ba-92c1-4f76e7239397">
											<SHORT-NAME>EthIndex</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the InstanceId of this module instance. If only one instance is present it shall have the Id 0.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>255</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthMaxCtrlsSupported -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:5dd19540-c70f-4e65-b839-510b53e355c7">
											<SHORT-NAME>EthMaxCtrlsSupported</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Limits the total number of supported controllers.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>255</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthUpdatePhysAddrFilter -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:3f2394e5-bdde-4327-b254-10bfcbda919b">
											<SHORT-NAME>EthUpdatePhysAddrFilter</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables/Disables optional API Eth_UpdatePhysAddrFilter.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EthVersionInfoApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:aec58ac0-ad1c-4331-a535-7f4301e98577">
											<SHORT-NAME>EthVersionInfoApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables / Disables version info API</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
