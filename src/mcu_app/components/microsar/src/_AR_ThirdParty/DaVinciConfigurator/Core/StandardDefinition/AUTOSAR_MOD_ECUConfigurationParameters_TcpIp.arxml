<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: TcpIp -->
						<ECUC-MODULE-DEF UUID="ECUC:b86af44e-33ca-4ffc-addb-799de20df7b6">
							<SHORT-NAME>TcpIp</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the TcpIp (TCP/IP stack) module.</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.6.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
										<DATE>2014-10-31</DATE>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-LINK-TIME</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-POST-BUILD</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: TcpIpConfig -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="f39fb728-364a-4aa7-947c-263b3ac95468">
									<SHORT-NAME>TcpIpConfig</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container contains the configuration parameters and sub containers of the AUTOSAR TcpIp module.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SUB-CONTAINERS>
										<!-- Container Definition: TcpIpCtrl -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="fa0185ea-8bbc-4d6b-9fdd-569acd00f722">
											<SHORT-NAME>TcpIpCtrl</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the EthIf controller used for IP communication and TcpIp errors that shall be reported to DEM.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: TcpIpIpFramePrioDefault -->
												<ECUC-INTEGER-PARAM-DEF UUID="4a48e069-b06d-4378-999f-a6cbf5bdae16">
													<SHORT-NAME>TcpIpIpFramePrioDefault</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Specifies the default value for the frame priority used by all sockets.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">Note: the value can be changed for each socket individually via TcpIp_ChangeParameter() service. If this optional parameter is not available, 0 is used as default priority.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>7</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: TcpIpDhcpServerConfigRef -->
												<ECUC-REFERENCE-DEF UUID="437632f9-fa1f-4d6a-b547-e284ab6a07ab">
													<SHORT-NAME>TcpIpDhcpServerConfigRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to a TcpIpDhcpServerConfig which shall be used for this controller setting (VLAN).</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/TcpIp/TcpIpConfig/TcpIpDhcpServerConfig</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<!-- Symbolic Name Reference Definition: TcpIpEthIfCtrlRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="31ec8ab1-1bc4-46d2-b79f-3d2d345aaefc">
													<SHORT-NAME>TcpIpEthIfCtrlRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to EthIf controller where the IP address shall be assigned.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EthIf/EthIfConfigSet/EthIfController</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
											<SUB-CONTAINERS>
												<!-- Container Definition: TcpIpCtrlDemEventParameterRefs -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="171d8e15-2221-4c34-97ee-fecd3dd4148c">
													<SHORT-NAME>TcpIpCtrlDemEventParameterRefs</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container is a subcontainer of TcpIpCtrl and specifies the references to DemEventParameter elements which shall be invoked using the API Dem_ReportErrorStatus API in case the corresponding TcpIp error occurs for communication on the EthIf Controller. The EventId is taken from the referenced DemEventParameter's DemEventId value. The standardized errors are provided in the container and can be extended by vendor specific error references.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<REFERENCES>
														<!-- Symbolic Name Reference Definition: TCPIP_E_CONNREFUSED -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="a1d84fdb-d29a-4cfd-ac49-d4a44677a60b">
															<SHORT-NAME>TCPIP_E_CONNREFUSED</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the DemEventParameter which shall be issued when the error "Connection refused” has occurred.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
														<!-- Symbolic Name Reference Definition: TCPIP_E_HOSTUNREACH -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="f3780506-**************-46acedc4347f">
															<SHORT-NAME>TCPIP_E_HOSTUNREACH</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the DemEventParameter which shall be issued when the error "No route to host” has occurred.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
														<!-- Symbolic Name Reference Definition: TCPIP_E_PACKETTOBIG -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="b42e0ca4-3ecf-4695-9272-38671b10ea3e">
															<SHORT-NAME>TCPIP_E_PACKETTOBIG</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the DemEventParameter which shall be issued when the error "Path does not support frame size" has occurred.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
														<!-- Symbolic Name Reference Definition: TCPIP_E_TIMEDOUT -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="42bd5b2a-fecc-46d2-b944-a7f725fe2c0f">
															<SHORT-NAME>TCPIP_E_TIMEDOUT</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the DemEventParameter which shall be issued when the error "Operation timed out” has occurred.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: TcpIpIpVXCtrl -->
												<ECUC-CHOICE-CONTAINER-DEF UUID="c7454466-eafa-4093-9930-19ededbf3a4e">
													<SHORT-NAME>TcpIpIpVXCtrl</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Specifies whether this controller is an Internet Protocol version 4 (IPv4) or Internet Protocol version 6 (IPv4) instance.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<CHOICES>
														<!-- Container Definition: TcpIpIpV4Ctrl -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="2ab7ecbc-88ed-4c6b-87c3-3341fc476534">
															<SHORT-NAME>TcpIpIpV4Ctrl</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Specifies an Internet Protocol version 4 (IPv4) instance.</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:postBuildSelectableChangeable">false</SD>
																		<SD GID="DV:postBuildNotDeletable">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<PARAMETERS>
																<ECUC-INTEGER-PARAM-DEF UUID="ae037c79-b220-4a64-9345-7b7ee8a5d1fc">
																	<SHORT-NAME>TcpIpIpTypeOfServiceDefault</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This value specifies the default for the "Type of Service" field of the IPv4 header in outgoing packets on this IP controller.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>0</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<ECUC-INTEGER-PARAM-DEF UUID="2d481e8c-7607-4368-bbd2-60ab601b3643">
																	<SHORT-NAME>TcpIpIpCtrlIdx</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">IpV4 Controller Index</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">IPv4 controller index (calculated automatically)</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>0</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<ECUC-INTEGER-PARAM-DEF UUID="9b3a7947-c7a7-46db-982c-440fab0e0884">
																	<SHORT-NAME>TcpIpIpDefaultTtl</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Default Time To Live</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">This value specifies the default for the "Time to Live" field of the IPv4 header in outgoing packets on this IP controller.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>64</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
															<REFERENCES>
																<!-- Reference Definition: TcpIpArpConfigRef -->
																<ECUC-REFERENCE-DEF UUID="7bd8c38e-2a76-40f0-97d5-68397b972d43">
																	<SHORT-NAME>TcpIpArpConfigRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to ARP configuration for this IPv4 instance.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">(Multiple IPv4 instances may use the same configuration container but will operate independently)</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/TcpIp/TcpIpConfig/TcpIpIpConfig/TcpIpIpV4Config/TcpIpArpConfig</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
																<!-- Reference Definition: TcpIpAutoIpConfigRef -->
																<ECUC-REFERENCE-DEF UUID="464b79fb-c0e5-42fc-9237-68c9ebac4fea">
																	<SHORT-NAME>TcpIpAutoIpConfigRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to AutoIp configuration for this IPv4 instance.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">(Multiple IPv4 instances may use the same configuration container but will operate independently)</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/TcpIp/TcpIpConfig/TcpIpIpConfig/TcpIpIpV4Config/TcpIpAutoIpConfig</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
																<!-- Reference Definition: TcpIpDhcpConfigRef -->
																<ECUC-REFERENCE-DEF UUID="71225b07-2bf6-4aec-8332-fbe97401610f">
																	<SHORT-NAME>TcpIpDhcpConfigRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to DHCP configuration for this IPv4 instance.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">(Multiple IPv4 instances may use the same configuration container but will operate independently)</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/TcpIp/TcpIpConfig/TcpIpIpConfig/TcpIpIpV4Config/TcpIpDhcpConfig</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
																<!-- Reference Definition: TcpIpFragmentationConfigRef -->
																<ECUC-REFERENCE-DEF UUID="1082db5e-3319-4ece-961a-2bc452de93ed">
																	<SHORT-NAME>TcpIpFragmentationConfigRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to Fragmentation configuration for this IPv4 instance.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">(Multiple IPv4 instances may use the same configuration container but will operate independently)</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/TcpIp/TcpIpConfig/TcpIpIpConfig/TcpIpIpV4Config/TcpIpIpFragmentationConfig</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
																<ECUC-REFERENCE-DEF UUID="7cae9e88-93a6-4ebf-87c1-b84a6b6bc333">
																	<SHORT-NAME>TcpIpStaticArpTableRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to a static ARP table that shall be used for this controller.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/TcpIp/TcpIpConfig/TcpIpIpConfig/TcpIpIpV4Config/TcpIpStaticArpTable</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
															</REFERENCES>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: TcpIpIpV6Ctrl -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="739c0200-ae9c-42da-8178-469e4380cfe7">
															<SHORT-NAME>TcpIpIpV6Ctrl</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Specifies an Internet Protocol version 6 (IPv6) instance.</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:postBuildSelectableChangeable">false</SD>
																		<SD GID="DV:postBuildNotDeletable">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<PARAMETERS>
																<ECUC-INTEGER-PARAM-DEF UUID="97eff9a8-3b09-4983-bb0c-15ed7584624a">
																	<SHORT-NAME>TcpIpIpV6DefaultHopLimit</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Default Hop Limit</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">This value specifies the default Hop Limit for outgoing IPv6 packets. If "Allow Hop Limit Reconfiguration" is checked, the value may be reconfigured based on received Router Advertisements according to [RFC4861 6.3.4. Processing Received Router Advertisements].</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>64</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<ECUC-INTEGER-PARAM-DEF UUID="8b7ee167-db6d-4482-9ed1-0bc056c6a7df">
																	<SHORT-NAME>TcpIpIpV6DefaultTrafficClass</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This value specifies the default 'Traffic Class' set in the IPv6 header of outgoing packets of this controller.
This value may be changed to a socket specific value during runtime.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>0</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<ECUC-INTEGER-PARAM-DEF UUID="00a48c96-3ca4-4fbb-80fd-73a5a9a4cf19">
																	<SHORT-NAME>TcpIpIpV6DefaultFlowLabel</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This value specifies the default 'FlowLabel' set in the IPv6 header of outgoing packets of this controller.
This value may be changed to a socket specific value during runtime.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>0</DEFAULT-VALUE>
																	<MAX>1048575</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<ECUC-INTEGER-PARAM-DEF UUID="d067c0c6-3daf-4a64-97d4-b7abfe991021">
																	<SHORT-NAME>TcpIpIpV6CtrlIdx</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">IpV6 Controller Index</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">IPv6 controller index (calculated automatically)</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>0</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
															<REFERENCES>
																<!-- Reference Definition: TcpIpIpV6DhcpConfigRef -->
																<ECUC-REFERENCE-DEF UUID="73944cf1-1420-4f8a-8fa7-8bfa1501dd96">
																	<SHORT-NAME>TcpIpIpV6DhcpConfigRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to DHCPv6 configuration.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">(Multiple IPv6 instances may use the same configuration container but will operate independently)</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/TcpIp/TcpIpConfig/TcpIpIpConfig/TcpIpIpV6Config/TcpIpDhcpV6Config</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
																<!-- Reference Definition: TcpIpIpV6FragmentationConfigRef -->
																<ECUC-REFERENCE-DEF UUID="6c6538e3-478e-448a-9603-4405e9e6eb2a">
																	<SHORT-NAME>TcpIpIpV6FragmentationConfigRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to IPv6 Fragmentation Configuration.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">(Multiple IPv6 instances may use the same configuration container but will operate independently)</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/TcpIp/TcpIpConfig/TcpIpIpConfig/TcpIpIpV6Config/TcpIpIpV6FragmentationConfig</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
																<!-- Reference Definition: TcpIpIpV6NdpConfigRef -->
																<ECUC-REFERENCE-DEF UUID="71a3d2ba-aefa-4d9c-b7e4-55ffc06e4812">
																	<SHORT-NAME>TcpIpIpV6NdpConfigRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to Neighbor Discovery Protocol Configuration.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">(Multiple IPv6 instances may use the same configuration container but will operate independently)</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/TcpIp/TcpIpConfig/TcpIpIpConfig/TcpIpIpV6Config/TcpIpNdpConfig</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
																<ECUC-REFERENCE-DEF UUID="63245d91-769a-4507-86d1-ae901e82ec6b">
																	<SHORT-NAME>TcpIpIpV6MldConfigRef</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">MLDv2 Configuration</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to Multicast Listener Discovery version 2 Configuration.
If no reference is set, MLDv2 [RFC3810] is disabled on this IpV6 instance.
(Multiple IpV6 instances may use the same configuration container but will operate independently)</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/TcpIp/TcpIpConfig/TcpIpIpConfig/TcpIpIpV6Config/TcpIpNdpConfig/TcpIpIpV6MldConfig</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
																<ECUC-REFERENCE-DEF UUID="7659b8c3-9f71-4660-8393-f65b47df0891">
																	<SHORT-NAME>TcpIpIpV6PrivExtConfigRef</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Privacy Extensions Configuration</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to Privacy Extensions Configuration.
If no reference is set, Privacy Extensions [RFC4941] are disabled on this IpV6 instance,
(Multiple IpV6 instances may use the same configuration container but will operate independently)</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/TcpIp/TcpIpConfig/TcpIpIpConfig/TcpIpIpV6Config/TcpIpNdpConfig/TcpIpIpV6PrivExtConfig</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
															</REFERENCES>
															<SUB-CONTAINERS>
																<!-- Container Definition: TcpIpIpV6MtuConfig -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="8123f92e-015e-40c8-afc5-e6f1cbd85228">
																	<SHORT-NAME>TcpIpIpV6MtuConfig</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container specifies the Maximum Transmission Unit parameters for this IPv6 instance.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:CfgPostBuild">
																				<SD GID="DV:postBuildSelectableChangeable">false</SD>
																				<SD GID="DV:postBuildNotDeletable">false</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: TcpIpIpV6DefaultMtuSize -->
																		<ECUC-INTEGER-PARAM-DEF UUID="6d215459-127b-4424-bad3-4d3d84d37f2c">
																			<SHORT-NAME>TcpIpIpV6DefaultMtuSize</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Maximum Transmission Unit (MTU) of the link.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">[RFC2460 5. Packet Size Issues]
                                                                        "IPv6 requires that every link in the internet have an MTU of 1280 octets or greater. On any link that cannot convey a 1280-octet packet in one piece, link-specific fragmentation and reassembly must be provided at a layer below IPv6.

                                                                        Links that have a configurable MTU (for example, PPP links [RFC-1661]) must be configured to have an MTU of at least 1280 octets; it is recommended that they be configured with an MTU of 1500 octets or greater, to accommodate possible encapsulations (i.e., tunneling) without incurring IPv6-layer fragmentation.

                                                                        From each link to which a node is directly attached, the node must be able to accept packets as large as that link's MTU."</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>1500</DEFAULT-VALUE>
																			<MAX>65535</MAX>
																			<MIN>1280</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpIpV6PathMtuEnabled -->
																		<ECUC-BOOLEAN-PARAM-DEF UUID="16a113dc-b8c7-4c10-ba8c-6308a25f4353">
																			<SHORT-NAME>TcpIpIpV6PathMtuEnabled</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">If enabled the IPv6 processes incoming ICMPv6 "Packet Too Big" messages and stores a MTU value for each destination address.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">See RFC1981 "Path MTU Discovery for IP version 6" for details about PathMTU.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>false</DEFAULT-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpIpV6PathMtuTimeout -->
																		<ECUC-FLOAT-PARAM-DEF UUID="eaccd3b1-bd1c-4fb4-8409-af69dcd3206a">
																			<SHORT-NAME>TcpIpIpV6PathMtuTimeout</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">If this value is &gt;0 the IpV6 will reset the MTU value stored for each destination after n seconds.</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:BaseUnit">SEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">see [RFC1981 5.3. Purging stale PMTU information]
                                                                        Default: 600 seconds (10 minutes)</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>600</DEFAULT-VALUE>
																			<MAX>86400</MAX>
																			<MIN>0</MIN>
																		</ECUC-FLOAT-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
															</SUB-CONTAINERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</CHOICES>
												</ECUC-CHOICE-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: TcpIpDhcpServerConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="1dd0738d-1fce-4b7e-9a36-0d578df47e44">
											<SHORT-NAME>TcpIpDhcpServerConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the configuration parameters of the DHCP Server sub-module.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: TcpIpDhcpDefaultRouter -->
												<ECUC-STRING-PARAM-DEF UUID="5b4fc04d-1532-4f96-8c22-eb13042061d7">
													<SHORT-NAME>TcpIpDhcpDefaultRouter</SHORT-NAME>
													<DESC>
														<L-2 L="EN">IP address of default router (gateway).</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpDhcpNetmask -->
												<ECUC-INTEGER-PARAM-DEF UUID="cf95c447-31eb-42c5-8d31-7375534ea05c">
													<SHORT-NAME>TcpIpDhcpNetmask</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Network mask of IPv4 address or address prefix of IPv6 address in CIDR Notation, i.e. decimal value between 0 and 32 (IPv4) or 0 and 128 (IPv6) that describes the number of significant bits defining the network number or prefix of an IP address.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>24</DEFAULT-VALUE>
													<MAX>128</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-FLOAT-PARAM-DEF UUID="4422c6da-00d4-435e-9ef7-20d13c131a87">
													<SHORT-NAME>TcpIpDhcpOfferValidTime</SHORT-NAME>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>5</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<ECUC-STRING-PARAM-DEF UUID="4a125b98-6fa9-4a6a-9f68-616a8bc11896">
													<SHORT-NAME>TcpIpDhcpGetPortMacAddrFunctionInclude</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines the name of a header file that contains the declaration of the function defined in TcpIpGetPortMacAddrFunctionName.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL>
															<DEFAULT-VALUE>EthIf.h</DEFAULT-VALUE>
															<MIN-LENGTH>3</MIN-LENGTH>
														</ECUC-STRING-PARAM-DEF-CONDITIONAL>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<ECUC-FUNCTION-NAME-DEF UUID="424b3df9-06a9-46be-9180-4fb53fa404fd">
													<SHORT-NAME>TcpIpDhcpGetPortMacAddrFunctionName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines the name of a function that will be called in order to map a client link-layer (MAC) address to a switch index and port index.
The valid address assignment configurations for a client are selected based on these values.

The function must have the following signature:
Std_ReturnType [TcpIpGetPortMacAddrFunctionName](uint8* MacAddrPtr, uint8* SwitchIdxPtr, uint8* PortIdxPtr)</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-FUNCTION-NAME-DEF-VARIANTS>
														<ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
															<DEFAULT-VALUE>EthIf_GetPortMacAddr</DEFAULT-VALUE>
														</ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
													</ECUC-FUNCTION-NAME-DEF-VARIANTS>
												</ECUC-FUNCTION-NAME-DEF>
												<ECUC-FLOAT-PARAM-DEF UUID="37fa2a4f-6c70-407e-917b-cc620d105ba3">
													<SHORT-NAME>TcpIpDhcpLeaseTimeMax</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the maximum lease time. If a client requests a longer lease time the server will use this maximum value instead.
If this parameter does not exists the maximum lease time is infinite.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>60</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>1</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<ECUC-FLOAT-PARAM-DEF UUID="7a6fe8d0-d21d-467b-9df4-68dfcca3b33f">
													<SHORT-NAME>TcpIpDhcpLeaseTimeMin</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the minimum lease time. If a client requests a shorter lease time the server will use this minimum value instead.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>60</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>1</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<ECUC-FLOAT-PARAM-DEF UUID="b959d8fa-3d4e-4d6a-9a81-3b1f9fa5dc62">
													<SHORT-NAME>TcpIpDhcpLeaseTimeDefault</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the default lease time that is used if a client does not has requested a specific lease time.
If this parameter does not exist the maximum lease time will be used as default.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>4294967295</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>1</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="b9118591-7d3b-4f97-b77b-7e565a418b4d">
													<SHORT-NAME>TcpIpDhcpMaxClientIdLen</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the maxumin length of a DHCP client identifier.
A client identifier may be either the value of the 'chaddr' filed in the DHCP message header or the content of the client identifier option [RFC2132 9.14. Client-identifier]. A client identifer may be up to 255 bytes long but usually the client MAC address is used as client identifer. This value should be at least set to 7 in order to store the type (1 byte) and the address value (6 bytes).</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>7</DEFAULT-VALUE>
													<MAX>255</MAX>
													<MIN>6</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-STRING-PARAM-DEF UUID="705720e0-fa08-4af9-8906-ebc82af1fbfd">
													<SHORT-NAME>TcpIpDhcpHostname</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines the value of the DHCP server name that will be sent in the 'sname' field of outgoing DHCPOFFER, DHCPACK and DHCPNAK messages. </L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL>
															<MAX-LENGTH>64</MAX-LENGTH>
															<MIN-LENGTH>1</MIN-LENGTH>
														</ECUC-STRING-PARAM-DEF-CONDITIONAL>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Symbolic Name Reference Definition: TcpIpDhcpEthIfSwitchRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="bd17f471-6149-4c36-ab4f-beb071214cee">
													<SHORT-NAME>TcpIpDhcpEthIfSwitchRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to EthIfSwitch representation.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">Optional in case the Dhcp server is operating without an Ethernet switch.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EthIf/EthIfConfigSet/EthIfSwitch</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
											<SUB-CONTAINERS>
												<!-- Container Definition: TcpIpDhcpAddressAssignment -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="5659caac-1d3e-4763-9796-197a6c159b98">
													<SHORT-NAME>TcpIpDhcpAddressAssignment</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Defines a Ethernet Switch port based IP address assignment.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: TcpIpDhcpAddressLowerBound -->
														<ECUC-STRING-PARAM-DEF UUID="547e9d46-173e-4371-a642-fc5dc2268763">
															<SHORT-NAME>TcpIpDhcpAddressLowerBound</SHORT-NAME>
															<DESC>
																<L-2 L="EN">The lower bound IP address which shall be assigned.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">If lower bound and upper bound are identical exactly this IP address shall be assigned.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: TcpIpDhcpAddressUpperBound -->
														<ECUC-STRING-PARAM-DEF UUID="e0134c3a-8251-4782-8a0b-01d2f17258ef">
															<SHORT-NAME>TcpIpDhcpAddressUpperBound</SHORT-NAME>
															<DESC>
																<L-2 L="EN">The upper bound IP address which shall be assigned.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">If lower bound and upper bound are identical exactly this IP address shall be assigned.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Symbolic Name Reference Definition: TcpIpDhcpSwitchPortRef -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="b4056ffe-3212-451b-a5e6-010d2a06491e">
															<SHORT-NAME>TcpIpDhcpSwitchPortRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to Ethernet Switch port.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">Optional in case the Dhcp server is operating without an Ethernet switch.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EthSwt/EthSwtConfig/EthSwtPort</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="65bba8ea-a57a-4066-87ef-3728b0abf66f">
															<SHORT-NAME>TcpIpDhcpLocalAddrRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to a TcpIp local address on which this address assignment configuration shall be active. This reference specifies the interface/VLAN this address assignment shall be valid for.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/TcpIp/TcpIpConfig/TcpIpLocalAddr</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: TcpIpIpConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="e6111133-72b5-492f-b012-f8f1370587a9">
											<SHORT-NAME>TcpIpIpConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the configuration parameters of the IP (Internet Protocol) sub-module</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SUB-CONTAINERS>
												<!-- Container Definition: TcpIpIpV4Config -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="61db77db-188c-4120-bd67-39c656db06b6">
													<SHORT-NAME>TcpIpIpV4Config</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Specifies the configuration parameters of the IPv4 (Internet Protocol version 4) sub-module.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SUB-CONTAINERS>
														<!-- Container Definition: TcpIpArpConfig -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="fe9d3070-ccfd-4031-be09-b9e09b2ec078">
															<SHORT-NAME>TcpIpArpConfig</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Specifies the configuration parameters of the ARP (Address Resolution Protocol) sub-module.</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:postBuildSelectableChangeable">false</SD>
																		<SD GID="DV:postBuildNotDeletable">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: TcpIpArpNumGratuitousARPonStartup -->
																<ECUC-INTEGER-PARAM-DEF UUID="e99492da-2582-4561-8c35-86563f0bf0cf">
																	<SHORT-NAME>TcpIpArpNumGratuitousARPonStartup</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Arp Num Gratuitous Arp On Startup</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">Specifies the number of gratuitous ARP replies which shall be sent on assignment of a new IP address.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>0</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: TcpIpArpPacketQueueEnabled -->
																<ECUC-BOOLEAN-PARAM-DEF UUID="c025e5a6-8787-43bb-a5fd-58e892dd5dbb">
																	<SHORT-NAME>TcpIpArpPacketQueueEnabled</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Enables (TRUE) or disables (FALSE) support of the ARP Packet Queue according to IETF RFC 1122, section *******.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>false</DEFAULT-VALUE>
																</ECUC-BOOLEAN-PARAM-DEF>
																<!-- PARAMETER DEFINITION: TcpIpArpTableEntryTimeout -->
																<ECUC-FLOAT-PARAM-DEF UUID="01629e6e-ce4b-47e1-8c4e-44558e37fbf3">
																	<SHORT-NAME>TcpIpArpTableEntryTimeout</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Timeout in seconds after which an unused ARP entry is removed.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>30</DEFAULT-VALUE>
																	<MAX>65535</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<!-- PARAMETER DEFINITION: TcpIpArpTableSizeMax -->
																<ECUC-INTEGER-PARAM-DEF UUID="eb74e38d-a4f4-4114-a180-4a877e9246ed">
																	<SHORT-NAME>TcpIpArpTableSizeMax</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Maximum number of entries in the ARP table.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>10</DEFAULT-VALUE>
																	<MAX>65535</MAX>
																	<MIN>1</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<ECUC-FLOAT-PARAM-DEF UUID="05e03ab6-7476-4357-81fb-91eb84721232">
																	<SHORT-NAME>TcpIpArpRetryTime</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">ARP Retry Time</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">This value specifies the maximum time for an ARP resolution.
If no answer has been received before this time has expired, ARP resolution is canceled.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>5</DEFAULT-VALUE>
																	<MAX>65535</MAX>
																	<MIN>1</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<ECUC-FLOAT-PARAM-DEF UUID="3b30286b-e8df-4c4b-82df-c0f1e85a79a6">
																	<SHORT-NAME>TcpIpArpRetryInterval</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">ARP Retry Interval</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">This value specifies the ARP retry interval.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>2</DEFAULT-VALUE>
																	<MAX>65535</MAX>
																	<MIN>1</MIN>
																</ECUC-FLOAT-PARAM-DEF>
															</PARAMETERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: TcpIpAutoIpConfig -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="e570ea6b-48e1-4f68-a688-782b3747b985">
															<SHORT-NAME>TcpIpAutoIpConfig</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Specifies the configuration parameters of the Auto-IP (automatic private IP addressing) sub-module.</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:postBuildSelectableChangeable">false</SD>
																		<SD GID="DV:postBuildNotDeletable">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: TcpIpAutoIpInitTimeout -->
																<ECUC-FLOAT-PARAM-DEF UUID="3bb4d309-a0d2-4f6c-be82-a2a3535c7b12">
																	<SHORT-NAME>TcpIpAutoIpInitTimeout</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">The time Auto-IP waits at startup, before beginning with ARP probing. This delay is used to give DHCP time to acquire a lease in case a DHCP server is present.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																				<SD GID="DV:Unit">MSEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>1</DEFAULT-VALUE>
																	<MAX>65.535</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<ECUC-INTEGER-PARAM-DEF UUID="def0d6ec-df9b-48f2-82a3-de657abed824">
																	<SHORT-NAME>TcpIpAutoIpProbeNum</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Probe Number</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">This value specifies the number of probe packets (RFC standard: 3)</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>1</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>1</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<ECUC-FLOAT-PARAM-DEF UUID="2c2bc815-1386-41ef-85f3-a191f5264d3b">
																	<SHORT-NAME>TcpIpAutoIpProbeMin</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Probe Min Delay</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">This value specifies the minimum interval between probe messages (RFC standard: 1s)</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																				<SD GID="DV:Unit">MSEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>0.4</DEFAULT-VALUE>
																	<MAX>65.535</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<ECUC-FLOAT-PARAM-DEF UUID="0bd6f823-4d44-4fa0-b399-6f647e2dd5d1">
																	<SHORT-NAME>TcpIpAutoIpProbeMax</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Probe Max Delay</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">This value specifies the maximum delay between probe messages (RFC standard: 2s)</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																				<SD GID="DV:Unit">MSEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>0.6</DEFAULT-VALUE>
																	<MAX>65.535</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<ECUC-FLOAT-PARAM-DEF UUID="a20e2884-e567-4d08-81f3-c64fa2b790de">
																	<SHORT-NAME>TcpIpAutoIpAnnounceWait</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Announce Wait</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">This value specifies the delay before the first announcement message (RFC standard: 2s)</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																				<SD GID="DV:Unit">MSEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>0.5</DEFAULT-VALUE>
																	<MAX>65.535</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<ECUC-INTEGER-PARAM-DEF UUID="7f35a4de-41d5-4496-814e-7757af22af73">
																	<SHORT-NAME>TcpIpAutoIpAnnounceNum</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Announce Number</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">This value specifies the number of announcement packets (RFC standard: 2)</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>1</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>1</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<ECUC-FLOAT-PARAM-DEF UUID="54d0c603-0468-499f-93fe-7c1c4f92cb64">
																	<SHORT-NAME>TcpIpAutoIpAnnounceInterval</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Announce Interval</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">This value specifies the interval between announcement packets (RFC standard: 2s)</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																				<SD GID="DV:Unit">MSEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>0.4</DEFAULT-VALUE>
																	<MAX>65.535</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<ECUC-INTEGER-PARAM-DEF UUID="39ea80bc-b6bf-471d-b526-179002f31b71">
																	<SHORT-NAME>TcpIpAutoIpMaxConflicts</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Max Number Of Conflicts</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">This value specifies the maximum number of conflicts before rate limiting (RFC standard: 10)

If the specified number of conflicts has been reached new address configurations attempts are rate limited by "Rate Limit Interval".</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>10</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>1</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<ECUC-FLOAT-PARAM-DEF UUID="bc1933d2-caa9-4b37-aa99-a2a370e42af4">
																	<SHORT-NAME>TcpIpAutoIpRateLimitInterval</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Rate Limit Interval</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">This value specifies the delay between successive address configuration attempts when rate limiting is active (RFC standard: 60s)

see "Max Number Of Conflicts"</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																				<SD GID="DV:Unit">MSEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>5</DEFAULT-VALUE>
																	<MAX>65.535</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<ECUC-FLOAT-PARAM-DEF UUID="7e717fac-5458-4c75-b9f1-fa2cbd7507ae">
																	<SHORT-NAME>TcpIpAutoIpDefendInterval</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Defend Interval</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">This value specifies the minimum interval between defensive ARPs (RFC standard: 10s)</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																				<SD GID="DV:Unit">MSEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>5</DEFAULT-VALUE>
																	<MAX>65.535</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<ECUC-FLOAT-PARAM-DEF UUID="e9ca4abc-cffe-4c3b-915b-7e85fae3a879">
																	<SHORT-NAME>TcpIpAutoIpProbeWaitMax</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Probe Wait Max</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">This value specifies the maximum initial random delay before the first probe message is sent. (RFC standard: 1s)</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																				<SD GID="DV:Unit">MSEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>7</DEFAULT-VALUE>
																	<MAX>65.535</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
															</PARAMETERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: TcpIpDhcpConfig -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="095c9a8d-524a-4106-9333-e602351abb95">
															<SHORT-NAME>TcpIpDhcpConfig</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Specifies the configuration parameters of the DHCPv4.</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:postBuildSelectableChangeable">false</SD>
																		<SD GID="DV:postBuildNotDeletable">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">This container may be referenced by multiple IPv4 instances if they shall use the same configuration.
                                                        This container may have multiple instances if different configurations are required for different IPv4 instances.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<PARAMETERS>
																<ECUC-FLOAT-PARAM-DEF UUID="896ae055-c47b-4dfa-90c8-4cdf02d43266">
																	<SHORT-NAME>TcpIpDhcpV4InitWaitMin</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Init Wait Min</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">Minimum initial delay for DISCOVER (RFC standard: 1s)

RESTRICTIONS: 'DhcpV4InitWaitMin' is only available, if 'TcpIpEnableDhcpV4' is set to 'true'.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																				<SD GID="DV:Unit">MSEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>100</DEFAULT-VALUE>
																	<MAX>65535</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<ECUC-FLOAT-PARAM-DEF UUID="5b13dc86-60dd-44e6-9acb-2be106995879">
																	<SHORT-NAME>TcpIpDhcpV4InitWaitMax</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Init Wait Max</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">Maximum initial delay for DISCOVER (RFC standard: 10s)

RESTRICTIONS: 'DhcpV4InitWaitMax' is only available, if 'TcpIpEnableDhcpV4' is set to 'true'.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																				<SD GID="DV:Unit">MSEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>800</DEFAULT-VALUE>
																	<MAX>65535</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<ECUC-INTEGER-PARAM-DEF UUID="0fe484ba-c5f1-4f35-9818-f0ab34b26350">
																	<SHORT-NAME>TcpIpDhcpV4DiscoverMaxNum</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Discover Max Num</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">Maximum number of DISCOVER messages

RESTRICTIONS: 'DhcpV4DiscoverMaxNum' is only available, if 'TcpIpEnableDhcpV4' is set to 'true'.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>5</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<ECUC-FLOAT-PARAM-DEF UUID="79231d1e-6638-4b1f-80a3-e16969a3cc99">
																	<SHORT-NAME>TcpIpDhcpV4DiscoverIntervalMin</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Discover Interval Min</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">Minimum DISCOVER interval

RESTRICTIONS: 'DhcpV4DiscoverIntervalMin' is only available, if 'TcpIpEnableDhcpV4' is set to 'true'.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>2</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<ECUC-FLOAT-PARAM-DEF UUID="79f3f297-5db3-4af3-a939-75cca6154823">
																	<SHORT-NAME>TcpIpDhcpV4DiscoverIntervalMax</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Discover Interval Max</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">Maximum DISCOVER interval

RESTRICTIONS: 'DhcpV4DiscoverIntervalMax' is only available, if 'TcpIpEnableDhcpV4' is set to 'true'.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>30</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<ECUC-INTEGER-PARAM-DEF UUID="7d39e724-68ba-42cd-b582-21c1cddd35d1">
																	<SHORT-NAME>TcpIpDhcpV4RequestMaxNum</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Request Max Num</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">Maximum number of REQUEST messages

RESTRICTIONS: 'DhcpV4RequestMaxNum' is only available, if 'TcpIpEnableDhcpV4' is set to 'true'.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>6</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<ECUC-FLOAT-PARAM-DEF UUID="38c28c76-f7a9-40f7-90e3-bdf60ff4c5b2">
																	<SHORT-NAME>TcpIpDhcpV4RequestInterval</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Request Interval</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">REQUEST interval

RESTRICTIONS: 'DhcpV4RequestInterval' is only available, if 'TcpIpEnableDhcpV4' is set to 'true'.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>2</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<ECUC-FLOAT-PARAM-DEF UUID="25fff6c2-1755-4f5a-ae23-a17870a41125">
																	<SHORT-NAME>TcpIpDhcpV4RenewIntervalMin</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Renew Interval Min</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">Minimum Renew interval

RESTRICTIONS: 'DhcpV4RenewIntervalMin' is only available, if 'TcpIpEnableDhcpV4' is set to 'true'.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>5</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<ECUC-FLOAT-PARAM-DEF UUID="0822ef67-ef53-4ce9-8656-0f15bbe31e79">
																	<SHORT-NAME>TcpIpDhcpV4RebindIntervalMin</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Rebind Interval Min</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">Minimum Rebind interval

RESTRICTIONS: 'DhcpV4RebindIntervalMin' is only available, if 'TcpIpEnableDhcpV4' is set to 'true'.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>5</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<ECUC-INTEGER-PARAM-DEF UUID="5a76a881-b0af-417a-8b51-145a437ccf70">
																	<SHORT-NAME>TcpIpDhcpV4HostNameLenMax</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Host Name Length Max</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">Maximum support Host name length [byte]

RESTRICTIONS: 'DhcpV4HostNameLenMax' is only available, if 'TcpIpEnableDhcpV4' is set to 'true'.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">BYTE</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>30</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<ECUC-BOOLEAN-PARAM-DEF UUID="76424154-e781-4344-a7a3-3fbb524f364b">
																	<SHORT-NAME>TcpIpDhcpV4RestartOnFail</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Restart On Failure</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">Restart the DHCP address assignment process if a used address lease times out and no lease update could be obtained. After the lease expires either the DHCP process is restarted or the process will be stopped completely (no new address assignment).

RESTRICTIONS: 'TcpIpDhcpV4RestartOnFail' is only available, if 'TcpIpEnableDhcpV4' is set to 'true'.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>false</DEFAULT-VALUE>
																</ECUC-BOOLEAN-PARAM-DEF>
															</PARAMETERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: TcpIpIcmpConfig -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="d31d6c5a-9ded-4e18-8ac5-8089905127d2">
															<SHORT-NAME>TcpIpIcmpConfig</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Specifies the configuration parameters of the ICMP (Internet Control Message Protocol) sub-module.</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:postBuildSelectableChangeable">false</SD>
																		<SD GID="DV:postBuildNotDeletable">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: TcpIpIcmpTtl -->
																<ECUC-INTEGER-PARAM-DEF UUID="d36dcbb7-c3e5-433c-b754-837aa8e07f12">
																	<SHORT-NAME>TcpIpIcmpTtl</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Default Time-to-live value of outgoing ICMP packets.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>64</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<ECUC-INTEGER-PARAM-DEF UUID="5e6f24a6-fd84-4fe9-bcaa-58db98994587">
																	<SHORT-NAME>TcpIpIcmpEchoReplyMaxLen</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Echo Data Buffer Size</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">This value specifies the maximum payload length of an Echo Reply.
If an Echo Request contains more data the payload of the response will be truncated.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">BYTE</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>32</DEFAULT-VALUE>
																	<MAX>32767</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
															<SUB-CONTAINERS>
																<!-- Container Definition: TcpIpIcmpMsgHandler -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="dfb89cc6-613b-4806-bb49-f1f35982290b">
																	<SHORT-NAME>TcpIpIcmpMsgHandler</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container is a subcontainer of TcpIpIcmpConfig and specifies the configuration parameters for the ICMP message handler.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:CfgPostBuild">
																				<SD GID="DV:postBuildSelectableChangeable">false</SD>
																				<SD GID="DV:postBuildNotDeletable">false</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: TcpIpIcmpMsgHandlerHeaderFileName -->
																		<ECUC-STRING-PARAM-DEF UUID="d726ddfc-319c-47ab-9626-486a7ab2c513">
																			<SHORT-NAME>TcpIpIcmpMsgHandlerHeaderFileName</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter specifies the name of the header file containing the definition of the ICMP message handler function.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<ECUC-STRING-PARAM-DEF-VARIANTS>
																				<ECUC-STRING-PARAM-DEF-CONDITIONAL>
																					<MAX-LENGTH>32</MAX-LENGTH>
																					<MIN-LENGTH>1</MIN-LENGTH>
																				</ECUC-STRING-PARAM-DEF-CONDITIONAL>
																			</ECUC-STRING-PARAM-DEF-VARIANTS>
																		</ECUC-STRING-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpIcmpMsgHandlerName -->
																		<ECUC-FUNCTION-NAME-DEF UUID="4d40c565-f7da-4939-931a-75a589e487c7">
																			<SHORT-NAME>TcpIpIcmpMsgHandlerName</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines the name of the ICMP message handler function &lt;User_IcmpMsgHandler&gt;.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																		</ECUC-FUNCTION-NAME-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
															</SUB-CONTAINERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: TcpIpIpFragmentationConfig -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="d3a8de10-ba6d-496f-83a9-cfaf937119d4">
															<SHORT-NAME>TcpIpIpFragmentationConfig</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Specifies the configuration parameters of IPv4 packet fragmentation/reassembly.</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:postBuildSelectableChangeable">false</SD>
																		<SD GID="DV:postBuildNotDeletable">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">This container may be referenced by multiple IPv4 instances if they shall use the same configuration.
                                                        This container may have multiple instances if different configurations are required for different IPv4 instances.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: TcpIpIpFragmentationRxEnabled -->
																<ECUC-BOOLEAN-PARAM-DEF UUID="f843b808-19d8-4696-a17c-c72881b9a7d7">
																	<SHORT-NAME>TcpIpIpFragmentationRxEnabled</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Enables (TRUE) or disables (FALSE) support for reassembling of incoming datagrams that are fragmented according to IETF RFC 815 (IP Datagram Reassembly Algorithms).</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>false</DEFAULT-VALUE>
																</ECUC-BOOLEAN-PARAM-DEF>
																<!-- PARAMETER DEFINITION: TcpIpIpNumFragments -->
																<ECUC-INTEGER-PARAM-DEF UUID="6c5f7d62-30c7-4f61-bc7f-8958970e834b">
																	<SHORT-NAME>TcpIpIpNumFragments</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Specifies the maximum number of IP fragments per datagram.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">Note: this parameter is only relevant if TcpIpIpFragmentationRxEnabled is TRUE.</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>0</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: TcpIpIpNumReassDgrams -->
																<ECUC-INTEGER-PARAM-DEF UUID="b350d840-7805-4c26-84ad-2b946c8f0674">
																	<SHORT-NAME>TcpIpIpNumReassDgrams</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Specifies the maximum number of fragmented IP datagrams that can be reassembled in parallel.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">Note: this parameter is only relevant if TcpIpIpFragmentationRxEnabled is TRUE.</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>3</DEFAULT-VALUE>
																	<MAX>65535</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: TcpIpIpReassTimeout -->
																<ECUC-FLOAT-PARAM-DEF UUID="07836198-3f49-448b-ba7c-b1d4ad129a9d">
																	<SHORT-NAME>TcpIpIpReassTimeout</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Specifies the timeout in [s] after which an incomplete datagram gets discarded.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">Note: this parameter is only relevant if TcpIpIpFragmentationRxEnabled is TRUE.</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>60</DEFAULT-VALUE>
																	<MAX>1.7976931348623157E+308</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
															</PARAMETERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="962cc953-240c-4e88-abff-b17c7bdd0c55">
															<SHORT-NAME>TcpIpStaticArpTable</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This container includes one or more static ARP table entries.
It may be referenced by multiple IpV4CtrlConfig containers if they shall use the same static ARP table.
The static ARP table is stored into the ROM and cannot be overwritten during runtime.
IP instances using this static ARP table will never send ARP requests for the configured addresses.</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:postBuildSelectableChangeable">false</SD>
																		<SD GID="DV:postBuildNotDeletable">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<SUB-CONTAINERS>
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="a768cd34-9ec8-48a0-80df-89635ce68e08">
																	<SHORT-NAME>TcpIpStaticArpEntry</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container includes a static IP address to physical address mapping.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:CfgPostBuild">
																				<SD GID="DV:postBuildSelectableChangeable">false</SD>
																				<SD GID="DV:postBuildNotDeletable">false</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
																	<PARAMETERS>
																		<ECUC-STRING-PARAM-DEF UUID="f1ceebbd-85f7-4df7-b9c6-a8c7cb024d5d">
																			<SHORT-NAME>TcpIpStaticArpEntryIpAddr</SHORT-NAME>
																			<LONG-NAME>
																				<L-4 L="EN">IPv4 Address</L-4>
																			</LONG-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines the IP address that shall be statically mapped to the physical address defined in IpV4StaticArpEntryPhysAddr</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<ECUC-STRING-PARAM-DEF-VARIANTS>
																				<ECUC-STRING-PARAM-DEF-CONDITIONAL>
																					<MAX-LENGTH>15</MAX-LENGTH>
																					<MIN-LENGTH>7</MIN-LENGTH>
																				</ECUC-STRING-PARAM-DEF-CONDITIONAL>
																			</ECUC-STRING-PARAM-DEF-VARIANTS>
																		</ECUC-STRING-PARAM-DEF>
																		<ECUC-STRING-PARAM-DEF UUID="b0e5ab81-c88d-491f-8a24-************">
																			<SHORT-NAME>TcpIpStaticArpEntryPhysAddr</SHORT-NAME>
																			<LONG-NAME>
																				<L-4 L="EN">Physical Address</L-4>
																			</LONG-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines the physical address that shall be used for outgoing packets to the IP address specified in IpV4StaticArpEntryIpAddr</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<ECUC-STRING-PARAM-DEF-VARIANTS>
																				<ECUC-STRING-PARAM-DEF-CONDITIONAL>
																					<MAX-LENGTH>17</MAX-LENGTH>
																					<MIN-LENGTH>17</MIN-LENGTH>
																				</ECUC-STRING-PARAM-DEF-CONDITIONAL>
																			</ECUC-STRING-PARAM-DEF-VARIANTS>
																		</ECUC-STRING-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
															</SUB-CONTAINERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: TcpIpIpV6Config -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="852b8696-f018-4f10-a8d4-8705bd6fb938">
													<SHORT-NAME>TcpIpIpV6Config</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Specifies the configuration parameters of the IPv6 (Internet Protocol version 6) sub-module.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SUB-CONTAINERS>
														<!-- Container Definition: TcpIpDhcpV6Config -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="5f7f6e81-f441-40f0-9fc5-c4c72eb6dba7">
															<SHORT-NAME>TcpIpDhcpV6Config</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Specifies the configuration parameters of the DHCPv6.</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:postBuildSelectableChangeable">false</SD>
																		<SD GID="DV:postBuildNotDeletable">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">This container may be referenced by multiple IPv6 instances if they shall use the same configuration.
                                                        This container may have multiple instances if different configurations are required for different IPv6 instances.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: TcpIpDhcpV6CnfDelayMax -->
																<ECUC-FLOAT-PARAM-DEF UUID="14b41014-748f-4bbc-98d4-cc57574a0da2">
																	<SHORT-NAME>TcpIpDhcpV6CnfDelayMax</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Maximum delay (s) before sending the first Confirm message. If this value is bigger than the previous minimum delay value a random delay will be chosen from the interval.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>1</DEFAULT-VALUE>
																	<MAX>100</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<!-- PARAMETER DEFINITION: TcpIpDhcpV6CnfDelayMin -->
																<ECUC-FLOAT-PARAM-DEF UUID="0d90a415-ff8a-430c-a2db-6ac88d6481dd">
																	<SHORT-NAME>TcpIpDhcpV6CnfDelayMin</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Minimum delay (s) before the first Confirm message will be sent.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>0</DEFAULT-VALUE>
																	<MAX>100</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<!-- PARAMETER DEFINITION: TcpIpDhcpV6InfDelayMax -->
																<ECUC-FLOAT-PARAM-DEF UUID="b038737f-723b-4b4b-a9a8-aeb6e3ed65bc">
																	<SHORT-NAME>TcpIpDhcpV6InfDelayMax</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Maximum delay (s) before sending the first Information Request message. If this value is bigger than the previous minimum delay value a random delay will be chosen from the interval.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>1</DEFAULT-VALUE>
																	<MAX>100</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<!-- PARAMETER DEFINITION: TcpIpDhcpV6InfDelayMin -->
																<ECUC-FLOAT-PARAM-DEF UUID="bc9bca3e-5f34-4db1-975f-fbc1d0a60b34">
																	<SHORT-NAME>TcpIpDhcpV6InfDelayMin</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Minimum delay (s) before the first Information Request message will be sent.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>0</DEFAULT-VALUE>
																	<MAX>100</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<!-- PARAMETER DEFINITION: TcpIpDhcpV6SolDelayMax -->
																<ECUC-FLOAT-PARAM-DEF UUID="9d7f93e6-cbff-4cab-9bd2-f2889df7a334">
																	<SHORT-NAME>TcpIpDhcpV6SolDelayMax</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Maximum delay (s) before sending the first Solicit message. If this value is bigger than the previous minimum delay value a random delay will be chosen from the interval.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>1</DEFAULT-VALUE>
																	<MAX>100</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<!-- PARAMETER DEFINITION: TcpIpDhcpV6SolDelayMin -->
																<ECUC-FLOAT-PARAM-DEF UUID="0aee8c0b-742b-4547-ac3e-69fc383fbe4e">
																	<SHORT-NAME>TcpIpDhcpV6SolDelayMin</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Minimum delay (s) before the first Solicit message will be sent.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>0</DEFAULT-VALUE>
																	<MAX>100</MAX>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<ECUC-FLOAT-PARAM-DEF UUID="09f2980f-1926-4d47-a909-7f477089cbc9">
																	<SHORT-NAME>TcpIpDhcpV6ClientIdTime</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Client DUID Time</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">Time value used to build the DHCP Unique Identifier (DUID) of the client.

The Identifier of the client will be built from this time value and the link-layer address of this node.

"The time value is the time that the DUID is generated represented in seconds since midnight (UTC), January 1, 2000, modulo 2^32."

[RFC3315 9.2. DUID Based on Link-layer Address Plus Time [DUID-LLT]]</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>0</DEFAULT-VALUE>
																	<MIN>0</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<ECUC-BOOLEAN-PARAM-DEF UUID="9ca84df5-a99c-4f88-b7a7-1df6f6c2c87d">
																	<SHORT-NAME>TcpIpDhcpV6RandomizeTimeouts</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Randomize Timeouts</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">Use randomization factor for computing retransmission timeouts.

"Each of the computations of a new RT include a randomization factor (RAND), which is a random number chosen with a uniform distribution between -0.1 and +0.1. The randomization factor is included to minimize synchronization of messages transmitted by DHCP clients."

[RFC3315 14. Reliability of Client Initiated Message Exchanges]</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>true</DEFAULT-VALUE>
																</ECUC-BOOLEAN-PARAM-DEF>
																<ECUC-INTEGER-PARAM-DEF UUID="635352f8-face-4394-b1b6-fe8f001d2539">
																	<SHORT-NAME>TcpIpDhcpV6TxMsgBufferLen</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">DHCP Tx Message Buffer Size</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">Size of the buffer for outgoing DHCPv6 messages. The size of a DHCPv6 message depends on the number and length of options included in it.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>128</DEFAULT-VALUE>
																	<MAX>1280</MAX>
																	<MIN>64</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<ECUC-BOOLEAN-PARAM-DEF UUID="11deef79-8a1d-4657-8309-bdf40845b846">
																	<SHORT-NAME>TcpIpDhcpV6UseFirstValidAdv</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Use first valid advertisment</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">If enabled the Dhcp will not wait for other advertise messages if a valid one has been received but the preference is less than 255.

"A client MUST collect Advertise messages for the first RT seconds, unless it receives an Advertise message with a preference value of 255"

[RFC3315 17.1.2. Transmission of Solicit Messages]</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>false</DEFAULT-VALUE>
																</ECUC-BOOLEAN-PARAM-DEF>
																<ECUC-BOOLEAN-PARAM-DEF UUID="21e6a08b-1dcd-424a-a305-49fedcd87aaf">
																	<SHORT-NAME>TcpIpDhcpV6Rfc3646DomainListOptEnabled</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Enable Domain List Option</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">If enabled the DhcpV6 will notify the Dns of dns search lists contained in DOMAIN_LIST options of received DHCPv6 Reply messages.

[RFC3646 4. Domain Search List option]

Requires Dns Component.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>false</DEFAULT-VALUE>
																</ECUC-BOOLEAN-PARAM-DEF>
																<ECUC-BOOLEAN-PARAM-DEF UUID="23292d42-0474-4fb4-adc4-83a276342dd6">
																	<SHORT-NAME>TcpIpDhcpV6Rfc3646DnsServersOptEnabled</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Enable DNS Server Option</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">If enabled the DhcpV6 will notify the Dns of dns server addresses contained in DNS_SERVERS options of received DHCPv6 Reply messages.

[RFC3646 3. DNS Recursive Name Server option]

Requires Dns Component.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>false</DEFAULT-VALUE>
																</ECUC-BOOLEAN-PARAM-DEF>
															</PARAMETERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: TcpIpIcmpV6Config -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="46a4dd20-d4d4-44c5-9bc5-624ea7e7edf1">
															<SHORT-NAME>TcpIpIcmpV6Config</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Specifies the configuration parameters of the ICMPv6 (Internet Control Message Protocol for IPv6) sub-module.</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:postBuildSelectableChangeable">false</SD>
																		<SD GID="DV:postBuildNotDeletable">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: TcpIpIcmpV6EchoDataBufferSize -->
																<ECUC-INTEGER-PARAM-DEF UUID="b36675e5-699a-4ac0-b29d-fc9bcae8534f">
																	<SHORT-NAME>TcpIpIcmpV6EchoDataBufferSize</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Configure the maximum amount of arbitrary data that will be sent back to the sender of an echo request. If the arbitrary data of an incoming echo request is bigger than configured here, the response payload will be truncated.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>64</DEFAULT-VALUE>
																	<MAX>1452</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: TcpIpIcmpV6EchoReplyEnabled -->
																<ECUC-BOOLEAN-PARAM-DEF UUID="f467b05d-b327-4a56-9eac-a7bc29ae0479">
																	<SHORT-NAME>TcpIpIcmpV6EchoReplyEnabled</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">If enabled, the stack will respond to incoming ICMPv6 Echo Requests (Pings).</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>false</DEFAULT-VALUE>
																</ECUC-BOOLEAN-PARAM-DEF>
																<!-- PARAMETER DEFINITION: TcpIpIcmpV6HopLimit -->
																<ECUC-INTEGER-PARAM-DEF UUID="eec28e79-7517-4f58-94ba-1ad1b8ba07c7">
																	<SHORT-NAME>TcpIpIcmpV6HopLimit</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Default Hop-Limit value of outgoing ICMPv6 packets.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>64</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: TcpIpIcmpV6MsgDestinationUnreachableEnabled -->
																<ECUC-BOOLEAN-PARAM-DEF UUID="0e915275-a801-414b-85ff-e4c1948dbc97">
																	<SHORT-NAME>TcpIpIcmpV6MsgDestinationUnreachableEnabled</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Dis/Enables transmission of Destination Unreachable Messages</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>false</DEFAULT-VALUE>
																</ECUC-BOOLEAN-PARAM-DEF>
																<!-- PARAMETER DEFINITION: TcpIpIcmpV6MsgParameterProblemEnabled -->
																<ECUC-BOOLEAN-PARAM-DEF UUID="ea3c1229-fd7f-43fe-aa93-117f8655f428">
																	<SHORT-NAME>TcpIpIcmpV6MsgParameterProblemEnabled</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">If enabled an ICMPv6 parameter problem message will be sent if a received packet has been dropped due to unknown options or headers that are found in the packet.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">[RFC2460 4. IPv6 Extension Headers]</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>true</DEFAULT-VALUE>
																</ECUC-BOOLEAN-PARAM-DEF>
																<ECUC-BOOLEAN-PARAM-DEF UUID="66401d45-ee93-4afe-ab27-ed2be9abff95">
																	<SHORT-NAME>TcpIpIcmpV6EchoRequestApiEnabled</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Enable Echo Request API</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">This parameter defines whether the API function IpV6_Icmp_TxEchoRequest() is available. This function can be used to send outgoing Echo Requests (Pings) to other Hosts.

Note: This function is for debugging purposes only. Incoming Echo Replies are currently not handled by the ICMPv6.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>false</DEFAULT-VALUE>
																</ECUC-BOOLEAN-PARAM-DEF>
																<ECUC-BOOLEAN-PARAM-DEF UUID="abfa5045-770f-40bf-8752-618c3b76f437">
																	<SHORT-NAME>TcpIpIcmpV6ErrorMessagesEnabled</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Send ICMP Error Messages</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">This parameter defines whether ICMPv6 Error Messages will be sent as specified in [RFC4443].

[RFC4443 3.2. Packet Too Big Message]
[RFC4443 3.3. Time Exceeded Message]
[RFC4443 3.4. Parameter Problem Message]

If disabled, no ICMPv6 Error Messages will be sent</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>true</DEFAULT-VALUE>
																</ECUC-BOOLEAN-PARAM-DEF>
																<ECUC-INTEGER-PARAM-DEF UUID="bc7714eb-33f6-4064-8717-8c47f95051e7">
																	<SHORT-NAME>TcpIpIcmpV6TxMessageBufferSize</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Tx Message Buffer Size</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">This value specifies the maximum size of outgoing ICMPv6 messages.
This value should be 1280 if IpV6EnableIcmpErrMsgs is enabled.
If the value is smaller the Original Packet payload in ICMPv6 Error messages will be truncated.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">BYTE</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>1280</DEFAULT-VALUE>
																	<MAX>1280</MAX>
																	<MIN>48</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
															<SUB-CONTAINERS>
																<!-- Container Definition: TcpIpIcmpV6MsgHandler -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="a7814f65-8b08-4b6f-a254-e74d836e432f">
																	<SHORT-NAME>TcpIpIcmpV6MsgHandler</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container is a subcontainer of TcpIpIcmpConfig and specifies the configuration parameters for the ICMPv6 message handler.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:CfgPostBuild">
																				<SD GID="DV:postBuildSelectableChangeable">false</SD>
																				<SD GID="DV:postBuildNotDeletable">false</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: TcpIpIcmpV6MsgHandlerHeaderFileName -->
																		<ECUC-STRING-PARAM-DEF UUID="9493c1bf-918c-486c-963f-1a7485740adf">
																			<SHORT-NAME>TcpIpIcmpV6MsgHandlerHeaderFileName</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter specifies the name of the header file containing the definition of the ICMPv6 message handler function.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<ECUC-STRING-PARAM-DEF-VARIANTS>
																				<ECUC-STRING-PARAM-DEF-CONDITIONAL>
																					<MAX-LENGTH>32</MAX-LENGTH>
																					<MIN-LENGTH>1</MIN-LENGTH>
																				</ECUC-STRING-PARAM-DEF-CONDITIONAL>
																			</ECUC-STRING-PARAM-DEF-VARIANTS>
																		</ECUC-STRING-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpIcmpV6MsgHandlerName -->
																		<ECUC-FUNCTION-NAME-DEF UUID="c392dd9f-e48a-43de-96e9-dce3ecd00dba">
																			<SHORT-NAME>TcpIpIcmpV6MsgHandlerName</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines the name of the ICMP message handler function &lt;User_IcmpMsgHandler&gt;.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																		</ECUC-FUNCTION-NAME-DEF>
																		<ECUC-FUNCTION-NAME-DEF UUID="5e39b3b4-d74c-474b-8675-228cb905a0f4">
																			<SHORT-NAME>TcpIpIcmpV6MaxPayloadLenChgHandlerName</SHORT-NAME>
																			<LONG-NAME>
																				<L-4 L="EN">Maximum Payload Length Change Callback Function</L-4>
																			</LONG-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines a function that will be called when the maximum payload length, that fits into one IPv6 packet, changes due to MTU limitations on the path to a specific destination.

see [RFC1981 Path MTU Discovery for IP version 6]

Callback function signature: void Cbk_Name(uint8 CtrlIdx, const IpBase_SockAddrType DstAddrPtr, uint16 Mtu)

For reasons of efficiency at least the callback "TcpIp_Cbk_VPathMtuChg" of "TcpIp_Cbk.h" should be configured here in order to avoid that TCP transmits packets that need to be fragmented by the IP layer.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																		</ECUC-FUNCTION-NAME-DEF>
																		<ECUC-FUNCTION-NAME-DEF UUID="526b7403-d881-445b-be07-35f71cb61391">
																			<SHORT-NAME>TcpIpIcmpV6IndAddrListReceivedHandlerName</SHORT-NAME>
																			<LONG-NAME>
																				<L-4 L="EN">Inverse ND Address List Received Callback Function</L-4>
																			</LONG-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines a function that will be called when an Inverse Neighbor Discovery Solicitation or Advertisement with an Address List Option [RFC3122] is received.

Callback function signature: void Cbk_Name(uint8 CtrlIdx, IPV6_P2C(Eth_PhysAddrType) RemoteLLAddrPtr, IPV6_P2C(IpV6_AddrType) AddrListPtr, uint8 AddrCount);</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																		</ECUC-FUNCTION-NAME-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
															</SUB-CONTAINERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: TcpIpIpV6FragmentationConfig -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="9a9db678-2190-4606-a8c1-8be8a0efba94">
															<SHORT-NAME>TcpIpIpV6FragmentationConfig</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Specifies the configuration parameters of IPv6 packet fragmentation/reassembly.</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:postBuildSelectableChangeable">false</SD>
																		<SD GID="DV:postBuildNotDeletable">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">This container may be referenced by multiple IPv6 instances if they shall use the same configuration.
                                                        This container may have multiple instances if different configurations are required for different IPv6 instances.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: TcpIpIpV6ReassemblyBufferCount -->
																<ECUC-INTEGER-PARAM-DEF UUID="5d040239-ec67-4a1a-8d33-ff96f8626ef1">
																	<SHORT-NAME>TcpIpIpV6ReassemblyBufferCount</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Number of buffers that can be used for fragment reassembly. In case of a reassembly error or if not all fragments are received in time this buffer will be blocked until the specified "Fragment Reassembly Timeout" has been exceeded.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">A value of 0 disables fragment reassembly.

                                                                [RFC2460 5. Packet Size Issues]
                                                                "In order to send a packet larger than a path's MTU, a node may use the IPv6 Fragment header to fragment the packet at the source and have it reassembled at the destination(s).  However, the use of such fragmentation is discouraged in any application that is able to adjust its packets to fit the measured path MTU (i.e., down to 1280 octets)."</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>2</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: TcpIpIpV6ReassemblyBufferSize -->
																<ECUC-INTEGER-PARAM-DEF UUID="5909005a-0115-4e2c-a207-dea162d33528">
																	<SHORT-NAME>TcpIpIpV6ReassemblyBufferSize</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">[RFC2460 5. Packet Size Issues]</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">"A node must be able to accept a fragmented packet that, after reassembly, is as large as 1500 octets.  A node is permitted to accept fragmented packets that reassemble to more than 1500 octets."the measured path MTU (i.e., down to 1280 octets)."</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>1500</DEFAULT-VALUE>
																	<MAX>65535</MAX>
																	<MIN>1500</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: TcpIpIpV6ReassemblySegmentCount -->
																<ECUC-INTEGER-PARAM-DEF UUID="aefaa68f-2dd8-4e1a-a9af-4139effa2258">
																	<SHORT-NAME>TcpIpIpV6ReassemblySegmentCount</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Specifies the maximum number of consecutive data segments that can be managed in each reassembly buffer. If all fragments are received in order, only one segment will be needed.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">To deal with fragments received out of order this value should be configured bigger than 1.</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>5</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>1</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: TcpIpIpV6ReassemblyTimeout -->
																<ECUC-FLOAT-PARAM-DEF UUID="a08567cf-0653-4778-8374-f7d8e2e5b9fd">
																	<SHORT-NAME>TcpIpIpV6ReassemblyTimeout</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">[RFC2460 4.5 Fragment Header]</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:Display">
																				<SD GID="DV:BaseUnit">SEC</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">Default: 60 seconds</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>60</DEFAULT-VALUE>
																	<MAX>100</MAX>
																	<MIN>0.001</MIN>
																</ECUC-FLOAT-PARAM-DEF>
																<!-- PARAMETER DEFINITION: TcpIpIpV6TxFragmentBufferCount -->
																<ECUC-INTEGER-PARAM-DEF UUID="e982f80e-cf78-46b6-87b2-1419207a9f9c">
																	<SHORT-NAME>TcpIpIpV6TxFragmentBufferCount</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">These buffers will be used if the IpV6 receives packets from the upper layer that do not fit into the MTU and thus must be fragmented.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">A value of 0 disables tx fragmentation.

                                                                If the upper layer transmits packets that do not fit into the link or path MTU, the IpV6 will split-up the packet into fragments.

                                                                see "Enable Fragment Reassembly"</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>2</DEFAULT-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: TcpIpIpV6TxFragmentBufferSize -->
																<ECUC-INTEGER-PARAM-DEF UUID="d59e41e1-d3a7-4ebc-852a-a8df995b9f34">
																	<SHORT-NAME>TcpIpIpV6TxFragmentBufferSize</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Size of each fragment tx buffer in bytes</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>1500</DEFAULT-VALUE>
																	<MAX>65535</MAX>
																	<MIN>1500</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: TcpIpNdpConfig -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="5c419794-dad8-4742-aacf-19f3fbe10fd0">
															<SHORT-NAME>TcpIpNdpConfig</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Specifies the configuration parameters of the Neighbor Discovery Protocol for IPv6</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:postBuildSelectableChangeable">false</SD>
																		<SD GID="DV:postBuildNotDeletable">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">This container may be referenced by multiple IPv6 instances if they shall use the same configuration.
                                                        This container may have multiple instances if different configurations are required for different IPv6 instances.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<PARAMETERS>
																<ECUC-BOOLEAN-PARAM-DEF UUID="a27f2540-ca40-4cb8-91f9-cb3140b7ad99">
																	<SHORT-NAME>TcpIpNdpRfc6106DnsslOptEnabled</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Enable DNS Search List Option</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">This parameter defines whether the IpV6 will notify the DNS of domain names of DNS suffixes contained in DNSSL options of received Router Advertisements.

[RFC6106 5.2. DNS Search List Option]</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>false</DEFAULT-VALUE>
																</ECUC-BOOLEAN-PARAM-DEF>
																<ECUC-BOOLEAN-PARAM-DEF UUID="da20026f-8187-4d37-b13e-8be3d8dd0d9e">
																	<SHORT-NAME>TcpIpNdpRfc6106RdnssOptEnabled</SHORT-NAME>
																	<LONG-NAME>
																		<L-4 L="EN">Enable Recursive DNS Server Option</L-4>
																	</LONG-NAME>
																	<DESC>
																		<L-2 L="EN">This parameter defines whether the IpV6 will notify the DNS of dns server adresses contained in RDNS options of received Router Advertisements.

[RFC6106 5.1. Recursive DNS Server Option]</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>Vector Informatik</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>false</DEFAULT-VALUE>
																</ECUC-BOOLEAN-PARAM-DEF>
															</PARAMETERS>
															<SUB-CONTAINERS>
																<!-- Container Definition: TcpIpNdpArNudConfig -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="6721979b-d7d3-417d-88b2-432d2320e0a1">
																	<SHORT-NAME>TcpIpNdpArNudConfig</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Specifies the configuration parameters for NDP Address Resolution and Neighbor Unreachability Detection.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:CfgPostBuild">
																				<SD GID="DV:postBuildSelectableChangeable">false</SD>
																				<SD GID="DV:postBuildNotDeletable">false</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: TcpIpNdpDefaultReachableTime -->
																		<ECUC-FLOAT-PARAM-DEF UUID="0b691f1a-ffb8-4bb3-a985-6dd9cb517d7e">
																			<SHORT-NAME>TcpIpNdpDefaultReachableTime</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Configuration of the ReachableTime (s) specified in [RFC4861 6.3.2. Host Variables].</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:BaseUnit">SEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">"The time a neighbor is considered reachable after receiving a reachability confirmation."

                                                                        If "TcpIpNdpDynamicReachableTimeEnabled" is checked, this value may be reconfigured based on received Router Advertisements.

                                                                        Default: REACHABLE_TIME = 30 seconds</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>30</DEFAULT-VALUE>
																			<MAX>120</MAX>
																			<MIN>0</MIN>
																		</ECUC-FLOAT-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpDefaultRetransTimer -->
																		<ECUC-FLOAT-PARAM-DEF UUID="0e7c1a2a-d5a8-4861-9502-7ec78a4f7004">
																			<SHORT-NAME>TcpIpNdpDefaultRetransTimer</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Configures the default value (s) for the RetransTimer variable specified in [RFC4861 6.3.2. Host Variables].</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:BaseUnit">SEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">"The time between retransmissions of Neighbor Solicitation messages to a neighbor when resolving the address or when probing the reachability of a neighbor."

                                                                        If "TcpIpNdpDynamicRetransTimeEnabled" is checked, this value may be reconfigured based on received Router Advertisements.

                                                                        Default: RETRANS_TIMER = 1 second</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>1</DEFAULT-VALUE>
																			<MAX>60</MAX>
																			<MIN>0</MIN>
																		</ECUC-FLOAT-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpDelayFirstProbeTime -->
																		<ECUC-FLOAT-PARAM-DEF UUID="4415dfcf-ca74-4e38-bb2b-f299de4c1a07">
																			<SHORT-NAME>TcpIpNdpDelayFirstProbeTime</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Delay before sending the first NUD probe in (s).</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:BaseUnit">SEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">[RFC4861 7.3.3. Node Behavior]

                                                                        Default: DELAY_FIRST_PROBE_TIME = 5 seconds</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>5</DEFAULT-VALUE>
																			<MAX>60</MAX>
																			<MIN>0</MIN>
																		</ECUC-FLOAT-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpMaxNeighborCacheSize -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ca7fe087-4682-452e-807e-c3a08c7a7cf1">
																			<SHORT-NAME>TcpIpNdpMaxNeighborCacheSize</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Maximum number of entries in the neighbor cache.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">[RFC4861 5.1. Conceptual Data Structures]</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>5</DEFAULT-VALUE>
																			<MAX>254</MAX>
																			<MIN>1</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpMaxRandomFactor -->
																		<ECUC-INTEGER-PARAM-DEF UUID="d4ca1f2c-720d-411e-ab60-c86d557c9557">
																			<SHORT-NAME>TcpIpNdpMaxRandomFactor</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Maximum random factor used for randomization</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">[RFC4861 10. Protocol Constants]

                                                                        Default: 15 (MAX_RANDOM_FACTOR = 1.5)</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>15</DEFAULT-VALUE>
																			<MAX>100</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpMinRandomFactor -->
																		<ECUC-INTEGER-PARAM-DEF UUID="4753de2b-8168-4026-b2ff-804c1b79ccdc">
																			<SHORT-NAME>TcpIpNdpMinRandomFactor</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Minimum random factor used for randomization</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">[RFC4861 10. Protocol Constants]

                                                                        Default: 5 (MIN_RANDOM_FACTOR = 0.5)</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>5</DEFAULT-VALUE>
																			<MAX>100</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpNeighborUnreachabilityDetectionEnabled -->
																		<ECUC-BOOLEAN-PARAM-DEF UUID="aa7cc7f9-c894-4273-9873-8699c1b1e526">
																			<SHORT-NAME>TcpIpNdpNeighborUnreachabilityDetectionEnabled</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Neighbor Unreachability Detection is used to remove unused entries from the neighbor cache. This feature is a basic feature of NDP and should be turned on.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>true</DEFAULT-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpNumMulticastSolicitations -->
																		<ECUC-INTEGER-PARAM-DEF UUID="cb9c4e2a-d2db-40f9-83e6-c952b1524b8b">
																			<SHORT-NAME>TcpIpNdpNumMulticastSolicitations</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Maximum number of multicast solicitations that will be sent when performing address resolution.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">[RFC4861 7.2.2. Sending Neighbor Solicitations]

                                                                        Default: MAX_MULTICAST_SOLICIT = 3</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>3</DEFAULT-VALUE>
																			<MAX>255</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpNumUnicastSolicitations -->
																		<ECUC-INTEGER-PARAM-DEF UUID="87799990-5fbe-4566-a4d1-fd713ff5a283">
																			<SHORT-NAME>TcpIpNdpNumUnicastSolicitations</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Maximum number of unicast solicitations that will be sent when performig Neighbor Unreachability Detection.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">[RFC4861 7.3.3. Node Behavior]

                                                                        Default: MAX_UNICAST_SOLICIT = 3</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>3</DEFAULT-VALUE>
																			<MAX>255</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpPacketQueueEnabled -->
																		<ECUC-BOOLEAN-PARAM-DEF UUID="5b480a6d-7885-4940-ac6b-bc573c5c8943">
																			<SHORT-NAME>TcpIpNdpPacketQueueEnabled</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Enables (TRUE) or disables (FALSE) support of a NDP Packet Queue according to IETF RFC 4861, section 7.2.2.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>true</DEFAULT-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpRandomReachableTimeEnabled -->
																		<ECUC-BOOLEAN-PARAM-DEF UUID="dae601b8-5cfc-4dd4-99bb-c6eae1c5aa90">
																			<SHORT-NAME>TcpIpNdpRandomReachableTimeEnabled</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">If enabled the value of ReachableTime will be multiplied with a random value between MIN_RANDOM_FACTOR and MAX_RANDOM_FACTOR in order to prevent multiple nodes from transmitting at exactly the same time</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">[RFC4861 6.3.2. Host Variables / ReachableTime]</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>true</DEFAULT-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																		<ECUC-BOOLEAN-PARAM-DEF UUID="8c81e7cf-ab55-47a2-ab38-27fdda22df4b">
																			<SHORT-NAME>TcpIpNdpInverseNaEnabled</SHORT-NAME>
																			<LONG-NAME>
																				<L-4 L="EN">Inverse Advertisements</L-4>
																			</LONG-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines whether Inverse Neighbor Advertisements are processed or silently discarded.
This also enables the API IpV6_Ndp_SendInverseSolicitation().

Use "User Callback Configuration" to configure a callback function that handles received Address Lists.

If disabled no Inverse Neighbor Solicitations can be sent and received Inverse Neighbor Advertisements will be silently ignored.

[RFC3122 4.2.2 Processing Inverse Neighbor Advertisement Messages]</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>false</DEFAULT-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																		<ECUC-BOOLEAN-PARAM-DEF UUID="ad52eedc-11a4-4e4a-ad55-b82d98ba116c">
																			<SHORT-NAME>TcpIpNdpInverseNsEnabled</SHORT-NAME>
																			<LONG-NAME>
																				<L-4 L="EN">Inverse Solicitations</L-4>
																			</LONG-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines whether Inverse Neighbor Advertisements are sent in response to received Inverse Neighbor Solicitations.

Use "User Callback Configuration" to configure a callback function that handles received Address Lists.

If disabled received Inverse Neighbor Solicitations will be silently ignored.

[RFC3122 4.2.2 Processing Inverse Neighbor Advertisement Messages]</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>false</DEFAULT-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																		<ECUC-BOOLEAN-PARAM-DEF UUID="752b17eb-edf2-4c01-b21e-70732a4556c5">
																			<SHORT-NAME>TcpIpNdpNeighborCacheUpdateByInverseNaEnabled</SHORT-NAME>
																			<LONG-NAME>
																				<L-4 L="EN">Inverse Advertisement Neighbor Cache Updates</L-4>
																			</LONG-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines whether the Neighbor Cache will be updated with information from received Inverse Neighbor Advertisements.

[RFC3122 4.2.2 Processing Inverse Neighbor Advertisement Messages]</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>true</DEFAULT-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: TcpIpNdpPrefixRouterDiscoveryConfig -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="d4f8ca7e-57f3-41ca-adb8-e310706bbb1c">
																	<SHORT-NAME>TcpIpNdpPrefixRouterDiscoveryConfig</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Specifies the configuration parameters for NDP Prefix and Router Discovery.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:CfgPostBuild">
																				<SD GID="DV:postBuildSelectableChangeable">false</SD>
																				<SD GID="DV:postBuildNotDeletable">false</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: TcpIpNdpDefaultRouterListSize -->
																		<ECUC-INTEGER-PARAM-DEF UUID="038358c8-5751-461d-bc16-4968fbae9fd3">
																			<SHORT-NAME>TcpIpNdpDefaultRouterListSize</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Maximum number of default router entries.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">[RFC4861 5.1. Conceptual Data Structures]</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>2</DEFAULT-VALUE>
																			<MAX>254</MAX>
																			<MIN>2</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpDestinationCacheSize -->
																		<ECUC-INTEGER-PARAM-DEF UUID="7e4351eb-50b0-4f79-8cf5-875c3b0920cc">
																			<SHORT-NAME>TcpIpNdpDestinationCacheSize</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Maximum number of entries in the destination cache.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">[RFC4861 5.1. Conceptual Data Structures]</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>5</DEFAULT-VALUE>
																			<MAX>254</MAX>
																			<MIN>1</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpDynamicHopLimitEnabled -->
																		<ECUC-BOOLEAN-PARAM-DEF UUID="dba1de79-d34f-4a46-856d-55de58f15b2d">
																			<SHORT-NAME>TcpIpNdpDynamicHopLimitEnabled</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">If enabled the default hop limit may be reconfigured based on received Router Advertisements.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">[RFC4861 6.3.4. Processing Received Router Advertisements]</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>true</DEFAULT-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpDynamicMtuEnabled -->
																		<ECUC-BOOLEAN-PARAM-DEF UUID="2112e1c0-6998-4d65-a691-5b0528648b4f">
																			<SHORT-NAME>TcpIpNdpDynamicMtuEnabled</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Allow dynamic reconfiguration of link MTU via Router Advertisements.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">[RFC4861 4.6.4. MTU]</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>false</DEFAULT-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpDynamicReachableTimeEnabled -->
																		<ECUC-BOOLEAN-PARAM-DEF UUID="92b93b2c-9e22-4dfd-8549-b14ffc0b7151">
																			<SHORT-NAME>TcpIpNdpDynamicReachableTimeEnabled</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">If enabled the default Reachable Time value may be reconfigured based on received Router Advertisements.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">[RFC4861 6.3.4. Processing Received Router Advertisements]

                                                                        Default: Enabled</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>true</DEFAULT-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpDynamicRetransTimeEnabled -->
																		<ECUC-BOOLEAN-PARAM-DEF UUID="3024653e-6fa5-4670-8957-ab2d567ae9d7">
																			<SHORT-NAME>TcpIpNdpDynamicRetransTimeEnabled</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">If enabled the default Retransmit Timer value may be reconfigured based on received Router Advertisements.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">[RFC4861 6.3.4. Processing Received Router Advertisements]

                                                                        Default: Enabled</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>true</DEFAULT-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpMaxRtrSolicitationDelay -->
																		<ECUC-FLOAT-PARAM-DEF UUID="f4956d66-3569-432c-be99-392231d54cda">
																			<SHORT-NAME>TcpIpNdpMaxRtrSolicitationDelay</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Maximum delay before the first Router Solicitation will be sent after interface initialization in (s).</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:BaseUnit">SEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">[RFC4861 6.3.7. Sending Router Solicitations]

                                                                        Default: MAX_RTR_SOLICITATION_DELAY = 1 second</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>1</DEFAULT-VALUE>
																			<MAX>60</MAX>
																			<MIN>0.001</MIN>
																		</ECUC-FLOAT-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpMaxRtrSolicitations -->
																		<ECUC-INTEGER-PARAM-DEF UUID="68f10eb3-b089-4b73-85ca-7dc45ecdb43f">
																			<SHORT-NAME>TcpIpNdpMaxRtrSolicitations</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Maximum number of Router Solicitations that will be sent before the first Router Advertisement has been received.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">0 = No Router Solicitations will be sent.
                                                                        This has no impact on handling Router Advertisements.

                                                                        [RFC4861 6.3.7. Sending Router Solicitations]

                                                                        Default: MAX_RTR_SOLICITATIONS = 3 transmissions</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>3</DEFAULT-VALUE>
																			<MAX>255</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpPrefixListSize -->
																		<ECUC-INTEGER-PARAM-DEF UUID="f2fb15a1-12db-408b-b3af-986ee1ba55a3">
																			<SHORT-NAME>TcpIpNdpPrefixListSize</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Maximum number of entries in the on-link prefix list.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">[RFC4861 5.1. Conceptual Data Structures]</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>5</DEFAULT-VALUE>
																			<MAX>254</MAX>
																			<MIN>1</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpRndRtrSolicitationDelayEnabled -->
																		<ECUC-BOOLEAN-PARAM-DEF UUID="44bf4182-733b-4bbf-9a1c-9b7e4fa911d5">
																			<SHORT-NAME>TcpIpNdpRndRtrSolicitationDelayEnabled</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">If enabled the first router solicitation will be delayed randomly from [0...MAX_RTR_SOLICITATION_DELAY]. Otherwise the first router solicitation will be sent after exactly MAX_RTR_SOLICITATION_DELAY milliseconds.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">[RFC4861 6.3.7. Sending Router Solicitations]

                                                                        Default: Enabled</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>true</DEFAULT-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpRtrSolicitationInterval -->
																		<ECUC-FLOAT-PARAM-DEF UUID="96d7e9d6-ee3e-4ce6-aa79-ff4607b07c36">
																			<SHORT-NAME>TcpIpNdpRtrSolicitationInterval</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Interval between consecutive Router Solicitations in (s).</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:BaseUnit">SEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">[RFC4861 6.3.7. Sending Router Solicitations]

                                                                        Default: RTR_SOLICITATION_INTERVAL = 4 seconds</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>4</DEFAULT-VALUE>
																			<MAX>60</MAX>
																			<MIN>0.001</MIN>
																		</ECUC-FLOAT-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: TcpIpNdpSlaacConfig -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="90d9871b-fc6b-4427-8819-7b24868d4fa4">
																	<SHORT-NAME>TcpIpNdpSlaacConfig</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Specifies the configuration parameters for StateLess Address AutoConfiguration.</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:CfgPostBuild">
																				<SD GID="DV:postBuildSelectableChangeable">false</SD>
																				<SD GID="DV:postBuildNotDeletable">false</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: TcpIpNdpSlaacDadNumberOfTransmissions -->
																		<ECUC-INTEGER-PARAM-DEF UUID="405e7fe8-66bf-4f88-baf7-63e6f560cf3b">
																			<SHORT-NAME>TcpIpNdpSlaacDadNumberOfTransmissions</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Number of Neighbor Solicitations that have to be unanswered in order to set an autoconfigurated address to PREFERRED (usable) state.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">[RFC4861 5.1. Node Configuration Variables]

                                                                        Default: DupAddrDetectTransmits = 1

                                                                        Setting this value to 0 turns off DAD.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>1</DEFAULT-VALUE>
																			<MAX>254</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpSlaacDadRetransmissionDelay -->
																		<ECUC-FLOAT-PARAM-DEF UUID="be24e378-7ad5-43ac-91a7-7bb6cf7b9ba5">
																			<SHORT-NAME>TcpIpNdpSlaacDadRetransmissionDelay</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Sets the maximum value for the address configuration delay (s).</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:BaseUnit">SEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">According to [RFC4861 5.4.2. Sending Neighbor Solicitation Messages] this value should be the same as MAX_RTR_SOLICITATION_DELAY.

                                                                        Default: MAX_RTR_SOLICITATION_DELAY = 1 second</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>1</DEFAULT-VALUE>
																			<MAX>10</MAX>
																			<MIN>0</MIN>
																		</ECUC-FLOAT-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpSlaacDelayEnabled -->
																		<ECUC-BOOLEAN-PARAM-DEF UUID="4f1b0629-037e-4c35-b798-41ebeac82379">
																			<SHORT-NAME>TcpIpNdpSlaacDelayEnabled</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">If enabled transmission of the first DAD Neighbor Solicitation will be delayed by a random value from [0...MAX_DAD_DELAY].</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">"This serves to alleviate congestion when many nodes start up on the link at the same time, such as after a power failure, and may help to avoid race conditions when more than one node is trying to solicit for the same address at the same time."

                                                                        "The delay will avoid similar congestion when multiple nodes are going to configure addresses by receiving the same single multicast router advertisement."

                                                                        [RFC4861 5.4.2. Sending Neighbor Solicitation Messages]

                                                                        Default: True</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>false</DEFAULT-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: TcpIpNdpSlaacOptimisticDadEnabled -->
																		<ECUC-BOOLEAN-PARAM-DEF UUID="2395a7d0-5677-4844-b4c6-c74a423ac6d4">
																			<SHORT-NAME>TcpIpNdpSlaacOptimisticDadEnabled</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Enable Optimistic Duplicate Address Detection (DAD) according to RFC4429.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>false</DEFAULT-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																		<ECUC-FLOAT-PARAM-DEF UUID="b4af1aed-70df-4f32-a0b8-e7e4ca538508">
																			<SHORT-NAME>TcpIpNdpSlaacMinLifetime</SHORT-NAME>
																			<LONG-NAME>
																				<L-4 L="EN">Minimum Address Lifetime</L-4>
																			</LONG-NAME>
																			<DESC>
																				<L-2 L="EN">This value specifies the minimum valid Lifetime for addresses configured via Stateless Address Autoconfiguration.

[RFC4862 5.5.3. e]

RFC Default: 7200sec (2hrs)</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:BaseUnit">SEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>7200</DEFAULT-VALUE>
																			<MAX>120000</MAX>
																			<MIN>10</MIN>
																		</ECUC-FLOAT-PARAM-DEF>
																		<ECUC-FLOAT-PARAM-DEF UUID="5c3659c3-d82d-4c21-a67a-80160b15aba4">
																			<SHORT-NAME>TcpIpNdpSlaacMaxDelay</SHORT-NAME>
																			<LONG-NAME>
																				<L-4 L="EN">Max address configuration delay</L-4>
																			</LONG-NAME>
																			<DESC>
																				<L-2 L="EN">This value specifies the maximum value for the address configuration delay.

According to [RFC4861 5.4.2. Sending Neighbor Solicitation Messages] this value should be the same as MAX_RTR_SOLICITATION_DELAY.

RFC Default: "MAX_RTR_SOLICITATION_DELAY" = 1000 milliseconds</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:BaseUnit">SEC</SD>
																						<SD GID="DV:Unit">MSEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>1000</DEFAULT-VALUE>
																			<MAX>10000</MAX>
																			<MIN>0</MIN>
																		</ECUC-FLOAT-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="1753a324-ce21-4c2b-9384-c864dc1fda18">
																	<SHORT-NAME>TcpIpIpV6MldConfig</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container includes configuration parameters for the Multicast Listener Discovery Version 2 (MLDv2) for IPv6.

See [RFC3810 Multicast Listener Discovery Version 2 (MLDv2) for IPv6]</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:CfgPostBuild">
																				<SD GID="DV:postBuildSelectableChangeable">false</SD>
																				<SD GID="DV:postBuildNotDeletable">false</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<PARAMETERS>
																		<ECUC-FLOAT-PARAM-DEF UUID="4565fd23-af62-4d5c-a652-ff64c87b85a6">
																			<SHORT-NAME>TcpIpMldV1QuerierPresentTimeout</SHORT-NAME>
																			<LONG-NAME>
																				<L-4 L="EN">Older Version Querier Present Timeout</L-4>
																			</LONG-NAME>
																			<DESC>
																				<L-2 L="EN">This value specifies the value of the "Older Version Querier Present Timeout" Variable.

See [RFC3810 9.12. Older Version Querier Present Timeout]

"This value MUST be ([Robustness Variable] times ([Query Interval]) plus (one half of [Query Response Interval])."</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:BaseUnit">SEC</SD>
																						<SD GID="DV:Unit">MSEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>5000</DEFAULT-VALUE>
																			<MAX>600000</MAX>
																			<MIN>1</MIN>
																		</ECUC-FLOAT-PARAM-DEF>
																		<ECUC-INTEGER-PARAM-DEF UUID="a887b3ac-c946-42b9-b5ba-83088808ab03">
																			<SHORT-NAME>TcpIpMldIfStatePoolSize</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This value specifies the maximum number of multicast addresses the IpV6 instance can listen to.</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:DefaultFormat">DEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>8</DEFAULT-VALUE>
																			<MAX>254</MAX>
																			<MIN>1</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<ECUC-INTEGER-PARAM-DEF UUID="0b3c6e70-b5e7-4562-a7bd-063bb8328423">
																			<SHORT-NAME>TcpIpMldSrcAddrPoolSize</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This value specifies the maximum number of source addresses that can be stored for all socket-based multicast memberships.

If multicast packets shall only be received if they have been sent from specific source addresses each of these addresses requires an entry in this pool.</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:DefaultFormat">DEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>32</DEFAULT-VALUE>
																			<MAX>254</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<ECUC-INTEGER-PARAM-DEF UUID="8eb3ca21-009e-49b3-af13-0c324a07a77d">
																			<SHORT-NAME>TcpIpMldSocketMembershipPoolSize</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This value specifies the maximum number of multicast memberships that can be managed via the IpV6_MulticastListen() API. All sockets share the same membership pool. Each multicast address can apper at most once for each socket.

Maximum: IpV6MldIfStatePoolSize * number of sockets (all socket are member of all multicast groups)
Minimum: IpV6MldIfStatePoolSize * 1 (exactly one socket is member of each multicast address group)</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:DefaultFormat">DEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>8</DEFAULT-VALUE>
																			<MAX>254</MAX>
																			<MIN>1</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<ECUC-INTEGER-PARAM-DEF UUID="a7578eb7-1626-4371-b385-c4d2de78446e">
																			<SHORT-NAME>TcpIpMldIfStateSrcAddrPoolSize</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This value specifies the total number of all source addresses for all multicast addresses.

This value may be less than IpV6MldSrcAddrPoolSize if multiple sockets only need multicast packets from the same source addresses.</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:DefaultFormat">DEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>16</DEFAULT-VALUE>
																			<MAX>254</MAX>
																			<MIN>1</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<ECUC-INTEGER-PARAM-DEF UUID="3a24a09e-4b30-42a5-883f-877b90be1ca4">
																			<SHORT-NAME>TcpIpMldRobustness</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This value specifies the value of the [Robustness Variable] which is defined in [RFC3810 9.1. Robustness Variable].

"MLD is robust to [Robustness Variable] - 1 packet losses. The value of the Robustness Variable MUST NOT be zero, and SHOULD NOT be one. Default value: 2."</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:DefaultFormat">DEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>2</DEFAULT-VALUE>
																			<MAX>255</MAX>
																			<MIN>1</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<ECUC-INTEGER-PARAM-DEF UUID="b7abc977-d3f0-4426-914f-1cef4ffefc5a">
																			<SHORT-NAME>TcpIpMldMaxReportMsgSize</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This value specifies the maximum size of a MLDv2 Report message inclusive the IPv6 header (40 bytes) and the Hop-by-Hop header (8 bytes).
This value directly depends on the Ethernet Tx buffer size. (No extra buffer will be allocated.)</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:BaseUnit">BYTE</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>1280</DEFAULT-VALUE>
																			<MAX>1500</MAX>
																			<MIN>76</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<ECUC-FLOAT-PARAM-DEF UUID="031f0bde-762d-4719-9434-73d504aa287f">
																			<SHORT-NAME>TcpIpMldMinUnsolicitedReportInt</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This value specifies the lower bound for the randomized Unsolicited Report Interval.

If this value equals IpV6MldMaxUnsolicitedReportInt the interval will be fixed.

RFC Default: 0

See: IpV6MldMaxUnsolicitedReportInt</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:BaseUnit">SEC</SD>
																						<SD GID="DV:Unit">MSEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>0</DEFAULT-VALUE>
																			<MAX>100000</MAX>
																			<MIN>0</MIN>
																		</ECUC-FLOAT-PARAM-DEF>
																		<ECUC-FLOAT-PARAM-DEF UUID="e2a3d82e-0340-4455-9452-512cb590e300">
																			<SHORT-NAME>TcpIpMldMaxUnsolicitedReportInt</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This value specifies the upper bound for the randomized Unsolicited Report Interval.

"The Unsolicited Report Interval is the time between repetitions of a node's initial report of interest in a multicast address." [RFC3810 9.11. Unsolicited Report Interval]

RFC Default: 1 second.

"To cover the possibility of the State Change Report being missed by one or more multicast routers, [Robustness Variable] - 1 retransmissions are scheduled, through a Retransmission Timer, at intervals chosen at random from the range (0, [Unsolicited Report Interval])." [RFC3810 6.1. Action on Change of Per-Interface State]</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:BaseUnit">SEC</SD>
																						<SD GID="DV:Unit">MSEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>1000</DEFAULT-VALUE>
																			<MAX>100000</MAX>
																			<MIN>0</MIN>
																		</ECUC-FLOAT-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="0acaae98-4d88-463e-bdd7-5b0db9f7e45c">
																	<SHORT-NAME>TcpIpIpV6PrivExtConfig</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container includes configuration parameters for the Privacy Extensions for Stateless Address Autoconfiguration in IPv6.

See [RFC4941 Privacy Extensions for Stateless Address Autoconfiguration in IPv6]</L-2>
																	</DESC>
																	<ADMIN-DATA>
																		<SDGS>
																			<SDG GID="DV:CfgPostBuild">
																				<SD GID="DV:postBuildSelectableChangeable">false</SD>
																				<SD GID="DV:postBuildNotDeletable">false</SD>
																			</SDG>
																		</SDGS>
																	</ADMIN-DATA>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<PARAMETERS>
																		<ECUC-FLOAT-PARAM-DEF UUID="5cbf1a31-38db-4659-8366-325c0fe6d6c1">
																			<SHORT-NAME>TcpIpPrivExtTempValidLifetime</SHORT-NAME>
																			<LONG-NAME>
																				<L-4 L="EN">Lifetime of temp. Address</L-4>
																			</LONG-NAME>
																			<DESC>
																				<L-2 L="EN">This value specifies the maximum time a temporary address is valid.

RFC Default: 604800s (1 week)
[RFC4941 5. Defined Constants]

[RFC4941 3.3. Generating Temporary Addresses / 1.]</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:BaseUnit">SEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>604800</DEFAULT-VALUE>
																			<MAX>4294967</MAX>
																			<MIN>0</MIN>
																		</ECUC-FLOAT-PARAM-DEF>
																		<ECUC-FLOAT-PARAM-DEF UUID="95293de4-1964-4a6b-a546-82104ff7aaf7">
																			<SHORT-NAME>TcpIpPrivExtTempPreferredLifetime</SHORT-NAME>
																			<LONG-NAME>
																				<L-4 L="EN">Time temp. Address is preferred</L-4>
																			</LONG-NAME>
																			<DESC>
																				<L-2 L="EN">This value specifies the maximum time a temporary address is preferred.

RFC Default: 86400s (1 day)
[RFC4941 5. Defined Constants]

[RFC4941 3.3. Generating Temporary Addresses / 1.]</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:BaseUnit">SEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>86400</DEFAULT-VALUE>
																			<MAX>4294967</MAX>
																			<MIN>0</MIN>
																		</ECUC-FLOAT-PARAM-DEF>
																		<ECUC-FLOAT-PARAM-DEF UUID="932d705f-7ca5-4ac1-a253-d5ba7bd13f84">
																			<SHORT-NAME>TcpIpPrivExtRegenAdvance</SHORT-NAME>
																			<LONG-NAME>
																				<L-4 L="EN">Time in advance for temp. Address Regeneration</L-4>
																			</LONG-NAME>
																			<DESC>
																				<L-2 L="EN">A new temporary addresses is generated RegenAdvance time units before its predecessor gets deprecated.

RFC Default: 5s
[RFC4941 5. Defined Constants]

"To ensure that a preferred temporary address is always available, a new temporary address SHOULD be regenerated slightly before its predecessor is deprecated. This is to allow sufficient time to avoid race conditions in the case where generating a new temporary address is not instantaneous, such as when duplicate address detection must be run. The node SHOULD start the address regeneration process REGEN_ADVANCE time units before a temporary address would actually be deprecated."
[RFC4941 3.4. Expiration of Temporary Addresses]</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:BaseUnit">SEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>5</DEFAULT-VALUE>
																			<MAX>100</MAX>
																			<MIN>0</MIN>
																		</ECUC-FLOAT-PARAM-DEF>
																		<ECUC-FLOAT-PARAM-DEF UUID="0b3a3466-d2ee-49d1-96e3-6fcb8d21ec35">
																			<SHORT-NAME>TcpIpPrivExtMaxDesyncFactor</SHORT-NAME>
																			<LONG-NAME>
																				<L-4 L="EN">DeSync-Factor upper bound</L-4>
																			</LONG-NAME>
																			<DESC>
																				<L-2 L="EN">This value specifies the upper bound for the random value DESYNC_FACTOR.

RFC Default: 600s (10 minutes)
[RFC4941 5. Defined Constants]

"The value DESYNC_FACTOR is a random value (different for each client) that ensures that clients don't synchronize with each other and generate new addresses at exactly thesame time."
[RFC4941 3.5. Regeneration of Randomized Interface Identifiers]</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:BaseUnit">SEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>600</DEFAULT-VALUE>
																			<MAX>10000</MAX>
																			<MIN>0</MIN>
																		</ECUC-FLOAT-PARAM-DEF>
																		<ECUC-INTEGER-PARAM-DEF UUID="00bb35ee-e901-44d6-b352-9567a528fece">
																			<SHORT-NAME>TcpIpPrivExtTempIdGenRetries</SHORT-NAME>
																			<LONG-NAME>
																				<L-4 L="EN">Interface UID generation retries</L-4>
																			</LONG-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines the number of times the node shall retry to generate an unique interface identifier for a temporary address.

If an unique identifier could not be generated after the specified number of retries, there will be no temporary address available.

RFC Default: 3
[RFC4941 5. Defined Constants]
[RFC4941 3.3. Generating Temporary Addresses / 7.]</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:DefaultFormat">DEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>3</DEFAULT-VALUE>
																			<MAX>10</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<ECUC-INTEGER-PARAM-DEF UUID="f4dbe358-6c0e-4576-8494-0784b66d3ab0">
																			<SHORT-NAME>TcpIpPrivExtMaxTempAddrs</SHORT-NAME>
																			<LONG-NAME>
																				<L-4 L="EN">Temp. Address count</L-4>
																			</LONG-NAME>
																			<DESC>
																				<L-2 L="EN">This value specifies the maximum number of temporary addresses that can be configured at the same time.

This should be at least 2 times the number of golbal prefixes advertised by routers.</L-2>
																			</DESC>
																			<ADMIN-DATA>
																				<SDGS>
																					<SDG GID="DV:Display">
																						<SD GID="DV:DefaultFormat">DEC</SD>
																					</SDG>
																				</SDGS>
																			</ADMIN-DATA>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>4</DEFAULT-VALUE>
																			<MAX>254</MAX>
																			<MIN>1</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<ECUC-BOOLEAN-PARAM-DEF UUID="3f0fe9a1-5685-4d71-951f-058117c55aba">
																			<SHORT-NAME>TcpIpPrivExtUseOnLinkPrefixes</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines whether the IpV6 will generate temporary addresses for prefixes marked as on-link in received Router Advertisements. Normally temporary addresses are only created for prefixes that are not marked as on-link.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>Vector Informatik</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>false</DEFAULT-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
															</SUB-CONTAINERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: TcpIpLocalAddr -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ef12f5ca-593f-4f49-a4f3-244a2800e58c">
											<SHORT-NAME>TcpIpLocalAddr</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the local IP (Internet Protocol) addresses used for IP communication.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: TcpIpAddrId -->
												<ECUC-INTEGER-PARAM-DEF UUID="67a403fe-2f00-4519-8e5b-379d56bd44ed">
													<SHORT-NAME>TcpIpAddrId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">IP address table identifier assigned by TCP/IP stack.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpAddressType -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="189afa14-e5b0-422c-a75d-5ca1931f82c5">
													<SHORT-NAME>TcpIpAddressType</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Address type.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="db4a948b-9d5b-4de7-acee-503d668334cf">
															<SHORT-NAME>TCPIP_MULTICAST</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Multicast</L-4>
															</LONG-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="57e9b73e-88ce-489f-8c72-810f7884d68b">
															<SHORT-NAME>TCPIP_UNICAST</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Unicast</L-4>
															</LONG-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="22314ec2-e28b-4f1c-9433-71a15b5be6c9">
															<SHORT-NAME>TCPIP_IPV4_BROADCAST</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Broadcast</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpDomainType -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="a73ae771-25bc-45c2-a02a-e2a2bb7c03d4">
													<SHORT-NAME>TcpIpDomainType</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Address family.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="f807649a-fdf8-48f9-b426-64bb391c7997">
															<SHORT-NAME>TCPIP_AF_INET</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="db0bd69d-a4de-4bb7-93e8-797396cd4a6b">
															<SHORT-NAME>TCPIP_AF_INET6</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<ECUC-BOOLEAN-PARAM-DEF UUID="4fb9b00e-4c71-4f97-8bdc-9949cf83e0bc">
													<SHORT-NAME>TcpIpIpAddrReceiveAllNotConfiguredMulticasts</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines whether an address of type "AutoSelect_Broadcast" shall also be used to receive multicasts that have not been configured individually by addresses of type "Multicast".

Note: If this parameter is set, the IPv4 will configure the EthIf to accept all multicast packets on the corresponsing controller.

This parameter is only valid for an "AutoSelect_Broadcast" address.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: TcpIpCtrlRef -->
												<ECUC-REFERENCE-DEF UUID="ed62a943-284f-469c-b777-22bf770e733f">
													<SHORT-NAME>TcpIpCtrlRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to a TcpIpCtrl specifying the EthIf Controller where the IP address shall be assigned and DEM errors that shall be reported in case of an error on this controller.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/TcpIp/TcpIpConfig/TcpIpCtrl</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
											<SUB-CONTAINERS>
												<!-- Container Definition: TcpIpAddrAssignment -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="8a900823-fc2e-4026-8b31-b66af24a31ce">
													<SHORT-NAME>TcpIpAddrAssignment</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container is a subcontainer of TcpIpLocalAddr and specifies the assignment policy for the IP address.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: TcpIpAssignmentLifetime -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="3cdbb6d1-db4a-4a6b-860c-0073d9c5ae3c">
															<SHORT-NAME>TcpIpAssignmentLifetime</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines the lifetime of a dynamically fetched IP address.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">If TcpIpAssignmentMethod = TCPIP_STATIC then TcpIpAssignmentLifetime shall be omitted.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>TCPIP_FORGET</DEFAULT-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="81eea702-bb90-461f-ac68-c0c54ead6ade">
																	<SHORT-NAME>TCPIP_FORGET</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="5ed397a8-46c9-483f-9373-740696e9e0b8">
																	<SHORT-NAME>TCPIP_STORE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: TcpIpAssignmentMethod -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="257a8813-f676-4382-a53d-1172d38d5040">
															<SHORT-NAME>TcpIpAssignmentMethod</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Method of address assignment</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="e5c0b133-595f-45c2-ba07-a2985d324af8">
																	<SHORT-NAME>TCPIP_DHCP</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="5361ed56-8374-40ce-9af8-d41ea04da13a">
																	<SHORT-NAME>TCPIP_IPV6_ROUTER</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ce4066e3-a99c-4d20-845c-33fb97d034af">
																	<SHORT-NAME>TCPIP_LINKLOCAL</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="e6cbee8e-1405-4c14-b853-8933d0c23e42">
																	<SHORT-NAME>TCPIP_LINKLOCAL_DOIP</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="58e26b94-1bc0-4491-be60-7a0da00a0fe9">
																	<SHORT-NAME>TCPIP_STATIC</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: TcpIpAssignmentPriority -->
														<ECUC-INTEGER-PARAM-DEF UUID="66ec827e-8e41-446a-b7d6-b649b6bc0f91">
															<SHORT-NAME>TcpIpAssignmentPriority</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Priority of assignment (1 is highest). If a new address from an assignment method with a higher priority is available, it overwrites the IP address previously assigned by an assignment method with a lower priority.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>1</DEFAULT-VALUE>
															<MAX>3</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: TcpIpAssignmentTrigger -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="9286b86a-91cf-4520-b9d2-c73af6504f7f">
															<SHORT-NAME>TcpIpAssignmentTrigger</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Trigger of address assignment.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="482cb5c9-4021-4993-ae4d-73d720508daf">
																	<SHORT-NAME>TCPIP_AUTOMATIC</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="91e9a08e-8e12-4617-8810-487dde52cd53">
																	<SHORT-NAME>TCPIP_MANUAL</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: TcpIpStaticIpAddressConfig -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="8d6861de-7c37-4128-8e83-97af7acb2387">
													<SHORT-NAME>TcpIpStaticIpAddressConfig</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container is a subcontainer of TcpIpLocalAddr and specifies a static IP address including directly related parameters.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: TcpIpDefaultRouter -->
														<ECUC-STRING-PARAM-DEF UUID="e9069409-5fee-4e28-a2d8-c191e558b754">
															<SHORT-NAME>TcpIpDefaultRouter</SHORT-NAME>
															<DESC>
																<L-2 L="EN">IP address of default router (gateway)</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: TcpIpNetmask -->
														<ECUC-INTEGER-PARAM-DEF UUID="a6bc4b11-58fc-4968-9d60-324b5954ebfa">
															<SHORT-NAME>TcpIpNetmask</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Network mask of IPv4 address or address prefix of IPv6 address in CIDR Notation, i.e. decimal value between 0 and 32 (IPv4) or 0 and 128 (IPv6) that describes the number of significant bits defining the network number or prefix of an IP address.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>128</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: TcpIpStaticIpAddress -->
														<ECUC-STRING-PARAM-DEF UUID="3b8a3709-2c5c-494d-b0a1-7172a6742b4a">
															<SHORT-NAME>TcpIpStaticIpAddress</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Static IP Address.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">To specify any IP address for a certain EthIfCtrl, “ANY” has to be set as wildcard. See TcpIp_Bind() for more details.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<ECUC-BOOLEAN-PARAM-DEF UUID="4f690c9f-ec8c-4cfb-84fd-80be392391ed">
															<SHORT-NAME>TcpIpRequestIpAddrAssignmentEnabled</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines if this ip address can be changed during runtime via the TcpIp_RequestIpAddrAssignment() API.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>true</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: TcpIpNvmBlock -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="c2bb41c1-4935-4e1b-acf0-3f595c89ea1c">
											<SHORT-NAME>TcpIpNvmBlock</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configuration of optional usage of Nvm in case the TcpIp module requires non volatile memory in the Ecu to store information (e.g. IP Address received via DHCP and shall be stored).</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<REFERENCES>
												<!-- Symbolic Name Reference Definition: TcpIpNvmBlockDescriptorRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="0a0315fb-fe9b-4da2-9061-5621026cea95">
													<SHORT-NAME>TcpIpNvmBlockDescriptorRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the Nvm block description in the Nvm module configuration.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/NvM/NvMBlockDescriptor</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: TcpIpPhysAddrConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="8bbfae3c-9972-402f-be2e-de6fa989972d">
											<SHORT-NAME>TcpIpPhysAddrConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the physical address configuration.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SUB-CONTAINERS>
												<!-- Container Definition: TcpIpPhysAddrChgHandler -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="4f251a1a-c10a-44b0-9224-bba3315cb450">
													<SHORT-NAME>TcpIpPhysAddrChgHandler</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container is a subcontainer of TcpIpPhysAddrConfig and</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">specifies the configuration parameters for physical address change handler.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: TcpIpPhysAddrChgHandlerHeaderFileName -->
														<ECUC-STRING-PARAM-DEF UUID="89044b9e-f193-4d7b-9400-c91fa361e7cc">
															<SHORT-NAME>TcpIpPhysAddrChgHandlerHeaderFileName</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter specifies the name of the header file containing the definition of the physical address change handler function.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<ECUC-STRING-PARAM-DEF-VARIANTS>
																<ECUC-STRING-PARAM-DEF-CONDITIONAL>
																	<MAX-LENGTH>32</MAX-LENGTH>
																	<MIN-LENGTH>1</MIN-LENGTH>
																</ECUC-STRING-PARAM-DEF-CONDITIONAL>
															</ECUC-STRING-PARAM-DEF-VARIANTS>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: TcpIpPhysAddrChgHandlerName -->
														<ECUC-FUNCTION-NAME-DEF UUID="db36425a-74a7-4c3e-9689-670d428eec1e">
															<SHORT-NAME>TcpIpPhysAddrChgHandlerName</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines the name of the physical address change function &lt;Up&gt;_PhysAddrTableChg.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-FUNCTION-NAME-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: TcpIpSocketOwnerConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="99b3e3b9-ff4a-47ac-9850-604c41875ccd">
											<SHORT-NAME>TcpIpSocketOwnerConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the upper layer modules of TcpIp using the socket API.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SUB-CONTAINERS>
												<!-- Container Definition: TcpIpSocketOwner -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="070f0e68-8e85-4d70-b307-3bd24911fb0d">
													<SHORT-NAME>TcpIpSocketOwner</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container is a subcontainer of TcpIpSocketOwnerConfig and specifies an upper layer of TcpIp that uses the socket API.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: TcpIpSocketOwnerCopyTxDataName -->
														<ECUC-STRING-PARAM-DEF UUID="31e19519-a2af-4071-8f98-9972932efd76">
															<SHORT-NAME>TcpIpSocketOwnerCopyTxDataName</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines the name of the &lt;Up_CopyTxData&gt; function of the TcpIpSocketOwner module. The function name shall only be configurable if TcpIpSocketOwnerUpperLayerType is set to CDD.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: TcpIpSocketOwnerHeaderFileName -->
														<ECUC-STRING-PARAM-DEF UUID="fd702bd9-4515-4c7e-9cbf-e8ca95106f95">
															<SHORT-NAME>TcpIpSocketOwnerHeaderFileName</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter specifies the name of the header file containing the definition of the TcpIpSocketOwner module functions. The header file name shall only be configurable if TcpIpSocketOwnerUpperLayerType is set to CDD.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: TcpIpSocketOwnerLocalIpAddrAssignmentChgName -->
														<ECUC-STRING-PARAM-DEF UUID="db9ec507-2c81-4399-9f7f-511e0a9801f7">
															<SHORT-NAME>TcpIpSocketOwnerLocalIpAddrAssignmentChgName</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines the name of the &lt;Up_LocalIpAddrAssignmentChg&gt; function of the TcpIpSocketOwner module. The function name shall only be configurable if TcpIpSocketOwnerUpperLayerType is set to CDD.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: TcpIpSocketOwnerRxIndicationName -->
														<ECUC-STRING-PARAM-DEF UUID="18f65b8d-9e74-4011-bf73-e2585e57113f">
															<SHORT-NAME>TcpIpSocketOwnerRxIndicationName</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines the name of the &lt;Up_RxIndication&gt; function of the TcpIpSocketOwner module. The function name shall only be configurable if TcpIpSocketOwnerUpperLayerType is set to CDD.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: TcpIpSocketOwnerTcpAcceptedName -->
														<ECUC-STRING-PARAM-DEF UUID="d3202b67-8321-46cf-832a-12ddcce6eaf6">
															<SHORT-NAME>TcpIpSocketOwnerTcpAcceptedName</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines the name of the &lt;Up_TcpAccepted&gt; function of the TcpIpSocketOwner module. The function name shall only be configurable if TcpIpSocketOwnerUpperLayerType is set to CDD.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: TcpIpSocketOwnerTcpConnectedName -->
														<ECUC-STRING-PARAM-DEF UUID="ea3021c1-8c38-4af8-b85a-f9bcbcabf73c">
															<SHORT-NAME>TcpIpSocketOwnerTcpConnectedName</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines the name of the &lt;Up_TcpConnected&gt; function of the TcpIpSocketOwner module. The function name shall only be configurable if TcpIpSocketOwnerUpperLayerType is set to CDD.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: TcpIpSocketOwnerTxConfirmationName -->
														<ECUC-STRING-PARAM-DEF UUID="c43a4af8-4317-45e1-9350-d618f4a01d9a">
															<SHORT-NAME>TcpIpSocketOwnerTxConfirmationName</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines the name of the &lt;Up_TxConfirmation&gt; function of the TcpIpSocketOwner module. The function name shall only be configurable if TcpIpSocketOwnerUpperLayerType is set to CDD.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: TcpIpSocketOwnerUpperLayerType -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="8227b25a-6ce0-403f-9cc5-8274e6fe05d7">
															<SHORT-NAME>TcpIpSocketOwnerUpperLayerType</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter specifies the type of the upper layer module.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="8ed2b18d-052a-40b5-8c3c-b907d7a5865e">
																	<SHORT-NAME>CDD</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="562b0915-59a1-4d5c-bf03-6ba464545579">
																	<SHORT-NAME>SOAD</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="eae9e772-6286-4d40-94c0-4450036b91e7">
																	<SHORT-NAME>DHCPV4_SERVER</SHORT-NAME>
																	<ORIGIN>Vector Informatik</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<ECUC-INTEGER-PARAM-DEF UUID="db654543-ffed-44ce-808f-292703b2c45a">
															<SHORT-NAME>TcpIpSocketOwnerId</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Socket User Index</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">This value specifies the ID of the socket user.
(auto calculated)</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>0</DEFAULT-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<ECUC-STRING-PARAM-DEF UUID="877fae0f-7cf2-4285-b5fb-e784076dc319">
															<SHORT-NAME>TcpIpSocketOwnerTcpIpEventName</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">TcpIp Event Callback Function</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines the function called for a TCP/IP event on related sockets.The function name shall only be configurable if TcpIpSocketOwnerUpperLayerType is set to CDD.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<ECUC-INTEGER-PARAM-DEF UUID="298c361b-29ce-4c1a-899f-76dc9a25463f">
															<SHORT-NAME>TcpIpSocketOwnerTcpListenSocketMax</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This value specifies the number of TCP listen sockets this socket owner will use simultaneously</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>0</DEFAULT-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: TcpIpTcpConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="cf13f3bf-480e-4446-882f-7ca705d7e778">
											<SHORT-NAME>TcpIpTcpConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the configuration parameters of the TCP (Transmission Control Protocol) sub-module.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: TcpIpTcpCongestionAvoidanceEnabled -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="fbb74212-5c12-4483-abb4-019016ad3d2a">
													<SHORT-NAME>TcpIpTcpCongestionAvoidanceEnabled</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables (TRUE) or disables (FALSE) support of TCP congestion avoidance algorithm according to IETF RFC 5681.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpTcpFastRecoveryEnabled -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="c157243f-6ad5-418e-92f7-37ed6b060c7d">
													<SHORT-NAME>TcpIpTcpFastRecoveryEnabled</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables (TRUE) or disables (FALSE) support of TCP Fast Recovery according to IETF RFC 5681.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpTcpFastRetransmitEnabled -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="313f0b44-0a1c-4541-8217-79e4fc1ac81e">
													<SHORT-NAME>TcpIpTcpFastRetransmitEnabled</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables (TRUE) or disables (FALSE) support of TCP Fast Retransmission according to IETF RFC 5681.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpTcpFinWait2Timeout -->
												<ECUC-FLOAT-PARAM-DEF UUID="f47ac90d-f6b7-45bd-9e3d-62e895e359cd">
													<SHORT-NAME>TcpIpTcpFinWait2Timeout</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Timeout in [s] to receive a FIN from the remote node (after this node has initiated connection termination), i.e. maximum time waiting in FINWAIT-2 for a connection termination request from the remote TCP.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>30</DEFAULT-VALUE>
													<MAX>1.7976931348623157E+308</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpTcpKeepAliveEnabled -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="232c5c88-2a34-4da7-ad35-740cad99d841">
													<SHORT-NAME>TcpIpTcpKeepAliveEnabled</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables (TRUE) or disables (FALSE) TCP Keep Alive Probes according to IETF RFC 1122 chapter *******</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpTcpKeepAliveInterval -->
												<ECUC-FLOAT-PARAM-DEF UUID="9763d9aa-9ee6-4a34-a63f-1176847a01a7">
													<SHORT-NAME>TcpIpTcpKeepAliveInterval</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Specifies the interval in [s] between subsequent keepalive probes.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>1.7976931348623157E+308</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpTcpKeepAliveProbesMax -->
												<ECUC-INTEGER-PARAM-DEF UUID="e46e8e8c-03a0-4351-95b5-6e439d335c97">
													<SHORT-NAME>TcpIpTcpKeepAliveProbesMax</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Maximum number of times that a TCP Keep Alive is retransmitted before the connection is closed.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpTcpKeepAliveTime -->
												<ECUC-FLOAT-PARAM-DEF UUID="221739f6-e136-448b-ac38-e6d782db2b07">
													<SHORT-NAME>TcpIpTcpKeepAliveTime</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Specifies the time in [s] between the last data packet sent (simple ACKs are not considered data) and the first keepalive probe. Note: Setting this configuration parameter to a value smaller or equal to the value of TcpIpMainFunctionPeriod results in the transmission of keep alive probes within every MainFunction cycle.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>7200</DEFAULT-VALUE>
													<MAX>1.7976931348623157E+308</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpTcpMaxRtx -->
												<ECUC-INTEGER-PARAM-DEF UUID="e59720fc-d0bf-4999-bbdd-416170936ec0">
													<SHORT-NAME>TcpIpTcpMaxRtx</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Maximum number of times that a TCP segment is retransmitted before the TCP connection is closed. This parameter is only valid if TcpIpTcpRetransmissionTimeout is configured.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">Note: This parameter also applies for FIN retransmissions.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpTcpMsl -->
												<ECUC-FLOAT-PARAM-DEF UUID="f9cbef19-c89e-4956-a226-61f33ec7654e">
													<SHORT-NAME>TcpIpTcpMsl</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Maximum segment lifetime in [s].</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">(Note: TIME-WAIT = 2 x TcpIpTcpMsl – to ensure that the remote node received the acknowledgment to its connection termination request.)</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>120</DEFAULT-VALUE>
													<MAX>1.7976931348623157E+308</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpTcpNagleEnabled -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="1f7a5768-fe1b-4c4b-81b0-338cf540cbcc">
													<SHORT-NAME>TcpIpTcpNagleEnabled</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables (TRUE) or disables (FALSE) support of Nagle’s algorithm according to IETF RFC 896. If enabled the Nagle’s algorithm is activated per default for all TCP sockets, but can be deactivated via TcpIp_ChangeParameter() API.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>true</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpTcpReceiveWindowMax -->
												<ECUC-INTEGER-PARAM-DEF UUID="cf0da76e-545f-4ce4-b55b-02ac907f9720">
													<SHORT-NAME>TcpIpTcpReceiveWindowMax</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Default value of maximum receive window in bytes.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>512</DEFAULT-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpTcpRetransmissionTimeout -->
												<ECUC-FLOAT-PARAM-DEF UUID="fd9d758d-c2d8-487f-bd0b-b3d76f04c987">
													<SHORT-NAME>TcpIpTcpRetransmissionTimeout</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Timeout in [s] before an unacknowledged TCP segment is sent again.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">If the timeout is disabled or set to INF, no TCP segments shall be retransmitted.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>1</DEFAULT-VALUE>
													<MAX>1.7976931348623157E+308</MAX>
													<MIN>0.001</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpTcpSlowStartEnabled -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="b65d3ad7-a9dd-4a1d-9ab6-99a820b722ce">
													<SHORT-NAME>TcpIpTcpSlowStartEnabled</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables (TRUE) or disables (FALSE) support of TCP slow start algorithm according to IETF RFC 5681.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpTcpSynMaxRtx -->
												<ECUC-INTEGER-PARAM-DEF UUID="e67e8d63-0387-42a3-b930-e03c65021b6d">
													<SHORT-NAME>TcpIpTcpSynMaxRtx</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Maximum number of times that a TCP SYN is retransmitted.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">Note: SYN will be retried after TcpIpTcpRetransmissionTimeout. The connection will be dropped if no matching connection request has been received after the last TCP SYN has been sent and TcpIpTcpRetransmissionTimeout has been expired.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>255</DEFAULT-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpTcpSynReceivedTimeout -->
												<ECUC-FLOAT-PARAM-DEF UUID="00286551-808e-403e-aa93-7f79b4153ce1">
													<SHORT-NAME>TcpIpTcpSynReceivedTimeout</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Timeout in [s] to complete a remotely initiated TCP connection establishment, i.e. maximum time waiting in SYN-RECEIVED for a confirming connection request acknowledgment after having both received and sent a connection request.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>1.7976931348623157E+308</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpTcpTtl -->
												<ECUC-INTEGER-PARAM-DEF UUID="55e70307-5151-4dcf-8e15-b480d1770986">
													<SHORT-NAME>TcpIpTcpTtl</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Default Time-to-live value of outgoing TCP packets.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>64</DEFAULT-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-BOOLEAN-PARAM-DEF UUID="648a1543-f1c2-4b09-a785-e95cf482c1bf">
													<SHORT-NAME>TcpIpTcpEnableDiagReadAckSeqNum</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enable reading of current TCP sequence number and acknowledgement number for a specified socket.
</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<ECUC-BOOLEAN-PARAM-DEF UUID="b1ae3760-7dff-4558-93ec-c14ba1f829a8">
													<SHORT-NAME>TcpIpTcpSackOptionSupport</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Enable Selective Acknowledgement Support</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines if the TCP option 'selective acknowlagement' is enabled.
[RFC 2018]

RESTRICTIONS: 'TcpSackOptionSupport' is only available, if 'TcpOutOfOrderRxSupport' is set to 'true'.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="95f52997-c21f-4b88-9388-e72b551789ba">
													<SHORT-NAME>TcpIpTcpMaxNumOooSegsPerSocket</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Max Num Out Of Order Segments per Socket</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifes the maximum number of out of order control elements that can be allocated for one TCP socket during runtime.

RESTRICTIONS: 'TcpAvgNumOooSegsPerSocket' is only available, if 'TcpOutOfOrderRxSupport' is set to 'true'.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>5</DEFAULT-VALUE>
													<MAX>65535</MAX>
													<MIN>1</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="0eb963a8-8b67-4bca-91c0-d1befc3b14dc">
													<SHORT-NAME>TcpIpTcpAvgNumOooSegsPerSocket</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Avg Num Out Of Order Segments per Socket</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifes the number of out of order control elements per TCP socket.

The elements are grouped into a pool shared by all TCP sockets.
A TCP socket is allowed to allocate as much elements as specified by 'TcpIpTcpMaxNumOooSegsPerSocket'.

RESTRICTIONS: 'TcpAvgNumOooSegsPerSocket' is only available, if 'TcpOutOfOrderRxSupport' is set to 'true'.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>5</DEFAULT-VALUE>
													<MAX>254</MAX>
													<MIN>1</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-FLOAT-PARAM-DEF UUID="6c7d23b6-e5ce-41ca-bf9f-0b119b979986">
													<SHORT-NAME>TcpIpTcpUserTimeoutDef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the default value for User Timeout.
(default: 20s)
(time transmitted data may stay un-acknowlaged)

RESTRICTION: Value of 'TcpUserTimeoutDef' has to be in the range defined by 'TcpUserTimeoutMin' and 'TcpUserTimeoutMax'.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>20</DEFAULT-VALUE>
													<MAX>10000</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<ECUC-FLOAT-PARAM-DEF UUID="97fe9a77-ec91-4c59-82ff-ad63abe5d901">
													<SHORT-NAME>TcpIpTcpUserTimeoutMax</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This value specifes the maximum value for User Timeout.
(default: 300s)

RESTRICTION: Value of 'TcpUserTimeoutMax' has to be greater than 'TcpUserTimeoutMin' and greater than or equal to 'TcpUserTimeoutDef'.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>300</DEFAULT-VALUE>
													<MAX>10000</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<ECUC-FLOAT-PARAM-DEF UUID="4074990f-5fd9-4775-937c-7368a2197ef7">
													<SHORT-NAME>TcpIpTcpUserTimeoutMin</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the minimum value for User Timeout.
(default: 5s)
RFC 1122 defines it to 100 seconds (3 minutes for SYN segments).

RESTRICTION: Value of 'TcpUserTimeoutMin' has to be smaller than 'TcpUserTimeoutMax' and smaller than or equal to 'TcpUserTimeoutDef'.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>5</DEFAULT-VALUE>
													<MAX>5000</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<ECUC-BOOLEAN-PARAM-DEF UUID="d7170237-f65e-442b-882a-cc5c8e010d35">
													<SHORT-NAME>TcpIpTcpUserTimeoutOptionSupport</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines if the User Timeout Option of TCP is enabled.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<ECUC-FLOAT-PARAM-DEF UUID="5e7b53e8-7aa8-4856-a259-f03a647f18f1">
													<SHORT-NAME>TcpIpTcpTxRetryIntMaxTime</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Max Tx Retry Interval Time</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the upper bound of the time interval, in which the retransmittion of a not acknowledged TCP packet is triggered.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>60</DEFAULT-VALUE>
													<MAX>60</MAX>
													<MIN>10</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<ECUC-FLOAT-PARAM-DEF UUID="49e579c0-d498-449a-a506-ffeb836de633">
													<SHORT-NAME>TcpIpTcpTxRetryIntMinTime</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Min Tx Retry Interval Time</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the lower bound of the time interval, in which the retransmittion of a not acknowledged TCP packet is triggered.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>1</DEFAULT-VALUE>
													<MAX>10</MAX>
													<MIN>0.1</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<ECUC-BOOLEAN-PARAM-DEF UUID="9bdfc8a2-b42b-4b26-8ddc-871fea60607a">
													<SHORT-NAME>TcpIpTcpTimeStampOptionSupport</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Enable Time Stamp Option</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines if the Timestamp Option of TCP is enabled.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<ECUC-FLOAT-PARAM-DEF UUID="86d734b0-dfe6-468f-8cec-273ea4454e57">
													<SHORT-NAME>TcpIpTcpNagleTimeout</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Nagle Timeout</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the timeout for Nagle algorithm,

Note:
Value must be a multiple of Main Function Period

RESTRICTIONS: 'TcpNagleTimeoutMsec' is only available, if 'TcpEnableNagle' is enabled.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
																<SD GID="DV:Unit">MSEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<ECUC-FLOAT-PARAM-DEF UUID="5880ffe0-2131-4be8-a4fc-596de5501492">
													<SHORT-NAME>TcpIpTcpIdleTimeout</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Idle Timeout</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifes the TCP idle timeout (must be bigger than MSL timeout)</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>30</DEFAULT-VALUE>
													<MAX>65535</MAX>
													<MIN>1</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="a4c3705c-875e-41d0-8c45-c4f476d64000">
													<SHORT-NAME>TcpIpTcpTxResetQueueSize</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Tx Reset Queue Size</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the amount of entries reserved for the Tx Reset Queue.

Note:
The Reset Queue is shared by all TCP Sockets.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>8</DEFAULT-VALUE>
													<MAX>127</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="c048dbcf-ba87-40a6-995e-d379c4f2f9ac">
													<SHORT-NAME>TcpIpTcpTxRetryQueueSize</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Average Tx Retry Queue Size</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the average amount of entries allocated for the Tx Retry Queue of one TCP Socket.

Note:
The Retry Queues are grouped in a pool shared by all TCP Sockets. Therefore a Socket may have less entries available as specified.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>10</DEFAULT-VALUE>
													<MAX>127</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-BOOLEAN-PARAM-DEF UUID="fe72aa01-e5dc-4492-bb66-b038c8f6a3e7">
													<SHORT-NAME>TcpIpTcpOutOfOrderRxSupport</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Out Of Order Rx Support</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines if the reception of out of order TCP segments is supported.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>true</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
											</PARAMETERS>
											<SUB-CONTAINERS>
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="1109a158-037d-4a0d-afbe-dceded4de7aa">
													<SHORT-NAME>TcpIpTcpSocketBuffer</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container includes the rx and tx buffer configuration of a TCP socket. 
The number of available socket handles will be the sum of all TCP rx and tx buffer configurations and the number of all TCP listen sockets.

Note: rx and tx buffers are not bound to a specific socket handle. 
The buffers are shared between all users and must be allocated to a socket during runtime via the TcpIp_ChangeParameter() API.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<PARAMETERS>
														<ECUC-INTEGER-PARAM-DEF UUID="ad411939-2b30-425f-a1f7-60d953202e96">
															<SHORT-NAME>TcpIpTcpSocketRxBufferSize</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This value specifies the size of a RX buffer segment that can be allocated to a TCP socket handle via the TcpIp_ChangeParameter() API during runtime.

Note: A buffer segment is shared between all TCP sockets but can only be used for one socket at a time. If multiple sockets with the same buffer size shall be used simultaneously, multiple buffer segments of the same size have to be configured.</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:Display">
																		<SD GID="DV:BaseUnit">BYTE</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>512</DEFAULT-VALUE>
															<MAX>65535</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<ECUC-INTEGER-PARAM-DEF UUID="036a1a56-15d7-4b4f-9b0c-81a782c48b2a">
															<SHORT-NAME>TcpIpTcpSocketTxBufferSize</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This value specifies the size of a TX buffer segment that can be allocated to a TCP socket handle via the TcpIp_ChangeParameter() API during runtime.

Note: A buffer segment is shared between all TCP sockets but can only be used for one socket at a time. If multiple sockets with the same buffer size shall be used simultaneously, multiple buffer segments of the same size have to be configured.</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:Display">
																		<SD GID="DV:BaseUnit">BYTE</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>512</DEFAULT-VALUE>
															<MAX>65535</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: TcpIpUdpConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="18648ba8-01de-4125-806b-c826fe615aaf">
											<SHORT-NAME>TcpIpUdpConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the configuration parameters of the UDP (User Datagram Protocol) sub-module</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: TcpIpUdpTtl -->
												<ECUC-INTEGER-PARAM-DEF UUID="600fcc21-1028-4ed2-8de4-6cca16b1cb48">
													<SHORT-NAME>TcpIpUdpTtl</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Default Time-to-live value of outgoing UDP packets.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>64</DEFAULT-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<SUB-CONTAINERS>
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="16114d8f-**************-5a0acc0c08a0">
													<SHORT-NAME>TcpIpUdpTxReqList</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container includes configuration parameters of a UDP TX request list that can be attached to a UDP socket handle via TcpIp_ChangeParameter() during runtime.

A UDP socket only needs a TX request list if a TxConfirmation is required by the upper layer.

Note: A UDP tx request list is shared between all UDP sockets but can only be used for one socket at a time. If multiple sockets with the same request list size shall be used simultaneously, multiple request lists of the same size have to be configured.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<PARAMETERS>
														<ECUC-INTEGER-PARAM-DEF UUID="6b572316-8596-4a09-8d9e-8e2bc755bbc4">
															<SHORT-NAME>TcpIpUdpTxReqListSize</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This value specifies the maximum number of entries in the UDP TX request list.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>1</DEFAULT-VALUE>
															<MAX>255</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: TcpIpGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="6673e254-b33c-4042-8147-65caec2bd8d5">
									<SHORT-NAME>TcpIpGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container is a subcontainer of TcpIp and specifies the general configuration parameters of the TCP/IP stack.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: TcpIpBufferMemory -->
										<ECUC-INTEGER-PARAM-DEF UUID="b781b6e7-e035-4982-b2d7-d628f6cea806">
											<SHORT-NAME>TcpIpBufferMemory</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Memory size in bytes reserved for TCP/IP buffers.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>4294967295</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: TcpIpDevErrorDetect -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="29def85d-ccf9-4ce5-9fd1-71420fbe0af5">
											<SHORT-NAME>TcpIpDevErrorDetect</SHORT-NAME>
											<DESC>
												<L-2 L="EN">If true then TCP/IP will enable the error-reporting to the Development Error Tracer (DET).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: TcpIpDhcpServerEnabled -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="e31cb094-7dfd-4525-b55a-7c2fd8ec2f30">
											<SHORT-NAME>TcpIpDhcpServerEnabled</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables (TRUE) or disables (FALSE) the DHCPv4 (Dynamic Host Configuration Protocol) Server.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: TcpIpMainFunctionPeriod -->
										<ECUC-FLOAT-PARAM-DEF UUID="e8bf6bc1-c28e-48eb-a3f3-4c1a0d7c27eb">
											<SHORT-NAME>TcpIpMainFunctionPeriod</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Period of TcpIp_MainFunction in [s].</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">SEC</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0.005</DEFAULT-VALUE>
											<MAX>1000</MAX>
											<MIN>0.0001</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: TcpIpResetIpAssignmentApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="4fe73b9c-64aa-483a-a991-27f3f0a1d19f">
											<SHORT-NAME>TcpIpResetIpAssignmentApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables/disables the API TcpIp_ResetIpAssignment of a DHCP-client.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: TcpIpScalabilityClass -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="b72e0af6-f289-4eaf-842f-b5377b870ba2">
											<SHORT-NAME>TcpIpScalabilityClass</SHORT-NAME>
											<DESC>
												<L-2 L="EN">In order to customize the TcpIp Stack to the specific needs of the user it can be scaled according to the scalability classes.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>SC1</DEFAULT-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="e28db74b-a451-4826-9ac8-3580acb52cdc">
													<SHORT-NAME>SC1</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="510c73c8-3e62-4a29-b9ba-ed7d011811c9">
													<SHORT-NAME>SC2</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="4d362753-772e-42d5-b1b2-6e82380f1525">
													<SHORT-NAME>SC3</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: TcpIpTcpEnabled -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="123aba43-d0b3-4161-9b93-a1c34709f253">
											<SHORT-NAME>TcpIpTcpEnabled</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables (TRUE) or disabled (FALSE) support of TCP (Transmission Control Protocol).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: TcpIpTcpSocketMax -->
										<ECUC-INTEGER-PARAM-DEF UUID="5052dd39-53ff-4260-9595-af06ff321a82">
											<SHORT-NAME>TcpIpTcpSocketMax</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Maximum number of TCP sockets</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: TcpIpUdpEnabled -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="8125010f-be32-40b7-8b88-4a9508d987b8">
											<SHORT-NAME>TcpIpUdpEnabled</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables (TRUE) or disabled (FALSE) support of UDP (User Datagram Protocol)</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: TcpIpUdpSocketMax -->
										<ECUC-INTEGER-PARAM-DEF UUID="16aa4ed5-1dfa-481f-af54-ec13ab44acea">
											<SHORT-NAME>TcpIpUdpSocketMax</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Maximum number of UDP sockets.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: TcpIpVersionInfoApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="4905db7c-a3a7-42bd-9515-8be12acc66e9">
											<SHORT-NAME>TcpIpVersionInfoApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">If true the TcpIp_GetVersionInfo API is available.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-STRING-PARAM-DEF UUID="c3e602e5-340e-4866-b08b-f7b733420e6d">
											<SHORT-NAME>TcpIpUserConfigFile</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">User Config File</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Reference to an external user configuration file that will be included during generation.
The content of the user configuration file will be added at the end of the generated module configuration file and allows altering or extending the generated code.
Caution: User configuration files can cause the software module to malfunction and must only be used with great care!</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-STRING-PARAM-DEF>
										<ECUC-FUNCTION-NAME-DEF UUID="8e2b0fe2-3b9b-4e84-9a87-1605d12b0e2b">
											<SHORT-NAME>TcpIpRandNoFct</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Random Number Function</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the function called for the random number generation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
													<DEFAULT-VALUE>Appl_Crypto_GetRandNo</DEFAULT-VALUE>
												</ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
										<ECUC-STRING-PARAM-DEF UUID="d2a005e3-0f86-4538-ab23-342c369180df">
											<SHORT-NAME>TcpIpRandNoFctIncludeFile</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Random Function Include File</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the header file holding the declaration of the random number generation function defined in 'TcpIpRandNoFct'.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-STRING-PARAM-DEF-VARIANTS>
												<ECUC-STRING-PARAM-DEF-CONDITIONAL>
													<DEFAULT-VALUE>Appl_Rand.h</DEFAULT-VALUE>
												</ECUC-STRING-PARAM-DEF-CONDITIONAL>
											</ECUC-STRING-PARAM-DEF-VARIANTS>
										</ECUC-STRING-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="c034b5ce-3bc4-4168-b025-3c6d4ba81355">
											<SHORT-NAME>TcpIpDiagExtensionsEnabled</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enable diagnostic read access to several parameters. For some parameters read access has to be enabled separately.

Read features can be located at TcpIp_General or at the different sub-components.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: TcpIpIpV4General -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="c1ca44c6-906c-422b-bee7-93600afcade7">
											<SHORT-NAME>TcpIpIpV4General</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container is a subcontainer of TcpIp and specifies the general configuration parameters of the TCP/IP stack for IPv4</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: TcpIpArpEnabled -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="9faa203f-6fdc-4b9e-a29a-ed423fe70e2a">
													<SHORT-NAME>TcpIpArpEnabled</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables (TRUE) or disables (FALSE) support of ARP (Address Resolution Protocol).</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>true</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpAutoIpEnabled -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="711cad52-6e74-4cbd-9658-722e3bb6fb8b">
													<SHORT-NAME>TcpIpAutoIpEnabled</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables (TRUE) or disables (FALSE) the Auto-IP (automatic private IP addressing) sub-module.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpDhcpClientEnabled -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="45110993-4b87-429e-9f19-bef9604954fa">
													<SHORT-NAME>TcpIpDhcpClientEnabled</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables (TRUE) or disables (FALSE) the DHCP (Dynamic Host Configuration Protocol) Client.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpIcmpEnabled -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="6b6fcd2a-09aa-463a-9c29-d3e76b7c3114">
													<SHORT-NAME>TcpIpIcmpEnabled</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables (TRUE) or disabled (FALSE) support of ICMP (Internet Control Message Protocol).</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>true</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpIpV4Enabled -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="6ed21325-4a2e-4a3f-ac20-71da2bd39dba">
													<SHORT-NAME>TcpIpIpV4Enabled</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables (TRUE) or disables (FALSE) support of IPv4 (Internet Protocol version 4).</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpLocalAddrIpv4EntriesMax -->
												<ECUC-INTEGER-PARAM-DEF UUID="129ee98f-fe2d-4b09-942d-1d7b215073e5">
													<SHORT-NAME>TcpIpLocalAddrIpv4EntriesMax</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Maximum number of LocalAddr table entries for IPv4.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpPathMtuDiscoveryEnabled -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="876cae05-7a32-4b40-8cf1-bc7420800abf">
													<SHORT-NAME>TcpIpPathMtuDiscoveryEnabled</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables (TRUE) or disables (FALSE) the discovery of the maximum transmission unit on a path according to IETF RfC 1191.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<ECUC-STRING-PARAM-DEF UUID="f1f49b49-7ac0-48ac-9cf8-4433b9f19577">
													<SHORT-NAME>TcpIpIpV4UserConfigFile</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">User Config File</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Reference to an external user configuration file that will be included during generation.
The content of the user configuration file will be added at the end of the generated module configuration file and allows altering or extending the generated code.
Caution: User configuration files can cause the software module to malfunction and must only be used with great care!</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<ECUC-REFERENCE-DEF UUID="ee6e9521-72d3-47eb-8fca-bb1f0172f660">
													<SHORT-NAME>TcpIpIpV4DefaultCtrl</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Default IP Controller</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Reference to the default controller for messages not directed to own subnet(s)</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/TcpIp/TcpIpConfig/TcpIpCtrl/TcpIpIpVXCtrl/TcpIpIpV4Ctrl</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: TcpIpIpV6General -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="0f963339-d6f3-4a29-b8c8-d26a8e21e09a">
											<SHORT-NAME>TcpIpIpV6General</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container is a subcontainer of TcpIp and specifies the general configuration parameters of the TCP/IP stack for IPv6.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: TcpIpDhcpV6ClientEnabled -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="d767476e-8e17-4452-a45b-8b670228026c">
													<SHORT-NAME>TcpIpDhcpV6ClientEnabled</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables (TRUE) or disables (FALSE) the DHCPv6 (Dynamic Host Configuration Protocol for IPv6) Client.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpIpV6Enabled -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="7785577e-7bae-4091-a6ec-53e6cde4f694">
													<SHORT-NAME>TcpIpIpV6Enabled</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables (TRUE) or disables (FALSE) support of IPv6 (Internet Protocol version 6).</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpIpV6PathMtuDiscoveryEnabled -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="3cc9b9c9-ac53-4031-9962-7ce25f9e3120">
													<SHORT-NAME>TcpIpIpV6PathMtuDiscoveryEnabled</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables (TRUE) or disables (FALSE) Path MTU Discovery support for IPv6 according to IETF RFC 1981.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpLocalAddrIpv6EntriesMax -->
												<ECUC-INTEGER-PARAM-DEF UUID="8ed02751-16d5-4506-9d6f-c3e7eccbb538">
													<SHORT-NAME>TcpIpLocalAddrIpv6EntriesMax</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Maximum number of LocalAddr table entries for IPv6.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpNdpAddressResolutionUnrechabilityDetectionEnabled -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="68a2e40c-4b38-461e-9401-851ad86d1a02">
													<SHORT-NAME>TcpIpNdpAddressResolutionUnrechabilityDetectionEnabled</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables (TRUE) or disables (FALSE) support of Address Resoultion and Neighbor Unreachability Detetion via NDP.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>true</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: TcpIpNdpPrefixAndRouterDiscoveryEnabled -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="37c8adfc-25e1-43ef-a3bf-bc618a6eb5c7">
													<SHORT-NAME>TcpIpNdpPrefixAndRouterDiscoveryEnabled</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Enables (TRUE) or disables (FALSE) support of Prefix and Router Discovery via NDP.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>true</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<ECUC-STRING-PARAM-DEF UUID="9b239a40-6859-45d0-a49c-70f7c788f755">
													<SHORT-NAME>TcpIpIpV6UserConfigFile</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">User Config File</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Reference to an external user configuration file that will be included during generation.
The content of the user configuration file will be added at the end of the generated module configuration file and allows altering or extending the generated code (in IpV6_Cfg.h).

Caution: User configuration files can cause the software module to malfunction and must only be used with great care!</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<ECUC-BOOLEAN-PARAM-DEF UUID="bc9cc465-c5a3-4186-9bc8-b45358cb31a3">
													<SHORT-NAME>TcpIpIpV6EthIfUpdatePhysAddrFilterApiEnabled</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Enable Configuration of Ethernet Group Address filter</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines whether the IpV6 will call EthIf_SetPhysAddrFilter() in order to configure the ethernet group address Rx filter.

Since the IPv6 Neighbor Discovery Protocol uses ethernet link-layer multicasts, the controller must be configured to accept received ethernet multicast IPv6 packets.

If the controller is in promiscuous mode or statically configured to accept all neccessary multicasts, this parameter can be disabled in order to avoid superfluous calls to the EthIf API.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>true</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<ECUC-BOOLEAN-PARAM-DEF UUID="bccbc7fa-254c-4b08-ad6a-e87ce0e32f8a">
													<SHORT-NAME>TcpIpIpV6SetTrafficClassAndFlowLabelApiEnabled</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Enable Traffic Class API</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines whether the IpV6 provides the IpV6_SetTrafficClass() and IpV6_SetFlowLabel() APIs to change the Traffic Class octet of the IPv6 header for outgoing packets.

If enabled the value can be set on a per-socket basis during runtime.
The default values for the Traffic Class and the Flow Label for all outgoing packets are controller specific and are configured by the IpV6DefaultTrafficClass and IpV6DefaultFlowLabel parameters.

[RFC2460 3. IPv6 Header Format]
[RFC2460 7. Traffic Classes]

see also [RFC2474 3. Differentiated Services Field Definition]</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<ECUC-BOOLEAN-PARAM-DEF UUID="cc6401f0-003a-4e1d-aa53-072cd7cc557d">
													<SHORT-NAME>TcpIpIpV6ExtendedDestAddrValidationEnabled</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines whether the IpV6 will check the destination address every time IpV6_ProvideTxBuffer() is called. 
If disabled the user should call IpV6_IsValidDestinationAddr() before IpV6_ProvideTxBuffer() is called the first time with a specific destination address.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<ECUC-BOOLEAN-PARAM-DEF UUID="cd4d574e-1ea1-4e6a-bfd0-0fa2ef3cd0ba">
													<SHORT-NAME>TcpIpIpV6CacheLookupOptimizationEnabled</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Enable Cache Lookup Optimization</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines whether lookups in internal IpV6 data structures shall be optimized.

If enabled the last lookup result is stored and reused for the next lookup.
Disabling this feature will save some bytes in RAM and ROM but reduce performance.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>true</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<ECUC-REFERENCE-DEF UUID="18479c9f-51b2-40ad-a00b-dcca1fb7667c">
													<SHORT-NAME>TcpIpIpV6DefaultCtrl</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Default IP Controller</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Reference to the IpV6 controller instance to which this address shall be assigned.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/TcpIp/TcpIpConfig/TcpIpCtrl/TcpIpIpVXCtrl/TcpIpIpV6Ctrl</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
