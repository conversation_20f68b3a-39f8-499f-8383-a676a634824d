import static com.vector.cfg.automation.api.ScriptApi.*

import com.vector.cfg.automation.model.ecuc.microsar.ecuc.EcuC
import com.vector.cfg.automation.model.ecuc.microsar.ecuc.ecucgeneral.EcucGeneral
import com.vector.cfg.automation.model.ecuc.microsar.ecuc.ecuchardware.ecuccoredefinition.EcucCoreDefinition

//daVinci enables the IDE code completion support
daVinci {
    scriptDescription "Creates container/parameter with the BswmdModel"

    /* 
     * Task: WriteTheActiveEcuC
     * Type: DV_PROJECT
     * -------------------------------------------------------------------------------------------------------
     * Creates container/parameter with the BswmdModel
     * -------------------------------------------------------------------------------------------------------
     */
	scriptTask("WriteTheActiveEcuCTask", DV_PROJECT){
		taskDescription 'Creates container/parameter with the BswmdModel'
		
		        taskHelp '''WriteTheActiveEcuC script task
    The task creates with the bswmdModel container and parameters and sets parameter values.
    '''
	
        code {
            transaction {
                // Gets the first found ecuc module instance
                EcuC ecuc = bswmdModel(EcuC.DefRef).single
                
                //Gets the EcucGeneral container or create one if it is missing
                EcucGeneral ecucGeneral = ecuc.ecucGeneralOrCreate

                // Gets an boolean parameter of this container or create one if it is missing
                def ecuCSafeBswChecks = ecucGeneral.ecuCSafeBswChecksOrCreate
				
				// Sets the parameter value to true
				ecuCSafeBswChecks.value = true

                //Gets the EcucCoreDefinition list (creates ecucHardware if it is missing)
                def ecucCoreDefinitions = ecuc.ecucHardwareOrCreate.ecucCoreDefinition

                //Adds two EcucCores
                EcucCoreDefinition core0 = ecucCoreDefinitions.createAndAdd("EcucCore0")
                EcucCoreDefinition core1 = ecucCoreDefinitions.createAndAdd("EcucCore1")

                if(ecucCoreDefinitions.exists("EcucCore0")) {
                    //Sets EcucCoreId to 0
                    ecucCoreDefinitions.byName("EcucCore0").ecucCoreId.value = 0;
                }

                //Creates a new EcucCore by method 'byNameOrCreate'
                EcucCoreDefinition core2 = ecucCoreDefinitions.byNameOrCreate("EcucCore2");
			}
	    }   
	} 
}