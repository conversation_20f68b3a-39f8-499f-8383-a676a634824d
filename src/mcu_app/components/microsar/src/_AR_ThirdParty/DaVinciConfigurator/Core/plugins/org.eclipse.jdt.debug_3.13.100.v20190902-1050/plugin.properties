###############################################################################
#  Copyright (c) 2000, 2014 IBM Corporation and others.
#
#  This program and the accompanying materials
#  are made available under the terms of the Eclipse Public License 2.0
#  which accompanies this distribution, and is available at
#  https://www.eclipse.org/legal/epl-2.0/
#
#  SPDX-License-Identifier: EPL-2.0
# 
#  Contributors:
#     IBM Corporation - initial API and implementation
###############################################################################


pluginName=JDI Debug Model
providerName=Eclipse.org

descriptionCollection=Array
descriptionMap=Map Entries
descriptionMapEntry=Key and Value

javaLineBreakpoint.name=Java Line Breakpoints
javaClassLoadBreakpoint.name=Java Class Load Breakpoints
javaExceptionBreakpoint.name=Java Exception Breakpoints
javaWatchpoint.name=Java Watchpoints
javaMethodBreakpoint.name=Java Method Breakpoints

virtualMachineManagerImpl= org.eclipse.jdi.internal.VirtualMachineManagerImpl
javaLogicalStructures= Java Logical Structures

JavaBreakpoint.name = Java Breakpoint
JavaClassLoadBreakpoint.name = Java Class Load Breakpoint
CommonJavaLineBreakpoint.name = Common Java Line Breakpoint
JavaLineBreakpoint.name = Java Line Breakpoint
JavaPatternBreakpoint.name = Java Pattern Breakpoint
JavaTargetPatternBreakpoint.name = Java Target Pattern Breakpoint
JavaExceptionBreakpoint.name = Java Exception Breakpoint
JavaWatchpoint.name = Java Watchpoint
JavaMethodBreakpoint.name = Java Method Breakpoint
JavaMethodEntryBreakpoint.name = Java Method Entry Breakpoint
JavaStratumLineBreakpoint.name = Java Stratum Line Breakpoint

breakpointListeners.name = Java Breakpoint Listeners

descriptionDomNode = XML DOM Element

trace.name = JDT Debug Core
