<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2017 rel. 3 sp1 (x64) (http://www.altova.com) by Vector employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-2-1.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.6.0
    Document Status: Final
    Part of Release: 4.2 (2014-10-31)
    Revision: 1
    -->
	<!-- Generated on Wed Oct 29 11:47:12 CET 2014 -->
	<!-- MMT:        2.32.1 -->
	<!-- Meta-Model: https://svn.autosar.org/repos/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@192120 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.6.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2014-10-31</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: Os -->
						<ECUC-MODULE-DEF UUID="ECUC:8f6994a0-a4c8-48b9-bede-db9a5b9c2474">
							<SHORT-NAME>Os</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the Os (Operating System) module.</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.6.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
										<DATE>2014-10-31</DATE>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<POST-BUILD-VARIANT-SUPPORT>false</POST-BUILD-VARIANT-SUPPORT>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: OsAlarm -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:f5bf116f-a0a2-4a8b-9f6a-1b023e6d9a13">
									<SHORT-NAME>OsAlarm</SHORT-NAME>
									<DESC>
										<L-2 L="EN">An OsAlarm may be used to asynchronously inform or activate a specific task. It is possible to start alarms automatically at system start-up depending on the application mode.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<REFERENCES>
										<!-- Reference Definition: OsAlarmAccessingApplication -->
										<ECUC-REFERENCE-DEF UUID="ECUC:7fe87d8e-e45a-4ce7-a368-b19db06f21cf">
											<SHORT-NAME>OsAlarmAccessingApplication</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to applications which have an access to this object.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsApplication</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<!-- Reference Definition: OsAlarmCounterRef -->
										<ECUC-REFERENCE-DEF UUID="ECUC:2cfca734-698f-4fbe-b67f-60309f9d1df0">
											<SHORT-NAME>OsAlarmCounterRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to the assigned counter for that alarm</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsCounter</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<!-- Container Definition: OsAlarmAction -->
										<ECUC-CHOICE-CONTAINER-DEF UUID="ECUC:2acfb5a0-b476-4fa4-a34c-08e4acfe9858">
											<SHORT-NAME>OsAlarmAction</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container defines which type of notification is used when the alarm expires.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<CHOICES>
												<!-- Container Definition: OsAlarmActivateTask -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:778af345-37da-4297-a5cf-617140faa94f">
													<SHORT-NAME>OsAlarmActivateTask</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container specifies the parameters to activate a task.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<REFERENCES>
														<!-- Reference Definition: OsAlarmActivateTaskRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:9d760cf7-d9fc-4fcf-a65f-1e5ac5d57eaf">
															<SHORT-NAME>OsAlarmActivateTaskRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the task that will be activated by that alarm action</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<SCOPE>LOCAL</SCOPE>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsTask</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: OsAlarmCallback -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:d5df234a-b9c3-40e5-b30e-6ee6919a6c8e">
													<SHORT-NAME>OsAlarmCallback</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container specifies the parameters to call a callback OS alarm action.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: OsAlarmCallbackName -->
														<ECUC-FUNCTION-NAME-DEF UUID="ECUC:146cd561-a131-447a-8070-26ea050189b9">
															<SHORT-NAME>OsAlarmCallbackName</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Name of the function that is called when this alarm callback is triggered.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<SCOPE>LOCAL</SCOPE>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<ECUC-FUNCTION-NAME-DEF-VARIANTS>
																<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
															</ECUC-FUNCTION-NAME-DEF-VARIANTS>
														</ECUC-FUNCTION-NAME-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: OsAlarmIncrementCounter -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e7f3faba-d482-41dd-9a78-a1bf24e96207">
													<SHORT-NAME>OsAlarmIncrementCounter</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container specifies the parameters to increment a counter.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<REFERENCES>
														<!-- Reference Definition: OsAlarmIncrementCounterRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:f011f6a5-29e7-446b-85f5-b91034e9f12b">
															<SHORT-NAME>OsAlarmIncrementCounterRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the counter that will be incremented by that alarm action</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<SCOPE>ECU</SCOPE>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsCounter</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: OsAlarmSetEvent -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:135c6787-8aad-4ca3-9c1b-f9db78eea4c0">
													<SHORT-NAME>OsAlarmSetEvent</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container specifies the parameters to set an event</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<REFERENCES>
														<!-- Reference Definition: OsAlarmSetEventRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:1f077d18-ca8d-42bd-b867-4e59ce3d8e50">
															<SHORT-NAME>OsAlarmSetEventRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the event that will be set by that alarm action</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<SCOPE>LOCAL</SCOPE>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsEvent</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
														<!-- Reference Definition: OsAlarmSetEventTaskRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:db071009-6743-496a-ae4a-bb272eeb975a">
															<SHORT-NAME>OsAlarmSetEventTaskRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the task that will be activated by that event</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<SCOPE>LOCAL</SCOPE>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsTask</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</CHOICES>
										</ECUC-CHOICE-CONTAINER-DEF>
										<!-- Container Definition: OsAlarmAutostart -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:964053de-2482-4575-a091-31f2b2d62db1">
											<SHORT-NAME>OsAlarmAutostart</SHORT-NAME>
											<DESC>
												<L-2 L="EN">If present this container defines if an alarm is started automatically at system start-up depending on the application mode.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: OsAlarmAlarmTime -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:60cfd12a-e181-4369-9286-c0432e6e5cf5">
													<SHORT-NAME>OsAlarmAlarmTime</SHORT-NAME>
													<DESC>
														<L-2 L="EN">The relative or absolute tick value when the alarm expires for the first time. Note that for an alarm which is RELATIVE the value must be at bigger than 0.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>18446744073709551615</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: OsAlarmAutostartType -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:3aeb1a3f-7305-4179-b995-e284a9efb475">
													<SHORT-NAME>OsAlarmAutostartType</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This specifies the type of autostart for the alarm..</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:1245fdf5-dd37-8737-3cd7-61854583b15d">
															<SHORT-NAME>ABSOLUTE</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:0466ecc3-dc3e-8973-3dfc-564d3d3a05c3">
															<SHORT-NAME>RELATIVE</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: OsAlarmCycleTime -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:3ad7702f-8f33-4b7d-87df-1fb32d89fb61">
													<SHORT-NAME>OsAlarmCycleTime</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Cycle time of a cyclic alarm in ticks. If the value is 0 than the alarm is not cyclic.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>18446744073709551615</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: OsAlarmAppModeRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:a1c6da78-4cb0-4df2-92ae-69875adaedeb">
													<SHORT-NAME>OsAlarmAppModeRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the application modes for which the AUTOSTART shall be performed</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsAppMode</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: OsAppMode -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:44b2af00-f4af-494b-9b23-6c4bd5ea9ca7">
									<SHORT-NAME>OsAppMode</SHORT-NAME>
									<DESC>
										<L-2 L="EN">OsAppMode is the object used to define OSEK OS properties for an OSEK OS application mode.</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">No standard attributes are defined for AppMode.

                                In a CPU, at least one AppMode object has to be defined.

                                [source: OSEK OIL Spec. 2.5]

                                An OsAppMode called OSDEFAULTAPPMODE must always be there for OSEK compatibility.</L-1>
										</P>
									</INTRODUCTION>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: OsApplication -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:3fe22410-f7c0-4ae4-a89a-e6d8c2861115">
									<SHORT-NAME>OsApplication</SHORT-NAME>
									<DESC>
										<L-2 L="EN">An AUTOSAR OS must be capable of supporting a collection of OS objects (tasks, interrupts, alarms, hooks etc.) that form a cohesive functional unit. This collection of objects is termed an OS-Application.</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">All objects which belong to the same OS-Application have access to each other. Access means to allow to use these objects within API services.

                                Access by other applications can be granted separately.</L-1>
										</P>
									</INTRODUCTION>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: OsTrusted -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:97ddd259-bb90-4c42-af70-aa8b24630faf">
											<SHORT-NAME>OsTrusted</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Parameter to specify if an OS-Application is trusted or not.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: OS-Application is trusted
                                        false: OS-Application is not trusted (default)</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>ECU</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: OsTrustedApplicationDelayTimingViolationCall -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:fd5344b6-872d-4e27-a451-f55587b0a918">
											<SHORT-NAME>OsTrustedApplicationDelayTimingViolationCall</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Parameter to specify if a timing violation which occurs within an trusted OS-Application is raised immediately of if it is delayed until the current task returns to the calling OS-Application (return of CallTrustedFunction)</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: violation / call to ProtectionHook() is delayed 
                                        false: timing violation cause an immediate call to the ProtectionHook().</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>ECU</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: OsTrustedApplicationWithProtection -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:e8a533a6-6a5d-4154-9ac4-2d7f90f12d44">
											<SHORT-NAME>OsTrustedApplicationWithProtection</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Parameter to specify if a trusted OS-Application is executed with memory protection or not.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: OS-Application runs within a protected environment. This means that write access is limited.
                                        false: OS-Application has full write access (default)</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>ECU</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Reference Definition: OsAppAlarmRef -->
										<ECUC-REFERENCE-DEF UUID="ECUC:f682f3ec-e731-4683-9429-6f3139967bc4">
											<SHORT-NAME>OsAppAlarmRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the OsAlarms that belong to the OsApplication.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<SCOPE>ECU</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsAlarm</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<!-- Reference Definition: OsAppCounterRef -->
										<ECUC-REFERENCE-DEF UUID="ECUC:4a2dbf82-5e24-4d1b-b778-6691fa1cde23">
											<SHORT-NAME>OsAppCounterRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">References the OsCounters that belong to the OsApplication.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsCounter</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<!-- Reference Definition: OsAppEcucPartitionRef -->
										<ECUC-REFERENCE-DEF UUID="ECUC:493ffec7-e80e-4945-bc55-09cfe0eb4c78">
											<SHORT-NAME>OsAppEcucPartitionRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Denotes which &quot;EcucPartition&quot; is implemented by this &quot;OSApplication&quot;.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPartitionCollection/EcucPartition</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<!-- Reference Definition: OsAppIsrRef -->
										<ECUC-REFERENCE-DEF UUID="ECUC:ff388a32-96e2-44e3-852f-b565616152fb">
											<SHORT-NAME>OsAppIsrRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">references which OsIsrs belong to the OsApplication</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsIsr</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<!-- Reference Definition: OsAppScheduleTableRef -->
										<ECUC-REFERENCE-DEF UUID="ECUC:80e78dc3-aacf-442e-8c44-5b7ede865f44">
											<SHORT-NAME>OsAppScheduleTableRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">References the OsScheduleTables that belong to the OsApplication.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsScheduleTable</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<!-- Reference Definition: OsAppTaskRef -->
										<ECUC-REFERENCE-DEF UUID="ECUC:934a7899-f255-444b-a117-7d4ffead78e9">
											<SHORT-NAME>OsAppTaskRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">references which OsTasks belong to the OsApplication</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsTask</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<!-- Reference Definition: OsRestartTask -->
										<ECUC-REFERENCE-DEF UUID="ECUC:d87bac0e-011a-4578-bfea-01885c0240f3">
											<SHORT-NAME>OsRestartTask</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Optionally one task of an OS-Application may be defined as Restart Task.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Multiplicity = 1: Restart Task is activated by the Operating System if the protection hook requests it.

                                        Multiplicity = 0: No task is automatically started after a protection error happened.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsTask</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<!-- Symbolic Name Reference Definition: OsApplicationCoreRef -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:487eb46e-c2de-4b01-8138-31906865ee1f">
											<SHORT-NAME>OsApplicationCoreRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to the Core Definition in the Ecuc Module where the CoreId is defined. This reference is used to describe to which Core the OsApplication is bound.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucHardware/EcucCoreDefinition</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<!-- Container Definition: OsApplicationHooks -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:504b9c2f-8c8f-4650-878e-fbaa905cd5c6">
											<SHORT-NAME>OsApplicationHooks</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container to structure the OS-Application-specific hooks</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: OsAppErrorHook -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:dae2e0ce-2b38-4991-81df-93f1ebd88c4f">
													<SHORT-NAME>OsAppErrorHook</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Select the OS-Application error hook.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">true: Hook is called
                                                false: Hook is not called</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: OsAppShutdownHook -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:b0d64ef8-4929-4647-ac16-2c7e541a19a4">
													<SHORT-NAME>OsAppShutdownHook</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Select the OS-Application specific shutdown hook for the OS-Application.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">true: Hook is called
                                                false: Hook is not called</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: OsAppStartupHook -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:d30c7023-7afc-468a-86da-70498ba04654">
													<SHORT-NAME>OsAppStartupHook</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Select the OS-Application specific startup hook for the OS-Application.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">true: Hook is called
                                                false: Hook is not called</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: OsApplicationTrustedFunction -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:727531f6-47c8-441a-b36f-15d71e958b91">
											<SHORT-NAME>OsApplicationTrustedFunction</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container to structure the configuration parameters of trusted functions</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: OsTrustedFunctionName -->
												<ECUC-FUNCTION-NAME-DEF UUID="ECUC:af3592ef-5e5c-4a29-8c5f-a3f80aa67ae7">
													<SHORT-NAME>OsTrustedFunctionName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Trusted function (as part of a trusted OS-Application) available to other OS-Applications. This also supersedes the OSEK OIL attribute TRUSTED in APPLICATION because the optionality of this parameter is describing that already.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-FUNCTION-NAME-DEF-VARIANTS>
														<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
													</ECUC-FUNCTION-NAME-DEF-VARIANTS>
												</ECUC-FUNCTION-NAME-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: OsCounter -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:027f4979-a1d4-4ba0-a615-19a5a8081073">
									<SHORT-NAME>OsCounter</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Configuration information for the counters that belong to the OsApplication.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: OsCounterMaxAllowedValue -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:deceab52-a44e-40eb-9737-8eea4e860802">
											<SHORT-NAME>OsCounterMaxAllowedValue</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Maximum possible allowed value of the system counter in ticks.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>18446744073709551615</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: OsCounterMinCycle -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:4a0415c2-c296-49d6-b76a-3ce3e75112f3">
											<SHORT-NAME>OsCounterMinCycle</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The MINCYCLE attribute specifies the minimum allowed number of counter ticks for a cyclic alarm linked to the counter.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>18446744073709551615</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: OsCounterTicksPerBase -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:25d694e2-85d2-4c8a-9ceb-6abafafbf40b">
											<SHORT-NAME>OsCounterTicksPerBase</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The TICKSPERBASE attribute specifies the number of ticks required to reach a counterspecific unit. The interpretation is implementation-specific.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: OsCounterType -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:77593730-3c82-4979-ba08-f9678e001cf3">
											<SHORT-NAME>OsCounterType</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter contains the natural type or unit of the counter.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:9d735b1c-ec0e-9b0f-4b2d-73c84e61bca2">
													<SHORT-NAME>HARDWARE</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:11446ab3-f2c1-94ac-28a5-687e992c120a">
													<SHORT-NAME>SOFTWARE</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: OsSecondsPerTick -->
										<ECUC-FLOAT-PARAM-DEF UUID="ECUC:7cbe21f0-2e73-4fc2-928e-3337f481cb10">
											<SHORT-NAME>OsSecondsPerTick</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Time of one counter tick in seconds.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>Inf</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Reference Definition: OsCounterAccessingApplication -->
										<ECUC-REFERENCE-DEF UUID="ECUC:48c50338-5eac-440a-860d-4158ed36ae2d">
											<SHORT-NAME>OsCounterAccessingApplication</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to applications which have an access to this object.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsApplication</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<!-- Container Definition: OsDriver -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:5187c6ca-a5b2-4939-b6e0-1d5775ef679e">
											<SHORT-NAME>OsDriver</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This Container contains the information who will drive the counter.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">This configuration is only valid if the counter has OsCounterType set to HARDWARE.

                                        If the container does not exist (multiplicity=0) the timer is managed by the OS internally (OSINTERNAL).

                                        If the container exists the OS can use the GPT interface to manage the timer. The user have to supply the GPT channel.

                                        If the counter is driven by some other (external to the OS) source (like a TPU for example) this must be described as a vendor specific extension.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<REFERENCES>
												<!-- Reference Definition: OsGptChannelRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:7a91b0f3-8545-4fb3-aa38-************">
													<SHORT-NAME>OsGptChannelRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the GPT channel.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Gpt/GptChannelConfigSet/GptChannelConfiguration</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: OsTimeConstant -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:6488e442-e1ab-4d9a-9974-22cb3a102f4a">
											<SHORT-NAME>OsTimeConstant</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Allows the user to define constants which can be e.g. used to compare time values with timer tick values.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">A time value will be converted to a timer tick value during generation and can later on accessed via the OsConstName. The conversation is done by rounding time values to the nearest fitting tick value.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: OsTimeValue -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:daba8b10-b46e-4dc9-a6d3-8f6ab1126f30">
													<SHORT-NAME>OsTimeValue</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter contains the value of the constant in seconds.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>Inf</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: OsEvent -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:08ff8745-1b33-4b16-829b-c1d7190a6ae6">
									<SHORT-NAME>OsEvent</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Representation of OS events in the configuration context. Adopted from the OSEK OIL specification.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: OsEventMask -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:51c76d97-345c-4b78-9e45-25dd67fe7095">
											<SHORT-NAME>OsEventMask</SHORT-NAME>
											<DESC>
												<L-2 L="EN">If event mask would be set to AUTO in OIL, this parameter should be omitted here.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>18446744073709551615</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: OsIoc -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:8b83effd-234a-4731-8213-0b637df7eefc">
									<SHORT-NAME>OsIoc</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Configuration of the IOC (Inter OS Application Communicator).</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SUB-CONTAINERS>
										<!-- Container Definition: OsIocCommunication -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:aba13287-7385-453e-9e67-fbdbe8c9f86f">
											<SHORT-NAME>OsIocCommunication</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Representation of a 1:1 or N:1 or N:M (unqueued only) communication between software parts located in different OS-Applications that are bound to the same or to different cores.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The name shall begin with the name of the sending software service and be followed by a unique identifier delivered by the sending software service. In the case of RTE as user attention shall be paid on the fact that uniqueness for identifier names has to be reached over ports, data elements, object instances and maybe additional identification properties (E.g. Case 1:N mapping to 1:1).

                                        Example:
                                        - &lt;NameSpace&gt;_UniqueID</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: OsIocBufferLength -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:c70d8576-6d52-46f7-b8ff-17c313e635ea">
													<SHORT-NAME>OsIocBufferLength</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This attribute defines the size of the IOC internal queue to be allocated for a queued communication.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This configuration information shall allow the optimization of the needed memory for communications requiring buffers within the RTE and within the IOC.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<SUB-CONTAINERS>
												<!-- Container Definition: OsIocDataProperties -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:5c503fab-e8a7-4dce-84b9-6d58467b7611">
													<SHORT-NAME>OsIocDataProperties</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Data properties of the data to be transferred on the IOC communication channel.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: OsIocDataPropertyIndex -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:ac4396f5-cb92-4713-b285-e5f8ea079275">
															<SHORT-NAME>OsIocDataPropertyIndex</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter is used to define in which order the data is send, e.g. whether IocSendGroup(A,B) or IocSendGroup(B,A) shall be used.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<MULTIPLICITY-CONFIG-CLASSES>
																<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															</MULTIPLICITY-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: OsIocInitValue -->
														<ECUC-STRING-PARAM-DEF UUID="ECUC:8986f06e-9232-4f20-aec6-a8dcd936d1e8">
															<SHORT-NAME>OsIocInitValue</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Initial Value for the data to be transferred on the IOC communication channel.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<MULTIPLICITY-CONFIG-CLASSES>
																<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															</MULTIPLICITY-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<ECUC-STRING-PARAM-DEF-VARIANTS>
																<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
															</ECUC-STRING-PARAM-DEF-VARIANTS>
														</ECUC-STRING-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Foreign Reference Definition: OsIocDataTypeRef -->
														<ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:37e02eb6-462c-41c1-9ca8-05f8ffd1c014">
															<SHORT-NAME>OsIocDataTypeRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This is the type of the data to be transferred on the IOC communication channel. This attribute is necessary to generate the parameter type of the Ioc functions. Additionally this information should be used to compute the data size for necessary data copy operations within the Ioc module.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">If more than one attribute is defined, the IOC generator should generate an IocXxxGroup function (Xxx= CHOICE [Send, Receive, Write, Read]).

                                                        N:1 or N:M communication (Multiplicity of OsIocSenderProperties &gt; 1) is only allowed for multiplicity of OsIocDataTypeRef = 1</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<DESTINATION-TYPE>IMPLEMENTATION-DATA-TYPE</DESTINATION-TYPE>
														</ECUC-FOREIGN-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: OsIocReceiverProperties -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:7748837c-c78f-4d1d-be0a-96a61a7b5d03">
													<SHORT-NAME>OsIocReceiverProperties</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Representation of receiver properties for one communication. For each OsIocCommunication one (1:1) or many receivers (N:M) have to be defined.  This container should be instantiated within an OsIocCommunication.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: OsIocFunctionImplementationKind -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:7d3d9611-afaf-921c-4cc6-9a6878ace501">
															<SHORT-NAME>OsIocFunctionImplementationKind</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter is used to select whether this communication is implemented as a macro or as a function.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<MULTIPLICITY-CONFIG-CLASSES>
																<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															</MULTIPLICITY-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>DO_NOT_CARE</DEFAULT-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:fe6bfef1-2414-ddf1-df71-cc84030cd400">
																	<SHORT-NAME>DO_NOT_CARE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:046f25a6-b147-d431-f68a-ad5dc5dfef5b">
																	<SHORT-NAME>FUNCTION</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:a7ef48c1-5e60-d7e4-0c56-8745d85c34dc">
																	<SHORT-NAME>MACRO</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: OsIocReceiverPullCB -->
														<ECUC-FUNCTION-NAME-DEF UUID="ECUC:6a5d33f7-6833-492a-a7df-60cbc722edd6">
															<SHORT-NAME>OsIocReceiverPullCB</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This attribute defines the name of a callback function that the IOC shall call on the receiving core for each data reception.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">In case of non existence of this attribute no ReceiverPullCB notification shall be applied by the IOC. The name of the function shall begin with the name of the receiving module, followed with a callback name and followed by the IocId.

                                                        Example: void RTE_ReceiverPullCB_RTE25 (void).

                                                        If this attribute does not exist, it means that no ReceiverPullCB shall be called (No notification from IOC is required). If this attribute exists the IOC shall call the callback function on the receiving core.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<MULTIPLICITY-CONFIG-CLASSES>
																<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															</MULTIPLICITY-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<ECUC-FUNCTION-NAME-DEF-VARIANTS>
																<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
															</ECUC-FUNCTION-NAME-DEF-VARIANTS>
														</ECUC-FUNCTION-NAME-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: OsIocReceivingOsApplicationRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:4f6d8b5f-01eb-47c6-a977-9ac5a00359fd">
															<SHORT-NAME>OsIocReceivingOsApplicationRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This attribute is a reference to the receiving OsApplication instance defined in the configuration file of the OS.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">This information allows for the generator to get additional information necessary for the code generation like:
                                                        * The protection properties of the communicating OsApplications to find out which protections have to be crossed
                                                        * The core identifiers to find out if an intra or an inter core communication has to be realized
                                                        * Interrupt details in case of cross core notification to realize over IRQs</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsApplication</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: OsIocSenderProperties -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:19574439-317b-4c79-839b-e2e1db7db6ee">
													<SHORT-NAME>OsIocSenderProperties</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Representation of sender properties for one communication. For each OsIocCommunication one (1:1) or many senders (N:1 or N:M) have to be defined. Multiplicity &gt; 1 (N:1 or N:M communication) is only allowed for Multiplicity of OsIocDataTypeRef = 1.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This container should be instantiated within an OsIocCommunication.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: OsIocFunctionImplementationKind -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:1f4c56ce-199b-9178-1257-e6a439af3eec">
															<SHORT-NAME>OsIocFunctionImplementationKind</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter is used to select whether this communication is implemented as a macro or as a function.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<MULTIPLICITY-CONFIG-CLASSES>
																<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															</MULTIPLICITY-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>DO_NOT_CARE</DEFAULT-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:a07abfad-8e00-dd4d-a503-18bfc40f2deb">
																	<SHORT-NAME>DO_NOT_CARE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:a67de663-1b33-d38d-bc1b-f99986e24946">
																	<SHORT-NAME>FUNCTION</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:49fe097d-c84c-d740-d1e7-d381995e8ec7">
																	<SHORT-NAME>MACRO</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: OsIocSenderId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:37e0ea2c-99bb-44bc-bd7c-022e8bfe7d41">
															<SHORT-NAME>OsIocSenderId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Representation of a sender in a N:1 or N:M communication to distinguish between senders.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">This parameter does not exist in 1:1 communication.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<MULTIPLICITY-CONFIG-CLASSES>
																<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															</MULTIPLICITY-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: OsIocSendingOsApplicationRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:45c166df-7068-47de-84b7-75ed075aab39">
															<SHORT-NAME>OsIocSendingOsApplicationRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This attribute is a reference to the sending OS-Application instance defined in the configuration file of the OS.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">This information shall allows the generator to get additional information necessary for the code generation like:
                                                        * The protection properties of the communicating OS-Applications to find out which protection boundaries have to be crossed.
                                                        * The core identifiers to find out if an intra or an inter core communication has to be realized
                                                        * Interrupt details in case of cross core notification to realize over IRQs</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsApplication</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: OsIsr -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:7d5716e3-c20e-4559-ba9c-2d90f50cfd28">
									<SHORT-NAME>OsIsr</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The OsIsr container represents an OSEK interrupt service routine.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: OsIsrCategory -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:5d95ea52-e3b6-4b84-bc0c-20a37ebfd672">
											<SHORT-NAME>OsIsrCategory</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This attribute specifies the category of this ISR.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:2e25f577-6916-89cb-52c3-cf8f0f997b8f">
													<SHORT-NAME>CATEGORY_1</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:0a6f27b6-f20e-940c-51a4-220a88eaa9bc">
													<SHORT-NAME>CATEGORY_2</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Reference Definition: OsIsrResourceRef -->
										<ECUC-REFERENCE-DEF UUID="ECUC:eb6f779e-e5dd-4870-a9cb-8f784fcccebe">
											<SHORT-NAME>OsIsrResourceRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This reference defines the resources accessed by this ISR.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsResource</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<!-- Container Definition: OsIsrTimingProtection -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:ed417020-f2f2-47e0-8abb-64e54cb4e8fd">
											<SHORT-NAME>OsIsrTimingProtection</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains all parameters which are related to timing protection</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">If the container exists, the timing protection is used for this interrupt. If the container does not exist, the interrupt is not supervised regarding timing violations.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: OsIsrAllInterruptLockBudget -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:911b5d2a-86c8-4a1c-9741-d1ada875c94e">
													<SHORT-NAME>OsIsrAllInterruptLockBudget</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter contains the maximum time for which the ISR is allowed to lock all interrupts (via SuspendAllInterrupts() or DisableAllInterrupts()) (in seconds).</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>Inf</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: OsIsrExecutionBudget -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:ff435610-1cde-4530-8b4d-1818caf9bf0f">
													<SHORT-NAME>OsIsrExecutionBudget</SHORT-NAME>
													<DESC>
														<L-2 L="EN">The parameter contains the maximum allowed execution time of the interrupt (in seconds).</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>Inf</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: OsIsrOsInterruptLockBudget -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:f13180b1-a237-4132-9f90-2e6e93e191cf">
													<SHORT-NAME>OsIsrOsInterruptLockBudget</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter contains the maximum time for which the ISR is allowed to lock all Category 2 interrupts (via SuspendOSInterrupts()) (in seconds).</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>Inf</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: OsIsrTimeFrame -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:3d855434-c46e-40a1-adcc-985f085d2efb">
													<SHORT-NAME>OsIsrTimeFrame</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter contains the minimum inter-arrival time between successive interrupts (in seconds).</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>Inf</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
											</PARAMETERS>
											<SUB-CONTAINERS>
												<!-- Container Definition: OsIsrResourceLock -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:2e290a5c-ef82-4b7d-8f75-d4bf62b2c3ab">
													<SHORT-NAME>OsIsrResourceLock</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container contains a list of times the interrupt uses resources.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: OsIsrResourceLockBudget -->
														<ECUC-FLOAT-PARAM-DEF UUID="ECUC:3b769901-8b89-42be-b408-244ff46e3cbd">
															<SHORT-NAME>OsIsrResourceLockBudget</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter contains the maximum time the interrupt is allowed to hold the given resource (in seconds).</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>Inf</MAX>
															<MIN>0</MIN>
														</ECUC-FLOAT-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: OsIsrResourceLockResourceRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:7f296c7f-70f7-40db-85ce-c48b5974edb7">
															<SHORT-NAME>OsIsrResourceLockResourceRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the resource the locking time is depending on</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsResource</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: OsOS -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:037fee7c-9826-4ab9-9067-69b24b57fe12">
									<SHORT-NAME>OsOS</SHORT-NAME>
									<DESC>
										<L-2 L="EN">OS is the object used to define OSEK OS properties for an OSEK application.</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">Per CPU exactly one OS object has to be defined.</L-1>
										</P>
									</INTRODUCTION>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: OsNumberOfCores -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:1369dbe1-88b9-4ff5-83d3-a62c5cf4106a">
											<SHORT-NAME>OsNumberOfCores</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Maximum number of cores that are controlled by the OS.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The OS uses the value internally. It depends on the ECU HW.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: OsScalabilityClass -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:a4c62701-41c5-417c-8b66-16c90d6d111f">
											<SHORT-NAME>OsScalabilityClass</SHORT-NAME>
											<DESC>
												<L-2 L="EN">A scalability class for each System Object &quot;OS&quot; has to be selected. In order to customize the operating system to the needs of the user and to take full advantage of the processor features the operating system can be scaled according to the scalability classes.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">If the scalability class is omitted this translates to the OIL AUTO mechanism.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:ce27c590-e408-98ea-42a2-bc1746a0c2cb">
													<SHORT-NAME>SC1</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:070246b2-500e-8def-4157-ade4cbb0c048">
													<SHORT-NAME>SC2</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:38bafefe-a5d1-9974-2581-505f9bc27e21">
													<SHORT-NAME>SC3</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:09b92f6c-de62-970b-3305-226efbeff18e">
													<SHORT-NAME>SC4</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: OsStackMonitoring -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:5e09948e-f8cb-44c6-9e7a-3595c1a2a6c4">
											<SHORT-NAME>OsStackMonitoring</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Select stack monitoring of Tasks/Category 2 ISRs</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: Stacks are monitored
                                        false: Stacks are not monitored</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: OsStatus -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:75a1986f-1458-49fb-9feb-b84c6e58956e">
											<SHORT-NAME>OsStatus</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The Status attribute specifies whether a system with standard or extended status has to be used. Automatic assignment is not supported for this attribute.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:d080f403-c709-8d73-5041-5bb10c8e5012">
													<SHORT-NAME>EXTENDED</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:459f6974-a636-913e-2f29-4a7081d99e3c">
													<SHORT-NAME>STANDARD</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: OsUseGetServiceId -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:412bc048-fac1-432d-af87-1267b11f565b">
											<SHORT-NAME>OsUseGetServiceId</SHORT-NAME>
											<DESC>
												<L-2 L="EN">As defined by OSEK</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: OsUseParameterAccess -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:742e833a-68cd-431e-94d8-2321a014481d">
											<SHORT-NAME>OsUseParameterAccess</SHORT-NAME>
											<DESC>
												<L-2 L="EN">As defined by OSEK</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: OsUseResScheduler -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:ca31f39a-5057-484b-8408-ca0d0ed82a46">
											<SHORT-NAME>OsUseResScheduler</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The OsUseResScheduler attribute defines whether the resource RES_SCHEDULER is used within the application.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: OsHooks -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e03dab8e-c227-44c9-bf9b-38f0564bd4df">
											<SHORT-NAME>OsHooks</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container to structure all hooks belonging to the OS</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: OsErrorHook -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:825a1254-49f2-4f16-aa94-d9eedd045480">
													<SHORT-NAME>OsErrorHook</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Error hook as defined by OSEK</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">true: Hook is called
                                                false: Hook is not called</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: OsPostTaskHook -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:67f9db57-53e9-4e0e-8e47-23cc534733b5">
													<SHORT-NAME>OsPostTaskHook</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Post-task hook as defined by OSEK</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">true: Hook is called
                                                false: Hook is not called</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: OsPreTaskHook -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:fbfe7932-0338-461a-b87d-58af3a85bbcb">
													<SHORT-NAME>OsPreTaskHook</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Pre-task hook as defined by OSEK</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">true: Hook is called
                                                false: Hook is not called</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: OsProtectionHook -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:bafbef31-9d83-4b29-b534-300490f20f1f">
													<SHORT-NAME>OsProtectionHook</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Switch to enable/disable the call to the (user supplied) protection hook.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">true: Protection hook is called on protection error
                                                false: Protection hook is not called</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: OsShutdownHook -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:69032d37-2751-420e-9b13-75625e8f3e9f">
													<SHORT-NAME>OsShutdownHook</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Shutdown hook as defined by OSEK</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">true: Hook is called
                                                false: Hook is not called</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: OsStartupHook -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:591b0fc5-9552-4b15-99d8-74b85ac249bd">
													<SHORT-NAME>OsStartupHook</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Startup hook as defined by OSEK</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">true: Hook is called
                                                false: Hook is not called</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: OsResource -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:8a186c09-bd3e-413f-869f-40b71e71b89e">
									<SHORT-NAME>OsResource</SHORT-NAME>
									<DESC>
										<L-2 L="EN">An OsResource object is used to co-ordinate the concurrent access by tasks and ISRs to a shared resource, e.g. the scheduler, any program sequence, memory or any hardware area.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: OsResourceProperty -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:00ff7313-1773-4a7c-a3f0-236baeb9dab5">
											<SHORT-NAME>OsResourceProperty</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This specifies the type of the resource.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:5698c562-a95f-8ed7-0be5-0da7c8f7df4f">
													<SHORT-NAME>INTERNAL</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:36e03569-0663-83dc-127c-89f7f01b051e">
													<SHORT-NAME>LINKED</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:fe0404bf-255a-8150-0b6e-dc8382946386">
													<SHORT-NAME>STANDARD</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Reference Definition: OsResourceAccessingApplication -->
										<ECUC-REFERENCE-DEF UUID="ECUC:feec2b3b-2a1e-4939-a9bf-f1debcd01166">
											<SHORT-NAME>OsResourceAccessingApplication</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to applications which have an access to this object.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsApplication</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<!-- Reference Definition: OsResourceLinkedResourceRef -->
										<ECUC-REFERENCE-DEF UUID="ECUC:9ce7daad-5838-4fdb-aedd-031c7f5691cd">
											<SHORT-NAME>OsResourceLinkedResourceRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The link to the resource. Must be valid if OsResourceProperty is LINKED. If OsResourceProperty is not LINKED the value is ignored.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsResource</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: OsScheduleTable -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:523c4517-6584-40ca-957a-8327c5c01843">
									<SHORT-NAME>OsScheduleTable</SHORT-NAME>
									<DESC>
										<L-2 L="EN">An OsScheduleTable addresses the synchronization issue by providing an encapsulation of a statically defined set of alarms that cannot be modified at runtime.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: OsScheduleTableDuration -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:65aea8af-eeef-43c7-81bf-bec539d2290d">
											<SHORT-NAME>OsScheduleTableDuration</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the modulus of the schedule table (in ticks).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>18446744073709551615</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: OsScheduleTableRepeating -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:c901492e-c50c-45d9-9c20-9448c2f26a40">
											<SHORT-NAME>OsScheduleTableRepeating</SHORT-NAME>
											<DESC>
												<L-2 L="EN">true: first expiry point on the schedule table shall be processed at final expiry point delay ticks after the final expiry point is processed.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">false: the schedule table processing stops when the final expiry point is processed.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Reference Definition: OsSchTblAccessingApplication -->
										<ECUC-REFERENCE-DEF UUID="ECUC:f3dc4356-37e5-4f5e-9aa9-6f9fbe362508">
											<SHORT-NAME>OsSchTblAccessingApplication</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to applications which have an access to this object.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsApplication</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<!-- Reference Definition: OsScheduleTableCounterRef -->
										<ECUC-REFERENCE-DEF UUID="ECUC:f1c26a81-2cfd-4626-8d21-ad2e90b780ad">
											<SHORT-NAME>OsScheduleTableCounterRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter contains a reference to the counter which drives the schedule table.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsCounter</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<!-- Container Definition: OsScheduleTableAutostart -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:ddae46d1-ae87-4bf1-9142-d38f36e01718">
											<SHORT-NAME>OsScheduleTableAutostart</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container specifies if and how the schedule table is started on startup of the Operating System. The options to start a schedule table correspond to the API calls to start schedule tables during runtime.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: OsScheduleTableAutostartType -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:b3a89925-bcbd-4c14-b18d-ce7ab18eab54">
													<SHORT-NAME>OsScheduleTableAutostartType</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This specifies the type of the autostart for the schedule table.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:4d90b9ec-6db6-9b55-47e5-f1586e6b71d7">
															<SHORT-NAME>ABSOLUTE</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:b6e655a1-5810-99b4-2593-9328c8b837fb">
															<SHORT-NAME>RELATIVE</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:a54767b0-3ea1-998b-1e8d-2a218882ea04">
															<SHORT-NAME>SYNCHRON</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: OsScheduleTableStartValue -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:3253c9f1-**************-bd5e22b64bf8">
													<SHORT-NAME>OsScheduleTableStartValue</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Absolute autostart tick value when the schedule table starts. Only used if the OsScheduleTableAutostartType is ABSOLUTE.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">Relative offset in ticks when the schedule table starts. Only used if the OsScheduleTableAutostartType is RELATIVE.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>18446744073709551615</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: OsScheduleTableAppModeRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:a1e5af6a-7900-4b84-a608-d0199790ed2d">
													<SHORT-NAME>OsScheduleTableAppModeRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference in which application modes the schedule table should be started during startup</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsAppMode</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: OsScheduleTableExpiryPoint -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:c7cd0a08-1a1b-4d8f-aba8-a93a48a1a2a8">
											<SHORT-NAME>OsScheduleTableExpiryPoint</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The point on a Schedule Table at which the OS activates tasks and/or sets events</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: OsScheduleTblExpPointOffset -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:ae9487c7-72d2-455b-84e3-0789069652ed">
													<SHORT-NAME>OsScheduleTblExpPointOffset</SHORT-NAME>
													<DESC>
														<L-2 L="EN">The offset from zero (in ticks) at which the expiry point is to be processed.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>18446744073709551615</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<SUB-CONTAINERS>
												<!-- Container Definition: OsScheduleTableEventSetting -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:da6a454f-c51f-499f-bf85-327d654b710d">
													<SHORT-NAME>OsScheduleTableEventSetting</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Event that is triggered by that schedule table.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<REFERENCES>
														<!-- Reference Definition: OsScheduleTableSetEventRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:5b6d6723-ab99-4855-b3a1-7307dcfb568c">
															<SHORT-NAME>OsScheduleTableSetEventRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to event that will be set by action</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsEvent</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
														<!-- Reference Definition: OsScheduleTableSetEventTaskRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:9c00d427-05c7-4bf2-960f-818e9c527827">
															<SHORT-NAME>OsScheduleTableSetEventTaskRef</SHORT-NAME>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsTask</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: OsScheduleTableTaskActivation -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:f65324cd-90ca-4bd5-9ba2-7abc0b9a6489">
													<SHORT-NAME>OsScheduleTableTaskActivation</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Task that is triggered by that schedule table.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<REFERENCES>
														<!-- Reference Definition: OsScheduleTableActivateTaskRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:a39530bb-23f0-4ff3-96c7-07c979bcfa55">
															<SHORT-NAME>OsScheduleTableActivateTaskRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to task that will be activated by action</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsTask</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: OsScheduleTblAdjustableExpPoint -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:4517e21c-7943-450e-8038-ac3dc14794f6">
													<SHORT-NAME>OsScheduleTblAdjustableExpPoint</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Adjustable expiry point</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: OsScheduleTableMaxLengthen -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:6dfec9f1-1edd-4155-9aee-bcc5a7eff09d">
															<SHORT-NAME>OsScheduleTableMaxLengthen</SHORT-NAME>
															<DESC>
																<L-2 L="EN">The maximum positive adjustment that can be made to the expiry point offset (in ticks).</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>18446744073709551615</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: OsScheduleTableMaxShorten -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:24814103-54dd-4a15-86ac-e315b1791fd0">
															<SHORT-NAME>OsScheduleTableMaxShorten</SHORT-NAME>
															<DESC>
																<L-2 L="EN">The maximum negative adjustment that can be made to the expiry point offset (in ticks).</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>18446744073709551615</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: OsScheduleTableSync -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:6242bbd5-43d6-45a6-9128-fad562be2af9">
											<SHORT-NAME>OsScheduleTableSync</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container specifies the synchronization parameters of the schedule table.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: OsScheduleTblExplicitPrecision -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:123e7dad-40fd-4311-9cd7-bb56c0a5c4de">
													<SHORT-NAME>OsScheduleTblExplicitPrecision</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This configuration is only valid if the explicit synchronization is used.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>18446744073709551615</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: OsScheduleTblSyncStrategy -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:b2420449-8111-4d58-8338-b9068501a1a6">
													<SHORT-NAME>OsScheduleTblSyncStrategy</SHORT-NAME>
													<DESC>
														<L-2 L="EN">AUTOSAR OS provides support for synchronization in two ways: explicit and implicit.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>NONE</DEFAULT-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:27383f2c-9521-8b59-32c3-3dc3a4a89a97">
															<SHORT-NAME>EXPLICIT</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:ab61576a-ae97-945c-2dfa-c1820014c8fa">
															<SHORT-NAME>IMPLICIT</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:b7a6be99-cb10-878c-2453-88a1bf5922e1">
															<SHORT-NAME>NONE</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: OsSpinlock -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:d693c889-237d-481d-836b-62c73ad00d48">
									<SHORT-NAME>OsSpinlock</SHORT-NAME>
									<DESC>
										<L-2 L="EN">An OsSpinlock object is used to co-ordinate concurrent access by TASKs/ISR2s on different cores to a shared resource.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: OsSpinlockLockMethod -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:a6a46311-b3c8-4178-acab-c75fa0d24275">
											<SHORT-NAME>OsSpinlockLockMethod</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Lock method which is used when a spinlock is taken. Note that it is possible that a user (e.g. a Task) might hold more than one spinlock. In this case the last lock taken is forced to use at least a lock methode which locks as strong as the current one.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>LOCK_NOTHING</DEFAULT-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:31acd999-e8a4-8dc9-38f8-818cb1ea655a">
													<SHORT-NAME>LOCK_ALL_INTERRUPTS</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:08fd53b9-c8b5-8af6-2ea1-76a33ab1a912">
													<SHORT-NAME>LOCK_CAT2_INTERRUPTS</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:ffe5b13f-d390-94ef-0a6e-bf3d7800f1b3">
													<SHORT-NAME>LOCK_NOTHING</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:976e7014-dde2-949b-28c9-041510e79421">
													<SHORT-NAME>LOCK_WITH_RES_SCHEDULER</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Reference Definition: OsSpinlockAccessingApplication -->
										<ECUC-REFERENCE-DEF UUID="ECUC:804a40be-efb2-4923-b684-7466bf088f7e">
											<SHORT-NAME>OsSpinlockAccessingApplication</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to OsApplications that have an access to this object.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsApplication</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<!-- Reference Definition: OsSpinlockSuccessor -->
										<ECUC-REFERENCE-DEF UUID="ECUC:de2b3a13-fb51-4c88-980d-89bd9a102d2a">
											<SHORT-NAME>OsSpinlockSuccessor</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to OsApplications that have an access to this object.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">To check whether a spinlock can be occupied (in a nested way) without any danger of deadlock, a linked list of spinlocks can be defined. A spinlock can only be occupied in the order of the linked list. It is allowed to skip a spinlock.

                                        If no linked list is specified, spinlocks cannot be nested.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsSpinlock</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: OsTask -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:f9cd89d3-97e4-4f05-ad16-d8f577b726d0">
									<SHORT-NAME>OsTask</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container represents an OSEK task.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: OsTaskActivation -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:f53345d4-aac0-420f-ba25-d5b65b0dc33d">
											<SHORT-NAME>OsTaskActivation</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This attribute defines the maximum number of queued activation requests for the task. A value equal to &quot;1&quot; means that at any time only a single activation is permitted for this task. Note that the value must be a natural number starting at 1.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: OsTaskPriority -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:61428e85-21e3-4df8-980f-9a985a02ede4">
											<SHORT-NAME>OsTaskPriority</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The priority of a task is defined by the value of this attribute. This value has to be understood as a relative value, i.e. the values show only the relative ordering of the tasks.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">OSEK OS defines the lowest priority as zero (0); larger values correspond to higher priorities.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: OsTaskSchedule -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:f9b59379-7036-4b07-ab5c-2adfabf501c9">
											<SHORT-NAME>OsTaskSchedule</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The OsTaskSchedule attribute defines the preemptability of the task.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">If this attribute is set to NON, no internal resources may be assigned to this task.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:8b73a537-2bfc-90f9-634b-bd89f14f5d7f">
													<SHORT-NAME>FULL</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:637b4dd5-ccdf-94c1-36a4-3733dddf49a3">
													<SHORT-NAME>NON</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Reference Definition: OsTaskAccessingApplication -->
										<ECUC-REFERENCE-DEF UUID="ECUC:171cf36f-49fc-411f-a6a6-1d7b2ad3980c">
											<SHORT-NAME>OsTaskAccessingApplication</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to applications which have an access to this object.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsApplication</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<!-- Reference Definition: OsTaskEventRef -->
										<ECUC-REFERENCE-DEF UUID="ECUC:7044fce9-f730-49ac-a996-dc177040d49e">
											<SHORT-NAME>OsTaskEventRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This reference defines the list of events the extended task may react on.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsEvent</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<!-- Reference Definition: OsTaskResourceRef -->
										<ECUC-REFERENCE-DEF UUID="ECUC:62e61405-1641-428b-8bb0-16f229d5e20e">
											<SHORT-NAME>OsTaskResourceRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This reference defines a list of resources accessed by this task.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsResource</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<!-- Container Definition: OsTaskAutostart -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e9cb3483-f89a-4318-bbc9-ad010cc385b3">
											<SHORT-NAME>OsTaskAutostart</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container determines whether the task is activated during the system start-up procedure or not for some specific application modes.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">If the task shall be activated during the system start-up, this container is present and holds the references to the application modes in which the task is auto-started.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<REFERENCES>
												<!-- Reference Definition: OsTaskAppModeRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:e7ace4a7-9613-4f24-908c-30ccc5bf56e6">
													<SHORT-NAME>OsTaskAppModeRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to application modes in which that task is activated on startup of the OS</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsAppMode</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: OsTaskTimingProtection -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:84e81766-4a5b-4236-9108-ef70def0f89f">
											<SHORT-NAME>OsTaskTimingProtection</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains all parameters regarding timing protection of the task.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: OsTaskAllInterruptLockBudget -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:8081828e-63a1-4557-aa36-499a08c8c7f5">
													<SHORT-NAME>OsTaskAllInterruptLockBudget</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter contains the maximum time for which the task is allowed to lock all interrupts (via SuspendAllInterrupts() or DisableAllInterrupts()) (in seconds).</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>Inf</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: OsTaskExecutionBudget -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:dfe8bdba-81ab-4678-9590-d572e5f51e63">
													<SHORT-NAME>OsTaskExecutionBudget</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter contains the maximum allowed execution time of the task (in seconds).</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>Inf</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: OsTaskOsInterruptLockBudget -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:76ffc748-10a8-4e58-8a72-eabb96ee8ec2">
													<SHORT-NAME>OsTaskOsInterruptLockBudget</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter contains the maximum time for which the task is allowed to lock all Category 2 interrupts (via SuspendOSInterrupts()) (in seconds).</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>Inf</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: OsTaskTimeFrame -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:faa19133-57b3-47fe-9ad3-3e028e0c5884">
													<SHORT-NAME>OsTaskTimeFrame</SHORT-NAME>
													<DESC>
														<L-2 L="EN">The minimum inter-arrival time between activations and/or releases of a task (in seconds).</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>Inf</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
											</PARAMETERS>
											<SUB-CONTAINERS>
												<!-- Container Definition: OsTaskResourceLock -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:4c924269-ca89-4c10-9481-cdccacc1a866">
													<SHORT-NAME>OsTaskResourceLock</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container contains the worst case time between getting and releasing a given resource (in seconds).</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: OsTaskResourceLockBudget -->
														<ECUC-FLOAT-PARAM-DEF UUID="ECUC:d22b3f26-8cf9-4334-a8e8-cd3657d64f9d">
															<SHORT-NAME>OsTaskResourceLockBudget</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter contains the maximum time the task is allowed to lock the resource (in seconds)</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>Inf</MAX>
															<MIN>0</MIN>
														</ECUC-FLOAT-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: OsTaskResourceLockResourceRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:ac350ae8-5ba7-4294-930a-d2e9c7cd6bc8">
															<SHORT-NAME>OsTaskResourceLockResourceRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the resource used by the task</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsResource</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
