<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: DoIP-->
						<ECUC-MODULE-DEF UUID="01a84faa-a44a-4934-b166-0c0854fb77aa">
							<SHORT-NAME>DoIP</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the DoIP (Diagnostic over IP) module.</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.3.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
										<DATE>2012-08-01</DATE>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-LINK-TIME</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-POST-BUILD</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: DoIPConfigSet -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="8d69e9a6-7dde-43cc-b8a5-6a893adec2c1">
									<SHORT-NAME>DoIPConfigSet</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container contains the configuration parameters and sub containers of the AUTOSAR DoIP module. This container is a MultipleConfigurationContainer, i.e. this container and its sub-containers exist once per configuration set.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>true</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: DoIPEid -->
										<ECUC-INTEGER-PARAM-DEF UUID="c95693a7-1fa8-4ec3-82d8-278e9a7c4b78">
											<SHORT-NAME>DoIPEid</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configured EID (Entity ID of) for vehicle identification/vehicle announcement. Only necessary if DoIPUseMacAddressForIdentification is set to FALSE.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>281474976710656</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPGid -->
										<ECUC-INTEGER-PARAM-DEF UUID="1a8fb635-bf3f-4bcc-9d25-44369902fd98">
											<SHORT-NAME>DoIPGid</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configured GID (Group ID of) for vehicle identification/vehicle announcement.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>281474976710656</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPLogicalAddress -->
										<ECUC-INTEGER-PARAM-DEF UUID="1084b0a2-dd48-4589-af11-a38fe122145a">
											<SHORT-NAME>DoIPLogicalAddress</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Describes the logical address of the DoIP entity, i.e. the SA that will route diagnostic requests to the Dcm of the DoIP entity.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: DoIPChannel -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="eef69e41-b1a0-4b5e-8468-47d0f59a4b79">
											<SHORT-NAME>DoIPChannel</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configuration of one DoIPChannel.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>65536</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<REFERENCES>
												<!-- Reference Definition: DoIPChannelSARef -->
												<ECUC-REFERENCE-DEF UUID="86af5955-9e74-4620-b55f-38d4d9ca71ae">
													<SHORT-NAME>DoIPChannelSARef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the DoIPTester.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/DoIP/DoIPConfigSet/DoIPTester</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<!-- Reference Definition: DoIPChannelTARef -->
												<ECUC-REFERENCE-DEF UUID="a8d6656f-f0b3-49c5-ab9f-159c3bf4c02a">
													<SHORT-NAME>DoIPChannelTARef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the target address.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/DoIP/DoIPConfigSet/DoIPConnections/DoIPTargetAddress</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
											<SUB-CONTAINERS>
												<!-- Container Definition: DoIPPduRRxPdu -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="8e8e65a8-1939-406e-aa0b-6dfb219b42ca">
													<SHORT-NAME>DoIPPduRRxPdu</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container contains the Rx Pdus to connect with the Rx Pdus of the PduR.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: DoIPPduRRxPduId -->
														<ECUC-INTEGER-PARAM-DEF UUID="d1ed6fd3-3843-46d7-ab9f-fd58ab8b9562">
															<SHORT-NAME>DoIPPduRRxPduId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">The DoIPPduRRxPduId is required by the API call DoIP_CancelReceive.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: DoIPPduRRxPduRef -->
														<ECUC-REFERENCE-DEF UUID="b7bf4060-ce84-436f-a701-33b9768b0a87">
															<SHORT-NAME>DoIPPduRRxPduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the "global" Pdu structure to allow harmonization of handle IDs in the COM-Stack.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: DoIPPduRTxPdu -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="6f9c6349-bdd5-4bb9-8280-b3a7d3cf9f6b">
													<SHORT-NAME>DoIPPduRTxPdu</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container contains the Tx Pdus to connect with the Tx Pdus of the PduR.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: DoIPPduRTxPduId -->
														<ECUC-INTEGER-PARAM-DEF UUID="4eee96d2-a3a6-43de-8ca6-************">
															<SHORT-NAME>DoIPPduRTxPduId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">The DoIPPduRTxPduId is required by DoIP_Transmit and DoIP_CancelTransmit.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: DoIPPduRTxPduRef -->
														<ECUC-REFERENCE-DEF UUID="6119b2e5-74a7-46f1-a07a-33d48cd9776b">
															<SHORT-NAME>DoIPPduRTxPduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the "global" Pdu structure to allow harmonization of handle IDs in the COM-Stack.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DoIPConnections -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="a8e99df5-fae8-4860-84b7-5484bc3aa5ea">
											<SHORT-NAME>DoIPConnections</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container contains all lower layer connection specific information, i.e. the single Pdu References and Handle IDs to the SoAd.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<SUB-CONTAINERS>
												<!-- Container Definition: DoIPTargetAddress -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="738662ff-bcd1-4bf9-b33c-a99388e763c4">
													<SHORT-NAME>DoIPTargetAddress</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container describes a possible TargetAddress that is supported by DoIP.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: DoIPTargetAddressValue -->
														<ECUC-INTEGER-PARAM-DEF UUID="d166da8b-269c-43a2-9cc1-28897951668b">
															<SHORT-NAME>DoIPTargetAddressValue</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Valid Target Address of a DoIP target address.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: DoIPTcpConnection -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="1d86f9fa-e08b-434e-8c6a-173985feb32a">
													<SHORT-NAME>DoIPTcpConnection</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Each container describes a tcp connection to the lower layer SoAd module.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<SUB-CONTAINERS>
														<!-- Container Definition: DoIPSoAdRxPdu -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="b780b617-6160-47fb-922d-2720b32889b0">
															<SHORT-NAME>DoIPSoAdRxPdu</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This container contains the Rx Pdus received by DoIP</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: DoIPSoAdRxPduId -->
																<ECUC-INTEGER-PARAM-DEF UUID="dfbc0748-ee25-4b04-98ca-27c4b9c26a48">
																	<SHORT-NAME>DoIPSoAdRxPduId</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">The DoIPSoAdRxPduId is required by the API call DoIP_SoAdTpRxIndication to receive I-PDUs from the SoAd.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
																	<MAX>65535</MAX>
																	<MIN>1</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
															<REFERENCES>
																<!-- Reference Definition: DoIPSoAdRxPduRef -->
																<ECUC-REFERENCE-DEF UUID="0693db04-cd76-4e47-ab9f-49b3d338cefc">
																	<SHORT-NAME>DoIPSoAdRxPduRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to the "global" Pdu structure to allow harmonization of handle IDs in the COM-Stack.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
															</REFERENCES>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: DoIPSoAdTxPdu -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="f764f413-32d6-415e-bf20-06b1f33ac811">
															<SHORT-NAME>DoIPSoAdTxPdu</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This container describes the TxPdu sent via the SoAd</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: DoIPSoAdTxPduId -->
																<ECUC-INTEGER-PARAM-DEF UUID="0bb68c55-8e46-4757-a573-2f08d3e45943">
																	<SHORT-NAME>DoIPSoAdTxPduId</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">The DoIPSoAdTxPduId is required by the API call DoIP_SoAdTpTxConfirmation that is called by the SoAd to confirm that the IPdu has been transmitted successfully.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
																	<MAX>65535</MAX>
																	<MIN>1</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
															<REFERENCES>
																<!-- Reference Definition: DoIPSoAdTxPduRef -->
																<ECUC-REFERENCE-DEF UUID="3c473230-72c7-48c8-b783-565ea21ada9b">
																	<SHORT-NAME>DoIPSoAdTxPduRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to the "global" Pdu structure to allow harmonization of handle IDs in the COM-Stack.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
															</REFERENCES>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: DoIPUdpConnection -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="d40873c8-93f6-4a30-8187-2f5b9175faac">
													<SHORT-NAME>DoIPUdpConnection</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This Container describes the udp connection to the lower layer SoAd module.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<SUB-CONTAINERS>
														<!-- Container Definition: DoIPSoAdRxPdu -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="2000490b-fc46-4b42-9762-e9bd84105208">
															<SHORT-NAME>DoIPSoAdRxPdu</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This container contains the Rx Pdus received by DoIP</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: DoIPSoAdRxPduId -->
																<ECUC-INTEGER-PARAM-DEF UUID="d6d9639f-165e-4837-95e1-18a976bc52f6">
																	<SHORT-NAME>DoIPSoAdRxPduId</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">The DoIPSoAdRxPduId is required by the API call DoIP_SoAdTpRxIndication to receive I-PDUs from the SoAd.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
																	<MAX>65535</MAX>
																	<MIN>1</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
															<REFERENCES>
																<!-- Reference Definition: DoIPSoAdRxPduRef -->
																<ECUC-REFERENCE-DEF UUID="77c3f3d4-c8ba-4ab6-b72e-32d5d68bbff1">
																	<SHORT-NAME>DoIPSoAdRxPduRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to the "global" Pdu structure to allow harmonization of handle IDs in the COM-Stack.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
															</REFERENCES>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: DoIPSoAdTxPdu -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="60b4dec8-ce99-4a24-8484-4d6853b2fe60">
															<SHORT-NAME>DoIPSoAdTxPdu</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This container describes the TxPdu sent via the SoAd</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: DoIPSoAdTxPduId -->
																<ECUC-INTEGER-PARAM-DEF UUID="ae89ffc6-eb59-420b-bbe8-31d4006bc7ad">
																	<SHORT-NAME>DoIPSoAdTxPduId</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">The DoIPSoAdTxPduId is required by the API call DoIP_SoAdTpTxConfirmation that is called by the SoAd to confirm that the IPdu has been transmitted successfully.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
																	<MAX>65535</MAX>
																	<MIN>1</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
															<REFERENCES>
																<!-- Reference Definition: DoIPSoAdTxPduRef -->
																<ECUC-REFERENCE-DEF UUID="c42b04dc-ce57-4b69-8b1d-953f583173ea">
																	<SHORT-NAME>DoIPSoAdTxPduRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to the "global" Pdu structure to allow harmonization of handle IDs in the COM-Stack.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
															</REFERENCES>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DoIPRoutingActivation -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="a3f863df-8d3a-424e-b358-5af96d6d2a19">
											<SHORT-NAME>DoIPRoutingActivation</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container describes the routing activation possibilities by representing for each container a possible routing activation request message to the DoIP entity and the according references to the activated diagnostic messages.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DoIPRoutingActivationNumber -->
												<ECUC-INTEGER-PARAM-DEF UUID="3e3fed60-6259-403d-80ec-5990e4220de7">
													<SHORT-NAME>DoIPRoutingActivationNumber</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Identifies the Routing activation Number which is received for a DoIP routing activation request message.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: DoIPTargetAddressRef -->
												<ECUC-REFERENCE-DEF UUID="56f74807-4daf-4b15-86f7-3ed91dcb50ff">
													<SHORT-NAME>DoIPTargetAddressRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to all DoIPTargetAddress which are activated on this Routing activation.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/DoIP/DoIPConfigSet/DoIPConnections/DoIPTargetAddress</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
											<SUB-CONTAINERS>
												<!-- Container Definition: DoIPRoutingActivationAuthenticationCallback -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="71d44007-a9ff-405c-98cd-b3d2fe7e1990">
													<SHORT-NAME>DoIPRoutingActivationAuthenticationCallback</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container describes the Callbackfunction to call on a Routing Activation Request for Authentication. If this container is configured but the DoIPRoutingAcivationAuthenticationFunc parameter is not present, the DoIP module will use an RPort of ServiceInterface &lt;RoutingActivation&gt;_RoutingActivation with the name "CB&lt;RoutingActivation&gt;RoutingActivation". &lt;RoutingActivation&gt; is the ShortName of the DoIPRoutingActiviation container.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: DoIPRoutingAcivationAuthenticationFunc -->
														<ECUC-FUNCTION-NAME-DEF UUID="c9c52180-7e06-4986-b8b6-175f2889e5e3">
															<SHORT-NAME>DoIPRoutingAcivationAuthenticationFunc</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Direct C Callback function to trigger the authentication function for routing activation. If the DoIPRoutingAcivationAuthenticationFunc parameter is present, the DoIP module will not use an RPort of ServiceInterface &lt;RoutingActivation&gt;_RoutingActivation but call the configured function.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-FUNCTION-NAME-DEF>
														<!-- PARAMETER DEFINITION: DoIPRoutingActivationAuthenticationReqLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="10ee88e3-2369-4e42-88ce-47ea6b95cd45">
															<SHORT-NAME>DoIPRoutingActivationAuthenticationReqLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Describes the amount of bytes used to handle to the authentication function on routing activation. If 0 is configured as length the parameter AuthenticationReqData will not be handled to the API.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: DoIPRoutingActivationAuthenticationResLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="0619576e-3da9-45d0-a851-90c63d51cb77">
															<SHORT-NAME>DoIPRoutingActivationAuthenticationResLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Describes the amount of bytes used to read by the authentication function on routing activation. If 0 is configured as length the parameter AuthenticationResData will not be fetched via the API.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: DoIPRoutingActivationConfirmationCallback -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="6f8e5715-be9d-448e-a87b-c0eef99ce37b">
													<SHORT-NAME>DoIPRoutingActivationConfirmationCallback</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container describes the Callbackfunction to call on a Routing Activation Request for Confirmation. If this container is configured but the DoIPRoutingAcivationConfirmationFunc parameter is not present the DoIP module will use an RPort of ServiceInterface &lt;RoutingActivation&gt;_RoutingActivation with the name "CB&lt;RoutingActivation&gt;RoutingActivation". &lt;RoutingActivation&gt; is the ShortName of the DoIPRoutingActiviation container.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: DoIPRoutingAcivationConfirmationFunc -->
														<ECUC-FUNCTION-NAME-DEF UUID="db39946a-f4e7-4b4b-a717-c99576579a3e">
															<SHORT-NAME>DoIPRoutingAcivationConfirmationFunc</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Direct C Callback function to trigger the confirmation function for routing activation. If the DoIPRoutingAcivationConfirmationFunc parameter is present the DoIP module will not use an RPort of ServiceInterface &lt;RoutingActivation&gt;_RoutingActivation but call the configured function.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-FUNCTION-NAME-DEF>
														<!-- PARAMETER DEFINITION: DoIPRoutingActivationConfirmationReqLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="6515852f-3fd6-44ba-b796-81511c0bb0c9">
															<SHORT-NAME>DoIPRoutingActivationConfirmationReqLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Describes the amount of bytes used to handle to the confirmation function on routing activation. If 0 is configured as length the parameter ConfirmedReqData will not be handled to the API.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: DoIPRoutingActivationConfirmationResLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="21942a0f-16d4-45ce-9b18-00bc8d9bbca2">
															<SHORT-NAME>DoIPRoutingActivationConfirmationResLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Describes the amount of bytes used to read by the confirmation function on routing activation. If 0 is configured as length the parameter ConfirmedResData will not be fetched via the API.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DoIPTester -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="aa1be77a-5cef-40ea-9126-0cd3e7588f5f">
											<SHORT-NAME>DoIPTester</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container describes the properties of the possible connectable Tester for the DoIP entity.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DoIPNumByteDiagAckNack -->
												<ECUC-INTEGER-PARAM-DEF UUID="597517c8-bad9-4b3f-ae96-1a3ffead784c">
													<SHORT-NAME>DoIPNumByteDiagAckNack</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Specifies the number of original Diagnostic request bytes the DoIP entity responses on a NACK of a diagnostic response message to the Tester.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>18446744073709551611</MAX>
													<MIN>1</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: DoIPTesterSA -->
												<ECUC-INTEGER-PARAM-DEF UUID="8c8e432f-61d4-4108-af04-86b61fd78c9d">
													<SHORT-NAME>DoIPTesterSA</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Source Address of the Tester sent via routing activation or diagnostic message.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>65535</MAX>
													<MIN>1</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: DoIPRoutingActivationRef -->
												<ECUC-REFERENCE-DEF UUID="3662eb2e-6e34-469a-83b8-a6e77357296d">
													<SHORT-NAME>DoIPRoutingActivationRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to a DoIPRoutingActivation describing the possible routing activations of the DoIPTester</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/DoIP/DoIPConfigSet/DoIPRoutingActivation</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: DoIPGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="a200892b-4c11-4c77-b67f-2952d1f89fa8">
									<SHORT-NAME>DoIPGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container specifies the general configuration parameters of the DoIP module.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: DoIPAliveCheckResponseTimeout -->
										<ECUC-FLOAT-PARAM-DEF UUID="793e123f-c484-4b36-bbc1-5bf785f30557">
											<SHORT-NAME>DoIPAliveCheckResponseTimeout</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Timeout  in [s] for waiting for a response to an Alive Check request before the connection is considered to be disconnected. Represents parameter T_TCP_AliveCheck of ISO 13400-2:2012.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>Inf</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPDevelopmentErrorDetect -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="c28940cb-abef-45c2-b81b-4acc74805f22">
											<SHORT-NAME>DoIPDevelopmentErrorDetect</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Pre-processor switch for enabling development error detection support.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPDhcpOptionVinUse -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="34eca337-52b5-4f79-b980-6dfd71924ce3">
											<SHORT-NAME>DoIPDhcpOptionVinUse</SHORT-NAME>
											<DESC>
												<L-2 L="EN">If DoIPDhcpOptionVinUse is set to true the DoIP module will add the VIN to the Dhcp host name if no valid Dhcp host name is already set.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPEntityStatusMaxByteFieldUse -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="a3eae043-90c1-4201-99b0-544baa6daa37">
											<SHORT-NAME>DoIPEntityStatusMaxByteFieldUse</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter is used to distinguish the optional support of the Max data size element of a diagnostic entity status response.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPGIDInvalidityPattern -->
										<ECUC-INTEGER-PARAM-DEF UUID="bdf5e8e8-4413-464e-9cb0-8de8ccb334ca">
											<SHORT-NAME>DoIPGIDInvalidityPattern</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the Byte pattern that is used for response messages if no valid GID could be retrieved.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>255</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPGeneralInactivityTime -->
										<ECUC-FLOAT-PARAM-DEF UUID="4f72ae2d-e209-4d55-80e4-1b1a184fdf7f">
											<SHORT-NAME>DoIPGeneralInactivityTime</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Timeout in [s] for maximum inactivity of a TCP socket connection before the DoIP module will close the according socket connection. Represents parameter T_TCP_General_Inactivity of ISO 13400-2:2012</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>Inf</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPHeaderFileInclusion -->
										<ECUC-STRING-PARAM-DEF UUID="be23b072-c4b1-42f1-9a54-8724b30ad635">
											<SHORT-NAME>DoIPHeaderFileInclusion</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Name of the header file(s) to be included by the DoIP module containing the used C-callback declarations.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-STRING-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPInitialInactivityTime -->
										<ECUC-FLOAT-PARAM-DEF UUID="6cee6e0a-89a7-432d-a52d-02778cb163b1">
											<SHORT-NAME>DoIPInitialInactivityTime</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Timeout in [s] used for initial inactivity of a connected TCP socket connection directly after socket connection. Represents parameter T_TCP_Initial_Inactivity of ISO 13400-2:2012</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>Inf</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPInitialVehicleAnnouncementTime -->
										<ECUC-FLOAT-PARAM-DEF UUID="a7f39a11-cdac-4b86-bb77-a4ff32f15a50">
											<SHORT-NAME>DoIPInitialVehicleAnnouncementTime</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Time to wait in [s] for sending first vehicle anouncement message after IP address assignment. Represents parameter A_DoIP_Announce_Wait of ISO 13400-2:2012</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>Inf</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPMainFunctionPeriod -->
										<ECUC-FLOAT-PARAM-DEF UUID="45805d43-26d8-499f-a62b-aac4ff4fa6ef">
											<SHORT-NAME>DoIPMainFunctionPeriod</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Determines the frequency at which the DoIP_MainFunction() is called in [s].</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>Inf</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPMaxRequestBytes -->
										<ECUC-INTEGER-PARAM-DEF UUID="1dc915a9-732a-43a8-aef7-863930443e13">
											<SHORT-NAME>DoIPMaxRequestBytes</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the maximum allowed bytes of a DoIP message request without the DoIP header.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>18446744073709551615</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPMaxTesterConnections -->
										<ECUC-INTEGER-PARAM-DEF UUID="07f5b919-fdf8-42f7-adf5-9bb3adaf5f10">
											<SHORT-NAME>DoIPMaxTesterConnections</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Maximum ammount of tester connections that shall be maintained at one time before alive check is performed.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>255</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPNodeType -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="61c81de9-2eba-4604-b9d9-d1ac5b7f839b">
											<SHORT-NAME>DoIPNodeType</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Describes the Type of the DoIP node.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="3531162d-6374-4ca0-99a3-66b74beb0bc5">
													<SHORT-NAME>DOIP_GATEWAY</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="8a8e93b9-6eca-491f-9e04-2e9a39f34143">
													<SHORT-NAME>DOIP_NODE</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPUseEIDasGID -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="bad7e06b-1634-436d-8300-99dff78ddc70">
											<SHORT-NAME>DoIPUseEIDasGID</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies if the DoIP entity shall use its EID if it is the Master for vehicle identification gid on the vehicle identification/vehicle announcement.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPUseMacAddressForIdentification -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="e4ac2e8b-253b-4854-a364-afd28ec5978d">
											<SHORT-NAME>DoIPUseMacAddressForIdentification</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Provided the information if a configured EID at vehicle identification response/vehicle announment is used or the MAC address.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">TRUE: Use MAC Address instead of EID for Vehicle identification/announcement. 
                                                    FALSE: Use configured EID for vehicle identification/announcement. Dependencies: DoIPEID</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPUseVehicleIdentificationSyncStatus -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="5ea10f94-5872-4c20-8f2f-c7075d2f2a44">
											<SHORT-NAME>DoIPUseVehicleIdentificationSyncStatus</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Defines if the optional VIN/GID synchronization status is used additionally in the vehicle identification/announcement.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPVehicleAnnouncementInterval -->
										<ECUC-FLOAT-PARAM-DEF UUID="7f8a0d21-9ee1-4c44-ad90-69c57b6bca35">
											<SHORT-NAME>DoIPVehicleAnnouncementInterval</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Time to wait in [s] for sending subsequent vehicle anouncement messages. Represents parameter A_DoIP_Announce_Interval of ISO 13400-2:2012</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>Inf</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPVehicleAnnouncementRepetition -->
										<ECUC-INTEGER-PARAM-DEF UUID="67c2379d-043a-47c4-85ef-fed9b25185e6">
											<SHORT-NAME>DoIPVehicleAnnouncementRepetition</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Amount of repetitions of the vehicle announcement message on IP address assignment. Represents parameter A_DoIP_Annunce_Num of ISO 13400-2:2012</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>255</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPVersionInfoApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="0df20c8e-36cd-42b1-ba65-bb9bdba72b35">
											<SHORT-NAME>DoIPVersionInfoApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Activates the DoIP_GetVersionInfo() API.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">TRUE: Enables the DoIP_GetVersionInfo() API. 
                                                    FALSE: DoIP_GetVersionInfo() API is not included.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPVinGidMaster -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="20d5dfb6-e29c-4cc9-a9ff-68bb282ac904">
											<SHORT-NAME>DoIPVinGidMaster</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies if the DoIP entity is the Vehicle identification Master for the GID (Group ID).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DoIPVinInvalidityPattern -->
										<ECUC-INTEGER-PARAM-DEF UUID="03d1fd23-6844-4ce8-a957-a235ad2196c0">
											<SHORT-NAME>DoIPVinInvalidityPattern</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the Byte pattern that is used for response messages if no valid VIN could be retrieved.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>255</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: DoIPGetGid -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="404eacad-16bf-4d40-8cbe-c29de0edbdd5">
											<SHORT-NAME>DoIPGetGid</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container describes the Callbackfunction to get the GID.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">If this container is not configured no Callbackfunction will be used.
            
                                                    If this container is configured but the DoIPGetGID parameter is not present, the DoIP module will use an RPort of ServiceInterface CallbackGetGID with the name "CBGetGID".</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DoIPGetGID -->
												<ECUC-FUNCTION-NAME-DEF UUID="345849a7-a78d-4180-bf69-3515f9cc891a">
													<SHORT-NAME>DoIPGetGID</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Direct C Callback function to get the GID of the DoIP entity. If the DoIPGetGID parameter is present, the DoIP module will not use an RPort of ServiceInterface CallbackGetGID but call the configured function.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DoIPPowerModeCallback -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="6e539553-d1c8-43ab-b1a3-203d615f12f9">
											<SHORT-NAME>DoIPPowerModeCallback</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container describes the Callbackfunction to get the Power Mode. This container shall always be present.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">If the DoIPPowerMode parameter is not present, the DoIP module will use an RPort of ServiceInterface CallbackGetPowerMode with the name "CBGetPowerMode".</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DoIPPowerMode -->
												<ECUC-FUNCTION-NAME-DEF UUID="94c460ea-9181-4043-8dab-35fc02b0c798">
													<SHORT-NAME>DoIPPowerMode</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Direct C Callback function to get the Power Mode of the DoIP entity. If the DoIPPowerMode parameter is present, the DoIP module will not use an RPort of ServiceInterface CallbackGetPowerMode but will call the configured function.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: DoIPTriggerGidSynchronization -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="6b8b0f97-aa8a-42a5-987f-b025ebb6c347">
											<SHORT-NAME>DoIPTriggerGidSynchronization</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container describes the Callbackfunction to trigger the GID synchronisation.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">If this container is not configured no Callbackfunction will be used.
            
                                                    If this container is configured but the DoIPTriggerGIDSynchronzation parameter is not present, the DoIP module will use an RPort of ServiceInterface CallbackTriggerGIDSynchronization with the name "CBTriggerGIDSynchronization".</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DoIPTriggerGIDSynchronzation -->
												<ECUC-FUNCTION-NAME-DEF UUID="bec04140-0464-4b01-8f65-e8c59aa1b004">
													<SHORT-NAME>DoIPTriggerGIDSynchronzation</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Direct C Callback function to trigger the synchronization of the GID. If the DoIPTriggerGIDSynchronzation parameter is present, the DoIP module will not use an RPort of ServiceInterface CallbackTriggerGIDSynchronization but call the configured function.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
