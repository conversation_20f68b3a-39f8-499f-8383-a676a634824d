<?xml version="1.0" encoding="UTF-8"?>
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://autosar.org/schema/r4.0">
	<AR-PACKAGES>
		<AR-PACKAGE UUID="3eee0e85-7e59-4556-993a-89107c5203c4">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<AR-PACKAGE UUID="7c03fef5-12e6-4144-8882-c14dce2584b8">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<ECUC-MODULE-DEF UUID="77cfbfad-6641-418a-a8da-801d0684cb2c">
							<SHORT-NAME>Csm</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the Csm (CryptoServiceManager) module.</L-2>
							</DESC>
							<CATEGORY>STANDARDIZED_MODULE_DEFINITION</CATEGORY>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.3.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: CsmCallbacks -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="342b4bc5-2620-4ce3-a9f4-c1c6d9dc84d2">
									<SHORT-NAME>CsmCallbacks</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for callback function configurations</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmCallback -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="d7c28795-a361-428d-8a25-db342dbb8d88">
											<SHORT-NAME>CsmCallback</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of a callback function</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmCallbackFunc -->
												<ECUC-FUNCTION-NAME-DEF UUID="e831c7bb-062a-4944-906b-d14f1cb6581c">
													<SHORT-NAME>CsmCallbackFunc</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if an asynchronous operation has finished. The corresponding job has to be configured to be processed asynchronously.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: CsmCallbackId -->
												<ECUC-INTEGER-PARAM-DEF UUID="10bef6b6-959b-42c6-8374-fe34651b9733">
													<SHORT-NAME>CsmCallbackId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Identifier of the callback function.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>4294967295</MAX>
													<MIN>1</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="6c473c7b-024e-411c-af04-5e9db9e0cfee">
									<SHORT-NAME>CsmGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for common configuration options.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmAsymPrivateKeyMaxLength -->
										<ECUC-INTEGER-PARAM-DEF UUID="89daa413-cf8a-4ce1-8ea9-f92822e77f9a">
											<SHORT-NAME>CsmAsymPrivateKeyMaxLength</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Maximum length in bytes of an asymmetric public key for all algorithm</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CsmAsymPublicKeyMaxLength -->
										<ECUC-INTEGER-PARAM-DEF UUID="346a2ed2-7409-4777-b09f-3078f147bba2">
											<SHORT-NAME>CsmAsymPublicKeyMaxLength</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Maximum length in bytes of an asymmetric key for all algorithm</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CsmDevErrorDetect -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="4a03c406-fd48-4e99-b9ae-9229840b0d9c">
											<SHORT-NAME>CsmDevErrorDetect</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Switches the development error detection and notification on or off.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">* true: detection and notification is enabled.
                                        * false: detection and notification is disabled.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CsmMainFunctionPeriod -->
										<ECUC-FLOAT-PARAM-DEF UUID="5d5e67e3-efcc-4923-9ab4-af1a3ea2cf0a">
											<SHORT-NAME>CsmMainFunctionPeriod</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the period of main function Csm_MainFunction in seconds.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>Inf</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CsmSymKeyMaxLength -->
										<ECUC-INTEGER-PARAM-DEF UUID="462ef3d1-41a4-4271-b007-d8bb83a8ead5">
											<SHORT-NAME>CsmSymKeyMaxLength</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Maximum length in bytes of a symmetric key for all algorithm</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CsmUseDeprecated -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="56ec4226-d07d-45c5-afd2-445555d041cc">
											<SHORT-NAME>CsmUseDeprecated</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Decides if the deprecated interfaces shall be used (Backwards combatibility).</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: use deprecated interfaces.
                                        false: use normal interfaces.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CsmVersionInfoApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="3c64e199-54f0-4303-b29e-5da2d7247994">
											<SHORT-NAME>CsmVersionInfoApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Pre-processor switch to enable and disable availability of the API Csm_GetVersionInfo().</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">True: API Csm_GetVersionInfo() is available. 
                                        False: API Csm_GetVersionInfo() is not available.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!--ASR 4.2 Parameters-->
										<!-- PARAMETER DEFINITION: CsmMaxAlignScalarType -->
										<ECUC-STRING-PARAM-DEF UUID="4585a508-9698-43e0-acfd-3624f4e1c906">
											<SHORT-NAME>CsmMaxAlignScalarType</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The scalar type which has the maximum alignment restrictions on the given platform.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">This type can be e.g. uint8, uint16 or uint32.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-STRING-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CsmMaximumBlockingTime -->
										<ECUC-INTEGER-PARAM-DEF UUID="b7a347c6-aa82-41f4-961d-852f498170aa">
											<SHORT-NAME>CsmMaximumBlockingTime</SHORT-NAME>
											<DESC>
												<L-2 L="EN">If interruption is turned on with the configuration option CsmUseInterruption, this option configures the maximum time in microseconds the main function shall be allowed to run before it must interrupt itself. The lowest allowed value for the option is implementation dependent.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CsmUseInterruption -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="d853cad8-e4e2-4977-b9f2-17184080d35a">
											<SHORT-NAME>CsmUseInterruption</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Pre-processor switch to enable and disable interruption of job processing.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">True: Interruption of job processing enabled
                                        False: Interruption of job processing disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CsmUseSyncJobProcessing -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="8e386d82-5b4c-4a81-b5bf-0ac1f89ab39d">
											<SHORT-NAME>CsmUseSyncJobProcessing</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Pre-processor switch to enable and disable synchronous job processing.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">True: synchronous job processing enabled
                                        False: synchronous job processing disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-STRING-PARAM-DEF UUID="4b17c60c-e3c3-41cd-aa5d-2db4872b3d9f">
											<SHORT-NAME>CsmCustomIncludeFiles</SHORT-NAME>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-STRING-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmJobs -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="da734be0-47e8-4c26-bdbf-26dc4957d4fd">
									<SHORT-NAME>CsmJobs</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for configuration of CSM jobs.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmJob -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="3096ab5e-0c60-4aed-8627-835991e31bbe">
											<SHORT-NAME>CsmJob</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of CSM job. The container name serves as a symbolic name for the identifier of a job configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmJobId -->
												<ECUC-INTEGER-PARAM-DEF UUID="77849ab6-7c4e-4dd5-a606-245df73f05c1">
													<SHORT-NAME>CsmJobId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Identifier of the CSM job</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmJobPrimitiveCallbackUpdateNotification -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="0f1dac97-99ce-460a-94da-07e45b85c769">
													<SHORT-NAME>CsmJobPrimitiveCallbackUpdateNotification</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter indicates, whether the callback function shall be called, if the UPDATE operation has been finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmJobPriority -->
												<ECUC-INTEGER-PARAM-DEF UUID="2a85fc0a-acc6-463f-9125-4455d2e16873">
													<SHORT-NAME>CsmJobPriority</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Priority of the job.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">The higher the value, the higher the job's priority.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmJobUsePort -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="752c6965-15ef-4251-aad3-6e24c857266d">
													<SHORT-NAME>CsmJobUsePort</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Does the job need RTE interfaces?</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">True: the job needs RTE interfaces
                                                False: the job needs no RTE interfaces</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: CsmJobKeyRef -->
												<ECUC-REFERENCE-DEF UUID="86cf7ebb-ed1c-478d-b649-66c660e94042">
													<SHORT-NAME>CsmJobKeyRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter refers to the key which shall be used for the CsmPrimitive. It's possible to use a CsmKey for different jobs</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Csm/CsmKeys/CsmKey</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<!-- Reference Definition: CsmJobPrimitiveCallbackRef -->
												<ECUC-REFERENCE-DEF UUID="b8d0db0d-b741-4cfa-9bda-323ea3a6630a">
													<SHORT-NAME>CsmJobPrimitiveCallbackRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter refers to the used CsmCallback.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">The referred CsmCallback is called when the crypto job has been finished.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Csm/CsmCallbacks/CsmCallback</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<!-- Reference Definition: CsmJobPrimitiveRef -->
												<ECUC-REFERENCE-DEF UUID="692153f7-27eb-4452-aa47-11526ca56b85">
													<SHORT-NAME>CsmJobPrimitiveRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter refers to the used CsmPrimitive.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">Different jobs may refer to one CsmPrimitive. The referred CsmPrimitive provides detailed information on the actual cryptographic routine.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Csm/CsmPrimitives</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<!-- Reference Definition: CsmJobQueueRef -->
												<ECUC-REFERENCE-DEF UUID="203a292a-7c69-45f1-9990-bf31d42745b6">
													<SHORT-NAME>CsmJobQueueRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter refers to the queue.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">The queue is used if the underlying crypto driver object is busy. The queue refers also to the channel which is used.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Csm/CsmQueues/CsmQueue</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmKeys -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="da4b1551-3b36-4629-8da8-64a5d8784cb7">
									<SHORT-NAME>CsmKeys</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for CSM key configurations.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmKey -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="73b38f1a-330b-477e-a86f-aa0cb6c45236">
											<SHORT-NAME>CsmKey</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of a CSM key. The container name serves as a symbolic name for the identifier of a key configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmKeyId -->
												<ECUC-INTEGER-PARAM-DEF UUID="4cb4439f-2105-4b1f-8e90-308a1e4f901b">
													<SHORT-NAME>CsmKeyId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Identifier of the CsmKey</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<MAX>4294967295</MAX>
													<MIN>1</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmKeyUsePort -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="874341ce-cbd0-45de-b220-4c163a2c90e6">
													<SHORT-NAME>CsmKeyUsePort</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Does the key need RTE interfaces?</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">True: RTE interfaces used for this key
                                                False: No RTE interfaces used for this key</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Symbolic Name Reference Definition: CsmKeyRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="87cd2ccc-c13b-4b38-b544-553c938e6ba0">
													<SHORT-NAME>CsmKeyRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter refers to the used CryIfKey. The underlying CryIfKey refers to a specific CryptoKey in the Crypto Driver.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/CryIf/CryIfKey</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmPrimitives -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="75ac8914-876c-47b6-bc22-5e44aeea7173">
									<SHORT-NAME>CsmPrimitives</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for configuration of CsmPrimitives</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmAEADDecrypt -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="b739f3e1-7603-4cb5-b560-8dfbeb36bc6e">
											<SHORT-NAME>CsmAEADDecrypt</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configuration of AEAD decryption primitives</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<SUB-CONTAINERS>
												<!-- Container Definition: CsmAEADDecryptConfig -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="092ae8e2-3de5-4d75-8d04-10ca423c7aaa">
													<SHORT-NAME>CsmAEADDecryptConfig</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container for configuration of a CSM decryption interface. The container name serves as a symbolic name for the identifier of an decryption interface.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CsmAEADDecryptAlgorithmFamiliy -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="a1cb1a6c-5691-450a-90b5-8345448f00c2">
															<SHORT-NAME>CsmAEADDecryptAlgorithmFamiliy</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm family used for the crypto service. This parameter defines the most significant part of the algorithm.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="be7f1dbc-f6b6-4bc4-bb39-added189181b">
																	<SHORT-NAME>CRYPTO_ALGOFAM_3DES</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="6af689b8-63f7-4ad3-a4d6-a50cb2101dd9">
																	<SHORT-NAME>CRYPTO_ALGOFAM_AES</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="fb7fec02-50f7-4ae1-b287-6e8aedf1c932">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmAEADDecryptAlgorithmFamilyCustom -->
														<ECUC-STRING-PARAM-DEF UUID="3b625cb7-be1a-4386-a828-4151a2f7cdc5">
															<SHORT-NAME>CsmAEADDecryptAlgorithmFamilyCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This is the name of the custom algorithm family, if CRYPTO_ALGOFAM_CUSTOM is used as CsmAEADDecryptAlgorithmFamily.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmAEADDecryptAlgorithmKeyLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="450e2edf-5a3c-4ff0-ab05-7125b48a878d">
															<SHORT-NAME>CsmAEADDecryptAlgorithmKeyLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Size of the AEAD decryption key in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmAEADDecryptAlgorithmMode -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="a1184a25-3707-472a-aa82-fee42f93bb1e">
															<SHORT-NAME>CsmAEADDecryptAlgorithmMode</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm mode used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="c3d5de67-19f5-45d1-8b2f-1aa0aa954158">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="1db6b49d-fde2-4e45-98d9-81ad522f6cdc">
																	<SHORT-NAME>CRYPTO_ALGOMODE_GCM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmAEADDecryptAlgorithmModeCustom -->
														<ECUC-STRING-PARAM-DEF UUID="1d192f2d-3ee5-4c04-840a-9b98402c9fec">
															<SHORT-NAME>CsmAEADDecryptAlgorithmModeCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Name of the custom algorithm mode used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmAEADDecryptAssociatedDataMaxLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="8bd3fccf-b370-4cd1-8265-f1fb1ac6867a">
															<SHORT-NAME>CsmAEADDecryptAssociatedDataMaxLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Max size of the input associated data length in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmAEADDecryptCiphertextMaxLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="5f714dd2-50c2-49f8-9e7a-ca8cbd6d076d">
															<SHORT-NAME>CsmAEADDecryptCiphertextMaxLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Max size of the input ciphertext in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmAEADDecryptPlaintextMaxLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="bc1a2108-2948-4811-ac8b-76bfdfd0bbcc">
															<SHORT-NAME>CsmAEADDecryptPlaintextMaxLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Size of the output plaintext length in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmAEADDecryptProcessing -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="afba8b6e-**************-84c6f3208639">
															<SHORT-NAME>CsmAEADDecryptProcessing</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines how the interface shall be used for that primitive. Synchronous processing returns with the result while asynchronous processing returns without processing the job. The caller will be notified by the corresponding callback</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="c5505d07-c9ec-42c1-ac79-1bdf42f13651">
																	<SHORT-NAME>CSM_ASYNCHRONOUS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="5c082a7f-912f-4547-b0b6-52171f12f102">
																	<SHORT-NAME>CSM_SYNCHRONOUS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmAEADDecryptTagLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="1daea247-1ea8-4036-8959-bb3b2c6a0243">
															<SHORT-NAME>CsmAEADDecryptTagLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Size of the input Tag length in BITS</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: CsmAEADDecryptKeyRef -->
														<ECUC-REFERENCE-DEF UUID="b5a0cf4d-314f-4eb2-994c-e02a2cb01647">
															<SHORT-NAME>CsmAEADDecryptKeyRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter refers to the key used for that decryption primitive.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Csm/CsmKeys/CsmKey</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
														<!-- Reference Definition: CsmAEADDecryptQueueRef -->
														<ECUC-REFERENCE-DEF UUID="94dddc37-a55c-4ce4-9606-6f4e39a46fbf">
															<SHORT-NAME>CsmAEADDecryptQueueRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter refers to the queue used for that decryption primitive.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Csm/CsmQueues/CsmQueue</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: CsmAEADEncrypt -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="291d8ff1-5965-48dd-8225-92258399195a">
											<SHORT-NAME>CsmAEADEncrypt</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configuration of AEAD encryption primitives</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<SUB-CONTAINERS>
												<!-- Container Definition: CsmAEADEncryptConfig -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="e74cce7c-8f3c-429a-9fef-e47a222c8641">
													<SHORT-NAME>CsmAEADEncryptConfig</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container for configuration of a CSM encryption interface. The container name serves as a symbolic name for the identifier of an encryption interface.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CsmAEADEncryptAlgorithmFamiliy -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="44247018-817a-4c07-9d09-738087f0a12f">
															<SHORT-NAME>CsmAEADEncryptAlgorithmFamiliy</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm family used for the crypto service. This parameter defines the most significant part of the algorithm.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="57f69ab8-386f-4271-b09b-91fbbf0fe6a2">
																	<SHORT-NAME>CRYPTO_ALGOFAM_3DES</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="b9f33ddc-4ce5-4ce4-9c9c-bb3eba997d96">
																	<SHORT-NAME>CRYPTO_ALGOFAM_AES</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="83ca4884-24ce-4b78-939b-319348a5977f">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmAEADEncryptAlgorithmFamilyCustom -->
														<ECUC-STRING-PARAM-DEF UUID="db453e7a-f929-4ee7-b079-a69f6ed2de1e">
															<SHORT-NAME>CsmAEADEncryptAlgorithmFamilyCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This is the name of the custom algorithm family, if CRYPTO_ALGOFAM_CUSTOM is used as CsmAEADEncryptAlgorithmFamily.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmAEADEncryptAlgorithmKeyLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="7de3cf6f-8066-453c-b9df-b00cfc339694">
															<SHORT-NAME>CsmAEADEncryptAlgorithmKeyLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Size of the AEAD encryption key in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmAEADEncryptAlgorithmMode -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="b9f7e510-bf5e-449e-bf06-4362033c7abd">
															<SHORT-NAME>CsmAEADEncryptAlgorithmMode</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm mode used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="3ae94f3b-ec84-42fa-b78c-32073f33ade4">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="7ecb8b4f-d9a6-4dab-b3ba-adc4a8885a01">
																	<SHORT-NAME>CRYPTO_ALGOMODE_GCM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmAEADEncryptAlgorithmModeCustom -->
														<ECUC-STRING-PARAM-DEF UUID="593d4d36-cc4c-4d60-a640-3d51e3c0bbc1">
															<SHORT-NAME>CsmAEADEncryptAlgorithmModeCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Name of the custom algorithm mode used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmAEADEncryptAssociatedDataMaxLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="6bf021a4-c465-4f38-9efd-c79b55cb7472">
															<SHORT-NAME>CsmAEADEncryptAssociatedDataMaxLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Max size of the input associated data length in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmAEADEncryptCiphertextMaxLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="8f7c6f2a-f15e-4edc-9903-a4769b9be669">
															<SHORT-NAME>CsmAEADEncryptCiphertextMaxLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Max size of the output ciphertext length in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmAEADEncryptPlaintextMaxLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="8141367d-8ba0-43bf-b352-f9d3d0e9c97d">
															<SHORT-NAME>CsmAEADEncryptPlaintextMaxLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Max size of the input plaintext length in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmAEADEncryptProcessing -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="a3064817-3b35-451c-9fba-b97a489ecb30">
															<SHORT-NAME>CsmAEADEncryptProcessing</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines how the interface shall be used for that primitive. Synchronous processing returns with the result while asynchronous processing returns without processing the job. The caller will be notified by the corresponding callback</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="4afd5b60-7a71-4f2b-a7f0-6dff4be953c9">
																	<SHORT-NAME>CSM_ASYNCHRONOUS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="8515d8eb-7572-4793-86c4-08fbba2ed0ec">
																	<SHORT-NAME>CSM_SYNCHRONOUS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmAEADEncryptTagLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="16c80a2a-2521-4a39-9d26-94be17c35bba">
															<SHORT-NAME>CsmAEADEncryptTagLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Size of the output Tag length in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: CsmAEADEncryptKeyRef -->
														<ECUC-REFERENCE-DEF UUID="92414fc6-7e7b-414b-81a9-78d0ff2e369b">
															<SHORT-NAME>CsmAEADEncryptKeyRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter refers to the key used for that encryption primitive.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Csm/CsmKeys/CsmKey</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
														<!-- Reference Definition: CsmAEADEncryptQueueRef -->
														<ECUC-REFERENCE-DEF UUID="25c269ce-976c-4db3-af94-566b5de1570e">
															<SHORT-NAME>CsmAEADEncryptQueueRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter refers to the queue used for that encryption primitive.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Csm/CsmQueues/CsmQueue</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: CsmDecrypt -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="46e267a3-d80d-47d7-a6fa-cb5043e82778">
											<SHORT-NAME>CsmDecrypt</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configurations of Decryption primitives</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<SUB-CONTAINERS>
												<!-- Container Definition: CsmDecryptConfig -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="70f0bc96-eee5-467a-9e61-4685c54041c4">
													<SHORT-NAME>CsmDecryptConfig</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container for configuration of a CSM decryption interface. The container name serves as a symbolic name for the identifier of an decryption interface.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CsmDecryptAlgorithmFamiliy -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="b0ca8007-09d0-483c-a315-70ae3a828a44">
															<SHORT-NAME>CsmDecryptAlgorithmFamiliy</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm family used for the crypto service. This parameter defines the most significant part of the algorithm.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="94a26ba2-1cae-4b31-aec0-7452f7fb55a6">
																	<SHORT-NAME>CRYPTO_ALGOFAM_3DES</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="34b0dd86-ae6a-4051-8796-67e2b0fa9db6">
																	<SHORT-NAME>CRYPTO_ALGOFAM_AES</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="be6c8a87-4b05-4adf-a688-c0754e322e84">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CHACHA</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="b1250c29-d408-4bb1-a56a-22e73f8a6d64">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="536e3933-6a15-4302-84df-394c41eefceb">
																	<SHORT-NAME>CRYPTO_ALGOFAM_ECIES</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="09006328-ee48-4a5a-96b4-9c54a54bc378">
																	<SHORT-NAME>CRYPTO_ALGOFAM_RSA</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmDecryptAlgorithmFamilyCustom -->
														<ECUC-STRING-PARAM-DEF UUID="18ce426e-9a84-42a8-85c9-742b1a236180">
															<SHORT-NAME>CsmDecryptAlgorithmFamilyCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Name of the custom algorithm family, if CRYPTO_ALGOFAM_CUSTOM is used as CsmDecryptAlgorithmFamily.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmDecryptAlgorithmKeyLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="02da7cab-eba0-48ef-8d37-93c51c8d4235">
															<SHORT-NAME>CsmDecryptAlgorithmKeyLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Size of the encryption key in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmDecryptAlgorithmMode -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="87f3baca-123a-40d2-ab1c-67a609eb4dbb">
															<SHORT-NAME>CsmDecryptAlgorithmMode</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm mode used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="2cfc29f0-231d-4951-9bca-1d046b3649b2">
																	<SHORT-NAME>CRYPTO_ALGOMODE_12ROUNDS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="2a0d6abf-54fd-4bc7-a2fe-c1460e180398">
																	<SHORT-NAME>CRYPTO_ALGOMODE_20ROUNDS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="e40b4dec-4b65-4340-a501-8c7182fe56b3">
																	<SHORT-NAME>CRYPTO_ALGOMODE_8ROUNDS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="b2469c38-9ad2-4f08-a38b-f860a7add86d">
																	<SHORT-NAME>CRYPTO_ALGOMODE_CBC</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="b7c17ec3-c902-4e4c-a8fb-4e25a901e0b2">
																	<SHORT-NAME>CRYPTO_ALGOMODE_CFB</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="e5e3e4bf-60f3-402d-aaac-d1de49d4ca2f">
																	<SHORT-NAME>CRYPTO_ALGOMODE_CTR</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="6c884816-8bfb-4c4d-835f-28b9d84226bd">
																	<SHORT-NAME>CRYPTO_ALGOMODE_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ad2f2595-a0db-478c-8beb-0e2ed27b5c33">
																	<SHORT-NAME>CRYPTO_ALGOMODE_ECB</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="0dfdd205-d211-4c92-81cc-7ccc6aa26f7f">
																	<SHORT-NAME>CRYPTO_ALGOMODE_OFB</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="112d0634-3d55-43bd-9270-eb0cda7e5047">
																	<SHORT-NAME>CRYPTO_ALGOMODE_RSAES_OAEP</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="a4c77c12-1946-4b73-bcc2-99f7985f72de">
																	<SHORT-NAME>CRYPTO_ALGOMODE_RSAES_PKCS1_v1_5</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="d0dbd8c8-8e51-465b-8bc4-ed5ffc4b2916">
																	<SHORT-NAME>CRYPTO_ALGOMODE_XTS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmDecryptAlgorithmModeCustom -->
														<ECUC-STRING-PARAM-DEF UUID="c5e762ce-2a8e-4dea-8a69-64319c3ab4e2">
															<SHORT-NAME>CsmDecryptAlgorithmModeCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Name of the custom algorithm mode used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmDecryptAlgorithmSecondaryFamily -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="f03e2853-49cc-4b88-9297-acfe4b9676ee">
															<SHORT-NAME>CsmDecryptAlgorithmSecondaryFamily</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the secondary algorithm family used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>CRYPTO_ALGOFAM_NOT_SET</DEFAULT-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="f6d1a352-e4ea-45b1-bbda-20fd901db483">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="f617aed2-7fab-439a-85d6-61819bb420b1">
																	<SHORT-NAME>CRYPTO_ALGOFAM_NOT_SET</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmDecryptAlgorithmSecondaryFamilyCustom -->
														<ECUC-STRING-PARAM-DEF UUID="23ae0e82-92a9-422f-8907-8800ccbfd491">
															<SHORT-NAME>CsmDecryptAlgorithmSecondaryFamilyCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Name of the custom secondary algorithm family used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmDecryptDataMaxLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="1fe43d94-68f5-4d8f-978a-f2beff28f8dd">
															<SHORT-NAME>CsmDecryptDataMaxLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Max size of the input ciphertext length in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmDecryptProcessing -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="350fc09d-2fc3-4427-a4e4-5cdb52540dce">
															<SHORT-NAME>CsmDecryptProcessing</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines how the interface shall be used for that primitive. Synchronous processing returns with the result while asynchronous processing returns without processing the job. The caller will be notified by the corresponding callback</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="433eb8a5-6d4b-45b2-b90b-fcd1fa201aa8">
																	<SHORT-NAME>CSM_ASYNCHRONOUS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="d7c42fe7-e8c0-45d0-8f4c-74a7047a2152">
																	<SHORT-NAME>CSM_SYNCHRONOUS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmDecryptResultMaxLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="ec875ce6-56bb-4a13-b18c-c9661079c293">
															<SHORT-NAME>CsmDecryptResultMaxLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Max size of the output plaintext length in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: CsmEncrypt -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="3f4b7ccf-df57-454a-86db-54556e64957c">
											<SHORT-NAME>CsmEncrypt</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configurations of Encryption primitives</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<SUB-CONTAINERS>
												<!-- Container Definition: CsmEncryptConfig -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="0714939b-250c-4472-8483-d555cab5d6ae">
													<SHORT-NAME>CsmEncryptConfig</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container for configuration of a CSM encryption interface. The container name serves as a symbolic name for the identifier of an encryption interface.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CsmEncryptAlgorithmFamiliy -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="b7865263-d6a8-4a50-80ad-2c064e4648be">
															<SHORT-NAME>CsmEncryptAlgorithmFamiliy</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm family used for the crypto service. This parameter defines the most significant part of the algorithm.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="948301fa-0ba7-4d1f-8ded-9e93a97a1718">
																	<SHORT-NAME>CRYPTO_ALGOFAM_3DES</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="0847907a-1f9a-4f18-a386-992b2606a432">
																	<SHORT-NAME>CRYPTO_ALGOFAM_AES</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="afa1a2eb-84a0-4474-9545-5fcdf982a30e">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CHACHA</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="f9bf2d34-15b2-45bb-a1d6-76abc478f702">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="fc301284-6da8-4bcc-8d8b-7fe96c585b0c">
																	<SHORT-NAME>CRYPTO_ALGOFAM_ECIES</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="9c1893ad-224c-487d-9bef-29021b06e0ab">
																	<SHORT-NAME>CRYPTO_ALGOFAM_RSA</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmEncryptAlgorithmFamilyCustom -->
														<ECUC-STRING-PARAM-DEF UUID="e6e09321-1edc-405b-b19a-22cc81b1076c">
															<SHORT-NAME>CsmEncryptAlgorithmFamilyCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This is the name of the custom algorithm family, if CRYPTO_ALGOFAM_CUSTOM is used as CsmEncryptAlgorithmFamily.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmEncryptAlgorithmMode -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="64c306cf-0947-4016-bd96-b398aaad3d3f">
															<SHORT-NAME>CsmEncryptAlgorithmMode</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm mode used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="443325d6-2c60-4877-95a7-fe2b759461ab">
																	<SHORT-NAME>CRYPTO_ALGOMODE_12ROUNDS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="60ae9151-cdde-45a9-b755-c203ce0ec530">
																	<SHORT-NAME>CRYPTO_ALGOMODE_20ROUNDS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="177ad0ff-b87a-47dc-91db-e1dfa867f5be">
																	<SHORT-NAME>CRYPTO_ALGOMODE_8ROUNDS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="f1ad66b2-e343-433c-8a39-5982c1c9ad73">
																	<SHORT-NAME>CRYPTO_ALGOMODE_CBC</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="c60ad26a-6add-4cf7-9cd9-81ca0b33edcc">
																	<SHORT-NAME>CRYPTO_ALGOMODE_CFB</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="74ec36d9-10be-4637-9a59-015739d6f77c">
																	<SHORT-NAME>CRYPTO_ALGOMODE_CTR</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="971d6306-aa51-411f-bd65-32d67682a906">
																	<SHORT-NAME>CRYPTO_ALGOMODE_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="249bd8a4-6202-4bcc-8a50-d91521d685ae">
																	<SHORT-NAME>CRYPTO_ALGOMODE_ECB</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="9961f75f-43f5-4f9a-8789-da2772412d2c">
																	<SHORT-NAME>CRYPTO_ALGOMODE_NOT_SET</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="a7344e2f-fff6-4cbf-aa23-2891fd9efd21">
																	<SHORT-NAME>CRYPTO_ALGOMODE_OFB</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="b70ee064-bb0d-4596-a131-cb2090dee598">
																	<SHORT-NAME>CRYPTO_ALGOMODE_RSAES_OAEP</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="1145e171-d468-4704-8f38-90c4b14ec961">
																	<SHORT-NAME>CRYPTO_ALGOMODE_RSAES_PKCS1_v1_5</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="12005b04-447a-4f48-bf35-0b38d0282e91">
																	<SHORT-NAME>CRYPTO_ALGOMODE_XTS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmEncryptAlgorithmModeCustom -->
														<ECUC-STRING-PARAM-DEF UUID="48b668ed-5a9d-4900-ae5e-b9eb6833b076">
															<SHORT-NAME>CsmEncryptAlgorithmModeCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Name of the custom algorithm mode used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmEncryptAlgorithmSecondaryFamily -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="572d8304-0329-4bab-8d72-49c9a9452dd5">
															<SHORT-NAME>CsmEncryptAlgorithmSecondaryFamily</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm family used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>CRYPTO_ALGOFAM_NOT_SET</DEFAULT-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="b8b3b0fd-9c06-46d1-8d24-25a951ff6321">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="757d7135-e520-4be4-8b0f-7773af8b8fd3">
																	<SHORT-NAME>CRYPTO_ALGOFAM_NOT_SET</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmEncryptAlgorithmSecondaryFamilyCustom -->
														<ECUC-STRING-PARAM-DEF UUID="3a45778b-dccf-4f3d-bd22-ebdeb04c9292">
															<SHORT-NAME>CsmEncryptAlgorithmSecondaryFamilyCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Name of the custom secondary algorithm family used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmEncryptDataMaxLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="6e378d3e-eb6d-45ac-9fa1-3cb39366237d">
															<SHORT-NAME>CsmEncryptDataMaxLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Max size of the input plaintext length in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmEncryptProcessing -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="b31a4d25-56d8-419b-9280-7c6c91cb5194">
															<SHORT-NAME>CsmEncryptProcessing</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines how the interface shall be used for that primitive. Synchronous processing returns with the result while asynchronous processing returns without processing the job. The caller will be notified by the corresponding callback</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="8e5293fc-4368-425e-8da7-17f5955226f7">
																	<SHORT-NAME>CSM_ASYNCHRONOUS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="3df7c304-0c6e-4de5-be54-92d26b027058">
																	<SHORT-NAME>CSM_SYNCHRONOUS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmEncryptResultMaxLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="c809b99b-cb37-46f3-b5de-d54a58c7952a">
															<SHORT-NAME>CsmEncryptResultMaxLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Max size of the output cipher length in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: CsmHash -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ffe193d3-66e3-4771-bc96-2f0034e89ec9">
											<SHORT-NAME>CsmHash</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for Hash Configurations</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<SUB-CONTAINERS>
												<!-- Container Definition: CsmHashConfig -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="2cc4aa34-4ee8-40f9-b31d-************">
													<SHORT-NAME>CsmHashConfig</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container for configuration of a CSM hash. The container name serves as a symbolic name for the identifier of a key configuration.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CsmHashAlgorithmFamiliy -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="0e3e2464-c73d-4ffe-885c-b9c11f22a394">
															<SHORT-NAME>CsmHashAlgorithmFamiliy</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm family used for the crypto service. This parameter defines the most significant part of the algorithm.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="0552587d-5162-44d3-ab28-8e0c9ed8a8ce">
																	<SHORT-NAME>CRYPTO_ALGOFAM_BLAKE_1_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="aae9b427-67de-42f5-a269-64a957c5ce1f">
																	<SHORT-NAME>CRYPTO_ALGOFAM_BLAKE_1_512</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="dfd3c6b7-445d-4bb3-b7f0-67cccfbed2c6">
																	<SHORT-NAME>CRYPTO_ALGOFAM_BLAKE_2s_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="df5b5870-744b-4a87-ba7d-712362ca65cc">
																	<SHORT-NAME>CRYPTO_ALGOFAM_BLAKE_2s_512</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="30078976-62e9-4fb3-a1dc-c5a0f5750f20">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="f5184826-8633-4dbb-afc1-7dfdaa53dda2">
																	<SHORT-NAME>CRYPTO_ALGOFAM_RIPEMD160</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="1cc00d45-0d83-4672-9f3c-40a140ef2458">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA1</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="fe06baeb-bdb7-439c-85b3-3d6db3157359">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_224</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="20501fcf-c925-4f37-bc4d-5df4d837c15d">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="b882a54c-0ee0-4b51-a0a3-e242808b305f">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_384</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="a6031a58-7f92-4a32-bdad-c368a7225fd4">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_512</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="85170411-e99a-427d-a769-a463e27b1ae6">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_512_224</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="da01c003-92e2-4d9e-8ba9-6a8554eeb1c6">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_512_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="dd042eb4-4db6-4134-8558-ed33b6221ae9">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_224</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="5fc38fb5-0097-4e6f-aa84-013fb623efb3">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="4c0e5bb4-dce3-4a4a-8c80-8776e0b95ae9">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_384</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="fa4db2d6-6194-46f4-90f1-3fb670d31e8f">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_512</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="54a8b1b2-5d2a-452c-a8d7-1e6974b5dac5">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_SHAKE128</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="c4fcec44-7341-4422-b6bb-565580090b80">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_SHAKE256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmHashAlgorithmFamilyCustom -->
														<ECUC-STRING-PARAM-DEF UUID="236996b7-c73d-4be2-8aad-03c1fef3e31c">
															<SHORT-NAME>CsmHashAlgorithmFamilyCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This is the name of the custom algorithm family, if CRYPTO_ALGOFAM_CUSTOM is used as CsmHashAlgorithmFamily.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmHashAlgorithmMode -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="92882e2f-45b6-4090-8595-4e24760418db">
															<SHORT-NAME>CsmHashAlgorithmMode</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm mode used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>CRYPTO_ALGOMODE_NOT_SET</DEFAULT-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="4615a652-**************-9402a8772728">
																	<SHORT-NAME>CRYPTO_ALGOMODE_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="c54a4e3b-efaa-4a4f-9de8-df520ea904e6">
																	<SHORT-NAME>CRYPTO_ALGOMODE_NOT_SET</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmHashAlgorithmModeCustom -->
														<ECUC-STRING-PARAM-DEF UUID="af6c08c7-5241-4643-af19-3003ff3887b9">
															<SHORT-NAME>CsmHashAlgorithmModeCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Name of the custom primitive mode.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmHashAlgorithmSecondaryFamily -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="a9336cbc-de94-44d9-8f6a-064a19bac97f">
															<SHORT-NAME>CsmHashAlgorithmSecondaryFamily</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm family used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>CRYPTO_ALGOFAM_NOT_SET</DEFAULT-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="fd5078be-a9ae-43ab-9db1-048eb988a946">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="06f2d815-400c-40ae-bc71-59e9bf23d362">
																	<SHORT-NAME>CRYPTO_ALGOFAM_NOT_SET</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmHashAlgorithmSecondaryFamilyCustom -->
														<ECUC-STRING-PARAM-DEF UUID="e1396f5c-2564-435f-8f71-14f363f92a61">
															<SHORT-NAME>CsmHashAlgorithmSecondaryFamilyCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This is the second name of the custom algorithm family, if CRYPTO_ALGOFAM_CUSTOM is set as CsmHashAlgorithmSecondaryFamily.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmHashDataMaxLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="b2d54e57-087d-4d3c-8643-c0d33d8ac296">
															<SHORT-NAME>CsmHashDataMaxLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Max size of the input data length in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmHashProcessing -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="5a743541-d1ce-4ada-9121-344647e8003c">
															<SHORT-NAME>CsmHashProcessing</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines how the interface shall be used for that primitive. Synchronous processing returns with the result while asynchronous processing returns without processing the job. The caller will be notified by the corresponding callback.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="fc254317-a33f-476e-af36-be61b241d1ac">
																	<SHORT-NAME>CSM_ASYNCHRONOUS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="17d954bc-66ab-45cc-9bde-eb8130bbf05f">
																	<SHORT-NAME>CSM_SYNCHRONOUS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmHashResultLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="5a3e0d0d-17df-4d63-94b8-ec6d0c16ecb4">
															<SHORT-NAME>CsmHashResultLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Size of the output hash length in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: CsmMacGenerate -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="76c1ab44-cf00-4731-a9db-6f0a9f1c10cb">
											<SHORT-NAME>CsmMacGenerate</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configurations of MacGenerate primitives</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<SUB-CONTAINERS>
												<!-- Container Definition: CsmMacGenerateConfig -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="c2671334-6ddd-435d-82aa-ad0dca40aeea">
													<SHORT-NAME>CsmMacGenerateConfig</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container for configuration of a CSM mac generation interface. The container name serves as a symbolic name for the identifier of a MAC generation interface.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CsmMacGenerateAlgorithmFamiliy -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="40188569-ca18-4705-bcd2-cf85d544b99e">
															<SHORT-NAME>CsmMacGenerateAlgorithmFamiliy</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm family used for the crypto service. This parameter defines the most significant part of the algorithm.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="832d7aa1-7ac8-4869-af2b-eb8313c6fb40">
																	<SHORT-NAME>CRYPTO_ALGOFAM_3DES</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="a370ff4b-a4e3-4913-adc4-3189708fe182">
																	<SHORT-NAME>CRYPTO_ALGOFAM_AES</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="fb687f05-4e9c-422a-83fb-15d49eb7c4ed">
																	<SHORT-NAME>CRYPTO_ALGOFAM_BLAKE_1_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="f5b22710-c3da-462c-bf84-7793c3a2d06f">
																	<SHORT-NAME>CRYPTO_ALGOFAM_BLAKE_1_512</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="673de23f-8f28-4e11-b272-093513ac624f">
																	<SHORT-NAME>CRYPTO_ALGOFAM_BLAKE_2s_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="6bb8e53d-f306-4d0d-a1a1-b06506c711b1">
																	<SHORT-NAME>CRYPTO_ALGOFAM_BLAKE_2s_512</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="091cbcaf-0a6f-442a-b682-a1d9a7c4873b">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CHACHA</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="5e34bbbe-0d2a-43a9-adb5-83cb0e36a3a0">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="a9c468c2-1d20-4189-8005-3e32e38ae325">
																	<SHORT-NAME>CRYPTO_ALGOFAM_RIPEMD160</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="b551a2b4-2de4-4099-95a8-4eeaa9ae08ed">
																	<SHORT-NAME>CRYPTO_ALGOFAM_RNG</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="8d2b3d32-30b3-4e95-ad0d-81b494873901">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA1</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ab3de170-8c3d-4f4e-a1fe-50c28d135d5f">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_224</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="33e4ff53-1da6-4549-a5f7-164c17c2cfaa">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="c8383d2a-8745-4353-bdae-9ccd18beafc7">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_384</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="06b7dcbc-4e88-485e-bd98-2da96d362838">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_512</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="56aed289-d0a3-4753-9697-eabf465b92d6">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_512_224</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="f5911157-d14c-44a8-a6a7-b9ba23ce245f">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_512_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="7917d40e-3238-4db8-83af-b6829c92e493">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_224</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="79a63346-5834-461b-83f3-d0a7bf3dd064">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ea76a7ee-11be-4dd8-93b6-ea9bbf02b40a">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_384</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="49912d4d-51b8-4a82-9231-cee2a572af89">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_512</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="fb9656d9-15a4-4179-8fdc-cf3619569342">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_SHAKE128</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="6e2a3220-dd6f-499c-8cf6-a4ef52ac23ee">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_SHAKE256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmMacGenerateAlgorithmFamilyCustom -->
														<ECUC-STRING-PARAM-DEF UUID="5d1b05de-53d1-4315-b2bf-7df6413955e8">
															<SHORT-NAME>CsmMacGenerateAlgorithmFamilyCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This is the name of the custom algorithm family,</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">if CRYPTO_ALGOFAM_CUSTOM is used as CsmMacGenerateAlgorithmFamily</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmMacGenerateAlgorithmKeyLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="4127714a-c247-426a-a1bd-d10ba5e97cb9">
															<SHORT-NAME>CsmMacGenerateAlgorithmKeyLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Size of the MAC key in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmMacGenerateAlgorithmMode -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="193d295a-43a0-4a74-81f7-3d5059e5f603">
															<SHORT-NAME>CsmMacGenerateAlgorithmMode</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm mode used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="3543db25-569a-4d8f-8df9-495c70d9ce3a">
																	<SHORT-NAME>CRYPTO_ALGOMODE_CMAC</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="e8cc628d-95dc-4444-a873-bd8e0451fd36">
																	<SHORT-NAME>CRYPTO_ALGOMODE_CTRDRBG</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="a6384f41-bcec-4e9e-b8da-c8779ea67a0a">
																	<SHORT-NAME>CRYPTO_ALGOMODE_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="93754989-97fc-49f2-a176-3a4f1dbba1eb">
																	<SHORT-NAME>CRYPTO_ALGOMODE_GMAC</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="2b10c909-277d-4b83-b872-9b9e25902f68">
																	<SHORT-NAME>CRYPTO_ALGOMODE_HMAC</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="caf7bf34-eeba-4462-a984-d2f8110b2ce1">
																	<SHORT-NAME>CRYPTO_ALGOMODE_NOT_SET</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="92f1055f-8b61-47e7-bd28-69c8cc96da59">
																	<SHORT-NAME>CRYPTO_ALGOMODE_SIPHASH_2_4</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="7018c452-26b9-4ca0-95df-ec8ff7299f41">
																	<SHORT-NAME>CRYPTO_ALGOMODE_SIPHASH_4_8</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmMacGenerateAlgorithmModeCustom -->
														<ECUC-STRING-PARAM-DEF UUID="9604fe54-b9ba-413c-bfea-2676a8ebbc2e">
															<SHORT-NAME>CsmMacGenerateAlgorithmModeCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Name of the custom algorithm mode used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmMacGenerateAlgorithmSecondaryFamily -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="b2679827-345c-47a8-9b74-f999c9ee15c9">
															<SHORT-NAME>CsmMacGenerateAlgorithmSecondaryFamily</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the secondary algorithm family used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>CRYPTO_ALGOFAM_NOT_SET</DEFAULT-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="75998751-37ee-41bc-b38b-be754e18f550">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="b2c37fd6-a730-41da-bba1-fec31d12bdd0">
																	<SHORT-NAME>CRYPTO_ALGOFAM_NOT_SET</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmMacGenerateAlgorithmSecondaryFamilyCustom -->
														<ECUC-STRING-PARAM-DEF UUID="f23baa62-39ce-43fd-bca9-edeb8e9ab805">
															<SHORT-NAME>CsmMacGenerateAlgorithmSecondaryFamilyCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This is the second name of the custom algorithm family, if CRYPTO_ALGOFAM_CUSTOM is set as CsmHashAlgorithmSecondaryFamilyCustom.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmMacGenerateDataMaxLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="667bf375-f2f6-42c6-937b-41c851114f7e">
															<SHORT-NAME>CsmMacGenerateDataMaxLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Max size of the input data length in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmMacGenerateProcessing -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="a422fc73-9626-413c-8faa-f6adac56081c">
															<SHORT-NAME>CsmMacGenerateProcessing</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines how the interface shall be used for that primitive. Synchronous processing returns with the result while asynchronous processing returns without processing the job. The caller will be notified by the corresponding callback.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="edc1d134-ffdd-4b01-b133-1a1ca22864dd">
																	<SHORT-NAME>CSM_ASYNCHRONOUS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="05d2630f-879d-4cee-a02f-95fe55cd5c16">
																	<SHORT-NAME>CSM_SYNCHRONOUS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmMacGenerateResultLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="5850299b-6489-4341-b9e9-93bc30da7655">
															<SHORT-NAME>CsmMacGenerateResultLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Size of the output MAC length in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: CsmMacVerify -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="8e223550-7ebc-44ce-84c7-4d05411daa04">
											<SHORT-NAME>CsmMacVerify</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configurations of MacVerify primitives</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<SUB-CONTAINERS>
												<!-- Container Definition: CsmMacVerifyConfig -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="aa358ae1-2419-4ccd-913d-2f79785805de">
													<SHORT-NAME>CsmMacVerifyConfig</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container for configuration of a CSM MAC verification interface. The container name serves as a symbolic name for the identifier of a MAC generation interface</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CsmMacVerifyAlgorithmFamiliy -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="3a1f9128-e737-4548-a8a1-60ac975c10b5">
															<SHORT-NAME>CsmMacVerifyAlgorithmFamiliy</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm family used for the crypto service. This parameter defines the most significant part of the algorithm.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="e851f48e-11c4-4b38-85ba-84a7eb191b76">
																	<SHORT-NAME>CRYPTO_ALGOFAM_AES</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="8fc94fa6-0cce-4f9e-ae8e-92502d1ecfa8">
																	<SHORT-NAME>CRYPTO_ALGOFAM_BLAKE_1_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="635ba7c7-eeb4-4571-a757-7ca399228d7f">
																	<SHORT-NAME>CRYPTO_ALGOFAM_BLAKE_1_512</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="98110371-09f5-4781-8edd-7bcc6715bcff">
																	<SHORT-NAME>CRYPTO_ALGOFAM_BLAKE_2s_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="3e0fa607-f9fa-414b-a805-85cf02cf40d6">
																	<SHORT-NAME>CRYPTO_ALGOFAM_BLAKE_2s_512</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="46a1574e-f134-4c03-a1a0-1769ca0760c5">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="13e6da9c-c1bd-487e-aaa7-c6f8af3a30a2">
																	<SHORT-NAME>CRYPTO_ALGOFAM_RIPEMD160</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="22ac0163-c1fe-41f2-bb92-c0f58d162b13">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA1</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="27c6b3a0-5441-4b40-a8eb-218ad7122427">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_224</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="7bd0b054-dfcc-4ba7-a6d1-5b1dd1d976fd">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="4a5f6621-97fe-46e0-9c98-3c67a9efbbc0">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_384</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="aa66c078-0fc2-4ac1-84e1-64ee0c5f8e57">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_512</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="8ba4b6ba-7fd8-45e1-a020-8115dbe11b2b">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_512_224</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="1ffec580-920f-4d06-a3be-9dcd46834d7e">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_512_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="910d3923-9529-4680-9e4f-25b37b3f15a6">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_224</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="96e9766c-98bc-498b-889e-bd2483fd5ba5">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="25ec4378-**************-39b03a149fc0">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_384</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="6d07ac74-521a-4b2a-a3c9-bd0e996ed501">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_512</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="76ad059e-9abb-4c9e-8ef6-48fa81171494">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_SHAKE128</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="034f63ae-4371-4ae7-bc3c-b8343175de6f">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_SHAKE256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="400d0da4-525e-4621-8b62-07aa3eff4303">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SIPHASH</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmMacVerifyAlgorithmFamilyCustom -->
														<ECUC-STRING-PARAM-DEF UUID="b24ba482-9b00-4ddf-bb79-cd7db6e9a952">
															<SHORT-NAME>CsmMacVerifyAlgorithmFamilyCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Name of the custom algorithm family used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmMacVerifyAlgorithmSecondaryFamily -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="eb37521b-6cfb-4292-9cbd-4607934da293">
															<SHORT-NAME>CsmMacVerifyAlgorithmSecondaryFamily</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the secondary algorithm family used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>CRYPTO_ALGOFAM_NOT_SET</DEFAULT-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="bbef71ac-3624-48da-b90a-10726e5a5452">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="231bff52-b33d-42b4-a2c7-048021ab95bb">
																	<SHORT-NAME>CRYPTO_ALGOFAM_NOT_SET</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmMacVerifyAlgorithmSecondaryFamilyCustom -->
														<ECUC-STRING-PARAM-DEF UUID="ddeadbff-a998-4e01-b761-0b54a3b66474">
															<SHORT-NAME>CsmMacVerifyAlgorithmSecondaryFamilyCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This is the second the name of the custom algorithm, if CRYPTO_ALGOFAM_CUSTOM is set as CsmMacVerifyAlgorithmSecondaryFamily</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmMacVerifyCompareLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="8264e0ae-cee8-43ed-9fa0-9e553543a5b8">
															<SHORT-NAME>CsmMacVerifyCompareLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Size of the input MAC length, that shall be verified, in BITS</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmMacVerifyDataMaxLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="b273ce86-523f-4ffc-bae5-338a2349b4d3">
															<SHORT-NAME>CsmMacVerifyDataMaxLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Max size of the input data length, for whichs MAC shall be verified, in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmMacVerifyProcessing -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="9ef96e0b-55be-41ed-b00b-4b13e6a12af3">
															<SHORT-NAME>CsmMacVerifyProcessing</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines how the interface shall be used for that primitive. Synchronous processing returns with the result while asynchronous processing returns without processing the job. The caller will be notified by the corresponding callback.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="c9015f01-5d21-4372-8a30-99c324305e6d">
																	<SHORT-NAME>CSM_ASYNCHRONOUS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="4c043dea-c040-45ce-82bf-8f979a192291">
																	<SHORT-NAME>CSM_SYNCHRONOUS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: CsmRandomGenerate -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ec02cfc2-8484-454b-a808-9a61cc8c8f71">
											<SHORT-NAME>CsmRandomGenerate</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configurations of RandomGenerate primitives</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<SUB-CONTAINERS>
												<!-- Container Definition: CsmRandomGenerateConfig -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="e0f13b04-04b6-4c86-a85a-f035dd5bb7e9">
													<SHORT-NAME>CsmRandomGenerateConfig</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container for configuration of a CSM random generator. The container name serves as a symbolic name for the identifier of a random generator configuration.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CsmRandomGenerateAlgorithmFamiliy -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="e13eb029-eab0-429e-b592-b6c2daa56d33">
															<SHORT-NAME>CsmRandomGenerateAlgorithmFamiliy</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm family used for the crypto service. This parameter defines the most significant part of the algorithm.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="f1aa6bf7-1517-4696-ae64-58dd248a9fcc">
																	<SHORT-NAME>CRYPTO_ALGOFAM_3DES</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="b7234333-1559-4ae4-bbac-b493340b22c5">
																	<SHORT-NAME>CRYPTO_ALGOFAM_AES</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="cea5ee7d-df5c-49c5-9848-45ccdded7b83">
																	<SHORT-NAME>CRYPTO_ALGOFAM_BLAKE_1_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="1869d327-15c7-4ff5-af11-616cb6dcc72b">
																	<SHORT-NAME>CRYPTO_ALGOFAM_BLAKE_1_512</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="5f26bb32-89d7-47a5-b024-88d8b8818d5b">
																	<SHORT-NAME>CRYPTO_ALGOFAM_BLAKE_2s_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="1c062bd1-a496-40d9-9b1d-eb338f61e444">
																	<SHORT-NAME>CRYPTO_ALGOFAM_BLAKE_2s_512</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="13559e95-982a-4bd8-9a2d-bd485f7515b0">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CHACHA</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="bd085ad6-5cea-4a10-bd78-d980feee5039">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="a74e276c-fa99-4c0a-9512-0b36fb80a19b">
																	<SHORT-NAME>CRYPTO_ALGOFAM_RIPEMD160</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="33306948-2fdb-4b7a-84f8-9feaa6b9c81a">
																	<SHORT-NAME>CRYPTO_ALGOFAM_RNG</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="2b912e1a-532f-4ee1-b574-acb6ef049ed1">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA1</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="dc3ed93b-01f6-425a-ac64-a6b21b070da4">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_224</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ff70b3f6-72a4-4ef5-b4af-03abcd3eb5b7">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="b201e60d-9d39-4482-b990-f4ea85e5155d">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_384</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="d2af595c-92f8-43e5-a482-0344c872f9f9">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_512</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="05e5ee90-3214-4342-bf19-57a4ea2a609f">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_512_224</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="1a4fb2ac-832b-4560-811f-4ee571ba6313">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_512_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="514f9711-8394-47de-953b-222d4a9c4de3">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_224</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="843b7013-b360-4942-af31-52c7969d616e">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="64499a5e-2877-41f2-b398-41400ec7f09f">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_384</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="4f2023d4-7ebf-440b-9c19-3c4cab72343b">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_512</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="3f01a0ad-cfc0-4784-8c2b-1543b1751641">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_SHAKE128</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="404fd56e-8c7f-4a9d-ab93-6e1204fca7e8">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_SHAKE256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmRandomGenerateAlgorithmFamilyCustom -->
														<ECUC-STRING-PARAM-DEF UUID="fc859224-bea1-48ae-8fe3-525653d8ee10">
															<SHORT-NAME>CsmRandomGenerateAlgorithmFamilyCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Name of the custom algorithm family used for the crypto service. This is the name of the custom algorithm family, if CRYPTO_ALGOFAM_CUSTOM is used as CsmRandomAlgorithmFamily</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmRandomGenerateAlgorithmMode -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="371136e3-8c98-41f2-aeb1-dc1a3b0b4346">
															<SHORT-NAME>CsmRandomGenerateAlgorithmMode</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm mode used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="45a13765-362e-4be4-84a2-1090cb882291">
																	<SHORT-NAME>CRYPTO_ALGOMODE_CMAC</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="98216a2e-4250-4fa6-881a-dee8ff429b0c">
																	<SHORT-NAME>CRYPTO_ALGOMODE_CTRDRBG</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="24cb6bd1-70f4-479e-970b-9c251532c77a">
																	<SHORT-NAME>CRYPTO_ALGOMODE_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ec317724-dd25-40c8-bf2e-71bc0ac3daf1">
																	<SHORT-NAME>CRYPTO_ALGOMODE_GMAC</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="8b65354c-b359-48fa-862b-d144718ab51e">
																	<SHORT-NAME>CRYPTO_ALGOMODE_HMAC</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="e78b7ef1-4b57-4924-be76-b2e12d289cfe">
																	<SHORT-NAME>CRYPTO_ALGOMODE_NOT_SET</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="493878aa-4289-46b4-9706-3e1ed4aa1494">
																	<SHORT-NAME>CRYPTO_ALGOMODE_SIPHASH_2_4</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="89ef18fc-3a43-4973-a7b1-9c82132d7034">
																	<SHORT-NAME>CRYPTO_ALGOMODE_SIPHASH_4_8</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmRandomGenerateAlgorithmModeCustom -->
														<ECUC-STRING-PARAM-DEF UUID="bcae0710-fa1d-499d-b70b-023ba0d2f70b">
															<SHORT-NAME>CsmRandomGenerateAlgorithmModeCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Name of the custom algorithm mode used for the crypto service. This is the name of the custom algorithm family, if CRYPTO_ALGOFAM_CUSTOM is used as CsmRandomGenerateAlgorithmFamily.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmRandomGenerateAlgorithmSecondaryFamily -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="b30aa787-6af6-457c-b8c3-53f2dde767ba">
															<SHORT-NAME>CsmRandomGenerateAlgorithmSecondaryFamily</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm family used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="c72dbaab-4634-4222-877b-f64956e51563">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="2803f5da-c3fe-4a31-abce-1b5c8438e0be">
																	<SHORT-NAME>CRYPTO_ALGOFAM_NOT_SET</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmRandomGenerateAlgorithmSecondaryFamilyCustom -->
														<ECUC-STRING-PARAM-DEF UUID="57b3224f-6783-458b-aa80-3ffbdca65503">
															<SHORT-NAME>CsmRandomGenerateAlgorithmSecondaryFamilyCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Name of the custom secondary algorithm family used for the crypto service. This is the second name of the custom algorithm family, if CRYPTO_ALGOFAM_CUSTOM is set as Csm RandomAlgorithmSecondaryFamily.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmRandomGenerateProcessing -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="8c64cdef-3c23-42fa-9458-e8323e5a2430">
															<SHORT-NAME>CsmRandomGenerateProcessing</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines how the interface shall be used for that primitive. Synchronous processing returns with the result while asynchronous processing returns without processing the job. The caller will be notified by the corresponding callback</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="e99eddfc-9640-4ee9-9a35-8bc14f23bb4e">
																	<SHORT-NAME>CSM_ASYNCHRONOUS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="1b1b5385-036d-45b1-acdc-eea2d174ae06">
																	<SHORT-NAME>CSM_SYNCHRONOUS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmRandomGenerateResultLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="b3d30c52-8e6b-4cac-a1f5-54adf3220495">
															<SHORT-NAME>CsmRandomGenerateResultLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Size of the random generate key in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: CsmSecureCounter -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ae8fcb3a-3062-4256-a7a5-fc85315fba62">
											<SHORT-NAME>CsmSecureCounter</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configurations of SecureCounter primitives</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<SUB-CONTAINERS>
												<!-- Container Definition: CsmSecureCounterConfig -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="3cc03b6b-07cd-4ae6-b4a0-015f78d5f527">
													<SHORT-NAME>CsmSecureCounterConfig</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container for configuration of a CSM counter. The container name serves as a symbolic name for the identifier of a secure counter configuration.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<REFERENCES>
														<!-- Reference Definition: CsmSecureCounterQueueRef -->
														<ECUC-REFERENCE-DEF UUID="793a134a-bab0-4c8b-92cd-1a983c713dc2">
															<SHORT-NAME>CsmSecureCounterQueueRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Not used. Queue is referred in Job.
		This parameter refers to the queue used for that secure counter.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Csm/CsmQueues/CsmQueue</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: CsmSignatureGenerate -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="913d2545-13ac-4c53-981c-83558b0ed28f">
											<SHORT-NAME>CsmSignatureGenerate</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configurations of SignatureGenerate primitives</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<SUB-CONTAINERS>
												<!-- Container Definition: CsmSignatureGenerateConfig -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="12372ff1-ccaf-4025-b434-09f6e2abf3f8">
													<SHORT-NAME>CsmSignatureGenerateConfig</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container for configuration of a CSM signature generation interface. The container name serves as a symbolic name for the identifier of signature generation interface.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CsmSignatureGenerateAlgorithmFamiliy -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="b03c8cdd-335b-4a99-bcb0-f56de0db845b">
															<SHORT-NAME>CsmSignatureGenerateAlgorithmFamiliy</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm family used for the crypto service. This parameter defines the most significant part of the algorithm.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="e3ae2b84-2589-446d-a3b6-085f1bbc45ae">
																	<SHORT-NAME>CRYPTO_ALGOFAM_BRAINPOOL</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="607bd5ac-0559-4f9f-9691-b3369fce49aa">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="206fa038-de35-4fbb-8732-b923c6686208">
																	<SHORT-NAME>CRYPTO_ALGOFAM_ECCNIST</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ae422e39-27be-4fdc-b09a-17c9cc84e357">
																	<SHORT-NAME>CRYPTO_ALGOFAM_ED25519</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="524d1a7a-3725-4125-bebf-61f49d960ece">
																	<SHORT-NAME>CRYPTO_ALGOFAM_RSA</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmSignatureGenerateAlgorithmFamilyCustom -->
														<ECUC-STRING-PARAM-DEF UUID="98d26f76-c63a-4c1b-a4e6-68d2e458dc7b">
															<SHORT-NAME>CsmSignatureGenerateAlgorithmFamilyCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Name of the custom algorithm family used for the crypto service.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">This is the name of the custom algorithm family, if CRYPTO_ALGOFAM_CUSTOM is used as CsmSignatureGenerateAlgorithmFamily.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmSignatureGenerateAlgorithmMode -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="caf4f4d1-064a-4b7a-86b2-56baff592e6f">
															<SHORT-NAME>CsmSignatureGenerateAlgorithmMode</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm mode used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="2fcce9d7-62be-4c20-9f92-fa654ec74e0d">
																	<SHORT-NAME>CRYPTO_ALGOMODE_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="6fee068c-abd1-4252-83a9-d45bdc716da4">
																	<SHORT-NAME>CRYPTO_ALGOMODE_NOT_SET</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="bfe4eec0-b38a-48db-b8b5-7a70df75ab58">
																	<SHORT-NAME>CRYPTO_ALGOMODE_RSASSA_PKCS1_v1_5</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="0cfd873f-3dd7-4378-a73f-647505e77992">
																	<SHORT-NAME>CRYPTO_ALGOMODE_RSASSA_PSS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmSignatureGenerateAlgorithmModeCustom -->
														<ECUC-STRING-PARAM-DEF UUID="cb3f8a86-07d7-4858-ab41-563eebe4f954">
															<SHORT-NAME>CsmSignatureGenerateAlgorithmModeCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Name of the custom algorithm mode used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmSignatureGenerateAlgorithmSecondaryFamily -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="831ec95b-fc47-4b7c-a3d2-d9b0100c573a">
															<SHORT-NAME>CsmSignatureGenerateAlgorithmSecondaryFamily</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm mode used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>CRYPTO_ALGOFAM_NOT_SET</DEFAULT-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="b77ac104-8118-4fe0-af20-da001229d516">
																	<SHORT-NAME>CRYPTO_ALGOFAM_BLAKE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="3ac0b009-0595-4923-bcb8-e0d201241959">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="eb53d81d-9f39-4308-bc51-bb9607307d86">
																	<SHORT-NAME>CRYPTO_ALGOFAM_NOT_SET</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="e9252cef-2d9c-4cf1-ab2f-623cc8a46742">
																	<SHORT-NAME>CRYPTO_ALGOFAM_RIPEMD160</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="6aa113e2-2bb5-4b6e-a3e5-b3cbc0d221bf">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA1</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="7f3400e9-b893-4bd1-9622-111400428507">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_224</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="d9248865-77e2-4cc1-9d29-821bb64f3d43">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="218ef119-cc3b-4dee-8da0-6f16972cb87e">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_384</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="97d19782-2b01-4331-a3b7-979ca9877d05">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_512</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="7b00630e-565c-4c6b-9699-081ebde40318">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_512_224</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="31b71922-73e6-46be-be25-d3311cebeceb">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_512_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="e701bd40-0705-4255-8119-9fe176984ce7">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_224</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="889f6e3d-91f4-45b5-b78c-eb7857fb147a">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="5e6de64c-f81e-4133-9ff5-3e5d816f8c04">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_384</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ed64da22-bccc-4407-a367-51901a09108c">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_512</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="31bd98eb-6b76-493d-b460-23aa3db2ef1c">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_SHAKE128</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="deb4481c-4398-46db-8438-29e1998ec2cc">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_SHAKE256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmSignatureGenerateAlgorithmSecondaryFamilyCustom -->
														<ECUC-STRING-PARAM-DEF UUID="5ff7ce61-5c6a-469d-9945-b21783480a5f">
															<SHORT-NAME>CsmSignatureGenerateAlgorithmSecondaryFamilyCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Name of the custom secondary algorithm family used for the crypto service. This is the second name of the custom algorithm family, if CRYPTO_ALGOFAM_CUSTOM is set as CsmSignatureGenerateAlgorithmSecondaryFamily.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmSignatureGenerateDataMaxLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="bd934cfe-20e9-4a41-a8b9-b03f4c3f7404">
															<SHORT-NAME>CsmSignatureGenerateDataMaxLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Size of the input data length in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmSignatureGenerateKeyLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="3768ba31-9eef-4607-a5df-1784aecf4b07">
															<SHORT-NAME>CsmSignatureGenerateKeyLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Size of the signature generate key in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmSignatureGenerateProcessing -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="d803212c-5e03-45d5-87b5-5ee154a69c97">
															<SHORT-NAME>CsmSignatureGenerateProcessing</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines how the interface shall be used for that primitive. Synchronous processing returns with the result while asynchronous processing returns without processing the job. The caller will be notified by the corresponding callback</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="98289efd-ba74-488b-8bde-1db7ee75f75c">
																	<SHORT-NAME>CSM_ASYNCHRONOUS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="a11990a7-4752-4c9f-92ad-b061d319bf0c">
																	<SHORT-NAME>CSM_SYNCHRONOUS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmSignatureGenerateResultLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="7d370d85-987d-47ba-937b-629c2b08efd3">
															<SHORT-NAME>CsmSignatureGenerateResultLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Size of the output signature length in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: CsmSignatureVerify -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="86ec2b03-ed09-48a8-9b17-0cbc21a0d0be">
											<SHORT-NAME>CsmSignatureVerify</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configurations of SignatureVerify primitives</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<SUB-CONTAINERS>
												<!-- Container Definition: CsmSignatureVerifyConfig -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="619a235d-53d8-47ce-b976-2abfbc4886d4">
													<SHORT-NAME>CsmSignatureVerifyConfig</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container for configuration of a CSM signature verification interface. The container name serves as a symbolic name for the identifier of signature verification interface.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CsmSignatureVerifyAlgorithmFamiliy -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="5170fc84-26a0-4b6e-b180-4e9c9fcb0d39">
															<SHORT-NAME>CsmSignatureVerifyAlgorithmFamiliy</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm family used for the crypto service. This parameter defines the most significant part of the algorithm.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="f55e0b9f-dcf1-4978-8e49-436da3f24f3d">
																	<SHORT-NAME>CRYPTO_ALGOFAM_BRAINPOOL</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="465d1224-b71f-47da-98be-21296e2e3ef2">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="8b058bbf-cbad-4189-ad0b-c8b02149e527">
																	<SHORT-NAME>CRYPTO_ALGOFAM_ECCNIST</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="48b0acae-7f4b-4c68-b228-4ac42e6880e3">
																	<SHORT-NAME>CRYPTO_ALGOFAM_ED25519</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="6f364cf0-4cb4-4037-9bcf-695866967f41">
																	<SHORT-NAME>CRYPTO_ALGOFAM_RSA</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmSignatureVerifyAlgorithmFamilyCustom -->
														<ECUC-STRING-PARAM-DEF UUID="e0646614-cdcc-4655-9cea-e03a8454e7da">
															<SHORT-NAME>CsmSignatureVerifyAlgorithmFamilyCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Name of the custom algorithm family used for the crypto service. This is the name of the custom algorithm family, if CRYPTO_ALGOFAM_CUSTOM is used as CsmSignatureVerifyAlgorithmFamily.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmSignatureVerifyAlgorithmMode -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="3826b457-6bb3-408b-acfd-87dbca97fc65">
															<SHORT-NAME>CsmSignatureVerifyAlgorithmMode</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm mode used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="b4e08e3a-197b-4e91-b5eb-739a088dbb86">
																	<SHORT-NAME>CRYPTO_ALGOMODE_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="0836d855-6498-4837-a6f2-546a4d4ef6fa">
																	<SHORT-NAME>CRYPTO_ALGOMODE_NOT_SET</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="0e68a736-bed7-44c7-80e0-bf798fed818e">
																	<SHORT-NAME>CRYPTO_ALGOMODE_RSASSA_PKCS1_v1_5</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="48324735-fa00-45ae-b16e-109f163c0b6b">
																	<SHORT-NAME>CRYPTO_ALGOMODE_RSASSA_PSS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmSignatureVerifyAlgorithmModeCustom -->
														<ECUC-STRING-PARAM-DEF UUID="b44042b1-bd7f-4c61-952e-a8e050c3c889">
															<SHORT-NAME>CsmSignatureVerifyAlgorithmModeCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Name of the custom algorithm mode used for the crypto service</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmSignatureVerifyAlgorithmSecondaryFamily -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="fc3181fd-48b8-4935-be61-bd186567e81a">
															<SHORT-NAME>CsmSignatureVerifyAlgorithmSecondaryFamily</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines the algorithm family used for the crypto service. This parameter defines the most significant part of the algorithm.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="e884cd89-c035-4b31-9117-69dd77a1c015">
																	<SHORT-NAME>CRYPTO_ALGOFAM_BLAKE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="7e5a2abd-f694-4277-a3fa-626cca04775c">
																	<SHORT-NAME>CRYPTO_ALGOFAM_CUSTOM</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="8a9bf987-2c67-4781-8e35-ce198e4a11c2">
																	<SHORT-NAME>CRYPTO_ALGOFAM_NOT_SET</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="05373e79-1d34-4c3c-a02e-f705f5ac8069">
																	<SHORT-NAME>CRYPTO_ALGOFAM_RIPEMD160</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="e16de22c-7486-4f25-8541-8882842f2905">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA1</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="c1a8ae94-6fef-4f9f-802b-bdb7787f245c">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_224</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="2cd7b567-d398-48d6-a972-d2ee337fac84">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="5a866b66-1a5e-4167-b36e-610efb2363be">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_384</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="54233268-d591-4e98-b564-ba396cec29ee">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_512</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="5627dd71-bf5d-45c8-8fe0-0c85edce9ed5">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_512_224</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="1986f1a8-76dd-4eb4-b7b8-50b5292232a4">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA2_512_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="3bf24719-2cc2-479e-b3cd-207dc1397fee">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_224</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="a1cd65f0-b439-4c94-b6fe-3c11a5bb3587">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="a0a46358-f95f-4e82-a9b0-35893d5495d3">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_384</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="4af04c0a-47d9-4e6c-9b41-6641fb7b9261">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_512</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="9d461430-258e-4285-bb22-96bf1c09fcaf">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_SHAKE128</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="a8b3f883-29aa-4c3a-a8ab-68f5b9af2be1">
																	<SHORT-NAME>CRYPTO_ALGOFAM_SHA3_SHAKE256</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmSignatureVerifyAlgorithmSecondaryFamilyCustom -->
														<ECUC-STRING-PARAM-DEF UUID="081135af-8d85-4042-8587-cfe8564aaf2a">
															<SHORT-NAME>CsmSignatureVerifyAlgorithmSecondaryFamilyCustom</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Name of the custom secondary algorithm family used for the crypto service. This is the name of the custom algorithm family, if CRYPTO_ALGOFAM_CUSTOM is used as CsmSignatureVerifyAlgorithmFamily.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmSignatureVerifyCompareLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="17ac44ee-4818-4575-90e7-4cafcec2d8e6">
															<SHORT-NAME>CsmSignatureVerifyCompareLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Size of the input data length, for whichs signature shall be verified, in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmSignatureVerifyDataMaxLength -->
														<ECUC-INTEGER-PARAM-DEF UUID="7b017ac6-521a-43de-9157-fdbf386df7d7">
															<SHORT-NAME>CsmSignatureVerifyDataMaxLength</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Size of the input data length, for whichs signature shall be verified, in bytes</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CsmSignatureVerifyProcessing -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="4479e8a3-fe1c-4a38-891d-3ee77e67a830">
															<SHORT-NAME>CsmSignatureVerifyProcessing</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Determines how the interface shall be used for that primitive. Synchronous processing returns with the result while asynchronous processing returns without processing the job. The caller will be notified by the corresponding callback.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="de1b102c-81c0-46e6-996f-6f4c8717d3e2">
																	<SHORT-NAME>CSM_ASYNCHRONOUS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="02a0f106-7663-45e5-b84e-3fa20e2cfbbd">
																	<SHORT-NAME>CSM_SYNCHRONOUS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmQueues -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="af440b9f-dd73-4d6c-87de-6cf07bb00884">
									<SHORT-NAME>CsmQueues</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for CSM queue configurations</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmQueue -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="642d787c-395f-4b6e-98c4-85d30705ff95">
											<SHORT-NAME>CsmQueue</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of a CSM queue. The container name serves as a symbolic name for the identifier of a queue configuration.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">A queue has two tasks: 
                                        1. queue jobs which cannot be processed since the underlying hardware is busy and 
                                        2. refer to channel which shall be used</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmQueueSize -->
												<ECUC-INTEGER-PARAM-DEF UUID="a36284d3-53ec-4e83-bd06-6c89f877480e">
													<SHORT-NAME>CsmQueueSize</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Size of the CsmQueue. If jobs cannot be processed by the underlying hardware since the hardware is busy, the jobs stay in the prioritized queue. If the queue is full, the next job will be rejected.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>4294967295</MAX>
													<MIN>1</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Symbolic Name Reference Definition: CsmChannelRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="783ac688-7be8-48ed-871d-8460cc545eec">
													<SHORT-NAME>CsmChannelRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Refers to the underlying Crypto Interface channel.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/CryIf/CryIfChannel</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!--ASR 4.2 Containers-->
								<!-- Container Definition: CsmAsymDecrypt -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="9fb26569-9a0b-4323-9feb-9baa4e40e55a">
									<SHORT-NAME>CsmAsymDecrypt</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of AsymDecrypt primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmAsymDecryptMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="f3a99177-88c1-46d9-a258-85ef728b089c">
											<SHORT-NAME>CsmAsymDecryptMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CRY primitives which implement an asymmetrical decryption.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmAsymDecryptConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="b33dbb5e-eead-4c69-b15d-3c1021300354">
											<SHORT-NAME>CsmAsymDecryptConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service AsymDecrypt. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmAsymDecryptInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="7a8a00ad-e714-496d-9be9-85ac452b58c8">
													<SHORT-NAME>CsmAsymDecryptInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmAsymDecryptPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="82a4db8f-82eb-465c-97ef-6a0ee6e8d0b7">
													<SHORT-NAME>CsmAsymDecryptPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmCallbackAsymDecrypt -->
												<ECUC-FUNCTION-NAME-DEF UUID="0b3ddea5-649a-45f0-b305-afe222d28c58">
													<SHORT-NAME>CsmCallbackAsymDecrypt</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmAsymEncrypt -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="2a67c3c0-6432-49e7-8849-31391d911793">
									<SHORT-NAME>CsmAsymEncrypt</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of AsymEncrypt primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmAsymEncryptMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="0b6ee349-2a6a-4a67-9f9a-6b8158cda111">
											<SHORT-NAME>CsmAsymEncryptMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CRY primitives which implement an asymmetrical encryption.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmAsymEncryptConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="b287df23-6fdf-4e05-9fa0-7e0606d98f75">
											<SHORT-NAME>CsmAsymEncryptConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service AsymEncrypt. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmAsymEncryptInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="e54df844-7e7b-4ae1-89d5-7c55516d032c">
													<SHORT-NAME>CsmAsymEncryptInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmAsymEncryptPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="30bec222-c614-4f64-89e7-6f6fad972277">
													<SHORT-NAME>CsmAsymEncryptPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmCallbackAsymEncrypt -->
												<ECUC-FUNCTION-NAME-DEF UUID="7c0c1644-16fe-4041-b19f-7e86e7f269e5">
													<SHORT-NAME>CsmCallbackAsymEncrypt</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmAsymPrivateKeyExtract -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="c8da83ed-7c91-436a-86bc-f377fe360bac">
									<SHORT-NAME>CsmAsymPrivateKeyExtract</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of AsymPrivateKeyExtract primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmAsymPrivateKeyExtractMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="34727ec9-ff79-4c90-807f-4e04e91afedd">
											<SHORT-NAME>CsmAsymPrivateKeyExtractMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CRY primitives which implement an asymmetrical private key extraction.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmAsymPrivateKeyExtractConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="fdfc95a1-d1c0-47a9-8e22-2fa0920ef5f7">
											<SHORT-NAME>CsmAsymPrivateKeyExtractConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service AsymPrivateKeyExtract.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The container name serves as a symbolic name for the identifier of a service configuration.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmAsymPrivateKeyExtractInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="e953f171-fe5a-4bfa-a772-b99d49187f3e">
													<SHORT-NAME>CsmAsymPrivateKeyExtractInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmAsymPrivateKeyExtractPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="9a7cf245-a086-40f5-979e-00b46308981d">
													<SHORT-NAME>CsmAsymPrivateKeyExtractPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmCallbackAsymPrivateKeyExtract -->
												<ECUC-FUNCTION-NAME-DEF UUID="4413f7a2-6bb4-4d84-b09c-ec164e56326c">
													<SHORT-NAME>CsmCallbackAsymPrivateKeyExtract</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmAsymPrivateKeyWrapAsym -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="85b8b27c-eb6e-4e19-b5bc-e817830be05d">
									<SHORT-NAME>CsmAsymPrivateKeyWrapAsym</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of AsymPrivateKeyWrapSym primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmAsymPrivateKeyWrapAsymMaxPrivKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="5a5d15a5-9481-4c75-83f9-741a7fe84e7c">
											<SHORT-NAME>CsmAsymPrivateKeyWrapAsymMaxPrivKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum length, in bytes, of all private key types used in all CRY primitives which implement an asymmetrical private key wrapping.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CsmAsymPrivateKeyWrapAsymMaxPubKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="5f72fc17-a543-4e42-919c-dfa5ea76f555">
											<SHORT-NAME>CsmAsymPrivateKeyWrapAsymMaxPubKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum length, in bytes, of all public key types used in all CRY primitives which implement an asymmetrical private key wrapping.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmAsymPrivateKeyWrapAsymConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="334df2e0-b37d-4945-b31d-0b6d195de61e">
											<SHORT-NAME>CsmAsymPrivateKeyWrapAsymConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service SymKeyWrapAsym. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmAsymPrivateKeyWrapAsymInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="8cf73cdc-cad2-4791-84c9-c6c1a841500c">
													<SHORT-NAME>CsmAsymPrivateKeyWrapAsymInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmAsymPrivateKeyWrapAsymPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="39040f9e-f52b-44e1-861d-6bdaaab899b9">
													<SHORT-NAME>CsmAsymPrivateKeyWrapAsymPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmCallbackAsymPrivateKeyWrapAsym -->
												<ECUC-FUNCTION-NAME-DEF UUID="1877e760-b6ba-4b6f-95c2-5ab73ac418b4">
													<SHORT-NAME>CsmCallbackAsymPrivateKeyWrapAsym</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmAsymPrivateKeyWrapSym -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="8868b248-5bc2-40ff-9f1f-e1cadaead327">
									<SHORT-NAME>CsmAsymPrivateKeyWrapSym</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of AsymPrivateKeyWrapSym primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmAsymPrivateKeyWrapSymMaxPrivKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="acc76fd2-5f5b-460b-b603-a83921cf3022">
											<SHORT-NAME>CsmAsymPrivateKeyWrapSymMaxPrivKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum length, in bytes, of all private information types used in all CRY primitives which implement an asymetric private key wrapping.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CsmAsymPrivateKeyWrapSymMaxSymKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="49d06f9c-b820-4219-971a-2adc568a370f">
											<SHORT-NAME>CsmAsymPrivateKeyWrapSymMaxSymKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CRY primitives which implement an asymetrical private key wrapping.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmAsymPrivateKeyWrapSymConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="9c201e5c-59e1-4c2c-9b8e-a27745041a1a">
											<SHORT-NAME>CsmAsymPrivateKeyWrapSymConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service AsymPrivateKeyWrapSym. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmAsymPrivateKeyWrapSymInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="52aaef53-bdcb-4b4b-bf3f-f670bb36f036">
													<SHORT-NAME>CsmAsymPrivateKeyWrapSymInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmAsymPrivateKeyWrapSymPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="7550c651-8e5d-44c1-af55-0c700c2f71ff">
													<SHORT-NAME>CsmAsymPrivateKeyWrapSymPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmCallbackAsymPrivateKeyWrapSym -->
												<ECUC-FUNCTION-NAME-DEF UUID="71af6d12-3e82-40d5-95b3-c47b7fcdb2e9">
													<SHORT-NAME>CsmCallbackAsymPrivateKeyWrapSym</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmAsymPublicKeyExtract -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="329fae2f-917f-41c5-ace8-e095bf5c678c">
									<SHORT-NAME>CsmAsymPublicKeyExtract</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of AsymPublicKeyExtract primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmAsymPublicKeyExtractMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="f9f03f1e-8197-431d-9ace-263e82f0f4c6">
											<SHORT-NAME>CsmAsymPublicKeyExtractMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CRY primitives which implement an asymmetrical public key extraction.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmAsymPublicKeyExtractConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ac2288e0-4851-4481-b126-5a33295f3da6">
											<SHORT-NAME>CsmAsymPublicKeyExtractConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service AsymPublicKeyExtract.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The container name serves as a symbolic name for the identifier of a service configuration.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmAsymPublicKeyExtractInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="fbe5d199-c287-427c-a8ff-baa9db0ce995">
													<SHORT-NAME>CsmAsymPublicKeyExtractInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmAsymPublicKeyExtractPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ccee8c30-6adc-4899-833c-66114acc3b00">
													<SHORT-NAME>CsmAsymPublicKeyExtractPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmCallbackAsymPublicKeyExtract -->
												<ECUC-FUNCTION-NAME-DEF UUID="bd995c50-095c-46e1-83b4-61ac3f5f49ed">
													<SHORT-NAME>CsmCallbackAsymPublicKeyExtract</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmChecksum -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="0716dc66-c745-4159-89e9-47ed37ed7e29">
									<SHORT-NAME>CsmChecksum</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of Checksum primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmChecksumConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="1a5f8a95-da00-41ba-aa41-0672bfe530b0">
											<SHORT-NAME>CsmChecksumConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service Checksum. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmCallbackChecksum -->
												<ECUC-FUNCTION-NAME-DEF UUID="5892ddc8-24bc-4547-8962-3942944d0d03">
													<SHORT-NAME>CsmCallbackChecksum</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: CsmChecksumInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="e6489ae3-2a12-4220-af68-f299d95ed8a4">
													<SHORT-NAME>CsmChecksumInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmChecksumPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="bbfddf01-1d80-4782-9238-906a2a44d6f0">
													<SHORT-NAME>CsmChecksumPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmDemEventParameterRefs -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="6fadaebb-8cae-40ce-8d05-7861d1f532de">
									<SHORT-NAME>CsmDemEventParameterRefs</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for the references to DemEventParameter elements which shall be invoked using the API Dem_ReportErrorStatus API in case the corresponding error occurs. The EventId is taken from the referenced DemEventParameter's DemEventId value. The standardized errors are provided in the container and can be extended by vendor specific error references.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<REFERENCES>
										<!-- Symbolic Name Reference Definition: CSM_E_INIT_FAILED -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="99865f68-093b-4d38-8a64-527b7a7ceee4">
											<SHORT-NAME>CSM_E_INIT_FAILED</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to the DemEventParameter which shall be issued when the error "Initialization of CSM module failed" has occured.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmHash -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="fe68712a-e9a3-4fe5-b2d5-038964094a84">
									<SHORT-NAME>CsmHash</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of Hash primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmHashConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ca3af9e5-dfee-488e-875f-482361bf96c0">
											<SHORT-NAME>CsmHashConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configurations for the Hash service. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmCallbackHash -->
												<ECUC-FUNCTION-NAME-DEF UUID="027f32fc-55af-4872-84d4-f89dab4c3888">
													<SHORT-NAME>CsmCallbackHash</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: CsmHashInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="1b548c2f-d7f9-4296-8053-0e0b4b9c978f">
													<SHORT-NAME>CsmHashInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmHashPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="c3f688e4-a216-4062-a17d-0a638dd90c9e">
													<SHORT-NAME>CsmHashPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmKeyDerive -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="64cfbc1d-c891-40c8-bd2b-60a0f1b6d055">
									<SHORT-NAME>CsmKeyDerive</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of KeyDerive primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmKeyDeriveMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ed7c2f40-6fad-4aa1-9944-93ed4bc1a29e">
											<SHORT-NAME>CsmKeyDeriveMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CRY primitives which implement a key derivation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmKeyDeriveConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="90d3ed6f-a29b-4439-b696-0f9cbb846074">
											<SHORT-NAME>CsmKeyDeriveConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service KeyDerive. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmCallbackKeyDerive -->
												<ECUC-FUNCTION-NAME-DEF UUID="8ba6c251-5ff6-4d4e-a6c2-dea5e0e2af19">
													<SHORT-NAME>CsmCallbackKeyDerive</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: CsmKeyDeriveInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="7b4c7407-1d2a-4553-b68c-f5ed6b2388b9">
													<SHORT-NAME>CsmKeyDeriveInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmKeyDerivePrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="493634e1-9fe3-477a-8847-75ea09f31294">
													<SHORT-NAME>CsmKeyDerivePrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmKeyDeriveSymKey -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="940e2278-429d-4411-a34a-fc0a579cb369">
									<SHORT-NAME>CsmKeyDeriveSymKey</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of CsmKeyDeriveSymKey primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmKeyDeriveSymKeyMaxSymKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="f60b427e-ab2f-4c31-8e8d-91c744d3c54f">
											<SHORT-NAME>CsmKeyDeriveSymKeyMaxSymKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CRY primitives which implement a key derivation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmKeyDeriveSymKeyConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="8156e10f-45ec-4e49-8e50-1a2f6e9cb584">
											<SHORT-NAME>CsmKeyDeriveSymKeyConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service CsmKeyDeriveSymKey. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmCallbackKeyDeriveSymKey -->
												<ECUC-FUNCTION-NAME-DEF UUID="3cc11cbb-9467-4463-a3e0-0cf311917b0e">
													<SHORT-NAME>CsmCallbackKeyDeriveSymKey</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: CsmKeyDeriveSymKeyInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ed8acad4-bb43-4b2b-ad62-91cb8bbb6b5c">
													<SHORT-NAME>CsmKeyDeriveSymKeyInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmKeyDeriveSymKeyPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="e5f768e9-6ab9-4ce2-9149-07bd31e14cf7">
													<SHORT-NAME>CsmKeyDeriveSymKeyPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmKeyExchangeCalcPubVal -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="a5f46697-dc17-4421-b949-d842c3d93fbc">
									<SHORT-NAME>CsmKeyExchangeCalcPubVal</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of KeyExchangeCalcPubVal primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmKeyExchangeCalcPubValMaxBaseTypeSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="d0739dea-c57f-4490-9b15-e105cc7ffd8d">
											<SHORT-NAME>CsmKeyExchangeCalcPubValMaxBaseTypeSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum length, in bytes, of all base types used in all CRY primitives which implement a public value calculation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CsmKeyExchangeCalcPubValMaxPrivateTypeSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="a3768e3f-b8f7-4d38-b34d-15c9ea2628b3">
											<SHORT-NAME>CsmKeyExchangeCalcPubValMaxPrivateTypeSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum length, in bytes, of all private information types used in all CRY primitives which implement a public value calculation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmKeyExchangeCalcPubValConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="38d047ba-f86d-4bc6-b834-622bc4410a35">
											<SHORT-NAME>CsmKeyExchangeCalcPubValConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service KeyExchangeCalcPubVal. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmCallbackKeyExchangeCalcPubVal -->
												<ECUC-FUNCTION-NAME-DEF UUID="1b59b103-cb9e-4954-a8be-a4fd774578a5">
													<SHORT-NAME>CsmCallbackKeyExchangeCalcPubVal</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: CsmKeyExchangeCalcPubValInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="57768c8e-8bb5-4283-9776-c8dd587123ce">
													<SHORT-NAME>CsmKeyExchangeCalcPubValInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmKeyExchangeCalcPubValPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="6ccd203c-adda-4c3e-8ddf-31d3ddfebe93">
													<SHORT-NAME>CsmKeyExchangeCalcPubValPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmKeyExchangeCalcSecret -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="3981ef51-dbab-45f1-81c6-92db63817b59">
									<SHORT-NAME>CsmKeyExchangeCalcSecret</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of KeyExchangeCalcSecret primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmKeyExchangeCalcSecretMaxBaseTypeSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="55179fac-5030-45c4-ac87-77914a7c395a">
											<SHORT-NAME>CsmKeyExchangeCalcSecretMaxBaseTypeSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum length, in bytes, of all base types used in all CRY primitives which implement a shared secret calculation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CsmKeyExchangeCalcSecretMaxPrivateTypeSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="e7e0d8de-70c6-49ff-9432-470d4170057d">
											<SHORT-NAME>CsmKeyExchangeCalcSecretMaxPrivateTypeSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum length, in bytes, of all private information types used in all CRY primitives which implement a shared secret calculation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmKeyExchangeCalcSecretConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="b4006496-c761-40f7-9f5a-432ca3804da1">
											<SHORT-NAME>CsmKeyExchangeCalcSecretConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service KeyExchangeCalcSecret. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmCallbackKeyExchangeCalcSecret -->
												<ECUC-FUNCTION-NAME-DEF UUID="9cdb2df2-011a-4bab-ad42-6c17a8dd1d3d">
													<SHORT-NAME>CsmCallbackKeyExchangeCalcSecret</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: CsmKeyExchangeCalcSecretInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="d71f3849-c5c0-4091-a59b-c51a1883cc47">
													<SHORT-NAME>CsmKeyExchangeCalcSecretInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmKeyExchangeCalcSecretPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="e14757bf-753b-4b14-bdd2-e0d3d27d2927">
													<SHORT-NAME>CsmKeyExchangeCalcSecretPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmKeyExchangeCalcSymKey -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="19989517-3625-44b7-8bcd-18b57861f6fd">
									<SHORT-NAME>CsmKeyExchangeCalcSymKey</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of KeyExchangeCalcSymKey primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmKeyExchangeCalcSymKeyMaxBaseTypeSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="068c9295-7105-4289-a895-ad0a38490d7a">
											<SHORT-NAME>CsmKeyExchangeCalcSymKeyMaxBaseTypeSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum length, in bytes, of all base types used in all CRY primitives which implement a symmetrical key calculation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CsmKeyExchangeCalcSymKeyMaxPrivateTypeSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="28d630f0-2720-426c-bcf9-f073e5556168">
											<SHORT-NAME>CsmKeyExchangeCalcSymKeyMaxPrivateTypeSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum length, in bytes, of all private information types used in all CRY primitives which implement a symmetrical key calculation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CsmKeyExchangeCalcSymKeyMaxSymKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="3e4ff5f6-f976-4d5b-8fda-26553baf20fd">
											<SHORT-NAME>CsmKeyExchangeCalcSymKeyMaxSymKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CRY primitives which implement a symmetrical key calculation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmKeyExchangeCalcSymKeyConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="a613fb3d-6e11-4c16-9fb3-8246faf6270f">
											<SHORT-NAME>CsmKeyExchangeCalcSymKeyConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service KeyExchangeCalcSymKey. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmCallbackKeyExchangeCalcSymKey -->
												<ECUC-FUNCTION-NAME-DEF UUID="74ba7a61-2d62-45e1-8d93-f5bea120c772">
													<SHORT-NAME>CsmCallbackKeyExchangeCalcSymKey</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: CsmKeyExchangeCalcSymKeyInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="9cf31fb8-7949-4d4e-af38-5612a258d80f">
													<SHORT-NAME>CsmKeyExchangeCalcSymKeyInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmKeyExchangeCalcSymKeyPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="0e0593b8-57fd-4fd0-b372-a98f3a00a367">
													<SHORT-NAME>CsmKeyExchangeCalcSymKeyPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmMacGenerate -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="3c432015-c246-4abe-8238-1641d59eb474">
									<SHORT-NAME>CsmMacGenerate</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of MacGenerate primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmMacGenerateMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="58f9faa3-823f-4655-a76b-851edd55cb9c">
											<SHORT-NAME>CsmMacGenerateMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CRY primitives which implement a MAC generation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmMacGenerateConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="cf620d4e-2171-4921-b508-01ec6bb907dd">
											<SHORT-NAME>CsmMacGenerateConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configurations for the MacGenerate service. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmCallbackMacGenerate -->
												<ECUC-FUNCTION-NAME-DEF UUID="1503de5f-cd6e-4fe7-bd68-4b3c571956e0">
													<SHORT-NAME>CsmCallbackMacGenerate</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: CsmMacGenerateInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="3eb4a998-3085-4c38-b84c-0b9f9d45edc3">
													<SHORT-NAME>CsmMacGenerateInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmMacGeneratePrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="8f585a42-8205-4e39-b73a-f517bb736c82">
													<SHORT-NAME>CsmMacGeneratePrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmMacVerify -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="dbc936d8-2019-4d9c-a639-5999b7bea4f2">
									<SHORT-NAME>CsmMacVerify</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of MacVerify primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmMacVerifyMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="8eae851b-fc8f-41a3-9c68-03e4d523cf34">
											<SHORT-NAME>CsmMacVerifyMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CRY primitives which implement a MAC verification.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmMacVerifyConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ae4e6e14-f07b-4d0e-b08c-4198eacd203f">
											<SHORT-NAME>CsmMacVerifyConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service MacVerify. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmCallbackMacVerify -->
												<ECUC-FUNCTION-NAME-DEF UUID="5e02e6f1-f24a-423f-945e-d43776dd211c">
													<SHORT-NAME>CsmCallbackMacVerify</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: CsmMacVerifyInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="eabaa3ec-48c8-4ffb-b601-352dcf08c062">
													<SHORT-NAME>CsmMacVerifyInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmMacVerifyPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="1d0f8057-375f-496a-b10f-76980e14d8fd">
													<SHORT-NAME>CsmMacVerifyPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmRandomGenerate -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="8e78ccf7-56b1-4953-b913-97f80c668e6d">
									<SHORT-NAME>CsmRandomGenerate</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of RandomGenerate primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmRandomGenerateConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="78639a0b-120f-4de5-a0fd-23ce47de80ca">
											<SHORT-NAME>CsmRandomGenerateConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service RandomGenerate. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmCallbackRandomGenerate -->
												<ECUC-FUNCTION-NAME-DEF UUID="0081a51f-3e56-426a-92b3-663754e4623b">
													<SHORT-NAME>CsmCallbackRandomGenerate</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: CsmRandomGenerateInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="bc65df2a-9406-45a8-adef-aad99ff63534">
													<SHORT-NAME>CsmRandomGenerateInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmRandomGeneratePrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="02054966-a590-4ea8-b679-84b2ec32d788">
													<SHORT-NAME>CsmRandomGeneratePrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmRandomSeed -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="9dd58103-32ac-4a14-b658-85b037efd2e4">
									<SHORT-NAME>CsmRandomSeed</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of RandomSeed primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmRandomSeedConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="a5f35999-cc53-4c55-9bcf-993da8681bb3">
											<SHORT-NAME>CsmRandomSeedConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service RandomSeed. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmCallbackRandomSeed -->
												<ECUC-FUNCTION-NAME-DEF UUID="23cadc6e-0435-4907-8ed5-e57b2817545e">
													<SHORT-NAME>CsmCallbackRandomSeed</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: CsmRandomSeedInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="565c4e76-6fde-4391-b881-3a49c937107d">
													<SHORT-NAME>CsmRandomSeedInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmRandomSeedPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="85debb31-caae-4057-a42b-bac260b0b35d">
													<SHORT-NAME>CsmRandomSeedPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmSignatureGenerate -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="e531f986-79e9-4f51-8710-e7f0cc87d52f">
									<SHORT-NAME>CsmSignatureGenerate</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of SignatureGenerate primitives</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmSignatureGenerateMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="cbe9989f-f3bf-4f90-b752-85f1ac745cf0">
											<SHORT-NAME>CsmSignatureGenerateMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CRY primitives which implement a signature generation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmSignatureGenerateConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="3101a0ce-7691-4c82-a11e-acffc1e48756">
											<SHORT-NAME>CsmSignatureGenerateConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service SignatureGenerate. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmCallbackSignatureGenerate -->
												<ECUC-FUNCTION-NAME-DEF UUID="c1a4a4d0-a0e8-490b-bd75-1f4e16fee15b">
													<SHORT-NAME>CsmCallbackSignatureGenerate</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: CsmSignatureGenerateInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="25e2ce24-fd48-43ce-b64b-d3a18e4883e5">
													<SHORT-NAME>CsmSignatureGenerateInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmSignatureGeneratePrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="94a03e7e-fa02-4454-81c2-299070d8e033">
													<SHORT-NAME>CsmSignatureGeneratePrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmSignatureVerify -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="87271917-ec0a-4c51-a7f7-824a217e5022">
									<SHORT-NAME>CsmSignatureVerify</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of SignatureVerify primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmSignatureVerifyMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="69732082-a8a1-4a5a-a02a-80105ea29e12">
											<SHORT-NAME>CsmSignatureVerifyMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CRY primitives which implement a signature verification.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmSignatureVerifyConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="6449700e-459b-4b89-8aa6-42df6ed5e0c3">
											<SHORT-NAME>CsmSignatureVerifyConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service SignatureVerify. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmCallbackSignatureVerify -->
												<ECUC-FUNCTION-NAME-DEF UUID="3423d44e-2389-4f6c-a7d0-d1001502930a">
													<SHORT-NAME>CsmCallbackSignatureVerify</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: CsmSignatureVerifyInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="5d34a497-8fc8-4604-945c-94fd18e92a3b">
													<SHORT-NAME>CsmSignatureVerifyInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmSignatureVerifyPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="951fa05f-4464-4c9f-baff-99100c67a6ec">
													<SHORT-NAME>CsmSignatureVerifyPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmSymBlockDecrypt -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="68bb2701-2f8e-4cdd-a619-4bb3ebaa53c5">
									<SHORT-NAME>CsmSymBlockDecrypt</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of SymBlockDecrypt primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmSymBlockDecryptMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="03c35a0d-3de9-46e7-a4f1-7e1977e24962">
											<SHORT-NAME>CsmSymBlockDecryptMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CRY primitives which implement a symmetrical block decryption.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmSymBlockDecryptConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ce937528-e3bf-4cbd-8814-2919dee285b1">
											<SHORT-NAME>CsmSymBlockDecryptConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service SymBlockDecrypt. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmCallbackSymBlockDecrypt -->
												<ECUC-FUNCTION-NAME-DEF UUID="6b63f27f-9a53-47a5-96d5-5f27d4f7e55c">
													<SHORT-NAME>CsmCallbackSymBlockDecrypt</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: CsmSymBlockDecryptInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="a4fae46f-22ef-49d9-8872-34663e382116">
													<SHORT-NAME>CsmSymBlockDecryptInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmSymBlockDecryptPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="6b1de68c-fcc8-4381-8e3e-42756f96a7bb">
													<SHORT-NAME>CsmSymBlockDecryptPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmSymBlockEncrypt -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="87937807-a8da-4416-8f66-91926206e879">
									<SHORT-NAME>CsmSymBlockEncrypt</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of SymBlockEncrypt primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmSymBlockEncryptMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="41ffaf70-23c0-4962-8590-8dac99e957ed">
											<SHORT-NAME>CsmSymBlockEncryptMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CRY primitives which implement a symmetrical block encryption.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmSymBlockEncryptConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="c30fadc7-8c41-41c5-9855-e83f701417de">
											<SHORT-NAME>CsmSymBlockEncryptConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service SymBlockEncrypt. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmCallbackSymBlockEncrypt -->
												<ECUC-FUNCTION-NAME-DEF UUID="ca22d10f-bad5-4786-b849-a78ed0ba8fbe">
													<SHORT-NAME>CsmCallbackSymBlockEncrypt</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: CsmSymBlockEncryptInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="b9084f4c-1860-406f-ad8a-718e0cbd0c8f">
													<SHORT-NAME>CsmSymBlockEncryptInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmSymBlockEncryptPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="d6e97a8b-711a-4db2-936b-3fc539672e81">
													<SHORT-NAME>CsmSymBlockEncryptPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmSymDecrypt -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="c4a05a94-fcef-48df-9a76-a16c74c6cc96">
									<SHORT-NAME>CsmSymDecrypt</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of SymDecrypt primitives</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmSymDecryptMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="0fa8606f-e70b-40fd-b273-bc8b0f6a1ae0">
											<SHORT-NAME>CsmSymDecryptMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CRY primitives which implement a symmetrical decryption.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmSymDecryptConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="69964c3f-4120-4941-bd6b-1c8bd49e6a98">
											<SHORT-NAME>CsmSymDecryptConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service SymDecrypt.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The container name serves as a symbolic name for the identifier of a service configuration.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmCallbackSymDecrypt -->
												<ECUC-FUNCTION-NAME-DEF UUID="09e22343-b2c4-4636-aba4-7d6dc0111b5a">
													<SHORT-NAME>CsmCallbackSymDecrypt</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: CsmSymDecryptInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="fb83e59c-7046-4444-ac49-2a19c5e5d919">
													<SHORT-NAME>CsmSymDecryptInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmSymDecryptPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="a051e25f-f559-445f-9221-1f256ae29e93">
													<SHORT-NAME>CsmSymDecryptPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmSymEncrypt -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="4f9e190b-ca4c-4bd7-b919-ff24a568f590">
									<SHORT-NAME>CsmSymEncrypt</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of SymEncrypt primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmSymEncryptMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="25215674-59db-445e-bc4a-8b46f3ee9ec2">
											<SHORT-NAME>CsmSymEncryptMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CRY primitives which implement a symmetrical encryption.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmSymEncryptConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="25b827ee-c6b9-467f-891f-1de0ce56bea2">
											<SHORT-NAME>CsmSymEncryptConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service SymEncrypt. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmCallbackSymEncrypt -->
												<ECUC-FUNCTION-NAME-DEF UUID="bca4a1e7-7919-4c35-a670-af00244bcf95">
													<SHORT-NAME>CsmCallbackSymEncrypt</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: CsmSymBlockEncryptInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="5a7fcf52-7bef-4fe7-84a7-c4e829107ddc">
													<SHORT-NAME>CsmSymBlockEncryptInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmSymEncryptPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="15db686f-c02e-40d5-a278-27e72467b4fc">
													<SHORT-NAME>CsmSymEncryptPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmSymKeyExtract -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="7efdc48e-4f8a-4b18-b646-fc0ae644c2c1">
									<SHORT-NAME>CsmSymKeyExtract</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of SymKeyExtract primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmSymKeyExtractMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="9dc06c67-1915-4738-adf7-e34e15ec674d">
											<SHORT-NAME>CsmSymKeyExtractMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CRY primitives which implement a symmetrical key extraction.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmSymKeyExtractConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="3abf783f-75f9-4416-b4bf-67fc54913f13">
											<SHORT-NAME>CsmSymKeyExtractConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service SymKeyExtract. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmCallbackSymKeyExtract -->
												<ECUC-FUNCTION-NAME-DEF UUID="24f2af08-3e4f-4dc3-b412-fb28b0424dad">
													<SHORT-NAME>CsmCallbackSymKeyExtract</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: CsmSymKeyExtractInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="c5b2c29c-e6a9-4f91-9149-85df4ea72099">
													<SHORT-NAME>CsmSymKeyExtractInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmSymKeyExtractPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="d2a9320d-af2b-456d-ada2-d50ea6a4c36f">
													<SHORT-NAME>CsmSymKeyExtractPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmSymKeyWrapAsym -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="40f55c00-56df-438b-beee-690dbf458e77">
									<SHORT-NAME>CsmSymKeyWrapAsym</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of SymKeyWrapSym primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmSymKeyWrapAsymMaxPubKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="c880bc92-7d0a-4b11-9d78-b6930492abb3">
											<SHORT-NAME>CsmSymKeyWrapAsymMaxPubKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum length, in bytes, of all public key  types used in all CRY primitives which implement an asymmetrical key wrapping.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CsmSymKeyWrapAsymMaxSymKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="c0d2491f-aeb9-406a-82ba-c3ec33736b0d">
											<SHORT-NAME>CsmSymKeyWrapAsymMaxSymKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CRY primitives which implement an asymmetrical key wrapping.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmSymKeyWrapAsymConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="b97138c7-3d49-4cf1-8ea6-8c0ffafbbb77">
											<SHORT-NAME>CsmSymKeyWrapAsymConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service SymKeyWrapAsym. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmCallbackSymKeyWrapAsym -->
												<ECUC-FUNCTION-NAME-DEF UUID="f865ae55-7e66-4997-b38b-8e8d642b43db">
													<SHORT-NAME>CsmCallbackSymKeyWrapAsym</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: CsmSymKeyWrapAsymInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="cd9b2448-fb3a-47e6-bb2f-122873a61092">
													<SHORT-NAME>CsmSymKeyWrapAsymInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmSymKeyWrapAsymPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="21c67f5e-9140-4a1b-b635-962d827e3835">
													<SHORT-NAME>CsmSymKeyWrapAsymPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CsmSymKeyWrapSym -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="d24e3239-8965-4da6-9b84-38f0f9ca6cba">
									<SHORT-NAME>CsmSymKeyWrapSym</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of SymKeyWrapSym primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CsmSymKeyWrapSymMaxSymKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="201e1b9d-2a0f-4a92-a84a-a42f8d0e8266">
											<SHORT-NAME>CsmSymKeyWrapSymMaxSymKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CRY primitives which implement an symmetrical key wrapping.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CsmSymKeyWrapSymConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="8bb111a5-443d-47ac-965d-8dbd525e5181">
											<SHORT-NAME>CsmSymKeyWrapSymConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service SymKeyWrapSym. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CsmCallbackSymKeyWrapSym -->
												<ECUC-FUNCTION-NAME-DEF UUID="c9c986bf-715e-4295-b5d0-dc46313c616d">
													<SHORT-NAME>CsmCallbackSymKeyWrapSym</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Callback function to be called if service has finished.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: CsmSymKeyWrapSymInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="c448975e-0512-4c13-ad73-0bef3c004ec4">
													<SHORT-NAME>CsmSymKeyWrapSymInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CsmSymKeyWrapSymPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="5f52b113-1146-4f93-ab2b-5e2f22652433">
													<SHORT-NAME>CsmSymKeyWrapSymPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>