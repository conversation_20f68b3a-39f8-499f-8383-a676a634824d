/*******************************************************************************
 * Copyright (c) 2010, 2019 <PERSON> and others.
 *
 * This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License 2.0
 * which accompanies this distribution, and is available at
 * https://www.eclipse.org/legal/epl-2.0/
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 *     <PERSON> <<EMAIL>> - initial API and implementation
 *******************************************************************************/

/*******************************************************************************
 * The following bugs are referred to in this style sheet
 * 1.) Bug 419482 - Cascading policy in CSS
 * 2.) Bug 430052 - Imported rules cannot be overridden
 *******************************************************************************/


/* @import url("platform:/plugin/org.eclipse.ui.themes/css/e4-dark.css"); Bug 430052 */
@import url("platform:/plugin/org.eclipse.ui.themes/css/dark/e4-dark_basestyle.css");
@import url("platform:/plugin/org.eclipse.ui.themes/css/dark/e4-dark_globalstyle.css"); /* Remove this to have ONLY the main IDE shell dark */
@import url("platform:/plugin/org.eclipse.ui.themes/css/dark/e4-dark_partstyle.css");
@import url("platform:/plugin/org.eclipse.ui.themes/css/dark/e4-dark_tabstyle.css");


.MTrimmedWindow.topLevel {
    margin-top: 3px;
    margin-bottom: 3px;
    margin-left: 3px;
    margin-right: 3px;
}

.MPartStack, .MPart {
    font-family: '#org-eclipse-ui-workbench-TAB_TEXT_FONT';
}

.MPart.busy {
    font-style: italic;
}

.MPart.highlighted {
    font-weight: bold;
}

CTabItem,
CTabItem CLabel {
    background-color: '#org-eclipse-ui-workbench-INACTIVE_TAB_BG_END'; /* HACK for background of CTabFolder inner Toolbars */
    color: '#org-eclipse-ui-workbench-INACTIVE_TAB_UNSELECTED_TEXT_COLOR';
}
CTabItem:selected,
CTabItem:selected CLabel {
    color: '#org-eclipse-ui-workbench-INACTIVE_TAB_SELECTED_TEXT_COLOR';
}

.MPartStack.active > CTabItem,
.MPartStack.active > CTabItem CLabel {
    background-color: '#org-eclipse-ui-workbench-ACTIVE_TAB_BG_END'; /* HACK for background of CTabFolder inner Toolbars */
    color: '#org-eclipse-ui-workbench-ACTIVE_TAB_UNSELECTED_TEXT_COLOR';
}
.MPartStack.active > CTabItem:selected,
.MPartStack.active > CTabItem:selected CLabel {
    color: '#org-eclipse-ui-workbench-ACTIVE_TAB_SELECTED_TEXT_COLOR';
}

.MPartStack.active.noFocus > CTabItem:selected {
    color: '#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_SELECTED_TEXT_COLOR';
}

CTabItem.busy {
    color: #888888;
}

#PerspectiveSwitcher {
    eclipse-perspective-keyline-color: #AAB0BF #AAB0BF;
}

.MToolControl.TrimStack {
    /*frame-image:  url(./gtkTSFrame.png);*/
    handle-image:  url(./dragHandle.png);
    /*frame-image-rotated:  url(./gtkTSFrame-rotated.png);*/
    handle-image-rotated:  url(./dragHandle-rotated.png);
    frame-cuts: 5px 1px 5px 16px;
}

.MToolBar.Draggable {
    handle-image:  url(./dragHandle.png);
}

.MToolControl.Draggable {
    handle-image:  url(./dragHandle.png);
}

.DragFeedback {
    background-color: COLOR-WIDGET-NORMAL-SHADOW;
}

.ModifiedDragFeedback {
    background-color: #4176AF;
}

.MTrimmedWindow {
    background-color: #515658;
}

.MTrimBar {
    background-color: #515658;
}

CTabFolder.MArea .MPartStack,CTabFolder.MArea .MPartStack.active {
    swt-shadow-visible: false;
}

.MPartStack.active CTabFolder Canvas {
    background-color: #262626;
    color: #CCC;
}

.MPartStack.active Table {
    background-color: #2F2F2F;
    color: #CCC;
}

.View {
    background-color: #313538;
    color: #F5F5F5;
}


/* ###################### Top Toolbar ########################## */

#org-eclipse-ui-main-toolbar, #PerspectiveSwitcher {
    eclipse-perspective-keyline-color: #585858;
    background-color: #515658 #515658 100%;
    handle-image: none;
    color: #EBE8E4;
}


/* #################### Bottom Status Bar ######################## */

#org-eclipse-ui-StatusLine,
#org-eclipse-ui-ProgressBar,
#org-eclipse-ui-ProgressBar Canvas {
    color: #CCCCCC;
}
#org-eclipse-ui-StatusLine CLabel {
    color: #BDBAB7;
}

StatusLine, ImageBasedFrame{
    color: #BDBAB7;
}

/* ###################### Global Styles ########################## */

/* Use unset to set the foreground/background color to null */

Table[swt-lines-visible=true],
/* the following are required due to Bug 419482 (see <1>): */
Composite > Table[swt-lines-visible=true],
TabFolder > Composite > Table[swt-lines-visible=true],
TabFolder > Composite > * > Table[swt-lines-visible=true],
DocCommentOwnerComposite > Group > Table[swt-lines-visible=true],
TabFolder > Composite > ScrolledComposite > Table[swt-lines-visible=true],
Shell > Composite > Composite > Table[swt-lines-visible=true],
Composite > Composite > Composite > Group > Table[swt-lines-visible=true],
Shell > Composite > Composite > Composite > Table[swt-lines-visible=true],
ScrolledComposite > Composite > Composite > Composite > Table[swt-lines-visible=true],
Shell > Composite > Composite > Composite > Composite > Composite > Table[swt-lines-visible=true],
Shell[style~='SWT.RADIO'][style~='SWT.CASCADE'][style~='SWT.SHADOW_ETCHED_IN'][style~='SWT.SHADOW_ETCHED_OUT'][style~='SWT.RESIZE'][style~='SWT.MENU'][style~='SWT.FULL_SELECTION'][style~='SWT.DATE'] > Composite > Table[swt-lines-visible=true],
Shell[style~='SWT.RADIO'][style~='SWT.CASCADE'][style~='SWT.SHADOW_ETCHED_IN'][style~='SWT.SHADOW_ETCHED_OUT'][style~='SWT.RESIZE'][style~='SWT.MENU'][style~='SWT.FULL_SELECTION'][style~='SWT.DATE'] > Composite > * > Table[swt-lines-visible=true] {
    background-color: unset;
}

Tree[swt-lines-visible=true],
RegistryFilteredTree[swt-lines-visible=true],
/* the following are required due to Bug 419482 (see <1>): */
Composite > Tree[swt-lines-visible=true],
TabFolder > Composite > Tree[swt-lines-visible=true],
TabFolder > Composite > * > Tree[swt-lines-visible=true],
DocCommentOwnerComposite > Group > Tree[swt-lines-visible=true],
TabFolder > Composite > ScrolledComposite > Tree[swt-lines-visible=true],
Shell > Composite > Composite > Tree[swt-lines-visible=true],
Composite > Composite > Composite > Group > Tree[swt-lines-visible=true],
Shell > Composite > Composite > Composite > Tree[swt-lines-visible=true],
ScrolledComposite > Composite > Composite > Composite > Tree[swt-lines-visible=true],
Shell > Composite > Composite > Composite > Composite > Composite > Tree[swt-lines-visible=true],
Shell[style~='SWT.RADIO'][style~='SWT.CASCADE'][style~='SWT.SHADOW_ETCHED_IN'][style~='SWT.SHADOW_ETCHED_OUT'][style~='SWT.RESIZE'][style~='SWT.MENU'][style~='SWT.FULL_SELECTION'][style~='SWT.DATE'] > Composite > Tree[swt-lines-visible=true],
Shell[style~='SWT.RADIO'][style~='SWT.CASCADE'][style~='SWT.SHADOW_ETCHED_IN'][style~='SWT.SHADOW_ETCHED_OUT'][style~='SWT.RESIZE'][style~='SWT.MENU'][style~='SWT.FULL_SELECTION'][style~='SWT.DATE'] > Composite > * > Tree[swt-lines-visible=true] {
    background-color: unset;
}

Button,
/* the following are required due to Bug 419482: */
Composite > Button,
TabFolder > Composite > Button,
TabFolder > Composite > * > Button,
DocCommentOwnerComposite > Group > Button,
TabFolder > Composite > ScrolledComposite > Button,
Shell > Composite > Composite > Button,
Composite > Composite > Composite > Group > Button,
Shell > Composite > Composite > Composite > Button,
ScrolledComposite > Composite > Composite > Composite > Button,
Shell > Composite > Composite > Composite > Composite > Composite > Button,
Shell[style~='SWT.RADIO'][style~='SWT.CASCADE'][style~='SWT.SHADOW_ETCHED_IN'][style~='SWT.SHADOW_ETCHED_OUT'][style~='SWT.RESIZE'][style~='SWT.MENU'][style~='SWT.FULL_SELECTION'][style~='SWT.DATE'] > Composite > Button,
Shell[style~='SWT.RADIO'][style~='SWT.CASCADE'][style~='SWT.SHADOW_ETCHED_IN'][style~='SWT.SHADOW_ETCHED_OUT'][style~='SWT.RESIZE'][style~='SWT.MENU'][style~='SWT.FULL_SELECTION'][style~='SWT.DATE'] > Composite > * > Button,
Form > LayoutComposite > LayoutComposite > Button,
.MPart Button,
.MPartStack.active .MPart Button,
.MPart LayoutComposite > * > LayoutComposite > Section > LayoutComposite > Button,
.MPartStack.active .MPart LayoutComposite > * > LayoutComposite > Section > LayoutComposite > Button {
    background-color: unset;
    color: unset;
}

