<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: StbM -->
						<ECUC-MODULE-DEF UUID="ECUC:506d0671-efed-4c60-bbf4-1a8c714a49ce">
							<SHORT-NAME>StbM</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the Synchronized Time-base Manager (StbM) module.</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.2.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
										<DATE>2011-11-09</DATE>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: StbMDemEventParameterRefs -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:4e91aaba-6482-4ff6-9b4a-5d3dcdb8b212">
									<SHORT-NAME>StbMDemEventParameterRefs</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for the references to DemEventParameter elements which shall be invoked using the API Dem_ReportErrorStatus API in case the corresponding error occurs. The EventId is taken from the referenced DemEventParameter&apos;s DemEventId value. The standardized errors are provided in the container and can be extended by vendor specific error references.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<REFERENCES>
										<!-- Symbolic Name Reference Definition: STBM_E_INIT_FAILED -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:67c11e8b-b684-48fe-b486-ce66edff1d63">
											<SHORT-NAME>STBM_E_INIT_FAILED</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to the DemEventParameter which shall be issued when the error &quot;StbM initialization failed&quot; has occured.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
										<!-- Symbolic Name Reference Definition: STBM_E_INTEGRITY_FAILED -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:45e96b1f-dad2-454a-9167-d23d288f8e39">
											<SHORT-NAME>STBM_E_INTEGRITY_FAILED</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to the DemEventParameter which shall be issued when the error &quot;API request integrity failed&quot; has occured.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
										<!-- Symbolic Name Reference Definition: STBM_E_REQ_FAILED -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:2104c443-c866-40d3-97d3-40c2daf55b60">
											<SHORT-NAME>STBM_E_REQ_FAILED</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to the DemEventParameter which shall be issued when the error &quot;API request failed&quot; has occured.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: StbMGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e3062823-830e-401e-ab3f-102908ca5111">
									<SHORT-NAME>StbMGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container holds the general parameters of the Synchronized Time-base Manager</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: StbMAbsoluteTimeApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:b01aba8a-dd01-49f2-82f2-b266a7909b4e">
											<SHORT-NAME>StbMAbsoluteTimeApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables/Disables the StbM_GetAbsoluteTime API.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: StbMDevErrorDetect -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:7a01b1fa-4807-49ad-84ba-************">
											<SHORT-NAME>StbMDevErrorDetect</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Switch for enabling the development error detection.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: StbMTickTypeRange -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:d2a34989-1c6b-4ba2-8a76-3ca07b924f2f">
											<SHORT-NAME>StbMTickTypeRange</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Defines the upper range of the type &quot;StbM_TickType&quot;.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: StbMVersionInfo -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:16c0bfdf-bfa8-4261-b458-2efa197b30d0">
											<SHORT-NAME>StbMVersionInfo</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Activate/Deactivate the version information API (StbM_GetVersionInfo). True: version information API activated False: version information API deactivated.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: StbMSynchronizedTimeBase -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:0eb5422e-e6ba-4303-8c41-1f56ad5d2f52">
									<SHORT-NAME>StbMSynchronizedTimeBase</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Synchronized time.base collects the information about a specific time-base provider within the system.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: StbMGlobalTimeProviderCallout -->
										<ECUC-FUNCTION-NAME-DEF UUID="ECUC:10f0fea7-0cd4-4d0c-ac63-939652db3a4b">
											<SHORT-NAME>StbMGlobalTimeProviderCallout</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Entry address of the time-base specific callout routine which shall be invoked by the StbM for gathering the current time-base value.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">In case the synchronized time-base is derived from the local time, this container can be omitted. For this case, the StbM achieves the current time-base value by calling the OS interface &quot;GetCounterValue&quot; with the respective OSCounter configured via the ECUC param &quot;StbMLocalTimeRef&quot;.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
										<!-- PARAMETER DEFINITION: StbMSyncStateProviderCallout -->
										<ECUC-FUNCTION-NAME-DEF UUID="ECUC:cae3a69b-05bb-4cbb-9e59-5cc7c2f272ca">
											<SHORT-NAME>StbMSyncStateProviderCallout</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Entry address of the time-base specific callout routine which shall be invoked by the StbM for gathering the current time-base status.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">In case the synchronized time-base is derived from the local time, this container can be omitted. For this case, the state is always &quot;STBM_STATE_SYNC&quot;.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
										<!-- PARAMETER DEFINITION: StbMSynchronizedTimeBaseIdentifier -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:3f37a751-19db-46c2-8fe8-727675c83547">
											<SHORT-NAME>StbMSynchronizedTimeBaseIdentifier</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Identification of a synchronized time-base via a unique identifier.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: StbMTickDurationProviderCallout -->
										<ECUC-FUNCTION-NAME-DEF UUID="ECUC:e82e1513-825a-444b-8111-bc0cf61715ad">
											<SHORT-NAME>StbMTickDurationProviderCallout</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Entry address of the time-base specific callout routine which shall be invoked by the StbM for gathering the current time-base tick duration.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">In case the synchronized time-base is derived from the local time, this container can be omitted. For this case, the StbM achieves the tick duration by configuring an alarm which uses the HW counter configured via the ECUC param &quot;StbMLocalTimeRef&quot;. Note: The tick duration of the local time will not change during runtime.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Foreign Reference Definition: StbMFlexRayClusterRef -->
										<ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:fe2acf49-59c8-452b-8a79-c77027ae248e">
											<SHORT-NAME>StbMFlexRayClusterRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Optional reference to the FlexRay cluster.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<DESTINATION-TYPE>FLEXRAY-CLUSTER</DESTINATION-TYPE>
										</ECUC-FOREIGN-REFERENCE-DEF>
										<!-- Foreign Reference Definition: StbMTtcanClusterRef -->
										<ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:0de45989-4c3e-4f5b-8e8f-499da82b05ff">
											<SHORT-NAME>StbMTtcanClusterRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Optional reference to the Ttcan cluster.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<DESTINATION-TYPE>TTCAN-CLUSTER</DESTINATION-TYPE>
										</ECUC-FOREIGN-REFERENCE-DEF>
										<!-- Reference Definition: StbMLocalTimeRef -->
										<ECUC-REFERENCE-DEF UUID="ECUC:9049f989-691f-409a-906d-e3ddffe934fa">
											<SHORT-NAME>StbMLocalTimeRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Optional sub container in case a local time shall be accessed.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">In case this subcontainer is used, the destinated OS counter has to be configured properly, meaning:
                                        - the counter is directly driven by a HW timer 
                                        - the counter&apos;s OsCounterTicksPerBase set to one tick in ms.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsCounter</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: StbMTriggeredCustomer -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:19e45c71-41c9-4253-a241-102f47f74b40">
									<SHORT-NAME>StbMTriggeredCustomer</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The triggered customer is directliy triggered by the Synchronized Time-base Manager by getting synchronized with the current (global) definition of time and passage of time.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: StbMGlobalTimeCustomerCallback -->
										<ECUC-FUNCTION-NAME-DEF UUID="ECUC:f0cb5415-8f6b-46a2-853a-10e02454e451">
											<SHORT-NAME>StbMGlobalTimeCustomerCallback</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Entry address of the customer specific call-back routine which shall be invoked by the StbM periodically for time value propagation.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">This configuration is only valid if the explicit OS ScheduleTable is NOT defined as triggered customer (via the reference &quot;StbMOSScheduleTableRef&quot;).</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
										<!-- PARAMETER DEFINITION: StbMSyncStateCustomerCallback -->
										<ECUC-FUNCTION-NAME-DEF UUID="ECUC:c1011a54-219e-440e-9002-fdf7b0d4c30f">
											<SHORT-NAME>StbMSyncStateCustomerCallback</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Entry address of the customer specific call-back routine which shall be invoked by the StbM when state changes occur.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">This configuration is only valid if the explicit OS ScheduleTable is NOT defined as triggered customer (via the reference &quot;StbMOSScheduleTableRef&quot;).</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
										<!-- PARAMETER DEFINITION: StbMTriggerInSyncState -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:bcefeab6-eb5c-45f9-a320-c42e7ec1391d">
											<SHORT-NAME>StbMTriggerInSyncState</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Activate/Deactivate the triggering of the customer in case the related time-base (StbmSynchronizedTimeBaseRef) is not synchronized.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">True: the customer will only be triggered when the related time-base is synchronized.
                                        False: the customer will be triggered with the local time-base when no synchronization for the related time-base is established.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: StbMTriggeredCustomerPeriod -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:66300845-e1f9-4af6-be9e-6bd32cd2e858">
											<SHORT-NAME>StbMTriggeredCustomerPeriod</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The triggering period of the triggered customer, called by the StbM_MainFunction.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The period is documented in microseconds.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Reference Definition: StbMOSScheduleTableRef -->
										<ECUC-REFERENCE-DEF UUID="ECUC:489979ba-a412-4da4-a60c-363cf7aec241">
											<SHORT-NAME>StbMOSScheduleTableRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Optional reference to synchronized OS ScheduleTables.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">This configuration is only valid if the triggered customer shall be an OS ScheduleTable. In this case, the OS ScheduleTable will be explicitely synchronized by the StbM.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsScheduleTable</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<!-- Symbolic Name Reference Definition: StbMSynchronizedTimeBaseRef -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:265babaa-6c72-4d8f-b6fe-f4cd7ac08b90">
											<SHORT-NAME>StbMSynchronizedTimeBaseRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Mandatory reference to the required synchronized time-base.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/StbM/StbMSynchronizedTimeBase</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
