<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-3-0.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: Det -->
						<ECUC-MODULE-DEF UUID="ECUC:6acf214a-da9c-4b50-85cd-59b92e6636e6">
							<SHORT-NAME>Det</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Det configuration includes the functions to be called at notification. On one</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.3.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<INTRODUCTION>
								<P>
									<L-1 L="EN">side the application functions are specified and in general it can be decided
                        whether Dlt shall be called at each call of Det.</L-1>
								</P>
							</INTRODUCTION>
							<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_Det_00001</RELATED-TRACE-ITEM-REF>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<POST-BUILD-VARIANT-SUPPORT>false</POST-BUILD-VARIANT-SUPPORT>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: DetConfigSet -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:5dd819cd-a8a0-4190-8c6a-8e1a29607869">
									<SHORT-NAME>DetConfigSet</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Configuration set container for Det.</L-2>
									</DESC>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_Det_00007</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SUB-CONTAINERS>
										<!-- Container Definition: DetModule -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:0b7d9c3e-7690-4cef-b6b1-394862e748f7">
											<SHORT-NAME>DetModule</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container describes a non BSW module that is using the Det via Service Interface.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_Det_00008</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: DetModuleId -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:90426035-5ea7-46d9-a899-026b1b5cb19c">
													<SHORT-NAME>DetModuleId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Unique identifier of the error reporting component. When reporting errors to the DET, a symbolic name derived from the moduleID has to be used to identify the reporter.</L-2>
													</DESC>
													<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_Det_00009</RELATED-TRACE-ITEM-REF>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<MAX>65535</MAX>
													<MIN>4096</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<SUB-CONTAINERS>
												<!-- Container Definition: DetModuleInstance -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:8e0d687f-fd94-48cd-b975-d661274fb333">
													<SHORT-NAME>DetModuleInstance</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Describes the Instance used for the according Service Port.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">It shall be used to differentiate software component instances when multiple instantiation is used.</L-1>
														</P>
													</INTRODUCTION>
													<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_Det_00013</RELATED-TRACE-ITEM-REF>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<POST-BUILD-VARIANT-MULTIPLICITY>true</POST-BUILD-VARIANT-MULTIPLICITY>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: DetInstanceId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:86c27104-85da-43ea-a8be-427590bfc8c3">
															<SHORT-NAME>DetInstanceId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Describes the InstanceId used for the according Service Port.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">It shall be used to differentiate software component instances when multiple instantiation is used.

                                                        Else it shall be set to 0.</L-1>
																</P>
															</INTRODUCTION>
															<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_Det_00012</RELATED-TRACE-ITEM-REF>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<SCOPE>LOCAL</SCOPE>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>0</DEFAULT-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: DetGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:b7be2bf7-601e-4026-ac3e-a373d11757e9">
									<SHORT-NAME>DetGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Generic configuration parameters of the Det module.</L-2>
									</DESC>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_Det_00002</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: DetForwardToDlt -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:b2357d09-e516-4d39-9ffc-ea9d879036cd">
											<SHORT-NAME>DetForwardToDlt</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Only if the parameter is present and set to true, the Det requires the Dlt interface and forwards it's call to the function Dlt_DetForwardErrorTrace. In this case the optional interface to Dlt_Det is required.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_Det_00006</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: DetReportRuntimeErrorCallout -->
										<ECUC-FUNCTION-NAME-DEF UUID="ECUC:2d406ad5-163f-459f-8ad8-75e7b6ec4234">
											<SHORT-NAME>DetReportRuntimeErrorCallout</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the existence and the names of callout functions for the corresponding runtime error handler.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The type of these functions shall be identical the type of Det_ReportRuntimeError itself: Std_ReturnType (*f)(uint16, uint8, uint8, uint8)</L-1>
												</P>
											</INTRODUCTION>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_Det_00010</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
										<!-- PARAMETER DEFINITION: DetReportTransientFaultCallout -->
										<ECUC-FUNCTION-NAME-DEF UUID="ECUC:ca0af0d8-ebf1-42e4-85b2-fccf8c24acf2">
											<SHORT-NAME>DetReportTransientFaultCallout</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the existence and the names of callout functions for the corresponding transient fault handler.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The type of these functions shall be identical the type of Det_ReportTransientFault itself: Std_ReturnType (*f)(uint16, uint8, uint8, uint8)</L-1>
												</P>
											</INTRODUCTION>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_Det_00011</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
										<!-- PARAMETER DEFINITION: DetVersionInfoApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:e159322f-adc7-4d76-a86c-f70943ef81d7">
											<SHORT-NAME>DetVersionInfoApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Pre-processor switch to enable / disable the API to read out the modules version information.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: Version info API enabled.
                                        false: Version info API disabled.</L-1>
												</P>
											</INTRODUCTION>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_Det_00003</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: DetNotification -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:1f580088-5563-4447-93e8-216411efb4c8">
									<SHORT-NAME>DetNotification</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Configuration of the notification functions.</L-2>
									</DESC>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_Det_00004</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: DetErrorHook -->
										<ECUC-FUNCTION-NAME-DEF UUID="ECUC:d84b34e6-1a69-4388-9f95-60c7bc06a711">
											<SHORT-NAME>DetErrorHook</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Optional list of functions to be called by the Default Error Tracer in context of each call of Det_ReportError.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The type of these functions shall be identical the type of Det_ReportError itself: Std_ReturnType (*f)(uint16, uint8, uint8, uint8).</L-1>
												</P>
											</INTRODUCTION>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_Det_00005</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
