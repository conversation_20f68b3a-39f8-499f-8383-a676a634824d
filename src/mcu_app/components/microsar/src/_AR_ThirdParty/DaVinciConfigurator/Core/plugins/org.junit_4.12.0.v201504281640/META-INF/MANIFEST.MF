Manifest-Version: 1.0
Bundle-SymbolicName: org.junit
Export-Package: junit.extensions;version="4.12.0",junit.framework;vers
 ion="4.12.0",junit.runner;version="4.12.0",junit.textui;version="4.12
 .0",org.junit;version="4.12.0",org.junit.experimental;version="4.12.0
 ",org.junit.experimental.categories;version="4.12.0",org.junit.experi
 mental.max;version="4.12.0",org.junit.experimental.results;version="4
 .12.0",org.junit.experimental.runners;version="4.12.0",org.junit.expe
 rimental.theories;version="4.12.0",org.junit.experimental.theories.in
 ternal;version="4.12.0";x-internal:=true,org.junit.experimental.theor
 ies.suppliers;version="4.12.0",org.junit.internal;version="4.12.0";x-
 internal:=true,org.junit.internal.builders;version="4.12.0";x-interna
 l:=true,org.junit.internal.matchers;version="4.12.0";x-internal:=true
 ,org.junit.internal.requests;version="4.12.0";x-internal:=true,org.ju
 nit.internal.runners;version="4.12.0";x-internal:=true,org.junit.inte
 rnal.runners.model;version="4.12.0";x-internal:=true,org.junit.intern
 al.runners.rules;version="4.12.0";x-internal:=true,org.junit.internal
 .runners.statements;version="4.12.0";x-internal:=true,org.junit.match
 ers;version="4.12.0",org.junit.rules;version="4.12.0",org.junit.runne
 r;version="4.12.0",org.junit.runner.manipulation;version="4.12.0",org
 .junit.runner.notification;version="4.12.0",org.junit.runners;version
 ="4.12.0",org.junit.runners.model;version="4.12.0",org.junit.runners.
 parameterized;version="4.12.0",org.junit.validator;version="4.12.0"
Bundle-Name: %pluginName
Bundle-Version: 4.12.0.v201504281640
Bundle-ClassPath: junit.jar
Bundle-Localization: plugin
Require-Bundle: org.hamcrest.core;bundle-version="1.3.0";visibility:=r
 eexport
Bundle-ManifestVersion: 2
Bundle-RequiredExecutionEnvironment: J2SE-1.5
Eclipse-SourceReferences: scm:cvs:pserver:dev.eclipse.org:/cvsroot/too
 ls:org.eclipse.orbit/org.junit;tag=v201504281640
Bundle-Vendor: %providerName

Name: about.html
SHA-256-Digest: hNJmGk+KVcw/Twz/+7lvSlruGHcfYimuLyb9Zc+6dVk=

Name: plugin.properties
SHA-256-Digest: PT7ATJweCFEkkugtCPOLxeVZKHQpSw2WACxnk3u/4Rw=

Name: junit.jar
SHA-256-Digest: WXIfCAXiI9hLkGd4h9n/Vn3FNNfFAsqQPAwrF/BcEWo=

Name: about_files/cpl-v10.html
SHA-256-Digest: 3eTCtmXIGmCm3sOb61S4mDauBx5VLZNaBIgPnVouWM0=

Name: META-INF/eclipse.inf
SHA-256-Digest: V6QEnvT0q4hXoCuO9z+E0j0ZNZ8e4GOivoyHmpHNAyI=

