<?xml version="1.0" encoding="UTF-8"?>
<feature
      id="com.vector.cfg.gui.feature"
      label="GUI for DaVinci Products"
      version="1.0.0.r97550"
      provider-name="Vector Informatik GmbH">

   <description>
      GUI components for DaVinci Products.
   </description>

   <copyright>
      (c) Copyright Vector Informatik GmbH. All Rights Reserved.
   </copyright>

   <license url="http://www.vector.com">
      Vector Group License Agreement

This software is licensed under the terms and conditions of „Delivery and maintenance of software products” of Vector Group, see www.vector.com
   </license>

   <includes
         id="com.vector.cfg.core.feature"
         version="1.0.0.r97550"/>

   <includes
         id="com.vector.cfg.automation.feature"
         version="1.0.0.r97550"/>

   <plugin
         id="com.vector.cfg.gui.app"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.gui.gen"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.ctrl.grid"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.gui.activity"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.gui.core"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.gui.core.ctrl"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.gui.core.error"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.davinci.ctrl.fw"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.gui.errorlog"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.gui.scripting"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.gen.core.genusage"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.ctrl"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.model.recorder"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.gui.core.image.resource"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.gui.core.image.service"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.gui.graphframework.fw"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

</feature>
