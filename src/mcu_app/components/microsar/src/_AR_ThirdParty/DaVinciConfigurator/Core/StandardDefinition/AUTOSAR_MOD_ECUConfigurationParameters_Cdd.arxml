<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: Cdd -->
						<ECUC-MODULE-DEF UUID="ECUC:1286e7ac-4948-4b92-8227-61d9d064283c">
							<SHORT-NAME>Cdd</SHORT-NAME>
							<DESC>
								<L-2 L="EN">The CDD module describes the minimal requirements that are necessary for the configuration of a CDD with respect to the surrounding standardized BSW modules.</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.2.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
										<DATE>2011-11-09</DATE>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
							<SUPPORTED-CONFIG-VARIANTS/>
							<CONTAINERS>
								<!-- Container Definition: CddComStackContribution -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:6c020b3e-c61f-45e6-988c-8b1dd46692b8">
									<SHORT-NAME>CddComStackContribution</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Contribution of COM Stack modules.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<SUB-CONTAINERS>
										<!-- Container Definition: CddComIfUpperLayerContribution -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:a85003e9-585f-44b6-b499-633eeaa58362">
											<SHORT-NAME>CddComIfUpperLayerContribution</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Parameters that are necessary for the configuration of a Complex Device Driver that serves as the UpperLayer of the Com Interface module.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<SUB-CONTAINERS>
												<!-- Container Definition: CddComIfUpperLayerRxPdu -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:c94191ae-a883-4bb9-ac3e-841fc5f69c09">
													<SHORT-NAME>CddComIfUpperLayerRxPdu</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container specifies Rx PDUs that are exchanged between the CDD and the standardized BSW module.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CddComIfHandleId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:cd9c5fb8-5f36-4b7a-8e90-ee6b8e7ba22c">
															<SHORT-NAME>CddComIfHandleId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">ECU wide unique, symbolic handle for the Pdu.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES/>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: CddComIfPduRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:4a9c0e26-9b38-48f8-8180-5dcee98e0fad">
															<SHORT-NAME>CddComIfPduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the &quot;global&quot; Pdu structure to allow harmonization of handle IDs in the COM-Stack.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES/>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: CddComIfUpperLayerTxPdu -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:ef09dcb2-7fd7-49cc-a931-f39a4eecac1c">
													<SHORT-NAME>CddComIfUpperLayerTxPdu</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container specifies Tx PDUs that are exchanged between the CDD and the standardized BSW module.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CddComIfHandleId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:b807ad28-9b7a-4650-8414-360909b41c1e">
															<SHORT-NAME>CddComIfHandleId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">ECU wide unique, symbolic handle for the Pdu.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES/>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: CddComIfPduRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:5f78db18-cb6f-41b4-84a2-55d58da0cb0c">
															<SHORT-NAME>CddComIfPduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the &quot;global&quot; Pdu structure to allow harmonization of handle IDs in the COM-Stack.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES/>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: CddComMLowerLayerContribution -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:15f24c4f-fbb3-4669-80f3-9b197deb7c62">
											<SHORT-NAME>CddComMLowerLayerContribution</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Parameters that are necessary for the configuration of a Complex Device Driver that serves as the LowerLayer of the Communication Manager module.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<SUB-CONTAINERS>
												<!-- Container Definition: CddComMLowerLayerChannel -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:56004dbf-51bf-4a1c-a027-483d08639255">
													<SHORT-NAME>CddComMLowerLayerChannel</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container contains the network specific parameters.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<REFERENCES>
														<!-- Reference Definition: CddComMLowerLayerChannelRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:5351dd2e-6f89-4c5c-9fc8-4a301d7cdac9">
															<SHORT-NAME>CddComMLowerLayerChannelRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Unique handle to identify one certain network. Reference to one of the network handles configured for the ComM.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES/>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/ComM/ComMConfigSet/ComMChannel</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: CddGenericNmLowerLayerContribution -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:d97e1043-dd9e-4703-a719-607ce792be19">
											<SHORT-NAME>CddGenericNmLowerLayerContribution</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Parameters that are necessary for the configuration of a Complex Device Driver that serves as the LowerLayer of the Generic NM module.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<SUB-CONTAINERS>
												<!-- Container Definition: CddGenericNmLowerLayerChannel -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:119abe0c-226e-4251-abb0-d59123945082">
													<SHORT-NAME>CddGenericNmLowerLayerChannel</SHORT-NAME>
													<DESC>
														<L-2 L="EN">NM Channel specific configuration parameters.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<REFERENCES>
														<!-- Reference Definition: CddGenericNmComMNetworkHandleRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:f5240286-e1ac-4c12-9f47-0c23e033cf85">
															<SHORT-NAME>CddGenericNmComMNetworkHandleRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This reference points to the unique channel defined by the ComMChannel and provides access to the unique channel index value in ComMChannelId.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES/>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/ComM/ComMConfigSet/ComMChannel</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: CddPduRLowerLayerContribution -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:d4920631-85b0-4376-8743-3babb19cdc29">
											<SHORT-NAME>CddPduRLowerLayerContribution</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Parameters that are necessary for the configuration of a Complex Device Driver that serves as the LowerLayer of the Pdu Router module.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<SUB-CONTAINERS>
												<!-- Container Definition: CddPduRLowerLayerRxPdu -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:32300c99-13ae-4fe8-8bba-93f316788818">
													<SHORT-NAME>CddPduRLowerLayerRxPdu</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container specifies Rx PDUs that are exchanged between the CDD and the standardized BSW module.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CddPduRLowerLayerHandleId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:74dd8870-0180-4f4a-85cb-5c53a1be131a">
															<SHORT-NAME>CddPduRLowerLayerHandleId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">ECU wide unique, symbolic handle for the Pdu.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES/>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: CddPduRLowerLayerPduRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:f4dbfb7a-93e4-4404-83f1-e66f3d41df74">
															<SHORT-NAME>CddPduRLowerLayerPduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the &quot;global&quot; Pdu structure to allow harmonization of handle IDs in the COM-Stack.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES/>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: CddPduRLowerLayerTxPdu -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:186d1878-40b0-4a5a-aae4-8ba77dd3ce93">
													<SHORT-NAME>CddPduRLowerLayerTxPdu</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container specifies Tx PDUs that are exchanged between the CDD and the standardized BSW module.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CddPduRLowerLayerHandleId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:e95cede4-9ee6-493d-9905-de3c13f222ec">
															<SHORT-NAME>CddPduRLowerLayerHandleId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">ECU wide unique, symbolic handle for the Pdu.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES/>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: CddPduRLowerLayerPduRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:5662fa00-3ca4-4663-ae73-ffde4f9379b5">
															<SHORT-NAME>CddPduRLowerLayerPduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the &quot;global&quot; Pdu structure to allow harmonization of handle IDs in the COM-Stack.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES/>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: CddPduRUpperLayerContribution -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:2eaf17e0-7822-4cb2-8cce-9083ec9291d2">
											<SHORT-NAME>CddPduRUpperLayerContribution</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Parameters that are necessary for the configuration of a Complex Device Driver that serves as the UpperLayer of the Pdu Router module.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<SUB-CONTAINERS>
												<!-- Container Definition: CddPduRUpperLayerRxPdu -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:8f579ded-0098-42fc-81eb-9fc765136ed6">
													<SHORT-NAME>CddPduRUpperLayerRxPdu</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container specifies Rx PDUs that are exchanged between the CDD and the standardized BSW module.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CddPduRUpperLayerHandleId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:331ea11f-6ea6-47c4-9231-db5716ea9e69">
															<SHORT-NAME>CddPduRUpperLayerHandleId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">ECU wide unique, symbolic handle for the Pdu.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES/>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: CddPduRUpperLayerPduRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:ce526005-b88e-4fc2-a44e-9112c948af82">
															<SHORT-NAME>CddPduRUpperLayerPduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the &quot;global&quot; Pdu structure to allow harmonization of handle IDs in the COM-Stack.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES/>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: CddPduRUpperLayerTxPdu -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:a52124c4-5abd-4b6a-8f67-a04f970428a3">
													<SHORT-NAME>CddPduRUpperLayerTxPdu</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container specifies Tx PDUs that are exchanged between the CDD and the standardized BSW module.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CddPduRUpperLayerHandleId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:ba3c213c-e704-46a1-a0e5-a59e7a501d50">
															<SHORT-NAME>CddPduRUpperLayerHandleId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">ECU wide unique, symbolic handle for the Pdu.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES/>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: CddPduRUpperLayerPduRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:245c3911-45a2-40e6-aae4-29b332894893">
															<SHORT-NAME>CddPduRUpperLayerPduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the &quot;global&quot; Pdu structure to allow harmonization of handle IDs in the COM-Stack.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES/>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: CddSoAdUpperLayerContribution -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="12e74aa2-3d7e-4da2-8f57-52a2d1421c74">
											<SHORT-NAME>CddSoAdUpperLayerContribution</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Parameters that are necessary for the configuration of a Complex Driver that serves as the UpperLayer of the SoAd module.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<SUB-CONTAINERS>
												<!-- Container Definition: CddSoAdUpperLayerRxPdu -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="a98634d8-a722-4ba1-956b-db956ed559e7">
													<SHORT-NAME>CddSoAdUpperLayerRxPdu</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container specifies Rx PDUs that are exchanged between the CDD and the standardized BSW module.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CddSoAdUpperLayerHandleId -->
														<ECUC-INTEGER-PARAM-DEF UUID="58919aed-6985-480a-8699-c2b092e9b930">
															<SHORT-NAME>CddSoAdUpperLayerHandleId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">ECU wide unique, symbolic handle for the Pdu.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: CddSoAdUpperLayerPduRef -->
														<ECUC-REFERENCE-DEF UUID="cea75abe-0a6c-4d37-b4e9-4128e4403e1d">
															<SHORT-NAME>CddSoAdUpperLayerPduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the "global" Pdu structure to allow harmonization of handle IDs in the COM-Stack.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: CddSoAdUpperLayerTxPdu -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="5ea321bf-1281-4831-bd47-150e96dd8f28">
													<SHORT-NAME>CddSoAdUpperLayerTxPdu</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container specifies Tx PDUs that are exchanged between the CDD and the standardized BSW module.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CddSoAdUpperLayerHandleId -->
														<ECUC-INTEGER-PARAM-DEF UUID="3a186a1d-7c19-45b9-a70b-1ea0870d5912">
															<SHORT-NAME>CddSoAdUpperLayerHandleId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">ECU wide unique, symbolic handle for the Pdu.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: CddSoAdUpperLayerPduRef -->
														<ECUC-REFERENCE-DEF UUID="29adab3b-3877-4f3a-9383-5bc2da9f4f59">
															<SHORT-NAME>CddSoAdUpperLayerPduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the "global" Pdu structure to allow harmonization of handle IDs in the COM-Stack.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CddEcucPartitionInteraction -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:5ddb5cdf-4b83-4980-b535-5aa4c700316f">
									<SHORT-NAME>CddEcucPartitionInteraction</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This optional container holds the partition interaction configuration.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CddPartitionStoppedFunctionName -->
										<ECUC-FUNCTION-NAME-DEF UUID="ECUC:b329985d-d398-4f17-93cd-f29503da4d52">
											<SHORT-NAME>CddPartitionStoppedFunctionName</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Function name to be called when the partition which is triggering the complex driver is stopped.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES/>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Reference Definition: CddEcucPartitionRef -->
										<ECUC-REFERENCE-DEF UUID="ECUC:46cceb2a-0c28-49ac-8bad-7558446a366c">
											<SHORT-NAME>CddEcucPartitionRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to the &quot;EcucPartition&quot; which executes the software which triggers the CDD.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES/>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPartitionCollection/EcucPartition</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
