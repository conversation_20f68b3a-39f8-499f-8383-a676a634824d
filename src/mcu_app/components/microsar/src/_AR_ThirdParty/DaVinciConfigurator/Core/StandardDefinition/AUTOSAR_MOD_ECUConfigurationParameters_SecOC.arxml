<?xml version="1.0" encoding="UTF-8"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-3-0.xsd">
	<AR-PACKAGES>
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: SecOC -->
						<ECUC-MODULE-DEF UUID="ECUC:916e6567-92b2-4cc2-b45f-5a34c3daf255">
							<SHORT-NAME>SecOC</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the SecOC (SecureOnboardCommunication) module.</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.3.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00001</RELATED-TRACE-ITEM-REF>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<POST-BUILD-VARIANT-SUPPORT>true</POST-BUILD-VARIANT-SUPPORT>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-LINK-TIME</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-POST-BUILD</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: SecOCGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:d974b231-dfd7-4419-9f0d-489b957b765b">
									<SHORT-NAME>SecOCGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Contains the general configuration parameters of the SecOC module.</L-2>
									</DESC>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00002</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: SecOCDevErrorDetect -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:b350a403-5388-4f68-9cf1-1c1349445692">
											<SHORT-NAME>SecOCDevErrorDetect</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Switches the development error detection and notification on or off.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">* true: detection and notification is enabled.
                                        * false: detection and notification is disabled.</L-1>
												</P>
											</INTRODUCTION>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00007</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCEnableForcedPassOverride -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:f820fbce-18ab-47c5-8f88-9dee469bc75b">
											<SHORT-NAME>SecOCEnableForcedPassOverride</SHORT-NAME>
											<DESC>
												<L-2 L="EN">When this configuration option is set to TRUE then the functionality inside the function SecOC_VerifyStatusOverride to forcibly override the VerifyStatus to "Pass" is enabled.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00051</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOcIgnoreVerificationResult -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:c2015929-5526-4d40-9671-12984967fec0">
											<SHORT-NAME>SecOcIgnoreVerificationResult</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The result of the authentication process (e.g. MAC Verify) is ignored after the first try and the SecOC proceeds like the result was a success. The calculation of the authenticator is still done, only its result will be ignored.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">- true: enabled (verification result is ignored).
                                         - false: disabled (verification result is NOT ignored).</L-1>
												</P>
											</INTRODUCTION>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00052</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCMainFunctionPeriodRx -->
										<ECUC-FLOAT-PARAM-DEF UUID="ECUC:e98911f2-9c55-4584-9857-8426b140cf41">
											<SHORT-NAME>SecOCMainFunctionPeriodRx</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Allows to configure the time for the MainFunction of the Rx path (as float in seconds).</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00053</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>ECU</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX INTERVAL-TYPE="OPEN">Inf</MAX>
											<MIN INTERVAL-TYPE="OPEN">0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCMainFunctionPeriodTx -->
										<ECUC-FLOAT-PARAM-DEF UUID="ECUC:db84819e-b5d4-4e04-a976-3c892b5ce0df">
											<SHORT-NAME>SecOCMainFunctionPeriodTx</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Allows to configure the time for the MainFunction of the Tx path (as float in seconds).</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00054</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>ECU</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX INTERVAL-TYPE="OPEN">Inf</MAX>
											<MIN INTERVAL-TYPE="OPEN">0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCMaxAlignScalarType -->
										<ECUC-STRING-PARAM-DEF UUID="ECUC:a7746493-ddf2-45f5-bdbc-410ccc7aff78">
											<SHORT-NAME>SecOCMaxAlignScalarType</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The scalar type which has the maximum alignment restrictions on the given platform. This type can be e.g. uint8, uint16 or uint32.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00047</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-STRING-PARAM-DEF-VARIANTS>
												<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
											</ECUC-STRING-PARAM-DEF-VARIANTS>
										</ECUC-STRING-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCQueryFreshnessValue -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:632d9b52-9ebb-4611-9175-413ffce45c70">
											<SHORT-NAME>SecOCQueryFreshnessValue</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter specifies if the freshness value shall be determined through a C-function (CD) or a software component (SW-C).</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00078</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>CFUNC</DEFAULT-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:6f3c8b21-0632-8d82-3788-79164cef5fb6">
													<SHORT-NAME>CFUNC</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:26734079-a258-8d8f-2bc8-2fa33a820502">
													<SHORT-NAME>RTE</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCVerificationStatusCallout -->
										<ECUC-FUNCTION-NAME-DEF UUID="ECUC:88f43685-965b-48a5-99cd-724c925ab9bc">
											<SHORT-NAME>SecOCVerificationStatusCallout</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Entry address of the customer specific call out routine which shall be invoked in case of a verification attempt.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00004</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
										<!-- PARAMETER DEFINITION: SecOCVersionInfoApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:6a9ce3ed-77e5-4cd3-bbb9-6f22ed1626a1">
											<SHORT-NAME>SecOCVersionInfoApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">If true the SecOC_GetVersionInfo API is available.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00003</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: SecOCRxPduProcessing -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:84ac6f7d-9b6b-4033-afe4-6ea5dcd6b15d">
									<SHORT-NAME>SecOCRxPduProcessing</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Contains the parameters to configure the RxPdus to be verified by the SecOC module.</L-2>
									</DESC>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00011</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: SecOCAuthDataFreshnessLen -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:15c4140f-6852-47c5-aa8c-5341962e605c">
											<SHORT-NAME>SecOCAuthDataFreshnessLen</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The length of the external authentic PDU data in bits (uint16).</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00082</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>ECU</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>18446744073709551615</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCAuthDataFreshnessStartPosition -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:6b514eaf-8659-49bf-837c-4b233cfb8213">
											<SHORT-NAME>SecOCAuthDataFreshnessStartPosition</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This value determines the start position in bits (uint16) of the Authentic PDU that shall be passed on to the Freshness SWC. The bit position starts counting from the MSB of the first byte of the PDU.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00081</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>ECU</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>18446744073709551615</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCAuthenticationBuildAttempts -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:939f4eb8-**************-55513ba98481">
											<SHORT-NAME>SecOCAuthenticationBuildAttempts</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter specifies the number of authentication build attempts.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00079</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCAuthenticationVerifyAttempts -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:62039fef-5c98-42f5-8204-42b14fee133c">
											<SHORT-NAME>SecOCAuthenticationVerifyAttempts</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter specifies the number of authentication verify attempts that are to be carried out when the verification of the authentication information failed for a given Secured I-PDU. If zero is set, then only one authentication verification attempt is done.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00080</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCAuthInfoTxLength -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:1d737833-2f08-8744-4b14-5954fb59a1d9">
											<SHORT-NAME>SecOCAuthInfoTxLength</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the length in bits of the authentication code to be included in the payload of the Secured I-PDU.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00034</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCDataId -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:62a5b06c-7e84-8fef-5f12-44cc3ef04d4f">
											<SHORT-NAME>SecOCDataId</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines a unique numerical identifier for the Secured I-PDU.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00030</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCFreshnessValueId -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:7e2042fb-6eaf-8b3c-5bc5-6746e75a807f">
											<SHORT-NAME>SecOCFreshnessValueId</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the Id of the Freshness Value.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The Freshness Value might be a normal counter or a time value.</L-1>
												</P>
											</INTRODUCTION>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00038</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCFreshnessValueLength -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:0325c14a-6710-89b7-6789-82ba4a4c96f1">
											<SHORT-NAME>SecOCFreshnessValueLength</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the complete length in bits of the Freshness Value. As long as the key doesn't change the counter shall not overflow. The length of the counter shall be determined based on the expected life time of the corresponding key and frequency of usage of the counter.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00031</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>64</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCFreshnessValueTxLength -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:b30f81b7-9bab-8d92-6a67-e79c10523977">
											<SHORT-NAME>SecOCFreshnessValueTxLength</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the length in bits of the Freshness Value to be included in the payload of the Secured I-PDU. This length is specific to the least significant bits of the complete Freshness Counter. If the parameter is 0 no Freshness Value is included in the Secured I-PDU.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00032</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>64</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCReceptionOverflowStrategy -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:9aab4468-17ce-4921-b104-eaa693a18107">
											<SHORT-NAME>SecOCReceptionOverflowStrategy</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the overflow strategy for receiving PDUs</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00076</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:581804c9-21dd-8986-3bec-e83e0e4085ae">
													<SHORT-NAME>QUEUE</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:f58ab1f6-0294-8348-4e5e-f106dea6de0a">
													<SHORT-NAME>REJECT</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:36b24891-37a4-8cab-680e-449b15edbb5a">
													<SHORT-NAME>REPLACE</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCReceptionQueueSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:c3a4683e-b733-4ff7-8987-301a8ff7e55c">
											<SHORT-NAME>SecOCReceptionQueueSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the queue size in case the overflow strategy for receiving PDUs is set to QUEUE.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00077</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCUseAuthDataFreshness -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:b7d5a803-39a6-4c32-8b4d-1bb409518e01">
											<SHORT-NAME>SecOCUseAuthDataFreshness</SHORT-NAME>
											<DESC>
												<L-2 L="EN">A Boolean value that indicates if a part of the Authentic-PDU shall be passed on to the SWC that verifies and generates the Freshness. If it is set to TRUE, the values SecOCAuthDataFreshnessStartPosition and SecOCAuthDataFreshnessLen must be set to specify the bit position and length within the Authentic-PDU.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00083</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>ECU</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCVerificationStatusPropagationMode -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:f3484e24-4b91-438e-bb7c-4bc7469c5ca7">
											<SHORT-NAME>SecOCVerificationStatusPropagationMode</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter is used to describe the propagation of the status of each verification attempt from the SecOC module to SWCs.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00046</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:08bf4253-8aea-82cd-3f47-ebee8af5908c">
													<SHORT-NAME>BOTH</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:d9a92403-aa28-892c-6a7d-a72217d1ef79">
													<SHORT-NAME>FAILURE_ONLY</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:4d91dfbe-1bcf-895d-5140-7bc2c3057872">
													<SHORT-NAME>NONE</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Symbolic Name Reference Definition: SecOCRxAuthServiceConfigRef -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:135e1ad2-b132-4b29-b4f7-577ceba4d50f">
											<SHORT-NAME>SecOCRxAuthServiceConfigRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This reference is used to define which crypto service function is called for authentication.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00048</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Csm/CsmJobs/CsmJob</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
										<!-- Reference Definition: SecOCSameBufferPduRef -->
										<ECUC-REFERENCE-DEF UUID="ECUC:e2fdffb8-2e63-869a-340b-807e017f95be">
											<SHORT-NAME>SecOCSameBufferPduRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This reference is used to collect Pdus that are using the same SecOC buffer.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00049</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/SecOC/SecOCSameBufferPduCollection</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<!-- Container Definition: SecOCRxAuthenticPduLayer -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:fe87ddb5-fcdc-45ff-ba4b-d688a3943e5d">
											<SHORT-NAME>SecOCRxAuthenticPduLayer</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container specifies the Pdu that is transmitted by the SecOC module to the PduR after the Mac was verified.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00044</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: SecOCPduType -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:c7a83543-ecfc-8780-5f35-903027fca582">
													<SHORT-NAME>SecOCPduType</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines API Type to use for communication with PduR.</L-2>
													</DESC>
													<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00075</RELATED-TRACE-ITEM-REF>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:e40a702c-a79a-c7f4-f834-b15e02be5674">
															<SHORT-NAME>SECOC_IFPDU</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:7df064b1-f70e-d53f-1124-e7a5c4271ea3">
															<SHORT-NAME>SECOC_TPPDU</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: SecOCRxAuthenticLayerPduRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:8a365fa6-92e8-426b-83ce-2447690ce342">
													<SHORT-NAME>SecOCRxAuthenticLayerPduRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the global Pdu.</L-2>
													</DESC>
													<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00045</RELATED-TRACE-ITEM-REF>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: SecOCRxSecuredPduLayer -->
										<ECUC-CHOICE-CONTAINER-DEF UUID="ECUC:0d17ebb7-f591-4e4e-b2f0-0ccedce78b25">
											<SHORT-NAME>SecOCRxSecuredPduLayer</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container specifies the Pdu that is received by the SecOC module from the PduR. For this Pdu the Mac verification is provided.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00041</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<CHOICES>
												<!-- Container Definition: SecOCRxSecuredPdu -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:eec91efc-2ddf-4d96-9944-f966b70fb710">
													<SHORT-NAME>SecOCRxSecuredPdu</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container specifies the Pdu that is received by the SecOC module from the PduR. For this Pdu the Mac verification is provided.</L-2>
													</DESC>
													<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00069</RELATED-TRACE-ITEM-REF>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: SecOCRxSecuredLayerPduId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:4e926dd6-0c6f-4ac2-8abb-5ed5687c5a69">
															<SHORT-NAME>SecOCRxSecuredLayerPduId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">PDU identifier assigned by SecOC module. Used by PduR for SecOC_PduRRxIndication.</L-2>
															</DESC>
															<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00043</RELATED-TRACE-ITEM-REF>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<SCOPE>LOCAL</SCOPE>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: SecOCRxSecuredLayerPduRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:ee06badc-d404-4032-afa3-f09f8849e756">
															<SHORT-NAME>SecOCRxSecuredLayerPduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the global Pdu.</L-2>
															</DESC>
															<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00042</RELATED-TRACE-ITEM-REF>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<SCOPE>LOCAL</SCOPE>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: SecOCRxSecuredPduCollection -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:f9ab58b5-f3c8-435f-a0f9-cabfafd185bb">
													<SHORT-NAME>SecOCRxSecuredPduCollection</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container specifies two Pdus that are received by the SecOC module from the PduR and a message linking between them.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">SecOCRxAuthenticPdu contains the original Authentic I-PDU, i.e. the secured data, and the SecOCRxCryptographicPdu contains the Authenticator, i.e. the actual Authentication Information.</L-1>
														</P>
													</INTRODUCTION>
													<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00067</RELATED-TRACE-ITEM-REF>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SUB-CONTAINERS>
														<!-- Container Definition: SecOCRxAuthenticPdu -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:90f8a475-b3c9-41c7-b693-8b818f376daa">
															<SHORT-NAME>SecOCRxAuthenticPdu</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This container specifies the Authetic Pdu that is received by the SecOC module from the PduR.</L-2>
															</DESC>
															<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00061</RELATED-TRACE-ITEM-REF>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: SecOCRxAuthenticPduId -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:20339ed9-a5f9-40e8-a20d-72cc560a0090">
																	<SHORT-NAME>SecOCRxAuthenticPduId</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">PDU identifier of the Authentic I-PDU assigned by SecOC module. Used by PduR for SecOC_PduRRxIndication.</L-2>
																	</DESC>
																	<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00062</RELATED-TRACE-ITEM-REF>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<SCOPE>LOCAL</SCOPE>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
																	<VALUE-CONFIG-CLASSES>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																	</VALUE-CONFIG-CLASSES>
																	<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
																	<MAX>65535</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
															<REFERENCES>
																<!-- Reference Definition: SecOCRxAuthenticPduRef -->
																<ECUC-REFERENCE-DEF UUID="ECUC:df07be45-f938-4937-802c-15353271afe9">
																	<SHORT-NAME>SecOCRxAuthenticPduRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to the global Pdu.</L-2>
																	</DESC>
																	<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00063</RELATED-TRACE-ITEM-REF>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<SCOPE>LOCAL</SCOPE>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
																	<VALUE-CONFIG-CLASSES>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																	</VALUE-CONFIG-CLASSES>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
															</REFERENCES>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: SecOCRxCryptographicPdu -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:372ebdd2-f32e-4581-a563-f907d6f610a4">
															<SHORT-NAME>SecOCRxCryptographicPdu</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This container specifies the Cryptographic Pdu that is received by the SecOC module from the PduR.</L-2>
															</DESC>
															<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00064</RELATED-TRACE-ITEM-REF>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: SecOCRxCryptographicPduId -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:f0f03b01-0ca7-499a-b8b8-d9ab68829e13">
																	<SHORT-NAME>SecOCRxCryptographicPduId</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">PDU identifier of the Cryptographic I-PDU assigned by SecOC module. Used by PduR for SecOC_PduRRxIndication.</L-2>
																	</DESC>
																	<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00065</RELATED-TRACE-ITEM-REF>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<SCOPE>LOCAL</SCOPE>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
																	<VALUE-CONFIG-CLASSES>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																	</VALUE-CONFIG-CLASSES>
																	<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
																	<MAX>65535</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
															<REFERENCES>
																<!-- Reference Definition: SecOCRxCryptographicPduRef -->
																<ECUC-REFERENCE-DEF UUID="ECUC:408b66b0-5a3a-4c10-ae65-2a94f557e26a">
																	<SHORT-NAME>SecOCRxCryptographicPduRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to the global Pdu.</L-2>
																	</DESC>
																	<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00066</RELATED-TRACE-ITEM-REF>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<SCOPE>LOCAL</SCOPE>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
																	<VALUE-CONFIG-CLASSES>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																	</VALUE-CONFIG-CLASSES>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
															</REFERENCES>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: SecOCUseMessageLink -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e1f46a76-00ec-88f9-2ef3-6f898dbf561e">
															<SHORT-NAME>SecOCUseMessageLink</SHORT-NAME>
															<DESC>
																<L-2 L="EN">SecOC links an Authentic I-PDU and Cryptographic I-PDU together by repeating a specific part (Message Linker) of the Authentic I-PDU in the Cryptographic I-PDU.</L-2>
															</DESC>
															<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00074</RELATED-TRACE-ITEM-REF>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: SecOCMessageLinkLen -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:e8019d75-51c9-c9b7-dc27-a7075822d745">
																	<SHORT-NAME>SecOCMessageLinkLen</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Length of the Message Linker inside the Authentic I-PDU in bits.</L-2>
																	</DESC>
																	<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00060</RELATED-TRACE-ITEM-REF>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<SCOPE>LOCAL</SCOPE>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
																	<VALUE-CONFIG-CLASSES>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																	</VALUE-CONFIG-CLASSES>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>65535</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: SecOCMessageLinkPos -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:d0abe4b3-c06a-d228-dd24-a2d98e7b5cf3">
																	<SHORT-NAME>SecOCMessageLinkPos</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">The position of the Message Linker inside the Authentic I-PDU in bits.</L-2>
																	</DESC>
																	<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00059</RELATED-TRACE-ITEM-REF>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<SCOPE>LOCAL</SCOPE>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
																	<VALUE-CONFIG-CLASSES>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																	</VALUE-CONFIG-CLASSES>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>65535</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</CHOICES>
										</ECUC-CHOICE-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: SecOCSameBufferPduCollection -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:38a259eb-8522-47b9-8d04-b2ccdf116e02">
									<SHORT-NAME>SecOCSameBufferPduCollection</SHORT-NAME>
									<DESC>
										<L-2 L="EN">SecOCBuffer configuration that may be used by a collection of Pdus.</L-2>
									</DESC>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00009</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: SecOCBufferLength -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:ce097fa0-d6cf-4f98-ba0f-726d0ee6803e">
											<SHORT-NAME>SecOCBufferLength</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the Buffer in bytes that is used by the SecOC module.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00008</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: SecOCTxPduProcessing -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:bd305409-5c26-4f43-bf5d-6efdb81e535b">
									<SHORT-NAME>SecOCTxPduProcessing</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Contains the parameters to configure the TxPdus to be secured by the SecOC module.</L-2>
									</DESC>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00012</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: SecOCAuthenticationBuildAttempts -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:cc233343-ea04-92a4-760e-55a916f1267f">
											<SHORT-NAME>SecOCAuthenticationBuildAttempts</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter specifies the number of authentication build attempts.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00079</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCAuthInfoTxLength -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:55f75cbe-efc3-9654-5a8d-59acd6a143d7">
											<SHORT-NAME>SecOCAuthInfoTxLength</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the length in bits of the authentication code to be included in the payload of the Secured I-PDU.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00018</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCDataId -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:9b2994f8-3f3f-9eff-6e8b-45241a37ef4d">
											<SHORT-NAME>SecOCDataId</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines a unique numerical identifier for the Secured I-PDU.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00014</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCFreshnessValueId -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:b6a42787-2f6a-9a4c-6b3e-679ec2a2227d">
											<SHORT-NAME>SecOCFreshnessValueId</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the Id of the Freshness Value.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The Freshness Value might be a normal counter or a time value.</L-1>
												</P>
											</INTRODUCTION>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00021</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCFreshnessValueLength -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:3ba9a5d6-27cb-98c7-7702-8312259438ef">
											<SHORT-NAME>SecOCFreshnessValueLength</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the complete length in bits of the Freshness Value. As long as the key doesn't change the counter shall not overflow. The length of the counter shall be determined based on the expected life time of the corresponding key and frequency of usage of the counter.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00015</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>64</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCFreshnessValueTxLength -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:eb936643-5c66-9ca2-79e0-e7f3eb99db75">
											<SHORT-NAME>SecOCFreshnessValueTxLength</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the length in bits of the Freshness Value to be included in the payload of the Secured I-PDU. This length is specific to the least significant bits of the complete Freshness Counter. If the parameter is 0 no Freshness Value is included in the Secured I-PDU.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00016</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>64</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCProvideTxTruncatedFreshnessValue -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:04f3b3b9-6144-4f28-8f86-529fd6db25cd">
											<SHORT-NAME>SecOCProvideTxTruncatedFreshnessValue</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter specifies if the Tx query freshness function provides the truncated freshness info instead of generating this by SecOC In this case, SecOC shall add this data to the Authentic PDU instead of truncating the freshness value.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00084</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SecOCUseTxConfirmation -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:1cf993ac-e97a-4695-939f-d9a66e0d812a">
											<SHORT-NAME>SecOCUseTxConfirmation</SHORT-NAME>
											<DESC>
												<L-2 L="EN">A Boolean value that indicates if the function SecOC_SPduTxConfirmation shall be called for this PDU.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00085</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Reference Definition: SecOCSameBufferPduRef -->
										<ECUC-REFERENCE-DEF UUID="ECUC:1b81e443-ef1e-95aa-4384-80d5dcc737bc">
											<SHORT-NAME>SecOCSameBufferPduRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This reference is used to collect Pdus that are using the same SecOC buffer.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00010</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/SecOC/SecOCSameBufferPduCollection</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<!-- Symbolic Name Reference Definition: SecOCTxAuthServiceConfigRef -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:0e085d61-00be-4364-841d-fb7fdc670ed7">
											<SHORT-NAME>SecOCTxAuthServiceConfigRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This reference is used to define which crypto service function is called for authentication.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00013</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Csm/CsmJobs/CsmJob</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<!-- Container Definition: SecOCTxAuthenticPduLayer -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:88fc782a-117f-4db3-88d6-e3e263ea3988">
											<SHORT-NAME>SecOCTxAuthenticPduLayer</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container specifies the Pdu that is received by the SecOC module from the PduR. For this Pdu the Mac generation is provided.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00023</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: SecOCPduType -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:521ccfb8-019f-8f34-2dc0-9d89e852a0ad">
													<SHORT-NAME>SecOCPduType</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines API Type to use for communication with PduR.</L-2>
													</DESC>
													<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00075</RELATED-TRACE-ITEM-REF>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:6e7f0aa0-bc3d-cfa8-c6bf-beb7c314519f">
															<SHORT-NAME>SECOC_IFPDU</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:0864ff26-0bb1-dcf3-dfaf-f4ff847d19ce">
															<SHORT-NAME>SECOC_TPPDU</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: SecOCTxAuthenticLayerPduId -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:6d3ef2f7-0768-4e56-8ed5-25d006a9b48b">
													<SHORT-NAME>SecOCTxAuthenticLayerPduId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">PDU identifier assigned by SecOC module. Used by PduR for SecOC_PduRTransmit.</L-2>
													</DESC>
													<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00026</RELATED-TRACE-ITEM-REF>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: SecOCTxAuthenticLayerPduRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:f3889cc4-627c-4e47-b2e0-8936f0298dd1">
													<SHORT-NAME>SecOCTxAuthenticLayerPduRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the global Pdu.</L-2>
													</DESC>
													<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00025</RELATED-TRACE-ITEM-REF>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: SecOCTxSecuredPduLayer -->
										<ECUC-CHOICE-CONTAINER-DEF UUID="ECUC:f28c4013-a122-48f9-8116-a9c2e70746e8">
											<SHORT-NAME>SecOCTxSecuredPduLayer</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container specifies the Pdu that is transmitted by the SecOC module to the PduR after the Mac was generated.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00024</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<CHOICES>
												<!-- Container Definition: SecOCTxSecuredPdu -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:7cbe5ea3-b956-4f43-8865-2d9eef01d0bd">
													<SHORT-NAME>SecOCTxSecuredPdu</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container specifies one Pdu that is transmitted by the SecOC module to the PduR after the Mac was generated. This Pdu contains the cryptographic information.</L-2>
													</DESC>
													<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00070</RELATED-TRACE-ITEM-REF>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: SecOCTxSecuredLayerPduId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:37a3a0b7-9500-4659-92c0-0100526f02ac">
															<SHORT-NAME>SecOCTxSecuredLayerPduId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">PDU identifier assigned by SecOC module. Used by PduR for confirmation (SecOC_PduRTxConfirmation) and for TriggerTransmit.</L-2>
															</DESC>
															<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00028</RELATED-TRACE-ITEM-REF>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<SCOPE>LOCAL</SCOPE>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: SecOCTxSecuredLayerPduRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:f4f69e53-8605-4d16-acbe-45db1e6eb0d4">
															<SHORT-NAME>SecOCTxSecuredLayerPduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the global Pdu.</L-2>
															</DESC>
															<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00027</RELATED-TRACE-ITEM-REF>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<SCOPE>LOCAL</SCOPE>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: SecOCTxSecuredPduCollection -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:818bd2c9-6dbe-4382-8b0b-9864ae0016a8">
													<SHORT-NAME>SecOCTxSecuredPduCollection</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container specifies the Pdu that is transmitted by the SecOC module to the PduR after the Mac was generated. Two separate Pdus are transmitted to the PduR:</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">Authentic I-PDU and Cryptographic I-PDU.</L-1>
														</P>
													</INTRODUCTION>
													<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00071</RELATED-TRACE-ITEM-REF>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SUB-CONTAINERS>
														<!-- Container Definition: SecOCTxAuthenticPdu -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:b8592877-9bb3-489b-af8c-a71c95eb88f8">
															<SHORT-NAME>SecOCTxAuthenticPdu</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This container specifies the Authetic Pdu that is transmitted by the SecOC module to the PduR after the Mac was generated.</L-2>
															</DESC>
															<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00072</RELATED-TRACE-ITEM-REF>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: SecOCTxAuthenticPduId -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:fd73c019-8d14-4d07-a5d9-bc9f6af4f2c0">
																	<SHORT-NAME>SecOCTxAuthenticPduId</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">PDU identifier of the Authentic I-PDU assigned by SecOC module. Used by PduR for confirmation (SecOC_PduRTxConfirmation) and for TriggerTransmit.</L-2>
																	</DESC>
																	<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00055</RELATED-TRACE-ITEM-REF>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<SCOPE>LOCAL</SCOPE>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
																	<VALUE-CONFIG-CLASSES>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																	</VALUE-CONFIG-CLASSES>
																	<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
																	<MAX>65535</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
															<REFERENCES>
																<!-- Reference Definition: SecOCTxAuthenticPduRef -->
																<ECUC-REFERENCE-DEF UUID="ECUC:abab787a-645e-4ad0-af50-f59116b3586d">
																	<SHORT-NAME>SecOCTxAuthenticPduRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to the global Pdu.</L-2>
																	</DESC>
																	<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00056</RELATED-TRACE-ITEM-REF>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<SCOPE>LOCAL</SCOPE>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
																	<VALUE-CONFIG-CLASSES>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																	</VALUE-CONFIG-CLASSES>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
															</REFERENCES>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: SecOCTxCryptographicPdu -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:f4548f58-7b28-494a-a772-380b0138620b">
															<SHORT-NAME>SecOCTxCryptographicPdu</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This container specifies the Cryptographic Pdu that is transmitted by the SecOC module to the PduR after the Mac was generated.</L-2>
															</DESC>
															<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00073</RELATED-TRACE-ITEM-REF>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: SecOCTxCryptographicPduId -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:4bcd8e92-**************-e8961ffccc6c">
																	<SHORT-NAME>SecOCTxCryptographicPduId</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">PDU identifier of the Cryptographic I-PDU assigned by SecOC module. Used by PduR for confirmation (SecOC_PduRTxConfirmation) and for TriggerTransmit.</L-2>
																	</DESC>
																	<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00057</RELATED-TRACE-ITEM-REF>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<SCOPE>LOCAL</SCOPE>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
																	<VALUE-CONFIG-CLASSES>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																	</VALUE-CONFIG-CLASSES>
																	<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
																	<MAX>65535</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
															<REFERENCES>
																<!-- Reference Definition: SecOCTxCryptographicPduRef -->
																<ECUC-REFERENCE-DEF UUID="ECUC:f4745072-90e6-4aa3-9005-6d676efba509">
																	<SHORT-NAME>SecOCTxCryptographicPduRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to the global Pdu.</L-2>
																	</DESC>
																	<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00058</RELATED-TRACE-ITEM-REF>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<SCOPE>LOCAL</SCOPE>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
																	<VALUE-CONFIG-CLASSES>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																	</VALUE-CONFIG-CLASSES>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
																</ECUC-REFERENCE-DEF>
															</REFERENCES>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: SecOCUseMessageLink -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:69d4e489-7ae2-891c-1905-3d2e8bede70b">
															<SHORT-NAME>SecOCUseMessageLink</SHORT-NAME>
															<DESC>
																<L-2 L="EN">SecOC links an Authentic I-PDU and Cryptographic I-PDU together by repeating a specific part (Message Linker) of the Authentic I-PDU in the Cryptographic I-PDU.</L-2>
															</DESC>
															<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00074</RELATED-TRACE-ITEM-REF>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: SecOCMessageLinkLen -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:6fe21788-cbbf-c9da-c639-74ac56516832">
																	<SHORT-NAME>SecOCMessageLinkLen</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Length of the Message Linker inside the Authentic I-PDU in bits.</L-2>
																	</DESC>
																	<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00060</RELATED-TRACE-ITEM-REF>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<SCOPE>LOCAL</SCOPE>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
																	<VALUE-CONFIG-CLASSES>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																	</VALUE-CONFIG-CLASSES>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>65535</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: SecOCMessageLinkPos -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:588c5ec7-3a60-d24b-c736-707e8ca9ede0">
																	<SHORT-NAME>SecOCMessageLinkPos</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">The position of the Message Linker inside the Authentic I-PDU in bits.</L-2>
																	</DESC>
																	<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SecOC_00059</RELATED-TRACE-ITEM-REF>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<SCOPE>LOCAL</SCOPE>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
																	<VALUE-CONFIG-CLASSES>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																		<ECUC-VALUE-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-VALUE-CONFIGURATION-CLASS>
																	</VALUE-CONFIG-CLASSES>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>65535</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</CHOICES>
										</ECUC-CHOICE-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
