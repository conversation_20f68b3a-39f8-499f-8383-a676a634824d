<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: FrTSyn -->
						<ECUC-MODULE-DEF UUID="95583b1d-b9f2-456d-9cfb-aa5cead70c54">
							<SHORT-NAME>FrTSyn</SHORT-NAME>
							<DESC>
								<L-2 L="EN">This represents the specific configuration variant for the TSyn on Flexray.</L-2>
							</DESC>
							<CATEGORY>VENDOR_SPECIFIC_MODULE_DEFINITION</CATEGORY>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>1.00.00</REVISION-LABEL>
										<ISSUED-BY>visssf</ISSUED-BY>
										<DATE>2014-08-26T09:27:58+02:00</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">Initial creation (Beta)</L-2>
												</CHANGE>
												<REASON>
													<L-2 L="EN">ESCAN00077543</L-2>
												</REASON>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
									<DOC-REVISION>
										<REVISION-LABEL>1.00.01</REVISION-LABEL>
										<ISSUED-BY>visssf</ISSUED-BY>
										<DATE>2015-03-20T08:57:30+01:00</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">Updated SW version</L-2>
												</CHANGE>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
									<DOC-REVISION>
										<REVISION-LABEL>2.00.00</REVISION-LABEL>
										<ISSUED-BY>visssf</ISSUED-BY>
										<DATE>2015-09-22T09:26:14+02:00</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">Updated SW version</L-2>
												</CHANGE>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
									<DOC-REVISION>
										<REVISION-LABEL>2.01.00</REVISION-LABEL>
										<ISSUED-BY>visssf</ISSUED-BY>
										<DATE>2016-02-26T11:52:11+01:00</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">Support multiple masters per Time Domain</L-2>
												</CHANGE>
												<REASON>
													<L-2 L="EN">ESCAN00088578</L-2>
												</REASON>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-POST-BUILD</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: FrTSynGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="b2d6f6c6-03f1-4c43-93b9-22d55f02c31f">
									<SHORT-NAME>FrTSynGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container holds the general parameters of the Flexray-specific Synchronized Time-base Manager</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: FrTSynDevErrorDetect -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="1983eba3-afd9-4a67-9f25-63424e723066">
											<SHORT-NAME>FrTSynDevErrorDetect</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Switch for enabling the development error detection.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: FrTSynMainFunctionPeriod -->
										<ECUC-FLOAT-PARAM-DEF UUID="c15e73f2-c481-411d-8ed4-cd8f86d91466">
											<SHORT-NAME>FrTSynMainFunctionPeriod</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Schedule period of the main function FrTSyn_MainFunction. Unit: [s].</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">SEC</SD>
														<SD GID="DV:Unit">MSEC</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>1.7976931348623157E+308</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: FrTSynVersionInfo -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="3d6306e8-fdeb-4c0c-9c2d-25060b66ca48">
											<SHORT-NAME>FrTSynVersionInfo</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Activate/Deactivate the version information API (FrTSyn_GetVersionInfo). True: version information API activated False: version information API deactivated.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: FrTSynGlobalTimeOfsDataIDList -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="8742b81d-4ed1-48a5-90ba-f1c0b0bdb779">
											<SHORT-NAME>FrTSynGlobalTimeOfsDataIDList</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The DataIDList for OFS messages ensures the identification of data elements due to CRC calculation process.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SUB-CONTAINERS>
												<!-- Container Definition: FrTSynGlobalTimeOfsDataIDListElement -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="4d325d1c-c117-4e13-a0e3-d1910c6df658">
													<SHORT-NAME>FrTSynGlobalTimeOfsDataIDListElement</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Element of the DataIDList for OFS messages ensures the identification of data elements due to CRC calculation process.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>16</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>16</UPPER-MULTIPLICITY>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: FrTSynGlobalTimeOfsDataIDListIndex -->
														<ECUC-INTEGER-PARAM-DEF UUID="1957894f-9379-4a47-b0c0-49dc1304a620">
															<SHORT-NAME>FrTSynGlobalTimeOfsDataIDListIndex</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Index of the DataIDList for OFS messages ensures the identification of data elements due to CRC calculation process.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>15</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: FrTSynGlobalTimeOfsDataIDListValue -->
														<ECUC-INTEGER-PARAM-DEF UUID="f8c7d06a-4e4f-4d11-afc3-e12ef6ea0f5a">
															<SHORT-NAME>FrTSynGlobalTimeOfsDataIDListValue</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Value of the DataIDList for OFS messages ensures the identification of data elements due to CRC calculation process.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: FrTSynGlobalTimeSyncDataIDList -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="57afac1e-9b6c-42e8-8b2b-944692f70263">
											<SHORT-NAME>FrTSynGlobalTimeSyncDataIDList</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The DataIDList for SYNC messages ensures the identification of data elements due to CRC calculation process.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SUB-CONTAINERS>
												<!-- Container Definition: FrTSynGlobalTimeSyncDataIDListElement -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="3efc7ecd-5afe-4744-8c05-286189157d62">
													<SHORT-NAME>FrTSynGlobalTimeSyncDataIDListElement</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Element of the DataIDList for SYNC messages ensures the identification of data elements due to CRC calculation process.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>16</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>16</UPPER-MULTIPLICITY>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: FrTSynGlobalTimeSyncDataIDListIndex -->
														<ECUC-INTEGER-PARAM-DEF UUID="3ce0ec93-3a30-4499-8590-89064491c8e5">
															<SHORT-NAME>FrTSynGlobalTimeSyncDataIDListIndex</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Index of the DataIDList for SYNC messages ensures the identification of data elements due to CRC calculation process.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>15</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: FrTSynGlobalTimeSyncDataIDListValue -->
														<ECUC-INTEGER-PARAM-DEF UUID="bb8efba2-cc58-40a1-b5d6-857850c7f461">
															<SHORT-NAME>FrTSynGlobalTimeSyncDataIDListValue</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Value of the DataIDList for SYNC messages ensures the identification of data elements due to CRC calculation process.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: FrTSynGlobalTimeDomain -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="2a2c3403-f52d-4f26-90dd-a3373cbbd944">
									<SHORT-NAME>FrTSynGlobalTimeDomain</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This represents the existence of a global time domain on Flexray. The FrTSyn module can administrate several global time domains at the same time that in itself form a hierarchy of domains and sub-domains.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">If the FrTSyn exists it is assumed that at least one global time domain exists.</L-1>
										</P>
									</INTRODUCTION>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: FrTSynGlobalTimeDomainId -->
										<ECUC-INTEGER-PARAM-DEF UUID="6bd287d0-cd0f-49d5-917c-246ba8261671">
											<SHORT-NAME>FrTSynGlobalTimeDomainId</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The global time domain ID.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>31</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: FrTSynGlobalTimeSequenceCounterJumpWidth -->
										<ECUC-INTEGER-PARAM-DEF UUID="6797a71b-7add-4a3d-973d-************">
											<SHORT-NAME>FrTSynGlobalTimeSequenceCounterJumpWidth</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The SequenceCounterJumpWidth specifies the maximum allowed gap of the Sequence Counter between two SYNC resp. two OFS messages.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>15</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Symbolic Name Reference Definition: FrTSynSynchronizedTimeBaseRef -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="70f35333-f467-40f7-9292-2e4d36af7623">
											<SHORT-NAME>FrTSynSynchronizedTimeBaseRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Mandatory reference to the required synchronized time-base.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/StbM/StbMSynchronizedTimeBase</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<!-- Container Definition: FrTSynGlobalTimeMaster -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="d51d41f6-629c-42ed-956e-938c0a6df97f">
											<SHORT-NAME>FrTSynGlobalTimeMaster</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configuration of the global time master. Each global time domain is required to have exactly one global time master. This master may or may not exist on the configured ECU.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">true</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: FrTSynGlobalTimeTxCrcSecured -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="1e4a4108-0c3f-45cc-b3fb-9d7440bf3cdb">
													<SHORT-NAME>FrTSynGlobalTimeTxCrcSecured</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This represents the configuration of whether or not CRC is supported.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="0014d07c-e87b-4e3a-805e-169d695cc6bb">
															<SHORT-NAME>CRC_NOT_SUPPORTED</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="d8c47892-d723-4862-a5c4-1d84d5c6752b">
															<SHORT-NAME>CRC_SUPPORTED</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: FrTSynGlobalTimeTxPeriod -->
												<ECUC-FLOAT-PARAM-DEF UUID="c6061087-9a2f-439d-9c0f-dcadb24bb670">
													<SHORT-NAME>FrTSynGlobalTimeTxPeriod</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This represents the TX period. Unit: seconds</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
																<SD GID="DV:Unit">MSEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>1.7976931348623157E+308</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
											</PARAMETERS>
											<SUB-CONTAINERS>
												<!-- Container Definition: FrTSynGlobalTimeMasterPdu -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="9df6d907-25b1-4119-9386-a68a6b472c1e">
													<SHORT-NAME>FrTSynGlobalTimeMasterPdu</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container carries all properties required to configure the PDU sent by the global time master for the given global time domain.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: FrTSynGlobalTimeMasterConfirmationHandleId -->
														<ECUC-INTEGER-PARAM-DEF UUID="258138b9-5ecb-4638-bb88-0861f76cf520">
															<SHORT-NAME>FrTSynGlobalTimeMasterConfirmationHandleId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This represents the handle ID of the PDU that contains the global time information.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>0</DEFAULT-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: FrTSynGlobalTimePduRef -->
														<ECUC-REFERENCE-DEF UUID="d341c0f4-e9fb-4453-a0c4-8bf17b699bed">
															<SHORT-NAME>FrTSynGlobalTimePduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This represents the reference to the Pdu taken to transmit the global time information. The global time master of a global time domain acts as the sender of the Pdu while all the time slaves are supposed to receive the Pdu.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: FrTSynGlobalTimeSlave -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="4c8dc0c1-cd5e-4583-9868-e7316f09d987">
											<SHORT-NAME>FrTSynGlobalTimeSlave</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This represents the time slave for the enclosing global time domain.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: FrTSynRxCrcValidated -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="1b9ad55b-62e9-47f1-8975-8e79a79c98cc">
													<SHORT-NAME>FrTSynRxCrcValidated</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter controls whether or not CRC validation shall be supported.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="6715019f-2909-4e71-b26a-4c4b0f0793e6">
															<SHORT-NAME>CRC_IGNORED</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="939dc04d-ad71-4598-a40c-4cce85dc1a57">
															<SHORT-NAME>CRC_NOT_VALIDATED</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="1004adaf-2a16-442d-8dfa-4c8fdadf81c2">
															<SHORT-NAME>CRC_VALIDATED</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
											</PARAMETERS>
											<SUB-CONTAINERS>
												<!-- Container Definition: FrTSynGlobalTimeSlavePdu -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="b9ed61bc-d1f3-4624-aec4-f782c64e66d7">
													<SHORT-NAME>FrTSynGlobalTimeSlavePdu</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container carries all properties required to configure the PDU received by the time slave for the given global time domain.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: FrTSynGlobalTimeSlaveHandleId -->
														<ECUC-INTEGER-PARAM-DEF UUID="82b3005b-09cd-49ab-81ca-8675dc9251bd">
															<SHORT-NAME>FrTSynGlobalTimeSlaveHandleId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This represents the handle ID of the PDU that contains the global time information.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>0</DEFAULT-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: FrTSynGlobalTimePduRef -->
														<ECUC-REFERENCE-DEF UUID="c1f89a23-dd78-46bb-863e-80213c975069">
															<SHORT-NAME>FrTSynGlobalTimePduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This represents the reference to the Pdu taken to transmit the global time information. The global time master of a global time domain acts as the sender of the Pdu while all the time slaves are supposed to receive the Pdu.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
