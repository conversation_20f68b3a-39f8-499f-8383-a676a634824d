Manifest-Version: 1.0
Automatic-Module-Name: org.eclipse.jdt.debug
Bundle-SymbolicName: org.eclipse.jdt.debug; singleton:=true
Built-By: genie.releng
Require-Bundle: org.eclipse.core.resources;bundle-version="[3.5.0,4.0.
 0)",org.eclipse.debug.core;bundle-version="[3.12.0,4.0.0)",org.eclips
 e.jdt.core;bundle-version="[3.18.0,4.0.0)",org.eclipse.core.runtime;b
 undle-version="[3.11.0,4.0.0)",org.eclipse.core.expressions;bundle-ve
 rsion="[3.4.0,4.0.0)"
Bundle-ManifestVersion: 2
Bundle-ActivationPolicy: lazy
Bundle-RequiredExecutionEnvironment: JavaSE-1.8
Eclipse-SourceReferences: scm:git:git://git.eclipse.org/gitroot/jdt/ec
 lipse.jdt.debug.git;path="org.eclipse.jdt.debug";tag="*********-0805"
 ;commitId=e045ffff028c71d51f97538113250975ebb89c66
Bundle-Vendor: %providerName
Import-Package: com.ibm.icu.text
Export-Package: com.sun.jdi;x-friends:="org.eclipse.jdt.debug.ui,org.e
 clipse.jdt.launching",com.sun.jdi.connect;x-friends:="org.eclipse.jdt
 .debug.ui,org.eclipse.jdt.launching",com.sun.jdi.connect.spi;x-friend
 s:="org.eclipse.jdt.debug.ui,org.eclipse.jdt.launching",com.sun.jdi.e
 vent;x-friends:="org.eclipse.jdt.debug.ui,org.eclipse.jdt.launching",
 com.sun.jdi.request;x-friends:="org.eclipse.jdt.debug.ui,org.eclipse.
 jdt.launching",org.eclipse.jdi,org.eclipse.jdi.hcr,org.eclipse.jdi.in
 ternal;x-friends:="org.eclipse.jdt.debug.ui",org.eclipse.jdi.internal
 .connect;x-friends:="org.eclipse.jdt.debug.ui",org.eclipse.jdi.intern
 al.event;x-friends:="org.eclipse.jdt.debug.ui",org.eclipse.jdi.intern
 al.jdwp;x-friends:="org.eclipse.jdt.debug.ui",org.eclipse.jdi.interna
 l.request;x-friends:="org.eclipse.jdt.debug.ui",org.eclipse.jdi.inter
 nal.spy;x-friends:="org.eclipse.jdt.debug.ui",org.eclipse.jdt.debug.c
 ore,org.eclipse.jdt.debug.eval,org.eclipse.jdt.internal.debug.core;x-
 friends:="org.eclipse.jdt.debug.ui,org.eclipse.jdt.launching",org.ecl
 ipse.jdt.internal.debug.core.breakpoints;x-friends:="org.eclipse.jdt.
 debug.ui",org.eclipse.jdt.internal.debug.core.hcr;x-friends:="org.ecl
 ipse.jdt.debug.ui",org.eclipse.jdt.internal.debug.core.logicalstructu
 res;x-friends:="org.eclipse.jdt.debug.ui",org.eclipse.jdt.internal.de
 bug.core.model;x-friends:="org.eclipse.jdt.debug.ui",org.eclipse.jdt.
 internal.debug.eval;x-friends:="org.eclipse.jdt.debug.ui",org.eclipse
 .jdt.internal.debug.eval.ast.engine;x-friends:="org.eclipse.jdt.debug
 .ui",org.eclipse.jdt.internal.debug.eval.ast.instructions;x-friends:=
 "org.eclipse.jdt.debug.ui"
Bundle-Name: %pluginName
Bundle-Version: 3.13.100.v20190902-1050
Bundle-ClassPath: jdi.jar,jdimodel.jar,tools.jar
Bundle-Localization: plugin
Bundle-Activator: org.eclipse.jdt.internal.debug.core.JDIDebugPlugin
Created-By: Apache Maven 3.5.4
Build-Jdk: 1.8.0_192
Eclipse-BundleShape: dir

Name: plugin.xml
SHA-256-Digest: pjJZPlOS3jUhHLzNJRHiOQerFDwf1rEVQsMH5LnTCag=

Name: about.html
SHA-256-Digest: 7mbS+ztMDS7S5/aYvJ7U49bPd/pcr0CtAylhJdsaxfA=

Name: plugin.properties
SHA-256-Digest: Z2ERadz5oHR6m2D0wWZDwsJJ7NlYo+osS6eRSYBZ5vg=

Name: .api_description
SHA-256-Digest: P2tG4atDf1KMoQqfIsgYianNGgwSEwingGiwhIc1TMg=

Name: jdi.jar
SHA-256-Digest: kvd37/+a09KPcA7moyLl+A7ZoB/8TAClK1Nw2jOI3tU=

Name: .options
SHA-256-Digest: BdMc2fx0WAAPoLCGV+a1fjhn3Cvf16tlkGpUODbAjjM=

Name: jdimodel.jar
SHA-256-Digest: q+VxN2YaP/Fj8ktvt+piGp65MTygUJaQPA9Z+CON5H8=

