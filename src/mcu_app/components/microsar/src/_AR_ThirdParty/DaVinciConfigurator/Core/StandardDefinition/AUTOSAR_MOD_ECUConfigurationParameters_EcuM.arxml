<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: EcuM -->
						<ECUC-MODULE-DEF UUID="ECUC:2cef58ce-79fe-4a40-935b-ee76b6a60605">
							<SHORT-NAME>EcuM</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the EcuM (ECU State Manager) module.</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.2.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
										<DATE>2011-11-09</DATE>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-POST-BUILD</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: EcuMConfiguration -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:16fb469f-1f19-45e7-b4e5-555ae0f14089">
									<SHORT-NAME>EcuMConfiguration</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container contains the configuration (parameters) of the ECU State Manager.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>true</MULTIPLE-CONFIGURATION-CONTAINER>
									<SUB-CONTAINERS>
										<!-- Container Definition: EcuMCommonConfiguration -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:8e610dbe-ea33-4a18-bca8-c4d70a9da2f1">
											<SHORT-NAME>EcuMCommonConfiguration</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the common configuration (parameters) of the ECU State Manager.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: EcuMConfigConsistencyHash -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:0bae5ee1-7bcc-4635-a400-d245750296dd">
													<SHORT-NAME>EcuMConfigConsistencyHash</SHORT-NAME>
													<DESC>
														<L-2 L="EN">A hash value generated across all pre-compile and link-time parameters of all BSW modules. This hash value is compared against a field in the EcuM_ConfigType and hence allows checking the consistency of the entire configuration.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>18446744073709551615</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: EcuMDefaultAppMode -->
												<ECUC-REFERENCE-DEF UUID="ECUC:65c6e769-6d64-4d96-8675-7983ccee0258">
													<SHORT-NAME>EcuMDefaultAppMode</SHORT-NAME>
													<DESC>
														<L-2 L="EN">The default application mode loaded when the ECU comes out of reset.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsAppMode</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<!-- Reference Definition: EcuMOSResource -->
												<ECUC-REFERENCE-DEF UUID="ECUC:c2dcaae0-bc92-410f-bdb9-adf1d91ecffc">
													<SHORT-NAME>EcuMOSResource</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter is a reference to a OS ressource which is used to bring the ECU into sleep mode.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">In case of multi core each core shall have an own OsResource.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsResource</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
											<SUB-CONTAINERS>
												<!-- Container Definition: EcuMDefaultShutdownTarget -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:aebde982-c84c-47f2-ac81-1989258cbaef">
													<SHORT-NAME>EcuMDefaultShutdownTarget</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container describes the default shutdown target to be selected by EcuM. The actual shutdown target may be overridden by the EcuM_SelectShutdownTarget service.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: EcuMDefaultState -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:ccac5543-c698-41bb-869a-fa1a6acbf26f">
															<SHORT-NAME>EcuMDefaultState</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter describes the state part of the default shutdown target selected when the ECU comes out of reset. If EcuMStateSleep is selected, the parameter EcuMDefaultSleepModeRef selects the specific sleep mode.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:f36aaca6-a0cc-925d-58a1-4862fed97f58">
																	<SHORT-NAME>EcuMStateOff</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:c45941d2-2cca-89f2-5062-070ec82de00a">
																	<SHORT-NAME>EcuMStateReset</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:812918e0-5f3d-9111-6062-4fc555392f17">
																	<SHORT-NAME>EcuMStateSleep</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Symbolic Name Reference Definition: EcuMDefaultResetModeRef -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:68b39a3a-9e3c-40c0-8b2f-d30262687598">
															<SHORT-NAME>EcuMDefaultResetModeRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">If EcuMDefaultShutdownTarget is EcuMStateReset, this parameter selects the default reset mode. Otherwise this parameter may be ignored.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMFlexConfiguration/EcuMResetMode</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
														<!-- Symbolic Name Reference Definition: EcuMDefaultSleepModeRef -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:7cf854cf-a9cb-4ead-8eae-96bd1592bd0a">
															<SHORT-NAME>EcuMDefaultSleepModeRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">If EcuMDefaultShutdownTarget is EcuMStateSleep, this parameter selects the default sleep mode. Otherwise this parameter may be ignored.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMSleepMode</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: EcuMDemEventParameterRefs -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:4f500e7d-fe92-4b94-8eed-941c9828f1aa">
													<SHORT-NAME>EcuMDemEventParameterRefs</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container for the references to DemEventParameter elements which shall be invoked using the API Dem_ReportErrorStatus in case the corresponding error occurs. The EventId is taken from the referenced DemEventParameter&apos;s DemEventId value.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">The standardized errors are provided in this container and can be extended by vendor specific error references.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<REFERENCES>
														<!-- Symbolic Name Reference Definition: ECUM_E_ALL_RUN_REQUESTS_KILLED -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:179e9f2b-bbea-4ec1-994c-cec0fe876e0e">
															<SHORT-NAME>ECUM_E_ALL_RUN_REQUESTS_KILLED</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the DemEventParameter which shall be issued when the error &quot;ECUM_E_ALL_RUN_REQUESTS_KILLED&quot; has occured.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
														<!-- Symbolic Name Reference Definition: ECUM_E_CONFIGURATION_DATA_INCONSISTENT -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:112330d7-eec4-4496-8b57-5c65e6e0b079">
															<SHORT-NAME>ECUM_E_CONFIGURATION_DATA_INCONSISTENT</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the DemEventParameter which shall be issued when the error &quot;ECUM_E_CONFIGURATION_DATA_INCONSISTENT&quot; has occured.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
														<!-- Symbolic Name Reference Definition: ECUM_E_RAM_CHECK_FAILED -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:61a2ab39-ed3f-42e5-9ec4-2f9920eb091a">
															<SHORT-NAME>ECUM_E_RAM_CHECK_FAILED</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the DemEventParameter which shall be issued when the error &quot;ECUM_E_RAM_CHECK_FAILED&quot; has occured.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: EcuMDriverInitListOne -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e108fe98-dc5c-4f3c-9d9e-5a4989fb20d0">
													<SHORT-NAME>EcuMDriverInitListOne</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container for Init Block I.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This container holds a list of module IDs that will be initialised. Each module in the list will be called for initialisation in the list order.

                                                All modules in this list are initilialised before the OS is started and so these modules require no OS support.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<SUB-CONTAINERS>
														<!-- Container Definition: EcuMDriverInitItem -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:b37908f7-5536-96bf-545e-fa7e8d6d11e0">
															<SHORT-NAME>EcuMDriverInitItem</SHORT-NAME>
															<DESC>
																<L-2 L="EN">These containers describe the entries in a driver init list.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: EcuMModuleID -->
																<ECUC-STRING-PARAM-DEF UUID="ECUC:d3117de4-d573-e62d-12aa-c0febc0b1cc6">
																	<SHORT-NAME>EcuMModuleID</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Short name of the module to be initialized, e.g. Mcu, Gpt etc.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<ECUC-STRING-PARAM-DEF-VARIANTS>
																		<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
																	</ECUC-STRING-PARAM-DEF-VARIANTS>
																</ECUC-STRING-PARAM-DEF>
																<!-- PARAMETER DEFINITION: EcuMModuleService -->
																<ECUC-STRING-PARAM-DEF UUID="ECUC:f7b11899-50e8-e38b-ef40-30412ee7e758">
																	<SHORT-NAME>EcuMModuleService</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">The service to be called to initialize that module, e.g. Init, PreInit, Start etc.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">If the service is Init and the parameter EcuMModuleConfigurationRef has been set for that module, the corresponding pointer to the init structure (&lt;Module&gt;_ConfigType) and in case of multiple instantiation an uint8 value to identify the instance of the module(&lt;MSN&gt;_CtrlIdx) shall be passed as arguments.</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<ECUC-STRING-PARAM-DEF-VARIANTS>
																		<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
																	</ECUC-STRING-PARAM-DEF-VARIANTS>
																</ECUC-STRING-PARAM-DEF>
															</PARAMETERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: EcuMDriverInitListZero -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:320a0bfb-905f-484f-89cb-c8938c1921b4">
													<SHORT-NAME>EcuMDriverInitListZero</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container for Init Block 0.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This container holds a list of module IDs that will be initialised. Each module in the list will be called for initialisation in the list order.

                                                All modules in this list are initilialised before the post-build configuration has been loaded and the OS is initialized. Therefore, these modules may not use post-build configuration.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<SUB-CONTAINERS>
														<!-- Container Definition: EcuMDriverInitItem -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:047a165a-0939-8fd2-408c-68c88f8b12c4">
															<SHORT-NAME>EcuMDriverInitItem</SHORT-NAME>
															<DESC>
																<L-2 L="EN">These containers describe the entries in a driver init list.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: EcuMModuleID -->
																<ECUC-STRING-PARAM-DEF UUID="ECUC:24128b47-8976-df40-fed8-2f48be291daa">
																	<SHORT-NAME>EcuMModuleID</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Short name of the module to be initialized, e.g. Mcu, Gpt etc.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<ECUC-STRING-PARAM-DEF-VARIANTS>
																		<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
																	</ECUC-STRING-PARAM-DEF-VARIANTS>
																</ECUC-STRING-PARAM-DEF>
																<!-- PARAMETER DEFINITION: EcuMModuleService -->
																<ECUC-STRING-PARAM-DEF UUID="ECUC:48b225fc-04eb-dc9e-db6d-9e8b3105e83c">
																	<SHORT-NAME>EcuMModuleService</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">The service to be called to initialize that module, e.g. Init, PreInit, Start etc.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">If the service is Init and the parameter EcuMModuleConfigurationRef has been set for that module, the corresponding pointer to the init structure (&lt;Module&gt;_ConfigType) and in case of multiple instantiation an uint8 value to identify the instance of the module(&lt;MSN&gt;_CtrlIdx) shall be passed as arguments.</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<ECUC-STRING-PARAM-DEF-VARIANTS>
																		<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
																	</ECUC-STRING-PARAM-DEF-VARIANTS>
																</ECUC-STRING-PARAM-DEF>
															</PARAMETERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: EcuMDriverRestartList -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:a32e3888-bdad-4685-bcb5-18248c199379">
													<SHORT-NAME>EcuMDriverRestartList</SHORT-NAME>
													<DESC>
														<L-2 L="EN">List of module IDs.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">EcuM2719: A configuration tool shall fill the callout EcuM_AL_DriverRestart with initialization calls to the listed drivers in the order in which they occur in the list.
                                                EcuM2720: Entries in this list must appear in the same order as in the combined list of EcuM_DriverInitListOne and EcuM_DriverInitListTwo. This list may be a real subset though. In all other cases, the generation tool shall report an error.
                                                The included container has the same structure as EcuM_DriverInitItem</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<SUB-CONTAINERS>
														<!-- Container Definition: EcuMDriverInitItem -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:759e42e7-3687-8e08-7375-b8598f8b8489">
															<SHORT-NAME>EcuMDriverInitItem</SHORT-NAME>
															<DESC>
																<L-2 L="EN">These containers describe the entries in a driver init list.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: EcuMModuleID -->
																<ECUC-STRING-PARAM-DEF UUID="ECUC:9536b7d4-b6c4-dd76-31c1-7ed9be298f6f">
																	<SHORT-NAME>EcuMModuleID</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Short name of the module to be initialized, e.g. Mcu, Gpt etc.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<ECUC-STRING-PARAM-DEF-VARIANTS>
																		<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
																	</ECUC-STRING-PARAM-DEF-VARIANTS>
																</ECUC-STRING-PARAM-DEF>
																<!-- PARAMETER DEFINITION: EcuMModuleService -->
																<ECUC-STRING-PARAM-DEF UUID="ECUC:b9d65289-3239-dad4-0e56-ee1c31065a01">
																	<SHORT-NAME>EcuMModuleService</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">The service to be called to initialize that module, e.g. Init, PreInit, Start etc.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">If the service is Init and the parameter EcuMModuleConfigurationRef has been set for that module, the corresponding pointer to the init structure (&lt;Module&gt;_ConfigType) and in case of multiple instantiation an uint8 value to identify the instance of the module(&lt;MSN&gt;_CtrlIdx) shall be passed as arguments.</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<ECUC-STRING-PARAM-DEF-VARIANTS>
																		<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
																	</ECUC-STRING-PARAM-DEF-VARIANTS>
																</ECUC-STRING-PARAM-DEF>
															</PARAMETERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: EcuMSleepMode -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:a9cbddde-5cd2-4616-9604-6041afbe9990">
													<SHORT-NAME>EcuMSleepMode</SHORT-NAME>
													<DESC>
														<L-2 L="EN">These containers describe the configured sleep modes.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">The names of these containers specify the symbolic names of the different sleep modes.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: EcuMSleepModeId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:6575d26f-25a3-419a-a1f9-73e213842f07">
															<SHORT-NAME>EcuMSleepModeId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This ID identifies this sleep mode in services like EcuM_SelectShutdownTarget.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: EcuMSleepModeSuspend -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:3da7b115-6bc9-4855-a744-83e78ae2f17b">
															<SHORT-NAME>EcuMSleepModeSuspend</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Flag, which is set true, if the CPU is suspended, halted, or powered off in the sleep mode. If the CPU keeps running in this sleep mode, then this flag must be set to false.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Symbolic Name Reference Definition: EcuMSleepModeMcuModeRef -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:f6907e19-209e-44bc-b8f4-08b7922a9adb">
															<SHORT-NAME>EcuMSleepModeMcuModeRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter is a reference to the corresponding MCU mode for this sleep mode.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuModeSettingConf</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
														<!-- Symbolic Name Reference Definition: EcuMWakeupSourceMask -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:35ef3ce4-a034-4906-8400-3b4b02eaa0b8">
															<SHORT-NAME>EcuMWakeupSourceMask</SHORT-NAME>
															<DESC>
																<L-2 L="EN">These parameters are references to the wakeup sources that shall be enabled for this sleep mode.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMWakeupSource</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: EcuMWakeupSource -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:0a729dd6-2641-4d68-95a0-5a26ca418457">
													<SHORT-NAME>EcuMWakeupSource</SHORT-NAME>
													<DESC>
														<L-2 L="EN">These containers describe the configured wakeup sources.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: EcuMValidationTimeout -->
														<ECUC-FLOAT-PARAM-DEF UUID="ECUC:db82044d-d3a9-40b9-a9f5-6d2f788d3266">
															<SHORT-NAME>EcuMValidationTimeout</SHORT-NAME>
															<DESC>
																<L-2 L="EN">The validation timeout (period for which the ECU State Manager will wait for the validation of a wakeup event) can be defined for each wakeup source independently. The timeout is specified in seconds.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">When the timeout is not instantiated, there is no validation routine and the ECU Manager shall not validate the wakeup source.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>Inf</MAX>
															<MIN>0</MIN>
														</ECUC-FLOAT-PARAM-DEF>
														<!-- PARAMETER DEFINITION: EcuMWakeupSourceId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:bdc9686e-5683-43b0-95b2-0f1cd8114964">
															<SHORT-NAME>EcuMWakeupSourceId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines the identifier of this wakeup source.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>31</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: EcuMWakeupSourcePolling -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:5af33790-**************-195b1e09d64a">
															<SHORT-NAME>EcuMWakeupSourcePolling</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter describes if the wakeup source needs polling.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Symbolic Name Reference Definition: EcuMComMChannelRef -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:7ad19fd1-f48a-43da-9491-0efb37cd51df">
															<SHORT-NAME>EcuMComMChannelRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter is a reference to a Network (channel) defined in the Communication Manager. No reference indicates that the wakeup source is not a communication channel.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/ComM/ComMConfigSet/ComMChannel</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
														<!-- Symbolic Name Reference Definition: EcuMResetReasonRef -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:be6be3ac-3dd8-4fb4-9890-c767597eb3ef">
															<SHORT-NAME>EcuMResetReasonRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter describes the mapping of reset reasons detected by the MCU driver into wakeup sources.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuPublishedInformation/McuResetReasonConf</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: EcuMFixedConfiguration -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:df793d0e-5c7f-4d1f-bbfe-b2caf6cf9d01">
											<SHORT-NAME>EcuMFixedConfiguration</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the configuration (parameters) of the EcuMFixed.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Only applicable if EcuMFixed is implemented.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: EcuMNvramReadallTimeout -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:18ebaefe-ddcf-47af-afa4-e15ec35d34b2">
													<SHORT-NAME>EcuMNvramReadallTimeout</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Period given in seconds for which the ECU State Manager will wait until it considers a ReadAll job of the NVRAM Manager as failed.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>Inf</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: EcuMNvramWriteallTimeout -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:e0d3746c-c2d8-4c81-b84b-83d3b7476c03">
													<SHORT-NAME>EcuMNvramWriteallTimeout</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Period given in seconds for which the ECU State Manager will wait until it considers a WriteAll job of the NVRAM Manager as failed.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>Inf</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: EcuMRunMinimumDuration -->
												<ECUC-FLOAT-PARAM-DEF UUID="ECUC:a4efee28-153a-4570-b592-7f3b06c25f7d">
													<SHORT-NAME>EcuMRunMinimumDuration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Duration given in seconds for which the ECU State Manager will</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">stay in RUN state even when no one requests RUN. This duration should be long at least as long as a SW-Cs needs to request RUN.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>Inf</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Choice Reference Definition: EcuMFixedModuleConfigurationRef -->
												<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:339c0a67-2745-468f-84b9-50ce45393058">
													<SHORT-NAME>EcuMFixedModuleConfigurationRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">These parameters contain references to the init structure of the corresponding BSW module.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REFS>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Adc/AdcConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Can/CanConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/CanIf/CanIfInitCfg</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/CanNm/CanNmGlobalConfig</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/CanSM/CanSMConfiguration</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/CanTp/CanTpConfig</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/CanTrcv/CanTrcvConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Com/ComConfig</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/ComM/ComMConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dbg/DbgMultipleConfigurationContainer</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dcm/DcmConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dio/DioConfig</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dlt/DltMultipleConfigurationContainer</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Eep/EepInitConfiguration</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Eth/EthConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EthIf/EthIfConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EthTrcv/EthTrcvConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/FiM/FiMConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Fls/FlsConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/FlsTst/FlsTstConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/FrArTp/FrArTpMultipleConfig</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/FrIf/FrIfConfig</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Fr/FrMultipleConfiguration</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/FrNm/FrNmChannelConfig</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/FrSM/FrSMConfig</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/FrTp/FrTpMultipleConfig</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Gpt/GptChannelConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Icu/IcuConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/IpduM/IpduMConfig</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/J1939Tp/J1939TpConfiguration</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Lin/LinGlobalConfig</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/LinIf/LinIfGlobalConfig</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/LinSM/LinSMConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/LinTp/LinTpGlobalConfig</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/PduR/PduRRoutingTables</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Port/PortConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Pwm/PwmChannelConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Rte/RtePostBuildVariantConfiguration</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/SoAd/SoAdDoIpConfig</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Spi/SpiDriver</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/UdpNm/UdpNmGlobalConfig</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/WdgM/WdgMConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Wdg/WdgSettingsConfig</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Xcp/XcpConfig</DESTINATION-REF>
													</DESTINATION-REFS>
												</ECUC-CHOICE-REFERENCE-DEF>
												<!-- Symbolic Name Reference Definition: EcuMComMCommunicationAllowedList -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:c1f1e7ec-cac9-4949-921e-cbc8701996a8">
													<SHORT-NAME>EcuMComMCommunicationAllowedList</SHORT-NAME>
													<DESC>
														<L-2 L="EN">These parameters contain references to the ComMChannels for which EcuM has to call ComM_CommunicationAllowed.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/ComM/ComMConfigSet/ComMChannel</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
												<!-- Symbolic Name Reference Definition: EcuMNormalMcuModeRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:da472057-880c-4613-9d87-e4d06c173ae6">
													<SHORT-NAME>EcuMNormalMcuModeRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter is a reference to the normal MCU mode to be restored after a sleep.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuModeSettingConf</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
											<SUB-CONTAINERS>
												<!-- Container Definition: EcuMDriverInitListThree -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:a83f64ee-812b-4e3b-b0d6-c4f289040010">
													<SHORT-NAME>EcuMDriverInitListThree</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container for Init Block III.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This container holds a list of module IDs that will be initialised. Each module in the list will be called for initialisation in the list order.

                                                All modules in this list are initilialised after the OS is started and so these modules may use OS support. These modules may also rely on the Nvram ReadAll job to have provided all data.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<SUB-CONTAINERS>
														<!-- Container Definition: EcuMDriverInitItem -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:7aaf6f4c-fa05-95be-6797-65278c75f120">
															<SHORT-NAME>EcuMDriverInitItem</SHORT-NAME>
															<DESC>
																<L-2 L="EN">These containers describe the entries in a driver init list.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: EcuMModuleID -->
																<ECUC-STRING-PARAM-DEF UUID="ECUC:9a47e43a-7a42-e52c-25e3-2ba7bb13fc06">
																	<SHORT-NAME>EcuMModuleID</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Short name of the module to be initialized, e.g. Mcu, Gpt etc.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<ECUC-STRING-PARAM-DEF-VARIANTS>
																		<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
																	</ECUC-STRING-PARAM-DEF-VARIANTS>
																</ECUC-STRING-PARAM-DEF>
																<!-- PARAMETER DEFINITION: EcuMModuleService -->
																<ECUC-STRING-PARAM-DEF UUID="ECUC:bee77eee-f5b7-e28a-0278-9aea2df0c698">
																	<SHORT-NAME>EcuMModuleService</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">The service to be called to initialize that module, e.g. Init, PreInit, Start etc.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">If the service is Init and the parameter EcuMModuleConfigurationRef has been set for that module, the corresponding pointer to the init structure (&lt;Module&gt;_ConfigType) and in case of multiple instantiation an uint8 value to identify the instance of the module(&lt;MSN&gt;_CtrlIdx) shall be passed as arguments.</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<ECUC-STRING-PARAM-DEF-VARIANTS>
																		<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
																	</ECUC-STRING-PARAM-DEF-VARIANTS>
																</ECUC-STRING-PARAM-DEF>
															</PARAMETERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: EcuMDriverInitListTwo -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:0f68e708-3206-4ec1-9bd7-c829398045a8">
													<SHORT-NAME>EcuMDriverInitListTwo</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container for Init Block II.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This container holds a list of module IDs that will be initialised. Each module in the list will be called for initialisation in the list order.

                                                All modules in this list are initilialised after the OS is started and so these modules may use OS support. These modules may not rely on the Nvram ReadAll job to have provided all data.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<SUB-CONTAINERS>
														<!-- Container Definition: EcuMDriverInitItem -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e1d8f166-aae0-9644-5298-685e3cf236b8">
															<SHORT-NAME>EcuMDriverInitItem</SHORT-NAME>
															<DESC>
																<L-2 L="EN">These containers describe the entries in a driver init list.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: EcuMModuleID -->
																<ECUC-STRING-PARAM-DEF UUID="ECUC:01716654-2b1d-e5b2-10e4-2ede6b90419e">
																	<SHORT-NAME>EcuMModuleID</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Short name of the module to be initialized, e.g. Mcu, Gpt etc.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<ECUC-STRING-PARAM-DEF-VARIANTS>
																		<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
																	</ECUC-STRING-PARAM-DEF-VARIANTS>
																</ECUC-STRING-PARAM-DEF>
																<!-- PARAMETER DEFINITION: EcuMModuleService -->
																<ECUC-STRING-PARAM-DEF UUID="ECUC:26110108-a692-e310-ed79-9e20de6d0c30">
																	<SHORT-NAME>EcuMModuleService</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">The service to be called to initialize that module, e.g. Init, PreInit, Start etc.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">If the service is Init and the parameter EcuMModuleConfigurationRef has been set for that module, the corresponding pointer to the init structure (&lt;Module&gt;_ConfigType) and in case of multiple instantiation an uint8 value to identify the instance of the module(&lt;MSN&gt;_CtrlIdx) shall be passed as arguments.</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<ECUC-STRING-PARAM-DEF-VARIANTS>
																		<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
																	</ECUC-STRING-PARAM-DEF-VARIANTS>
																</ECUC-STRING-PARAM-DEF>
															</PARAMETERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: EcuMFixedUserConfig -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:6b792715-f9f3-4d2e-a927-5b9685082fbc">
													<SHORT-NAME>EcuMFixedUserConfig</SHORT-NAME>
													<DESC>
														<L-2 L="EN">These containers describe the identifiers that are needed to refer to a software component or another appropriate entity in the system which is designated to request the RUN state. Application requestors refer to entities above RTE, system requestors to entities below RTE (e.g. Communication Manager).</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: EcuMFixedUser -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:08fe9652-c3bc-4795-87d0-6a44d67c541e">
															<SHORT-NAME>EcuMFixedUser</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Parameter used to identify one user.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: EcuMTTII -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:f21cd27b-2d15-499c-861f-6495cccfc5ca">
													<SHORT-NAME>EcuMTTII</SHORT-NAME>
													<DESC>
														<L-2 L="EN">These containers describe the structures and the following configuration items describe its elements. These structures are concatenated to build a list as indicated by Figure 27 - Configuration Container Diagram.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">The list must contain at least one element when ECUM_TTII_ENABLED is set to true.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: EcuMDivisor -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:a3491391-905c-4db0-833b-00a997c65d1f">
															<SHORT-NAME>EcuMDivisor</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines the divisor preload value.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>18446744073709551615</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Symbolic Name Reference Definition: EcuMSleepModeRef -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:b2b38b93-dca9-4b83-8eb5-a18d722c300d">
															<SHORT-NAME>EcuMSleepModeRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This configuration parameter is a reference to a configured sleep mode that is used for TTII.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMSleepMode</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
														<!-- Symbolic Name Reference Definition: EcuMSuccessorRef -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:e0264bd4-2ca3-42fc-b149-44a8d3014a41">
															<SHORT-NAME>EcuMSuccessorRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter is a reference to the next sleep mode in the TTII protocol.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMSleepMode</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: EcuMFlexConfiguration -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e088e752-6fde-4e09-9190-f0619180e9b4">
											<SHORT-NAME>EcuMFlexConfiguration</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the configuration (parameters) of the EcuMFlex.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Only applicable if EcuMFlex is implemented.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<REFERENCES>
												<!-- Choice Reference Definition: EcuMFlexModuleConfigurationRef -->
												<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:e927b74c-cd3f-496d-b60a-2e1d8628e268">
													<SHORT-NAME>EcuMFlexModuleConfigurationRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">These parameters contain references to the init structure of the corresponding BSW module.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REFS>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Adc/AdcConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Can/CanConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Fls/FlsConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Gpt/GptChannelConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Icu/IcuConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Lin/LinGlobalConfig</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Port/PortConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Pwm/PwmChannelConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Spi/SpiDriver</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/WdgM/WdgMConfigSet</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Wdg/WdgSettingsConfig</DESTINATION-REF>
													</DESTINATION-REFS>
												</ECUC-CHOICE-REFERENCE-DEF>
												<!-- Symbolic Name Reference Definition: EcuMNormalMcuModeRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:ef0eb80f-8127-45a4-8e24-04744d48a5f8">
													<SHORT-NAME>EcuMNormalMcuModeRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter is a reference to the normal MCU mode to be restored after a sleep.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuModeSettingConf</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
											<SUB-CONTAINERS>
												<!-- Container Definition: EcuMAlarmClock -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:750dd6de-13ec-48cf-befd-615fde9145b3">
													<SHORT-NAME>EcuMAlarmClock</SHORT-NAME>
													<DESC>
														<L-2 L="EN">These containers describe the configured alarm clocks.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">The name of these conatiners allows giving a symbolic name to one alarm clock.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: EcuMAlarmClockId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:49861b32-83d2-464f-afd2-24fb12a3e1e0">
															<SHORT-NAME>EcuMAlarmClockId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This ID identifies this alarmclock.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: EcuMAlarmClockTimeOut -->
														<ECUC-FLOAT-PARAM-DEF UUID="ECUC:9677f083-e76f-486e-8634-df1555753290">
															<SHORT-NAME>EcuMAlarmClockTimeOut</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter allows to define a timout for this alarm clock.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>Inf</MAX>
															<MIN>0</MIN>
														</ECUC-FLOAT-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Symbolic Name Reference Definition: EcuMAlarmClockUser -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:e6ff4bc0-7648-4128-b239-de3f273d33d2">
															<SHORT-NAME>EcuMAlarmClockUser</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter allows an alarm to be assigned to a user.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMFlexConfiguration/EcuMFlexUserConfig</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: EcuMFlexUserConfig -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:3058925d-af3b-4a8a-9252-bd6700407009">
													<SHORT-NAME>EcuMFlexUserConfig</SHORT-NAME>
													<DESC>
														<L-2 L="EN">These containers describe the identifiers that are needed to refer to a software component or another appropriate entity in the system which uses the EcuMFlex Interfaces.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: EcuMFlexUser -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:7cefee01-1e46-45e6-b582-678888f226cc">
															<SHORT-NAME>EcuMFlexUser</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Parameter used to identify one user.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: EcuMFlexEcucPartitionRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:fe138ccd-1882-4c4c-ad96-ce9be41c7a07">
															<SHORT-NAME>EcuMFlexEcucPartitionRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Denotes in which &quot;EcucPartition&quot; the user of the EcuM is executed.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPartitionCollection/EcucPartition</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: EcuMGoDownAllowedUsers -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:7d3d53a3-4f3d-4595-9bd9-4c2e42819a48">
													<SHORT-NAME>EcuMGoDownAllowedUsers</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container describes the collection of allowed users which are allowed to call the EcuM_GoDown API.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<REFERENCES>
														<!-- Symbolic Name Reference Definition: EcuMGoDownAllowedUserRef -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:79119397-b629-4210-a818-6071972fb77f">
															<SHORT-NAME>EcuMGoDownAllowedUserRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">These parameters describe the references to the users which are allowed to call the EcuM_GoDown API.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMFlexConfiguration/EcuMFlexUserConfig</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: EcuMResetMode -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:f2f88c2c-79a4-47d4-8e3a-750f1bb3624e">
													<SHORT-NAME>EcuMResetMode</SHORT-NAME>
													<DESC>
														<L-2 L="EN">These containers describe the configured reset modes.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">The name of these containers allows one of the following symbolic names to be given to the different reset modes:
                                                - ECUM_RESET_MCU
                                                - ECUM_RESET_WDGM
                                                - ECUM_RESET_IO.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: EcuMResetModeId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:0a945601-d809-4f20-9ffe-e90e4d9a60a4">
															<SHORT-NAME>EcuMResetModeId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This ID identifies this reset mode in services like EcuM_SelectShutdownTarget.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: EcuMSetClockAllowedUsers -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:b2941bdd-11fe-440e-a773-bce84948a4fd">
													<SHORT-NAME>EcuMSetClockAllowedUsers</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container describes the collection of allowed users which are allowed to call the EcuM_SetClock API.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<REFERENCES>
														<!-- Symbolic Name Reference Definition: EcuMSetClockAllowedUserRef -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:6c8dce8f-f2da-4d1f-9a06-da9d9829e14e">
															<SHORT-NAME>EcuMSetClockAllowedUserRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">These parameters describe the references to the users which are allowed to call the EcuM_SetClock API.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMFlexConfiguration/EcuMFlexUserConfig</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: EcuMShutdownCause -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:76c32704-2514-4828-a00c-c26c6ae45534">
													<SHORT-NAME>EcuMShutdownCause</SHORT-NAME>
													<DESC>
														<L-2 L="EN">These containers describe the configured shut down or reset causes.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">The name of these containers allows to give one of the following symbolic names to the diffenrent shut down causes:
                                                - ECUM_CAUSE_ECU_STATE - ECU state machine entered a state for shutdown,
                                                - ECUM_CAUSE_WDGM - WdgM detected failure,
                                                - ECUM_CAUSE_DCM - Dcm requests shutdown (split into UDS services?),
                                                - and values from configuration.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: EcuMShutdownCauseId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:596b234d-0197-46f6-87ee-f6a25635fa91">
															<SHORT-NAME>EcuMShutdownCauseId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This ID identifies this shut down cause.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: EcuMShutdownTarget -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e55d1cfa-47ca-42b7-a22c-38a105284cd1">
													<SHORT-NAME>EcuMShutdownTarget</SHORT-NAME>
													<DESC>
														<L-2 L="EN">These containers describe the configured shut down targets.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">The name of these containers allows to give symbolic names to the different shut down targets.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: EcuMShutdownTargetId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:0d27bade-16c3-4e3e-bbff-b6cf696e01b8">
															<SHORT-NAME>EcuMShutdownTargetId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This ID identifies this shut down target in services like EcuM_SelectShutdownTarget.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: EcuMFixedGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:dbdde2a8-1a59-413e-9bd4-37c1fbfa00d1">
									<SHORT-NAME>EcuMFixedGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container holds the general, pre-compile configuration parameters for the EcuMFixed.</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">Only applicable if EcuMFixed is implemented.</L-1>
										</P>
									</INTRODUCTION>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: EcuMIncludeComM -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:c001d57b-52b5-4868-9fce-780aaae852c3">
											<SHORT-NAME>EcuMIncludeComM</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This configuration parameter defines whether the communication  manager is supported by EcuM. This feature is presented for development purpose to compile out the communication manager in the early debugging phase.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EcuMIncludeNvM -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:429e3d5a-5f8b-4dd3-a3c0-b22879eb3bfa">
											<SHORT-NAME>EcuMIncludeNvM</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This configuration parameter defines whether the non volatile memory manager is supported by EcuM. This feature is presented for development purpose to compile out the volatile memory manager in the early debugging phase.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EcuMIncludeNvramMgr -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:65634043-e1b3-4f7e-bb12-34e0bf9fe643">
											<SHORT-NAME>EcuMIncludeNvramMgr</SHORT-NAME>
											<DESC>
												<L-2 L="EN">If NVRAM manager is enabled but both flash and EEPROM driver are missing, then an error shall be flagged by the configuration tool</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EcuMTTIIEnabled -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:f759ceef-b54a-461b-9878-7fbea25998c8">
											<SHORT-NAME>EcuMTTIIEnabled</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Boolean switch to enable / disable TTII</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Symbolic Name Reference Definition: EcuMTTIIWakeupSourceRef -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:9f2cb71a-8f8b-4b0f-bb52-ccc6ac065b27">
											<SHORT-NAME>EcuMTTIIWakeupSourceRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This configuration parameter references the initial sleep mode to be used by TTII when TTII is activated after a RUN mode.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">EcuM2785: Whenever RUN mode is reached, the TTII protocol shall be reset to use the wakeup source referenced by this parameter.
                                        This configuration parameter is a human readable name for a TTII wakeup source which is only needed by the configuration tool. For imlementation on the ECU, this parameter may be dropped and replaced by a generated list index of EcuM_TTII.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMWakeupSource</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: EcuMFlexGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:2042330b-0dc9-451d-9f4d-48e741cee88f">
									<SHORT-NAME>EcuMFlexGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container holds the general, pre-compile configuration parameters for the EcuMFlex.</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">Only applicable if EcuMFlex is implemented.</L-1>
										</P>
									</INTRODUCTION>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: EcuMAlarmClockPresent -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:b21df21d-f88f-4e68-a784-0b5b0836f989">
											<SHORT-NAME>EcuMAlarmClockPresent</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This flag indicates whether the optional AlarmClock feature is present.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EcuMEnableDefBehaviour -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:7be4a46d-bc7e-4d31-b4f6-93cb16adc9be">
											<SHORT-NAME>EcuMEnableDefBehaviour</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Switches the defensive behaviour on or off.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EcuMResetLoopDetection -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:53a6ceed-b5cc-4bd4-8741-60747b77dd76">
											<SHORT-NAME>EcuMResetLoopDetection</SHORT-NAME>
											<DESC>
												<L-2 L="EN">If false, no reset loop detection is performed.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Symbolic Name Reference Definition: EcuMAlarmWakeupSource -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:824c1dac-d512-4089-aa9e-15bf7d01a829">
											<SHORT-NAME>EcuMAlarmWakeupSource</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter describes the reference to the EcuMWakeupSource being used for the EcuM AlarmClock.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMWakeupSource</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: EcuMGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:fd0b9fcb-4de6-4f6d-9725-5c36640588db">
									<SHORT-NAME>EcuMGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container holds the general, pre-compile configuration parameters.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: EcuMDevErrorDetect -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:07363168-09da-4d24-b4be-b85eba468e64">
											<SHORT-NAME>EcuMDevErrorDetect</SHORT-NAME>
											<DESC>
												<L-2 L="EN">If false, no debug artifacts (e.g. calls to DET) shall remain in the executable object. Initialization of DET, however is controlled by configuration of optional BSW modules.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EcuMIncludeDem -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:af5750f4-9fc7-47b9-bcc2-2e0820256f3c">
											<SHORT-NAME>EcuMIncludeDem</SHORT-NAME>
											<DESC>
												<L-2 L="EN">If enabled, the according BSW module will be included to the ECU State Manager.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EcuMIncludeDet -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:eb2066d7-d0e8-4fc5-bc8e-3a3d35c30a52">
											<SHORT-NAME>EcuMIncludeDet</SHORT-NAME>
											<DESC>
												<L-2 L="EN">If defined, the according BSW module will be initialized by the ECU State Manager</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EcuMMainFunctionPeriod -->
										<ECUC-FLOAT-PARAM-DEF UUID="ECUC:40a1fc47-8607-49ff-be26-3f868176d15f">
											<SHORT-NAME>EcuMMainFunctionPeriod</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the schedule period of EcuM_MainFunction.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Unit: [s]</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>Inf</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: EcuMVersionInfoApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:fa4df206-514e-4273-98fe-65daf9b2536c">
											<SHORT-NAME>EcuMVersionInfoApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Switches the version info API on or off</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
