<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"/>
<title>About</title>
</head>
<body lang="EN-US">
<h2>About This Content</h2>

<p>July 16, 2018</p>
<h3>License</h3>

<p>The Eclipse Foundation makes available all content in this plug-in (&quot;Content&quot;).  Unless otherwise
indicated below, the Content is provided to you under the terms and conditions of the
Eclipse Public License Version 1.0 (&quot;EPL&quot;).  A copy of the EPL is available
at <a href="http://www.eclipse.org/legal/epl-v10.html">http://www.eclipse.org/legal/epl-v10.html</a>.
For purposes of the EPL, &quot;Program&quot; will mean the Content.</p>

<p>If you did not receive this Content directly from the Eclipse Foundation, the Content is
being redistributed by another party (&quot;Redistributor&quot;) and different terms and conditions may
apply to your use of any object code in the Content.  Check the Redistributor's license that was
provided with the Content.  If no such license exists, contact the Redistributor.  Unless otherwise
indicated below, the terms and conditions of the EPL still apply to any source code in the Content
and such source code may be obtained at <a href="http://www.eclipse.org/">http://www.eclipse.org</a>.</p>

<h3>Third Party Content</h3>
<p>The Content includes items that have been sourced from third parties as set out below. If you
did not receive this Content directly from the Eclipse Foundation, the following is provided
for informational purposes only, and you should look to the Redistributor's license for
terms and conditions of use.</p>

<h4>Apache Ant Core</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant Core project.</p>

<p>Apache Ant Core is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant Core including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant + ANTLR</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant + ANTLR project.</p>

<p>Apache Ant + ANTLR is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant + ANTLR including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant + BCEL</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant + BCEL project.</p>

<p>Apache Ant + BCEL is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant + BCEL including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant + BSF</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant + BSF project.</p>

<p>Apache Ant + BSF is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant + BSF including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant + Log4J</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant + Log4J project.</p>

<p>Apache Ant + Log4J is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant + Log4J including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant + Apache Oro</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant + Apache Oro project.</p>

<p>Apache Ant + Apache Oro is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant + Apache Oro including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant + Apache Regexp</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant + Apache Regexp project.</p>

<p>Apache Ant + Apache Regexp is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant + Apache Regexp including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant + Apache Resolver</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant + Apache Resolver project.</p>

<p>Apache Ant + Apache Resolver is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant + Apache Resolver including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant + Xalan 2</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant + Xalan 2 project.</p>

<p>Apache Ant + Xalan 2 is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant + Xalan 2 including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant + Commons Logging</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant + Commons Logging project.</p>

<p>Apache Ant + Commons Logging is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant + Commons Logging including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant + Commons Net</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant + Commons Net project.</p>

<p>Apache Ant + Commons Net is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant + Commons Net including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant + JAI</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant + JAI project.</p>

<p>Apache Ant + JAI is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant + JAI including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant + JavaMail</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant + JavaMail project.</p>

<p>Apache Ant + JavaMail is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant + JavaMail including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant + JDepend</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant + JDepend project.</p>

<p>Apache Ant + JDepend is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant + JDepend including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant + JMF</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant + JMF project.</p>

<p>Apache Ant + JMF is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant + JMF including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant + JSch</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant + JSch project.</p>

<p>Apache Ant + JSch is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant + JSch including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant + JUnit</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant + JUnit project.</p>

<p>Apache Ant + JUnit is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant + JUnit including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant + JUnit 4</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant + JUnit 4 project.</p>

<p>Apache Ant + JUnit 4 is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant + JUnit 4 including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant + JUnitlauncher</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant + JUnitlauncher project.</p>

<p>Apache Ant + JUnitlauncher is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant + JUnitlauncher including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant Launcher</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant Launcher project.</p>

<p>Apache Ant Launcher is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant Launcher including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant + NetRexx</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant + NetRexx project.</p>

<p>Apache Ant + NetRexx is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant + NetRexx including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant + Swing</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant + Swing project.</p>

<p>Apache Ant + Swing is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant + Swing including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant Test Utilities</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant Test Utilities project.</p>

<p>Apache Ant Test Utilities is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant Test Utilities including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>
<h4>Apache Ant + XZ</h4>

<p>The plug-in includes software developed by <a href="http://www.apache.org/" target="_blank">The Apache Software Foundation</a> as part of the Apache Ant + XZ project.</p>

<p>Apache Ant + XZ is provided to you under the terms and conditions of the <a href="http://www.apache.org/licenses/LICENSE-2.0.txt" target="_blank">The Apache Software License, Version 2.0</a> (<a href="about_files/THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt" target="_blank">THE_APACHE_SOFTWARE_LICENSE__VERSION_2.0.txt</a>) license.</p>

<p>Apache Ant + XZ including its source is available from <a href="http://ant.apache.org/" target="_blank">ant.apache.org/</a>. Bugs or feature requests can be made in the project issue tracking system at <a href="http://issues.apache.org/bugzilla/" target="_blank">issues.apache.org/bugzilla/</a>. The following mailing lists can be used to communicate with the project communities: Ant Developers List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-dev" target="_blank">archive</a>) or Ant Users List &lt;<EMAIL>&gt; (<a href="http://mail-archives.apache.org/mod_mbox/ant-user" target="_blank">archive</a>).</p>


</body>
</html>