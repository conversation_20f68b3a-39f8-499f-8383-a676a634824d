<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: CanTSyn -->
						<ECUC-MODULE-DEF UUID="eba84a57-5c11-41dd-b908-2176663a2cc4">
							<SHORT-NAME>CanTSyn</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the Synchronized Time-base Manager (StbM) module with respect to global time handling on CAN.</L-2>
							</DESC>
							<CATEGORY>VENDOR_SPECIFIC_MODULE_DEFINITION</CATEGORY>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>1.00.00</REVISION-LABEL>
										<ISSUED-BY>visssf</ISSUED-BY>
										<DATE>2014-08-25T05:10:29+02:00</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">Initial creation (Beta)</L-2>
												</CHANGE>
												<REASON>
													<L-2 L="EN">ESCAN00077542</L-2>
												</REASON>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
									<DOC-REVISION>
										<REVISION-LABEL>1.01.00</REVISION-LABEL>
										<ISSUED-BY>visssf</ISSUED-BY>
										<DATE>2014-12-04T11:01:52+01:00</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">Added default values for handle IDs</L-2>
												</CHANGE>
												<REASON>
													<L-2 L="EN">ESCAN00079949</L-2>
												</REASON>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
									<DOC-REVISION>
										<REVISION-LABEL>1.02.00</REVISION-LABEL>
										<ISSUED-BY>visssf</ISSUED-BY>
										<DATE>2015-07-14T04:16:14+02:00</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">Updated SW version</L-2>
												</CHANGE>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
									<DOC-REVISION>
										<REVISION-LABEL>2.00.00</REVISION-LABEL>
										<ISSUED-BY>visssf</ISSUED-BY>
										<DATE>2015-09-22T09:22:59+02:00</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">Updated SW version</L-2>
												</CHANGE>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
									<DOC-REVISION>
										<REVISION-LABEL>2.01.00</REVISION-LABEL>
										<ISSUED-BY>visssf</ISSUED-BY>
										<DATE>2016-02-26T01:39:24+01:00</DATE>
										<MODIFICATIONS>
											<MODIFICATION>
												<CHANGE>
													<L-2 L="EN">Support multiple masters per Time Domain</L-2>
												</CHANGE>
												<REASON>
													<L-2 L="EN">ESCAN00088577</L-2>
												</REASON>
											</MODIFICATION>
										</MODIFICATIONS>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-POST-BUILD</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: CanTSynGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="3382b529-a397-45f2-b808-d912a45980f4">
									<SHORT-NAME>CanTSynGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container holds the general parameters of the CAN-specific Synchronized Time-base Manager</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CanTSynDevErrorDetect -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="203c4ddc-448c-4f33-8b0c-6d7fcf088b6a">
											<SHORT-NAME>CanTSynDevErrorDetect</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Switch for enabling the development error detection.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CanTSynMainFunctionPeriod -->
										<ECUC-FLOAT-PARAM-DEF UUID="e63c7a9a-cae1-4cc3-a7c8-2b2bcc21210b">
											<SHORT-NAME>CanTSynMainFunctionPeriod</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Schedule period of the main function CanTSyn_MainFunction. Unit: [s].</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">SEC</SD>
														<SD GID="DV:Unit">MSEC</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>1.7976931348623157E+308</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CanTSynVersionInfo -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="b06337f6-4cee-4d9a-9080-f95e6bfc9607">
											<SHORT-NAME>CanTSynVersionInfo</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Activate/Deactivate the version information API (CanTSyn_GetVersionInfo). True: version information API activated False: version information API deactivated.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CanTSynGlobalTimeFupDataIDList -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="4e4f73f8-e829-4f3e-b07f-974aa62b9a5d">
											<SHORT-NAME>CanTSynGlobalTimeFupDataIDList</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The DataIDList for FUP messages ensures the identification of data elements due to CRC calculation process.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SUB-CONTAINERS>
												<!-- Container Definition: CanTSynGlobalTimeFupDataIDListElement -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="47228971-6b1a-40c6-8dad-42fedfe5b17c">
													<SHORT-NAME>CanTSynGlobalTimeFupDataIDListElement</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Element of the DataIDList for FUP messages ensures the identification of data elements due to CRC calculation process.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>16</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>16</UPPER-MULTIPLICITY>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CanTSynGlobalTimeFupDataIDListIndex -->
														<ECUC-INTEGER-PARAM-DEF UUID="d88c08dd-3788-497f-902b-ec6c08915549">
															<SHORT-NAME>CanTSynGlobalTimeFupDataIDListIndex</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Index of the DataIDList for FUP messages ensures the identification of data elements due to CRC calculation process.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>15</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTSynGlobalTimeFupDataIDListValue -->
														<ECUC-INTEGER-PARAM-DEF UUID="62f22ae0-1796-4f11-a14a-4d21081523f3">
															<SHORT-NAME>CanTSynGlobalTimeFupDataIDListValue</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Value of the DataIDList for FUP messages ensures the identification of data elements due to CRC calculation process.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: CanTSynGlobalTimeOfnsDataIDList -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="011ec66b-71b1-4637-bbf6-d38a140f301c">
											<SHORT-NAME>CanTSynGlobalTimeOfnsDataIDList</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The DataIDList for OFNS messages ensures the identification of data elements due to CRC calculation process.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SUB-CONTAINERS>
												<!-- Container Definition: CanTSynGlobalTimeOfnsDataIDListElement -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="7fad34bf-3421-493d-9812-b5836ba40e22">
													<SHORT-NAME>CanTSynGlobalTimeOfnsDataIDListElement</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Element of the DataIDList for OFNS messages ensures the identification of data elements due to CRC calculation process.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>16</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>16</UPPER-MULTIPLICITY>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CanTSynGlobalTimeOfnsDataIDListIndex -->
														<ECUC-INTEGER-PARAM-DEF UUID="dc82f823-5ff8-41e3-8631-1470b810d4c2">
															<SHORT-NAME>CanTSynGlobalTimeOfnsDataIDListIndex</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Index of the DataIDList for OFNS messages ensures the identification of data elements due to CRC calculation process.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>15</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTSynGlobalTimeOfnsDataIDListValue -->
														<ECUC-INTEGER-PARAM-DEF UUID="7b714c16-a3c9-4e09-8c7b-a87ee4dc09b4">
															<SHORT-NAME>CanTSynGlobalTimeOfnsDataIDListValue</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Value of the DataIDList for OFNS messages ensures the identification of data elements due to CRC calculation process.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: CanTSynGlobalTimeOfsDataIDList -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="*************-4314-9ab7-c6053c065ecc">
											<SHORT-NAME>CanTSynGlobalTimeOfsDataIDList</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The DataIDList for OFS messages ensures the identification of data elements due to CRC calculation process.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SUB-CONTAINERS>
												<!-- Container Definition: CanTSynGlobalTimeOfsDataIDListElement -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ec80be40-6a04-48ac-8670-1e1804b462a2">
													<SHORT-NAME>CanTSynGlobalTimeOfsDataIDListElement</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Element of the DataIDList for OFS messages ensures the identification of data elements due to CRC calculation process.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>16</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>16</UPPER-MULTIPLICITY>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CanTSynGlobalTimeOfsDataIDListIndex -->
														<ECUC-INTEGER-PARAM-DEF UUID="2b37266e-c350-4fb8-9b58-39e650d72ec7">
															<SHORT-NAME>CanTSynGlobalTimeOfsDataIDListIndex</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Index of the DataIDList for OFS messages ensures the identification of data elements due to CRC calculation process.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<MAX>15</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTSynGlobalTimeOfsDataIDListValue -->
														<ECUC-INTEGER-PARAM-DEF UUID="b514073a-b3c0-4cf3-bd4c-be9b03878b62">
															<SHORT-NAME>CanTSynGlobalTimeOfsDataIDListValue</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Value of the DataIDList for OFS messages ensures the identification of data elements due to CRC calculation process.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: CanTSynGlobalTimeSyncDataIDList -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="c2180445-f3e5-4172-92bc-d1ad2135cfbd">
											<SHORT-NAME>CanTSynGlobalTimeSyncDataIDList</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The DataIDList for SYNC messages ensures the identification of data elements due to CRC calculation process.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SUB-CONTAINERS>
												<!-- Container Definition: CanTSynGlobalTimeSyncDataIDListElement -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="7bcbb251-748b-4f96-bd5e-380cc45bc0e3">
													<SHORT-NAME>CanTSynGlobalTimeSyncDataIDListElement</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Element of the DataIDList for SYNC messages ensures the identification of data elements due to CRC calculation process.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>16</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>16</UPPER-MULTIPLICITY>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CanTSynGlobalTimeSyncDataIDListIndex -->
														<ECUC-INTEGER-PARAM-DEF UUID="0f9bbeea-4b20-4524-b83a-acbcc487709d">
															<SHORT-NAME>CanTSynGlobalTimeSyncDataIDListIndex</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Index for the DataIDList for SYNC messages ensures the identification of data elements due to CRC calculation process.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>15</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTSynGlobalTimeSyncDataIDListValue -->
														<ECUC-INTEGER-PARAM-DEF UUID="b8aa78f2-4bd3-42d2-9b0a-04e650b9d025">
															<SHORT-NAME>CanTSynGlobalTimeSyncDataIDListValue</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Value of the DataIDList for SYNC messages ensures the identification of data elements due to CRC calculation process.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CanTSynGlobalTimeDomain -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="dd476b72-786b-4117-9254-242370e89290">
									<SHORT-NAME>CanTSynGlobalTimeDomain</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This represents the existence of a global time domain on CAN. The CanTSyn module can administrate several global time domains at the same time that in itself form a hierarchy of domains and sub-domains.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">If the CanTSyn exists it is assumed that at least one global time domain exists.</L-1>
										</P>
									</INTRODUCTION>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CanTSynGlobalTimeDomainId -->
										<ECUC-INTEGER-PARAM-DEF UUID="0d7f9a6e-a3d6-4eef-b3dd-640d819f13e3">
											<SHORT-NAME>CanTSynGlobalTimeDomainId</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The global time domain ID.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>31</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CanTSynGlobalTimeFollowUpTimeout -->
										<ECUC-FLOAT-PARAM-DEF UUID="c153ed9a-184d-4bca-8b2a-bbfa82bbc53d">
											<SHORT-NAME>CanTSynGlobalTimeFollowUpTimeout</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Rx timeout for the follow-up message. This is only relevant for selected bus systems Unit:seconds</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">SEC</SD>
														<SD GID="DV:Unit">MSEC</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>1.7976931348623157E+308</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CanTSynGlobalTimeSequenceCounterJumpWidth -->
										<ECUC-INTEGER-PARAM-DEF UUID="addb4646-3ca6-4123-b601-c8fbd956d96c">
											<SHORT-NAME>CanTSynGlobalTimeSequenceCounterJumpWidth</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The SequenceCounterJumpWidth specifies the maximum allowed gap of the Sequence Counter between two SYNC resp. two OFS messages.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>15</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Symbolic Name Reference Definition: CanTSynSynchronizedTimeBaseRef -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="54ef0005-8f97-4870-b93d-fb2c78a00c92">
											<SHORT-NAME>CanTSynSynchronizedTimeBaseRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Mandatory reference to the required synchronized time-base.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/StbM/StbMSynchronizedTimeBase</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<!-- Container Definition: CanTSynGlobalTimeMaster -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="e2a7d862-fc1b-420e-a0dd-dbd35bb9e147">
											<SHORT-NAME>CanTSynGlobalTimeMaster</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configuration of the global time master. Each global time domain is required to have exactly one global time master. This master may or may not exist on the configured ECU.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">true</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CanTSynGlobalTimeTxCrcSecured -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="028c9dae-7cc2-48c1-b3cd-78f7a829f450">
													<SHORT-NAME>CanTSynGlobalTimeTxCrcSecured</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This represents the configuration of whether or not CRC is supported.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="483fbafc-3b06-4dd5-973a-fefe6d89ca94">
															<SHORT-NAME>CRC_NOT_SUPPORTED</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="fd76cd9b-454f-4814-ba44-c179ed56632e">
															<SHORT-NAME>CRC_SUPPORTED</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CanTSynGlobalTimeTxFollowUpOffset -->
												<ECUC-FLOAT-PARAM-DEF UUID="acdaf4e6-57c6-48d8-bff5-7a921363bd75">
													<SHORT-NAME>CanTSynGlobalTimeTxFollowUpOffset</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This represents the transmission time offset between a SYNC message and the related FUP message resp. OFS message and related OFNS message. Unit: seconds.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
																<SD GID="DV:Unit">MSEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>1.7976931348623157E+308</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CanTSynGlobalTimeTxPeriod -->
												<ECUC-FLOAT-PARAM-DEF UUID="d928eace-ac32-4067-b2f9-38dde1ad107c">
													<SHORT-NAME>CanTSynGlobalTimeTxPeriod</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This represents configuration of the TX period. Unit: seconds</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
																<SD GID="DV:Unit">MSEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>1.7976931348623157E+308</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CanTSynMasterConfirmationTimeout -->
												<ECUC-FLOAT-PARAM-DEF UUID="9f6a97ff-2266-4af0-bf51-4771c29db32c">
													<SHORT-NAME>CanTSynMasterConfirmationTimeout</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This represents the confirmation timeout after transmission of a SYNC message resp. OFS message. Unit: seconds.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
																<SD GID="DV:Unit">MSEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>1.7976931348623157E+308</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
											</PARAMETERS>
											<SUB-CONTAINERS>
												<!-- Container Definition: CanTSynGlobalTimeMasterPdu -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="224dae13-8577-42b2-914b-a481e307a9a7">
													<SHORT-NAME>CanTSynGlobalTimeMasterPdu</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container encloses the configuration of the PDU that is supposed to contain the global time information.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CanTSynGlobalTimeMasterConfirmationHandleId -->
														<ECUC-INTEGER-PARAM-DEF UUID="3cac33bb-89fa-43ca-b863-1b4d7b552d56">
															<SHORT-NAME>CanTSynGlobalTimeMasterConfirmationHandleId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This represents the handle ID of the PDU that contains the global time information.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>0</DEFAULT-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: CanTSynGlobalTimePduRef -->
														<ECUC-REFERENCE-DEF UUID="ed4fbecb-709f-443e-a33e-255d09f6d2de">
															<SHORT-NAME>CanTSynGlobalTimePduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This represents the reference to the Pdu taken to transmit the global time information. The global time master of a global time domain acts as the sender of the Pdu while all the time slaves are supposed to receive the Pdu.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: CanTSynGlobalTimeSlave -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="e38377b2-690c-4029-a86c-8d889be07808">
											<SHORT-NAME>CanTSynGlobalTimeSlave</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configuration of a global time slave. Each global time domain is required to have at least one time slave. The configured ECU may or may not represent a time slave.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CanTSynRxCrcValidated -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="6126e429-bd7f-4ad2-97ec-944474cb6481">
													<SHORT-NAME>CanTSynRxCrcValidated</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Definition of whether or not validation of the CRC is supported.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="be975098-7367-4b2a-8d85-d756c87dc3a6">
															<SHORT-NAME>CRC_IGNORED</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="e034239b-16c0-49db-aee6-0d5784bb35d8">
															<SHORT-NAME>CRC_NOT_VALIDATED</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="bb82462a-3c5b-46c2-8ada-63addf10d05b">
															<SHORT-NAME>CRC_VALIDATED</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
											</PARAMETERS>
											<SUB-CONTAINERS>
												<!-- Container Definition: CanTSynGlobalTimeSlavePdu -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="288a4d2d-b0d8-44d8-8ba9-c27d7bc5461d">
													<SHORT-NAME>CanTSynGlobalTimeSlavePdu</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container encloses the configuration of the PDU that is supposed to contain the global time information.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CanTSynGlobalTimeSlaveHandleId -->
														<ECUC-INTEGER-PARAM-DEF UUID="244aa8ff-670f-456e-a25c-eac05b1df60c">
															<SHORT-NAME>CanTSynGlobalTimeSlaveHandleId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This represents the handle ID of the PDU that contains the global time information.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>0</DEFAULT-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: CanTSynGlobalTimePduRef -->
														<ECUC-REFERENCE-DEF UUID="ff0ee991-ccdc-469a-89ff-16cfc8b8acc1">
															<SHORT-NAME>CanTSynGlobalTimePduRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This represents the reference to the Pdu taken to transmit the global time information. The global time master of a global time domain acts as the sender of the Pdu while all the time slaves are supposed to receive the Pdu.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
