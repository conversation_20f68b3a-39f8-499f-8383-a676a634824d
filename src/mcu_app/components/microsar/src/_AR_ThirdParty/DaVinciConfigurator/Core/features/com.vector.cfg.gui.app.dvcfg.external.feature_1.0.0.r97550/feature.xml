<?xml version="1.0" encoding="UTF-8"?>
<feature
      id="com.vector.cfg.gui.app.dvcfg.external.feature"
      label="3rd Party Libraries for DaVinci Cfg"
      version="1.0.0.r97550"
      provider-name="Vector Informatik GmbH">

   <description>
      3rd party libraries for DaVinci Configurator.
   </description>

   <copyright>
      (c) Copyright Vector Informatik GmbH. All Rights Reserved.
   </copyright>

   <license url="http://www.vector.com">
      Vector Group License Agreement

This software is licensed under the terms and conditions of „Delivery and maintenance of software products” of Vector Group, see www.vector.com
   </license>

   <includes
         id="org.eclipse.help"
         version="2.2.700.v20190916-1045"/>

   <includes
         id="org.eclipse.emf.common"
         version="2.16.0.v20190625-1131"/>

   <includes
         id="org.eclipse.emf.ecore"
         version="2.19.0.v20190822-1451"/>

   <includes
         id="com.vector.cfg.core.external.feature"
         version="1.0.0.r97550"/>

   <requires>
      <import plugin="org.eclipse.xtext.xbase.lib" version="2.10.0" match="greaterOrEqual"/>
   </requires>

   <plugin
         id="ca.odell.glazedlists"
         download-size="0"
         install-size="0"
         version="1.9.0.v201303080712"
         unpack="false"/>

   <plugin
         id="org.apache.batik.css"
         download-size="0"
         install-size="0"
         version="1.11.0.v20190515-0436"
         unpack="false"/>

   <plugin
         id="org.apache.batik.util"
         download-size="0"
         install-size="0"
         version="1.11.0.v20190515-0436"
         unpack="false"/>

   <plugin
         id="org.apache.commons.codec"
         download-size="0"
         install-size="0"
         version="1.10.0.v20180409-1845"
         unpack="false"/>

   <plugin
         id="org.apache.lucene.analyzers-common"
         download-size="0"
         install-size="0"
         version="8.0.0.v20190404-1858"
         unpack="false"/>

   <plugin
         id="org.apache.lucene.core"
         download-size="0"
         install-size="0"
         version="8.0.0.v20190404-1858"
         unpack="false"/>

   <plugin
         id="org.eclipse.ant.core"
         download-size="0"
         install-size="0"
         version="3.5.500.v20190701-1953"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.databinding.beans"
         download-size="0"
         install-size="0"
         version="1.5.100.v20190624-2109"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.core.di.extensions.supplier"
         download-size="0"
         install-size="0"
         version="0.15.400.v20190709-0707"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.emf.xpath"
         download-size="0"
         install-size="0"
         version="0.2.400.v20190621-1946"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.bindings"
         download-size="0"
         install-size="0"
         version="0.12.600.v20190625-0735"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.css.core"
         download-size="0"
         install-size="0"
         version="0.12.800.v20190805-1157"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.css.swt"
         download-size="0"
         install-size="0"
         version="0.13.600.v20190805-1157"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.css.swt.theme"
         download-size="0"
         install-size="0"
         version="0.12.400.v20190812-0413"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.di"
         download-size="0"
         install-size="0"
         version="1.2.600.v20190510-1100"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.model.workbench"
         download-size="0"
         install-size="0"
         version="2.1.500.v20190824-1021"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.services"
         download-size="0"
         install-size="0"
         version="1.3.600.v20190716-1245"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.widgets"
         download-size="0"
         install-size="0"
         version="1.2.500.v20190624-0808"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.workbench"
         download-size="0"
         install-size="0"
         version="1.10.100.v20190810-0814"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.workbench.addons.swt"
         download-size="0"
         install-size="0"
         version="1.3.600.v20190716-1245"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.workbench.renderers.swt"
         download-size="0"
         install-size="0"
         version="0.14.800.v20190716-1245"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.workbench.swt"
         download-size="0"
         install-size="0"
         version="0.14.700.v20190807-1716"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.workbench3"
         download-size="0"
         install-size="0"
         version="0.15.200.v20190621-1448"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.p2.core"
         download-size="0"
         install-size="0"
         version="2.6.100.v20190705-1223"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.p2.engine"
         download-size="0"
         install-size="0"
         version="2.6.400.v20190716-0825"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.p2.metadata"
         download-size="0"
         install-size="0"
         version="2.4.500.v20190807-0737"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.p2.metadata.repository"
         download-size="0"
         install-size="0"
         version="1.3.200.v20190808-0702"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.p2.repository"
         download-size="0"
         install-size="0"
         version="2.4.500.v20190716-0939"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.security"
         download-size="0"
         install-size="0"
         version="1.3.300.v20190714-1851"
         unpack="false"/>

   <plugin
         id="org.eclipse.jface"
         download-size="0"
         install-size="0"
         version="3.17.0.v20190820-1444"
         unpack="false"/>

   <plugin
         id="org.eclipse.jface.databinding"
         download-size="0"
         install-size="0"
         version="1.9.100.v20190805-1255"
         unpack="false"/>

   <plugin
         id="org.eclipse.jface.text"
         download-size="0"
         install-size="0"
         version="3.15.300.v20190819-0725"
         unpack="false"/>

   <plugin
         id="org.eclipse.nebula.widgets.nattable.core"
         download-size="0"
         install-size="0"
         version="1.6.0.201909181823"
         unpack="false"/>

   <plugin
         id="org.eclipse.nebula.widgets.nattable.extension.glazedlists"
         download-size="0"
         install-size="0"
         version="1.6.0.201909181823"
         unpack="false"/>

   <plugin
         id="org.eclipse.nebula.widgets.tablecombo"
         download-size="0"
         install-size="0"
         version="1.2.0.201907151344"
         unpack="false"/>

   <plugin
         id="org.eclipse.swt"
         download-size="0"
         install-size="0"
         version="3.112.0.v20200310-0942-543747"
         unpack="false"/>

   <plugin
         id="org.eclipse.swt.win32.win32.x86_64"
         os="win32"
         ws="win32"
         arch="x86_64"
         download-size="0"
         install-size="0"
         version="3.112.0.v20200310-0942-543747"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.eclipse.ui"
         download-size="0"
         install-size="0"
         version="3.114.0.v20190808-1317"
         unpack="false"/>

   <plugin
         id="org.eclipse.ui.console"
         download-size="0"
         install-size="0"
         version="3.8.600.v20190815-2020"
         unpack="false"/>

   <plugin
         id="org.eclipse.ui.forms"
         download-size="0"
         install-size="0"
         version="3.8.100.v20190625-1825"
         unpack="false"/>

   <plugin
         id="org.eclipse.ui.views"
         download-size="0"
         install-size="0"
         version="3.10.0.v20190805-1157"
         unpack="false"/>

   <plugin
         id="org.eclipse.ui.views.properties.tabbed"
         download-size="0"
         install-size="0"
         version="3.8.600.v20190713-1021"
         unpack="false"/>

   <plugin
         id="org.eclipse.ui.workbench"
         download-size="0"
         install-size="0"
         version="3.116.0.v20190826-1428"
         unpack="false"/>

   <plugin
         id="org.eclipse.ui.workbench.texteditor"
         download-size="0"
         install-size="0"
         version="3.13.0.v20190903-0631"
         unpack="false"/>

   <plugin
         id="org.hamcrest.library"
         download-size="0"
         install-size="0"
         version="1.3.0.v20180524-2246"
         unpack="false"/>

   <plugin
         id="org.tukaani.xz"
         download-size="0"
         install-size="0"
         version="1.8.0.v20180207-1613"
         unpack="false"/>

   <plugin
         id="org.eclipse.ui.ide"
         download-size="0"
         install-size="0"
         version="3.16.0.v20190916-1323"
         unpack="false"/>

   <plugin
         id="org.eclipse.ui.ide.application"
         download-size="0"
         install-size="0"
         version="1.3.400.v20190818-1234"
         unpack="false"/>

   <plugin
         id="org.eclipse.ui.navigator"
         download-size="0"
         install-size="0"
         version="3.9.0.v20190807-2204"
         unpack="false"/>

   <plugin
         id="org.eclipse.ui.navigator.resources"
         download-size="0"
         install-size="0"
         version="3.7.0.v20190820-1649"
         unpack="false"/>

   <plugin
         id="org.eclipse.ltk.core.refactoring"
         download-size="0"
         install-size="0"
         version="3.10.200.v20190814-1719"
         unpack="false"/>

   <plugin
         id="org.eclipse.ltk.ui.refactoring"
         download-size="0"
         install-size="0"
         version="3.10.0.v20190819-2110"
         unpack="false"/>

   <plugin
         id="org.eclipse.compare"
         download-size="0"
         install-size="0"
         version="3.7.700.v20190802-1838"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.filebuffers"
         download-size="0"
         install-size="0"
         version="3.6.700.v20190614-0928"
         unpack="false"/>

   <plugin
         id="org.eclipse.team.ui"
         download-size="0"
         install-size="0"
         version="3.8.600.v20190819-1553"
         unpack="false"/>

   <plugin
         id="org.eclipse.ui.editors"
         download-size="0"
         install-size="0"
         version="3.12.0.v20190730-1840"
         unpack="false"/>

   <plugin
         id="org.eclipse.update.configurator"
         download-size="0"
         install-size="0"
         version="3.4.300.v20190518-1030"
         unpack="false"/>

   <plugin
         id="org.eclipse.debug.core"
         download-size="0"
         install-size="0"
         version="3.14.0.v20190812-1404"
         unpack="false"/>

   <plugin
         id="org.eclipse.jdt.debug"
         download-size="0"
         install-size="0"
         version="3.13.100.v20190902-1050"/>

   <plugin
         id="org.eclipse.jdt.launching"
         download-size="0"
         install-size="0"
         version="3.15.0.v20190826-1639"
         unpack="false"/>

   <plugin
         id="org.apache.batik.i18n"
         download-size="0"
         install-size="0"
         version="1.11.0.v20190515-0436"
         unpack="false"/>

   <plugin
         id="org.apache.xmlgraphics"
         download-size="0"
         install-size="0"
         version="2.3.0.v20190515-0436"
         unpack="false"/>

   <plugin
         id="org.apache.batik.constants"
         download-size="0"
         install-size="0"
         version="1.11.0.v20190515-0436"
         unpack="false"/>

   <plugin
         id="org.eclipse.urischeme"
         download-size="0"
         install-size="0"
         version="1.0.400.v20190621-1448"
         unpack="false"/>

   <plugin
         id="org.w3c.css.sac"
         download-size="0"
         install-size="0"
         version="1.3.1.v200903091627"
         unpack="false"/>

   <plugin
         id="org.w3c.dom.events"
         download-size="0"
         install-size="0"
         version="3.0.0.draft20060413_v201105210656"
         unpack="false"/>

   <plugin
         id="org.w3c.dom.smil"
         download-size="0"
         install-size="0"
         version="1.0.1.v200903091627"
         unpack="false"/>

   <plugin
         id="org.w3c.dom.svg"
         download-size="0"
         install-size="0"
         version="1.1.0.v201011041433"
         unpack="false"/>

   <plugin
         id="org.eclipse.help"
         download-size="0"
         install-size="0"
         version="3.8.500.v20190624-2105"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.bidi"
         download-size="0"
         install-size="0"
         version="1.2.100.v20190815-1535"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.core.commands"
         download-size="0"
         install-size="0"
         version="0.12.700.v20190621-1412"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.core.contexts"
         download-size="0"
         install-size="0"
         version="1.8.200.v20190620-0649"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.core.di"
         download-size="0"
         install-size="0"
         version="1.7.400.v20190903-1311"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.core.di.annotations"
         download-size="0"
         install-size="0"
         version="1.6.400.v20190518-1217"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.core.di.extensions"
         download-size="0"
         install-size="0"
         version="0.15.300.v20190213-1308"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.core.services"
         download-size="0"
         install-size="0"
         version="2.2.0.v20190630-2019"
         unpack="false"/>

   <plugin
         id="org.apache.commons.jxpath"
         download-size="0"
         install-size="0"
         version="1.3.0.v200911051830"
         unpack="false"/>

   <plugin
         id="org.eclipse.elk.alg.common"
         download-size="0"
         install-size="0"
         version="0.5.0"
         unpack="false"/>

   <plugin
         id="org.eclipse.elk.core"
         download-size="0"
         install-size="0"
         version="0.5.0"
         unpack="false"/>

   <plugin
         id="org.eclipse.zest.core"
         download-size="0"
         install-size="0"
         version="1.5.300.201606061308"
         unpack="false"/>

   <plugin
         id="org.eclipse.elk.alg.layered"
         download-size="0"
         install-size="0"
         version="0.5.0"
         unpack="false"/>

   <plugin
         id="org.eclipse.elk.graph"
         download-size="0"
         install-size="0"
         version="0.5.0"
         unpack="false"/>

   <plugin
         id="org.eclipse.draw2d"
         download-size="0"
         install-size="0"
         version="3.10.100.201606061308"
         unpack="false"/>

   <plugin
         id="org.eclipse.zest.layouts"
         download-size="0"
         install-size="0"
         version="1.1.300.201606061308"
         unpack="false"/>

   <plugin
         id="com.google.guava"
         download-size="0"
         install-size="0"
         version="27.1.0.v20190517-1946"
         unpack="false"/>

   <plugin
         id="org.eclipse.xtext.xbase.lib"
         download-size="0"
         install-size="0"
         version="2.19.0.v20190902-0728"
         unpack="false"/>

   <plugin
         id="org.eclipse.nebula.widgets.nattable.extension.e4"
         download-size="0"
         install-size="0"
         version="1.2.0.201909181823"
         unpack="false"/>

   <plugin
         id="org.eclipse.ui.themes"
         download-size="0"
         install-size="0"
         version="1.2.700.v20190826-0816"/>

   <plugin
         id="org.eclipse.e4.ui.dialogs"
         download-size="0"
         install-size="0"
         version="1.1.600.v20190814-0636"
         unpack="false"/>

</feature>
