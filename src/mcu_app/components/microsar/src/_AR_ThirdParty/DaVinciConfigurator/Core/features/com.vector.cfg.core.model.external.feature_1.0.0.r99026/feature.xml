<?xml version="1.0" encoding="UTF-8"?>
<feature
      id="com.vector.cfg.core.model.external.feature"
      label="3rd Party Libraries for DaVinci Model"
      version="1.0.0.r99026"
      provider-name="Vector Informatik GmbH">

   <description url="http://www.example.com/description">
      [Enter Feature Description here.]
   </description>

   <copyright url="http://www.example.com/copyright">
      [Enter Copyright Description here.]
   </copyright>

   <license url="http://www.example.com/license">
      [Enter License Description here.]
   </license>

   <plugin
         id="org.aopalliance"
         download-size="0"
         install-size="0"
         version="1.0.0.v201105210816"
         unpack="false"/>

   <plugin
         id="com.google.guava"
         download-size="0"
         install-size="0"
         version="18.0.0.p1"
         unpack="false"/>

   <plugin
         id="com.google.guava"
         download-size="0"
         install-size="0"
         version="25.1.0.jre"
         unpack="false"/>

   <plugin
         id="com.google.inject"
         download-size="0"
         install-size="0"
         version="4.2.2"
         unpack="false"/>

   <plugin
         id="com.google.inject.assistedinject"
         download-size="0"
         install-size="0"
         version="4.2.2"
         fragment="true"
         unpack="false"/>

   <plugin
         id="com.lmax.disruptor"
         download-size="0"
         install-size="0"
         version="3.4.2"
         unpack="false"/>

   <plugin
         id="javax.inject"
         download-size="0"
         install-size="0"
         version="1.0.0.v20091030"
         unpack="false"/>

   <plugin
         id="net.sf.trove4j"
         download-size="0"
         install-size="0"
         version="3.0.3"
         unpack="false"/>

   <plugin
         id="org.apache.commons.cli"
         download-size="0"
         install-size="0"
         version="1.4.0"
         unpack="false"/>

   <plugin
         id="org.apache.logging.log4j.api"
         download-size="0"
         install-size="0"
         version="2.13.1"
         unpack="false"/>

   <plugin
         id="org.apache.logging.log4j.core"
         download-size="0"
         install-size="0"
         version="2.13.1"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.commands"
         download-size="0"
         install-size="0"
         version="3.9.500.v20190805-1157"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.runtime"
         download-size="0"
         install-size="0"
         version="3.16.0.v20190823-1314"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.common"
         download-size="0"
         install-size="0"
         version="3.10.500.v20190815-1535"
         unpack="false"/>

   <plugin
         id="org.eclipse.jdt.annotation"
         download-size="0"
         install-size="0"
         version="2.2.300.v20190328-1431"
         unpack="false"/>

   <plugin
         id="org.eclipse.jdt.compiler.apt"
         download-size="0"
         install-size="0"
         version="1.3.700.v20190704-1731"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.eclipse.jdt.compiler.tool"
         download-size="0"
         install-size="0"
         version="1.2.600.v20190322-0450"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.eclipse.jdt.core"
         download-size="0"
         install-size="0"
         version="3.19.0.v20190903-0936"
         unpack="false"/>

   <plugin
         id="javax.annotation"
         download-size="0"
         install-size="0"
         version="1.2.0.v201602091430"
         unpack="false"/>

   <plugin
         id="org.eclipse.osgi"
         download-size="0"
         install-size="0"
         version="3.15.0.v20190830-1434"
         unpack="false"/>

   <plugin
         id="org.eclipse.osgi.compatibility.state"
         download-size="0"
         install-size="0"
         version="1.1.600.v20190814-1451"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.eclipse.osgi.services"
         download-size="0"
         install-size="0"
         version="3.8.0.v20190206-2147"
         unpack="false"/>

   <plugin
         id="org.eclipse.osgi.util"
         download-size="0"
         install-size="0"
         version="3.5.300.v20190708-1141"
         unpack="false"/>

   <plugin
         id="com.ibm.icu"
         download-size="0"
         install-size="0"
         version="64.2.0.v20190507-1337"
         unpack="false"/>

   <plugin
         id="com.ning.compress-lzf"
         download-size="0"
         install-size="0"
         version="1.0.5.201804161355"
         unpack="false"/>

   <plugin
         id="javax.servlet"
         download-size="0"
         install-size="0"
         version="3.1.0.v201410161800"
         unpack="false"/>

   <plugin
         id="joda-time"
         download-size="0"
         install-size="0"
         version="2.9.3"
         unpack="false"/>

   <plugin
         id="org.apache.commons.io"
         download-size="0"
         install-size="0"
         version="2.6.0.v20190123-2029"
         unpack="false"/>

   <plugin
         id="org.apache.felix.gogo.command"
         download-size="0"
         install-size="0"
         version="1.0.2.v20170914-1324"
         unpack="false"/>

   <plugin
         id="org.apache.felix.gogo.runtime"
         download-size="0"
         install-size="0"
         version="1.1.0.v20180713-1646"
         unpack="false"/>

   <plugin
         id="org.apache.felix.gogo.shell"
         download-size="0"
         install-size="0"
         version="1.1.0.v20180713-1646"
         unpack="false"/>

   <plugin
         id="org.apache.felix.scr"
         download-size="0"
         install-size="0"
         version="2.1.14.v20190123-1619"
         unpack="false"/>

   <plugin
         id="org.apache.servicemix.bundles.jdom"
         download-size="0"
         install-size="0"
         version="*******"
         unpack="false"/>

   <plugin
         id="org.eclipse.compare.core"
         download-size="0"
         install-size="0"
         version="3.6.600.v20190615-1517"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.contenttype"
         download-size="0"
         install-size="0"
         version="3.7.400.v20190624-1144"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.databinding"
         download-size="0"
         install-size="0"
         version="1.7.500.v20190624-2109"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.databinding.observable"
         download-size="0"
         install-size="0"
         version="1.8.0.v20190805-1157"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.databinding.property"
         download-size="0"
         install-size="0"
         version="1.7.100.v20190805-1157"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.expressions"
         download-size="0"
         install-size="0"
         version="3.6.500.v20190617-1926"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.filesystem"
         download-size="0"
         install-size="0"
         version="1.7.500.v20190620-1312"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.filesystem.win32.x86_64"
         os="win32"
         ws="win32"
         arch="x86_64"
         download-size="0"
         install-size="0"
         version="1.4.200.v20190812-0909"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.jobs"
         download-size="0"
         install-size="0"
         version="3.10.500.v20190620-1426"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.resources"
         download-size="0"
         install-size="0"
         version="3.13.500.v20190819-0800"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.resources.win32.x86_64"
         os="win32"
         ws="win32"
         arch="x86_64"
         download-size="0"
         install-size="0"
         version="3.5.400.v20190812-0909"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.variables"
         download-size="0"
         install-size="0"
         version="3.4.600.v20190614-1239"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.app"
         download-size="0"
         install-size="0"
         version="1.4.300.v20190815-1535"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.console"
         download-size="0"
         install-size="0"
         version="1.4.0.v20190819-1430"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.ds"
         download-size="0"
         install-size="0"
         version="1.6.0.v20190122-0806"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.event"
         download-size="0"
         install-size="0"
         version="1.5.200.v20190814-0953"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.launcher"
         download-size="0"
         install-size="0"
         version="1.5.500.v20190715-1310"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.launcher.win32.win32.x86_64"
         os="win32"
         ws="win32"
         arch="x86_64"
         download-size="0"
         install-size="0"
         version="1.1.1100.v20190907-0426"
         fragment="true"/>

   <plugin
         id="org.eclipse.equinox.preferences"
         download-size="0"
         install-size="0"
         version="3.7.500.v20190815-1535"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.registry"
         download-size="0"
         install-size="0"
         version="3.8.500.v20190714-1850"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.simpleconfigurator"
         download-size="0"
         install-size="0"
         version="1.3.300.v20190716-0825"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.util"
         download-size="0"
         install-size="0"
         version="1.1.300.v20190714-1852"
         unpack="false"/>

   <plugin
         id="org.eclipse.team.core"
         download-size="0"
         install-size="0"
         version="3.8.700.v20190619-1613"
         unpack="false"/>

   <plugin
         id="org.eclipse.text"
         download-size="0"
         install-size="0"
         version="3.9.0.v20190826-1019"
         unpack="false"/>

   <plugin
         id="sun.misc"
         download-size="0"
         install-size="0"
         version="1.0.0.201906171301"
         fragment="true"/>

   <plugin
         id="org.apache.commons.lang"
         download-size="0"
         install-size="0"
         version="2.6.0.v201404270220"
         unpack="false"/>

</feature>
