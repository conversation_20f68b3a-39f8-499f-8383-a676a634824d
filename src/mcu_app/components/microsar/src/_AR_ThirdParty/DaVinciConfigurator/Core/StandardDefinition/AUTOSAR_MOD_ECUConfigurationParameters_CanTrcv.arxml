<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: CanTrcv -->
						<ECUC-MODULE-DEF UUID="ECUC:e1256557-38d2-4224-8563-f273b12e1ee8">
							<SHORT-NAME>CanTrcv</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the CanTrcv (CAN Transceiver driver) module.</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.2.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
										<DATE>2011-11-09</DATE>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-POST-BUILD</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: CanTrcvConfigSet -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:c20cd1af-0232-48fa-9f0b-f3d531f2fe20">
									<SHORT-NAME>CanTrcvConfigSet</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This is the multiple configuration set container for CAN Transceiver.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>true</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CanTrcvSPICommRetries -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:093c3711-8c92-4094-994a-a67de25b2cea">
											<SHORT-NAME>CanTrcvSPICommRetries</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Indicates the maximum number of communication retries in case of a failed SPI communication (applies both to timed out communication and to errors/NACK in the response data).</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">If configured value is &apos;0&apos;, no retry is allowed (communication is expected to succeed at first try).</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>255</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CanTrcvSPICommTimeout -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:a8bda218-8bf9-4dc0-86b1-cb9fa70fd9df">
											<SHORT-NAME>CanTrcvSPICommTimeout</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Indicates the maximum time allowed to the CanTrcv for replying (either positively or negatively) to a SPI command.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Timeout is configured in milliseconds. Timeout value of &apos;0&apos; means that no specific timeout is to be used by CanTrcv and the communication is executed at the best of the SPI HW capacity.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>100</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CanTrcvChannel -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:4e6513a4-4c5a-4486-a836-f269d7e92dd9">
											<SHORT-NAME>CanTrcvChannel</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container gives CAN transceiver driver information about a single CAN</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">transceiver (channel).</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CanTrcvChannelId -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:f1f3dba5-d4d7-43d3-8d03-2e0bb504e860">
													<SHORT-NAME>CanTrcvChannelId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Unique identifier of the CAN Transceiver Channel.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CanTrcvChannelUsed -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:88536366-15ed-4967-80dd-9974891b652e">
													<SHORT-NAME>CanTrcvChannelUsed</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Shall the related CAN transceiver channel be used?</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>true</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CanTrcvControlsPowerSupply -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:1ab6ed3e-bf66-41ba-b2f6-a4d964b54744">
													<SHORT-NAME>CanTrcvControlsPowerSupply</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Is ECU power supply controlled by this transceiver?</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">TRUE = Controlled by transceiver.
                                                FALSE = Not controlled by transceiver.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CanTrcvHwPnSupport -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:ad140e6f-373b-4f3e-a7a5-87ba8b29e7e3">
													<SHORT-NAME>CanTrcvHwPnSupport</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Indicates whether the HW supports the selective wake-up function</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">TRUE = Selective wakeup feature is supported by the transceiver
                                                FALSE = Selective wakeup functionality is not available in transceiver</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CanTrcvInitState -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:8e511d66-410f-4c0c-8540-01776a793337">
													<SHORT-NAME>CanTrcvInitState</SHORT-NAME>
													<DESC>
														<L-2 L="EN">State of CAN transceiver after call to CanTrcv_Init.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>CANTRCV_OP_MODE_NORMAL</DEFAULT-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:4418dfa2-a377-8e0c-4edf-48a3dbcf0071">
															<SHORT-NAME>CANTRCV_OP_MODE_NORMAL</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:817e43fd-9695-8580-616e-096c1109b628">
															<SHORT-NAME>CANTRCV_OP_MODE_SLEEP</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:e6b8e393-73b3-8865-597f-70fbbfa48b17">
															<SHORT-NAME>CANTRCV_OP_MODE_STANDBY</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CanTrcvMaxBaudrate -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:b89bc632-1e2a-4709-ab62-957c7252e0fe">
													<SHORT-NAME>CanTrcvMaxBaudrate</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Max baudrate for transceiver hardware type. Only used for validation purposes. Value shall be configured by configuration tool based on</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">transceiver hardware type.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>1000</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CanTrcvWakeupByBusUsed -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:52db1d3e-02dd-4437-a377-00108ef8a886">
													<SHORT-NAME>CanTrcvWakeupByBusUsed</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Is wake up by bus supported? If CAN transceiver hardware does not support wake up by bus value is always FALSE. If CAN transceiver hardware supports wake up by bus value is TRUE or FALSE depending whether it is used or not.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">TRUE = Is used.
                                                FALSE = Is not used.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: CanTrcvWakeupSourceRef -->
												<ECUC-REFERENCE-DEF UUID="ECUC:b00d0abd-0eaa-4026-9ec9-b035bb76ae17">
													<SHORT-NAME>CanTrcvWakeupSourceRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to a wakeup source in the EcuM configuration.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">This reference is only needed if CanTrcvWakeupByBusUsed is true.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMWakeupSource</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
											<SUB-CONTAINERS>
												<!-- Container Definition: CanTrcvAccess -->
												<ECUC-CHOICE-CONTAINER-DEF UUID="ECUC:9175045e-5280-4caf-a0c4-9033cb129316">
													<SHORT-NAME>CanTrcvAccess</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container gives CanTrcv Driver information about access to a single CAN transceiver.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<CHOICES>
														<!-- Container Definition: CanTrcvDioAccess -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:1d8e654e-9746-428f-929d-ddefe3a44578">
															<SHORT-NAME>CanTrcvDioAccess</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Container gives CAN transceiver driver information about accessing ports and port pins. In addition relation between CAN transceiver hardware pin names and Dio port access information is given.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">If a CAN transceiver hardware has no Dio interface, there is no instance of this container.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<SUB-CONTAINERS>
																<!-- Container Definition: CanTrcvDioChannelAccess -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:f3735ce7-dc10-4544-b55e-626808f713e0">
																	<SHORT-NAME>CanTrcvDioChannelAccess</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Container gives DIO channel access by single Can transceiver channel.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: CanTrcvHardwareInterfaceName -->
																		<ECUC-STRING-PARAM-DEF UUID="ECUC:0ec03d65-0055-4853-8dc8-5fcd1ef5a43e">
																			<SHORT-NAME>CanTrcvHardwareInterfaceName</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">CAN transceiver hardware interface name. It is typically the name of a pin. From a Dio point of view it is either a port, a single channel or a channel group. Depending on this fact either</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">CANTRCV_DIO_PORT_SYMBOLIC_NAME or CANTRCV_DIO_CHANNEL_SYMBOLIC_NAME or CANTRCV_DIO_CHANNEL_GROUP_SYMBOLIC_NAME 
                                                                        shall reference a Dio configuration.
                                                                        The CAN transceiver driver implementation description shall list up this name for the appropriate CAN transceiver hardware.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<ECUC-STRING-PARAM-DEF-VARIANTS>
																				<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
																			</ECUC-STRING-PARAM-DEF-VARIANTS>
																		</ECUC-STRING-PARAM-DEF>
																	</PARAMETERS>
																	<REFERENCES>
																		<!-- Choice Reference Definition: CanTrcvDioSymNameRef -->
																		<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:1e464264-ce16-4a16-87d1-89aab53d6a2b">
																			<SHORT-NAME>CanTrcvDioSymNameRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Choice Reference to a DIO Port, DIO Channel or DIO Channel Group. This reference replaces the CANTRCV_DIO_PORT_SYM_NAME,  CANTRCV_DIO_CHANNEL_SYM_NAME and  CANTRCV_DIO_GROUP_SYM_NAME references in the Can Trcv SWS.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REFS>
																				<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dio/DioConfig/DioPort/DioChannel</DESTINATION-REF>
																				<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dio/DioConfig/DioPort/DioChannelGroup</DESTINATION-REF>
																				<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dio/DioConfig/DioPort</DESTINATION-REF>
																			</DESTINATION-REFS>
																		</ECUC-CHOICE-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
															</SUB-CONTAINERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
														<!-- Container Definition: CanTrcvSpiSequence -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:9b42b06c-0a7a-45e1-bd04-40ea181004db">
															<SHORT-NAME>CanTrcvSpiSequence</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Container gives CAN transceiver driver information about one SPI sequence.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">One SPI sequence used by CAN transceiver driver is in exclusive use for it. No other driver is allowed to access this sequence. CAN transceiver driver may use one sequence to access n CAN transceiver hardwares chips of the same type or n sequences are used to access one single CAN transceiver hardware chip.
                                                        If a CAN transceiver hardware has no SPI interface, there is no instance of this container.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: CanTrcvSpiAccessSynchronous -->
																<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:7bcf2b70-75a7-41b5-ad47-bc7bfd282257">
																	<SHORT-NAME>CanTrcvSpiAccessSynchronous</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This parameter is used to define whether the access to the Spi sequence is synchronous or asynchronous.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">true: SPI access is synchronous. 
                                                                false: SPI access is asynchronous.</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>false</DEFAULT-VALUE>
																</ECUC-BOOLEAN-PARAM-DEF>
															</PARAMETERS>
															<REFERENCES>
																<!-- Symbolic Name Reference Definition: CanTrcvSpiSequenceName -->
																<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:67fba62d-4920-4112-80e9-839907bcc46a">
																	<SHORT-NAME>CanTrcvSpiSequenceName</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Reference to a Spi sequence configuration container.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Spi/SpiDriver/SpiSequence</DESTINATION-REF>
																</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
															</REFERENCES>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</CHOICES>
												</ECUC-CHOICE-CONTAINER-DEF>
												<!-- Container Definition: CanTrcvPartialNetwork -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:ca5b30fc-a757-4af2-bd25-7f0bf8a13690">
													<SHORT-NAME>CanTrcvPartialNetwork</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Container gives CAN transceiver driver information about the configuration of Partial Networking functionality.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: CanTrcvBaudRate -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:9ab3dac1-5cd1-4a6b-a556-dbcb6bf39ff5">
															<SHORT-NAME>CanTrcvBaudRate</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Indicates the CAN Bus communication baud rate in kbps.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>1000</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTrcvBusErrFlag -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:e7104ca6-1fad-4cfd-92c2-b18fac5c8a05">
															<SHORT-NAME>CanTrcvBusErrFlag</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Indicates if the Bus Error (BUSERR) flag is managed by the BSW. This flag is set if a bus failure is detected by the transceiver.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">TRUE = Supported by transceiver and managed by BSW. 
                                                        FALSE = Not managed by BSW.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTrcvPnCanIdIsExtended -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:bce803d7-e415-4177-a29f-ff17bdac4793">
															<SHORT-NAME>CanTrcvPnCanIdIsExtended</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Indicates whether extended or standard ID is used.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">TRUE = Extended Can identifier is used.
                                                        FALSE = Standard Can identifier is used</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTrcvPnEnabled -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:a5cd2332-0b8f-4e8b-93f0-8c20e953d527">
															<SHORT-NAME>CanTrcvPnEnabled</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Indicates whether the selective wake-up function is enabled or disabled in HW.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">TRUE = Selective wakeup feature is enabled in the  transceiver hardware
                                                        FALSE = Selective wakeup feature is disabled in the transceiver hardware</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTrcvPnFrameCanId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:4eadd3d6-3770-462b-a515-9a677fe5d057">
															<SHORT-NAME>CanTrcvPnFrameCanId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">CAN ID of the Wake-up Frame (WUF).</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTrcvPnFrameCanIdMask -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:138f069a-dcc3-403d-9398-9d247ccefbf8">
															<SHORT-NAME>CanTrcvPnFrameCanIdMask</SHORT-NAME>
															<DESC>
																<L-2 L="EN">ID Mask for the selective activation of the transceiver. It is used to enableFrame Wake-up (WUF) on a group of IDs.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTrcvPnFrameDlc -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:35e1b603-21c3-4bad-aca7-ff1081d6f632">
															<SHORT-NAME>CanTrcvPnFrameDlc</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Data Length of the Wake-up Frame (WUF).</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>8</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: CanTrcvPowerOnFlag -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:869324cb-1350-4b1c-b366-5b8b609b4ffc">
															<SHORT-NAME>CanTrcvPowerOnFlag</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Description: Indicates if the Power On Reset (POR) flag is available and is managed by the transceiver.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">TRUE = Supported by Hardware. 
                                                        FALSE = Not supported by Hardware</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
													</PARAMETERS>
													<SUB-CONTAINERS>
														<!-- Container Definition: CanTrcvPnFrameDataMaskSpec -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:a8cf14ab-3c00-4929-adf3-38a776992bf6">
															<SHORT-NAME>CanTrcvPnFrameDataMaskSpec</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Defines data payload mask to be used on the received payload in order to determine if the transceiver must be woken up by the received Wake-up Frame (WUF).</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>8</UPPER-MULTIPLICITY>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: CanTrcvPnFrameDataMask -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:656a6966-3a40-4948-9b0a-562933acdeb9">
																	<SHORT-NAME>CanTrcvPnFrameDataMask</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Defines the n byte (Byte0 = LSB) of the data payload mask to be used on the received payload in order to determine if the transceiver must be woken up by the received Wake-up Frame (WUF).</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: CanTrcvPnFrameDataMaskIndex -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:9693190e-eb2c-4688-a833-a834acc4d8d2">
																	<SHORT-NAME>CanTrcvPnFrameDataMaskIndex</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">holds the position n in frame of the mask-part</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>7</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CanTrcvGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:fce14966-e5e5-4705-80b8-c0fedce975d9">
									<SHORT-NAME>CanTrcvGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container gives CAN transceiver driver basic information.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CanTrcvDevErrorDetect -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:305a9a10-11cc-4baf-a909-107f41bc5ad4">
											<SHORT-NAME>CanTrcvDevErrorDetect</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Switches development error detection and notification on and off.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">If switched on, #define CANTRCV_DEV _ERROR_DETECT ON shall be generated. If switched off, #define CANTRCV_DEV_ERROR _DETECT OFF shall be generated. Define shall be part of file CanTrcv_Cfg.h.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CanTrcvGetVersionInfo -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:2d33aa8f-dca2-4d91-bad7-b9cd268b182e">
											<SHORT-NAME>CanTrcvGetVersionInfo</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Switches version information API on and off. If switched off, function need not be present in compiled code.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CanTrcvSPICommRetries -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:220ac6dc-bb8e-451b-a63b-63d7aa6307ef">
											<SHORT-NAME>CanTrcvSPICommRetries</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Indicates the maximal number of communication retries in case of failed SPI communication (applies both to timed out communication and to errors/NACK in the response data).</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">(0 ... 255 times, 0 means no retry allowed, communication must succeed at first try)</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>255</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CanTrcvSPICommTimeout -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:4807a785-38d3-426c-ad25-f1d9988e46c4">
											<SHORT-NAME>CanTrcvSPICommTimeout</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Indicates the maximal time allowed to the Transceiver in order to reply</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">(either positively or negatively) to a SPI command.

                                        (value in ms, 0ms means no specific timeout is to be used,
                                        communication is executed at the best of the SPI HW capacity)</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>100</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CanTrcvWaitCount -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:3c48b6f3-61ff-4bd9-be07-4e0ed8351855">
											<SHORT-NAME>CanTrcvWaitCount</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Indicates the number of wait states to change the transceiver operation mode. Transceiver hardware may need wait states for some transitions.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>4294967295</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CanTrcvWakeUpSupport -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:95db2b45-712c-42e8-88d9-8ca25bf4cd4d">
											<SHORT-NAME>CanTrcvWakeUpSupport</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Informs whether wake up is supported by polling or not supported. In case no</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">wake up is supported by the hardware, setting has to be NOT_SUPPORTED. Only in the case of wake up supported by polling, function CanTrcv_MainFunction has to be present and to be invoked by the scheduler.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:e18a544d-4933-8ce7-0d84-14941088ff5c">
													<SHORT-NAME>CANTRCV_WAKEUP_BY_POLLING</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:69705d89-ca02-88c9-3401-5d704f5ff5a4">
													<SHORT-NAME>CANTRCV_WAKEUP_NOT_SUPPORTED</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
