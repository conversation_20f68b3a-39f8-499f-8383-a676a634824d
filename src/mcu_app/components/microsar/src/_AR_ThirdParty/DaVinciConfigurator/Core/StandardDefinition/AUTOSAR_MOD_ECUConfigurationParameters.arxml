<?xml version="1.0" encoding="UTF-8"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<ECUC-DEFINITION-COLLECTION UUID="ECUC:4c30de73-30fc-43b3-a1b3-e74211eedff3">
							<SHORT-NAME>AUTOSARParameterDefinition</SHORT-NAME>
							<MODULE-REFS>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Adc</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/BswM</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Cal</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Can</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/CanIf</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/CanNm</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/CanSM</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/CanTp</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/CanTrcv</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Cdd</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Com</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/ComM</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/CorTst</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Crc</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Csm</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Dbg</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Dcm</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Dem</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Det</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Dio</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Dlt</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/DoIP</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Ea</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Ecom</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/EcuC</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/EcuM</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Eep</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Eth</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/EthIf</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/EthSM</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/EthSwt</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/EthTrcv</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Fee</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/FiM</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Fls</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/FlsTst</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Fr</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/FrArTp</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/FrIf</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/FrNm</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/FrSM</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/FrTp</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/FrTrcv</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Gpt</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Icu</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/IpduM</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/J1939Dcm</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/J1939Nm</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/J1939Rm</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/J1939Tp</MODULE-REF>
                                <MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/KeyM</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Lin</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/LinIf</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/LinNm</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/LinSM</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/LinTp</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/LinTrcv</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Mcu</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/MemIf</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/MemMap</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Nm</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/NvM</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Os</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/PduR</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Port</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Pwm</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/RamTst</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Rte</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Sd</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/SecOC</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/SoAd</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Spi</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/StbM</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/TcpIp</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/UdpNm</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Wdg</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/WdgIf</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/WdgM</MODULE-REF>
								<MODULE-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Xcp</MODULE-REF>
							</MODULE-REFS>
						</ECUC-DEFINITION-COLLECTION>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
