/*******************************************************************************
 * Copyright (c) 2010, 2014 IBM Corporation and others.
 *
 * This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License 2.0
 * which accompanies this distribution, and is available at
 * https://www.eclipse.org/legal/epl-2.0/
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *     <PERSON> <<EMAIL>> - Bug 420836
 *     <PERSON> <<EMAIL>> - Bug 325937
 *     <PERSON><PERSON> <<EMAIL>> - Bug 501250
 *******************************************************************************/

@import url("platform:/plugin/org.eclipse.ui.themes/css/e4_basestyle.css");

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_START {
	color: #D2E1F0;
}

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_END {
	color: #CEDDED;
}

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_TAB_OUTER_KEYLINE_COLOR {
	color: #CCCCCC;
}

ColorDefinition#org-eclipse-ui-workbench-INACTIVE_UNSELECTED_TABS_COLOR_START {
	color: #F4F6FC;
}

ColorDefinition#org-eclipse-ui-workbench-INACTIVE_UNSELECTED_TABS_COLOR_END {
	color: #F4F6FC;
}

ColorDefinition#org-eclipse-ui-workbench-INACTIVE_TAB_OUTER_KEYLINE_COLOR {
	color: #FFFFFF;
}

ColorDefinition#org-eclipse-ui-workbench-INACTIVE_TAB_OUTLINE_COLOR {
	color: #CED5E5;
}

ColorDefinition#org-eclipse-ui-workbench-INACTIVE_TAB_BG_START {
	color: #D5E1ED;
}

ColorDefinition#org-eclipse-ui-workbench-INACTIVE_TAB_BG_END {
	color: #FFFFFF;
}

.MTrimmedWindow {
	background-color: #E1E6F6;
}

.MPartStack {
	swt-simple: true;
	swt-mru-visible: false;
}

.MTrimBar {
	background-color: #E1E6F6;
}

.MTrimBar#org-eclipse-ui-main-toolbar {
	background-color: #F5F7FC #E1E6F6 100%;
}

.MToolControl.TrimStack {
	frame-image:  url(./win7TSFrame.png);
	handle-image:  url(./win7Handle.png);
	frame-image-rotated:  url(./win7TSFrame-rotated.png);
	handle-image-rotated:  url(./win7Handle-rotated.png);
}

#PerspectiveSwitcher  {
	background-color: #F5F7FC #E1E6F6 100%;
	eclipse-perspective-keyline-color: #E1E6F6 #AAB0BF;
}

#org-eclipse-ui-editorss {
	swt-tab-height: 8px;
	padding: 0px 5px 7px;
}

CTabFolder.MArea .MPartStack, CTabFolder.MArea .MPartStack.active {
	swt-shadow-visible: false;
}

CTabFolder Canvas {
	background-color: #F8F8F8;
}

Form, FormHeading {
	background-color: #ffffff;
	background: #ffffff;
	background-image: #ffffff;
	color: #505050;
}

Form {
	/* Bug 465148: Additional styling for the Form */
	text-background-color: #ffffff;

	tb-toggle-hover-color: #505050;
	tb-toggle-color: #505050;
	h-hover-full-color: #505050;
	h-hover-light-color: #505050;
	h-bottom-keyline-2-color: #eaeaea;
	h-bottom-keyline-1-color: #eaeaea;
}



Section {
	background-color: #ffffff;
  	color: #505050;
  	background-color-titlebar: #eaeaea;
  	background-color-gradient-titlebar: #eaeaea;
  	border-color-titlebar: #ffffff;
}

TabbedPropertyTitle > CLabel{
	color: #505050;
}

TabbedPropertyTitle {
	swt-backgroundGradientStart-color:  #eaeaea;
	swt-backgroundGradientEnd-color:    #eaeaea;
	swt-backgroundBottomKeyline1-color: #eaeaea;
	swt-backgroundBottomKeyline2-color: #eaeaea;
}

TabbedPropertyList {
	swt-tabAreaBackground-color : #ffffff;
	swt-tabBackground-color     : '#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_BG_START';
	swt-tabNormalShadow-color   : '#org-eclipse-ui-workbench-INACTIVE_TAB_OUTLINE_COLOR';             /* color of shadow lines around the tabs */
	swt-tabDarkShadow-color     : '#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_SELECTED_TEXT_COLOR'; /* line color of the tiny scroll triangle (at top / at bottom) */
	color                       : #505050; /* text color in the tab / tab area */
}