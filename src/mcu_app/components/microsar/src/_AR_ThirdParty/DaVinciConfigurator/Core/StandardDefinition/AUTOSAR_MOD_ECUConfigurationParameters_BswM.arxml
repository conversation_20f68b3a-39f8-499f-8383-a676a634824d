<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: BswM -->
						<ECUC-MODULE-DEF UUID="ECUC:98c4844c-a16a-4b8b-9928-d9846dcb2d25">
							<SHORT-NAME>BswM</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the BswM (Basic SW Mode Manager) module.</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.2.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
										<DATE>2011-11-09</DATE>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-LINK-TIME</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: BswMConfig -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:80f40234-216b-48cd-a96c-4eeab986d79c">
									<SHORT-NAME>BswMConfig</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container contains the configuration parameters and sub containers of the AUTOSAR BswM module. This container is a MultipleConfigurationContainer, i.e. this container and its sub-containers exist once per configuration set.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<SUB-CONTAINERS>
										<!-- Container Definition: BswMArbitration -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:1bb152a0-b36c-460c-bada-c3347399266e">
											<SHORT-NAME>BswMArbitration</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container includes all configuration sub-containers and parameters related to the mode arbitration functionality of the BswM.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<SUB-CONTAINERS>
												<!-- Container Definition: BswMLogicalExpression -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:24badaf3-6672-43f0-b3b0-3343c67796e6">
													<SHORT-NAME>BswMLogicalExpression</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container describes the logical expressions that can be used for the mode arbitration. The logical expressions are built of a set of arguments and a logical operator.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">Each argument can either be a mode condition or a sub-expression to allow definition of more complex logical expressions.
                                                There may be an unlimited number of arguments in each logical expression.
                                                Note that the order of evaluation of the expressions is not defined.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: BswMLogicalOperator -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:71652a7c-9360-42b3-9432-2cd58bfe7949">
															<SHORT-NAME>BswMLogicalOperator</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter specifies the logical operator to be used in the logical expression. If the expression only consists of a single condition this parameter shall not be used.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:f87e5a65-29c2-8689-691c-9dfdd3f79558">
																	<SHORT-NAME>BSWM_AND</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:f33c5280-44e2-8a5e-4635-67ce52facb58">
																	<SHORT-NAME>BSWM_NAND</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:0f3a087f-c03f-931f-3d60-6cfc0ee1578f">
																	<SHORT-NAME>BSWM_OR</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:a85efe27-1a02-9246-3ba5-638452e9708f">
																	<SHORT-NAME>BSWM_XOR</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Choice Reference Definition: BswMArgumentRef -->
														<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:509833cd-a9df-4ffb-ba49-a4fea4761bf3">
															<SHORT-NAME>BswMArgumentRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This is a choice reference either to a mode condition or a sub-expression.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">In case the BswMLogicalExpression.BswMLogicalOperator equals BSWM_NAND only two operands are supported.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REFS>
																<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/BswM/BswMConfig/BswMArbitration/BswMLogicalExpression</DESTINATION-REF>
																<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/BswM/BswMConfig/BswMArbitration/BswMModeCondition</DESTINATION-REF>
															</DESTINATION-REFS>
														</ECUC-CHOICE-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: BswMModeCondition -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:55038f0f-7e6c-49bf-a155-df75c068efe5">
													<SHORT-NAME>BswMModeCondition</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container describes the BswM mode conditions that can be used either by itself to form a rule or as a part of a logical expression.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: BswMConditionType -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:4f137728-854d-4d76-aa9c-311449567f09">
															<SHORT-NAME>BswMConditionType</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter specifies what kind of comparison that is made for the evaluation of the mode condition.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:19edad62-5e88-8f8f-55b6-32c95062c4bc">
																	<SHORT-NAME>BSWM_EQUALS</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:99b74636-4546-8e0d-376d-44ae5c065b65">
																	<SHORT-NAME>BSWM_EQUALS_NOT</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: BswMConditionMode -->
														<ECUC-REFERENCE-DEF UUID="ECUC:6a6a7ef5-77b2-4220-b440-3c7ad95e6237">
															<SHORT-NAME>BswMConditionMode</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter references the mode request port that is used for the condition.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/BswM/BswMConfig/BswMArbitration/BswMModeRequestPort</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
													<SUB-CONTAINERS>
														<!-- Container Definition: BswMConditionValue -->
														<ECUC-CHOICE-CONTAINER-DEF UUID="ECUC:6dfef60d-1d6f-4b42-a4ae-f32d9a3df94f">
															<SHORT-NAME>BswMConditionValue</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This container holds the parameters and references necessary to identify the mode type and the value that the mode request is compared to.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<CHOICES>
																<!-- Container Definition: BswMBswMode -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:a9aa03c3-81c2-4949-b63a-a84a134a3291">
																	<SHORT-NAME>BswMBswMode</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container defines the value and type of a mode in the BSW.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: BswMBswRequestedMode -->
																		<ECUC-STRING-PARAM-DEF UUID="ECUC:04a0b0ee-322c-48dc-a12d-2287e87e2e44">
																			<SHORT-NAME>BswMBswRequestedMode</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter contains the symbolic name (as a string) of a certain mode/state that can be requested/indicated by the BSW modules.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<ECUC-STRING-PARAM-DEF-VARIANTS>
																				<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
																			</ECUC-STRING-PARAM-DEF-VARIANTS>
																		</ECUC-STRING-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMModeDeclaration -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:2ed5817c-df9e-47bd-a384-c5c818caf935">
																	<SHORT-NAME>BswMModeDeclaration</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">When the mode corresponds to a mode request or mode indication interface the mode is defined by a mode declaration. The mode declarations are defined in the SW-C Template and hence a foreign reference to the corresponding Mode Declaration is used.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Foreign Reference Definition: BswMModeValueRef -->
																		<ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:16d03c04-047d-4b09-b573-5986fdeff4ed">
																			<SHORT-NAME>BswMModeValueRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a foreign reference to the Mode Declaration used for the mode requests corresponding to this condition.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-TYPE>MODE-DECLARATION</DESTINATION-TYPE>
																		</ECUC-FOREIGN-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
															</CHOICES>
														</ECUC-CHOICE-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: BswMModeRequestPort -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:46239dbf-b616-4d8e-be72-4e2f06f0c7df">
													<SHORT-NAME>BswMModeRequestPort</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Each instance of this container defines a mode request interface that is used to requests or indicate modes from/to the BswM. These interfaces are implemented as ports or as ordinary C-functions based upon if the request is made by an SW-C or a BSW module.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">There are different types of mode requests:
                                                1. Mode requests from the SW-C:s
                                                2. Mode Requests from other BSW modules such as the DCM. 
                                                3. State/mode indications from the RTE or other BSW modules such as the bus specific State Managers.

                                                Note that the BswM treats all request and indications in the exact same way.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: BswMRequestProcessing -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:a853591d-e671-4863-aa10-0082efe4dda1">
															<SHORT-NAME>BswMRequestProcessing</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines if the processing of the mode arbitration shall be done immediately when a mode request is received or if it shall be deferred to the processing of the main function of BswM.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:c288aa3d-846e-8fbb-54df-5a1886837216">
																	<SHORT-NAME>BSWM_DEFERRED</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:747a062c-d980-92dd-42bc-9f0c1194e87e">
																	<SHORT-NAME>BSWM_IMMEDIATE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
													</PARAMETERS>
													<SUB-CONTAINERS>
														<!-- Container Definition: BswMModeInitValue -->
														<ECUC-CHOICE-CONTAINER-DEF UUID="ECUC:a780195c-9698-494b-810e-d6f1b56cef30">
															<SHORT-NAME>BswMModeInitValue</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This container defines the initial mode value that is used by BswM for the corresponding mode request after initialization. This container is optional and shall only be used for Mode Requests that do not already have an initial value.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">If this container is not present the requested mode is undefined after initialization of BswM. The requested mode will remain undefined until the requester performs a request.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<CHOICES>
																<!-- Container Definition: BswMBswModeInitValue -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:6b72e37c-506c-4dee-91b5-84f3dfabcf3b">
																	<SHORT-NAME>BswMBswModeInitValue</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This is the choice for a initial mode value used for the initialization of mode requests in case the request is made by a BSW module.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: BswMBswModeInitValueMode -->
																		<ECUC-STRING-PARAM-DEF UUID="ECUC:ce82e9c7-09eb-4a12-8e46-aaa2333a3fb7">
																			<SHORT-NAME>BswMBswModeInitValueMode</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines the initial mode value that is used by BswM for the corresponding mode request after initialization.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<ECUC-STRING-PARAM-DEF-VARIANTS>
																				<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
																			</ECUC-STRING-PARAM-DEF-VARIANTS>
																		</ECUC-STRING-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMSwcModeInitValue -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:9100ba60-b7ab-4c63-9121-f9c057463ebf">
																	<SHORT-NAME>BswMSwcModeInitValue</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This is the choice for a foreign reference to the Mode Declaration used for the initialization of mode requests in case the request is made by a SW-C.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Foreign Reference Definition: BswMSwcModeInitValueRef -->
																		<ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:087f03c1-56f6-4fdf-bba4-1050c4d08f8a">
																			<SHORT-NAME>BswMSwcModeInitValueRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a foreign reference to the Mode Declaration used for the initialization of mode requests.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-TYPE>MODE-DECLARATION</DESTINATION-TYPE>
																		</ECUC-FOREIGN-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
															</CHOICES>
														</ECUC-CHOICE-CONTAINER-DEF>
														<!-- Container Definition: BswMModeRequestSource -->
														<ECUC-CHOICE-CONTAINER-DEF UUID="ECUC:dd5d5587-bfd1-4960-a654-bcbc222aca93">
															<SHORT-NAME>BswMModeRequestSource</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This choice container specifies the source of the mode request or state/mode indication. The requester of a mode can be both SW-C:s and other BSW Modules, such as the bus specific State Managers.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<CHOICES>
																<!-- Container Definition: BswMBswModeNotification -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:a9fda01e-f3ad-46c3-affd-8a4e594a80e0">
																	<SHORT-NAME>BswMBswModeNotification</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">The source of the mode request is a Bsw Module.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">If the BswMRequestProcessing of this BswMModeRequestPort is set to BSWM_IMMEDIATE then it is assumed that the Basic Software Module Description of the BswM contains a BswSchedulableEntity which is activated by a BswModeSwitchEvent. This BswModeSwitchEvent shall refer to the ModeDeclarationGroupPrototype which is referenced by BswMBswModeDeclarationGroupPrototypeRef.

                                                                If the BswMRequestProcessing of this BswMModeRequestPort is set to BSWM_DEFERRED then it is up to the implementer of the BswM tooling whether a BswSchedulableEntity is used to update the BswM internal mode mirror or whether the BswM internal mode mirror is updated during the main function execution.</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Foreign Reference Definition: BswMBswModeDeclarationGroupPrototypeRef -->
																		<ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:d1c204e5-6c8a-45e9-a191-7cc780db6f12">
																			<SHORT-NAME>BswMBswModeDeclarationGroupPrototypeRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a foreign reference to the Mode Declaration Group Prototype.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-TYPE>MODE-DECLARATION-GROUP-PROTOTYPE</DESTINATION-TYPE>
																		</ECUC-FOREIGN-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMCanSMIndication -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:60c83276-d2ab-4077-9e33-d59f361ac518">
																	<SHORT-NAME>BswMCanSMIndication</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This is an indication of the current state of the CAN State Manager.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMCanSMChannelRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:45faeefb-4d0e-4a75-bac3-3fe76ff15822">
																			<SHORT-NAME>BswMCanSMChannelRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to the CAN channel handle that the mode request corresponds to.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/ComM/ComMConfigSet/ComMChannel</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMComMIndication -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:873fd64c-17e3-44f6-84a5-b045a48b8dc0">
																	<SHORT-NAME>BswMComMIndication</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This is an indication of the current communication mode of a channel in the Communication Manager.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMComMChannelRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:4ad44a4c-b49e-4bff-99ef-aa813a13ee6e">
																			<SHORT-NAME>BswMComMChannelRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to the Communication Manager channel handle that the indication corresponds to.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/ComM/ComMConfigSet/ComMChannel</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMComMPncRequest -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:48cb0816-8ced-4ad5-a493-2a63540eae85">
																	<SHORT-NAME>BswMComMPncRequest</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This is a request of the current communication mode of a Partial Network Cluster in the Communication Manager.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMComMPncRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:7b2f53bf-0115-4dc2-bd73-2ad6a4eac383">
																			<SHORT-NAME>BswMComMPncRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to the Communication Manager PNC handle of the Partial Network Cluster that the request corresponds to.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/ComM/ComMConfigSet/ComMPnc</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMDcmApplicationUpdatedIndication -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:ccb455cd-746c-48e3-a406-201cc4294c40">
																	<SHORT-NAME>BswMDcmApplicationUpdatedIndication</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This is a request to update application data from the DCM. This container does not contain any parameters since there are no further configuration needed for this type of request.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMDcmComModeRequest -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:f00a68aa-b64e-4d58-b694-f815644860a2">
																	<SHORT-NAME>BswMDcmComModeRequest</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">The source of the mode request is the Diagnostic Communication Manager.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: BswMDcmComMNetwork -->
																		<ECUC-STRING-PARAM-DEF UUID="ECUC:2bd12696-b9c4-49a7-bec0-b38cf1de1d05">
																			<SHORT-NAME>BswMDcmComMNetwork</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter specifies the network the request relates to.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<ECUC-STRING-PARAM-DEF-VARIANTS>
																				<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
																			</ECUC-STRING-PARAM-DEF-VARIANTS>
																		</ECUC-STRING-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMEcuMIndication -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:90275834-804e-460b-9d2b-0a066e5130ef">
																	<SHORT-NAME>BswMEcuMIndication</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This is a notification of the current operation mode of the ECU State Manager. This container does not contain any parameters since there are no further configuration needed for this type of request.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMEcuMWakeupSource -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:fedcd4b6-8179-4135-b1eb-0d4ed3702d3a">
																	<SHORT-NAME>BswMEcuMWakeupSource</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This is a notification of the current state of an ECU State Manager wakeup source.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMEcuMWakeupSrcRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:c11bd4c1-5ed5-4ed5-8d1d-4d1370aeef89">
																			<SHORT-NAME>BswMEcuMWakeupSrcRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to the ECU State Manager Wakeup Source that the indication corresponds to.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMWakeupSource</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMEthSMIndication -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:c7a1a655-ad99-4506-8f61-328c655fc837">
																	<SHORT-NAME>BswMEthSMIndication</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This is an indication of the current state of the Ethernet State Manager.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMEthSMChannelRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:1276bcab-5dc0-4704-b50b-7b714bdcbf4a">
																			<SHORT-NAME>BswMEthSMChannelRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to the Ethernet channel handle that the mode request corresponds to.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/ComM/ComMConfigSet/ComMChannel</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMFrSMIndication -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:bacadd7e-ce13-40d0-bd5e-2a656b772fbd">
																	<SHORT-NAME>BswMFrSMIndication</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This is an indication of the current state of the FlexRay State Manager.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMFrSMChannelRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:58be722e-6d43-4ed1-b1f0-441da75ea1cc">
																			<SHORT-NAME>BswMFrSMChannelRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to the  FlexRay Cluster handle that the mode request corresponds to.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/ComM/ComMConfigSet/ComMChannel</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMGenericRequest -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:92e6a2bd-21cc-446c-abf1-172371d7a891">
																	<SHORT-NAME>BswMGenericRequest</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This mode request originates from a requester that is not among the list of standardized mode requesters (i.e. the different resource managers).</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: BswMModeRequesterId -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:418ab994-a918-4944-ad81-e4720ddd8b56">
																			<SHORT-NAME>BswMModeRequesterId</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameters identifies the different users of the generic mode request interface.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<MAX>65535</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																		<!-- PARAMETER DEFINITION: BswMRequestedModeMax -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:5a53cdcf-97de-4ac2-9526-ecc1e037bad0">
																			<SHORT-NAME>BswMRequestedModeMax</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines the upper limit for the modes requested by this mode requester.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<MAX>65535</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMLinSMIndication -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:17fe2db3-4d90-462a-9e6b-d1ba8d923419">
																	<SHORT-NAME>BswMLinSMIndication</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This is an indication of the current state of the LIN State Manager.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMLinSMChannelRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:806494fe-5721-4700-9aff-afb9b96842f4">
																			<SHORT-NAME>BswMLinSMChannelRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to the LIN channel handle that the mode request corresponds to.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/ComM/ComMConfigSet/ComMChannel</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMLinScheduleIndication -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:b605a404-6163-4591-8c3c-0832afb197ce">
																	<SHORT-NAME>BswMLinScheduleIndication</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This is an indication of the currently active LIN Schedule Table for a specific LIN Interface.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMLinScheduleRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:a377f06e-9872-4bba-9040-e413f8949261">
																			<SHORT-NAME>BswMLinScheduleRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to the LIN Schedule Table handle that the mode request corresponds to.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/LinSM/LinSMConfigSet/LinSMChannel/LinSMSchedule</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMLinTpModeRequest -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:34b88433-6423-4a7c-9d50-73e217a7730a">
																	<SHORT-NAME>BswMLinTpModeRequest</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This is a LinTp mode request from the LinIf. This port corresponds to a call of the BswM_LinTp_RequestMode API.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMLinTpChannelRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:2d8c4b55-134e-4e91-8aa2-8990b56a74c3">
																			<SHORT-NAME>BswMLinTpChannelRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to the LIN Interface Channel that the mode</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">request corresponds to.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/LinIf/LinIfGlobalConfig/LinIfChannel</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMNvMJobModeIndication -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:2452616b-0819-4fa3-9a20-28c9fd64b6fe">
																	<SHORT-NAME>BswMNvMJobModeIndication</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Indicates the current status of the multiblock job. The job is identified via BswMNvmService, e.g. 0x0c for NvmReadAll, 0x0d for NvmWriteAll. Possible Values are:</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">NvM_RequestResultType
                                                                NVM_REQ_OK
                                                                NVM_REQ_NOT_OK
                                                                NVM_REQ_PENDING
                                                                NVM_REQ_INTEGRITY_FAILED
                                                                NVM_REQ_BLOCK_SKIPPED
                                                                NVM_REQ_NV_INVALIDATED
                                                                NVM_REQ_CANCELED
                                                                NVM_REQ_REDUNDANCY_FAILED
                                                                NVM_REQ_RESTORED_FROM_ROM</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: BswMNvmService -->
																		<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:ac4b5a2a-a386-4bb4-b7b8-58a5baaefe2c">
																			<SHORT-NAME>BswMNvmService</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">Identifies the Nvm job which is related to the mode request.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES/>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<LITERALS>
																				<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:8b0dfe07-c763-98da-5910-74b56e4370b1">
																					<SHORT-NAME>NvmReadAll</SHORT-NAME>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																				</ECUC-ENUMERATION-LITERAL-DEF>
																				<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:605710a3-91e4-9616-4d00-5b9637f4b267">
																					<SHORT-NAME>NvmWriteAll</SHORT-NAME>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																				</ECUC-ENUMERATION-LITERAL-DEF>
																			</LITERALS>
																		</ECUC-ENUMERATION-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMNvMRequest -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:7e2e5f6f-6b37-4408-8b4b-fb66169315d4">
																	<SHORT-NAME>BswMNvMRequest</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Via this Mode Request Source the NvM indicates the current status of the specified block. Possible Values are:</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">NvM_RequestResultType
                                                                NVM_REQ_OK
                                                                NVM_REQ_NOT_OK
                                                                NVM_REQ_PENDING
                                                                NVM_REQ_INTEGRITY_FAILED
                                                                NVM_REQ_BLOCK_SKIPPED
                                                                NVM_REQ_NV_INVALIDATED
                                                                NVM_REQ_CANCELED
                                                                NVM_REQ_REDUNDANCY_FAILED
                                                                NVM_REQ_RESTORED_FROM_ROM</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMNvMBlockRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:63b81990-3e16-437b-84f5-08451acf7709">
																			<SHORT-NAME>BswMNvMBlockRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to the NvM Block Descriptor that the request corresponds to.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/NvM/NvMBlockDescriptor</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMSwcModeNotification -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:2f25796e-4212-4187-a4fd-5cae123930de">
																	<SHORT-NAME>BswMSwcModeNotification</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This is a mode switch notification associated with a RTE switch interface.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Foreign Reference Definition: BswMSwcModeNotificationModeDeclarationGroupPrototypeRef -->
																		<ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:5aa3f84f-623b-4f8a-846d-b0f2f8506bd5">
																			<SHORT-NAME>BswMSwcModeNotificationModeDeclarationGroupPrototypeRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a foreign reference to the ModeDeclarationGroupPrototype.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-TYPE>MODE-DECLARATION-GROUP-PROTOTYPE</DESTINATION-TYPE>
																		</ECUC-FOREIGN-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMSwcModeRequest -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:cec88478-df6b-44b1-9036-edb5f3e101eb">
																	<SHORT-NAME>BswMSwcModeRequest</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">The source of the mode request is a SW Component.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Foreign Reference Definition: BswMSwcModeRequestModeDeclarationGroupPrototypeRef -->
																		<ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:106e96eb-0779-4153-b2f9-b05730aa991a">
																			<SHORT-NAME>BswMSwcModeRequestModeDeclarationGroupPrototypeRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a foreign reference to the ModeDeclarationGroupPrototype. As the mode request is SR-Communication the BswM shall provide a SR-Interface which corresponds to the ModeDeclarationGroupPrototype.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">The SR-Interface shall contain one ApplicationPrimitiveDataType which is defined as an enumeration (compuMethod) with the enumeration literals matching the ModeDeclarations.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-TYPE>MODE-DECLARATION-GROUP-PROTOTYPE</DESTINATION-TYPE>
																		</ECUC-FOREIGN-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMWdgMRequestPartitionReset -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:77b44dcd-e54f-4030-bdbc-ecdd6e569ce4">
																	<SHORT-NAME>BswMWdgMRequestPartitionReset</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This is a Partition Reset request from from the WdgM. This port</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">corresponds to a call of the BswM_WdgM_RequestPartitionReset API.</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Reference Definition: BswMWdgMRequestPartitionResetRef -->
																		<ECUC-REFERENCE-DEF UUID="ECUC:6770d76c-a522-43c4-961f-26d1d6e61afa">
																			<SHORT-NAME>BswMWdgMRequestPartitionResetRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to the partition that shall be reset.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPartitionCollection/EcucPartition</DESTINATION-REF>
																		</ECUC-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
															</CHOICES>
														</ECUC-CHOICE-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: BswMRule -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:9711550f-8c6a-4c9d-a1bd-b6d3828a9ce5">
													<SHORT-NAME>BswMRule</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Each instance of this container describes a BswM arbitration rule. The rule either consists of a simple mode condition or a more complex logical expression. This container also references the action lists that shall be invoked when the rule is evaluated to True or False.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: BswMNestedExecutionOnly -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:75ab5231-5ebb-4aec-a088-bc956ba35023">
															<SHORT-NAME>BswMNestedExecutionOnly</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines for its related Rule if the Rule is an Independent rule or a Subordinate rule;</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">false: an Independent rule, i.e. to be evaluated each time applicable (both as standalone Rule driven by its own BswMModeRequestSource and when referenced by another Rule).

                                                        true: a Subordinated rule, to be evaluated ONLY as a result of being referenced in one or more Action Lists.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: BswMRuleInitState -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:42592c03-2e60-4cb0-8357-5a711cba0547">
															<SHORT-NAME>BswMRuleInitState</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter is a part of the reset/initialization behavior of BswM.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">Action lists are executed when the result of a rule evaluation have changed since the last evaluation. This parameter defines the &quot;previous evaluation result&quot; of a rule to be used after initialization of the BswM.

                                                        If this parameter is set to BSWM_UNDEFINED, the evaluation result is always treated as changed at the first evaluation of the rule after initialization.

                                                        If this parameter is set to BSWM_TRUE, the evaluation result is treated as changed if the rule is evaluated to false. 

                                                        If this parameter is set to BSWM_FALSE, the evaluation result is treated as changed if the rule is evaluated to true.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:28696f72-b875-91ce-2200-ef4ba71c8f7c">
																	<SHORT-NAME>BSWM_FALSE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:7b7f3fa8-bcd7-9938-58d1-cd3c9995f3cf">
																	<SHORT-NAME>BSWM_TRUE</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:4dc19ee4-898c-9426-495e-0f245ad848a8">
																	<SHORT-NAME>BSWM_UNDEFINED</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<!-- Reference Definition: BswMRuleExpressionRef -->
														<ECUC-REFERENCE-DEF UUID="ECUC:f734f4cd-0746-4a1b-85b4-2791ac9d910e">
															<SHORT-NAME>BswMRuleExpressionRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This choice reference either references the mode condition or the logical expression that is evaluated for each rule.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/BswM/BswMConfig/BswMArbitration/BswMLogicalExpression</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
														<!-- Reference Definition: BswMRuleFalseActionList -->
														<ECUC-REFERENCE-DEF UUID="ECUC:cd76267f-738a-4738-ac2c-7b7d38b31ffa">
															<SHORT-NAME>BswMRuleFalseActionList</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This is a reference to the action list that shall be executed when the rule is evaluated to False</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/BswM/BswMConfig/BswMModeControl/BswMActionList</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
														<!-- Reference Definition: BswMRuleTrueActionList -->
														<ECUC-REFERENCE-DEF UUID="ECUC:309d8777-fea3-4ded-a73e-980296f093aa">
															<SHORT-NAME>BswMRuleTrueActionList</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This is a reference to the action list that shall be executed when the rule is evaluated to True</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/BswM/BswMConfig/BswMModeControl/BswMActionList</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: BswMDataTypeMappingSets -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:fd679d55-8201-4daf-8762-a432eb79cb8c">
											<SHORT-NAME>BswMDataTypeMappingSets</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Collection of references to DataTypeMappingSet.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<REFERENCES>
												<!-- Foreign Reference Definition: BswMDataTypeMappingSetRef -->
												<ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:f0b7d567-bfdf-47c7-8459-32f20d811612">
													<SHORT-NAME>BswMDataTypeMappingSetRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to DataTypeMappingSet.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<DESTINATION-TYPE>DATA-TYPE-MAPPING-SET</DESTINATION-TYPE>
												</ECUC-FOREIGN-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: BswMModeControl -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:0f9b55d8-de62-4333-96a5-061519c7ffc1">
											<SHORT-NAME>BswMModeControl</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container includes all configuration sub-containers and parameters related to the mode control functionality of the BswM.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<SUB-CONTAINERS>
												<!-- Container Definition: BswMAction -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:add00e45-e97c-4d9a-8208-befcbfbfd3e1">
													<SHORT-NAME>BswMAction</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Each container of this type defines an action. These actions can be part of one or several action lists.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<SUB-CONTAINERS>
														<!-- Container Definition: BswMAvailableActions -->
														<ECUC-CHOICE-CONTAINER-DEF UUID="ECUC:cc9ad948-01ce-4ed4-a898-41fcd6ede588">
															<SHORT-NAME>BswMAvailableActions</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Choice container including the available actions to be used in the action lists.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<CHOICES>
																<!-- Container Definition: BswMComMAllowCom -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:0ce4c9d3-b52a-4bf4-8268-aebdfac80f52">
																	<SHORT-NAME>BswMComMAllowCom</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container includes all parameters for the action to allow or to block communication for a ComM Channel. ComM_CommunicationAllowed  is called when this action is configured.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: BswMComAllowed -->
																		<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:a75f55aa-9e34-48e5-9c33-8e121ac7f7d8">
																			<SHORT-NAME>BswMComAllowed</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">The parameter BswMComMAllowChannelRef refers to a channel which will allow or block communication using the function ComM_CommunicationAllowed().</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">This parameter corresponds to the parameter &quot;Allowed&quot; of the function ComM_CommunicationAllowed().</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																	</PARAMETERS>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMComMAllowChannelRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:cfa3f8ee-4192-4364-a046-f5ef39f8626f">
																			<SHORT-NAME>BswMComMAllowChannelRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to the ComM Channel for which communication shall be allowed or blocked.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">This reference corresponds to the parameter &quot;Channel&quot; of the function ComM_CommunicationAllowed().</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/ComM/ComMConfigSet/ComMChannel</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMComMModeLimitation -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:93ede02b-24af-4c08-a0ef-1e3a636fbbcc">
																	<SHORT-NAME>BswMComMModeLimitation</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container includes all parameters related to a limitation of communication mode for a ComM Channel. ComM_LimitChannelToNoComMode is called when this action is configured.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: BswMComMLimitMode -->
																		<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:a90697cd-02ca-46c7-b4d9-3561ac126656">
																			<SHORT-NAME>BswMComMLimitMode</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">The function ComM_LimitChannelToNoComMode() takes in this boolean parameter to limit the channel&apos;s com mode to no-com mode.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">This parameter corresponds to the parameter &quot;Status&quot; of the function ComM_LimitChannelToNoComMode.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																	</PARAMETERS>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMComMLimitChannelRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:dfb83a13-105c-4ad9-b65d-8465bcad8ddd">
																			<SHORT-NAME>BswMComMLimitChannelRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to the ComM channel for which the communication mode should be limited.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">This reference corresponds to the parameter &quot;Channel&quot; of the function ComM_LimitChannelToNoComMode.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/ComM/ComMConfigSet/ComMChannel</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMComMModeSwitch -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:5c07eb69-9db2-4f5c-8e08-36329bcf4dc9">
																	<SHORT-NAME>BswMComMModeSwitch</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container includes all parameters related to a switch of communication mode for a ComM User. ComM_RequestComMode is called when this action is configured.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: BswMComMRequestedMode -->
																		<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:a8479736-a047-4a2e-a9de-9de9d1fe5e26">
																			<SHORT-NAME>BswMComMRequestedMode</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter specifies if the requested communication mode.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">This parameter corresponds to the parameter &quot;ComMode&quot; of the function ComM_RequestComMode.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<LITERALS>
																				<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:*************-9e0a-302a-67c0b9d5a6bc">
																					<SHORT-NAME>BSWM_FULL_COM</SHORT-NAME>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																				</ECUC-ENUMERATION-LITERAL-DEF>
																				<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:06fa79b9-8643-97c4-34d7-2b8f7a7ec203">
																					<SHORT-NAME>BSWM_NO_COM</SHORT-NAME>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																				</ECUC-ENUMERATION-LITERAL-DEF>
																			</LITERALS>
																		</ECUC-ENUMERATION-PARAM-DEF>
																	</PARAMETERS>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMComMUserRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:38ab9166-a9b6-4666-8064-b0fe9192d7af">
																			<SHORT-NAME>BswMComMUserRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to the ComM User that is associated to the Communication channel for which the communication mode should be requested.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">This reference corresponds to the parameter &quot;User&quot; of the function ComM_RequestComMode.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/ComM/ComMConfigSet/ComMUser</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMDeadlineMonitoringControl -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:22778ab5-dd84-4876-b8f8-3baced96702a">
																	<SHORT-NAME>BswMDeadlineMonitoringControl</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container includes all parameters related to enabling and disabling of deadline monitoring for one or several PDUs in COM.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">COM_ReceptionDMControl  is called when this action is configured.</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMDisabledDMPduGroupRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:858f665d-7fc6-49e4-a22a-b512c6e0b69b">
																			<SHORT-NAME>BswMDisabledDMPduGroupRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to a PDU Group for which the Deadline Monitoring should be disabled.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">Together with the BswMEnabledDMPduGroupRef this reference corresponds to the parameter &quot;ipduGroupVector&quot; of the function COM_ReceptionDMControl.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Com/ComConfig/ComIPduGroup</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																		<!-- Symbolic Name Reference Definition: BswMEnabledDMPduGroupRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:489675a3-4dfa-43b5-8f8e-48609a142a1d">
																			<SHORT-NAME>BswMEnabledDMPduGroupRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to a PDU Group for which the Deadline Monitoring should be enabled.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">Together with the BswMDisabledDMPduGroupRef this reference corresponds to the parameter &quot;ipduGroupVector&quot; of the function COM_ReceptionDMControl.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Com/ComConfig/ComIPduGroup</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMEcuMGoDown -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:88eb34f2-e877-4fd8-8ecb-b5f8ee97123d">
																	<SHORT-NAME>BswMEcuMGoDown</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container defines the UserId which shall be forwarded to the GoDown request.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMEcuMUserIdRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:5bf3393d-903b-435c-822a-e8df76b79337">
																			<SHORT-NAME>BswMEcuMUserIdRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to a EcuM UserId.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMFlexConfiguration/EcuMFlexUserConfig</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMEcuMSelectShutdownTarget -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:20f34f5e-3ff3-4e97-8120-ed86c17c2351">
																	<SHORT-NAME>BswMEcuMSelectShutdownTarget</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container defines the shutdown target.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMEcuMShutdownTargetRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:46e261aa-240e-40c3-8b60-5242f58edfb2">
																			<SHORT-NAME>BswMEcuMShutdownTargetRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to a shutdown target.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMSleepMode</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMLinScheduleSwitch -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:23b5f4bd-fb88-4b60-b666-96efbced6dcc">
																	<SHORT-NAME>BswMLinScheduleSwitch</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container includes all parameters related to a switch of LIN schedule table. LinSM_ScheduleRequest is called when this action is configured.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">The configuration for the &quot;network&quot; parameter can be accessed via the reference LinSMComMNetworkHandleRef contained in the parent container LinSMChannel of the container referenced by BswMLinScheduleRef.</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMLinScheduleRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:32b27d57-fa6d-4f23-a0c7-f872b0d789a0">
																			<SHORT-NAME>BswMLinScheduleRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to the LIN schedule table that the LIN SM shall change to.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">This reference corresponds to the parameter &quot;schedule&quot; of the function LinSM_ScheduleRequest.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/LinSM/LinSMConfigSet/LinSMChannel/LinSMSchedule</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMNMControl -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:d44540a9-aa53-4e68-a8b5-ea3a59dcff78">
																	<SHORT-NAME>BswMNMControl</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container includes all parameters related to enabling and disabling of Network Management communication. Disabling of NM communication can be requested by DCM.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">Nm_EnableCommunication or Nm_DisableCommunication is called when this action is configured.</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: BswMNMAction -->
																		<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:b0783d19-a6d0-4699-ac8c-a4049b4d461e">
																			<SHORT-NAME>BswMNMAction</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter specifies if the communication of the corresponding NM channel should be enabled or disabled.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<LITERALS>
																				<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:edf3ec60-f51a-8fdd-4049-f692e3aadfbf">
																					<SHORT-NAME>BSWM_NM_DISABLE</SHORT-NAME>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																				</ECUC-ENUMERATION-LITERAL-DEF>
																				<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:06556486-d5e7-9058-42e5-c7bffe14eab7">
																					<SHORT-NAME>BSWM_NM_ENABLE</SHORT-NAME>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																				</ECUC-ENUMERATION-LITERAL-DEF>
																			</LITERALS>
																		</ECUC-ENUMERATION-PARAM-DEF>
																	</PARAMETERS>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMComMNetworkHandleRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:71f03668-a386-4d49-b226-dd2c739ebc06">
																			<SHORT-NAME>BswMComMNetworkHandleRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This reference points to the unique channel defined by the ComMChannel and provides access to the unique channel index value in ComMChannelId.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">This reference corresponds to the parameter &quot;NetworkHandle&quot; of the function Nm_EnableCommunication and Nm_DisableCommunication.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/ComM/ComMConfigSet/ComMChannel</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMPduGroupSwitch -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:b7e1d5b8-cc18-4508-bbbf-9257454d5c80">
																	<SHORT-NAME>BswMPduGroupSwitch</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container includes references to the PDU groups that shall be enabled and disabled. Com_IpduGroupControl is called when this action is configured.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: BswMPduGroupSwitchReinit -->
																		<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:b6fb3fbb-cb5c-4d7a-9560-c5bc39a5da54">
																			<SHORT-NAME>BswMPduGroupSwitchReinit</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines if the values for the parameters like periodical timer, minimum delay timer etc is retainer or reinitialized during a PDU Group Switch.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">This parameter corresponds to the parameter &quot;initialize&quot; of the function Com_IpduGroupControl.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<DEFAULT-VALUE>false</DEFAULT-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																	</PARAMETERS>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMDisabledPduGroupRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:d63a77a6-da9d-467a-9c2f-6879f96a8b50">
																			<SHORT-NAME>BswMDisabledPduGroupRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to a PDU Group that should be disabled.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">Together with the BswMEnabledIPduGroupRef this reference corresponds to the parameter &quot;ipduGroupVector&quot; of the function Com_IpduGroupControl.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Com/ComConfig/ComIPduGroup</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																		<!-- Symbolic Name Reference Definition: BswMEnabledPduGroupRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:754e70f9-ccd0-4a90-8e06-58d065bb4184">
																			<SHORT-NAME>BswMEnabledPduGroupRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to a PDU Group that should be enabled.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">Together with the BswMDisabledIPduGroupRef this reference corresponds to the parameter &quot;ipduGroupVector&quot; of the function Com_IpduGroupControl.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Com/ComConfig/ComIPduGroup</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMPduRouterControl -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:40e0c9d2-b19e-4c8a-b501-1aac72a34ff5">
																	<SHORT-NAME>BswMPduRouterControl</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container includes all parameters related to enabling and disabling of routing of Routing Path Groups in the PDU Router. PduR_EnableRouting or PduR_DisableRouting is called when this action is configured.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: BswPduRouterAction -->
																		<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:7a7d7b39-fdff-4f16-9aa1-9ea5d6b3f02f">
																			<SHORT-NAME>BswPduRouterAction</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter specifies if the routing of the corresponding PDU should be enabled or disabled.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<LITERALS>
																				<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:1ff791d8-d8a6-9424-6c9d-a63aecb5c2df">
																					<SHORT-NAME>BSWM_PDUR_DISABLE</SHORT-NAME>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																				</ECUC-ENUMERATION-LITERAL-DEF>
																				<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:711758be-9312-8d26-607d-b527fccc5da3">
																					<SHORT-NAME>BSWM_PDUR_ENABLE</SHORT-NAME>
																					<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																				</ECUC-ENUMERATION-LITERAL-DEF>
																			</LITERALS>
																		</ECUC-ENUMERATION-PARAM-DEF>
																	</PARAMETERS>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMPduRoutingPathGroupRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:edbfb25c-322b-4d20-93f0-b1e32d0f50f5">
																			<SHORT-NAME>BswMPduRoutingPathGroupRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to the PDU Routing Path Group for which the routing in the PDU Router should be enabled or disabled.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">This reference corresponds to the parameter &quot;id&quot; of the function PduR_EnableRouting and PduR_DisableRouting.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/PduR/PduRRoutingTables/PduRRoutingPathGroup</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMRteSwitch -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:3d4d94e4-a15a-4442-88ce-2c30ccbffbee">
																	<SHORT-NAME>BswMRteSwitch</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container defines a mode switch indication that the BswM provides to the SW-C that need to be notified about the mode switch. RTE_Switch is called when this action is configured.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Foreign Reference Definition: BswMSwitchedMode -->
																		<ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:3d6f5054-ebed-40f3-9abc-b366bc6a8017">
																			<SHORT-NAME>BswMSwitchedMode</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter contains the integer value that corresponds to a certain mode in a Mode Declaration Group.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-TYPE>MODE-DECLARATION</DESTINATION-TYPE>
																		</ECUC-FOREIGN-REFERENCE-DEF>
																		<!-- Reference Definition: BswMRteSwitchPortRef -->
																		<ECUC-REFERENCE-DEF UUID="ECUC:4642d375-9c6d-4db5-84c5-fb8e6d59972c">
																			<SHORT-NAME>BswMRteSwitchPortRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to the BswMSwitchPort.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/BswM/BswMConfig/BswMModeControl/BswMSwitchPort</DESTINATION-REF>
																		</ECUC-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMSchMSwitch -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:07525aab-f7a7-4e5b-a6b0-762131ec234d">
																	<SHORT-NAME>BswMSchMSwitch</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container defines a mode switch indication that the BswM provides to the SW-C that need to be notified about the mode switch. SchM_Switch is called when this action is configured.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Foreign Reference Definition: BswMSchMSwitchedMode -->
																		<ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:8223503b-58a4-4608-9fe1-4317c3c27858">
																			<SHORT-NAME>BswMSchMSwitchedMode</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter contains the integer value that corresponds to a certain mode in a Mode Declaration Group.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-TYPE>MODE-DECLARATION</DESTINATION-TYPE>
																		</ECUC-FOREIGN-REFERENCE-DEF>
																		<!-- Reference Definition: BswMSchMSwitchPortRef -->
																		<ECUC-REFERENCE-DEF UUID="ECUC:e18f3107-9272-4ace-95ac-cfce6556be97">
																			<SHORT-NAME>BswMSchMSwitchPortRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to the BswMSwitchPort.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/BswM/BswMConfig/BswMModeControl/BswMSwitchPort</DESTINATION-REF>
																		</ECUC-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMSwitchIPduMode -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:239dfa77-aac8-4c83-a5e7-1be8c85669a5">
																	<SHORT-NAME>BswMSwitchIPduMode</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container includes all parameters related to the selection of the transmission mode an I-PDU to be sent by COM. Com_SwitchIpduTxMode is called when this action is configured.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: BswMSwitchIPduModeValue -->
																		<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:75026229-698c-4fbc-a905-6652e28355d6">
																			<SHORT-NAME>BswMSwitchIPduModeValue</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines which transmission mode shall be selected during this call.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">This parameter corresponds to the parameter &quot;truefalsemode&quot; of the function Com_SwitchIpduTxMode.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																		</ECUC-BOOLEAN-PARAM-DEF>
																	</PARAMETERS>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMSwitchIPduModeRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:2629fedf-9668-4d93-af1e-99e2346f915f">
																			<SHORT-NAME>BswMSwitchIPduModeRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to an I-PDU for which the transmission mode shall be set.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">This reference corresponds to the parameter &quot;PduId&quot; of the function Com_SwitchIpduTxMode.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Com/ComConfig/ComIPdu</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMTriggerIPduSend -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:5e313869-250f-44b6-964e-4dbda0dc8af8">
																	<SHORT-NAME>BswMTriggerIPduSend</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container includes all parameters related to the triggering of an I-PDU to be sent by COM. Com_TriggerIPDUSend is called when this action is configured.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<REFERENCES>
																		<!-- Symbolic Name Reference Definition: BswMTriggeredIPduRef -->
																		<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:bf813042-367e-403f-849e-eb3d8b1c950f">
																			<SHORT-NAME>BswMTriggeredIPduRef</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This is a reference to an I-PDU that should be triggered for transmission.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">This reference corresponds to the parameter &quot;PduId&quot; of the function Com_TriggerIPDUSend.</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Com/ComConfig/ComIPdu</DESTINATION-REF>
																		</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
																	</REFERENCES>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMTriggerSlaveRTEStop -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:c187c207-dff7-477d-b46f-1e0518c19801">
																	<SHORT-NAME>BswMTriggerSlaveRTEStop</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container includes all parameters needed to stop the RTE on a slave core. This choice shall only be chosen if multicore is used.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: BswMCoreId -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:8fd528a4-a7eb-8b28-53f8-45f371b4c7ea">
																			<SHORT-NAME>BswMCoreId</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines the identifier of the slave core that is used as input parameter for the BswM_TriggerStartUpPhase2 and BswM_TriggerSlaveRTEStop functions. The value of this parameter shall be synchronized with the OsApplicationCoreAssignment parameter.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<MAX>18446744073709551615</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMTriggerStartUpPhase2 -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:0438f6fb-7ba7-401e-9953-274c33f4c3c5">
																	<SHORT-NAME>BswMTriggerStartUpPhase2</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container includes all parameters needed to start phase two on a slave core. This choice shall only be chosen if multicore is used.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: BswMCoreId -->
																		<ECUC-INTEGER-PARAM-DEF UUID="ECUC:d2865d98-439b-83c9-38dc-4f3a8ce7f3ae">
																			<SHORT-NAME>BswMCoreId</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter defines the identifier of the slave core that is used as input parameter for the BswM_TriggerStartUpPhase2 and BswM_TriggerSlaveRTEStop functions. The value of this parameter shall be synchronized with the OsApplicationCoreAssignment parameter.</L-2>
																			</DESC>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<MAX>18446744073709551615</MAX>
																			<MIN>0</MIN>
																		</ECUC-INTEGER-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
																<!-- Container Definition: BswMUserCallout -->
																<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:1c998532-6f53-42ea-9dad-4825df61605a">
																	<SHORT-NAME>BswMUserCallout</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This container includes all details needed for a user defined function call.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
																	<PARAMETERS>
																		<!-- PARAMETER DEFINITION: BswMUserCalloutFunction -->
																		<ECUC-STRING-PARAM-DEF UUID="ECUC:40b9feb6-e998-4e06-a351-81f431b4850e">
																			<SHORT-NAME>BswMUserCalloutFunction</SHORT-NAME>
																			<DESC>
																				<L-2 L="EN">This parameter specifies the complete function call including all parameters. The parameters are specified during configuration time, and cannot be changed during run time. Any return values passed by the callout will be ignored.</L-2>
																			</DESC>
																			<INTRODUCTION>
																				<P>
																					<L-1 L="EN">Example usage can be:
                                                                        Actions to initialize other BSW modules
                                                                        Action to call Rte_Start()
                                                                        Action to call Rte_Stop()
                                                                        Action to call NvM_ReadAll()
                                                                        Action to call NvM_WriteAll()</L-1>
																				</P>
																			</INTRODUCTION>
																			<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																			<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																			<IMPLEMENTATION-CONFIG-CLASSES>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>LINK</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																				<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																					<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																					<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																				</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			</IMPLEMENTATION-CONFIG-CLASSES>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																			<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																			<ECUC-STRING-PARAM-DEF-VARIANTS>
																				<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
																			</ECUC-STRING-PARAM-DEF-VARIANTS>
																		</ECUC-STRING-PARAM-DEF>
																	</PARAMETERS>
																</ECUC-PARAM-CONF-CONTAINER-DEF>
															</CHOICES>
														</ECUC-CHOICE-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: BswMActionList -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:407b970e-46be-4d6b-b8ad-157663aa5d94">
													<SHORT-NAME>BswMActionList</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Each instance of this container defines an action list that is invoked based on the BswM Rules. An action list contains a list of numbered action items to be processed. An action list can also include other action lists.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: BswMActionListExecution -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:e6f03759-bac9-40cb-816c-4432a6d70644">
															<SHORT-NAME>BswMActionListExecution</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter controls if the corresponding action list shall be executed every time the rule is evaluated or only when the result of the evaluation changes.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:2d655647-7259-99c2-3b19-7e3f81d3dc32">
																	<SHORT-NAME>BSWM_CONDITION</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:f863a919-8799-9a03-5062-0b9c594b1590">
																	<SHORT-NAME>BSWM_TRIGGER</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
													</PARAMETERS>
													<SUB-CONTAINERS>
														<!-- Container Definition: BswMActionListItem -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:57ce84a2-0f20-4dd5-85a3-4fd44d75b91a">
															<SHORT-NAME>BswMActionListItem</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This container defines an item in an action list.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: BswMAbortOnFail -->
																<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:79c629de-d4aa-49d9-b686-9a6bd7347aec">
																	<SHORT-NAME>BswMAbortOnFail</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This parameter defines if the execution of the action list shall be aborted if this specific action returns E_NOT_OK. Note that this is only applicable for actions that have E_NOT_OK as a possible return value.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<DEFAULT-VALUE>false</DEFAULT-VALUE>
																</ECUC-BOOLEAN-PARAM-DEF>
																<!-- PARAMETER DEFINITION: BswMActionListItemIndex -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:d2944d1c-b5c4-4d6b-bc4c-ae6c1a89e946">
																	<SHORT-NAME>BswMActionListItemIndex</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">This parameter defines the index of the action in the action list. It is used define in which order the actions shall be performed.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>255</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
															</PARAMETERS>
															<REFERENCES>
																<!-- Choice Reference Definition: BswMActionListItemRef -->
																<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:756c431f-9e65-40e8-b3a3-47599991abcb">
																	<SHORT-NAME>BswMActionListItemRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">The action item can either be an atomic action or a reference to another action list or rule.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REFS>
																		<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/BswM/BswMConfig/BswMModeControl/BswMAction</DESTINATION-REF>
																		<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/BswM/BswMConfig/BswMModeControl/BswMActionList</DESTINATION-REF>
																		<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/BswM/BswMConfig/BswMArbitration/BswMRule</DESTINATION-REF>
																	</DESTINATION-REFS>
																</ECUC-CHOICE-REFERENCE-DEF>
																<!-- Symbolic Name Reference Definition: BswMReportFailToDemRef -->
																<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:5b95c4bb-7e3b-4788-bb34-eaf73a792725">
																	<SHORT-NAME>BswMReportFailToDemRef</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">If the reference is given, the DEM event shall be reported failed if this specific action returns E_NOT_OK; it shall be reported passed if this specific action returns E_OK.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
																</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
															</REFERENCES>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: BswMSwitchPort -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:f962bf44-5314-4e9c-894b-e8cf24f38351">
													<SHORT-NAME>BswMSwitchPort</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container specifies PPorts and/or providedModeDeclarationGroups, which the BswM has to create in its SWCD resp. BSWMD.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">If the container is referenced by one or more BswMRteSwitchActions the BswM shall create a corresponding PPort in its Service Description.

                                                If the container is referenced by a BswMSchMSwitch action the BswM shall create the corresponding ModeDeaclarationGroupPrototype as providedModeDeclarationGroup in it BSWMD.

                                                If the container is referenced by BswMSchMSwitch AND BswmRteSwitchActions the a providedModeDeclarationGroup as well as a PPort shall be created. In the corresponding SwcBswMapping a synchronizedModeGroup has to be created. See also chapter 4.4.7 in SWS_RTE.</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<REFERENCES>
														<!-- Foreign Reference Definition: BswMModeSwitchInterfaceRef -->
														<ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:868ddf33-9a9f-4e55-9dcb-598cd2bed53a">
															<SHORT-NAME>BswMModeSwitchInterfaceRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the ModeSwitchInterface of this BswMModeSwitchPort.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<DESTINATION-TYPE>MODE-SWITCH-INTERFACE</DESTINATION-TYPE>
														</ECUC-FOREIGN-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: BswMGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:fa61d6da-9eb0-40f5-91ad-14a6c6302f8f">
									<SHORT-NAME>BswMGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">General configuration parameters of the Basic SW Mode Manager.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: BswMCanSMEnabled -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:20b09b71-af6c-4742-bc19-d7be46ef2c56">
											<SHORT-NAME>BswMCanSMEnabled</SHORT-NAME>
											<DESC>
												<L-2 L="EN">enable/disable CanSM module related BswM API:</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: Enabled
                                        false: Disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: BswMComMEnabled -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:5cf2bda7-4d17-4ce1-82ce-f766c3b72a24">
											<SHORT-NAME>BswMComMEnabled</SHORT-NAME>
											<DESC>
												<L-2 L="EN">enable/disable ComM module related BswM API:</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: Enabled
                                        false: Disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: BswMDcmEnabled -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:541b52fa-ca18-4b78-80ce-d3465511157e">
											<SHORT-NAME>BswMDcmEnabled</SHORT-NAME>
											<DESC>
												<L-2 L="EN">enable/disable Dcm module related BswM API:</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: Enabled
                                        false: Disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: BswMDevErrorDetect -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:d8e85281-44c8-4346-9f78-2dc8123999ce">
											<SHORT-NAME>BswMDevErrorDetect</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Switches the Development Error Detection and Notification ON or OFF.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: Enabled
                                        false: Disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: BswMEcuMEnabled -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:4b8a67a5-6cb4-410f-a920-88f95b9b6135">
											<SHORT-NAME>BswMEcuMEnabled</SHORT-NAME>
											<DESC>
												<L-2 L="EN">enable/disable EcuM module related BswM API:</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: Enabled
                                        false: Disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: BswMEthSMEnabled -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:9c14cece-710b-491c-881c-3f26036d5934">
											<SHORT-NAME>BswMEthSMEnabled</SHORT-NAME>
											<DESC>
												<L-2 L="EN">enable/disable EthSM module related BswM API:</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: Enabled
                                        false: Disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: BswMFrSMEnabled -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:470ff123-9312-48f9-9437-fa492139535d">
											<SHORT-NAME>BswMFrSMEnabled</SHORT-NAME>
											<DESC>
												<L-2 L="EN">enable/disable FrSM module related BswM API:</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: Enabled
                                        false: Disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: BswMGenericRequestEnabled -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:9a887940-d041-456c-8f7b-1275123155f8">
											<SHORT-NAME>BswMGenericRequestEnabled</SHORT-NAME>
											<DESC>
												<L-2 L="EN">enable/disable Generic Request related BswM API:</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: Enabled
                                        false: Disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: BswMLinSMEnabled -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:bfacb96c-1654-4a15-8bf0-ab9450e57349">
											<SHORT-NAME>BswMLinSMEnabled</SHORT-NAME>
											<DESC>
												<L-2 L="EN">enable/disable LinSM module related BswM API:</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: Enabled
                                        false: Disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: BswMLinTPEnabled -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:2e5c6fe7-5142-49a1-aab3-f8f6d46572bb">
											<SHORT-NAME>BswMLinTPEnabled</SHORT-NAME>
											<DESC>
												<L-2 L="EN">enable/disable LinTP module related BswM API:</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: Enabled
                                        false: Disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: BswMMainFunctionPeriod -->
										<ECUC-FLOAT-PARAM-DEF UUID="ECUC:abcfbdc1-fe8b-436d-91b9-4ddc763395c4">
											<SHORT-NAME>BswMMainFunctionPeriod</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The cycle time of the periodic main function of BswM. Defined in seconds .</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>Inf</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: BswMNvMEnabled -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:036412df-b35c-43c0-b986-10d000024996">
											<SHORT-NAME>BswMNvMEnabled</SHORT-NAME>
											<DESC>
												<L-2 L="EN">enable/disable NvM module related BswM API:</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: Enabled
                                        false: Disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: BswMSchMEnabled -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:cc68af98-14aa-460e-a4cd-b26655bf0148">
											<SHORT-NAME>BswMSchMEnabled</SHORT-NAME>
											<DESC>
												<L-2 L="EN">enable/disable SchM module related BswM API:</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: Enabled
                                        false: Disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: BswMVersionInfoApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:dd2c4266-ade2-43ac-a7a3-adba2f1ca684">
											<SHORT-NAME>BswMVersionInfoApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Switches the possibility to read the version information with the service BswM_GetVersionInfo().</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: Enabled
                                        false: Disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: BswMWdgMEnabled -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:393d1df2-c7d9-4d1b-9b1f-37b58840b592">
											<SHORT-NAME>BswMWdgMEnabled</SHORT-NAME>
											<DESC>
												<L-2 L="EN">enable/disable WdgM module related BswM API:</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">true: Enabled
                                        false: Disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: BswMUserIncludeFiles -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:b1c3db6a-2121-452d-aaa1-51f63963bb03">
											<SHORT-NAME>BswMUserIncludeFiles</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Collection of header file names which shall be included by the BswM.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: BswMUserIncludeFile -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:2bfbd962-d945-4d4a-84c5-3fe3a3e06004">
													<SHORT-NAME>BswMUserIncludeFile</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Header file name which shall be included by the BswM.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">The value of this parameter shall be used as h-char-sequence or q-char-sequence according to ISO C90 section 6.10.2 &quot;source file inclusion&quot;.

                                                The parameter value MUST NOT represent a path, since ISO C90 does not specify how such a path is treated (i.e., this is implementation defined (and additionally depends on the operating system and the underlying file system)).</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
