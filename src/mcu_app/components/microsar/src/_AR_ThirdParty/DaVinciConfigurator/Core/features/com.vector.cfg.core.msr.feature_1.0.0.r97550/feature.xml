<?xml version="1.0" encoding="UTF-8"?>
<feature
      id="com.vector.cfg.core.msr.feature"
      label="DaVinci Cfg Core"
      version="1.0.0.r97550"
      provider-name="Vector Informatik GmbH">

   <description>
      DaVinci Configurator core for AUTOSAR classic platform.
   </description>

   <copyright>
      (c) Copyright Vector Informatik GmbH. All Rights Reserved.
   </copyright>

   <license url="http://www.vector.com">
      Vector Group License Agreement

This software is licensed under the terms and conditions of „Delivery and maintenance of software products” of Vector Group, see www.vector.com
   </license>

   <includes
         id="com.vector.cfg.core.feature"
         version="1.0.0.r97550"/>

   <includes
         id="com.vector.cfg.gen.core.feature"
         version="1.0.0.r97550"/>

   <includes
         id="com.vector.cfg.interop.feature"
         version="1.0.0.r97550"/>

   <plugin
         id="com.vector.cfg.datamining"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.installation.msr.win32"
         os="win32"
         ws="win32"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         fragment="true"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.business.usecases"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.business.defrefs"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.util.davincideveloper"
         os="win32"
         ws="win32"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.project.creation"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.core.license.dvcfg"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.project.creation.msr"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.persistency.dpa"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.persistency.addons.msr"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.util.platform.msr"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         fragment="true"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.project.creation.msr.groovy"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.installation.msr"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.business.vtt.msr"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.validation.basicrules.msr.impl"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.project.msr"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.persistency.addons.filesupervision.msr"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.project.operations.msr"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.persistency.addons.published"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.persistency.arxml.resources.msr"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.datamining.pai.if"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.datamining.pai.impl"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.consistency.contribution.msr.impl"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.consistency.core.msr.impl"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>
         
          <plugin
         id="com.vector.cfg.project.evs.if"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.project.evs.impl"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.project.evs.contribution.if"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.project.evs.pai.if"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

   <plugin
         id="com.vector.cfg.project.evs.pai.impl"
         download-size="0"
         install-size="0"
         version="1.0.0.97550"
         unpack="false"/>

</feature>
