<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: RamTst -->
						<ECUC-MODULE-DEF UUID="ECUC:c41e9d3e-2da8-4985-a0ad-283ad8661088">
							<SHORT-NAME>RamTst</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the RamTst module.</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.2.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
										<DATE>2011-11-09</DATE>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-LINK-TIME</SUPPORTED-CONFIG-VARIANT>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: RamTstCommon -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:8e3b9850-bbd7-415a-a5bb-e6e4962cc5da">
									<SHORT-NAME>RamTstCommon</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container holds a list of all available functions in the RamTst module.  Each function is turned ON or OFF before compiling so that only the desired functions and test algorithms are in the compiled code.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: RamTstAllowApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:16d97fa1-5ab2-41a9-af8c-3cda57200b97">
											<SHORT-NAME>RamTstAllowApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Preprocessor switch to disable / enable the function &quot;RamTst_Allow&quot;.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: RamTstChangeNumOfTestedCellsApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:67b4eef0-edc6-4494-9520-88892daafa0c">
											<SHORT-NAME>RamTstChangeNumOfTestedCellsApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Preprocessor switch to disable / enable the function &quot;RamTst_ChangeNumberOfTestedCells&quot;.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: RamTstDevErrorDetect -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:93c7f35b-d732-421e-b9fb-4b1dcb8d2b14">
											<SHORT-NAME>RamTstDevErrorDetect</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Preprocessor switch to select the development error tracer (DET)</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">ON or OFF</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: RamTstGetAlgParamsApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:121a2fc3-d8e6-487c-aec7-2de919158616">
											<SHORT-NAME>RamTstGetAlgParamsApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Preprocessor switch to disable / enable the function &quot;RamTst_GetAlgParams&quot;.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: RamTstGetExecutionStatusApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:ce59a5d7-75da-4e29-ae79-682dbf67224e">
											<SHORT-NAME>RamTstGetExecutionStatusApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Preprocessor switch to disable / enable the function</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">&quot;RamTst_GetExecutionStatus&quot;</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: RamTstGetNumberOfTestedCellsApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:2c64f7c8-4708-445b-b554-59cf1ed50d00">
											<SHORT-NAME>RamTstGetNumberOfTestedCellsApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Preprocessor switch to disable / enable the function &quot;RamTst_GetNumberOfTestedCells&quot;.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: RamTstGetTestAlgorithmApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:22e5162a-cb96-4587-ae82-d484e700063b">
											<SHORT-NAME>RamTstGetTestAlgorithmApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Preprocessor switch to disable / enable the function</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">&quot;RamTst_GetTestAlgorithm&quot;</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: RamTstGetTestResultApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:1cf74528-d6b7-427e-bdef-5e5bc20a272d">
											<SHORT-NAME>RamTstGetTestResultApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Preprocessor switch to disable / enable the function</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">&quot;RamTst_GetTestResult&quot;</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: RamTstGetTestResultPerBlockApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:d498d99f-687c-4c3e-aee3-7dcb64361ceb">
											<SHORT-NAME>RamTstGetTestResultPerBlockApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Preprocessor switch to disable / enable the function</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">&quot;RamTst_GetTestResultPerBlock&quot;</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: RamTstGetVersionInfoApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:2b74d931-78c0-46ec-9f62-34fa7cbdec9c">
											<SHORT-NAME>RamTstGetVersionInfoApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Preprocessor switch to disable / enable the function</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">&quot;RamTst_GetVersionInfo&quot;</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: RamTstResumeApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:53eb56f0-9d37-4896-a0a2-f8ed5386f467">
											<SHORT-NAME>RamTstResumeApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Preprocessor switch to disable / enable the function &quot;RamTst_Resume&quot;.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: RamTstRunFullTestApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:7ac6372f-f153-4355-82cc-681d17b7f103">
											<SHORT-NAME>RamTstRunFullTestApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Preprocessor switch to disable / enable the function</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">&quot;RamTst_RunFullTest&quot;</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: RamTstRunPartialTestApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:d16c5d80-6a90-45d6-85b1-e625962a5345">
											<SHORT-NAME>RamTstRunPartialTestApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Preprocessor switch to disable / enable the function</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">&quot;RamTst_RunPartialTest&quot;</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: RamTstSelectAlgParamsApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:bf32a854-b733-4de2-b7bc-4b4828533715">
											<SHORT-NAME>RamTstSelectAlgParamsApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Preprocessor switch to disable / enable the function &quot;RamTst_SelectAlgParams&quot;.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: RamTstStopApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:e8ffd34d-8e36-4485-b40c-985cff41cc2d">
											<SHORT-NAME>RamTstStopApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Preprocessor switch to disable / enable the function</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">&quot;RamTst_Stop&quot;</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: RamTstSuspendApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:7a003020-da8b-4bc7-8833-d53d520c82be">
											<SHORT-NAME>RamTstSuspendApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Preprocessor switch to disable / enable the function &quot;RamTst_Suspend&quot;.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: RamTstAlgorithms -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:62eb25f3-28ac-452e-a2fa-0f92357c16a6">
											<SHORT-NAME>RamTstAlgorithms</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container holds all of the available test algorithms for the specific microcontroller.  Each test algorithm is selected ON or OFF before compiling so that only the desired test algorithms are in the compiled code.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: RamTstAbrahamTestSelected -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:03482ab8-281d-4bfb-ae2d-fd63631d8108">
													<SHORT-NAME>RamTstAbrahamTestSelected</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Preprocessor switch to select the Abraham Test ON or OFF</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: RamTstCheckerboardTestSelected -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:0f5c4e78-46da-40cf-a1c7-11bce1d9263f">
													<SHORT-NAME>RamTstCheckerboardTestSelected</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Preprocessor switch to select the Checkerboard Test ON or OFF</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: RamTstGalpatTestSelected -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:090daeed-f256-425a-8e8f-be78873010f1">
													<SHORT-NAME>RamTstGalpatTestSelected</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Preprocessor switch to select the Galpat Test ON or OFF</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: RamTstMarchTestSelected -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:3582c79a-0327-4966-8752-343657127d47">
													<SHORT-NAME>RamTstMarchTestSelected</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Preprocessor switch to select the March Test ON or OFF</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: RamTstTranspGalpatTestSelected -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:94098234-bf83-41a3-84e4-f233699940b5">
													<SHORT-NAME>RamTstTranspGalpatTestSelected</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Preprocessor switch to select the Transparent Galpat Test ON or OFF</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<!-- PARAMETER DEFINITION: RamTstWalkPathTestSelected -->
												<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:f58faded-f154-4097-bfab-f56397d80138">
													<SHORT-NAME>RamTstWalkPathTestSelected</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Preprocessor switch to select the Walking Path Test ON or OFF</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: RamTstConfigParams -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:f21793ef-75e0-4393-b885-e5580bc5e76f">
											<SHORT-NAME>RamTstConfigParams</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container specifies configuration parameters which are set at pre-compile or link time.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: RamTstDefaultAlgParamsId -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:06024431-c473-4eca-9fc7-cb09b2c469a0">
													<SHORT-NAME>RamTstDefaultAlgParamsId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This is the identifier for the default &quot;RamTstAlgParams&quot; valid after the &quot;RamTst_Init(..)&quot; function. It is the initial value for a RAM variable which could be changed by the function &quot;RamTst_SelectAlgParams&quot;.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>1</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: RamTstMinNumberOfTestedCells -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:b5bda6a2-ef40-4cbd-b1b2-0798c4470c9b">
													<SHORT-NAME>RamTstMinNumberOfTestedCells</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Minimum number of tested cells for one cyle of a background test, as defined by implementer.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>4294967295</MAX>
													<MIN>1</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: RamTstNumberOfAlgParamSets -->
												<ECUC-INTEGER-PARAM-DEF UUID="ECUC:657cca5b-020d-4d2f-8f3c-72fb031dc992">
													<SHORT-NAME>RamTstNumberOfAlgParamSets</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Number of configured parameter sets for the available test algorithms.</L-2>
													</DESC>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">calculationFormula = count of the container RamTst_AlgParams</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>255</MAX>
													<MIN>1</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: RamTstTestCompletedNotification -->
												<ECUC-FUNCTION-NAME-DEF UUID="ECUC:c1358c78-a1a1-4f16-a6da-7ab1912eca98">
													<SHORT-NAME>RamTstTestCompletedNotification</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This function will be called from abackground test after finishing the RAM test without an error.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-FUNCTION-NAME-DEF-VARIANTS>
														<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
													</ECUC-FUNCTION-NAME-DEF-VARIANTS>
												</ECUC-FUNCTION-NAME-DEF>
												<!-- PARAMETER DEFINITION: RamTstTestErrorNotification -->
												<ECUC-FUNCTION-NAME-DEF UUID="ECUC:a1575dfa-04a7-496c-bb71-d5dbf722f0cc">
													<SHORT-NAME>RamTstTestErrorNotification</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This function will be called from a background test if an error occurs during the RAM test.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-FUNCTION-NAME-DEF-VARIANTS>
														<ECUC-FUNCTION-NAME-DEF-CONDITIONAL/>
													</ECUC-FUNCTION-NAME-DEF-VARIANTS>
												</ECUC-FUNCTION-NAME-DEF>
											</PARAMETERS>
											<SUB-CONTAINERS>
												<!-- Container Definition: RamTstAlgParams -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:72143fb4-1c83-44dc-8753-************">
													<SHORT-NAME>RamTstAlgParams</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container holds parameters for configuring an algorithm.  For each algorithm selected in the RamTst_Algorithms container there can be one or more RamTstAlgParams containers. The multiplicity of the included container RamTstBlockParams depends on the number of separate blocks of RAM which are defined for the particular test configuration.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: RamTstAlgParamsId -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:ee97557e-a517-4dc6-a63d-e47756d5572f">
															<SHORT-NAME>RamTstAlgParamsId</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This is the identifier by which this RamTstAlgParams set can be selected.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>255</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: RamTstAlgorithm -->
														<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:c7ffb64f-2076-45ab-83c3-8c16a01d9a62">
															<SHORT-NAME>RamTstAlgorithm</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This is the algorithm for which this RamTstAlgParams set is defined. Note that the same algorithm can be used in more than one RamTstAlgParams.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">Constraint: Only the algorithms selected by RamTstCommon/ RamTstAlgorithms can be used.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<LITERALS>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:89235eef-f97e-92cc-2242-250c14ff289b">
																	<SHORT-NAME>RAMTST_ABRAHAM_TEST</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:2faa6741-6e4d-9315-2ebb-983974b2121c">
																	<SHORT-NAME>RAMTST_CHECKERBOARD_TEST</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:90e9f327-35e1-8f1d-3930-313399247e6a">
																	<SHORT-NAME>RAMTST_GALPAT_TEST</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:06effc39-22d4-8744-462b-64cdcd759234">
																	<SHORT-NAME>RAMTST_MARCH_TEST</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:*************-8697-256f-b46a0d157a9a">
																	<SHORT-NAME>RAMTST_TRANSP_GALPAT_TEST</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
																<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:1e8394e9-183e-8eb3-3632-e880d508d12d">
																	<SHORT-NAME>RAMTST_WALK_PATH_TEST</SHORT-NAME>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																</ECUC-ENUMERATION-LITERAL-DEF>
															</LITERALS>
														</ECUC-ENUMERATION-PARAM-DEF>
														<!-- PARAMETER DEFINITION: RamTstExtNumberOfTestedCells -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:733eb7c6-c2b1-4d00-9a53-564d38e9565f">
															<SHORT-NAME>RamTstExtNumberOfTestedCells</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This is the absolute maximum value for the number of cells that NUMBER_OF_TESTED_CELLS and MAX_NUMBER_OF_TESTED_CELLS can be.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: RamTstMaxNumberOfTestedCells -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:ed2796ab-085b-470c-b351-095568a161f2">
															<SHORT-NAME>RamTstMaxNumberOfTestedCells</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This is the maximum value for the number of cells that can be tested in one cycle of a background test.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: RamTstNumberOfBlocks -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:2e0f071e-2902-4850-a181-728a84d916b6">
															<SHORT-NAME>RamTstNumberOfBlocks</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Number of RAM blocks configured using the container</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">&quot;RamTst_BlockParams&quot;
                                                        calculationFormula = Count of RamTstBlockParams contained in this RamTstAlgParams.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>65535</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: RamTstNumberOfTestedCells -->
														<ECUC-INTEGER-PARAM-DEF UUID="ECUC:8ba7b460-0266-4e5f-816d-0d5aa100dfa7">
															<SHORT-NAME>RamTstNumberOfTestedCells</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This is the initial value for a RAM variable, which can be changed by the function &quot;RamTst_ChangeNumberOfTestedCells&quot;</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>LINK</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<SUB-CONTAINERS>
														<!-- Container Definition: RamTstBlockParams -->
														<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:365453a2-a957-4210-899c-637b8bb698fe">
															<SHORT-NAME>RamTstBlockParams</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This container holds the description for one block of RAM.  For each RAM block to be tested by a given algorithm, there is one container which describes the block.  Multiple instances of this container are included in each container RamTst_AlgParams.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>65535</UPPER-MULTIPLICITY>
															<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
															<PARAMETERS>
																<!-- PARAMETER DEFINITION: RamTstBlockId -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:870cc0a7-22ab-4ec1-a6c3-bbdd1f48186a">
																	<SHORT-NAME>RamTstBlockId</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">ID of the RAM block</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>65535</MAX>
																	<MIN>1</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: RamTstEndAddress -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:db9b3de1-6fa0-4124-9b4c-54348cf8a124">
																	<SHORT-NAME>RamTstEndAddress</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">End Address of the RAM block.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">Constraint: It must be larger than the RamTstStartAddress.</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>4294967295</MAX>
																	<MIN>1</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: RamTstFillPattern -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:9cb6843f-a344-4e37-b442-d28f2b57f222">
																	<SHORT-NAME>RamTstFillPattern</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Pattern to be filled into each memory cell after destructive test of this block.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>4294967295</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: RamTstStartAddress -->
																<ECUC-INTEGER-PARAM-DEF UUID="ECUC:3cf53207-3a55-4307-b184-899a4d9e6060">
																	<SHORT-NAME>RamTstStartAddress</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Start Address of the RAM block.</L-2>
																	</DESC>
																	<INTRODUCTION>
																		<P>
																			<L-1 L="EN">Constraint: It must be smaller than the RamTstEndAddress.</L-1>
																		</P>
																	</INTRODUCTION>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<MAX>4294967295</MAX>
																	<MIN>0</MIN>
																</ECUC-INTEGER-PARAM-DEF>
																<!-- PARAMETER DEFINITION: RamTstTestPolicy -->
																<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:1ba15d70-cca5-4af0-8497-2f540a258f38">
																	<SHORT-NAME>RamTstTestPolicy</SHORT-NAME>
																	<DESC>
																		<L-2 L="EN">Policy regading destruction or non-destruction of memory content.</L-2>
																	</DESC>
																	<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
																	<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
																	<IMPLEMENTATION-CONFIG-CLASSES>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>LINK</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																		<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																			<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																			<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																		</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	</IMPLEMENTATION-CONFIG-CLASSES>
																	<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																	<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
																	<LITERALS>
																		<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:51907af5-f214-890b-0b70-8b6d737673ff">
																			<SHORT-NAME>RAMTEST_DESTRUCTIVE</SHORT-NAME>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																		</ECUC-ENUMERATION-LITERAL-DEF>
																		<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:da46063c-c134-8aa4-382d-4199364056f3">
																			<SHORT-NAME>RAMTEST_NON_DESTRUCTIVE</SHORT-NAME>
																			<ORIGIN>AUTOSAR_ECUC</ORIGIN>
																		</ECUC-ENUMERATION-LITERAL-DEF>
																	</LITERALS>
																</ECUC-ENUMERATION-PARAM-DEF>
															</PARAMETERS>
														</ECUC-PARAM-CONF-CONTAINER-DEF>
													</SUB-CONTAINERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: RamTstDemEventParameterRefs -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:837481e5-843f-49a5-bef6-fbe366860373">
									<SHORT-NAME>RamTstDemEventParameterRefs</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for the references to DemEventParameter elements which shall be invoked using the API Dem_ReportErrorStatus in case the corresponding error occurs. The EventId is taken from the referenced DemEventParameter&apos;s DemEventId value.</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">The standardized errors are provided in the container and can be extended by vendor-specific error references.</L-1>
										</P>
									</INTRODUCTION>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<REFERENCES>
										<!-- Symbolic Name Reference Definition: RAMTST_E_RAM_FAILURE -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:b688e59f-c065-4c8d-92f7-1dd4a961f00b">
											<SHORT-NAME>RAMTST_E_RAM_FAILURE</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to the DemEventParameter which shall be issued when the error &quot;RAM failure&quot; has occurred.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: RamTstPublishedInformation -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:ada3cd50-4795-4481-84dd-e5f0575be6c9">
									<SHORT-NAME>RamTstPublishedInformation</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container holding all RamTst specific published information parameter.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: RamTstCellSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:79ad475e-d255-4fb7-a3d8-4ae49f9106ef">
											<SHORT-NAME>RamTstCellSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Size of RAM cells (in bits) which can be tested individually by the given implementation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>64</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
