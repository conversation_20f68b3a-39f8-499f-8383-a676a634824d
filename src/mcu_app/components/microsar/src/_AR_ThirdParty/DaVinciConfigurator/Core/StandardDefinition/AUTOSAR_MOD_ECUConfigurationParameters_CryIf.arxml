<?xml version="1.0" encoding="UTF-8"?>
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://autosar.org/schema/r4.0">
	<AR-PACKAGES>
		<AR-PACKAGE UUID="4b4a8b92-464b-4bfd-b3f4-a30f04980120">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<AR-PACKAGE UUID="f65e9e5a-9bce-474f-981c-79940a8dda59">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<ECUC-MODULE-DEF UUID="5c356f6b-b245-448d-ab87-a187670dfc09">
							<SHORT-NAME>CryIf</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the Crypto Interface. </L-2>
							</DESC>
							<CATEGORY>STANDARDIZED_MODULE_DEFINITION</CATEGORY>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="91a69947-1657-4d8d-b5bd-16b642e663ba">
									<SHORT-NAME>CryIfChannel</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of CryIfChannel.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CryIfChannelId -->
										<ECUC-INTEGER-PARAM-DEF UUID="ce4895de-0771-4952-a43b-8f9e45ef12e9">
											<SHORT-NAME>CryIfChannelId</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Identifier of the crypto channel </L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Specifies to which crypto channel the CSM queue is connected to.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="6a9a7df6-6b56-4018-b1f4-783319d3b03c">
											<SHORT-NAME>CryIfDriverObjectRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter refers to a Crypto Driver Object.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Specifies to which Crypto Driver Object the crypto channel is connected to</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Crypto/CryptoDriverObjects/CryptoDriverObject</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CryIfGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="6b9a23ff-1bfa-417c-9a9d-0b1a5a821159">
									<SHORT-NAME>CryIfGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for common configuration options.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<ECUC-BOOLEAN-PARAM-DEF UUID="0130d24e-7814-434e-976c-89786e823298">
											<SHORT-NAME>CryIfDevErrorDetect</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Switches the development error detection and notification on or off. 
  true: detection and notification is enabled. 
  false: detection and notification is disabled. </L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">True: Development error detection enabled.
                                        False: Development error detection disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CryIfVersionInfoApi -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="b70b2cdc-5423-45ff-8609-387c8f5f9940">
											<SHORT-NAME>CryIfVersionInfoApi</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Pre-processor switch to enable and disable availability of the API CryIf_GetVersionInfo().</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">True: API CryIf_GetVersionInfo() is available
                                        False: API CryIf_GetVersionInfo() is not available.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CryIfKey -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="f8a4b7b7-b5cd-467e-8146-2767bbc8674a">
									<SHORT-NAME>CryIfKey</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for Channel configuration </L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<ECUC-INTEGER-PARAM-DEF UUID="290b1e96-3cdf-4301-8ecf-08eff0297f1f">
											<SHORT-NAME>CryIfKeyId</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Identifier of the key. </L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Specifies to which CryIf key the CSM key is mapped to.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="8587f0ef-1152-40e2-b7b0-e404af0329d9">
											<SHORT-NAME>CryIfKeyRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter refers to the crypto driver key. </L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Specifies to which crypto driver key the CryIf key is mapped to.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Crypto/CryptoKeys/CryptoKey</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
