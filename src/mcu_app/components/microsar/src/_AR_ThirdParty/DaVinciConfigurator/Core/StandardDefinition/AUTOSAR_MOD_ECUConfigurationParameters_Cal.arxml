<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by <PERSON><PERSON> Employee (Vector Informatik GmbH) -->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd">
	<!-- AUTOSAR ECU Configuration Parameter Definition -->
	<!--
    Document Owner: AUTOSAR
    Document Responsibility: AUTOSAR
    Document Identification No: 289
    Document Classification: Standard
    Document Version: 4.2.0
    Document Status: Final
    Part of Release: 4.0 (2011-11-09)
    Revision: 3
    -->
	<!-- Generated on Thu Nov 10 11:36:22 CET 2011 -->
	<!-- MMT:        2.7.5 -->
	<!-- Meta-Model: https://svn3.autosar.org/repos2/work/24_Sources/branches/R4.0/MMOD_MetaModel_059/master/AUTOSAR_MetaModel_Master.EAP@105697 -->
	<!--
    Disclaimer

    This specification and the material contained in it, as released by AUTOSAR, 
    is for the purpose of information only. AUTOSAR and the companies that have 
    contributed to it shall not be liable for any use of the specification.

    The material contained in this specification is protected by copyright and 
    other types of Intellectual Property Rights. The commercial exploitation of 
    the material contained in this specification requires a license to such 
    Intellectual Property Rights.

    This specification may be utilized or reproduced without any modification, in 
    any form or by any means, for informational purposes only.
    For any other purpose, no part of the specification may be utilized or 
    reproduced, in any form or by any means, without permission in writing from 
    the publisher.

    The AUTOSAR specifications have been developed for automotive applications 
    only. They have neither been developed, nor tested for non-automotive 
    applications.

    The word AUTOSAR and the AUTOSAR logo are registered trademarks.



    Advice for users

    AUTOSAR specifications may contain exemplary items (exemplary reference models,
    "use cases", and/or references to exemplary technical solutions, devices, 
    processes or software).

    Any such exemplary items are contained in the specifications for illustration 
    purposes only, and they themselves are not part of the AUTOSAR Standard. 
    Neither their presence in such specifications, nor any later documentation of 
    AUTOSAR conformance of products actually implementing such exemplary items, 
    imply that intellectual property rights covering such exemplary items are 
    licensed under the same rules as applicable to the AUTOSAR Standard.

    -->
	<ADMIN-DATA>
		<DOC-REVISIONS>
			<DOC-REVISION>
				<REVISION-LABEL>4.2.0</REVISION-LABEL>
				<ISSUED-BY>AUTOSAR</ISSUED-BY>
				<DATE>2011-11-09</DATE>
			</DOC-REVISION>
		</DOC-REVISIONS>
	</ADMIN-DATA>
	<AR-PACKAGES>
		<!-- AR-Package: AUTOSAR -->
		<AR-PACKAGE UUID="ECUC:AUTOSAR">
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<!-- AR-Package: AUTOSAR -->
				<AR-PACKAGE UUID="ECUC:ECUCDEFS">
					<SHORT-NAME>EcucDefs</SHORT-NAME>
					<ELEMENTS>
						<!-- Module Definition: Cal -->
						<ECUC-MODULE-DEF UUID="ECUC:7d9a2d7e-881c-40f3-aa63-27da2ea178ed">
							<SHORT-NAME>Cal</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the Cal (CryptoAbstractionLibrary) module.</L-2>
							</DESC>
							<ADMIN-DATA>
								<DOC-REVISIONS>
									<DOC-REVISION>
										<REVISION-LABEL>4.2.0</REVISION-LABEL>
										<ISSUED-BY>AUTOSAR</ISSUED-BY>
										<DATE>2011-11-09</DATE>
									</DOC-REVISION>
								</DOC-REVISIONS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<SUPPORTED-CONFIG-VARIANTS>
								<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
							</SUPPORTED-CONFIG-VARIANTS>
							<CONTAINERS>
								<!-- Container Definition: CalAsymDecrypt -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:c2b02512-1d6f-45ea-b7ad-768fcf8d52f5">
									<SHORT-NAME>CalAsymDecrypt</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of AsymDecrypt primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalAsymDecryptMaxCtxBufByteSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:45ad4d7f-37a2-463c-9059-bb94a312631f">
											<SHORT-NAME>CalAsymDecryptMaxCtxBufByteSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all context buffers used in all CPL primitives which implement an asymmetrical decryption.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CalAsymDecryptMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:37f98b68-c1e5-4c64-a1dd-cbdf20e4921c">
											<SHORT-NAME>CalAsymDecryptMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CPL primitives which implement an asymmetrical decryption.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalAsymDecryptConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:99a7c886-305c-4c91-a2b3-2809b1e11b0f">
											<SHORT-NAME>CalAsymDecryptConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service AsymDecrypt. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalAsymDecryptInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:606bede9-3ac0-46ba-ad54-6a592891a23f">
													<SHORT-NAME>CalAsymDecryptInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalAsymDecryptPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:c1ac42e3-ded0-443c-97a4-f85c1056e8fe">
													<SHORT-NAME>CalAsymDecryptPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalAsymEncrypt -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:6954f702-9bfb-440a-88c5-206dc0e4ad88">
									<SHORT-NAME>CalAsymEncrypt</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of AsymEncrypt primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalAsymEncryptMaxCtxBufByteSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:62990ba6-a5cf-4c9a-8dfe-502071854dd9">
											<SHORT-NAME>CalAsymEncryptMaxCtxBufByteSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all context buffers used in all CPL primitives which implement an asymmetrical encryption.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CalAsymEncryptMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:b76c9d68-2e6b-4d9f-bbbe-83a60a6a33c2">
											<SHORT-NAME>CalAsymEncryptMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CPL primitives which implement an asymmetrical encryption.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalAsymEncryptConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:d591c6b1-f35b-4e4f-8f35-78e78337ddc8">
											<SHORT-NAME>CalAsymEncryptConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service AsymEncrypt. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalAsymEncryptInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:8af3dcaa-3c53-4aba-b232-688e52884686">
													<SHORT-NAME>CalAsymEncryptInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalAsymEncryptPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:f04729b5-91f1-4f49-b731-c38e7dc94c24">
													<SHORT-NAME>CalAsymEncryptPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalAsymPrivateKeyExtract -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:5b3aa5d9-f98e-4d0e-bc37-c5f92692f57d">
									<SHORT-NAME>CalAsymPrivateKeyExtract</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of AsymPrivateKeyExtract primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalAsymPrivateKeyExtractMaxCtxBufByteSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:f6cf1905-541b-4174-841a-918e922b90fa">
											<SHORT-NAME>CalAsymPrivateKeyExtractMaxCtxBufByteSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all context buffers used in all CPL primitives which implement an asymmetrical private key extraction.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CalAsymPrivateKeyExtractMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:a143f0b9-858f-4572-bf2d-5a302030e55d">
											<SHORT-NAME>CalAsymPrivateKeyExtractMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CPL primitives which implement an asymmetrical private key extraction.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalAsymPrivateKeyExtractConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:16e8d036-8426-4c55-8b8b-4b37511af318">
											<SHORT-NAME>CalAsymPrivateKeyExtractConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service AsymPrivateKeyExtract.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The container name serves as a symbolic name for the identifier of a service configuration.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalAsymPrivateKeyExtractInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:51d30aa7-82c1-4fe6-a67c-e5ed2529aacc">
													<SHORT-NAME>CalAsymPrivateKeyExtractInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalAsymPrivateKeyExtractPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:9380adbb-5da7-4f62-806e-3bb05608aa91">
													<SHORT-NAME>CalAsymPrivateKeyExtractPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalAsymPrivateKeyWrapAsym -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:16b1dc6b-a36e-449c-80dd-42b1849f7d26">
									<SHORT-NAME>CalAsymPrivateKeyWrapAsym</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of AsymPrivateKeyWrapAsym primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalAsymPrivateKeyWrapAsymMaxPrivKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:731f5d40-bd32-42c9-a424-e1ebfa814a99">
											<SHORT-NAME>CalAsymPrivateKeyWrapAsymMaxPrivKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum length, in bytes, of all private information types used in all CPL primitives which implement an asymmetrical key wrapping.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CalAsymPrivateKeyWrapAsymMaxPubKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:9995d79e-d314-4c42-bd2e-faae5d1e332b">
											<SHORT-NAME>CalAsymPrivateKeyWrapAsymMaxPubKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum length, in bytes, of all public key types used in all CPL primitives which implement an asymmetrical key wrapping.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalAsymPrivateKeyWrapAsymConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:f62dd31b-d9cd-420f-a2f0-030fd25de322">
											<SHORT-NAME>CalAsymPrivateKeyWrapAsymConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service AsymPrivateKeyWrapAsym.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The container name serves as a symbolic name for the identifier of a service configuration.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalAsymPrivateKeyWrapAsymInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:2358ab88-4336-40c3-879e-4283e5e86e7d">
													<SHORT-NAME>CalAsymPrivateKeyWrapAsymInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalAsymPrivateKeyWrapAsymPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:db5be4a1-bfdc-43ed-924e-c6f5d4bc7cce">
													<SHORT-NAME>CalAsymPrivateKeyWrapAsymPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalAsymPrivateKeyWrapSym -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:0318a2c1-136c-44e3-bff5-5594777d79b8">
									<SHORT-NAME>CalAsymPrivateKeyWrapSym</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of AsymPrivateKeyWrapSym primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalAsymPrivateKeyWrapSymMaxPrivKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:46859cd1-3c15-471b-90b5-b73dc47e103f">
											<SHORT-NAME>CalAsymPrivateKeyWrapSymMaxPrivKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum length, in bytes, of all private information types used in all CPL primitives which implement an asymmetrical key wrapping.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CalAsymPrivateKeyWrapSymMaxSymKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:07b7d500-164d-4248-9e7e-edb9948c45ab">
											<SHORT-NAME>CalAsymPrivateKeyWrapSymMaxSymKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CPL primitives which implement an asymmetrical key wrapping.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalAsymPrivateKeyWrapSymConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:141dc2be-aa87-48f1-9d9d-a84104b9dcb5">
											<SHORT-NAME>CalAsymPrivateKeyWrapSymConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service AsymPrivateKeyWrapSym.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The container name serves as a symbolic name for the identifier of a service configuration.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalAsymPrivateKeyWrapSymInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:93df8c89-92b7-433a-983d-b69f175725b8">
													<SHORT-NAME>CalAsymPrivateKeyWrapSymInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalAsymPrivateKeyWrapSymPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:8e62c3ed-c8af-4ab0-88c8-c5432d054cc0">
													<SHORT-NAME>CalAsymPrivateKeyWrapSymPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalAsymPublicKeyExtract -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:ef7bcb3c-d009-4fb0-bf83-9e85b44d69d1">
									<SHORT-NAME>CalAsymPublicKeyExtract</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of AsymPublicKeyExtract primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalAsymPublicKeyExtractMaxCtxBufByteSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:a6658840-f5d9-4fcc-b794-38bc510e163c">
											<SHORT-NAME>CalAsymPublicKeyExtractMaxCtxBufByteSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all context buffers used in all CPL primitives which implement an asymmetrical public key extraction.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CalAsymPublicKeyExtractMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:42bc63da-a495-4225-955b-df4cef36d944">
											<SHORT-NAME>CalAsymPublicKeyExtractMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CPL primitives which implement an asymmetrical public key extraction.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalAsymPublicKeyExtractConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:86c587fa-6f48-4a6e-88fb-10c1d3004813">
											<SHORT-NAME>CalAsymPublicKeyExtractConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service AsymPublicKeyExtract.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The container name serves as a symbolic name for the identifier of a service configuration.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalAsymPublicKeyExtractInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:8e447d12-d852-4079-9463-50d421db4f46">
													<SHORT-NAME>CalAsymPublicKeyExtractInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalAsymPublicKeyExtractPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:69167f65-fab4-4252-9f3b-bbbca9e55543">
													<SHORT-NAME>CalAsymPublicKeyExtractPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalChecksum -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:b8d7b754-116e-4061-bbd5-f1584bf62d30">
									<SHORT-NAME>CalChecksum</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of Checksum primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalChecksumMaxCtxBufByteSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:cdf72f4b-e20e-44ca-ad1d-ec93104d1ae5">
											<SHORT-NAME>CalChecksumMaxCtxBufByteSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all context buffers used in all CPL primitives which implement a checksum computation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalChecksumConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:0c67f22b-47c0-4f61-846e-034d26e89538">
											<SHORT-NAME>CalChecksumConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service Checksum. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalChecksumInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:eede1f61-32c3-48e0-b59a-78e21dbcd3b6">
													<SHORT-NAME>CalChecksumInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalChecksumPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:0c102bf1-a9c2-496a-8f51-c6dadd85fa50">
													<SHORT-NAME>CalChecksumPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:7d813e6d-d38d-48d4-bfff-a46f8ec6f674">
									<SHORT-NAME>CalGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for common configuration options.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalMaxAlignScalarType -->
										<ECUC-STRING-PARAM-DEF UUID="ECUC:1ca75bb2-f39f-4655-97e9-10f2b129ea52">
											<SHORT-NAME>CalMaxAlignScalarType</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The scalar type which has the maximum alignment restrictions on the given platform.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">This type can be e.g. uint8, uint16 or uint32.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-STRING-PARAM-DEF-VARIANTS>
												<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
											</ECUC-STRING-PARAM-DEF-VARIANTS>
										</ECUC-STRING-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalHash -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:983a4ad8-fe68-429b-8127-2d43c16b729a">
									<SHORT-NAME>CalHash</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of Hash primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalHashMaxCtxBufByteSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:eda55c7c-5886-4134-aba2-4009777fe1e6">
											<SHORT-NAME>CalHashMaxCtxBufByteSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all context buffers used in all CPL primitives which implement a hash computation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalHashConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:afb56866-a648-48f6-b0bd-a6aa913291fc">
											<SHORT-NAME>CalHashConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configurations for the Hash service. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalHashInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:a506efe9-dc26-45e1-80bc-331dda93fa54">
													<SHORT-NAME>CalHashInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalHashPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:d7df6a25-8d30-4b48-894d-9656b5069286">
													<SHORT-NAME>CalHashPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalKeyDerive -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:1cb9ff39-036d-4c46-b893-27ae38c17605">
									<SHORT-NAME>CalKeyDerive</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of KeyDerive primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalKeyDeriveMaxCtxBufByteSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:8711c8bc-3725-430f-a77f-5032e64fc4c6">
											<SHORT-NAME>CalKeyDeriveMaxCtxBufByteSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all context buffers used in all CPL primitives which implement a key derivation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CalKeyDeriveMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:95db1898-c86b-4a72-b725-a59364466ddc">
											<SHORT-NAME>CalKeyDeriveMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CRL primitives which implement a key derivation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalKeyDeriveConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:c8ca5182-81f1-4ccd-85fb-2ac691f4f63e">
											<SHORT-NAME>CalKeyDeriveConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service KeyDerive. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalKeyDeriveInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:060790a3-40f7-4417-9b92-dde48d01fc13">
													<SHORT-NAME>CalKeyDeriveInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalKeyDerivePrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:974fe581-b127-46bc-9948-c9aa70ad000e">
													<SHORT-NAME>CalKeyDerivePrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalKeyExchangeCalcPubVal -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:54f59a16-893f-49ea-94fe-c0d105975e3c">
									<SHORT-NAME>CalKeyExchangeCalcPubVal</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of KeyExchangeCalcPubVal primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalKeyExchangeCalcPubValMaxBaseTypeSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:d2abb09c-c27e-4e44-a3b3-14cb736f3db9">
											<SHORT-NAME>CalKeyExchangeCalcPubValMaxBaseTypeSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum length, in bytes, of all base types used in all CPL primitives which implement a public value calculation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CalKeyExchangeCalcPubValMaxCtxBufByteSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:09388930-896a-40d7-95f5-3f48731a6cd6">
											<SHORT-NAME>CalKeyExchangeCalcPubValMaxCtxBufByteSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all context buffers used in all CPL primitives which implement a public value calculation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CalKeyExchangeCalcPubValMaxPrivateTypeSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:342d08b4-37f6-403a-93a1-445ae3c2a31d">
											<SHORT-NAME>CalKeyExchangeCalcPubValMaxPrivateTypeSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum length, in bytes, of all private information types used in all CPL primitives which implement a public value calculation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalKeyExchangeCalcPubValConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:6047f1e3-150c-4062-9de5-83009e98ed29">
											<SHORT-NAME>CalKeyExchangeCalcPubValConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service KeyExchangeCalcPubVal. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalKeyExchangeCalcPubValInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:c9fea318-8a6d-4693-a210-ee67f39d8b83">
													<SHORT-NAME>CalKeyExchangeCalcPubValInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalKeyExchangeCalcPubValPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:9998d915-e42e-4458-a051-82fcc6179f74">
													<SHORT-NAME>CalKeyExchangeCalcPubValPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalKeyExchangeCalcSecret -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:0bbf5f7e-f757-48ab-990b-b49b938aa477">
									<SHORT-NAME>CalKeyExchangeCalcSecret</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of KeyExchangeCalcSecret primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalKeyExchangeCalcSecretMaxBaseTypeSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:9202d352-3dd7-478b-a9f1-0a7331eda4d2">
											<SHORT-NAME>CalKeyExchangeCalcSecretMaxBaseTypeSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum length, in bytes, of all base types used in all CPL primitives which implement a shared secret calculation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CalKeyExchangeCalcSecretMaxCtxBufByteSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:5299e2f5-c14f-4acf-97e9-58fe7847a70f">
											<SHORT-NAME>CalKeyExchangeCalcSecretMaxCtxBufByteSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all context buffers used in all CPL primitives which implement a shared secret calculation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CalKeyExchangeCalcSecretMaxPrivateTypeSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:961c6c12-62ed-4535-bda4-7c915ff4befa">
											<SHORT-NAME>CalKeyExchangeCalcSecretMaxPrivateTypeSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum length, in bytes, of all private information types used in all CPL primitives which implement a shared secret calculation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalKeyExchangeCalcSecretConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:505681d9-7848-4817-80af-ae6cb5fe77d1">
											<SHORT-NAME>CalKeyExchangeCalcSecretConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service KeyExchangeCalcSecret. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalKeyExchangeCalcSecretInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:1ace766d-4d32-401c-b0f6-20f707f099cf">
													<SHORT-NAME>CalKeyExchangeCalcSecretInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalKeyExchangeCalcSecretPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:ae74d778-36b0-45ed-a5bd-6c9e3ebd669e">
													<SHORT-NAME>CalKeyExchangeCalcSecretPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalMacGenerate -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:d88ab491-979c-4e5f-b986-41b9492056d9">
									<SHORT-NAME>CalMacGenerate</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of MacGenerate primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalMacGenerateMaxCtxBufByteSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:ab6c8df7-5d9b-4774-9fbd-3d129c1a9722">
											<SHORT-NAME>CalMacGenerateMaxCtxBufByteSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all context buffers used in all CPL primitives which implement a MAC generation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CalMacGenerateMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:0a380b91-ee5e-4e17-9e18-34d356fdc74d">
											<SHORT-NAME>CalMacGenerateMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CPL primitives which implement a MAC generation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalMacGenerateConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:676e518e-d86e-423e-a684-c6092b48bd23">
											<SHORT-NAME>CalMacGenerateConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configurations for the MacGenerate service. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalMacGenerateInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:56919315-3e9b-4690-b498-816f55155234">
													<SHORT-NAME>CalMacGenerateInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalMacGeneratePrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:63570cda-923b-40a7-80af-ae1603d2caf6">
													<SHORT-NAME>CalMacGeneratePrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalMacVerify -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:9afc6b54-3bc5-49a6-ad92-3354c2a9e09a">
									<SHORT-NAME>CalMacVerify</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of MacVerify primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalMacVerifyMaxCtxBufByteSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:1b823ea2-7e54-404f-add3-e42389e881a4">
											<SHORT-NAME>CalMacVerifyMaxCtxBufByteSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all context buffers used in all CPL primitives which implement a MAC verification.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CalMacVerifyMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:429c9ad2-bafe-49a8-8899-3d75f0fa8517">
											<SHORT-NAME>CalMacVerifyMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CPL primitives which implement a MAC verification.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalMacVerifyConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:04c7c28a-81e5-4a06-97f4-a44aef32279d">
											<SHORT-NAME>CalMacVerifyConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service MacVerify. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalMacVerifyInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:0db6135a-2ecd-414c-af07-bcff544557b2">
													<SHORT-NAME>CalMacVerifyInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalMacVerifyPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:8849e93f-e34c-407c-bcbf-d475291d2981">
													<SHORT-NAME>CalMacVerifyPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalRandomGenerate -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:15c9bbec-fd88-4744-8563-e8b245477cae">
									<SHORT-NAME>CalRandomGenerate</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of RandomGenerate primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalRandomGenerateConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:3be06fd3-a46d-42a6-a6e7-83c3da56547e">
											<SHORT-NAME>CalRandomGenerateConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service RandomGenerate. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalRandomGenerateInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:d31994d7-dc24-4be8-b079-cd7a3f62b41a">
													<SHORT-NAME>CalRandomGenerateInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalRandomGeneratePrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:703f0519-3619-4715-a475-01f404a460bd">
													<SHORT-NAME>CalRandomGeneratePrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalRandomSeed -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:f331107f-2173-4e57-9317-78f8eeda1df6">
									<SHORT-NAME>CalRandomSeed</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of RandomSeed primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalRandomMaxCtxBufByteSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:ef467bd8-c0f7-4713-a091-e7b849962b87">
											<SHORT-NAME>CalRandomMaxCtxBufByteSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all context buffers used in all CPL primitives which implement seeding or generating a random number.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalRandomSeedConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:32d0487f-dba6-436d-931b-cc3009ac07d3">
											<SHORT-NAME>CalRandomSeedConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service RandomSeed. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalRandomSeedInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:4d6f2112-9185-4cdc-b3c3-ea305191f3cd">
													<SHORT-NAME>CalRandomSeedInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalRandomSeedPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:a26b0cd5-3f4f-44f8-ab1c-696fbcfb7e6e">
													<SHORT-NAME>CalRandomSeedPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalSignatureGenerate -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:c9f667d5-c938-4e16-a5cf-dc0a6557457c">
									<SHORT-NAME>CalSignatureGenerate</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of SignatureGenerate primitives</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalSignatureGenerateMaxCtxBufByteSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:32c828cf-1ebd-4914-86cf-73da9f17d4f1">
											<SHORT-NAME>CalSignatureGenerateMaxCtxBufByteSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all context buffers used in all CPL primitives which implement a signature generation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CalSignatureGenerateMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:15af97e0-1d91-41dc-9dd9-a7f73fc54fce">
											<SHORT-NAME>CalSignatureGenerateMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CPL primitives which implement a signature generation.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalSignatureGenerateConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:fba64fb0-3d09-44f0-8c89-27a3ba3783e1">
											<SHORT-NAME>CalSignatureGenerateConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service SignatureGenerate. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalSignatureGenerateInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:190fbdd7-2112-4a48-b241-1fcc2991bb59">
													<SHORT-NAME>CalSignatureGenerateInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalSignatureGeneratePrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:dde18f27-1038-41a8-8605-0ad51aa1ebbe">
													<SHORT-NAME>CalSignatureGeneratePrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalSignatureVerify -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:0896c97f-29da-4728-b518-a9fb32d757ef">
									<SHORT-NAME>CalSignatureVerify</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of SignatureVerify primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalSignatureVerifyMaxCtxBufByteSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:446c212f-3cce-411b-8b5c-44b7f3727279">
											<SHORT-NAME>CalSignatureVerifyMaxCtxBufByteSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all context buffers used in all CPL primitives which implement a signature verification.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CalSignatureVerifyMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:6f4d8a51-cab8-44d0-9952-7a6e2c55caad">
											<SHORT-NAME>CalSignatureVerifyMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CPL primitives which implement a signature verification.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalSignatureVerifyConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:4ddb2ff1-9ab7-4857-b942-3902c2b3a8c3">
											<SHORT-NAME>CalSignatureVerifyConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service SignatureVerify. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalSignatureVerifyInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:31b6c628-78c8-421b-8f71-6e7ec94970d5">
													<SHORT-NAME>CalSignatureVerifyInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalSignatureVerifyPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:d5572bd8-c4e1-4579-940b-58ab550d2e3e">
													<SHORT-NAME>CalSignatureVerifyPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalSymBlockDecrypt -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:30c15161-f8c6-4817-9a4c-c6e84bf0cfe1">
									<SHORT-NAME>CalSymBlockDecrypt</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of SymBlockDecrypt primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalSymBlockDecryptMaxCtxBufByteSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:98c3d5ee-bde9-4cb5-9f1d-e95698b418db">
											<SHORT-NAME>CalSymBlockDecryptMaxCtxBufByteSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all context buffers used in all CPL primitives which implement a symmetrical block decryption.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CalSymBlockDecryptMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:dd0f7b4d-52ea-47b4-8c6e-ac73c1811773">
											<SHORT-NAME>CalSymBlockDecryptMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CPL primitives which implement a symmetrical block decryption.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalSymBlockDecryptConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:27c2233c-bd85-4b46-82e7-481d8f9446c8">
											<SHORT-NAME>CalSymBlockDecryptConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service SymBlockDecrypt. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalSymBlockDecryptInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:5df8bfb0-4d7b-4b48-af49-311ce14c21b4">
													<SHORT-NAME>CalSymBlockDecryptInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalSymBlockDecryptPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:aa7bc88e-e328-4ecd-a1f5-ca1235e2d486">
													<SHORT-NAME>CalSymBlockDecryptPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalSymBlockEncrypt -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:ee9537c9-2dd9-46e4-a7b6-6226c2a490aa">
									<SHORT-NAME>CalSymBlockEncrypt</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of SymBlockEncrypt primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalSymBlockEncryptMaxCtxBufByteSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:c9671303-95a0-4ccd-9bdf-3430ecd33748">
											<SHORT-NAME>CalSymBlockEncryptMaxCtxBufByteSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all context buffers used in all CPL primitives which implement a symmetrical block encryption.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CalSymBlockEncryptMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:6cead96f-93a9-439a-b30a-30941797d7d8">
											<SHORT-NAME>CalSymBlockEncryptMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CPL primitives which implement a symmetrical block encryption.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalSymBlockEncryptConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:61f2c3e0-c9d1-401e-b5bc-885a4299b333">
											<SHORT-NAME>CalSymBlockEncryptConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service SymBlockEncrypt. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalSymBlockEncryptInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:3909228c-b3e8-40fd-9071-c0589a663275">
													<SHORT-NAME>CalSymBlockEncryptInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalSymBlockEncryptPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:a909e448-87be-4f4c-80a6-0404c0c745f1">
													<SHORT-NAME>CalSymBlockEncryptPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalSymDecrypt -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:de88d16a-4393-47ef-90ce-94dfd42a9c44">
									<SHORT-NAME>CalSymDecrypt</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of SymDecrypt primitives</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalSymDecryptMaxCtxBufByteSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:7e86b49f-ca1e-4969-8d23-6b58a4387f7d">
											<SHORT-NAME>CalSymDecryptMaxCtxBufByteSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all context buffers used in all CPL primitives which implement a symmetrical decryption.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CalSymDecryptMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:207278f2-5333-4d05-bf17-4acc4b52de50">
											<SHORT-NAME>CalSymDecryptMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CPL primitives which implement a symmetrical decryption.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalSymDecryptConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:f413d024-59fa-42d0-8b92-1c64c7d5a1b5">
											<SHORT-NAME>CalSymDecryptConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service SymDecrypt.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The container name serves as a symbolic name for the identifier of a service configuration.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalSymDecryptInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:a3a3297a-fedf-4f32-9946-d78b46d6894d">
													<SHORT-NAME>CalSymDecryptInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalSymDecryptPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:1467d2d9-0718-4aa1-9d15-b864a1bfcc6f">
													<SHORT-NAME>CalSymDecryptPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalSymEncrypt -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:874ca516-3ea1-4673-bdcb-78dcb8e75e34">
									<SHORT-NAME>CalSymEncrypt</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of SymEncrypt primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalSymEncryptMaxCtxBufByteSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:c228691f-bd5d-4d14-b9ed-4db30fa05d33">
											<SHORT-NAME>CalSymEncryptMaxCtxBufByteSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all context buffers used in all CPL primitives which implement a symmetrical encryption.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CalSymEncryptMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:9b575a02-485b-4815-8432-bcf32b45f39f">
											<SHORT-NAME>CalSymEncryptMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CPL primitives which implement a symmetrical encryption.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalSymEncryptConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:58bb6a94-d5f0-4bf6-b922-9a2ae8dcf9ac">
											<SHORT-NAME>CalSymEncryptConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service SymEncrypt. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalSymBlockEncryptInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:6e493190-58ea-4034-94da-04ce84ff7225">
													<SHORT-NAME>CalSymBlockEncryptInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalSymEncryptPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:0d22d540-3889-4dd5-80bc-4d329748bb00">
													<SHORT-NAME>CalSymEncryptPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalSymKeyExtract -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:fadfb320-923d-4607-80e8-f04e6cbe6787">
									<SHORT-NAME>CalSymKeyExtract</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of SymKeyExtract primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalSymKeyExtractMaxCtxBufByteSize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:c4dcf674-b454-4c2c-bd06-a836ab76c3db">
											<SHORT-NAME>CalSymKeyExtractMaxCtxBufByteSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all context buffers used in all CPL primitives which implement a symmetrical key extraction.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CalSymKeyExtractMaxKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:27a1e153-127b-4b7d-b178-bf24afaa9184">
											<SHORT-NAME>CalSymKeyExtractMaxKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CPL primitives which implement a symmetrical key extraction.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalSymKeyExtractConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:f671e200-2693-4ca1-b610-f9edfa2683e9">
											<SHORT-NAME>CalSymKeyExtractConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service SymKeyExtract. The container name serves as a symbolic name for the identifier of a service configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalSymKeyExtractInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:b18dbda5-2217-4bfd-8ede-58095ba221ad">
													<SHORT-NAME>CalSymKeyExtractInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalSymKeyExtractPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:879aa62c-b8ef-4371-90c9-79bfe6e4e2bc">
													<SHORT-NAME>CalSymKeyExtractPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalSymKeyWrapAsym -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e6940cad-173b-4cca-9023-11a4724ea7d5">
									<SHORT-NAME>CalSymKeyWrapAsym</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of SymKeyWrapAsym primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalSymKeyWrapAsymMaxPubKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:9fba9927-6edf-405d-b14e-dfc06619ce87">
											<SHORT-NAME>CalSymKeyWrapAsymMaxPubKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum length, in bytes, of all public key  types used in all CPL primitives which implement a symmetrical key wrapping.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: CalSymKeyWrapAsymMaxSymKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:664ffa6e-cdf0-4239-89fe-ed591362e592">
											<SHORT-NAME>CalSymKeyWrapAsymMaxSymKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CPL primitives which implement a symmetrical key wrapping.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalSymKeyWrapAsymConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:32deddc9-6f4d-4e39-a51c-b3ea37a4fcf0">
											<SHORT-NAME>CalSymKeyWrapAsymConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service SymKeyWrapAsym.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The container name serves as a symbolic name for the identifier of a service configuration.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalSymKeyWrapAsymInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:75bee5f8-3bbd-4b08-b7d2-adbf72f7e8f8">
													<SHORT-NAME>CalSymKeyWrapAsymInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalSymKeyWrapAsymPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:3fa84ac3-6b11-49e2-983b-166fd1a72b4a">
													<SHORT-NAME>CalSymKeyWrapAsymPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: CalSymKeyWrapSym -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:a48d5fad-4e91-4887-ac82-090b693ab99f">
									<SHORT-NAME>CalSymKeyWrapSym</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for incorporation of SymKeyWrapSym primitives.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: CalSymKeyWrapSymMaxSymKeySize -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:b9a42ac3-a2ef-4a64-9b33-4a398fdd2fde">
											<SHORT-NAME>CalSymKeyWrapSymMaxSymKeySize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The maximum, in bytes, of all key lengths used in all CPL primitives which implement a symmetrical key wrapping.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<!-- Container Definition: CalSymKeyWrapSymConfig -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:467640ee-e2a3-4818-8044-9c26aa919257">
											<SHORT-NAME>CalSymKeyWrapSymConfig</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Container for configuration of service SymKeyWrapSym.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">The container name serves as a symbolic name for the identifier of a service configuration.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: CalSymKeyWrapSymInitConfiguration -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:3ff6e410-231d-4fb5-87ed-05cb4017634f">
													<SHORT-NAME>CalSymKeyWrapSymInitConfiguration</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of a C symbol which contains the configuration of the underlying cryptographic primitive.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
												<!-- PARAMETER DEFINITION: CalSymKeyWrapSymPrimitiveName -->
												<ECUC-STRING-PARAM-DEF UUID="ECUC:8835a39e-8f26-4b59-a1b9-285e0a5fc59f">
													<SHORT-NAME>CalSymKeyWrapSymPrimitiveName</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Name of the cryptographic primitive to use.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-STRING-PARAM-DEF-VARIANTS>
														<ECUC-STRING-PARAM-DEF-CONDITIONAL/>
													</ECUC-STRING-PARAM-DEF-VARIANTS>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</CONTAINERS>
						</ECUC-MODULE-DEF>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
