<?xml version="1.0" encoding="UTF-8"?>
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://autosar.org/schema/r4.0">
	<AR-PACKAGES>
		<AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
			<SHORT-NAME>MICROSAR</SHORT-NAME>
			<ELEMENTS>
				<BSW-IMPLEMENTATION UUID="29b74dcc-81cb-42ee-8659-7233b41a802a">
					<SHORT-NAME>vBRS_Impl</SHORT-NAME>
					<PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
					<SW-VERSION>1.00.00</SW-VERSION>
					<USED-CODE-GENERATOR>DaV<PERSON><PERSON> Configurator</USED-CODE-GENERATOR>
					<VENDOR-ID>30</VENDOR-ID>
					<AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
					<PRECONFIGURED-CONFIGURATION-REFS>
						<PRECONFIGURED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/vBRS_Pre</PRECONFIGURED-CONFIGURATION-REF>
					</PRECONFIGURED-CONFIGURATION-REFS>
					<RECOMMENDED-CONFIGURATION-REFS>
						<RECOMMENDED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/vBRS_Rec</RECOMMENDED-CONFIGURATION-REF>
					</RECOMMENDED-CONFIGURATION-REFS>
					<VENDOR-SPECIFIC-MODULE-DEF-REFS>
						<VENDOR-SPECIFIC-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/MICROSAR/vBRS</VENDOR-SPECIFIC-MODULE-DEF-REF>
					</VENDOR-SPECIFIC-MODULE-DEF-REFS>
				</BSW-IMPLEMENTATION>
				<ECUC-MODULE-DEF UUID="3d6c4b68-93ce-4e32-a675-63dc141ae480">
					<SHORT-NAME>vBRS</SHORT-NAME>
					<LONG-NAME>
						<L-4 L="EN">Basic Runtime System</L-4>
					</LONG-NAME>
					<DESC>
						<L-2 L="EN">Configuration of the Basic Runtime System.</L-2>
					</DESC>
					<CATEGORY>VENDOR_SPECIFIC_MODULE_DEFINITION</CATEGORY>
					<ADMIN-DATA>
						<DOC-REVISIONS>
							<DOC-REVISION>
								<REVISION-LABEL>1.00.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>vibwa</ISSUED-BY>
								<DATE>2018-05-03</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">initial BRS version</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">new BRS concept</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.00.01</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>visbwa</ISSUED-BY>
								<DATE>2018-08-15</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Added EcuM stub switch and additional driver handling CAN+LIN channels</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">feature update</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.01.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>visbwa</ISSUED-BY>
								<DATE>2018-10-10</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Added SchM stub switch, DisableSetupSupport and MakefileUserCode</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">vBaseEnv 1.01.00</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.02.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>visbwa</ISSUED-BY>
								<DATE>2019-03-11</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Added TogglePin Dio references and EnableSchmStubSupportDrvCanAN, added peripheral clock parameters for all communication variants, added vBRSEvalBoardOscClock and reworked descriptions of EvalBoard parameters, removed outdated vBRSOverrideSwitches</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">vBaseEnv 1.04.00</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.03.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>visbwa</ISSUED-BY>
								<DATE>2019-05-23</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Increased number of LIN channels to 0..22</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">vBaseEnv 1.06.00</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.05.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>visbwa</ISSUED-BY>
								<DATE>2019-08-09</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Added support for several FBL UseCases, added Memory InitPattern parameters</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">vBaseEnv 1.07.00</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.06.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>visbwa</ISSUED-BY>
								<DATE>2020-02-19</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Added parameter to enable HSM Core support</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">vBaseEnv 1.10.00</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>2.01.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>visbwa</ISSUED-BY>
								<DATE>2020-06-29</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Removed vBRSTccSupport value for TCM, added vBRSPreferPLLWatchdogInit</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">vBaseEnv 2.01.00</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>2.01.01</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>visbwa</ISSUED-BY>
								<DATE>2020-07-20</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Changed long name of user config file</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">user feedback</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>2.02.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>visbwa</ISSUED-BY>
								<DATE>2020-08-10</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Chnaged naming of FBL UseCases</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">vBaseEnv 2.04.00</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
						</DOC-REVISIONS>
					</ADMIN-DATA>
					<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
					<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
					<SUPPORTED-CONFIG-VARIANTS>
						<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
					</SUPPORTED-CONFIG-VARIANTS>
					<CONTAINERS>
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="bb882025-f8ef-4820-b10a-520712c7af28">
							<SHORT-NAME>vBRSGeneral</SHORT-NAME>
							<DESC>
								<L-2 L="EN">General configuration parameters of the vBRS.
</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
							<PARAMETERS>
								<ECUC-BOOLEAN-PARAM-DEF UUID="a475ebec-4229-4a0c-be19-d645882ffcf8">
									<SHORT-NAME>vBRSEnableSupportLEDs</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Toggle LED-Pin</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Use the functionality of a blinking live-LED for the used EvalBoard
Sets the following generated parameters:
BRS_ENABLE_SUPPORT_LEDS in vBrsCfg.h</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="ec531c9c-dc87-448a-a404-1603ad6f9191">
									<SHORT-NAME>vBRSEnableSafeContextSupport</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Enable OS SafeContext (SC3) support</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Enable support of SafeContext OS (used for OS partitioning UseCase).
Sets the following generated parameters:
BRS_ENABLE_SAFECTXSUPPORT in vBrsCfg.h</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="3ffe05ed-defd-4255-9810-5eb28f663ad9">
									<SHORT-NAME>vBRSEnableMultiCoreSupport</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Enable OS MultiCore support</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Enable support of MultiCore OS.
Sets the following generated parameters:
BRS_ENABLE_OS_MULTICORESUPPORT in vBrsCfg.h</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-STRING-PARAM-DEF UUID="cc60ef48-3bc0-455d-a809-d4a07376982b">
									<SHORT-NAME>vBRSUserConfigFile</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">User Config Header File</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">If a file name is entered here, it will be included in vBrsCfg.h.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
								</ECUC-STRING-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="2689f64a-3654-4ee4-82a7-c4f6e687821f">
									<SHORT-NAME>vBRSEnableSupportToggleWdPin</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Toggle WD-Pin</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Use the functionality of a blinking live-LED for the used EvalBoard
Sets the following generated parameters:
BRS_ENABLE_SUPPORT_TOGGLE_WD_PIN in vBrsCfg.h</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="a62f3135-661c-425d-8b3e-955cd933b056">
									<SHORT-NAME>vBRSEnableSupportToggleCustomPin</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Toggle Custom-Pin</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Use the functionality of a blinking live-LED for the used EvalBoard
Sets the following generated parameters:
BRS_ENABLE_SUPPORT_TOGGLE_CUSTOM_PIN in vBrsCfg.h</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="1947fd9a-6d78-4444-997a-5602624f235e">
									<SHORT-NAME>vBRSEnableEcuMStubGeneration</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Generation of EcuM Stub</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">If no real EcuM is part of the configuration this functionality generates an EcuM.c/h stub that calls the Init-functions of the Asr modules as specified in the EcuC configuration.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="c9bdd18d-7188-4912-bdfd-790973ad25b5">
									<SHORT-NAME>vBRSDisableSetupSupport</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Activate this, if you don't want the BRS to guide you through the default MSR stack configuration (e.g. AutosolvingActions for mandatory tasks).</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="15b41f44-cf5c-49f7-8d13-7050599cf9aa">
									<SHORT-NAME>vBRSEnableSchmStubGeneration</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Generation of SchM Stub</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Activate this, if the SchM stubs containing the defines for EXCLUSIVE AREAs should be generated.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="2d9c352f-b34c-49e2-8dcd-9a8f31878381">
									<SHORT-NAME>vBRSEnableSchmStubSupportDrvCanAN</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Generation of SchM Stub - support DrvCan AN</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Prevents the SchM stub from generation of the following exclusive areas:
CAN Driver (CAN_EXCLUSIVE_AREA_6) 
CAN Interface (CANIF_EXCLUSIVE_AREA_0) 
CANSM (CANSM_ EXCLUSIVE_AREA_1, CANSM_ EXCLUSIVE_AREA_4) 
COMM (COMM_ EXCLUSIVE_AREA_1)
As described within CanDriver ApplicationNote AN-ISC-8-1149</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-ENUMERATION-PARAM-DEF UUID="c9c9aa04-8669-4a13-9983-1a9eed8171db">
									<SHORT-NAME>vBRSFblSupport</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">FBL Support Configuration</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Configure the support for FlashBootLoader projects.
Sets the following generated parameters:
BRS_ENABLE_FBL in vBrsCfg.h, additional to one of the following:
BRS_FBL_NO_ECUMINIT, BRS_FBL_WITH_ECUMINIT</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>Disabled</DEFAULT-VALUE>
									<LITERALS>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="74396fc1-2e8c-463a-b2fa-601047fe1a74">
											<SHORT-NAME>Disabled</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Disable FBL Support</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="fb93ba8b-aef3-4fdc-b5be-6c85c2d9bc5e">
											<SHORT-NAME>FBL_without_EcuMInit</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">oldstyle FBL integration, direct call of FblMain</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="cbed3bdf-5259-4b13-bb6a-4a8cba3dfcca">
											<SHORT-NAME>FBL_with_EcuMInit</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Cyclic handling in FBL, module initialization in EcuM</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
									</LITERALS>
								</ECUC-ENUMERATION-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="02332af1-3dcf-401f-9231-b864e9ac6e7d">
									<SHORT-NAME>vBRSEnableHSMSupport</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Enable HSM Core support</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Enable support of HSM Core UseCase.
Sets the following generated parameters:
BRS_ENABLE_HSM_SUPPORT in vBrsCfg.h</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
							</PARAMETERS>
							<REFERENCES>
								<ECUC-REFERENCE-DEF UUID="a4e3946e-a6aa-4680-95ba-dbe6a0845946">
									<SHORT-NAME>vBRSToggleLEDDioChannel</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Toggle LED-Pin - Dio Channel Reference</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Dio channel reference setting for toggling the LED port.
Only relevant if non-stub Dio is part of the configuration.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dio/DioConfig/DioPort/DioChannel</DESTINATION-REF>
								</ECUC-REFERENCE-DEF>
								<ECUC-REFERENCE-DEF UUID="c6a7ce01-85cf-44fb-9d51-57922171864a">
									<SHORT-NAME>vBRSToggleWdPinDioChannel</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Toggle WD-Pin - Dio Channel Reference</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Dio channel reference setting for toggling the WD port.
Only relevant if non-stub Dio is part of the configuration.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dio/DioConfig/DioPort/DioChannel</DESTINATION-REF>
								</ECUC-REFERENCE-DEF>
								<ECUC-REFERENCE-DEF UUID="06feb7af-96ea-4c15-8231-e0eec3651799">
									<SHORT-NAME>vBRSToggleCustomPinDioChannel</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Toggle Custom-Pin - Dio Channel Reference</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Dio channel reference setting for toggling the Custom port.
Only relevant if non-stub Dio is part of the configuration.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dio/DioConfig/DioPort/DioChannel</DESTINATION-REF>
								</ECUC-REFERENCE-DEF>
							</REFERENCES>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="bacc25bd-3c8c-45e2-956b-eebd74fe88bb">
							<SHORT-NAME>vBRSHwConfig</SHORT-NAME>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
							<PARAMETERS>
								<ECUC-INTEGER-PARAM-DEF UUID="49b3f15a-0e60-47c4-b9d4-a885be4ce6a4">
									<SHORT-NAME>vBRSOscClock</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Clock Config: Oscillator Clock Frequency</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Frequency of the board specific external oscillator.

Sets the following generated parameters:
BRS_OSC_CLK  in vBrsCfg.h 
MAIN_OSC_CLK in Makefile.config.generated</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:BaseUnit">HZ</SD>
												<SD GID="DV:Unit">MHZ</SD>
											</SDG>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0</DEFAULT-VALUE>
									<MAX>18446744073709551615</MAX>
									<MIN>0</MIN>
								</ECUC-INTEGER-PARAM-DEF>
								<ECUC-INTEGER-PARAM-DEF UUID="3000fa4f-fdb3-4e27-ac75-02136f810785">
									<SHORT-NAME>vBRSTimebaseClock</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Clock Config: Timebase (PLL) clock frequency</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Frequency, used for PLL calculation (normally used as CPU core clock).

Sets the following generated parameters:
BRS_TIMEBASE_CLOCK in vBrsCfg.h 
TIMEBASE_CLOCK in Makefile.config.generated</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:BaseUnit">HZ</SD>
												<SD GID="DV:Unit">MHZ</SD>
											</SDG>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0</DEFAULT-VALUE>
									<MAX>18446744073709551615</MAX>
									<MIN>0</MIN>
								</ECUC-INTEGER-PARAM-DEF>
								<ECUC-INTEGER-PARAM-DEF UUID="0cb893a3-c6c8-44fa-8cd6-65bd08431c38">
									<SHORT-NAME>vBRSPeriphClock</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Clock Config: Peripheral (Timer) Clock Frequency</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Frequency, used for PLL calculation (normally used as CPU core clock).

Sets the following generated parameters:
BRS_PERIPH_CLOCK in vBrsCfg.h 
PERIPH_CLOCK in Makefile.config.generated</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:BaseUnit">HZ</SD>
												<SD GID="DV:Unit">MHZ</SD>
											</SDG>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0</DEFAULT-VALUE>
									<MAX>18446744073709551615</MAX>
									<MIN>0</MIN>
								</ECUC-INTEGER-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="54206c8d-cddf-44c6-a29d-4884d95a9c0e">
									<SHORT-NAME>vBRSEnablePortHandling</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">BrsHw: Enable Port Handling</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Enable the handling of Ports configuration inside BrsHw.

Sets the following generated parameter:
BRS_ENABLE_PORT in vBrsCfg.h</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="752d1115-29fb-45fa-8985-7d64e34d7873">
									<SHORT-NAME>vBRSEnableWatchdogHandling</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">BrsHw: Enable Watchdog Handling</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Enable the handling of Watchdog configuration inside BrsHw.

Sets the following generated parameter:
BRS_ENABLE_WATCHDOG in vBrsCfg.h</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="d7e68e18-3245-46df-a02f-ae8d9b52b535">
									<SHORT-NAME>vBRSEnablePLLClocksHandling</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">BrsHw: Enable PLL&amp;Clocks Handling</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Enable the handling of PLL and Clocks configuration inside BrsHw.

Sets the following generated parameter:
BRS_ENABLE_PLLCLOCKS in vBrsCfg.h</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-ENUMERATION-PARAM-DEF UUID="dc60561f-3c86-46c6-960c-ce4adac23176">
									<SHORT-NAME>vBRSCompilerInstructionSet</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Makesupport: Compiler Instruction Set</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Select the platform dependent Instruction Set for compile/link of the project.
Note: not all platforms are supporting different instruction sets.

Generated as the following:
Makefile.config.generated: INSTRUCTION_SET = xxx
vBrsCfg.h: BRS_INSTRUCTION_SET_xxx</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<LITERALS>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="e3357877-5b2f-4f9a-815c-0e65599d4682">
											<SHORT-NAME>THUMB</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Thumb Mode for Arm core controllers</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="003cd612-5a63-4671-a201-d7cdc621a532">
											<SHORT-NAME>ARM</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Arm Mode for Arm core controllers</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="8f85266d-360c-4535-a303-51791463622a">
											<SHORT-NAME>VLE</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Vle Mode for PPC core controllers</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="91c22608-4e38-4079-9451-ca92d2726b0a">
											<SHORT-NAME>BOOKE</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Book-E Mode for PPC core controllers</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
									</LITERALS>
								</ECUC-ENUMERATION-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="127a23dc-aec7-4bbd-87d3-391e505c47a0">
									<SHORT-NAME>vBRSCompilerEnableFPU</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Makesupport: Enable FPU support</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Enable FPU usage on the controller. This has effect on the compiler/linker options.
Note: not all platforms are supporting enabling/disabling of FPU usage.

Generated as the following:
Makefile.config.generated: FPU_USED = 1/0
vBrsCfg.h: BRS_FPU_USED</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-STRING-PARAM-DEF UUID="3e40bec2-0481-4ec1-8354-1ed632289ac7">
									<SHORT-NAME>vBRSPeriphClockDescription</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Clock Config: Peripheral (Timer) Clock Description</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Platform/family specific preconfig parameter with description of the recommended platform/family clock values and for usage of the peripheral clock (connection to OS timer).</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
								</ECUC-STRING-PARAM-DEF>
								<ECUC-MULTILINE-STRING-PARAM-DEF UUID="871793a1-d3c0-4855-b587-1e0c1639583f">
									<SHORT-NAME>vBRSMakefileUserCode</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Makesupport: UserCode</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Define here additional UserCode, to be generated into Makefile.config.generated</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
								</ECUC-MULTILINE-STRING-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="50543d65-b406-4162-bc61-93cc7750ce6e">
									<SHORT-NAME>vBRSPreferPLLWatchdogInit</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">BrsHw: Prefer PLL and Watchdog Init</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Prefer the initialization of PLL and Watchdog, previous to memory initialization.
This will cause the depending init calls, previous to init stage One, inside the callout BrsMain_MemoryInit_StageOne_Hook() of BrsMain_Callout_Stubs.c.

Sets the following generated parameter:
BRS_ENABLE_PREFER_PLL_WATCHDOG_INIT in vBrsCfg.h</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
							</PARAMETERS>
							<REFERENCES>
								<ECUC-REFERENCE-DEF UUID="491b3333-9050-4e22-8ca4-755a2ee85de5">
									<SHORT-NAME>vBRSMcuClockReferencePointForBrsPeriphClock</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Clock Config: Peripheral (Timer) Clock Mcu Reference</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">McuClockReferencePoint reference for peripheral clock setting.
If set, a solving action is provided to set the BrsPeriphClock to the value, this reference is pointing to.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint</DESTINATION-REF>
								</ECUC-REFERENCE-DEF>
								<ECUC-REFERENCE-DEF UUID="562bb218-f781-4cf4-88b5-a8ee28db1948">
									<SHORT-NAME>vBRSEvaluationBoard</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">BrsHw: Select Evaluation Board</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Select a Eval Board from the list of supported ones.
Sets the following generated parameters:
BRS_EVA_BOARD_xxx in vBrsCfg.h 
EVA_BOARD in Makefile.config.generated</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vBRS/vBRSHwConfig/vBRSEvalBoardInfo</DESTINATION-REF>
								</ECUC-REFERENCE-DEF>
							</REFERENCES>
							<SUB-CONTAINERS>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="666d966b-dc52-4e56-b0d9-15333115de1c">
									<SHORT-NAME>vBRSDriverHandlingCAN</SHORT-NAME>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<ECUC-BOOLEAN-PARAM-DEF UUID="7fa71e59-f71a-4202-9701-d1deccfb09b2">
											<SHORT-NAME>vBRSEnableDriverHandlingCAN</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Enable handling of CAN driver</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">This switch enables the handling of driver specific settings of
- PLL and clock settings
- Port Pin settings
- On some platforms additional register settings, e.g. inside BrsHwPreInitPowerOn() or BrsHw_EnableHwAccess()

To handle this inside the BrsHw Sourcecode, the define
BRS_ENABLE_CAN_SUPPORT
is genrated.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="f786037d-728d-47b1-84a8-b363b0bac48c">
											<SHORT-NAME>vBRSDriverHandlingCAN_channel0</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 0</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 0.
Sets the following generated parameter:
BRS_ENABLE_CAN_CHANNEL_0 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="bee954e4-f93a-488b-80fc-875588f4852f">
											<SHORT-NAME>vBRSDriverHandlingCAN_channel1</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 1</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 1.
Sets the following generated parameter:
BRS_ENABLE_CAN_CHANNEL_1 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="5f1a1d92-a22b-4e37-8873-8b74a63e8be3">
											<SHORT-NAME>vBRSDriverHandlingCAN_channel2</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 2</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 2.
Sets the following generated parameter:
BRS_ENABLE_CAN_CHANNEL_2 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="fbee95e3-c0e6-40e3-a36c-99d613ea2f1f">
											<SHORT-NAME>vBRSDriverHandlingCAN_channel3</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 3</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 3.
Sets the following generated parameter:
BRS_ENABLE_CAN_CHANNEL_3 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="27f469c1-5e15-4a24-aa9c-f3397780d64e">
											<SHORT-NAME>vBRSDriverHandlingCAN_channel4</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 4</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 4.
Sets the following generated parameter:
BRS_ENABLE_CAN_CHANNEL_4 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="b53498ad-013f-4ccb-8359-6333c1c1ad3a">
											<SHORT-NAME>vBRSDriverHandlingCAN_channel5</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 5</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 5.
Sets the following generated parameter:
BRS_ENABLE_CAN_CHANNEL_5 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="7a304631-7ec2-40a4-956a-72811143c4b9">
											<SHORT-NAME>vBRSDriverHandlingCAN_channel6</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 6</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 6.
Sets the following generated parameter:
BRS_ENABLE_CAN_CHANNEL_6 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="5f6e203f-b592-4289-b0f0-50ef7cbfaf7a">
											<SHORT-NAME>vBRSDriverHandlingCAN_channel7</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 7</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 7.
Sets the following generated parameter:
BRS_ENABLE_CAN_CHANNEL_7 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="dbdabdfa-1afb-4c1f-8dae-2cca12dd067a">
											<SHORT-NAME>vBRSDriverHandlingCAN_channel8</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 8</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 8.
Sets the following generated parameter:
BRS_ENABLE_CAN_CHANNEL_8 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="8a41b9cb-95b3-4e82-8c39-7b258bf981a3">
											<SHORT-NAME>vBRSDriverHandlingCAN_channel9</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 9</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 9.
Sets the following generated parameter:
BRS_ENABLE_CAN_CHANNEL_9 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="746b7558-b794-4dfc-bb4d-32658ef6d469">
											<SHORT-NAME>vBRSDriverHandlingCAN_PeripheralClock</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Frequency is similar with timer peripheral clock</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">If enabled, the clock defined in 
vBRSHwConfig/vBRSPeriphClock
will also be used for the peripheral clock, used by the CAN driver.

If the clock, provided to the driver cell, is different, disable this parameter and configure the valid frequency within the paramter 
vBRSDriverHandlingCAN_Clock or
vBRSDriverHandlingCAN_ClockRef (if Mcu is used)</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="a21ce4e9-2768-4c75-84bf-63c7790b5a4b">
											<SHORT-NAME>vBRSDriverHandlingCAN_Clock</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Frequency of peripheral clock</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Frequency, used for PLL calculation of CAN driver peripheral clock.
Sets the following generated parameters:
BRS_CAN_CLOCK in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">HZ</SD>
														<SD GID="DV:Unit">MHZ</SD>
													</SDG>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>18446744073709551615</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<ECUC-REFERENCE-DEF UUID="8a8c5ec1-e018-4711-9114-f9047a977a5d">
											<SHORT-NAME>vBRSDriverHandlingCAN_ClockRef</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Frequency of peripheral clock - Mcu Reference</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">McuClockReferencePoint reference for peripheral clock setting.
If set, a solving action is provided to set the peripheral clock to the value, this reference is pointing to.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="72388ed4-e12a-4fa5-b9d7-f57089a4fddf">
									<SHORT-NAME>vBRSDriverHandlingLIN</SHORT-NAME>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<ECUC-BOOLEAN-PARAM-DEF UUID="44cf4bfc-3b59-4983-985f-e1e263c0959d">
											<SHORT-NAME>vBRSEnableDriverHandlingLIN</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Enable handling of LIN driver</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">This switch enables the handling of driver specific settings of
- PLL and clock settings
- Port Pin settings
- On some platforms additional register settings, e.g. inside BrsHwPreInitPowerOn() or BrsHw_EnableHwAccess()

To handle this inside the BrsHw Sourcecode, the define
BRS_ENABLE_LIN_SUPPORT
is genrated.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="95cc802a-1ff6-4598-953e-409c77fc443d">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel0</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 00</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 0.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_0 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="2a73a38b-04b3-4367-93ea-187cb7bfe850">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel1</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 01</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 1.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_1 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="5299ca95-7b22-4d5e-851a-36cdc1e1acfb">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel2</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 02</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 2.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_2 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="9a7ba021-**************-401902135df4">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel3</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 03</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 3.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_3 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="1267f38e-c1f1-46b0-b8a4-08b2e87ee325">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel4</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 04</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 4.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_4 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="6055702f-147a-445b-b12b-baf1d6164c54">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel5</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 05</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 5.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_5 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="ee28fb2e-8acc-4b97-8911-cc1b0047000e">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel6</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 06</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 6.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_6 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="d52acda5-7ede-4d5c-86c3-18dfa7191e6c">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel7</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 07</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 7.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_7 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="3d358a0c-8b7a-45e7-a60d-a4d392125430">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel8</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 08</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 8.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_8 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="d26e53a4-930b-4ada-92cc-c50e61cca635">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel9</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 09</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 9.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_9 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="28b5975b-7499-45f0-b336-0aa3f120c67c">
											<SHORT-NAME>vBRSDriverHandlingLIN_Clock</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Frequency of peripheral clock</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Frequency, used for PLL calculation of LIN driver peripheral clock.
Sets the following generated parameters:
BRS_LIN_CLOCK in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">HZ</SD>
														<SD GID="DV:Unit">MHZ</SD>
													</SDG>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>18446744073709551615</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="306922d8-f561-4291-9302-9acc9e5559d4">
											<SHORT-NAME>vBRSDriverHandlingLIN_PeripheralClock</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Frequency is similar with timer peripheral clock</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">If enabled, the clock defined in 
vBRSHwConfig/vBRSPeriphClock
will also be used for the peripheral clock, used by the LIN driver.

If the clock, provided to the driver cell, is different, disable this parameter and configure the valid frequency within the paramter 
vBRSDriverHandlingLIN_Clock or
vBRSDriverHandlingLIN_ClockRef (if Mcu is used)</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="bee7c712-f8b4-4b84-b841-6a78d75a3f10">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel10</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 10</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 10.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_10 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="63d36744-e6eb-4fbe-aa75-828c7f977384">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel11</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 11</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 11.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_11 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="2a82d389-8b4c-4de7-ab3f-1d23f8c59c21">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel12</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 12</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 12.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_12 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="8df52179-cb28-4950-a0e4-4f62cea86ea2">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel13</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 13</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 13.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_13 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="1c8eafcc-d2ad-4462-808d-fd82cf5805e1">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel14</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 14</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 14.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_14 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="ffd07b8d-f51d-4a21-bc80-4f7cad966af8">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel15</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 15</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 15.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_15 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="c6317f3f-eba8-4be2-8a25-b506e7989d9b">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel16</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 16</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 16.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_16 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="f1f0c9a5-786c-4e22-91aa-fef116911bd4">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel17</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 17</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 10.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_10 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="5b8f3d86-d198-4b33-a653-db8738b6ad72">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel18</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 18</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 18.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_18 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="3d0b4376-8c84-4a17-bec8-8349654d09f4">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel19</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 19</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 19.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_19 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="6a508ea9-ff4a-491c-9db6-3f052f3f2ed1">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel20</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 20</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 20.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_20 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="3fb2889a-1e7d-4fba-af5b-baa9a34b0ccf">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel21</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 21</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 21.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_21 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="abd70e5c-070a-4736-b781-9b7606ab67bc">
											<SHORT-NAME>vBRSDriverHandlingLIN_channel22</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 22</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 22.
Sets the following generated parameter:
BRS_ENABLE_LIN_CHANNEL_22 in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<ECUC-REFERENCE-DEF UUID="4650b3d8-56a4-4c59-9e5d-88c55cd87c71">
											<SHORT-NAME>vBRSDriverHandlingLIN_ClockRef</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Frequency of peripheral clock - Mcu Reference</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">McuClockReferencePoint reference for peripheral clock setting.
If set, a solving action is provided to set the peripheral clock to the value, this reference is pointing to.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="9a69c72f-3c13-44b2-84c3-e642ecef1133">
									<SHORT-NAME>vBRSDriverHandlingFlexray</SHORT-NAME>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<ECUC-BOOLEAN-PARAM-DEF UUID="aa7a9e83-b4ac-4ce8-9c26-8104e43e85cc">
											<SHORT-NAME>vBRSEnableDriverHandlingFlexray</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Enable handling of FlexRay driver</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">This switch enables the handling of driver specific settings of
- PLL and clock settings
- Port Pin settings
- On some platforms additional register settings, e.g. inside BrsHwPreInitPowerOn() or BrsHw_EnableHwAccess()

To handle this inside the BrsHw Sourcecode, the define
BRS_ENABLE_FLEXRAY_SUPPORT
is genrated.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="9d140e60-c3a7-4be3-96b8-427b0f28c4bd">
											<SHORT-NAME>vBRSDriverHandlingFlexray_channel0A</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 0A</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 0A.
Sets the following generated parameter:
BRS_ENABLE_FLEXRAY_CHANNEL_0A in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="fa9fd5a7-4ba9-4ed8-b3f8-b2188ab93db9">
											<SHORT-NAME>vBRSDriverHandlingFlexray_channel0B</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 0B</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 0B.
Sets the following generated parameter:
BRS_ENABLE_FLEXRAY_CHANNEL_0B in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="b78c3ce9-17e9-41cd-9788-e91f67a88be9">
											<SHORT-NAME>vBRSDriverHandlingFlexray_channel1A</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 1A</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 1A.
Sets the following generated parameter:
BRS_ENABLE_FLEXRAY_CHANNEL_1A in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="89bbf9c7-0204-446d-9d01-1080cbd7c45a">
											<SHORT-NAME>vBRSDriverHandlingFlexray_channel1B</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Handling of channel 1B</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the configuration of communication channel 1B.
Sets the following generated parameter:
BRS_ENABLE_FLEXRAY_CHANNEL_1B in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="36617ead-159a-4e0b-ac88-200453a85de6">
											<SHORT-NAME>vBRSDriverHandlingFlexray_PeripheralClock</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Frequency is similar with timer peripheral clock</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">If enabled, the clock defined in 
vBRSHwConfig/vBRSPeriphClock
will also be used for the peripheral clock, used by the FlexRay driver.

If the clock, provided to the driver cell, is different, disable this parameter and configure the valid frequency within the paramter 
vBRSDriverHandlingFlexray_Clock or
vBRSDriverHandlingFlexray_ClockRef (if Mcu is used)</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="7b3a0f78-776e-4161-bc9b-2c643578b4df">
											<SHORT-NAME>vBRSDriverHandlingFlexray_Clock</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Frequency of peripheral clock</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Frequency, used for PLL calculation of FlexRay driver peripheral clock.
Sets the following generated parameters:
BRS_FLEXRAY_CLOCK in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">HZ</SD>
														<SD GID="DV:Unit">MHZ</SD>
													</SDG>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>18446744073709551615</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<ECUC-REFERENCE-DEF UUID="2b98fbfd-1e40-47d6-8ef0-db9d705599d9">
											<SHORT-NAME>vBRSDriverHandlingFlexray_ClockRef</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Frequency of peripheral clock - Mcu Reference</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">McuClockReferencePoint reference for peripheral clock setting.
If set, a solving action is provided to set the peripheral clock to the value, this reference is pointing to.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="129f46fb-7cbd-42c7-a71d-26c4af70c239">
									<SHORT-NAME>vBRSDriverHandlingEthernet</SHORT-NAME>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<ECUC-BOOLEAN-PARAM-DEF UUID="bf2cdb1c-0230-422b-8725-313b0112522e">
											<SHORT-NAME>vBRSEnableDriverHandlingEthernet</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Enable handling of EtherNet driver</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">This switch enables the handling of driver specific settings of
- PLL and clock settings
- Port Pin settings
- On some platforms additional register settings, e.g. inside BrsHwPreInitPowerOn() or BrsHw_EnableHwAccess()

To handle this inside the BrsHw Sourcecode, the define
BRS_ENABLE_ETHERNET_SUPPORT
is genrated.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="0e9e5206-6ea9-4102-a535-b513da765524">
											<SHORT-NAME>vBRSDriverHandlingEthernet_PeripheralClock</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Frequency is similar with timer peripheral clock</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">If enabled, the clock defined in 
vBRSHwConfig/vBRSPeriphClock
will also be used for the peripheral clock, used by the EtherNet driver.

If the clock, provided to the driver cell, is different, disable this parameter and configure the valid frequency within the paramter 
vBRSDriverHandlingEthernet_Clock or
vBRSDriverHandlingEthernet_ClockRef (if Mcu is used)</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="904d3034-8c0b-4ea8-9a1b-34782fb12adc">
											<SHORT-NAME>vBRSDriverHandlingEthernet_Clock</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Frequency of peripheral clock</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Frequency, used for PLL calculation of EtherNet driver peripheral clock.
Sets the following generated parameters:
BRS_ETHERNET_CLOCK in vBrsCfg.h</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">HZ</SD>
														<SD GID="DV:Unit">MHZ</SD>
													</SDG>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>18446744073709551615</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<ECUC-REFERENCE-DEF UUID="d3d04cdf-337e-4541-ac17-077b5554d50e">
											<SHORT-NAME>vBRSDriverHandlingEthernet_ClockRef</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Frequency of peripheral clock - Mcu Reference</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">McuClockReferencePoint reference for peripheral clock setting.
If set, a solving action is provided to set the peripheral clock to the value, this reference is pointing to.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="aaf1b66c-be23-42a0-ba74-67339fe1947c">
									<SHORT-NAME>vBRSEvalBoardInfo</SHORT-NAME>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<ECUC-STRING-PARAM-DEF UUID="19b52e1c-c70a-44f6-8cbe-1998f6dc3b1f">
											<SHORT-NAME>vBRSEvalBoardDescription</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Evaluation Board Description</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">General description of the supported evaluation board.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-STRING-PARAM-DEF>
										<ECUC-STRING-PARAM-DEF UUID="e2189ef8-2b2c-4102-a78b-4291f53c4bb3">
											<SHORT-NAME>vBRSEvalBoard</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Evaluation Board Define</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Define, used in SourceCode, for this specific Evaluation Board.
Most times, this is used for board specific pin settings.
Sets the following generated parameters:
BRS_EVA_BOARD_x in vBrsCfg.h</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-STRING-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="067b0a10-9c71-4285-8c10-7e8dd71d0855">
											<SHORT-NAME>vBRSEvalBoardOscClock</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Evaluation Board Oscillator Clock Frequency</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Defines the frequency of the oscillator clock, equipped on the specific evaluation board.
This valus will be verified through the vBRS generator to fit with the configured value of vBRSOscClock.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">HZ</SD>
														<SD GID="DV:Unit">MHZ</SD>
													</SDG>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>18446744073709551615</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</SUB-CONTAINERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="d4cf47a7-601c-43f3-9096-a9951bf4e007">
							<SHORT-NAME>vBRSTestsuiteSupport</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Support for Vector internal Testsuites</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
							<PARAMETERS>
								<ECUC-BOOLEAN-PARAM-DEF UUID="6775c756-daac-4d4f-89a8-601019dd1747">
									<SHORT-NAME>vBRSEnableTestsuiteSupport</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Enable support of the Generic TestSuite connectioin.
Via the define BRS_ENABLE_TESTSUITE_SUPPORT, the Generic TestSuite concept headerfile BrsTestsuite.h will be included and the Generic Testsuite init function BrsTestsuiteInit() will be called within BrsMain.c</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-ENUMERATION-PARAM-DEF UUID="4680cc87-4a96-4148-9861-18a805729bb3">
									<SHORT-NAME>vBRSTccSupport</SHORT-NAME>
									<DESC>
										<L-2 L="EN">vBRS Tcc Support</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>None</DEFAULT-VALUE>
									<LITERALS>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="9a200413-39a5-4ec3-b321-d482a8908a2c">
											<SHORT-NAME>None</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">None</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="8fa43837-d5d4-455c-9048-ce06ea504b12">
											<SHORT-NAME>TCCviaComIf</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">TCC via ComIf</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
									</LITERALS>
								</ECUC-ENUMERATION-PARAM-DEF>
							</PARAMETERS>
							<SUB-CONTAINERS>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="5746989b-50fb-465b-82ad-7a3b93b508b5">
									<SHORT-NAME>vBRSTcc</SHORT-NAME>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">true</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<SUB-CONTAINERS>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="7c512a93-f20c-4929-afd3-3554e4c9b41c">
											<SHORT-NAME>vBRSComIfUpperLayerRxPdu</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container specifies Rx PDU that is received by the BRS through communication stack.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">true</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<PARAMETERS>
												<ECUC-INTEGER-PARAM-DEF UUID="448ec58a-67b0-48b2-9a0d-f2984747fcd6">
													<SHORT-NAME>vBRSComIfHandleId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">ECU wide unique, symbolic handle for the Pdu.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<ECUC-REFERENCE-DEF UUID="a2e652a4-f08f-4d30-93ef-bdb7f0abb462">
													<SHORT-NAME>vBRSComIfPduRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the "global" Pdu structure to allow harmonization of handle IDs in the COM-Stack.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="57ae5c92-64c6-431f-ac96-d2c09b3127a9">
											<SHORT-NAME>vBRSComIfUpperLayerTxPdu</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container specifies Tx PDU that is transmitted by the CDD through communication stack.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">true</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<PARAMETERS>
												<ECUC-INTEGER-PARAM-DEF UUID="6d57d352-3f18-47b6-b3fe-1ae4a9a898c0">
													<SHORT-NAME>vBRSComIfHandleId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">ECU wide unique, symbolic handle for the Pdu.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<ECUC-REFERENCE-DEF UUID="fdf4a161-acf2-4a86-9e9c-21111fb278dc">
													<SHORT-NAME>vBRSComIfPduRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the "global" Pdu structure to allow harmonization of handle IDs in the COM-Stack.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</SUB-CONTAINERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="3def5a32-9ec7-4be0-925c-a345cb687c53">
							<SHORT-NAME>vBRSMemoryInit</SHORT-NAME>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
							<PARAMETERS>
								<ECUC-INTEGER-PARAM-DEF UUID="a67bd17f-9988-40cb-8f2c-b0c09cfa7526">
									<SHORT-NAME>vBRSInitPatternBlocks</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Init pattern for Zero Init Blocks</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Configure the init pattern for the Brs_MemoryZeroInit() call within Brs_PreMainStartup().
This value is for the blocks, configured with "Zero Init Policy" = ALWAYS within the vLinkGen configuration.
The pattern has to be 32-bit aligned.
Default value is 0x0.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:DefaultFormat">HEX</SD>
											</SDG>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0</DEFAULT-VALUE>
									<MAX>4294967295</MAX>
									<MIN>0</MIN>
								</ECUC-INTEGER-PARAM-DEF>
								<ECUC-INTEGER-PARAM-DEF UUID="f96c22ed-8e23-44f8-a3db-ebc065504de1">
									<SHORT-NAME>vBRSInitPatternHardResetBlocks</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Init pattern for Hard Reset Zero Init Blocks</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Configure the init pattern for the Brs_MemoryZeroInit() call within Brs_PreMainStartup().
This value is for the blocks, configured with "Zero Init Policy" = HARD_RESET within the vLinkGen configuration.
The pattern has to be 32-bit aligned.
Default value is 0x0.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:DefaultFormat">HEX</SD>
											</SDG>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0</DEFAULT-VALUE>
									<MAX>4294967295</MAX>
									<MIN>0</MIN>
								</ECUC-INTEGER-PARAM-DEF>
								<ECUC-INTEGER-PARAM-DEF UUID="c736c634-41a5-4a71-8a3b-b1202c471231">
									<SHORT-NAME>vBRSInitPatternAreas</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Init pattern for Zero Init Areas</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Configure the init pattern for the Brs_MemoryZeroInit() call within Brs_PreMainStartup().
This value is for the areas, configured with "Zero Init Policy" = ALWAYS within the vLinkGen configuration.
The pattern has to be 32-bit aligned.
Default value is 0x0.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:DefaultFormat">HEX</SD>
											</SDG>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0</DEFAULT-VALUE>
									<MAX>4294967295</MAX>
									<MIN>0</MIN>
								</ECUC-INTEGER-PARAM-DEF>
								<ECUC-INTEGER-PARAM-DEF UUID="849945da-c15b-4518-a0f8-9b657f23813b">
									<SHORT-NAME>vBRSInitPatternHardResetAreas</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Init pattern for Hard Reset Zero Init Areas</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Configure the init pattern for the Brs_MemoryZeroInit() call within Brs_PreMainStartup().
This value is for the areas, configured with "Zero Init Policy" = HARD_RESET within the vLinkGen configuration.
The pattern has to be 32-bit aligned.
Default value is 0x0.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:DefaultFormat">HEX</SD>
											</SDG>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0</DEFAULT-VALUE>
									<MAX>4294967295</MAX>
									<MIN>0</MIN>
								</ECUC-INTEGER-PARAM-DEF>
							</PARAMETERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
					</CONTAINERS>
				</ECUC-MODULE-DEF>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="6c41a082-900a-4c56-9770-5ecf1d87cfe7">
					<SHORT-NAME>vBRS_Pre</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/vBRS</DEFINITION-REF>
					<CONTAINERS></CONTAINERS>
				</ECUC-MODULE-CONFIGURATION-VALUES>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="20bd2d98-c314-4887-bff4-25acc67bca4d">
					<SHORT-NAME>vBRS_Rec</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/vBRS</DEFINITION-REF>
					<CONTAINERS></CONTAINERS>
				</ECUC-MODULE-CONFIGURATION-VALUES>
			</ELEMENTS>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>