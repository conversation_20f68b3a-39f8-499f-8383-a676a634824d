<!-- Entities -->

<!ENTITY ti.ComponentType                       "ComponentType">
<!ENTITY ti.PortInterface                       "PortInterface">
<!ENTITY ti.PortInterfaceMap                    "PortInterfaceMap">
<!ENTITY ti.PortPrototype                       "PortPrototype">
<!ENTITY ti.DataElementPrototype                "DataElementPrototype">
<!ENTITY ti.OperationPrototype                  "OperationPrototype">
<!ENTITY ti.CalElementPrototype                 "CalElementPrototype">
<!ENTITY ti.Constant                            "Constant">
<!ENTITY ti.Composition                         "Composition">
<!ENTITY ti.ComponentPrototype                  "ComponentPrototype">
<!ENTITY ti.RunnableDataAccess                  "RunnableDataAccess">
<!ENTITY ti.OperationAccess                     "OperationAccess">
<!ENTITY ti.ExclusiveArea                       "ExclusiveArea">
<!ENTITY ti.ExclusiveAreaAccess                 "ExclusiveAreaAccess">
<!ENTITY ti.InterRunnableVariable               "InterRunnableVariable">
<!ENTITY ti.InterRunnableVariableAccess         "InterRunnableVariableAccess">
<!ENTITY ti.CalibrationParameter                "CalibrationParameter">
<!ENTITY ti.DataType                            "DataType">
<!ENTITY ti.ModeDclrGroupPrototype              "ModeDclrGroupPrototype">
<!ENTITY ti.ModeDclr                            "ModeDclr">
<!ENTITY ti.ModeSwitchAccess                    "ModeSwitchAccess">
<!ENTITY ti.ModeAccess                          "ModeAccess">
<!ENTITY ti.PerInstanceMemory                   "PerInstanceMemory">
<!ENTITY ti.NvMBlockNeed                        "NvMBlockNeed">
<!ENTITY ti.ComMUserNeed                        "ComMUserNeed">
<!ENTITY ti.FunctionInhibitionNeed              "FunctionInhibitionNeed">
<!ENTITY ti.CryptoNeed                          "CryptoNeed">
<!ENTITY ti.DltUserNeed                         "DltUserNeed">
<!ENTITY ti.StbMUserNeed                        "StbMUserNeed">
<!ENTITY ti.BswMNeed                            "BswMNeed">
<!ENTITY ti.DoIpPowerModeStatusNeed             "DoIpPowerModeStatusNeed">
<!ENTITY ti.DoIpRoutActConfirmNeed              "DoIpRoutActConfirmNeed">
<!ENTITY ti.DoIpRoutActAuthentNeed              "DoIpRoutActAuthentNeed">
<!ENTITY ti.DoIpGidSynchNeed                    "DoIpGidSynchNeed">
<!ENTITY ti.DoIpGidNeed                         "DoIpGidNeed">
<!ENTITY ti.DoIpActivationLineNeed              "DoIpActivationLineNeed">
<!ENTITY ti.DemNeed                             "DemNeed">
<!ENTITY ti.DiagEventNeed                       "DiagEventNeed">
<!ENTITY ti.DiagEventInfoNeed                   "DiagEventInfoNeed">
<!ENTITY ti.WarningIndicatorRequestedBitNeed    "WarningIndicatorRequestedBitNeed">
<!ENTITY ti.DiagOperationCycleNeed              "DiagOperationCycleNeed">
<!ENTITY ti.DiagEnableConditionNeed             "DiagEnableConditionNeed">
<!ENTITY ti.DTCStatusChangeNotificationNeed     "DTCStatusChangeNotificationNeed">
<!ENTITY ti.OBDRatioNeed                        "OBDRatioNeed">
<!ENTITY ti.OBDPIDNeed                          "OBDPIDNeed">
<!ENTITY ti.OBDInfoNeed                         "OBDInfoNeed">
<!ENTITY ti.DiagStorageConditionNeed            "DiagStorageConditionNeed">
<!ENTITY ti.EcuMUserNeed                        "EcuMUserNeed">
<!ENTITY ti.DiagCommunicationNeed               "DiagCommunicationNeed">
<!ENTITY ti.SupervisedEntityNeed                "SupervisedEntityNeed">
<!ENTITY ti.ECUSWComposition                    "ECUSWComposition">
<!ENTITY ti.Package                             "Package">
<!ENTITY ti.IncludedDataTypeSet                 "IncludedDataTypeSet">
<!ENTITY ti.IncludedDataType                    "IncludedDataType">
<!ENTITY ti.MappingSet                          "MappingSet">
<!ENTITY ti.ConstMappingSet                     "ConstMappingSet">
<!ENTITY ti.NvBlockDescriptor                   "NvBlockDescriptor">
<!ENTITY ti.NvBlockDataPrototype                "NvBlockDataPrototype">
<!ENTITY ti.RecordElement                       "RecordElement">
<!ENTITY ti.RecElementApp4                      "RecElementApp4">
<!ENTITY ti.RecElementImpl4                     "RecElementImpl4">
<!ENTITY ti.RoleBasedPortAssignment             "RoleBasedPortAssignment">
<!ENTITY ti.PortGroup                           "PortGroup">
<!ENTITY ti.PredefVariant                       "PredefVariant">
<!ENTITY ti.UnionElement                        "UnionElement">
<!ENTITY ti.TimerDefinition                     "TimerDefinition">

<!-- Elements -->

<!ELEMENT ComponentType (%C.DefAttr;,
                        CONTENT*,
                        ATTRLink*,
                        Runnable*,
                        (Application | SensorActuator | Service | Composition | Calibration | NvBlockSw ),
                        PortPrototype*,
                        FileEntry*,
                        GenAttrList?,
                        ExclusiveArea*,
                        InterRunnableVariable*,
                        CalibrationParameter*,
                        PerInstanceMemory*,
                        NvMBlockNeed*,
                        ComMUserNeed*,
                        FunctionInhibitionNeed*,
                        CryptoNeed*,
                        DltUserNeed*,
                        StbMUserNeed*,
                        BswMNeed*,
                        DoIpPowerModeStatusNeed*,
                        DoIpRoutActConfirmNeed*,
                        DoIpRoutActAuthentNeed*,
                        DoIpGidNeed*,
                        DoIpGidSynchNeed*,
                        DoIpActivationLineNeed*,
                        DemNeed*,
                        DiagEventNeed*,
                        DiagEventInfoNeed*,
                        WarningIndicatorRequestedBitNeed*,
                        DiagOperationCycleNeed*,
                        DiagEnableConditionNeed*,
                        DTCStatusChangeNotificationNeed*,
                        OBDRatioNeed*,
                        OBDPIDNeed*,
                        OBDInfoNeed*,
                        DiagStorageConditionNeed*,
                        EcuMUserNeed*,
                        DiagCommunicationNeed*,
                        SupervisedEntityNeed*,
                        IncludedDataTypeSet*,
                        DataTypeMapping*,
                        ConstantMapping*,
                        PortGroup*)>

<!ELEMENT PortPrototype (%C.DefAttr;, ATTRLink*, GenAttrList?, SenderReceiverComSpec*, ClientServerComSpec*, ModeSwitchComSpec*, CalibrationComSpec*, PortAPIOption?, PredefVariantLink* )>
<!ELEMENT PortGroup     (%C.DefAttr;, ATTRLink*, GenAttrList?, PortPrototypeLink*, PortGroupMapping* )>

<!ELEMENT Runnable                   (%C.DefAttr;, ATTRLink*, Trigger*, RunnableDataAccess*, GenAttrList?, OperationAccess*, ExclusiveAreaAccess*, InterRunnableVariableAccess*, ModeSwitchAccess*, ModeAccess*)>
<!ELEMENT Trigger                    (TimerDefinition?, TriggerInput?, DataSendCompletionTrigger?, OperationInvocationTrigger?, OperationCallReturnTrigger?, ModeSwitchTrigger?, ModeSwitchAckTrigger?, DataReceptionErrorTrigger?, RunnableInitTrigger?, ModeExecutionInhibitor*, BackgroundTrigger*)>
<!ELEMENT TimerDefinition            (%C.DefAttr;)>
<!ELEMENT RunnableInitTrigger        (%C.DefAttr;)>
<!ELEMENT BackgroundTrigger          (%C.DefAttr;)>
<!ELEMENT TriggerInput               (%C.DefAttr;, ATTRLink*)>
<!ELEMENT DataSendCompletionTrigger  (ATTRLink*, TriggerSource*)>
<!ELEMENT OperationInvocationTrigger (%C.DefAttr;, ATTRLink*)>
<!ELEMENT OperationCallReturnTrigger (ATTRLink*, TriggerSource*)>
<!ELEMENT ModeSwitchTrigger          (%C.DefAttr;, ATTRLink*)>
<!ELEMENT ModeSwitchAckTrigger       (ATTRLink*, TriggerSource*)>
<!ELEMENT DataReceptionErrorTrigger  (%C.DefAttr;, ATTRLink*)>
<!ELEMENT TriggerSource              (%C.DefAttr;, ATTRLink?)>
<!ELEMENT ModeExecutionInhibitor     (ATTRLink*)>
<!ELEMENT RunnableDataAccess         (%C.DefAttr;, ATTRLink*, WaitPoint*)>
<!ELEMENT OperationAccess            (%C.DefAttr;, ATTRLink*, WaitPoint*)>
<!ELEMENT ModeSwitchAccess           (%C.DefAttr;, ATTRLink*, WaitPoint*)>
<!ELEMENT ModeAccess                 (ATTRLink*)>
<!ELEMENT WaitPoint                  (%C.DefAttr;, ATTRLink?)>
<!ELEMENT ExclusiveArea              (%C.DefAttr;, GenAttrList?)>
<!ELEMENT ExclusiveAreaAccess        (ATTRLink*)>
<!ELEMENT InterRunnableVariable      (%C.DefAttr;, ATTRLink*,Constant*,GenAttrList?)>
<!ELEMENT InterRunnableVariableAccess (ATTRLink*)>
<!ELEMENT CalibrationParameter       (%C.DefAttr;, ATTRLink*, GenAttrList?,Constant*)>
<!ELEMENT PerInstanceMemory          (%C.DefAttr;, ATTRLink*, GenAttrList?)>
<!ELEMENT IncludedDataTypeSet        (IncludedDataType*)>
<!ELEMENT IncludedDataType           (ATTRLink?)>
<!ELEMENT DataTypeMapping            (ATTRLink?)>
<!ELEMENT ConstantMapping            (ATTRLink?)>
<!ELEMENT PortGroupMapping           (ATTRLink?)>

<!ELEMENT SenderReceiverComSpec (ATTRLink*,Constant*)>
<!ELEMENT ClientServerComSpec   (ATTRLink*)>
<!ELEMENT ModeSwitchComSpec     (ATTRLink*)>
<!ELEMENT CalibrationComSpec    (ATTRLink*,Constant*)>
<!ELEMENT PortAPIOption         (PortDAVal*)>
<!ELEMENT PortDAVal  (%C.DefAttr;, ATTRLink*)>

<!ELEMENT Application    (ATTRLink*)>
<!ELEMENT SensorActuator (ATTRLink*)>
<!ELEMENT Service        (ATTRLink*)>
<!ELEMENT Calibration    EMPTY>
<!ELEMENT Composition    (CONTENT*, ComponentPrototype*, PortConnector*, PortTerminator*)>
<!ELEMENT NvBlockSw      (ATTRLink*, NvBlockDescriptor*)>

<!ELEMENT NvBlockDescriptor               (%C.DefAttr;, ATTRLink*, GenAttrList?, NvBlockDataPrototype?, CalibrationParameter?, NvMBlockNeed?, NvDataMap*, Constant*, DataTypeMapping*)>
<!ELEMENT NvBlockDataPrototype            (%C.DefAttr;, ATTRLink*, GenAttrList?)>
<!ELEMENT NvDataMap                       (ATTRLink*)>

<!ELEMENT ComponentPrototype              (%C.DefAttr;, ATTRLink*, GenAttrList?)>
<!ELEMENT PortConnector                   (%C.DefAttr;, ATTRLink*, GenAttrList?, SenderReceiverConnectorComSpec*, ClientServerConnectorComSpec* )>
<!ELEMENT PortTerminator                  (DESC?, ATTRLink*, GenAttrList? )>
<!ELEMENT SenderReceiverConnectorComSpec  (ATTRLink*)>
<!ELEMENT ClientServerConnectorComSpec    EMPTY>

<!ELEMENT NvMBlockNeed                      (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT ComMUserNeed                      (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT FunctionInhibitionNeed            (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT DiagEventNeed                     (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*, FunctionInhibitionNeedLink*)>
<!ELEMENT DiagEventInfoNeed                 (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT WarningIndicatorRequestedBitNeed  (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT DiagOperationCycleNeed            (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT DiagEnableConditionNeed           (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT DTCStatusChangeNotificationNeed   (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT OBDRatioNeed                      (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*, FunctionInhibitionNeedLink*)>
<!ELEMENT OBDPIDNeed                        (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT OBDInfoNeed                       (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT DiagStorageConditionNeed          (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT DemNeed                           (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT EcuMUserNeed                      (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT DiagCommunicationNeed             (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT SupervisedEntityNeed              (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT CryptoNeed                        (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT DltUserNeed                       (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT StbMUserNeed                      (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT BswMNeed                          (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT DoIpPowerModeStatusNeed           (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT DoIpRoutActConfirmNeed            (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT DoIpRoutActAuthentNeed            (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT DoIpGidNeed                       (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT DoIpGidSynchNeed                  (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT DoIpActivationLineNeed            (%C.DefAttr;, ATTRLink*, GenAttrList?, RoleBasedPortAssignment*)>
<!ELEMENT RoleBasedPortAssignment           (%C.DefAttr;, ATTRLink*, GenAttrList?)>

<!ATTLIST RoleBasedPortAssignment %A.ChildItem;
    Role                CDATA ""
    PortPrototypeLink   NMTOKEN #FIXED "&ti.PortPrototype;"
>

<!ATTLIST NvBlockDescriptor   %A.ChildItem;
          RomInitLink         NMTOKEN #FIXED "&ti.Constant;"
          RamInitLink         NMTOKEN #FIXED "&ti.Constant;"
          TimingEventLink     NMTOKEN #FIXED "&ti.TimerDefinition;"
          SupportDirtyFlag            CDATA "0"
          SupportDirtyFlag_Specified  CDATA "0"
>

<!ATTLIST NvBlockDataPrototype %A.ChildItem;
          CalAccess           (RO | NA | RW | NS) "NA"
          DataTypeLink         NMTOKEN #FIXED "&ti.DataType;"
>

<!ATTLIST NvDataMap            %A.ChildItem;
          ArrayIndex           CDATA "-1"
          RecordElementLink    NMTOKEN #FIXED "&ti.RecordElement;"
          RecordElementApp4Link   NMTOKEN #FIXED "&ti.RecElementApp4;"
          RecordElementImpl4Link  NMTOKEN #FIXED "&ti.RecElementImpl4;"
          UnionElementLink        NMTOKEN #FIXED "&ti.UnionElement;"
          NvBlockDataPrototypeLink  NMTOKEN #FIXED "&ti.NvBlockDataPrototype;"
          WritePortLink        NMTOKEN #FIXED "&ti.PortPrototype;"
          WriteDEPLink         NMTOKEN #FIXED "&ti.DataElementPrototype;"
          ReadPortLink         NMTOKEN #FIXED "&ti.PortPrototype;"
          ReadDEPLink          NMTOKEN #FIXED "&ti.DataElementPrototype;"    
>

<!ATTLIST ComponentType %A.ConfigItem;
          SupportMI           CDATA "0"
          ImplCodeType        (SRC | OBJ) "SRC"
          BSWModule           CDATA ''
          ECUAbstraction      CDATA "0"
          ComplexDeviceDriver CDATA "0"
          PackageLink         NMTOKEN #FIXED "&ti.Package;"
          IsE2EProxy          CDATA "0"
>

<!ATTLIST PortPrototype %A.ChildItem;
          IsInput               CDATA "0"
          PortInterfaceLink     NMTOKEN #FIXED "&ti.PortInterface;"
>

<!-- Note: These values have to match the initial values in the tool dialogs -->

<!ATTLIST SenderReceiverComSpec %A.ChildItem;
   AliveTimeout             CDATA "0"
   ResynchroTime            CDATA "0"
   SupportInvalidate        CDATA "0"
   HandleInvalid            (NONE | KEEP | REPLACE) "NONE"
   QueueLength              CDATA "1"
   InitValueConstLink       NMTOKEN #FIXED "&ti.Constant;"
   DataElementPrototypeLink NMTOKEN #FIXED "&ti.DataElementPrototype;" 
   UsesTxAcknowledge        CDATA "0"
   TxAckTimeOut             CDATA "0"
   TxAckTimeOutUnit         (USEC | MSEC | SEC) "MSEC"
   RxFilter                 CDATA "0"
   UsesE2EProtection        CDATA "0"
   HandleNeverReceived      CDATA "0"
   EnableUpdate             CDATA "0"
   NvInitValueConstLink     NMTOKEN #FIXED "&ti.Constant;"
>

<!ATTLIST ClientServerComSpec %A.ChildItem;
   QueueLength              CDATA "1"
   OperationPrototypeLink   NMTOKEN #FIXED "&ti.OperationPrototype;" 
>

<!ATTLIST ModeSwitchComSpec %A.ChildItem;
   QueueLength                  CDATA "1"
   SwitchAckTimeOut             CDATA "0"
   SwitchAckTimeOutUnit         (USEC | MSEC | SEC) "MSEC"
   UsesModeSwitchAck            CDATA "0"
   ModeDclrGroupPrototypeLink   NMTOKEN #FIXED "&ti.ModeDclrGroupPrototype;"
   EnhancedModeAPI              CDATA "0"
>

<!ATTLIST CalibrationComSpec %A.ChildItem;
   InitValueConstLink       NMTOKEN #FIXED "&ti.Constant;"
   CalElementPrototypeLink  NMTOKEN #FIXED "&ti.CalElementPrototype;" 
>

<!ATTLIST Runnable      %A.ChildItem;
	CanBeInvokedConcurrently    CDATA "0"
	Symbol                      CDATA ""
  MinimumStartInterval CDATA       "0"
	MinimumStartIntervalUnit         (USEC | MSEC | SEC) "MSEC"
>

<!ATTLIST IncludedDataTypeSet %A.ChildItem;
    LiteralPrefix CDATA ""
>

<!ATTLIST IncludedDataType %A.ChildItem;
    DataTypeLink NMTOKEN #FIXED "&ti.DataType;"
>

<!ATTLIST DataTypeMapping %A.ChildItem;
    MappingSetLink NMTOKEN #FIXED "&ti.MappingSet;"
>

<!ATTLIST ConstantMapping %A.ChildItem;
    ConstMappingSetLink NMTOKEN #FIXED "&ti.ConstMappingSet;"
>

<!ATTLIST PortGroupMapping %A.ChildItem;
    PortGroupLink       NMTOKEN #FIXED "&ti.PortGroup;"
    CompPrototypeLink   NMTOKEN #FIXED "&ti.ComponentPrototype;"
>

<!ATTLIST Trigger          %A.ChildItem;>

<!ATTLIST TimerDefinition  %A.ChildItem; 
          CycleTime CDATA       "10"
	      CycleTimeUnit         (USEC | MSEC | SEC) "MSEC"
>

<!ATTLIST RunnableInitTrigger %A.ChildItem; 
>

<!ATTLIST BackgroundTrigger %A.ChildItem; 
>

<!ATTLIST TriggerInput  %A.ChildItem; 
	DataElementPrototypeLink NMTOKEN #FIXED "&ti.DataElementPrototype;"
	PortPrototypeLink        NMTOKEN #FIXED "&ti.PortPrototype;"
>

<!ATTLIST DataSendCompletionTrigger  %A.ChildItem; 
	DataElementPrototypeLink NMTOKEN #FIXED "&ti.DataElementPrototype;"
	PortPrototypeLink        NMTOKEN #FIXED "&ti.PortPrototype;"
>

<!ATTLIST OperationInvocationTrigger  %A.ChildItem; 
	OperationPrototypeLink   NMTOKEN #FIXED "&ti.OperationPrototype;"
	PortPrototypeLink        NMTOKEN #FIXED "&ti.PortPrototype;"
	AccessMode               ( NONE | TRIGGERED ) "TRIGGERED"
>

<!ATTLIST OperationCallReturnTrigger  %A.ChildItem; 
	OperationPrototypeLink   NMTOKEN #FIXED "&ti.OperationPrototype;"
	PortPrototypeLink        NMTOKEN #FIXED "&ti.PortPrototype;"
	AccessMode               ( NONE | TRIGGERED ) "TRIGGERED"
>

<!ATTLIST ModeSwitchTrigger %A.ChildItem;
    TriggerCondition                  ( OnEntry | OnExit | OnTransition ) "OnEntry"
    ModeDclrGroupPrototypeLink        NMTOKEN #FIXED "&ti.ModeDclrGroupPrototype;"
    ModeDclrLink                      NMTOKEN #FIXED "&ti.ModeDclr;"
    PortPrototypeLink                 NMTOKEN #FIXED "&ti.PortPrototype;"
    ModeDclrDestinationLink           NMTOKEN #FIXED "&ti.ModeDclr;"
>

<!ATTLIST ModeSwitchAckTrigger %A.ChildItem;
    ModeDclrGroupPrototypeLink        NMTOKEN #FIXED "&ti.ModeDclrGroupPrototype;"
    PortPrototypeLink                 NMTOKEN #FIXED "&ti.PortPrototype;"
>

<!ATTLIST DataReceptionErrorTrigger %A.ChildItem;
    DataElementPrototypeLink NMTOKEN #FIXED "&ti.DataElementPrototype;"
	PortPrototypeLink        NMTOKEN #FIXED "&ti.PortPrototype;"
>
<!ATTLIST TriggerSource %A.ChildItem;
  ModeSwitchAccessLink NMTOKEN #FIXED "&ti.ModeSwitchAccess;"
  OperationAccessLink  NMTOKEN #FIXED "&ti.OperationAccess;"
  DataAccessLink       NMTOKEN #FIXED "&ti.RunnableDataAccess;"
>

<!ATTLIST ModeExecutionInhibitor %A.ChildItem;
    ModeDclrGroupPrototypeLink        NMTOKEN #FIXED "&ti.ModeDclrGroupPrototype;"
    ModeDclrLink                      NMTOKEN #FIXED "&ti.ModeDclr;"
    PortPrototypeLink                 NMTOKEN #FIXED "&ti.PortPrototype;"
>

<!ATTLIST RunnableDataAccess  %A.ChildItem; 
	DataElementPrototypeLink NMTOKEN #FIXED                            "&ti.DataElementPrototype;"
	PortPrototypeLink        NMTOKEN #FIXED                            "&ti.PortPrototype;"
	AccessMode               ( NONE | DIRECT | BUFFERED | POLLING | WAITING | DIRECTBYVALUE ) "NONE"
	TimeOut                  CDATA                                     "0"
	TimeOutUnit              (USEC | MSEC | SEC)                       "MSEC"
	TxAcknowledgeMode        ( NONE | POLLING | WAITING )              "POLLING"
    EventName                CDATA                                     ""
    Role                     ( READ | WRITE )                          "READ"
>

<!ATTLIST OperationAccess  %A.ChildItem; 
	OperationPrototypeLink   NMTOKEN #FIXED                            "&ti.OperationPrototype;"
	PortPrototypeLink        NMTOKEN #FIXED                            "&ti.PortPrototype;"
	AccessMode               ( NONE | DIRECT | ASYNCHRON )             "NONE"
	CallReturnsMode          ( NONE | POLLING | WAITING )              "NONE"
	TimeOut                  CDATA                                     "0"
	TimeOutUnit              (USEC | MSEC | SEC)                       "MSEC"
	EventName                CDATA                                     ""
    ResultPointName          CDATA                                     ""
>

<!ATTLIST ModeSwitchAccess
          %A.ChildItem;
          PortPrototypeLink       NMTOKEN #FIXED "&ti.PortPrototype;"
          RefMDGroupPrototypeLink NMTOKEN #FIXED "&ti.ModeDclrGroupPrototype;"
          ModeSwitchAckMode       (POLLING | WAITING | NONE)           "POLLING"
          AckOnly                 CDATA                         "0"
          EventName               CDATA                         ""
>

<!ATTLIST ModeAccess
          %A.ChildItem;
          PortPrototypeLink       NMTOKEN #FIXED "&ti.PortPrototype;"
          RefMDGroupPrototypeLink NMTOKEN #FIXED "&ti.ModeDclrGroupPrototype;"
          Role                    ( ReadReceived | ReadSent )  "ReadReceived"
>

<!ATTLIST WaitPoint %A.ChildItem;
  ModeSwitchAccessLink NMTOKEN #FIXED "&ti.ModeSwitchAccess;"
  OperationAccessLink  NMTOKEN #FIXED "&ti.OperationAccess;"
  DataAccessLink       NMTOKEN #FIXED "&ti.RunnableDataAccess;"
>


<!ATTLIST Application    %A.ChildItem;
          RealizationPackageLink         NMTOKEN #FIXED "&ti.Package;"
          ImplPackageLink         NMTOKEN #FIXED "&ti.Package;"
          RealizationName CDATA ''
          ImplName        CDATA ''
          AutoARChildHdlg CDATA "-1"
          SymbolProps     CDATA '' 
          SymbolPropsName CDATA ''
          CodeDescriptorName CDATA ''
          ImplUUID        CDATA ''
          RealizationUUID CDATA ''
>
<!ATTLIST SensorActuator %A.ChildItem;
          RealizationPackageLink         NMTOKEN #FIXED "&ti.Package;"
          ImplPackageLink         NMTOKEN #FIXED "&ti.Package;"
          RealizationName CDATA ''
          ImplName        CDATA ''
          AutoARChildHdlg CDATA "-1"
          SymbolProps     CDATA ''
          SymbolPropsName CDATA '' 
          CodeDescriptorName CDATA ''
          ImplUUID        CDATA ''
          RealizationUUID CDATA ''
>
<!ATTLIST Service        %A.ChildItem;
          RealizationPackageLink         NMTOKEN #FIXED "&ti.Package;"
          ImplPackageLink         NMTOKEN #FIXED "&ti.Package;"
          RealizationName CDATA ''
          ImplName        CDATA ''
          AutoARChildHdlg CDATA "-1"
          SymbolProps     CDATA ''
          SymbolPropsName CDATA '' 
          CodeDescriptorName CDATA ''
          ImplUUID        CDATA ''
          RealizationUUID CDATA ''
>

<!ATTLIST Calibration    %A.ChildItem;>
<!ATTLIST Composition    %A.ChildItem;>

<!ATTLIST NvBlockSw      %A.ChildItem;
          RealizationPackageLink         NMTOKEN #FIXED "&ti.Package;"
          ImplPackageLink         NMTOKEN #FIXED "&ti.Package;"
          RealizationName CDATA ''
          ImplName        CDATA ''
          AutoARChildHdlg CDATA "-1"
          SymbolProps     CDATA '' 
          SymbolPropsName CDATA ''
          CodeDescriptorName CDATA ''
          ImplUUID        CDATA ''
          RealizationUUID CDATA ''
>

<!ATTLIST ComponentPrototype %A.ChildItem;
   ComponentTypeLink      NMTOKEN #FIXED "&ti.ComponentType;"
>

<!ATTLIST PortConnector %A.ChildItem;
   ReceiverPortLink             NMTOKEN #FIXED "&ti.PortPrototype;"
   TransmitterPortLink          NMTOKEN #FIXED "&ti.PortPrototype;"
   ReceiveCompPrototypeLink     NMTOKEN #FIXED "&ti.ComponentPrototype;"
   ReceiveCompTypeLink          NMTOKEN #FIXED "&ti.ComponentType;"
   TransmitCompPrototypeLink    NMTOKEN #FIXED "&ti.ComponentPrototype;"
   TransmitCompTypeLink         NMTOKEN #FIXED "&ti.ComponentType;"
   PortInterfaceMapLink         NMTOKEN #FIXED "&ti.PortInterfaceMap;"
>

<!ATTLIST PortTerminator %A.ChildItem;
   PortLink              NMTOKEN #FIXED "&ti.PortPrototype;"
   CompPrototypeLink     NMTOKEN #FIXED "&ti.ComponentPrototype;"
   CompTypeLink          NMTOKEN #FIXED "&ti.ComponentType;"
   ECUSWCompLink         NMTOKEN #FIXED "&ti.ECUSWComposition;"
>
<!ATTLIST SenderReceiverConnectorComSpec %A.ChildItem;
   MaxResponseTime           CDATA "0"
   MinResponseTime           CDATA "0"
   MaxResponseTimeUnit			(USEC|MSEC|SEC)       "MSEC"
   MinResponseTimeUnit	    (USEC|MSEC|SEC)       "MSEC"
   MaxJitter                 CDATA "0"
   JitterTimeUnit			(USEC|MSEC|SEC)       "MSEC"
   DataElementPrototypeLink  NMTOKEN #FIXED "&ti.DataElementPrototype;"
>

<!ATTLIST ExclusiveArea      %A.ChildItem; >

<!ATTLIST ExclusiveAreaAccess  %A.ChildItem; 
	ExclusiveAreaLink       NMTOKEN #FIXED "&ti.ExclusiveArea;"
	AccessMode              ( RUNS_IN | CAN_ENTER ) "CAN_ENTER"
>

<!ATTLIST InterRunnableVariable %A.ChildItem; 
	DataTypeLink              NMTOKEN #FIXED "&ti.DataType;"
    InitValueConstLink        NMTOKEN #FIXED "&ti.Constant;"
	ComApproachType          ( DIRECT | BUFFERED ) "DIRECT"
    CalAccess                (RO | NA | RW | NS) "NA"
>

<!ATTLIST InterRunnableVariableAccess  %A.ChildItem; 
	InterRunnableVariableLink NMTOKEN #FIXED "&ti.InterRunnableVariable;"
	AccessMode              ( READ | WRITE | READWRITE ) "READ"
>

<!ATTLIST CalibrationParameter %A.ChildItem; 
	DataTypeLink              NMTOKEN #FIXED "&ti.DataType;"
  InitValueConstLink        NMTOKEN #FIXED "&ti.Constant;"
	Scope                     ( SHARED | PER_INSTANCE ) "SHARED"
  CalAccess                 (RO | NA | RW | NS) "RW"
>

<!ATTLIST PerInstanceMemory %A.ChildItem;
    UseDataTypeReference    CDATA          "1"
	  DataTypeLink            NMTOKEN #FIXED "&ti.DataType;"
    Type                    CDATA          ""
    TypeDefinition          CDATA          ""
    NvMBlockNeedLink        NMTOKEN #FIXED "&ti.NvMBlockNeed;"
    CalAccess                 (RO | NA | RW | NS) "RW"
>

<!ATTLIST PortAPIOption  %A.ChildItem;
    UseIndirectAPI            CDATA "0"
    EnableTakeAddress         CDATA "0"
>

<!ATTLIST PortDAVal %A.ChildItem; 
	DataTypeLink              NMTOKEN #FIXED "&ti.DataType;"
    Value                     CDATA ''
    OrderIndex                CDATA "-1"
>

<!ATTLIST NvMBlockNeed %A.ChildItem;
  DataSets                        CDATA "0"
  DataSets_Specified              CDATA "-1"
  NVMReadOnly                     CDATA "0"
  NVMReadOnly_Specified           CDATA "0"
  Reliability                     ( LOW | MEDIUM | HIGH ) "LOW"
  Reliability_Specified           CDATA "-1"
  ResistantToChangedSW            CDATA "0"
  ResistantToChangedSW_Specified  CDATA "0"
  RestoreAtStart                  CDATA "0"
  RestoreAtStart_Specified        CDATA "-1"
  WriteOnlyOnce                   CDATA "0"
  WriteOnlyOnce_Specified         CDATA "0"
  WritingFrequency                CDATA "0"
  WritingFrequency_Specified      CDATA "0"
  WritingPriority                 ( LOW | MEDIUM | HIGH ) "LOW"
  WritingPriority_Specified       CDATA "0"
  DefaultValueLink                NMTOKEN #FIXED "&ti.CalibrationParameter;"
  DefaultValue_Specified          CDATA "0"
  BlockSize                       CDATA "0"
  ServiceNeedName                 CDATA ''
  AutoServiceNeedNameHdlg         CDATA "-1"
  StoreAtShutdown                 CDATA "0"
  StoreAtShutdown_Specified       CDATA "-1"
  RAMBlock_Specified              CDATA "0"
  RamBlockStatusCtrl              ( NVM | API ) "API"
  RamBlockStatusCtrl_Specified    CDATA "-1"
  CalculateRAMBlockCRC            CDATA "0"
  CalculateRAMBlockCRC_Specified  CDATA "0"
  CheckStaticBlockId              CDATA "0"
  CheckStaticBlockId_Specified    CDATA "0"
  NumberOfROMBlocks               CDATA "0"
  NumberOfROMBlocks_Specified     CDATA "0"
  RAMBlockStatusControl           ( API | NVRAMMANAGER) "API"
  StoreAtShutdown                 CDATA "0"
  WriteVerification               CDATA "0"
  WriteVerification_Specified     CDATA "0"
  PortGroupLink                   NMTOKEN #FIXED "&ti.PortGroup;"
  CyclicWritingPeriod             CDATA "0"
  CyclicWritingPeriodUnit         (USEC | MSEC | SEC) "MSEC"
  CyclicWritingPeriod_Specified   CDATA "0"
  StoreCyclic                     CDATA "0"
  StoreCyclic_Specified           CDATA "0"
  StoreEmergency                  CDATA "0"
  StoreEmergency_Specified        CDATA "0"
  StoreImmediate                  CDATA "0"
  StoreImmediate_Specified        CDATA "0"
  UseAutoValidationAtShutDown     CDATA "0"
  UseAutoValidationAtShutDown_Specified CDATA "0"
  UseCRCCompMechanism             CDATA "0"
  UseCRCCompMechanism_Specified   CDATA "0"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST ComMUserNeed %A.ChildItem;
  MaxCommMode               ( NO | SILENT | FULL ) "NO"
  ServiceNeedName           CDATA ""
  AutoServiceNeedNameHdlg   CDATA "-1"
  PortGroupLink             NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST SupervisedEntityNeed %A.ChildItem;
  ActiveAtStart             CDATA "0"
  EnableDeactivation        CDATA "0"
  ExpectedAliveCycle        CDATA "0"
  MaxAliveCycle             CDATA "0"
  MinAliveCycle             CDATA "0"
  ToleratedFailedCycles     CDATA "0"
  ServiceNeedName           CDATA ""
  AutoServiceNeedNameHdlg   CDATA "-1"
  PortGroupLink             NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST EcuMUserNeed %A.ChildItem;
  ServiceNeedName           CDATA ""
  AutoServiceNeedNameHdlg   CDATA "-1"
  PortGroupLink             NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST DiagCommunicationNeed %A.ChildItem;
  ServiceNeedName               CDATA ""
  AutoServiceNeedNameHdlg       CDATA "-1"
  SecurityAccessLevel           CDATA "0"
  SecurityAccessLevel_Specified CDATA "-1"
  PortGroupLink                 NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST FunctionInhibitionNeed %A.ChildItem;
  ServiceNeedName           CDATA ""
  AutoServiceNeedNameHdlg   CDATA "-1"
  PortGroupLink             NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST DiagEventNeed %A.ChildItem;
  ServiceNeedName                 CDATA ""
  AutoServiceNeedNameHdlg         CDATA "-1"
  SecurityAccessLevel             CDATA "0"
  SecurityAccessLevel_Specified   CDATA "-1"
  DTCKind                         ( EMISSION_RELATED | NON_EMISSION_RELATED ) "EMISSION_RELATED"
  DTCKind_Specified               CDATA "-1"
  DTCNumber                       CDATA "0"
  DTCNumber_Specified             CDATA "-1"
  OBDDTCNumber                    CDATA "0"
  OBDDTCNumber_Specified          CDATA "-1"
  UDSDTCNumber                    CDATA "0"
  UDSDTCNumber_Specified          CDATA "-1"
  ConsiderPTOStatus               CDATA "0"
  ConsiderPTOStatus_Specified     CDATA "-1"
  PrimaryInhibitionFidLink        NMTOKEN #FIXED "&ti.FunctionInhibitionNeed;"
  PrimaryInhibitionFid_Specified  CDATA "-1"
  SecondaryFIDs_Specified         CDATA "-1"
  PortGroupLink                   NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST DiagEventInfoNeed %A.ChildItem;
  ServiceNeedName               CDATA ""
  AutoServiceNeedNameHdlg       CDATA "-1"
  SecurityAccessLevel           CDATA "0"
  SecurityAccessLevel_Specified CDATA "-1"
  DTCKind                       ( EMISSION_RELATED | NON_EMISSION_RELATED ) "EMISSION_RELATED"
  DTCKind_Specified             CDATA "-1"
  DTCNumber                     CDATA "0"
  DTCNumber_Specified           CDATA "-1"
  OBDDTCNumber                  CDATA "0"
  OBDDTCNumber_Specified        CDATA "-1"
  UDSDTCNumber                  CDATA "0"
  UDSDTCNumber_Specified        CDATA "-1"
  PortGroupLink                 NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST WarningIndicatorRequestedBitNeed %A.ChildItem;
  ServiceNeedName               CDATA ""
  AutoServiceNeedNameHdlg       CDATA "-1"
  SecurityAccessLevel           CDATA "0"
  SecurityAccessLevel_Specified CDATA "-1"
  PortGroupLink                 NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST DiagOperationCycleNeed %A.ChildItem;
  ServiceNeedName               CDATA ""
  AutoServiceNeedNameHdlg       CDATA "-1"
  SecurityAccessLevel           CDATA "0"
  SecurityAccessLevel_Specified CDATA "-1"
  OperationCycle                (IGNITION | OBD_DCY | WARM_UP | POWER | TIME | OTHER) "OTHER"
  AutoEnd                       CDATA "0"
  AutoStart                     CDATA "0"
  PortGroupLink                 NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST DiagEnableConditionNeed %A.ChildItem;
  ServiceNeedName               CDATA ""
  AutoServiceNeedNameHdlg       CDATA "-1"
  SecurityAccessLevel           CDATA "0"
  SecurityAccessLevel_Specified CDATA "-1"
  InitialStatus                 (ENABLED | DISABLED ) "ENABLED"
  PortGroupLink                 NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST DTCStatusChangeNotificationNeed %A.ChildItem;
  ServiceNeedName               CDATA ""
  AutoServiceNeedNameHdlg       CDATA "-1"
  SecurityAccessLevel           CDATA "0"
  SecurityAccessLevel_Specified CDATA "-1"
  DTCFormatType                 (OBD | UDS | J1939 ) "OBD"
  PortGroupLink                 NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST OBDRatioNeed %A.ChildItem;
  ServiceNeedName                   CDATA ""
  AutoServiceNeedNameHdlg           CDATA "-1"
  SecurityAccessLevel               CDATA "0"
  SecurityAccessLevel_Specified     CDATA "-1"
  ConnectionType                    (API_USE | OBSERVER) "API_USE"
  IumprGroup                        CDATA ""
  RateBasedMonitoredEventLink       NMTOKEN #FIXED "&ti.DiagEventNeed;"
  RateBasedMonitoredEvent_Specified CDATA "-1"
  UsedFidLink                       NMTOKEN #FIXED "&ti.FunctionInhibitionNeed;"
  UsedFid_Specified                 CDATA "-1"
  UsedSecondaryFIDs_Specified       CDATA "-1"
  PortGroupLink                     NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST OBDPIDNeed %A.ChildItem;
  ServiceNeedName               CDATA ""
  AutoServiceNeedNameHdlg       CDATA "-1"
  SecurityAccessLevel           CDATA "0"
  SecurityAccessLevel_Specified CDATA "-1"
  DataLength                    CDATA "0"
  DataLength_Specified          CDATA "-1"
  ParameterID                   CDATA "0"
  Standard                      CDATA "0"
  PortGroupLink                 NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST OBDInfoNeed %A.ChildItem;
  ServiceNeedName               CDATA ""
  AutoServiceNeedNameHdlg       CDATA "-1"
  SecurityAccessLevel           CDATA "0"
  SecurityAccessLevel_Specified CDATA "-1"
  DataLength                    CDATA "0"
  DataLength_Specified          CDATA "-1"
  InfoType                      CDATA "0"
  PortGroupLink                 NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST DiagStorageConditionNeed %A.ChildItem;
  ServiceNeedName                 CDATA ""
  AutoServiceNeedNameHdlg         CDATA "-1"
  SecurityAccessLevel             CDATA "0"
  SecurityAccessLevel_Specified   CDATA "-1"
  InitialStatus                   (ENABLED | DISABLED ) "ENABLED"
  PortGroupLink                   NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST DemNeed %A.ChildItem;
  ServiceNeedName               CDATA ""
  AutoServiceNeedNameHdlg       CDATA "-1"
  SecurityAccessLevel           CDATA "0"
  SecurityAccessLevel_Specified CDATA "-1"
  PortGroupLink                 NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST CryptoNeed %A.ChildItem;
  MaxKeyLength              CDATA "0"
  MaxKeyLength_Specified    CDATA "-1"
  ServiceNeedName           CDATA ""
  AutoServiceNeedNameHdlg   CDATA "-1"
  PortGroupLink             NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST DltUserNeed %A.ChildItem;
  ServiceNeedName           CDATA ""
  AutoServiceNeedNameHdlg   CDATA "-1"
  PortGroupLink             NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST StbMUserNeed %A.ChildItem;
  ServiceNeedName           CDATA ""
  AutoServiceNeedNameHdlg   CDATA "-1"
  
  PortGroupLink             NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST BswMNeed %A.ChildItem;
  ServiceNeedName           CDATA ""
  AutoServiceNeedNameHdlg   CDATA "-1"
  
  PortGroupLink             NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST DoIpPowerModeStatusNeed %A.ChildItem;
  ServiceNeedName           CDATA ""
  AutoServiceNeedNameHdlg   CDATA "-1"
  PortGroupLink             NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST DoIpRoutActConfirmNeed %A.ChildItem;
  ServiceNeedName               CDATA ""
  AutoServiceNeedNameHdlg       CDATA "-1"
  DataLengthRequest             CDATA "0"
  DataLengthRequest_Specified   CDATA "-1"
  DataLengthResponse            CDATA "0"
  DataLengthResponse_Specified  CDATA "-1"
  ActivationType                CDATA ""
  PortGroupLink                 NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST DoIpRoutActAuthentNeed %A.ChildItem;
  ServiceNeedName               CDATA ""
  AutoServiceNeedNameHdlg       CDATA "-1"
  DataLengthRequest             CDATA "0"
  DataLengthRequest_Specified   CDATA "-1"
  DataLengthResponse            CDATA "0"
  DataLengthResponse_Specified  CDATA "-1"
  ActivationType                CDATA ""
  PortGroupLink                 NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST DoIpGidNeed %A.ChildItem;
  ServiceNeedName           CDATA ""
  AutoServiceNeedNameHdlg   CDATA "-1"
  PortGroupLink             NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST DoIpGidSynchNeed %A.ChildItem;
  ServiceNeedName           CDATA ""
  AutoServiceNeedNameHdlg   CDATA "-1"
  PortGroupLink             NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!ATTLIST DoIpActivationLineNeed %A.ChildItem;
  ServiceNeedName           CDATA ""
  AutoServiceNeedNameHdlg   CDATA "-1"
  PortGroupLink             NMTOKEN #FIXED "&ti.PortGroup;"
  SwcServiceDependencyUUID        CDATA ""
>

<!-- Links -->

<!ELEMENT ComponentTypeLink EMPTY>

<!ATTLIST ComponentTypeLink 
          %A.LinkGUID;
          %A.LinkChildGUID;
          %A.LinkVersion;
          %A.LinkType; "&ti.ComponentType;"
>
          
