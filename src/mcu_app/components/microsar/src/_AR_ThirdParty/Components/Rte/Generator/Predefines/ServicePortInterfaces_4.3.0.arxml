<?xml version="1.0" encoding="utf-8"?>
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-3-0.xsd" xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<AR-PACKAGES>
		<AR-PACKAGE>
			<SHORT-NAME>Predefined_DEV</SHORT-NAME>
			<AR-PACKAGES>
				<AR-PACKAGE>
					<SHORT-NAME>DataConstraints</SHORT-NAME>
					<ELEMENTS>
						<DATA-CONSTR>
							<SHORT-NAME>Dem_UdsStatusByteType</SHORT-NAME>
							<DATA-CONSTR-RULES>
								<DATA-CONSTR-RULE>
									<INTERNAL-CONSTRS>
										<LOWER-LIMIT>0x00</LOWER-LIMIT>
										<UPPER-LIMIT>0xFF</UPPER-LIMIT>
									</INTERNAL-CONSTRS>
								</DATA-CONSTR-RULE>
							</DATA-CONSTR-RULES>
						</DATA-CONSTR>
					</ELEMENTS>
				</AR-PACKAGE>
				<AR-PACKAGE>
					<SHORT-NAME>CompuMethods</SHORT-NAME>
					<ELEMENTS>
						<COMPU-METHOD UUID="F2570AA0-D6E5-4217-80F7-5F60B8778133">
							<SHORT-NAME>NvM_RequestResultType</SHORT-NAME>
							<CATEGORY>TEXTTABLE</CATEGORY>
							<COMPU-INTERNAL-TO-PHYS>
								<COMPU-SCALES>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_REQ_OK</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_REQ_OK</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_REQ_NOT_OK</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_REQ_NOT_OK</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_REQ_PENDING</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_REQ_PENDING</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_REQ_INTEGRITY_FAILED</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">3</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">3</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_REQ_INTEGRITY_FAILED</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_REQ_BLOCK_SKIPPED</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">4</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">4</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_REQ_BLOCK_SKIPPED</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_REQ_NV_INVALIDATED</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">5</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">5</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_REQ_NV_INVALIDATED</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_REQ_CANCELED</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">6</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">6</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_REQ_CANCELED</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_REQ_REDUNDANCY_FAILED</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">7</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">7</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_REQ_REDUNDANCY_FAILED</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_REQ_RESTORED_FROM_ROM</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">8</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">8</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_REQ_RESTORED_FROM_ROM</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
								</COMPU-SCALES>
							</COMPU-INTERNAL-TO-PHYS>
						</COMPU-METHOD>
						<COMPU-METHOD UUID="007B24D3-5C46-4986-B3CA-31D71FEDD510">
							<SHORT-NAME>NvM_ServiceIdType</SHORT-NAME>
							<CATEGORY>TEXTTABLE</CATEGORY>
							<COMPU-INTERNAL-TO-PHYS>
								<COMPU-SCALES>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_READ_BLOCK</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">6</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">6</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_READ_BLOCK</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_WRITE_BLOCK</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">7</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">7</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_WRITE_BLOCK</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_RESTORE_BLOCK_DEFAULTS</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">8</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">8</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_RESTORE_BLOCK_DEFAULTS</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_ERASE_BLOCK</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">9</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">9</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_ERASE_BLOCK</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_INVALIDATE_NV_BLOCK</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">11</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">11</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_INVALIDATE_NV_BLOCK</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_READ_ALL</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">12</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">12</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_READ_ALL</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
								</COMPU-SCALES>
							</COMPU-INTERNAL-TO-PHYS>
						</COMPU-METHOD>
						<COMPU-METHOD UUID="{AF35D1D4-4362-4055-B014-780FA6F9E75B}">
							<SHORT-NAME>Dem_UdsStatusByteType</SHORT-NAME>
							<CATEGORY>BITFIELD_TEXTTABLE</CATEGORY>
							<COMPU-INTERNAL-TO-PHYS>
								<COMPU-SCALES>
									<COMPU-SCALE>
										<SHORT-LABEL>DEM_UDS_STATUS_TF</SHORT-LABEL>
										<SYMBOL>DEM_UDS_STATUS_TF</SYMBOL>
										<MASK>1</MASK>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>DEM_UDS_STATUS_TFTOC</SHORT-LABEL>
										<SYMBOL>DEM_UDS_STATUS_TFTOC</SYMBOL>
										<MASK>2</MASK>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>DEM_UDS_STATUS_PDTC</SHORT-LABEL>
										<SYMBOL>DEM_UDS_STATUS_PDTC</SYMBOL>
										<MASK>4</MASK>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">4</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">4</UPPER-LIMIT>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>DEM_UDS_STATUS_CDTC</SHORT-LABEL>
										<SYMBOL>DEM_UDS_STATUS_CDTC</SYMBOL>
										<MASK>8</MASK>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">8</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">8</UPPER-LIMIT>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>DEM_UDS_STATUS_TNCSLC</SHORT-LABEL>
										<SYMBOL>DEM_UDS_STATUS_TNCSLC</SYMBOL>
										<MASK>16</MASK>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">16</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">16</UPPER-LIMIT>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>DEM_UDS_STATUS_TFSLC</SHORT-LABEL>
										<SYMBOL>DEM_UDS_STATUS_TFSLC</SYMBOL>
										<MASK>32</MASK>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">32</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">32</UPPER-LIMIT>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>DEM_UDS_STATUS_TNCTOC</SHORT-LABEL>
										<SYMBOL>DEM_UDS_STATUS_TNCTOC</SYMBOL>
										<MASK>64</MASK>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">64</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">64</UPPER-LIMIT>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>DEM_UDS_STATUS_WIR</SHORT-LABEL>
										<SYMBOL>DEM_UDS_STATUS_WIR</SYMBOL>
										<MASK>128</MASK>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">128</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">128</UPPER-LIMIT>
									</COMPU-SCALE>
								</COMPU-SCALES>
							</COMPU-INTERNAL-TO-PHYS>
						</COMPU-METHOD>
					</ELEMENTS>
				</AR-PACKAGE>
				<AR-PACKAGE>
					<SHORT-NAME>ImplementationDataTypes</SHORT-NAME>
					<ELEMENTS>
						<IMPLEMENTATION-DATA-TYPE UUID="91037BEA-8260-4D81-AF92-D77F36795EEF">
							<SHORT-NAME>NvM_RequestResultType</SHORT-NAME>
							<CATEGORY>TYPE_REFERENCE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<COMPU-METHOD-REF DEST="COMPU-METHOD">/Predefined_DEV/CompuMethods/NvM_RequestResultType</COMPU-METHOD-REF>
										<IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE UUID="E8567FAC-3DF7-4B9A-AE12-961DD3DAE8F3">
							<SHORT-NAME>NvM_ServiceIdType</SHORT-NAME>
							<CATEGORY>TYPE_REFERENCE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<COMPU-METHOD-REF DEST="COMPU-METHOD">/Predefined_DEV/CompuMethods/NvM_ServiceIdType</COMPU-METHOD-REF>
										<IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE UUID="{BFD05982-482E-4C42-9D53-AEC2E5E9005D}">
							<SHORT-NAME>Dem_UdsStatusByteType</SHORT-NAME>
							<CATEGORY>TYPE_REFERENCE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<COMPU-METHOD-REF DEST="COMPU-METHOD">/Predefined_DEV/CompuMethods/Dem_UdsStatusByteType</COMPU-METHOD-REF>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/Predefined_DEV/DataConstraints/Dem_UdsStatusByteType</DATA-CONSTR-REF>
										<IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</IMPLEMENTATION-DATA-TYPE>
					</ELEMENTS>
				</AR-PACKAGE>
				<AR-PACKAGE>
					<SHORT-NAME>PortInterfaces</SHORT-NAME>
					<ELEMENTS>
						<CLIENT-SERVER-INTERFACE UUID="5C21FAD0-38A7-4388-9C0A-FA015136641D">
							<SHORT-NAME>NvMAdministration</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="59958010-C247-46E2-869A-3CCC4950B3D2">
									<SHORT-NAME>SetBlockProtection</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="8DAC3D9B-C101-489D-9C83-4706B9BF2268">
											<SHORT-NAME>ProtectionEnabled</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMAdministration/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="74CED587-616F-4D06-917E-3F53B5BEEA2D">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="B741FB9B-3C0D-42B0-AF56-EDE07FBDB8E9">
							<SHORT-NAME>NvMNotifyInitBlock</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="9FF7D803-9528-451F-9794-525F8978CF8A">
									<SHORT-NAME>InitBlock</SHORT-NAME>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="B8555106-E469-4DD0-BAF6-679623E34240">
							<SHORT-NAME>NvMNotifyJobFinished</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="7603180A-A831-4627-AD2C-D6438D4D393A">
									<SHORT-NAME>JobFinished</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="29AB48A0-6ED3-4286-84BB-E8E220D872E0">
											<SHORT-NAME>ServiceId</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_ServiceIdType</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
										<ARGUMENT-DATA-PROTOTYPE UUID="70EF7279-4CF8-4044-A7E1-F8AA6F234C0C">
											<SHORT-NAME>JobResult</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="89E0F11F-93F5-487E-8594-75FEEF4E2A90">
							<SHORT-NAME>NvMService</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="36C974FB-6331-46D1-BDDD-B7E92DEC1B76">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="1BF1A5D6-0A21-4245-8364-9666EECB3900">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="A7AFD599-1333-460B-8602-E5B8D38BC232">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="011A03C8-6F6F-41A8-8B80-DB13A7142A89">
							<SHORT-NAME>NvMService_AC2</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="769198CB-5577-4FE3-AF21-FAD29F443525">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="2841F06B-C744-4215-B24B-C2998C822ABA">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="06631A5C-21C5-4058-829F-DC93D10C926A">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="F174CDB4-C07C-4E93-B577-AC5AFCB8A1B1">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="95BCC8D3-2739-4D19-AEC8-907F688C167F">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="F91F3A7B-583E-468E-B788-75CC7ED234E0">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="955CA27E-4944-411C-BAAE-69B1FC02E735">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="2151E2A0-EB99-49FA-A445-70DB43DC732F">
							<SHORT-NAME>NvMService_AC2_Defs</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="177B2113-7D73-4102-9738-7072581FED1B">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="3B7FC79A-7F1F-4088-9F32-AD8794C14363">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="2C51AEB0-99D4-435B-B007-064BC21181AA">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="E335C82D-864C-4803-BC40-CC718296B615">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="6A871911-DF93-48CF-9B23-520562FA90EA">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="24556D6D-ED13-48A6-AB90-33780E53ED92">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="02B21A5D-8DC6-485F-8211-8A725D180AE3">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="9F1FB285-12C9-4CC6-A6F9-97C8D54354F1">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="F57D208E-3B24-44B1-85E2-6DE677CE2E4C">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="B1B4B02A-3408-4F5D-97C8-592BA3B79EC1">
							<SHORT-NAME>NvMService_AC2_DS</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="21ABED64-CEFE-4FF1-9D15-0CAC43BEAEC1">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="AFD513C0-2D85-42A5-9F47-D91F6EEEF574">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="08DFF7BA-441C-4FDA-BCE9-047D03C82489">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="58762F23-4FC8-40E3-882C-E0D78E8D2C8D">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="A7796B0E-7299-43E5-87F9-8E0E9A78B88F">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="E60D3EA2-FB16-446B-8221-96488EAF4DEB">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="EF5345C6-85D9-4E57-AF3B-CEA55FF72FCA">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="C6491CB2-A0DF-4A4B-98B9-FA1905C47762">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="CDC75609-83D3-4833-95E8-E4362D5F992E">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="B1D99385-8155-4D95-911F-3D179FF3AD2E">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="AE7C0F3C-1FF2-4885-AC24-720F2C4803CC">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="4C08DF52-BB40-4FB1-ABCD-288C185CFBC7">
							<SHORT-NAME>NvMService_AC2_DS_Defs</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="D9E45B66-49BE-495D-92A5-3D1B27A51E9C">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="4348A7F5-8B1D-481A-9E50-98B1C39E51BE">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="757CAC85-3C7C-4641-9A4B-21D99AF85CE6">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="13AF00E9-AA4E-4166-BDD5-C5E236477097">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="95068122-95CD-450F-85F1-8DD2C8CBC8BC">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="7E117C7B-A452-43BD-B50C-942C53B1073D">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="39FBC4C6-2118-46CC-A17E-3E81B5FE7665">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="3A9A2526-5884-4765-92DA-87918E8FEE93">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="1BB98DE4-1EF9-4BB4-BBCB-B5608EB23467">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="47AEE42A-B5A3-4324-91BC-635EC9316B99">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="2F0430D3-EAEA-40FB-B775-FAD0F4098567">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="B50A29E9-95A3-4306-9A81-F12B1D8895BB">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="6972E423-93E3-412A-B274-623A39FD4885">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="578E0238-4848-48B1-AE0B-4C9C5C153A10">
							<SHORT-NAME>NvMService_AC2_SRBS</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="105B9280-D778-4B8C-8EBC-B6E032B73420">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="F91E2003-CF7E-47A0-A2E1-621DF116ADFC">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="600371C0-30EF-4416-98A9-BA7EA3EFF6D1">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="44AAAAAD-C3C9-4B14-8B75-A7E8134625FD">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="19B5D614-855D-49DD-9438-7A22B0B56CBB">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="E9B2A4AB-9873-46A2-97FF-385A3864B29E">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="545A5B31-F576-4C8A-B987-09FBA567FCA1">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="6007FE72-A519-4BF1-BB90-EB57E6D94E7E">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="AC97B486-472E-4E26-9095-534043BD6059">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="77381917-8F2B-4444-9A2B-049BAB5A4E9D">
							<SHORT-NAME>NvMService_AC2_SRBS_Defs</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="C51495C3-92CD-4D1C-AE8A-F11FB0815E0C">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="FF83F6BE-E5FD-4EA4-8022-20C2EB551A9B">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="727CEF23-78CF-4FF8-9231-F9900A991E00">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="313357D1-C108-42AA-9D17-6DCEF045D9A1">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="13E81230-8020-4376-9EBF-4F18FD02DA1A">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="A005B9C2-012B-4E69-A8AC-E797A05507F5">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="1347A770-3EFD-4858-86AF-5B285A31E606">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="CE5C1EEB-306E-4A11-B321-D91BB1C7F16F">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="31F72D1F-A542-4068-9EFF-9DD8D7FE7693">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="41597713-F118-4EC7-B1A5-B220EB007FB9">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="3A6380A3-5689-4BF4-8130-4077BF42A783">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="8E47D945-A0AC-4B67-8B05-B09CD29F7989">
							<SHORT-NAME>NvMService_AC2_SRBS_DS</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="9B764047-C504-48FF-B064-EE1E64FEB02A">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="D0698CDF-B712-4912-9900-A81A57F20283">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="CAEFFBAB-294D-478B-AA73-18F1D3BC7E91">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="C60AD2F5-60DC-4940-89C5-37D16B9A3F8B">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="8836CBFD-F615-44DB-A2C1-774F33565640">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="56EEBF31-9047-4E9D-B2AE-2A7EECF45B77">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="417E96D8-9EE6-4277-B991-D0CEE36CC649">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="704DEFEC-2785-431C-88C7-8A2EC4D4144D">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="10697314-7301-45DC-BD31-44E2D7EA1F0D">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="F0351E4D-F088-4297-BEE0-015504F36BA8">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="4274C975-3600-4258-BD8A-DE1C3EE1EE95">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="7280D43F-F85A-4D98-93B4-D1E3791BCC76">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="DD49315B-E90D-42DE-84EB-9038C40AC0F4">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="285E536D-86A6-4FDB-A20F-08DF30E5CC4D">
							<SHORT-NAME>NvMService_AC2_SRBS_DS_Defs</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="6BDAA532-E22E-48C3-83AD-5044567525AA">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="0E861863-1F08-42D0-90F9-BC2851344418">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="F39DC5B4-C8F3-45C2-BB54-08655F77C991">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="6C3547DD-73C7-40D5-B9D7-2B1FD58F1118">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="AD82FA9B-B1DB-490F-9154-DB12E0013F3E">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="22A00272-943E-435D-A0CF-8425D41147DF">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="5A4D6B64-B284-4D05-B83B-4713D52EF62D">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="F77ED2D6-E496-4A70-83E6-5189B6078196">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="D7802FED-2086-4701-A81F-5F70034CB16D">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="882FACFF-2A40-494B-9435-B8909A41F6DF">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="8D421683-35D5-4CBD-9082-44BD456ACB2A">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="224072C1-7B36-41D5-B22F-9544913507F5">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="8B6FC960-1F5F-4211-9398-79FF034629BA">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="C1884DAE-3D57-4ACE-B1E8-196E3A346A56">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC2_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="35C3D373-0A95-48FB-A7E8-C6547D847643">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="7E160602-DFEF-460D-B68A-51C182D9B265">
							<SHORT-NAME>NvMService_AC3</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="331A0E31-5376-4CF4-93DB-6F4D0183D60B">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="4997261A-050C-47D7-9B17-C29B068EA8EE">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="C0B809CF-4986-41B6-A27A-A59ADF3E493C">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="4755CB1D-62FA-4BAA-A501-47A54D83409D">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="E24B9B0B-B594-4583-A29C-3FB0C02E6114">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="FE38E726-1BC3-4531-920D-2CF01EE50378">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="4D50B5E2-F6AF-4B7C-A1A0-AF7773398420">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="9064E8BE-28B7-408C-B156-B14C72C24D25">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="411B4EE7-B370-4551-84C5-F2CBA01BEF63">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="EAB24B51-595D-49D9-B17A-42F5FD8260F2">
							<SHORT-NAME>NvMService_AC3_Defs</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="2737F21A-A9B3-4349-BDB6-6AEC36D533FE">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="51722155-652C-4D0E-8360-21E29E25691B">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="6C3D2701-8AE8-49F3-AD13-F612093706FB">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="3F4E71B5-B4DA-4582-8DD5-1EFDFFA2B6FE">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="783AADFB-F64B-4387-AFB2-66EEC2668DF4">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="91CC4246-5AE6-45C4-9653-606418085B6C">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="433B3127-A91C-4E04-A1B1-8906D8C14BFF">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="3AF90C8D-B25A-4DB9-B8D9-C37C6977AD0A">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="BEBBF36C-C317-4A91-84D2-15F93120E083">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="240AD2BC-09D3-46E2-A904-38D3599C3818">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="3A6983A4-9F58-4C23-856C-E88B9E309311">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="7B48733B-A90F-4832-ABB0-E530C2BBB40D">
							<SHORT-NAME>NvMService_AC3_DS</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="73CFEF68-5F37-4BDF-B55D-7A4B485A7CE8">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="730BB7FD-61B3-4544-90A5-353ED106AED6">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="3C750BD2-DCEB-4BE2-B8EC-A738AB45FA03">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="1469AF55-A54C-4103-A727-A6BF9EB315E3">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="0DBE90EF-E025-4240-BADB-FE5E093A1B15">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="06CD4176-F9DA-406C-A269-C41F9BB7D9FC">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="1C3BCF52-9F6E-4763-B69D-B2FBB0C035F1">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="324468E8-20F3-4E96-ADE1-86EBBDA7E44C">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="29C03D32-6938-43FA-AD4B-97425BDEB671">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="4B1C3ED3-5073-47E4-BEB7-62256587B0B4">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="D904E415-A12F-4BF5-BCB3-7BE5A991507C">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="B9E54A1E-6FC8-45FF-A266-683132DD63D9">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="E970E446-1330-433C-92DD-02B1B320873E">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="2E3C0C95-DAFB-4B73-8A39-51E9D819726A">
							<SHORT-NAME>NvMService_AC3_DS_Defs</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="E9FE8046-CCF3-47C4-A8D7-467DDA312D94">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="25507FDD-3B29-4B60-A3DB-12A14EE26C84">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="6825C6EF-76AE-4813-B7DC-88BEA67F6D1E">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="CA3C2702-286B-4FCA-9F69-E501555A63FD">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="C1C2E0C1-C932-46D8-B68A-8DCFC420D9F9">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="807A2334-E823-4B6F-95FB-B64E05316B5B">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="9A77B9D5-FF3D-4225-822D-8F4C333527E6">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="61FDAA75-4FA5-4474-864C-************">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="22C2BC3A-2A37-4EFA-8AC4-BB32BB46283D">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="D891252D-C35F-49EF-950B-31A685492933">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="BD6A57F0-1EB7-4170-BC69-85C8ACBB5374">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="277D4073-D8C1-4154-A731-BD8091A7B680">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="FD6758E8-91ED-4CC8-9F06-087C88B7B2BA">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="3D64C49D-39B0-4BAD-ABE5-1BA219F42C03">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="37C797B4-27EB-423F-A6B1-ACFC4F2468BA">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="2B6926C4-9BB8-40CB-A7CD-F5162242BB1F">
							<SHORT-NAME>NvMService_AC3_SRBS</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="37D02903-6513-4550-AAAE-475E0750A5F1">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="2C71C0A9-2B4F-49EF-9410-B161FB0BEE16">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="5A0466AD-FE48-481D-830D-67B20B7B61B5">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="5F48A21C-BAA1-4BF8-A612-12687A6BBB92">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="A0D7C113-A504-403A-9F83-8A203E39CCBA">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="AA0B2ABC-4F89-46C8-A5D8-1F55BDC5BB13">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="4B6AE704-C074-4178-BD5C-E06044B0F401">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="3E69F0DF-1332-419B-8D78-CBA391024337">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="E90F797A-B53A-4250-89AB-6CBB6F6F0A49">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="C95EB8B8-EADF-452E-86F4-A4BE0B86FF7F">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="0FB784A8-81A7-4D73-A273-2637B6C95B77">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="4B3C4AE4-F40A-4810-BCC8-7BD71E0910F7">
							<SHORT-NAME>NvMService_AC3_SRBS_Defs</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="F1FD7536-AA7B-4BDA-B8FD-FE505333D835">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="4AB34AA1-AB5C-4DD4-91C9-2CC040EB429B">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="E979FCCF-CA67-463F-8A20-B8557D3DA6C4">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="0732EDBB-490D-4811-9499-F7879D7F334C">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="750B989F-D4B3-49EB-93F7-8568A3B85BBC">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="4E41DF76-2A1C-4E4C-8BAA-D712F67D4342">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="5DD30FE5-D102-43D4-A177-127F84B7DE80">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="CF3861E7-01CA-447E-9645-23D23303340A">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="DBE936BF-B9D8-4B2E-B725-25350066D407">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="2F7A9FE9-FA3F-4694-B8BC-67C92DCFD210">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="CADF618E-0952-41EA-AC0E-0582753A5042">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="EB5EA5B2-FCB4-4455-8C19-52401BEBB8DC">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="5D147234-5CAA-49EC-92C2-8599A89FC2ED">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="275B628D-30CC-4CCD-91BB-0474893DF7B4">
							<SHORT-NAME>NvMService_AC3_SRBS_DS</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="C6717C47-1D96-4658-BB6F-8F12BA8EB980">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="1ABB944D-64FA-4E9B-9560-59A911B9CA67">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="57D090A4-11D8-4D75-8174-F7EBC68DDE86">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="3D7FD419-5C4F-4B31-9E3C-FC14640B1DD9">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="51D46A72-8CD0-402C-8DEE-6A752A910681">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="7E1B5F37-BF54-4A62-BEB2-D1FBF597C958">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="869BE49E-68E7-4528-8BDC-604B8902ED59">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="8626BC38-7E2C-4513-90E6-9759F5AD338C">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="545718D2-4690-4939-92A2-BE07DB888BB6">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="05310CEE-D8FF-40C8-9036-F3FAC02A33ED">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="C91EA01C-DC78-4616-AC00-85FC48DBCDFD">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="327C3BC8-E348-4798-BBB8-B2D98D0B2C41">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="B52F2009-1AFB-42E7-872E-31B2E9C29989">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="7CE2B7E8-2D67-4588-AB54-21EA502C3720">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="85EB2C6E-0E4E-4FD7-81B6-0A478E1FDCE9">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="2BEF6089-B2FF-4F04-867E-7945B5717603">
							<SHORT-NAME>NvMService_AC3_SRBS_DS_Defs</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="D412A0D3-7377-4BA4-A997-06416D3BEEA4">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="C0D9520F-05B0-4F5B-8813-7FFBE1D4EB76">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="90D7E111-F196-4F3D-9176-39869A866F08">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="726254D1-96BF-4607-82A4-089BB7635E82">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="AA7B3621-9DA5-4152-935F-28A1CF5B2A84">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="D7EC607C-DD71-4D15-A8E1-1A2EE2C9EEEA">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="8B22C444-7645-4038-BD7C-7C6111BD5865">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="97E02B88-2D57-4D27-9E70-EEF1745CF15B">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="3ADCF84E-1239-49B1-8EDC-1478CC506A9B">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="EAF18754-2DCD-48E7-8F59-A28F4BC7E55B">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="A1146D76-9D3E-4561-B2C6-DAD7DA0B7F51">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="25F176C1-EF6E-4A93-A44B-998EE1AF79A4">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="2CF3F506-F36D-46B3-AB9E-F14D26732CDC">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="89D75D2A-49CB-412F-8CE1-5C2BC4B28E41">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="3EE02ED4-5A34-49AB-B581-C50B10889506">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="BA980B41-9DAC-4048-B0AA-8917BC20ADB0">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="B317254F-A1D4-4ABA-B790-A9B56185F29F">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="A2789A0C-8A9C-422D-8D85-A9553D535411">
							<SHORT-NAME>NvMService_SRBS</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="C4A70BF3-64BD-49DF-9431-D0658DD33656">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="22F1726F-C25E-4FC5-B691-0B1FD7C78A2A">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="E12D4D93-EF97-433A-950D-90A92F397935">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="4770AC1F-6AF4-486A-BCAE-3AB58F07A6A8">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvMService_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="C25D8A75-6B98-417F-AE89-B977A098FAE3">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="{162712C5-BA29-4B7E-B9F8-528C4763F279}">
							<SHORT-NAME>CallbackDTCStatusChange</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<SERVICE-KIND>DIAGNOSTIC-EVENT-MANAGER</SERVICE-KIND>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="{5404B057-9A9A-4152-A5B6-9BCF0BA866AD}">
									<SHORT-NAME>DTCStatusChanged</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="{0B13C73E-4E00-41DE-BC45-3D7962E4F99B}">
											<SHORT-NAME>DTC</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
										<ARGUMENT-DATA-PROTOTYPE UUID="{B2F2799A-E7BB-4E66-81CC-85B96A8E62F5}">
											<SHORT-NAME>DTCStatusOld</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/Dem_UdsStatusByteType</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
										<ARGUMENT-DATA-PROTOTYPE UUID="{9A6EFC93-A757-4990-B3F6-B3AD09BDB6D7}">
											<SHORT-NAME>DTCStatusNew</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/Dem_UdsStatusByteType</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/CallbackDTCStatusChange/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="{4C2A25CC-37AD-475E-9DBB-130FFB6A501C}">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
