<?xml version="1.0" encoding="utf-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by Vector Employee (Vector Informatik GmbH) -->
<!--
This file was saved with a tool from Vector Informatik GmbH
-->
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-1-3.xsd" xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<AR-PACKAGES>
		<AR-PACKAGE>
			<SHORT-NAME>AUTOSAR_Platform</SHORT-NAME>
			<AR-PACKAGES>
				<AR-PACKAGE>
					<SHORT-NAME>BaseTypes</SHORT-NAME>
					<ELEMENTS>
						<SW-BASE-TYPE>
							<SHORT-NAME>dtRef_const_VOID</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>1</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>VOID</BASE-TYPE-ENCODING>
							<NATIVE-DECLARATION>void</NATIVE-DECLARATION>
						</SW-BASE-TYPE>
						<SW-BASE-TYPE>
							<SHORT-NAME>dtRef_VOID</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>1</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>VOID</BASE-TYPE-ENCODING>
							<NATIVE-DECLARATION>void</NATIVE-DECLARATION>
						</SW-BASE-TYPE>
						<SW-BASE-TYPE>
							<SHORT-NAME>boolean</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>8</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>BOOLEAN</BASE-TYPE-ENCODING>
						</SW-BASE-TYPE>
						<SW-BASE-TYPE>
							<SHORT-NAME>float32</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>32</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>IEEE754</BASE-TYPE-ENCODING>
						</SW-BASE-TYPE>
						<SW-BASE-TYPE>
							<SHORT-NAME>float64</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>64</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>IEEE754</BASE-TYPE-ENCODING>
						</SW-BASE-TYPE>
						<SW-BASE-TYPE>
							<SHORT-NAME>sint16</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>16</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>2C</BASE-TYPE-ENCODING>
						</SW-BASE-TYPE>
						<SW-BASE-TYPE>
							<SHORT-NAME>sint16_least</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>64</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>2C</BASE-TYPE-ENCODING>
						</SW-BASE-TYPE>
						<SW-BASE-TYPE>
							<SHORT-NAME>sint32</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>32</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>2C</BASE-TYPE-ENCODING>
						</SW-BASE-TYPE>
						<SW-BASE-TYPE>
							<SHORT-NAME>sint32_least</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>64</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>2C</BASE-TYPE-ENCODING>
						</SW-BASE-TYPE>
						<SW-BASE-TYPE>
							<SHORT-NAME>sint64</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>64</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>2C</BASE-TYPE-ENCODING>
						</SW-BASE-TYPE>
						<SW-BASE-TYPE>
							<SHORT-NAME>sint8</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>8</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>2C</BASE-TYPE-ENCODING>
						</SW-BASE-TYPE>
						<SW-BASE-TYPE>
							<SHORT-NAME>sint8_least</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>64</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>2C</BASE-TYPE-ENCODING>
						</SW-BASE-TYPE>
						<SW-BASE-TYPE>
							<SHORT-NAME>uint16</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>16</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>NONE</BASE-TYPE-ENCODING>
						</SW-BASE-TYPE>
						<SW-BASE-TYPE>
							<SHORT-NAME>uint16_least</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>64</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>NONE</BASE-TYPE-ENCODING>
						</SW-BASE-TYPE>
						<SW-BASE-TYPE>
							<SHORT-NAME>uint32</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>32</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>NONE</BASE-TYPE-ENCODING>
						</SW-BASE-TYPE>
						<SW-BASE-TYPE>
							<SHORT-NAME>uint32_least</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>64</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>NONE</BASE-TYPE-ENCODING>
						</SW-BASE-TYPE>
						<SW-BASE-TYPE>
							<SHORT-NAME>uint64</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>64</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>NONE</BASE-TYPE-ENCODING>
						</SW-BASE-TYPE>
						<SW-BASE-TYPE>
							<SHORT-NAME>uint8</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>8</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>NONE</BASE-TYPE-ENCODING>
						</SW-BASE-TYPE>
						<SW-BASE-TYPE>
							<SHORT-NAME>uint8_least</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>64</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>NONE</BASE-TYPE-ENCODING>
						</SW-BASE-TYPE>
					</ELEMENTS>
				</AR-PACKAGE>
				<AR-PACKAGE>
					<SHORT-NAME>CompuMethods</SHORT-NAME>
					<ELEMENTS>
						<COMPU-METHOD>
							<SHORT-NAME>boolean_CompuMethod</SHORT-NAME>
							<CATEGORY>TEXTTABLE</CATEGORY>
							<COMPU-INTERNAL-TO-PHYS>
								<COMPU-SCALES>
									<COMPU-SCALE>
										<SHORT-LABEL>FALSE</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>FALSE</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>TRUE</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>TRUE</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
								</COMPU-SCALES>
							</COMPU-INTERNAL-TO-PHYS>
						</COMPU-METHOD>
					</ELEMENTS>
				</AR-PACKAGE>
				<AR-PACKAGE>
					<SHORT-NAME>DataConstrs</SHORT-NAME>
					<ELEMENTS>
						<DATA-CONSTR>
							<SHORT-NAME>boolean_DataConstr</SHORT-NAME>
							<DATA-CONSTR-RULES>
								<DATA-CONSTR-RULE>
									<INTERNAL-CONSTRS>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
									</INTERNAL-CONSTRS>
								</DATA-CONSTR-RULE>
							</DATA-CONSTR-RULES>
						</DATA-CONSTR>
						<DATA-CONSTR>
							<SHORT-NAME>float32_DataConstr</SHORT-NAME>
							<DATA-CONSTR-RULES>
								<DATA-CONSTR-RULE>
									<INTERNAL-CONSTRS>
										<LOWER-LIMIT INTERVAL-TYPE="OPEN">-INF</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="OPEN">INF</UPPER-LIMIT>
									</INTERNAL-CONSTRS>
								</DATA-CONSTR-RULE>
							</DATA-CONSTR-RULES>
						</DATA-CONSTR>
						<DATA-CONSTR>
							<SHORT-NAME>float64_DataConstr</SHORT-NAME>
							<DATA-CONSTR-RULES>
								<DATA-CONSTR-RULE>
									<INTERNAL-CONSTRS>
										<LOWER-LIMIT INTERVAL-TYPE="OPEN">-INF</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="OPEN">INF</UPPER-LIMIT>
									</INTERNAL-CONSTRS>
								</DATA-CONSTR-RULE>
							</DATA-CONSTR-RULES>
						</DATA-CONSTR>
						<DATA-CONSTR>
							<SHORT-NAME>sint16_DataConstr</SHORT-NAME>
							<DATA-CONSTR-RULES>
								<DATA-CONSTR-RULE>
									<INTERNAL-CONSTRS>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">-32768</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">32767</UPPER-LIMIT>
									</INTERNAL-CONSTRS>
								</DATA-CONSTR-RULE>
							</DATA-CONSTR-RULES>
						</DATA-CONSTR>
						<DATA-CONSTR>
							<SHORT-NAME>sint16_least_DataConstr</SHORT-NAME>
							<DATA-CONSTR-RULES>
								<DATA-CONSTR-RULE>
									<INTERNAL-CONSTRS>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">-32768</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">32767</UPPER-LIMIT>
									</INTERNAL-CONSTRS>
								</DATA-CONSTR-RULE>
							</DATA-CONSTR-RULES>
						</DATA-CONSTR>
						<DATA-CONSTR>
							<SHORT-NAME>sint32_DataConstr</SHORT-NAME>
							<DATA-CONSTR-RULES>
								<DATA-CONSTR-RULE>
									<INTERNAL-CONSTRS>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">-2147483648</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">2147483647</UPPER-LIMIT>
									</INTERNAL-CONSTRS>
								</DATA-CONSTR-RULE>
							</DATA-CONSTR-RULES>
						</DATA-CONSTR>
						<DATA-CONSTR>
							<SHORT-NAME>sint32_least_DataConstr</SHORT-NAME>
							<DATA-CONSTR-RULES>
								<DATA-CONSTR-RULE>
									<INTERNAL-CONSTRS>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">-2147483648</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">2147483647</UPPER-LIMIT>
									</INTERNAL-CONSTRS>
								</DATA-CONSTR-RULE>
							</DATA-CONSTR-RULES>
						</DATA-CONSTR>
						<DATA-CONSTR>
							<SHORT-NAME>sint64_DataConstr</SHORT-NAME>
							<DATA-CONSTR-RULES>
								<DATA-CONSTR-RULE>
									<INTERNAL-CONSTRS>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">-9223372036854775808</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">9223372036854775807</UPPER-LIMIT>
									</INTERNAL-CONSTRS>
								</DATA-CONSTR-RULE>
							</DATA-CONSTR-RULES>
						</DATA-CONSTR>
						<DATA-CONSTR>
							<SHORT-NAME>sint8_DataConstr</SHORT-NAME>
							<DATA-CONSTR-RULES>
								<DATA-CONSTR-RULE>
									<INTERNAL-CONSTRS>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">-128</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">127</UPPER-LIMIT>
									</INTERNAL-CONSTRS>
								</DATA-CONSTR-RULE>
							</DATA-CONSTR-RULES>
						</DATA-CONSTR>
						<DATA-CONSTR>
							<SHORT-NAME>sint8_least_DataConstr</SHORT-NAME>
							<DATA-CONSTR-RULES>
								<DATA-CONSTR-RULE>
									<INTERNAL-CONSTRS>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">-128</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">127</UPPER-LIMIT>
									</INTERNAL-CONSTRS>
								</DATA-CONSTR-RULE>
							</DATA-CONSTR-RULES>
						</DATA-CONSTR>
						<DATA-CONSTR>
							<SHORT-NAME>uint16_DataConstr</SHORT-NAME>
							<DATA-CONSTR-RULES>
								<DATA-CONSTR-RULE>
									<INTERNAL-CONSTRS>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">65535</UPPER-LIMIT>
									</INTERNAL-CONSTRS>
								</DATA-CONSTR-RULE>
							</DATA-CONSTR-RULES>
						</DATA-CONSTR>
						<DATA-CONSTR>
							<SHORT-NAME>uint16_least_DataConstr</SHORT-NAME>
							<DATA-CONSTR-RULES>
								<DATA-CONSTR-RULE>
									<INTERNAL-CONSTRS>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">65535</UPPER-LIMIT>
									</INTERNAL-CONSTRS>
								</DATA-CONSTR-RULE>
							</DATA-CONSTR-RULES>
						</DATA-CONSTR>
						<DATA-CONSTR>
							<SHORT-NAME>uint32_DataConstr</SHORT-NAME>
							<DATA-CONSTR-RULES>
								<DATA-CONSTR-RULE>
									<INTERNAL-CONSTRS>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">4294967295</UPPER-LIMIT>
									</INTERNAL-CONSTRS>
								</DATA-CONSTR-RULE>
							</DATA-CONSTR-RULES>
						</DATA-CONSTR>
						<DATA-CONSTR>
							<SHORT-NAME>uint32_least_DataConstr</SHORT-NAME>
							<DATA-CONSTR-RULES>
								<DATA-CONSTR-RULE>
									<INTERNAL-CONSTRS>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">4294967295</UPPER-LIMIT>
									</INTERNAL-CONSTRS>
								</DATA-CONSTR-RULE>
							</DATA-CONSTR-RULES>
						</DATA-CONSTR>
						<DATA-CONSTR>
							<SHORT-NAME>uint64_DataConstr</SHORT-NAME>
							<DATA-CONSTR-RULES>
								<DATA-CONSTR-RULE>
									<INTERNAL-CONSTRS>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">18446744073709551615</UPPER-LIMIT>
									</INTERNAL-CONSTRS>
								</DATA-CONSTR-RULE>
							</DATA-CONSTR-RULES>
						</DATA-CONSTR>
						<DATA-CONSTR>
							<SHORT-NAME>uint8_DataConstr</SHORT-NAME>
							<DATA-CONSTR-RULES>
								<DATA-CONSTR-RULE>
									<INTERNAL-CONSTRS>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">255</UPPER-LIMIT>
									</INTERNAL-CONSTRS>
								</DATA-CONSTR-RULE>
							</DATA-CONSTR-RULES>
						</DATA-CONSTR>
						<DATA-CONSTR>
							<SHORT-NAME>uint8_least_DataConstr</SHORT-NAME>
							<DATA-CONSTR-RULES>
								<DATA-CONSTR-RULE>
									<INTERNAL-CONSTRS>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">255</UPPER-LIMIT>
									</INTERNAL-CONSTRS>
								</DATA-CONSTR-RULE>
							</DATA-CONSTR-RULES>
						</DATA-CONSTR>
					</ELEMENTS>
				</AR-PACKAGE>
				<AR-PACKAGE>
					<SHORT-NAME>ImplementationDataTypes</SHORT-NAME>
					<ELEMENTS>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>dtRef_const_VOID</SHORT-NAME>
							<CATEGORY>DATA_REFERENCE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<SW-POINTER-TARGET-PROPS>
											<TARGET-CATEGORY>VALUE</TARGET-CATEGORY>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/dtRef_const_VOID</BASE-TYPE-REF>
														<SW-IMPL-POLICY>CONST</SW-IMPL-POLICY>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
										</SW-POINTER-TARGET-PROPS>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>dtRef_VOID</SHORT-NAME>
							<CATEGORY>DATA_REFERENCE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<SW-POINTER-TARGET-PROPS>
											<TARGET-CATEGORY>VALUE</TARGET-CATEGORY>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/dtRef_VOID</BASE-TYPE-REF>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
										</SW-POINTER-TARGET-PROPS>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>boolean</SHORT-NAME>
							<DESC>
								<L-2 L="FOR-ALL">boolean has the value space required to support the mathematical concept of 
binary-valued logic: {true, false}.</L-2>
							</DESC>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/boolean</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<COMPU-METHOD-REF DEST="COMPU-METHOD">/AUTOSAR_Platform/CompuMethods/boolean_CompuMethod</COMPU-METHOD-REF>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR_Platform/DataConstrs/boolean_DataConstr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
							<TYPE-EMITTER>Platform_Type</TYPE-EMITTER>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>float32</SHORT-NAME>
							<DESC>
								<L-2 L="FOR-ALL">float32 corresponds to the IEEE single-precision 32-bit floating point type
[IEEE 754-1985]. The basic value space of float32 consists of the values 
m * 2^e, where m is an integer whose absolute value is less than 2^24, 
and e is an integer between -149 and 104, inclusive. In addition to the basic
value space described above, the value space of float32 also contains the 
following special values: positive and negative zero, positive and negative 
infinity and not-a-number. The order-relation on float32 is: 
x &lt; y if y - x is positive. Positive zero is greater than negative zero.
Not-a-number equals itself and is greater than all float values including positive infinity. 

float32 values have a lexical representation consisting of a mantissa followed, 
optionally, by the character "E" or "e", followed by an exponent. The exponent
must be an integer. The mantissa must be a decimal number. The representations
for exponent and mantissa must follow the lexical rules for integer and decimal.
If the "E" or "e" and the following exponent are omitted, an exponent value 
of 0 is assumed. 

The special values positive and negative zero, positive and negative infinity
and not-a-number have lexical representations 0, -0, INF, -INF and NaN, 
respectively. 

For example, -1E4, 1267.43233E12, 12.78e-2, 12 and INF are all legal literals
for float32.</L-2>
							</DESC>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/float32</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR_Platform/DataConstrs/float32_DataConstr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
							<TYPE-EMITTER>Platform_Type</TYPE-EMITTER>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>float64</SHORT-NAME>
							<DESC>
								<L-2 L="FOR-ALL">The float64 datatype corresponds to IEEE float64-precision 64-bit floating point
type [IEEE 754-1985]. The basic value space of float64 consists of the values
m * 2^e, where m is an integer whose absolute value is less than 2^53, and e is
 an integer between -1075 and 970, inclusive. In addition to the basic value 
space described above, the value space of float64 also contains the following 
special values: positive and negative zero, positive and negative infinity 
and not-a-number. 
The order-relation on float64 is: x &lt; y if y - x is positive. 
Positive zero is greater than negative zero. Not-a-number equals itself and 
is greater than all float64 values including positive infinity. 
                    
float64 values have a lexical representation consisting of a mantissa followed,
optionally, by the character "E" or "e", followed by an exponent. 
The exponent must be an integer. The mantissa must be a decimal number.
The representations for exponent and mantissa must follow the lexical rules 
for integer and decimal. If the "E" or "e" and the following exponent are 
omitted, an exponent value of 0 is assumed. 

The special values positive and negative zero, positive and negative infinity
and not-a-number have lexical representations 0, -0, INF, -INF and NaN, 
respectively. 

For example, -1E4, 1267.43233E12, 12.78e-2, 12 and INF are all 
legal literals for float64.</L-2>
							</DESC>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/float64</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR_Platform/DataConstrs/float64_DataConstr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
							<TYPE-EMITTER>Platform_Type</TYPE-EMITTER>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>sint16</SHORT-NAME>
							<DESC>
								<L-2 L="FOR-ALL">SInt16 represents integers with a minimum value of -32768 and a maximum value 
of 32767. The order-relation on sint16 is: x &lt; y if y - x is positive.
sint16 has a lexical representation consisting of an optional sign followed
by a finite-length sequence of decimal digits (#x30-#x39). If the sign is
omitted, "+" is assumed. 

For example: -1, 0, -12678, +10000, 2500</L-2>
							</DESC>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/sint16</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR_Platform/DataConstrs/sint16_DataConstr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
							<TYPE-EMITTER>Platform_Type</TYPE-EMITTER>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>sint16_least</SHORT-NAME>
							<DESC>
								<L-2 L="FOR-ALL">sint16_least represents a signed integer with values defined by at least 16 bit.</L-2>
							</DESC>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/sint16_least</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR_Platform/DataConstrs/sint16_least_DataConstr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
							<TYPE-EMITTER>Platform_Type</TYPE-EMITTER>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>sint32</SHORT-NAME>
							<DESC>
								<L-2 L="FOR-ALL">sint32 represents integers with a minimum value of -2147483648 and a maximum 
value of 2147483647. The order-relation on sint32 is: x &lt; y if y - x is
positive. sint32 has a lexical representation consisting of an optional sign 
allowed by a finite-length sequence of decimal digits (#x30-#x39). If the 
sign is omitted, "+" is assumed. 

For example: -1, 0, -12688778, +10000, 250098675.</L-2>
							</DESC>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/sint32</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR_Platform/DataConstrs/sint32_DataConstr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
							<TYPE-EMITTER>Platform_Type</TYPE-EMITTER>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>sint32_least</SHORT-NAME>
							<DESC>
								<L-2 L="FOR-ALL">sint32_least represents a signed integer with values defined by at least 32 bit.</L-2>
							</DESC>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/sint32_least</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR_Platform/DataConstrs/sint32_least_DataConstr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
							<TYPE-EMITTER>Platform_Type</TYPE-EMITTER>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>sint64</SHORT-NAME>
							<DESC>
								<L-2 L="FOR-ALL">sint64 represents integers with a minimum value of -9223372036854775808 and a maximum 
value of 9223372036854775807. The order-relation on sint64 is: x &lt; y if y - x is
positive. sint64 has a lexical representation consisting of an optional sign 
allowed by a finite-length sequence of decimal digits (#x30-#x39). If the 
sign is omitted, "+" is assumed. 

For example: -1, 0, -12688778, +10000, 250098675.</L-2>
							</DESC>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/sint64</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR_Platform/DataConstrs/sint64_DataConstr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
							<TYPE-EMITTER>Platform_Type</TYPE-EMITTER>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>sint8</SHORT-NAME>
							<DESC>
								<L-2 L="FOR-ALL">sint8 represents integers with a minimum value of -128 and a maximum value of 127.
The order-relation on sint8 is: x &lt; y if y - x is positive.
sint8 has a lexical representation consisting of an optional sign followed 
by a finite-length sequence of decimal digits (#x30-#x39). If the sign is 
omitted, "+" is assumed. 

For example: -1, 0, 12678, +10000.</L-2>
							</DESC>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/sint8</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR_Platform/DataConstrs/sint8_DataConstr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
							<TYPE-EMITTER>Platform_Type</TYPE-EMITTER>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>sint8_least</SHORT-NAME>
							<DESC>
								<L-2 L="FOR-ALL">sint8_least represents a signed integer with values defined by at least 8 bit.</L-2>
							</DESC>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/sint8_least</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR_Platform/DataConstrs/sint8_least_DataConstr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
							<TYPE-EMITTER>Platform_Type</TYPE-EMITTER>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>uint16</SHORT-NAME>
							<DESC>
								<L-2 L="FOR-ALL">uint16 represents integers with a minimum value of 0 and a maximum value of 65535.
The order-relation on uint16 is: x &lt; y if y - x is positive.
uint16 has a lexical representation consisting of a finite-length sequence 
of decimal digits (#x30-#x39).

For example: 1, 0, 1267, +10000.</L-2>
							</DESC>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/uint16</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR_Platform/DataConstrs/uint16_DataConstr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
							<TYPE-EMITTER>Platform_Type</TYPE-EMITTER>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>uint16_least</SHORT-NAME>
							<DESC>
								<L-2 L="FOR-ALL">uint16_least represents an unsigned integer with values defined by at least 16 bit.</L-2>
							</DESC>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/uint16_least</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR_Platform/DataConstrs/uint16_least_DataConstr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
							<TYPE-EMITTER>Platform_Type</TYPE-EMITTER>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>uint32</SHORT-NAME>
							<DESC>
								<L-2 L="FOR-ALL">uint32 represents integers with a minimum value of 0 and a maximum value 
of 4294967295. The order-relation on uint32 is: x &lt; y if y - x is positive.
uint32 has a lexical representation consisting of a finite-length sequence 
of decimal digits (#x30-#x39). 

For example: 1, 0, 12234567, 104400.</L-2>
							</DESC>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/uint32</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR_Platform/DataConstrs/uint32_DataConstr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
							<TYPE-EMITTER>Platform_Type</TYPE-EMITTER>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>uint32_least</SHORT-NAME>
							<DESC>
								<L-2 L="FOR-ALL">uint32_least represents an unsigned integer with values defined by at least 32 bit.</L-2>
							</DESC>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/uint32_least</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR_Platform/DataConstrs/uint32_least_DataConstr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
							<TYPE-EMITTER>Platform_Type</TYPE-EMITTER>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>uint64</SHORT-NAME>
							<DESC>
								<L-2 L="FOR-ALL">uint64 represents integers with a minimum value of 0 and a maximum value 
of 18446744073709551615. The order-relation on uint64 is: x &lt; y if y - x is positive.
uint64 has a lexical representation consisting of a finite-length sequence 
of decimal digits (#x30-#x39). 

For example: 1, 0, 12234567, 104400.</L-2>
							</DESC>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/uint64</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR_Platform/DataConstrs/uint64_DataConstr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
							<TYPE-EMITTER>Platform_Type</TYPE-EMITTER>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>uint8</SHORT-NAME>
							<DESC>
								<L-2 L="FOR-ALL">uint8 represents integers with a minimum value of 0 and a maximum value of 255.
The order-relation on uint8 is: x &lt; y if y - x is positive.
uint8 has a lexical representation consisting of a finite-length sequence 
of decimal digits (#x30-#x39).

For example: 1, 0, 126, +10.</L-2>
							</DESC>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/uint8</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR_Platform/DataConstrs/uint8_DataConstr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
							<TYPE-EMITTER>Platform_Type</TYPE-EMITTER>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>uint8_least</SHORT-NAME>
							<DESC>
								<L-2 L="FOR-ALL">uint8_least represents an unsigned integer with values defined by at least 8 bit.</L-2>
							</DESC>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/uint8_least</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR_Platform/DataConstrs/uint8_least_DataConstr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
							<TYPE-EMITTER>Platform_Type</TYPE-EMITTER>
						</IMPLEMENTATION-DATA-TYPE>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
