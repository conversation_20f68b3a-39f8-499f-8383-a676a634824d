<?xml version="1.0" encoding="utf-8"?>
<!--
This file was saved with a tool from Vector Informatik GmbH
-->
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-1-3.xsd" xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<AR-PACKAGES>
		<AR-PACKAGE>
			<SHORT-NAME>AUTOSAR_Platform</SHORT-NAME>
			<AR-PACKAGES>
				<AR-PACKAGE>
					<SHORT-NAME>BaseTypes</SHORT-NAME>
					<ELEMENTS>
						<SW-BASE-TYPE UUID="146B2C87-5443-4140-92B1-5D604789DACA">
							<SHORT-NAME>boolean</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>8</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>BOOLEAN</BASE-TYPE-ENCODING>
						</SW-BASE-TYPE>
						<SW-BASE-TYPE UUID="17DCFE84-E181-4040-BF51-B0B817C3D730">
							<SHORT-NAME>dtRef_const_VOID</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>1</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>VOID</BASE-TYPE-ENCODING>
							<NATIVE-DECLARATION>void</NATIVE-DECLARATION>
						</SW-BASE-TYPE>
						<SW-BASE-TYPE UUID="90174CD1-F254-4CF8-B90D-B0A2176D10A7">
							<SHORT-NAME>dtRef_VOID</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>1</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>VOID</BASE-TYPE-ENCODING>
							<NATIVE-DECLARATION>void</NATIVE-DECLARATION>
						</SW-BASE-TYPE>
						<SW-BASE-TYPE UUID="1B0803FC-73DE-456E-9010-DC820D9F42BB">
							<SHORT-NAME>uint8</SHORT-NAME>
							<CATEGORY>FIXED_LENGTH</CATEGORY>
							<BASE-TYPE-SIZE>8</BASE-TYPE-SIZE>
							<BASE-TYPE-ENCODING>NONE</BASE-TYPE-ENCODING>
						</SW-BASE-TYPE>
					</ELEMENTS>
				</AR-PACKAGE>
				<AR-PACKAGE>
					<SHORT-NAME>CompuMethods</SHORT-NAME>
					<ELEMENTS>
						<COMPU-METHOD UUID="0F53C45A-9155-4E53-BE27-23B5C2679E6C">
							<SHORT-NAME>boolean_CompuMethod</SHORT-NAME>
							<CATEGORY>TEXTTABLE</CATEGORY>
							<COMPU-INTERNAL-TO-PHYS>
								<COMPU-SCALES>
									<COMPU-SCALE>
										<SHORT-LABEL>FALSE</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>FALSE</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>TRUE</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>TRUE</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
								</COMPU-SCALES>
							</COMPU-INTERNAL-TO-PHYS>
						</COMPU-METHOD>
					</ELEMENTS>
				</AR-PACKAGE>
				<AR-PACKAGE>
					<SHORT-NAME>ImplementationDataTypes</SHORT-NAME>
					<ELEMENTS>
						<IMPLEMENTATION-DATA-TYPE UUID="15E151D3-EF62-42B7-BF3D-F74CBCC0E2D1">
							<SHORT-NAME>boolean</SHORT-NAME>
							<DESC>
								<L-2 L="FOR-ALL">boolean has the value space required to support the mathematical concept of 
binary-valued logic: {true, false}.</L-2>
							</DESC>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/boolean</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<COMPU-METHOD-REF DEST="COMPU-METHOD">/AUTOSAR_Platform/CompuMethods/boolean_CompuMethod</COMPU-METHOD-REF>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR_Platform/DataConstrs/boolean_DataConstr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
							<TYPE-EMITTER>Platform_Type</TYPE-EMITTER>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE UUID="B9A4C31D-1500-472E-8540-ECD79E134B44">
							<SHORT-NAME>dtRef_const_VOID</SHORT-NAME>
							<CATEGORY>DATA_REFERENCE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<SW-POINTER-TARGET-PROPS>
											<TARGET-CATEGORY>VALUE</TARGET-CATEGORY>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/dtRef_const_VOID</BASE-TYPE-REF>
														<SW-IMPL-POLICY>CONST</SW-IMPL-POLICY>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
										</SW-POINTER-TARGET-PROPS>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE UUID="9E19C642-5054-4141-907E-BED5DA312D07">
							<SHORT-NAME>dtRef_VOID</SHORT-NAME>
							<CATEGORY>DATA_REFERENCE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<SW-POINTER-TARGET-PROPS>
											<TARGET-CATEGORY>VALUE</TARGET-CATEGORY>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/dtRef_VOID</BASE-TYPE-REF>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
										</SW-POINTER-TARGET-PROPS>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE UUID="C22DE2F9-4B36-47CD-9C31-FB5F77FEE55D">
							<SHORT-NAME>uint8</SHORT-NAME>
							<DESC>
								<L-2 L="FOR-ALL">uint8 represents integers with a minimum value of 0 and a maximum value of 255.
The order-relation on uint8 is: x &lt; y if y - x is positive.
uint8 has a lexical representation consisting of a finite-length sequence 
of decimal digits (#x30-#x39).

For example: 1, 0, 126, +10.</L-2>
							</DESC>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR_Platform/BaseTypes/uint8</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR_Platform/DataConstrs/uint8_DataConstr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
							<TYPE-EMITTER>Platform_Type</TYPE-EMITTER>
						</IMPLEMENTATION-DATA-TYPE>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
		<AR-PACKAGE>
			<SHORT-NAME>Predefined_DEV</SHORT-NAME>
			<AR-PACKAGES>
				<AR-PACKAGE>
					<SHORT-NAME>CompuMethods</SHORT-NAME>
					<ELEMENTS>
						<COMPU-METHOD UUID="F2570AA0-D6E5-4217-80F7-5F60B8778133">
							<SHORT-NAME>NvM_RequestResultType</SHORT-NAME>
							<CATEGORY>TEXTTABLE</CATEGORY>
							<COMPU-INTERNAL-TO-PHYS>
								<COMPU-SCALES>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_REQ_OK</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_REQ_OK</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_REQ_NOT_OK</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_REQ_NOT_OK</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_REQ_PENDING</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_REQ_PENDING</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_REQ_INTEGRITY_FAILED</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">3</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">3</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_REQ_INTEGRITY_FAILED</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_REQ_BLOCK_SKIPPED</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">4</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">4</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_REQ_BLOCK_SKIPPED</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_REQ_NV_INVALIDATED</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">5</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">5</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_REQ_NV_INVALIDATED</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_REQ_CANCELED</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">6</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">6</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_REQ_CANCELED</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_REQ_REDUNDANCY_FAILED</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">7</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">7</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_REQ_REDUNDANCY_FAILED</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_REQ_RESTORED_FROM_ROM</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">8</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">8</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_REQ_RESTORED_FROM_ROM</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
								</COMPU-SCALES>
							</COMPU-INTERNAL-TO-PHYS>
						</COMPU-METHOD>
						<COMPU-METHOD UUID="007B24D3-5C46-4986-B3CA-31D71FEDD510">
							<SHORT-NAME>NvM_ServiceIdType</SHORT-NAME>
							<CATEGORY>TEXTTABLE</CATEGORY>
							<COMPU-INTERNAL-TO-PHYS>
								<COMPU-SCALES>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_READ_BLOCK</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">6</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">6</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_READ_BLOCK</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_WRITE_BLOCK</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">7</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">7</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_WRITE_BLOCK</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_RESTORE_BLOCK_DEFAULTS</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">8</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">8</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_RESTORE_BLOCK_DEFAULTS</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_ERASE_BLOCK</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">9</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">9</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_ERASE_BLOCK</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_INVALIDATE_NV_BLOCK</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">11</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">11</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_INVALIDATE_NV_BLOCK</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
									<COMPU-SCALE>
										<SHORT-LABEL>NVM_READ_ALL</SHORT-LABEL>
										<LOWER-LIMIT INTERVAL-TYPE="CLOSED">12</LOWER-LIMIT>
										<UPPER-LIMIT INTERVAL-TYPE="CLOSED">12</UPPER-LIMIT>
										<COMPU-CONST>
											<VT>NVM_READ_ALL</VT>
										</COMPU-CONST>
									</COMPU-SCALE>
								</COMPU-SCALES>
							</COMPU-INTERNAL-TO-PHYS>
						</COMPU-METHOD>
					</ELEMENTS>
				</AR-PACKAGE>
				<AR-PACKAGE>
					<SHORT-NAME>ImplementationDataTypes</SHORT-NAME>
					<ELEMENTS>
						<IMPLEMENTATION-DATA-TYPE UUID="91037BEA-8260-4D81-AF92-D77F36795EEF">
							<SHORT-NAME>NvM_RequestResultType</SHORT-NAME>
							<CATEGORY>TYPE_REFERENCE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<COMPU-METHOD-REF DEST="COMPU-METHOD">/Predefined_DEV/CompuMethods/NvM_RequestResultType</COMPU-METHOD-REF>
										<IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE UUID="E8567FAC-3DF7-4B9A-AE12-961DD3DAE8F3">
							<SHORT-NAME>NvM_ServiceIdType</SHORT-NAME>
							<CATEGORY>TYPE_REFERENCE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<COMPU-METHOD-REF DEST="COMPU-METHOD">/Predefined_DEV/CompuMethods/NvM_ServiceIdType</COMPU-METHOD-REF>
										<IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</IMPLEMENTATION-DATA-TYPE>
					</ELEMENTS>
				</AR-PACKAGE>
				<AR-PACKAGE>
					<SHORT-NAME>PortInterfaces</SHORT-NAME>
					<ELEMENTS>
						<CLIENT-SERVER-INTERFACE UUID="E0A7D605-5720-4347-9A9D-8CE7CC4D5D23">
							<SHORT-NAME>NvSWC_NvMAdministration</SHORT-NAME>
							<IS-SERVICE>false</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="0D577392-FA79-46B8-AA6E-DD1568909329">
									<SHORT-NAME>SetBlockProtection</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="2A40B8E1-A4FC-4E11-B0DC-12C16FDD2D4A">
											<SHORT-NAME>ProtectionEnabled</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMAdministration/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="6B9A70AB-B127-4BE0-815D-B29710D5293F">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="8566A566-0FD2-433B-8036-5852CAB188FF">
							<SHORT-NAME>NvSWC_NvMNotifyInitBlock</SHORT-NAME>
							<IS-SERVICE>false</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="9373F33A-8777-45D4-A132-653A549C370F">
									<SHORT-NAME>InitBlock</SHORT-NAME>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="151BCBEB-17B3-4431-9766-C9E503E6C707">
							<SHORT-NAME>NvSWC_NvMNotifyJobFinished</SHORT-NAME>
							<IS-SERVICE>false</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="2B8D13E7-1EC0-4BDF-8707-56458F4A8DF4">
									<SHORT-NAME>JobFinished</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="F54D6C44-EDA3-4F16-A554-ACFDCD25553D">
											<SHORT-NAME>ServiceId</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_ServiceIdType</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
										<ARGUMENT-DATA-PROTOTYPE UUID="EE8D8E0B-5C12-4CDC-B220-21A1B3A102D6">
											<SHORT-NAME>JobResult</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="B8F55386-720C-47F0-A99C-8C9CD9977D8A">
							<SHORT-NAME>NvSWC_NvMService</SHORT-NAME>
							<IS-SERVICE>false</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="8087F094-C886-47A6-8D02-4A522941EF65">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="B9E1A929-B8F9-4DFB-A699-3592F0AE1365">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="7E3CE6B8-1997-44D8-B698-F6983653F390">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="9056FBFC-C0C1-4C2B-A5A8-723C4203EA70">
							<SHORT-NAME>NvSWC_NvMService_AC2</SHORT-NAME>
							<IS-SERVICE>false</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="F5CD581D-4EC6-4C00-8966-3214A793C3CE">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="A2D2F658-31FA-4D35-A661-07E91B9A7502">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="DC93BB61-B4A1-43C2-B589-59BD1F1E3EC5">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="58683E79-E31B-4302-883E-487F13687227">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="ED70FE2B-8131-4E83-81DC-242CCE0A799F">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="CF9F5A08-CF1A-4F84-88F5-E8701014C940">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="87E31B18-FBF9-45FB-AE02-54D03CC44410">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="60696674-FDA2-4B56-A28C-5504159BA092">
							<SHORT-NAME>NvSWC_NvMService_AC2_Defs</SHORT-NAME>
							<IS-SERVICE>false</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="AC6E1B47-DCD4-4D23-8082-85F27E7AF6CA">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="C45FF539-B812-434C-A1D7-8BC5ACBC6C11">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="EE259D70-AEB5-4F34-9098-C9EC064E9FC4">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="D80319A6-229A-4878-B67E-236D56149911">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="8719DBE3-9948-434D-A939-1FDABB66BD0A">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="DA1A8204-B69D-4F96-837E-DF7C478162DC">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="469C9A59-18CF-4848-8213-DAD96F3E727C">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="7E7142D7-72E8-4918-9FDF-76AE648F0A57">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="38196A38-C113-403E-8710-D9A4DB2DCB7C">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="508C21F2-126B-4765-9A72-F9AA8C919C90">
							<SHORT-NAME>NvSWC_NvMService_AC2_DS</SHORT-NAME>
							<IS-SERVICE>false</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="53C868F8-B777-4C0A-8924-D35C982E7E19">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="0B4ECF69-85BB-48D6-8C0D-3B144601CBE8">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="0BF32937-37B5-49A0-AA29-9EAB39D3CF34">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="4572475C-BE82-4347-AF17-F4798AD66C9F">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="8D2AD536-8E91-4888-BFCF-C98BE5E8C67D">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="6BD564AB-52F2-455E-9D53-F2B900B6646D">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="1A6A34C5-AC51-4B5B-878C-6BB0FD952281">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="77AE1F54-FDFD-4556-BBD4-49108BF8EAD6">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="E7AD0BE9-45BE-4A6D-95FF-DF80C779DE0D">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="E9282AE1-5228-4E90-82EB-92111FEFF848">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="86679F7A-9112-41EA-8CE9-7B0B64F8F10E">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="C04E6F72-473C-4437-8556-D7F0E8AACC93">
							<SHORT-NAME>NvSWC_NvMService_AC2_DS_Defs</SHORT-NAME>
							<IS-SERVICE>false</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="10F9D149-8349-42EF-B916-F99EB7B34A3B">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="C8BEAF97-3C03-413E-84EA-3561B9259270">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="4E073850-A3F4-4A74-A0E5-87A449E0D709">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="35A013C2-C702-4EDD-8107-38D9F690C914">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="D788B1F3-688E-471D-B059-409CF5DD2931">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="441A1816-0029-4083-8E1F-B3E8B952D5B8">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="7024343C-B28A-4D9A-A1CE-855BBD81E700">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="6AE72612-3F81-4D75-9DC6-F969E78CEFA2">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="FC722A5B-E60C-4266-8D1B-80B5C3B501BD">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="D113C60F-BACB-4E7F-9915-09B8343ADC18">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="E0FEC2D7-13F2-4B75-BAB0-FB51B83E3F7D">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="326CDC45-F741-430A-98C7-4DADABC8B42C">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="2CD9C18B-1508-4E06-AE5B-55EB6ED4AE1E">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="3458998D-3A08-4EF4-8C1D-052FCEA70755">
							<SHORT-NAME>NvSWC_NvMService_AC2_SRBS</SHORT-NAME>
							<IS-SERVICE>false</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="254DF529-D9FE-4549-AAD0-EDA86F9B9D09">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="25BB8F91-3535-4E1A-99D8-04EB5621D1AE">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="A39250D1-618E-4BD4-8C51-ADF0FF0CA5D8">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="718156D3-EA66-4F3F-A742-30D1B6DFBE31">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="DA62F920-B68B-4877-BEB7-7954FA21A0FC">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="9C974DDB-8A04-4E1A-893F-928DFA591315">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="F1CE5FDE-E305-44E4-A6B1-D920B844CCF9">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="CA1A9974-581B-4EBB-B760-7CEF2456A5B6">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="212FF34E-F98B-4B8F-A93C-806D768CCA6B">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="DDD8F223-1DBC-479E-8E06-AB219CBAE2E0">
							<SHORT-NAME>NvSWC_NvMService_AC2_SRBS_Defs</SHORT-NAME>
							<IS-SERVICE>false</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="0749571D-59F3-42B5-B73E-450B1E22BE2B">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="9BBF8C41-A10E-45DD-81DD-C47E911873D8">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="074E4A60-EE9D-4112-AC4A-F077A7B36809">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="C15CBE02-037C-449A-A84A-83CDBF83D37C">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="DDDB65C2-FAA4-4FA4-A20F-82D538EFFD36">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="4848DE9B-370B-4DF6-9D61-F4DC68E675F2">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="A52D10DE-B1A3-4AEE-9AF4-7296815EE49B">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="76FE702D-32CD-4C3A-A3D2-68B0039AE961">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="3572D03C-F402-4EE5-9860-E6889229D6CD">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="E7B0F213-D740-4F04-8A5B-0ECA92F6023C">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="8D4913EF-2DCA-499E-B61F-7A0C820299B6">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="DE273126-9748-42E0-8053-F1DC379733AE">
							<SHORT-NAME>NvSWC_NvMService_AC2_SRBS_DS</SHORT-NAME>
							<IS-SERVICE>false</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="2ECB68FF-4706-428F-834E-BFC9853EEF0E">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="56B75359-C923-4E65-A615-FE79EEF3494A">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="5D90C595-BE08-4B82-BA48-71FBAF5FDC6E">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="BF40CA3E-568F-427D-9669-8CFA9DA16100">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="AB036761-28E8-44A5-97BE-2E47FA72FE2D">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="998ABA7C-BCA7-49EA-B0B0-E381BB4C8B3E">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="A4958CD8-156F-4C80-970A-4CD1468DDA05">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="2FC52BFC-81CC-400A-97CB-82C491071AD4">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="DE4E244F-075A-4462-ADDC-4B9D653D4670">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="1BE15E19-E9CA-4BF9-A2FD-1A1249FD50A5">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="B273E5BF-B9AD-43B3-88E2-417DE28CC758">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="78103938-51A3-49E4-B953-216272DD0311">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="4B0CB611-845A-46B4-8804-A72E3DDBC280">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="8DD0FB08-964F-4EDD-8CDA-7FBEE8412B93">
							<SHORT-NAME>NvSWC_NvMService_AC2_SRBS_DS_Defs</SHORT-NAME>
							<IS-SERVICE>false</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="A49987FD-18F4-443C-81FE-2E819B16A662">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="51192FF1-ABC2-486E-9CF9-B4C1A52562E8">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="558AA311-44AA-47E2-8F5F-B1BFB3CC8760">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="E11AC610-9275-4F63-8AA5-8DEA1245F37B">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="1C3743F8-532A-4240-B96E-56F6A58D3B1A">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="0764F651-3421-48A6-98C6-9DE6C11027BB">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="6AABF3FF-9882-4B8F-B6BC-E65CAF44E98B">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="7198CB16-8D93-43F2-B115-625F87CF9990">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="30252DE6-59EF-4816-A278-3A35A00FE64F">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="AB87C17F-AEE6-42F5-B254-3D75899BFB52">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="C5E63316-C297-4B9C-ACDE-D8A86E8A0A36">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="22DD57D1-660D-4486-BE3D-A97F16C6E67F">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="AF25F0BC-1C59-4AEA-A140-F61995A6305F">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="46ECAADA-816B-404D-A5B8-DBD9BBC5B8BE">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC2_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="F8510316-D4F5-4099-8C4B-8B535F0DC637">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="666A84F2-1266-4713-8270-5D15D53600F3">
							<SHORT-NAME>NvSWC_NvMService_AC3</SHORT-NAME>
							<IS-SERVICE>false</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="81197302-7DD2-4DD6-B479-55679C9088C5">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="9137CF11-F65A-4154-BC53-04069D6B9136">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="*************-4C72-A074-FA9A08AA1E94">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="6AE952DB-634C-43BE-8BC1-F0B83D37D4C4">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="B451BE9F-2A1B-493B-BC18-C298F1B67C38">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="22A199C7-C723-4BAA-B950-1776E197E975">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="BA0A8BC8-70EE-4D13-BD9B-CD2223B91F42">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="44408F10-1658-4312-8D85-2B2EE24C1F04">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="C3F8628D-F73F-4A7A-839D-42D24BF6C335">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="D78F1626-81EB-4894-A38E-3E711E0E1D26">
							<SHORT-NAME>NvSWC_NvMService_AC3_Defs</SHORT-NAME>
							<IS-SERVICE>false</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="787E5964-592D-4D02-97E9-E692B45B8E19">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="584AFDB5-DF5F-4373-BCA7-A12A0C27F433">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="98D67C53-2916-4B72-B9B5-DE6C26270FE4">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="FF0E545B-E3E9-4AAF-A1D3-2644F6E445C7">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="F85AB65D-B2CC-45DF-9985-07088CFE51D6">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="4AEBBE04-9103-410E-8AAE-3B157287ADBB">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="452E8818-510B-4878-9972-E53DEDBA1EAC">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="441EE033-4E13-4812-B71D-73BE7CFDF55B">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="CB0D33CD-6A69-4FE5-A882-53E9197624C1">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="873DC786-37CB-4E8B-B58B-8613642EF294">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="91D780B3-A8A2-4E9B-842F-B5946B451CCC">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="35B2CE60-D78F-4BCB-88FE-165AFA333AB8">
							<SHORT-NAME>NvSWC_NvMService_AC3_DS</SHORT-NAME>
							<IS-SERVICE>false</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="0E68DC66-CD44-404F-AA07-D72224759DCE">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="736FA058-AC8D-4A13-B5BD-B1BFD5D3764E">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="25AA3697-AE79-4559-B55E-637E6C3B66C5">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="CFA1F0A1-88CE-4AC6-8968-5774C50570D9">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="90A0DF21-08CE-41A5-9029-2656316FB937">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="9013F0E9-FFDC-4B72-AC58-4FA6C19FA801">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="B6E2B547-3A77-4886-B2EF-4CA5D8CA3104">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="D74AA7AC-338F-440D-9436-76F1FCA9DF42">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="1357CD23-6C37-4E26-8C52-C62F3B073F2E">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="F9D05FC5-C325-440A-B797-EC8899BE68EF">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="069490ED-E6CC-4E10-959F-ABF4B07D4F0B">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="8E5B6C9E-8EB8-4CE4-8508-5270315B9B8B">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="A406FAE2-1978-4E73-835D-ACE055CE3276">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="B3B4861F-87F7-4684-9187-732830D03BB6">
							<SHORT-NAME>NvSWC_NvMService_AC3_DS_Defs</SHORT-NAME>
							<IS-SERVICE>false</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="D7B553A4-27B0-4FB9-B0FA-16C7F948F76C">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="D72216F0-53AC-4C84-A3AB-7259CF9B156B">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="29443FA1-FD35-4FBD-894E-2CACE2F95446">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="DEE7F96A-0709-4331-BD2D-B222B96675E3">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="243600A9-AE96-4C7D-A406-D8F30F453B1E">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="3A54394D-52AE-4B46-812F-64E7EFCEC3D8">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="78A09580-DBAD-45F3-B69C-0C4B0033582D">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="25F00815-952E-4E58-85A5-453053AF3105">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="D5355951-2F60-4ABD-B7FD-5021D25B34EC">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="7A767D6C-7223-4248-AC0F-BF7C4ABA0F8D">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="46EB752D-F869-4D33-A5B3-F3D14F48A154">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="BBBB0E98-D538-411C-BC8C-2FBF8F6AEA08">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="B17028F7-C437-4A30-AB0D-F0D475ECCEC9">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="F9C9EF07-C7A9-4A5F-A716-D53CCC76701A">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="F6CF58F3-F332-4C16-8B77-3B3431B7291B">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="8CA595F7-F901-45BF-95D4-57AC366CECF0">
							<SHORT-NAME>NvSWC_NvMService_AC3_SRBS</SHORT-NAME>
							<IS-SERVICE>false</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="99C91C86-B05E-4B77-A67E-B6BE77423591">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="6EB40B4F-0D10-49DD-BB97-163F10D1DB57">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="A69471BC-92C8-4009-8074-2F44FB6CAF24">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="428ED409-F529-42A4-AE97-D92F4EFF33E6">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="88C729AB-F4EB-4D24-8B46-E322FF20E15D">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="078BF24E-BB46-4468-9695-9BB2D26D8777">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="6CE2F10E-4D52-417C-B1C1-3804219D6A13">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="C6B13C20-CC39-4B43-8AD8-41D96D3BED66">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="9A09C9B8-B304-4948-B3D5-F346CBED89E3">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="46B28183-CD40-4473-8216-756027A3FEBA">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="419091BB-CC3F-4518-9B87-F2C428DE884F">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="C61C9D58-96C4-45C4-9D63-DBD3006576D3">
							<SHORT-NAME>NvSWC_NvMService_AC3_SRBS_Defs</SHORT-NAME>
							<IS-SERVICE>false</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="888B1C39-C315-48E8-906D-FB3968CB55C0">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="353F0684-1A6B-4042-B97D-396E4AC14F96">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="7805E6DC-2F6E-4D3E-BB27-143E76043FAE">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="9C017156-45F6-427A-AA9E-3F0062B05673">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="46E4D6B6-ED61-4AA5-A684-FAFFFD8E429B">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="2BEBEFC7-0E8E-43D9-9E76-6DF434A4EC6E">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="A6106597-DB6A-401D-B6E1-A959604DAAE6">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="6F1407BA-D61A-4A56-8A52-D023E346C70F">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="C1BA4636-BBD7-41F0-8CEC-CE393044DCCC">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="3D9ADDCD-A39D-4972-88BE-72AC7FA4312E">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="B6A1D92B-0F25-4BD7-90D0-E3650F52E949">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="4D6528DC-7DAB-4F5A-B081-B029DD0335A2">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="B0DF499C-5446-40C9-BA84-69FE6BBA9AFA">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="4F0EDDA9-4F91-473F-9BF7-6643C9682C7B">
							<SHORT-NAME>NvSWC_NvMService_AC3_SRBS_DS</SHORT-NAME>
							<IS-SERVICE>false</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="F2C0DB3D-8DDB-450B-B394-5574319C08ED">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="F4773455-9CDA-441B-91DD-7E804B99EB99">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="A05A1A25-D59B-4B42-8DE8-B1534BC16D44">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="DC2F7622-5E63-40DE-A61F-125D690422D6">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="9FB1435A-C25D-4B52-9A84-137EEF874781">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="28648508-25AE-4640-909E-5C94E75B0C23">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="5C607D35-0FE7-4D6E-85B7-5241E437E9AF">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="770A9CC4-F406-4068-B357-46CF7FA4C719">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="B91F16C8-26DB-494E-85C6-EAD706E3FC4C">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="F630E3BC-EB05-4EFF-9AA2-67F438D2C54B">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="3DFFA9E3-86E9-484F-A5A4-6606C606B0E0">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="45CB4C9A-238D-456E-A51F-9D7BE373FA4F">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="3653365E-3277-424A-AFD5-D1AED277B2E6">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="E0AF72A4-0815-4E4A-918C-BE8324A33676">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="E2B7EDDF-443E-4066-8CC0-428C08FE9932">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="F4934383-9CF3-4BEE-9A06-C1A56AFBCB9D">
							<SHORT-NAME>NvSWC_NvMService_AC3_SRBS_DS_Defs</SHORT-NAME>
							<IS-SERVICE>false</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="BD8DB4BD-0358-497A-BBA5-5DFB0EA52176">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="53264D63-32E0-4109-8E0A-9915F6807033">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="4742456F-EE32-47D4-A523-CAFD800EF817">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="4BFBCFA0-AAE0-42F3-B351-6EDB9F980362">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="5323A12A-6F17-46A9-A30B-CF8E5399BBC6">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="0D71D531-F727-4DED-9C4B-B8CB45C6541E">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="6AA5090B-FA38-4E5B-BF55-3A55118498B9">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="3D40B9BF-FC96-49B0-B4D0-EA8142D4849E">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="2C98FA69-F041-4607-B8E7-DF8004D5B25C">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="DA027D35-2AA3-4D16-9BC0-632951B5886A">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="9FAC6533-7FD1-4411-8F42-754BE6E4FA3C">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="ED3BF6F9-16FA-4330-80D6-F29AF72AC133">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="A9BB5F93-3F87-4DEC-83D9-FCF99124B711">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="C4A6602E-C7CD-4F86-9CEB-6C7D041FDFC2">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="587E140B-8F65-4B13-A6C8-EB621F0D0F55">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="21FA1B67-3766-489E-AB77-71318DD3A72A">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="29D8C45D-AD65-4ADB-A8C0-9051D3BA8EB3">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="CE1E246A-3C1A-4776-BF35-9D7B88C3D2B3">
							<SHORT-NAME>NvSWC_NvMService_SRBS</SHORT-NAME>
							<IS-SERVICE>false</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="CCC66E69-E0EF-4864-9F0C-3B5429FB8DDE">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="23F48AA1-7EBC-47C5-B7F7-B9E6028AD3E3">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/Predefined_DEV/ImplementationDataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="8CB498FF-E19B-4744-B4DB-11BE5A525877">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="2ACDC522-7BD3-423A-A75A-F9EC9998294D">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
											<SERVER-ARGUMENT-IMPL-POLICY>USE-ARGUMENT-TYPE</SERVER-ARGUMENT-IMPL-POLICY>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/PortInterfaces/NvSWC_NvMService_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="6C11D3FD-1F97-4F18-9F28-AEBD06095420">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
