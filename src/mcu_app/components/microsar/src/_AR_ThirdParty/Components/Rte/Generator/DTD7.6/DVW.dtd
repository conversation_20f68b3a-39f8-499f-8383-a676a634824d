<!-- Entities -->
<!ENTITY DtdVersion "7.6">

<!ENTITY % A.DTDVersion  "DTDVersion CDATA '&DtdVersion;'">
<!ENTITY % A.GUID        "GUID      CDATA '{00000000-0000-0000-0000-000000000000}'">
<!ENTITY % A.GUID2       "GUID2     CDATA ''">
<!ENTITY % A.Version     "Version   CDATA '0'">
<!ENTITY % A.CMVersion   "CMVersion CDATA ''">
<!ENTITY % A.CMState     "CMState   CDATA ''">
<!ENTITY % A.ReadOnly    "RO        CDATA '0'">

<!ENTITY % A.LinkGUID           "LinkGUID CDATA ''">
<!ENTITY % A.LinkChildGUID      "LinkChildGUID CDATA ''">
<!ENTITY % A.LinkVersion        "LinkVersion  CDATA ''">
<!ENTITY % A.LinkType           "LinkType  NMTOKEN #FIXED">
<!ENTITY % A.<PERSON>ny        "LinkType  CDATA ''">
<!ENTITY % A.LinkName           "LinkName">
<!ENTITY % A.External           "External CDATA '0'">

<!ENTITY % A.ConfigItem  "%A.CMVersion;
                          %A.CMState;
                          %A.GUID;
                          %A.Version;
                          %A.DTDVersion;
                          %A.ReadOnly;
                          %A.GUID2;
                          %A.External;">

<!ENTITY % A.ChildItem   "%A.GUID;
                          %A.GUID2;">

<!ENTITY % C.DefAttr     "NAME,LONGNAME?,DESC?">

<!ENTITY % AttrDefSetDtd            SYSTEM "AttrDefSet.dtd">
<!ENTITY % BaseTypeDtd              SYSTEM "BaseType.dtd">
<!ENTITY % BaseTypePoolDtd          SYSTEM "BaseTypePool.dtd">
<!ENTITY % ComponentTypeDtd         SYSTEM "ComponentType.dtd">
<!ENTITY % CompuMethodDtd           SYSTEM "CompuMethod.dtd">
<!ENTITY % CompuMethodPoolDtd       SYSTEM "CompuMethodPool.dtd">
<!ENTITY % ConstantDtd              SYSTEM "Constant.dtd">
<!ENTITY % ConstantPoolDtd          SYSTEM "ConstantPool.dtd">
<!ENTITY % ConstMappingSetDtd       SYSTEM "ConstMappingSet.dtd">
<!ENTITY % DataConstrDtd            SYSTEM "DataConstr.dtd">
<!ENTITY % DataConstrPoolDtd        SYSTEM "DataConstrPool.dtd">
<!ENTITY % DataTypeDtd              SYSTEM "DataType.dtd">
<!ENTITY % DataTypePoolDtd          SYSTEM "DataTypePool.dtd">
<!ENTITY % ECUProjectDtd            SYSTEM "ECUProject.dtd">
<!ENTITY % MappingSetDtd            SYSTEM "MappingSet.dtd">
<!ENTITY % ModeDclrGroupDtd         SYSTEM "ModeDclrGroup.dtd">
<!ENTITY % ModeDclrGroupPoolDtd     SYSTEM "ModeDclrGroupPool.dtd">
<!ENTITY % PackageDtd               SYSTEM "Package.dtd">
<!ENTITY % PortInterfaceDtd         SYSTEM "PortInterface.dtd">
<!ENTITY % PortInterfacePoolDtd     SYSTEM "PortInterfacePool.dtd">
<!ENTITY % UnitDtd                  SYSTEM "Unit.dtd">
<!ENTITY % UnitPoolDtd              SYSTEM "UnitPool.dtd">
<!ENTITY % BlueprintDtd             SYSTEM "Blueprint.dtd">
<!ENTITY % BlueprintPoolDtd         SYSTEM "BlueprintPool.dtd">
<!ENTITY % BlueprintMapSetDtd       SYSTEM "BlueprintMapSet.dtd">
<!ENTITY % PortIntMapSetDtd         SYSTEM "PortIntMapSet.dtd">
<!ENTITY % EvalVariantSetDtd        SYSTEM "EvalVariantSet.dtd">
<!ENTITY % PredefVariantDtd         SYSTEM "PredefVariant.dtd">
<!ENTITY % PBVCritValueSetDtd       SYSTEM "PBVCritValueSet.dtd">
<!ENTITY % PBVCriterionDtd          SYSTEM "PBVCriterion.dtd">

<!ENTITY ti.AttrDefSet  "AttrDefSet">

<!NOTATION EntityType   SYSTEM "">

<!-- DTD includes -->

%AttrDefSetDtd;
%BaseTypeDtd;
%BaseTypePoolDtd;
%ComponentTypeDtd;
%CompuMethodDtd;
%CompuMethodPoolDtd;
%ConstantDtd;
%ConstantPoolDtd;
%ConstMappingSetDtd;
%DataConstrDtd;
%DataConstrPoolDtd;
%DataTypeDtd;
%DataTypePoolDtd;
%ECUProjectDtd;
%MappingSetDtd;
%ModeDclrGroupDtd;
%ModeDclrGroupPoolDtd;
%PackageDtd;
%PortInterfaceDtd;
%PortInterfacePoolDtd;
%UnitDtd;
%UnitPoolDtd;
%BlueprintDtd;
%BlueprintPoolDtd;
%BlueprintMapSetDtd;
%PortIntMapSetDtd;
%EvalVariantSetDtd;
%PredefVariantDtd;
%PBVCritValueSetDtd;
%PBVCriterionDtd;

<!-- Elements -->

<!ELEMENT DVW (NAME?,LONGNAME?,ATTRLink*,AttrDefSetLink*,ConstantLink*,DataTypeLink*,ComponentTypeLink*,ECUProjectLink*,PortInterfaceLink*,ModeDclrGroupLink*,CompatibleVersions?,PackageLink*,BaseTypeLink*,UnitLink*,CompuMethodLink*,DataConstrLink*,MappingSetLink*,ConstMappingSetLink*,BlueprintLink*,BlueprintMapSetLink*, PortIntMapSetLink*, EvalVariantSetLink*, PredefVariantLink*, PBVCritValueSetLink*, PBVCriterionLink*)>

<!ATTLIST DVW %A.ConfigItem; 
          InstVers          CDATA  #IMPLIED
          SpecialBuild      CDATA  ""
          AUTOSARVersion    CDATA  "AUTOSAR3X"
          AttrDefSetLink    NMTOKEN #FIXED "&ti.AttrDefSet;"
>

<!ELEMENT DESC          (#PCDATA)>
<!ELEMENT NAME          (#PCDATA)>
<!ELEMENT LONGNAME      (#PCDATA)>
<!ELEMENT CONTENT       (#PCDATA)>
<!ELEMENT DTDVersion    (#PCDATA)>

<!ELEMENT CompatibleVersions (DTDVersion*)>

<!ATTLIST CONTENT       
          Version   CDATA '0'
>

<!ELEMENT ATTRLink EMPTY>
<!ATTLIST ATTRLink 
          %A.LinkGUID;
          %A.LinkVersion;
          %A.LinkChildGUID;
          %A.LinkTypeAny; 
          %A.LinkName;  CDATA ""
>

<!ELEMENT AttrValue         (DESC?,ATTRLink*,AttrDefinition?)>
<!ATTLIST AttrValue  
          %A.ChildItem;
          BaseAttrDefLink    NMTOKEN #FIXED "&ti.AttrDefinition;"
>

<!ELEMENT GenAttrList       (ATTRLink*,GenAttr*)>
<!ATTLIST GenAttrList    
          %A.ChildItem;
          AttrDefSetLink    NMTOKEN #FIXED "&ti.AttrDefSet;"
>

<!ELEMENT GenAttr  (%C.DefAttr;,AttrValue)>
<!ATTLIST GenAttr    
          %A.ChildItem;
          Category          CDATA ""
>

<!ELEMENT FileEntry         (%C.DefAttr;,CONTENT*)>
<!ATTLIST FileEntry    
          %A.ChildItem;
          FileName          CDATA ""
          FileType          CDATA ""
          FileCategory      CDATA ""
          FilePath          CDATA ""
          FileUsage         (ALL|SKIP) "ALL"
          Parameter         CDATA ""
          AssignedAdapter   CDATA ""
          TimeStamp         CDATA ""
          AdptCreationStr   CDATA ""
>

<!ELEMENT VariationPoint    (PBVCondition*)>
<!ATTLIST VariationPoint    %A.ChildItem;
          Label             CDATA ""
>

<!ELEMENT PBVCondition      (ATTRLink?)>
<!ATTLIST PBVCondition      %A.ChildItem;
          Value             CDATA ""
          PBVCriterionLink  NMTOKEN #FIXED "&ti.PBVCriterion;"
>
