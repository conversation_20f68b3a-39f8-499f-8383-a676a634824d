<!-- Entities -->

<!ENTITY ti.EvalVariantSet    "EvalVariantSet">
<!ENTITY ti.PredefVariant     "PredefVariant">
<!ENTITY ti.Package           "Package">

<!-- Elements -->

<!ELEMENT EvalVariantSet (%C.DefAttr;, ATTRLink*, PredefVariantLink*, GenAttrList?)>

<!ATTLIST EvalVariantSet %A.Config<PERSON>tem;
    PackageLink          NMTOKEN #FIXED "&ti.Package;"
>

<!-- Links -->
<!ELEMENT EvalVariantSetLink EMPTY>
<!ATTLIST EvalVariantSetLink 
          %A.LinkGUID;
          %A<PERSON>;
          %A.LinkType; "&ti.EvalVariantSet;">
