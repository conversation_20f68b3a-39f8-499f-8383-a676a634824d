<?xml version="1.0" encoding="UTF-8"?>
<AUTOSAR xsi:schemaLocation="http://autosar.org/3.2.1 autosar_321.xsd" xmlns="http://autosar.org/3.2.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<TOP-LEVEL-PACKAGES>
		<AR-PACKAGE>
			<SHORT-NAME>DataType</SHORT-NAME>
			<ELEMENTS>
				<BOOLEAN-TYPE>
					<SHORT-NAME>Boolean</SHORT-NAME>
					<DESC>
						<L-2 L="FOR-ALL"><PERSON><PERSON><PERSON> has the value space required to support the mathematical concept of 
binary-valued logic: {true, false}.</L-2>
					</DESC>
				</BOOLEAN-TYPE>
				<INTEGER-TYPE>
					<SHORT-NAME>SInt16</SHORT-NAME>
					<DESC>
						<L-2 L="FOR-ALL">SInt16 represents integers with a minimum value of -32768 and a maximum value 
of 32767. The order-relation on SInt16 is: x &lt; y if y - x is positive.
SInt16 has a lexical representation consisting of an optional sign followed
by a finite-length sequence of decimal digits (#x30-#x39). If the sign is
omitted, "+" is assumed. 

For example: -1, 0, -12678, +10000, 2500</L-2>
					</DESC>
					<LOWER-LIMIT INTERVAL-TYPE="CLOSED">-32768</LOWER-LIMIT>
					<UPPER-LIMIT INTERVAL-TYPE="CLOSED">32767</UPPER-LIMIT>
				</INTEGER-TYPE>
				<INTEGER-TYPE>
					<SHORT-NAME>SInt32</SHORT-NAME>
					<DESC>
						<L-2 L="FOR-ALL">SInt32 represents integers with a minimum value of -2147483648 and a maximum 
value of 2147483647. The order-relation on SInt32 is: x &lt; y if y - x is
positive. SInt32 has a lexical representation consisting of an optional sign 
allowed by a finite-length sequence of decimal digits (#x30-#x39). If the 
sign is omitted, "+" is assumed. 

For example: -1, 0, -12688778, +10000, 250098675.</L-2>
					</DESC>
					<LOWER-LIMIT INTERVAL-TYPE="CLOSED">-2147483648</LOWER-LIMIT>
					<UPPER-LIMIT INTERVAL-TYPE="CLOSED">2147483647</UPPER-LIMIT>
				</INTEGER-TYPE>
				<INTEGER-TYPE>
					<SHORT-NAME>SInt4</SHORT-NAME>
					<DESC>
						<L-2 L="FOR-ALL">SInt4 represents integers with a minimum value of -8 and a maximum value of 7.
The order-relation on SInt4 is: x &lt; y if y - x is positive.
SInt4 has a lexical representation consisting of an optional sign followed 
by a finite-length sequence of decimal digits (#x30-#x39). If the sign is 
omitted, "+" is assumed. 

For example: -1, 0, 2, +4.</L-2>
					</DESC>
					<LOWER-LIMIT INTERVAL-TYPE="CLOSED">-8</LOWER-LIMIT>
					<UPPER-LIMIT INTERVAL-TYPE="CLOSED">7</UPPER-LIMIT>
				</INTEGER-TYPE>
				<INTEGER-TYPE>
					<SHORT-NAME>SInt8</SHORT-NAME>
					<DESC>
						<L-2 L="FOR-ALL">SInt8 represents integers with a minimum value of -128 and a maximum value of 127.
The order-relation on SInt8 is: x &lt; y if y - x is positive.
SInt8 has a lexical representation consisting of an optional sign followed 
by a finite-length sequence of decimal digits (#x30-#x39). If the sign is 
omitted, "+" is assumed. 

For example: -1, 0, 12678, +10000.</L-2>
					</DESC>
					<LOWER-LIMIT INTERVAL-TYPE="CLOSED">-128</LOWER-LIMIT>
					<UPPER-LIMIT INTERVAL-TYPE="CLOSED">127</UPPER-LIMIT>
				</INTEGER-TYPE>
				<INTEGER-TYPE>
					<SHORT-NAME>UInt16</SHORT-NAME>
					<DESC>
						<L-2 L="FOR-ALL">UInt16 represents integers with a minimum value of 0 and a maximum value of 65535.
The order-relation on UInt16 is: x &lt; y if y - x is positive.
UInt16 has a lexical representation consisting of a finite-length sequence 
of decimal digits (#x30-#x39).

For example: 1, 0, 1267, +10000.</L-2>
					</DESC>
					<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
					<UPPER-LIMIT INTERVAL-TYPE="CLOSED">65535</UPPER-LIMIT>
				</INTEGER-TYPE>
				<INTEGER-TYPE>
					<SHORT-NAME>UInt32</SHORT-NAME>
					<DESC>
						<L-2 L="FOR-ALL">UInt32 represents integers with a minimum value of 0 and a maximum value 
of 4294967295. The order-relation on UInt32 is: x &lt; y if y - x is positive.
UInt32 has a lexical representation consisting of a finite-length sequence 
of decimal digits (#x30-#x39). 

For example: 1, 0, 12234567, 104400.</L-2>
					</DESC>
					<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
					<UPPER-LIMIT INTERVAL-TYPE="CLOSED">4294967295</UPPER-LIMIT>
				</INTEGER-TYPE>
				<INTEGER-TYPE>
					<SHORT-NAME>UInt4</SHORT-NAME>
					<DESC>
						<L-2 L="FOR-ALL">UInt4 represents integers with a minimum value of 0 and a maximum value of 15.
The order-relation on UInt4 is: x &lt; y if y - x is positive.
UInt4 has a lexical representation consisting of a finite-length sequence 
of decimal digits (#x30-#x39).

For example: 1, 0, 12, +10.</L-2>
					</DESC>
					<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
					<UPPER-LIMIT INTERVAL-TYPE="CLOSED">15</UPPER-LIMIT>
				</INTEGER-TYPE>
				<INTEGER-TYPE>
					<SHORT-NAME>UInt8</SHORT-NAME>
					<DESC>
						<L-2 L="FOR-ALL">UInt8 represents integers with a minimum value of 0 and a maximum value of 255.
The order-relation on UInt8 is: x &lt; y if y - x is positive.
UInt8 has a lexical representation consisting of a finite-length sequence 
of decimal digits (#x30-#x39).

For example: 1, 0, 126, +10.</L-2>
					</DESC>
					<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
					<UPPER-LIMIT INTERVAL-TYPE="CLOSED">255</UPPER-LIMIT>
				</INTEGER-TYPE>
				<REAL-TYPE>
					<SHORT-NAME>Double</SHORT-NAME>
					<DESC>
						<L-2 L="FOR-ALL">The Double datatype corresponds to IEEE double-precision 64-bit floating point
type [IEEE 754-1985]. The basic value space of Double consists of the values
m * 2^e, where m is an integer whose absolute value is less than 2^53, and e is
 an integer between -1075 and 970, inclusive. In addition to the basic value 
space described above, the value space of Double also contains the following 
special values: positive and negative zero, positive and negative infinity 
and not-a-number. 
The order-relation on double is: x &lt; y if y - x is positive. 
Positive zero is greater than negative zero. Not-a-number equals itself and 
is greater than all double values including positive infinity. 
                    
Double values have a lexical representation consisting of a mantissa followed,
optionally, by the character "E" or "e", followed by an exponent. 
The exponent must be an integer. The mantissa must be a decimal number.
The representations for exponent and mantissa must follow the lexical rules 
for integer and decimal. If the "E" or "e" and the following exponent are 
omitted, an exponent value of 0 is assumed. 

The special values positive and negative zero, positive and negative infinity
and not-a-number have lexical representations 0, -0, INF, -INF and NaN, 
respectively. 

For example, -1E4, 1267.43233E12, 12.78e-2, 12 and INF are all 
legal literals for Double.</L-2>
					</DESC>
					<LOWER-LIMIT INTERVAL-TYPE="INFINITE"></LOWER-LIMIT>
					<UPPER-LIMIT INTERVAL-TYPE="INFINITE"></UPPER-LIMIT>
					<ALLOW-NAN>true</ALLOW-NAN>
					<ENCODING>DOUBLE</ENCODING>
				</REAL-TYPE>
				<REAL-TYPE>
					<SHORT-NAME>Float</SHORT-NAME>
					<DESC>
						<L-2 L="FOR-ALL">Float corresponds to the IEEE single-precision 32-bit floating point type
[IEEE 754-1985]. The basic value space of float consists of the values 
m * 2^e, where m is an integer whose absolute value is less than 2^24, 
and e is an integer between -149 and 104, inclusive. In addition to the basic
value space described above, the value space of Float also contains the 
following special values: positive and negative zero, positive and negative 
infinity and not-a-number. The order-relation on Float is: 
x &lt; y if y - x is positive. Positive zero is greater than negative zero.
Not-a-number equals itself and is greater than all float values including positive infinity. 

Float values have a lexical representation consisting of a mantissa followed, 
optionally, by the character "E" or "e", followed by an exponent. The exponent
must be an integer. The mantissa must be a decimal number. The representations
for exponent and mantissa must follow the lexical rules for integer and decimal.
If the "E" or "e" and the following exponent are omitted, an exponent value 
of 0 is assumed. 

The special values positive and negative zero, positive and negative infinity
and not-a-number have lexical representations 0, -0, INF, -INF and NaN, 
respectively. 

For example, -1E4, 1267.43233E12, 12.78e-2, 12 and INF are all legal literals
for Float.</L-2>
					</DESC>
					<LOWER-LIMIT INTERVAL-TYPE="INFINITE"></LOWER-LIMIT>
					<UPPER-LIMIT INTERVAL-TYPE="INFINITE"></UPPER-LIMIT>
					<ALLOW-NAN>false</ALLOW-NAN>
					<ENCODING>SINGLE</ENCODING>
				</REAL-TYPE>
			</ELEMENTS>
			<SUB-PACKAGES>
				<AR-PACKAGE>
					<SHORT-NAME>DataTypeSemantics</SHORT-NAME>
				</AR-PACKAGE>
				<AR-PACKAGE>
					<SHORT-NAME>DataTypeUnits</SHORT-NAME>
				</AR-PACKAGE>
			</SUB-PACKAGES>
		</AR-PACKAGE>
	</TOP-LEVEL-PACKAGES>
</AUTOSAR>
