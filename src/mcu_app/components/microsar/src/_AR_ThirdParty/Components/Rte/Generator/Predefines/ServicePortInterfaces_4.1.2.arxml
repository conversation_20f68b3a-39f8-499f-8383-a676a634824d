<?xml version="1.0" encoding="utf-8"?>
<!-- edited with XMLSpy v2009 sp1 (http://www.altova.com) by Vector Employee (Vector Informatik GmbH) -->
<!--
This file was saved with a tool from Vector Informatik GmbH
-->
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-1-2.xsd" xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<AR-PACKAGES>
		<AR-PACKAGE>
			<SHORT-NAME>MICROSAR_SWCS_DEV</SHORT-NAME>
			<AR-PACKAGES>
				<AR-PACKAGE>
					<SHORT-NAME>NvM_swc</SHORT-NAME>
					<AR-PACKAGES>
						<AR-PACKAGE>
							<SHORT-NAME>DataTypes</SHORT-NAME>
							<ELEMENTS>
								<IMPLEMENTATION-DATA-TYPE UUID="336c6943-3c45-4195-9247-2b53ace85e8c">
									<SHORT-NAME>Boolean</SHORT-NAME>
									<CATEGORY>VALUE</CATEGORY>
									<SW-DATA-DEF-PROPS>
										<SW-DATA-DEF-PROPS-VARIANTS>
											<SW-DATA-DEF-PROPS-CONDITIONAL>
												<BASE-TYPE-REF DEST="SW-BASE-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/BaseTypes/Boolean</BASE-TYPE-REF>
												<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
											</SW-DATA-DEF-PROPS-CONDITIONAL>
										</SW-DATA-DEF-PROPS-VARIANTS>
									</SW-DATA-DEF-PROPS>
								</IMPLEMENTATION-DATA-TYPE>
								<IMPLEMENTATION-DATA-TYPE UUID="62507227-17cc-4a9d-846d-bd9e6dd84490">
									<SHORT-NAME>dtRef_const_VOID</SHORT-NAME>
									<CATEGORY>DATA_REFERENCE</CATEGORY>
									<SW-DATA-DEF-PROPS>
										<SW-DATA-DEF-PROPS-VARIANTS>
											<SW-DATA-DEF-PROPS-CONDITIONAL>
												<SW-POINTER-TARGET-PROPS>
													<TARGET-CATEGORY>VALUE</TARGET-CATEGORY>
													<SW-DATA-DEF-PROPS>
														<SW-DATA-DEF-PROPS-VARIANTS>
															<SW-DATA-DEF-PROPS-CONDITIONAL>
																<BASE-TYPE-REF DEST="SW-BASE-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/BaseTypes/dtRef_const_VOID</BASE-TYPE-REF>
																<SW-IMPL-POLICY>CONST</SW-IMPL-POLICY>
															</SW-DATA-DEF-PROPS-CONDITIONAL>
														</SW-DATA-DEF-PROPS-VARIANTS>
													</SW-DATA-DEF-PROPS>
												</SW-POINTER-TARGET-PROPS>
											</SW-DATA-DEF-PROPS-CONDITIONAL>
										</SW-DATA-DEF-PROPS-VARIANTS>
									</SW-DATA-DEF-PROPS>
								</IMPLEMENTATION-DATA-TYPE>
								<IMPLEMENTATION-DATA-TYPE UUID="15481263-05af-44b4-a593-9b92f4207ea4">
									<SHORT-NAME>dtRef_VOID</SHORT-NAME>
									<CATEGORY>DATA_REFERENCE</CATEGORY>
									<SW-DATA-DEF-PROPS>
										<SW-DATA-DEF-PROPS-VARIANTS>
											<SW-DATA-DEF-PROPS-CONDITIONAL>
												<SW-POINTER-TARGET-PROPS>
													<TARGET-CATEGORY>VALUE</TARGET-CATEGORY>
													<SW-DATA-DEF-PROPS>
														<SW-DATA-DEF-PROPS-VARIANTS>
															<SW-DATA-DEF-PROPS-CONDITIONAL>
																<BASE-TYPE-REF DEST="SW-BASE-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/BaseTypes/dtRef_VOID</BASE-TYPE-REF>
															</SW-DATA-DEF-PROPS-CONDITIONAL>
														</SW-DATA-DEF-PROPS-VARIANTS>
													</SW-DATA-DEF-PROPS>
												</SW-POINTER-TARGET-PROPS>
											</SW-DATA-DEF-PROPS-CONDITIONAL>
										</SW-DATA-DEF-PROPS-VARIANTS>
									</SW-DATA-DEF-PROPS>
								</IMPLEMENTATION-DATA-TYPE>
								<IMPLEMENTATION-DATA-TYPE UUID="ef86b7fd-ce05-47f1-83a3-3e823f67094c">
									<SHORT-NAME>NvM_RequestResultType</SHORT-NAME>
									<CATEGORY>VALUE</CATEGORY>
									<SW-DATA-DEF-PROPS>
										<SW-DATA-DEF-PROPS-VARIANTS>
											<SW-DATA-DEF-PROPS-CONDITIONAL>
												<BASE-TYPE-REF DEST="SW-BASE-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/BaseTypes/NvM_RequestResultType</BASE-TYPE-REF>
												<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
												<COMPU-METHOD-REF DEST="COMPU-METHOD">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/CompuMethods/NvM_RequestResultType</COMPU-METHOD-REF>
											</SW-DATA-DEF-PROPS-CONDITIONAL>
										</SW-DATA-DEF-PROPS-VARIANTS>
									</SW-DATA-DEF-PROPS>
								</IMPLEMENTATION-DATA-TYPE>
								<IMPLEMENTATION-DATA-TYPE UUID="abb229ba-1a35-4a01-9f76-039e15f8fda4">
									<SHORT-NAME>NvM_ServiceIdType</SHORT-NAME>
									<CATEGORY>VALUE</CATEGORY>
									<SW-DATA-DEF-PROPS>
										<SW-DATA-DEF-PROPS-VARIANTS>
											<SW-DATA-DEF-PROPS-CONDITIONAL>
												<BASE-TYPE-REF DEST="SW-BASE-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/BaseTypes/NvM_ServiceIdType</BASE-TYPE-REF>
												<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
												<COMPU-METHOD-REF DEST="COMPU-METHOD">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/CompuMethods/NvM_ServiceIdType</COMPU-METHOD-REF>
											</SW-DATA-DEF-PROPS-CONDITIONAL>
										</SW-DATA-DEF-PROPS-VARIANTS>
									</SW-DATA-DEF-PROPS>
								</IMPLEMENTATION-DATA-TYPE>
								<IMPLEMENTATION-DATA-TYPE UUID="1a444139-6f87-4695-a514-bd658dac467a">
									<SHORT-NAME>UInt8</SHORT-NAME>
									<CATEGORY>VALUE</CATEGORY>
									<SW-DATA-DEF-PROPS>
										<SW-DATA-DEF-PROPS-VARIANTS>
											<SW-DATA-DEF-PROPS-CONDITIONAL>
												<BASE-TYPE-REF DEST="SW-BASE-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/BaseTypes/UInt8</BASE-TYPE-REF>
												<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
												<DATA-CONSTR-REF DEST="DATA-CONSTR">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/DataConstrs/UInt8_Constr</DATA-CONSTR-REF>
											</SW-DATA-DEF-PROPS-CONDITIONAL>
										</SW-DATA-DEF-PROPS-VARIANTS>
									</SW-DATA-DEF-PROPS>
								</IMPLEMENTATION-DATA-TYPE>
							</ELEMENTS>
							<AR-PACKAGES>
								<AR-PACKAGE>
									<SHORT-NAME>BaseTypes</SHORT-NAME>
									<ELEMENTS>
										<SW-BASE-TYPE UUID="41a3c193-5372-43db-b9df-33823b187afa">
											<SHORT-NAME>Boolean</SHORT-NAME>
											<CATEGORY>FIXED_LENGTH</CATEGORY>
											<BASE-TYPE-SIZE>1</BASE-TYPE-SIZE>
											<BASE-TYPE-ENCODING>BOOLEAN</BASE-TYPE-ENCODING>
											<NATIVE-DECLARATION>boolean</NATIVE-DECLARATION>
										</SW-BASE-TYPE>
										<SW-BASE-TYPE UUID="cfc7af85-c00c-4e19-ac6c-6e528155e28c">
											<SHORT-NAME>dtRef_const_VOID</SHORT-NAME>
											<CATEGORY>FIXED_LENGTH</CATEGORY>
											<BASE-TYPE-SIZE>1</BASE-TYPE-SIZE>
											<BASE-TYPE-ENCODING>VOID</BASE-TYPE-ENCODING>
											<NATIVE-DECLARATION>void</NATIVE-DECLARATION>
										</SW-BASE-TYPE>
										<SW-BASE-TYPE UUID="4ee52b6d-81d9-4838-9228-1cbea96240cf">
											<SHORT-NAME>dtRef_VOID</SHORT-NAME>
											<CATEGORY>FIXED_LENGTH</CATEGORY>
											<BASE-TYPE-SIZE>1</BASE-TYPE-SIZE>
											<BASE-TYPE-ENCODING>VOID</BASE-TYPE-ENCODING>
											<NATIVE-DECLARATION>void</NATIVE-DECLARATION>
										</SW-BASE-TYPE>
										<SW-BASE-TYPE UUID="9f97d5ac-800c-424d-a299-23ef393dba12">
											<SHORT-NAME>NvM_RequestResultType</SHORT-NAME>
											<CATEGORY>FIXED_LENGTH</CATEGORY>
											<BASE-TYPE-SIZE>8</BASE-TYPE-SIZE>
											<BASE-TYPE-ENCODING>NONE</BASE-TYPE-ENCODING>
											<NATIVE-DECLARATION>uint8</NATIVE-DECLARATION>
										</SW-BASE-TYPE>
										<SW-BASE-TYPE UUID="a5fb976a-758a-4b69-8819-5a90550f993a">
											<SHORT-NAME>NvM_ServiceIdType</SHORT-NAME>
											<CATEGORY>FIXED_LENGTH</CATEGORY>
											<BASE-TYPE-SIZE>8</BASE-TYPE-SIZE>
											<BASE-TYPE-ENCODING>NONE</BASE-TYPE-ENCODING>
											<NATIVE-DECLARATION>uint8</NATIVE-DECLARATION>
										</SW-BASE-TYPE>
										<SW-BASE-TYPE UUID="3d44ca3c-c06e-4286-8bc8-2f12c6dcaf23">
											<SHORT-NAME>UInt8</SHORT-NAME>
											<CATEGORY>FIXED_LENGTH</CATEGORY>
											<BASE-TYPE-SIZE>8</BASE-TYPE-SIZE>
											<BASE-TYPE-ENCODING>NONE</BASE-TYPE-ENCODING>
											<NATIVE-DECLARATION>uint8</NATIVE-DECLARATION>
										</SW-BASE-TYPE>
									</ELEMENTS>
								</AR-PACKAGE>
								<AR-PACKAGE>
									<SHORT-NAME>CompuMethods</SHORT-NAME>
									<ELEMENTS>
										<COMPU-METHOD UUID="d6936161-c7ca-4ca9-8ef6-06b0b35ba574">
											<SHORT-NAME>NvM_RequestResultType</SHORT-NAME>
											<CATEGORY>TEXTTABLE</CATEGORY>
											<COMPU-INTERNAL-TO-PHYS>
												<COMPU-SCALES>
													<COMPU-SCALE>
														<SHORT-LABEL>NVM_REQ_OK</SHORT-LABEL>
														<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
														<UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
														<COMPU-CONST>
															<VT>NVM_REQ_OK</VT>
														</COMPU-CONST>
													</COMPU-SCALE>
													<COMPU-SCALE>
														<SHORT-LABEL>NVM_REQ_NOT_OK</SHORT-LABEL>
														<LOWER-LIMIT INTERVAL-TYPE="CLOSED">1</LOWER-LIMIT>
														<UPPER-LIMIT INTERVAL-TYPE="CLOSED">1</UPPER-LIMIT>
														<COMPU-CONST>
															<VT>NVM_REQ_NOT_OK</VT>
														</COMPU-CONST>
													</COMPU-SCALE>
													<COMPU-SCALE>
														<SHORT-LABEL>NVM_REQ_PENDING</SHORT-LABEL>
														<LOWER-LIMIT INTERVAL-TYPE="CLOSED">2</LOWER-LIMIT>
														<UPPER-LIMIT INTERVAL-TYPE="CLOSED">2</UPPER-LIMIT>
														<COMPU-CONST>
															<VT>NVM_REQ_PENDING</VT>
														</COMPU-CONST>
													</COMPU-SCALE>
													<COMPU-SCALE>
														<SHORT-LABEL>NVM_REQ_INTEGRITY_FAILED</SHORT-LABEL>
														<LOWER-LIMIT INTERVAL-TYPE="CLOSED">3</LOWER-LIMIT>
														<UPPER-LIMIT INTERVAL-TYPE="CLOSED">3</UPPER-LIMIT>
														<COMPU-CONST>
															<VT>NVM_REQ_INTEGRITY_FAILED</VT>
														</COMPU-CONST>
													</COMPU-SCALE>
													<COMPU-SCALE>
														<SHORT-LABEL>NVM_REQ_BLOCK_SKIPPED</SHORT-LABEL>
														<LOWER-LIMIT INTERVAL-TYPE="CLOSED">4</LOWER-LIMIT>
														<UPPER-LIMIT INTERVAL-TYPE="CLOSED">4</UPPER-LIMIT>
														<COMPU-CONST>
															<VT>NVM_REQ_BLOCK_SKIPPED</VT>
														</COMPU-CONST>
													</COMPU-SCALE>
													<COMPU-SCALE>
														<SHORT-LABEL>NVM_REQ_NV_INVALIDATED</SHORT-LABEL>
														<LOWER-LIMIT INTERVAL-TYPE="CLOSED">5</LOWER-LIMIT>
														<UPPER-LIMIT INTERVAL-TYPE="CLOSED">5</UPPER-LIMIT>
														<COMPU-CONST>
															<VT>NVM_REQ_NV_INVALIDATED</VT>
														</COMPU-CONST>
													</COMPU-SCALE>
													<COMPU-SCALE>
														<SHORT-LABEL>NVM_REQ_CANCELED</SHORT-LABEL>
														<LOWER-LIMIT INTERVAL-TYPE="CLOSED">6</LOWER-LIMIT>
														<UPPER-LIMIT INTERVAL-TYPE="CLOSED">6</UPPER-LIMIT>
														<COMPU-CONST>
															<VT>NVM_REQ_CANCELED</VT>
														</COMPU-CONST>
													</COMPU-SCALE>
													<COMPU-SCALE>
														<SHORT-LABEL>NVM_REQ_REDUNDANCY_FAILED</SHORT-LABEL>
														<LOWER-LIMIT INTERVAL-TYPE="CLOSED">7</LOWER-LIMIT>
														<UPPER-LIMIT INTERVAL-TYPE="CLOSED">7</UPPER-LIMIT>
														<COMPU-CONST>
															<VT>NVM_REQ_REDUNDANCY_FAILED</VT>
														</COMPU-CONST>
													</COMPU-SCALE>
													<COMPU-SCALE>
														<SHORT-LABEL>NVM_REQ_RESTORED_FROM_ROM</SHORT-LABEL>
														<LOWER-LIMIT INTERVAL-TYPE="CLOSED">8</LOWER-LIMIT>
														<UPPER-LIMIT INTERVAL-TYPE="CLOSED">8</UPPER-LIMIT>
														<COMPU-CONST>
															<VT>NVM_REQ_RESTORED_FROM_ROM</VT>
														</COMPU-CONST>
													</COMPU-SCALE>
												</COMPU-SCALES>
											</COMPU-INTERNAL-TO-PHYS>
										</COMPU-METHOD>
										<COMPU-METHOD UUID="205ba1b6-ffb2-4fad-bd3e-f42bc9388caf">
											<SHORT-NAME>NvM_ServiceIdType</SHORT-NAME>
											<CATEGORY>TEXTTABLE</CATEGORY>
											<COMPU-INTERNAL-TO-PHYS>
												<COMPU-SCALES>
													<COMPU-SCALE>
														<SHORT-LABEL>NVM_READ_BLOCK</SHORT-LABEL>
														<LOWER-LIMIT INTERVAL-TYPE="CLOSED">6</LOWER-LIMIT>
														<UPPER-LIMIT INTERVAL-TYPE="CLOSED">6</UPPER-LIMIT>
														<COMPU-CONST>
															<VT>NVM_READ_BLOCK</VT>
														</COMPU-CONST>
													</COMPU-SCALE>
													<COMPU-SCALE>
														<SHORT-LABEL>NVM_WRITE_BLOCK</SHORT-LABEL>
														<LOWER-LIMIT INTERVAL-TYPE="CLOSED">7</LOWER-LIMIT>
														<UPPER-LIMIT INTERVAL-TYPE="CLOSED">7</UPPER-LIMIT>
														<COMPU-CONST>
															<VT>NVM_WRITE_BLOCK</VT>
														</COMPU-CONST>
													</COMPU-SCALE>
													<COMPU-SCALE>
														<SHORT-LABEL>NVM_RESTORE_BLOCK_DEFAULTS</SHORT-LABEL>
														<LOWER-LIMIT INTERVAL-TYPE="CLOSED">8</LOWER-LIMIT>
														<UPPER-LIMIT INTERVAL-TYPE="CLOSED">8</UPPER-LIMIT>
														<COMPU-CONST>
															<VT>NVM_RESTORE_BLOCK_DEFAULTS</VT>
														</COMPU-CONST>
													</COMPU-SCALE>
													<COMPU-SCALE>
														<SHORT-LABEL>NVM_ERASE_BLOCK</SHORT-LABEL>
														<LOWER-LIMIT INTERVAL-TYPE="CLOSED">9</LOWER-LIMIT>
														<UPPER-LIMIT INTERVAL-TYPE="CLOSED">9</UPPER-LIMIT>
														<COMPU-CONST>
															<VT>NVM_ERASE_BLOCK</VT>
														</COMPU-CONST>
													</COMPU-SCALE>
													<COMPU-SCALE>
														<SHORT-LABEL>NVM_INVALIDATE_NV_BLOCK</SHORT-LABEL>
														<LOWER-LIMIT INTERVAL-TYPE="CLOSED">11</LOWER-LIMIT>
														<UPPER-LIMIT INTERVAL-TYPE="CLOSED">11</UPPER-LIMIT>
														<COMPU-CONST>
															<VT>NVM_INVALIDATE_NV_BLOCK</VT>
														</COMPU-CONST>
													</COMPU-SCALE>
													<COMPU-SCALE>
														<SHORT-LABEL>NVM_READ_ALL</SHORT-LABEL>
														<LOWER-LIMIT INTERVAL-TYPE="CLOSED">12</LOWER-LIMIT>
														<UPPER-LIMIT INTERVAL-TYPE="CLOSED">12</UPPER-LIMIT>
														<COMPU-CONST>
															<VT>NVM_READ_ALL</VT>
														</COMPU-CONST>
													</COMPU-SCALE>
												</COMPU-SCALES>
											</COMPU-INTERNAL-TO-PHYS>
										</COMPU-METHOD>
									</ELEMENTS>
								</AR-PACKAGE>
								<AR-PACKAGE>
									<SHORT-NAME>DataConstrs</SHORT-NAME>
									<ELEMENTS>
										<DATA-CONSTR UUID="6f84ff03-fe9c-4351-88e1-f125d2f17c6f">
											<SHORT-NAME>UInt8_Constr</SHORT-NAME>
											<DATA-CONSTR-RULES>
												<DATA-CONSTR-RULE>
													<INTERNAL-CONSTRS>
														<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
														<UPPER-LIMIT INTERVAL-TYPE="CLOSED">255</UPPER-LIMIT>
													</INTERNAL-CONSTRS>
												</DATA-CONSTR-RULE>
											</DATA-CONSTR-RULES>
										</DATA-CONSTR>
									</ELEMENTS>
								</AR-PACKAGE>
							</AR-PACKAGES>
						</AR-PACKAGE>
					</AR-PACKAGES>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
		<AR-PACKAGE>
			<SHORT-NAME>Predefined_DEV</SHORT-NAME>
			<AR-PACKAGES>
				<AR-PACKAGE>
					<SHORT-NAME>DataConstraints</SHORT-NAME>
					<ELEMENTS>
						<DATA-CONSTR>
							<SHORT-NAME>Dem_UdsStatusByteType</SHORT-NAME>
							<DATA-CONSTR-RULES>
								<DATA-CONSTR-RULE>
									<INTERNAL-CONSTRS>
										<LOWER-LIMIT>0x00</LOWER-LIMIT>
										<UPPER-LIMIT>0xFF</UPPER-LIMIT>
									</INTERNAL-CONSTRS>
								</DATA-CONSTR-RULE>
							</DATA-CONSTR-RULES>
						</DATA-CONSTR>
					</ELEMENTS>
				</AR-PACKAGE>
				<AR-PACKAGE>
					<SHORT-NAME>ServicePortInterfaces</SHORT-NAME>
					<ELEMENTS>
						<CLIENT-SERVER-INTERFACE UUID="8b731bc4-d35c-4096-bd70-c38c59466e16">
							<SHORT-NAME>NvMAdministration</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="40945393-0ca8-4798-9410-e25d7a71fcb3">
									<SHORT-NAME>SetBlockProtection</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="d41a7b64-e617-4608-bb02-db720cf2a5e3">
											<SHORT-NAME>ProtectionEnabled</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/Boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMAdministration/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="65a745b7-0d72-4eb4-9453-7306f95407ee">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="e138c74c-cf0d-4799-ae89-b508111c79ef">
							<SHORT-NAME>NvMNotifyInitBlock</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="f18882a6-8bd3-4942-bc4d-33d702de0f88">
									<SHORT-NAME>InitBlock</SHORT-NAME>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="7ff4c18b-9a63-4695-8194-9228410cd639">
							<SHORT-NAME>NvMNotifyJobFinished</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="b64bc0a7-05d8-44b1-a70a-************">
									<SHORT-NAME>JobFinished</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="98abd3d6-bae4-4bb9-93ec-d41babfad795">
											<SHORT-NAME>ServiceId</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/NvM_ServiceIdType</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
										<ARGUMENT-DATA-PROTOTYPE UUID="63b16778-42ce-4768-8bc8-f887525da119">
											<SHORT-NAME>JobResult</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="3e9f4736-d77c-4074-990c-7cf36e90fb9d">
							<SHORT-NAME>NvMService</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="a7d6197b-8812-474a-99a6-59ce3bff94ec">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="787b976f-29c3-4097-b697-209c9ef76f37">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="db8a7a47-bbf1-4bd3-8a48-b7ace6099ac8">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="c8b8f0b0-3ab2-451e-aac0-d1d28bd156dc">
							<SHORT-NAME>NvMService_AC2</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="e2afce94-2a2d-4b47-a973-02c738c61d6f">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="5d8b6ab1-6b39-46f2-9e06-9120406901ff">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="60cda58e-f017-4ca2-bff6-f0a8dd2edf88">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="1618a0aa-bd11-450a-a005-c8dcd8866e96">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="9fb74f17-be44-4fdb-b2a6-9128732ef788">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="d3f772f6-404b-4468-bed7-50f15aef4a42">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="380cc5f8-b266-4a3e-be25-05ea0f983d34">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="f0526e46-3515-481c-9b0c-65221f83a3d3">
							<SHORT-NAME>NvMService_AC2_Defs</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="6442444a-bcd1-4648-872d-42be641c8924">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="030c1b75-b27b-45e3-8fde-ac7ee594d277">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="c4928c5b-acb1-4e26-9820-6300747651df">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="0c4ea1bf-175d-4ea0-9703-9af9389a7207">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="62da0054-1603-45b7-b225-010c39c3811e">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="dff4853f-87f2-48c6-96ba-fb97cd7ed1be">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="2e8ba169-4203-4a27-97f6-1d5670bc75d3">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="dbb500e5-6846-4ce1-ab59-f044d24007d7">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="a75367e0-1e51-4d2c-833d-1ab7fb8d538f">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="93b5a2f6-a833-406f-a89a-df6be680f8fb">
							<SHORT-NAME>NvMService_AC2_DS</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="60b54ad0-983e-4349-bf7c-00722e0b6ef2">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="69a4091c-2de7-41fd-9364-df82799cb99b">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/UInt8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="b3beaea4-5216-4e7e-805f-1d883acbb244">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="c82243f0-9f8c-4720-b987-738ab25cac0c">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="15d21c85-3953-43ec-9d38-007c2dfaa415">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="1337d0ca-11e5-46f2-8f78-51757645d559">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="cff9c3d9-be21-4449-b913-fc5dba998fcd">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="26cc23b4-85d1-4a59-b48b-ae782f3963ea">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/UInt8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="083a7681-9ba0-4bda-9a53-97675fcb2a24">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="2d7f332c-0bce-4f77-837f-3e2df83577eb">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="ffe90917-5c6b-409b-a1ea-7f8bb4649935">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="2f7ac1da-be4b-4538-af48-2074efe8971d">
							<SHORT-NAME>NvMService_AC2_DS_Defs</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="b5e56316-6245-45f0-94c5-d7ab98d2ceca">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="182f48da-d808-4c48-8c15-c6138ec71319">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/UInt8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="46ccccaf-aaf1-45ae-aade-a2919a5bdd45">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="47c9ad93-b8c9-41e5-a60f-70e82f1fb909">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="19f1bfc9-4a5d-46c4-abb3-35ccace4e853">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="70396572-48b3-4ebd-8a59-53f25156f9a0">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="9021cd47-c7c9-40d3-acc4-c578df9bfd44">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="57f88a70-9484-4ffe-b41c-56b443387fec">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="54c7f24d-9963-4d90-ab30-5276684d2a41">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="0c65ed50-d947-4af7-b881-c3a2edd645dc">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/UInt8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="78cae0f5-0d36-49b2-8c41-622c0ba4aee4">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="d8362a32-70c6-41eb-a558-a5579ef30a62">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="09a9e208-0f66-4df2-a175-1f264c6a7119">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="64060292-5d12-4733-aae5-05c67634f8f5">
							<SHORT-NAME>NvMService_AC2_SRBS</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="7fa0554b-0987-458d-bea1-3482a80bfbf7">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="043c68fb-3382-4743-a40c-086cfb3732d4">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="afa9a813-**************-e7c5b0bbb610">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="956e05f5-2e9f-40a0-ae32-7cae1a4e8ff9">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="74faf36f-7cb3-49f7-95b3-9cc15f34da51">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="bd4ca4b7-358a-4471-9505-b05832a2a1ab">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/Boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="4b5ee453-fc29-4295-a0d8-2f4a673eeac8">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="e7b51251-00da-4066-a036-3090b4aa8861">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="4f934da4-78e0-49fc-b358-c387b96d5173">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="fad7bef5-**************-12c9c85a8f0b">
							<SHORT-NAME>NvMService_AC2_SRBS_Defs</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="1562d568-cdc9-452f-a6ef-494b94f69501">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="dbf5fbee-eab4-4db9-8665-e7faf15677c6">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="d4827e69-89a6-47f5-b5e3-358268f630f4">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="4fc13678-ccf1-4f93-9735-551d8ba29ce9">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="c5c456fc-aa34-459a-922a-c719d284fce3">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="3a063ef3-3304-4b17-ba38-3a4f412a20ac">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="e87cf9fd-64ac-4d36-8f39-ecb446892c2d">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="85397bcf-e8df-4cff-839c-1a6a1d93749b">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/Boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="aed0d98c-fa43-4031-9cbe-f48a32dcb6cc">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="152de2d4-1631-4bbe-97a7-6ad6f04366e9">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="3cf1878f-7187-4f8c-8b9e-8f5c6851df2d">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="65b6bcf1-192b-4905-8c86-8b7beab3e6e3">
							<SHORT-NAME>NvMService_AC2_SRBS_DS</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="4618ba2f-7c82-43d2-8cee-b3a933287e7e">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="e28a160b-91a4-4010-8393-1adf6e1bd746">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/UInt8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="b878a1b7-1f9a-4b9e-934d-e0538acbef00">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="22473071-1a73-48f7-9199-dca56d5573ca">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="2d08e276-e0fc-46d4-9014-c1e597915c5f">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="c40cbbe1-0c5e-46d5-a997-b349e1e2edf8">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="89f39dd4-4cca-4c19-b9f0-c3e8e64668bd">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="6088d6a6-23b0-4094-984a-e5537893926b">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/UInt8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="ad5991ed-21f3-4258-a1fc-b7ee720884ea">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="95f56aa9-b143-418e-9b8d-a8f266181a94">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/Boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="2d13a561-a457-42c5-b2d3-a73667e4b5f2">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="10f3aba8-649a-4611-828f-01bce26b0e40">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="bd3bb828-a41f-4c66-83df-179d47db8d3b">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="dd7c6435-6550-42ef-bc71-508471cff7de">
							<SHORT-NAME>NvMService_AC2_SRBS_DS_Defs</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="1e9daf42-404e-4e6f-988d-4883b8b346d3">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="ea289f79-0224-4f75-938f-9a1a9aeebf73">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/UInt8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="ee47d2eb-dc21-409c-99b6-14ca9e6e03d5">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="0764ad14-619c-4547-bad8-30c044904ae3">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="f44a16d7-ed0a-4330-a239-0ac7fd327397">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="9aca1b48-9581-4e4b-a456-ad7cfcc7a585">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="1e90eee7-8b25-4bf1-9577-986ef9bb1049">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="ef54b3a6-73e6-4ce7-b7db-9ef5007c79e9">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="73ee0525-c15f-4068-859c-3b240915b9f7">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="26e8d2c2-b4fd-4d67-a5a3-62aa2617fc4d">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/UInt8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="841de53b-6b5b-4f5f-aafa-c9a9167167ab">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="57dbc605-46cb-4558-9708-28253fb32562">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/Boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="6ad74428-2740-46a3-ba48-6b6eacf062b4">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="acf9bd07-9a95-4d88-91f5-9f5f060206ae">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC2_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="d67e48cf-3c2c-461f-b9fa-73ce553b202f">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="49233c2b-3ca8-4a8c-97d3-e532bcfade5c">
							<SHORT-NAME>NvMService_AC3</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="429fac93-4816-4d3a-b4e6-4ab24a896f74">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="d59e1093-1743-4842-b0f7-58b98ec0504c">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="ff5cefb6-9d02-4979-9f5c-28962b340a43">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="19f1edc9-c919-4ac0-904e-3af597b37dd4">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="fc3688ed-34de-49aa-93fa-1e642474e233">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="1345a609-5965-4ed2-becf-8aed1f6add6b">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="7e447b75-1673-4cc5-9f3e-fb5036c37415">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="1c521c8c-a23b-4c11-801c-02886a34e683">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="234b4650-ecd6-4877-8ddd-64285e74a0e6">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="fc355f80-141b-4e8b-a3f2-660b99d51961">
							<SHORT-NAME>NvMService_AC3_Defs</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="5553bf7d-4c4f-476e-82d9-e7b9957edcdd">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="8d9a20bd-b295-4cd4-b5d2-d4730d0fd90a">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="e443a94c-c20b-46c1-9acd-4355b0a7fe97">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="7dde8418-81f0-404b-8aec-3b554c8c31b7">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="475546f8-64a8-4f3d-a70c-acddf72435d7">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="c10b9a55-9d90-4fcd-9278-1975b05bcfa7">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="2613fab1-e8d6-4363-a769-5cc7ae8b55e6">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="0c1d8262-46eb-43e5-a5c8-060ec871c7f5">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="21974da8-8a1b-4cd4-b12d-f3a130a99f8f">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="060d1c61-fcb7-443e-aba0-85941ae52ac1">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="0acfdf5c-1cda-4b13-b1dc-5e5b41d118e6">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="0c92d268-b881-4f51-baff-0c23f69d02ba">
							<SHORT-NAME>NvMService_AC3_DS</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="43e6ea3b-3776-4be0-a96b-e138c9fa7f5b">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="95567aa1-a65e-497f-b8cf-ada546c3d0ad">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="6ba67f4c-711e-4709-83cf-a26cebb3e389">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/UInt8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="68aff477-b743-4de8-ae2e-334e1b57442e">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="6bb3b3c5-bf0d-465a-9a47-87646bb7419b">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="761d7ea3-a4db-426d-9ade-1d96db3b5c5d">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="7a8c1ea4-5da3-4724-a7a1-c9ab5eee7360">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="92a026e9-1f42-4125-b7bd-83efba81304c">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="0e4d5c83-3328-4776-beb1-6c349c968762">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="6a5957b7-7884-401c-8d05-cfe06e0349b3">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/UInt8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="7b307704-9ec2-4392-a4ba-4392c2a11519">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="77aed649-079c-49a4-a732-9ca8bdf2b31e">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="d158d97e-c743-44a1-98f9-c2d5184224ec">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="4ea3cc7e-e7a1-4fb6-ad08-ebc0fc544a43">
							<SHORT-NAME>NvMService_AC3_DS_Defs</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="54b665bf-9c5f-49ae-863d-cb66d4945b79">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="6992185d-732d-4557-b1b1-8284aa7d23f3">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="0d0afc65-b390-4ea6-b43d-6b692d4ac446">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/UInt8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="d47efc6e-bc29-4c72-8168-8f1e24f68da0">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="f44dc1df-ab47-4149-870e-eff819a637f3">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="593b35be-b7b6-4ece-a713-d07a76dfe4ba">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="24fb3a79-300b-45ab-986c-e88155cea187">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="041cc7df-f3bb-423a-a056-5a8bb9fb9a4b">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="0a38ca63-cc48-4682-8294-a92f2e65c033">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="dfe1a83a-d691-4ced-bb7b-67d162a82ebf">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="4b369441-4373-43ed-b71e-c202aae859d2">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="290bd719-cc84-4687-93e3-ca6ba03a2625">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/UInt8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="63dac2f5-7c0b-41d4-9dd3-88924a593d1e">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="d84ff1e4-5d08-48a7-8967-a6dcec12f367">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="8b73f80b-8154-4c4c-8cdc-85a8a900f197">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="30E67C90-BC6D-4A3B-8697-9D8D795DC512">
							<SHORT-NAME>NvMService_AC3_SRBS</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="B72BC8F1-7C80-46FE-83CD-9895857D6678">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="D27CF944-AA08-43B3-90B5-C9FD44B21D6E">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="98CAA9D2-AC08-491A-8F2F-EA65B0E56153">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="D4F8FE14-7B66-4DBE-A3C9-AAED264C2597">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="ABE3C51C-F6F6-49E6-A3FB-EF90881268C9">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="8CC1DC22-BBF9-4764-93EE-3CC7A905BD67">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="A146ABF7-CE57-4BE8-AA5B-A69F0E039BFC">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="F4F39CC2-9834-4BA8-80AD-F59635C1EBE9">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/Boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="5096F6BE-46D6-4424-8789-B76C9EEB9727">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="0E106893-5229-41E1-9CBC-3DE93A08C4A1">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="193012E9-9B25-4EBD-9BF6-F47959FCAEFE">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="10caf715-8ed0-49b7-bf67-49a2a0cb11f0">
							<SHORT-NAME>NvMService_AC3_SRBS_Defs</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="f442d8c2-9d96-4109-85f9-a578d1ad7ff4">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="237057c7-9aa4-4c58-aa27-6393260af353">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="8c823d34-31fc-4296-962e-7af15a969c1e">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="fdfc7c47-2e8f-44a5-8bf9-e647cff73855">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="723b3687-a724-42b4-a2ce-44b63227768b">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="c184db3f-5334-4275-981f-0668dc70c540">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="7008b900-6b18-4b94-9fce-ff82f46e7b14">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="e531fcdd-6f2b-4dea-bd5d-92cd176bd9df">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="0eb6b538-7922-4820-bc4f-ffe380a81906">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="62f56811-ca6e-4abf-b0a0-fe8df6d02a7f">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/Boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="6fcdbede-c6fe-413a-a369-e663cb7e1cc7">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="eb45e99f-4117-4617-bf4a-a5825558fbab">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="30a739ae-1b5f-4b12-a5f6-f9d1c70e4370">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="456138ca-9ece-4fb5-9739-208f969e70da">
							<SHORT-NAME>NvMService_AC3_SRBS_DS</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="9555a00a-3ab9-4276-8646-a4cf2c6ac705">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="0a41acab-4b04-4c4e-9faf-8b165a0896af">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="e795e36f-a517-4431-9161-7bc68a05a2b7">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/UInt8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="b57f192e-e0dc-4b53-9d85-9580c0925204">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="41861506-97c4-477e-85c7-e0983ada71d2">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="1c6b40bf-9209-4726-b569-193fa76b5def">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="1f5cbaed-674d-4f14-b1b4-43a356cd104f">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="15694c7a-f196-4941-b0fa-cd6435e6103d">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="531d5893-**************-90fcb36c23d0">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="b5ec0dcb-9292-4d81-9c2e-33460d0038c4">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/UInt8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="17045340-5383-4243-bdd8-c5dd14c4cab6">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="746132d1-de9f-4f16-a738-19b8fb708483">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/Boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="2a4e8fef-d0a6-4f5e-9428-b15ac0b3f116">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="f757b6e1-d1bd-4932-9e74-e6f371486531">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_DS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="39a2dc01-686b-4d6b-87ff-9683be0bc552">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="61687d39-add5-4138-ba63-e4dfda6e04f1">
							<SHORT-NAME>NvMService_AC3_SRBS_DS_Defs</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="79f40e54-d13a-4e51-9542-db20fbc2fb3b">
									<SHORT-NAME>EraseBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="b97ceba9-d90b-473a-a8df-24c699e6cc2a">
									<SHORT-NAME>GetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="4963ea50-393f-42b5-8365-41c26548f5b0">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/UInt8</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="04743ba7-7eb8-4a8d-a961-89c4aad3b16d">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="ee01dfc8-d7c3-4eb0-a0d8-fd5216e1792a">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="56897376-66a0-4d65-bf75-618f0ab972b5">
									<SHORT-NAME>InvalidateNvBlock</SHORT-NAME>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="01c0f6ac-c711-4f21-a620-************">
									<SHORT-NAME>ReadBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="fae036dd-c311-4bb5-a762-c0fb68e36508">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="f844a387-054d-4fee-9acb-33f5fb7f1ae4">
									<SHORT-NAME>RestoreBlockDefaults</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="038da939-5a0f-46a2-93e1-aba6ca7b16cf">
											<SHORT-NAME>DstPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="f1789979-baca-4808-b5a3-cdec0886cbf6">
									<SHORT-NAME>SetDataIndex</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="6711e026-daa8-49fd-add9-af853f93a7f5">
											<SHORT-NAME>DataIndex</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/UInt8</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="5f19594d-a52f-4754-8de5-e8ef58b28df5">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="8c50df09-a333-478a-82df-0c80433cbfa7">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/Boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="3cc2332f-8ac6-4ead-b161-38fc43977ae3">
									<SHORT-NAME>WriteBlock</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="d8c509d7-d1fd-43ca-826d-9ddbf3041f47">
											<SHORT-NAME>SrcPtr</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/dtRef_const_VOID</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_AC3_SRBS_DS_Defs/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="f9f25686-6856-46c5-9762-bcce655bdbc7">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
						<CLIENT-SERVER-INTERFACE UUID="2472b850-7f41-4a8d-9d1b-638b9439c445">
							<SHORT-NAME>NvMService_SRBS</SHORT-NAME>
							<IS-SERVICE>true</IS-SERVICE>
							<OPERATIONS>
								<CLIENT-SERVER-OPERATION UUID="a01dc377-af3e-4439-b8ab-34dbeb99671f">
									<SHORT-NAME>GetErrorStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="1e88f205-c7c5-47b7-a28d-bf3e43299d53">
											<SHORT-NAME>ErrorStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/NvM_RequestResultType</TYPE-TREF>
											<DIRECTION>OUT</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
								<CLIENT-SERVER-OPERATION UUID="d6760e78-95fb-4af7-822f-f3beee5be674">
									<SHORT-NAME>SetRamBlockStatus</SHORT-NAME>
									<ARGUMENTS>
										<ARGUMENT-DATA-PROTOTYPE UUID="eb2eaab6-a23a-4524-bbc4-fd37bcba27ad">
											<SHORT-NAME>RamBlockStatus</SHORT-NAME>
											<SW-DATA-DEF-PROPS>
												<SW-DATA-DEF-PROPS-VARIANTS>
													<SW-DATA-DEF-PROPS-CONDITIONAL>
														<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
													</SW-DATA-DEF-PROPS-CONDITIONAL>
												</SW-DATA-DEF-PROPS-VARIANTS>
											</SW-DATA-DEF-PROPS>
											<TYPE-TREF DEST="IMPLEMENTATION-DATA-TYPE">/MICROSAR_SWCS_DEV/NvM_swc/DataTypes/Boolean</TYPE-TREF>
											<DIRECTION>IN</DIRECTION>
										</ARGUMENT-DATA-PROTOTYPE>
									</ARGUMENTS>
									<POSSIBLE-ERROR-REFS>
										<POSSIBLE-ERROR-REF DEST="APPLICATION-ERROR">/Predefined_DEV/ServicePortInterfaces/NvMService_SRBS/E_NOT_OK</POSSIBLE-ERROR-REF>
									</POSSIBLE-ERROR-REFS>
								</CLIENT-SERVER-OPERATION>
							</OPERATIONS>
							<POSSIBLE-ERRORS>
								<APPLICATION-ERROR UUID="1d622847-103f-447e-8e2b-a4b09570d036">
									<SHORT-NAME>E_NOT_OK</SHORT-NAME>
									<ERROR-CODE>1</ERROR-CODE>
								</APPLICATION-ERROR>
							</POSSIBLE-ERRORS>
						</CLIENT-SERVER-INTERFACE>
					</ELEMENTS>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>
