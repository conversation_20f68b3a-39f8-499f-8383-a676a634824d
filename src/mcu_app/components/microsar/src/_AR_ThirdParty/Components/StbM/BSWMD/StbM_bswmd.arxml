<?xml version="1.0" encoding="UTF-8"?>
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-3-1.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://autosar.org/schema/r4.0">
	<AR-PACKAGES>
		<AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
			<SHORT-NAME>MICROSAR</SHORT-NAME>
			<ELEMENTS>
				<ECUC-MODULE-DEF UUID="ECUC:506d0671-efed-4c60-bbf4-1a8c714a49ce">
					<SHORT-NAME>StbM</SHORT-NAME>
					<DESC>
						<L-2 L="EN">The Synchronized Time-Base Manager provides synchronized time bases to its customers, i.e., time bases, which are synchronized with time bases on other nodes of a distributed system.
#TimeSync
#AUTOSAR #ServiceComponent</L-2>
					</DESC>
					<CATEGORY>VENDOR_SPECIFIC_MODULE_DEFINITION</CATEGORY>
					<ADMIN-DATA>
						<DOC-REVISIONS>
							<DOC-REVISION>
								<REVISION-LABEL>1.00.00</REVISION-LABEL>
								<ISSUED-BY>visssf</ISSUED-BY>
								<DATE>2013-01-17</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Initial creation (Beta)</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00060987</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.01.00</REVISION-LABEL>
								<ISSUED-BY>visssf</ISSUED-BY>
								<DATE>2013-04-10</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Updated SW version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>2.00.00</REVISION-LABEL>
								<ISSUED-BY>visssf</ISSUED-BY>
								<DATE>2013-09-17</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Set default value for development error detection to true, removed obsolete VectorCommonData container</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00067562, ESCAN00070442</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>3.00.00</REVISION-LABEL>
								<ISSUED-BY>visssf</ISSUED-BY>
								<DATE>2014-08-25T04:45:32+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Support new global time synchronization concept</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00077484</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>3.01.00</REVISION-LABEL>
								<ISSUED-BY>visssf</ISSUED-BY>
								<DATE>2014-12-04T11:04:27+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Updated SW version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>3.01.01</REVISION-LABEL>
								<ISSUED-BY>visssf</ISSUED-BY>
								<DATE>2015-03-02T09:03:46+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Updated SW version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>3.02.00</REVISION-LABEL>
								<ISSUED-BY>visssf</ISSUED-BY>
								<DATE>2015-07-14T03:40:04+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Updated SW version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>4.00.00</REVISION-LABEL>
								<ISSUED-BY>visssf</ISSUED-BY>
								<DATE>2015-09-22T09:18:25+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Support of High Resolution Time Base Reference Clock based on GPT</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00085766</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>4.01.00</REVISION-LABEL>
								<ISSUED-BY>visssf</ISSUED-BY>
								<DATE>2016-02-26T06:16:10+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Updated SW version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>4.01.01</REVISION-LABEL>
								<ISSUED-BY>visssf</ISSUED-BY>
								<DATE>2016-04-12T10:58:24+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Updated SW version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>4.02.00</REVISION-LABEL>
								<ISSUED-BY>visssf</ISSUED-BY>
								<DATE>2017-02-23T01:23:57+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Time Synchronization acc. to AR 4.3 [Feature Pack 1]</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">FEATC-644</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>5.00.00</REVISION-LABEL>
								<ISSUED-BY>visssf</ISSUED-BY>
								<DATE>2017-04-07T02:05:40+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Update BSWMD Files to AR 4.3</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">STORYC-364</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>5.01.00</REVISION-LABEL>
								<ISSUED-BY>visms</ISSUED-BY>
								<DATE>2017-05-19T02:05:40+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Removed unsupported variant Postbuild</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>5.02.00</REVISION-LABEL>
								<ISSUED-BY>visssf</ISSUED-BY>
								<DATE>2017-08-30T10:39:37+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Rework maximum and default values for time parameters</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00081204</L-2>
										</REASON>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Added missing default values</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>5.03.00</REVISION-LABEL>
								<ISSUED-BY>vistra</ISSUED-BY>
								<DATE>2017-09-25T10:15:28+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Improved description of StbMOSScheduleTableRef</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">STORYC-1351</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>5.04.00</REVISION-LABEL>
								<ISSUED-BY>vistra, visssf</ISSUED-BY>
								<DATE>2018-01-16T18:13:14+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Introduction of SafeBswCheck BSWMD Parameter</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">STORYC-2498</L-2>
										</REASON>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Corrected lower multiplicity of StbMTimeNotificationCallback</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00097744</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>5.05.00</REVISION-LABEL>
								<ISSUED-BY>visssf</ISSUED-BY>
								<DATE>2018-04-25T10:03:26+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Support schedule table synchronisation for different counter resolutions</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">STORYC-5063</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>5.06.00</REVISION-LABEL>
								<ISSUED-BY>visssf</ISSUED-BY>
								<DATE>2018-06-15T15:32:55+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Updated SW version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>5.07.00</REVISION-LABEL>
								<ISSUED-BY>visssf</ISSUED-BY>
								<DATE>2018-08-10T03:15:36+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Increased max value of StbMRateCorrectionMeasurementDuration</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00100279</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>5.08.00</REVISION-LABEL>
								<ISSUED-BY>vistra</ISSUED-BY>
								<DATE>2018-09-05T10:30:58+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Improved description of StbMCustomerHeaderInclude</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">Review finding (ReviewLogSheet_Implementation rev. 362842)</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>5.08.01</REVISION-LABEL>
								<ISSUED-BY>visssf</ISSUED-BY>
								<DATE>2018-10-30T10:14:02+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Updated SW version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>5.08.02</REVISION-LABEL>
								<ISSUED-BY>vissi</ISSUED-BY>
								<DATE>2019-06-17T09:27:00+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Improved description of StbMTimeLeapFutureThreshold and StbMTimeLeapPastThreshold</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.00.00</REVISION-LABEL>
								<ISSUED-BY>visgig</ISSUED-BY>
								<DATE>2019-07-19T13:35:15+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Updated SW version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.00.01</REVISION-LABEL>
								<ISSUED-BY>visgig</ISSUED-BY>
								<DATE>2019-10-29T06:22:07+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Adapted multiplicity of /MICROSAR/StbM/StbMSynchronizedTimeBase/StbMLocalTimeClock/StbMLocalTimeHardware</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00104585</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>7.00.00</REVISION-LABEL>
								<ISSUED-BY>visssf</ISSUED-BY>
								<DATE>2020-01-30T15:52:17+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Updated SW version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>8.00.00</REVISION-LABEL>
								<ISSUED-BY>visjwe</ISSUED-BY>
								<DATE>2020-05-05T16:24:50+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">[GTS] SafeCarTime acc. to BugZilla RfC 77436 for Ethernet UseCase (Beta)</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">MSR4-29714</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>8.01.00</REVISION-LABEL>
								<ISSUED-BY>visgig</ISSUED-BY>
								<DATE>2020-08-19T11:51:00+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Enabled Post-Build Variant Support</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">MSR4-30311</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>8.02.00</REVISION-LABEL>
								<ISSUED-BY>visssf</ISSUED-BY>
								<DATE>2021-02-16T14:59:15+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Updated SW version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>8.02.01</REVISION-LABEL>
								<ISSUED-BY>vissi</ISSUED-BY>
								<DATE>2021-07-28T09:10:00+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Added package merger description</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">MSR4-31441</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>8.02.02</REVISION-LABEL>
								<ISSUED-BY>visssf</ISSUED-BY>
								<DATE>2021-08-13T14:29:44+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Updated SW version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
						</DOC-REVISIONS>
					</ADMIN-DATA>
					<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00065</RELATED-TRACE-ITEM-REF>
					<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
					<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
					<POST-BUILD-VARIANT-SUPPORT>true</POST-BUILD-VARIANT-SUPPORT>
					<REFINED-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/StbM</REFINED-MODULE-DEF-REF>
					<SUPPORTED-CONFIG-VARIANTS>
						<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
					</SUPPORTED-CONFIG-VARIANTS>
					<CONTAINERS>
						<!-- Container Definition: StbMGeneral -->
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e3062823-830e-401e-ab3f-102908ca5111">
							<SHORT-NAME>StbMGeneral</SHORT-NAME>
							<DESC>
								<L-2 L="EN">This container holds the general parameters of the Synchronized Time-base Manager</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">true</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00002</RELATED-TRACE-ITEM-REF>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<MULTIPLICITY-CONFIG-CLASSES>
								<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
									<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
								</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
							</MULTIPLICITY-CONFIG-CLASSES>
							<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
							<REQUIRES-INDEX>false</REQUIRES-INDEX>
							<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
							<PARAMETERS>
								<!-- PARAMETER DEFINITION: StbMCustomerHeaderInclude -->
								<ECUC-STRING-PARAM-DEF UUID="ECUC:76198e3e-b0de-4050-b979-eab4512e1206">
									<SHORT-NAME>StbMCustomerHeaderInclude</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Defines the header file, which has the declaration of the callback function prototype for the notification customer of the reference Time Base.</L-2>
									</DESC>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00040</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
								</ECUC-STRING-PARAM-DEF>
								<!-- PARAMETER DEFINITION: StbMDevErrorDetect -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:7a01b1fa-4807-49ad-84ba-************">
									<SHORT-NAME>StbMDevErrorDetect</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Switches the development error detection and notification on or off.</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">* true: detection and notification is enabled.
												* false: detection and notification is disabled.</L-1>
										</P>
									</INTRODUCTION>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00012</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: StbMGetCurrentTimeExtendedAvailable -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:c433c237-94b3-452b-9e71-63fd3700849a">
									<SHORT-NAME>StbMGetCurrentTimeExtendedAvailable</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This allows to define whether an additional variant of the API GetCurrentTime with a 64 bit argument is provided.</L-2>
									</DESC>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00032</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: StbMMainFunctionPeriod -->
								<ECUC-FLOAT-PARAM-DEF UUID="ECUC:1d28d937-66d3-458b-9843-0e21ea280db6">
									<SHORT-NAME>StbMMainFunctionPeriod</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Schedule period of the main function StbM_MainFunction.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:BaseUnit">SEC</SD>
												<SD GID="DV:Unit">MSEC</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00027</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0.01</DEFAULT-VALUE>
									<MAX INTERVAL-TYPE="CLOSED">1</MAX>
									<MIN INTERVAL-TYPE="OPEN">0</MIN>
								</ECUC-FLOAT-PARAM-DEF>
								<!-- PARAMETER DEFINITION: StbMTimeRecordingSupport -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:581861fb-8d14-4e49-8e38-74b7266d8b46">
									<SHORT-NAME>StbMTimeRecordingSupport</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Enables/Disables the usage of the recording functionality for Synchronized and Offset timebases for Global Time precision measurement purpose.</L-2>
									</DESC>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00038</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: StbMVersionInfoApi -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:16c0bfdf-bfa8-4261-b458-2efa197b30d0">
									<SHORT-NAME>StbMVersionInfoApi</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Activate/Deactivate the version information API (StbM_GetVersionInfo). True: version information API activated False: version information API deactivated.</L-2>
									</DESC>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00013</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="468c9408-6f95-4eb9-bfbd-b51cc9b7b661">
									<SHORT-NAME>StbMSafeBswChecks</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Selects if safety checks are used. These checks improve robustness as e.g. invalid parameters are checked before function is executed. This attribute has to be enabled for safety projects where this component is mapped to an ASIL partition.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
							</PARAMETERS>
							<REFERENCES>
								<!-- Symbolic Name Reference Definition: StbMGptTimerRef -->
								<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ECUC:0cdc6dd0-fd5a-492d-bace-768bf495e607">
									<SHORT-NAME>StbMGptTimerRef</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This represents an optional sub-container in case any Time Notification Customer is configured.</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">The designated GPT timer has to be configured  to have a tick duration of one micro second.</L-1>
										</P>
									</INTRODUCTION>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00039</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<REQUIRES-SYMBOLIC-NAME-VALUE>true</REQUIRES-SYMBOLIC-NAME-VALUE>
									<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Gpt/GptChannelConfigSet/GptChannelConfiguration</DESTINATION-REF>
								</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
							</REFERENCES>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
						<!-- Container Definition: StbMSynchronizedTimeBase -->
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:0eb5422e-e6ba-4303-8c41-1f56ad5d2f52">
							<SHORT-NAME>StbMSynchronizedTimeBase</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Synchronized time.base collects the information about a specific time-base provider within the system.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">true</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00003</RELATED-TRACE-ITEM-REF>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>65536</UPPER-MULTIPLICITY>
							<MULTIPLICITY-CONFIG-CLASSES>
								<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
									<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
								</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
							</MULTIPLICITY-CONFIG-CLASSES>
							<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
							<REQUIRES-INDEX>false</REQUIRES-INDEX>
							<PARAMETERS>
								<!-- PARAMETER DEFINITION: StbMAllowMasterRateCorrection -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:b2e9cbf0-a6cc-4df7-bc43-72d64ecb83b1">
									<SHORT-NAME>StbMAllowMasterRateCorrection</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This attribute describes whether the rate correction value of a Time Base can be set by StbM_SetRateCorrection():</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">* false: the rate correction value can not be set by StbM_SetRateCorrection()
												* true: the rate correction value can be set by StbM_SetRateCorrection()

Note: This parameter is only relevant for Global Time Masters.</L-1>
										</P>
									</INTRODUCTION>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00043</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: StbMAllowSystemWideGlobalTimeMaster -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:cb072c54-dffe-4a81-9232-4584c0a7b4db">
									<SHORT-NAME>StbMAllowSystemWideGlobalTimeMaster</SHORT-NAME>
									<DESC>
										<L-2 L="EN">For postbuild variant of the StbM this parameter has to be set to true for a Global Time Master that may act as a system-wide source of time. Otherwise no corresponding service ports/interfaces is provided.</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">The Global Time Master functionality behind the service ports/interfaces has to be enabled/disabled separately via parameter StbMIsSystemWideGlobalTimeMaster.

Note: This parameter is only relevant for Global Time Masters.</L-1>
										</P>
									</INTRODUCTION>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00066</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: StbMClearTimeleapCount -->
								<ECUC-INTEGER-PARAM-DEF UUID="ECUC:d2af0f71-daf1-499e-8286-f15c0f436252">
									<SHORT-NAME>StbMClearTimeleapCount</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This attribute describes the required number of updates to the Time Base where the time difference to the previous value has to remain below StbMTimeLeapPastThreshold/StbMTimeLeapFutureThreshold until the TIMELEAP_PAST/TIMELEAP_FUTURE bit within timeBaseStatus of the Time Base is cleared.

Note: This parameter is only relevant for Time Slaves.</L-2>
									</DESC>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00037</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>1</DEFAULT-VALUE>
									<MAX>65535</MAX>
									<MIN>1</MIN>
								</ECUC-INTEGER-PARAM-DEF>
								<!-- PARAMETER DEFINITION: StbMIsSystemWideGlobalTimeMaster -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:a4bd47a0-95aa-405b-8752-8bd48af3d145">
									<SHORT-NAME>StbMIsSystemWideGlobalTimeMaster</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter shall be set to true for a Global Time Master that acts as a system-wide source of time information with respect to Global Time.</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">It is possible that several Global Time Masters exist that have set this parameter set to true because the Global Time Masters exist once per Global Time Domain and one ECU may own several Global Time Domains on different buses it is connected to.

Note: This parameter is only relevant for Global Time Masters.</L-1>
										</P>
									</INTRODUCTION>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00036</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: StbMMasterRateDeviationMax -->
								<ECUC-INTEGER-PARAM-DEF UUID="ECUC:a807fe9b-0e6d-43ff-bb74-6cff7ce4d07d">
									<SHORT-NAME>StbMMasterRateDeviationMax</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This attribute describes the maximum allowed absolute value of the rate deviation value to be set by StbM_SetRateCorrection().

Note: This parameter is only relevant for Global Time Masters.</L-2>
									</DESC>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00044</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0</DEFAULT-VALUE>
									<MAX>32000</MAX>
									<MIN>0</MIN>
								</ECUC-INTEGER-PARAM-DEF>
								<!-- PARAMETER DEFINITION: StbMStatusNotificationCallback -->
								<ECUC-FUNCTION-NAME-DEF UUID="ECUC:39782520-9fd9-4107-b204-1e3e19253cee">
									<SHORT-NAME>StbMStatusNotificationCallback</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Name of the customer specific status notification callback function, which shall be called, if a non-masked status event occurs.</L-2>
									</DESC>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00046</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
								</ECUC-FUNCTION-NAME-DEF>
								<!-- PARAMETER DEFINITION: StbMStatusNotificationMask -->
								<ECUC-INTEGER-PARAM-DEF UUID="ECUC:6bce2c95-96d5-428d-badf-a1541e69ff3a">
									<SHORT-NAME>StbMStatusNotificationMask</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The parameter defines the initial value for NotificationMask mask, which defines the events for which the event notification callback function shall be called.</L-2>
									</DESC>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00045</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0</DEFAULT-VALUE>
									<MAX>4294967295</MAX>
									<MIN>0</MIN>
								</ECUC-INTEGER-PARAM-DEF>
								<!-- PARAMETER DEFINITION: StbMStoreTimebaseNonVolatile -->
								<ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:10043553-7e9d-48cd-b52b-d4a6ed064dd9">
									<SHORT-NAME>StbMStoreTimebaseNonVolatile</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This allows for specifying that the Time Base shall be stored in the NvRam.</L-2>
									</DESC>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00031</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>NO_STORAGE</DEFAULT-VALUE>
									<LITERALS>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:8619508d-59be-8dbe-1082-c1025c7ebb64">
											<SHORT-NAME>NO_STORAGE</SHORT-NAME>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="ECUC:427c8636-fa76-8917-2111-5f9a37d5bb31">
											<SHORT-NAME>STORAGE_AT_SHUTDOWN</SHORT-NAME>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
									</LITERALS>
								</ECUC-ENUMERATION-PARAM-DEF>
								<!-- PARAMETER DEFINITION: StbMSynchronizedTimeBaseIdentifier -->
								<ECUC-INTEGER-PARAM-DEF UUID="ECUC:3f37a751-19db-46c2-8fe8-727675c83547">
									<SHORT-NAME>StbMSynchronizedTimeBaseIdentifier</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Identification of a Synchronized TimeBase via a unique identifier.</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">Range:
												* 0 .. 15: Synchronized Time Bases
												* 16 .. 31: Offset Time Bases
												* 32 .. 127: Pure Local Time Bases
												* 128 .. 65535: Reserved</L-1>
										</P>
									</INTRODUCTION>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00021</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
									<MAX>65535</MAX>
									<MIN>0</MIN>
								</ECUC-INTEGER-PARAM-DEF>
								<!-- PARAMETER DEFINITION: StbMSyncLossTimeout -->
								<ECUC-FLOAT-PARAM-DEF UUID="ECUC:a27afdc4-309a-499d-ac82-c95edcb1f5b1">
									<SHORT-NAME>StbMSyncLossTimeout</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This attribute describes the timeout for the situation that the time synchronization gets lost in the scope of the time domain.

Note: This parameter is only relevant for Time Slaves.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:BaseUnit">SEC</SD>
												<SD GID="DV:Unit">MSEC</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00028</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>1</DEFAULT-VALUE>
									<MAX INTERVAL-TYPE="CLOSED">4294967</MAX>
									<MIN INTERVAL-TYPE="OPEN">0</MIN>
								</ECUC-FLOAT-PARAM-DEF>
								<!-- PARAMETER DEFINITION: StbMTimeLeapFutureThreshold -->
								<ECUC-FLOAT-PARAM-DEF UUID="ECUC:6e77c22f-0aa9-4a6a-b38a-2202357ebd44">
									<SHORT-NAME>StbMTimeLeapFutureThreshold</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This represents the maximum allowed positive difference between the current Local Time Base value and a newly received Global Time Base value.
A threshold of 0 deactivates the Time Leap check.

Note: This parameter is only relevant for Time Slaves.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:BaseUnit">SEC</SD>
												<SD GID="DV:Unit">MSEC</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00041</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>1</DEFAULT-VALUE>
									<MAX INTERVAL-TYPE="CLOSED">2</MAX>
									<MIN INTERVAL-TYPE="CLOSED">0</MIN>
								</ECUC-FLOAT-PARAM-DEF>
								<!-- PARAMETER DEFINITION: StbMTimeLeapPastThreshold -->
								<ECUC-FLOAT-PARAM-DEF UUID="ECUC:d7d8c5ff-6f13-4558-8c42-66b829b422e9">
									<SHORT-NAME>StbMTimeLeapPastThreshold</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This represents the maximum allowed negative difference between the current Local Time Base value and a newly received Global Time Base value.
A threshold of 0 deactivates the Time Leap check.

Note: This parameter is only relevant for Time Slaves.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:BaseUnit">SEC</SD>
												<SD GID="DV:Unit">MSEC</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00042</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>1</DEFAULT-VALUE>
									<MAX INTERVAL-TYPE="CLOSED">2</MAX>
									<MIN INTERVAL-TYPE="CLOSED">0</MIN>
								</ECUC-FLOAT-PARAM-DEF>
							</PARAMETERS>
							<REFERENCES>
								<!-- Reference Definition: StbMOffsetTimeBase -->
								<ECUC-REFERENCE-DEF UUID="ECUC:7b397fdd-c1dc-41ae-bce8-46f3949cd47d">
									<SHORT-NAME>StbMOffsetTimeBase</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This is the reference to the Synchronized Time-Base this Offset Time-Base is based on. This reference makes the containing StbMSynchronizedTimeBase an Offset Time-Base.</L-2>
									</DESC>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00030</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<REQUIRES-SYMBOLIC-NAME-VALUE>false</REQUIRES-SYMBOLIC-NAME-VALUE>
									<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/StbM/StbMSynchronizedTimeBase</DESTINATION-REF>
								</ECUC-REFERENCE-DEF>
							</REFERENCES>
							<SUB-CONTAINERS>
								<!-- Container Definition: StbMLocalTimeClock -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:fa6338a8-afbe-4f71-86e8-2dc16d94e81d">
									<SHORT-NAME>StbMLocalTimeClock</SHORT-NAME>
									<DESC>
										<L-2 L="EN">References the hardware reference clock of this Synchronized Time Base.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">true</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00047</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: StbMClockFrequency -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:8f90dad1-1648-4e13-a1a0-fdb54dc30e51">
											<SHORT-NAME>StbMClockFrequency</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Represents the frequency of the HW reference clock used by the StbM.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">HZ</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00051</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: StbMClockPrescaler -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:77a2eb1e-2e6d-422c-9ce3-5f5f7016432a">
											<SHORT-NAME>StbMClockPrescaler</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Represents the prescaler to calculate the resulting frequency of the HW reference clock used by the StbM.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00052</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Choice Reference Definition: StbMLocalTimeHardware -->
										<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:99851e3c-5734-4f0a-9509-d90a8a1434b2">
											<SHORT-NAME>StbMLocalTimeHardware</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to the local time hardware.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00053</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<REQUIRES-SYMBOLIC-NAME-VALUE>false</REQUIRES-SYMBOLIC-NAME-VALUE>
											<DESTINATION-REFS>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/EthTSyn/EthTSynGlobalTimeDomain</DESTINATION-REF>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Gpt/GptChannelConfigSet/GptChannelConfiguration</DESTINATION-REF>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsCounter</DESTINATION-REF>
											</DESTINATION-REFS>
										</ECUC-CHOICE-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: StbMNotificationCustomer -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:af461e1b-abdf-4e1b-9382-cbfe1705b9c2">
									<SHORT-NAME>StbMNotificationCustomer</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container holds the configuration of a notification customer, which is notified is informed about the occurance of a Time-base related event.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">true</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00050</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: StbMNotificationCustomerId -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:8887ed5a-f808-4a63-b669-1f2798058a58">
											<SHORT-NAME>StbMNotificationCustomerId</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Identification of a event notification customer.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00062</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: StbMTimeNotificationCallback -->
										<ECUC-FUNCTION-NAME-DEF UUID="ECUC:8cf64d77-c567-4c56-8f3f-f61eb597a8a6">
											<SHORT-NAME>StbMTimeNotificationCallback</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Name of the customer specific notification callback function, which shall be called, if the time previously set by the customer is reached.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00064</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-FUNCTION-NAME-DEF>
										<!-- PARAMETER DEFINITION: StbMTimerStartThreshold -->
										<ECUC-FLOAT-PARAM-DEF UUID="ECUC:da9f7a43-396f-46c6-a53e-4df7895539c7">
											<SHORT-NAME>StbMTimerStartThreshold</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This  interval defines, when a GPT Timer shall be started for Time Notification Customers for which the corresponding Customer Timer is running.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">SEC</SD>
														<SD GID="DV:Unit">MSEC</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00063</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0.01</DEFAULT-VALUE>
											<MAX INTERVAL-TYPE="CLOSED">2</MAX>
											<MIN INTERVAL-TYPE="OPEN">0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: StbMTimeCorrection -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:470ce21a-f5a3-44c7-8b8a-e9c04f5619e3">
									<SHORT-NAME>StbMTimeCorrection</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Collects the information relevant for the rate- and offset correction of a Time Base.

Note: This container is only relevant for Time Slaves.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">true</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00048</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: StbMOffsetCorrectionAdaptionInterval -->
										<ECUC-FLOAT-PARAM-DEF UUID="ECUC:1d86529a-589b-410b-b435-2fcf4aad9012">
											<SHORT-NAME>StbMOffsetCorrectionAdaptionInterval</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Defines the interval during which the adaptive rate correction cancels out the rate- and time deviation.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">SEC</SD>
														<SD GID="DV:Unit">MSEC</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00057</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>1</DEFAULT-VALUE>
											<MAX INTERVAL-TYPE="CLOSED">4</MAX>
											<MIN INTERVAL-TYPE="OPEN">0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: StbMOffsetCorrectionJumpThreshold -->
										<ECUC-FLOAT-PARAM-DEF UUID="ECUC:6bd98c9d-622d-46ab-a0b8-115607bbc6ea">
											<SHORT-NAME>StbMOffsetCorrectionJumpThreshold</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Threshold for the correction method. Deviations below this value will be corrected by a linear reduction over a defined timespan. Values equal- and greater than this value will be corrected by immediately setting the correct time- and rate in form of a jump.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">SEC</SD>
														<SD GID="DV:Unit">MSEC</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00056</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>1</DEFAULT-VALUE>
											<MAX INTERVAL-TYPE="CLOSED">4</MAX>
											<MIN INTERVAL-TYPE="CLOSED">0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: StbMRateCorrectionMeasurementDuration -->
										<ECUC-FLOAT-PARAM-DEF UUID="ECUC:89193384-5b70-44d3-bd65-ec80b1d2e55c">
											<SHORT-NAME>StbMRateCorrectionMeasurementDuration</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Definition of the time span which is used to calculate the rate deviation.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">SEC</SD>
														<SD GID="DV:Unit">MSEC</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00054</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>1</DEFAULT-VALUE>
											<MAX INTERVAL-TYPE="CLOSED">60</MAX>
											<MIN INTERVAL-TYPE="CLOSED">0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: StbMRateCorrectionsPerMeasurementDuration -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:f3168974-38ae-4164-9d56-8ebab743042e">
											<SHORT-NAME>StbMRateCorrectionsPerMeasurementDuration</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Number of simultaneous rate measurements to determine the current rate deviation.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00055</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>1</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: StbMTimeRecording -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:b0120a44-ce38-4aff-9773-85b00de861b1">
									<SHORT-NAME>StbMTimeRecording</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Collects the information relevant for configuration of the precision measurement of a Time Base.

Note: This container is only relevant for Time Slaves.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">true</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00049</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: StbMOffsetTimeRecordBlockCallback -->
										<ECUC-FUNCTION-NAME-DEF UUID="ECUC:e90d79c5-0e03-41c2-be6a-b0ebc038143e">
											<SHORT-NAME>StbMOffsetTimeRecordBlockCallback</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Name of the customer specific callback function, which shall be called, if a measurement data for a Offset Time Base is available.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00061</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-FUNCTION-NAME-DEF>
										<!-- PARAMETER DEFINITION: StbMOffsetTimeRecordTableBlockCount -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:638d4d87-9937-4298-9e81-b24e064b73fa">
											<SHORT-NAME>StbMOffsetTimeRecordTableBlockCount</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Represents the number of Blocks used for queueing time measurement events for the Offset Time Base Record Table.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00059</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: StbMSyncTimeRecordBlockCallback -->
										<ECUC-FUNCTION-NAME-DEF UUID="ECUC:394d1251-9b73-43d0-9324-d73636ba28b3">
											<SHORT-NAME>StbMSyncTimeRecordBlockCallback</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Name of the customer specific callback function, which shall be called, if a measurement data for a Synchronized Time Base is available.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00060</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-FUNCTION-NAME-DEF>
										<!-- PARAMETER DEFINITION: StbMSyncTimeRecordTableBlockCount -->
										<ECUC-INTEGER-PARAM-DEF UUID="ECUC:88d10345-6f2a-4656-b8b3-8bb070a4138c">
											<SHORT-NAME>StbMSyncTimeRecordTableBlockCount</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Represents the number of Blocks used for queueing time measurement events for the Synchronized Time Base Record Table.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00058</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: StbMTimeValidation -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="458a9584-ad7b-4fa4-94df-78737ea47640">
									<SHORT-NAME>StbMTimeValidation</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container with TimeValidation configuration for a TimeBase.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildNotDeletable">true</SD>
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: StbMTimingRecordTableBlockCount -->
										<ECUC-INTEGER-PARAM-DEF UUID="e6fe1ca6-f590-4b23-bbef-48da39ddc76d">
											<SHORT-NAME>StbMTimingRecordTableBlockCount</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Size of Timing Record Table used for storing TimeValidation timing data.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</SUB-CONTAINERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
						<!-- Container Definition: StbMTriggeredCustomer -->
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:19e45c71-41c9-4253-a241-102f47f74b40">
							<SHORT-NAME>StbMTriggeredCustomer</SHORT-NAME>
							<DESC>
								<L-2 L="EN">The triggered customer is directly triggered by the Synchronized Time-base Manager by getting synchronized with the current (global) definition of time and passage of time.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">true</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00004</RELATED-TRACE-ITEM-REF>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>65536</UPPER-MULTIPLICITY>
							<MULTIPLICITY-CONFIG-CLASSES>
								<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
									<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
								</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
							</MULTIPLICITY-CONFIG-CLASSES>
							<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
							<REQUIRES-INDEX>false</REQUIRES-INDEX>
							<PARAMETERS>
								<!-- PARAMETER DEFINITION: StbMTriggeredCustomerPeriod -->
								<ECUC-INTEGER-PARAM-DEF UUID="ECUC:66300845-e1f9-4af6-be9e-6bd32cd2e858">
									<SHORT-NAME>StbMTriggeredCustomerPeriod</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The triggering period of the triggered customer, called by the StbM_MainFunction.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:BaseUnit">USEC</SD>
												<SD GID="DV:Unit">MSEC</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">The period is documented in microseconds.</L-1>
										</P>
									</INTRODUCTION>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00020</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>10000</DEFAULT-VALUE>
									<MAX>4294967295</MAX>
									<MIN>1</MIN>
								</ECUC-INTEGER-PARAM-DEF>
								<ECUC-FLOAT-PARAM-DEF UUID="4b312488-c989-401c-907e-56d2dcbf8a3d">
									<SHORT-NAME>StbMOSScheduleTableCounterTickDuration</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Tick duration of the OS counter, which drives the synchronized OS ScheduleTable, in seconds.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:BaseUnit">SEC</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<MAX INTERVAL-TYPE="CLOSED">1E-06</MAX>
									<MIN INTERVAL-TYPE="CLOSED">1E-09</MIN>
								</ECUC-FLOAT-PARAM-DEF>
							</PARAMETERS>
							<REFERENCES>
								<!-- Reference Definition: StbMOSScheduleTableRef -->
								<ECUC-REFERENCE-DEF UUID="ECUC:489979ba-a412-4da4-a60c-363cf7aec241">
									<SHORT-NAME>StbMOSScheduleTableRef</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Mandatory reference to synchronized OS ScheduleTable, which will be explicitly synchronized by the StbM.</L-2>
									</DESC>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00007</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<REQUIRES-SYMBOLIC-NAME-VALUE>false</REQUIRES-SYMBOLIC-NAME-VALUE>
									<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsScheduleTable</DESTINATION-REF>
								</ECUC-REFERENCE-DEF>
								<!-- Reference Definition: StbMSynchronizedTimeBaseRef -->
								<ECUC-REFERENCE-DEF UUID="ECUC:265babaa-6c72-4d8f-b6fe-f4cd7ac08b90">
									<SHORT-NAME>StbMSynchronizedTimeBaseRef</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Mandatory reference to the required synchronized time-base.</L-2>
									</DESC>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_StbM_00010</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<REQUIRES-SYMBOLIC-NAME-VALUE>false</REQUIRES-SYMBOLIC-NAME-VALUE>
									<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/StbM/StbMSynchronizedTimeBase</DESTINATION-REF>
								</ECUC-REFERENCE-DEF>
							</REFERENCES>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
					</CONTAINERS>
				</ECUC-MODULE-DEF>
				<BSW-IMPLEMENTATION UUID="fe386218-4649-472f-97cd-8c56262c9005">
					<SHORT-NAME>StbM_Impl</SHORT-NAME>
					<PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
					<SW-VERSION>8.02.02</SW-VERSION>
					<USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
					<VENDOR-ID>30</VENDOR-ID>
					<AR-RELEASE-VERSION>4.05.00</AR-RELEASE-VERSION>
					<BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/StbM_ib_bswmd/BswModuleDescriptions/StbM/StbMBehavior</BEHAVIOR-REF>
					<PRECONFIGURED-CONFIGURATION-REFS>
						<PRECONFIGURED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/StbM_Pre</PRECONFIGURED-CONFIGURATION-REF>
					</PRECONFIGURED-CONFIGURATION-REFS>
					<RECOMMENDED-CONFIGURATION-REFS>
						<RECOMMENDED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/StbM_Rec</RECOMMENDED-CONFIGURATION-REF>
					</RECOMMENDED-CONFIGURATION-REFS>
					<VENDOR-SPECIFIC-MODULE-DEF-REFS>
						<VENDOR-SPECIFIC-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/MICROSAR/StbM</VENDOR-SPECIFIC-MODULE-DEF-REF>
					</VENDOR-SPECIFIC-MODULE-DEF-REFS>
				</BSW-IMPLEMENTATION>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="49e04d4e-4431-4556-80db-32afa415cdfd">
					<SHORT-NAME>StbM_Pre</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/StbM</DEFINITION-REF>
					<CONTAINERS></CONTAINERS>
				</ECUC-MODULE-CONFIGURATION-VALUES>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="13064dd8-530f-4496-b9db-fb78372253ab">
					<SHORT-NAME>StbM_Rec</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/StbM</DEFINITION-REF>
					<CONTAINERS></CONTAINERS>
				</ECUC-MODULE-CONFIGURATION-VALUES>
			</ELEMENTS>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>