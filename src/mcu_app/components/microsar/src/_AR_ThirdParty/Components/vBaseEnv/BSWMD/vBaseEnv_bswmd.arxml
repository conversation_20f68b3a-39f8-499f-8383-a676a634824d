<?xml version="1.0" encoding="UTF-8"?>
<!--
***********************************************************************************************************************
COPYRIGHT
===============================================================================
Copyright (c) 2020 by Vector Informatik GmbH.                                         All rights reserved.

    This software is copyright protected and proprietary to Vector Informatik GmbH.
    Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
    All other rights remain with Vector Informatik GmbH.
===============================================================================
FILE DESCRIPTION
===============================================================================
File:           vBaseEnv_bswmd.arxml
Component:      vBaseEnv
Module:         vBaseEnv
Generator:      $GENERATOR$
Description:    $DESCRIPTION$
*********************************************************************************************************************** 
-->
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://autosar.org/schema/r4.0">
	<AR-PACKAGES>
		<AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
			<SHORT-NAME>MICROSAR</SHORT-NAME>
			<ADMIN-DATA>
				<DOC-REVISIONS>
					<DOC-REVISION>
						<REVISION-LABEL>0.06.00</REVISION-LABEL>
						<ISSUED-BY>visbwa</ISSUED-BY>
						<DATE>2018-07-03</DATE>
						<MODIFICATIONS>
							<MODIFICATION>
								<CHANGE>
									<L-2 L="EN">DerivativeInformation and TestedDerivative moved from BRS into vBaseEnv</L-2>
								</CHANGE>
								<REASON>
									<L-2 L="EN">First Release of vBaseEnv 0.06.0</L-2>
								</REASON>
							</MODIFICATION>
						</MODIFICATIONS>
					</DOC-REVISION>
					<DOC-REVISION>
						<REVISION-LABEL>1.00.00</REVISION-LABEL>
						<ISSUED-BY>visbwa</ISSUED-BY>
						<DATE>2018-08-14</DATE>
						<MODIFICATIONS>
							<MODIFICATION>
								<CHANGE>
									<L-2 L="EN">Added MemLayoutHwRegion for vLinkGen and added AdditionalParameter PowerDownModes</L-2>
								</CHANGE>
								<REASON>
									<L-2 L="EN">First Release of final version vBaseEnv 1.0.0</L-2>
								</REASON>
							</MODIFICATION>
						</MODIFICATIONS>
					</DOC-REVISION>
					<DOC-REVISION>
						<REVISION-LABEL>1.00.01</REVISION-LABEL>
						<ISSUED-BY>visbwa</ISSUED-BY>
						<DATE>2018-08-15</DATE>
						<MODIFICATIONS>
							<MODIFICATION>
								<CHANGE>
									<L-2 L="EN">Added CpuCore value Cortex-M7</L-2>
								</CHANGE>
							</MODIFICATION>
						</MODIFICATIONS>
					</DOC-REVISION>
					<DOC-REVISION>
						<REVISION-LABEL>1.00.02</REVISION-LABEL>
						<ISSUED-BY>visbwa</ISSUED-BY>
						<DATE>2018-08-16</DATE>
						<MODIFICATIONS>
							<MODIFICATION>
								<CHANGE>
									<L-2 L="EN">Removed out-dated HwRegionZeroInitType and HwRegionZeroInitCore from MemLayoutHwRegion</L-2>
								</CHANGE>
								<REASON>
									<L-2 L="EN">Structure for vLinkGen 1.0.0 was finally changed</L-2>
								</REASON>
							</MODIFICATION>
						</MODIFICATIONS>
					</DOC-REVISION>
					<DOC-REVISION>
						<REVISION-LABEL>1.00.03</REVISION-LABEL>
						<ISSUED-BY>visbwa</ISSUED-BY>
						<DATE>2018-09-13</DATE>
						<MODIFICATIONS>
							<MODIFICATION>
								<CHANGE>
									<L-2 L="EN">Changed default display settings of memory sections to HEX</L-2>
								</CHANGE>
							</MODIFICATION>
						</MODIFICATIONS>
					</DOC-REVISION>
					<DOC-REVISION>
						<REVISION-LABEL>1.00.04</REVISION-LABEL>
						<ISSUED-BY>visbwa</ISSUED-BY>
						<DATE>2019-01-16</DATE>
						<MODIFICATIONS>
							<MODIFICATION>
								<CHANGE>
									<L-2 L="EN">Added several CpuCore defines (including OTHER)</L-2>
								</CHANGE>
							</MODIFICATION>
						</MODIFICATIONS>
					</DOC-REVISION>
					<DOC-REVISION>
						<REVISION-LABEL>1.00.05</REVISION-LABEL>
						<ISSUED-BY>visbwa</ISSUED-BY>
						<DATE>2019-01-22</DATE>
						<MODIFICATIONS>
							<MODIFICATION>
								<CHANGE>
									<L-2 L="EN">Added several CpuCore defines</L-2>
								</CHANGE>
							</MODIFICATION>
						</MODIFICATIONS>
					</DOC-REVISION>
					<DOC-REVISION>
						<REVISION-LABEL>1.01.00</REVISION-LABEL>
						<ISSUED-BY>visbwa</ISSUED-BY>
						<DATE>2019-03-15</DATE>
						<MODIFICATIONS>
							<MODIFICATION>
								<CHANGE>
									<L-2 L="EN">Added InterruptHandling, increased several AdditionalParameters lists</L-2>
								</CHANGE>
								<REASON>
									<L-2 L="EN">vBaseEnv 1.4.0</L-2>
								</REASON>
							</MODIFICATION>
						</MODIFICATIONS>
					</DOC-REVISION>
					<DOC-REVISION>
						<REVISION-LABEL>1.02.00</REVISION-LABEL>
						<ISSUED-BY>visbwa</ISSUED-BY>
						<DATE>2019-05-09</DATE>
						<MODIFICATIONS>
							<MODIFICATION>
								<CHANGE>
									<L-2 L="EN">Added peripheral clock settings, added several CpuCore defines, added Watchdog Group, added OpMode Group, added UserModeAccess Group</L-2>
								</CHANGE>
								<REASON>
									<L-2 L="EN">vBaseEnv 1.5.0</L-2>
								</REASON>
							</MODIFICATION>
						</MODIFICATIONS>
					</DOC-REVISION>
					<DOC-REVISION>
						<REVISION-LABEL>1.02.01</REVISION-LABEL>
						<ISSUED-BY>visbwa</ISSUED-BY>
						<DATE>2019-05-14</DATE>
						<MODIFICATIONS>
							<MODIFICATION>
								<CHANGE>
									<L-2 L="EN">Fixed typo in vBaseEnvLin_ClockRef</L-2>
								</CHANGE>
							</MODIFICATION>
						</MODIFICATIONS>
					</DOC-REVISION>
					<DOC-REVISION>
						<REVISION-LABEL>1.03.00</REVISION-LABEL>
						<ISSUED-BY>visbwa</ISSUED-BY>
						<DATE>2019-05-29</DATE>
						<MODIFICATIONS>
							<MODIFICATION>
								<CHANGE>
									<L-2 L="EN">Added several CpuCore defines for PowerPC cores</L-2>
								</CHANGE>
								<REASON>
									<L-2 L="EN">vBaseEnv 1.7.0</L-2>
								</REASON>
							</MODIFICATION>
						</MODIFICATIONS>
					</DOC-REVISION>
					<DOC-REVISION>
						<REVISION-LABEL>1.03.01</REVISION-LABEL>
						<ISSUED-BY>visbwa</ISSUED-BY>
						<DATE>2019-08-16</DATE>
						<MODIFICATIONS>
							<MODIFICATION>
								<CHANGE>
									<L-2 L="EN">Added additional parameter PowerDownModes</L-2>
								</CHANGE>
								<REASON>
									<L-2 L="EN">needed for RH850</L-2>
								</REASON>
							</MODIFICATION>
						</MODIFICATIONS>
					</DOC-REVISION>
					<DOC-REVISION>
						<REVISION-LABEL>1.03.02</REVISION-LABEL>
						<ISSUED-BY>visjhr</ISSUED-BY>
						<DATE>2019-09-02</DATE>
						<MODIFICATIONS>
							<MODIFICATION>
								<CHANGE>
									<L-2 L="EN">Added new CpuCore values: CORTEX_R5F and E200Z759</L-2>
								</CHANGE>
								<REASON>
									<L-2 L="EN">PSC-3381 und PSC-3383</L-2>
								</REASON>
							</MODIFICATION>
						</MODIFICATIONS>
					</DOC-REVISION>
					<DOC-REVISION>
						<REVISION-LABEL>1.04.00</REVISION-LABEL>
						<ISSUED-BY>visbwa</ISSUED-BY>
						<DATE>2019-09-19</DATE>
						<MODIFICATIONS>
							<MODIFICATION>
								<CHANGE>
									<L-2 L="EN">Added new parameter vBaseEnvCpuInitCore, reworked several descriptions of DerivativeInformation parameters</L-2>
								</CHANGE>
								<REASON>
									<L-2 L="EN">PSC-3549</L-2>
								</REASON>
							</MODIFICATION>
						</MODIFICATIONS>
					</DOC-REVISION>
					<DOC-REVISION>
						<REVISION-LABEL>1.05.00</REVISION-LABEL>
						<ISSUED-BY>visbwa</ISSUED-BY>
						<DATE>2020-02-17</DATE>
						<MODIFICATIONS>
							<MODIFICATION>
								<CHANGE>
									<L-2 L="EN">Added new parameter vBaseEnvHwRegionType</L-2>
								</CHANGE>
								<REASON>
									<L-2 L="EN">HALBE-1135</L-2>
								</REASON>
							</MODIFICATION>
						</MODIFICATIONS>
					</DOC-REVISION>
					<DOC-REVISION>
						<REVISION-LABEL>1.05.01</REVISION-LABEL>
						<ISSUED-BY>vismaa</ISSUED-BY>
						<DATE>2020-09-09</DATE>
						<MODIFICATIONS>
							<MODIFICATION>
								<CHANGE>
									<L-2 L="EN">Added new CpuCore TC18</L-2>
								</CHANGE>
								<REASON>
									<L-2 L="EN">HALBE-2332</L-2>
								</REASON>
							</MODIFICATION>
						</MODIFICATIONS>
					</DOC-REVISION>
					<DOC-REVISION>
						<REVISION-LABEL>1.05.02</REVISION-LABEL>
						<ISSUED-BY>visbwa</ISSUED-BY>
						<DATE>2020-09-18</DATE>
						<MODIFICATIONS>
							<MODIFICATION>
								<CHANGE>
									<L-2 L="EN">Added new DerivativeInformation optional parameter vBaseEnvUsedManual</L-2>
								</CHANGE>
								<REASON>
									<L-2 L="EN">HALBE-2773</L-2>
								</REASON>
							</MODIFICATION>
						</MODIFICATIONS>
					</DOC-REVISION>
				</DOC-REVISIONS>
			</ADMIN-DATA>
			<ELEMENTS>
				<BSW-IMPLEMENTATION UUID="159ca0aa-babb-4b42-9884-5b0625d2a6a7">
					<SHORT-NAME>vBaseEnv_Impl</SHORT-NAME>
					<PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
					<SW-VERSION>1.00.00</SW-VERSION>
					<USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
					<VENDOR-ID>30</VENDOR-ID>
					<AR-RELEASE-VERSION>4.03.00</AR-RELEASE-VERSION>
					<PRECONFIGURED-CONFIGURATION-REFS>
						<PRECONFIGURED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/vBaseEnv_Pre</PRECONFIGURED-CONFIGURATION-REF>
					</PRECONFIGURED-CONFIGURATION-REFS>
					<RECOMMENDED-CONFIGURATION-REFS>
						<RECOMMENDED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/vBaseEnv_Rec</RECOMMENDED-CONFIGURATION-REF>
					</RECOMMENDED-CONFIGURATION-REFS>
					<VENDOR-SPECIFIC-MODULE-DEF-REFS>
						<VENDOR-SPECIFIC-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/MICROSAR/vBaseEnv</VENDOR-SPECIFIC-MODULE-DEF-REF>
					</VENDOR-SPECIFIC-MODULE-DEF-REFS>
				</BSW-IMPLEMENTATION>
				<ECUC-MODULE-DEF UUID="520e746e-c7dd-4c58-99b7-ae3ab1339329">
					<SHORT-NAME>vBaseEnv</SHORT-NAME>
					<LONG-NAME>
						<L-4 L="EN">Vector Base Environment</L-4>
					</LONG-NAME>
					<DESC>
						<L-2 L="EN">Configuration of the hardware.</L-2>
					</DESC>
					<CATEGORY>VENDOR_SPECIFIC_MODULE_DEFINITION</CATEGORY>
					<ADMIN-DATA>
						<DOC-REVISIONS>
							<DOC-REVISION>
								<REVISION-LABEL>0.01.00</REVISION-LABEL>
								<STATE>prototype</STATE>
								<ISSUED-BY>vibwa</ISSUED-BY>
								<DATE>2018-06-11</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">initial vBaseEnv version</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">new Preconfig and HW-selection concept</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
						</DOC-REVISIONS>
					</ADMIN-DATA>
					<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
					<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
					<SUPPORTED-CONFIG-VARIANTS>
						<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
					</SUPPORTED-CONFIG-VARIANTS>
					<CONTAINERS>
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="36bfbe61-fa45-4418-bdca-b4c641dda822">
							<SHORT-NAME>vBaseEnvGeneral</SHORT-NAME>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
							<REFERENCES>
								<ECUC-REFERENCE-DEF UUID="678b7739-4321-422f-a313-73754e9173f0">
									<SHORT-NAME>vBaseEnvTestedDerivativeRef</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Tested Derivative</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Select a Tested Derivative from the list of supported ones.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vBaseEnv/vBaseEnvGeneral/vBaseEnvDerivativeInformation</DESTINATION-REF>
								</ECUC-REFERENCE-DEF>
							</REFERENCES>
							<SUB-CONTAINERS>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="b536cccb-fdc6-4f64-8db7-aeecbc0f583a">
									<SHORT-NAME>vBaseEnvDerivativeInformation</SHORT-NAME>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<ECUC-INTEGER-PARAM-DEF UUID="6372fa95-80fa-4154-b7e5-084796197335">
											<SHORT-NAME>vBaseEnvCpuMaxFrequency</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configure the maximum frequency of this derivative.
This value is used to validate the vBRS PLL configuration.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">HZ</SD>
														<SD GID="DV:Unit">MHZ</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>100000000</DEFAULT-VALUE>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="fed537f9-924e-42a0-a567-a626a5e40ba8">
											<SHORT-NAME>vBaseEnvAvailableChannels_Flexray</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configure the amount of Flexray channels, available on this derivative.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>256</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="aba5d6ca-abb3-4a10-b94a-83fa5411142e">
											<SHORT-NAME>vBaseEnvAvailableChannels_Can</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configure the amount of CAN channels, available on this derivative.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>256</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="6e4e0fbb-ddde-4f30-901a-3ab09d7b79b9">
											<SHORT-NAME>vBaseEnvAvailableChannels_Lin</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configure the amount of LIN channels, available on this derivative.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>256</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="bcb37216-aa4c-4f98-a6d6-c09520446dc6">
											<SHORT-NAME>vBaseEnvAvailableChannels_Ethernet</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configure the amount of Ethernet channels, available on this derivative.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>256</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="040cbea7-7c2a-49ff-9a0a-b5fb3efd695e">
											<SHORT-NAME>vBaseEnvCpuCoreAmount</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configure the amount of cores, available on this derivative.
This value will be generated into vBrsCfg.h as
BRS_CPU_CORE_AMOUNT</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>1</DEFAULT-VALUE>
											<MAX>256</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-STRING-PARAM-DEF UUID="9aaccae4-85a2-442b-9db8-d199be13222b">
											<SHORT-NAME>vBaseEnvTestedDerivative</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configure the correct Mcu_Derivative value of this derivative.
This value will be generated into vBrsCfg.h as
BRS_DERIVATIVE_x</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-STRING-PARAM-DEF>
										<ECUC-STRING-PARAM-DEF UUID="872bdfe9-aa6e-4c16-8118-f5ac2e38049b">
											<SHORT-NAME>vBaseEnvTestedDerivativeDescription</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Describe here more precisely this derivative.
This description is only textual information for the user.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-STRING-PARAM-DEF>
										<ECUC-ENUMERATION-PARAM-DEF UUID="ab16d57a-9dbb-411f-abd4-88bb83615862">
											<SHORT-NAME>vBaseEnvCpuCore</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configure the core architecture of this derivative.
This value will be generated into vBrsCfg.h as
BRS_CPU_CORE_x</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="b226e73f-c27b-4a32-ba5e-fabefe3f23c7">
													<SHORT-NAME>OTHER</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Generic value for cores with no specific value</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="dc14e103-**************-5d2720a77f12">
													<SHORT-NAME>CORTEX_M0</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Arm Cortex M0</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="beaacc8d-98a7-43ec-bcc9-1ec8b787e91c">
													<SHORT-NAME>CORTEX_M0PLUS</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Arm Cortex M0+</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="7ae06a90-6aae-452a-85a7-ba2ea6458ae7">
													<SHORT-NAME>CORTEX_M3</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Arm Cortex M3</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="6167f26e-6158-4cb3-9ede-6865729dedb4">
													<SHORT-NAME>CORTEX_M4</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Arm Cortex M4</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="cb693971-57bd-4295-8d48-e65cacfae570">
													<SHORT-NAME>CORTEX_M4F</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Arm Cortex M4F</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="f5f1354f-dab2-4779-a354-57c47e75b6b4">
													<SHORT-NAME>CORTEX_M7</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Arm Cortex M7</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="65348938-8b65-461a-8f0f-075bd0fac94a">
													<SHORT-NAME>CORTEX_M33</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Arm Cortex M33</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="49592f36-4620-44c3-b4f9-e7c35ea36d8b">
													<SHORT-NAME>CORTEX_R4</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Arm Cortex R4</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="21fe0999-6cec-4fb5-b01d-8422c7bc86b1">
													<SHORT-NAME>CORTEX_R4F</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Arm Cortex R4F</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="ff56490d-5163-494c-92ba-4efd368711c1">
													<SHORT-NAME>CORTEX_R5</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">ARM Cortex R5</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="97a2d2c7-ada5-45d7-ad0f-719fa199f1dc">
													<SHORT-NAME>CORTEX_R5F</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Arm Cortex R5F</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="fb4a18bc-819b-49da-86b0-0b5e8f9d48b1">
													<SHORT-NAME>CORTEX_R7</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">ARM Cortex R7</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="e2144343-ae96-46cf-a508-060a987bb1c8">
													<SHORT-NAME>CORTEX_R52</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Arm Cortex R52</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="2f7354df-5502-47e0-b6ca-b295cfecf38c">
													<SHORT-NAME>E200Z0</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">NXP/ST PowerPC RISC Core e200z0</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="78c9b05d-247d-4d06-9bb8-3615c9338da0">
													<SHORT-NAME>E200Z210</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">NXP/ST PowerPC RISC Core e200z210</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="a674c5d6-fce3-4ea1-8d7f-d9171cf1806e">
													<SHORT-NAME>E200Z215</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">NXP/ST PowerPC RISC Core e200z215</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="c75179df-f1e9-41d0-a3d9-fd42d7cb85a8">
													<SHORT-NAME>E200Z225</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">NXP/ST PowerPC RISC Core e200z225</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="f63a1c8c-4f7c-459a-a0ec-381cc7bcefad">
													<SHORT-NAME>E200Z410</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">NXP/ST PowerPC RISC Core e200z410</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="*************-4fe0-8071-a90ed0b57359">
													<SHORT-NAME>E200Z420</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">NXP/ST PowerPC RISC Core e200z420</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="613cdf77-bf89-43f1-8b19-46df413fec54">
													<SHORT-NAME>E200Z425</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">NXP/ST PowerPC RISC Core e200z425</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="d2390a12-9ecd-4993-90e4-7af9b9f293cb">
													<SHORT-NAME>E200Z710</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">NXP/ST PowerPC RISC Core e200z710</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="20213b37-af65-44b6-82df-fb63f645b00b">
													<SHORT-NAME>E200Z720</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">NXP/ST PowerPC RISC Core e200z720</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="fcae7f4d-c6a2-413d-b3f0-26b9d2c3a8b2">
													<SHORT-NAME>E200Z726</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">NXP/ST PowerPC RISC Core e200z726</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="593d6c6d-47f3-4ea7-9e74-bb65db3b3e66">
													<SHORT-NAME>E200Z759</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">NXP/ST PowerPC RISC Core e200z759</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="72cb3407-4984-4679-9c7d-340c5216a2fb">
													<SHORT-NAME>G3K</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Renesas G3K</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="bda69edb-92e2-4eec-bbfc-213d086c58fe">
													<SHORT-NAME>G3KH</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Renesas G3KH</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="b57017e5-b7a1-4ebd-bba4-fa05df4a9fbc">
													<SHORT-NAME>G3M</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Renesas G3M</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="17b2157e-b4a4-43a6-83df-5be26f7ae655">
													<SHORT-NAME>G4MH</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Renesas G4MH</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="59b613fe-4269-440d-a186-781dc94a5ff5">
													<SHORT-NAME>TC161</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Infineon TriCore Aurix Core</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="add5b863-63ed-4461-b6de-fa95d54dfda0">
													<SHORT-NAME>TC162</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Infineon TriCore AurixPlus Core</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="70b837ee-e428-4093-bd35-7f0b0c080cd5">
													<SHORT-NAME>TC18</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Infineon TriCore Aurix 3G (TC4xx)</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="74d97346-1f4f-4847-b505-2ce151f94bb0">
											<SHORT-NAME>vBaseEnvCpuInitCore</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Configure the core ID of the core, which should be used for general initialization.
For normal, this is the boot core.
This value will be generated into vBrsCfg.h as
BRS_CPU_INIT_CORE</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>255</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-STRING-PARAM-DEF UUID="b5ce5f95-13d9-4a28-bdbf-eabf865db7a2">
											<SHORT-NAME>vBaseEnvUsedManual</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Manual and version, used for the development of the vBaseEnv preconfig</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-STRING-PARAM-DEF>
									</PARAMETERS>
									<SUB-CONTAINERS>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="c1d9a404-967f-4991-8e85-05437d9f95bb">
											<SHORT-NAME>vBaseEnvAdditionalParameters</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Additional parameters for generation of additional defines, if necessary for the depending platform.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<PARAMETERS>
												<ECUC-STRING-PARAM-DEF UUID="6c60aa3b-1a48-44fd-8857-65a330434c15">
													<SHORT-NAME>vBaseEnvDerivativeGroup</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Define the Derivative Group of this derivative</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<ECUC-ENUMERATION-PARAM-DEF UUID="0164165d-f469-4f9d-bcfc-261ed161887c">
													<SHORT-NAME>vBaseEnvPllGroup</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Configure the PLL Group of your derivative</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="8b335b41-afc3-48e9-821a-815988b57c5e">
															<SHORT-NAME>A</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">PLL Group A</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="6320c7aa-790c-4c00-864e-f27459c249ad">
															<SHORT-NAME>A1</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">PLL Group A1</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="eeabff4e-4f8e-4bfa-9cbb-e5a1498372b7">
															<SHORT-NAME>A2</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">PLL Group A2</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="27bba7e6-51e7-469e-858a-410849f2737b">
															<SHORT-NAME>A3</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">PLL Group A3</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="7ca1cb16-317e-4d42-916a-a68f6e67c952">
															<SHORT-NAME>A4</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">PLL Group A4</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="861551a2-e0df-476c-ae65-e8f45f6c18b2">
															<SHORT-NAME>B</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">PLL Group B</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="3234aabe-426c-4477-8fd2-a4459619daba">
															<SHORT-NAME>C</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">PLL Group C</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="2052483b-268b-4abe-9371-6eb32d002299">
															<SHORT-NAME>D</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">PLL Group D</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="c7484368-3084-462c-bdb2-abd8a7bda28f">
															<SHORT-NAME>E</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">PLL Group E</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="99821635-62c2-40cb-826f-fa361da75b1b">
															<SHORT-NAME>F</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">PLL Group F</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="eff6c71a-12be-4203-acb0-a0cd19961449">
															<SHORT-NAME>G</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">PLL Group G</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="bdec6458-bca5-472d-8781-ba65c765a383">
															<SHORT-NAME>H</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">PLL Group H</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="908510f8-60dc-4410-a53b-b8c8aa739089">
															<SHORT-NAME>I</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">PLL Group I</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="c5c9bbdd-c02c-4de8-a879-20dcc6c2b2ad">
															<SHORT-NAME>J</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">PLL Group J</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="e3978bc4-b653-40a0-a33e-f5c01f58b796">
															<SHORT-NAME>K</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">PLL Group K</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="8099c2b1-8b34-425f-afaf-839f837cf28f">
															<SHORT-NAME>L</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">PLL Group L</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<ECUC-ENUMERATION-PARAM-DEF UUID="1811e373-7a78-4632-ac63-0a783fb2787b">
													<SHORT-NAME>vBaseEnvClockGroup</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Configure the Clock Group of your derivative</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="25e3d78b-0e1f-406b-b486-87cbed874849">
															<SHORT-NAME>A</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Clock Group A</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="a87b8ddc-91db-4f74-a7e0-b404b0183b68">
															<SHORT-NAME>A1</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Clock Group A1</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="bf2b1d5c-a943-410d-b6a7-93bd72789f16">
															<SHORT-NAME>A2</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Clock Group A2</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ce15fe8e-d0f7-4a87-847b-8766d5b0e4c8">
															<SHORT-NAME>A3</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Clock Group A3</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="1ed1657d-6cd8-4b19-8cb2-5179edb8ed3e">
															<SHORT-NAME>A4</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Clock Group A4</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="7d5f6cdf-f6c1-4428-88e2-67a059c50963">
															<SHORT-NAME>B</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Clock Group B</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="39aef1e0-aa7d-4599-aeaf-c354dda1adb4">
															<SHORT-NAME>C</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Clock Group C</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="0ba525a7-4d70-47ea-9f9b-7fc6933b3f86">
															<SHORT-NAME>D</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Clock Group D</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="a43c955f-9f41-40a5-8d27-2a942d88d635">
															<SHORT-NAME>E</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Clock Group E</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="271b4fed-a49a-45c7-a2aa-4a414db835fd">
															<SHORT-NAME>F</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Clock Group F</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="0e751f46-eb62-43b8-918f-f70879335250">
															<SHORT-NAME>G</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Clock Group G</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="3c85a8ac-4da5-4cc9-bd1b-fe9c78804135">
															<SHORT-NAME>H</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Clock Group H</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="30bf85eb-b1da-4b48-9060-df1d9b0b6039">
															<SHORT-NAME>I</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Clock Group I</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="a7d9c09e-3d69-4b7b-a0fb-7343af85af3d">
															<SHORT-NAME>J</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Clock Group J</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="76424116-e818-486b-87c4-08b532080d01">
															<SHORT-NAME>K</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Clock Group K</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ae8045a3-4221-4c9d-ac03-3c5177724639">
															<SHORT-NAME>L</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Clock Group L</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<ECUC-ENUMERATION-PARAM-DEF UUID="eb9f584f-a9a0-43dd-9900-c23d78fdee2f">
													<SHORT-NAME>vBaseEnvPortGroup</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Configure the Port Group of your derivative</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="867ec031-70fc-481a-857a-7c5865024aed">
															<SHORT-NAME>A</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Port Group A</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="f5f338da-c737-46f6-a6b1-e8744ccd138f">
															<SHORT-NAME>A1</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Port Group A1</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="7eab78f0-ac3b-4f10-b4cc-f46d8427d125">
															<SHORT-NAME>A2</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Port Group A2</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="c92142c5-0d8b-4932-a34e-7c0d9376a5ed">
															<SHORT-NAME>A3</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Port Group A3</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="5f42728d-123c-494c-8aff-54fc72ea1784">
															<SHORT-NAME>A4</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Port Group A4</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="746831c2-df09-46c8-9363-f72aad5806d6">
															<SHORT-NAME>B</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Port Group B</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="d7d174e0-dc0c-400c-a251-33af5d9fa247">
															<SHORT-NAME>B1</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Port Group B1</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="be0f009e-4c02-4f6a-a21b-fe1f6f1d4180">
															<SHORT-NAME>B2</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Port Group B2</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="c34a60b3-da46-4522-a542-c43c735bd782">
															<SHORT-NAME>C</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Port Group C</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="bcb7688d-8a99-44a9-ab7f-e627708256d4">
															<SHORT-NAME>D</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Port Group D</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="79dcd485-7844-4b0b-96d1-eec00850b78d">
															<SHORT-NAME>E</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Port Group E</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="5b834bb5-8692-4f3b-b2ac-a7fd8b83a858">
															<SHORT-NAME>F</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Port Group F</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="0aacf1c5-656a-4110-b55d-d7afed64a6c6">
															<SHORT-NAME>G</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Port Group G</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="a0af4d54-864c-426e-a09e-522320d67814">
															<SHORT-NAME>H</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Port Group H</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="8a4c4962-ae3f-410c-9c31-42fe3fcba825">
															<SHORT-NAME>I</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Port Group I</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="183bc6eb-0641-43ae-8139-600b8d031061">
															<SHORT-NAME>J</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Port Group J</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="e01e4f0b-13c7-4dd1-a6a9-e08eb7144009">
															<SHORT-NAME>K</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Port Group K</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="eb839e79-66b1-4a1d-bf80-c6b520b60448">
															<SHORT-NAME>L</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Port Group L</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<ECUC-ENUMERATION-PARAM-DEF UUID="1347312d-6baa-47f7-ac4c-784ea44a2d18">
													<SHORT-NAME>vBaseEnvProtectionGroup</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Configure the Protection Group of your derivative</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="6e3395a7-2068-4e12-b61a-5f1cd56d91fd">
															<SHORT-NAME>A</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Protection Group A</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="66721746-6d0a-4f4d-898b-17d337633893">
															<SHORT-NAME>B</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Protection Group B</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="a89aad53-c188-4f75-af2f-d660e8cd903b">
															<SHORT-NAME>C</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Protection Group C</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="35aae31f-d172-4a25-a898-37a99b843f80">
															<SHORT-NAME>D</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Protection Group D</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="d1638429-12e4-4450-81f5-86142994d58a">
															<SHORT-NAME>E</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Protection Group E</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="753a345d-c7c8-4573-a4f5-f335a08028e9">
															<SHORT-NAME>F</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Protection Group F</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="d3d4f892-83d6-40bc-9261-546f81138d71">
															<SHORT-NAME>G</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Protection Group G</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="203081bf-2e0e-4ac7-808c-7948d440d153">
															<SHORT-NAME>H</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Protection Group H</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="21148253-245d-45e0-8939-d235083d9361">
															<SHORT-NAME>I</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Protection Group I</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="8ac3b93f-55ba-4fd0-ae34-6d0b37af6c83">
															<SHORT-NAME>J</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Protection Group J</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="680886b6-4c51-45fd-a1ff-7dd4f1202d5d">
															<SHORT-NAME>K</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Protection Group K</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="e3e31e8c-161b-40ba-8b90-3b3114eaf098">
															<SHORT-NAME>L</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Protection Group L</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="1fe36518-b14f-4c91-a2fa-f7dd5d9173e0">
															<SHORT-NAME>NONE</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">No Protection Group</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<ECUC-ENUMERATION-PARAM-DEF UUID="9c44d3e1-c3f2-490e-aeab-53cddc52a177">
													<SHORT-NAME>vBaseEnvResetGroup</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Configure the Reset Group of your derivative</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="149ced65-ab85-4ddd-bd47-6987b56685ae">
															<SHORT-NAME>A</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Reset Group A</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="6b19c7c7-b79c-416f-b364-149502d07cbd">
															<SHORT-NAME>B</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Reset Group B</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="b1225905-8233-4574-b979-eb953faac83c">
															<SHORT-NAME>C</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Reset Group C</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="05540ed3-4c72-4f48-a08f-596a03c7b001">
															<SHORT-NAME>D</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Reset Group D</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="fd8facc9-58c0-4627-9c4d-ba40d125a7eb">
															<SHORT-NAME>E</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Reset Group E</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="e1a2b149-2e2b-4fe8-9fab-26e8203b939a">
															<SHORT-NAME>F</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Reset Group F</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ee7a1a8b-74ff-47be-94e3-803a5ce63f83">
															<SHORT-NAME>G</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Reset Group G</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="a46f4067-56eb-4a9d-85b8-f286126bc705">
															<SHORT-NAME>H</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Reset Group H</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="7d3cbd9c-3222-401f-ad98-c13be32873ce">
															<SHORT-NAME>I</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Reset Group I</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="b443ab10-8df7-4588-8f9a-be0dbc937fa6">
															<SHORT-NAME>J</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Reset Group J</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="32fecffa-f32a-4482-800a-400baf64f8af">
															<SHORT-NAME>K</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Reset Group K</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="0d848c4a-87cb-403b-97e1-4737a08a3f26">
															<SHORT-NAME>L</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Reset Group L</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<ECUC-ENUMERATION-PARAM-DEF UUID="9a1276d0-**************-e683819e682c">
													<SHORT-NAME>vBaseEnvWatchdogGroup</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Configure the Watchdog Group of your derivative</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="91574a57-cb60-4ca6-b4a0-8517c71f9383">
															<SHORT-NAME>NONE</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Watchdog Group NONE</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="5abb8fde-82fb-4816-a8b9-381388089bc6">
															<SHORT-NAME>A</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Watchdog Group A</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="0a502882-8801-45d4-9ddb-fc226ea3b011">
															<SHORT-NAME>A1</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Watchdog Group A1</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="378af8c9-ca21-4a76-9b9b-d03691c9dd38">
															<SHORT-NAME>A2</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Watchdog Group A2</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="fb851987-bfb4-4546-a57e-6c73df3436e2">
															<SHORT-NAME>A3</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Watchdog Group A3</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="f9d68a2a-319b-41fc-b6eb-5e6a9652dbf3">
															<SHORT-NAME>A4</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Watchdog Group A4</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="08260af4-75ef-4b2a-9cf8-9c153fabe807">
															<SHORT-NAME>A5</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Watchdog Group A5</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="09041338-d204-4383-a4b1-0c978e16b963">
															<SHORT-NAME>A6</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Watchdog Group A6</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="4bd0571b-5f4c-4e0f-9978-1634d06491ea">
															<SHORT-NAME>A7</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Watchdog Group A7</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="8a512c61-c4dc-4e50-b43d-28aae35ac6f9">
															<SHORT-NAME>B</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Watchdog Group B</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="48266ed5-fc25-494e-beb1-58d691364ac4">
															<SHORT-NAME>C</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Watchdog Group C</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="71a78f8a-0e03-485a-9077-9635f9a56ec5">
															<SHORT-NAME>D</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Watchdog Group D</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="278a9ea9-0e3e-43e4-be16-aa1cff2b58b9">
															<SHORT-NAME>E</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Watchdog Group E</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<ECUC-ENUMERATION-PARAM-DEF UUID="1d5f9ab1-9c27-47a8-bd2f-f7ad39088322">
													<SHORT-NAME>vBaseEnvOpModeGroup</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Configure the Operation Mode Group of your derivative</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="b6debfc9-c3fd-49d8-8f06-d6143694d49f">
															<SHORT-NAME>NONE</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Operation Mode Group NONE</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="791963d4-4139-4d65-a758-edda855474df">
															<SHORT-NAME>A</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Operation Mode Group A</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="01158177-7704-4a2a-8ff5-21b46bf1caa1">
															<SHORT-NAME>A1</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Operation Mode Group A1</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="51b8dbe8-b87d-4a03-b446-eaada2b8846a">
															<SHORT-NAME>A2</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Operation Mode Group A2</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="aae2d583-387d-46eb-b4da-7e64cdde4b83">
															<SHORT-NAME>B</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Operation Mode Group B</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="237f5bcf-5b0a-4375-8e51-be6f7fdaf4d3">
															<SHORT-NAME>C</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Operation Mode Group C</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="7cca6a6e-cd48-41a1-b736-4b99be43edb9">
															<SHORT-NAME>D</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Operation Mode Group D</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="57c8e96d-07de-4102-a79a-ce17c0e8401c">
															<SHORT-NAME>E</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Operation Mode Group E</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<ECUC-ENUMERATION-PARAM-DEF UUID="24f6d986-eaa7-433c-9ff1-a6ae43d00db0">
													<SHORT-NAME>vBaseEnvUserModeAccessGroup</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Configure the User Mode Access Group of your derivative</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ef168c43-d991-48b3-b903-d678c107db86">
															<SHORT-NAME>A</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">User Mode Access Group A</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="d7ab720d-a832-47dd-bd8b-6fe9677257ef">
															<SHORT-NAME>A1</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">User Mode Access Group A1</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="19e00ada-9910-48b9-b311-d285400e8d27">
															<SHORT-NAME>A2</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">User Mode Access Group A2</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="83673bd7-69b1-4201-ab83-eed7b4b4351f">
															<SHORT-NAME>B</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">User Mode Access Group B</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="b6affa9d-0565-4d02-be1a-1be68031ab1e">
															<SHORT-NAME>C</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">User Mode Access Group C</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="20d7a5fc-c591-47ea-9036-2fdd7cb76bd3">
															<SHORT-NAME>D</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">User Mode Access Group D</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="dca8c6fd-e58c-4587-a6b2-03b90a7bfd94">
															<SHORT-NAME>E</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">User Mode Access Group E</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="1051fe7b-75b3-482f-9293-e3d3e28f2c60">
															<SHORT-NAME>F</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">User Mode Access Group F</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<ECUC-BOOLEAN-PARAM-DEF UUID="2a0798ad-dc7d-4bb8-a2cc-434472cb9a5f">
													<SHORT-NAME>vBaseEnvPowerDownModes</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Configure, if Power Down Modes to be used for your derivative</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="eb21f87b-b6ec-4719-a9c7-dacdbb6b6869">
											<SHORT-NAME>vBaseEnvMemLayoutHwRegion</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Memory Layout Hardware Region</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Container to structure the configuration parameters of hardware memory regions.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildNotDeletable">true</SD>
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<PARAMETERS>
												<ECUC-INTEGER-PARAM-DEF UUID="6ee5851b-58f9-41d2-8cd6-e11e83d8f9d8">
													<SHORT-NAME>vBaseEnvHwRegionStartAddress</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Hardware Region Start Address</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Start address of the hardware memory region.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:DefaultFormat">HEX</SD>
															</SDG>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="fe991146-26ad-46e2-a7ff-194d880bb1a7">
													<SHORT-NAME>vBaseEnvHwRegionSize</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Hardware Region Size</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Size of the hardware memory region.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:DefaultFormat">HEX</SD>
															</SDG>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="68a0a334-2d79-45af-974c-200097b63d67">
													<SHORT-NAME>vBaseEnvHwRegionEccAlignment</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Hardware Region ECC Alignment</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Required ECC Alignment of the hardware memory region.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:DefaultFormat">HEX</SD>
															</SDG>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-ENUMERATION-PARAM-DEF UUID="169fdfa6-1e71-4f05-acbf-82e7e750d49c">
													<SHORT-NAME>vBaseEnvHwRegionType</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Hardware Region Type</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Type of the hardware memory region.
This parameter is necessary for some linkers (e.g. Tasking), to distinguish between RAM/ROM/NVRAM.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="f2d7b68c-d760-4f4b-ad84-28009ed86a39">
															<SHORT-NAME>RAM</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Random-Access Memory</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="d3661b3e-bc80-452a-8f30-12f5a12bd013">
															<SHORT-NAME>ROM</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Read-Only Memory</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="0b2178e9-87d5-4fd2-b84f-1be496672b59">
															<SHORT-NAME>NVRAM</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Non-Volatile Random-Access Memory</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="dbd5200f-9bee-4b1e-83a0-2bb210fb0c80">
									<SHORT-NAME>vBaseEnvMemLayoutHwRegion</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Memory Layout Hardware Region</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Container to structure the configuration parameters of hardware memory regions.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildNotDeletable">true</SD>
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="0d46b873-5159-4f4b-9d41-add34afa2bf2">
									<SHORT-NAME>vBaseEnvInterruptHandling</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for family specific default interrupt settings</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<SUB-CONTAINERS>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="2a686f3f-d5a9-44ef-8b92-df2bf6ab3f74">
											<SHORT-NAME>vBaseEnvCan</SHORT-NAME>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<SUB-CONTAINERS>
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="c8b6dde1-038d-4a9f-b5eb-06adb9f57893">
													<SHORT-NAME>vBaseEnvChannel</SHORT-NAME>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<PARAMETERS>
														<ECUC-INTEGER-PARAM-DEF UUID="b4cadab5-977a-42d8-9159-fc70350faa9b">
															<SHORT-NAME>vBaseEnvIndex</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Channel Index</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">Logical channel</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:variantMultiplicityPBL">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<ECUC-INTEGER-PARAM-DEF UUID="1191b17c-c217-4973-80f3-ef2f7afebde0">
															<SHORT-NAME>vBaseEnvIsrInterruptPriority</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Channel Priority</L-4>
															</LONG-NAME>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:variantMultiplicityPBL">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<ECUC-INTEGER-PARAM-DEF UUID="c2b23bb8-b802-4570-92f9-2f42a21dbfc3">
															<SHORT-NAME>vBaseEnvIsrInterruptSource</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Channel Source Number</L-4>
															</LONG-NAME>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:variantMultiplicityPBL">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="7025aa7c-8ce8-45c6-9400-109bb77ae077">
											<SHORT-NAME>vBaseEnvLin</SHORT-NAME>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<SUB-CONTAINERS>
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="183ca6d5-5afc-43cf-af42-af7c53359920">
													<SHORT-NAME>vBaseEnvChannel</SHORT-NAME>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<PARAMETERS>
														<ECUC-INTEGER-PARAM-DEF UUID="c729ed6b-6ff0-43a3-90e0-8e6d85e70a7f">
															<SHORT-NAME>vBaseEnvIndex</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Channel Index</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">Logical channel</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:variantMultiplicityPBL">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<ECUC-INTEGER-PARAM-DEF UUID="003d33d5-2e10-4e3e-8640-2b50b1d858a5">
															<SHORT-NAME>vBaseEnvIsrInterruptPriority</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Channel Priority</L-4>
															</LONG-NAME>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:variantMultiplicityPBL">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<ECUC-INTEGER-PARAM-DEF UUID="49fed7c0-d9bd-40db-8e1f-63a07abf01ed">
															<SHORT-NAME>vBaseEnvIsrInterruptSource</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Channel Source Number</L-4>
															</LONG-NAME>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:variantMultiplicityPBL">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ba8359a0-6cf7-4521-b992-aac66c3bfab3">
											<SHORT-NAME>vBaseEnvFlexray</SHORT-NAME>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<SUB-CONTAINERS>
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="4c8dcfc3-8998-4a2a-aa56-465b29e0b7b9">
													<SHORT-NAME>vBaseEnvChannel</SHORT-NAME>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<PARAMETERS>
														<ECUC-INTEGER-PARAM-DEF UUID="da9f0fcf-e04c-45e2-bb36-7f08b6fd8139">
															<SHORT-NAME>vBaseEnvIndex</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Channel Index</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">Logical channel</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:variantMultiplicityPBL">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<ECUC-INTEGER-PARAM-DEF UUID="3f6e3105-803f-48d2-8b44-816f649ebffa">
															<SHORT-NAME>vBaseEnvIsrInterruptPriority</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Channel Priority</L-4>
															</LONG-NAME>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:variantMultiplicityPBL">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<ECUC-INTEGER-PARAM-DEF UUID="cff7d8b3-b257-47c0-812e-e29b2169fc1f">
															<SHORT-NAME>vBaseEnvIsrInterruptSource</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Channel Source Number</L-4>
															</LONG-NAME>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:variantMultiplicityPBL">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="f434df43-e5c7-47ef-a078-cbf24e74b231">
											<SHORT-NAME>vBaseEnvEthernet</SHORT-NAME>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<SUB-CONTAINERS>
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="d6b26782-a8dd-411f-9e86-0ae11e9a7fdf">
													<SHORT-NAME>vBaseEnvChannel</SHORT-NAME>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<PARAMETERS>
														<ECUC-INTEGER-PARAM-DEF UUID="b3b68af2-3737-4999-8a84-d64230b0cde2">
															<SHORT-NAME>vBaseEnvIndex</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Channel Index</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">Logical channel</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:variantMultiplicityPBL">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<ECUC-INTEGER-PARAM-DEF UUID="b20c3af5-f4ef-4ce1-b07c-1fc5029d27d4">
															<SHORT-NAME>vBaseEnvIsrInterruptPriority</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Channel Priority</L-4>
															</LONG-NAME>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:variantMultiplicityPBL">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<ECUC-INTEGER-PARAM-DEF UUID="5c48491f-fae8-4cfc-8d6d-69ab11a4962a">
															<SHORT-NAME>vBaseEnvIsrInterruptSource</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Channel Source Number</L-4>
															</LONG-NAME>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:CfgPostBuild">
																		<SD GID="DV:variantMultiplicityPBL">false</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="02859b55-98c6-481d-b86d-1cf9e970db7f">
									<SHORT-NAME>vBaseEnvPeripheralClocks</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for family specific default peripheral clock settings</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<SUB-CONTAINERS>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ecdc4218-39c4-445e-9b9b-292e0c78195f">
											<SHORT-NAME>vBaseEnvCan</SHORT-NAME>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<PARAMETERS>
												<ECUC-INTEGER-PARAM-DEF UUID="3f6c319f-60dc-4f6e-b75f-66c6adf67ecf">
													<SHORT-NAME>vBaseEnvCan_Clock</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Frequency of CAN peripheral clock</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Default frequency, used for PLL calculation of CAN driver peripheral clock.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">HZ</SD>
																<SD GID="DV:Unit">MHZ</SD>
															</SDG>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>18446744073709551615</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<ECUC-REFERENCE-DEF UUID="fcf9bf71-f304-4a55-a578-a133e1023bc1">
													<SHORT-NAME>vBaseEnvCan_ClockRef</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Frequency of CAN peripheral clock - Mcu Reference</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Default McuClockReferencePoint reference for CAN peripheral clock setting.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="5da695a4-b9be-4ad9-85a8-557a2b4ba857">
											<SHORT-NAME>vBaseEnvLin</SHORT-NAME>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<PARAMETERS>
												<ECUC-INTEGER-PARAM-DEF UUID="2859e1ef-f2d3-4b21-a520-2e7a244b3d6a">
													<SHORT-NAME>vBaseEnvLin_Clock</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Frequency of LIN peripheral clock</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Default frequency, used for PLL calculation of LIN driver peripheral clock.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">HZ</SD>
																<SD GID="DV:Unit">MHZ</SD>
															</SDG>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>18446744073709551615</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<ECUC-REFERENCE-DEF UUID="d8b7fe8c-13eb-47e0-8f86-b8459072e1b8">
													<SHORT-NAME>vBaseEnvLin_ClockRef</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Frequency of LIN peripheral clock - Mcu Reference</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Default McuClockReferencePoint reference for LIN peripheral clock setting.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="f476e74d-52b6-4c8d-acff-6abdbe910dbb">
											<SHORT-NAME>vBaseEnvFlexray</SHORT-NAME>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<PARAMETERS>
												<ECUC-INTEGER-PARAM-DEF UUID="193bc9fe-3187-444f-a9da-668930ca363b">
													<SHORT-NAME>vBaseEnvFlexray_Clock</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Frequency of FlexRay peripheral clock</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Default frequency, used for PLL calculation of FlexRay driver peripheral clock.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">HZ</SD>
																<SD GID="DV:Unit">MHZ</SD>
															</SDG>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>18446744073709551615</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<ECUC-REFERENCE-DEF UUID="7c648641-**************-c7efa49704f3">
													<SHORT-NAME>vBaseEnvFlexray_ClockRef</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Frequency of FlexRay peripheral clock - Mcu Reference</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Default McuClockReferencePoint reference for FlexRay peripheral clock setting.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="3030cf48-07ba-44f7-8c35-c4fca66077b5">
											<SHORT-NAME>vBaseEnvEthernet</SHORT-NAME>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<PARAMETERS>
												<ECUC-INTEGER-PARAM-DEF UUID="022860d8-b6b6-414d-9388-32ad6706b4a0">
													<SHORT-NAME>vBaseEnvEthernet_Clock</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Frequency of EtherNet peripheral clock</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Default frequency, used for PLL calculation of EtherNet driver peripheral clock.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">HZ</SD>
																<SD GID="DV:Unit">MHZ</SD>
															</SDG>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>18446744073709551615</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<ECUC-REFERENCE-DEF UUID="ab15e55b-2c59-4805-9ec4-6d763a18e4f4">
													<SHORT-NAME>vBaseEnvEthernet_ClockRef</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Frequency of EtherNet peripheral clock - Mcu Reference</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Default McuClockReferencePoint reference for EtherNet peripheral clock setting.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:variantMultiplicityPBL">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</SUB-CONTAINERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
					</CONTAINERS>
				</ECUC-MODULE-DEF>
			</ELEMENTS>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>