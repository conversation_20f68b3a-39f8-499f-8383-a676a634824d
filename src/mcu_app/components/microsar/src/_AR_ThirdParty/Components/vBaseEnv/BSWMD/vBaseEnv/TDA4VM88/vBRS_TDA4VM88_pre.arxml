<?xml version="1.0" encoding="UTF-8"?>
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://autosar.org/schema/r4.0">
	<AR-PACKAGES>
		<AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
			<SHORT-NAME>MICROSAR</SHORT-NAME>
			<ELEMENTS>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="86f7f4ab-ea96-4863-b164-ecf13339ac55">
					<SHORT-NAME>vBRS_Pre</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/vBRS</DEFINITION-REF>
					<CONTAINERS>
						<ECUC-CONTAINER-VALUE UUID="a0815f58-71bb-4653-9991-270d9f640cad">
							<SHORT-NAME>vBRSHwConfig</SHORT-NAME>
							<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vBRS/vBRSHwConfig</DEFINITION-REF>
							<PARAMETER-VALUES>
								<ECUC-TEXTUAL-PARAM-VALUE>
									<DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/MICROSAR/vBRS/vBRSHwConfig/vBRSPeriphClockDescription</DEFINITION-REF>
									<VALUE>SBL powers up the timers and clock sources. Timer: 250MHz, CAN: 80 MHz, LIN: 48MHz</VALUE>
								</ECUC-TEXTUAL-PARAM-VALUE>
							</PARAMETER-VALUES>
							<SUB-CONTAINERS>
								<ECUC-CONTAINER-VALUE UUID="f47dd5cf-c028-4209-9dd0-bc7bc5717c59">
									<SHORT-NAME>J721EVM</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Jacinto7 J721E/DRA8299/TDA4VM Evaluation Module</L-4>
									</LONG-NAME>
									<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vBRS/vBRSHwConfig/vBRSEvalBoardInfo</DEFINITION-REF>
									<PARAMETER-VALUES>
										<ECUC-TEXTUAL-PARAM-VALUE>
											<DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/MICROSAR/vBRS/vBRSHwConfig/vBRSEvalBoardInfo/vBRSEvalBoard</DEFINITION-REF>
											<VALUE>J721EVM</VALUE>
										</ECUC-TEXTUAL-PARAM-VALUE>
										<ECUC-TEXTUAL-PARAM-VALUE>
											<DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/MICROSAR/vBRS/vBRSHwConfig/vBRSEvalBoardInfo/vBRSEvalBoardDescription</DEFINITION-REF>
											<VALUE>No Led Support</VALUE>
										</ECUC-TEXTUAL-PARAM-VALUE>
										<ECUC-NUMERICAL-PARAM-VALUE>
											<DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/vBRS/vBRSHwConfig/vBRSEvalBoardInfo/vBRSEvalBoardOscClock</DEFINITION-REF>
											<VALUE>19200000</VALUE>
										</ECUC-NUMERICAL-PARAM-VALUE>
									</PARAMETER-VALUES>
								</ECUC-CONTAINER-VALUE>
							</SUB-CONTAINERS>
						</ECUC-CONTAINER-VALUE>
					</CONTAINERS>
				</ECUC-MODULE-CONFIGURATION-VALUES>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="1c868348-0eb8-4195-bbec-1be7fcc42112">
					<SHORT-NAME>vBRS_Rec</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/vBRS</DEFINITION-REF>
					<CONTAINERS>
						<ECUC-CONTAINER-VALUE UUID="0542c2bb-90ed-4734-b10f-bc0253ec4b71">
							<SHORT-NAME>vBRSHwConfig</SHORT-NAME>
							<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vBRS/vBRSHwConfig</DEFINITION-REF>
							<PARAMETER-VALUES>
								<ECUC-NUMERICAL-PARAM-VALUE>
									<DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/vBRS/vBRSHwConfig/vBRSOscClock</DEFINITION-REF>
									<VALUE>19200000</VALUE>
								</ECUC-NUMERICAL-PARAM-VALUE>
								<ECUC-NUMERICAL-PARAM-VALUE>
									<DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/vBRS/vBRSHwConfig/vBRSPeriphClock</DEFINITION-REF>
									<VALUE>250000000</VALUE>
								</ECUC-NUMERICAL-PARAM-VALUE>
								<ECUC-NUMERICAL-PARAM-VALUE>
									<DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/vBRS/vBRSHwConfig/vBRSTimebaseClock</DEFINITION-REF>
									<VALUE>1000000000</VALUE>
								</ECUC-NUMERICAL-PARAM-VALUE>
								<ECUC-TEXTUAL-PARAM-VALUE>
									<DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/vBRS/vBRSHwConfig/vBRSCompilerInstructionSet</DEFINITION-REF>
									<VALUE>ARM</VALUE>
								</ECUC-TEXTUAL-PARAM-VALUE>
								<ECUC-NUMERICAL-PARAM-VALUE>
									<DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/vBRS/vBRSHwConfig/vBRSCompilerEnableFPU</DEFINITION-REF>
									<VALUE>1</VALUE>
								</ECUC-NUMERICAL-PARAM-VALUE>
								<ECUC-TEXTUAL-PARAM-VALUE>
									<DEFINITION-REF DEST="ECUC-MULTILINE-STRING-PARAM-DEF">/MICROSAR/vBRS/vBRSHwConfig/vBRSMakefileUserCode</DEFINITION-REF>
									<VALUE>ADDITIONAL_INCLUDES += .</VALUE>
								</ECUC-TEXTUAL-PARAM-VALUE>
								<ECUC-TEXTUAL-PARAM-VALUE>
									<DEFINITION-REF DEST="ECUC-MULTILINE-STRING-PARAM-DEF">/MICROSAR/vBRS/vBRSHwConfig/vBRSMakefileUserCode</DEFINITION-REF>
									<VALUE>ADDITIONAL_LDFLAGS_FROM_VBRS = -i ti/drvlib</VALUE>
								</ECUC-TEXTUAL-PARAM-VALUE>
								<ECUC-TEXTUAL-PARAM-VALUE>
									<DEFINITION-REF DEST="ECUC-MULTILINE-STRING-PARAM-DEF">/MICROSAR/vBRS/vBRSHwConfig/vBRSMakefileUserCode</DEFINITION-REF>
									<VALUE>ADDITIONAL_LDFLAGS_FROM_VBRS += --library=ti.csl.aer5f</VALUE>
								</ECUC-TEXTUAL-PARAM-VALUE>
								<ECUC-TEXTUAL-PARAM-VALUE>
									<DEFINITION-REF DEST="ECUC-MULTILINE-STRING-PARAM-DEF">/MICROSAR/vBRS/vBRSHwConfig/vBRSMakefileUserCode</DEFINITION-REF>
									<VALUE>ADDITIONAL_LDFLAGS_FROM_VBRS += --library=sciclient.aer5f</VALUE>
								</ECUC-TEXTUAL-PARAM-VALUE>
								<ECUC-TEXTUAL-PARAM-VALUE>
									<DEFINITION-REF DEST="ECUC-MULTILINE-STRING-PARAM-DEF">/MICROSAR/vBRS/vBRSHwConfig/vBRSMakefileUserCode</DEFINITION-REF>
									<VALUE>ADDITIONAL_LDFLAGS_FROM_VBRS += --library=ti.osal.aer5f</VALUE>
								</ECUC-TEXTUAL-PARAM-VALUE>
								<ECUC-TEXTUAL-PARAM-VALUE>
									<DEFINITION-REF DEST="ECUC-MULTILINE-STRING-PARAM-DEF">/MICROSAR/vBRS/vBRSHwConfig/vBRSMakefileUserCode</DEFINITION-REF>
									<VALUE>ADDITIONAL_LDFLAGS_FROM_VBRS += --library=ti.board.aer5f</VALUE>
								</ECUC-TEXTUAL-PARAM-VALUE>
								<ECUC-TEXTUAL-PARAM-VALUE>
									<DEFINITION-REF DEST="ECUC-MULTILINE-STRING-PARAM-DEF">/MICROSAR/vBRS/vBRSHwConfig/vBRSMakefileUserCode</DEFINITION-REF>
									<VALUE>ADDITIONAL_LDFLAGS_FROM_VBRS += --library=ti.drv.uart.aer5f</VALUE>
								</ECUC-TEXTUAL-PARAM-VALUE>
								<ECUC-TEXTUAL-PARAM-VALUE>
									<DEFINITION-REF DEST="ECUC-MULTILINE-STRING-PARAM-DEF">/MICROSAR/vBRS/vBRSHwConfig/vBRSMakefileUserCode</DEFINITION-REF>
									<VALUE>ADDITIONAL_LDFLAGS_FROM_VBRS += --library=ti.drv.i2c.aer5f</VALUE>
								</ECUC-TEXTUAL-PARAM-VALUE>
								<ECUC-TEXTUAL-PARAM-VALUE>
									<DEFINITION-REF DEST="ECUC-MULTILINE-STRING-PARAM-DEF">/MICROSAR/vBRS/vBRSHwConfig/vBRSMakefileUserCode</DEFINITION-REF>
									<VALUE>ADDITIONAL_LDFLAGS_FROM_VBRS += --library=ti.csl.init.aer5f</VALUE>
								</ECUC-TEXTUAL-PARAM-VALUE>
							</PARAMETER-VALUES>
						</ECUC-CONTAINER-VALUE>
					</CONTAINERS>
				</ECUC-MODULE-CONFIGURATION-VALUES>
			</ELEMENTS>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>