<?xml version="1.0" encoding="UTF-8"?>
<!--
***********************************************************************************************************************
COPYRIGHT
===============================================================================
Copyright (c) 2020 by Vector Informatik GmbH.                                         All rights reserved.

    This software is copyright protected and proprietary to Vector Informatik GmbH.
    Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
    All other rights remain with Vector Informatik GmbH.
===============================================================================
FILE DESCRIPTION
===============================================================================
File:           Xlock_bswmd.arxml
Component:      Cdd_AsrXlock
Module:         Xlock
Generator:      Vector DaVinci Configurator Pro
Description:    -
*********************************************************************************************************************** 
-->
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://autosar.org/schema/r4.0">
	<AR-PACKAGES>
		<AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
			<SHORT-NAME>MICROSAR</SHORT-NAME>
			<ELEMENTS>
				<BSW-IMPLEMENTATION UUID="cb8fa347-5047-4694-8cfd-aaad752762c7">
					<SHORT-NAME>Xlock_Impl</SHORT-NAME>
					<PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
					<SW-VERSION>5.00.00</SW-VERSION>
					<USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
					<VENDOR-ID>30</VENDOR-ID>
					<AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
					<BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/Xlock_ib_bswmd/BswModuleDescriptions/Xlock/XlockBehavior</BEHAVIOR-REF>
					<PRECONFIGURED-CONFIGURATION-REFS>
						<PRECONFIGURED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/Xlock_Pre</PRECONFIGURED-CONFIGURATION-REF>
					</PRECONFIGURED-CONFIGURATION-REFS>
					<RECOMMENDED-CONFIGURATION-REFS>
						<RECOMMENDED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/Xlock_Rec</RECOMMENDED-CONFIGURATION-REF>
					</RECOMMENDED-CONFIGURATION-REFS>
					<VENDOR-SPECIFIC-MODULE-DEF-REFS>
						<VENDOR-SPECIFIC-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/MICROSAR/Xlock</VENDOR-SPECIFIC-MODULE-DEF-REF>
					</VENDOR-SPECIFIC-MODULE-DEF-REFS>
				</BSW-IMPLEMENTATION>
				<ECUC-MODULE-DEF UUID="5944e285-272f-4feb-acad-cf94d99c2cd1">
					<SHORT-NAME>Xlock</SHORT-NAME>
					<DESC>
						<L-2 L="EN">Confguration of the Xlock (cross locking) module.</L-2>
					</DESC>
					<CATEGORY>VENDOR_SPECIFIC_MODULE_DEFINITION</CATEGORY>
					<ADMIN-DATA>
						<DOC-REVISIONS>
							<DOC-REVISION>
								<REVISION-LABEL>1.00.00</REVISION-LABEL>
								<STATE>release</STATE>
								<ISSUED-BY>visade</ISSUED-BY>
								<DATE>2014-06-26T02:31:50+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Initial creation</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">-</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.01.00</REVISION-LABEL>
								<STATE>release</STATE>
								<ISSUED-BY>virrro</ISSUED-BY>
								<DATE>2015-01-14T10:26:28+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Update for ASR4 usage</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00080550, ESCAN00080551</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>2.00.00</REVISION-LABEL>
								<STATE>release</STATE>
								<ISSUED-BY>virrro</ISSUED-BY>
								<DATE>2015-08-04T12:45:53+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Update for ASR4 usage</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00082711</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>3.00.00</REVISION-LABEL>
								<STATE>release</STATE>
								<ISSUED-BY>virrro</ISSUED-BY>
								<DATE>2015-10-13T07:25:02+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Update for R14</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00084987;'ESCAN00087609</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>4.00.00</REVISION-LABEL>
								<STATE>release</STATE>
								<ISSUED-BY>virrro</ISSUED-BY>
								<DATE>2017-06-16T10:03:04+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Updated DiagnosticMonitor interface to ASR 4.3.0</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">-</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>5.00.00</REVISION-LABEL>
								<STATE>release</STATE>
								<ISSUED-BY>vsarcmimo</ISSUED-BY>
								<DATE>2020-05-20T08:03:54+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Added multi-partition/core support</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">CARC-326</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
						</DOC-REVISIONS>
					</ADMIN-DATA>
					<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
					<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
					<SUPPORTED-CONFIG-VARIANTS>
						<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
					</SUPPORTED-CONFIG-VARIANTS>
					<CONTAINERS>
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="35f06ded-c489-4cad-bf74-1e2ebcb4f1f8">
							<SHORT-NAME>XlockConfigSet</SHORT-NAME>
							<DESC>
								<L-2 L="EN">This container contains the configuration parameters and sub containers of the Xlock module.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
							<SUB-CONTAINERS>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="6ffa9263-e191-42bd-86cf-a1339fbfba06">
									<SHORT-NAME>XlockCrossLockConfig</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container containing the cross lock configuration of a Dem event.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<ECUC-BOOLEAN-PARAM-DEF UUID="033d359b-e009-4352-a723-09cba11441bc">
											<SHORT-NAME>XlockCrossLockImported</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Indicates whether the configuration of this container has been imported or not.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="46c59df6-8f2f-4cad-9cdf-cdba3e8c0fc8">
											<SHORT-NAME>XlockCrossLockMaskLastFailed</SHORT-NAME>
											<DESC>
												<L-2 L="EN">If enabled, the inhibition mask "FIM_LAST_FAILED" is generated for each locking event.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="2de53164-2a5e-41ef-8869-1003744d2eee">
											<SHORT-NAME>XlockCrossLockMaskNotTested</SHORT-NAME>
											<DESC>
												<L-2 L="EN">If enabled, the inhibition mask "FIM_NOT_TESTED" is generated for each locking event.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="84dd8ae2-a717-4ce2-ac3c-86b5159c9011">
											<SHORT-NAME>XlockCrossLockMaskTested</SHORT-NAME>
											<DESC>
												<L-2 L="EN">If enabled, the inhibition mask "FIM_TESTED" is generated for each locking event.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="2a41182c-154b-48a7-800a-f9022c89cd82">
											<SHORT-NAME>XlockCrossLockMaskTestedAndFailed</SHORT-NAME>
											<DESC>
												<L-2 L="EN">If enabled, the inhibition mask "FIM_TESTED_AND_FAILED" is generated for each locking event.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="bd8ee009-d7e3-45cd-a0d8-8877befe19eb">
											<SHORT-NAME>XlockCrossLockedEvent</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The event for which the setting of the event status will be prevented (the locked event).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="899e1f57-9a65-4af7-8604-c1be4d4d7fc8">
											<SHORT-NAME>XlockCrossLockingEvent</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The event which prevents the setting of the event status of the locked event.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="4d81767d-ef3b-472b-8e8f-cd7ef80eb757">
											<SHORT-NAME>XlockCrossLockingEventGroup</SHORT-NAME>
											<DESC>
												<L-2 L="EN">The event group whose events prevent the setting of the event status of the locked event.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Xlock/XlockConfigSet/XlockEventGroup</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="acd00dee-90ae-4817-b7a8-6b8fad56f788">
									<SHORT-NAME>XlockFunctionLockingPort</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container represents a RTE sender/receiver port of the function locking application component.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<SUB-CONTAINERS>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="4c492221-4725-4c0a-a601-96ab84b97229">
											<SHORT-NAME>XlockFunctionLockingPortElement</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container represents a RTE sender/receiver port element of the parent function locking port.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<PARAMETERS>
												<ECUC-INTEGER-PARAM-DEF UUID="7e546d03-0b50-412d-bdbd-6df40b99b7c5">
													<SHORT-NAME>XlockFunctionLockingPortDefaultValue</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter specifies the default value which is used for the port element in the RTE communication specification.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<SUB-CONTAINERS>
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="8ada368a-1f81-43d9-a63a-0e5433d2b254">
													<SHORT-NAME>XlockFunctionLockingBit</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container represents one bit in the parent sender/receiver port element.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<PARAMETERS>
														<ECUC-INTEGER-PARAM-DEF UUID="ab98c208-9c28-4956-af7c-71cf908ffe20">
															<SHORT-NAME>XlockFunctionLockingBitPosition</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter sets the position of the current bit within the RTE port element.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>0</DEFAULT-VALUE>
															<MAX>31</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<ECUC-BOOLEAN-PARAM-DEF UUID="f03eba6e-9593-4688-b6ed-390f9b501a01">
															<SHORT-NAME>XlockFunctionLockingImported</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Indicates whether the configuration of this container has been imported or not.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<ECUC-BOOLEAN-PARAM-DEF UUID="7c53236b-316f-41d3-b318-3af59605d729">
															<SHORT-NAME>XlockFunctionLockingMaskLastFailed</SHORT-NAME>
															<DESC>
																<L-2 L="EN">If enabled, the inhibition mask "FIM_LAST_FAILED" is generated for each locking event.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>true</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<ECUC-BOOLEAN-PARAM-DEF UUID="37ce224b-acb1-4626-8281-2863e5a212b7">
															<SHORT-NAME>XlockFunctionLockingMaskNotTested</SHORT-NAME>
															<DESC>
																<L-2 L="EN">If enabled, the inhibition mask "FIM_NOT_TESTED" is generated for each locking event.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<ECUC-BOOLEAN-PARAM-DEF UUID="6fa8c041-318e-462d-bd30-1521f290ee5d">
															<SHORT-NAME>XlockFunctionLockingMaskTested</SHORT-NAME>
															<DESC>
																<L-2 L="EN">If enabled, the inhibition mask "FIM_TESTED" is generated for each locking event.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<ECUC-BOOLEAN-PARAM-DEF UUID="5a1b1ea9-2e17-4ea0-a5af-bfdcd15ccf8c">
															<SHORT-NAME>XlockFunctionLockingMaskTestedAndFailed</SHORT-NAME>
															<DESC>
																<L-2 L="EN">If enabled, the inhibition mask "FIM_TESTED_AND_FAILED" is generated for each locking event.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="4b23a40f-b9bd-44d0-adc8-4a625e18c4fb">
															<SHORT-NAME>XlockFunctionLockingEvent</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter sets the event(s) which cause the current bit in the RTE port element to be locked.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="eaeffd20-875a-41e6-9009-10454a103d80">
															<SHORT-NAME>XlockFunctionLockingEventGroup</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter sets the event group(s) whose event(s) cause the current bit in the RTE port element to be locked.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Xlock/XlockConfigSet/XlockEventGroup</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="1b866e70-b09a-4e6b-a159-c5903026958b">
									<SHORT-NAME>XlockErrorReportingPort</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container represents a RTE sender/receiver port of the error reporting application component.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<SUB-CONTAINERS>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="fe6ab544-3af9-4307-bcc2-800c1f039957">
											<SHORT-NAME>XlockErrorReportingPortElement</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container represents a RTE sender/receiver port element of the parent error reporting port.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<PARAMETERS>
												<ECUC-INTEGER-PARAM-DEF UUID="b432851a-85bf-4d62-bdfc-22aef50254a4">
													<SHORT-NAME>XlockErrorReportingPortDefaultValue</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter specifies the default value which is used for the port element in the RTE communication specification.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<SUB-CONTAINERS>
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="f6853fa0-bdf6-4633-99ca-ab55d3ec526c">
													<SHORT-NAME>XlockErrorReportingBit</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container represents one bit in the parent sender/receiver port element.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>32</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<PARAMETERS>
														<ECUC-INTEGER-PARAM-DEF UUID="3f561b6f-3b04-43f5-9d2c-212d7f2ff202">
															<SHORT-NAME>XlockErrorReportingBitPosition</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter sets the position of the current bit within the RTE port element.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>0</DEFAULT-VALUE>
															<MAX>31</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<ECUC-BOOLEAN-PARAM-DEF UUID="fb50100c-df68-47e1-87be-4b655fb0f208">
															<SHORT-NAME>XlockErrorReportingImported</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Indicates whether the configuration of this container has been imported or not.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<ECUC-BOOLEAN-PARAM-DEF UUID="e018244e-0e17-4578-8ddd-f3aed2537d11">
															<SHORT-NAME>XlockErrorReportingMaskLastFailed</SHORT-NAME>
															<DESC>
																<L-2 L="EN">If enabled, the inhibition mask "FIM_LAST_FAILED" is generated for each locking event.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>true</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<ECUC-BOOLEAN-PARAM-DEF UUID="ab32ec54-009c-4b0f-8a29-f0b8a96d46ae">
															<SHORT-NAME>XlockErrorReportingMaskNotTested</SHORT-NAME>
															<DESC>
																<L-2 L="EN">If enabled, the inhibition mask "FIM_NOT_TESTED" is generated for each locking event.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>true</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<ECUC-BOOLEAN-PARAM-DEF UUID="ba4f2619-df77-49de-9eb2-a1355f58a111">
															<SHORT-NAME>XlockErrorReportingMaskTested</SHORT-NAME>
															<DESC>
																<L-2 L="EN">If enabled, the inhibition mask "FIM_TESTED" is generated for each locking event.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<ECUC-BOOLEAN-PARAM-DEF UUID="0214c3c9-acad-4f96-bf0c-d067bb23f650">
															<SHORT-NAME>XlockErrorReportingMaskTestedAndFailed</SHORT-NAME>
															<DESC>
																<L-2 L="EN">If enabled, the inhibition mask "FIM_TESTED_AND_FAILED" is generated for each locking event.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="772a1a7f-0585-44be-9fb8-8cb3af3ad23b">
															<SHORT-NAME>XlockErrorReportingEvent</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter sets the event(s) which cause the current bit in the RTE port element to be locked.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="be773b02-651d-4174-bcb4-d8effcc89c99">
															<SHORT-NAME>XlockErrorReportingEventGroup</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter sets the event group(s) whose event(s) cause the current bit in the RTE port element to be locked.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Xlock/XlockConfigSet/XlockEventGroup</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="980e612b-8050-4e79-8460-0dc60ef74324">
											<SHORT-NAME>XlockClearDtcInfoPortElement</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Port element which is used to inform the application about a Mode $04 request.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<PARAMETERS>
												<ECUC-ENUMERATION-PARAM-DEF UUID="3f0bed5c-5b5f-4b27-be44-83c683433938">
													<SHORT-NAME>XlockClearDtcInfoPortDefaultValue</SHORT-NAME>
													<DESC>
														<L-2 L="EN">The default value for the port element</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>FALSE</DEFAULT-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="a1c1ebb4-56d1-4e54-ad83-9f49c016f88f">
															<SHORT-NAME>TRUE</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">TRUE</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="4e3672d2-1305-4608-a712-7eb55529034c">
															<SHORT-NAME>FALSE</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">FALSE		</L-4>
															</LONG-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ea8c32d0-660f-4497-bf9a-8833e5097432">
									<SHORT-NAME>XlockEventGroup</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container to group multiple events in a group.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">true</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
									<REFERENCES>
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="47bd9686-09d2-430d-adef-c39d3ff03ee0">
											<SHORT-NAME>XlockEventGroupEvent</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter specifies the event(s) belonging to this event group.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ae388711-ed77-44e3-8520-64e497dd6d5a">
									<SHORT-NAME>XlockSatellite</SHORT-NAME>
									<DESC>
										<L-2 L="EN">List of Xlock satellites (partitions).</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<REFERENCES>
										<ECUC-REFERENCE-DEF UUID="61ec23a8-3ecd-4f3d-901f-828b2bd2262f">
											<SHORT-NAME>XlockOsApplicationRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to the partition this satellite is assigned to.

Xlock directly supports multi partition architectures, i.e. ECUs using multiple cores or memory protection for safety reasons.
See the TechnicalReference for details.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsApplication</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</SUB-CONTAINERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="5253681e-b61a-419a-af30-634e6035c686">
							<SHORT-NAME>XlockGeneral</SHORT-NAME>
							<DESC>
								<L-2 L="EN">This container contains the configuration (parameters) for Component wide parameters</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
							<PARAMETERS>
								<ECUC-BOOLEAN-PARAM-DEF UUID="5c6ca21e-6eee-4b5a-8464-5f36a6864320">
									<SHORT-NAME>XlockImportCrossLockMatrix</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Specifies whether the cross lock matrix will be imported or not.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-STRING-PARAM-DEF UUID="4e1d8563-aaeb-4ca7-b3d6-7aac8d10bdc5">
									<SHORT-NAME>XlockCrossLockMatrixFilename</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Specifies the file name from which the cross locking configuration will be imported</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<ECUC-STRING-PARAM-DEF-VARIANTS>
										<ECUC-STRING-PARAM-DEF-CONDITIONAL>
											<DEFAULT-VALUE>QVM.csv</DEFAULT-VALUE>
										</ECUC-STRING-PARAM-DEF-CONDITIONAL>
									</ECUC-STRING-PARAM-DEF-VARIANTS>
								</ECUC-STRING-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="44f183de-e30f-4b70-94eb-b5fc2bdf4d43">
									<SHORT-NAME>XlockImportFunctionLockingMatrix</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Specifies whether the function lock matrix will be imported or not.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-STRING-PARAM-DEF UUID="001794bc-6d04-433b-a6ba-5e8278d66f14">
									<SHORT-NAME>XlockFunctionLockingMatrixFilename</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Specifies the file name from which the function locking configuration will be imported</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<ECUC-STRING-PARAM-DEF-VARIANTS>
										<ECUC-STRING-PARAM-DEF-CONDITIONAL>
											<DEFAULT-VALUE>FVM.csv</DEFAULT-VALUE>
										</ECUC-STRING-PARAM-DEF-CONDITIONAL>
									</ECUC-STRING-PARAM-DEF-VARIANTS>
								</ECUC-STRING-PARAM-DEF>
								<ECUC-STRING-PARAM-DEF UUID="3fc17fc8-9574-4f82-8639-44197c15aea5">
									<SHORT-NAME>XlockCsvDelimiter</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The delimiter used for importing CSV files.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<ECUC-STRING-PARAM-DEF-VARIANTS>
										<ECUC-STRING-PARAM-DEF-CONDITIONAL>
											<DEFAULT-VALUE>,</DEFAULT-VALUE>
										</ECUC-STRING-PARAM-DEF-CONDITIONAL>
									</ECUC-STRING-PARAM-DEF-VARIANTS>
								</ECUC-STRING-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="4c3c4145-6f0f-45b4-8f5d-fb0d335f7f88">
									<SHORT-NAME>XlockGenerateEventMappingXml</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Specifies if a XML document with the mapping between UDS DTCs and DEM event IDs will be generated or not</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="173f7032-b58b-4ff7-811e-75c54fe2812d">
									<SHORT-NAME>XlockImportErrorReportingMatrix</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Specifies whether the error reporting matrix will be imported or not.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-STRING-PARAM-DEF UUID="14aa1247-a3c1-42bc-bd1e-9c61e1aab716">
									<SHORT-NAME>XlockErrorReportingFilename</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Specifies the file name from which the error reporting configuration will be imported</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<ECUC-STRING-PARAM-DEF-VARIANTS>
										<ECUC-STRING-PARAM-DEF-CONDITIONAL>
											<DEFAULT-VALUE>ErrorReporting.csv</DEFAULT-VALUE>
										</ECUC-STRING-PARAM-DEF-CONDITIONAL>
									</ECUC-STRING-PARAM-DEF-VARIANTS>
								</ECUC-STRING-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="303825ac-b650-4803-8dc1-08a35734add8">
									<SHORT-NAME>XlockUseRte</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Configuration for usage of RTE.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="0c0bab91-2fde-425b-97a3-d6145a4396dc">
									<SHORT-NAME>XlockCanDisableFunctionLocking</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Allows disabling of the function lock procedure by setting the variable Xlock_DisableFunctionLocking to 0.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="524b231e-02a9-457d-9b9c-4761113b3a70">
									<SHORT-NAME>XlockCanDisableErrorReporting</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Allows disabling of the error reporting procedure by setting the variable Xlock_DisableErrorReporting to 0.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-INTEGER-PARAM-DEF UUID="cd690dd5-4996-47da-b11c-a66198c7819f">
									<SHORT-NAME>XlockFunctionLockingCycleTime</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The call cycle of the Xlock FunctionLock MainFunction.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>100</DEFAULT-VALUE>
									<MAX>65535</MAX>
									<MIN>1</MIN>
								</ECUC-INTEGER-PARAM-DEF>
								<ECUC-INTEGER-PARAM-DEF UUID="f44dc9eb-1611-4c53-a422-0f588111e5b2">
									<SHORT-NAME>XlockErrorReportingCycleTime</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The call cycle of the Xlock ErrorReporting MainFunction.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>100</DEFAULT-VALUE>
									<MAX>65535</MAX>
									<MIN>1</MIN>
								</ECUC-INTEGER-PARAM-DEF>
								<ECUC-ENUMERATION-PARAM-DEF UUID="bf0d4383-7884-4c7e-b575-2697b1e8f76d">
									<SHORT-NAME>XlockApplicationComponentDescriptionVersion</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Specifies the format of the application component descriptions for Error Reporting and Function Locking.
XLOCK_SWC_ASR3 will generate application component descriptions in AUTOSAR 3.2.1 format.
XLOCK_SWC_ASR4 will generate application component descriptions in AUTOSAR 4.0.3 format.
</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>XLOCK_SWC_ASR4</DEFAULT-VALUE>
									<LITERALS>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="63b7144b-b056-4d08-9746-58a50b2e6364">
											<SHORT-NAME>XLOCK_SWC_ASR3</SHORT-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="b4b1f97e-6e51-4640-a28b-e4b854ae03fc">
											<SHORT-NAME>XLOCK_SWC_ASR4</SHORT-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
									</LITERALS>
								</ECUC-ENUMERATION-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="266f51d4-2734-42d8-bef4-aa2fa6b8be47">
									<SHORT-NAME>XlockVersionInfoApi</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter enables/disables the function Xlock_GetVersionInfo() to get major, minor and patch version information.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="e608e0f2-df61-4421-9444-779c842bf017">
									<SHORT-NAME>XlockDevErrorDetect</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter switches the Development Error Detection and Notification ON or OFF.
true:	Development error detection is enabled.
false:	Development error detection is disabled.

Note: In general, the development error detection is recommended during pre-test phase. It is not recommended to enable the development error detection in production code due to increased runtime and ROM needs.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
							</PARAMETERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
					</CONTAINERS>
				</ECUC-MODULE-DEF>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="4761aca1-4776-40f8-aad2-2d6d39e927d6">
					<SHORT-NAME>Xlock_Pre</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/Xlock</DEFINITION-REF>
					<CONTAINERS></CONTAINERS>
				</ECUC-MODULE-CONFIGURATION-VALUES>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="26ffe14e-e3b8-4eb2-862d-331d4be2944d">
					<SHORT-NAME>Xlock_Rec</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/Xlock</DEFINITION-REF>
					<CONTAINERS></CONTAINERS>
				</ECUC-MODULE-CONFIGURATION-VALUES>
			</ELEMENTS>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>