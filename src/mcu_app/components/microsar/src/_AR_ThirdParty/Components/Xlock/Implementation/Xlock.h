/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *  Copyright (c) 2020 by Vector Informatik GmbH.                                              All rights reserved.
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -----------------------------------------------------------------------------------------------------------------*/
/*!        \file  
 *        \brief  Header for Autosar CDD module Xlock
 *      \details  Static header file for Xlock. May not be changed by customer / generator
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  AUTHOR IDENTITY
 *  -------------------------------------------------------------------------------------------------------------------
 *  Name                          Initials      Company
 *  -------------------------------------------------------------------------------------------------------------------
 *  Mikael Molander               vsarcmimo     Vector Informatik GmbH
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  REVISION HISTORY
 *  -------------------------------------------------------------------------------------------------------------------
 *  Version   Date        Author      Change Id     Description
 *  -------------------------------------------------------------------------------------------------------------------
 *  5.0.0     2020-05-28  vsarcmimo   CARC-330      Xlock - Lift documentation/dev process to PES PR 3.0
 *                                    CARC-326      Xlock - Implement multi-partition suppport
 *********************************************************************************************************************/

#if !defined (XLOCK_H)
# define XLOCK_H

/**********************************************************************************************************************
 *  INCLUDES
 *********************************************************************************************************************/
# include "Xlock_Cfg.h"
# include "Dem.h"
# include "FiM.h"

/**********************************************************************************************************************
 *  GLOBAL CONSTANT MACROS
 *********************************************************************************************************************/

/* Vendor and module identification */
# define XLOCK_VENDOR_ID                           (30u)
# define XLOCK_MODULE_ID                           (255u)

/* AUTOSAR Software specification version information */
# define XLOCK_AR_RELEASE_MAJOR_VERSION            (4u)
# define XLOCK_AR_RELEASE_MINOR_VERSION            (3u)
# define XLOCK_AR_RELEASE_REVISION_VERSION         (0u)

/* ----- Component version information (decimal version of ALM implementation package) ----- */
# define XLOCK_SW_MAJOR_VERSION                    (5u)
# define XLOCK_SW_MINOR_VERSION                    (0u)
# define XLOCK_SW_PATCH_VERSION                    (0u)

# define XLOCK_INSTANCE_ID_DET                     (255u)

/* ----- API service IDs ----- */
# define XLOCK_SID_INIT                            (0x00u) /*!< Service ID: Xlock_Init() */
# define XLOCK_SID_SET_EVENT_STATUS                (0x01u) /*!< Service ID: Xlock_SetEventStatus() */
# define XLOCK_SID_GET_VERSION_INFO                (0x10u) /*!< Service ID: Xlock_GetVersionInfo() */

/* ----- Error codes ----- */
# define XLOCK_E_NO_ERROR                          (0x00u) /*!< used to check if no error occurred - use a value unequal to any error code */
# define XLOCK_E_PARAM_CONFIG                      (0x0Au) /*!< Error code: API service Xlock_Init() called with wrong parameter */
# define XLOCK_E_PARAM_POINTER                     (0x0Cu) /*!< Error code: API service used with invalid pointer parameter (NULL) */
# define XLOCK_E_UNINIT                            (0x10u) /*!< Error code: API service used without module initialization */
# define XLOCK_E_ALREADY_INITIALIZED               (0x11u) /*!< Error code: The service Xlock_Init() is called while the module is already initialized  */

/* ----- Modes ----- */
# define XLOCK_UNINIT                              (0x00u)
# define XLOCK_INIT                                (0x01u)

/**********************************************************************************************************************
 *  GLOBAL FUNCTION MACROS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL DATA TYPES AND STRUCTURES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL DATA PROTOTYPES
 *********************************************************************************************************************/
# define XLOCK_START_SEC_VAR_NO_INIT_8
# include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_19.1 */

 /*! Global variable to disable the SetEventStatus implementation */
extern VAR(boolean, XLOCK_VAR_NOINIT) Xlock_SetEventStatusEnabled;

/*! Global variable to disable Cross Locking in the SetEventStatus implementation */
extern VAR(boolean, XLOCK_VAR_NOINIT) Xlock_SetEventStatusCrossLockingEnabled;

/*! Global variable to disable the ReportErrorStatus implementation */
extern VAR(boolean, XLOCK_VAR_NOINIT) Xlock_ReportErrorStatusEnabled;

/*! Global variable to disable Cross Locking in the ReportErrorStatus implementation */
extern VAR(boolean, XLOCK_VAR_NOINIT) Xlock_ReportErrorStatusCrossLockingEnabled;

# define XLOCK_STOP_SEC_VAR_NO_INIT_8
# include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_19.1 */

/**********************************************************************************************************************
 *  GLOBAL FUNCTION PROTOTYPES
 *********************************************************************************************************************/
# define XLOCK_START_SEC_CODE
# include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


/**********************************************************************************************************************
 *  Xlock_InitMemory()
 *********************************************************************************************************************/
/*! \brief       Initialization for *_INIT_*-variables
 *  \details     Service to initialize module global variables at power up. This function initializes the
 *               variables in *_INIT_* sections. Used in case they are not initialized by the startup code.
 *  \pre         Module is uninitialized.
 *  \context     TASK
 *  \reentrant   FALSE
 *  \synchronous TRUE
 *  \trace CREQ-191448
 *********************************************************************************************************************/
FUNC(void, XLOCK_CODE) Xlock_InitMemory(void);


/**********************************************************************************************************************
 * Xlock_Init()
 *********************************************************************************************************************/
/*! \brief       Initialization function
 *  \details     Service to initialize the module Xlock. It sets the module state to initialized.
 *  \pre         Interrupts are disabled.
 *               Module is uninitialized.
 *               Xlock_InitMemory has been called unless Xlock_ModuleInitialized is initialized by start-up code.
 *  \context     TASK
 *  \reentrant   FALSE
 *  \synchronous TRUE
 *  \note        Specification of module initialization
 *  \trace CREQ-191448
 *********************************************************************************************************************/
FUNC(void, XLOCK_CODE) Xlock_Init(void);


# if (XLOCK_VERSION_INFO_API == STD_ON)
/**********************************************************************************************************************
*  Xlock_GetVersionInfo()
*********************************************************************************************************************/
/*! \brief      Returns the version information
*  \details     Xlock_GetVersionInfo() returns version information, vendor ID and AUTOSAR module ID of the component.
*  \param[out]  Versioninfo             Pointer to where to store the version information. Parameter must not be NULL.
*  \pre         -
*  \context     TASK|ISR2
*  \reentrant   TRUE
*  \synchronous TRUE
*  \trace CREQ-191450
*********************************************************************************************************************/
FUNC(void, XLOCK_CODE) Xlock_GetVersionInfo(P2VAR(Std_VersionInfoType, AUTOMATIC, XLOCK_DATA) Versioninfo);
# endif


/**********************************************************************************************************************
*  Xlock_SetEventStatus
**********************************************************************************************************************/
/*!\brief                        SWC API (AUTOSAR 4.x) to report a diagnostic monitor result with cross locking.
*
*  \details                      The call is forwarded to Dem_SetEventstatus if no cross locking configuration
*                                is inhibiting the specific event.
*
* \attention                     The operations Xlock_SetEventStatus(),
*                                Xlock_ResetEventStatus(), Xlock_ReportErrorStatus() and
*                                Xlock_ResetEventDebounceStatus() are not reentrant
*                                for the same EventId.
*
*  \param[in] EventId            Event id to report to Dem, must match a FiM Function Id.
*  \param[in] EventStatus        Event status to report to Dem.
*
*  \return                       E_NOT_OK - Result could not be reported to Dem
*                                E_OK - Success, result reported to Dem or cross-locking functionality is disabled.
*
*  \pre                          -
*  \context                      TASK|ISR2, from the respective satellite partition only
*  \reentrant                    TRUE for different EventIds.
*  \synchronous                  TRUE
*  \trace CREQ-241658
**********************************************************************************************************************/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_SetEventStatus(Dem_EventIdType EventId, Dem_EventStatusType EventStatus);


/**********************************************************************************************************************
*  Xlock_ReportErrorStatus
**********************************************************************************************************************/
/*!\brief                        SWC API (AUTOSAR 3.x) to report a diagnostic monitor result with cross locking.
*
*  \details                      The call is forwarded to Dem_SetEventstatus if no cross locking configuration
*                                is inhibiting the specific event.
*
* \attention                     The operations Xlock_SetEventStatus(),
*                                Xlock_ResetEventStatus(), Xlock_ReportErrorStatus() and
*                                Xlock_ResetEventDebounceStatus() are not reentrant
*                                for the same EventId.
*
*  \param[in] EventId            Event id to report to Dem, must match a FiM Function Id.
*  \param[in] EventStatus        Event status to report to Dem.
*
*  \return                       E_NOT_OK - Result could not be reported to Dem
*                                E_OK - Success, result reported to Dem or cross-locking functionality is disabled.
*
*  \pre                          -
*  \context                      TASK|ISR2, from the respective satellite partition only
*  \reentrant                    TRUE for different EventIds.
*  \synchronous                  TRUE
*  \trace CREQ-241658
**********************************************************************************************************************/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_ReportErrorStatus(Dem_EventIdType EventId, Dem_EventStatusType EventStatus);


# if (XLOCK_ERROR_REPORTING_ENABLED == STD_ON)
/**********************************************************************************************************************
*  Xlock_ErrorReporting_MainFunction
**********************************************************************************************************************
*! \brief                        Cyclic MainFunction for the Error Reporting functionality.
*
*  \details                      Updates the function permission status of the configured ErrorReporting ports
*                                by iterating over the ports and calling the FiM for each bit position.
*  \pre                          -
*  \context                      TASK|ISR2, any partition
*  \reentrant                    TRUE
*  \synchronous                  TRUE
*  \trace CREQ-241937
*********************************************************************************************************************/
/* FUNC(void, XLOCK_CODE) Xlock_ErrorReporting_MainFunction(void); */
# endif


# if (XLOCK_FUNCTION_LOCKING_ENABLED == STD_ON)
/**********************************************************************************************************************
*  Xlock_FunctionLocking_MainFunction
**********************************************************************************************************************/
/*!\brief                        Cyclic MainFunction for the Function Locking functionality.
*
*  \details                      Updates the function permission status of the configured FunctionLocking ports
*                                by iterating over the ports and calling the FiM for each bit position.
*  \pre                          -
*  \context                      TASK|ISR2, any partition
*  \reentrant                    TRUE
*  \synchronous                  TRUE
*  \trace CREQ-241938
*********************************************************************************************************************/
/* FUNC(void, XLOCK_CODE) Xlock_FunctionLocking_MainFunction(void); */
# endif

# if (XLOCK_ERROR_REPORTING_ENABLED == STD_ON || XLOCK_FUNCTION_LOCKING_ENABLED == STD_ON)
/**********************************************************************************************************************
*  Xlock_UpdateFunctionPermission()
*********************************************************************************************************************/
/*! \brief                       Internal helper function for ErrorReporting and FunctionLocking.
*
*  \details                      Implements common functionality between ErrorReporting and FunctionLocking
*
*  \param[in] portTable          Xlock_ErrorReportingPort* Array of port definitions
*  \param[in] noOfPorts          uint32 Number of port elements in array
*
*  \pre                          -
*  \context                      TASK|ISR2, any partition
*  \reentrant                    TRUE for different EventIds.
*  \synchronous                  TRUE
*  \trace DSGN-Xlock22929
**********************************************************************************************************************/
FUNC(void, XLOCK_CODE) Xlock_UpdateFunctionPermission(P2CONST(Xlock_ErrorReportingPort, AUTOMATIC, XLOCK_DATA) portTable,
                                                      uint32 noOfPorts);
# endif


/**********************************************************************************************************************
*  FORWARDED FUNCTION CALLS
*********************************************************************************************************************/

/**********************************************************************************************************************
*  Xlock_GetDTCOfEvent
**********************************************************************************************************************/
/*!\note         Xlock SWC API which is forwarded to Dem.
*
* \brief         API to read the DTC number for the given EventId
*
* \details       This function retrieves the configured DTC number of an event
*                from the configuration. Since the configuration is not known
*                until full initialization, this API cannot be called prior
*                to Dem_Init().
*
*                Xlock_GetDTCOfEvent() is part of the RTE interface, but
*                but may be called directly as well.

* \param[in]     EventId
*                Unique handle of the Event
* \param[in]     DTCFormat
*                DEM_DTC_FORMAT_OBD or DEM_DTC_FORMAT_UDS to select which
*                DTC number is requested
* \param[out]    DTCOfEvent
*                Pointer receiving the DTC number

* \return        E_OK
*                The request was successful, DTCOfEvent now contains the DTC
*                value.
* \return        DEM_E_NO_DTC_AVAILABLE
*                The event has no DTC in the requested format.
* \return        E_NOT_OK
*                The request was rejected, e.g. due to variant coding (see
*                Dem_SetEventAvailable() )
*
* \pre           -
*
* \context       TASK|ISR2
* \synchronous   TRUE
* \reentrant     TRUE
* \trace         DSGN-Xlock22908
*********************************************************************************************************************/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_GetDTCOfEvent(
  Dem_EventIdType EventId,
  Dem_DTCFormatType DTCFormat,
  P2VAR(uint32, AUTOMATIC, XLOCK_DATA) DTCOfEvent);


/**********************************************************************************************************************
*  Xlock_GetEventExtendedDataRecordEx
**********************************************************************************************************************/
/*!\note         Xlock SWC API which is forwarded to Dem
*
* \brief         SWC API to read the extended record data of an event.
*
* \details       This API is intended to be called via RTE_CALL interface,
*                as Operation of port interface 'DiagnosticInfo'.
*
*                Xlock_GetEventExtendedDataRecordEx() copies extended data
*                available or stored for an event into the passed buffer.
*                This is particularly useful to retrieve statistic data, like
*                occurrence counter or aging counter - if these are mapped to
*                an extended data record.
*
* \param[in]     EventId
*                Unique handle of the Event.
* \param[in]     RecordNumber
*                Extended DataRecord number to read. The record numbers 0xfe
*                and 0xff are reserved.
* \param[out]    DestBuffer
*                Buffer to receive the data.
* \param[in,out] BufSize
*                In:  Size of the buffer.
*                Out: Number of bytes written.
*
* \return        E_OK
*                The requested data was copied to the destination buffer.
* \return        DEM_NO_SUCH_ELEMENT
*                The data is not currently stored for the requested event
*                or
*                the requested data was not copied due to an undefined
*                RecordNumber for the given event.
* \return        E_NOT_OK
*                The request was rejected, e.g. due to variant coding (see
*                Dem_SetEventAvailable() )
*                or
*                the requested data is currently not accessible (e.g. in case
*                of asynchronous preempted data retrieval from application).
* \return        DEM_BUFFER_TOO_SMALL
*                The provided buffer is too small.
*
* \pre           RTE is active (if RTE is used).
*
* \context       TASK|ISR2, from the master partition only
* \synchronous   TRUE
* \reentrant     TRUE
* \trace         DSGN-Xlock22908
*********************************************************************************************************************/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_GetEventExtendedDataRecordEx(
  Dem_EventIdType  EventId,
  uint8  RecordNumber,
  P2VAR(uint8, AUTOMATIC, XLOCK_DATA) DestBuffer,
  P2VAR(uint16, AUTOMATIC, XLOCK_DATA)  BufSize);


/**********************************************************************************************************************
*  Xlock_GetEventFailed
**********************************************************************************************************************/
/*!\note         Xlock SWC API which is forwarded to Dem.
*
* \brief         API to read the event status bit 0.
*
* \details       This API returns bit0 of the current events status.
*
*                Xlock_GetEventFailed() is part of the RTE interface, but
*                may be called directly as well.
*
* \param[in]     EventId
*                Unique handle of the Event
*
* \param[out]    EventFailed
*                Pointer receiving the current failed bit:
*                TRUE if bit0 is set, FALSE otherwise
*
* \return        E_OK
*                EventFailed now contains the event status.
*
* \return        E_NOT_OK
*                The request was rejected, e.g. due to variant coding (see
*                Dem_SetEventAvailable()).
*
* \pre           -
*
* \context       TASK|ISR2
* \synchronous   TRUE
* \reentrant     TRUE
* \trace         DSGN-Xlock22908
*********************************************************************************************************************/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_GetEventFailed(
  Dem_EventIdType  EventId,
  P2VAR(boolean, AUTOMATIC, XLOCK_DATA)  EventFailed);


/**********************************************************************************************************************
*  Xlock_GetEventFreezeFrameDataEx
**********************************************************************************************************************/
/*!\note         Xlock SWC API which is forwarded to Dem.
*
* \brief         SWC API to read the stored snapshot data of an event.
*
* \details       This API is intended to be called via RTE_CALL interface,
*                as Operation of port interface 'DiagnosticInfo'.
*
*                Xlock_GetEventFreezeFrameDataEx() copies data from the stored
*                snapshot data of an event into DestBuffer, following no
*                particular format and contains no header-information like
*                RecordNumber or DataId.
*
*                Only the DID data actually stored in the event storage is
*                returned here. If the Dcm adds additional parts to a DID,
*                these will be missing.
*
*                The record number 0xff is a magic value used to read the most
*                recently stored record. This is only supported in case record
*                numbers are calculated (DemTypeOfFreezeFrameRecordNumeration).
*
* \param[in]     EventId
*                Unique handle of the Event.
* \param[in]     RecordNumber
*                SnapshotRecord number to read. Contrary to usual usage, 0xFF
*                returns the most recently stored record.
* \param[in]     DataId
*                Return only the data of this DID.
* \param[out]    DestBuffer
*                Buffer to receive the data.
* \param[in,out] BufSize
*                In: Size of the buffer.
*                Out: Number of bytes written.
*
* \return        E_OK
*                The requested data was copied to the destination buffer.
* \return        DEM_NO_SUCH_ELEMENT
*                The data is currently not stored for the requested event.
*                or
*                the requested RecordNumber is not supported for the given
*                event
*                or
*                the requested data identifier is not supported within the
*                requested record (freeze frame).
* \return        E_NOT_OK
*                The request was rejected, e.g. due to variant coding (see
*                Dem_SetEventAvailable() )
*                or
*                the requested data is currently not accessible (e.g. in case
*                of asynchronous preempted data retrieval from application).
* \return        DEM_BUFFER_TOO_SMALL
*                The provided buffer is too small.
*
* \pre           RTE is active (if RTE is used).
*
* \context       TASK|ISR2, from the master partition only
* \synchronous   TRUE
* \reentrant     TRUE
* \trace         DSGN-Xlock22908
*********************************************************************************************************************/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_GetEventFreezeFrameDataEx(
  Dem_EventIdType EventId,
  uint8 RecordNumber,
  uint16 DataId,
  P2VAR(uint8, AUTOMATIC, XLOCK_DATA) DestBuffer,
  P2VAR(uint16, AUTOMATIC, XLOCK_DATA)  BufSize);


/**********************************************************************************************************************
*  Xlock_GetEventStatus
**********************************************************************************************************************/
/*!\note         Xlock SWC API which is forwarded to Dem.
*
* \brief         API to read the UDS event status.
*
* \details       This API reads the current event status byte of an event.
*                The status byte is encoded as described in ISO 14229, without
*                applying the status availability mask.
*
*                Xlock_GetEventStatus() is part of the RTE interface, but
*                may be called directly as well.
*
* \param[in]     EventId
*                Unique handle of the Event
*
* \param[out]    UDSStatusByte
*                Pointer receiving the event status:
*                Bit0:  DEM_UDS_STATUS_TF
*                Bit1:  DEM_UDS_STATUS_TFTOC
*                Bit2:  DEM_UDS_STATUS_PDTC
*                Bit3:  DEM_UDS_STATUS_CDTC
*                Bit4:  DEM_UDS_STATUS_TNCSLC
*                Bit5:  DEM_UDS_STATUS_TFSLC
*                Bit6:  DEM_UDS_STATUS_TNCTOC
*                Bit7:  DEM_UDS_STATUS_WIR
*
* \return        E_OK
*                EventUdsStatus now contains the event status.
*
* \return        E_NOT_OK
*                The request was rejected, e.g. due to variant coding (see
*                Dem_SetEventAvailable()).
*
* \pre           -
*
* \context       TASK|ISR2
* \synchronous   TRUE
* \reentrant     TRUE
* \trace         DSGN-Xlock22908
*********************************************************************************************************************/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_GetEventStatus(
  Dem_EventIdType EventId,
  P2VAR(Dem_UdsStatusByteType, AUTOMATIC, XLOCK_DATA) UDSStatusByte);


/**********************************************************************************************************************
*  Xlock_GetEventTested
**********************************************************************************************************************/
/*!\note         Xlock SWC API which is forwarded to Dem.
*
* \brief         SWC API to read the negated event status bit 6.
*
* \details       This API returns TRUE in 'EventTested' if the event has
*                completed a test in this operation cycle, FALSE otherwise.
*                This corresponds to the negated status bit6.
*
*                Xlock_GetEventTested() is part of the RTE interface, but
*                may be called directly as well.
*
* \param[in]     EventId
*                Unique handle of the Event
*
* \param[out]    EventTested
*                Pointer receiving the current tested bit
*
* \return        E_OK
*                EventTested now contains the event status
*
* \return        E_NOT_OK
*                The request was rejected, e.g. due to variant coding (see
*                Dem_SetEventAvailable())
*
* \pre           -
*
* \context       TASK|ISR2
* \synchronous   TRUE
* \reentrant     TRUE
* \trace         DSGN-Xlock22908
*********************************************************************************************************************/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_GetEventTested(
  Dem_EventIdType EventId,
  P2VAR(boolean, AUTOMATIC, XLOCK_DATA) EventTested);


/**********************************************************************************************************************
*  Xlock_GetFaultDetectionCounter
**********************************************************************************************************************/
/*!\note         Xlock SWC API which is forwarded to Dem.
*
* \brief         SWC API to read the fault detection counter of an event
*
* \details       This API is intended to be called via RTE_CALL interface,
*                as Operation of port interface 'DiagnosticInfo'.
*
*                For events that are debounced within the Dem (counter based
*                and time based debouncing), the internal debouncing state
*                is converted to an UDS FaultDetectionCounter value in
*                range [-128..127].
*
*                For events that are debounced in application, the configured
*                callback is invoked to read the current FDC.
*
* \param[in]     EventId
*                Unique handle of the Event
* \param[out]    FaultDetectionCounter
*                Pointer receiving the current fault detection counter.
*
* \return        E_OK
*                The FDC was stored into FaultDetectionCounter
* \return        DEM_E_NO_FDC_AVAILABLE
*                The event is debounced within the application, but no
*                callback was configured to read the FDC.
* \return        E_NOT_OK if
*                The request was rejected, e.g. due to variant coding (see
*                Dem_SetEventAvailable() )
*
* \pre           RTE is active (if RTE is used)
*
* \context       TASK|ISR2
* \synchronous   TRUE
* \reentrant     TRUE
* \trace         DSGN-Xlock22908
*********************************************************************************************************************/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_GetFaultDetectionCounter(
  Dem_EventIdType EventId,
  P2VAR(sint8, AUTOMATIC, XLOCK_DATA) FaultDetectionCounter);


/**********************************************************************************************************************
*  Xlock_ResetEventStatus
**********************************************************************************************************************/
/*!\note         Xlock SWC API which is forwarded to Dem.
*
* \brief         SWC API to reset an events failed bit.
*
* \details       This API is intended to be called via RTE_CALL interface,
*                as Operation of port interface 'DiagnosticMonitor'.
*
*                Xlock_ResetEventStatus() resets the failed bit of an event
*                that can not be tested in this operation cycle. If the event
*                has already completed a test, the request is rejected.
*
*                The Dem module will not treat resetting the event failed bit
*                as a trigger to start aging of an event.
*
* \attention     The operations Xlock_SetEventStatus(),
*                Xlock_ResetEventStatus(), Xlock_ReportErrorStatus() and
*                Xlock_ResetEventDebounceStatus() are not reentrant
*                for the same EventId.
*
* \param[in]     EventId
*                Unique handle of the Event to reset.
*
* \return        E_OK
*                The request was processed successfully
*
* \return        E_NOT_OK
*                The request was rejected, e.g. the event had already
*                completed testing, or due to variant coding
*                (see Dem_SetEventAvailable() )
*
* \pre           RTE is started (if RTE is used).
*
* \context       TASK|ISR2, from the respective satellite partition only
* \synchronous   TRUE
* \reentrant     TRUE for different EventIds.
* \trace         DSGN-Xlock22908
*********************************************************************************************************************/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_ResetEventStatus(
  Dem_EventIdType EventId);


/**********************************************************************************************************************
* Xlock_ResetEventDebounceStatus
**********************************************************************************************************************/
/*!\note         Xlock SWC API which is forwarded to Dem.
*
* \brief         SWC API to control the Dem internal event debouncing.
*
* \details       This API is intended to be called via RTE_CALL interface,
*                as Operation of port interface 'DiagnosticMonitor'.
*
*                Depending on DebounceResetStatus and the EventId's configured
*                debouncing algorithm, this API performs the following:
*
*                Time Based Debouncing:
*                DEM_DEBOUNCE_STATUS_FREEZE: If the debounce timer is active,
*                it is paused without modifying its current value. Otherwise
*                this has no effect. The timer will continue if the monitor
*                reports another PREFAILED or PREPASSED in the same direction.
*
*                DEM_DEBOUNCE_STATUS_RESET: The debounce timer is stopped and
*                Its value is set to 0.
*
*                Counter Based Debouncing:
*                DEM_DEBOUNCE_STATUS_FREEZE: This has no effect.
*
*                DEM_DEDOUNCE_STATUS_RESET: This will set the current value
*                of the debounce counter back to 0.
*
*                Monitor Internal Debouncing:
*                The API returns E_NOT_OK in either case.
*
* \attention     The operations Xlock_SetEventStatus(),
*                Xlock_ResetEventStatus(), Xlock_ReportErrorStatus() and
*                Xlock_ResetEventDebounceStatus() are not reentrant
*                for the same EventId.
*
* \param[in]     EventId
*                Unique handle of the Event to report.
*
* \param[in]     DebounceResetStatus
*                DEM_DEBOUNCE_STATUS_RESET: Reset debouncing
*                DEM_DEBOUNCE_STATUS_FREEZE: Freeze debouncing
*
* \return        E_OK
*                The request was processed successfully
*
* \return        E_NOT_OK
*                The request was rejected, e.g. due to variant coding (see
*                Dem_SetEventAvailable() or supported debouncing algorithm )
*
* \pre           Dem has been pre-initialized.
*
* \context       TASK|ISR2, from the respective satellite partition only
* \synchronous   TRUE
* \reentrant     TRUE for different EventIds.
* \trace         DSGN-Xlock22908
*********************************************************************************************************************/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_ResetEventDebounceStatus(
  Dem_EventIdType  EventId,
  Dem_DebounceResetStatusType  DebounceResetStatus);


/**********************************************************************************************************************
*  Xlock_GetEventUdsStatus
**********************************************************************************************************************/
/*!\note         Xlock SWC API which is forwarded to Dem.
*
* \brief         API to read the UDS event status.
*
* \details       This API reads the current event status byte of an event.
*                The status byte is encoded as described in ISO 14229, without
*                applying the status availability mask.
*
*                Xlock_GetEventUdsStatus() is part of the RTE interface, but
*                may be called directly as well.
*
* \param[in]     EventId
*                Unique handle of the Event
*
* \param[out]    UDSStatusByte
*                Pointer receiving the event status:
*                Bit0:  DEM_UDS_STATUS_TF
*                Bit1:  DEM_UDS_STATUS_TFTOC
*                Bit2:  DEM_UDS_STATUS_PDTC
*                Bit3:  DEM_UDS_STATUS_CDTC
*                Bit4:  DEM_UDS_STATUS_TNCSLC
*                Bit5:  DEM_UDS_STATUS_TFSLC
*                Bit6:  DEM_UDS_STATUS_TNCTOC
*                Bit7:  DEM_UDS_STATUS_WIR
*
* \return        E_OK
*                EventUdsStatus now contains the event status.
*
* \return        E_NOT_OK
*                The request was rejected, e.g. due to variant coding (see
*                Dem_SetEventAvailable()).
*
* \pre           -
*
* \context       TASK|ISR2
* \synchronous   TRUE
* \reentrant     TRUE
* \trace         DSGN-Xlock22908
*********************************************************************************************************************/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_GetEventUdsStatus(
  Dem_EventIdType  EventId,
  P2VAR(Dem_UdsStatusByteType, AUTOMATIC, XLOCK_DATA)  UDSStatusByte);


# define XLOCK_STOP_SEC_CODE
# include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/***********************************************************************************************************************
 *  EXCLUSIVE AREA DEFINITION
 **********************************************************************************************************************/



#endif /* XLOCK_H */

/**********************************************************************************************************************
 *  END OF FILE: Xlock.h
 *********************************************************************************************************************/
