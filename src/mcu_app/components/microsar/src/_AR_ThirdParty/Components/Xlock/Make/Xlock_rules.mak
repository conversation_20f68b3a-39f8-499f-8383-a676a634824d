############################################################################### 
# File Name  : Xlock_rules.mak 
# Description: Rules makefile 
#------------------------------------------------------------------------------
# COPYRIGHT
#------------------------------------------------------------------------------
# Copyright (c) 2020 by Vector Informatik GmbH.  All rights reserved.
#------------------------------------------------------------------------------
# REVISION HISTORY
#------------------------------------------------------------------------------
# Version   Date        Author     Description
#------------------------------------------------------------------------------
# 1.00.00   2020-08-31  vsarcmimo  Initial Version base on template v1.02.00
#------------------------------------------------------------------------------
# TemplateVersion = 1.02
###############################################################################

# Component Files
CC_FILES_TO_BUILD       += Xlock$(BSW_SRC_DIR)\Xlock.c Xlock$(BSW_SRC_DIR)\Xlock_ErrorReporting.c Xlock$(BSW_SRC_DIR)\Xlock_FunctionLocking.c
GENERATED_SOURCE_FILES  += $(GENDATA_DIR)\Xlock_ErrorReporting_Cfg.c $(GENDATA_DIR)\Xlock_FunctionLocking_Cfg.c

# Library Settings
LIBRARIES_TO_BUILD      += Xlock
Xlock_FILES             = Xlock$(BSW_SRC_DIR)\Xlock.c