/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *  Copyright (c) 2020 by Vector Informatik GmbH.                                                  All rights reserved.
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -----------------------------------------------------------------------------------------------------------------*/
/*!        \file  Xlock.c
 *        \brief  Source for Autosar CDD module Xlock
 *      \details  Static source file for Xlock. May not be changed by customer / generator
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  REVISION HISTORY
 *  -------------------------------------------------------------------------------------------------------------------
 *  Refer to the module's header file.
 * 
 *  FILE VERSION
 *  -------------------------------------------------------------------------------------------------------------------
 *  Refer to the VERSION CHECK below.
 *********************************************************************************************************************/

#define XLOCK_SOURCE
/**********************************************************************************************************************
 *  INCLUDES
 *********************************************************************************************************************/
#include "Xlock.h"

#if (XLOCK_DEV_ERROR_REPORT == STD_ON)
# include "Det.h"
#endif


/**********************************************************************************************************************
 *  VERSION CHECK
 *********************************************************************************************************************/
/* Check the version of Xlock header file */
#if (  (XLOCK_SW_MAJOR_VERSION != (5u)) \
    || (XLOCK_SW_MINOR_VERSION != (0u)) \
    || (XLOCK_SW_PATCH_VERSION != (0u)) )
# error "Vendor specific version numbers of Xlock.c and Xlock.h are inconsistent"
#endif

/* Check the version of the configuration header file */
#if (  (XLOCK_CFG_SW_MAJOR_VERSION != (5u)) \
    || (XLOCK_CFG_SW_MINOR_VERSION != (0u)) \
    || (XLOCK_CFG_SW_PATCH_VERSION != (0u)) )
# error "Version numbers of Xlock.c and Xlock_Cfg.h are inconsistent!"
#endif

/**********************************************************************************************************************
 *  LOCAL CONSTANT MACROS
 *********************************************************************************************************************/
#if !defined (XLOCK_LOCAL)
# define XLOCK_LOCAL                                                  static
#endif

#if !defined (XLOCK_LOCAL_INLINE)
# define XLOCK_LOCAL_INLINE                                           LOCAL_INLINE
#endif

/**********************************************************************************************************************
 *  LOCAL FUNCTION MACROS
 *********************************************************************************************************************/

 /*! Set bit in port element (macro is easier to read).       */
#define Xlock_SetPortElement(port,bitpos)                             ((port) |=  (1UL << (bitpos)))


/**********************************************************************************************************************
 *  LOCAL DATA TYPES AND STRUCTURES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL DATA PROTOTYPES
 *********************************************************************************************************************/
#define XLOCK_START_SEC_VAR_NO_INIT_8
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

 /*! Global variable to disable the SetEventStatus implementation */
VAR(boolean, XLOCK_VAR_NOINIT) Xlock_SetEventStatusEnabled;

/*! Global variable to disable Cross Locking in the SetEventStatus implementation */
VAR(boolean, XLOCK_VAR_NOINIT) Xlock_SetEventStatusCrossLockingEnabled;

/*! Global variable to disable the ReportErrorStatus implementation */
VAR(boolean, XLOCK_VAR_NOINIT) Xlock_ReportErrorStatusEnabled;

/*! Global variable to disable Cross Locking in the ReportErrorStatus implementation */
VAR(boolean, XLOCK_VAR_NOINIT) Xlock_ReportErrorStatusCrossLockingEnabled;

#define XLOCK_STOP_SEC_VAR_NO_INIT_8
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


#define XLOCK_START_SEC_VAR_CLEARED_8
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/*! Initialization state of the module */
#if (XLOCK_DEV_ERROR_DETECT == STD_ON)
XLOCK_LOCAL VAR(uint8, XLOCK_VAR_ZERO_INIT) Xlock_ModuleInitialized = XLOCK_UNINIT;
#endif

#define XLOCK_STOP_SEC_VAR_CLEARED_8
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


/**********************************************************************************************************************
 *  GLOBAL DATA
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL FUNCTION PROTOTYPES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  Xlock_CrossLockEvent()
 *********************************************************************************************************************/
 /*! \brief                       Internal helper function to perform cross-locking.
 *
 *  \details                      This function is used because Xlock must support both AUTOSAR 3.x and 4.x APIs
 *
 *  \param[in] forwardingEnabled      Boolean to control event forwarding.
 *  \param[in] crossLockingEnabled    Boolean if crosslocking should be performed.
 *  \param[in] eventId                Event id to report to Dem, must match a FiM Function Id.
 *  \param[in] eventStatus            Event status to report to Dem.
 *
 *  \return                           E_NOT_OK - Result could not be reported to Dem
 *                                    E_OK - Success, result reported to Dem or cross-locking functionality is disabled.
 *
 *  \pre                          -
 *  \context                      TASK|ISR2, from the respective satellite partition only
 *  \reentrant                    TRUE for different EventIds.
 *  \trace DSGN-Xlock22985
 **********************************************************************************************************************/
XLOCK_LOCAL FUNC(Std_ReturnType, XLOCK_CODE) Xlock_CrossLockEvent(boolean forwardingEnabled,boolean crossLockingEnabled,
                                                                  Dem_EventIdType eventId, Dem_EventStatusType eventStatus);


#define XLOCK_START_SEC_CODE
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */
/**********************************************************************************************************************
 *  LOCAL FUNCTIONS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  Xlock_CrossLockEvent()
 *********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 *
 *
 */
XLOCK_LOCAL FUNC(Std_ReturnType, XLOCK_CODE) Xlock_CrossLockEvent(boolean forwardingEnabled, boolean crossLockingEnabled,
                                                                  Dem_EventIdType eventId, Dem_EventStatusType eventStatus)
{
  Std_ReturnType retVal = E_OK;

  /* #10 Check if event status forwarding is enabled. */
  if (forwardingEnabled == TRUE)
  {

    /* #20 Check if cross locking should be performed. */
    if (crossLockingEnabled == TRUE)
    {
      boolean allowed = FALSE;
      (void)FiM_GetFunctionPermission((FiM_FunctionIdType)eventId, &allowed);

      /* #30 Forward event status to Dem only if FiM allows it. */
      if (allowed == TRUE)
      {
        retVal = Dem_SetEventStatus(eventId, eventStatus);
      }
    }
    else
    {
      /* If cross locking is disabled, always forward event status */
      retVal = Dem_SetEventStatus(eventId, eventStatus);
    }
  }

  return retVal;
}



/**********************************************************************************************************************
 *  GLOBAL FUNCTIONS
 *********************************************************************************************************************/

 /**********************************************************************************************************************
 *  Xlock_InitMemory()
 *********************************************************************************************************************/
 /*!
 * Internal comment removed.
 *
 *
 */
FUNC(void, XLOCK_CODE) Xlock_InitMemory(void)
{
#if (XLOCK_DEV_ERROR_DETECT == STD_ON)
  Xlock_ModuleInitialized = XLOCK_UNINIT;
#endif
}


/**********************************************************************************************************************
*  Xlock_Init()
*********************************************************************************************************************/
/*!
* Internal comment removed.
 *
 *
 *
 *
 *
 *
 *
 *
*/
FUNC(void, XLOCK_CODE) Xlock_Init(void)
{
  /* ----- Local Variables ---------------------------------------------- */
  uint8 errorId = XLOCK_E_NO_ERROR;

  /* ----- Development Error Checks ------------------------------------- */
#if (XLOCK_DEV_ERROR_DETECT == STD_ON)
  /* Check initialization state of the component */
  if (Xlock_ModuleInitialized == (uint8)XLOCK_INIT)
  {
    errorId = XLOCK_E_ALREADY_INITIALIZED;
  }
  else
#endif
  {
#if (XLOCK_DEV_ERROR_DETECT == STD_ON)
    Xlock_ModuleInitialized = (uint8)XLOCK_INIT;
#endif
    Xlock_SetEventStatusEnabled = TRUE;
    Xlock_SetEventStatusCrossLockingEnabled = TRUE;
    Xlock_ReportErrorStatusEnabled = TRUE;
    Xlock_ReportErrorStatusCrossLockingEnabled = TRUE;
  }

  /* ----- Development Error Report --------------------------------------- */
#if (XLOCK_DEV_ERROR_REPORT == STD_ON)
  if (errorId != XLOCK_E_NO_ERROR)
  {
    (void)Det_ReportError(XLOCK_MODULE_ID, XLOCK_INSTANCE_ID_DET, XLOCK_SID_INIT, errorId);
  }
#else
  XLOCK_DUMMY_STATEMENT(errorId); /* PRQA S 1338, 2983, 3122 */ /* MD_MSR_DummyStmt */ /*lint !e438 */
#endif
}


#if (XLOCK_VERSION_INFO_API == STD_ON)
/**********************************************************************************************************************
*  Xlock_GetVersionInfo()
*********************************************************************************************************************/
/*!
* Internal comment removed.
 *
 *
 *
*/
FUNC(void, XLOCK_CODE) Xlock_GetVersionInfo(P2VAR(Std_VersionInfoType, AUTOMATIC, XLOCK_DATA) Versioninfo)
{
  /* ----- Local Variables ---------------------------------------------- */
  uint8 errorId = XLOCK_E_NO_ERROR;

  /* ----- Development Error Checks ------------------------------------- */
# if (XLOCK_DEV_ERROR_DETECT == STD_ON)
  if (Versioninfo == NULL_PTR)
  {
    errorId = XLOCK_E_PARAM_POINTER;
  }
  else
# endif
  {
    /* ----- Implementation ----------------------------------------------- */
    Versioninfo->vendorID = (XLOCK_VENDOR_ID);
    Versioninfo->moduleID = (XLOCK_MODULE_ID);
    Versioninfo->sw_major_version = (XLOCK_SW_MAJOR_VERSION);
    Versioninfo->sw_minor_version = (XLOCK_SW_MINOR_VERSION);
    Versioninfo->sw_patch_version = (XLOCK_SW_PATCH_VERSION);
  }

  /* ----- Development Error Report --------------------------------------- */
# if (XLOCK_DEV_ERROR_REPORT == STD_ON)
  if (errorId != XLOCK_E_NO_ERROR)
  {
    (void)Det_ReportError(XLOCK_MODULE_ID, XLOCK_INSTANCE_ID_DET, XLOCK_SID_GET_VERSION_INFO, errorId);
  }
# else
  XLOCK_DUMMY_STATEMENT(errorId); /* PRQA S 1338, 2983, 3122 */ /* MD_MSR_DummyStmt */ /*lint !e438 */
# endif
}
#endif


/**********************************************************************************************************************
*  Xlock_SetEventStatus()
*********************************************************************************************************************/
/*!
* Internal comment removed.
 *
 *
*/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_SetEventStatus(Dem_EventIdType EventId, Dem_EventStatusType EventStatus)
{
  return Xlock_CrossLockEvent(Xlock_SetEventStatusEnabled, Xlock_SetEventStatusCrossLockingEnabled, EventId, EventStatus);
}


/**********************************************************************************************************************
*  Xlock_ReportErrorStatus()
*********************************************************************************************************************/
/*!
* Internal comment removed.
 *
 *
*/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_ReportErrorStatus(Dem_EventIdType EventId, Dem_EventStatusType EventStatus)
{
  return Xlock_CrossLockEvent(Xlock_ReportErrorStatusEnabled, Xlock_ReportErrorStatusCrossLockingEnabled, EventId, EventStatus);
}


#if (XLOCK_ERROR_REPORTING_ENABLED == STD_ON || XLOCK_FUNCTION_LOCKING_ENABLED == STD_ON)
/**********************************************************************************************************************
*  Xlock_UpdateFunctionPermission
**********************************************************************************************************************/
/*!
* Internal comment removed.
 *
 *
 *
 *
*/
FUNC(void, XLOCK_CODE) Xlock_UpdateFunctionPermission(P2CONST(Xlock_ErrorReportingPort, AUTOMATIC, XLOCK_DATA) portTable, uint32 noOfPorts)
{
  Xlock_ErrorReportingElement data;
  boolean functionPermission;

  /* #10 Iterate over the sender/receiver (out) port elements. */
  uint32_least i;
  for (i = 0; i < noOfPorts; i++)
  {
    uint8_least j;
    Xlock_ErrorReportingPort element = portTable[i];

    /* Initialize temp variables */
    data = 0;

    for (j = 0; j < element.size; j++) {

      /* #20 Check function permission from the FiM and assign it to the corresponding bit position. */
      functionPermission = FALSE;
      (void)element.rPort[j].inhibitionPort(&functionPermission);

      if (functionPermission == TRUE)
      {
        Xlock_SetPortElement(data, element.rPort[j].bitPosition);
      }
    }

    /* #30 Write the output port data. */
    element.pPort(data);
  }
}
#endif

/**********************************************************************************************************************
*  FORWARDED FUNCTION CALLS
*********************************************************************************************************************/

/**********************************************************************************************************************
*  Xlock_GetDTCOfEvent
*********************************************************************************************************************/
/*!
* Internal comment removed.
 *
 *
*/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_GetDTCOfEvent(
  Dem_EventIdType EventId,
  Dem_DTCFormatType DTCFormat,
  P2VAR(uint32, AUTOMATIC, XLOCK_DATA) DTCOfEvent)
{
  return Dem_GetDTCOfEvent(EventId, DTCFormat, DTCOfEvent);
}

/**********************************************************************************************************************
*  Xlock_GetEventExtendedDataRecordEx
*********************************************************************************************************************/
/*!
* Internal comment removed.
 *
 *
*/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_GetEventExtendedDataRecordEx(
  Dem_EventIdType  EventId,
  uint8  RecordNumber,
  P2VAR(uint8, AUTOMATIC, XLOCK_DATA) DestBuffer,
  P2VAR(uint16, AUTOMATIC, XLOCK_DATA)  BufSize)
{
  return Dem_GetEventExtendedDataRecordEx(EventId, RecordNumber, DestBuffer, BufSize);
}

/**********************************************************************************************************************
*  Xlock_GetEventFailed
*********************************************************************************************************************/
/*!
* Internal comment removed.
 *
 *
*/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_GetEventFailed(
  Dem_EventIdType  EventId,
  P2VAR(boolean, AUTOMATIC, XLOCK_DATA)  EventFailed)
{
  return Dem_GetEventFailed(EventId, EventFailed);
}

/**********************************************************************************************************************
*  Xlock_GetEventFreezeFrameDataEx
*********************************************************************************************************************/
/*!
* Internal comment removed.
 *
 *
*/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_GetEventFreezeFrameDataEx(
  Dem_EventIdType EventId,
  uint8 RecordNumber,
  uint16 DataId,
  P2VAR(uint8, AUTOMATIC, XLOCK_DATA) DestBuffer,
  P2VAR(uint16, AUTOMATIC, XLOCK_DATA)  BufSize)
{
  return Dem_GetEventFreezeFrameDataEx(EventId, RecordNumber, DataId, DestBuffer, BufSize);
}

/**********************************************************************************************************************
*  Xlock_GetEventStatus
*********************************************************************************************************************/
/*!
* Internal comment removed.
 *
 *
*/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_GetEventStatus(
  Dem_EventIdType EventId,
  P2VAR(Dem_UdsStatusByteType, AUTOMATIC, XLOCK_DATA)  UDSStatusByte)
{
  return Dem_GetEventUdsStatus(EventId, UDSStatusByte);
}

/**********************************************************************************************************************
*  Xlock_GetEventTested
*********************************************************************************************************************/
/*!
* Internal comment removed.
 *
 *
*/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_GetEventTested(
  Dem_EventIdType EventId,
  P2VAR(boolean, AUTOMATIC, XLOCK_DATA) EventTested)
{
  return Dem_GetEventTested(EventId, EventTested);
}

/**********************************************************************************************************************
*  Xlock_GetFaultDetectionCounter
*********************************************************************************************************************/
/*!
* Internal comment removed.
 *
 *
*/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_GetFaultDetectionCounter(
  Dem_EventIdType EventId,
  P2VAR(sint8, AUTOMATIC, XLOCK_DATA) FaultDetectionCounter)
{
  return Dem_GetFaultDetectionCounter(EventId, FaultDetectionCounter);
}

/**********************************************************************************************************************
*  Xlock_ResetEventStatus
*********************************************************************************************************************/
/*!
* Internal comment removed.
 *
 *
*/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_ResetEventStatus(Dem_EventIdType EventId)
{
  return Dem_ResetEventStatus(EventId);
}

/**********************************************************************************************************************
* Xlock_ResetEventDebounceStatus
*********************************************************************************************************************/
/*!
* Internal comment removed.
 *
 *
*/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_ResetEventDebounceStatus(
  Dem_EventIdType  EventId,
  Dem_DebounceResetStatusType  DebounceResetStatus
)
{
  return Dem_ResetEventDebounceStatus(EventId, DebounceResetStatus);
}

/**********************************************************************************************************************
*  Xlock_GetEventUdsStatus
*********************************************************************************************************************/
/*!
* Internal comment removed.
 *
 *
*/
FUNC(Std_ReturnType, XLOCK_CODE) Xlock_GetEventUdsStatus(
  Dem_EventIdType  EventId,
  P2VAR(Dem_UdsStatusByteType, AUTOMATIC, XLOCK_DATA)  UDSStatusByte
)
{
  return Dem_GetEventUdsStatus(EventId, UDSStatusByte);
}


#define XLOCK_STOP_SEC_CODE
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 *  END OF FILE: Xlock.c
 *********************************************************************************************************************/
