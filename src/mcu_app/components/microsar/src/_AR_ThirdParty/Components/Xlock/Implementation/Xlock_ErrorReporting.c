/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *  Copyright (c) 2020 by Vector Informatik GmbH.                                                  All rights reserved.
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -----------------------------------------------------------------------------------------------------------------*/
/*!        \file  Xlock_ErrorReporting.c
 *        \brief  Source for Autosar CDD module Xlock ErrorReporting SWC
 *      \details  Static source file for Xlock. May not be changed by customer / generator
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  REVISION HISTORY
 *  -------------------------------------------------------------------------------------------------------------------
 *  Refer to the module's header file.
 * 
 *  FILE VERSION
 *  -------------------------------------------------------------------------------------------------------------------
 *  Refer to the VERSION CHECK below.
 *********************************************************************************************************************/

#define XLOCK_ERROR_REPORTING_SOURCE
/**********************************************************************************************************************
 *  INCLUDES
 *********************************************************************************************************************/
#include "Xlock.h"
#include "Rte_Xlock_ErrorReporting.h"

/**********************************************************************************************************************
 *  LOCAL CONSTANT MACROS
 *********************************************************************************************************************/
#if !defined (XLOCK_LOCAL)
# define XLOCK_LOCAL                                                  static
#endif

/**********************************************************************************************************************
 *  LOCAL FUNCTION MACROS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL DATA TYPES AND STRUCTURES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL DATA PROTOTYPES
 *********************************************************************************************************************/
#define Xlock_ErrorReporting_START_SEC_VAR_NOINIT_UNSPECIFIED
#include "Xlock_ErrorReporting_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#if (XLOCK_ERROR_REPORTING_CLEAR_DTC == STD_ON)
XLOCK_LOCAL VAR(boolean, Xlock_ErrorReporting_VAR_NOINIT) Xlock_ErrorReporting_ClearDtcIndicationActive;
#endif

#define Xlock_ErrorReporting_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#include "Xlock_ErrorReporting_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 *  GLOBAL DATA
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL FUNCTION PROTOTYPES
 *********************************************************************************************************************/


#define Xlock_ErrorReporting_START_SEC_CODE
#include "Xlock_ErrorReporting_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */
/**********************************************************************************************************************
 *  LOCAL FUNCTIONS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL FUNCTIONS
 *********************************************************************************************************************/

#if (XLOCK_ERROR_REPORTING_ENABLED == STD_ON)
/**********************************************************************************************************************
*  Xlock_ErrorReporting_MainFunction()
**********************************************************************************************************************/
/*!
* Internal comment removed.
 *
 *
 *
 *
*/
FUNC(void, Xlock_ErrorReporting_CODE) Xlock_ErrorReporting_MainFunction(void)
{
  /* #10 Check if error reporting is enabled. */
# if (XLOCK_CAN_DISABLE_ERROR_REPORTING == STD_ON)
  if (Rte_CData_Xlock_ErrorReportingEnabled() == TRUE)
# endif
  {
    /* #20 Call common functionality helper */
    (void)Xlock_UpdateFunctionPermission(Xlock_ErrorReportingDataTable, XLOCK_ERROR_REPORTING_NO_OF_PORTS);

    /* #30 Clear DTC ports */
# if (XLOCK_ERROR_REPORTING_CLEAR_DTC == STD_ON)
    {
      uint8_least i;
      for (i = 0; i < XLOCK_ERROR_REPORTING_CLEAR_DTC_PORTS; i++) {
        (void)Xlock_ErrorReportingClearDTCList[i](Xlock_ErrorReporting_ClearDtcIndicationActive);
      }
    }
# endif
  }
}
#endif

#if (XLOCK_ERROR_REPORTING_CLEAR_DTC == STD_ON)
/**********************************************************************************************************************
* Xlock_ErrorReporting_ClearDtcIndication()
**********************************************************************************************************************/
/*!
* Internal comment removed.
 *
 *
*/
FUNC(void, Xlock_ErrorReporting_CODE) Xlock_ErrorReporting_ClearDtcIndication(void) /* PRQA S 0850 */ /* MD_MSR_19.8 */
{
  Xlock_ErrorReporting_ClearDtcIndicationActive = TRUE;
}


/**********************************************************************************************************************
*  Xlock_ErrorReporting_Init()
**********************************************************************************************************************/
/*!
* Internal comment removed.
 *
 *
*/
FUNC(void, Xlock_ErrorReporting_CODE) Xlock_ErrorReporting_Init(void) /* PRQA S 0850 */ /* MD_MSR_19.8 */
{
  Xlock_ErrorReporting_ClearDtcIndicationActive = FALSE;
}
#endif

#define Xlock_ErrorReporting_STOP_SEC_CODE
#include "Xlock_ErrorReporting_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 *  END OF FILE: Xlock_ErrorReporting.c
 *********************************************************************************************************************/
