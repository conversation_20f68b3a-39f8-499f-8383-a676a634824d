/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *  Copyright (c) 2020 by Vector Informatik GmbH.                                                  All rights reserved.
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -----------------------------------------------------------------------------------------------------------------*/
/*!        \file  Xlock_FunctionLocking.c
 *        \brief  Source for Autosar CDD module Xlock FunctionLocking SWC
 *      \details  Static source file for Xlock. May not be changed by customer / generator
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  REVISION HISTORY
 *  -------------------------------------------------------------------------------------------------------------------
 *  Refer to the module's header file.
 * 
 *  FILE VERSION
 *  -------------------------------------------------------------------------------------------------------------------
 *  Refer to the VERSION CHECK below.
 *********************************************************************************************************************/

#define XLOCK_FUNCTION_LOCKING_SOURCE
/**********************************************************************************************************************
 *  INCLUDES
 *********************************************************************************************************************/
#include "Xlock.h"
#include "Rte_Xlock_FunctionLocking.h"

/**********************************************************************************************************************
 *  LOCAL CONSTANT MACROS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL FUNCTION MACROS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL DATA TYPES AND STRUCTURES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL DATA PROTOTYPES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL DATA
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL FUNCTION PROTOTYPES
 *********************************************************************************************************************/

#define Xlock_FunctionLocking_START_SEC_CODE
#include "Xlock_FunctionLocking_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */
/**********************************************************************************************************************
 *  LOCAL FUNCTIONS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL FUNCTIONS
 *********************************************************************************************************************/

#if (XLOCK_FUNCTION_LOCKING_ENABLED == STD_ON)
/**********************************************************************************************************************
*  Xlock_FunctionLocking_MainFunction
**********************************************************************************************************************/
/*!
* Internal comment removed.
 *
 *
 *
*/
FUNC(void, Xlock_FunctionLocking_CODE) Xlock_FunctionLocking_MainFunction(void)
{
  /* #10 Check if function locking is enabled. */
# if (XLOCK_CAN_DISABLE_FUNCTION_LOCKING == STD_ON)
  if (Rte_CData_Xlock_FunctionLockingEnabled() == TRUE)
# endif
  {
    /* #20 Call common functionality helper */
    (void)Xlock_UpdateFunctionPermission(Xlock_FunctionLockingDataTable, XLOCK_FUNCTION_LOCKING_NO_OF_PORTS);
  }
}
#endif

#define Xlock_FunctionLocking_STOP_SEC_CODE
#include "Xlock_FunctionLocking_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 *  END OF FILE: Xlock_FunctionLocking.c
 *********************************************************************************************************************/
