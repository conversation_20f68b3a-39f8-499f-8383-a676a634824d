<?xml version="1.0" encoding="UTF-8"?>
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-2-1.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://autosar.org/schema/r4.0">
	<AR-PACKAGES>
		<AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
			<SHORT-NAME>MICROSAR</SHORT-NAME>
			<ELEMENTS>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="9ace38e4-46e5-441c-b6ed-42c1071f3ed8">
					<SHORT-NAME>vLinkGen_Pre</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/vLinkGen</DEFINITION-REF>
					<CONTAINERS>
						<ECUC-CONTAINER-VALUE UUID="c9c2be44-9cd5-4c79-af56-a48ce1fcee8a">
							<SHORT-NAME>vLinkGenPublishedInformation</SHORT-NAME>
							<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenPublishedInformation</DEFINITION-REF>
							<SUB-CONTAINERS>
								<ECUC-CONTAINER-VALUE UUID="a644da16-22a8-4908-83e4-f7ecb74a5c29">
									<SHORT-NAME>vLinkGenRegionBlockFlags</SHORT-NAME>
									<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenPublishedInformation/vLinkGenRegionBlockFlags</DEFINITION-REF>
									<SUB-CONTAINERS>
										<ECUC-CONTAINER-VALUE UUID="20d5ceb8-2d97-4236-92a2-c743b551441c">
											<SHORT-NAME>EXECUTE</SHORT-NAME>
											<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenPublishedInformation/vLinkGenRegionBlockFlags/vLinkGenRegionBlockFlag</DEFINITION-REF>
										</ECUC-CONTAINER-VALUE>
										<ECUC-CONTAINER-VALUE UUID="f6810011-1722-4d57-8018-5798087b84bc">
											<SHORT-NAME>READ</SHORT-NAME>
											<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenPublishedInformation/vLinkGenRegionBlockFlags/vLinkGenRegionBlockFlag</DEFINITION-REF>
										</ECUC-CONTAINER-VALUE>
										<ECUC-CONTAINER-VALUE UUID="3d53268e-7aa7-44c1-a615-24ea82d0b2a0">
											<SHORT-NAME>WRITE</SHORT-NAME>
											<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenPublishedInformation/vLinkGenRegionBlockFlags/vLinkGenRegionBlockFlag</DEFINITION-REF>
										</ECUC-CONTAINER-VALUE>
									</SUB-CONTAINERS>
								</ECUC-CONTAINER-VALUE>
								<ECUC-CONTAINER-VALUE UUID="93a58b16-7f48-4467-a522-868a83f81385">
									<SHORT-NAME>vLinkGenConstGroupFlags</SHORT-NAME>
									<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenPublishedInformation/vLinkGenConstGroupFlags</DEFINITION-REF>
									<SUB-CONTAINERS>
										<ECUC-CONTAINER-VALUE UUID="0a0a32e9-865f-41c6-bf13-1c866aba7391">
											<SHORT-NAME>NOLOAD</SHORT-NAME>
											<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenPublishedInformation/vLinkGenConstGroupFlags/vLinkGenConstGroupFlag</DEFINITION-REF>
										</ECUC-CONTAINER-VALUE>
										<ECUC-CONTAINER-VALUE UUID="922566cb-3fd7-44c1-90fb-103cb715c6b2">
											<SHORT-NAME>PALIGN_BLOCK</SHORT-NAME>
											<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenPublishedInformation/vLinkGenConstGroupFlags/vLinkGenConstGroupFlag</DEFINITION-REF>
										</ECUC-CONTAINER-VALUE>
									</SUB-CONTAINERS>
								</ECUC-CONTAINER-VALUE>
								<ECUC-CONTAINER-VALUE UUID="e4ca105c-74dc-441a-b3dd-9a6b542b34b4">
									<SHORT-NAME>vLinkGenVarGroupFlags</SHORT-NAME>
									<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenPublishedInformation/vLinkGenVarGroupFlags</DEFINITION-REF>
									<SUB-CONTAINERS>
										<ECUC-CONTAINER-VALUE UUID="831a7785-409c-47df-abf2-01f3a0c4ee44">
											<SHORT-NAME>NOLOAD</SHORT-NAME>
											<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenPublishedInformation/vLinkGenVarGroupFlags/vLinkGenVarGroupFlag</DEFINITION-REF>
										</ECUC-CONTAINER-VALUE>
										<ECUC-CONTAINER-VALUE UUID="f62824b2-7834-4325-826d-26251df68a2b">
											<SHORT-NAME>PALIGN_BLOCK</SHORT-NAME>
											<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenPublishedInformation/vLinkGenVarGroupFlags/vLinkGenVarGroupFlag</DEFINITION-REF>
										</ECUC-CONTAINER-VALUE>
									</SUB-CONTAINERS>
								</ECUC-CONTAINER-VALUE>
							</SUB-CONTAINERS>
						</ECUC-CONTAINER-VALUE>
					</CONTAINERS>
				</ECUC-MODULE-CONFIGURATION-VALUES>
			</ELEMENTS>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>