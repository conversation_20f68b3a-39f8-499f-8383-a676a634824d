<?xml version="1.0" encoding="UTF-8"?>
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-2-1.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://autosar.org/schema/r4.0">
	<AR-PACKAGES>
		<AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
			<SHORT-NAME>MICROSAR</SHORT-NAME>
			<ELEMENTS>
				<BSW-IMPLEMENTATION UUID="279b8d1f-cc47-41cd-b47f-cb88db0d739a">
					<SHORT-NAME>vLinkGen_Impl</SHORT-NAME>
					<PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
					<SW-VERSION>2.01.04</SW-VERSION>
					<USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
					<VENDOR-ID>30</VENDOR-ID>
					<AR-RELEASE-VERSION>4.02.01</AR-RELEASE-VERSION>
					<PRECONFIGURED-CONFIGURATION-REFS>
						<PRECONFIGURED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/vLinkGen_Pre</PRECONFIGURED-CONFIGURATION-REF>
					</PRECONFIGURED-CONFIGURATION-REFS>
					<RECOMMENDED-CONFIGURATION-REFS>
						<RECOMMENDED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/vLinkGen_Rec</RECOMMENDED-CONFIGURATION-REF>
					</RECOMMENDED-CONFIGURATION-REFS>
					<VENDOR-SPECIFIC-MODULE-DEF-REFS>
						<VENDOR-SPECIFIC-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/MICROSAR/vLinkGen</VENDOR-SPECIFIC-MODULE-DEF-REF>
					</VENDOR-SPECIFIC-MODULE-DEF-REFS>
				</BSW-IMPLEMENTATION>
				<ECUC-MODULE-DEF UUID="86dbbcb5-bd6a-4dd9-96a0-dd9dede7218b">
					<SHORT-NAME>vLinkGen</SHORT-NAME>
					<DESC>
						<L-2 L="EN">Configuration of the MICROSAR vLinkGen module.</L-2>
					</DESC>
					<CATEGORY>VENDOR_SPECIFIC_MODULE_DEFINITION</CATEGORY>
					<ADMIN-DATA>
						<DOC-REVISIONS>
							<DOC-REVISION>
								<REVISION-LABEL>1.00.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>visdfe</ISSUED-BY>
								<DATE>2018-02-07</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Initial version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.01.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>visces</ISSUED-BY>
								<DATE>2018-11-23</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Changed configuration of MemoryRegions</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.02.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virbse</ISSUED-BY>
								<DATE>2019-01-03</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">STORYC-7355, STORYC-7336, STORYC-7404 and updated parameter descriptions</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.03.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virbse</ISSUED-BY>
								<DATE>2019-02-28</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">STORYC-7216, STORYC-7583, STORYC-7403, STORYC-7502</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.04.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virbse</ISSUED-BY>
								<DATE>2019-05-03</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">STORYC-7432, STORYC-7223, STORYC-7581</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.05.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virbse</ISSUED-BY>
								<DATE>2019-07-05</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">STORYC-7433, STORYC-8292, ESCAN00103522, ESCAN00103566</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.06.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virbse</ISSUED-BY>
								<DATE>2019-08-02</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">OSC-140, OSC-4932</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.07.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virbse</ISSUED-BY>
								<DATE>2019-09-27</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">OSC-5156, OSC-5213, OSC-5216</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.07.01</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virbse</ISSUED-BY>
								<DATE>2019-12-19</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">OSC-5594</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>2.00.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virbse</ISSUED-BY>
								<DATE>2020-02-28</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">OSC-68, OSC-5599, OSC-5795</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>2.00.01</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virbse</ISSUED-BY>
								<DATE>2020-06-17</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Updated software version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>2.01.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virbse</ISSUED-BY>
								<DATE>2020-08-26</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">OSC-6453, OSC-6464, OSC-6490, OSC-6328, OSC-6441, OSC-6528, OSC-6457</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>2.01.01</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virbse</ISSUED-BY>
								<DATE>2020-09-03</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">OSC-6629</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>2.01.02</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virbse</ISSUED-BY>
								<DATE>2020-09-22</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Updated software version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>2.01.03</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virbse</ISSUED-BY>
								<DATE>2020-09-28</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Updated software version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>2.01.04</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virbse</ISSUED-BY>
								<DATE>2020-10-27</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">OSC-6809</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
						</DOC-REVISIONS>
					</ADMIN-DATA>
					<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
					<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
					<POST-BUILD-VARIANT-SUPPORT>false</POST-BUILD-VARIANT-SUPPORT>
					<SUPPORTED-CONFIG-VARIANTS>
						<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
					</SUPPORTED-CONFIG-VARIANTS>
					<CONTAINERS>
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="59082afe-de43-4df7-83e6-263bbf34a0b2">
							<SHORT-NAME>vLinkGenGeneral</SHORT-NAME>
							<DESC>
								<L-2 L="EN">General configuration parameters of the vLinkGen.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildNotDeletable">true</SD>
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<MULTIPLICITY-CONFIG-CLASSES>
								<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
									<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
								</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
							</MULTIPLICITY-CONFIG-CLASSES>
							<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
							<REQUIRES-INDEX>false</REQUIRES-INDEX>
							<PARAMETERS>
								<ECUC-ENUMERATION-PARAM-DEF UUID="47407139-fa26-40bf-beeb-9d60ffa1b8e6">
									<SHORT-NAME>vLinkGenFileGeneration</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">File Generation</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Specifies how the linker script shall be generated. The different types of generation are described below.

STANDARD:
Generate one complete compiler specific linker script.
Logical groups that are assigned to one or more variants are encapsulated with C preprocessor macros.
If variants are configured, this file may need to be preprocessed before it can be passed to the linker. 
This applies to all linkers that do not have their own preprocessor that can resolve C preprocessor macros (e.g. #ifdef ...).
The name of the generated linker script is: 'vLinkGen_Template.&lt;ext&gt;'

ONE_FILE_PER_VARIANT:
Generate complete linker scripts per variant containing all Logical Groups assigned to the variant as well as all without a variant.
They can be used as they are and passed to the linker.
These files are generated as follows: 'vLinkGen_Template_&lt;VariantName&gt;.&lt;ext&gt;'

MODULE_SNIPPETS:
Generate several linker script snippets containing all Logical Groups (including all Section Groups) with a module reference. See notes below.
These snippets are generated as follows: 'vLinkGen_Inc_&lt;ModuleName&gt;.&lt;ext&gt;'

VARIANT_SNIPPETS:
Generate several linker script snippets containing all Logical Groups (including all Section Groups) of a variant. See notes below.
These snippets are generated as follows: 'vLinkGen_Inc_&lt;VariantName&gt;.&lt;ext&gt;'

LOGICAL_GROUP_SNIPPETS:
Generate several linker script snippets containing all Section Groups of a Logical Group. See notes below.
These snippets are generated as follows: 'vLinkGen_Inc_&lt;LogicalGroupName&gt;.&lt;ext&gt;'

Note:
- Snippets are not supported by all compilers.
- Snippets only contain the corresponding logical groups and nothing else.
- Snippets cannot be passed directly to the linker. They must be included by other linker scripts.
  However, the names of the Memory Region Blocks (e.g. FLASH) must match the memory definitions from the linker script into which the snippets are to be included.
- If variants are configured, logical groups inside the snippets are encapsulated the same way as for STANDARD generation.
- Snippets never contain memory definitions and global (Pre/Post) user groups.
- &lt;ext&gt; means the compiler specific file extension of the linker script.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>STANDARD</DEFAULT-VALUE>
									<LITERALS>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="7ca2615c-359a-4173-8db9-1d0b0c88ac8a">
											<SHORT-NAME>STANDARD</SHORT-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="45fcf32f-ba28-4e09-9063-57b1c53342cd">
											<SHORT-NAME>ONE_FILE_PER_VARIANT</SHORT-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="cc99a573-2609-466e-b4bf-56b1558ae37b">
											<SHORT-NAME>MODULE_SNIPPETS</SHORT-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="5f1ea9b4-f0cc-4dd1-94d0-2e73651dcc22">
											<SHORT-NAME>VARIANT_SNIPPETS</SHORT-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="7b2db178-0f29-4d89-9c8e-468a1bdb99ed">
											<SHORT-NAME>LOGICAL_GROUP_SNIPPETS</SHORT-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
									</LITERALS>
								</ECUC-ENUMERATION-PARAM-DEF>
								<ECUC-STRING-PARAM-DEF UUID="0fd4d310-4927-40ff-abef-74392dc42ebf">
									<SHORT-NAME>vLinkGenEntrySymbol</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Startup Entry Symbol</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Defines the symbol for the startup entry. This symbol is required by some compilers to define the entry point of the program code.

Note:
This parameter is currently only evaluated in projects with Tasking Compiler.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<ECUC-STRING-PARAM-DEF-VARIANTS>
										<ECUC-STRING-PARAM-DEF-CONDITIONAL>
											<DEFAULT-VALUE>StartupEntry</DEFAULT-VALUE>
										</ECUC-STRING-PARAM-DEF-CONDITIONAL>
									</ECUC-STRING-PARAM-DEF-VARIANTS>
								</ECUC-STRING-PARAM-DEF>
								<ECUC-STRING-PARAM-DEF UUID="abc743c7-4fce-4024-b132-b5e7889c3650">
									<SHORT-NAME>vLinkGenUserConfigFile</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">User Config File</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Reference to an external text file that will be included during generation to vLinkGen_Cfg.h.
The content of the user configuration file will be added at the beginning of the generated module configuration file and allows altering or extending the generated code.

Note:
In contrast to the other MICROSAR components, vLinkGen inserts the user config file at the beginning of vLinkGen_Cfg.h.
This is necessary to make possible defined variant macros also known in the file itself.

Caution:
User configuration files can cause the software module to malfunction and must only be used with great care!</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
								</ECUC-STRING-PARAM-DEF>
							</PARAMETERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="206c6b28-e87f-468e-ad64-7c4b11901201">
							<SHORT-NAME>vLinkGenMemLayout</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Container to structure all memory layout relevant configuration parameters.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildNotDeletable">true</SD>
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<MULTIPLICITY-CONFIG-CLASSES>
								<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
									<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
								</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
							</MULTIPLICITY-CONFIG-CLASSES>
							<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
							<REQUIRES-INDEX>false</REQUIRES-INDEX>
							<SUB-CONTAINERS>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="4e46e9f1-968e-4e87-a986-8ea535c78fbf">
									<SHORT-NAME>vLinkGenLinkerSections</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container to structure the configuration parameters of the different Linker Section types.

Note:
Linker Sections are comparable to so called input sections from (some) compiler manuals.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildNotDeletable">true</SD>
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SUB-CONTAINERS>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="3a3e3223-5997-4558-8ca9-bae8bbf0d448">
											<SHORT-NAME>vLinkGenLinkerCodeSection</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container includes all configuration parameters for a CODE Linker Section.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildNotDeletable">true</SD>
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<PARAMETERS>
												<ECUC-LINKER-SYMBOL-DEF UUID="a7d2809c-6b5a-4a9a-bb37-689c892cadb0">
													<SHORT-NAME>vLinkGenLinkerCodeSectionName</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Name</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This parameter specifies the name of the Linker Section to be generated in the linker script.

Note:
Leading points (.) need not be specified here. They will be added by vLinkGen if necessary.
If sections with special values are required, a vLinkGenLinkerSpecSection can be used instead.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-LINKER-SYMBOL-DEF-VARIANTS>
														<ECUC-LINKER-SYMBOL-DEF-CONDITIONAL>
															<DEFAULT-VALUE>text</DEFAULT-VALUE>
														</ECUC-LINKER-SYMBOL-DEF-CONDITIONAL>
													</ECUC-LINKER-SYMBOL-DEF-VARIANTS>
												</ECUC-LINKER-SYMBOL-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="16294775-320c-430f-a1c1-561444cee737">
													<SHORT-NAME>vLinkGenLinkerCodeSectionAlignment</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Alignment</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the optional alignment of the Linker Section in bytes.
This allows the start address of the Linker Section to be aligned accordingly after linking.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">BYTE</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-BOOLEAN-PARAM-DEF UUID="9c46e17a-d9ae-45cc-92de-db19a96bedcb">
													<SHORT-NAME>vLinkGenLinkerCodeSectionFast</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Fast Addressing</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Specifies whether the Linker Section shall be linked for fast addressing (NEAR) or not (FAR).

Note:
This parameter is only needed for a few compilers, but should always be set correctly.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<ECUC-STRING-PARAM-DEF UUID="1136b7f1-f436-4be3-9fcf-6703702709ba">
													<SHORT-NAME>vLinkGenLinkerCodeSectionFiles</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Filenames</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Specifies the name of one ore more files from which the content of the Linker Section is to be linked.
Whereby the names of individual files or archives (libraries) with the files they contain can be specified.
So it is possible to place the content of a linker section only from one or more files to a certain position.

If several files or archives are to be specified, they must be entered as a semicolon-separated list.
Where an archive is defined by a name followed by parentheses. Within these parentheses, members of the archive can optionally be specified as a comma-separated list.
See examples below.

From this, vLinkGen generates, if necessary for the selected compiler, individual selection commands for each file or archive.
For individual selection commands, the file names are generated according to their specified order.

Examples:
- file.o
- file_a.o;file_b.o;file_c.o
- lib.a()
- lib.a(file_a.o,file_b.o)
- lib1.a(file_*); lib2.a(*); file_?.o
- /usr/folder/lib/lib.a(/usr/folder/source/file1.o)
- C:/folder/lib/lib.a(C:/folder/source/file1.o)

Note:
- The specified file or archive names will be generated as they are. The string is only split and whitespaces are removed.
- That means for most compilers it is also possible to specify wildcards (*, ?) and complete paths.
- Ensure that UNIX style file paths do not contain parentheses.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<ECUC-REFERENCE-DEF UUID="f0b695f6-f45c-4878-b8a4-dfa76a3e41db">
													<SHORT-NAME>vLinkGenLinkerCodeSectionFlag</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Compiler Specific Flags</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Defines compiler specific flags to be generated for the Linker Section.

Note:
The flags to be selected are predefined and cannot be changed.
If no flags are selectable, the compiler either does not support flags on section level or the vLinkGen doesn't support them.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenPublishedInformation/vLinkGenConstSectionFlags/vLinkGenConstSectionFlag</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<ECUC-FOREIGN-REFERENCE-DEF UUID="0a7eaeec-3b2e-4d04-8339-1db2f31051b2">
													<SHORT-NAME>vLinkGenLinkerCodeSectionModule</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Module Ref</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Optional reference to the module which owns (requires) this Linker Section.

Note:
If this reference is set, the container is typically created by another module's generator.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<DESTINATION-TYPE>ECUC-MODULE-CONFIGURATION-VALUES</DESTINATION-TYPE>
												</ECUC-FOREIGN-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="3e57951c-37be-4e82-8452-def98b358150">
											<SHORT-NAME>vLinkGenLinkerConstSection</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container includes all configuration parameters for a CONST Linker Section.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildNotDeletable">true</SD>
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<PARAMETERS>
												<ECUC-LINKER-SYMBOL-DEF UUID="b5f5d853-c782-47b5-a161-e7d5e6dcdd11">
													<SHORT-NAME>vLinkGenLinkerConstSectionName</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Name</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This parameter specifies the name of the Linker Section to be generated in the linker script.

Note:
Leading points (.) need not be specified here. They will be added by vLinkGen if necessary.
If sections with special values are required, a vLinkGenLinkerSpecSection can be used instead.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-LINKER-SYMBOL-DEF-VARIANTS>
														<ECUC-LINKER-SYMBOL-DEF-CONDITIONAL>
															<DEFAULT-VALUE>rodata</DEFAULT-VALUE>
														</ECUC-LINKER-SYMBOL-DEF-CONDITIONAL>
													</ECUC-LINKER-SYMBOL-DEF-VARIANTS>
												</ECUC-LINKER-SYMBOL-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="63868933-f8a9-4a70-a160-9f5356262a9a">
													<SHORT-NAME>vLinkGenLinkerConstSectionAlignment</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Alignment</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the optional alignment of the Linker Section in bytes.
This allows the start address of the Linker Section to be aligned accordingly after linking.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">BYTE</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-BOOLEAN-PARAM-DEF UUID="e7655877-dea1-4c4f-a79e-010d9ebc2dea">
													<SHORT-NAME>vLinkGenLinkerConstSectionFast</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Fast Addressing</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Specifies whether the Linker Section shall be linked for fast addressing (NEAR) or not (FAR).

Note:
This parameter is only needed for a few compilers, but should always be set correctly.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<ECUC-STRING-PARAM-DEF UUID="38bd2f8f-dc58-4209-9a21-e16b0f25f4fd">
													<SHORT-NAME>vLinkGenLinkerConstSectionFiles</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Filenames</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Specifies the name of one ore more files from which the content of the Linker Section is to be linked.
Whereby the names of individual files or archives (libraries) with the files they contain can be specified.
So it is possible to place the content of a linker section only from one or more files to a certain position.

If several files or archives are to be specified, they must be entered as a semicolon-separated list.
Where an archive is defined by a name followed by parentheses. Within these parentheses, members of the archive can optionally be specified as a comma-separated list.
See examples below.

From this, vLinkGen generates, if necessary for the selected compiler, individual selection commands for each file or archive.
For individual selection commands, the file names are generated according to their specified order.

Examples:
- file.o
- file_a.o;file_b.o;file_c.o
- lib.a()
- lib.a(file_a.o,file_b.o)
- lib1.a(file_*); lib2.a(*); file_?.o
- /usr/folder/lib/lib.a(/usr/folder/source/file1.o)
- C:/folder/lib/lib.a(C:/folder/source/file1.o)

Note:
- The specified file or archive names will be generated as they are. The string is only split and whitespaces are removed.
- That means for most compilers it is also possible to specify wildcards (*, ?) and complete paths.
- Ensure that UNIX style file paths do not contain parentheses.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<ECUC-REFERENCE-DEF UUID="fd8e4959-090f-4ce9-bf69-46b46ed337e3">
													<SHORT-NAME>vLinkGenLinkerConstSectionFlag</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Compiler Specific Flags</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Defines compiler specific flags to be generated for the Linker Section.

Note:
The flags to be selected are predefined and cannot be changed.
If no flags are selectable, the compiler either does not support flags on section level or the vLinkGen doesn't support them.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenPublishedInformation/vLinkGenConstSectionFlags/vLinkGenConstSectionFlag</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<ECUC-FOREIGN-REFERENCE-DEF UUID="24d39de4-a3ac-412d-8caa-200bb29f2724">
													<SHORT-NAME>vLinkGenLinkerConstSectionModule</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Module Ref</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Optional reference to the module which owns (requires) this Linker Section.

Note:
If this reference is set, the container is typically created by another module's generator.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<DESTINATION-TYPE>ECUC-MODULE-CONFIGURATION-VALUES</DESTINATION-TYPE>
												</ECUC-FOREIGN-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="69dbfdb1-23c9-439a-860a-18a30e066f43">
											<SHORT-NAME>vLinkGenLinkerVarSection</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container includes all configuration parameters for a VAR Linker Section.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildNotDeletable">true</SD>
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<PARAMETERS>
												<ECUC-LINKER-SYMBOL-DEF UUID="d38e1bc0-df92-4991-bd76-37689549d04e">
													<SHORT-NAME>vLinkGenLinkerVarSectionName</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Name</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This parameter specifies the name of the Linker Section to be generated in the linker script.

Note:
Leading points (.) need not be specified here. They will be added by vLinkGen if necessary.
If sections with special values are required, a vLinkGenLinkerSpecSection can be used instead.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-LINKER-SYMBOL-DEF-VARIANTS>
														<ECUC-LINKER-SYMBOL-DEF-CONDITIONAL>
															<DEFAULT-VALUE>data</DEFAULT-VALUE>
														</ECUC-LINKER-SYMBOL-DEF-CONDITIONAL>
													</ECUC-LINKER-SYMBOL-DEF-VARIANTS>
												</ECUC-LINKER-SYMBOL-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="882ff07d-d444-4aab-a708-1e806ccc8605">
													<SHORT-NAME>vLinkGenLinkerVarSectionAlignment</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Alignment</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the optional alignment of the Linker Section in bytes.
This allows the start address of the Linker Section to be aligned accordingly after linking.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">BYTE</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-BOOLEAN-PARAM-DEF UUID="2a927e16-8e03-4a82-b171-e00f4ccd5525">
													<SHORT-NAME>vLinkGenLinkerVarSectionFast</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Fast Addressing</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Specifies whether the Linker Section shall be linked for fast addressing (NEAR) or not (FAR).

Note:
This parameter is only needed for a few compilers, but should always be set correctly.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<ECUC-STRING-PARAM-DEF UUID="52432307-4db7-4085-9bbb-3a0f016c5647">
													<SHORT-NAME>vLinkGenLinkerVarSectionFiles</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Filenames</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Specifies the name of one ore more files from which the content of the Linker Section is to be linked.
Whereby the names of individual files or archives (libraries) with the files they contain can be specified.
So it is possible to place the content of a linker section only from one or more files to a certain position.

If several files or archives are to be specified, they must be entered as a semicolon-separated list.
Where an archive is defined by a name followed by parentheses. Within these parentheses, members of the archive can optionally be specified as a comma-separated list.
See examples below.

From this, vLinkGen generates, if necessary for the selected compiler, individual selection commands for each file or archive.
For individual selection commands, the file names are generated according to their specified order.

Examples:
- file.o
- file_a.o;file_b.o;file_c.o
- lib.a()
- lib.a(file_a.o,file_b.o)
- lib1.a(file_*); lib2.a(*); file_?.o
- /usr/folder/lib/lib.a(/usr/folder/source/file1.o)
- C:/folder/lib/lib.a(C:/folder/source/file1.o)

Note:
- The specified file or archive names will be generated as they are. The string is only split and whitespaces are removed.
- That means for most compilers it is also possible to specify wildcards (*, ?) and complete paths.
- Ensure that UNIX style file paths do not contain parentheses.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<ECUC-REFERENCE-DEF UUID="bae1e992-aa3e-423b-ae5c-d2fa446dda81">
													<SHORT-NAME>vLinkGenLinkerVarSectionFlag</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Compiler Specific Flags</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Defines compiler specific flags to be generated for the Linker Section.

Note:
The flags to be selected are predefined and cannot be changed.
If no flags are selectable, the compiler either does not support flags on section level or the vLinkGen doesn't support them.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenPublishedInformation/vLinkGenVarSectionFlags/vLinkGenVarSectionFlag</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<ECUC-FOREIGN-REFERENCE-DEF UUID="cf978dd6-ee5a-4d4c-8bf0-960a265bd84b">
													<SHORT-NAME>vLinkGenLinkerVarSectionModule</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Module Ref</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Optional reference to the module which owns (requires) this Linker Section.

Note:
If this reference is set, the container is typically created by another module's generator.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<DESTINATION-TYPE>ECUC-MODULE-CONFIGURATION-VALUES</DESTINATION-TYPE>
												</ECUC-FOREIGN-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="4a0bd071-a7ea-47fd-96cc-1cb4a79d2162">
											<SHORT-NAME>vLinkGenLinkerStackSection</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container includes all configuration parameters for a STACK Linker Section.

Caution:
STACK Linker Sections are special variable sections, which shall only be used by the operating system.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildNotDeletable">true</SD>
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<PARAMETERS>
												<ECUC-LINKER-SYMBOL-DEF UUID="c5558d41-eecc-4737-b4a7-2e0fd0ea945b">
													<SHORT-NAME>vLinkGenLinkerStackSectionName</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Name</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This parameter specifies the name of the Linker Section to be generated in the linker script.

Note:
Leading points (.) need not be specified here. They will be added by vLinkGen if necessary.
If sections with special values are required, a vLinkGenLinkerSpecSection can be used instead.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<ECUC-LINKER-SYMBOL-DEF-VARIANTS>
														<ECUC-LINKER-SYMBOL-DEF-CONDITIONAL>
															<DEFAULT-VALUE>stack</DEFAULT-VALUE>
														</ECUC-LINKER-SYMBOL-DEF-CONDITIONAL>
													</ECUC-LINKER-SYMBOL-DEF-VARIANTS>
												</ECUC-LINKER-SYMBOL-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="b877bcf7-4f52-47fc-bd22-759a7dc0b9d0">
													<SHORT-NAME>vLinkGenLinkerStackSectionAlignment</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Alignment</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the optional alignment of the Linker Section in bytes.
This allows the start address of the Linker Section to be aligned accordingly after linking.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">BYTE</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<ECUC-REFERENCE-DEF UUID="fd96917a-360e-487a-aaca-89cf471b2d82">
													<SHORT-NAME>vLinkGenLinkerStackSectionFlag</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Compiler Specific Flags</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Defines compiler specific flags to be generated for the Linker Section.

Note:
The flags to be selected are predefined and cannot be changed.
If no flags are selectable, the compiler either does not support flags on section level or the vLinkGen doesn't support them.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenPublishedInformation/vLinkGenVarSectionFlags/vLinkGenVarSectionFlag</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<ECUC-FOREIGN-REFERENCE-DEF UUID="03c329ad-7605-4d73-9fd1-315c8bcec759">
													<SHORT-NAME>vLinkGenLinkerStackSectionModule</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Module Ref</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Optional reference to the module which owns (requires) this Linker Section.

Note:
If this reference is set, the container is typically created by another module's generator.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<DESTINATION-TYPE>ECUC-MODULE-CONFIGURATION-VALUES</DESTINATION-TYPE>
												</ECUC-FOREIGN-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="0617fa65-db5e-4abb-86eb-1376948a8c39">
											<SHORT-NAME>vLinkGenLinkerSpecSection</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container includes all configuration parameters for a compiler specific Linker Section.

Note:
These sections are generated as configured, so the entered values must contain linker specific code. For details, see chapter "Sections with Special Values" of the Technical Reference.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildNotDeletable">true</SD>
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<PARAMETERS>
												<ECUC-STRING-PARAM-DEF UUID="9e6f22db-98aa-4fb8-ba22-e5de9a7621cf">
													<SHORT-NAME>vLinkGenLinkerSpecSectionValue</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Value</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This parameter specifies the compiler specific section value.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="4d0f6201-6011-46b4-8aac-2f0116915d18">
													<SHORT-NAME>vLinkGenLinkerSpecSectionAlignment</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Alignment</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the optional alignment of the Linker Section in bytes.
This allows the start address of the Linker Section to be aligned accordingly after linking.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">BYTE</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="a4c65c14-4289-4f66-97d2-4c1534d4b73d">
													<SHORT-NAME>vLinkGenLinkerSpecSectionPad</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Padding</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the optional padding (reserved size) of the Linker Section in bytes.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">BYTE</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<ECUC-REFERENCE-DEF UUID="904c4eb4-3acd-4bcc-a90a-ffd540070c48">
													<SHORT-NAME>vLinkGenLinkerSpecSectionFlag</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Compiler Specific Flags</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Defines compiler specific flags to be generated for the Linker Section.

Note:
The flags to be selected are predefined and cannot be changed.
If no flags are selectable, the compiler either does not support flags on section level or the vLinkGen doesn't support them.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenPublishedInformation/vLinkGenSpecSectionFlags/vLinkGenSpecSectionFlag</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<ECUC-FOREIGN-REFERENCE-DEF UUID="a5580389-43e3-4e39-9d45-d5db7bee5912">
													<SHORT-NAME>vLinkGenLinkerSpecSectionModule</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Module Ref</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Optional reference to the module which owns (requires) this Linker Section.

Note:
If this reference is set, the container is typically created by another module's generator.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<DESTINATION-TYPE>ECUC-MODULE-CONFIGURATION-VALUES</DESTINATION-TYPE>
												</ECUC-FOREIGN-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ab65a9ff-f823-411f-b21f-520210c41419">
									<SHORT-NAME>vLinkGenLogicalConstGroup</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container includes all configuration parameters for a Logical CONST Group.
Logical Groups are an abstraction layer to group multiple Section Groups and map them to a Memory Region Block.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildNotDeletable">true</SD>
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<PARAMETERS>
										<ECUC-INTEGER-PARAM-DEF UUID="6f281aef-1545-48ad-b39f-6df49f8f0163">
											<SHORT-NAME>vLinkGenConstGroupPosition</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Position</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Specifies the relative position of the Logical Group within all Logical Groups.
If multiple groups have the same position, they are sorted alphabetically by their container names.

Note:
With some compilers, the actual order of the Logical Group after linking may differ from the configured one.
This is a compiler limitation and cannot be influenced by vLinkGen.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>5</DEFAULT-VALUE>
											<MAX>4294967295</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="5b73e106-7dd6-424c-9d2e-376d370deb1a">
											<SHORT-NAME>vLinkGenConstGroupInitCore</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Init Core ID</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Specifies the physical core ID which shall perform the copying of all sections (configured as COPY_TO_RAM) assigned to the Section Group from ROM to RAM.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>4294967295</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<ECUC-REFERENCE-DEF UUID="1fc2ee6d-7403-4656-b77c-fb59f57c4c5f">
											<SHORT-NAME>vLinkGenConstGroupRegion</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Memory Region Block</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Reference to the ROM Memory Region Block where the Section Group should be linked to.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenMemoryRegion/vLinkGenMemoryRegionBlock</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<ECUC-REFERENCE-DEF UUID="c10c6794-0d8e-434d-9430-1dcc6186392b">
											<SHORT-NAME>vLinkGenConstGroupRamRegion</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Memory Region Block - RAM Copy</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Reference to the RAM Memory Region Block where the Section Group should be copied to at ECU startup.

Note:
Depending on this parameter, the vLinkGen only generates a structure which can be used to copy code or constants from ROM to RAM.
This is normally done by the startup code of the vBRS. For details please refer to the TechnicalReference.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenMemoryRegion/vLinkGenMemoryRegionBlock</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<ECUC-REFERENCE-DEF UUID="0a69152e-39d2-41b6-8d1a-c23b832d9cfa">
											<SHORT-NAME>vLinkGenConstGroupVariant</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Variants</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Reference to the linker script variants in which this group is to be generated.

Note:
If variants are configured but this group is not assigned to any, it applies to all of them.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenVariantHandling/vLinkGenVariant</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<ECUC-FOREIGN-REFERENCE-DEF UUID="b64cff49-832b-4637-9734-b48694bf12ff">
											<SHORT-NAME>vLinkGenConstGroupModule</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Module Ref</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Optional reference to the module which owns (requires) this Logical Group.

Note:
If this reference is set, the container is typically created by another module's generator.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-TYPE>ECUC-MODULE-CONFIGURATION-VALUES</DESTINATION-TYPE>
										</ECUC-FOREIGN-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="713f30b9-83ca-4caa-bda2-169b627ef9e2">
											<SHORT-NAME>vLinkGenConstSectionGroup</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies a group of Linker Sections whose content shall be linked into this Section Group.

Note:
Section Groups are comparable to so called output sections from (some) compiler manuals.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildNotDeletable">true</SD>
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<PARAMETERS>
												<ECUC-INTEGER-PARAM-DEF UUID="e9de6c3a-d906-44d5-973d-a50258fe5b6d">
													<SHORT-NAME>vLinkGenConstSectionGroupPosition</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Position</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Specifies the relative position of the Section Group within its parent Logical Group.
If multiple groups have the same position, they are sorted alphabetically by their container names.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="8059a6cc-ee09-4d99-a3c5-f4757b516d71">
													<SHORT-NAME>vLinkGenConstSectionGroupAlignment</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Alignment</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the optional start address alignment of the Section Group in bytes.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">BYTE</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>4</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="cb0bf2be-1db7-43a1-8cba-f44fac7cace9">
													<SHORT-NAME>vLinkGenConstSectionGroupEndAlignment</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">End Alignment</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the optional end (length) alignment of the Section Group in bytes.
This allows the generated END or LIMIT labels to be aligned accordingly.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">BYTE</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>4</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="d4de87e8-9701-4121-aadf-0a9eaf00faca">
													<SHORT-NAME>vLinkGenConstSectionGroupSize</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Group Size</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the optional size of the Section Group in bytes.
This can be used to keep an area free in the memory layout for groups without any Linker Sections (empty groups).</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">BYTE</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="412a475e-5dba-46bf-9027-563acbad6035">
													<SHORT-NAME>vLinkGenConstSectionGroupGapSize</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Gap Size</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the size of the optional gap to be kept free after the Section Group in bytes.
In order to correctly configure the MPU on some platforms, a certain area must be left free after the individual MPU regions. This area can be configured via this parameter.
The vLinkGen then generates the linker script in the appropriate way so the configured area after the section group is kept free after linking.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">BYTE</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-ENUMERATION-PARAM-DEF UUID="2f450cc3-5518-4b52-8518-a8762e38b1b3">
													<SHORT-NAME>vLinkGenConstSectionGroupInit</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Init Policy</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Specifies whether the Section Group shall be copied from ROM to RAM at ECU startup or not.
This is typically used to execute code from RAM.

NONE:
Default behavior for the CONST Section Group. No copying from ROM to RAM.

COPY_TO_RAM:
Copy Section Group from ROM to RAM at ECU startup.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>NONE</DEFAULT-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="f474191e-ad9e-43aa-a575-574def41dc07">
															<SHORT-NAME>NONE</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="4e2219ba-8dd6-4c31-8103-7348b9aaf4d4">
															<SHORT-NAME>COPY_TO_RAM</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<ECUC-ENUMERATION-PARAM-DEF UUID="a915d76d-1755-45f8-bfba-26b9816f31b0">
													<SHORT-NAME>vLinkGenConstSectionGroupInitStage</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Init Stage</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Specifies the stage at ECU startup for the copying of the Section Group.
This parameter is only evaluated if the Init Policy is set to COPY_TO_RAM.

NONE:
No initialization configured.

ZERO:
Always initialized first during the default vBRS startup routine.

ONE:
Always initialized second during the default vBRS startup routine.
By default, this value should be used if an initialization is required.

HARD_RESET_ONLY:
Initialized right after stage ONE. However, initialization is only done if a hardware reset occurred.

TWO, THREE:
Optional init stages, which are initialized accordingly after the HARD_RESET_ONLY stage. Shall only be used if really necessary.
One use case might be to reset the Wdg between the initialization of two stages.
</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>ONE</DEFAULT-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="80d86f94-ffeb-4aba-bf4f-ec909739db34">
															<SHORT-NAME>NONE</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="4d5cc140-c9df-45ee-8b70-3e64362432d5">
															<SHORT-NAME>ZERO</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="21bc90d8-90b3-430c-a89e-eb3d45fbdda6">
															<SHORT-NAME>ONE</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="307bdda7-da15-498e-b161-f31cfbd972b2">
															<SHORT-NAME>HARD_RESET_ONLY</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="d63240e3-ab5a-4ff5-afde-93da3cb133d6">
															<SHORT-NAME>TWO</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="ca6df577-9ebc-4a6d-ac0e-549f29a25b23">
															<SHORT-NAME>THREE</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<ECUC-CHOICE-REFERENCE-DEF UUID="f3aa9e28-141b-40d4-9e84-5688355844fe">
													<SHORT-NAME>vLinkGenConstSectionGroupRef</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Sections</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Reference to the sections which shall be linked into the Section Group.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<DESTINATION-REFS>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLinkerSections/vLinkGenLinkerCodeSection</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLinkerSections/vLinkGenLinkerConstSection</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLinkerSections/vLinkGenLinkerSpecSection</DESTINATION-REF>
													</DESTINATION-REFS>
												</ECUC-CHOICE-REFERENCE-DEF>
												<ECUC-CHOICE-REFERENCE-DEF UUID="1d57c78d-c0a3-4189-b707-bc1dad01efa2">
													<SHORT-NAME>vLinkGenConstSectionGroupFlag</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Compiler Specific Flags</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Defines compiler specific flags to be generated for the Section Group.

Note:
The flags to be selected are predefined and cannot be changed.
If none are selectable, the compiler either does not support flags on group level or the vLinkGen doesn't support them.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<DESTINATION-REFS>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenPublishedInformation/vLinkGenConstGroupFlags/vLinkGenConstGroupFlag</DESTINATION-REF>
													</DESTINATION-REFS>
												</ECUC-CHOICE-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="8b49ddd5-843c-439b-a745-971b7bf2f5c4">
									<SHORT-NAME>vLinkGenLogicalVarGroup</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container includes all configuration parameters for a Logical VAR Group.
Logical Groups are an abstraction layer to group multiple Section Groups and map them to a Memory Region Block.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildNotDeletable">true</SD>
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<PARAMETERS>
										<ECUC-INTEGER-PARAM-DEF UUID="39cfef63-abe5-4308-ab2b-ba4b18c57c8e">
											<SHORT-NAME>vLinkGenVarGroupPosition</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Position</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Specifies the relative position of the Logical Group within all Logical Groups.
If multiple groups have the same position, they are sorted alphabetically by their container names.

Note:
With some compilers, the actual order of the Logical Group after linking may differ from the configured one.
This is a compiler limitation and cannot be influenced by vLinkGen.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>5</DEFAULT-VALUE>
											<MAX>4294967295</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="187b4cba-71cb-4e5d-8577-df75fb614950">
											<SHORT-NAME>vLinkGenVarGroupInitCore</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Init Core ID</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Specifies the physical core ID which shall perform the initialization for all INIT or ZERO_INIT sections assigned to the Section Group.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>4294967295</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<ECUC-REFERENCE-DEF UUID="3cddb3c3-ee5b-4964-95ac-7d90dcabaf63">
											<SHORT-NAME>vLinkGenVarGroupRegion</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Memory Region Block</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Reference to the RAM Memory Region Block where the Section Group should be linked to.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenMemoryRegion/vLinkGenMemoryRegionBlock</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<ECUC-REFERENCE-DEF UUID="e1b48eab-1434-4092-8b3d-b08f423f3644">
											<SHORT-NAME>vLinkGenVarGroupRomRegion</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Memory Region Block - Init ROM Data</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Reference to the ROM Memory Region Block where the predefined data of the Section Group's variables should be linked to.
Typically used for initialization of INIT data (ROM to RAM copy).

Note:
Depending on this parameter, the vLinkGen only generates a structure which can be used to initialize configured Linker Sections with concrete data.
This is normally done by the startup code of the vBRS. For details please refer to the TechnicalReference.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenMemoryRegion/vLinkGenMemoryRegionBlock</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<ECUC-REFERENCE-DEF UUID="44ec7bf3-bb4d-4ce5-941e-90e26f74233b">
											<SHORT-NAME>vLinkGenVarGroupVariant</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Variants</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Reference to the linker script variants in which this group is to be generated.

Note:
If variants are configured but this group is not assigned to any, it applies to all of them.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenVariantHandling/vLinkGenVariant</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<ECUC-FOREIGN-REFERENCE-DEF UUID="0c1923e3-2fa4-4155-a551-3e9aa62efa8c">
											<SHORT-NAME>vLinkGenVarGroupModule</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Module Ref</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Optional reference to the module which owns (requires) this Logical Group.

Note:
If this reference is set, the container is typically created by another module's generator.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-TYPE>ECUC-MODULE-CONFIGURATION-VALUES</DESTINATION-TYPE>
										</ECUC-FOREIGN-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="25ad9ad6-9ddd-4504-8f22-8cbb10bc9fba">
											<SHORT-NAME>vLinkGenVarSectionGroup</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies a group of Linker Sections whose content shall be linked into this Section Group.

Note:
Section Groups are comparable to so called output sections from (some) compiler manuals.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildNotDeletable">true</SD>
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<PARAMETERS>
												<ECUC-INTEGER-PARAM-DEF UUID="7162d559-55e9-4e8c-993b-643d9085b7d8">
													<SHORT-NAME>vLinkGenVarSectionGroupPosition</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Position</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Specifies the relative position of the Section Group within its parent Logical Group.
If multiple groups have the same position, they are sorted alphabetically by their container names.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="f823dec9-f76a-4255-b67e-ea070b38345e">
													<SHORT-NAME>vLinkGenVarSectionGroupAlignment</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Alignment</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the optional start address alignment of the Section Group in bytes.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">BYTE</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>4</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="4852b354-a36e-474b-8853-3b792f27d17c">
													<SHORT-NAME>vLinkGenVarSectionGroupEndAlignment</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">End Alignment</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the optional end (length) alignment of the Section Group in bytes.
This allows the generated END or LIMIT labels to be aligned accordingly.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">BYTE</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>4</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="c4182c49-a2a5-4447-856e-1516e5a7e5f1">
													<SHORT-NAME>vLinkGenVarSectionGroupSize</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Group Size</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the optional size of the Section Group in bytes.
This can be used to keep an area free in the memory layout for groups without any Linker Sections (empty groups).</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">BYTE</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="eef518cc-a439-4b8e-899b-6a4ce28f9409">
													<SHORT-NAME>vLinkGenVarSectionGroupGapSize</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Gap Size</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the size of the optional gap to be kept free after the Section Group in bytes.
In order to correctly configure the MPU on some platforms, a certain area must be left free after the individual MPU regions. This area can be configured via this parameter.
The vLinkGen then generates the linker script in the appropriate way so the configured area after the section group is kept free after linking.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">BYTE</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-ENUMERATION-PARAM-DEF UUID="bc9d56fe-986a-4919-be00-01b67614db27">
													<SHORT-NAME>vLinkGenVarSectionGroupInit</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Init Policy</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Specifies whether the Section Group shall be initialized at ECU startup or not.

NONE:
No initialization required.

INIT:
Initialize all variable data within the Section Group with predefined values (ROM to RAM copy).

ZERO_INIT:
Initialize all variable data within the Section Group with completely with zeros.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>NONE</DEFAULT-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="a2af9e1a-bf6c-411f-88e4-101054870094">
															<SHORT-NAME>NONE</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="e2b2a329-6fd5-4456-bfbf-6ae2d4d3ba4c">
															<SHORT-NAME>INIT</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="a5e45ffd-7c89-4eb1-89a6-a8876b3a7a65">
															<SHORT-NAME>ZERO_INIT</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<ECUC-ENUMERATION-PARAM-DEF UUID="cbdc9e29-6ad3-493d-9829-33f52acf5b21">
													<SHORT-NAME>vLinkGenVarSectionGroupInitStage</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Init Stage</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Specifies the stage at ECU startup for the initialization of the Section Group.
This parameter is only evaluated if the Init Policy is set to value other than NONE.

NONE:
No initialization configured.

EARLY:
Initialize always directly after reset. Shall only be used if really necessary.
Since the PLL might not be initialized at that time, the initialization of large areas may take a long time.
Only supported for ZERO_INIT Section Groups.

ZERO:
Always initialized first during the default vBRS startup routine.

ONE:
Always initialized second during the default vBRS startup routine.
By default, this value should be used if an initialization is required.

HARD_RESET_ONLY:
Initialized right after stage ONE. However, initialization is only done if a hardware reset occurred.

TWO, THREE:
Optional init stages, which are initialized accordingly after the HARD_RESET_ONLY stage. Shall only be used if really necessary.
One use case might be to reset the Wdg between the initialization of two stages.
</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>ONE</DEFAULT-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="4aabe87e-ed3f-4931-9a9d-7ec80b64033d">
															<SHORT-NAME>NONE</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="c8565c8f-a5c8-403c-a8d9-f370b9bafa72">
															<SHORT-NAME>EARLY</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="15e158c6-6bfd-4d41-8295-6737beff184b">
															<SHORT-NAME>ZERO</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="6289615a-23d8-485a-8edc-e5507173eacf">
															<SHORT-NAME>ONE</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="251c87cf-2bfa-4ea7-8dc9-feda409db7e2">
															<SHORT-NAME>HARD_RESET_ONLY</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="e5783cd2-70b2-4207-a3cf-ea897f2a9486">
															<SHORT-NAME>TWO</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="0e20f057-6d5e-483d-bc43-277972a1e887">
															<SHORT-NAME>THREE</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<ECUC-CHOICE-REFERENCE-DEF UUID="5751946f-bec9-4c43-92ca-7f86e72172c1">
													<SHORT-NAME>vLinkGenVarSectionGroupRef</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Sections</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Reference to the sections which shall be linked into the Section Group.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<DESTINATION-REFS>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLinkerSections/vLinkGenLinkerVarSection</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLinkerSections/vLinkGenLinkerStackSection</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLinkerSections/vLinkGenLinkerSpecSection</DESTINATION-REF>
													</DESTINATION-REFS>
												</ECUC-CHOICE-REFERENCE-DEF>
												<ECUC-CHOICE-REFERENCE-DEF UUID="16954758-1aec-4579-8769-4c1a7e6c687e">
													<SHORT-NAME>vLinkGenVarSectionGroupFlag</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Compiler Specific Flags</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Defines compiler specific flags to be generated for the Section Group.

Note:
The flags to be selected are predefined and cannot be changed.
If none are selectable, the compiler either does not support flags on group level or the vLinkGen doesn't support them.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<DESTINATION-REFS>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenPublishedInformation/vLinkGenVarGroupFlags/vLinkGenVarGroupFlag</DESTINATION-REF>
													</DESTINATION-REFS>
												</ECUC-CHOICE-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="4e8e0822-f983-40b4-ae5c-2c30eeb6d77a">
									<SHORT-NAME>vLinkGenLogicalSymbolGroup</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container includes all configuration parameters for a Logical Symbol Group.
Logical Symbol Groups are an abstraction layer to group multiple Linker Symbols and generate them in the list of Logical Groups.

Note:
Some compilers expect global symbols to be defined at a certain position in the linker script. In this case the configured position is simply ignored.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildNotDeletable">true</SD>
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<PARAMETERS>
										<ECUC-INTEGER-PARAM-DEF UUID="9470f57f-fc97-4d2b-97a0-41e246a7dfce">
											<SHORT-NAME>vLinkGenSymbolGroupPosition</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Position</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Specifies the relative position of the Logical Group within all Logical Groups.
If multiple groups have the same position, they are sorted alphabetically by their container names.

Note:
With some compilers, the actual order of the Logical Group after linking may differ from the configured one.
This is a compiler limitation and cannot be influenced by vLinkGen.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>5</DEFAULT-VALUE>
											<MAX>4294967295</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<ECUC-REFERENCE-DEF UUID="7baa5367-ae78-4bea-baae-51308545d368">
											<SHORT-NAME>vLinkGenSymbolGroupVariant</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Variants</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Reference to the linker script variants in which this group is to be generated.

Note:
If variants are configured but this group is not assigned to any, it applies to all of them.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenVariantHandling/vLinkGenVariant</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<ECUC-FOREIGN-REFERENCE-DEF UUID="c06a72c0-fdf1-44bd-bb0f-7799f4093436">
											<SHORT-NAME>vLinkGenSymbolGroupModule</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Module Ref</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Optional reference to the module which owns (requires) this Logical Group.

Note:
If this reference is set, the container is typically created by another module's generator.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-TYPE>ECUC-MODULE-CONFIGURATION-VALUES</DESTINATION-TYPE>
										</ECUC-FOREIGN-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="815ffcb1-74d6-474e-a77c-c2751c0dcaee">
											<SHORT-NAME>vLinkGenLinkerSymbol</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container includes all configuration parameters for a compiler independent symbol definition which can have a name and a value.
That means it is possible to assign values to own symbols, and vLinkGen will generate these assignments in the appropriate syntax into the linker script.

Note:
Some compilers do not support the definition of global Linker Symbols at all or only to a limited extent.
This should be considered when using Linker Symbols. However, vLinkGen will issue warnings if a compiler does not support them or only to a limited extent.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildNotDeletable">true</SD>
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<PARAMETERS>
												<ECUC-INTEGER-PARAM-DEF UUID="bf59ab66-24a1-4988-9911-30e65d15d2e8">
													<SHORT-NAME>vLinkGenLinkerSymbolPosition</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Position</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Specifies the relative position of the Linker Symbol within its parent Logical Group.
If multiple symbols have the same position, they are sorted alphabetically by their container names.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-LINKER-SYMBOL-DEF UUID="ce606404-8ea4-4795-9878-28d344a8e4fb">
													<SHORT-NAME>vLinkGenLinkerSymbolName</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Name</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This parameter specifies the name of the symbol.

Note:
The symbol must be a valid linker symbol.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-LINKER-SYMBOL-DEF>
												<ECUC-STRING-PARAM-DEF UUID="f9e91d17-49be-445a-b3ec-35989fc7fa72">
													<SHORT-NAME>vLinkGenLinkerSymbolValue</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Value</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This parameter specifies the value that the symbol should get.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="6973e9c1-cf16-46f5-be08-019313cfe6a6">
									<SHORT-NAME>vLinkGenLogicalUserGroup</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container includes all configuration parameters for a Logical User Group.
Logical Groups are an abstraction layer to group multiple Section Groups and map them to a Memory Region Block.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildNotDeletable">true</SD>
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<PARAMETERS>
										<ECUC-INTEGER-PARAM-DEF UUID="f41e4a9d-884e-411a-b8ff-b0332bfe6028">
											<SHORT-NAME>vLinkGenUserGroupPosition</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Position</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Specifies the relative position of the Logical Group within all Logical Groups.
If multiple groups have the same position, they are sorted alphabetically by their container names.

Note:
With some compilers, the actual order of the Logical Group after linking may differ from the configured one.
This is a compiler limitation and cannot be influenced by vLinkGen.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>5</DEFAULT-VALUE>
											<MAX>4294967295</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-ENUMERATION-PARAM-DEF UUID="5aedaf13-5a69-43f0-bf22-03c25f6348c6">
											<SHORT-NAME>vLinkGenUserGroupPlacement</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Placement</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Specifies where the user code group should be generated in the linker script.

PRE:
The user code is generated at the beginning of the linker script.

POST:
The user code is generated at the end of the linker script.

GROUP_LIST:
The user code is generated within the Section Groups list at the specified position.
If set, the Memory Region Block reference need also to be set for some compilers.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>PRE</DEFAULT-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="9d554785-8701-4958-bdba-984464ee533b">
													<SHORT-NAME>PRE</SHORT-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="d7c08a44-4f8c-4899-a774-8ed19fd3f352">
													<SHORT-NAME>POST</SHORT-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="efaa4980-e11d-458c-88ba-dfa01746cbca">
													<SHORT-NAME>GROUP_LIST</SHORT-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<ECUC-REFERENCE-DEF UUID="9371b3bd-09d7-4299-9905-481bd08f2bc2">
											<SHORT-NAME>vLinkGenUserGroupRegion</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Memory Region Block</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Reference to the Memory Region Block where the user Section Group should be linked to.

Note:
Only applicable if, vLinkGenUserGroupPlacement is set to GROUP_LIST and the compiler requires the configuration.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenMemoryRegion/vLinkGenMemoryRegionBlock</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<ECUC-REFERENCE-DEF UUID="af403cbc-13af-4c42-8177-f564bd10de01">
											<SHORT-NAME>vLinkGenUserGroupVariant</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Variants</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Reference to the linker script variants in which this group is to be generated.

Note:
If variants are configured but this group is not assigned to any, it applies to all of them.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenVariantHandling/vLinkGenVariant</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<ECUC-FOREIGN-REFERENCE-DEF UUID="b4dfb785-ab0d-44b8-87f9-ec80de7ac902">
											<SHORT-NAME>vLinkGenUserGroupModule</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Module Ref</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Optional reference to the module which owns (requires) this Logical Group.

Note:
If this reference is set, the container is typically created by another module's generator.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-TYPE>ECUC-MODULE-CONFIGURATION-VALUES</DESTINATION-TYPE>
										</ECUC-FOREIGN-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="68364a08-4209-43f6-b6ad-e1ade434e36a">
											<SHORT-NAME>vLinkGenUserSectionGroup</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies an user code section that is generated into the linker script at the specified position.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildNotDeletable">true</SD>
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<PARAMETERS>
												<ECUC-INTEGER-PARAM-DEF UUID="654f97b7-0899-499c-91fd-d50f4486fb41">
													<SHORT-NAME>vLinkGenUserSectionGroupPosition</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Position</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Specifies the relative position of the Section Group within its parent Logical Group.
If multiple groups have the same position, they are sorted alphabetically by their container names.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-MULTILINE-STRING-PARAM-DEF UUID="df8dbbd8-aa6a-4065-9249-312ce6082125">
													<SHORT-NAME>vLinkGenUserSectionGroupValue</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">User Code</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Specifies the compiler specific user code that is directly generated into the linker script.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
												</ECUC-MULTILINE-STRING-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="b5e4333c-3c89-4262-a6ee-433bce751d7f">
									<SHORT-NAME>vLinkGenMemoryRegion</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container includes all configuration parameters for one Memory Region.
Memory Regions are a logical abstraction layer to separate the configuration from the actual memory layout and to make it hardware independent.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildNotDeletable">true</SD>
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<PARAMETERS>
										<ECUC-INTEGER-PARAM-DEF UUID="e18ca48d-c067-4e67-9676-fafd6f439169">
											<SHORT-NAME>vLinkGenMemoryRegionAddrOffset</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Offset</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Specifies the offset of the memory region to the referenced hardware region start address.

The resulting start address of the Memory Region is calculated from the start address of the hardware memory area in vBaseEnv and this offset value.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:DefaultFormat">HEX</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>4294967295</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="8f69cfe8-8d7f-4d6a-a49a-5f9f293adc70">
											<SHORT-NAME>vLinkGenMemoryRegionSize</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Size</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">This value specifies the size of the Memory Region in bytes.

A hardware memory area can be splitted into multiple Memory Regions in vLinkGen.
In this the sizes of all Memory Regions must not exceed the size of the actual hardware memory area.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:DefaultFormat">HEX</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>4294967295</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="327e4cd6-b8a2-4c03-913e-da9741f45889">
											<SHORT-NAME>vLinkGenMemoryRegionAddress</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Calculated Start Address</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Shows the calculated start address of the Memory Region.

The displayed value is the actual start address of the Memory Region after linking.
It is calculated from the start address of the hardware memory area in vBaseEnv and the address offset of the Memory Region.

Caution:
This parameter is for information only and must not be changed manually.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:DefaultFormat">HEX</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>4294967295</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<ECUC-REFERENCE-DEF UUID="1293eae4-9fe7-4b3b-9521-2059d7de5ceb">
											<SHORT-NAME>vLinkGenMemoryRegionHwRegion</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Hardware Memory Area</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Reference to the hardware memory area where the Memory Region should be mapped to.

The hardware memory areas are predefined in the vBaseEnv and cannot be changed.
They reflect the actual memory layout in the hardware.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vBaseEnv/vBaseEnvGeneral/vBaseEnvMemLayoutHwRegion</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="df9c02a1-2077-423f-bed0-4840b6904709">
											<SHORT-NAME>vLinkGenMemoryRegionBlock</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container includes all configuration parameters for a Memory Region Block.
Memory Region Blocks can be used to partition a Memory Region. They are used for the actual memory definitions in the generated linker script.

Note:
The position of a Memory Region Block in the memory area is determined by its position, size and boundary.
The actual start addresses of several blocks within a Memory Region are calculated using these parameters.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildNotDeletable">true</SD>
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<PARAMETERS>
												<ECUC-INTEGER-PARAM-DEF UUID="b3303934-13e7-4bb9-ab01-e518d00fa0d5">
													<SHORT-NAME>vLinkGenMemoryRegionBlockPosition</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Position</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Specifies the relative position of the Memory Region Block within its parent Memory Region.
If multiple blocks have the same position, they are sorted alphabetically by their container names.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:DefaultFormat">DEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="800b2308-fb88-4aed-849f-a57ec4658829">
													<SHORT-NAME>vLinkGenMemoryRegionBlockSize</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Size</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value specifies the size of the Memory Region Block in bytes.

A Memory Region can be splitted into multiple Memory Region Blocks.
In this the sizes of all blocks must not exceed the size of the parent Memory Region.
														</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:DefaultFormat">HEX</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="a7d596ff-0f27-4443-a45f-1899a261e600">
													<SHORT-NAME>vLinkGenMemoryRegionBlockAddress</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Calculated Start Address</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Shows the calculated start address of the Memory Region Block.

The displayed value is the actual start address of the Memory Region Block after linking.
It is derived from the calculated start address of its parent Memory Region and its own boundary and position.

Caution:
This parameter is for information only and must not be changed manually.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:DefaultFormat">HEX</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-ENUMERATION-PARAM-DEF UUID="802bd1fd-1dc2-4020-a6bc-e81a313684c2">
													<SHORT-NAME>vLinkGenMemoryRegionBlockBoundary</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Boundary</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This value defines the location of the memory area block within its parent memory area.

Memory Region Blocks can be mapped either to the lower (lower address value) or upper (higher address value) end of the Memory Region.
Their start address is then calculated based on the boundary value, their position and the size of their preceding blocks.
This can be useful if, for example, between two Memory Region Blocks within a Memory Region an area in memory is to be left free.

LOWER:
The Memory Region Block is linked to the lower boundary of the Memory Region.

UPPER:
The Memory Region Block is linked to the upper boundary of the Memory Region.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>LOWER</DEFAULT-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="9fcfd76b-6ee1-439a-858e-ff7ae45373da">
															<SHORT-NAME>LOWER</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="eee79fcd-6198-4239-a30f-fdabd95569a2">
															<SHORT-NAME>UPPER</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<ECUC-ENUMERATION-PARAM-DEF UUID="64acba45-2880-4fd7-a131-faf2be46b417">
													<SHORT-NAME>vLinkGenMemoryRegionBlockInitStage</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Init Stage</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines whether the complete Memory Region Block shall be initialized with zeros and when.
This is typically required for ECC protected memory areas before the first read or write access.

NONE:
No initialization required.

EARLY:
Initialize always directly after reset. Shall only be used if really necessary.
Since the PLL might not be initialized at that time, the initialization of large areas may take a long time.

ONE:
Always initialized second during the default vBRS startup routine.
By default, this value should be used if an initialization is required.

HARD_RESET_ONLY:
Initialized right after stage ONE. However, initialization is only done if a hardware reset occurred.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>NONE</DEFAULT-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="2172d6d0-1a7b-41df-8d94-502e2221232e">
															<SHORT-NAME>NONE</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="d40a12ef-39a6-4c67-9fcf-51a15fe12916">
															<SHORT-NAME>EARLY</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="9c9225ae-c97c-4c1b-ac61-943183731ef7">
															<SHORT-NAME>ONE</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="f6887eec-0cd1-4fe1-9d0d-0ace7fc090b1">
															<SHORT-NAME>HARD_RESET_ONLY</SHORT-NAME>
															<ORIGIN>Vector Informatik</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="1598ebbf-97f2-4c2a-999f-eec67aad5ea7">
													<SHORT-NAME>vLinkGenMemoryRegionBlockInitCore</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Init Core ID</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Specifies the physical core ID which shall perform the zero initialization of the Memory Region Block.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967295</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<ECUC-REFERENCE-DEF UUID="11a14ac7-e7c5-4f2c-8ca1-7121c9e35468">
													<SHORT-NAME>vLinkGenMemoryRegionBlockFlag</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Compiler Specific Flags</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Defines compiler specific flags to be generated for the Memory Region Block.

Note:
The flags to be selected are predefined and cannot be changed.
If none are selectable, the compiler either does not support flags on Memory Region Block level or the vLinkGen doesn't support them.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenPublishedInformation/vLinkGenRegionBlockFlags/vLinkGenRegionBlockFlag</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</SUB-CONTAINERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="244f13d8-6387-43bf-8e40-55bb08b37456">
							<SHORT-NAME>vLinkGenPublishedInformation</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Contains additional published compiler specific informations.

Caution:
This container (and all including sub-elements) must not be changed manually.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildNotDeletable">true</SD>
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<MULTIPLICITY-CONFIG-CLASSES>
								<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
									<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
								</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
							</MULTIPLICITY-CONFIG-CLASSES>
							<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
							<REQUIRES-INDEX>false</REQUIRES-INDEX>
							<PARAMETERS>
								<ECUC-INTEGER-PARAM-DEF UUID="5701aaf4-98b1-40cc-a591-4183a3033d3f">
									<SHORT-NAME>vLinkGenGeneration</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Defines the internal vLinkGen code generation.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0</DEFAULT-VALUE>
									<MAX>4294967295</MAX>
									<MIN>0</MIN>
								</ECUC-INTEGER-PARAM-DEF>
								<ECUC-INTEGER-PARAM-DEF UUID="c7e03d89-092e-4b1b-9c45-970619ff849a">
									<SHORT-NAME>vLinkGenDefaultConstGroupAlignment</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Default CONST Group Start Alignment</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Specifies the optional default start address alignment for all CONST Section Groups in bytes.

Note:
It is only used for newly created groups that have no predefined values (e.g. recommended configuration).
It can, however, be changed in the groups later.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0</DEFAULT-VALUE>
									<MAX>4294967295</MAX>
									<MIN>0</MIN>
								</ECUC-INTEGER-PARAM-DEF>
								<ECUC-INTEGER-PARAM-DEF UUID="3166b268-6c30-476f-af02-8ce7ea49b00e">
									<SHORT-NAME>vLinkGenDefaultVarGroupAlignment</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Default VAR Group Start Alignment</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Specifies the optional default start address alignment for all VAR Section Groups in bytes.

Note:
It is only used for newly created groups that have no predefined values (e.g. recommended configuration).
It can, however, be changed in the groups later.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0</DEFAULT-VALUE>
									<MAX>4294967295</MAX>
									<MIN>0</MIN>
								</ECUC-INTEGER-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="8a74bf37-0320-4e49-9e30-6371157df14f">
									<SHORT-NAME>vLinkGenDuplicatedLabels</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Duplicated Labels</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Specifies whether the vLinkGen shall generate duplicated labels or not.

true: Generate additional labels with double underscores ("__") at the beginning
false: Normal label generation. No duplicated labels</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
							</PARAMETERS>
							<SUB-CONTAINERS>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="a9f18960-a32a-47c5-843e-86c5e4c58302">
									<SHORT-NAME>vLinkGenRegionBlockFlags</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Collection of compiler specific Memory Region Block flags.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildNotDeletable">true</SD>
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SUB-CONTAINERS>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="6e3bdae1-be72-498f-ae9f-b1146b8f0f0d">
											<SHORT-NAME>vLinkGenRegionBlockFlag</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Definition of a compiler specific Memory Region Block flag.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildNotDeletable">true</SD>
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="c6781e24-acae-47c1-8647-4f2bf9639a53">
									<SHORT-NAME>vLinkGenConstGroupFlags</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Collection of compiler specific Section Group flags.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildNotDeletable">true</SD>
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SUB-CONTAINERS>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="c35002ce-db9a-4af4-90d3-c1e9c55b6f17">
											<SHORT-NAME>vLinkGenConstGroupFlag</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Definition of a compiler specific Section Group flag.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildNotDeletable">true</SD>
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="a3defa47-ad5e-4292-8b75-35522b6d2f6d">
									<SHORT-NAME>vLinkGenConstSectionFlags</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Collection of compiler specific Linker Section flags.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildNotDeletable">true</SD>
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SUB-CONTAINERS>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="2dc3e3ec-e69d-4b61-af6e-6e76d627aa7b">
											<SHORT-NAME>vLinkGenConstSectionFlag</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Definition of a compiler specific Linker Section flag.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildNotDeletable">true</SD>
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="94d980f4-e280-4d58-980c-934c43772861">
									<SHORT-NAME>vLinkGenVarGroupFlags</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Collection of compiler specific Section Group flags.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildNotDeletable">true</SD>
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SUB-CONTAINERS>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="d42af726-d14a-4128-a70c-7bb7ac4d7163">
											<SHORT-NAME>vLinkGenVarGroupFlag</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Definition of a compiler specific Section Group flag.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildNotDeletable">true</SD>
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="1d207e60-d361-4801-a650-41c6e0a69304">
									<SHORT-NAME>vLinkGenVarSectionFlags</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Collection of compiler specific Linker Section flags.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildNotDeletable">true</SD>
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SUB-CONTAINERS>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="b7f8b083-dfb2-4753-ad43-1ead3f5dfa82">
											<SHORT-NAME>vLinkGenVarSectionFlag</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Definition of a compiler specific Linker Section flag.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildNotDeletable">true</SD>
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="74a5b7b0-d4aa-4218-9ad7-044fa507a5a1">
									<SHORT-NAME>vLinkGenSpecSectionFlags</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Collection of compiler specific Linker Section flags.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildNotDeletable">true</SD>
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SUB-CONTAINERS>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="1df25bf0-f6db-4aac-97c3-ca148ad0d57f">
											<SHORT-NAME>vLinkGenSpecSectionFlag</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Definition of a compiler specific Linker Section flag.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildNotDeletable">true</SD>
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</SUB-CONTAINERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="8113f31a-2d7f-47f0-86b1-5359359105ac">
							<SHORT-NAME>vLinkGenVariantHandling</SHORT-NAME>
							<DESC>
								<L-2 L="EN">This container contains all configuration parameters for the variant handling.

Caution:
If variants are configured, exactly one of them must always be defined in a build process.
For example, by passing it as compiler and/or linker option (e.g. -D&lt;VariantName&gt;).</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildNotDeletable">true</SD>
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<MULTIPLICITY-CONFIG-CLASSES>
								<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
									<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
								</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
							</MULTIPLICITY-CONFIG-CLASSES>
							<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
							<REQUIRES-INDEX>false</REQUIRES-INDEX>
							<SUB-CONTAINERS>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="6ffe8e03-bd32-4960-a13c-bdfffcc0a42c">
									<SHORT-NAME>vLinkGenVariant</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container specifies one linker script variant.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildNotDeletable">true</SD>
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</SUB-CONTAINERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
					</CONTAINERS>
				</ECUC-MODULE-DEF>
			</ELEMENTS>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>