<?xml version="1.0" encoding="UTF-8"?>
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-2-1.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://autosar.org/schema/r4.0">
	<AR-PACKAGES>
		<AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
			<SHORT-NAME>MICROSAR</SHORT-NAME>
			<ELEMENTS>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="acc8892f-4b5a-4dd1-85fc-3d4d603dee9b">
					<SHORT-NAME>vLinkGen_Rec</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/vLinkGen</DEFINITION-REF>
					<CONTAINERS>
						<ECUC-CONTAINER-VALUE UUID="069ae388-7d3f-480b-b9f2-13326771d7ee">
							<SHORT-NAME>vLinkGenMemLayout</SHORT-NAME>
							<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout</DEFINITION-REF>
							<SUB-CONTAINERS>
								<ECUC-CONTAINER-VALUE UUID="812706e9-8c6f-4b81-b526-308ac2ada5a9">
									<SHORT-NAME>vLinkGenLinkerSections</SHORT-NAME>
									<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLinkerSections</DEFINITION-REF>
									<SUB-CONTAINERS>
										<ECUC-CONTAINER-VALUE UUID="0ae51534-487b-4c81-9e4b-43961f28f9d4">
											<SHORT-NAME>text</SHORT-NAME>
											<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLinkerSections/vLinkGenLinkerSpecSection</DEFINITION-REF>
											<PARAMETER-VALUES>
												<ECUC-TEXTUAL-PARAM-VALUE>
													<DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLinkerSections/vLinkGenLinkerSpecSection/vLinkGenLinkerSpecSectionValue</DEFINITION-REF>
													<VALUE>.text</VALUE>
												</ECUC-TEXTUAL-PARAM-VALUE>
											</PARAMETER-VALUES>
										</ECUC-CONTAINER-VALUE>
										<ECUC-CONTAINER-VALUE UUID="a1dc275e-75f3-49b3-bf94-1b77f115688d">
											<SHORT-NAME>rodata</SHORT-NAME>
											<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLinkerSections/vLinkGenLinkerSpecSection</DEFINITION-REF>
											<PARAMETER-VALUES>
												<ECUC-TEXTUAL-PARAM-VALUE>
													<DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLinkerSections/vLinkGenLinkerSpecSection/vLinkGenLinkerSpecSectionValue</DEFINITION-REF>
													<VALUE>.const</VALUE>
												</ECUC-TEXTUAL-PARAM-VALUE>
											</PARAMETER-VALUES>
										</ECUC-CONTAINER-VALUE>
										<ECUC-CONTAINER-VALUE UUID="fc3fec3e-cd05-4a89-a332-36e6478956d0">
											<SHORT-NAME>data</SHORT-NAME>
											<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLinkerSections/vLinkGenLinkerSpecSection</DEFINITION-REF>
											<PARAMETER-VALUES>
												<ECUC-TEXTUAL-PARAM-VALUE>
													<DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLinkerSections/vLinkGenLinkerSpecSection/vLinkGenLinkerSpecSectionValue</DEFINITION-REF>
													<VALUE>.data</VALUE>
												</ECUC-TEXTUAL-PARAM-VALUE>
											</PARAMETER-VALUES>
										</ECUC-CONTAINER-VALUE>
										<ECUC-CONTAINER-VALUE UUID="60a3f638-7221-49b3-b42d-b5b438bac924">
											<SHORT-NAME>bss</SHORT-NAME>
											<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLinkerSections/vLinkGenLinkerSpecSection</DEFINITION-REF>
											<PARAMETER-VALUES>
												<ECUC-TEXTUAL-PARAM-VALUE>
													<DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLinkerSections/vLinkGenLinkerSpecSection/vLinkGenLinkerSpecSectionValue</DEFINITION-REF>
													<VALUE>.bss</VALUE>
												</ECUC-TEXTUAL-PARAM-VALUE>
											</PARAMETER-VALUES>
										</ECUC-CONTAINER-VALUE>
										<ECUC-CONTAINER-VALUE UUID="224a27cf-f9c8-47f9-97c7-c314df312003">
											<SHORT-NAME>stack</SHORT-NAME>
											<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLinkerSections/vLinkGenLinkerSpecSection</DEFINITION-REF>
											<PARAMETER-VALUES>
												<ECUC-TEXTUAL-PARAM-VALUE>
													<DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLinkerSections/vLinkGenLinkerSpecSection/vLinkGenLinkerSpecSectionValue</DEFINITION-REF>
													<VALUE>.stack</VALUE>
												</ECUC-TEXTUAL-PARAM-VALUE>
											</PARAMETER-VALUES>
										</ECUC-CONTAINER-VALUE>
										<ECUC-CONTAINER-VALUE UUID="62a5774e-e592-4ff7-a4aa-6d1736df9a5b">
											<SHORT-NAME>heap</SHORT-NAME>
											<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLinkerSections/vLinkGenLinkerSpecSection</DEFINITION-REF>
											<PARAMETER-VALUES>
												<ECUC-TEXTUAL-PARAM-VALUE>
													<DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLinkerSections/vLinkGenLinkerSpecSection/vLinkGenLinkerSpecSectionValue</DEFINITION-REF>
													<VALUE>.heap</VALUE>
												</ECUC-TEXTUAL-PARAM-VALUE>
											</PARAMETER-VALUES>
										</ECUC-CONTAINER-VALUE>
									</SUB-CONTAINERS>
								</ECUC-CONTAINER-VALUE>
								<ECUC-CONTAINER-VALUE UUID="c18c9744-248f-471f-aac8-63b7b1a08733">
									<SHORT-NAME>Const_Default</SHORT-NAME>
									<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLogicalConstGroup</DEFINITION-REF>
									<PARAMETER-VALUES>
										<ECUC-NUMERICAL-PARAM-VALUE>
											<DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLogicalConstGroup/vLinkGenConstGroupPosition</DEFINITION-REF>
											<VALUE>1000</VALUE>
										</ECUC-NUMERICAL-PARAM-VALUE>
									</PARAMETER-VALUES>
									<SUB-CONTAINERS>
										<ECUC-CONTAINER-VALUE UUID="c2209172-d8e9-44fe-802d-a430580493f1">
											<SHORT-NAME>text</SHORT-NAME>
											<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLogicalConstGroup/vLinkGenConstSectionGroup</DEFINITION-REF>
											<REFERENCE-VALUES>
												<ECUC-REFERENCE-VALUE>
													<DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLogicalConstGroup/vLinkGenConstSectionGroup/vLinkGenConstSectionGroupRef</DEFINITION-REF>
													<VALUE-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/vLinkGen_Rec/vLinkGenMemLayout/vLinkGenLinkerSections/text</VALUE-REF>
												</ECUC-REFERENCE-VALUE>
											</REFERENCE-VALUES>
										</ECUC-CONTAINER-VALUE>
										<ECUC-CONTAINER-VALUE UUID="df4c874d-cd08-42c6-af13-87caa5eb530b">
											<SHORT-NAME>rodata</SHORT-NAME>
											<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLogicalConstGroup/vLinkGenConstSectionGroup</DEFINITION-REF>
											<REFERENCE-VALUES>
												<ECUC-REFERENCE-VALUE>
													<DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLogicalConstGroup/vLinkGenConstSectionGroup/vLinkGenConstSectionGroupRef</DEFINITION-REF>
													<VALUE-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/vLinkGen_Rec/vLinkGenMemLayout/vLinkGenLinkerSections/rodata</VALUE-REF>
												</ECUC-REFERENCE-VALUE>
											</REFERENCE-VALUES>
										</ECUC-CONTAINER-VALUE>
									</SUB-CONTAINERS>
								</ECUC-CONTAINER-VALUE>
								<ECUC-CONTAINER-VALUE UUID="74072a70-ae33-4b7c-8ee3-ed50b3c1a3ed">
									<SHORT-NAME>Data_Default</SHORT-NAME>
									<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLogicalVarGroup</DEFINITION-REF>
									<PARAMETER-VALUES>
										<ECUC-NUMERICAL-PARAM-VALUE>
											<DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLogicalVarGroup/vLinkGenVarGroupPosition</DEFINITION-REF>
											<VALUE>1000</VALUE>
										</ECUC-NUMERICAL-PARAM-VALUE>
									</PARAMETER-VALUES>
									<SUB-CONTAINERS>
										<ECUC-CONTAINER-VALUE UUID="ebbc39c3-bc11-4c3f-8ba1-15e1b2f1c1b5">
											<SHORT-NAME>data</SHORT-NAME>
											<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLogicalVarGroup/vLinkGenVarSectionGroup</DEFINITION-REF>
											<PARAMETER-VALUES>
												<ECUC-TEXTUAL-PARAM-VALUE>
													<DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLogicalVarGroup/vLinkGenVarSectionGroup/vLinkGenVarSectionGroupInit</DEFINITION-REF>
													<VALUE>INIT</VALUE>
												</ECUC-TEXTUAL-PARAM-VALUE>
											</PARAMETER-VALUES>
											<REFERENCE-VALUES>
												<ECUC-REFERENCE-VALUE>
													<DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLogicalVarGroup/vLinkGenVarSectionGroup/vLinkGenVarSectionGroupRef</DEFINITION-REF>
													<VALUE-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/vLinkGen_Rec/vLinkGenMemLayout/vLinkGenLinkerSections/data</VALUE-REF>
												</ECUC-REFERENCE-VALUE>
											</REFERENCE-VALUES>
										</ECUC-CONTAINER-VALUE>
										<ECUC-CONTAINER-VALUE UUID="afe3f787-80c3-44a4-a613-6b92dd93b5dc">
											<SHORT-NAME>bss</SHORT-NAME>
											<DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLogicalVarGroup/vLinkGenVarSectionGroup</DEFINITION-REF>
											<PARAMETER-VALUES>
												<ECUC-TEXTUAL-PARAM-VALUE>
													<DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLogicalVarGroup/vLinkGenVarSectionGroup/vLinkGenVarSectionGroupInit</DEFINITION-REF>
													<VALUE>ZERO_INIT</VALUE>
												</ECUC-TEXTUAL-PARAM-VALUE>
											</PARAMETER-VALUES>
											<REFERENCE-VALUES>
												<ECUC-REFERENCE-VALUE>
													<DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLogicalVarGroup/vLinkGenVarSectionGroup/vLinkGenVarSectionGroupRef</DEFINITION-REF>
													<VALUE-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/vLinkGen_Rec/vLinkGenMemLayout/vLinkGenLinkerSections/bss</VALUE-REF>
												</ECUC-REFERENCE-VALUE>
												<ECUC-REFERENCE-VALUE>
													<DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLogicalVarGroup/vLinkGenVarSectionGroup/vLinkGenVarSectionGroupRef</DEFINITION-REF>
													<VALUE-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/vLinkGen_Rec/vLinkGenMemLayout/vLinkGenLinkerSections/stack</VALUE-REF>
												</ECUC-REFERENCE-VALUE>
												<ECUC-REFERENCE-VALUE>
													<DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/MICROSAR/vLinkGen/vLinkGenMemLayout/vLinkGenLogicalVarGroup/vLinkGenVarSectionGroup/vLinkGenVarSectionGroupRef</DEFINITION-REF>
													<VALUE-REF DEST="BSW-MODULE-ENTRY">/MICROSAR/vLinkGen_Rec/vLinkGenMemLayout/vLinkGenLinkerSections/heap</VALUE-REF>
												</ECUC-REFERENCE-VALUE>
											</REFERENCE-VALUES>
										</ECUC-CONTAINER-VALUE>
									</SUB-CONTAINERS>
								</ECUC-CONTAINER-VALUE>
							</SUB-CONTAINERS>
						</ECUC-CONTAINER-VALUE>
					</CONTAINERS>
				</ECUC-MODULE-CONFIGURATION-VALUES>
			</ELEMENTS>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>