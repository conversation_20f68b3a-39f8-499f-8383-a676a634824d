############################################################################### 
# File Name  : vLinkGen_rules.mak
# Description: Rules makefile
#------------------------------------------------------------------------------
# COPYRIGHT
#------------------------------------------------------------------------------
# Copyright (c) 2020 by Vector Informatik GmbH.  All rights reserved.
#------------------------------------------------------------------------------
# REVISION HISTORY
#------------------------------------------------------------------------------
# Version   Date        Author  Description
#------------------------------------------------------------------------------
# 1.00.00   2007-06-13  visaba  Initial Version of Template (1.0)
# 1.01.00   2017-05-30  vismas  Clean-up
# 1.02.00   2019-01-22  vircbl  Added support of component-based SIP structure
#------------------------------------------------------------------------------
# TemplateVersion = 1.02
###############################################################################

# Component Files
CC_FILES_TO_BUILD       += 
GENERATED_SOURCE_FILES  += $(GENDATA_DIR)\vLinkGen_Lcfg.c

# Library Settings
LIBRARIES_TO_BUILD      += 
vLinkGen_FILES          += 

