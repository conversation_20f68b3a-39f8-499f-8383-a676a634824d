<?xml version="1.0" encoding="UTF-8"?>
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-3-0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://autosar.org/schema/r4.0">
	<AR-PACKAGES>
		<AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
			<SHORT-NAME>MICROSAR</SHORT-NAME>
			<ELEMENTS>
				<BSW-IMPLEMENTATION UUID="f31abfdd-507b-42af-8f51-688c536289c0">
					<SHORT-NAME>vSet_Impl</SHORT-NAME>
					<PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
					<USED-CODE-GENERATOR>DaVin<PERSON> Configurator</USED-CODE-GENERATOR>
					<VENDOR-ID>30</VENDOR-ID>
					<AR-RELEASE-VERSION>4.03.00</AR-RELEASE-VERSION>
					<PRECONFIGURED-CONFIGURATION-REFS>
						<PRECONFIGURED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/vSet_Pre</PRECONFIGURED-CONFIGURATION-REF>
					</PRECONFIGURED-CONFIGURATION-REFS>
					<RECOMMENDED-CONFIGURATION-REFS>
						<RECOMMENDED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/vSet_Rec</RECOMMENDED-CONFIGURATION-REF>
					</RECOMMENDED-CONFIGURATION-REFS>
					<VENDOR-SPECIFIC-MODULE-DEF-REFS>
						<VENDOR-SPECIFIC-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/MICROSAR/vSet</VENDOR-SPECIFIC-MODULE-DEF-REF>
					</VENDOR-SPECIFIC-MODULE-DEF-REFS>
				</BSW-IMPLEMENTATION>
				<ECUC-MODULE-DEF UUID="c4d6e280-1fca-43b1-8dd2-d622ab48ee13">
					<SHORT-NAME>vSet</SHORT-NAME>
					<LONG-NAME>
						<L-4 L="EN">Vector Common Settings</L-4>
					</LONG-NAME>
					<CATEGORY>VENDOR_SPECIFIC_MODULE_DEFINITION</CATEGORY>
					<ADMIN-DATA>
						<DOC-REVISIONS>
							<DOC-REVISION>
								<REVISION-LABEL>1.00.00</REVISION-LABEL>
								<ISSUED-BY>visms</ISSUED-BY>
								<DATE>2017-02-20</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">FEAT-2365: Support standalone distribution of AR4.3 SecOC</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">FEATC-1188</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>2.00.00</REVISION-LABEL>
								<ISSUED-BY>visms</ISSUED-BY>
								<DATE>2018-03-26T14:22:20+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">STORYC-4057: Provide CommonAsr_vSet in the Product</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">STORYC-4057</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>2.00.00</REVISION-LABEL>
								<ISSUED-BY>visms</ISSUED-BY>
								<DATE>2018-03-26T14:48:29+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Address parameter for virtual memory segment</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">STORYC-2994</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
						</DOC-REVISIONS>
					</ADMIN-DATA>
					<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
					<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
					<POST-BUILD-VARIANT-SUPPORT>true</POST-BUILD-VARIANT-SUPPORT>
					<SUPPORTED-CONFIG-VARIANTS>
						<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
					</SUPPORTED-CONFIG-VARIANTS>
					<CONTAINERS>
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="2a8265a4-bf89-4189-ae04-6fbf5b5dd00e">
							<SHORT-NAME>vSetGeneral</SHORT-NAME>
							<DESC>
								<L-2 L="EN">General parameters.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildNotDeletable">true</SD>
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<MULTIPLICITY-CONFIG-CLASSES>
								<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
									<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
								</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
							</MULTIPLICITY-CONFIG-CLASSES>
							<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
							<REQUIRES-INDEX>false</REQUIRES-INDEX>
							<PARAMETERS>
								<ECUC-ENUMERATION-PARAM-DEF UUID="f691fcc2-b1ef-4e27-99d4-80cf10db8943">
									<SHORT-NAME>vSetDummyStatementKind</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Dummy statements used to avoid compiler warnings about e.g. unused parameters.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>ECU</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>None</DEFAULT-VALUE>
									<LITERALS>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="b65e6ecd-3dc3-46be-a939-ae11ca023f2b">
											<SHORT-NAME>None</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">None</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="6ad6d886-9a6c-4d5c-9531-792324a42ea5">
											<SHORT-NAME>VoidCast</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">VoidCast</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="366ffa71-6836-4a24-b83b-119ab0f7acfa">
											<SHORT-NAME>SelfAssignment</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">SelfAssignment</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="70b19098-7ea4-48d9-9d3a-20f958a717cb">
											<SHORT-NAME>UserDefined</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">UserDefined</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
									</LITERALS>
								</ECUC-ENUMERATION-PARAM-DEF>
							</PARAMETERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="c6b2a864-4d19-46bf-8511-32780b6b40d3">
							<SHORT-NAME>vSetPlatform</SHORT-NAME>
							<DESC>
								<L-2 L="EN">General platform and compiler specific parameters.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildNotDeletable">true</SD>
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<MULTIPLICITY-CONFIG-CLASSES>
								<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
									<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
								</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
							</MULTIPLICITY-CONFIG-CLASSES>
							<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
							<REQUIRES-INDEX>false</REQUIRES-INDEX>
							<PARAMETERS>
								<ECUC-ENUMERATION-PARAM-DEF UUID="b54c8e07-5a67-44e3-9813-93587a4928d3">
									<SHORT-NAME>vSetByteOrder</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Byte order on memory level.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>ECU</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<LITERALS>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="0a1d5e25-2e81-4aa8-beb6-c813a4ca003b">
											<SHORT-NAME>BIG_ENDIAN</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Big Endian</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="5e446c70-5b8b-46d1-b383-9c39b2ff3372">
											<SHORT-NAME>LITTLE_ENDIAN</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Little Endian</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
									</LITERALS>
								</ECUC-ENUMERATION-PARAM-DEF>
								<ECUC-ENUMERATION-PARAM-DEF UUID="95c710a7-d933-4f75-acde-7101af946fd7">
									<SHORT-NAME>vSetBitOrder</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Bit order on register level.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>ECU</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<LITERALS>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="1ab33429-6822-4dd5-a0c8-701241dd17b6">
											<SHORT-NAME>LSB_to_MSB</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">LSB to MSB</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="ef76fba4-fecf-4cf5-9142-a5ed38c62019">
											<SHORT-NAME>MSB_to_LSB</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">MSB to LSB</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
									</LITERALS>
								</ECUC-ENUMERATION-PARAM-DEF>
								<ECUC-ENUMERATION-PARAM-DEF UUID="3af8ac42-5ed5-4dbd-aa2c-22bd6f3a68dc">
									<SHORT-NAME>vSetCPUType</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Register width of the CPU in bit.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>ECU</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<LITERALS>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="92fe3556-e980-496e-9fb4-7a275c824b2c">
											<SHORT-NAME>CPU8Bit</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">8 Bit</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="32e73049-a7aa-4ab3-90ad-1a81f981cedd">
											<SHORT-NAME>CPU16Bit</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">16 Bit</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="9db754a5-12df-4628-8d65-2ad6a4332d77">
											<SHORT-NAME>CPU32Bit</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">32 Bit</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="5edde5fe-89c2-4ef6-b612-666108a6550c">
											<SHORT-NAME>CPU64Bit</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">64 Bit</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
									</LITERALS>
								</ECUC-ENUMERATION-PARAM-DEF>
								<ECUC-ENUMERATION-PARAM-DEF UUID="7a331f19-8133-45a6-ab80-22ebf18b5fea">
									<SHORT-NAME>vSetSizeOfEnum</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Size of type enum in bit.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>ECU</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<LITERALS>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="982c5ddb-bad8-4d48-83f4-fbb1f3a6f1ad">
											<SHORT-NAME>Size8Bit</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">8 Bit</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="82c07f85-421b-427c-9b89-bacf24363f65">
											<SHORT-NAME>Size16Bit</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">16 Bit</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="f1003fb8-7d91-4544-8285-d682671e35f6">
											<SHORT-NAME>Size32Bit</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">32 Bit</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
									</LITERALS>
								</ECUC-ENUMERATION-PARAM-DEF>
								<ECUC-ENUMERATION-PARAM-DEF UUID="deae1120-8220-4a8f-9507-89a314f42889">
									<SHORT-NAME>vSetSizeOfInt</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Size of type int in bit.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>ECU</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<LITERALS>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="da1fe51d-781d-46c1-b264-d22652110efc">
											<SHORT-NAME>Size16Bit</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">16 Bit</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="a88960ab-6934-4089-a61e-99940ecb1467">
											<SHORT-NAME>Size32Bit</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">32 Bit</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
									</LITERALS>
								</ECUC-ENUMERATION-PARAM-DEF>
								<ECUC-ENUMERATION-PARAM-DEF UUID="30dbc95a-6c19-495c-943b-b7a69a514fb9">
									<SHORT-NAME>vSetSizeOfROMPointer</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Size of ROM pointer in bit.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>ECU</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<LITERALS>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="a4818c06-6872-4604-9c7c-c8f177f777ca">
											<SHORT-NAME>Size16Bit</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">16 Bit</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="5e565dd8-9409-4dbb-8d0a-95c2db3b07d7">
											<SHORT-NAME>Size24Bit</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">24 Bit</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="c820a10c-c230-40ff-a364-9c43929b6b01">
											<SHORT-NAME>Size32Bit</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">32 Bit</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="57003efb-139a-48d1-8bf3-10f6e2136a8c">
											<SHORT-NAME>Size64Bit</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">64 Bit</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
									</LITERALS>
								</ECUC-ENUMERATION-PARAM-DEF>
								<ECUC-ENUMERATION-PARAM-DEF UUID="4272f486-5b0b-4bb9-b30d-6461ed776365">
									<SHORT-NAME>vSetSizeOfRAMPointer</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Size of RAM pointer in bit.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>ECU</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<LITERALS>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="923c1ac1-a132-4e61-b789-0e17f4860629">
											<SHORT-NAME>Size16Bit</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">16 Bit</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="ca487019-fe63-4458-a8a6-ddf7d52ac6bb">
											<SHORT-NAME>Size24Bit</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">24 Bit</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="e04ca80f-f2c0-4241-b4ea-36190717c0ce">
											<SHORT-NAME>Size32Bit</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">32 Bit</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="f0fa9a0f-3d2b-41bf-84cd-93a12e91a334">
											<SHORT-NAME>Size64Bit</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">64 Bit</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
									</LITERALS>
								</ECUC-ENUMERATION-PARAM-DEF>
							</PARAMETERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="182db01f-1a8d-4a5b-b23f-cf1b1677d008">
							<SHORT-NAME>vSetCalibration</SHORT-NAME>
							<DESC>
								<L-2 L="EN">General calibration specific parameters.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildNotDeletable">true</SD>
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<MULTIPLICITY-CONFIG-CLASSES>
								<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
									<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
								</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
							</MULTIPLICITY-CONFIG-CLASSES>
							<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
							<REQUIRES-INDEX>false</REQUIRES-INDEX>
							<PARAMETERS>
								<ECUC-INTEGER-PARAM-DEF UUID="8bac8384-4730-4640-9cc3-68abc25ae0be">
									<SHORT-NAME>vSetPBCalibrationVirtualStartAddress</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Start address of the virtual memory segment for calibration of post-build loadable parameters.

The memory segment is used by standard calibration tools like vCDMStudio to display and manage post-build loadable parameters in a physical representation. The segment is only available in the HEX-file and the data base of the calibration tool. It is not transferred to the ECU's memory. The address should not lie in the address range of the ECU. Recommended is an address beyond the ECU's address range because the virtual memory segment size depends on the concrete configuration and is calculated automatically.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:DefaultFormat">HEX</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>ECU</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0</DEFAULT-VALUE>
									<MAX>4294967295</MAX>
									<MIN>0</MIN>
								</ECUC-INTEGER-PARAM-DEF>
							</PARAMETERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
					</CONTAINERS>
				</ECUC-MODULE-DEF>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="6cd518c0-b6e2-4237-8ea5-296f67ddf0f8">
					<SHORT-NAME>vSet_Pre</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/vSet</DEFINITION-REF>
					<CONTAINERS></CONTAINERS>
				</ECUC-MODULE-CONFIGURATION-VALUES>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="e630bc81-3bf5-4f6a-8b06-89e111edc4bb">
					<SHORT-NAME>vSet_Rec</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/vSet</DEFINITION-REF>
					<CONTAINERS></CONTAINERS>
				</ECUC-MODULE-CONFIGURATION-VALUES>
			</ELEMENTS>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>