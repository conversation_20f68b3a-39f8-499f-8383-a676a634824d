<?xml version="1.0" encoding="UTF-8"?>
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-2-1.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://autosar.org/schema/r4.0">
	<AR-PACKAGES>
		<AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
			<SHORT-NAME>MICROSAR</SHORT-NAME>
			<ELEMENTS>
				<ECUC-MODULE-DEF UUID="e81b85c3-5895-4754-a4c1-9accb3f474c7">
					<SHORT-NAME>SomeIpXf</SHORT-NAME>
					<DESC>
						<L-2 L="EN">Configuration of the SomeIpXf module.</L-2>
					</DESC>
					<CATEGORY>VENDOR_SPECIFIC_MODULE_DEFINITION</CATEGORY>
					<ADMIN-DATA>
						<DOC-REVISIONS>
							<DOC-REVISION>
								<REVISION-LABEL>1.00.00</REVISION-LABEL>
								<ISSUED-BY>visrco</ISSUED-BY>
								<DATE>2015-04-21</DATE>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.00.01</REVISION-LABEL>
								<ISSUED-BY>visrco</ISSUED-BY>
								<DATE>2015-09-01T09:40:54+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Support platform settings in VTT DualTarget-UseCase</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.01.00</REVISION-LABEL>
								<ISSUED-BY>vissi</ISSUED-BY>
								<DATE>2015-09-14T16:51:00+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Disabled postbuild and linktime support in the BSWMD file</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.02.00</REVISION-LABEL>
								<ISSUED-BY>visrco</ISSUED-BY>
								<DATE>2016-02-26T01:36:17+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Increased version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.03.00</REVISION-LABEL>
								<ISSUED-BY>vissi</ISSUED-BY>
								<DATE>2016-04-20T02:51:37+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Increased version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.04.00</REVISION-LABEL>
								<ISSUED-BY>vissi</ISSUED-BY>
								<DATE>2016-06-20T08:18:58+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Increased version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.04.01</REVISION-LABEL>
								<ISSUED-BY>vissi</ISSUED-BY>
								<DATE>2016-10-21T09:39:50+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Increased version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.05.00</REVISION-LABEL>
								<ISSUED-BY>visso</ISSUED-BY>
								<DATE>2016-10-21T10:25:55+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Increased version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.05.01</REVISION-LABEL>
								<ISSUED-BY>vissi</ISSUED-BY>
								<DATE>2017-01-20T08:50:00+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Increased version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.06.00</REVISION-LABEL>
								<ISSUED-BY>vissi</ISSUED-BY>
								<DATE>2017-01-20T09:30:00+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Increased version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.07.00</REVISION-LABEL>
								<ISSUED-BY>vissi</ISSUED-BY>
								<DATE>2017-05-19T09:00:00+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Increased version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.08.00</REVISION-LABEL>
								<ISSUED-BY>vissi</ISSUED-BY>
								<DATE>2017-06-30T09:00:00+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Increased version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.09.00</REVISION-LABEL>
								<ISSUED-BY>vissi</ISSUED-BY>
								<DATE>2017-08-29T13:30:00+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Increased version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.10.00</REVISION-LABEL>
								<ISSUED-BY>vissi</ISSUED-BY>
								<DATE>2017-11-20T14:00:00+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Increased version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.11.00</REVISION-LABEL>
								<ISSUED-BY>visso</ISSUED-BY>
								<DATE>2018-05-05T14:57:58+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Increased version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.12.00</REVISION-LABEL>
								<ISSUED-BY>visso</ISSUED-BY>
								<DATE>2018-09-24T14:30:15+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Increased version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.13.00</REVISION-LABEL>
								<ISSUED-BY>visso</ISSUED-BY>
								<DATE>2019-04-04T11:51:44+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Increased version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.14.00</REVISION-LABEL>
								<ISSUED-BY>visso</ISSUED-BY>
								<DATE>2019-08-28T15:01:57+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Increased version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.15.00</REVISION-LABEL>
								<ISSUED-BY>visso</ISSUED-BY>
								<DATE>2020-04-06T19:06:35+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Increased version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
						</DOC-REVISIONS>
					</ADMIN-DATA>
					<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
					<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
					<UPPER-MULTIPLICITY-INFINITE>false</UPPER-MULTIPLICITY-INFINITE>
					<POST-BUILD-VARIANT-SUPPORT>false</POST-BUILD-VARIANT-SUPPORT>
					<REFINED-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Xfrm</REFINED-MODULE-DEF-REF>
					<SUPPORTED-CONFIG-VARIANTS>
						<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
					</SUPPORTED-CONFIG-VARIANTS>
					<CONTAINERS>
						<!-- Container Definition: XfrmGeneral -->
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="bd9c97b2-2953-4055-a05f-2c7de79ff791">
							<SHORT-NAME>XfrmGeneral</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Contains the general configuration parameters of the module.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">true</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<MULTIPLICITY-CONFIG-CLASSES>
								<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
									<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
								</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
							</MULTIPLICITY-CONFIG-CLASSES>
							<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
							<REQUIRES-INDEX>false</REQUIRES-INDEX>
							<PARAMETERS>
								<!-- PARAMETER DEFINITION: XfrmDevErrorDetect -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="b5a56063-a14a-4f4f-a8d5-f30ba2cad084">
									<SHORT-NAME>XfrmDevErrorDetect</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Switches the Development Error Detection and Notification ON or OFF.</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">true: Enabled
                                        false: Disabled</L-1>
										</P>
									</INTRODUCTION>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: XfrmVersionInfoApi -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="d69ec8fc-86b9-4f17-b5f7-bbcbe22f5705">
									<SHORT-NAME>XfrmVersionInfoApi</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Activate/Deactivate the version information API.</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">True: version information API activated
                                        False: version information API deactivated</L-1>
										</P>
									</INTRODUCTION>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
							</PARAMETERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
						<!-- Container Definition: XfrmImplementationMapping -->
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="e453c46d-048e-40d1-8bb7-172f0fa6c9b7">
							<SHORT-NAME>XfrmImplementationMapping</SHORT-NAME>
							<DESC>
								<L-2 L="EN">For each transformer (TransformationTechnology) in a transformer chain (DataTransformation) which is applied to an ISignal it is necessary to specify the BswModuleEntry which implements it. This is the container to hold these mappings.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
							<MULTIPLICITY-CONFIG-CLASSES>
								<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
									<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
								</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
							</MULTIPLICITY-CONFIG-CLASSES>
							<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
							<REQUIRES-INDEX>false</REQUIRES-INDEX>
							<REFERENCES>
								<!-- Foreign Reference Definition: XfmTransformationBswModuleEntryRef -->
								<ECUC-FOREIGN-REFERENCE-DEF UUID="f23530e8-2a6e-4a49-b2f2-348a20c55f99">
									<SHORT-NAME>XfrmTransformerBswModuleEntryRef</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Reference to the BswModuleEntry which implements the referenced transformer on the sending/calling side.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<DESTINATION-TYPE>BSW-MODULE-ENTRY</DESTINATION-TYPE>
								</ECUC-FOREIGN-REFERENCE-DEF>
								<!-- Foreign Reference Definition: XfrmInvTransformerBswModuleEntryRef -->
								<ECUC-FOREIGN-REFERENCE-DEF UUID="7e36fe7d-0a92-4565-8b34-abe93fc2c830">
									<SHORT-NAME>XfrmInvTransformerBswModuleEntryRef</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Reference to the BswModuleEntry which implements the referenced inverse transformer on the receiving/called side.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<DESTINATION-TYPE>BSW-MODULE-ENTRY</DESTINATION-TYPE>
								</ECUC-FOREIGN-REFERENCE-DEF>
								<!-- Foreign Reference Definition: XfrmTransformationTechnologyRef -->
								<ECUC-FOREIGN-REFERENCE-DEF UUID="8e2ede8e-aa4c-4be4-9623-102c50ccd453">
									<SHORT-NAME>XfrmTransformationTechnologyRef</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Reference to the TransformationTechnology in the DataTransformation of the system description for which the implementation (BswModuleEntry) shall be mapped.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<DESTINATION-TYPE>TRANSFORMATION-TECHNOLOGY</DESTINATION-TYPE>
								</ECUC-FOREIGN-REFERENCE-DEF>
								<!-- Instance Reference Definition: XfrmVariableDataPrototypeInstanceRef -->
								<ECUC-INSTANCE-REFERENCE-DEF UUID="d0c57776-e186-4f00-9636-382322d7b62b">
									<SHORT-NAME>XfrmVariableDataPrototypeInstanceRef</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Instance Reference to a VariableDataPrototype in case a dedicated transformer BswModuleEntry is required per VariableDataPrototype access.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<DESTINATION-CONTEXT>SW-COMPONENT-PROTOTYPE PORT-PROTOTYPE</DESTINATION-CONTEXT>
									<DESTINATION-TYPE>VARIABLE-DATA-PROTOTYPE</DESTINATION-TYPE>
								</ECUC-INSTANCE-REFERENCE-DEF>
							</REFERENCES>
							<SUB-CONTAINERS>
								<!-- Container Definition: XfrmDemEventParameterRefs -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="d529eb30-84d7-4cff-a942-076facfdee8e">
									<SHORT-NAME>XfrmDemEventParameterRefs</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for the references to DemEventParameter elements which shall be invoked using the API Dem_ReportErrorStatus API in case the corresponding error occurs. The EventId is taken from the referenced DemEventParameter's DemEventId value. The standardized errors are provided in the container and can be extended by vendor specific error references.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<REFERENCES>
										<!-- Symbolic Name Reference Definition: XFRM_E_MALFORMED_MESSAGE -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="e1e0f614-ced5-4f34-9f64-cbeb6ba73d93">
											<SHORT-NAME>XFRM_E_MALFORMED_MESSAGE</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to configured DEM event to report if malformed messages were received by the transformer.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: XfrmSignal -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="b93d0ebe-31cf-4a7b-af86-7e12df5eb891">
									<SHORT-NAME>XfrmSignal</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Reference to the signal in the system description that transports the transformed data.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">true</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SUB-CONTAINERS>
										<!-- Container Definition: XfrmSignalChoice -->
										<ECUC-CHOICE-CONTAINER-DEF UUID="6df03243-85f3-4500-897a-c0a062847e06">
											<SHORT-NAME>XfrmSignalChoice</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Choice whether an ISignal or an ISignalGroup shall be referenced.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">true</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<CHOICES>
												<!-- Container Definition: XfrmISignalGroupRefChoice -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="7d5eff83-4417-4e5c-84e3-8802677e8ac4">
													<SHORT-NAME>XfrmISignalGroupRefChoice</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the ISignalGroup in the system description that transports the transformed data.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<REFERENCES>
														<!-- Foreign Reference Definition: XfrmISignalGroupRef -->
														<ECUC-FOREIGN-REFERENCE-DEF UUID="d5047912-26b6-4bfb-a0fd-d8e6baa542ba">
															<SHORT-NAME>XfrmISignalGroupRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the ISignalGroup in the system description that transports the transformed data.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<SCOPE>LOCAL</SCOPE>
															<MULTIPLICITY-CONFIG-CLASSES>
																<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															</MULTIPLICITY-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<DESTINATION-TYPE>I-SIGNAL-GROUP</DESTINATION-TYPE>
														</ECUC-FOREIGN-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: XfrmISignalRefChoice -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="2680e10f-b8d5-44cb-bd0c-669905a01094">
													<SHORT-NAME>XfrmISignalRefChoice</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the ISignal in the system description that transports the transformed data.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<REFERENCES>
														<!-- Foreign Reference Definition: XfrmISignalRef -->
														<ECUC-FOREIGN-REFERENCE-DEF UUID="6fb71d00-5b52-4cf9-91ce-4735bec27604">
															<SHORT-NAME>XfrmISignalRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Reference to the ISignal in the system description that transports the transformed data.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<SCOPE>LOCAL</SCOPE>
															<MULTIPLICITY-CONFIG-CLASSES>
																<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															</MULTIPLICITY-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
															<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<VALUE-CONFIG-CLASSES>
																<ECUC-VALUE-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-VALUE-CONFIGURATION-CLASS>
															</VALUE-CONFIG-CLASSES>
															<DESTINATION-TYPE>I-SIGNAL</DESTINATION-TYPE>
														</ECUC-FOREIGN-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</CHOICES>
										</ECUC-CHOICE-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</SUB-CONTAINERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
					</CONTAINERS>
				</ECUC-MODULE-DEF>
				<BSW-IMPLEMENTATION UUID="92a32557-572f-4f8e-b617-ace9e504c5e7">
					<SHORT-NAME>SomeIpXf_Impl</SHORT-NAME>
					<PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
					<SW-VERSION>1.15.00</SW-VERSION>
					<USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
					<VENDOR-ID>30</VENDOR-ID>
					<AR-RELEASE-VERSION>4.03.01</AR-RELEASE-VERSION>
					<BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/SomeIpXf_ib_bswmd/BswModuleDescriptions/SomeIpXf/SomeIpXfBehavior</BEHAVIOR-REF>
					<PRECONFIGURED-CONFIGURATION-REFS>
						<PRECONFIGURED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/SomeIpXf_Pre</PRECONFIGURED-CONFIGURATION-REF>
					</PRECONFIGURED-CONFIGURATION-REFS>
					<RECOMMENDED-CONFIGURATION-REFS>
						<RECOMMENDED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/SomeIpXf_Rec</RECOMMENDED-CONFIGURATION-REF>
					</RECOMMENDED-CONFIGURATION-REFS>
					<VENDOR-SPECIFIC-MODULE-DEF-REFS>
						<VENDOR-SPECIFIC-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/MICROSAR/SomeIpXf</VENDOR-SPECIFIC-MODULE-DEF-REF>
					</VENDOR-SPECIFIC-MODULE-DEF-REFS>
				</BSW-IMPLEMENTATION>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="6bc60967-b326-47fc-aa4a-cb1f67da0af9">
					<SHORT-NAME>SomeIpXf_Pre</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/SomeIpXf</DEFINITION-REF>
					<CONTAINERS></CONTAINERS>
				</ECUC-MODULE-CONFIGURATION-VALUES>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="47d7ebcb-de9a-428d-a0e7-142061c31465">
					<SHORT-NAME>SomeIpXf_Rec</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/SomeIpXf</DEFINITION-REF>
					<CONTAINERS></CONTAINERS>
				</ECUC-MODULE-CONFIGURATION-VALUES>
			</ELEMENTS>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>