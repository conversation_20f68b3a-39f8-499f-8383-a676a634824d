/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *  Copyright (c) 2020 by Vector Informatik GmbH.                                              All rights reserved.
 * 
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *         File:  TcpIpXcp.c
 *    Component:  TcpIpXcp Implementation
 *       Module:  -
 *    Generator:  -
 *
 *  Description:  Implementation of XCP over TcpIp based on Vector AUTOSAR Stack
 *  
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  AUTHOR IDENTITY
 *  -------------------------------------------------------------------------------------------------------------------
 *  Name                          Initials      Company
 *  -------------------------------------------------------------------------------------------------------------------
 *  Andreas Herkommer             Hr            Vector Informatik GmbH
 *  Oliver Reineke                Ore           Vector Informatik GmbH
 *  -------------------------------------------------------------------------------------------------------------------
 *  REVISION HISTORY
 *  -------------------------------------------------------------------------------------------------------------------
 *  Version   Date        Author  Change Id     Description
 *  -------------------------------------------------------------------------------------------------------------------
 *  01.00.00  2008-10-09  Hr                    First Version
 *  02.00.00  2013-06-04  Hr      ESCAN00059277 Adapt component to SoAd
 *                        Hr      ESCAN00069959 Support ASR4 environment
 *  02.00.01  2013-11-15  Hr      ESCAN00071975 Missing XcpOnEth header in response packet
 *  02.00.02  2014-02-25  Ore     ESCAN00073922 Compiler error: Symbol "TCPIPXCP_EXCLUSIVE_AREA_0" unknown
 *                        Hr      ESCAN00074272 SetPduMode feature leads to continuous transmission
 *  02.00.03  2014-05-09  Hr      ESCAN00075589 Improve robustness of use case "if transmission of XCP on Ethernet frame fails"
 *  02.00.04  2014-07-07  Hr      ESCAN00076800 Rx buffer not aligned to 32Bit
 *                        Hr      ESCAN00076953 Xcp connection terminated in case of Alive Supervision Timeout
 *  02.01.00  2015-02-16  Hr      ESCAN00079998 Missing memory abstraction in header
 *            2015-04-23  Hr      ESCAN00080791 Support Resume Mode
 *  02.02.00  2015-05-05  Hr      ESCAN00082845 Support TxConfirmation timeout timer
 *  02.02.01  2015-11-12  Hr      ESCAN00086397 FEAT-1357: SafeXCP
 *                        Hr      ESCAN00085638 Compiler warning: Modifiable lvalue
 *  03.00.00  2016-08-31  Hr      ESCAN00091918 FEAT-1980: Add Multi Client / Multi Connection support
 *                        Hr      ESCAN00091954 Safe deactivation does not consider TcpIpXcp_Init
 *                        Hr      ESCAN00091955 Safe deactivation shall be performed before DET checks or anything
 *  03.00.01  2017-04-19  Hr      ESCAN00094223 DET error due to incorrect channel id conversion
 *  04.00.00  2019-03-26  Hr      ESCAN00101342 Compiler warning: unreferenced formal parameter
 *                        Hr      ESCAN00100672 Possible buffer overflow when XCP Master ignores MaxCto/Dto
 *                        Hr      STORYC-5256   MISRA-C:2012 Compliance Cp_XcpOnTcpIpAsr
 *                        Hr      STORY-7952    Support IF Modules on different cores for XCP
 *  04.00.01  2020-07-06  Hr      ESCAN00106749 Race condition leads to lost tx frame
 *  05.00.00  2020-08-06  Hr      SWAT-1020     XCP Ethernet Frame Concatenation
 *********************************************************************************************************************/

#define TCPIPXCP_SOURCE

/**********************************************************************************************************************
 *  INCLUDES
 *********************************************************************************************************************/
#include "TcpIpXcp.h"
#include "SoAd.h"

#if ( TCPIPXCP_DEV_ERROR_DETECT == STD_ON )
  #include "Det.h"              /* Include Diagnostic Event Manager */
#endif

#if defined (TCPIPXCP_CONFIGURATOR) && (TCPIPXCP_CONFIGURATOR == 5u)
  #include "SchM_Xcp.h"
#endif

/**********************************************************************************************************************
 *  VERSION CHECK
 *********************************************************************************************************************/
/* vendor specific version information is BCD coded */
#if (  (TCPIPXCP_SW_MAJOR_VERSION != (0x05u)) \
    || (TCPIPXCP_SW_MINOR_VERSION != (0x00u)) \
    || (TCPIPXCP_SW_PATCH_VERSION != (0x00u)) )
  #error "Vendor specific version numbers of TcpIpXcp.c and TcpIpXcp.h are inconsistent"
#endif

/* AUTOSAR version information check has to match definition in header file */
#if (  (TCPIPXCP_AR_MAJOR_VERSION != (0x04u)) \
    || (TCPIPXCP_AR_MINOR_VERSION != (0x00u)) \
    || (TCPIPXCP_AR_PATCH_VERSION != (0x03u)) )
  #error "AUTOSAR Specification Version numbers of TcpIpXcp.c and TcpIpXcp.h are inconsistent!"
#endif

/**********************************************************************************************************************
 *  LOCAL CONSTANT MACROS
 **********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL FUNCTION MACROS
 **********************************************************************************************************************/
#define TCPIPXCP_MAX(a, b)   (((a) > (b)) ? (a) : (b)) /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */

#if (TCPIPXCP_DEV_ERROR_DETECT == STD_ON)
/* If DET is enabled, a MISRA-C:2004 analysis will yield deviations to the rules:
*  - 14.3 Before preprocessing, a null statement shall only occur on a line by itself; it may be followed by a comment provided that the first character following the null statement is a white-space character.
*  - 14.7 A function shall have a single point of exit at the end of the function
*  - 19.4 C macros shall only expand to a braced initializer, a constant, a parenthesized expression, a type qualifier, a storage class specifier, or a do-while-zero construct
*  - 19.7 A function should be used in preference to a function-like macro.
*  These deviations are caused by design of the runtime/ressource optimized DET handling and are globally justified.
*/
#define TcpIpXcp_CheckDetErrorReturnVoid( CONDITION, API_ID, ERROR_CODE ) { if(!(CONDITION)) { \
  (void)Det_ReportError( TCPIPXCP_MODULE_ID, 0, (API_ID), (ERROR_CODE)); return; } }
#define TcpIpXcp_CheckDetErrorReturnValue( CONDITION, API_ID, ERROR_CODE, RET_VAL ) { if(!(CONDITION)) { \
  (void)Det_ReportError( TCPIPXCP_MODULE_ID, 0, (API_ID), (ERROR_CODE)); return (RET_VAL); } }
#define TcpIpXcp_CheckDetErrorContinue( CONDITION, API_ID, ERROR_CODE ) { if(!(CONDITION)) { \
  (void)Det_ReportError( TCPIPXCP_MODULE_ID, 0, (API_ID), (ERROR_CODE)); } }
#define TcpIpXcp_CallDetReportError( API_ID, ERROR_CODE ) (void)Det_ReportError( TCPIPXCP_MODULE_ID, 0, (API_ID), (ERROR_CODE)) /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */
#else
#define TcpIpXcp_CheckDetErrorReturnVoid( CONDITION, API_ID, ERROR_CODE )
#define TcpIpXcp_CheckDetErrorReturnValue( CONDITION, API_ID, ERROR_CODE, RET_VAL ) 
#define TcpIpXcp_CheckDetErrorContinue( CONDITION, API_ID, ERROR_CODE ) 
#define TcpIpXcp_CallDetReportError( API_ID, ERROR_CODE ) 
#endif

#if !defined ( TCPIPXCP_COPY )
  #if defined ( XCP_ENABLE_ALIGNED_DTO )
    #define TCPIPXCP_COPY(dest, src, length) TcpIpXcp_FastCopy( (DAQBYTEPTR)(dest), (ROMDAQBYTEPTR)(src), (length) ) /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */
  #else
  #define TCPIPXCP_COPY(dest, src, length) Xcp_MemCpy( (DAQBYTEPTR)(dest), (ROMDAQBYTEPTR)(src), (length) ) /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */
  #endif
#endif

/* Configuration access (depending on configuration variant)*/
#if ( TCPIPXCP_CONFIG_VARIANT == 3 )
#define TcpIpXcp_GetSoAdTxPduId(idx) TcpIpXcp_Configuration->TcpIpXcp_XcpPduPtr[idx].SoAdTxPduId /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */
#define TcpIpXcp_GetTxConfPduId(idx) TcpIpXcp_Configuration->TcpIpXcp_XcpPduPtr[idx].TxConfPduId /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */
#define TcpIpXcp_GetRxPduId(idx)     TcpIpXcp_Configuration->TcpIpXcp_XcpPduPtr[idx].RxPduId /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */
#define TcpIpXcp_GetSoConId(idx)     TcpIpXcp_Configuration->TcpIpXcp_XcpPduPtr[idx].SoConId /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */

#elif ( TCPIPXCP_CONFIG_VARIANT == 2 )
#define TcpIpXcp_GetSoAdTxPduId(idx) TcpIpXcp_PduIdField[idx].SoAdTxPduId /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */
#define TcpIpXcp_GetTxConfPduId(idx) TcpIpXcp_PduIdField[idx].TxConfPduId /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */
#define TcpIpXcp_GetRxPduId(idx)     TcpIpXcp_PduIdField[idx].RxPduId /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */
#define TcpIpXcp_GetSoConId(idx)     TcpIpXcp_PduIdField[idx].SoConId /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */

#elif ( TCPIPXCP_CONFIG_VARIANT == 1 )
#define TcpIpXcp_GetSoAdTxPduId(idx) TcpIpXcp_PduIdField[idx].SoAdTxPduId /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */
#define TcpIpXcp_GetTxConfPduId(idx) TcpIpXcp_PduIdField[idx].TxConfPduId /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */
#define TcpIpXcp_GetRxPduId(idx)     TcpIpXcp_PduIdField[idx].RxPduId /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */
#define TcpIpXcp_GetSoConId(idx)     TcpIpXcp_PduIdField[idx].SoConId /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */

#else
  #error "TcpIpXcp.c: invalid or missing TCPIPXCP_CONFIG_VARIANT, (value 1, 2 or 3 expected in config file)"
#endif
  /* TCPIPXCP_CONFIG_VARIANT */

/* States of TcpIpXcp_SendWithoutQueueState */
#define TCPIPXCP_MSG_IDLE           0u
#define TCPIPXCP_MSG_PENDING        1u

/**********************************************************************************************************************
 *  LOCAL DATA TYPES AND STRUCTURES
 **********************************************************************************************************************/
typedef union { /* PRQA S 0750 */ /* MD_MSR_Union */
  uint8  fc[TCPIPXCP_PDU_SIZE];  /* PRQA S 3493, 3494 */ /* MD_TCPIPXCP_3493_4 */
  uint32 dw[(TCPIPXCP_PDU_SIZE)/4u];  /* PRQA S 3493, 3494 */ /* MD_TCPIPXCP_3493_4 */
} TcpIpXcpAlignedFrameCacheType;


/**********************************************************************************************************************
 *  LOCAL DATA PROTOTYPES
 **********************************************************************************************************************/
#define TCPIPXCP_START_SEC_VAR_NOINIT_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/* PRQA S 3218 3 */ /* MD_TCPIPXCP_3218_FileScopeStatic */
TCPIPXCP_LOCAL VAR(TcpIpXcpAlignedFrameCacheType, TCPIPXCP_VAR_NOINIT) TcpIpXcp_TxFrameCache[TCPIPXCP_NUMBER_OF_CHANNELS]; /* PRQA S 3408 */ /* MD_TCPIPXCP_3408 */
#if (TCPIPXCP_ALIGN_RXBUFFER == STD_ON) /* COV_TCPIPXCP_MSR3_COMPATIBILITY */
TCPIPXCP_LOCAL VAR(TcpIpXcpAlignedFrameCacheType, TCPIPXCP_VAR_NOINIT) TcpIpXcp_RxFrameCache; /* PRQA S 3408 */ /* MD_TCPIPXCP_3408 */
#endif

#if ( TCPIPXCP_CONFIG_VARIANT == 3u )
P2CONST(TcpIpXcp_ConfigType, TCPIPXCP_VAR_NOINIT, TCPIPXCP_PBCFG) TcpIpXcp_Configuration; /* PRQA S 3408 */ /* MD_TCPIPXCP_3408 */
#endif

VAR(PduInfoType, TCPIPXCP_VAR_NOINIT) TcpIpXcp_QueuedPduInfo[TCPIPXCP_NUMBER_OF_CHANNELS]; /* PRQA S 3408, 1514 */ /* MD_TCPIPXCP_3408 */
volatile VAR(TcpIpXcp_TimeoutTimerType, TCPIPXCP_VAR_NOINIT) TcpIpXcp_ConfTimeoutTimer[TCPIPXCP_NUMBER_OF_CHANNELS]; /* PRQA S 3408 */ /* MD_TCPIPXCP_3408 */

#if ( XCP_NUMBER_OF_CHANNELS <= 1u )
VAR(PduIdType, TCPIPXCP_VAR_NOINIT) TcpIpXcp_RxPduId; /* PRQA S 3408 */ /* MD_TCPIPXCP_3408 */
#endif

#if ( TCPIPXCP_ENABLE_PDUMODE == STD_ON )
VAR(TcpIpXcp_PduSetModeType, TCPIPXCP_VAR_NOINIT) TcpIpXcp_PduState[TCPIPXCP_NUMBER_OF_CHANNELS]; /* PRQA S 3408 */ /* MD_TCPIPXCP_3408 */
#endif

#define TCPIPXCP_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


#define TCPIPXCP_START_SEC_VAR_NOINIT_16BIT
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

VAR(uint16, TCPIPXCP_VAR_NOINIT) TcpIpXcp_SequenceCounter[TCPIPXCP_NUMBER_OF_CHANNELS]; /* PRQA S 3408 */ /* MD_TCPIPXCP_3408 */
volatile VAR(uint16, TCPIPXCP_VAR_NOINIT) TcpIpXcp_ConcatPos[TCPIPXCP_NUMBER_OF_CHANNELS]; /* PRQA S 3408 */ /* MD_TCPIPXCP_3408 */

#define TCPIPXCP_STOP_SEC_VAR_NOINIT_16BIT
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


#define TCPIPXCP_START_SEC_VAR_NOINIT_8BIT
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

volatile VAR(uint8, TCPIPXCP_VAR_NOINIT) TcpIpXcp_SendWithoutQueueState[TCPIPXCP_NUMBER_OF_CHANNELS]; /* PRQA S 3408 */ /* MD_TCPIPXCP_3408 */

#define TCPIPXCP_STOP_SEC_VAR_NOINIT_8BIT
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


#define TCPIPXCP_START_SEC_VAR_INIT_8BIT
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#if ( TCPIPXCP_DEV_ERROR_DETECT == STD_ON )
TCPIPXCP_LOCAL volatile VAR(uint8, TCPIPXCP_VAR_INIT) TcpIpXcp_InitializationState = TCPIPXCP_UNINIT;
#endif
#if ( TCPIPXCP_ENABLE_CONTROL == STD_ON )
VAR(uint8, TCPIPXCP_VAR_INIT) TcpIpXcp_ControlDeniedCount = 0u; /* PRQA S 3408 */ /* MD_TCPIPXCP_3408 */
#endif

#define TCPIPXCP_STOP_SEC_VAR_INIT_8BIT
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


#if ( TCPIPXCP_ENABLE_CONTROL == STD_ON )
# define TCPIPXCP_START_SEC_VAR_INIT_UNSPECIFIED_SAFE
# include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/* TL Enable/Disable state */
VAR(uint8, TCPIPXCP_VAR_INIT) TcpIpXcp_ControlState = kXcponTcpIp_Control_Enable;

# define TCPIPXCP_STOP_SEC_VAR_INIT_UNSPECIFIED_SAFE
# include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */
#endif

/**********************************************************************************************************************
 *  GLOBAL DATA
 **********************************************************************************************************************/
#define TCPIPXCP_START_SEC_CONST_8BIT
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/* Requirement Specification Template version */
CONST(uint8, TCPIPXCP_CONST) TcpIpXcp_MainVersion    = (uint8)TCPIPXCP_SW_MAJOR_VERSION;
CONST(uint8, TCPIPXCP_CONST) TcpIpXcp_SubVersion     = (uint8)TCPIPXCP_SW_MINOR_VERSION;
CONST(uint8, TCPIPXCP_CONST) TcpIpXcp_ReleaseVersion = (uint8)TCPIPXCP_SW_PATCH_VERSION;

#define TCPIPXCP_STOP_SEC_CONST_8BIT
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


/**********************************************************************************************************************
 *  LOCAL FUNCTION PROTOTYPES
 **********************************************************************************************************************/
#define TCPIPXCP_START_SEC_CODE
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#if defined ( XCP_ENABLE_ALIGNED_DTO )
/**********************************************************************************************************************
 *  TcpIpXcp_FastCopy()
 *********************************************************************************************************************/
/*! 
 *  \brief       32Bit copy function for aligned data.
 *  \details     -
 *  \param[out]  dest    Pointer to the destination.
 *  \param[in]   src     Pointer to the source.
 *  \param[in]   len     The length of the data to be copied.
 *  \pre         -
 *  \reentrant   TRUE
 *  \synchronous TRUE
 **********************************************************************************************************************/
STATIC FUNC(void, TCPIPXCP_CODE) TcpIpXcp_FastCopy( P2VAR(uint8, AUTOMATIC, TCPIPXCP_VAR_NOINIT) dest, P2CONST(uint8, AUTOMATIC, TCPIPXCP_VAR_NOINIT) src, uint16 len );
#endif


/**********************************************************************************************************************
 *  LOCAL FUNCTIONS
 **********************************************************************************************************************/

#if defined ( XCP_ENABLE_ALIGNED_DTO )
/**********************************************************************************************************************
 *  TcpIpXcp_FastCopy()
 *********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 */
STATIC FUNC(void, TCPIPXCP_CODE) TcpIpXcp_FastCopy( P2VAR(uint8, AUTOMATIC, TCPIPXCP_VAR_NOINIT) dest, P2CONST(uint8, AUTOMATIC, TCPIPXCP_VAR_NOINIT) src, uint16 len )
{
  while( len >= 4u )
  {
    *(P2VAR(uint32, AUTOMATIC, TCPIPXCP_VAR_NOINIT))dest = *(P2CONST(uint32, AUTOMATIC, TCPIPXCP_VAR_NOINIT))src; /* PRQA S 0310, 3305 */ /* MD_TCPIPXCP_0310, MD_TCPIPXCP_3305 */
    dest = &dest[4]; /* PRQA S 1338 */ /* MD_TCPIPXCP_1338 */
    src = &src[4]; /* PRQA S 1338 */ /* MD_TCPIPXCP_1338 */
    len -= 4u; /* PRQA S 1338 */ /* MD_TCPIPXCP_1338 */
  }
  while( len > 0u )
  {
    *(P2VAR(uint8, AUTOMATIC, TCPIPXCP_VAR_NOINIT))dest = *(P2CONST(uint8, AUTOMATIC, TCPIPXCP_VAR_NOINIT))src;
    dest = &dest[1]; /* PRQA S 1338 */ /* MD_TCPIPXCP_1338 */
    src = &src[1]; /* PRQA S 1338 */ /* MD_TCPIPXCP_1338 */
    len -= 1u; /* PRQA S 1338 */ /* MD_TCPIPXCP_1338 */
  }
}
#endif

/**********************************************************************************************************************
 *  GLOBAL FUNCTIONS
 **********************************************************************************************************************/

/***********************************************************************************************************************
 *  Xcp_SoAdRxIndication
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 */

FUNC(void, TCPIPXCP_CODE) Xcp_SoAdRxIndication(PduIdType RxPduId, P2CONST(PduInfoType, AUTOMATIC, TCPIPXCP_APPL_DATA) PduInfoPtr) /* PRQA S 2889 */ /* MD_TCPIPXCP_2889 */
{
  uint16 xcpFrameLen;
#if ( XCP_NUMBER_OF_CHANNELS > 1u )
  PduIdType TcpIpXcp_RxPduId;
#endif

  /* Activation control */
#if ( TCPIPXCP_ENABLE_CONTROL == STD_ON )
  if ( TcpIpXcp_ControlState == kXcponTcpIp_Control_Disable )
  {
    TcpIpXcp_ControlDeniedCount++;
    return;
  }
#endif

  TcpIpXcp_CheckDetErrorReturnVoid( TCPIPXCP_UNINIT != TcpIpXcp_InitializationState,
                                    TCPIPXCP_RXINDICATION_SERVICE_ID, TCPIPXCP_E_NOT_INITIALIZED )
  TcpIpXcp_CheckDetErrorReturnVoid( PduInfoPtr != (P2VAR(PduInfoType, AUTOMATIC, TCPIPXCP_APPL_VAR))NULL_PTR,
                                    TCPIPXCP_RXINDICATION_SERVICE_ID, TCPIPXCP_E_NULL_POINTER )
  TcpIpXcp_CheckDetErrorReturnVoid( PduInfoPtr->SduDataPtr != (SduDataPtrType)NULL_PTR,
                                    TCPIPXCP_RXINDICATION_SERVICE_ID, TCPIPXCP_E_NULL_POINTER )

#if ( XCP_NUMBER_OF_CHANNELS > 1u )
  TcpIpXcp_CheckDetErrorReturnVoid( (XCP_TRANSPORT_LAYER_TCPIP + RxPduId) < XCP_NUMBER_OF_CHANNELS, /* PRQA S 2986 */ /* MD_TCPIPXCP_2985_6 */
                                    TCPIPXCP_RXINDICATION_SERVICE_ID, TCPIPXCP_E_INV_SOCK_IDX )
  /* Store the Rx PDU Id to determine the reception channel. */
  TcpIpXcp_RxPduId = RxPduId;
#else
# if ( TCPIPXCP_NUMBER_OF_CHANNELS > 1u )
  if ( TcpIpXcp_RxPduId != RxPduId )
  {
#  if ( TCPIPXCP_MULTI_CONN_PROT == STD_ON )
    /* Ensure that no XCP connection is interrupt via another channel. */
    if ( 0u != (Xcp_GetSessionStatus((Xcp_ChannelType)(XCP_TRANSPORT_LAYER_TCPIP + TcpIpXcp_RxPduId)) & (SessionStatusType)SS_CONNECTED) ) /* PRQA S 2986 */ /* MD_TCPIPXCP_2985_6 */
    {
      /* Do not transmit an error packet due to XcpConfirmation() will be called on the wrong channel. */
      return;
    }
    else
#  endif
    {
      /* Store the Rx PDU Id to determine the reception channel. */
      TcpIpXcp_RxPduId = RxPduId;
    }
  }
# else
  /* Verifiy RX Pdu Id */
  if(RxPduId == TcpIpXcp_GetRxPduId(0))
  {
    /* Store the Rx PDU Id for the reception. */
    TcpIpXcp_RxPduId = RxPduId;
# endif
#endif

  xcpFrameLen = (PduInfoPtr->SduDataPtr[0]) | ((uint16)PduInfoPtr->SduDataPtr[1] << 8);
  /* Check for valid length */
  if( (xcpFrameLen <= (PduInfoPtr->SduLength - (uint16)TCPIPXCP_HEADER_SIZE))
    /* ESCAN00100672 */
   && (xcpFrameLen <= (TCPIPXCP_MAX(kTcpIpXcpMaxCTO, kTcpIpXcpMaxDTO))))  /* PRQA S 3493, 3494 */ /* MD_TCPIPXCP_3493_4 */
  {
    /* Is this a connect? */
    if(0xffu == PduInfoPtr->SduDataPtr[TCPIPXCP_HEADER_SIZE])
    {
      /* In case of connect we set this as active TL */
      Xcp_SetActiveTl((Xcp_ChannelType)(XCP_TRANSPORT_LAYER_TCPIP + TcpIpXcp_RxPduId), kTcpIpXcpMaxCTO, kTcpIpXcpMaxDTO, (uint8)(XCP_TRANSPORT_LAYER_TCPIP + TcpIpXcp_RxPduId)); /* PRQA S 2986 */ /* MD_TCPIPXCP_2985_6 */
    }

    if((XCP_TRANSPORT_LAYER_TCPIP + TcpIpXcp_RxPduId) == Xcp_GetActiveTl((uint8)(XCP_TRANSPORT_LAYER_TCPIP + TcpIpXcp_RxPduId))) /* PRQA S 2986 */ /* MD_TCPIPXCP_2985_6 */
    {
#if (TCPIPXCP_ALIGN_RXBUFFER == STD_ON) /* COV_TCPIPXCP_MSR3_COMPATIBILITY */
      /* Copy to temporary buffer to guarantee 32Bit alignment */
      Xcp_MemCpy( (DAQBYTEPTR)&TcpIpXcp_RxFrameCache.fc[0], (ROMDAQBYTEPTR)&PduInfoPtr->SduDataPtr[TCPIPXCP_HEADER_SIZE], (uint8)xcpFrameLen);
      Xcp_Command((uint8)(XCP_TRANSPORT_LAYER_TCPIP + TcpIpXcp_RxPduId), &TcpIpXcp_RxFrameCache.dw[0]);
#else
      /* Copying of the received frame to an 32-Bit aligned buffer is not necessary as ETH pdu are already aligned. */
      /* PRQA S 0310 3 */ /* MD_TCPIPXCP_0310 */
      /* PRQA S 2986 2 */ /* MD_TCPIPXCP_2985_6 */
      /* PRQA S 3305 1 */ /* MD_TCPIPXCP_3305 */
      Xcp_Command((uint8)(XCP_TRANSPORT_LAYER_TCPIP + TcpIpXcp_RxPduId), (uint32*)&(PduInfoPtr->SduDataPtr[TCPIPXCP_HEADER_SIZE]));
#endif
    }
  }
#if ( XCP_NUMBER_OF_CHANNELS > 1u )
#else
# if ( TCPIPXCP_NUMBER_OF_CHANNELS > 1u )
# else
  }
# endif
#endif
  /* PRQA S 6010 1 */ /* MD_MSR_STPTH */
}

/***********************************************************************************************************************
 *  Xcp_SoAdTxConfirmation
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 */
FUNC(void, TCPIPXCP_CODE) Xcp_SoAdTxConfirmation(PduIdType TxPduId) /* PRQA S 2889 */ /* MD_TCPIPXCP_2889 */
{
  uint8_least i;

  /* Activation control */
#if ( TCPIPXCP_ENABLE_CONTROL == STD_ON )
  if ( TcpIpXcp_ControlState == kXcponTcpIp_Control_Disable )
  {
    TcpIpXcp_ControlDeniedCount++;
    return;
  }
#endif

  TcpIpXcp_CheckDetErrorReturnVoid( TCPIPXCP_UNINIT != TcpIpXcp_InitializationState,
                                    TCPIPXCP_TXCONFIRMATION_SERVICE_ID, TCPIPXCP_E_NOT_INITIALIZED )

  /* Search respective channel handle */
  for(i=0u; i<TCPIPXCP_NUMBER_OF_CHANNELS; i++)
  {
    if( TcpIpXcp_GetTxConfPduId(i) == TxPduId )
    {
      TcpIpXcp_ConfTimeoutTimer[i] = (TcpIpXcp_TimeoutTimerType)0u;
      (void)Xcp_SendCallBack((uint8)(XCP_TRANSPORT_LAYER_TCPIP + i)); /* PRQA S 2986 */ /* MD_TCPIPXCP_2985_6 */
      break;
    }
  }
}

#if ( TCPIPXCP_MODE_CHG_API == STD_ON )
/***********************************************************************************************************************
 *  Xcp_SoConModeChg
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 */
FUNC(void, TCPIPXCP_CODE) Xcp_SoConModeChg(SoAd_SoConIdType SoConId, SoAd_SoConModeType Mode) /* PRQA S 2889 */ /* MD_TCPIPXCP_2889 */
{
  /* Activation control */
#if ( TCPIPXCP_ENABLE_CONTROL == STD_ON )
  if ( TcpIpXcp_ControlState == kXcponTcpIp_Control_Disable )
  {
    TcpIpXcp_ControlDeniedCount++;
    return;
  }
#endif

  TcpIpXcp_CheckDetErrorReturnVoid( TCPIPXCP_UNINIT != TcpIpXcp_InitializationState,
                                    TCPIPXCP_SOCONMODECHG_SERVICE_ID, TCPIPXCP_E_NOT_INITIALIZED )

#if( TCPIPXCP_PROTOCOL_FORMAT == TCPIPXCP_PROTOCOL_TCP )
  if( SOAD_SOCON_OFFLINE == Mode )
  {
    uint8_least i;

    /* Search respective channel handle */
    for(i=0; i<TCPIPXCP_NUMBER_OF_CHANNELS; i++)
    {
      if( TcpIpXcp_GetSoConId(i) == SoConId )
      {
        break;
      }
    }

    if( i < TCPIPXCP_NUMBER_OF_CHANNELS )
    {
      TcpIpXcp_ConfTimeoutTimer[i] = (TcpIpXcp_TimeoutTimerType)0u;
      Xcp_Disconnect((uint8)(XCP_TRANSPORT_LAYER_TCPIP + i));
    }
  }
#else
  XCP_DUMMY_STATEMENT (SoConId); /* PRQA S 3112, 1338, 2983 */ /* MD_MSR_DummyStmt */ /*lint -e{438} */
  XCP_DUMMY_STATEMENT (Mode); /* PRQA S 3112, 1338, 2983 */ /* MD_MSR_DummyStmt */ /*lint -e{438} */
#endif
}
#endif

/***********************************************************************************************************************
 *  TcpIpXcp_Send
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 */
FUNC(void, TCPIPXCP_CODE) TcpIpXcp_Send( uint8 Xcp_Channel, uint16 len, P2CONST(uint8, AUTOMATIC, TCPIPXCP_APPL_DATA) msg ) /* PRQA S 2889 */ /* MD_TCPIPXCP_2889 */
{
  uint16 currentPos;
  uint8 actualChannel;
#if ( TCPIPXCP_CONCATENATION_MODE == STD_OFF )
  PduInfoType PduInfo;
#endif

  /* Activation control */
#if ( TCPIPXCP_ENABLE_CONTROL == STD_ON )
  if ( TcpIpXcp_ControlState == kXcponTcpIp_Control_Disable )
  {
    TcpIpXcp_ControlDeniedCount++;
    return;
  }
#endif

  TcpIpXcp_CheckDetErrorReturnVoid( TCPIPXCP_UNINIT != TcpIpXcp_InitializationState,
                                    TCPIPXCP_SEND_SERVICE_ID, TCPIPXCP_E_NOT_INITIALIZED )
  TcpIpXcp_CheckDetErrorReturnVoid( msg != (P2VAR(uint8, AUTOMATIC, TCPIPXCP_APPL_VAR))NULL_PTR,
                                    TCPIPXCP_SEND_SERVICE_ID, TCPIPXCP_E_NULL_POINTER )
/* Set correct channel index */
#if ( XCP_NUMBER_OF_CHANNELS <= 1u )
  XCP_DUMMY_STATEMENT (Xcp_Channel); /* PRQA S 3112, 1338, 2983 */ /* MD_MSR_DummyStmt */ /*lint -e{438} */
  actualChannel = (uint8)TcpIpXcp_RxPduId;
#else
  actualChannel = Xcp_Channel - XCP_TRANSPORT_LAYER_TCPIP; /* PRQA S 2985 */ /* MD_TCPIPXCP_2985_6 */
#endif

  TcpIpXcp_CheckDetErrorReturnVoid(actualChannel < TCPIPXCP_NUMBER_OF_CHANNELS,
                                   TCPIPXCP_SEND_SERVICE_ID, TCPIPXCP_E_INV_SOCK_IDX)

  /* Prepare TcpIpXcp Header and insert the XCP frame */
  currentPos = TcpIpXcp_ConcatPos[actualChannel];
  TcpIpXcp_TxFrameCache[actualChannel].fc[currentPos] = (uint8)(len & 0xffu);
  TcpIpXcp_TxFrameCache[actualChannel].fc[currentPos + 1u] = (uint8)(len >> 8);
  TcpIpXcp_TxFrameCache[actualChannel].fc[currentPos + 2u] = (uint8)(TcpIpXcp_SequenceCounter[actualChannel] & 0xffu);
  TcpIpXcp_TxFrameCache[actualChannel].fc[currentPos + 3u] = (uint8)(TcpIpXcp_SequenceCounter[actualChannel] >> 8);
  TcpIpXcp_SequenceCounter[actualChannel]++;

  TCPIPXCP_COPY(&(TcpIpXcp_TxFrameCache[actualChannel].fc[currentPos + TCPIPXCP_HEADER_SIZE]), msg, len);
  TcpIpXcp_ConcatPos[actualChannel] = currentPos + len + TCPIPXCP_HEADER_SIZE;

#if ( TCPIPXCP_CONCATENATION_MODE == STD_OFF )
  {
    /* And if required, we transmit it now */
    PduInfo.SduDataPtr = (P2VAR(uint8, AUTOMATIC, TCPIPXCP_APPL_VAR)) & TcpIpXcp_TxFrameCache[actualChannel].fc[0];
    PduInfo.SduLength = (PduLengthType)TcpIpXcp_ConcatPos[actualChannel];

# if ( TCPIPXCP_ENABLE_PDUMODE == STD_ON )
    if (TCPIPXCP_SET_ONLINE == TcpIpXcp_PduState[actualChannel])
    {
# endif
      TcpIpXcp_ConfTimeoutTimer[actualChannel] = (TcpIpXcp_TimeoutTimerType)TCPIPXCP_CONF_TIMEOUT;
      TcpIpXcp_ConcatPos[actualChannel] = 0u;
      if (SoAd_IfTransmit(TcpIpXcp_GetSoAdTxPduId(actualChannel), &PduInfo) == (Std_ReturnType)E_OK)
      {
        /* The message was sent successfully. */
      }
      else
      {
        /* Error. XCP Packet cannot be transmitted. */
        TcpIpXcp_ConfTimeoutTimer[actualChannel] = (TcpIpXcp_TimeoutTimerType)0u;
        TcpIpXcp_SendWithoutQueueState[actualChannel] = (uint8)TCPIPXCP_MSG_PENDING;
        TcpIpXcp_QueuedPduInfo[actualChannel] = PduInfo;
      }
# if ( TCPIPXCP_ENABLE_PDUMODE == STD_ON )
    }
    else
    {
      TcpIpXcp_SendWithoutQueueState[actualChannel] = (uint8)TCPIPXCP_MSG_PENDING;
      TcpIpXcp_QueuedPduInfo[actualChannel] = PduInfo;
    }
# endif
  }
#endif
}

/***********************************************************************************************************************
 *  TcpIpXcp_SendFlush
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 */
FUNC(void, TCPIPXCP_CODE) TcpIpXcp_SendFlush( uint8 Xcp_Channel, uint8 XcpFlushTypeSel ) /* PRQA S 2889 */ /* MD_TCPIPXCP_2889 */
{
  /* Activation control */
#if ( TCPIPXCP_ENABLE_CONTROL == STD_ON )
  if ( TcpIpXcp_ControlState == kXcponTcpIp_Control_Disable )
  {
    TcpIpXcp_ControlDeniedCount++;
    return;
  }
#endif

  TcpIpXcp_CheckDetErrorReturnVoid( TCPIPXCP_UNINIT != TcpIpXcp_InitializationState,
                                    TCPIPXCP_SENDFLUSH_SERVICE_ID, TCPIPXCP_E_NOT_INITIALIZED )

  XCP_DUMMY_STATEMENT (Xcp_Channel); /* PRQA S 3112, 1338, 2983 */ /* MD_MSR_DummyStmt */ /*lint -e{438} */  
  XCP_DUMMY_STATEMENT (XcpFlushTypeSel); /* PRQA S 3112, 1338, 2983 */ /* MD_MSR_DummyStmt */ /*lint -e{438} */
}

/***********************************************************************************************************************
 *  TcpIpXcp_TLService
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 */
FUNC(uint8, TCPIPXCP_CODE) TcpIpXcp_TLService( uint8 Xcp_Channel, P2CONST(uint8, AUTOMATIC, TCPIPXCP_APPL_DATA) pCmd ) /* PRQA S 2889 */ /* MD_TCPIPXCP_2889 */
{
  /* Activation control */
#if ( TCPIPXCP_ENABLE_CONTROL == STD_ON )
  if ( TcpIpXcp_ControlState == kXcponTcpIp_Control_Disable )
  {
    TcpIpXcp_ControlDeniedCount++;
    return(XCP_CMD_UNKNOWN);
  }
#endif

  TcpIpXcp_CheckDetErrorReturnValue( TCPIPXCP_UNINIT != TcpIpXcp_InitializationState,
                                     TCPIPXCP_TLSERVICE_SERVICE_ID, TCPIPXCP_E_NOT_INITIALIZED, (uint8)XCP_CMD_UNKNOWN )

  XCP_DUMMY_STATEMENT (Xcp_Channel); /* PRQA S 3112, 1338, 2983 */ /* MD_MSR_DummyStmt */ /*lint -e{438} */  
  XCP_DUMMY_STATEMENT (pCmd); /* PRQA S 3112, 1338, 2983 */ /* MD_MSR_DummyStmt */ /*lint -e{438} */
  return(XCP_CMD_UNKNOWN);
}

/***********************************************************************************************************************
 *  TcpIpXcp_MainFunction
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 */
FUNC(void, TCPIPXCP_CODE) TcpIpXcp_MainFunction( void ) /* PRQA S 2889 */ /* MD_TCPIPXCP_2889 */
{
  uint8_least i;
#if ( TCPIPXCP_CONCATENATION_MODE == STD_ON )
  PduInfoType PduInfo;
  boolean messagePending;
#endif

  /* Activation control */
#if ( TCPIPXCP_ENABLE_CONTROL == STD_ON )
  if ( TcpIpXcp_ControlState == kXcponTcpIp_Control_Disable )
  {
    TcpIpXcp_ControlDeniedCount++;
    return;
  }
#endif

  for( i = 0u; i < TCPIPXCP_NUMBER_OF_CHANNELS; i++ )
  {
#if ( TCPIPXCP_ENABLE_PDUMODE == STD_ON )
    if ( TCPIPXCP_SET_ONLINE == TcpIpXcp_PduState[i] )
#endif
    {
      /* If a message is pending, try to send it */
      if (TcpIpXcp_SendWithoutQueueState[i] == (uint8)TCPIPXCP_MSG_PENDING)
      {
        /* Clear the 'message pending' bit. */
        TcpIpXcp_SendWithoutQueueState[i] = (uint8)TCPIPXCP_MSG_IDLE;
        /* Try to transmit the XCP Packet */
        TcpIpXcp_ConfTimeoutTimer[i] = (TcpIpXcp_TimeoutTimerType)TCPIPXCP_CONF_TIMEOUT;
        TcpIpXcp_ConcatPos[i] = 0u;
        if (SoAd_IfTransmit(TcpIpXcp_GetSoAdTxPduId(i), &TcpIpXcp_QueuedPduInfo[i]) == (Std_ReturnType)E_OK)
        {
          /* The message was sent successfully. */
        }
        else
        {
          /* The message is still pending. */
          TcpIpXcp_SendWithoutQueueState[i] = (uint8)TCPIPXCP_MSG_PENDING;
          TcpIpXcp_ConfTimeoutTimer[i] = (TcpIpXcp_TimeoutTimerType)0u;
        }
      }
      
#if ( TCPIPXCP_CONCATENATION_MODE == STD_ON )
      if ((TcpIpXcp_SendWithoutQueueState[i] == (uint8)TCPIPXCP_MSG_IDLE)
       && (TcpIpXcp_ConfTimeoutTimer[i] == 0u))
      {
        /* Concatenation logic, we insert the next frame, as long as there is enough space in the Eth PDU */
        messagePending = Xcp_TlQueryMessagePending((uint8)(XCP_TRANSPORT_LAYER_TCPIP + i)); /* PRQA S 2986 */ /* MD_TCPIPXCP_2985_6 */
        /* PRQA S 3494 1 */ /* MD_TCPIPXCP_3494 */
        while ((messagePending == TRUE) && ((TCPIPXCP_PDU_SIZE - TcpIpXcp_ConcatPos[i]) >= (TCPIPXCP_MAX(kTcpIpXcpMaxCTO, kTcpIpXcpMaxDTO) + TCPIPXCP_HEADER_SIZE)))
        {
          /* Insert next frame */
          (void)Xcp_SendCallBack((uint8)(XCP_TRANSPORT_LAYER_TCPIP + i)); /* PRQA S 2986 */ /* MD_TCPIPXCP_2985_6 */
          messagePending = Xcp_TlQueryMessagePending((uint8)(XCP_TRANSPORT_LAYER_TCPIP + i)); /* PRQA S 2986 */ /* MD_TCPIPXCP_2985_6 */
        }

        if (TcpIpXcp_ConcatPos[i] > 0u)
        {
          /* And if required, we transmit it now */
          PduInfo.SduDataPtr = (P2VAR(uint8, AUTOMATIC, TCPIPXCP_APPL_VAR)) & TcpIpXcp_TxFrameCache[i].fc[0];
          PduInfo.SduLength = (PduLengthType)TcpIpXcp_ConcatPos[i];

          TcpIpXcp_ConfTimeoutTimer[i] = (TcpIpXcp_TimeoutTimerType)TCPIPXCP_CONF_TIMEOUT;
          TcpIpXcp_ConcatPos[i] = 0u;
          if (SoAd_IfTransmit(TcpIpXcp_GetSoAdTxPduId(i), &PduInfo) == (Std_ReturnType)E_OK)
          {
            /* The message was sent successfully. */
          }
          else
          {
            /* Error. XCP Packet cannot be transmitted. */
            TcpIpXcp_ConfTimeoutTimer[i] = (TcpIpXcp_TimeoutTimerType)0u;
            TcpIpXcp_SendWithoutQueueState[i] = (uint8)TCPIPXCP_MSG_PENDING;
            TcpIpXcp_QueuedPduInfo[i] = PduInfo;
          }
        }
      }
#endif
    }

    TcpIpXcp_EnterCritical();
    if( TcpIpXcp_ConfTimeoutTimer[i] > 0u)
    {
      TcpIpXcp_ConfTimeoutTimer[i] = TcpIpXcp_ConfTimeoutTimer[i] - 1u;
      TcpIpXcp_LeaveCritical();
      if( 0u == TcpIpXcp_ConfTimeoutTimer[i] )
      {
        /* Timeout happened, release PDU */
        (void)Xcp_SendCallBack((uint8)(XCP_TRANSPORT_LAYER_TCPIP + i)); /* PRQA S 2986 */ /* MD_TCPIPXCP_2985_6 */
      }
    }
    else
    {
      TcpIpXcp_LeaveCritical();
    }

    /* Trigger pending DTOs */
    Xcp_TlMainFunction((uint8)(XCP_TRANSPORT_LAYER_TCPIP + i)); /* PRQA S 2986 */ /* MD_TCPIPXCP_2985_6 */
  }
  /* PRQA S 6010 3 */ /* MD_MSR_STPTH */
  /* PRQA S 6030 2 */ /* MD_MSR_STCYC */
  /* PRQA S 6080 1 */ /* MD_MSR_STMIF */
}

/***********************************************************************************************************************
 *  TcpIpXcp_InitMemory
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 */
FUNC(void, TCPIPXCP_CODE) TcpIpXcp_InitMemory( void )
{
#if ( TCPIPXCP_ENABLE_CONTROL == STD_ON )
  TcpIpXcp_ControlState = (uint8)kXcponTcpIp_Control_Enable;
  TcpIpXcp_ControlDeniedCount = 0u;
#endif

#if ( TCPIPXCP_DEV_ERROR_DETECT == STD_ON )
  TcpIpXcp_InitializationState = TCPIPXCP_UNINIT;
#endif
}

/***********************************************************************************************************************
 *  TcpIpXcp_Init
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 */
FUNC(void, TCPIPXCP_CODE) TcpIpXcp_Init( P2CONST(TcpIpXcp_ConfigType, AUTOMATIC, TCPIPXCP_PBCFG) ConfigPtr ) /* PRQA S 2889 */ /* MD_TCPIPXCP_2889 */
{
  uint8_least i;

  /* Activation control */
#if ( TCPIPXCP_ENABLE_CONTROL == STD_ON )
  if ( TcpIpXcp_ControlState == kXcponTcpIp_Control_Disable )
  {
    TcpIpXcp_ControlDeniedCount++;
    return;
  }
#endif

#if ( TCPIPXCP_DEV_ERROR_DETECT == STD_ON )
  TcpIpXcp_InitializationState = TCPIPXCP_UNINIT;
#endif

#if ( TCPIPXCP_CONFIG_VARIANT == 3u )
  TcpIpXcp_Configuration = ConfigPtr;
#else
  XCP_DUMMY_STATEMENT (ConfigPtr); /* PRQA S 3112, 1338, 2983 */ /* MD_MSR_DummyStmt */ /*lint -e{438} */
#endif

  /* Initialize static variables. */
#if ( XCP_NUMBER_OF_CHANNELS <= 1u )
  TcpIpXcp_RxPduId = TcpIpXcp_GetRxPduId(0);
#endif
  for( i = 0u; i < TCPIPXCP_NUMBER_OF_CHANNELS; i++ )
  {
#if ( TCPIPXCP_ENABLE_PDUMODE == STD_ON )
    TcpIpXcp_PduState[i] = (uint8)TCPIPXCP_SET_OFFLINE;
#endif
    /* Set the length to 0 */
    TcpIpXcp_ConfTimeoutTimer[i] = 0u;
    TcpIpXcp_SequenceCounter[i] = 0u;
    TcpIpXcp_SendWithoutQueueState[i] = (uint8)TCPIPXCP_MSG_IDLE;
    TcpIpXcp_ConcatPos[i] = 0u;
  }

#if ( TCPIPXCP_DEV_ERROR_DETECT == STD_ON )
  TcpIpXcp_InitializationState = TCPIPXCP_INIT;
#endif
}

#if ( TCPIPXCP_VERSION_INFO_API == STD_ON )
/***********************************************************************************************************************
 *  TcpIpXcp_GetVersionInfo
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 */
FUNC(void, TCPIPXCP_CODE) TcpIpXcp_GetVersionInfo(P2VAR(Std_VersionInfoType, AUTOMATIC, TCPIPXCP_APPL_DATA) versioninfo) /* PRQA S 2889 */ /* MD_TCPIPXCP_2889 */
{
  /* Activation control */
#if ( TCPIPXCP_ENABLE_CONTROL == STD_ON )
  if ( TcpIpXcp_ControlState == kXcponTcpIp_Control_Disable )
  {
    TcpIpXcp_ControlDeniedCount++;
    return;
  }
#endif

  TcpIpXcp_CheckDetErrorReturnVoid( versioninfo != (P2VAR(Std_VersionInfoType, AUTOMATIC, TCPIPXCP_APPL_VAR))NULL_PTR,
                                  TCPIPXCP_GETVERSIONINFO_SERVICE_ID, TCPIPXCP_E_NULL_POINTER )

  versioninfo->vendorID         = TCPIPXCP_VENDOR_ID;
  versioninfo->moduleID         = TCPIPXCP_MODULE_ID;
  versioninfo->sw_major_version = (uint8)TCPIPXCP_SW_MAJOR_VERSION;
  versioninfo->sw_minor_version = (uint8)TCPIPXCP_SW_MINOR_VERSION;
  versioninfo->sw_patch_version = (uint8)TCPIPXCP_SW_PATCH_VERSION;
}
#endif


#if ( TCPIPXCP_ENABLE_PDUMODE == STD_ON )
/***********************************************************************************************************************
 *  TcpIpXcp_SetPduMode
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 */
FUNC(void, TCPIPXCP_CODE) TcpIpXcp_SetPduMode( NetworkHandleType XcpNwH, TcpIpXcp_PduSetModeType PduMode )
{
  TcpIpXcp_CheckDetErrorReturnVoid( TCPIPXCP_UNINIT != TcpIpXcp_InitializationState,
                                    TCPIPXCP_SETPDUMODE_SERVICE_ID, TCPIPXCP_E_NOT_INITIALIZED )


  TcpIpXcp_PduState[XcpNwH] = PduMode;
}
#endif


#if defined ( XCP_ENABLE_DAQ ) && defined ( XCP_ENABLE_DAQ_RESUME )
/***********************************************************************************************************************
 *  TcpIpXcp_DaqResumeGet
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 */
FUNC(void, TCPIPXCP_CODE) TcpIpXcp_DaqResumeGet( CONSTP2VAR(SoAd_SockAddrIn6Type, AUTOMATIC, TCPIPXCP_APPL_DATA) resumeData ) /* PRQA S 2889 */ /* MD_TCPIPXCP_2889 */
{
  uint8_least i;

  /* Activation control */
#if ( TCPIPXCP_ENABLE_CONTROL == STD_ON )
  if ( TcpIpXcp_ControlState == kXcponTcpIp_Control_Disable )
  {
    TcpIpXcp_ControlDeniedCount++;
    return;
  }
#endif

  TcpIpXcp_CheckDetErrorReturnVoid( TCPIPXCP_UNINIT != TcpIpXcp_InitializationState,
                                    TCPIPXCP_DAQRESUMEGET_SERVICE_ID, TCPIPXCP_E_NOT_INITIALIZED )
  TcpIpXcp_CheckDetErrorReturnVoid( resumeData != (CONSTP2VAR(SoAd_SockAddrIn6Type, AUTOMATIC, TCPIPXCP_APPL_DATA))NULL_PTR,
                                    TCPIPXCP_DAQRESUMEGET_SERVICE_ID, TCPIPXCP_E_NULL_POINTER )

  for(i=0; i<TCPIPXCP_NUMBER_OF_CHANNELS; i++)
  {
    (void)SoAd_GetRemoteAddr(TcpIpXcp_GetSoConId(i), (CONSTP2VAR(SoAd_SockAddrType, AUTOMATIC, TCPIPXCP_APPL_DATA))&(resumeData[i]));
  }
}


/***********************************************************************************************************************
 *  TcpIpXcp_DaqResumeStore
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 */
FUNC(void, TCPIPXCP_CODE) TcpIpXcp_DaqResumeStore( CONSTP2CONST(SoAd_SockAddrIn6Type, AUTOMATIC, TCPIPXCP_APPL_DATA) resumeData ) /* PRQA S 2889 */ /* MD_TCPIPXCP_2889 */
{
  uint8_least i;

  /* Activation control */
#if ( TCPIPXCP_ENABLE_CONTROL == STD_ON )
  if ( TcpIpXcp_ControlState == kXcponTcpIp_Control_Disable )
  {
    TcpIpXcp_ControlDeniedCount++;
    return;
  }
#endif

  TcpIpXcp_CheckDetErrorReturnVoid( TCPIPXCP_UNINIT != TcpIpXcp_InitializationState,
                                    TCPIPXCP_DAQRESUMESTORE_SERVICE_ID, TCPIPXCP_E_NOT_INITIALIZED )
  TcpIpXcp_CheckDetErrorReturnVoid( resumeData != (CONSTP2CONST(SoAd_SockAddrIn6Type, AUTOMATIC, TCPIPXCP_APPL_DATA))NULL_PTR,
                                    TCPIPXCP_DAQRESUMESTORE_SERVICE_ID, TCPIPXCP_E_NULL_POINTER )

  for(i=0; i<TCPIPXCP_NUMBER_OF_CHANNELS; i++)
  {
    (void)SoAd_SetRemoteAddr(TcpIpXcp_GetSoConId(i), (CONSTP2CONST(SoAd_SockAddrType, AUTOMATIC, TCPIPXCP_APPL_DATA))&(resumeData[i]));
  }
}
#endif /* defined ( XCP_ENABLE_DAQ ) && defined ( XCP_ENABLE_DAQ_RESUME ) */


#define TCPIPXCP_STOP_SEC_CODE
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


/* module specific MISRA deviations:
  MD_TCPIPXCP_0310: Rule 11.3
      Reason:     pointer cast has no side effects if underlying layer aligns the buffer accordingly.
      Risk:       If this option is enabled and the buffer is not aligned a trap will occur.
      Prevention: Optional feature

  MD_TCPIPXCP_1338: Rule 17.8
      Reason:     Parameter is modified to keep function simple and readable.
      Risk:       None.
      Prevention: Not required.

  MD_TCPIPXCP_2985_6: Rule 2.2
      Reason:     generated code might have 0 as value which leads to this warning.
      Risk:       No Risk.
      Prevention: Not required.

  MD_TCPIPXCP_2889: Rule 15.5
      Reason:     API pattern not applied in this component yet.
      Risk:       Code understandability and maintainability reduced.
      Prevention: Covered by code review.

  MD_TCPIPXCP_3218_FileScopeStatic: Rule 8.9
      Reason:     To provide AUTOSAR memory mapping a global variable is required.
      Risk:       No Risk.
      Prevention: Not required.

  MD_TCPIPXCP_3305: Rule 11.3
      Reason:     A uint8 pointer is cast to a uint32 pointer.
      Risk:       Misaligned access might happen.
      Prevention: It is guaranteed in code that access is always 16bit aligned. See XCP_START_SEC_VAR_NOCACHE_NOINIT_32BIT.
                  Covered by review.

  MD_TCPIPXCP_3408: Rule 8.4
      Reason:     The variable is not defined as static to be accessible for measurement. No prototype is required for this.
      Risk:       There is no risk as this variable is not accessed externally.
      Prevention: Not required.

  MD_TCPIPXCP_3493_4: Rule 14.3
      Reason:     A pre-compile macros is used to determine parameters based on configuration.
      Risk:       There is no risk as this is a valid operation and performed by pre-processor.
      Prevention: Not required.

  MD_TCPIPXCP_3494: Rule 14.3
      Reason:     A variable is modified in another function.
      Risk:       There is no risk as the variable is set to volatile.
      Prevention: Not required.

*/

/* COV_JUSTIFICATION_BEGIN

\ID COV_TCPIPXCP_MSR_COMPATIBILITY
\ACCEPT TX
\ACCEPT XF
\REASON [COV_MSR_COMPATIBILITY]

\ID COV_TCPIPXCP_MSR3_COMPATIBILITY
\ACCEPT XF
\REASON [COV_MSR_COMPATIBILITY]

COV_JUSTIFICATION_END */


/**********************************************************************************************************************
 *  END OF FILE: TcpIpXcp.C
 *********************************************************************************************************************/
