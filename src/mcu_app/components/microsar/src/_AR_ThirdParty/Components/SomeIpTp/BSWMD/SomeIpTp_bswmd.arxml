<?xml version="1.0" encoding="UTF-8"?>
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-3-0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://autosar.org/schema/r4.0">
	<AR-PACKAGES>
		<AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
			<SHORT-NAME>MICROSAR</SHORT-NAME>
			<ELEMENTS>
				<ECUC-MODULE-DEF UUID="2a1aa01e-a087-499e-a6f3-f5eb34fde39b">
					<SHORT-NAME>SomeIpTp</SHORT-NAME>
					<DESC>
						<L-2 L="EN">Configuration of the SomeIpTp module.</L-2>
					</DESC>
					<CATEGORY>VENDOR_SPECIFIC_MODULE_DEFINITION</CATEGORY>
					<ADMIN-DATA>
						<DOC-REVISIONS>
							<DOC-REVISION>
								<REVISION-LABEL>1.0.0</REVISION-LABEL>
								<ISSUED-BY>AUTOSAR</ISSUED-BY>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.1.0</REVISION-LABEL>
								<ISSUED-BY>vissuk</ISSUED-BY>
								<DATE>2018-01-19T10:46:04+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Added base unit to timing parameters and a switch for runtime error reporting</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00097733</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.1.1</REVISION-LABEL>
								<ISSUED-BY>visath</ISSUED-BY>
								<DATE>2018-02-16T13:28:07+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SomeIpTpTxNSduHandleId and SomeIpTpTxNPduHandleId are now mandatory</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00098426</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.1.2</REVISION-LABEL>
								<ISSUED-BY>vissuk</ISSUED-BY>
								<DATE>2018-09-24T12:49:09+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SOMEIPTP90500: A generated value is not in range of the specified datatype</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00100608</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.1.2</REVISION-LABEL>
								<ISSUED-BY>visath</ISSUED-BY>
								<DATE>2018-09-26T12:50:10+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">It's not possible to add user configuration files</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00100624</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>2.0.0</REVISION-LABEL>
								<ISSUED-BY>vissuk</ISSUED-BY>
								<DATE>2020-03-04T16:03:12+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Added new parameter SomeIpTpTxBurstSize</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>3.0.0</REVISION-LABEL>
								<ISSUED-BY>vissuk</ISSUED-BY>
								<DATE>2020-09-09T10:18:46+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Updated Impl version</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">SWAT-954</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
						</DOC-REVISIONS>
					</ADMIN-DATA>
					<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SomeIpTp_00001</RELATED-TRACE-ITEM-REF>
					<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
					<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
					<POST-BUILD-VARIANT-SUPPORT>true</POST-BUILD-VARIANT-SUPPORT>
					<REFINED-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/SomeIpTp</REFINED-MODULE-DEF-REF>
					<SUPPORTED-CONFIG-VARIANTS>
						<SUPPORTED-CONFIG-VARIANT>VARIANT-LINK-TIME</SUPPORTED-CONFIG-VARIANT>
						<SUPPORTED-CONFIG-VARIANT>VARIANT-POST-BUILD</SUPPORTED-CONFIG-VARIANT>
						<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
					</SUPPORTED-CONFIG-VARIANTS>
					<CONTAINERS>
						<!-- Container Definition: SomeIpTpChannel -->
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="5122ea80-4544-444c-b257-552f508c2dd2">
							<SHORT-NAME>SomeIpTpChannel</SHORT-NAME>
							<DESC>
								<L-2 L="EN">This container contains the configuration parameters of the SomeIpTp channel.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">true</SD>
										<SD GID="DV:postBuildNotDeletable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SomeIpTp_00003</RELATED-TRACE-ITEM-REF>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
							<MULTIPLICITY-CONFIG-CLASSES>
								<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									<CONFIG-CLASS>LINK</CONFIG-CLASS>
									<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
								</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
								<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
									<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
								</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
								<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
									<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
								</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
							</MULTIPLICITY-CONFIG-CLASSES>
							<POST-BUILD-VARIANT-MULTIPLICITY>true</POST-BUILD-VARIANT-MULTIPLICITY>
							<REQUIRES-INDEX>false</REQUIRES-INDEX>
							<PARAMETERS>
								<!-- PARAMETER DEFINITION: SomeIpTpNPduSeparationTime -->
								<ECUC-FLOAT-PARAM-DEF UUID="19ba0354-a0a0-46c3-84ff-b8047e8061f8">
									<SHORT-NAME>SomeIpTpNPduSeparationTime</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Sets the duration of the minimum time in seconds the SomeIpTp module shall wait between the transmissions of N-PDUs.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:BaseUnit">SEC</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SomeIpTp_00006</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>LINK</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0.001</DEFAULT-VALUE>
									<MAX INTERVAL-TYPE="OPEN">65.535</MAX>
									<MIN INTERVAL-TYPE="OPEN">0</MIN>
								</ECUC-FLOAT-PARAM-DEF>
								<!-- PARAMETER DEFINITION: SomeIpTpRxTimeoutTime -->
								<ECUC-FLOAT-PARAM-DEF UUID="d78d4ec0-f952-43e5-8976-7efebff5c2b1">
									<SHORT-NAME>SomeIpTpRxTimeoutTime</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Timer to monitor the successful reception. It is started when the first NPdu
is received, restarted after reception of intermediate NPdus, and is stopped when the last NPdu has been received.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:BaseUnit">SEC</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SomeIpTp_00023</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>LINK</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0.5</DEFAULT-VALUE>
									<MAX INTERVAL-TYPE="OPEN">65.535</MAX>
									<MIN INTERVAL-TYPE="OPEN">0</MIN>
								</ECUC-FLOAT-PARAM-DEF>
								<ECUC-FLOAT-PARAM-DEF UUID="e1ce68a7-6289-4ebe-a46f-a86fb845ce73">
									<SHORT-NAME>SomeIpTpTxConfTimeoutTime</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Tx Confirmation Timeout Time</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Timer to monitor the successful Transmission Confirmation. It is started when the NPdu is triggered and is stopped when the Confirmation has been received.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:BaseUnit">SEC</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>LINK</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0.5</DEFAULT-VALUE>
									<MAX INTERVAL-TYPE="OPEN">65.535</MAX>
									<MIN INTERVAL-TYPE="OPEN">0</MIN>
								</ECUC-FLOAT-PARAM-DEF>
							</PARAMETERS>
							<SUB-CONTAINERS>
								<!-- Container Definition: SomeIpTpRxNSdu -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="f1c7caab-8f29-49df-b206-27162fa231d8">
									<SHORT-NAME>SomeIpTpRxNSdu</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The following parameters needs to be configured for each N-SDU which has to be passed as one assembled RxPdu to the upper layer.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">true</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SomeIpTp_00008</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>LINK</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>true</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<REFERENCES>
										<!-- Reference Definition: SomeIpTpRxSduRef -->
										<ECUC-REFERENCE-DEF UUID="4d96cf63-35a5-4d1e-9a26-b0aa930c9af3">
											<SHORT-NAME>SomeIpTpRxSduRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to a Pdu in the COM-Stack that represents the assembled RxPdu which is passed via the PduR to the upper layer.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SomeIpTp_00010</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<REQUIRES-SYMBOLIC-NAME-VALUE>false</REQUIRES-SYMBOLIC-NAME-VALUE>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<!-- Container Definition: SomeIpTpRxNPdu -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="65ecbcf0-244a-4e14-bcf2-34d00966b846">
											<SHORT-NAME>SomeIpTpRxNPdu</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the configuration parameters of the NPdu that is received from a lower layer</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">true</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SomeIpTp_00011</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: SomeIpTpRxNPduHandleId -->
												<ECUC-INTEGER-PARAM-DEF UUID="83ff3ffb-b840-4a4f-8b15-c3590226eff2">
													<SHORT-NAME>SomeIpTpRxNPduHandleId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines the handle ID that is used by the PduR when calling SomeIpTp_RxIndication.</L-2>
													</DESC>
													<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SomeIpTp_00013</RELATED-TRACE-ITEM-REF>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: SomeIpTpRxNPduRef -->
												<ECUC-REFERENCE-DEF UUID="01e4ff6f-ae20-4142-acfc-81b2a92162b1">
													<SHORT-NAME>SomeIpTpRxNPduRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to a global Pdu that is used to harmonize HandleIDs in the COM-Stack.</L-2>
													</DESC>
													<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SomeIpTp_00012</RELATED-TRACE-ITEM-REF>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<REQUIRES-SYMBOLIC-NAME-VALUE>false</REQUIRES-SYMBOLIC-NAME-VALUE>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: SomeIpTpTxNSdu -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="68f189b3-61db-41c6-adfd-5c697aa3a6cc">
									<SHORT-NAME>SomeIpTpTxNSdu</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The following parameters needs to be configured for each N-SDU that the SomeIpTp module transmits via the SomeIpTpChannel.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">true</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SomeIpTp_00009</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>LINK</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<POST-BUILD-VARIANT-MULTIPLICITY>true</POST-BUILD-VARIANT-MULTIPLICITY>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: SomeIpTpTxNSduHandleId -->
										<ECUC-INTEGER-PARAM-DEF UUID="e97aac55-a600-414b-9967-f3844c2347d0">
											<SHORT-NAME>SomeIpTpTxNSduHandleId</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the handle ID of the NSdu that represents the original TxSdu which is segmented and passed via the PduR to the lower layer. This handle ID is used by PduR when calling SomeIpTp_Transmit.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SomeIpTp_00020</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="e2365eb8-a6dd-41ed-9b02-ae1113810a5b">
											<SHORT-NAME>SomeIpTpTxBurstSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Number of SomeIpTp segments transmitted consecutively in the SomeIpTp mainfunction without waiting for separation time.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>1</DEFAULT-VALUE>
											<MAX>255</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Reference Definition: SomeIpTpTxNSduRef -->
										<ECUC-REFERENCE-DEF UUID="fca22875-05a8-4e2a-aa49-e93317bf028e">
											<SHORT-NAME>SomeIpTpTxNSduRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to a global Pdu in the COM-Stack that represents the original TxSdu which is segmented and passed via the PduR to the lower layer.</L-2>
											</DESC>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SomeIpTp_00015</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<SCOPE>LOCAL</SCOPE>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<VALUE-CONFIG-CLASSES>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>LINK</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
												<ECUC-VALUE-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-VALUE-CONFIGURATION-CLASS>
											</VALUE-CONFIG-CLASSES>
											<REQUIRES-SYMBOLIC-NAME-VALUE>false</REQUIRES-SYMBOLIC-NAME-VALUE>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<!-- Container Definition: SomeIpTpTxNPdu -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="2f254c47-6ac8-4d75-96ad-a0d32e40f392">
											<SHORT-NAME>SomeIpTpTxNPdu</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container contains the configuration parameters of the segmented Tx NPdus that are transmitted to a lower layer.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">true</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SomeIpTp_00016</RELATED-TRACE-ITEM-REF>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<MULTIPLICITY-CONFIG-CLASSES>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
												<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											</MULTIPLICITY-CONFIG-CLASSES>
											<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: SomeIpTpTxNPduHandleId -->
												<ECUC-INTEGER-PARAM-DEF UUID="b7bf3441-2417-4799-84d7-565edd283f21">
													<SHORT-NAME>SomeIpTpTxNPduHandleId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines the handle ID that is used by PduR when calling SomeIpTp_TriggerTransmit.</L-2>
													</DESC>
													<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SomeIpTp_00017</RELATED-TRACE-ITEM-REF>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: SomeIpTpTxNPduRef -->
												<ECUC-REFERENCE-DEF UUID="17640361-f9f2-420a-a927-e9229b5c0386">
													<SHORT-NAME>SomeIpTpTxNPduRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to a global Pdu that is used to harmonize HandleIDs in the COM-Stack.</L-2>
													</DESC>
													<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SomeIpTp_00018</RELATED-TRACE-ITEM-REF>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<SCOPE>LOCAL</SCOPE>
													<MULTIPLICITY-CONFIG-CLASSES>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
														<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
													</MULTIPLICITY-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
													<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<VALUE-CONFIG-CLASSES>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>LINK</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
														<ECUC-VALUE-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-VALUE-CONFIGURATION-CLASS>
													</VALUE-CONFIG-CLASSES>
													<REQUIRES-SYMBOLIC-NAME-VALUE>false</REQUIRES-SYMBOLIC-NAME-VALUE>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</SUB-CONTAINERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
						<!-- Container Definition: SomeIpTpGeneral -->
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="69ea9f82-5370-41aa-b68d-e4676d8131b5">
							<SHORT-NAME>SomeIpTpGeneral</SHORT-NAME>
							<DESC>
								<L-2 L="EN">This container contains the general configuration parameters of the SomeIpTp module.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">true</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SomeIpTp_00002</RELATED-TRACE-ITEM-REF>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<MULTIPLICITY-CONFIG-CLASSES>
								<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
									<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
								</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
								<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
									<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
								</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
								<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
									<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
								</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
							</MULTIPLICITY-CONFIG-CLASSES>
							<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
							<REQUIRES-INDEX>false</REQUIRES-INDEX>
							<PARAMETERS>
								<!-- PARAMETER DEFINITION: SomeIpTpDevErrorDetect -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="5bc7d379-fe50-4290-a732-625c6846f939">
									<SHORT-NAME>SomeIpTpDevErrorDetect</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Switches the Development Error Detection and Notification ON or OFF.</L-2>
									</DESC>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SomeIpTp_00004</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: SomeIpTpRxMainFunctionPeriod -->
								<ECUC-FLOAT-PARAM-DEF UUID="30425de9-3d10-4af0-a702-1de07ddcefeb">
									<SHORT-NAME>SomeIpTpRxMainFunctionPeriod</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter defines the cycle time in seconds of the periodic call of the SomeIpTp_MainFunctionRx.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:BaseUnit">SEC</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SomeIpTp_00021</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0.001</DEFAULT-VALUE>
									<MAX INTERVAL-TYPE="OPEN">1.7976931348623157E+308</MAX>
									<MIN INTERVAL-TYPE="OPEN">0</MIN>
								</ECUC-FLOAT-PARAM-DEF>
								<!-- PARAMETER DEFINITION: SomeIpTpTxMainFunctionPeriod -->
								<ECUC-FLOAT-PARAM-DEF UUID="bba29731-fcaf-42a7-af94-e12024fbfc32">
									<SHORT-NAME>SomeIpTpTxMainFunctionPeriod</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter defines the cycle time in seconds of the periodic call of the SomeIpTp_MainFunctionTx.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:BaseUnit">SEC</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SomeIpTp_00005</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0.001</DEFAULT-VALUE>
									<MAX INTERVAL-TYPE="OPEN">1.7976931348623157E+308</MAX>
									<MIN INTERVAL-TYPE="OPEN">0</MIN>
								</ECUC-FLOAT-PARAM-DEF>
								<!-- PARAMETER DEFINITION: SomeIpTpVersionInfoApi -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="7945609a-7c85-4b4f-8c7c-06dc6c6299c8">
									<SHORT-NAME>SomeIpTpVersionInfoApi</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Activates the SomeIpTp_GetVersionInfo() API.</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">TRUE: Enables the SomeIpTp_GetVersionInfo() API.
                                        FALSE: SomeIpTp_GetVersionInfo() API is not included.</L-1>
										</P>
									</INTRODUCTION>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SomeIpTp_00019</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="68eacd33-ad73-4c95-a249-567f4ac3baf0">
									<SHORT-NAME>SomeIpTpRuntimeErrorReport</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Switches the Runtime Error Notification ON or OFF.</L-2>
									</DESC>
									<RELATED-TRACE-ITEM-REF BASE="ArTrace" DEST="TRACEABLE">ECUC_SomeIpTp_00004</RELATED-TRACE-ITEM-REF>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-STRING-PARAM-DEF UUID="0ec053fd-2c98-472e-a611-4a150f3b8bcb">
									<SHORT-NAME>SomeIpTpUserConfigFile</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Reference to an external text file that will be included during generation to SomeIpTp_Cfg.h.
The content of the user configuration file will be added at the end of the generated module configuration file and allows altering or extending the generated code.
Caution: User configuration files can cause the software module to malfunction and must only be used with great care!
</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>LINK</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>true</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
								</ECUC-STRING-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="2754de1b-bb4e-48aa-911d-14f268f7964c">
									<SHORT-NAME>SomeIpTpSafeBswChecks</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Selects if safety checks are used. These checks improve robustness as e.g. invalid parameters are checked before function is executed. This attribute has to be enabled for safety projects where this component is mapped to an ASIL partition.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<SCOPE>LOCAL</SCOPE>
									<MULTIPLICITY-CONFIG-CLASSES>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
										<ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
									</MULTIPLICITY-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
									<POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<VALUE-CONFIG-CLASSES>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
										<ECUC-VALUE-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
										</ECUC-VALUE-CONFIGURATION-CLASS>
									</VALUE-CONFIG-CLASSES>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
							</PARAMETERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
					</CONTAINERS>
				</ECUC-MODULE-DEF>
				<BSW-IMPLEMENTATION UUID="96c1d100-db3b-4d49-bddb-be13c0335c68">
					<SHORT-NAME>SomeIpTp_Impl</SHORT-NAME>
					<PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
					<SW-VERSION>4.00.00</SW-VERSION>
					<USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
					<VENDOR-ID>30</VENDOR-ID>
					<AR-RELEASE-VERSION>4.03.01</AR-RELEASE-VERSION>
					<BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/SomeIpTp_ib_bswmd/BswModuleDescriptions/SomeIpTp/SomeIpTpBehavior</BEHAVIOR-REF>
					<PRECONFIGURED-CONFIGURATION-REFS>
						<PRECONFIGURED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/SomeIpTp_Pre</PRECONFIGURED-CONFIGURATION-REF>
					</PRECONFIGURED-CONFIGURATION-REFS>
					<RECOMMENDED-CONFIGURATION-REFS>
						<RECOMMENDED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/SomeIpTp_Rec</RECOMMENDED-CONFIGURATION-REF>
					</RECOMMENDED-CONFIGURATION-REFS>
					<VENDOR-SPECIFIC-MODULE-DEF-REFS>
						<VENDOR-SPECIFIC-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/MICROSAR/SomeIpTp</VENDOR-SPECIFIC-MODULE-DEF-REF>
					</VENDOR-SPECIFIC-MODULE-DEF-REFS>
				</BSW-IMPLEMENTATION>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="5f661863-677e-4a1f-9790-a5d7b2888bfd">
					<SHORT-NAME>SomeIpTp_Pre</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/SomeIpTp</DEFINITION-REF>
					<CONTAINERS />
				</ECUC-MODULE-CONFIGURATION-VALUES>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="da948649-8415-4dab-a02b-b275848c84b2">
					<SHORT-NAME>SomeIpTp_Rec</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/SomeIpTp</DEFINITION-REF>
					<CONTAINERS />
				</ECUC-MODULE-CONFIGURATION-VALUES>
			</ELEMENTS>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>