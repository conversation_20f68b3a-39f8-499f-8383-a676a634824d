/***********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *  Copyright (c) 2020 by Vector Informatik GmbH.                                                  All rights reserved.
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -----------------------------------------------------------------------------------------------------------------*/
/*!        \file  SomeIpTp.c
 *        \brief  SomeIpTp source file
 *      \details  Implementation of Some/IP Transport protocol.
 **********************************************************************************************************************/

/***********************************************************************************************************************
 *  REVISION HISTORY
 *  -------------------------------------------------------------------------------------------------------------------
 *  Refer to the module's header file.
 * 
 *  FILE VERSION
 *  -------------------------------------------------------------------------------------------------------------------
 *  Refer to the VERSION CHECK below.
 **********************************************************************************************************************/

#define SOMEIPTP_SOURCE

/***********************************************************************************************************************
 *  INCLUDES
 **********************************************************************************************************************/
#include "SomeIpTp.h"
#include "SomeIpTp_Priv.h"
#if (SOMEIPTP_USE_INIT_POINTER == STD_ON)
#include "EcuM_Error.h"
#endif
#include "SomeIpTp_Cbk.h"
#include "SchM_SomeIpTp.h"
#include "PduR_SomeIpTp.h"
#if ((SOMEIPTP_DEV_ERROR_REPORT == STD_ON) || (SOMEIPTP_RUNTIME_ERROR_REPORT == STD_ON))
#include "Det.h"
#endif

/***********************************************************************************************************************
 *  VERSION CHECK
 **********************************************************************************************************************/
/* Check the version of SomeIpTp header file */
#if (  (SOMEIPTP_SW_MAJOR_VERSION != (4u)) \
    || (SOMEIPTP_SW_MINOR_VERSION != (0u)) \
    || (SOMEIPTP_SW_PATCH_VERSION != (0u)) )
# error "Vendor specific version numbers of SomeIpTp.c and SomeIpTp.h are inconsistent"
#endif

/* Check the version of the configuration header file */
#if (  (SOMEIPTP_CFG_GEN_MAJOR_VERSION != (3u)) \
    || (SOMEIPTP_CFG_GEN_MINOR_VERSION != (0u)) )
# error "Version numbers of SomeIpTp.c and SomeIpTp_Cfg.h are inconsistent!"
#endif

/***********************************************************************************************************************
 *  LOCAL CONSTANT MACROS
 ***********************************************************************************************************************/

/***********************************************************************************************************************
 *  LOCAL FUNCTION MACROS
 ***********************************************************************************************************************/
#if ( SOMEIPTP_DEV_ERROR_REPORT == STD_ON )
#define SomeIpTp_ReportDet(a, b)     (void)Det_ReportError(SOMEIPTP_MODULE_ID, SOMEIPTP_INSTANCE_ID_DET, a, b) /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */
#else
#define SomeIpTp_ReportDet(a, b)
#endif

#if ( SOMEIPTP_RUNTIME_ERROR_REPORT == STD_ON )
#define SomeIpTp_ReportRuntimeDet(a, b)   (void)Det_ReportRuntimeError(SOMEIPTP_MODULE_ID, SOMEIPTP_INSTANCE_ID_DET, a, b) /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */
#else
#define SomeIpTp_ReportRuntimeDet(a, b)
#endif

/***********************************************************************************************************************
 *  LOCAL DATA TYPES AND STRUCTURES
 ***********************************************************************************************************************/
 
/***********************************************************************************************************************
 *  LOCAL DATA PROTOTYPES
 ***********************************************************************************************************************/

/***********************************************************************************************************************
 *  GLOBAL DATA
 ***********************************************************************************************************************/
#define SOMEIPTP_START_SEC_VAR_ZERO_INIT_8BIT
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/*! Initialization state of the module */
VAR(uint8, SOMEIPTP_VAR_ZERO_INIT) SomeIpTp_ModuleInitialized = SOMEIPTP_UNINIT;

#if (SOMEIPTP_TXNSDU == STD_ON)
/* ! Current Queue size */
VAR(SomeIpTp_TxQueueElementType, SOMEIPTP_VAR_ZERO_INIT) SomeIpTp_TxQueueSize = SOMEIPTP_QUEUE_EMPTY;
#endif

#define SOMEIPTP_STOP_SEC_VAR_ZERO_INIT_8BIT
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#define SOMEIPTP_START_SEC_VAR_ZERO_INIT_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/*! Pointer to the current configuration */
P2CONST(SomeIpTp_ConfigType, SOMEIPTP_VAR_ZERO_INIT, SOMEIPTP_PBCFG) SomeIpTp_ConfigDataPtr = NULL_PTR;

#define SOMEIPTP_STOP_SEC_VAR_ZERO_INIT_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/***********************************************************************************************************************
 *  LOCAL FUNCTION PROTOTYPES
 ***********************************************************************************************************************/
#define SOMEIPTP_START_SEC_CODE
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#if (SOMEIPTP_TXNSDU == STD_ON)
/***********************************************************************************************************************
 * SomeIpTp_AddQueueElement()
 ***********************************************************************************************************************/
/*! \brief        Add Element to Queue function
 *  \details      This function adds sdu requested for transmission to the queue
 *  \param[in]    PduId     Sdu ID passed by the upper layer which is used for transmission
 *  \pre          Interrupts are enabled.
 *  \pre          Module is initialized.
 *  \context      TASK|ISR2
 *  \reentrant    TRUE for different PDU IDs
 *  \synchronous  FALSE
 *  \trace        DSGN-SomeIpTpTxQueue
 ***********************************************************************************************************************/
SOMEIPTP_LOCAL FUNC(Std_ReturnType, SOMEIPTP_CODE) SomeIpTp_AddQueueElement(VAR(PduIdType, SOMEIPTP_APPL_VAR) PduId);

/***********************************************************************************************************************
 * SomeIpTp_DeleteQueueElement()
 ***********************************************************************************************************************/
/*! \brief        Delete Element from Queue function
 *  \details      This function deletes sdu requested for transmission from the queue either after the transmission 
 *                is complete or disassembly process is interrupted.
 *  \param[in]    PduId      Sdu ID passed by the upper layer which is used for transmission
 *  \pre          Interrupts are enabled.
 *  \pre          Module is initialized.
 *  \context      TASK|ISR2
 *  \reentrant    TRUE for different PDU IDs
 *  \synchronous  FALSE
 *  \trace        DSGN-SomeIpTpTxQueue
 ***********************************************************************************************************************/
SOMEIPTP_LOCAL FUNC(void, SOMEIPTP_CODE) SomeIpTp_DelQueueElement(VAR(PduIdType, SOMEIPTP_APPL_VAR) PduId);

/***********************************************************************************************************************
 * SomeIpTp_CreateHeader()
 ***********************************************************************************************************************/
/*! \brief        Assemble Header function
 *  \details      This function prepares header for each segmented PDU before transmission
 *  \param[in]    Id             Sdu ID passed by the upper layer which is used for transmission
 *  \param[in]    SduDataPtr     Pointer to payload
 *  \param[in]    MoreSegFlag    More segment flag of TP header
 *  \param[in]    FirstSegFlag   Specifies if it is the first segment
 *  \pre          Interrupts are enabled.
 *  \pre          Module is initialized.
 *  \context      TASK|ISR2
 *  \reentrant    TRUE for different PDU IDs
 *  \synchronous  FALSE
 ***********************************************************************************************************************/
SOMEIPTP_LOCAL FUNC(void, SOMEIPTP_CODE) SomeIpTp_CreateHeader(PduIdType Id, SduDataPtrType SduDataPtr, 
  boolean MoreSegFlag, boolean FirstSegFlag);

/***********************************************************************************************************************
 * SomeIpTp_CancelCurrTxObject()
 ***********************************************************************************************************************/
/*! \brief        Cancel Tx Object function
 *  \details      This function stops the on-going disassembly process for the corresponding SDU
 *  \param[in]    Id       Sdu ID passed by the upper layer which is used for transmission
 *  \pre          Interrupts are enabled.
 *  \pre          Module is initialized.
 *  \context      TASK|ISR2
 *  \reentrant    TRUE for different PDU IDs
 *  \synchronous  FALSE
 ***********************************************************************************************************************/
SOMEIPTP_LOCAL FUNC(void, SOMEIPTP_CODE) SomeIpTp_CancelCurrTxObject(PduIdType Id);
#endif

#if (SOMEIPTP_RXNSDU == STD_ON)
/***********************************************************************************************************************
 * SomeIpTp_StartNewRxSession()
 ***********************************************************************************************************************/
/*! \brief        Start New Rx Session function
 *  \details      This function starts the assembly process for the corresponding Pdu
 *  \param[in]    Id          Pdu ID passed by the lower layer which is used for reception
 *  \param[in]    PduInfoPtr  Pdu Info passed by the lower layer
 *  \param[in]    PayloadLen  Payload length
 *  \pre          Interrupts are enabled.
 *  \pre          Module is initialized.
 *  \context      TASK|ISR2
 *  \reentrant    TRUE for different PDU IDs
 *  \synchronous  FALSE
 ***********************************************************************************************************************/
SOMEIPTP_LOCAL FUNC(uint8, SOMEIPTP_CODE) SomeIpTp_StartNewRxSession(VAR(PduIdType, SOMEIPTP_APPL_VAR) Id, 
  P2CONST(PduInfoType, AUTOMATIC, SOMEIPTP_APPL_DATA) PduInfoPtr, VAR(PduLengthType, SOMEIPTP_APPL_VAR) PayloadLen);
#endif

/***********************************************************************************************************************
 *  LOCAL FUNCTIONS
 ***********************************************************************************************************************/
#if (SOMEIPTP_TXNSDU == STD_ON)
/***********************************************************************************************************************
 * SomeIpTp_AddQueueElement()
 ***********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 */
SOMEIPTP_LOCAL FUNC(Std_ReturnType, SOMEIPTP_CODE) SomeIpTp_AddQueueElement(VAR(PduIdType, SOMEIPTP_APPL_VAR) PduId)
{
  Std_ReturnType lReturnVal = E_NOT_OK;
  /* #10 Add Tx Sdu to the queue if the queue is not full. */
  SchM_Enter_SomeIpTp_SOMEIPTP_EXCLUSIVE_AREA_0();
  if(SomeIpTp_TxQueueSize < SomeIpTp_GetSizeOfTxQueueElement())
  {
    SomeIpTp_SetTxQueueElement(SomeIpTp_TxQueueSize, PduId); /* SBSW_SOMEIPTP_ADD_QUEUE_ELEMENT */
    SomeIpTp_TxQueueSize++;
    lReturnVal = E_OK;
  }
  SchM_Exit_SomeIpTp_SOMEIPTP_EXCLUSIVE_AREA_0();
  return lReturnVal;
}

/***********************************************************************************************************************
 * SomeIpTp_DeleteQueueElement()
 ***********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 */
SOMEIPTP_LOCAL FUNC(void, SOMEIPTP_CODE) SomeIpTp_DelQueueElement(VAR(PduIdType, SOMEIPTP_APPL_VAR) PduId)
{
  SomeIpTp_TxQueueElementIterType lIndex;
  SomeIpTp_TxQueueElementIterType lQPos;
  /* #10 Search Tx Sdu in the queue, if the queue is not empty and delete it from the queue. */
  if((SomeIpTp_TxQueueSize != 0u) && (SomeIpTp_TxQueueSize <= SomeIpTp_GetSizeOfTxQueueElement()))
  {
    for (lIndex = 0u;lIndex < SomeIpTp_TxQueueSize;lIndex++)
    {
      if(SomeIpTp_GetTxQueueElement(lIndex) == PduId)
      {
        SchM_Enter_SomeIpTp_SOMEIPTP_EXCLUSIVE_AREA_0();

        for(lQPos = lIndex;lQPos < ((SomeIpTp_TxQueueElementIterType)SomeIpTp_TxQueueSize-1u);lQPos++)
        {
          SomeIpTp_SetTxQueueElement(lQPos, SomeIpTp_GetTxQueueElement(lQPos+1u)); /* SBSW_SOMEIPTP_DELETE_QUEUE_ELEMENT */
        }
        SomeIpTp_TxQueueSize--;

        SchM_Exit_SomeIpTp_SOMEIPTP_EXCLUSIVE_AREA_0();
        break;
      }
    }
  }
}

/***********************************************************************************************************************
 * SomeIpTp_CreateHeader()
 ***********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 */
SOMEIPTP_LOCAL FUNC(void, SOMEIPTP_CODE) SomeIpTp_CreateHeader(PduIdType Id, SduDataPtrType SduDataPtr, 
  boolean MoreSegFlag, boolean FirstSegFlag)
{
  uint32 lOffset;
  uint8_least lIndex;
  SomeIpTp_TxStateIdxOfTXNSduType lTxStateIdx = SomeIpTp_GetTxStateIdxOfTXNSdu(Id);

  /* #10 Assemble SOMEIP header along with offset and more segment flag.*/
  if( FirstSegFlag == FALSE)
  {
    const uint8 *lTpHeader;
    lTpHeader = SomeIpTp_GetTpHeaderOfTxState(lTxStateIdx);
    for(lIndex = 0;lIndex < SOMEIP_HEADER_LENGTH;lIndex++)
    {
      SduDataPtr[lIndex] = lTpHeader[lIndex];  /* SBSW_SOMEIPTP_CREATEHEADER_WRITE_SDUDATAPTR */
    }
  }

  SomeIpTp_SetOffsetOfTxState(lTxStateIdx, (SomeIpTp_GetOffsetOfTxState(lTxStateIdx) << 4u)); /* SBSW_SOMEIPTP_CSL03 */
  lOffset = SomeIpTp_GetOffsetOfTxState(lTxStateIdx);

  SduDataPtr[SOMEIPTP_MESSAGE_TYPE_8BIT_OFFSET] |= SOMEIPTP_TPFLAG_8BIT_VAL;  /* SBSW_SOMEIPTP_CREATEHEADER_WRITE_SDUDATAPTR */
  SduDataPtr[SOMEIPTP_OFFSET_FIELD0_8BIT_OFFSET] = (uint8) (lOffset >> 24u);  /* SBSW_SOMEIPTP_CREATEHEADER_WRITE_SDUDATAPTR */
  SduDataPtr[SOMEIPTP_OFFSET_FIELD1_8BIT_OFFSET] = (uint8) (lOffset >> 16u);  /* SBSW_SOMEIPTP_CREATEHEADER_WRITE_SDUDATAPTR */
  SduDataPtr[SOMEIPTP_OFFSET_FIELD2_8BIT_OFFSET] = (uint8) (lOffset >> 8u);   /* SBSW_SOMEIPTP_CREATEHEADER_WRITE_SDUDATAPTR */
  SduDataPtr[SOMEIPTP_MORE_SEG_FLAG_8BIT_OFFSET] = ((((uint8) lOffset) & SOMEIPTP_OFFSET_FIELD_MASK_8BIT) | MoreSegFlag);  /* SBSW_SOMEIPTP_CREATEHEADER_WRITE_SDUDATAPTR */
}

/***********************************************************************************************************************
 * SomeIpTp_CancelCurrTxObject()
 ***********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 *
 */
SOMEIPTP_LOCAL FUNC(void, SOMEIPTP_CODE) SomeIpTp_CancelCurrTxObject(PduIdType Id)
{
  /* #10 Delete Sdu from the queue and reset the timer.*/
  SomeIpTp_DelQueueElement(Id);
  SomeIpTp_SetTimerOfTxState(SomeIpTp_GetTxStateIdxOfTXNSdu(Id), 0);  /* SBSW_SOMEIPTP_CSL03 */
  /* #20 Reset the status of SDU to Idle.*/
  SomeIpTp_SetStatusOfTxState(SomeIpTp_GetTxStateIdxOfTXNSdu(Id), SOMEIPTP_TX_STATUS_IDLE);  /* SBSW_SOMEIPTP_CSL03 */
}
#endif

#if (SOMEIPTP_RXNSDU == STD_ON)
/***********************************************************************************************************************
 * SomeIpTp_StartNewRxSession()
 ***********************************************************************************************************************/
/*! 
 * Internal comment removed.
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
SOMEIPTP_LOCAL FUNC(uint8, SOMEIPTP_CODE) SomeIpTp_StartNewRxSession(VAR(PduIdType, SOMEIPTP_APPL_VAR) Id, 
  P2CONST(PduInfoType, AUTOMATIC, SOMEIPTP_APPL_DATA) PduInfoPtr, VAR(PduLengthType, SOMEIPTP_APPL_VAR) PayloadLen)
{
  PduInfoType lPduInfo;
  PduLengthType lBufferSizePtr;
  PduLengthType lSduLength;
  PduIdType lULId;
  SomeIpTp_RxStateIdxOfRXNSduType lRxStateIdx = SomeIpTp_GetRxStateIdxOfRXNSdu(Id);
  SomeIpTp_MetadataLengthOfRXNSduType lMetadataLength = SomeIpTp_GetMetadataLengthOfRXNSdu(Id);
  uint8 lErrorId = SOMEIPTP_E_NO_ERROR;

  /* Store the header before modifying it, since it will be used as a reference for the next segments. */
  SomeIpTp_CopyHeader(PduInfoPtr->SduDataPtr, SomeIpTp_GetTpHeaderOfRxState(lRxStateIdx));  /* SBSW_SOMEIPTP_INTERNAL_API_CALL_SDUDATAPTR */

  /* #10 If metadata exists, assign the SduPtr to metadata and set the SduLength to metadata length
  *      and indicate start of reception to upper layer with unknown length by calling PduR_SomeIpTpStartOfReception
  *      with TPSduLength set to 0 and verify the return value and available buffer size. */
# if defined (SOMEIPTP_METADATAOFRXSTATE)
  if (lMetadataLength > 0u)
  {
    lPduInfo.SduDataPtr = &PduInfoPtr->SduDataPtr[(PduInfoPtr->SduLength)-lMetadataLength];
    SomeIpTp_CopyMetadata(lMetadataLength, lPduInfo.SduDataPtr,                             /* SBSW_SOMEIPTP_INTERNAL_API_CALL_SDUDATAPTR */
                            SomeIpTp_GetMetadataOfRxState(lRxStateIdx));
  }
  else
# endif
  {
    lPduInfo.SduDataPtr = NULL_PTR;
  }
  
  lPduInfo.SduLength = lMetadataLength;

  lULId = SomeIpTp_GetUpperLayerIdOfRXNSdu(Id);
  /* Call start of reception indicating unknown message length */
  lSduLength = 0;

  if(PduR_SomeIpTpStartOfReception(lULId, &lPduInfo, lSduLength, &lBufferSizePtr) == BUFREQ_OK)  /* SBSW_SOMEIPTP_STARTOFRECEPTION_CALL */
  {
    lSduLength = PayloadLen + SOMEIP_HEADER_LENGTH;
    /* #11 If BUFREQ_OK, Check if Payload plus header fits in the available buffer. */
    if(lBufferSizePtr >= lSduLength)
    {
      /* #110 Call PduR_SomeIpTpCopyRxData with the SomeIp header first and verify the return value.*/
      lPduInfo.SduDataPtr = PduInfoPtr->SduDataPtr;
      /* Reset TP flag */
      lPduInfo.SduDataPtr[SOMEIPTP_MESSAGE_TYPE_8BIT_OFFSET] &= ~SOMEIPTP_TPFLAG_8BIT_VAL;  /* SBSW_SOMEIPTP_STARTNEWRXSESSION_WRITE_SDUDATAPTR */
      lPduInfo.SduLength = SOMEIP_HEADER_LENGTH;

      if(PduR_SomeIpTpCopyRxData(lULId, &lPduInfo, &lBufferSizePtr) == BUFREQ_OK)                /* SBSW_SOMEIPTP_COPYRXDATA_CALL */
      {
        /* Assign the payload */
        lPduInfo.SduDataPtr = (SduDataPtrType)&PduInfoPtr->SduDataPtr[SOMEIPTP_SF_HEADER_LENGTH];
        lPduInfo.SduLength = PayloadLen;

        /* #1100 If BUFREQ_OK, call Copy Rx Data with payload and verify the return value. */
        if(PduR_SomeIpTpCopyRxData(lULId, &lPduInfo, &lBufferSizePtr) == BUFREQ_OK)              /* SBSW_SOMEIPTP_COPYRXDATA_CALL */
        {
          /* #11000 If BUFREQ_OK, update data index and store the available buffer. */
          SomeIpTp_SetDataIndexOfRxState(lRxStateIdx, PayloadLen);  /* SBSW_SOMEIPTP_CSL03 */
          SomeIpTp_SetAvailBufSizeOfRxState(lRxStateIdx, lBufferSizePtr); /* SBSW_SOMEIPTP_CSL03 */

          /* #11002 Start the Rx Timeout time and change the state of the Pdu to wait for next SF */
          SomeIpTp_SetTimerOfRxState(lRxStateIdx, SomeIpTp_GetRxTimeoutTimeOfChannel(SomeIpTp_GetChannelIdxOfRXNSdu(Id))); /* SBSW_SOMEIPTP_CSL03 */
          SomeIpTp_SetStatusOfRxState(lRxStateIdx, SOMEIPTP_RX_STATUS_WAIT_NEXT_SF); /* SBSW_SOMEIPTP_CSL03 */
        }
        else /* #1101 If not BUFREQ_OK, Report runtime error with Assembly Interrupt */
        {
          lErrorId = SOMEIPTP_E_ASSEMBLY_INTERRUPT;
        }
      }
      else /* #111 If not BUFREQ_OK, Report runtime error with Assembly Interrupt */
      {
        lErrorId = SOMEIPTP_E_ASSEMBLY_INTERRUPT;
      }
    }
    else /* #12 If available buffer is insufficient, Report runtime error with Assembly Interrupt */
    {
      lErrorId = SOMEIPTP_E_ASSEMBLY_INTERRUPT;
    }
  }
  else
  {
    SomeIpTp_ReportRuntimeDet(SOMEIPTP_SID_RXINDICATION, SOMEIPTP_E_ASSEMBLY_INTERRUPT);
  }
  
  return lErrorId;
}
#endif

/***********************************************************************************************************************
 *  GLOBAL FUNCTIONS
 ***********************************************************************************************************************/

/***********************************************************************************************************************
 *  SomeIpTp_InitMemory()
 ***********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 *
 *
 *
 */
FUNC(void, SOMEIPTP_CODE) SomeIpTp_InitMemory(void)
{
  /* ----- Implementation ----------------------------------------------- */
  SomeIpTp_ModuleInitialized = SOMEIPTP_UNINIT;
}

/***********************************************************************************************************************
 * SomeIpTp_Init()
 ***********************************************************************************************************************/
/*! 
 * Internal comment removed.
 *
 *
 *
 *
 */
FUNC(void, SOMEIPTP_CODE) SomeIpTp_Init(P2CONST(SomeIpTp_ConfigType, AUTOMATIC, SOMEIPTP_INIT_DATA) ConfigPtr)
{

#if (SOMEIPTP_USE_INIT_POINTER == STD_ON)
  /* #10 Check for ConfigPtr validity and Compatibility of configuration in case of Post build loadable. */
  SomeIpTp_ConfigDataPtr = ConfigPtr;
  /* ----- Development Error Checks ------------------------------------------------------------ */
  if(SomeIpTp_ConfigDataPtr == NULL_PTR)
  {
    EcuM_BswErrorHook((uint16) SOMEIPTP_MODULE_ID, (uint8)ECUM_BSWERROR_NULLPTR);
# if (SOMEIPTP_DEV_ERROR_REPORT == STD_ON)
    SomeIpTp_ReportDet(SOMEIPTP_SID_INIT, SOMEIPTP_E_PARAM_POINTER);
# endif
  }
# if (SOMEIPTP_CONFIGURATION_VARIANT == SOMEIPTP_CONFIGURATION_VARIANT_POSTBUILD_LOADABLE)
  /* Check magic number */
  else if (SOMEIPTP_FINAL_MAGIC_NUMBER != SomeIpTp_GetFinalMagicNumber())
  {
    EcuM_BswErrorHook((uint16) SOMEIPTP_MODULE_ID, (uint8)ECUM_BSWERROR_MAGICNUMBER);
  }
# endif
  else
  {
#endif
    {
#if (SOMEIPTP_TXNSDU == STD_ON)
      {
        SomeIpTp_TxStateIterType lTxIndex;
        /* #20 Reset the state of Tx and Rx Nsdus to Idle. */
        for(lTxIndex=0;lTxIndex<SomeIpTp_GetSizeOfTxState();lTxIndex++)
        {
          SomeIpTp_SetStatusOfTxState(lTxIndex, SOMEIPTP_TX_STATUS_IDLE);  /* SBSW_SOMEIPTP_CSL03 */
        }
        SomeIpTp_TxQueueSize = SOMEIPTP_QUEUE_EMPTY;
      }
#endif

#if (SOMEIPTP_RXNSDU == STD_ON)
      {
        SomeIpTp_RxStateIterType lRxIndex;
        for(lRxIndex=0;lRxIndex<SomeIpTp_GetSizeOfRxState();lRxIndex++)
        {
          SomeIpTp_SetStatusOfRxState(lRxIndex, SOMEIPTP_RX_STATUS_IDLE);  /* SBSW_SOMEIPTP_CSL03 */
        }
      }
#endif
       /* #30 Set the module state to Initialized. */
      SomeIpTp_ModuleInitialized = SOMEIPTP_INIT;
    }
#if (SOMEIPTP_USE_INIT_POINTER == STD_ON)
  }
#else
  SOMEIPTP_DUMMY_STATEMENT(ConfigPtr);
#endif
} /* SomeIpTp_Init */

#if (SOMEIPTP_VERSION_INFO_API == STD_ON)
/***********************************************************************************************************************
 * SomeIpTp_GetVersionInfo()
 ***********************************************************************************************************************/
/*! 
 * Internal comment removed.
 *
 *
 *
 */
FUNC(void, SOMEIPTP_CODE) SomeIpTp_GetVersionInfo(P2VAR(Std_VersionInfoType, AUTOMATIC, SOMEIPTP_APPL_DATA) VersionInfo)
{
  /* ----- Local Variables ---------------------------------------------- */
  uint8 lErrorId = SOMEIPTP_E_NO_ERROR;
  /* #10 Report DET error if versionInfo is null pointer. */

    /* ----- Development Error Checks ------------------------------------- */
# if (SOMEIPTP_DEV_ERROR_DETECT == STD_ON) /* COV_SOMEIPTP_DET_CHECK */
  if(VersionInfo == NULL_PTR)
  {
    lErrorId = SOMEIPTP_E_PARAM_POINTER;
  }
  else
# endif
  {
    /* #20 Set versionInfo specified by corresponding macros in the header file. */
    VersionInfo->moduleID = SOMEIPTP_MODULE_ID;  /* SBSW_SOMEIPTP_GETVERSION_PARAM */
    VersionInfo->vendorID = SOMEIPTP_VENDOR_ID;  /* SBSW_SOMEIPTP_GETVERSION_PARAM */

    VersionInfo->sw_major_version = SOMEIPTP_SW_MAJOR_VERSION;  /* SBSW_SOMEIPTP_GETVERSION_PARAM */
    VersionInfo->sw_minor_version = SOMEIPTP_SW_MINOR_VERSION;  /* SBSW_SOMEIPTP_GETVERSION_PARAM */
    VersionInfo->sw_patch_version = SOMEIPTP_SW_PATCH_VERSION;  /* SBSW_SOMEIPTP_GETVERSION_PARAM */
  }
  /* ----- Development Error Report --------------------------------------- */
# if (SOMEIPTP_DEV_ERROR_REPORT == STD_ON)
  if(lErrorId != SOMEIPTP_E_NO_ERROR)
  {
    SomeIpTp_ReportDet(SOMEIPTP_SID_GET_VERSION_INFO, lErrorId);
  }
# else
  SOMEIPTP_DUMMY_STATEMENT(lErrorId);
# endif

} /* SomeIpTp_GetVersionInfo */
#endif

/***********************************************************************************************************************
 * SomeIpTp_Transmit()
 ***********************************************************************************************************************/
/*! 
 * Internal comment removed.
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
FUNC(Std_ReturnType, SOMEIPTP_CODE) SomeIpTp_Transmit(VAR(PduIdType, SOMEIPTP_APPL_VAR) TxPduId, 
  P2CONST(PduInfoType, AUTOMATIC, SOMEIPTP_APPL_DATA) PduInfoPtr)
{
#if (SOMEIPTP_TXNSDU == STD_ON)

  /* ----- Local Variables ---------------------------------------------- */
  uint8 lErrorId = SOMEIPTP_E_NO_ERROR;
  uint8 lReturnValue = E_NOT_OK;
  
  /* #10 Check if SomeIpTp is initialized and all parameters are in valid range. */
  if(SomeIpTp_IsInitialized() == FALSE)
  {
    lErrorId = SOMEIPTP_E_NOTINIT;
  }
    /* ----- Development Error Checks ------------------------------------- */
# if (SOMEIPTP_DEV_ERROR_DETECT == STD_ON)  /* COV_SOMEIPTP_DET_CHECK */
  else if(SomeIpTp_IsTxSduIdInvalid(TxPduId) == TRUE)
  {
    lErrorId = SOMEIPTP_E_PARAM;
  }
  else if(SomeIpTp_IsSduPtrValid(PduInfoPtr) == FALSE)  /* SBSW_SOMEIPTP_ISSDUPTRVALID_CALL */
  {
    lErrorId = SOMEIPTP_E_PARAM_POINTER;
  }
# endif
  else
  {
     SomeIpTp_TxStateIdxOfTXNSduType lTxStateIdx = SomeIpTp_GetTxStateIdxOfTXNSdu(TxPduId);
     SomeIpTp_MetadataLengthOfTXNSduType lMetadataLength = SomeIpTp_GetMetadataLengthOfTXNSdu(TxPduId);
     SchM_Enter_SomeIpTp_SOMEIPTP_EXCLUSIVE_AREA_0();
     /* #20 Check if no segmentation is on-going for the requested PDU ID. */
     if(SomeIpTp_GetStatusOfTxState(lTxStateIdx) == SOMEIPTP_TX_STATUS_IDLE)
     {
        PduLengthType lSduLength;

        lSduLength = (PduInfoPtr->SduLength) - lMetadataLength;

        /* #21 Verify for minimum length of the SDU. */
        if(SomeIpTp_IsPduLengthValid(lSduLength) == TRUE)
        {
          PduInfoType lPduInfo;
          const uint8 *lSdudata;
          uint8 *lTpHeader;
          PduLengthType lSFPayload;
          PduLengthType lPduLength;

          /* #210 Derive lower layer PDU ID and length which will be used for every segmented PDU. */
          lSdudata = PduInfoPtr->SduDataPtr;
          lPduLength = SomeIpTp_GetTxNPduLengthOfTXNSdu(TxPduId);

          lTpHeader = SomeIpTp_GetTpHeaderOfTxState(lTxStateIdx);
          /* #211 Store the header, metadata and length. */
          SomeIpTp_CopyHeader(lSdudata, lTpHeader);  /* SBSW_SOMEIPTP_INTERNAL_API_CALL_SDUDATAPTR */
# if defined (SOMEIPTP_METADATAOFTXSTATE)
          if (lMetadataLength > 0u)
          {
            SomeIpTp_CopyMetadata(lMetadataLength, &PduInfoPtr->SduDataPtr[lSduLength],   /* SBSW_SOMEIPTP_INTERNAL_API_CALL_SDUDATAPTR */
                                  SomeIpTp_GetMetadataOfTxState(lTxStateIdx));
          }
# endif
          SomeIpTp_SetDataLengthOfTxState(lTxStateIdx, lSduLength-SOMEIP_HEADER_LENGTH);  /* SBSW_SOMEIPTP_CSL03 */

          /* #212 If the SDU does not fit in a single PDU. */
          if(lSduLength > lPduLength)
          {
            /* #2120 Calculate the size of the first SOME/IP segment(Header+Payload) and set TP flag. */
            lTpHeader[SOMEIPTP_MESSAGE_TYPE_8BIT_OFFSET] = (lSdudata[SOMEIPTP_MESSAGE_TYPE_8BIT_OFFSET] |    /* SBSW_SOMEIPTP_TRANSMIT_WRITE_TPHEADER */
                                                                SOMEIPTP_TPFLAG_8BIT_VAL);
            /* Align the payload length */
            lSFPayload = SomeIpTp_GetMaxTxSFPayloadOfTXNSdu(TxPduId);
            /* Set the Data Offset to zero */
            SomeIpTp_SetOffsetOfTxState(lTxStateIdx, SOMEIPTP_FF_OFFSET);  /* SBSW_SOMEIPTP_CSL03 */
            /* Set the Data Index to zero */
            SomeIpTp_SetDataIndexOfTxState(lTxStateIdx, SOMEIPTP_FF_OFFSET);  /* SBSW_SOMEIPTP_CSL03 */
            /* Set the status to wait for Tx Confirmation */ 
            SomeIpTp_SetStatusOfTxState(lTxStateIdx, SOMEIPTP_TX_STATUS_WAIT_TXCONF_SF);  /* SBSW_SOMEIPTP_CSL03 */
          }
          else
          {
            /* #2121 Else store the header unmodified.*/
            lSFPayload = lSduLength;
            SomeIpTp_SetStatusOfTxState(lTxStateIdx, SOMEIPTP_TX_STATUS_WAIT_TXCONF_LAST_SF);  /* SBSW_SOMEIPTP_CSL03 */
          }

          SchM_Exit_SomeIpTp_SOMEIPTP_EXCLUSIVE_AREA_0();

          /* Store the pdu length requested to Transmit */
          SomeIpTp_SetNextSegmentDLOfTxState(lTxStateIdx, lSFPayload);  /* SBSW_SOMEIPTP_CSL03 */

          /* #213 Start Tx Confirmation Timeout time. */
          SomeIpTp_SetTimerOfTxState(lTxStateIdx,                                              /* SBSW_SOMEIPTP_CSL03 */
            SomeIpTp_GetTxConfTimeoutTimeOfChannel(SomeIpTp_GetChannelIdxOfTXNSdu(TxPduId)));

          /* #214 If metadata exists for the PDU, pass the frame to PduR with calculated length and metadata 
           *      by the sduptr, otherwise set the sduptr to null and verify the return result.  */
# if defined (SOMEIPTP_METADATAOFTXSTATE)
          if (lMetadataLength > 0u)
          {
            lPduInfo.SduDataPtr = SomeIpTp_GetMetadataOfTxState(lTxStateIdx);
            lPduInfo.SduLength = lSFPayload + lMetadataLength;
          }
          else
# endif
          {
            lPduInfo.SduDataPtr = NULL_PTR;
            lPduInfo.SduLength = lSFPayload;
          }

          lReturnValue = PduR_SomeIpTpTransmit(SomeIpTp_GetLowerLayerIdOfTXNSdu(TxPduId),  /* SBSW_SOMEIPTP_TRANSMIT_CALL */
            (const PduInfoType*)&lPduInfo);
          /* #215 If request accepted, add it to TX queue to trigger further segmented frames.*/
          if(lReturnValue == E_OK)
          {
            if(SomeIpTp_AddQueueElement(TxPduId) != E_OK)
            {
              /* Fatal error: Queue must be equal to the number of Tx SDUs.
              Hence overflow of the queue cannot occur. */
              lReturnValue = E_NOT_OK;
# if (SOMEIPTP_DEV_ERROR_DETECT == STD_ON) /* COV_SOMEIPTP_DET_CHECK */
              lErrorId = SOMEIPTP_E_FATAL_ERROR;
# endif
              SomeIpTp_SetStatusOfTxState(lTxStateIdx, SOMEIPTP_TX_STATUS_IDLE);  /* SBSW_SOMEIPTP_CSL03 */
            }
          }
          else /* #216 Otherwise reset the SDU state to Idle. */
          {
            SomeIpTp_SetStatusOfTxState(lTxStateIdx, SOMEIPTP_TX_STATUS_IDLE);  /* SBSW_SOMEIPTP_CSL03 */
          }
        }
        else  /* Pdu Length invalid */
        {
          SchM_Exit_SomeIpTp_SOMEIPTP_EXCLUSIVE_AREA_0();
        }
     }
     else
     {
        SchM_Exit_SomeIpTp_SOMEIPTP_EXCLUSIVE_AREA_0();
        /* #30 Else interrupt the ongoing assembly process, call Tx Confirmation with negative result
        and report runtime error. */
        SomeIpTp_CancelCurrTxObject(TxPduId);
        PduR_SomeIpTpTxConfirmation(SomeIpTp_GetUpperLayerIdOfTXNSdu(TxPduId), E_NOT_OK);
        SomeIpTp_ReportRuntimeDet(SOMEIPTP_SID_TRANSMIT, SOMEIPTP_E_DISASSEMBLY_INTERRUPT);
     }
  }
  /* ----- Development Error Report --------------------------------------- */
# if (SOMEIPTP_DEV_ERROR_REPORT == STD_ON)
  if(lErrorId != SOMEIPTP_E_NO_ERROR)
  {
    SomeIpTp_ReportDet(SOMEIPTP_SID_TRANSMIT, lErrorId);
  }
# else
  SOMEIPTP_DUMMY_STATEMENT(lErrorId);
# endif
  return lReturnValue;

#else
  SOMEIPTP_DUMMY_STATEMENT(TxPduId); 
  SOMEIPTP_DUMMY_STATEMENT(PduInfoPtr);
  return E_NOT_OK;
#endif
} /* PRQA S 6080, 6050, 6030 */ /* MD_MSR_STMIF */ /* MD_MSR_STCAL */ /* MD_MSR_STCYC */

/***********************************************************************************************************************
 * SomeIpTp_TriggerTransmit()
 ***********************************************************************************************************************/
/*! 
 * Internal comment removed.
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
FUNC(Std_ReturnType, SOMEIPTP_CODE) SomeIpTp_TriggerTransmit(VAR(PduIdType, SOMEIPTP_APPL_VAR) TxPduId, 
  P2VAR(PduInfoType, AUTOMATIC, SOMEIPTP_APPL_DATA) PduInfoPtr) /* PRQA S 3673 */ /* MD_MSR_Rule8.13 */
{
#if (SOMEIPTP_TXNSDU == STD_ON)

  /* ----- Local Variables ---------------------------------------------- */
  uint8 lErrorId = SOMEIPTP_E_NO_ERROR;
  uint8 lReturnValue = E_NOT_OK;

    /* ----- Development Error Checks ------------------------------------- */
    /* #10 Check if SomeIpTp is initialized and all parameters are in valid range. */
# if (SOMEIPTP_DEV_ERROR_DETECT == STD_ON) /* COV_SOMEIPTP_DET_CHECK */
  if(SomeIpTp_IsInitialized() == FALSE)
  {
    lErrorId = SOMEIPTP_E_NOTINIT;
  }
  else if(SomeIpTp_IsTxSduIdInvalid(TxPduId) == TRUE)
  {
    lErrorId = SOMEIPTP_E_PARAM;
  }
  else
# endif
  {
    SomeIpTp_TxStateIdxOfTXNSduType lTxStateIdx = SomeIpTp_GetTxStateIdxOfTXNSdu(TxPduId);
    PduLengthType lPdulength;
    PduLengthType lPayloadLen;
    /* Length of segmented frame */
    lPdulength = SomeIpTp_GetNextSegmentDLOfTxState(lTxStateIdx);

    /* #20 Verify if the passed length and data pointer are valid. */
    if((SomeIpTp_IsSduPtrValid(PduInfoPtr) == TRUE) && (PduInfoPtr->SduLength >= lPdulength))  /* SBSW_SOMEIPTP_ISSDUPTRVALID_CALL */
    {
      PduLengthType lCurrDataIndex;
      PduInfoType  lSduInfo;
      PduLengthType lAvailableDataLen;
      PduLengthType lRemDataLen;
      boolean lFirstSegFlag = FALSE;
      boolean lMoreSegFlag = FALSE;
      uint8 lMsgType;
      uint8 lHeaderLen;

      /* #21 Calculate the amount of payload ,pass the buffer to PduR and verify the return value. */
      lMsgType = SomeIpTp_GetTpHeaderOfTxState(lTxStateIdx)[SOMEIPTP_MESSAGE_TYPE_8BIT_OFFSET];
      if(SomeIpTp_IsMsgTypeTp(lMsgType) == TRUE)
      {
        lHeaderLen = SOMEIPTP_SF_HEADER_LENGTH;
      }
      else /* Single Frame */
      {
        lHeaderLen = SOMEIP_HEADER_LENGTH;
      }

      lPayloadLen = lPdulength-lHeaderLen;

      if(lHeaderLen == SOMEIPTP_SF_HEADER_LENGTH)
      {
        lSduInfo.SduLength = lPayloadLen;
        lSduInfo.SduDataPtr = &PduInfoPtr->SduDataPtr[SOMEIPTP_SF_HEADER_LENGTH];

        /* If first segment, call CopyTxData to copy Header first */
        if (SomeIpTp_GetDataIndexOfTxState(lTxStateIdx) == SOMEIPTP_FF_OFFSET)
        {
          lFirstSegFlag = TRUE;
          lSduInfo.SduLength = SOMEIP_HEADER_LENGTH;
          lSduInfo.SduDataPtr = PduInfoPtr->SduDataPtr;

          if((PduR_SomeIpTpCopyTxData(SomeIpTp_GetUpperLayerIdOfTXNSdu(TxPduId),      /* SBSW_SOMEIPTP_COPYTXDATA_CALL */
            &lSduInfo, NULL_PTR, &lAvailableDataLen) == BUFREQ_OK) && (lSduInfo.SduLength == SOMEIP_HEADER_LENGTH))
          {
            if(lAvailableDataLen >= lPayloadLen)
            {
              /* Call CopyTxData with the calculated payload. */
              lSduInfo.SduLength = lPayloadLen;
              lSduInfo.SduDataPtr = &PduInfoPtr->SduDataPtr[SOMEIPTP_SF_HEADER_LENGTH];
              if((PduR_SomeIpTpCopyTxData(SomeIpTp_GetUpperLayerIdOfTXNSdu(TxPduId),  /* SBSW_SOMEIPTP_COPYTXDATA_CALL */
                &lSduInfo, NULL_PTR, &lAvailableDataLen) == BUFREQ_OK) && (lSduInfo.SduLength == lPayloadLen))
              {
                lReturnValue = E_OK;
              }
            }
          }
        }
        else /* Consecutive segments */
        {
          /* Call CopyTxData with the calculated payload */
          if((PduR_SomeIpTpCopyTxData(SomeIpTp_GetUpperLayerIdOfTXNSdu(TxPduId),      /* SBSW_SOMEIPTP_COPYTXDATA_CALL */
            &lSduInfo, NULL_PTR, &lAvailableDataLen) == BUFREQ_OK) \
            && (lSduInfo.SduLength == lPayloadLen))
          {
            lReturnValue = E_OK;
          }
        }
      }
      else  /* single frame */
      {
        /* Call CopyTxData to copy the complete payload along with SOME/IP Header */
        if((PduR_SomeIpTpCopyTxData(SomeIpTp_GetUpperLayerIdOfTXNSdu(TxPduId),        /* SBSW_SOMEIPTP_COPYTXDATA_CALL */
          PduInfoPtr, NULL_PTR, &lAvailableDataLen) == BUFREQ_OK) && (PduInfoPtr->SduLength == lPdulength))
        {
          lReturnValue = E_OK;
        }
      }
      /* #22 if BUFREQ_OK and message type TP: */
      if((lReturnValue == E_OK) && (lHeaderLen == SOMEIPTP_SF_HEADER_LENGTH))
      {
        /* Set the current offset */
        SomeIpTp_SetOffsetOfTxState(lTxStateIdx,                                            /* SBSW_SOMEIPTP_CSL03 */
          ((SomeIpTp_OffsetOfTxStateType)SomeIpTp_GetDataIndexOfTxState(lTxStateIdx)/SOMEIPTP_DATA_ALIGNMENT_LENGTH));
        /* Recalculate current data index */
        lCurrDataIndex = SomeIpTp_GetDataIndexOfTxState(lTxStateIdx) + lPayloadLen;

        SomeIpTp_SetDataIndexOfTxState(lTxStateIdx, lCurrDataIndex);  /* SBSW_SOMEIPTP_CSL03 */

        /* #220 Calculate the remaining data for the next segment. */
        lRemDataLen = SomeIpTp_GetDataLengthOfTxState(lTxStateIdx) - lCurrDataIndex;

        /* #221 If the current segment is not the last segment: */
        if(lRemDataLen != 0u)
        {
          lMoreSegFlag = TRUE;
          /* #2210 If the available data for next segment is neither less than last segment data if next segment is the
                last one nor less than 16 bytes. */
          if(((SomeIpTp_IsLastSegment(TxPduId, lRemDataLen) == TRUE) && (lAvailableDataLen >= lRemDataLen)) || 
            (lAvailableDataLen >= SOMEIPTP_DATA_ALIGNMENT_LENGTH))
          {
            /* #22100 Return the amount of data copied plus header. */
            PduInfoPtr->SduLength = lPdulength;  /* SBSW_SOMEIPTP_TRIGGERTRANSMIT_WRITE_PDUINFOPTR */

            /* #22101 Calculate the payload length of the next segment considering available Data. */
            /* If available data is larger than the PDU Length , then limit next segment data length to the PDU length */
            if((lAvailableDataLen + SOMEIPTP_SF_HEADER_LENGTH) <= SomeIpTp_GetTxNPduLengthOfTXNSdu(TxPduId))
            {
              /* Align the payload of the next segment if it is not the last segment */
              if(SomeIpTp_IsLastSegment(TxPduId, lRemDataLen) == FALSE)
              {
                SomeIpTp_SetNextSegmentDLOfTxState(lTxStateIdx,                                            /* SBSW_SOMEIPTP_CSL03 */
                  ((lAvailableDataLen/SOMEIPTP_DATA_ALIGNMENT_LENGTH) * SOMEIPTP_DATA_ALIGNMENT_LENGTH) + SOMEIPTP_SF_HEADER_LENGTH);
              }
              else
              {
                SomeIpTp_SetNextSegmentDLOfTxState(lTxStateIdx, lRemDataLen+SOMEIPTP_SF_HEADER_LENGTH);    /* SBSW_SOMEIPTP_CSL03 */
              }
            }
            else
            {
              SomeIpTp_SetNextSegmentDLOfTxState(lTxStateIdx, SomeIpTp_GetMaxTxSFPayloadOfTXNSdu(TxPduId)); /* SBSW_SOMEIPTP_CSL03 */
            }
          }
          else /* Available length invalid */
          {
            lReturnValue = E_NOT_OK;
          }
        }
        else  /* Last segment. Not required to validate the available data length */
        {
          lReturnValue = E_OK;
        }
        /* #222 If the available data length is valid,assemble header for the SOME/IP segment. */
        if(lReturnValue == E_OK)
        {
          SomeIpTp_CreateHeader(TxPduId, PduInfoPtr->SduDataPtr, lMoreSegFlag, lFirstSegFlag);  /* SBSW_SOMEIPTP_INTERNAL_API_CALL_SDUDATAPTR */
        }
      }
      /* #23 If not BUFREQ_OK and invalid available data length, Call PduR_SomeIpTpTxConfirmation with result 
          E_NOT_OK and stop the disassembly process. */
      if(lReturnValue == E_NOT_OK)
      {
        /* Delete element from the queue */
        SomeIpTp_CancelCurrTxObject(TxPduId);
        PduR_SomeIpTpTxConfirmation(SomeIpTp_GetUpperLayerIdOfTXNSdu(TxPduId), E_NOT_OK);
        SomeIpTp_ReportRuntimeDet(SOMEIPTP_SID_TRIGGER_TRANSMIT, SOMEIPTP_E_DISASSEMBLY_INTERRUPT);
      }
      else /* Single Frame */
      {
        /* Data copy successful */
      }
    }
  }
  /* ----- Development Error Report --------------------------------------- */
# if (SOMEIPTP_DEV_ERROR_REPORT == STD_ON)
  if(lErrorId != SOMEIPTP_E_NO_ERROR)
  {
    SomeIpTp_ReportDet(SOMEIPTP_SID_TRIGGER_TRANSMIT, lErrorId);
  }
# else
  SOMEIPTP_DUMMY_STATEMENT(lErrorId);
# endif
  return lReturnValue;

#else
  SOMEIPTP_DUMMY_STATEMENT(TxPduId); 
  SOMEIPTP_DUMMY_STATEMENT(PduInfoPtr);
  return E_NOT_OK;
#endif
} /* PRQA S 6080, 6030, 6050, 6010 */ /* MD_MSR_STMIF */ /* MD_MSR_STCYC */ /* MD_MSR_STCAL */ /* MD_MSR_STPTH */

/***********************************************************************************************************************
 * SomeIpTp_TxConfirmation()
 ***********************************************************************************************************************/
/*! 
 * Internal comment removed.
 *
 *
 *
 *
 *
 *
 */
FUNC(void, SOMEIPTP_CODE) SomeIpTp_TxConfirmation(VAR(PduIdType, SOMEIPTP_APPL_VAR) TxPduId)
{
#if (SOMEIPTP_TXNSDU == STD_ON)

  /* ----- Local Variables ---------------------------------------------- */
  uint8 lErrorId = SOMEIPTP_E_NO_ERROR;

  /* ----- Development Error Checks ------------------------------------- */
    /* #10 Check if SomeIpTp is initialized. */
# if (SOMEIPTP_DEV_ERROR_DETECT == STD_ON) /* COV_SOMEIPTP_DET_CHECK */
  if(SomeIpTp_IsInitialized() == FALSE)
  {
    lErrorId = SOMEIPTP_E_NOTINIT;
  }
  else if(SomeIpTp_IsTxSduIdInvalid(TxPduId) == TRUE)
  {
    lErrorId = SOMEIPTP_E_PARAM;
  }
  else
# endif
  {
    SomeIpTp_StatusOfTxStateType lStatusTx;
    SomeIpTp_TxStateIdxOfTXNSduType lTxStateIdx = SomeIpTp_GetTxStateIdxOfTXNSdu(TxPduId);
    lStatusTx = SomeIpTp_GetStatusOfTxState(lTxStateIdx);

    /* #20 If last transmitted frame is not the last segmented frame, start the separation time counter.*/
    if(lStatusTx == SOMEIPTP_TX_STATUS_WAIT_TXCONF_SF)
    {
      /* #21 If the burst size is the default burst size, set the timer to configured separation time.
       *     Otherwise, set the timer to 0. */
      if (SomeIpTp_GetTxBurstSizeOfTXNSdu(TxPduId) == SOMEIPTP_DEFAULT_BURST_SIZE)
      {
        SomeIpTp_SetTimerOfTxState(lTxStateIdx,                           /* SBSW_SOMEIPTP_CSL03 */
                  SomeIpTp_GetNPduSeparationTimeOfChannel(SomeIpTp_GetChannelIdxOfTXNSdu(TxPduId)));
      }
      else
      {
        SomeIpTp_SetTimerOfTxState(lTxStateIdx, 0u);                      /* SBSW_SOMEIPTP_CSL03 */
      }
      
      SomeIpTp_SetStatusOfTxState(lTxStateIdx, SOMEIPTP_TX_STATUS_WAIT_ST); /* SBSW_SOMEIPTP_CSL03 */
    }
    /* #30 Otherwise, reset the status of SDU to idle and pass the confirmation to PduR. */
    else if(lStatusTx == SOMEIPTP_TX_STATUS_WAIT_TXCONF_LAST_SF)
    {
      /* Delete Tx Object from Queue */
      SomeIpTp_CancelCurrTxObject(TxPduId);
      PduR_SomeIpTpTxConfirmation(SomeIpTp_GetUpperLayerIdOfTXNSdu(TxPduId), E_OK);
    }
    else
    {
       /* Do nothing */
    }
  }
    /* ----- Development Error Report --------------------------------------- */
# if (SOMEIPTP_DEV_ERROR_REPORT == STD_ON)
  if(lErrorId != SOMEIPTP_E_NO_ERROR)
  {
    SomeIpTp_ReportDet(SOMEIPTP_SID_TXCONFIRMATION, lErrorId);
  }
# else
  SOMEIPTP_DUMMY_STATEMENT(lErrorId);
# endif

#else
  SOMEIPTP_DUMMY_STATEMENT(TxPduId); 
#endif
} /* PRQA S 6080 */ /* MD_MSR_STMIF */

/***********************************************************************************************************************
 * SomeIpTp_MainFunctionTx()
 ***********************************************************************************************************************/
/*! 
 * Internal comment removed.
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
FUNC(void, SOMEIPTP_CODE) SomeIpTp_MainFunctionTx(void)
{
#if (SOMEIPTP_TXNSDU == STD_ON)
  /* ----- Local Variables ---------------------------------------------- */
  SomeIpTp_TxQueueElementIterType lQIndex = 0u;

  /* #10 Check if SomeIpTp is initialized.*/
  if((SomeIpTp_IsInitialized()==TRUE) && (SomeIpTp_IsTxQueueEmpty()==FALSE)) /* PRQA S 3415 */ /* MD_SomeIpTp_3415 */
  {
    /* #100  If initialized, loop through the Tx-Queue and check if there is any disassembly process going on for any SDU
     *       or waiting for Tx Confirmation. */
    while(lQIndex < SomeIpTp_TxQueueSize)
    {
      PduIdType lSduId = SomeIpTp_GetTxQueueElement(lQIndex);
      SomeIpTp_TxStateIdxOfTXNSduType lTxStateIdx = SomeIpTp_GetTxStateIdxOfTXNSdu(lSduId);
      lQIndex++;

      SchM_Enter_SomeIpTp_SOMEIPTP_EXCLUSIVE_AREA_0();
      if(SomeIpTp_GetStatusOfTxState(lTxStateIdx) != SOMEIPTP_TX_STATUS_IDLE)
      {
        if(SomeIpTp_GetTimerOfTxState(lTxStateIdx) != 0u)
        {
          SomeIpTp_DecTimerOfTxState(lTxStateIdx);  /* SBSW_SOMEIPTP_CSL03 */ 
          SchM_Exit_SomeIpTp_SOMEIPTP_EXCLUSIVE_AREA_0();
        }
        else
        {
          /* #101 If the separation time expired: \trace CREQ-169366 */
          if(SomeIpTp_GetStatusOfTxState(lTxStateIdx) == SOMEIPTP_TX_STATUS_WAIT_ST)
          {
            PduInfoType lPduInfo;
            uint8_least lburstIdx;

            SchM_Exit_SomeIpTp_SOMEIPTP_EXCLUSIVE_AREA_0();

            /* #102 Loop through the configured burst size and trigger the transmission of the next segments consecutively. */
            for (lburstIdx = 0; lburstIdx < SomeIpTp_GetTxBurstSizeOfTXNSdu(lSduId); lburstIdx++)
            {         
              Std_ReturnType lRetVal;
              boolean lIsLastSegment = FALSE;
              /* #103 If metadata exists for the PDU, pass the metadata by the SduPtr and set the datalength to SduLength+
               *      metadataLength, otherwise set the SduPtr to null ptr. */
# if defined(SOMEIPTP_METADATAOFTXSTATE)
              SomeIpTp_MetadataLengthOfTXNSduType lMetadataLength = SomeIpTp_GetMetadataLengthOfTXNSdu(lSduId);
              if (lMetadataLength > 0u)
              {
                lPduInfo.SduDataPtr = SomeIpTp_GetMetadataOfTxState(lTxStateIdx);
                lPduInfo.SduLength = SomeIpTp_GetNextSegmentDLOfTxState(lTxStateIdx) + lMetadataLength;
              }
              else
# endif
              {
                lPduInfo.SduLength = SomeIpTp_GetNextSegmentDLOfTxState(lTxStateIdx);
                lPduInfo.SduDataPtr = NULL_PTR;
              }
                              
              /* #104 Start the confirmation timeout timer and call the lower layer transmit function. */
              /* If last segment */
              if((SomeIpTp_GetDataLengthOfTxState(lTxStateIdx) - SomeIpTp_GetDataIndexOfTxState(lTxStateIdx)) > 
                (SomeIpTp_GetNextSegmentDLOfTxState(lTxStateIdx) - (PduLengthType)SOMEIPTP_SF_HEADER_LENGTH))
              {
                SomeIpTp_SetStatusOfTxState(lTxStateIdx, SOMEIPTP_TX_STATUS_WAIT_TXCONF_SF);  /* SBSW_SOMEIPTP_CSL03 */
              }
              else
              {
                SomeIpTp_SetStatusOfTxState(lTxStateIdx, SOMEIPTP_TX_STATUS_WAIT_TXCONF_LAST_SF);  /* SBSW_SOMEIPTP_CSL03 */
                lIsLastSegment = TRUE;
              }
              SomeIpTp_SetTimerOfTxState(lTxStateIdx,                                       /* SBSW_SOMEIPTP_CSL03 */
                SomeIpTp_GetTxConfTimeoutTimeOfChannel(SomeIpTp_GetChannelIdxOfTXNSdu(lSduId)));

              lRetVal = PduR_SomeIpTpTransmit(SomeIpTp_GetLowerLayerIdOfTXNSdu(lSduId), &lPduInfo);  /* SBSW_SOMEIPTP_TRANSMIT_CALL */
              /* #105 If transmit request fails, stop the disassembly process, invoke negative confirmation to upper layer. */
              if(lRetVal != E_OK)
              {
                SomeIpTp_CancelCurrTxObject(lSduId);
                PduR_SomeIpTpTxConfirmation(SomeIpTp_GetUpperLayerIdOfTXNSdu(lSduId), E_NOT_OK);
                SomeIpTp_ReportRuntimeDet(SOMEIPTP_SID_MAINFUNCTION_TX, SOMEIPTP_E_DISASSEMBLY_INTERRUPT);            
              }

              /* #106 If transmit request fails or the current segment triggered is the last one, exit the loop. */
              if ((lRetVal != E_OK) || (lIsLastSegment == TRUE))
              {
                /* Decrement the queue index as the current element is removed. */
                lQIndex--;
                break;
              }        
            }
          }
          else /* Confirmation timed-out */
          {
            /* #107 If the confirmation timeout time expired, Stop the disassembly process and invoke 
             *      negative confirmation to upper layer. */
            SomeIpTp_CancelCurrTxObject(lSduId);
            SchM_Exit_SomeIpTp_SOMEIPTP_EXCLUSIVE_AREA_0();
            PduR_SomeIpTpTxConfirmation(SomeIpTp_GetUpperLayerIdOfTXNSdu(lSduId), E_NOT_OK);
            SomeIpTp_ReportRuntimeDet(SOMEIPTP_SID_MAINFUNCTION_TX, SOMEIPTP_E_DISASSEMBLY_INTERRUPT);
            /* Decrement the queue index as the current element is removed. */ 
            lQIndex--;
          }
        }
      }
      else
      {
        SchM_Exit_SomeIpTp_SOMEIPTP_EXCLUSIVE_AREA_0();
      }
    }
  }
#endif
} /* PRQA S 6080, 6030 */ /* MD_MSR_STMIF */ /* MD_MSR_STCYC */

/***********************************************************************************************************************
 * SomeIpTp_RxIndication()
 ***********************************************************************************************************************/
/*! 
 * Internal comment removed.
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
FUNC(void, SOMEIPTP_CODE) SomeIpTp_RxIndication(VAR(PduIdType, SOMEIPTP_APPL_VAR) RxPduId, 
  P2CONST(PduInfoType, AUTOMATIC, SOMEIPTP_APPL_DATA) PduInfoPtr)
{
#if (SOMEIPTP_RXNSDU == STD_ON)

  /* ----- Local Variables ---------------------------------------------- */
  uint8 lErrorId = SOMEIPTP_E_NO_ERROR;
  /* ----- Development Error Checks ------------------------------------- */
  /* #10 Check if SomeIpTp is initialized and all parameters are in valid range. */
# if (SOMEIPTP_DEV_ERROR_DETECT == STD_ON) /* COV_SOMEIPTP_DET_CHECK */
  if(SomeIpTp_IsInitialized() == FALSE)
  {
    lErrorId = SOMEIPTP_E_NOTINIT;
  }
  else if(SomeIpTp_IsRxPduIdInvalid(RxPduId) == TRUE)
  {
    lErrorId = SOMEIPTP_E_PARAM;
  }
  else if(SomeIpTp_IsSduPtrValid(PduInfoPtr) == FALSE)  /* SBSW_SOMEIPTP_ISSDUPTRVALID_CALL */
  {
    lErrorId = SOMEIPTP_E_PARAM_POINTER;
  }
  else if(SomeIpTp_IsPduLengthValid(PduInfoPtr->SduLength) == FALSE)
  {
    lErrorId = SOMEIPTP_E_PARAM;
  }
  else
# endif
  {
    const uint8 *lSduPtr;
    uint32 lOffsetVal;
    PduIdType lULId;
    PduLengthType lPayloadLen;
    SomeIpTp_RxStateIdxOfRXNSduType lRxStateIdx = SomeIpTp_GetRxStateIdxOfRXNSdu(RxPduId);
    SomeIpTp_MetadataLengthOfRXNSduType lMetadataLength = SomeIpTp_GetMetadataLengthOfRXNSdu(RxPduId);
    uint8 lRuntimeErrId = SOMEIPTP_E_NO_ERROR;

    lSduPtr = PduInfoPtr->SduDataPtr;

    lULId = SomeIpTp_GetUpperLayerIdOfRXNSdu(RxPduId);

    /* #20 Check if the frame is a single frame or segmented frame. */
    if(SomeIpTp_IsMsgTypeTp(lSduPtr[SOMEIPTP_MESSAGE_TYPE_8BIT_OFFSET]) == TRUE)
    {
      uint32 lTpSpecificBits;

      lTpSpecificBits = SomeIpTp_Get32BitVal(lSduPtr[SOMEIPTP_OFFSET_FIELD0_8BIT_OFFSET], 
        lSduPtr[SOMEIPTP_OFFSET_FIELD1_8BIT_OFFSET], 
        lSduPtr[SOMEIPTP_OFFSET_FIELD2_8BIT_OFFSET], lSduPtr[SOMEIPTP_MORE_SEG_FLAG_8BIT_OFFSET]);

      lPayloadLen = (PduInfoPtr->SduLength)-SOMEIPTP_SF_HEADER_LENGTH-lMetadataLength;
      lOffsetVal = ((lTpSpecificBits & SOMEIPTP_OFFSET_FIELD_MASK) >> 4u);

      /* #21 If no assembly process is on-going for this Pdu: */
      if(SomeIpTp_GetStatusOfRxState(lRxStateIdx) == SOMEIPTP_RX_STATUS_IDLE)
      {
        /* #210 if the frame is segmented frame, verify if the offset is zero, 
        more segment flag is set and Payload is 16 byte aligned.*/
        if((lOffsetVal == SOMEIPTP_FF_OFFSET) && 
          (SomeIpTp_IsMoreSegFlagSet(lTpSpecificBits)==TRUE) && (SomeIpTp_IsPayloadSixteenByteAligned(lPayloadLen)==TRUE)) /* PRQA S 3415 */ /* MD_SomeIpTp_3415 */
        {
          /* #2100 Start the assembly process for this Pdu. */
          lRuntimeErrId = SomeIpTp_StartNewRxSession(RxPduId, PduInfoPtr, lPayloadLen);  /* SBSW_SOMEIPTP_STARTNEWRXSESSION_CALL */
        }
# if (SOMEIPTP_RUNTIME_ERROR_REPORT == STD_ON)
        /* #211 Else report runtime error with SOMEIPTP_E_INCONSISTENT_SEQUENCE. */
        else if(lOffsetVal == SOMEIPTP_FF_OFFSET)
        {
          SomeIpTp_ReportRuntimeDet(SOMEIPTP_SID_RXINDICATION, SOMEIPTP_E_ASSEMBLY_INTERRUPT);
        }
        else
        {
          SomeIpTp_ReportRuntimeDet(SOMEIPTP_SID_RXINDICATION, SOMEIPTP_E_INCONSISTENT_SEQUENCE);
        }
# endif
      }
      else /* #22 Else if PDU received in the current session */
      {
        /* #220 Check if Offset is greater than zero */
        if(lOffsetVal != SOMEIPTP_FF_OFFSET)
        {
          /* 2200 Stop the assembly process ,Report runtime error and give negative Rx Indication to the upper layer if: */
          /* 1. Inconsistent header */
          if(SomeIpTp_IsHeaderValid(lSduPtr, SomeIpTp_GetTpHeaderOfRxState(lRxStateIdx)) == FALSE)  /* SBSW_SOMEIPTP_INTERNAL_API_CALL_SDUDATAPTR */
          {
            lRuntimeErrId = SOMEIPTP_E_INCONSISTENT_HEADER;
          }
          /* 2. Received offset does not match with the payload received in the previous segments */
          else if((lOffsetVal * SOMEIPTP_DATA_ALIGNMENT_LENGTH) != SomeIpTp_GetDataIndexOfRxState(lRxStateIdx))
          {
            lRuntimeErrId = SOMEIPTP_E_INCONSISTENT_SEQUENCE;
          }
          /* 3.Received payload does not fit in the available buffer */
          else if(SomeIpTp_GetAvailBufSizeOfRxState(lRxStateIdx) < lPayloadLen)
          {
            lRuntimeErrId = SOMEIPTP_E_ASSEMBLY_INTERRUPT;
          }
# if defined (SOMEIPTP_METADATAOFRXSTATE)
          /* 4. Inconsistent metadata */  
          else if((lMetadataLength > 0u) && (SomeIpTp_IsMetadataValid(lMetadataLength, &lSduPtr[PduInfoPtr->SduLength-lMetadataLength], /* PRQA S 3415 */ /* MD_SomeIpTp_3415 */ /* SBSW_SOMEIPTP_INTERNAL_API_CALL_SDUDATAPTR */
          SomeIpTp_GetMetadataOfRxState(lRxStateIdx)) == FALSE)) 
          {
            lRuntimeErrId = SOMEIPTP_E_INCONSISTENT_METADATA;
          }
# endif
          else
          {
            PduInfoType lPduInfo;
            PduLengthType lBufSize;
            lPduInfo.SduDataPtr = &PduInfoPtr->SduDataPtr[SOMEIPTP_SF_HEADER_LENGTH];
            lPduInfo.SduLength = lPayloadLen;

            /* is payload 16 byte aligned */
            if((SomeIpTp_IsMoreSegFlagSet(lTpSpecificBits)==TRUE) && 
              (SomeIpTp_IsPayloadSixteenByteAligned(lPayloadLen)==TRUE)) /* PRQA S 3415 */ /* MD_SomeIpTp_3415 */
            {
              /* 2201 Call PduR_SomeIpTpCopyRxData to copy data by the upper layer and verify the return value */
              if(PduR_SomeIpTpCopyRxData(lULId, &lPduInfo, &lBufSize) == BUFREQ_OK)     /* SBSW_SOMEIPTP_COPYRXDATA_CALL */
              {
                /* 22010 if More seg flag set and BUFREQ_OK ,Update received payload and store the available 
                buffer size for the next segment */
                SomeIpTp_SetDataIndexOfRxState(lRxStateIdx, SomeIpTp_GetDataIndexOfRxState(lRxStateIdx)+lPayloadLen); /* SBSW_SOMEIPTP_CSL03 */
                SomeIpTp_SetAvailBufSizeOfRxState(lRxStateIdx, lBufSize);  /* SBSW_SOMEIPTP_CSL03 */
                SomeIpTp_SetTimerOfRxState(lRxStateIdx,                    /* SBSW_SOMEIPTP_CSL03 */
                  SomeIpTp_GetRxTimeoutTimeOfChannel(SomeIpTp_GetChannelIdxOfRXNSdu(RxPduId)));
              }
              else  /* 6. if upperlayer returns other than BUFREQ_OK */
              {
                lRuntimeErrId = SOMEIPTP_E_ASSEMBLY_INTERRUPT;
              }
            }
            else if(SomeIpTp_IsMoreSegFlagSet(lTpSpecificBits)==FALSE)
            {
              if(PduR_SomeIpTpCopyRxData(lULId, &lPduInfo, &lBufSize) == BUFREQ_OK)  /* SBSW_SOMEIPTP_COPYRXDATA_CALL */
              {
                /* 22011 if More seg flag not set and BUFREQ_OK ,Give Rx Indication to the upper layer 
                indicating the last frame received */
                PduR_SomeIpTpRxIndication(lULId, E_OK);
                /* Reset the Pdu state to Idle */
                SomeIpTp_SetStatusOfRxState(lRxStateIdx, SOMEIPTP_RX_STATUS_IDLE);  /* SBSW_SOMEIPTP_CSL03 */
              }
              else  /* 6. if upperlayer returns other than BUFREQ_OK */
              {
                lRuntimeErrId = SOMEIPTP_E_ASSEMBLY_INTERRUPT;
              }
            }
            else /* 5. If Payload is not divisible by 16 when more segment flag is set */
            {
              lRuntimeErrId = SOMEIPTP_E_ASSEMBLY_INTERRUPT;
            }
          }
        }
        else
        { /* #221 Else if offset is zero , Report runtime error , negative Rx Indication to upper layer 
          and start the assembly process with offset 0 */
          SomeIpTp_ReportRuntimeDet(SOMEIPTP_SID_RXINDICATION, SOMEIPTP_E_INCONSISTENT_SEQUENCE);
          PduR_SomeIpTpRxIndication(lULId, E_NOT_OK);
          lRuntimeErrId = SomeIpTp_StartNewRxSession(RxPduId, PduInfoPtr, lPayloadLen); /* SBSW_SOMEIPTP_STARTNEWRXSESSION_CALL */
        }
      }
    }
    else /* #30 if the message type is not TP type */
    {
      /* #31 Check if no session is currently running */
      if(SomeIpTp_GetStatusOfRxState(lRxStateIdx) == SOMEIPTP_RX_STATUS_IDLE)
      {
        PduInfoType lPduInfo;
        PduLengthType lAvailBufSize;

        /* #310 If metadata exists, pass the metadata by the SduPtr and set the SduLength to metadata length. Otherwise 
         *      set the SduPtr to null ptr. Indicate start of reception to the upperlayer and verify the available 
         *      buffer size and return value. */
        lPayloadLen = (PduInfoPtr->SduLength) - lMetadataLength;
        lPduInfo.SduLength =  lMetadataLength;
# if defined (SOMEIPTP_METADATAOFRXSTATE)
        if (lMetadataLength > 0u)
        {
          lPduInfo.SduDataPtr = &PduInfoPtr->SduDataPtr[lPayloadLen];
        }
        else
# endif
        {
          lPduInfo.SduDataPtr = NULL_PTR;
        } 

        if(PduR_SomeIpTpStartOfReception(lULId, &lPduInfo, lPayloadLen, &lAvailBufSize) == BUFREQ_OK)   /* SBSW_SOMEIPTP_STARTOFRECEPTION_CALL */
        {
          /* #3100 if payload fits in the available buffer and BUFREQ_OK, call Copy Rx Data to the upper layer 
          and verify the return value */
          lPduInfo.SduDataPtr = PduInfoPtr->SduDataPtr;
          lPduInfo.SduLength = lPayloadLen;

          if((lAvailBufSize >= lPayloadLen) && 
            (PduR_SomeIpTpCopyRxData(lULId, &lPduInfo, &lAvailBufSize) == BUFREQ_OK)) /* PRQA S 3415 */ /* MD_SomeIpTp_3415 */ /* SBSW_SOMEIPTP_COPYRXDATA_CALL */
          {
            /* #31000 If BUFREQ_OK, call Rx Indication to the upper layer indicating end of reception */
            PduR_SomeIpTpRxIndication(lULId, E_OK);
          }
          else
          {
            /* #31001 Else report runtime error with assembly interrupt and negative Rx Indication */
            SomeIpTp_ReportRuntimeDet(SOMEIPTP_SID_RXINDICATION, SOMEIPTP_E_ASSEMBLY_INTERRUPT);
            PduR_SomeIpTpRxIndication(lULId, E_NOT_OK);
          }
        }
        else
        {
          SomeIpTp_ReportRuntimeDet(SOMEIPTP_SID_RXINDICATION, SOMEIPTP_E_ASSEMBLY_INTERRUPT);
        }
      }
      else /* 32 If session is currently running, report runtime error with wrong message type */
      {
        lRuntimeErrId = SOMEIPTP_E_MESSAGE_TYPE;
      }
    }
    /* ----- Runtime Error Report --------------------------------------- */
    if(lRuntimeErrId != SOMEIPTP_E_NO_ERROR)
    {
      SomeIpTp_ReportRuntimeDet(SOMEIPTP_SID_RXINDICATION, lRuntimeErrId);
      PduR_SomeIpTpRxIndication(lULId, E_NOT_OK);
      SomeIpTp_SetStatusOfRxState(lRxStateIdx, SOMEIPTP_RX_STATUS_IDLE);  /* SBSW_SOMEIPTP_CSL03 */
    }
  }
  /* ----- Development Error Report --------------------------------------- */
# if (SOMEIPTP_DEV_ERROR_REPORT == STD_ON)
  if(lErrorId != SOMEIPTP_E_NO_ERROR)
  {
    SomeIpTp_ReportDet(SOMEIPTP_SID_RXINDICATION, lErrorId);
  }
# else
  SOMEIPTP_DUMMY_STATEMENT(lErrorId);
# endif

#else
SOMEIPTP_DUMMY_STATEMENT(RxPduId);
SOMEIPTP_DUMMY_STATEMENT(PduInfoPtr);
#endif
} /* PRQA S 6080, 6050, 6030, 6010 */ /* MD_MSR_STMIF */ /* MD_MSR_STCAL */ /* MD_MSR_STCYC */ /* MD_MSR_STPTH */

/***********************************************************************************************************************
 * SomeIpTp_MainFunctionRx()
 ***********************************************************************************************************************/
/*! 
 * Internal comment removed.
 *
 *
 *
 *
 *
 */
FUNC(void, SOMEIPTP_CODE) SomeIpTp_MainFunctionRx(void)
{
#if (SOMEIPTP_RXNSDU == STD_ON)
  /* #10 Check if SomeIpTp is initialized.*/
  if(SomeIpTp_IsInitialized() == TRUE)
  {
    /* ----- Local Variables ---------------------------------------------- */
    SomeIpTp_RXNSduIterType lSduIndex;

    for(lSduIndex=0; lSduIndex < SomeIpTp_GetSizeOfRXNSdu(); lSduIndex++)
    {
# if (SOMEIPTP_INVALIDHNDOFRXNSDU == STD_ON)
      if (SomeIpTp_IsInvalidHndOfRXNSdu(lSduIndex) == FALSE) /* PRQA S 1881 */ /* MD_MSR_AutosarBoolean */
# endif
      {
        SomeIpTp_RxStateIdxOfRXNSduType lRxStateIdx = SomeIpTp_GetRxStateIdxOfRXNSdu(lSduIndex);
        /* #100 If initialized, Check if there is an assembly process going on for any SDU. */
        if(SomeIpTp_GetStatusOfRxState(lRxStateIdx) == SOMEIPTP_RX_STATUS_WAIT_NEXT_SF)
        {
          SchM_Enter_SomeIpTp_SOMEIPTP_EXCLUSIVE_AREA_0();
          /* PDU still not received? */
          if(SomeIpTp_GetStatusOfRxState(lRxStateIdx) == SOMEIPTP_RX_STATUS_WAIT_NEXT_SF)  /* COV_SOMEIPTP_RX_STATE_ALWAYS_TRUE */
          {
            /* #1000 If RX Timeout time expired, stop the current assembly process and give negative Rx Indication 
            to the upper layer. */
            if(SomeIpTp_GetTimerOfRxState(lRxStateIdx) != 0u)
            {
              SomeIpTp_DecTimerOfRxState(lRxStateIdx);  /* SBSW_SOMEIPTP_CSL03 */
              SchM_Exit_SomeIpTp_SOMEIPTP_EXCLUSIVE_AREA_0();
            }
            else /* Rx Indication Timed-out */
            {
              /* \trace CREQ-169363 */
              SomeIpTp_SetStatusOfRxState(lRxStateIdx, SOMEIPTP_RX_STATUS_IDLE);  /* SBSW_SOMEIPTP_CSL03 */
              SchM_Exit_SomeIpTp_SOMEIPTP_EXCLUSIVE_AREA_0();
              SomeIpTp_ReportRuntimeDet(SOMEIPTP_SID_MAINFUNCTION_RX, SOMEIPTP_E_ASSEMBLY_INTERRUPT);
              PduR_SomeIpTpRxIndication(SomeIpTp_GetUpperLayerIdOfRXNSdu(lSduIndex), E_NOT_OK);
            }
          }
          else
          {
            SchM_Exit_SomeIpTp_SOMEIPTP_EXCLUSIVE_AREA_0();
          }
        }
      }
    }
  }
#endif
} /* PRQA S 6080 */ /* MD_MSR_STMIF */

/* Justification for module-specific MISRA deviations:

MD_SomeIpTp_3415: The right hand operand of '&&' or '||' has side effects.
  Reason:     Function call in right hand operator does not have any side effects.
  Risk:       None.
  Prevention: Covered by code review.

*/
/* *INDENT-OFF* */

/* SBSW_JUSTIFICATION_BEGIN
  \ID SBSW_SOMEIPTP_ADD_QUEUE_ELEMENT
    \DESCRIPTION      Write access to the SomeIpTp_TxQueueElement, indexed by the SomeIpTp_TxQueueSize parameter.
                      The function expects SomeIpTp_TxQueueSize to be valid (SomeIpTp_TxQueueSize < maximum number of queue elements).
    \COUNTERMEASURE \R The current size of the queue is checked to be smaller than maximum queue size.

  \ID SBSW_SOMEIPTP_DELETE_QUEUE_ELEMENT
    \DESCRIPTION      Write access to the SomeIpTp_TxQueueElement, indexed by the lQPos parameter.
                      The function expects lQPos to be valid (lQPos < maximum number of queue elements).
    \COUNTERMEASURE \R The parameter lQPos is checked against maximum queue size.
  
  \ID SBSW_SOMEIPTP_CSL03
    \DESCRIPTION      Access to Table1 via indirection over Table2. Index to SomeIpTp_TxState and SomeIpTp_RxState
                      must be valid. Index is retrieved by SomeIpTp_GetTxStateIdxOfTXNSdu, SomeIpTp_GetRxStateIdxOfRXNSdu
                      using TxSduId and RxSduId handles. The validity of TxSduId and RxSduId is verified by DET.
    \COUNTERMEASURE \N Qualified use-case CSL03 of ComStackLib.

  \ID SBSW_SOMEIPTP_CREATEHEADER_WRITE_SDUDATAPTR
    \DESCRIPTION      Write access to SduDataPtr. Validity of pointer and SduLength is verified by the calling function.
    \COUNTERMEASURE \R The SduLength is verified against the expected segment length by the calling 
                       function(SomeIpTp_TriggerTransmit) and the SduDataPtr is expected to be valid.
                    \M [CM_SOMEIPTP_MAXTXSFPAYLOAD]
  
  \ID SBSW_SOMEIPTP_INTERNAL_API_CALL_SDUDATAPTR
    \DESCRIPTION     The API is called with a pointer to SDU Data. 
    \COUNTERMEASURE \R The validity of the pointer is verified by the calling function.

  \ID SBSW_SOMEIPTP_STARTOFRECEPTION_CALL
    \DESCRIPTION     The API PduR_SomeIpTpStartOfReception is called with a pointer to PduInfo and Buffer size.
    \COUNTERMEASURE \N The pointers are either intialized with the local stack variables or with the pointers forwarded.
                       The pointers forwarded are verified by the calling function.
 
 \ID SBSW_SOMEIPTP_COPYRXDATA_CALL
    \DESCRIPTION     The API PduR_SomeIpTpCopyRxData is called with a pointer to PduInfo and Buffer size.
    \COUNTERMEASURE \N The pointers are either intialized with the local stack variables or with the pointers forwarded.
                       The forwarded pointers are verified by the calling function.
  
  \ID SBSW_SOMEIPTP_TRANSMIT_CALL
    \DESCRIPTION     The API PduR_SomeIpTpTransmit is called with a pointer to PduInfo.
    \COUNTERMEASURE \R The SduDataPtr and SduLength of PduInfoPtr are verified by the calling function.
  
  \ID SBSW_SOMEIPTP_COPYTXDATA_CALL
    \DESCRIPTION     The API PduR_SomeIpTpCopyTxData is called with a pointer to lSduInfo and lAvailableDataLength
    \COUNTERMEASURE \N The pointers are either intialized with the local stack variables or with the pointers forwarded.
                       The forwarded pointers are verified by the calling function.
  
  \ID SBSW_SOMEIPTP_GETVERSION_PARAM
    \DESCRIPTION    The SomeIpTp_GetVersionInfo writes to the 'versioninfo' pointer parameter. It is checked against NULL,
                    but this does not guarantee that the referenced memory is valid.
    \COUNTERMEASURE \N  Pointers passed to public SomeIpTp APIs point to a valid memory range. A general countermeasure 
                        is included in the safety manual.

 \ID SBSW_SOMEIPTP_ISSDUPTRVALID_CALL
    \DESCRIPTION    The internal API SomeIpTp_IsSduPtrValid is called to check the validity of the PduInfoPtr and SduDataPtr.
                    No write access is performed to these pointers inside this function.
    \COUNTERMEASURE \N  -

  \ID SBSW_SOMEIPTP_STARTNEWRXSESSION_CALL
    \DESCRIPTION    The internal API SomeIpTp_StartNewRxSession is called with RxPduId and PduInfoPtr. RxPduId is used as
                    an index to internal structures.                    
    \COUNTERMEASURE \R - The validity of RxPduId and PduInfoPtr is verified by the calling function (SomeIpTp_RxIndication) by DET.

  \ID SBSW_SOMEIPTP_STARTNEWRXSESSION_WRITE_SDUDATAPTR
    \DESCRIPTION      Write access to SduDataPtr. The pointer is forwarded and validity of pointer is verified against null pointer
                      by the calling function.
    \COUNTERMEASURE \R The SduDataPtr is verified against null pointer by DET by the calling function(SomeIpTp_RxIndication) 
                       and the SduDataPtr is expected to be valid.
 
 \ID SBSW_SOMEIPTP_TRANSMIT_WRITE_TPHEADER
    \DESCRIPTION      Write access to local array lTpHeader. lTpHeader is initialized to the global TpHeaderOfTxState 
                      which is always valid.
    \COUNTERMEASURE \M [CM_SOMEIPTP_PRECOMPILEOPTIONS]
  
  \ID SBSW_SOMEIPTP_TRIGGERTRANSMIT_WRITE_PDUINFOPTR
    \DESCRIPTION      Write access to PduInfoPtr->SduLength. The validity of the pointer is verified against null pointer.
    \COUNTERMEASURE \R The validity of the pointer is verified in the function SomeIpTp_TriggerTransmit before ths write access.

  \ID SBSW_SOMEIPTP_COPYHEADER_WRITE_DESTPTR
    \DESCRIPTION      Write access to Dest pointer in the inline function SomeIpTp_CopyHeader. The destination pointer 
                      points to the TpHeader array generated by SomeIpTp and the size of the array is always valid.
    \COUNTERMEASURE \M [CM_SOMEIPTP_PRECOMPILEOPTIONS]
  
  \ID SBSW_SOMEIPTP_COPYMETADATA_WRITE_DESTPTR
    \DESCRIPTION      Write access to Dest pointer in the inline function SomeIpTp_CopyMetadata. The destination pointer 
                      points to the Metadata array and the size of the array is the metadata length configured and 
                      is always valid.
    \COUNTERMEASURE \N -

SBSW_JUSTIFICATION_END */

/* ------------------------------------------------------------------------------------------------------------------ */
/* Silent BSW Countermeasures                                                                                         */
/* ------------------------------------------------------------------------------------------------------------------ */
/*
--- MSSV Plugin ---
\CM CM_SOMEIPTP_PRECOMPILEOPTIONS To ensure that all mandatory / excluded features are as expected, the following must be verified by MSSV:
                                  - SOMEIPTP_DEV_ERROR_DETECT = STD_ON
                                  - SOMEIPTP_MIN_SDU_LENGTH = 8

\CM CM_SOMEIPTP_MAXTXSFPAYLOAD The MaxTxSFPayload of each Tx SDU must be greater than or equal to the length of the
                               SOME/IP TP header (12 bytes).
*/

/* ------------------------------------------------------------------------------------------------------------------ */
/* Coverage Justifications                                                                                            */
/* ------------------------------------------------------------------------------------------------------------------ */

/* COV_JUSTIFICATION_BEGIN
 *
--- Preprocessor Coverage Justifications ------------------------------------------------------------------------------
\ID COV_SOMEIPTP_DET_CHECK
  \ACCEPT TX
  \REASON DET has to be enabled for SafeBSW. A MSSV Plugin enforces this configuration.

--- Condition Coverage Justifications ---------------------------------------------------------------------------------
\ID COV_SOMEIPTP_RX_STATE_ALWAYS_TRUE
    \ACCEPT TX
    \REASON This code block cannot be interrupted within test environment and this check is added for robustness.
            Therefore, the condition is always true.

COV_JUSTIFICATION_END */

/***********************************************************************************************************************
 *  END OF FILE: SomeIpTp.c
 **********************************************************************************************************************/
