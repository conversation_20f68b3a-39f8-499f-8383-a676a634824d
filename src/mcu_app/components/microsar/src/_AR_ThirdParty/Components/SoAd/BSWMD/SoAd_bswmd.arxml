<?xml version="1.0" encoding="UTF-8"?>
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://autosar.org/schema/r4.0">
	<AR-PACKAGES>
		<AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
			<SHORT-NAME>MICROSAR</SHORT-NAME>
			<ELEMENTS>
				<ECUC-MODULE-DEF UUID="4231da07-9bd4-4fb6-8568-cea2233c2c32">
					<SHORT-NAME>SoAd</SHORT-NAME>
					<DESC>
						<L-2 L="EN">Configuration of the SoAd (Socket Adaptor) module.</L-2>
					</DESC>
					<CATEGORY>VENDOR_SPECIFIC_MODULE_DEFINITION</CATEGORY>
					<ADMIN-DATA>
						<DOC-REVISIONS>
							<DOC-REVISION>
								<REVISION-LABEL>4.00.00</REVISION-LABEL>
								<STATE>beta</STATE>
								<ISSUED-BY>vismha</ISSUED-BY>
								<DATE>2013-05-21</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">initial creation of SoAd Asr4.1.1 BSWMD with Asr4.0.3 schema</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>4.01.00</REVISION-LABEL>
								<STATE>beta</STATE>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2014-01-31</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">added SoAdSocketUdpImmediateIfTxConfirmation and SoAdSocketGetReceivedRemoteAddressEnabled</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>4.01.02</REVISION-LABEL>
								<ISSUED-BY>vismha</ISSUED-BY>
								<DATE>2014-03-17T01:55:44+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">added Best Match switch parameters, added udp alive timeout paramter</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00074241, ESCAN00074239, ESCAN00074096</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>4.01.03</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2014-04-25T08:37:43+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">reworked description for SoAdSocketUdpImmediateIfTxConfirmation</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00074601</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>4.02.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2014-05-16T07:15:38+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">added TxQeue for streaming-based TxConfirmation</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00075636</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>4.03.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2014-05-22T06:07:09+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>4.05.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2014-07-24T06:47:06+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Additional SoConModeChg callback user, Support remote address "not set" value</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00076929, ESCAN00076808</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>4.05.01</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2014-09-04T10:43:18+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>4.05.02</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2014-09-04T10:43:19+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>4.07.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2014-09-09T12:45:23+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>5.00.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2014-10-02T06:10:22+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.00.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2015-01-27T02:03:47+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">FEAT-261: API between SoAd and TcpIp according to AR 4.2.1</L-2>
										</CHANGE>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">FEAT-1001: Sd Enhanced Config for Asr4.2.1</L-2>
										</CHANGE>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Implement constant pointer parameter according to ASR4.2.1</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00081894</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.00.01</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2015-05-07T07:36:02+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SoAdShutdownFinishedWaitTime has no Description</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00082235</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.00.02</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2015-05-22T06:54:26+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>7.00.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2015-04-24T01:23:51+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">FEAT-1249: Optimized UDP retry behavior e.g. in case of ARP miss</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00082642</L-2>
										</REASON>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">FEAT-1410: Support BSD Socket API</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00082652</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>7.00.01</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2015-08-14T01:31:18+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>7.00.02</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2015-08-28T04:46:12+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>8.00.00</REVISION-LABEL>
								<ISSUED-BY>vispcn</ISSUED-BY>
								<DATE>2015-09-28T11:01:27+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">FEAT-1505: Postbuild Loadable for Socket Adaptor and Service Discovery</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00085173</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>8.01.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2016-01-22T10:03:37+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">FEAT-1347: TLS as TcpIp plug-in</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00087656</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>8.01.01</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2016-04-05T02:34:20+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">added SoAdShutdownFinishedCallback/SoAdShutdownFinished</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00089436</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>8.01.02</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2016-04-19T02:38:27+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>8.01.03</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2016-04-20T04:23:23+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>8.02.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2016-04-22T08:57:56+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">FEAT-1910: Release of BSD-Socket API</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00089672</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>8.02.01</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2016-05-02T05:03:12+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>8.02.02</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2016-05-19T04:20:18+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>8.03.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2016-05-19T04:22:52+02:00</DATE>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>8.03.01</REVISION-LABEL>
								<ISSUED-BY>vispcn</ISSUED-BY>
								<DATE>2016-07-26T09:28:45+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>8.03.02</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2016-08-10T10:55:06+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>8.03.03</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2016-08-22T04:05:43+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Include additional header files in SoAd_Lcfg.c and SoAd_Cfg.h</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00091562</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>9.00.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2016-08-22T05:20:11+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">FEAT-972: SoAd shall implement an optimized buffer handling for PDU fan-out on multiple socket connections</L-2>
										</CHANGE>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">FEAT-1816: Split MainFunctions to optimize gateway use-case</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>9.00.01</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2016-11-29T12:39:02+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>9.01.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2017-01-13T09:06:07+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">FEATC-886: FEAT-2385: Use Event Queues for SoAd MainFunction handling</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>9.02.00</REVISION-LABEL>
								<ISSUED-BY>vispcn</ISSUED-BY>
								<DATE>2017-02-17T01:20:22+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">FEAT-1824: Support a component-specific SafeBSW configuration switch</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">FEATC-939</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>10.00.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2017-05-02T09:53:11+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>10.01.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2017-06-02T19:37:41+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>10.01.01</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2017-06-12T10:09:23+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>10.02.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2017-06-12T13:44:35+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">STORYC-1202: SoAd: Callout for Diagnostic Firewall Use Case</L-2>
										</CHANGE>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">STORYC-303: SoAd: Support optimized PDU handling for C/S calls</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>10.03.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2017-07-21T11:00:04+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">STORYC-372: P3 Code Refactoring / CDD Step 1</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>10.04.00</REVISION-LABEL>
								<ISSUED-BY>vispcn</ISSUED-BY>
								<DATE>2017-08-07T11:38:08+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>11.00.00</REVISION-LABEL>
								<ISSUED-BY>vispcn</ISSUED-BY>
								<DATE>2017-08-07T11:40:15+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">STORYC-1852: SAFE Code Refactoring Generator (ComStackLib)</L-2>
										</CHANGE>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">STORYC-4097: Support QNX</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>11.00.01</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2018-04-12T10:30:59+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>11.00.02</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2018-04-17T12:40:05+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Local IP address change is not detected</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00098610</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>12.00.00</REVISION-LABEL>
								<ISSUED-BY>vissem</ISSUED-BY>
								<DATE>2018-06-28</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Added "INTEGRITY" to SoAdSocketApiType</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">STORYC-5228: SoAd Option BSD: Support Integrity</L-2>
										</REASON>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Changed default value of SoAdTxUdpTriggerMode to "TRIGGER_NEVER"</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>12.00.01</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2018-08-09T08:21:40+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>12.01.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2018-08-09T09:54:42+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>13.00.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2018-09-21T08:51:19+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>14.00.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2019-03-18T16:06:15+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<REASON>
											<L-2 L="EN">STORY-11153</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>14.01.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2019-05-22T13:06:11+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>14.01.01</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2019-07-24T15:23:19+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>15.00.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2019-08-15T12:35:24+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">TCPIP-833: Support SoAd_GetAndResetMeasurementData()</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">TCPIP-833</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>15.00.01</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2019-09-02T10:04:05+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Transmission via TriggerTransmit with IP fragmentation fails</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00098754</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>15.00.02</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2019-11-20T11:10:11+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SW version update</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>15.00.03</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2020-01-16T15:08:02+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">TCP transmission in wrong order</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00102846</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>16.00.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2020-02-07T13:38:57+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Support for AR RfC 92400: SOME/IP multi instance support on client side using the same socket</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">SAA-302</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>16.01.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2020-05-12T10:11:58+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Optimized IF transmission</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">SAA-640</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>16.02.00</REVISION-LABEL>
								<ISSUED-BY>vismda</ISSUED-BY>
								<DATE>2020-06-10T10:19:43+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SoAd PDU-Fanout for SomeIp-TP</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">SAA-735</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>16.03.00</REVISION-LABEL>
								<ISSUED-BY>visjsb</ISSUED-BY>
								<DATE>2020-07-13T15:46:36+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Support meta data for TriggerTransmit</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">SAA-684</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>16.04.00</REVISION-LABEL>
								<ISSUED-BY>jelsaesser</ISSUED-BY>
								<DATE>2020-09-10T10:17:47+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Make reception of broadcasts in SoAd configurable</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">SAA-716</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
						</DOC-REVISIONS>
					</ADMIN-DATA>
					<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
					<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
					<REFINED-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/SoAd</REFINED-MODULE-DEF-REF>
					<SUPPORTED-CONFIG-VARIANTS>
						<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
						<SUPPORTED-CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</SUPPORTED-CONFIG-VARIANT>
					</SUPPORTED-CONFIG-VARIANTS>
					<CONTAINERS>
						<!-- Container Definition: SoAdBswModules -->
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="e38bf46e-bcae-4c5a-87d4-3c5269ce8d0d">
							<SHORT-NAME>SoAdBswModules</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Each container describes a specific BSW module that the SoAd shall interface to.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
							<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
							<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
							<PARAMETERS>
								<!-- PARAMETER DEFINITION: SoAdIf -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="3a92407d-bf4e-4cf0-a911-5335ab35f70f">
									<SHORT-NAME>SoAdIf</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Specifies if the BSW module supports the Communication Interface APIs or not. Value true means that the APIs are supported. A module can have both Communication Interface APIs and Transport Protocol APIs (e.g. the PduR module).</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: SoAdIfTriggerTransmit -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="fd1f5968-5e27-4bab-a472-3b9e5453f852">
									<SHORT-NAME>SoAdIfTriggerTransmit</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Specifies if the BSW module supports the TriggerTransmit API or not. Value true means that the API is supported.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: SoAdIfTxConfirmation -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="4e2b064d-1665-4f8f-bea0-6f8546424bdd">
									<SHORT-NAME>SoAdIfTxConfirmation</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Specifies if the BSW module supports the TxConfirmation API or not. Value true means that the API is supported.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: SoAdLocalIpAddrAssigmentChg -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="e3b2a69d-c684-4fd4-8cc1-b9579289b67c">
									<SHORT-NAME>SoAdLocalIpAddrAssigmentChg</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Specifies if the BSW module supports the LocalIpAddrAssigmentChg API or not. Value true means that the API is supported.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: SoAdSoConModeChg -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="add80472-7f4a-4ced-af43-f25280b8fd29">
									<SHORT-NAME>SoAdSoConModeChg</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Specifies if the BSW module supports the SoConModeChg API or not. Value true means that the API is supported.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: SoAdTp -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="ba22283e-14e3-47a5-9937-b1edd99b28f6">
									<SHORT-NAME>SoAdTp</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Specifies if the BSW module supports the TransportProtocol APIs or not. Value true means that the APIs are supported. A module can have both Communication Interface APIs and Transport Protocol APIs (e.g. the PduR module).</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: SoAdUseCallerInfix -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="32151598-c92a-4b21-bee8-0a274e9d657c">
									<SHORT-NAME>SoAdUseCallerInfix</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Specifies if SoAd shall use (TRUE) the infix "SoAd" when calling an upper layer module function or not (FALSE). E.g. if SoAdUseCallerInfix is TRUE for the upper layer "ABC" then SoAd will call ABC_SoAdIfRxIndication() otherwise SoAd would call ABC_IfRxIndication().</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: SoAdUseTypeInfix -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="e0f2ae36-c1de-4f2e-b382-************">
									<SHORT-NAME>SoAdUseTypeInfix</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Specifies if SoAd shall use (TRUE) the API type infix "Tp" or "If" when calling an upper layer module function or not (FALSE). E.g. if SoAdUseTypeInfix is TRUE for the upper layer "ABC" then SoAd will call ABC_IfRxIndication(), otherwise SoAd would call ABC_RxIndication().</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="9d8b6f7a-471e-40f5-8964-507cc2e73a68">
									<SHORT-NAME>SoAdShutdownFinishedCbk</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter enables a callback which is called if SoAd is in shutdown state after successful call to SoAd_Shutdown().</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="f4cb85d7-effb-4b49-87d6-5f0235e20fd7">
									<SHORT-NAME>SoAdTpCopyTxDataWithConstPointer</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter specifies if the upper layer Ul_[SoAd][Tp]CopyTxData() function is implemented with constant pointer for parameter "info".</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="46c95bce-d6e9-42cf-86d7-fa0f533aa6ed">
									<SHORT-NAME>SoAdTpCopyRxDataWithConstPointer</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter specifies if the upper layer Ul_[SoAd][Tp]CopyRxData() function is implemented with constant pointer for parameter "info".</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="3dc1750e-a20a-4470-9f00-e98156048da7">
									<SHORT-NAME>SoAdTpStartOfReceptionWithConstPointer</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter specifies if the upper layer Ul_[SoAd][Tp]StartOfReception() function is implemented with constant pointer for parameter "info".</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
							</PARAMETERS>
							<REFERENCES>
								<!-- Foreign Reference Definition: SoAdBswModuleRef -->
								<ECUC-FOREIGN-REFERENCE-DEF UUID="95ed6f88-108a-4626-9441-fb4333d9b903">
									<SHORT-NAME>SoAdBswModuleRef</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This is a reference to one BSW module's configuration (i.e. not the ECUC parameter definition template).</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">Example, there could be several configurations of PduR and this reference selects one of them.

                                        SoAd has to figure out from the structure of the referenced BSW module's configuration, what kind of upper layer he deals with.
                                        In case of a CDD SoAd expects UL-APIs in form of _SoAd&lt;If|Tp&gt;&lt;function&gt; and expects CDD Pdu configuration structures according to the Ecu Configuration specification (chapter CDD module\Socket Adaptor).
                                        In case it is one of the standardized AUTOSAR BSW modules, the configuration structures and API names for interaction with SoAd are defined in the corresponding SWS.</L-1>
										</P>
									</INTRODUCTION>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<DESTINATION-TYPE>ECUC-MODULE-CONFIGURATION-VALUES</DESTINATION-TYPE>
								</ECUC-FOREIGN-REFERENCE-DEF>
							</REFERENCES>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
						<!-- Container Definition: SoAdConfig -->
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="1184255e-b46f-420e-b4bc-cc7ed0c1c11f">
							<SHORT-NAME>SoAdConfig</SHORT-NAME>
							<DESC>
								<L-2 L="EN">This container contains the configuration parameters and sub containers of the AUTOSAR SoAd module. This container is a MultipleConfigurationContainer, i.e. this container and its sub-containers exist once per configuration set.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
							<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
							<SUB-CONTAINERS>
								<!-- Container Definition: SoAdPduRoute -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="c32ed2df-5355-4060-bdef-7b09670d11e4">
									<SHORT-NAME>SoAdPduRoute</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Describes the path of a PDU from an upper layer of the SoAd to the socket in the TCP/IP stack for transmission.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: SoAdTxPduId -->
										<ECUC-INTEGER-PARAM-DEF UUID="309a330c-09f0-44f9-bccd-6f89a78b1796">
											<SHORT-NAME>SoAdTxPduId</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">PDU ID</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Tx PDU ID of the PDU coming from the PDU Router.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SoAdTxUpperLayerType -->
										<ECUC-ENUMERATION-PARAM-DEF UUID="7f0c87ef-e801-4625-b9a5-3c25a1e726d6">
											<SHORT-NAME>SoAdTxUpperLayerType</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Upper Layer Type</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Specifies the upper layer interface type (must be "IF" in case of multiple PduRoutes).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>IF</DEFAULT-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="6d278683-1c06-4a75-a33f-55c3554ed76c">
													<SHORT-NAME>IF</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="2160864e-f302-437e-be45-0ff415024e17">
													<SHORT-NAME>TP</SHORT-NAME>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="9a5e32ea-9a84-46a5-a032-e7f7c204b738">
											<SHORT-NAME>SoAdTxIfTriggerTransmit</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">If Trigger Transmit</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines if this PDU is transmitted via TriggerTransmit API.
true: On transmission current value is retrieved via call to TriggerTransmit API of upper layer.
false: Transmission is performed in context of transmit service.

In context of nPduUdpTxBuffer this behavior can be used to optimize the nPduUdpTxBuffer and replace it by a request queue.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="95e35cc7-1656-477c-962f-b3c07c1b5895">
											<SHORT-NAME>SoAdTxTpOptimized</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Tp Optimized</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines if optimization for TP transmission shall be used for this PDU.
true: SoAd handles transmission in context of SoAd_TpTransmit i.e. TpCopyTxData and TpTxConfirmation of upper layer may be called in context of SoAd_TpTransmit.
false: SoAd stores transmission request in SoAd_TpTransmit and handles transmission in SoAd_MainFunction (accroding to AUTOSAR).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="085f4e65-272c-4d17-8f50-7fbce413cdfd">
											<SHORT-NAME>SoAdTxIfOptimized</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">If Optimized</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines if optimization for IF transmission shall be used for this PDU.
true: SoAd handles the transmission in context of SoAd_IfTransmit. This means Ul_[SoAd][If]TxConfirmation and Ul_[SoAd][If]TriggerTransmit of upper layer may be called in context of SoAd_IfTransmit.
false: SoAd stores and forwards transmission requests according to AUTOSAR</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="80ce2ab9-3960-442d-8c4a-401300b4e945">
											<SHORT-NAME>SoAdTxIfTriggerTransmitForceSingleCall</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines if the Ul_[SoAd][If]TriggerTransmit API is forced to be called only once. In case of a PDU fan-out, SoAd calls Ul_[SoAd][If]TriggerTransmit for each SoAdSocketConnection, otherwise. If this parameter is enabled, a SoAdIfTriggerTransmitBuffer is used to store the PDU data retrieved by Ul_[SoAd][If]TriggerTransmit before initiating the PDU fan-out.
true: SoAd calls Ul_[SoAd][If]TriggerTransmit only once.
false: SoAd calls Ul_[SoAd][If]TriggerTransmit for each SoAdSocketConnection.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Reference Definition: SoAdTxPduRef -->
										<ECUC-REFERENCE-DEF UUID="58e34d01-**************-ac06bf2fb611">
											<SHORT-NAME>SoAdTxPduRef</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">PDU</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Reference to the global PDU structure</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<!-- Container Definition: SoAdPduRouteDest -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="227a3664-04ef-4b56-b368-68313d1a7c33">
											<SHORT-NAME>SoAdPduRouteDest</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the PDU route destination.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: SoAdTxPduHeaderId -->
												<ECUC-INTEGER-PARAM-DEF UUID="77d256f2-52ae-4af6-ae7a-4710b7769f6f">
													<SHORT-NAME>SoAdTxPduHeaderId</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">PDU Header ID</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">ID to be sent on the TCP/IP connection if the PDU header option is enabled.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>4294967296</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: SoAdTxUdpTriggerMode -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="348cdb69-8b4e-46cf-b576-8e03b6195e3f">
													<SHORT-NAME>SoAdTxUdpTriggerMode</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">UDP Trigger Mode</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Specifies whether a PDU triggers the transmission of the nPduUdpTxBuffer. NPduUdpTxBuffer can only be used for upper layers with "IF" API, i.e. this parameter shall only be set if all upper layers belonging to the related socket connection have SoAdTxUpperLayerType set to "IF".
This parameter is only relevant for UDP connections.

TRIGGER_ALWAYS: PDU transmission triggers transmission of nPduUdpTxBuffer
TRIGGER_NEVER: PDU transmission is stored in nPduUdpTxBuffer and triggers nPduUdpTxBuffer transmission only if buffer size is not sufficient</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>TRIGGER_NEVER</DEFAULT-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="f93624e2-6ac1-4696-af5c-6b6b265e31e3">
															<SHORT-NAME>TRIGGER_ALWAYS</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="a92b3c3a-3628-46c5-827a-d34e1f57fa98">
															<SHORT-NAME>TRIGGER_NEVER</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: SoAdTxUdpTriggerTimeout -->
												<ECUC-FLOAT-PARAM-DEF UUID="ed4eaee5-16cd-4499-9f41-a4c07fae0cf6">
													<SHORT-NAME>SoAdTxUdpTriggerTimeout</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">UDP Trigger Timeout</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Specifies the timeout in [s] the nPduUdpTxBuffer shall be transmitted at the latest after this PDU is put into the buffer. This optional parameter is only relevant if SoAdTxUdpTriggerMode is TRIGGER_NEVER.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>2</DEFAULT-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: SoAdTxSocketConnOrSocketConnBundleRef -->
												<ECUC-CHOICE-REFERENCE-DEF UUID="7981eea4-847c-4482-a3de-b1f2ef522429">
													<SHORT-NAME>SoAdTxSocketConnOrSocketConnBundleRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Choice Reference to a SocketConnection or to a SocketConnectionGroup on which the PDU is to be sent on. The reference to a SocketConnectionGroup shall only be used for upper layers with IF API.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REFS>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/SoAd/SoAdConfig/SoAdSocketConnectionGroup/SoAdSocketConnection</DESTINATION-REF>
														<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/SoAd/SoAdConfig/SoAdSocketConnectionGroup</DESTINATION-REF>
													</DESTINATION-REFS>
												</ECUC-CHOICE-REFERENCE-DEF>
												<!-- Reference Definition: SoAdTxRoutingGroupRef -->
												<ECUC-REFERENCE-DEF UUID="3dd79e17-4c01-4548-9760-da96eddba7bb">
													<SHORT-NAME>SoAdTxRoutingGroupRef</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Routing Group</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Reference to the routing group.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/SoAd/SoAdConfig/SoAdRoutingGroup</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: SoAdRoutingGroup -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="88f63feb-72a8-400b-818b-2c686daae84e">
									<SHORT-NAME>SoAdRoutingGroup</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Each container describes a specific routing group which can be enabled or disabled. A routing group consists of PDUs. Routing of PDUs can either be forwarding of PDUs from the upper layer to a TCP or UDP socket of the TCP/IP stack specified by a SoAdPduRoute or the other way around specified by a SoAdSocketRoute.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: SoAdRoutingGroupId -->
										<ECUC-INTEGER-PARAM-DEF UUID="c8757cea-4c2f-49f9-8b89-e4a43a07a1f2">
											<SHORT-NAME>SoAdRoutingGroupId</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Routing Group ID</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Unique ID of Routing Group</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SoAdRoutingGroupIsEnabledAtInit -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="1e212995-c561-4b62-a174-6ea8f2a83669">
											<SHORT-NAME>SoAdRoutingGroupIsEnabledAtInit</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Enable at Initialization</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">If set to true this routing group will be enabled after initializing the SoAd module (i.e. enabled in the SoAd_Init function).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SoAdRoutingGroupTxTriggerable -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="d494337b-9309-4d2d-93fb-13707aa7192c">
											<SHORT-NAME>SoAdRoutingGroupTxTriggerable</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Enable Tx Triggerable</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Specifies if the If-TxPDUs related to the PduRouteDest containers</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">referenced by this routing group can be triggered via
                                                SoAd_IfRoutingGroupTransmit (TRUE) or not (FALSE).</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: SoAdSocketConnectionGroup -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="88c5e842-9c21-4a26-a037-306f08c46e6b">
									<SHORT-NAME>SoAdSocketConnectionGroup</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Specifies the configuration of a socket connection group, i.e. specifies the socket connections belonging to the group and the parameters which are common for all socket connections of the group. A socket connection specifies how data can be received and transmited via a TCP or UDP socket.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: SoAdPduHeaderEnable -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="5fa007e2-b392-4300-9ad9-5d3192f2eec4">
											<SHORT-NAME>SoAdPduHeaderEnable</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Enable PDU Header</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enables the transmission of the PDU header (ID, length) on this socket connection.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">TRUE: add SoAd PDU header before PDU data 
                                                FALSE: No SoAd PDU header is used</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SoAdResourceManagementEnable -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="6006c17d-a245-4506-bbe0-1446e0fefc06">
											<SHORT-NAME>SoAdResourceManagementEnable</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Enable Resource Management</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enables the resource management option for this socket.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">May not be activated for UDP sockets in receive.

                                                TRUE: resource management option enabled
                                                FALSE: resource management option disabled</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SoAdSocketAutomaticSoConSetup -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="49e18d91-1bb4-4344-a0f1-3498a69c9084">
											<SHORT-NAME>SoAdSocketAutomaticSoConSetup</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Enable Automatic Socket Connection Setup</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Specifies if the setup of the socket connection shall be done automatically (TRUE) or manually (FALSE) via SoAd_OpenSoCon() and SoAd_CloseSoCon().</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SoAdSocketFramePriority -->
										<ECUC-INTEGER-PARAM-DEF UUID="0a8e75aa-98c4-4022-bbdb-d1247a0bb3bf">
											<SHORT-NAME>SoAdSocketFramePriority</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Ethernet Frame Priority</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Specifies the priority of the Ethernet frame.</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">If IEEE 802.1Q VLAN Tags are used, the specified priority will be used in the VLAN Tag PCP filed. If this optional parameter is not available the default priority specified in the TcpIp module is used.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>7</DEFAULT-VALUE>
											<MAX>7</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SoAdSocketIpAddrAssignmentChgNotification -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="97182fe0-9616-45ea-8c5c-01c2c95f0c3b">
											<SHORT-NAME>SoAdSocketIpAddrAssignmentChgNotification</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Enable IP Address Assignment Change Notification</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Specifies if the local IP address assignment change notification callback function of the upper layer shall be called if the assignment of the local IP address used by this socket connection changes.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SoAdSocketLocalPort -->
										<ECUC-INTEGER-PARAM-DEF UUID="10fa2471-0d25-45de-837c-dc4cf8515064">
											<SHORT-NAME>SoAdSocketLocalPort</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Local Port</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Local UDP or TCP port used for this connection.If this parameter set to 0 SoAd requests TcpIp to select an ephemeral port.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SoAdSocketMsgAcceptanceFilterEnabled -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="eaf1dfea-0497-4c25-8f39-fd1e0b88f6cb">
											<SHORT-NAME>SoAdSocketMsgAcceptanceFilterEnabled</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Enable Message Acceptance Filter</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Specifies if the message acceptance filter is enabled (TRUE) or not (FALSE).</L-2>
											</DESC>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Note: if a wildcard is used in SoAdSocketRemoteAddress AND SoAdSocketUdpListenOnly is FALSE, this parameter must be TRUE.
                                                Note: if multiple SoAdSocketConnections are configured for one SoAdSocketConnectionGroup, this parameter must be TRUE.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SoAdSocketSoConModeChgNotification -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="31bbf9a1-d895-4900-b50f-80fcb5aec4b5">
											<SHORT-NAME>SoAdSocketSoConModeChgNotification</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Enable Socket Connection Mode Change Notification</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Specifies if the SoCon mode change notification callback function of the upper layer shall be called in case of SoCon mode change.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: SoAdSocketTpRxBufferMin -->
										<ECUC-INTEGER-PARAM-DEF UUID="26bec412-742f-49e3-b833-515dcdd7ad14">
											<SHORT-NAME>SoAdSocketTpRxBufferMin</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Minimum TP Rx Buffer</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Specifies the amout of data in bytes (PDU data for the upper layer and PDU Header if used) the SoAd shall at least be able to buffer for data reception via each socket connection of the socket connection group and using an upper layer with TP.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">BYTE</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Note: in case of a TCP socket where PduHeaderMode is used and an upper layer with IF-API, the required buffer size can be determined automatically.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>512</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="f82012a4-1978-4f87-9c45-62403f788a84">
											<SHORT-NAME>SoAdBestMatchWithSockRouteEnabled</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Enable Best Match Algorithm with SocketRoute validation</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">If this feature is enabled the best match algorithm for this socket connection group considers that a socket route is configured for a socket connection.
This means: The best match algortihm will not choose a socket connection which has no receiver even if the remote address matches.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="3ce92643-63cb-4ebe-9880-b104ead0de5f">
											<SHORT-NAME>SoAdBestMatchWithPduHeaderEnabled</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Enable Best Match Algorithm with PDU Header validation</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">If this feature is enabled the best match algorithm of this socket connection group considers the received PDU Header when choosing a socket connection. A received PDU is discarded if no socket route could be found containing the corresponding PDU Header ID.
</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Symbolic Name Reference Definition: SoAdSocketLocalAddressRef -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="8833ffa4-b27a-4962-896d-f522bd1039f3">
											<SHORT-NAME>SoAdSocketLocalAddressRef</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Local Address Reference</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Local IP address and interface used for this connection.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/TcpIp/TcpIpConfig/TcpIpLocalAddr</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<!-- Container Definition: SoAdSocketConnection -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="7d77d0d1-ef5e-4898-81ee-7296f9474dd9">
											<SHORT-NAME>SoAdSocketConnection</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the socket connection (Id and remote address information).</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Note: Parameters which are common to all socket connections of a socket connection group are specified directly at the group.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: SoAdSocketId -->
												<ECUC-INTEGER-PARAM-DEF UUID="846b9a8d-11d9-4e1d-9404-5654dc6f2e74">
													<SHORT-NAME>SoAdSocketId</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Socket Connection ID</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">The Socket ID is used as a reference to a particular connection when transferring data to and from the PDU Router.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-BOOLEAN-PARAM-DEF UUID="9ae969e9-225d-4a3e-b614-9c36f434853d">
													<SHORT-NAME>SoAdSocketGetReceivedRemoteAddressEnabled</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines the ability to store the remote IP address of the last received data on the corresponding Socket Connection.

The remote IP address can be retrived by the upper layer by calling the SoAd_GetRcvRemoteAddr() API with the corresponding Socket Connection ID as function parameter.

false:    Remote IP address of last received data on Socket Connection isn't stored
true:     Remote IP address of last received data on Socket Connection is stored and can be retrieved by calling the SoAd_GetRcvRemoteAddr() API</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="3fc39180-29b7-48d8-979c-9ff1da354c6a">
													<SHORT-NAME>SoAdTcpTxQueueSize</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines the size of a TxQueue to support parallel transmission requests on TCP and a streaming-based TxConfirmation.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>1</DEFAULT-VALUE>
													<MAX>65535</MAX>
													<MIN>1</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-BOOLEAN-PARAM-DEF UUID="bec3febc-35c5-40a8-97ff-b5eb837dbe9b">
													<SHORT-NAME>SoAdVerifyRxPduEnable</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter enables the usage of the Rx PDU verification callback on this socket connection.
The corresponding callback is configured by parameter SoAdVerifyRxPduCallback.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>false</DEFAULT-VALUE>
												</ECUC-BOOLEAN-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<ECUC-REFERENCE-DEF UUID="b55c6185-35e6-45c1-beca-9c52edaa0a0f">
													<SHORT-NAME>SoAdAdditionalChgNotificationRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to an additional SoAdBswModule which shall be registered to get change notifications (SoConModeChg, IpAddrAssignmentChg) for this socket connection.
Which notification is called depends on specific SoAdBswModule configuration (parameter SoAdLocalIpAddrAssigmentChg and SoAdSoConModeChg).
SoAdBswModules which are already registered via SoAdPduRouteDest or SoAdSocketRoute must not be added here.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/SoAd/SoAdBswModules</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<ECUC-REFERENCE-DEF UUID="94392887-aaff-49f3-b669-2040e794f9ed">
													<SHORT-NAME>SoAdAdditionalSoConModeChgRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines a reference to an additional callback to notify any module about a socket connection state change on this socket connection.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/SoAd/SoAdGeneral/SoAdAdditionalSoConModeChgCallback</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<ECUC-REFERENCE-DEF UUID="479cc7d8-b6cf-455d-b9f0-892616923b63">
													<SHORT-NAME>SoAdAdditionalLocalIpAddrAssignmentChgRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines a reference to an additional callback to notify any module about a local IP address assignment change on this socket connection.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/SoAd/SoAdGeneral/SoAdAdditionalLocalIpAddrAssignmentChgCallback</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
											<SUB-CONTAINERS>
												<!-- Container Definition: SoAdSocketRemoteAddress -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="28b40b24-0258-48a3-a936-7c1a76e253f2">
													<SHORT-NAME>SoAdSocketRemoteAddress</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Subcontainer of SoAdSocketConnection to specify the remote address (IP address and port) for a socket connection.
If remote address contains wildcards socket connection cannot be openned (set to online). The remote address has to be overwritten at runtime via API call, on UDP frame reception or via TCP connection establishment.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<INTRODUCTION>
														<P>
															<L-1 L="EN">If SoAdSocketRemoteAddress is not specified the remote address has to be set by the upper layer via SoAd_SetRemoteAddr().</L-1>
														</P>
													</INTRODUCTION>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: SoAdSocketRemoteIpAddress -->
														<ECUC-STRING-PARAM-DEF UUID="12785fe4-4bc8-4923-bc2d-498de62320d6">
															<SHORT-NAME>SoAdSocketRemoteIpAddress</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Remote IP Address</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines the IP address of remote node.
To accept any remote IP address, leave this parameter empty. See message acceptance policy for more details.
If this parameter is empty (wildcard) socket connection cannot be openned (set to online). The remote address has to be overwritten at runtime via API call, on UDP frame reception or via TCP connection establishment.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
														</ECUC-STRING-PARAM-DEF>
														<!-- PARAMETER DEFINITION: SoAdSocketRemotePort -->
														<ECUC-INTEGER-PARAM-DEF UUID="af06bc0c-39be-483d-9355-d635cf012b3a">
															<SHORT-NAME>SoAdSocketRemotePort</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Remote Port</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines the UDP or TCP port of remote node.
To accept any remote port, set SoAdSocketRemotePort to 0. See message acceptance policy for more details.
If this parameter is set to 0 socket connection cannot be openned (set to online). The remote address has to be overwritten at runtime via API call, on UDP frame reception or via TCP connection establishment.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>0</DEFAULT-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<ECUC-BOOLEAN-PARAM-DEF UUID="015c49f3-23fb-4c5c-992a-a63f5ccdbe78">
															<SHORT-NAME>SoAdSocketRemoteIpAddressNotSet</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Remote IP Address not set</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines whether remote IP address is set (valid or wildcard) or not set.

True: IP address not set,
False: IP address set (valid/wildcard).</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<ECUC-BOOLEAN-PARAM-DEF UUID="edde5477-e4e5-4eab-b62c-bc3adac91560">
															<SHORT-NAME>SoAdSocketRemotePortNotSet</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Remote Port not set</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines whether remote port is set (valid or wildcard) or not set.

True: port not set,
False: port set (valid/wildcard).</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="f7307c0d-147c-4737-b5af-7bea48da77d0">
													<SHORT-NAME>SoAdTcpTlsConfig</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container contains the TLS configuration for a socket connection. TLS is supported on TCP client sockets only (SoAdSocketTcp/TcpInitiate = TRUE).
If this container does not exist TLS is disabled on this socket connection.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
													<PARAMETERS>
														<ECUC-INTEGER-PARAM-DEF UUID="dd891078-94ac-4e34-8626-81d77919a13e">
															<SHORT-NAME>SoAdTcpTlsTxBufferSize</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines the transmission buffer size for Tls. The value of this parameter is forwarded to TcpIp and Tls on socket creation at runtime.</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:Display">
																		<SD GID="DV:BaseUnit">BYTE</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>512</DEFAULT-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<ECUC-INTEGER-PARAM-DEF UUID="8ea04b2c-bfa5-41bf-9300-9821fe8d03f5">
															<SHORT-NAME>SoAdTcpTlsRxBufferSize</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines the reception buffer size for Tls. The value of this parameter is forwarded to TcpIp and Tls on socket creation at runtime.</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:Display">
																		<SD GID="DV:BaseUnit">BYTE</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>512</DEFAULT-VALUE>
															<MAX>4294967295</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
													<REFERENCES>
														<ECUC-REFERENCE-DEF UUID="307deac8-3cae-43dd-adaa-5a69282f3820">
															<SHORT-NAME>SoAdTcpTlsCallbackRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines a reference to a callback container to notify a user about a socket connection specific TLS event.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/SoAd/SoAdGeneral/SoAdTcpTlsCallback</DESTINATION-REF>
														</ECUC-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Container Definition: SoAdSocketProtocol -->
										<ECUC-CHOICE-CONTAINER-DEF UUID="16b75c1a-d5f9-4eb6-8408-d50cc5051fe0">
											<SHORT-NAME>SoAdSocketProtocol</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Specifies the transport protocol and transport protocol specific parameters used for the socket connections of the socket connection group.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
											<CHOICES>
												<!-- Container Definition: SoAdSocketTcp -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="b251c8bd-d43c-4a54-9fed-cc0e7d1e8515">
													<SHORT-NAME>SoAdSocketTcp</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Specifies that TCP is used as transport protocol for the socket connection group and parameters only related to TCP socket connections.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: SoAdSocketTcpImmediateTpTxConfirmation -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="33fcf06a-e3e5-4a98-a79a-e4715844ce4d">
															<SHORT-NAME>SoAdSocketTcpImmediateTpTxConfirmation</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Enable Immediate Tx Confirmation</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">If set to FALSE, SoAd notifies the TP upper layer via transmit confirmation after a Tcp Ack has been received. If set to TRUE, SoAd notifies the TP upper layer via transmit confirmation immediately after transmit has been accepted by TcpIp.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: SoAdSocketTcpInitiate -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="a9f6d4f6-c2d0-49fa-aa6c-10c7ccb0abda">
															<SHORT-NAME>SoAdSocketTcpInitiate</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Initiate TCP Connection</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">Specifies the initiator for this TCP connection. It will not be defined for UDP sockets.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">TRUE: This TCP connection is initiated by this module. 
                                                                FALSE: This TCP connection is to be initiated in the listen mode.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: SoAdSocketTcpKeepAlive -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="df38486b-4a1d-4e45-a3ef-02e53bd396ce">
															<SHORT-NAME>SoAdSocketTcpKeepAlive</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Enable Keep Alive</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">Specifies to use the keep-alive mechanism for this connection. It will not be 
defined for UDP sockets. 
TRUE:      This TCP connection will use the keep-alive mechanism. 
FALSE:     This TCP connection will not use the keep-alive mechanism. 

Note: This parameter must not be set to TRUE if TcpIpTcpKeepAliveEnabled is set to 
FALSE.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: SoAdSocketTcpKeepAliveInterval -->
														<ECUC-FLOAT-PARAM-DEF UUID="bc73148d-b959-4110-8cb3-ed1b8e730bea">
															<SHORT-NAME>SoAdSocketTcpKeepAliveInterval</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Keep Alive Interval</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">Specifies the interval in seconds between subsequent keepalive probes.</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:Display">
																		<SD GID="DV:BaseUnit">SEC</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>1</DEFAULT-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-FLOAT-PARAM-DEF>
														<!-- PARAMETER DEFINITION: SoAdSocketTcpKeepAliveProbesMax -->
														<ECUC-INTEGER-PARAM-DEF UUID="401eceb8-daf0-4be5-a4b2-20eee480dca5">
															<SHORT-NAME>SoAdSocketTcpKeepAliveProbesMax</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Maximum Keep Alive Probes</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">Maximum number of times that TCP retransmits an individual data 
segment before aborting the connection.</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:Display">
																		<SD GID="DV:DefaultFormat">DEC</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>1</DEFAULT-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: SoAdSocketTcpKeepAliveTime -->
														<ECUC-FLOAT-PARAM-DEF UUID="820713e2-429f-417c-a9ac-652d87ca66f1">
															<SHORT-NAME>SoAdSocketTcpKeepAliveTime</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Keep Alive Time</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">Specifies the time in seconds between the last data packet sent and the 
first keepalive probe.</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:Display">
																		<SD GID="DV:BaseUnit">SEC</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>7200</DEFAULT-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-FLOAT-PARAM-DEF>
														<!-- PARAMETER DEFINITION: SoAdSocketTcpNoDelay -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="4ff7b491-2d21-4342-9590-bb020e8b81fd">
															<SHORT-NAME>SoAdSocketTcpNoDelay</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Disable TCP Congestion Control</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">Specifies not to use the congestion control mechanism for this connection. It will not be defined for UDP sockets.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">TRUE: This TCP connection will NOT use congestion control.
                                                                FALSE: This TCP connection will use congestion control.  
                                                                If the optional parameter is not enabled, the default behavior configured for TcpIp via the parameter TcpIpTcpNagleEnabled is applied.
                                                                Note: This parameter must not be set to FALSE if TcpIpTcpNagleEnabled is set to FALSE.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: SoAdSocketTcpTxQuota -->
														<ECUC-INTEGER-PARAM-DEF UUID="4392f2a1-0cf8-4a3e-96d5-ad31f7e7d42f">
															<SHORT-NAME>SoAdSocketTcpTxQuota</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Transmission Quota</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">Specifies the maximum amout of bytes (PDU data provided by the upper layer and PDU Header if used) the SoAd may queue for transmission via TCP at the TcpIp module for each socket connection of this socket connection group.

(not used)</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:Display">
																		<SD GID="DV:BaseUnit">BYTE</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">Rationale: prohibits that a socket connection consumes all available transmit buffers at the TcpIp and blocks transmissions via other socket connections.
                                                                If the optional parameter is not enabled, the amout of data is not limited.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>4294967295</DEFAULT-VALUE>
															<MAX>4294967295</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<ECUC-INTEGER-PARAM-DEF UUID="de7fc095-e7c1-4d46-8a6d-c8415ef4f066">
															<SHORT-NAME>SoAdSocketTcpTxBufferMin</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Minimum Tx Buffer</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines a minimum transmission buffer size the TcpIp must be able to provide on each socket connection of this group. The value is forwarded to TcpIp on socket creation at runtime. If this optional parameter is not configured TcpIp will use a default value.</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:Display">
																		<SD GID="DV:BaseUnit">BYTE</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>512</DEFAULT-VALUE>
															<MAX>65535</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
												<!-- Container Definition: SoAdSocketUdp -->
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="78d1300d-b663-4bbb-ab36-90f1a2fab335">
													<SHORT-NAME>SoAdSocketUdp</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Specifies that UDP is used as transport protocol for the socket connection group and parameters only related to UDP socket connections.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<PARAMETERS>
														<!-- PARAMETER DEFINITION: SoAdSocketUdpAliveSupervisionTimeout -->
														<ECUC-FLOAT-PARAM-DEF UUID="ee9cea99-da8e-406d-bc2f-724a1f513b16">
															<SHORT-NAME>SoAdSocketUdpAliveSupervisionTimeout</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Alive Supervision Timeout</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">Specifies the time in [s] a UDP socket connection remains in the mode SOAD_SOCON_ONLINE after the latest reception of a frame from the remote peer specified by the remote address. If this optional parameter is not enabled UDP Alive Supervision is deactivated for the related socket connection group.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>1</DEFAULT-VALUE>
															<MAX>320</MAX>
															<MIN>0</MIN>
														</ECUC-FLOAT-PARAM-DEF>
														<!-- PARAMETER DEFINITION: SoAdSocketUdpListenOnly -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="71152800-71f8-4175-86d6-11f9c61564ae">
															<SHORT-NAME>SoAdSocketUdpListenOnly</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Enable Listen Only</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">Specifies if the socket connection group is only used for reception (TRUE) or used for both reception and transmission (FALSE).</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: SoAdSocketUdpStrictHeaderLenCheckEnabled -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="72d25a79-49e9-4444-907d-7b6b8bf85614">
															<SHORT-NAME>SoAdSocketUdpStrictHeaderLenCheckEnabled</SHORT-NAME>
															<DESC>
																<L-2 L="EN">Specifies if UDP messages shall be dropped (TRUE) if the length of all contained PDUs does not match the length of the whole message or not (FALSE). Shall only be set to TRUE if SoAdPduHeaderEnable is also set to TRUE.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: SoAdSocketUdpTriggerTimeout -->
														<ECUC-FLOAT-PARAM-DEF UUID="6033cc52-fffa-433b-a5d5-74879ea83812">
															<SHORT-NAME>SoAdSocketUdpTriggerTimeout</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Timeout for Trigger Transmission</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">Specifies the timeout in [s] a nPduUdpTxBuffer is waiting for a PDU with TriggerMode = TRIGGER_ALWAYS, i.e. when the timeout expires the nPduUdpTxBuffer is transmitted. Timer is reset after each UDP transmission. This optional parameter is only relevant if a nPduUdpTxBuffer is used.</L-2>
															</DESC>
															<ADMIN-DATA>
																<SDGS>
																	<SDG GID="DV:Display">
																		<SD GID="DV:BaseUnit">SEC</SD>
																	</SDG>
																</SDGS>
															</ADMIN-DATA>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>2</DEFAULT-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-FLOAT-PARAM-DEF>
														<!-- PARAMETER DEFINITION: SoAdSocketnPduUdpTxBufferMin -->
														<ECUC-INTEGER-PARAM-DEF UUID="d9d68db9-6563-465e-affa-d4d42a2e5beb">
															<SHORT-NAME>SoAdSocketnPduUdpTxBufferMin</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Socket n Pdu Udp Tx Buffer Min</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">Specifies the amout of data in bytes (PDU data provided by the upper layer and PDU Header if used) the SoAd shall be able to buffer for data transmission via this socket connection in case the UDP message shall be buffered for transmission of multiple PDUs per UDP (nPduUdpTxBuffer).

This parameter must not exist if no nPduUdpTxBuffer is used.</L-2>
															</DESC>
															<INTRODUCTION>
																<P>
																	<L-1 L="EN">Note: in case of a UDP socket and an upper layer with TP API or an upper layer with IF API with UDP transmit retry (for single PDUs) configured, the required buffer size can be determined automatically. This optional parameter is only relevant if a nPduUdpTxBuffer is used.</L-1>
																</P>
															</INTRODUCTION>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>32</DEFAULT-VALUE>
															<MAX>65535</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<!-- PARAMETER DEFINITION: SoAdSocketUdpAddressResolutionRetryEnabled -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="8d4c918f-c16f-45da-a583-336c1843ed67">
															<SHORT-NAME>SoAdSocketUdpAddressResolutionRetryEnabled</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Enable Address Resolution Retry</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">Specifies if a transmission request shall be stored in TcpIp module to retry transmission in case of missing address resolution (ARP/NDP).
True: Retry enabled
False: Retry disabled</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>true</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<!-- PARAMETER DEFINITION: SoAdSocketUdpImmediateIfTxConfirmation -->
														<ECUC-BOOLEAN-PARAM-DEF UUID="0d7bf58c-c61b-484c-ab23-2011df725c2c">
															<SHORT-NAME>SoAdSocketUdpImmediateIfTxConfirmation</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines the ability to notify the IF upper layers via TX Confirmation in context of Ethernet driver's TX Confirmation.

All upper layers using a Socket Connection of a Socket Connection Group configured for UDP communication with this feature enabled will be notified by the TX Confirmation in context of Ethernet driver's TX Confirmation.

false:    Upper Layers are notified by the TX Confirmation called in next main function cycle of SoAd (Task-Context). The call is independent from the TX Confirmation by the lower layer of SoAd.
true:    Upper Layers are notified by the TX Confirmation propagated through the Ethernet Stack starting by the Ethernet Driver (Task- or Interrupt-Context).</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>false</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
														<ECUC-INTEGER-PARAM-DEF UUID="92a6c3a3-0c04-43d6-836b-2cb473de4958">
															<SHORT-NAME>SoAdSocketUdpImmediateIfTxConfirmationListSize</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter specifies the size of a list which is used in the TcpIp module to handle UDP transmissions with TxConfirmation. 
Please refer to TcpIp parameter "TcpIpUdpTxReqListSize" for more details.

This parameter is used if "SoAdSocketUdpImmediateIfTxConfirmation" is enabled.
</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>1</DEFAULT-VALUE>
															<MAX>255</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<ECUC-INTEGER-PARAM-DEF UUID="e23edd63-9e0d-4cff-99be-595f7877da43">
															<SHORT-NAME>SoAdSocketnPduUdpTxQueueSize</SHORT-NAME>
															<LONG-NAME>
																<L-4 L="EN">Socket n Pdu Udp Tx Queue Size</L-4>
															</LONG-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines the size of a management queue for each socket connection. This queue is used to manage PDUs in the nPduUdpTxBuffer and PDU transmission requests for trigger transmit. Both transmission types are stored within one queue, so make sure that size is sufficient for all related SoAdPduRoutes.

This parameter must not exist if no related SoAdPduRoute exisits with enabled SoAdTxIfTriggerTransmit.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>2</DEFAULT-VALUE>
															<MAX>255</MAX>
															<MIN>1</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<ECUC-INTEGER-PARAM-DEF UUID="6221fe99-556f-4b3d-b9b1-c1e406268e82">
															<SHORT-NAME>SoAdSocketUdpAddressResolutionRetryQueueLimit</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter sets the limitation of maximum used UDP transmission retry queue elements for this socket. 
The value is forwarded to the TcpIp module on socket creation.
Example: A transmission over a UDP socket fails in TcpIp module since address resolution (ARP) is not finished yet. This transmission is stored in the retry queue and the transmission request is assumed as successful. If the limitation is set to 1 TcpIp will reject further transmission requests until retry queue element is released.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>1</DEFAULT-VALUE>
															<MAX>255</MAX>
															<MIN>0</MIN>
														</ECUC-INTEGER-PARAM-DEF>
														<ECUC-BOOLEAN-PARAM-DEF UUID="04b2ba99-08fd-40f5-90ea-19bb116f2ef9">
															<SHORT-NAME>SoAdSocketUdpBroadcastReceptionEnabled</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This parameter defines if the reception of broadcasts (IPv4 limited and directed broadcast) and multicasts (IPv6 all nodes and all routers multicast) is enabled. 
The parameter must exist if SoAdSocketApiType is LINUX or QNX and if SoAdSocketLocalAddressRef references a unicast address.
Otherwise, this parameter must not exist.
True: Broadcasts and multicasts shall be received.
False: Broadcasts and multicasts shall not be received.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>Vector Informatik</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
															<DEFAULT-VALUE>true</DEFAULT-VALUE>
														</ECUC-BOOLEAN-PARAM-DEF>
													</PARAMETERS>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</CHOICES>
										</ECUC-CHOICE-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Container Definition: SoAdSocketRoute -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="d3ea7ccb-83e6-486f-b6b4-b2ff9635ac39">
									<SHORT-NAME>SoAdSocketRoute</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Describes the path of a PDU from a socket in the TCP/IP stack to an upper layer of the SoAd after reception in the TCP/IP Stack.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: SoAdRxPduHeaderId -->
										<ECUC-INTEGER-PARAM-DEF UUID="bddb9d21-5c2b-4198-8b09-f2b4d58f01dc">
											<SHORT-NAME>SoAdRxPduHeaderId</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">PDU Header ID</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">ID contained in the packet received on the TCP/IP connection if the PDU header option is enabled.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>4294967296</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Reference Definition: SoAdRxSocketConnectionRef -->
										<ECUC-CHOICE-REFERENCE-DEF UUID="5dbb92a5-7cb8-40e5-93be-e6b1251d4a78">
											<SHORT-NAME>SoAdRxSocketConnOrSocketConnBundleRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Choice Reference to a SocketConnection or to a SocketConnectionGroup on which the PDU was received.  The reference to a SocketConnectionGroup shall only be used for upper layers with IF API.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REFS>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/SoAd/SoAdConfig/SoAdSocketConnectionGroup/SoAdSocketConnection</DESTINATION-REF>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/SoAd/SoAdConfig/SoAdSocketConnectionGroup</DESTINATION-REF>
											</DESTINATION-REFS>
										</ECUC-CHOICE-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<!-- Container Definition: SoAdSocketRouteDest -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="7a21c944-a675-43c1-8615-b604f6096676">
											<SHORT-NAME>SoAdSocketRouteDest</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Describes the upper layer destination PDU for a message received on a TcpIp socket.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: SoAdRxPduId -->
												<ECUC-INTEGER-PARAM-DEF UUID="12859264-e953-4156-a2f8-1db021dc7198">
													<SHORT-NAME>SoAdRxPduId</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">PDU ID</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">This unique identifier is used for a receive cancellation request from an upper layer of the SoAd.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: SoAdRxUpperLayerType -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="c096319f-c53c-4eaf-884c-a94f7f683d6f">
													<SHORT-NAME>SoAdRxUpperLayerType</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Upper Layer Type</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Specifies the upper layer interface type (must be "IF" in case of multiple RxPdus).</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>IF</DEFAULT-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="e1ec7cec-17fb-4c96-9c2c-8757232b2414">
															<SHORT-NAME>IF</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="59adbcdd-ceaa-4e53-89f7-504526d212d2">
															<SHORT-NAME>TP</SHORT-NAME>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Reference Definition: SoAdRxPduRef -->
												<ECUC-REFERENCE-DEF UUID="0fe04701-7ea5-4e09-a838-6d93624c1f0f">
													<SHORT-NAME>SoAdRxPduRef</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">PDU</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Reference to the global PDU structure</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPduCollection/Pdu</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
												<!-- Reference Definition: SoAdRxRoutingGroupRef -->
												<ECUC-REFERENCE-DEF UUID="3472d35f-f773-425b-9fc3-63139142859e">
													<SHORT-NAME>SoAdRxRoutingGroupRef</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">Routing Group</L-4>
													</LONG-NAME>
													<DESC>
														<L-2 L="EN">Reference to the routing group.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/SoAd/SoAdConfig/SoAdRoutingGroup</DESTINATION-REF>
												</ECUC-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="191e85a2-3ab4-4b55-8843-9ffe15cd5020">
									<SHORT-NAME>SoAdIpConfig</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container provides IP related configuration parameter.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
									<SUB-CONTAINERS>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="c03f63f9-2cb0-49e8-a741-e004ecad4b2d">
											<SHORT-NAME>SoAdIpFragmentationBuffer</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container provides parameter to configure an IP fragmentation buffer.

An IP fragmentation buffer is used when a SoAdPduRoute configures SoAdTxIfTriggerTransmit to true and no nPduUdpTxBuffer is used on a UDP socket. In this case a buffer is required since TcpIp copies the PDU fragmented (multiple SoAd API calls) but TriggerTransmit API allows only one call.

The IP fragmentation buffers are shared by all socket connections. A transmission request fails when no buffer is available at the moment.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>65535</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
											<PARAMETERS>
												<ECUC-INTEGER-PARAM-DEF UUID="e2a5e4b3-4d12-4ae0-9c89-84eaffb32bdc">
													<SHORT-NAME>SoAdIpFragmentationBufferSize</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines the size of the IP fragmentation buffer.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">BYTE</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>3000</DEFAULT-VALUE>
													<MAX>65535</MAX>
													<MIN>1</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="a44fbca6-2c78-43aa-b824-bfb54c61caaa">
									<SHORT-NAME>SoAdIfTriggerTransmitConfig</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container provides trigger transmit related configuration parameter.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
									<SUB-CONTAINERS>
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="c638c30c-66c5-4384-ae02-72c3da05bc05">
											<SHORT-NAME>SoAdIfTriggerTransmitBuffer</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container provides parameter to configure a trigger transmit buffer.
A trigger transmit buffer is used whenever PDU data has to be stored in SoAd before transmission is possible and SoAdTxIfTriggerTransmit is enabled. This applies for example if SoAdTxIfTriggerTransmitForceSingleCall is enabled for a PDU fan-out or for transmissions via TCP.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>65535</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
											<PARAMETERS>
												<ECUC-INTEGER-PARAM-DEF UUID="5dc4dbe0-721e-4259-b610-b95095aba06f">
													<SHORT-NAME>SoAdIfTriggerTransmitBufferSize</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines the size of the trigger transmit buffer.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">BYTE</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>1500</DEFAULT-VALUE>
													<MAX>65535</MAX>
													<MIN>1</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</SUB-CONTAINERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
						<!-- Container Definition: SoAdGeneral -->
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="33ac262e-4bb6-4ffd-bede-e79bd38aa8e1">
							<SHORT-NAME>SoAdGeneral</SHORT-NAME>
							<DESC>
								<L-2 L="EN">This container contains all global configuration parameters of SoAd.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
							<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
							<PARAMETERS>
								<!-- PARAMETER DEFINITION: SoAdDevErrorDetect -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="cf286f06-9325-4638-bd9c-11fe4bd15aca">
									<SHORT-NAME>SoAdDevErrorDetect</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Development Error Detection</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Pre-processor switch for enabling development error detection support.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: SoAdIPv6AddressEnabled -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="3376d88e-8b48-4809-8180-873a0b7c4096">
									<SHORT-NAME>SoAdIPv6AddressEnabled</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">IPv6 Address Enabled</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Allows for increased memory allocation to store IPv6 addresses.</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">TRUE: Enables support for IPv6 addresses
                                        FALSE: Only IPv4 addresses are supported</L-1>
										</P>
									</INTRODUCTION>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: SoAdMainFunctionPeriod -->
								<ECUC-FLOAT-PARAM-DEF UUID="d1470831-7738-47f6-8ecc-00640357993a">
									<SHORT-NAME>SoAdMainFunctionPeriod</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Main Function Period</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Determines the frequency at which the SoAd_MainFunction() is called in [s].</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:BaseUnit">SEC</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0.005</DEFAULT-VALUE>
									<MAX>65535</MAX>
									<MIN>0</MIN>
								</ECUC-FLOAT-PARAM-DEF>
								<!-- PARAMETER DEFINITION: SoAdRoutingGroupMax -->
								<ECUC-INTEGER-PARAM-DEF UUID="********-e0e8-49e0-853d-25100fef97e7">
									<SHORT-NAME>SoAdRoutingGroupMax</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Routing Group Count</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Specifies the maximum number of SoAd routing groups. Furthermore it defines the platform type used for RoutingGroupIdType. If SoAdRoutingGroupMax is not greater than 256, an uint8 is used,</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">otherwise an uint16.</L-1>
										</P>
									</INTRODUCTION>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0</DEFAULT-VALUE>
									<MAX>65535</MAX>
									<MIN>0</MIN>
								</ECUC-INTEGER-PARAM-DEF>
								<!-- PARAMETER DEFINITION: SoAdSoConMax -->
								<ECUC-INTEGER-PARAM-DEF UUID="fd87cc44-9a0e-455c-be48-7e1b4f7bc236">
									<SHORT-NAME>SoAdSoConMax</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Socket Connection Count</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Count of configured SoAd Socket Connections.</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">Furthermore it defines the platform type used for SoAd_SoConIdType. If SoAdSoConMax is not greater than 256, an uint8 is used, otherwise uint16.</L-1>
										</P>
									</INTRODUCTION>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>0</DEFAULT-VALUE>
									<MAX>65535</MAX>
									<MIN>0</MIN>
								</ECUC-INTEGER-PARAM-DEF>
								<!-- PARAMETER DEFINITION: SoAdVersionInfoApi -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="de157d55-4f9b-4832-8108-70da859c5cfd">
									<SHORT-NAME>SoAdVersionInfoApi</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Version Info API</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Activates the SoAd_GetVersionInfo() API.</L-2>
									</DESC>
									<INTRODUCTION>
										<P>
											<L-1 L="EN">TRUE: Enables the SoAd_GetVersionInfo() API.
                                        FALSE: SoAd_GetVersionInfo() API is not included.</L-1>
										</P>
									</INTRODUCTION>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-STRING-PARAM-DEF UUID="0f7dc9b1-cfec-4d58-bd2c-365e3708d701">
									<SHORT-NAME>SoAdUserConfigFile</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">User Config File</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">If a file path will be specified, the file is read and its contents are put at the end of the SoAd config file.
</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<ECUC-STRING-PARAM-DEF-VARIANTS>
										<ECUC-STRING-PARAM-DEF-CONDITIONAL>
											<DEFAULT-VALUE>SoAd_UserCfg.h</DEFAULT-VALUE>
										</ECUC-STRING-PARAM-DEF-CONDITIONAL>
									</ECUC-STRING-PARAM-DEF-VARIANTS>
								</ECUC-STRING-PARAM-DEF>
								<ECUC-FLOAT-PARAM-DEF UUID="70734081-5fa3-4e52-8300-f9af367f3f45">
									<SHORT-NAME>SoAdShutdownFinishedWaitTime</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">Shutdown Finished Wait Time</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">This parameter defines a timout for the shutdown mechanism which is initiated by call to SoAd_Shutdown(). If this timeout expires SoAd will switch to state shutdown even if sockets are not yet closed completely.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:BaseUnit">SEC</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>2</DEFAULT-VALUE>
									<MAX>65535</MAX>
									<MIN>0</MIN>
								</ECUC-FLOAT-PARAM-DEF>
								<ECUC-STRING-PARAM-DEF UUID="8dfc7d9e-57fc-498b-af77-9796de71fcb7">
									<SHORT-NAME>SoAdHeaderFileInclusion</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter defines a list of include header files containing callback declarations (e.g. for TLS callback configuration).
These header files are included in the SoAd_Lcfg.c file.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<ECUC-STRING-PARAM-DEF-VARIANTS>
										<ECUC-STRING-PARAM-DEF-CONDITIONAL>
											<MAX-LENGTH>255</MAX-LENGTH>
											<MIN-LENGTH>0</MIN-LENGTH>
										</ECUC-STRING-PARAM-DEF-CONDITIONAL>
									</ECUC-STRING-PARAM-DEF-VARIANTS>
								</ECUC-STRING-PARAM-DEF>
								<ECUC-STRING-PARAM-DEF UUID="4296164b-eaee-470e-ae5f-2c848993691c">
									<SHORT-NAME>SoAdHeaderFileInclusionExtended</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter defines a list of include header files containing extended information which are needed in static SoAd source file (e.g. linux/if_oacket.h).
These header files are included in the SoAd_Cfg.h file.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<ECUC-STRING-PARAM-DEF-VARIANTS>
										<ECUC-STRING-PARAM-DEF-CONDITIONAL>
											<MAX-LENGTH>255</MAX-LENGTH>
											<MIN-LENGTH>0</MIN-LENGTH>
										</ECUC-STRING-PARAM-DEF-CONDITIONAL>
									</ECUC-STRING-PARAM-DEF-VARIANTS>
								</ECUC-STRING-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="25e7a521-0409-458a-a28d-089abd6509ac">
									<SHORT-NAME>SoAdTcpTlsEnabled</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter defines if TLS communication shall be enabled for SoAd.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="f4559e8f-40c0-4473-abeb-671a60b9fae6">
									<SHORT-NAME>SoAdTxDynamicLengthEnabled</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter defines if data length can be modified or set during transmission process.
In case of transmission via IfRoutingGroupTrannsmit API no PDU length is known until call to TriggerTransmit callback to retrieve data from upper layer. Therefore an internal buffer is used to store the PDU first before transmit function of TcpIp module can be called. If this feature is enabled the mentioned buffer is not needed anymore since TcpIp module can handle setting length on transmission.
In case of transmission via IfTriggerTransmit API length can differ between transmission request and TriggerTransmit callback. If this feature is not enabled such transmissions are aborted.
In case of nPdu and if IfTriggerTransmit is enabled on the corresponding SoAdPduRoutes nPduUdpTxBuffer can be optimized and is replaced by a request queue if this feature is enabled.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="e1e27e02-ff9a-42c0-ace0-438eca534828">
									<SHORT-NAME>SoAdMainFunctionSplitEnabled</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter specifies whether SoAd shall split up the SoAd_MainFunction into three functions:

SoAd_MainFunctionRx
SoAd_MainFunctionState
SoAd_MainFunctionTx

This allows optimizing the data reception and data transmission path for a specific use case.

Note: All three MainFunctions must be called from the same task or at least from tasks that do not interrupt each other.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="8282c8c0-6f7b-4853-86ff-e1f34684d27f">
									<SHORT-NAME>SoAdSafeBswChecks</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Selects if safety checks are used. These checks improve robustness as e.g. invalid parameters are checked before function is executed. This attribute has to be enabled for safety projects where this component is mapped to an ASIL partition.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-INTEGER-PARAM-DEF UUID="f5387ef4-3623-461b-b3b5-d6627af9132a">
									<SHORT-NAME>SoAdMetaDataRxBufferSize</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter defines the size of a buffer used to forward PDUs using meta data. The size of this buffer has to be 2 bytes larger than the maximum length of all PDUs using meta data. Ths last 2 bytes are used to store the meta data.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:Display">
												<SD GID="DV:BaseUnit">BYTE</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>1</DEFAULT-VALUE>
									<MAX>65535</MAX>
									<MIN>1</MIN>
								</ECUC-INTEGER-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="9d41dac0-a666-48c2-86e2-8a21d7894961">
									<SHORT-NAME>SoAdGetAndResetMeasurementDataApi</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter enables/disables the function SoAd_GetAndResetMeasurementData() to get/reset the measurement data.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
							</PARAMETERS>
							<SUB-CONTAINERS>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="73cb789f-1347-4db7-81a0-4e5a3aad419a">
									<SHORT-NAME>SoAdTcpTlsCallback</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container contains a callback which is called if a Tls socket was created.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<ECUC-FUNCTION-NAME-DEF UUID="223b855a-7d77-4f11-b4fb-bd605cee4c63">
											<SHORT-NAME>SoAdTcpTlsSocketCreatedNotification</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines a callback which is called if a TLS socket was created. The callback can be used to set additional parameters for TLS communication (e.g. set client certificate).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
													<MAX-LENGTH>255</MAX-LENGTH>
													<MIN-LENGTH>0</MIN-LENGTH>
												</ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="55582bfc-a2d9-4712-aa35-d1651be1bdf1">
									<SHORT-NAME>SoAdAdditionalLocalIpAddrAssignmentChgCallback</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container contains an addtional callback to notify an user about a local IP address assignment change.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<ECUC-FUNCTION-NAME-DEF UUID="a80b2eb2-149f-408d-acbf-fe65323c0bdd">
											<SHORT-NAME>SoAdAdditionalLocalIpAddrAssignmentChg</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines an additional callback to notify any module about a local IP address assignment change on a socket connection (LocalIpAddrAssignmentChg). There are no restrictions reagrding name but function parameters have to match the specification.
This additional callback may be required if notified component is no SoAd upper layer.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
													<MAX-LENGTH>255</MAX-LENGTH>
													<MIN-LENGTH>0</MIN-LENGTH>
												</ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="92dcf1b4-07ea-4baa-82dd-169bf73d820c">
									<SHORT-NAME>SoAdAdditionalSoConModeChgCallback</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container contains an addtional callback to notify an user about a socket connection state change.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<ECUC-FUNCTION-NAME-DEF UUID="ec68ade7-d4bb-4387-89fb-7b6bfdeb79ee">
											<SHORT-NAME>SoAdAdditionalSoConModeChg</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines an additional callback to notify any module about a socket connection mode change (SoConModeChg). There are no restrictions reagrding name but function parameters have to match the specification.
This additional callback may be required if notified component is no SoAd upper layer.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
													<MAX-LENGTH>255</MAX-LENGTH>
													<MIN-LENGTH>0</MIN-LENGTH>
												</ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="66dde0d6-27a9-4fa6-8436-4993b05e1da3">
									<SHORT-NAME>SoAdShutdownFinishedCallback</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container contains a callback to notify an user about finished shutdown.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<ECUC-FUNCTION-NAME-DEF UUID="0fb5d410-8359-443d-b9a7-3c5538215d12">
											<SHORT-NAME>SoAdShutdownFinished</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines a callback to notify any module about a successful shutdown after call to SoAd_Shutdown().</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
													<MAX-LENGTH>255</MAX-LENGTH>
													<MIN-LENGTH>0</MIN-LENGTH>
												</ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="0a8f2b1f-8194-4b73-9a7f-6d2c56b37499">
									<SHORT-NAME>SoAdEventQueueOptimization</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container provides optimization parameter for module internal event queues.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<ECUC-INTEGER-PARAM-DEF UUID="9d6b3fe4-00fd-4b15-a9c1-540da90a9502">
											<SHORT-NAME>SoAdEventQueueLimitTpRxSoCon</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter limits the number of handled TP receptions on socket connections per main function. If more TP receptions are active they will be handled in further main functions. This parameter can be used to minimize main function runtime but may lead to performance losses for TP receptions.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>1</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="ca3110ea-7030-4ce3-95d2-6c1b3d269103">
											<SHORT-NAME>SoAdEventQueueLimitStateSoCon</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter limits the number of handled socket connection events per main function (e.g. after remote address change). If more socket connection events have to be handled they will be handled in further main functions. This parameter can be used to minimize main function runtime but may lead to performance losses on socket connections state change (e.g. open/close).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>1</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="274896e1-3bb0-418e-86b7-cb713e36a766">
											<SHORT-NAME>SoAdEventQueueLimitTcpTxSoCon</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter limits the number of handled TCP transmissions in case of BSD on socket connections per main function. If more transmissions are active they will be handled in further main functions. This parameter can be used to minimize main function runtime but may lead to performance losses for TCP transmissions in case of BSD.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>1</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="07433fb8-59ee-4ca8-bb1c-e4dddec489dd">
											<SHORT-NAME>SoAdEventQueueLimitIfUdpPduRoute</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter limits the number of handled IF TxConfirmations on PduRoutes per main function. If more TxConfirmations are pending they will be handled in further main functions. This parameter can be used to minimize main function runtime but may lead to performance losses for IF PduRoutes since TxConfirmation may be called delayed.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>1</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="36bdf2ff-bb74-4b78-a324-30ae6895e645">
											<SHORT-NAME>SoAdEventQueueLimitIfTxRouteGrp</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter limits the number of handled IF Routing Group transmissions per main function. If more transmissions are pending they will be handled in further main functions. This parameter can be used to minimize main function runtime but may lead to performance losses for IF Routing Group transmissions.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>1</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="95bad51c-57d6-4bc9-8010-7536cf442e37">
											<SHORT-NAME>SoAdEventQueueLimitSocketIdx</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter limits the number of handled events on sockets (e.g. TCP socket state changes, reception of data) per main function in case of BSD. If more events are pending they will be handled in further main functions. This parameter can be used to minimize main function runtime but may lead to performance losses for socket handling.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>1</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="866904ef-3d3c-45e5-80d4-29945540aa1b">
											<SHORT-NAME>SoAdEventQueueLimitLocalAddr</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter limits the number of handled events on local addresses (e.g. IP address assignment) per main function in case of BSD. If more events are pending they will be handled in further main functions. This parameter can be used to minimize main function runtime but may lead to performance losses for local address handling (e.g. slow IP address assignment).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>1</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="febee45d-feb1-4223-9188-07a2519a419d">
											<SHORT-NAME>SoAdEventQueueLimitTpTxSoCon</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter limits the number of handled TP transmissions on socket connections per main function. If more transmissions are active they will be handled in further main functions. This parameter can be used to minimize main function runtime but may lead to performance losses for TP transmissions.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>1</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="11fe5437-a57b-466c-81f5-5310759db083">
									<SHORT-NAME>SoAdTimeoutListOptimization</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container provides optimization parameter for module internal timeout lists.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>true</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<ECUC-INTEGER-PARAM-DEF UUID="6f9c83b8-7841-44db-8d2a-3fd30fe28131">
											<SHORT-NAME>SoAdTimeoutListLimitNPduUdpTx</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter limits the number of parallel nPdu transmissions per main function. All further transmission requests are rejected if list is filled. This parameter can be used to minimize main function runtime and RAM.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>1</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="0c568c61-d9b7-49a6-9cd1-085e5aef3fb3">
									<SHORT-NAME>SoAdVerifyRxPduCallback</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container contains a callback to verify if a Rx PDU is allowed to be received.

The callback forwards local socket address, remote socket address, PDU Header ID and a configured number of PDU data to an user. If callback fails SoAd drops the received PDU completely. Otherwise SoAd forwards the received PDU.

Via SoAdVerifyRxPduEnable the feature can be enabled on each socket connection separately.

Hint: This feature is only available for TP-PDUs with PDU header option on TCP socket connections.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<ECUC-FUNCTION-NAME-DEF UUID="34ae0de4-d8e4-4af3-bb09-5e931f747cbd">
											<SHORT-NAME>SoAdVerifyRxPdu</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the callback name for the Rx PDU verification.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
													<MAX-LENGTH>255</MAX-LENGTH>
													<MIN-LENGTH>0</MIN-LENGTH>
												</ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="c242a216-201c-4ff9-afba-c6fe850ac1ef">
											<SHORT-NAME>SoAdVerifyRxPduMaxDataLength</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the number of received PDU data which are forwarded to the user for Rx PDU verification.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">BYTE</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>255</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="7495ef7c-ae15-45c9-814e-30aef0f76e60">
									<SHORT-NAME>SoAdGeneration</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Contains the generation configuration parameters of the module SoAd.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">true</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<!--SoAdOutOfBoundsWriteProtectionStrategy-->
										<ECUC-ENUMERATION-PARAM-DEF UUID="d40a0d57-2209-48af-9e31-b5d79d4ef04b">
											<SHORT-NAME>SoAdOutOfBoundsWriteProtectionStrategy</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter is used to configure a strategy to protect the code to write out of bounds.

NONE: no protection strategy is generated in the data access.
INDEX_SATURATION: arrays are blown up and the data access index is saturated by an appropriate mask. The advantage is the speed of the data access, but own data elements at other indexes of the same variable can be overridden.
INDEX_CHECKING: the data access index is validated by a runtime check. The advantage is that values are never written to incorrect indexes of the data access.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>NONE</DEFAULT-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="3fc529b0-b28b-4fa4-9937-075ca5f22b1c">
													<SHORT-NAME>NONE</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">none</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="421fee8a-819a-4d9e-bc1f-07860481cd98">
													<SHORT-NAME>INDEX_SATURATION</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">index saturation</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="941e2a22-5b0c-4908-9859-a46a852f62d4">
													<SHORT-NAME>INDEX_CHECKING</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">index checking</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!--SoAdOutOfBoundsWriteSanitizer-->
										<ECUC-BOOLEAN-PARAM-DEF UUID="7860e17e-d471-4192-978e-5a273d1ad75b">
											<SHORT-NAME>SoAdOutOfBoundsWriteSanitizer</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter activates/deactivates the generation of runtime checks which call a DET error notification function to find easily out of bounds write problems.

FALSE:  no checks are generated in the data access.
TRUE: the data access is enriched with DET checks to validate indexes.
</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!--SoAdOutOfBoundsReadSanitizer-->
										<ECUC-BOOLEAN-PARAM-DEF UUID="736f51ca-ccf5-420d-8398-f9d2590bae8a">
											<SHORT-NAME>SoAdOutOfBoundsReadSanitizer</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter activates/deactivates the generation of runtime checks which call a DET error notification function to find easily out of bounds read problems.

FALSE:  no checks are generated in the data access.
TRUE: the data access is enriched with DET checks to validate indexes.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!--SoAdReduceIdenticalValues2Define-->
										<ECUC-BOOLEAN-PARAM-DEF UUID="6179eb43-3818-4bab-82c2-e3cedd1ed1ce">
											<SHORT-NAME>SoAdReduceConstantData2Define</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Reduce Constant Data To a Define</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">This parameter activates/deactivates the capability to generate CONFIG-CLASS PRE-COMPILE ROM arrays as constant define.

FALSE: ROM arrays are generated as data even if all values are identical.
TRUE: ROM arrays are generated as constant define if all values are identical.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!--SoAdBoolDataInArrayOfStructStrategy-->
										<ECUC-ENUMERATION-PARAM-DEF UUID="01df6817-6e77-41eb-bdd1-725c395dbe8d">
											<SHORT-NAME>SoAdBoolDataInArrayOfStructStrategy</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter is used to tailor the usage of boolean data in structures in all CONFIG-CLASSES ROM data. The difference between BITFIELD and BITMASKING depends on your compiler options and memory mapping.

BOOLEAN: the datatype of boolean data is native boolean.
BITFIELD: the bitfield type is used and the compiler extracts the boolean data from structures.
BITMASKING: generated masks are used to extract the boolean data from structures.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>BITMASKING</DEFAULT-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="48387925-a716-4aff-969b-7084a264e6fa">
													<SHORT-NAME>BOOLEAN</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">boolean</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="860cbabe-de2b-40ce-9e19-9c6e9abc81bf">
													<SHORT-NAME>BITFIELD</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">bitfield</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="381ac91a-0f61-412f-9960-8e3b56c8cb85">
													<SHORT-NAME>BITMASKING</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">bitmasking</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!--SoAdDeduplicateIndirectedData-->
										<ECUC-BOOLEAN-PARAM-DEF UUID="f82c2094-7716-4295-bd80-8e72364fb4df">
											<SHORT-NAME>SoAdDeduplicateIndirectedData</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter activates/deactivates the capability to compress 0:N relational ROM data in all CONFIG-CLASSES without increasing the runtime.

FALSE: 0:N relational ROM data is not compressed.
TRUE: 0:N relational ROM data is compressed without increasing the runtime.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!--SoAdDataDeduplicationStrategy-->
										<ECUC-ENUMERATION-PARAM-DEF UUID="8b6a9646-0482-4399-b545-44e13984665d">
											<SHORT-NAME>SoAdDataDeduplicationStrategy</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter is used to taylor the the deduplication of CONFIG-CLASS PRE-COMPILE  ROM data.

NONE: The generated data is not deduplicated.
DEDUPLICATE_CONST_DATA_WITHOUT_CAST: The data is deduplicated without using casts.
DEDUPLICATE_CONST_DATA_WITH_CAST: The data is deduplicated using casts.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>DEDUPLICATE_CONST_DATA_WITH_CAST</DEFAULT-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="b03a1630-07cb-46b6-b7d3-45dd4c39e540">
													<SHORT-NAME>NONE</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">none</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="d55b114a-6928-42e0-ae35-7d2b5e8e0341">
													<SHORT-NAME>DEDUPLICATE_CONST_DATA_WITHOUT_CAST</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">deduplicate const data without cast</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="04d251d3-e3ef-4ea6-8768-c48d62bda319">
													<SHORT-NAME>DEDUPLICATE_CONST_DATA_WITH_CAST</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">deduplicate const data with cast</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!--SoAdReduceBoolDataByNegationThreshold-->
										<ECUC-INTEGER-PARAM-DEF UUID="cc4b1ce4-299b-4084-ae56-a5635c178643">
											<SHORT-NAME>SoAdReduceBoolDataByNegationThreshold</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter activates/deactivates the capability to compress boolean CONFIG-CLASS PRE-COMPILE ROM data by using the negation operator.

0: The optimization is deactivated.
&gt;0: If the number of deduplicateable elements is greater than this threshold the data optimization can be performed.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>2</DEFAULT-VALUE>
											<MAX>9223372036854775807</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!--SoAdReduceBoolDataByNumericalComparisonThreshold-->
										<ECUC-INTEGER-PARAM-DEF UUID="5b7a70fa-f91c-4528-a348-ab9475f1dbc2">
											<SHORT-NAME>SoAdReduceBoolDataByNumericalComparisonThreshold</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter activates/deactivates the capability to compress boolean CONFIG-CLASS PRE-COMPILE ROM data by using numerical comparison with other ROM data.

0: The optimization is deactivated.
&gt;0: If the number of deduplicateable elements is greater than this threshold the data optimization can be performed.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>2</DEFAULT-VALUE>
											<MAX>9223372036854775807</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!--SoAdReduceBoolDataByNumericalRelationThreshold-->
										<ECUC-INTEGER-PARAM-DEF UUID="f5c326c3-64bb-426c-9bd8-9eb6515548d2">
											<SHORT-NAME>SoAdReduceBoolDataByNumericalRelationThreshold</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter activates/deactivates the capability to compress boolean CONFIG-CLASS PRE-COMPILE ROM data by using relational comparison with other ROM data.

0: The optimization is deactivated.
&gt;0: If the number of deduplicateable elements is greater than this threshold the data optimization can be performed.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>2</DEFAULT-VALUE>
											<MAX>9223372036854775807</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!--SoAdReduceBoolDataByNumericalOperandStrategy-->
										<ECUC-ENUMERATION-PARAM-DEF UUID="1892f15e-cddf-4bb4-b8f5-9775cb49e99a">
											<SHORT-NAME>SoAdReduceBoolDataByNumericalOperandStrategy</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter is used to taylor the CONFIG-CLASS PRE-COMPILE ROM bool data with number deduplication mechanisms. A comparison with 0 is very efficient, but a numerical comparison with a value not 0 can be used to increase the ROM data compression rate.

NONE: ROM data deduplications are switched off.
DEDUPLICATE_DATA_WITH_ZERO: ROM data deduplications can be applied with the value 0.
DEDUPLICATE_DATA_WITH_ANY_VALUE: ROM data deduplications can be applied with any numerical value.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>DEDUPLICATE_DATA_WITH_ANY_VALUE</DEFAULT-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="dcd2e3bc-9fb4-471e-bab0-42ae32211c31">
													<SHORT-NAME>NONE</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">none</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="6b70eed7-075b-4a8e-9329-4af842f02bb8">
													<SHORT-NAME>DEDUPLICATE_DATA_WITH_ZERO</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">deduplicate data with zero</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="d668fbfb-dbd2-459c-bdf6-11acd9fcdb64">
													<SHORT-NAME>DEDUPLICATE_DATA_WITH_ANY_VALUE</SHORT-NAME>
													<LONG-NAME>
														<L-4 L="EN">deduplicate data with any value</L-4>
													</LONG-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<!--SoAdReduceNumericalDataByOffsetThreshold-->
										<ECUC-INTEGER-PARAM-DEF UUID="b116a9c4-d331-4c00-bd88-fca058014897">
											<SHORT-NAME>SoAdReduceNumericalDataByOffsetThreshold</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter activates/deactivates the capability to compress numerical CONFIG-CLASS PRE-COMPILE ROM data by using a constant offset.

0: The optimization is deactivated.
&gt;0: If the number of deduplicateable elements is greater than this threshold the data optimization can be performed.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>2</DEFAULT-VALUE>
											<MAX>9223372036854775807</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!--SoAdReduceNumericalDataByArraySubtractionThreshold-->
										<ECUC-INTEGER-PARAM-DEF UUID="7f9ff504-5afa-481f-aaf2-405d5133b960">
											<SHORT-NAME>SoAdReduceNumericalDataByArraySubtractionThreshold</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter activates/deactivates the capability to compress numerical CONFIG-CLASS PRE-COMPILE ROM data by using a subtraction with other ROM data.

0: The optimization is deactivated.
&gt;0: If the number of deduplicateable elements is greater than this threshold the data optimization can be performed.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>2</DEFAULT-VALUE>
											<MAX>9223372036854775807</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!--SoAdReduceDataByStreaming-->
										<ECUC-BOOLEAN-PARAM-DEF UUID="e2b0e431-d22e-40ba-8507-a925c35a3468">
											<SHORT-NAME>SoAdReduceDataByStreaming</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter activates/deactivates the capability to pack generated CONFIG-CLASS PRE-COMPILE ROM data into a data type dependent stream.

FALSE: generated const data is not packed into a data type dependent stream.
TRUE: generated const data is packed into a data type dependent stream.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!--SoAdShortSymbols-->
										<ECUC-BOOLEAN-PARAM-DEF UUID="f2bb9304-4cfd-4588-9b99-fe5cfdce1399">
											<SHORT-NAME>SoAdShortSymbols</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter activates/deactivates the capability to generate shortened symbol names.

FALSE: symbol names are generated in a human readable style based on the MIP, tags and variant names.
TRUE: symbol names are generated based on the MIP and a CRC32.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!--SoAdInterfacesForDeactivatedData-->
										<ECUC-BOOLEAN-PARAM-DEF UUID="e209f3f4-afc2-46cc-92de-d4ee97e5399d">
											<SHORT-NAME>SoAdInterfacesForDeactivatedData</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter activates/deactivates the capability to generate bsw data interfaces for deactivated data elements. This is an advantage for the BSW developer to reduce the time to market with a development environment using auto completition and to investigate potential interfaces.

FALSE: data interfaces are not generated if the data elementis deactivated.
TRUE: data interfaces are generated as e.g. emty macros.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!--SoAdReferringKeysInComments-->
										<ECUC-BOOLEAN-PARAM-DEF UUID="c32a1835-1f3c-4cbf-a510-1a54c4912438">
											<SHORT-NAME>SoAdReferringKeysInComments</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter activates/deactivates the capability to generate referring keys in comments. This is an advantage for the developer to investigate indirections, but this feature reduces the overall readability of the generated data.

FALSE: referring keys are not generated in comments.
TRUE: referring keys are generated in comments.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="73e78b67-49b4-4ab0-aadf-041657fcc7b9">
									<SHORT-NAME>SoAdSocketApi</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container contains configuration parameters describing socket API dependent parameter.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<ECUC-ENUMERATION-PARAM-DEF UUID="ed73cbb1-3e41-4b32-9058-94e23b3b2ed2">
											<SHORT-NAME>SoAdSocketApiType</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the socket API.
AUTOSAR: AUTOSAR compatible API.
LINUX: Linux compatible API (used in Linux operating system).
QNX: QNX compatible API (used in QNX operating system).
INTEGRITY: INTEGRITY compatible API (used in INTEGRITY operating system).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>AUTOSAR</DEFAULT-VALUE>
											<LITERALS>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="4b56396b-a5bf-4229-95e7-77da748b9b0a">
													<SHORT-NAME>AUTOSAR</SHORT-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="a85e8746-9574-4e39-86bc-d15e3b629372">
													<SHORT-NAME>LINUX</SHORT-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="fec0db3f-83b2-4fe9-b5b1-188fab9eea0a">
													<SHORT-NAME>QNX</SHORT-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
												<ECUC-ENUMERATION-LITERAL-DEF UUID="3d585d22-535c-475f-82cd-49332608fbee">
													<SHORT-NAME>INTEGRITY</SHORT-NAME>
													<ORIGIN>Vector Informatik</ORIGIN>
												</ECUC-ENUMERATION-LITERAL-DEF>
											</LITERALS>
										</ECUC-ENUMERATION-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="ea5dddd4-329d-4c5f-b5e1-aa48f5127d15">
											<SHORT-NAME>SoAdSocketTxBufferSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the size of a global buffer which is used to store data to be transmitted. This buffer is used for UDP and TCP transmissions. It is recommended to set this value to the maximum transmission unit on SoAd level (MTU without IP and UDP/TCP header).

This parameter is only relevant if a socket API is used which is based on polling (e.g. Linux).</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">BYTE</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Note: in case of a TCP socket where PduHeaderMode is used and an upper layer with IF-API, the required buffer size can be determined automatically.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>1452</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="45952ce2-47da-4211-9c7c-d55a1122fe77">
											<SHORT-NAME>SoAdSocketTxBufferNum</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the number of global buffer which are used to store data to be transmitted. In case transmission to socket API fails for TCP, SoAd performs a retry for the buffer until the data could be sent or the socket is closed.

This parameter is only relevant if a socket API is used which is based on polling (e.g. Linux).</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">BYTE</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Note: in case of a TCP socket where PduHeaderMode is used and an upper layer with IF-API, the required buffer size can be determined automatically.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>1</DEFAULT-VALUE>
											<MAX>254</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="ca640815-9ec8-4862-b7cc-a0771c9a57e7">
											<SHORT-NAME>SoAdSocketTxBufferMaxNumPerSocket</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the maximum number of socket transmission buffer which can be assigned to a single socket at the same time. The transmission request fails for all subsequent transmission requests.

This parameter is only relevant if a socket API is used which is based on polling (e.g. Linux).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>1</DEFAULT-VALUE>
											<MAX>254</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="28eb0d1a-849a-4912-af47-46462c2fb16c">
											<SHORT-NAME>SoAdSocketRxBufferSize</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the size of a global buffer which is used to store received data.This buffer is used for UDP only. It is recommended to set this value to the maximum reception unit on SoAd level (MTU without IP and UDP/TCP header).

This parameter is only relevant if a socket API is used which is based on polling (e.g. Linux).</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">BYTE</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<INTRODUCTION>
												<P>
													<L-1 L="EN">Note: in case of a TCP socket where PduHeaderMode is used and an upper layer with IF-API, the required buffer size can be determined automatically.</L-1>
												</P>
											</INTRODUCTION>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>1452</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="d67a3ba2-6bfb-4293-8e6c-c4f6e2933eb7">
											<SHORT-NAME>SoAdSocketLingerTimeoutEnabled</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Indicates whether the SoAd sets linger time to 0 (SO_LINGER) via setsockopt(). This option can be used to close TCP connections immediately.
true: option is set on socket creation
false: option is not set on socket creation

This parameter is only relevant if a socket API is used which is related to the BSD API (e.g. Linux).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<ECUC-FUNCTION-NAME-DEF UUID="eb6cc17a-5a13-461c-9a10-a143cd7b7e11">
											<SHORT-NAME>SoAdSocketReportErrorFunction</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines a callback which is called in case any call to socket API returns an unexpected error.

This parameter is only relevant if a socket API is used which is related to the BSD API (e.g. Linux).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
													<MAX-LENGTH>255</MAX-LENGTH>
													<MIN-LENGTH>1</MIN-LENGTH>
												</ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
										<ECUC-FLOAT-PARAM-DEF UUID="5539fd35-8665-4c84-8282-9aa67765eae1">
											<SHORT-NAME>SoAdLocalIpAddrCheckInterval</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the interval used to check if a local IP address is available.
This interval can be used to optimze calls to the socket API to prevent timing issues.
If parameter is 0 the check is perfomed in each main function cycle.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">SEC</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0.1</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="7a3b1cdf-8a35-4568-bc31-661e95f837af">
									<SHORT-NAME>SoAdDhcpEventCallback</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container contains a callback to notify user of a DHCP event.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<ECUC-FUNCTION-NAME-DEF UUID="f617f5b5-f9f1-40f4-9224-a9318506be85">
											<SHORT-NAME>SoAdDhcpEvent</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines a callback to notify any module of a DHCP event.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-LOADABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-FUNCTION-NAME-DEF-VARIANTS>
												<ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
													<MAX-LENGTH>255</MAX-LENGTH>
													<MIN-LENGTH>0</MIN-LENGTH>
												</ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
											</ECUC-FUNCTION-NAME-DEF-VARIANTS>
										</ECUC-FUNCTION-NAME-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</SUB-CONTAINERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
					</CONTAINERS>
				</ECUC-MODULE-DEF>
				<BSW-IMPLEMENTATION UUID="e2b22e5d-7458-491d-9936-688488e6f364">
					<SHORT-NAME>SoAd_Impl</SHORT-NAME>
					<PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
					<SW-VERSION>16.04.00</SW-VERSION>
					<USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
					<VENDOR-ID>30</VENDOR-ID>
					<AR-RELEASE-VERSION>4.02.02</AR-RELEASE-VERSION>
					<BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/SoAd_ib_bswmd/BswModuleDescriptions/SoAd/SoAdBehavior</BEHAVIOR-REF>
					<PRECONFIGURED-CONFIGURATION-REFS>
						<PRECONFIGURED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/SoAd_Pre</PRECONFIGURED-CONFIGURATION-REF>
					</PRECONFIGURED-CONFIGURATION-REFS>
					<RECOMMENDED-CONFIGURATION-REFS>
						<RECOMMENDED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/SoAd_Rec</RECOMMENDED-CONFIGURATION-REF>
					</RECOMMENDED-CONFIGURATION-REFS>
					<VENDOR-SPECIFIC-MODULE-DEF-REFS>
						<VENDOR-SPECIFIC-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/MICROSAR/SoAd</VENDOR-SPECIFIC-MODULE-DEF-REF>
					</VENDOR-SPECIFIC-MODULE-DEF-REFS>
				</BSW-IMPLEMENTATION>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="62f73f36-9edb-430d-a03e-007d8480885c">
					<SHORT-NAME>SoAd_Pre</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/SoAd</DEFINITION-REF>
					<CONTAINERS></CONTAINERS>
				</ECUC-MODULE-CONFIGURATION-VALUES>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="af986b81-b8da-43dc-b234-5b18373406d7">
					<SHORT-NAME>SoAd_Rec</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/SoAd</DEFINITION-REF>
					<CONTAINERS></CONTAINERS>
				</ECUC-MODULE-CONFIGURATION-VALUES>
			</ELEMENTS>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>