############################################################################### 
# File Name  : Sd_check.mak 
# Description: Configuration check makefile 
#------------------------------------------------------------------------------
# COPYRIGHT
#------------------------------------------------------------------------------
# Copyright (c) 2019 by Vector Informatik GmbH.  All rights reserved.
#------------------------------------------------------------------------------
# REVISION HISTORY
#------------------------------------------------------------------------------
# Refer to the Sd_rules.mak file.
############################################################################### 
PREPARE_CONFIGURATION_INTERFACE     += 
CHECK_VARS_WHICH_ARE_REQUIRED       +=  
CHECK_VARS_WHICH_ARE_OPTIONAL       += 
CHECK_VARS_WHICH_ARE_OBSOLETE       += 
CHECK_VARS_WITH_ONE_CC_FILE         += 
CHECK_VARS_WITH_MORE_CC_FILES       += 
CHECK_VARS_WITH_ONE_CPP_FILE        += 
CHECK_VARS_WITH_MORE_CPP_FILES      += 
CHECK_VARS_WITH_ONE_ASM_FILE        += 
CHECK_VARS_WITH_MORE_ASM_FILES      += 
CHECK_VARS_WITH_ONE_LIB_FILE        += 
CHECK_VARS_WITH_MORE_LIB_FILES      += 
CHECK_VARS_WITH_ONE_OBJ_FILE        += 
CHECK_VARS_WITH_MORE_OBJ_FILES      += 
#e.g: CHECK_VARS_WITH_ONE_DIRECTORY       +=     $(DIRECTORIES_TO_CREATE) 
#       $(DIRECTORIES_TO_CREATE) = C:\demo\drv          (this var is defined in _rules.mak) 
CHECK_VARS_WITH_ONE_DIRECTORY       += 
CHECK_VARS_WITH_MORE_DIRECTORIES    += 
CHECK_VARS_WITH_ONE_FILE            += 
CHECK_VARS_WITH_MORE_FILES          += 
CHECK_VARS_WITH_MAX_LENGTH_ONE      += 
############################################################### 
# SPECIFIC 
# 
#ifneq ($(CAN_DRIVER_MODE),singlechannel) 
#ifneq ($(CAN_DRIVER_MODE),multichannel) 
#$(error The value of the variable CAN_DRIVER_MODE is not valid. 
# Please use singlechannel or multichannel) 
#endif 
#endif 
############################################################### 
# REQUIRED 
# 
# No other resources are required for a Configuration Check  
# Makefile 
# 
############################################################### 
############################################################### 
# PROVIDE 
# 
# A configuration Makefile does not provide resources for other 
# basic software bundles or the base-make package. 
# 
############################################################### 
