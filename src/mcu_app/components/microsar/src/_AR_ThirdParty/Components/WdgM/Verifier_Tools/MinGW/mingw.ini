[mingw]
Build=12
URL=http://prdownloads.sourceforge.net/mingw
Filename=MinGW-5.1.6.exe
packages=previous|current|candidate

[current]
runtime=mingwrt-3.15.2-mingw32-dev.tar.gz|7616
runtimeDLL=mingwrt-3.15.2-mingw32-dll.tar.gz|40
w32api=w32api-3.13-mingw32-dev.tar.gz|14420
binutils=binutils-2.19.1-mingw32-bin.tar.gz|21093
core=gcc-core-3.4.5-20060117-3.tar.gz|7712
gpp=gcc-g++-3.4.5-20060117-3.tar.gz|15480
g77=gcc-g77-3.4.5-20060117-3.tar.gz|5272
ada=gcc-ada-3.4.5-20060117-3.tar.gz|33860
java=gcc-java-3.4.5-20060117-3.tar.gz|43160
objc=gcc-objc-3.4.5-20060117-3.tar.gz|3720
make=mingw32-make-3.81-20080326-2.tar.gz|727

[previous]
runtime=mingw-runtime-3.14.tar.gz|6500
w32api=w32api-3.11.tar.gz|14500
binutils=binutils-2.17.50-20060824-1.tar.gz|21940
core=gcc-core-3.4.2-20040916-1.tar.gz|8627
gpp=gcc-g++-3.4.2-20040916-1.tar.gz|16542
g77=gcc-g77-3.4.2-20040916-1.tar.gz|5158
ada=gcc-ada-3.4.2-20040916-1.tar.gz|33333
java=gcc-java-3.4.2-20040916-1.tar.gz|45547
objc=gcc-objc-3.4.2-20040916-1.tar.gz|4555
make=mingw32-make-3.81-2.tar.gz|720

[candidate]
binutils=binutils-2.18.50-20080109-2.tar.gz|20505
core=gcc-core-3.4.5-20060117-3.tar.gz|7712
gpp=gcc-g++-3.4.5-20060117-3.tar.gz|15480
g77=gcc-g77-3.4.5-20060117-3.tar.gz|5272
ada=gcc-ada-3.4.5-20060117-3.tar.gz|33860
java=gcc-java-3.4.5-20060117-3.tar.gz|43160
objc=gcc-objc-3.4.5-20060117-3.tar.gz|3720
make=mingw32-make-3.81-2.tar.gz|720
