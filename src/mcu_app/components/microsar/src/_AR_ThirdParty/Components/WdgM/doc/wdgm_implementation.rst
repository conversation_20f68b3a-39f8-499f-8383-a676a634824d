Implementation
=======================================================

WDG处理流程图：
 .. figure:: ./wdgm_flowchart.PNG
   :align: left

WDG处理流程说明：

1.FUSA模块通过调用WdgM_CheckpointReached()函数，将CheckPoint的检测情况上报给WDGM模块

2.WDGM模块的WdgM_MainFunction()函数中判断CheckPoint检测结果，如果检测到错误则会触发重启，否则继续监测

3.如果需要重启，WDGM会通过调用 WdgM_PerformReset()函数下发重启命令给WDGIF

4.当WDGIF接到重启命令会调用WdgIf_SetTriggerWindow(0,0)将WDG超时时间设置为0，WDG模块判断超时立即重启；而没有超时的状态下WDG会在窗口时间内喂狗，系统正常运行

Function
------------------------------------------
.. doxygenfunction:: WdgM_CheckpointReached
.. doxygenfunction:: WdgM_PerformReset
.. doxygenfunction:: WdgM_MainFunction
.. doxygenfunction:: WdgM_Init
.. doxygenfunction:: WdgM_SetMode
.. doxygenfunction:: WdgM_GetMode
.. doxygenfunction:: WdgIf_SetTriggerCondition

Definition
------------------------------------------
.. doxygendefine::	API_ID_WdgM_Init
.. doxygendefine::	API_ID_WdgM_DeInit
.. doxygendefine::	API_ID_WdgM_CheckpointReached 
.. doxygendefine::	API_ID_WdgM_SetMode
.. doxygendefine::	API_ID_WdgM_GetMode
.. doxygendefine::  API_ID_WdgM_GetLocalStatus 
.. doxygendefine::  API_ID_WdgM_GetGlobalStatus
.. doxygendefine::	API_ID_WdgM_PerformReset
.. doxygendefine::	API_ID_WdgM_MainFunction
.. doxygendefine::	WDGM_INTERNAL_SOFTWARE_TICK
.. doxygendefine::	WDGM_OS_COUNTER_TICK
.. doxygendefine::	WDGM_EXTERNAL_TICK
.. doxygendefine::	WDGM_USE_MODE_SWITCH_PORTS 
.. doxygendefine::	WDGM_USE_NOTIFICATIONS


Typedef
------------------------------------------
.. doxygentypedef:: WdgM_CoreIdType
.. doxygentypedef:: WdgM_CounterType
.. doxygentypedef:: WdgM_ApplicationType
.. doxygentypedef:: WdgM_BooleanParamType
.. doxygentypedef:: WdgM_TransitionIdType
.. doxygentypedef:: WdgM_TimeBaseTickType
.. doxygentypedef:: WdgM_TriggerTimeMsType
.. doxygentypedef:: WdgM_TriggerTimeTickType
.. doxygentypedef:: WdgM_AliveCntType
.. doxygentypedef:: WdgM_AliveMarginType
.. doxygentypedef:: WdgM_SupervisedCycCntType
.. doxygentypedef:: WdgM_RefCycleType
.. doxygentypedef:: WdgM_WatchdogIdType
.. doxygentypedef:: WdgM_SharedBooleanType
.. doxygentypedef:: WdgM_ViolationCntType

Variable
------------------------------------------
.. doxygenvariable:: g_wdgm_cfg_ptr_array
.. doxygenvariable:: WdgMSupervisedEntity_pt
.. doxygenvariable:: WdgMNrOfSupervisedEntities

Struct
------------------------------------------

.. doxygenstruct:: WdgM_AliveCounterLType
    :members:
.. doxygenstruct:: WdgM_EntityStatusLType
    :members:
.. doxygenstruct:: WdgM_AliveCounterGType
    :members:
.. doxygenstruct:: WdgM_EntityStatusGType
    :members:
.. doxygenstruct:: WdgM_DataGType
    :members:
.. doxygenstruct:: WdgM_DataGSType
    :members:
.. doxygenstruct:: WdgM_GlobalTransitionFlagGSType
    :members:
.. doxygenstruct:: WdgM_EntityGSType
    :members:
.. doxygenstruct:: WdgM_TriggerModeType
    :members:
.. doxygenstruct:: WdgM_WatchdogDeviceType
    :members:
.. doxygenstruct:: WdgM_CallersType
    :members:
.. doxygenstruct:: WdgM_TransitionType
    :members:
.. doxygenstruct:: WdgM_GlobalTransitionType
    :members:
.. doxygenstruct:: WdgM_CheckPointType
    :members:
.. doxygenstruct:: WdgM_SupervisedEntityType
    :members:
.. doxygenstruct:: WdgM_GlobalVariablesType
    :members:
.. doxygenstruct:: WdgM_ConfigType
    :members:
	

`具体信息可以点击此处查看 <../html/index.html>`_

