<?xml version="1.0" encoding="UTF-8"?>
<!--
***********************************************************************************************************************
COPYRIGHT
===============================================================================
Copyright (c) 2020 by Vector Informatik GmbH.                                         All rights reserved.

    This software is copyright protected and proprietary to Vector Informatik GmbH.
    Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
    All other rights remain with Vector Informatik GmbH.
===============================================================================
FILE DESCRIPTION
===============================================================================
File:           WdgM_bswmd.arxml
Component:      -
Module:         WdgM
Generator:      -
Description:    -
*********************************************************************************************************************** 
-->
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://autosar.org/schema/r4.0">
	<!--
       Note for XML Schema validation purposes:
       You may have to set the `xsi:schemaLocation` attribute above so that
       it points to the actual location of `AUTOSAR.xsd`
  -->
	<AR-PACKAGES>
		<AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
			<SHORT-NAME>MICROSAR</SHORT-NAME>
			<ELEMENTS>
				<BSW-IMPLEMENTATION UUID="fd26c7c1-00dc-4cd6-b4f6-f6e9f892aecc">
					<SHORT-NAME>WdgM_Impl</SHORT-NAME>
					<PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
					<SW-VERSION>5.05.03</SW-VERSION>
					<USED-CODE-GENERATOR>DaVinci Configurator</USED-CODE-GENERATOR>
					<VENDOR-ID>30</VENDOR-ID>
					<AR-RELEASE-VERSION>4.02.02</AR-RELEASE-VERSION>
					<BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/MICROSAR/WdgM_ib_bswmd/BswModuleDescriptions/WdgM/WdgMBehavior</BEHAVIOR-REF>
					<PRECONFIGURED-CONFIGURATION-REFS>
						<PRECONFIGURED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/WdgM_Pre</PRECONFIGURED-CONFIGURATION-REF>
					</PRECONFIGURED-CONFIGURATION-REFS>
					<RECOMMENDED-CONFIGURATION-REFS>
						<RECOMMENDED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/WdgM_Rec</RECOMMENDED-CONFIGURATION-REF>
					</RECOMMENDED-CONFIGURATION-REFS>
					<VENDOR-SPECIFIC-MODULE-DEF-REFS>
						<VENDOR-SPECIFIC-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/MICROSAR/WdgM</VENDOR-SPECIFIC-MODULE-DEF-REF>
					</VENDOR-SPECIFIC-MODULE-DEF-REFS>
				</BSW-IMPLEMENTATION>
				<ECUC-MODULE-DEF UUID="3d9e436f-54f3-45c6-882f-8c810c00ad7c">
					<SHORT-NAME>WdgM</SHORT-NAME>
					<DESC>
						<L-2 L="EN">Configuration of the WdgM (Watchdog Manager) module.</L-2>
					</DESC>
					<CATEGORY>VENDOR_SPECIFIC_MODULE_DEFINITION</CATEGORY>
					<ADMIN-DATA>
						<DOC-REVISIONS>
							<DOC-REVISION>
								<REVISION-LABEL>5.00.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virchl</ISSUED-BY>
								<DATE>2016-02-16T03:38:30+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Initial version of migrated WdgM description file (ARSR4)</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.00.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virchl</ISSUED-BY>
								<DATE>2016-03-22T04:20:49+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Initial version with MICROSAR root</L-2>
										</CHANGE>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Supported config variant limited to PRE_COMPILE</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.00.01</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virchl</ISSUED-BY>
								<DATE>2016-07-29T10:37:00+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SwVersion set to 5.1.1</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.00.02</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virchl</ISSUED-BY>
								<DATE>2016-08-08T04:55:42+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SwVersion set to 5.1.2</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.00.03</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virchl</ISSUED-BY>
								<DATE>2016-10-11T07:53:50+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SwVersion set to 5.1.3</L-2>
										</CHANGE>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Literal "WDGM_LOCAL_STATUS_FAILED" of WdgMSupervisedEntityInitialMode deleted</L-2>
										</CHANGE>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Description of WdgMSupervisedEntityId and WdgMCheckpointId modified</L-2>
										</CHANGE>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SWS traceability added</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.01.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virchl</ISSUED-BY>
								<DATE>2017-02-22T04:44:25+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SwVersion set to 5.2.0</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">FEATC-621</L-2>
										</REASON>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Parameter WdgMOsCounterRef added and enumeration value of WdgMTimebaseSource changed (OsCounter)</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">FEATC-621</L-2>
										</REASON>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Parameter WdgMStateChangeNotification deleted and WdgMStatusReportingMechanism added</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">FEATC-622</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.01.01</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virchl</ISSUED-BY>
								<DATE>2017-10-25T15:39:33+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Description of WdgMGlobalStateChangeCbk and WdgMLocalStateChangeCbk updated</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00097148</L-2>
										</REASON>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SwVersion set to 5.2.1</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.01.02</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virchl</ISSUED-BY>
								<DATE>2018-04-10T10:31:05+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Unify AUTOSAR version in component</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">STORYC-2913</L-2>
										</REASON>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SwVersion set to 5.2.2</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">STORYC-2913</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.02.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virchl</ISSUED-BY>
								<DATE>2018-04-24T09:03:51+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Parameter WdgMReportStatusViaRte added in WdgMSupervisedEntity and WdgMMode</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">STORYC-5033</L-2>
										</REASON>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Description of WdgMLocalStateChangeCbk and WdgMGlobalStateChangeCbk modified</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">STORYC-5033</L-2>
										</REASON>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Default values and display settings added</L-2>
										</CHANGE>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Unused parameter WdgMTriggerWindowStart deleted</L-2>
										</CHANGE>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SwVersion set to 5.3.0</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">STORYC-5033</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.02.01</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>vircre</ISSUED-BY>
								<DATE>2018-12-18T08:56:05+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SwVersion set to 5.4.0</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">STORYC-5024</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.03.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virbmz</ISSUED-BY>
								<DATE>2019-07-12T10:28:58+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">WdgM shall support at least 6 cores, max 10 cores</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">MWDG-7</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<STATE>released</STATE>
								<ISSUED-BY>vircre</ISSUED-BY>
								<DATE>2019-07-25T10:31:13+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Compiler error: Multiple application header files included. Only description of parameter adjusted.</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00103755</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.03.01</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virsrl</ISSUED-BY>
								<DATE>2019-11-29T10:55:39+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Compiler error: Types header incompatible with RTE</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00104766</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.03.02</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virsrl</ISSUED-BY>
								<DATE>2019-12-06T13:56:01+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Description correction</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">-</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.03.03</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virbka</ISSUED-BY>
								<DATE>2020-08-17T17:03:13+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SwVersion set to 5.5.3</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">MWDG-3639</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
						</DOC-REVISIONS>
					</ADMIN-DATA>
					<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
					<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
					<REFINED-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/WdgM</REFINED-MODULE-DEF-REF>
					<SUPPORTED-CONFIG-VARIANTS>
						<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
					</SUPPORTED-CONFIG-VARIANTS>
					<CONTAINERS>
						<!-- Parameter Container Definition: WdgMConfigSet -->
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="2dbc28d5-018a-4415-8be7-da339d2a5b46">
							<SHORT-NAME>WdgMConfigSet</SHORT-NAME>
							<DESC>
								<L-2 L="EN">This container describes one of multiple configuration sets of WdgM.

This is a MultipleConfigurationContainer, i.e. this container and its sub-containers exist once per configuration set.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
							<MULTIPLE-CONFIGURATION-CONTAINER>true</MULTIPLE-CONFIGURATION-CONTAINER>
							<REFERENCES>
								<!-- Symbolic Name Reference Definition: WdgMInitialMode -->
								<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="418f2b84-5b5d-45ae-8319-ff5afb67a64d">
									<SHORT-NAME>WdgMInitialMode</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The mode that the Watchdog Manager is in after it has been initialized.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/WdgM/WdgMConfigSet/WdgMMode</DESTINATION-REF>
								</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
							</REFERENCES>
							<SUB-CONTAINERS>
								<!-- Container Definition: WdgMDemEventParameterRefs -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="56811dfb-d481-409f-b698-52b79aea9f8f">
									<SHORT-NAME>WdgMDemEventParameterRefs</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Container for the references to DemEventParameter elements which shall be invoked using the API Dem_ReportErrorStatus API in case the corresponding error occurs. The EventId is taken from the referenced DemEventParameter's DemEventId value. The standardized errors are provided in the container and can be extended by vendor specific error references.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<REFERENCES>
										<!-- Symbolic Name Reference Definition: WDGM_E_IMPROPER_CALLER -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="9d923b71-7742-4bf8-a0e7-a48ab9aec6d8">
											<SHORT-NAME>WDGM_E_IMPROPER_CALLER</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to the DemEventParameter which shall be issued when the defensive behavior checks have detected an improper caller.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
										<!-- Symbolic Name Reference Definition: WDGM_E_MONITORING -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="435446f1-e62b-45f6-8174-befa94529f1f">
											<SHORT-NAME>WDGM_E_MONITORING</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to the DemEventParameter which shall be issued when the error "Monitoring has failed and a watchdog reset will occur" has occured.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
										<!-- Symbolic Name Reference Definition: WDGM_E_SET_MODE -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="e175d039-9bc4-4f4d-9677-49f508c5bd9a">
											<SHORT-NAME>WDGM_E_SET_MODE</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Parameter not used.

Reference to the DemEventParameter which shall be issued when the error "Watchdog drivers' mode switch has failed" has occured.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Parameter Container Definition: WdgMMode -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="e57d7de3-99ef-444c-bf4b-05ab61fcada5">
									<SHORT-NAME>WdgMMode</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The container describes one of several modes of the Watchdog Manager.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: WdgMExpiredSupervisionCycleTol -->
										<ECUC-INTEGER-PARAM-DEF UUID="df12cf51-bd95-4777-80cf-afdcfa7baf3d">
											<SHORT-NAME>WdgMExpiredSupervisionCycleTol</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter shall be used to define a value that fixes the amount of expired supervision cycles for how long the blocking of watchdog triggering shall be postponed.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:DefaultFormat">DEC</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: WdgMModeId -->
										<ECUC-INTEGER-PARAM-DEF UUID="fb4a8d25-3b98-467d-a9ae-5d82bbb5ab2a">
											<SHORT-NAME>WdgMModeId</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter represents the WdgM mode. </L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:DefaultFormat">DEC</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>255</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-FLOAT-PARAM-DEF UUID="7afa4880-cddf-4a75-9f0e-4b8931d186b6">
											<SHORT-NAME>WdgMSupervisionCycle</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the period with which the main function (WdgM_MainFunction) is called.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:BaseUnit">SEC</SD>
														<SD GID="DV:Unit">MSEC</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>1.7976931348623157E+308</MAX>
											<MIN>0</MIN>
										</ECUC-FLOAT-PARAM-DEF>
										<!-- PARAMETER DEFINITION: WdgMInitialTriggerModeId -->
										<ECUC-INTEGER-PARAM-DEF UUID="93d2de70-0156-4bd7-b852-cd4c6b89cf07">
											<SHORT-NAME>WdgMInitialTriggerModeId</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Denotes the ID of the Trigger Mode which shall be used at system start. </L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:DefaultFormat">DEC</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>255</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: WdgMModeCoreAssignment -->
										<ECUC-INTEGER-PARAM-DEF UUID="212cac17-bb17-40ad-837b-277d032d7df5">
											<SHORT-NAME>WdgMModeCoreAssignment</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This value specifies the assignment of configuration mode to a processor core ID. Maximal 10 cores are able to be used.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:DefaultFormat">DEC</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>9</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: WdgMGlobalStateChangeCbk -->
										<ECUC-FUNCTION-NAME-DEF UUID="f9df1e12-27fe-47e1-a544-a96c49652719">
											<SHORT-NAME>WdgMGlobalStateChangeCbk</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Callback function for notifying Watchdog Manager global status change.

Note:
- This parameter can only be used if WdgMStatusReportingMechanism is set to WDGM_USE_NOTIFICATIONS.
- This parameter can only be used if parameter WdgMReportStatusViaRte of this WdgMMode is set false</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-FUNCTION-NAME-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="46824363-560a-40ef-b05f-e74dc6149d16">
											<SHORT-NAME>WdgMTicksPerSecond</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines the number of Watchdog Manager ticks per second.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:DefaultFormat">DEC</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="985fdea7-9c8b-4faa-a5fd-17d9b4addaa8">
											<SHORT-NAME>WdgMReportStatusViaRte</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter is used to enable the status reporting via Rte.

Notes:
- This parameter can only be set true if WdgMStatusReportingMechanism is _not_ WDGM_USE_NO_STATUS_REPORTING
- If this parameter is set true in any of the supervised entities or in WdgMMode, the global switch WdgMUseRte has to be set true, too.
- This parameter cannot be set true if global switch WdgMUseRte is set false.
- If this parameter is set true, the optional parameter WdgMGlobalStateChangeCbk must not exist.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Symbolic Name Reference Definition: WdgMGlobalMemoryAppTaskRef -->
										<ECUC-CHOICE-REFERENCE-DEF UUID="c5059eda-0e74-4f1d-bc42-99beef4e69f3">
											<SHORT-NAME>WdgMGlobalMemoryAppTaskRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to an OS Application where the WdgM is running. In case of OS SC3 (with memory protection) the global variables of the Watchdog Manager module should be placed in the same memory segment as the application in the context of which the Watchdog Manager is running.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REFS>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsApplication</DESTINATION-REF>
											</DESTINATION-REFS>
										</ECUC-CHOICE-REFERENCE-DEF>
										<ECUC-REFERENCE-DEF UUID="8c278d8d-4132-4ade-8e9f-5566663e96d2">
											<SHORT-NAME>WdgMOsCounterRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to the counter which shall be used for deadline monitoring.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsCounter</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<!-- Parameter Container Definition: WdgMAliveSupervision -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="17d83b33-4272-443a-aec7-7b73f961aed4">
											<SHORT-NAME>WdgMAliveSupervision</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container collects all configuration parameters of Alive-Supervision.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>65535</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: WdgMExpectedAliveIndications -->
												<ECUC-INTEGER-PARAM-DEF UUID="b565551d-d0c1-403e-85ca-6cd59f20d460">
													<SHORT-NAME>WdgMExpectedAliveIndications</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter contains the amount of expected alive indications within the referenced amount of defined supervision cycles according to corresponding SE.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:DefaultFormat">DEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: WdgMMaxMargin -->
												<ECUC-INTEGER-PARAM-DEF UUID="2bebf10e-04c2-4964-b4ba-31537ed41f5b">
													<SHORT-NAME>WdgMMaxMargin</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter contains the amount of alive indications that are acceptable to be additional on the expected alive indications within the corresponding supervision reference cycle.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:DefaultFormat">DEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: WdgMMinMargin -->
												<ECUC-INTEGER-PARAM-DEF UUID="b9ac601c-755d-4258-941d-fe67dc6a5131">
													<SHORT-NAME>WdgMMinMargin</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter contains the amount of alive indications that are acceptable to be missed from the expected alive indications within the corresponding supervision reference cycle.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:DefaultFormat">DEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: WdgMSupervisionReferenceCycle -->
												<ECUC-INTEGER-PARAM-DEF UUID="ef3e223c-a08c-4dab-89c7-0cb9e93c0594">
													<SHORT-NAME>WdgMSupervisionReferenceCycle</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter contains the amount of supervision cycles to be used as reference by the alive-supervision mechanism to perform the checkup with counted alive indications according to corresponding SE.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:DefaultFormat">DEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>65535</MAX>
													<MIN>1</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Symbolic Name Reference Definition: WdgMAliveSupervisionCheckpointRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="9c14cf8a-482d-4c4f-bbd3-7d8146c7703e">
													<SHORT-NAME>WdgMAliveSupervisionCheckpointRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the Checkpoint for which this alive counter is configured.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/WdgM/WdgMGeneral/WdgMSupervisedEntity/WdgMCheckpoint</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Parameter Container Definition: WdgMDeadlineSupervision -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="31e680b5-fc6f-4feb-8c70-ddce7d29784a">
											<SHORT-NAME>WdgMDeadlineSupervision</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container collects all configuration parameters for Deadline Monitoring for a Supervised Entity.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>65535</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: WdgMDeadlineMax -->
												<ECUC-FLOAT-PARAM-DEF UUID="79cf6acd-1479-4a4b-9c25-5ac54b58afff">
													<SHORT-NAME>WdgMDeadlineMax</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter contains the longest time span after which the deadline is considered to be met.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
																<SD GID="DV:Unit">MSEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>1.7976931348623157E+308</MAX>
													<MIN>0.0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
												<!-- PARAMETER DEFINITION: WdgMDeadlineMin -->
												<ECUC-FLOAT-PARAM-DEF UUID="6bd050aa-5a0b-4266-a5dd-aac1c9b024c3">
													<SHORT-NAME>WdgMDeadlineMin</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter contains the shortest time span after which the deadline is considered to be met.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">SEC</SD>
																<SD GID="DV:Unit">MSEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>1.7976931348623157E+308</MAX>
													<MIN>0.0</MIN>
												</ECUC-FLOAT-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Symbolic Name Reference Definition: WdgMDeadlineStartRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="447ca545-4f72-4dfc-b437-c7589ddcdb33">
													<SHORT-NAME>WdgMDeadlineStartRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This is the reference to the start Checkpoint for Deadline Monitoring.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/WdgM/WdgMGeneral/WdgMSupervisedEntity/WdgMCheckpoint</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
												<!-- Symbolic Name Reference Definition: WdgMDeadlineStopRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="ebe91d65-d95b-46ec-bf8f-a96848d151a2">
													<SHORT-NAME>WdgMDeadlineStopRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This is the reference to the stop Checkpoint for Deadline Monitoring. Note that the deadline's start and stop references must match an existing local or a global transition.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/WdgM/WdgMGeneral/WdgMSupervisedEntity/WdgMCheckpoint</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Parameter Container Definition: WdgMExternalLogicalSupervision -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="a7389008-8797-4ff3-8c29-3001838dfb32">
											<SHORT-NAME>WdgMExternalLogicalSupervision</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container collects all configuration parameters for Logical Supervision for one external graph.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<REFERENCES>
												<!-- Symbolic Name Reference Definition: WdgMExternalCheckpointFinalRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="54303b7c-9f68-4443-8e78-1cf288f293bd">
													<SHORT-NAME>WdgMExternalCheckpointFinalRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">A reference to the final global Checkpoint. Note that there might be several final global checkpoints.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>65535</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/WdgM/WdgMGeneral/WdgMSupervisedEntity/WdgMCheckpoint</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
												<!-- Symbolic Name Reference Definition: WdgMExternalCheckpointInitialRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="52370a40-da98-40cc-8349-b36730e48f59">
													<SHORT-NAME>WdgMExternalCheckpointInitialRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">A reference to the global initial Checkpoint.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/WdgM/WdgMGeneral/WdgMSupervisedEntity/WdgMCheckpoint</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
											<SUB-CONTAINERS>
												<ECUC-PARAM-CONF-CONTAINER-DEF UUID="9a8e9a25-92e4-435a-810f-5fd323edd887">
													<SHORT-NAME>WdgMExternalTransition</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This container collects the Checkpoints for a Global Transition across Supervised Entities.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:CfgPostBuild">
																<SD GID="DV:postBuildSelectableChangeable">false</SD>
																<SD GID="DV:postBuildNotDeletable">false</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>65535</UPPER-MULTIPLICITY>
													<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
													<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
													<REFERENCES>
														<!-- Symbolic Name Reference Definition: WdgMExternalTransitionDestRef -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="738ec705-b880-4260-815c-fe7e06deb8c6">
															<SHORT-NAME>WdgMExternalTransitionDestRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This is the reference to the destination Checkpoint of an External Transition.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/WdgM/WdgMGeneral/WdgMSupervisedEntity/WdgMCheckpoint</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
														<!-- Symbolic Name Reference Definition: WdgMExternalTransitionSourceRef -->
														<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="cef4db25-a448-4795-866b-fec00a586aa4">
															<SHORT-NAME>WdgMExternalTransitionSourceRef</SHORT-NAME>
															<DESC>
																<L-2 L="EN">This is the reference to the source Checkpoint of an External Transition.</L-2>
															</DESC>
															<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
															<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
															<IMPLEMENTATION-CONFIG-CLASSES>
																<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
																	<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
																	<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
																</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															</IMPLEMENTATION-CONFIG-CLASSES>
															<ORIGIN>AUTOSAR_ECUC</ORIGIN>
															<REQUIRES-INDEX>false</REQUIRES-INDEX>
															<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/WdgM/WdgMGeneral/WdgMSupervisedEntity/WdgMCheckpoint</DESTINATION-REF>
														</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
													</REFERENCES>
												</ECUC-PARAM-CONF-CONTAINER-DEF>
											</SUB-CONTAINERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Parameter Container Definition: WdgMTrigger -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="e5f8eed7-2309-4f4a-a169-be93791bfa92">
											<SHORT-NAME>WdgMTrigger</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container collects all configuration parameters for the triggering of hardware watchdogs.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: WdgMWatchdogMode -->
												<ECUC-ENUMERATION-PARAM-DEF UUID="74c429ed-60d9-4ff6-a274-d3a5f0d8641d">
													<SHORT-NAME>WdgMWatchdogMode</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter contains the watchdog mode that shall be used for the referenced watchdog in this Watchdog Manager mode.

Implementation Type: WdgIf_ModeType</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="MSR:Process">
																<SD GID="MSR:TraceRef">SPEC-2068335</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="74117981-e900-4f49-974e-08bf2a3a6933">
															<SHORT-NAME>WDGIF_FAST_MODE</SHORT-NAME>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="08d8886f-9a72-4e8d-b7e0-505e683cdd63">
															<SHORT-NAME>WDGIF_OFF_MODE</SHORT-NAME>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="c573cb9d-0084-4d90-8499-1126813b63af">
															<SHORT-NAME>WDGIF_SLOW_MODE</SHORT-NAME>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<!-- PARAMETER DEFINITION: WdgMTriggerConditionValue -->
												<ECUC-INTEGER-PARAM-DEF UUID="292ee535-f62c-45eb-b0d2-df847155bf47">
													<SHORT-NAME>WdgMTriggerConditionValue</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter defines the maximum possible trigger time where the watchdog trigger is accepted. The consumer of this parameter is the WdgIf_SetTriggerCondition for the corresponding watchdog.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:BaseUnit">MSEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>65535</MAX>
													<MIN>1</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<!-- PARAMETER DEFINITION: WdgMTriggerWindowStart -->
												<!-- PARAMETER DEFINITION: WdgMTriggerModeId -->
												<ECUC-INTEGER-PARAM-DEF UUID="3a676183-68c1-4bdd-9231-ebf1ec194573">
													<SHORT-NAME>WdgMTriggerModeId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter contains a unique identifier of the trigger mode.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:DefaultFormat">DEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>254</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Symbolic Name Reference Definition: WdgMTriggerWatchdogRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="82b5c6db-c67b-4cb1-a1d4-6997e8c52b8c">
													<SHORT-NAME>WdgMTriggerWatchdogRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter is a reference to the configured watchdog.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/WdgM/WdgMGeneral/WdgMWatchdog</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Parameter Container Definition: WdgMLocalStatusParams -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="c5f89145-ad15-4030-a200-d8f0a856d560">
											<SHORT-NAME>WdgMLocalStatusParams</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container collects all configuration parameters for the Local Status of a Supervised Entity.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>65535</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: WdgMFailedAliveSupervisionRefCycleTol -->
												<ECUC-INTEGER-PARAM-DEF UUID="d4b7363f-b318-455b-9aaf-15b410e26324">
													<SHORT-NAME>WdgMFailedAliveSupervisionRefCycleTol</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter contains the acceptable amount of failed alive supervisions for this Supervised Entity</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:DefaultFormat">DEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<MAX>65534</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-ENUMERATION-PARAM-DEF UUID="185cccc7-8eeb-4932-a6d2-f52784aaa8e4">
													<SHORT-NAME>WdgMSupervisedEntityInitialMode</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Initial local monitoring status of the supervised entity.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>WDGM_LOCAL_STATUS_OK</DEFAULT-VALUE>
													<LITERALS>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="6d3a6dcf-424c-4308-85c4-51d172d78a23">
															<SHORT-NAME>WDGM_LOCAL_STATUS_DEACTIVATED</SHORT-NAME>
														</ECUC-ENUMERATION-LITERAL-DEF>
														<ECUC-ENUMERATION-LITERAL-DEF UUID="599b0207-f31b-4f06-a0d8-40feab375c4f">
															<SHORT-NAME>WDGM_LOCAL_STATUS_OK</SHORT-NAME>
														</ECUC-ENUMERATION-LITERAL-DEF>
													</LITERALS>
												</ECUC-ENUMERATION-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="dbaaf237-fcfe-4d0e-a567-8fdd590b64f0">
													<SHORT-NAME>WdgMFailedDeadlineRefCycleTol</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter contains the acceptable amount of violated deadlines for this Supervised Entity.

Note:
The AUTOSAR implementation can be simulated for deadline violations with:
WdgMDeadlineReferenceCycle = WdgMFailedDeadlineRefCycleTol = 0</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:DefaultFormat">DEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>65534</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="*************-433e-9ae3-302a69463d7c">
													<SHORT-NAME>WdgMDeadlineReferenceCycle</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter contains the amount of supervision cycles to be used as reference by the deadline supervisions of this SE.

Note:
The AUTOSAR implementation can be simulated for deadline violations with:
WdgMDeadlineReferenceCycle = WdgMFailedDeadlineRefCycleTol = 0</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:DefaultFormat">DEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="aacd8d0f-b112-4136-b5aa-71b245ec77be">
													<SHORT-NAME>WdgMFailedProgramFlowRefCycleTol</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter contains the acceptable amount of program flow violations for this Supervised Entity.

Note:
The AUTOSAR implementation can be simulated for program flow violations with:
WdgMProgramFlowReferenceCycle = WdgMFailedProgramFlowRefCycleTol = 0</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:DefaultFormat">DEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>65534</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
												<ECUC-INTEGER-PARAM-DEF UUID="8e0e4ba9-4606-45d1-a855-a7ca85cef560">
													<SHORT-NAME>WdgMProgramFlowReferenceCycle</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter contains the amount of supervision cycles to be used as reference by the program flow supervisions of this SE.

Note:
The AUTOSAR implementation can be simulated for program flow violations with:
WdgMProgramFlowReferenceCycle = WdgMFailedProgramFlowRefCycleTol = 0</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:DefaultFormat">DEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>Vector Informatik</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>0</DEFAULT-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
											<REFERENCES>
												<!-- Symbolic Name Reference Definition: WdgMLocalStatusSupervisedEntityRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="f347f1b1-b1c9-454e-8d3f-045f2daf60e5">
													<SHORT-NAME>WdgMLocalStatusSupervisedEntityRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">Reference to the Supervised Entity for which the parameters of this container are set.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/WdgM/WdgMGeneral/WdgMSupervisedEntity</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</SUB-CONTAINERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
						<!-- Parameter Container Definition: WdgMGeneral -->
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="e7a76712-0412-499a-b9ab-8dcbcd038a04">
							<SHORT-NAME>WdgMGeneral</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Container structures all general configuration parameters of the Watchdog Manager.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
							<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
							<PARAMETERS>
								<!-- PARAMETER DEFINITION: WdgMDevErrorDetect -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="1d802c58-233d-4981-89c1-d825e51c03ab">
									<SHORT-NAME>WdgMDevErrorDetect</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Preprocessor switch to enable/disable development error detection and reporting.
Shall be used to remove unneeded code segments regarding DET features

True: Development error detection is enabled
False: Development error detection is disabled</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: WdgMDemReport -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="50e42660-bc0c-4757-9cf7-f0b3d98a936b">
									<SHORT-NAME>WdgMDemReport</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Preprocessor switch to enable / disable calls to DEM in case of production error detection.

True: DEM calls enabled in case of production errors
False: DEM calls disabled in case of production errors</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: WdgMImmediateReset -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="d425bde4-b8cf-437c-9958-049448b4b187">
									<SHORT-NAME>WdgMImmediateReset</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This preprocessor switch enables/disablse the immediate reset feature in case of alive, deadline or program flow failure.

True: Immediate reset is enabled
False: Immediate reset is disabled</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: WdgMOffModeEnabled -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="119efb42-de08-420e-86b6-b7437348ee83">
									<SHORT-NAME>WdgMOffModeEnabled</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This preprocessor switch enables/disables the selection of the "OffMode" of the watchdog driver.

True: "OffMode" selection is allowed
False: "OffMode" selection is disallowed</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: WdgMVersionInfoApi -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="*************-44c2-9636-d64c45e453fb">
									<SHORT-NAME>WdgMVersionInfoApi</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Preprocessor switch to enable/disable the existence of the API WdgM_GetVersionInfo. Shall be used to remove unneeded code segments.

True: API is enabled
False: API is disabled</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: WdgMDefensiveBehavior -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="39bf3444-8c8c-4a56-aa61-3a20ee0559f1">
									<SHORT-NAME>WdgMDefensiveBehavior</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Preprocessor switch to enable/disable the defensive behavior of the Watchdog Manager module.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="DCE:f45351c2-0460-419e-8479-b17297e90c92">
									<SHORT-NAME>WdgMUseRte</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This preprocessor switch instructs the Watchdog Manager to use the by Rte generated defines and typedefs.

True: the WdgM uses the by Rte generated defines and typedefs
False: the WdgM uses own defines and typedefs</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="84a70523-8e81-451f-a03c-6463a89c00a3">
									<SHORT-NAME>WdgMDemSupervisionReport</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Preprocessor switch to enable / disable call to DEM in case the Safe Watchdog Manager has the WDGM_GLOBAL_STATE_STOPPED reached.

True: The DEM call is enabled
False: The DEM call is disabled</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="2ee41282-ee51-4429-8b93-433fad17241c">
									<SHORT-NAME>WdgMFirstCycleAliveCounterReset</SHORT-NAME>
									<DESC>
										<L-2 L="EN">When this preprocessor switch is enabled, then the Alive counters in the first Supervision cycle are not evaluated
                    </L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="6b4b203b-5f93-4d15-a048-69a010df04b5">
									<SHORT-NAME>WdgMAutosarDebugging</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Preprocessor switch to enable/disable the compilation of AUTOSAR debugging features, such as WdgM_GetFirstExpiredSEViolation.

True: AUTOSAR debugging is enabled
False: AUTOSAR debugging is disabled</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: WdgMUseOsSuspendInterrupt -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="ab1e808c-42d5-4448-82ef-0e5f1d32eef7">
									<SHORT-NAME>WdgMUseOsSuspendInterrupt</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This preprocessor switch enables/disables the usage of OS suspend and resume interrupt services within WdgM.

Note: Suspending and Resuming interrupts is done by SchM. Therefore, if this option is enabled, SchM_WdgM.h is included and not Os.h. Normally SchM_WdgM.h is generated by Rte. If so, also enable the parameter / option WdgMUseRte.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: WdgMTimebaseSource -->
								<ECUC-ENUMERATION-PARAM-DEF UUID="e9be11ab-f793-409c-a1c4-a42cf10d194b">
									<SHORT-NAME>WdgMTimebaseSource</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter selects the Tick counter source for Deadline monitoring.

WDGM_INTERNAL_SOFTWARE_TICK: The counter is incremented each time `WdgM_MainFunction` is called.
WDGM_OS_COUNTER_TICK: The timebase is based on the Os hardware counter.
WDGM_EXTERNAL_TICK: The counter is incremented each time `WdgM_UpdateTickCount` is called.
                    </L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>WDGM_INTERNAL_SOFTWARE_TICK</DEFAULT-VALUE>
									<LITERALS>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="a844aac6-4de1-4a64-9261-52d31bcfcd77">
											<SHORT-NAME>WDGM_INTERNAL_SOFTWARE_TICK</SHORT-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="069184aa-fff1-4219-8242-30fb0e86f0be">
											<SHORT-NAME>WDGM_OS_COUNTER_TICK</SHORT-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="9cb694b7-3d66-4416-99a4-fca1afd51ff8">
											<SHORT-NAME>WDGM_EXTERNAL_TICK</SHORT-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
									</LITERALS>
								</ECUC-ENUMERATION-PARAM-DEF>
								<!-- PARAMETER DEFINITION: WdgMSecondResetPath -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="f24ee5b0-f655-4e9d-be82-fe94774d4631">
									<SHORT-NAME>WdgMSecondResetPath</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This preprocessor switch generally enables the access to the external Mcu_PerformReset() function. This function  is used as second reset path a specially when the primary reset path signals failure.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: WdgMTickOverrunCorrection -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="e84b56de-5558-4890-b2c3-57ec1dc6403c">
									<SHORT-NAME>WdgMTickOverrunCorrection</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This preprocessor switch enables/disables 32 bit timebase counter overflow detection and correction in case of WdgMTimebaseSource is selected to be WDGM_EXTERNAL_TICK.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: WdgMEntityDeactivationEnabled -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="42423127-45e3-407f-ba7d-a4f66b07c929">
									<SHORT-NAME>WdgMEntityDeactivationEnabled</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This preprocessor switch enables generally entity deactivation. Please see additional important safety information regarding this functionality in the WdgM Safety Manual.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: WdgMStateChangeNotification -->
								<!-- PARAMETER DEFINITION: WdgMUseOsCoreIdentification -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="ab1e818c-42d5-4348-82ef-0e3f1d32eef7">
									<SHORT-NAME>WdgMUseOsCoreIdentification</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This preprocessor switch enables/disables the usage of the OS GetCoreID() service.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="8621e4b1-e8be-4348-b0e2-b42ff416116b">
									<SHORT-NAME>WdgMGenerateCPIdAsPortDefinedArgument</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This preprocessor switch enables/disables the generation of a port per checkpoint where the checkpoint id is added as port-defined argument.

True: A port is generated in the software component (swc) for each checkpoint of a supervised entity with both supervised entity id and checkpoint id as port-defined arguments
False: A port is generated in the software component (swc) only for each supervised entity with the supervised entity id as port-defined argument. The checkpoint id has to be added in the function call manually.

Note: To obtain AUTOSAR behavior this parameter has to be set to false.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-ENUMERATION-PARAM-DEF UUID="010babfb-75ea-4160-a011-a2004e419941">
									<SHORT-NAME>WdgMStatusReportingMechanism</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter selects which mechansim should be applied for status reporting.

WDGM_USE_MODE_SWITCH_PORTS: ModeSwitchPorts are used to report a status change.
WDGM_USE_NOTIFICATIONS: Notifications are used to report a status change.
WDGM_USE_NO_STATUS_REPORTING: No reporting mechanism of a status change is used.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>WDGM_USE_MODE_SWITCH_PORTS</DEFAULT-VALUE>
									<LITERALS>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="d9cb4f26-67d4-4057-900c-fdbbfe822d26">
											<SHORT-NAME>WDGM_USE_MODE_SWITCH_PORTS</SHORT-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="2fca238d-c0d8-40fb-90b9-71b5ee22c882">
											<SHORT-NAME>WDGM_USE_NOTIFICATIONS</SHORT-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="d2d0dc7a-e85b-46b1-9516-22e2a4ac596a">
											<SHORT-NAME>WDGM_USE_NO_STATUS_REPORTING</SHORT-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
									</LITERALS>
								</ECUC-ENUMERATION-PARAM-DEF>
							</PARAMETERS>
							<SUB-CONTAINERS>
								<!-- Parameter Container Definition: WdgMSupervisedEntity -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="1f1b7f90-09ce-418d-9914-9fa978eaa8b9">
									<SHORT-NAME>WdgMSupervisedEntity</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container collects all common (mode-independent) parameters of a Supervised Entity to be supervised by the Watchdog Manager.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>65535</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: WdgMSupervisedEntityId -->
										<ECUC-INTEGER-PARAM-DEF UUID="5d11b234-1da9-499e-9813-61d8b6ea146d">
											<SHORT-NAME>WdgMSupervisedEntityId</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter shall contain the unique identifier of the supervised entity.

All supervised entity ids must form a zero-based list of integers.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="MSR:Process">
														<SD GID="MSR:TraceRef">SPEC-2068326</SD>
													</SDG>
													<SDG GID="DV:Display">
														<SD GID="DV:DefaultFormat">DEC</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>65535</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: OSApplication -->
										<!-- PARAMETER DEFINITION: WdgMEnableEntityDeactivation -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="8b5262a5-986b-4625-ac67-502059a51c25">
											<SHORT-NAME>WdgMEnableEntityDeactivation</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Enables deactivation of this supervised enitiy.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: WdgMSupportedAutosarAPI -->
										<!-- PARAMETER DEFINITION: WdgMLocalStateChangeCbk -->
										<ECUC-FUNCTION-NAME-DEF UUID="90266c73-7e46-465a-97f7-afd37e16fd1d">
											<SHORT-NAME>WdgMLocalStateChangeCbk</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Callback function for notifying the supervised entity's status change.

Note:
- This parameter can only be used if WdgMStatusReportingMechanism is set to WDGM_USE_NOTIFICATIONS.
- This parameter can only be used if parameter WdgMReportStatusViaRte of this supervised entity is set false</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
										</ECUC-FUNCTION-NAME-DEF>
										<ECUC-BOOLEAN-PARAM-DEF UUID="504bd038-9420-4668-9f80-7eb011cde650">
											<SHORT-NAME>WdgMReportStatusViaRte</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter is used to enable the status reporting via Rte.

Notes:
- This parameter can only be set true if WdgMStatusReportingMechanism is _not_ WDGM_USE_NO_STATUS_REPORTING
- If this parameter is set true in any of the supervised entities or in WdgMMode, the global switch WdgMUseRte has to be set true, too.
- This parameter cannot be set true if global switch WdgMUseRte is set false.
- If this parameter is set true, the optional parameter WdgMLocalStateChangeCbk must not exist.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Symbolic Name Reference Definition: WdgMInternalCheckpointFinalRef -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="d17ccb90-6fd6-486f-9928-ef51b47de4a5">
											<SHORT-NAME>WdgMInternallCheckpointFinalRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">A reference to the final local Checkpoint of this supervised entity. Note that there might be several final local checkpoints.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>65535</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/WdgM/WdgMGeneral/WdgMSupervisedEntity/WdgMCheckpoint</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
										<!-- Symbolic Name Reference Definition: WdgMInternalCheckpointInitialRef -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="e9e3a78f-46a7-4642-8c33-6d47199acb38">
											<SHORT-NAME>WdgMInternalCheckpointInitialRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">A reference to the initial local Checkpoint of this supervised entity.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/WdgM/WdgMGeneral/WdgMSupervisedEntity/WdgMCheckpoint</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
										<!-- Symbolic Name Reference Definition: WdgMAppTaskRef -->
										<ECUC-CHOICE-REFERENCE-DEF UUID="d0537c3a-7739-487f-b445-0b49b715ea47">
											<SHORT-NAME>WdgMAppTaskRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to an OS Application. In case of OS SC3 the local data of the supervised entity should be placed in the same memory segment as the application this supervised entity is part of.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REFS>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsApplication</DESTINATION-REF>
											</DESTINATION-REFS>
										</ECUC-CHOICE-REFERENCE-DEF>
									</REFERENCES>
									<SUB-CONTAINERS>
										<!-- Parameter Container Definition: WdgMCheckpoint -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="4162e1c3-29b2-4714-a81a-f529b92aa19a">
											<SHORT-NAME>WdgMCheckpoint</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container collects all Checkpoints of this Supervised Entity.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>65535</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<PARAMETERS>
												<!-- PARAMETER DEFINITION: WdgMCheckpointId -->
												<ECUC-INTEGER-PARAM-DEF UUID="822fbd5d-93cf-46bb-9592-aec82b80e415">
													<SHORT-NAME>WdgMCheckpointId</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This parameter shall contain the unique identifier of checkpoint within a supervised entity.

All checkpoint ids must form a zero-based list of integers.</L-2>
													</DESC>
													<ADMIN-DATA>
														<SDGS>
															<SDG GID="DV:Display">
																<SD GID="DV:DefaultFormat">DEC</SD>
															</SDG>
														</SDGS>
													</ADMIN-DATA>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
													<DEFAULT-VALUE>65535</DEFAULT-VALUE>
													<MAX>65535</MAX>
													<MIN>0</MIN>
												</ECUC-INTEGER-PARAM-DEF>
											</PARAMETERS>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
										<!-- Parameter Container Definition: WdgMInternalTransition -->
										<ECUC-PARAM-CONF-CONTAINER-DEF UUID="9c3078e8-0991-495d-912a-b176954bf595">
											<SHORT-NAME>WdgMInternalTransition</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This container defines the graph of Internal Transitions within this Supervised Entity.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:postBuildSelectableChangeable">false</SD>
														<SD GID="DV:postBuildNotDeletable">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>65535</UPPER-MULTIPLICITY>
											<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
											<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
											<REFERENCES>
												<!-- Symbolic Name Reference Definition: WdgMInternalTransitionDestRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="6295dfa9-ae85-4892-b425-dc5345974767">
													<SHORT-NAME>WdgMInternalTransitionDestRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This is the reference to the destination Checkpoint of an Internal Transition within this Supervised Entity.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/WdgM/WdgMGeneral/WdgMSupervisedEntity/WdgMCheckpoint</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
												<!-- Symbolic Name Reference Definition: WdgMInternalTransitionSourceRef -->
												<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="587857ad-a04b-4270-afe9-62afbb02c92f">
													<SHORT-NAME>WdgMInternalTransitionSourceRef</SHORT-NAME>
													<DESC>
														<L-2 L="EN">This is the reference to the source Checkpoint of an Internal Transition within this Supervised Entity.</L-2>
													</DESC>
													<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
													<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
													<IMPLEMENTATION-CONFIG-CLASSES>
														<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
															<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
															<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
														</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													</IMPLEMENTATION-CONFIG-CLASSES>
													<ORIGIN>AUTOSAR_ECUC</ORIGIN>
													<REQUIRES-INDEX>false</REQUIRES-INDEX>
													<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/WdgM/WdgMGeneral/WdgMSupervisedEntity/WdgMCheckpoint</DESTINATION-REF>
												</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
											</REFERENCES>
										</ECUC-PARAM-CONF-CONTAINER-DEF>
									</SUB-CONTAINERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Parameter Container Definition: WdgMWatchdog -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="97ffb261-94c1-4c06-b9ee-db4b2438f249">
									<SHORT-NAME>WdgMWatchdog</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container collects all common (mode-independent) parameters of a Watchdog to be triggered by the Watchdog Manager.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER DEFINITION: WdgMWatchdogName -->
										<ECUC-STRING-PARAM-DEF UUID="34b7583c-0063-41bd-8163-d37122ebec3c">
											<SHORT-NAME>WdgMWatchdogName</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter is a symbolic name of the Watchdog. Is used as comment only.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
										</ECUC-STRING-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<!-- Symbolic Name Reference Definition: WdgMWatchdogDeviceRef -->
										<ECUC-SYMBOLIC-NAME-REFERENCE-DEF UUID="18932417-a0b0-47d5-aa4f-779f42f86cd4">
											<SHORT-NAME>WdgMWatchdogDeviceRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to one device container of Watchdog Interface. In the referenced container WdgIfDevice, the parameter WdgIfDeviceIndex contains the Index parameter that WdgM has to use for WdgIf_SetTriggerCondition calls for that watchdog instance.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/WdgIf/WdgIfDevice</DESTINATION-REF>
										</ECUC-SYMBOLIC-NAME-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="da6a51af-e6c2-4999-b39a-75ae5f68c2f1">
									<SHORT-NAME>WdgMCallerIds</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Contains the definition of valid CallerIds for the callers who have permission to call the function WdgM_SetMode
                    </L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- PARAMETER-DEFINITION: WdgMCallerId -->
										<ECUC-INTEGER-PARAM-DEF UUID="af28aff6-ff31-4716-a35b-c1477327216f">
											<SHORT-NAME>WdgMCallerId</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter defines one valid CallerId for the callers who have permission to
 call the function WdgM_SetMode.
                        </L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:Display">
														<SD GID="DV:DefaultFormat">DEC</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>255</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>AUTOSAR_ECUC</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</SUB-CONTAINERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
					</CONTAINERS>
				</ECUC-MODULE-DEF>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="68f4e362-c89d-450c-905d-79fcedbb9a6c">
					<SHORT-NAME>WdgM_Pre</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/WdgM</DEFINITION-REF>
					<CONTAINERS></CONTAINERS>
				</ECUC-MODULE-CONFIGURATION-VALUES>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="789d2022-f9dc-404b-9013-5f6651060f9f">
					<SHORT-NAME>WdgM_Rec</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/WdgM</DEFINITION-REF>
					<CONTAINERS></CONTAINERS>
				</ECUC-MODULE-CONFIGURATION-VALUES>
			</ELEMENTS>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>