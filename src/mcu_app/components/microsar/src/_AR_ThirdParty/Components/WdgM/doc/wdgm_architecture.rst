Architecture
==========================================================
wdg static design
----------------------------------------------------

Component Diagram
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

This section displays the static component diagram of the wdg module：

.. uml::
   :caption: wdg component diagram
   :align: center
   
   title static_wdg_module


   package "FUSA" {
	   frame "app_functions"{
         component FUSA_func  [ 
             fusasm_runnable.cpp
         ] 
      }
	  
   }
   
   
   package "wdgif" {
	   frame "wdgif_functions"{
         component wdgif_func  [ 
                     WdgIf.c
         ] 
      }
	  
   }
   
   package "wdgm"  {
	   frame "wdgm_functions"{
         component wdgm_func [
            WdgM.c
         ]
      }
	  
   }

   package "wdg"  {
	   frame "wdg_functions"{
         component wdg_func  [
            Wdg.c
         ]
      }
	  
   }

 
   [wdgm_func] --> [wdgm_func] : WdgM_MainFunction()

   [FUSA_func] -right-> [wdgm_func]: WdgM_CheckpointReached()

   [wdgm_func] -down-> [wdgif_func] : WdgM_PerformReset()

   [wdgif_func] -right-> [wdg_func] :   WdgIf_SetTriggerCondition(0,0) 

   [wdg_func] --> [wdg_func] : Wdg_lTrigger()




wdg sequence design
----------------------------------------------------

Sequence Diagram
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

This section displays the dynamic Sequence diagram of the wdg module：


.. uml::
   :caption: wdg sequence diagram
   :align: center

   title sequence_wdg_module

  
   '这是注释
   '先列出涉及到的角色
   'Start 部分
   participant FUSA 
   participant wdgm    

   'FSE_Start 部分
   participant wdgif   
   participant wdg     

   '[->run : task 10ms

   'autonumber 1

   FUSA -> wdg : Wdg_Init
   wdg --> FUSA

   FUSA -> wdgm : WdgM_Init

   wdgm -> wdgif  : WdgIf_SetTriggerCondition
   wdgif -> wdg : Wdg_SetTriggerCondition
   wdg -->wdgif
   wdgif --> wdgm

   wdgm -> wdgif  : WdgIf_SetMode
   wdgif -> wdg : Wdg_SetMode
   wdg --> wdgif
   wdgif-->wdgm

   wdgm -> wdgm : WdgM_MainFunction

   FUSA -> wdgm : WdgM_CheckpointReached
   wdgm -->FUSA

wdg dynamic design
----------------------------------------------------

Activity Diagram
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

This section displays the dynamic Activity diagram of the wdg module：


.. uml::
   :caption: Overview of Watchdog Manager Supervision
   :align: center

   title dynamic_wdg_module

  
   |SE context|
   start
   -> wdgM_CheckointReached();
   :Increment Alive
   Indication Counter of
   Checkpoint;
   :Deadline Supervision of
   SE (early arrivals and
   delays);
   :Logical Supervision of SE;
   stop



   |os scheduler context|
   start
   -> WdgM_MainFunction();
   :Alive Supervision of SE;
   :Determine the Local Supervision
   Status of SE;
   :Determine Global Supervision Status;
   :Handle errors;
   :Set condition for HW watchdog
   triggering;


   stop
   