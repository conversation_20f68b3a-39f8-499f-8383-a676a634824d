<?xml version="1.0" encoding="UTF-8"?>
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://autosar.org/schema/r4.0">
	<AR-PACKAGES>
		<AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
			<SHORT-NAME>MICROSAR</SHORT-NAME>
			<ELEMENTS>
				<BSW-IMPLEMENTATION UUID="ad84c620-a803-42c5-8183-2f6ad36564c7">
					<SHORT-NAME>WdgIf_Impl</SHORT-NAME>
					<PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
					<SW-VERSION>5.04.00</SW-VERSION>
					<USED-CODE-GENERATOR>DaVin<PERSON> Configurator</USED-CODE-GENERATOR>
					<VENDOR-ID>30</VENDOR-ID>
					<AR-RELEASE-VERSION>4.02.02</AR-RELEASE-VERSION>
					<PRECONFIGURED-CONFIGURATION-REFS>
						<PRECONFIGURED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/WdgIf_Pre</PRECONFIGURED-CONFIGURATION-REF>
					</PRECONFIGURED-CONFIGURATION-REFS>
					<RECOMMENDED-CONFIGURATION-REFS>
						<RECOMMENDED-CONFIGURATION-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/MICROSAR/WdgIf_Rec</RECOMMENDED-CONFIGURATION-REF>
					</RECOMMENDED-CONFIGURATION-REFS>
					<VENDOR-SPECIFIC-MODULE-DEF-REFS>
						<VENDOR-SPECIFIC-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/MICROSAR/WdgIf</VENDOR-SPECIFIC-MODULE-DEF-REF>
					</VENDOR-SPECIFIC-MODULE-DEF-REFS>
				</BSW-IMPLEMENTATION>
				<ECUC-MODULE-DEF UUID="ECUC:a2dc5f81-f4d5-4986-bf4b-38652d4a8126">
					<SHORT-NAME>WdgIf</SHORT-NAME>
					<DESC>
						<L-2 L="EN">Configuration of the WdgIf (Watchdog Interface) module.</L-2>
					</DESC>
					<CATEGORY>VENDOR_SPECIFIC_MODULE_DEFINITION</CATEGORY>
					<ADMIN-DATA>
						<DOC-REVISIONS>
							<DOC-REVISION>
								<REVISION-LABEL>6.00.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virchl</ISSUED-BY>
								<DATE>2016-06-16T01:55:18+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Initial version with MICROSAR root</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.00.01</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virchl</ISSUED-BY>
								<DATE>2016-10-21T07:46:14+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SwVersion set to 5.1.1</L-2>
										</CHANGE>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">VendorId fixed to 30</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.01.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virchl</ISSUED-BY>
								<DATE>2017-01-04T03:41:23+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SwVersion set to 5.2.0</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00093505</L-2>
										</REASON>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Parameters WdgIfDeviceIncludeFile, WdgIfDeviceSetMode and WdgIfDeviceSetTriggerCondition added</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">FEATC-922</L-2>
										</REASON>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Parameters WdgIfUseAutosarDrvApi (also pre config), WdgIfInternalTickCounterRef and WdgIfStateCombinerUseManualMode deleted</L-2>
										</CHANGE>
									</MODIFICATION>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Default value and min value of WdgIfStateCombinerReferenceCycle set to 1</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.01.01</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virchl</ISSUED-BY>
								<DATE>2018-05-29T09:01:46+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Unify AUTOSAR version in component</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">STORYC-5328</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.02.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>virchl</ISSUED-BY>
								<DATE>2018-11-09T13:56:36+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">SwVersion set to 5.3.0</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">STORYC-5023</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.03.00</REVISION-LABEL>
								<STATE>released</STATE>
								<ISSUED-BY>vircre</ISSUED-BY>
								<DATE>2019-08-06T01:00:02+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">WdgIf shall support at least 6 cores</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">MWDG-123</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
						</DOC-REVISIONS>
					</ADMIN-DATA>
					<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
					<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
					<REFINED-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/WdgIf</REFINED-MODULE-DEF-REF>
					<SUPPORTED-CONFIG-VARIANTS>
						<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
					</SUPPORTED-CONFIG-VARIANTS>
					<CONTAINERS>
						<!-- Parameter Container Definition: WdgIfDevice -->
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:b5418b77-eccf-4831-acdd-b50dbb01413b">
							<SHORT-NAME>WdgIfDevice</SHORT-NAME>
							<DESC>
								<L-2 L="EN">It contains the information for the selection of a particular Watchdog device in case multiple Watchdog drivers are connected.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
							<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
							<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
							<PARAMETERS>
								<!-- PARAMETER DEFINITION: WdgIfDeviceIndex -->
								<ECUC-INTEGER-PARAM-DEF UUID="ECUC:41e5cd64-56e8-4593-b945-b615918cd5a7">
									<SHORT-NAME>WdgIfDeviceIndex</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Represents the watchdog Device ID so that it can be referenced by the watchdog manager.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>255</DEFAULT-VALUE>
									<MAX>255</MAX>
									<MIN>0</MIN>
								</ECUC-INTEGER-PARAM-DEF>
								<ECUC-STRING-PARAM-DEF UUID="3921e479-8a07-47fe-9bf8-471577f93ca8">
									<SHORT-NAME>WdgIfDeviceIncludeFile</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter contains the file name of the include file of the referenced watchdog device.

If the information is present in the referenced watchdog driver's description file, this parameter is filled automatically by selecting the device reference 'WdgIfDriverRef'. In case this field stays empty or another include file is required, please enter the include file manually.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
								</ECUC-STRING-PARAM-DEF>
								<ECUC-FUNCTION-NAME-DEF UUID="adce0337-34f6-4a56-90ac-305fe54f988c">
									<SHORT-NAME>WdgIfDeviceSetMode</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter contains the function name of the 'WdgIf_SetMode()'-service of the referenced watchdog device.

If the information is present in the referenced watchdog driver's description file, this parameter is filled automatically by selecting the device reference 'WdgIfDriverRef'. In case this field stays empty or another include file is required, please enter the function name manually.

The function to be called has to have a signature equal to the according AUTOSAR 4 API service specified in the Watchdog Driver:

void Wdg_SetMode(WdgIf_ModeType Mode) </L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
								</ECUC-FUNCTION-NAME-DEF>
								<ECUC-FUNCTION-NAME-DEF UUID="b227f688-2379-41a0-b86e-bfa29240397c">
									<SHORT-NAME>WdgIfDeviceSetTriggerCondition</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter contains the function name of the 'WdgIf_SetTriggerCondition()'-service of the referenced watchdog device.

If the information is present in the referenced watchdog driver's description file, this parameter is filled automatically by selecting the device reference 'WdgIfDriverRef'. In case this field stays empty or another include file is required, please enter the function name manually.

The function to be called has to have a signature equal to the according AUTOSAR 4 API service specified in the Watchdog Driver:

void Wdg_SetTriggerCondition(uint16 timeout) </L-2>
									</DESC>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
								</ECUC-FUNCTION-NAME-DEF>
							</PARAMETERS>
							<REFERENCES>
								<!-- Symbolic Name Reference Definition: WdgIfDriverRef -->
								<ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:c18b762d-1ffc-4203-9936-4e4539c6fea6">
									<SHORT-NAME>WdgIfDriverRef</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Reference to the watchdog driver of this watchdog device or to a master or slave State Combiner instance.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<DESTINATION-REFS>
										<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Wdg/WdgGeneral</DESTINATION-REF>
										<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/WdgIf/WdgIfStateCombiner/WdgIfStateCombinerMaster</DESTINATION-REF>
										<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/WdgIf/WdgIfStateCombiner/WdgIfStateCombinerSlave</DESTINATION-REF>
									</DESTINATION-REFS>
								</ECUC-CHOICE-REFERENCE-DEF>
							</REFERENCES>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
						<!-- Parameter Container Definition: WdgIfGeneral -->
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:2199b96d-33aa-488f-a1ae-99f6d4bd3c6a">
							<SHORT-NAME>WdgIfGeneral</SHORT-NAME>
							<DESC>
								<L-2 L="EN">This container collects all generic watchdog interface parameters.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
							<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
							<PARAMETERS>
								<!-- PARAMETER DEFINITION: WdgIfDevErrorDetect -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:4363aa34-c419-4350-b2cb-75ec7dfda67f">
									<SHORT-NAME>WdgIfDevErrorDetect</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Pre-processor switch for enabling the development error reporting.

true: Development error reporting enabled
false: Development error reporting disabled</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- PARAMETER DEFINITION: WdgIfVersionInfoApi -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:313c10c6-c907-4f72-a6d8-722b8886775b">
									<SHORT-NAME>WdgIfVersionInfoApi</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Pre-processor switch to enable / disable the service returning the version information.

true: Version information service enabled
false: Version information service disabled</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>AUTOSAR_ECUC</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<!-- StateCombiner Enable   -->
								<!-- Symbolic Name Reference Definition: WdgIfUseStateCombiner   -->
								<ECUC-BOOLEAN-PARAM-DEF UUID="b378669d-ebe0-4233-8fd6-bd9637f32e6b">
									<SHORT-NAME>WdgIfUseStateCombiner</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Pre-processor switch for enabling the usage of the State Combiner feature, which is part of the WdgIf. If enabled, it allows WdgM instances running on different processor cores to share and trigger one watchdog device.

true: WdgIf State Combiner is enabled and must be configured.
false: WdgIf State Combiner is disabled.</L-2>
									</DESC>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
							</PARAMETERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="161c3800-7b98-4fcc-909c-625fadc351ef">
							<SHORT-NAME>WdgIfStateCombiner</SHORT-NAME>
							<DESC>
								<L-2 L="EN">This container describes the optinal feature of the Safe Watchdog Interface - the State Combiner.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
							<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
							<SUB-CONTAINERS>
								<!-- Parameter Container Definition: WdgIfStateCombinerGeneral -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:d3088cb2-0610-4981-bede-0f4539127001">
									<SHORT-NAME>WdgIfStateCombinerGeneral</SHORT-NAME>
									<DESC>
										<L-2 L="EN">All general parameters of the StateCombiner are collected here.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<!-- SPECIFIC STATE COMBINER PARAMETERS -->
										<!-- PARAMETER DEFINITION: WdgIfStateCombinerUseOsSpinlock -->
										<ECUC-BOOLEAN-PARAM-DEF UUID="3a2855dd-871c-444c-aee9-2c12fd5f9d8b">
											<SHORT-NAME>WdgIfStateCombinerUseOsSpinlock</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This preprocessor switch enables/disables the usage of OS spinlock mechanism. 

Note: If no OS spinlock is used a user-defined spinlock must be implemented - the file Appl_Spinlock.h will be inlcuded (instead of Os.h).</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>true</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
										<!-- PARAMETER DEFINITION: WdgIfStateCombinerSpinlockID -->
										<ECUC-INTEGER-PARAM-DEF UUID="df0f3704-27f5-4123-afc8-9fed0c812cec">
											<SHORT-NAME>WdgIfStateCombinerSpinlockID</SHORT-NAME>
											<DESC>
												<L-2 L="EN">ID of the spinlock used by the StateCombiner for inter-core synchronization.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>4294967295</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- PARAMETER DEFINITION: WdgIfStateCombinerStartUpSyncCycles -->
										<ECUC-INTEGER-PARAM-DEF UUID="e17b5cfd-f237-4306-9ba8-e572df587147">
											<SHORT-NAME>WdgIfStateCombinerStartUpSyncCycles</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Number of master cycles during start-up in which the master does not check the slave triggering. Used to synchronize master and slave during start-up.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<!-- / SPECIFIC STATE COMBINER PARAMETERS -->
									</PARAMETERS>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Parameter Container Definition: WdgIfStateCombinerMaster -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="9fedf590-c93a-4244-b281-83a5d279a9f8">
									<SHORT-NAME>WdgIfStateCombinerMaster</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Parameters for the State Combiner master device.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<REFERENCES>
										<ECUC-CHOICE-REFERENCE-DEF UUID="29660165-6bd3-4bda-881e-01e75b971d4e">
											<SHORT-NAME>WdgIfGlobalMemoryAppTaskRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to an OS Application. In case of OS SC3 the global variables of this State Combiner master device module should be placed in the same memory segment as the application that the overlying Watchdog Manager module is part of.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REFS>
												<!-- issue45280: only Applications must be selected <DESTINATION-REF DEST="PARAM-CONF-CONTAINER-DEF">/AUTOSAR/Os/OsTask</DESTINATION-REF> -->
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsApplication</DESTINATION-REF>
											</DESTINATION-REFS>
										</ECUC-CHOICE-REFERENCE-DEF>
										<!-- SPECIFIC STATE COMBINER PARAMETERS -->
										<!-- Symbolic Name Reference Definition: WdgIfStateCombinerMasterWdgRef -->
										<ECUC-REFERENCE-DEF UUID="2935aef6-734d-4d71-91c1-d37edc0dea15">
											<SHORT-NAME>WdgIfStateCombinerMasterWdgRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to the watchdog driver serviced by the StateCombiner master.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Wdg/WdgGeneral</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
										<!-- / SPECIFIC STATE COMBINER PARAMETERS -->
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<!-- Parameter Container Definition: WdgIfStateCombinerSlave -->
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:d3088cb2-0610-4a81-bede-0f4539127001">
									<SHORT-NAME>WdgIfStateCombinerSlave</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Parameters for the State Combiner slave.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">false</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>9</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>true</MULTIPLE-CONFIGURATION-CONTAINER>
									<PARAMETERS>
										<ECUC-INTEGER-PARAM-DEF UUID="4799060c-77c0-44e6-9aab-10d62da2f50a">
											<!-- PARAMETER DEFINITION: WdgIfStateCombinerReferenceCycle -->
											<SHORT-NAME>WdgIfStateCombinerReferenceCycle</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter specifies the reference cycle of the master for this slave.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>1</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>1</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="68de33f7-fbe8-4a5d-885a-23ebc9e471d8">
											<SHORT-NAME>WdgIfStateCombinerSlaveIncrementsMin</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter specifies the minimum allowed number of increments (triggers) for slave instance in one master check period.

Note: in synchronous mode WdgIfStateCombinerSlaveIncrementsMin is equal to WdgIfStateCombinerSlaveIncrementsMax.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
										<ECUC-INTEGER-PARAM-DEF UUID="6fc1fb14-aeeb-436b-a117-a82cf01783a0">
											<SHORT-NAME>WdgIfStateCombinerSlaveIncrementsMax</SHORT-NAME>
											<DESC>
												<L-2 L="EN">This parameter specifies the maximum allowed number of increments (triggers) for slave instance in one master check period.

Note: in synchronous mode WdgIfStateCombinerSlaveIncrementsMax is equal to WdgIfStateCombinerSlaveIncrementsMin.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>0</DEFAULT-VALUE>
											<MAX>65535</MAX>
											<MIN>0</MIN>
										</ECUC-INTEGER-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<ECUC-CHOICE-REFERENCE-DEF UUID="5ce282c9-71d2-4d44-a769-acad2946221b">
											<SHORT-NAME>WdgIfGlobalMemoryAppTaskRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Parameter not used.

Reference to an OS Application. In case of OS SC3 the global variables of this State Combiner slave device should be placed in the same memory segment as the application that the overlying Watchdog Manager module is part of.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REFS>
												<!-- issue45280: only Applications must be selected <DESTINATION-REF DEST="PARAM-CONF-CONTAINER-DEF">/AUTOSAR/Os/OsTask</DESTINATION-REF> -->
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsApplication</DESTINATION-REF>
											</DESTINATION-REFS>
										</ECUC-CHOICE-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</SUB-CONTAINERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
					</CONTAINERS>
				</ECUC-MODULE-DEF>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="28184d7b-e19b-467f-b69d-57195ed2cbe6">
					<SHORT-NAME>WdgIf_Pre</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/WdgIf</DEFINITION-REF>
					<IMPLEMENTATION-CONFIG-VARIANT>VARIANT-PRE-COMPILE</IMPLEMENTATION-CONFIG-VARIANT>
					<CONTAINERS></CONTAINERS>
				</ECUC-MODULE-CONFIGURATION-VALUES>
				<ECUC-MODULE-CONFIGURATION-VALUES UUID="50672f52-7b42-4e19-82ce-8dc1298afa02">
					<SHORT-NAME>WdgIf_Rec</SHORT-NAME>
					<DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/WdgIf</DEFINITION-REF>
					<CONTAINERS></CONTAINERS>
				</ECUC-MODULE-CONFIGURATION-VALUES>
			</ELEMENTS>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>