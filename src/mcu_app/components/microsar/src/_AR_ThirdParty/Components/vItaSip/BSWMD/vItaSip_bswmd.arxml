<?xml version="1.0" encoding="UTF-8"?>
<!--
***********************************************************************************************************************
COPYRIGHT
===============================================================================
Copyright (c) 2019 by Vector Informatik GmbH.                                       All rights reserved.

    This software is copyright protected and proprietary to Vector Informatik GmbH.
    Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
    All other rights remain with Vector Informatik GmbH.
===============================================================================
FILE DESCRIPTION
===============================================================================
File:           vItaSip_bswmd.arxml
Module:         vItaSip

Description:    This is the module description of vItaSip.
*********************************************************************************************************************** 
-->
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-0-3.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://autosar.org/schema/r4.0">
	<AR-PACKAGES>
		<AR-PACKAGE UUID="ff55d029-386d-43a2-b3c2-dfef4c7a5e36">
			<SHORT-NAME>MICROSAR</SHORT-NAME>
			<ELEMENTS>
				<BSW-IMPLEMENTATION UUID="d7cc4baa-7d44-4399-8032-f72a038be15f">
					<SHORT-NAME>vItaSip_Impl</SHORT-NAME>
					<PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
					<SW-VERSION>6.00.00</SW-VERSION>
					<AR-RELEASE-VERSION>4.00.03</AR-RELEASE-VERSION>
					<VENDOR-SPECIFIC-MODULE-DEF-REFS>
						<VENDOR-SPECIFIC-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/MICROSAR/vItaSip</VENDOR-SPECIFIC-MODULE-DEF-REF>
					</VENDOR-SPECIFIC-MODULE-DEF-REFS>
				</BSW-IMPLEMENTATION>
				<ECUC-MODULE-DEF UUID="32a616ec-5d83-4ff8-ad6e-786b17399581">
					<SHORT-NAME>vItaSip</SHORT-NAME>
					<DESC>
						<L-2 L="EN">Configuration of the vItaSip (Vector IntegrationTest Application for Software Integration Package) module.</L-2>
					</DESC>
					<CATEGORY>VENDOR_SPECIFIC_MODULE_DEFINITION</CATEGORY>
					<ADMIN-DATA>
						<DOC-REVISIONS>
							<DOC-REVISION>
								<REVISION-LABEL>0.00.01</REVISION-LABEL>
								<ISSUED-BY>vispkg</ISSUED-BY>
								<DATE>2015-04-08T12:41:33+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Initial Version</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>0.01.00</REVISION-LABEL>
								<ISSUED-BY>vispkg</ISSUED-BY>
								<DATE>2015-05-04T12:24:16+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Complete Refactoring</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.00.00</REVISION-LABEL>
								<ISSUED-BY>vispkg</ISSUED-BY>
								<DATE>2015-05-18T03:04:53+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">First implemented version, reduced to Vitamin COM use case</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<ISSUED-BY>vispkg</ISSUED-BY>
								<DATE>2015-08-19T03:37:19+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Vitamin Com, Mem, Diag + Docu</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.00.01</REVISION-LABEL>
								<ISSUED-BY>vispkg</ISSUED-BY>
								<DATE>2015-09-10T08:57:32+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Introduced VitaComSignalGroups</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00085115</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.00.02</REVISION-LABEL>
								<ISSUED-BY>vishre</ISSUED-BY>
								<DATE>2015-11-11T10:01:12+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Support user implemented adapter SWC and CAPL code for control communication</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00086398</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.00.03</REVISION-LABEL>
								<ISSUED-BY>vishre</ISSUED-BY>
								<DATE>2015-12-10T03:19:08+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Support Ethernet for CANoeChannelRef</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00086508</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.00.04</REVISION-LABEL>
								<ISSUED-BY>vishre</ISSUED-BY>
								<DATE>2016-02-03T09:29:48+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Introduced test element VitaDcmDslProtocolRx</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00087821</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<ISSUED-BY>vispkg</ISSUED-BY>
								<DATE>2016-02-09T02:16:31+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Introduced vitamax configuration container</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">ESCAN00088175</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<ISSUED-BY>vispkg</ISSUED-BY>
								<DATE>2016-03-10T12:10:28+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Made Elementnames unique</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.03.00</REVISION-LABEL>
								<ISSUED-BY>vishre</ISSUED-BY>
								<DATE>2016-08-24T09:25:32+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Introduced VitaMinWdg</L-2>
										</CHANGE>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>1.04.00</REVISION-LABEL>
								<ISSUED-BY>vishre</ISSUED-BY>
								<DATE>2017-02-06T02:13:27+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Changed reference from VitaChannelRef to FrGeneral to FrIfController</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">WORKITEM14724</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<ISSUED-BY>vishre</ISSUED-BY>
								<DATE>2017-02-07T01:43:26+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Introduced VitaMinXcp</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">WORKITEM15121</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<ISSUED-BY>vishre</ISSUED-BY>
								<DATE>2017-02-20T04:06:31+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Introduced VitaMinJ1939Diag</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">WORKITEM15215</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>2.00.00</REVISION-LABEL>
								<ISSUED-BY>vishre</ISSUED-BY>
								<DATE>2017-04-06T02:47:32+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Adaption for cases without communication</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">WORKITEM15422</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<ISSUED-BY>vistun</ISSUED-BY>
								<DATE>2017-05-17T02:54:28+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Introduced VitaMinScc</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">WORKITEM15906</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<ISSUED-BY>vispkg</ISSUED-BY>
								<DATE>2017-05-31T09:33:21+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Introduced VitaMinMulticore + testelements</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">WORKITEM15973</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>2.01.00</REVISION-LABEL>
								<ISSUED-BY>vishre</ISSUED-BY>
								<DATE>2017-07-20T02:06:48+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Introduced VitaMinMemoryProtection</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">WORKITEM16341</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<ISSUED-BY>vispkg</ISSUED-BY>
								<DATE>2017-07-28T09:56:03+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Introduced VitaMinComSecOcSupport</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">WORKITEM16342</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>2.01.01</REVISION-LABEL>
								<ISSUED-BY>vistun</ISSUED-BY>
								<DATE>2017-10-17T08:01:51+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Enable VitaMinComSecOcSupport</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">WORKITEM4145</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>3.00.00</REVISION-LABEL>
								<ISSUED-BY>vispkg</ISSUED-BY>
								<DATE>2017-11-10T08:18:01+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Introduced IDM support</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">WORKITEM16820</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>3.01.00</REVISION-LABEL>
								<ISSUED-BY>vispkg</ISSUED-BY>
								<DATE>2018-02-27T10:36:21+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Introduced Task/Application mapping validation</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">WORKITEM17040</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<ISSUED-BY>vistun</ISSUED-BY>
								<DATE>2018-03-09T10:54:22+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Introduced SoAd CDD, modify Vita Generic Port Interface Parameter</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">WORKITEM17551</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>4.00.00</REVISION-LABEL>
								<ISSUED-BY>vispkg</ISSUED-BY>
								<DATE>2018-05-23T12:55:25+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Introduced VitaMinFbl</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">WORKITEM16955</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>4.01.00</REVISION-LABEL>
								<ISSUED-BY>vistun</ISSUED-BY>
								<DATE>2018-07-12T15:58:03+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Introduced VitaMinEth</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">WORKITEM17555</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<ISSUED-BY>visros</ISSUED-BY>
								<DATE>2018-08-16T15:47:08+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Changed description for VitaTaskToApplicationMapping</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">WORKITEM18585</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<ISSUED-BY>visros</ISSUED-BY>
								<DATE>2018-08-22T14:01:42+02:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Changed description for VitaControlSignals</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">WORKITEM18634</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>5.00.00</REVISION-LABEL>
								<ISSUED-BY>vislit</ISSUED-BY>
								<DATE>2019-01-16T12:27:55+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Changed description for VitaTaskToApplicationMapping</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">WORKITEM18996</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<ISSUED-BY>vislit</ISSUED-BY>
								<DATE>2019-02-11T14:27:17+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Removed unused parameters</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">WORKITEM19896</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>6.00.00</REVISION-LABEL>
								<ISSUED-BY>vislit</ISSUED-BY>
								<DATE>2019-03-15T10:17:02+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Rename to vItaSip and BSWMD redesign</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">FEAT-5560</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>7.00.00</REVISION-LABEL>
								<ISSUED-BY>vispkg</ISSUED-BY>
								<DATE>2020-02-06T10:17:02+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Extend Task-OsApplication mapping</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">PTSC-83</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
							<DOC-REVISION>
								<REVISION-LABEL>7.00.01</REVISION-LABEL>
								<ISSUED-BY>vispkg</ISSUED-BY>
								<DATE>2020-03-30T10:17:02+01:00</DATE>
								<MODIFICATIONS>
									<MODIFICATION>
										<CHANGE>
											<L-2 L="EN">Adapt OS Task to Appl Mapping for Default_Init_Task_Trusted</L-2>
										</CHANGE>
										<REASON>
											<L-2 L="EN">PTSC-196</L-2>
										</REASON>
									</MODIFICATION>
								</MODIFICATIONS>
							</DOC-REVISION>
						</DOC-REVISIONS>
					</ADMIN-DATA>
					<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
					<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
					<SUPPORTED-CONFIG-VARIANTS>
						<SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
						<SUPPORTED-CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</SUPPORTED-CONFIG-VARIANT>
					</SUPPORTED-CONFIG-VARIANTS>
					<CONTAINERS>
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="2fea153c-34d4-40d7-8e41-bbde15b2b4a7">
							<SHORT-NAME>vItaSipGeneral</SHORT-NAME>
							<DESC>
								<L-2 L="EN">General configuration parameters of the vItaSip module.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
							<PARAMETERS>
								<ECUC-BOOLEAN-PARAM-DEF UUID="6837a8cb-2022-49e6-b6dd-2f2d9432f860">
									<SHORT-NAME>vItaSipBreSupport</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">BRE Support</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Enable the BRE support.

TRUE: Start Application uses BSW API (BRE use case).
FALSE or parameter does not exist: Start Application is modelled as SWC and uses ASR ports (RTE use case).</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-ENUMERATION-PARAM-DEF UUID="9d25cf2f-ebc8-48c0-82cb-8707f062c4e4">
									<SHORT-NAME>vItaSipTaskToApplicationMapping</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Choose a mapping for your SC3/SC4 configuration dependent on the use case.
Parameter has no effect in a SC1/SC2 configuration.

Task															SafeBsw		SafePart		QM
Default_Init_Task										Trusted			NonTrusted	NonTrusted
Default_Init_Task_Trusted							Trusted			Trusted			Trusted
Default_BSW_Sync_Task							Trusted			Trusted			NonTrusted
Default_BSW_Async_Task							Trusted			NonTrusted	NonTrusted
Default_RTE_Mode_switch_Task				Trusted			NonTrusted	NonTrusted
StartApplication_Appl_Task						NonTrusted	NonTrusted	NonTrusted
NoSwc_Appl_Task									Trusted			NonTrusted	NonTrusted
NoSwc_Init_Task										Trusted			NonTrusted	NonTrusted
StartApplication_MemoryViolation_Task	NonTrusted	NonTrusted	NonTrusted
StartApplication_Trusted_Task					Trusted			Trusted			Trusted
Default_Background_Task							Trusted			NonTrusted	NonTrusted
StartApplication_Appl_Init_Task				NonTrusted	NonTrusted	NonTrusted</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>QM</DEFAULT-VALUE>
									<LITERALS>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="21a83f6d-555f-4f6c-b4cf-bb5e6d730380">
											<SHORT-NAME>QM</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">QM</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="0a27fd42-27ff-45ee-9216-1ae03e5340c0">
											<SHORT-NAME>SAFEBSW</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">SAFEBSW</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="b62c8fc7-3edb-44e7-94dd-9a362e896ab8">
											<SHORT-NAME>SAFEPARTITIONING</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">SAFEPARTITIONING</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
									</LITERALS>
								</ECUC-ENUMERATION-PARAM-DEF>
								<ECUC-ENUMERATION-PARAM-DEF UUID="af5ed5ba-f3f1-4624-93e8-86e9fd244511">
									<SHORT-NAME>vItaSipIdmUsage</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This parameter is only relevant in an IDM configuration.

USE_IDENTITY_AS_MAIN_IDENTITY:
This identity is used for vItaSip. The identity is configured as main identity which means that all use cases of vItaSip are supported. This setting is restricted for one identity.
USE_IDENTITY:
This identity is used for vItaSip. The identity is not configured as main identity which means that not all use cases of vItaSip are supported.
NOT_USED:
This identity is not used for vItaSip.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>NOT_USED</DEFAULT-VALUE>
									<LITERALS>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="ec00f84c-1e3f-4ded-b5d9-c4caa451f74b">
											<SHORT-NAME>USE_IDENTITY_AS_MAIN_IDENTITY</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">USE_IDENTITY_AS_MAIN_IDENTITY</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="3d7134b0-9f9a-4037-8bab-06afe1346bd3">
											<SHORT-NAME>USE_IDENTITY</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">USE_IDENTITY</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="22b7f238-3a3d-4032-9a3d-d58eac8892cf">
											<SHORT-NAME>NOT_USED</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">NOT_USED</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
									</LITERALS>
								</ECUC-ENUMERATION-PARAM-DEF>
							</PARAMETERS>
							<SUB-CONTAINERS>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="ffc782dc-c45e-45b3-9f2a-bc8666de1145">
									<SHORT-NAME>vItaSipCANoeChannel</SHORT-NAME>
									<LONG-NAME>
										<L-4 L="EN">CANoe Channel</L-4>
									</LONG-NAME>
									<DESC>
										<L-2 L="EN">Configuration of references to physical channels of the ECU which shall be considered for vItaSip.

At least one vItaSipCANoeChannel is required.

vItaSipCANoeChannel is NOT required for "Setup Support Only". In this case vItaSip will only have the follwing functionality:
- Solving Actions to create Default Tasks
- Generation of BswInit.c</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">true</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<ECUC-STRING-PARAM-DEF UUID="543dc6b0-ad08-4b22-90ed-6ae52048ecb9">
											<SHORT-NAME>vItaSipCANoeChannelName</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">CANoe Channel Name</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">The name should be the same as configured in the CANoe network. Do not use CANoe keywords (CAN1...CAN32) and avoid using similar constructs like CAN0, CAN00, etc.</L-2>
											</DESC>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<ECUC-STRING-PARAM-DEF-VARIANTS>
												<ECUC-STRING-PARAM-DEF-CONDITIONAL>
													<DEFAULT-VALUE>_unset_</DEFAULT-VALUE>
												</ECUC-STRING-PARAM-DEF-CONDITIONAL>
											</ECUC-STRING-PARAM-DEF-VARIANTS>
										</ECUC-STRING-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<ECUC-CHOICE-REFERENCE-DEF UUID="bef5e136-75b7-4f31-8075-c1f0ecb196fd">
											<SHORT-NAME>vItaSipChannelRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference to the configuration element representing a physical channel.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REFS>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Can/CanConfigSet/CanController</DESTINATION-REF>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/FrIf/FrIfConfig/FrIfCluster/FrIfController</DESTINATION-REF>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Lin/LinGlobalConfig/LinChannel</DESTINATION-REF>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Eth/EthConfigSet/EthCtrlConfig</DESTINATION-REF>
											</DESTINATION-REFS>
										</ECUC-CHOICE-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</SUB-CONTAINERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="5b8cfebc-cab8-430d-bee1-79a80d58f3b4">
							<SHORT-NAME>vItaSipControlCommunication</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of vItaSip control communication. The control communication configuration is required to establish a communication path between the CANoe tester and the Start Application SWC. This provides the ability to the CANoe tester to remote control the Start Application SWC. Thus it is possible to switch use cases, trigger function calls or receive status information from the application running on the ECU side.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
							<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
							<PARAMETERS>
								<ECUC-ENUMERATION-PARAM-DEF UUID="53291b76-66b8-4e1c-866b-629fd4c2b12f">
									<SHORT-NAME>vItaSipControlCommunicationType</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Select the control communication path.

COM: Control communication is done via signal-based communication. The user has to configure valid references to ComSignals/ComSignalGroups in the subcontainer vItaSipControlCommunicationCom. For every reference a corresponding port is generated in the SWC. This is the default behaviour.

GENERIC_ADAPTER: The user has to configure/implement communication path between CANoe and the control communication ports. A generic uint8 port is generated for every control communication signal.

SOAD_CDD: Control communication is done via SoAd CDD (StartApplicationEth). Dedicated Start Application PDUs are used for the communication between CANoe and the SoAd CDD which transacts the PDU payload to the Start Application ports. The StartApplicationEth CDD and PDUs must be added by the user.

NOTE:
Ensure that the physical channels of the ECU which are required for the control communication are part of the vItaSipCANoeChannel configuration.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>COM</DEFAULT-VALUE>
									<LITERALS>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="f4f06cfb-1f04-4952-a43d-fa03a7962981">
											<SHORT-NAME>COM</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">COM</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="0f4d513a-73e4-45fd-8e70-623742faf603">
											<SHORT-NAME>GENERIC_ADAPTER</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">GENERIC_ADAPTER</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
										<ECUC-ENUMERATION-LITERAL-DEF UUID="9868f02e-ba44-40d1-962c-aa64335ed8eb">
											<SHORT-NAME>SOAD_CDD</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">SOAD_CDD</L-4>
											</LONG-NAME>
											<ORIGIN>Vector Informatik</ORIGIN>
										</ECUC-ENUMERATION-LITERAL-DEF>
									</LITERALS>
								</ECUC-ENUMERATION-PARAM-DEF>
							</PARAMETERS>
							<SUB-CONTAINERS>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="def70fb9-dabe-4d6e-80b7-a66577a693d7">
									<SHORT-NAME>vItaSipControlCommunicationCom</SHORT-NAME>
									<DESC>
										<L-2 L="EN">Configuration of control communication type Com. The referenced ComSignals/ComSignalGroups are used for the control communication between CANoe tester and Start Application SWC. The physical channels of the ECU which are used for the transfer of the signals must be part of the CANoe configuration and vItaSipCANoeChannel configuration.

Rx direction: Represents the communication path from the CANoe tester to the ECU.
Tx direction: Represents the communication path from the ECU to the CANoe tester.

NOTE:
The referenced signals from the vItaSipDataRxSignalRef/vItaSipDataTxSignalRef are maybe taken into account as vItaSipCom test elemts of the COM use case.
In case there are more than one identity, control communication Com should be configured for each identity used in vItaSip module.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">true</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
									<REFERENCES>
										<ECUC-CHOICE-REFERENCE-DEF UUID="a8cd00bb-3677-4add-9bf6-75c5a43ba0ae">
											<SHORT-NAME>vItaSipControlRxSignalRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Select a Rx signal or group signal with at least 8bit.

Following data types are supported:
- UINT8
- UINT16
- UINT32
- SINT16
- SINT32

Following bus types are supported:
Can, Lin, FlexRay

Next to a directly routing between ComIPdu and BusIf, following additional modules inbetween are supported:
IpduM (by using I-PDU Multiplexing)
SecOC (by using the SecOC configuration for the Start Application)</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REFS>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Com/ComConfig/ComSignal</DESTINATION-REF>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Com/ComConfig/ComSignalGroup/ComGroupSignal</DESTINATION-REF>
											</DESTINATION-REFS>
										</ECUC-CHOICE-REFERENCE-DEF>
										<ECUC-CHOICE-REFERENCE-DEF UUID="03d6bf59-4ae1-4517-bc77-61a3a73d6119">
											<SHORT-NAME>vItaSipControlTxSignalRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Select a Tx signal or group signal with at least 8bit.

Following data types are supported:
- UINT8
- UINT16
- UINT32
- SINT16
- SINT32

Following combination of ComTxModeMode and ComTransferProperty are supported:
ComTxModeMode (ComTxModeTrue):
- 'DIRECT' with ComTransferProperty 'TRIGGERED' or 'TRIGGERED_WITHOUT_REPETITION'
- 'PERIODIC' with ComTransferProperty all 'TRIGGERED*' variants and 'PENDING'
- 'MIXED' with ComTransferProperty all 'TRIGGERED*' variants and 'PENDING'
- 'NONE' only in case of FrIfNoneMode is set to true

Following bus types are supported:
Can, Lin, FlexRay

Next to a directly routing between ComIPdu and BusIf, following additional modules inbetween are supported:
IpduM (by using I-PDU Multiplexing)
SecOC (by using the SecOC configuration for the Start Application)</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REFS>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Com/ComConfig/ComSignal</DESTINATION-REF>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Com/ComConfig/ComSignalGroup/ComGroupSignal</DESTINATION-REF>
											</DESTINATION-REFS>
										</ECUC-CHOICE-REFERENCE-DEF>
										<ECUC-CHOICE-REFERENCE-DEF UUID="7ca4c681-fd1d-47e7-8a67-be955bb2aec7">
											<SHORT-NAME>vItaSipDataRxSignalRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Select a Rx signal or group signal with at least 8bit.

Following data types are supported:
- UINT8
- UINT16
- UINT32
- SINT16
- SINT32

Following bus types are supported:
Can, Lin, FlexRay

Next to a directly routing between ComIPdu and BusIf, following additional modules inbetween are supported:
IpduM (by using I-PDU Multiplexing)
SecOC (by using the SecOC configuration for the Start Application)</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REFS>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Com/ComConfig/ComSignal</DESTINATION-REF>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Com/ComConfig/ComSignalGroup/ComGroupSignal</DESTINATION-REF>
											</DESTINATION-REFS>
										</ECUC-CHOICE-REFERENCE-DEF>
										<ECUC-CHOICE-REFERENCE-DEF UUID="9d4eb631-7fc4-463b-8555-f694a73877fb">
											<SHORT-NAME>vItaSipDataTxSignalRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Select a Tx signal or group signal with at least 8bit.

Following data types are supported:
- UINT8
- UINT16
- UINT32
- SINT16
- SINT32

Following combination of ComTxModeMode and ComTransferProperty are supported:
ComTxModeMode (ComTxModeTrue):
- 'DIRECT' with ComTransferProperty 'TRIGGERED' or 'TRIGGERED_WITHOUT_REPETITION'
- 'PERIODIC' with ComTransferProperty all 'TRIGGERED*' variants and 'PENDING'
- 'MIXED' with ComTransferProperty all 'TRIGGERED*' variants and 'PENDING'
- 'NONE' only in case of FrIfNoneMode is set to true

Following bus types are supported:
Can, Lin, FlexRay

Next to a directly routing between ComIPdu and BusIf, following additional modules inbetween are supported:
IpduM (by using I-PDU Multiplexing)
SecOC (by using the specific SecOC configuration for the Start Application)</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REFS>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Com/ComConfig/ComSignal</DESTINATION-REF>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Com/ComConfig/ComSignalGroup/ComGroupSignal</DESTINATION-REF>
											</DESTINATION-REFS>
										</ECUC-CHOICE-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</SUB-CONTAINERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
						<ECUC-PARAM-CONF-CONTAINER-DEF UUID="8f3c9006-b26f-46e2-b754-28662091a9ed">
							<SHORT-NAME>vItaSipUseCases</SHORT-NAME>
							<DESC>
								<L-2 L="EN">Configuration of the vItaSip use cases.</L-2>
							</DESC>
							<ADMIN-DATA>
								<SDGS>
									<SDG GID="DV:CfgPostBuild">
										<SD GID="DV:postBuildSelectableChangeable">false</SD>
										<SD GID="DV:postBuildNotDeletable">false</SD>
									</SDG>
								</SDGS>
							</ADMIN-DATA>
							<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
							<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
							<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
							<MULTIPLE-CONFIGURATION-CONTAINER>false</MULTIPLE-CONFIGURATION-CONTAINER>
							<PARAMETERS>
								<ECUC-BOOLEAN-PARAM-DEF UUID="26385451-330b-49d0-968d-b0f70d2ec8ef">
									<SHORT-NAME>vItaSipUseCaseCom</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The COM use case demonstrates the simple signal transmission as well as reception on runnable level.

TRUE: Activate the COM use case
FALSE: Deactivate the COM use case

NOTE: 
The configuration of the COM use case can be done in vItaSipCom.
It's required to configure at least one vItaSipCANoeChannel.
In case of vItaSipControlCommunicationType COM the vItaSipDataRxSignalRef and vItaSipDataTxSignalRef are maybe automatically taken into account as test elements.
In addition, SecOCSupport can also be configured.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>true</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="a05862f7-bb4d-4359-9efd-1362c4ee0a41">
									<SHORT-NAME>vItaSipUseCaseMem</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The MEM use case demonstrates that simple reading and writing of an NvBlock works on runnable level.

TRUE: Activate the MEM use case
FALSE: Deactivate the MEM use case</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="f2d8d693-3936-4f78-8360-2b30011a42ac">
									<SHORT-NAME>vItaSipUseCaseDiag</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The DIAG use case demonstrates the setting of a DemEvent as well as Dcm RDBI/WDBI on runnable level.

TRUE: Activate the DIAG use case
FALSE: Deactivate the DIAG use case</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="25150b8c-0ea9-418d-be69-f7b74e0c0c92">
									<SHORT-NAME>vItaSipUseCaseNm</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The NM use case demonstrates basic sleep/wakeup for CAN, LIN, FlexRay and ETH.

TRUE: Activate the NM use case
FALSE: Deactivate the NM use case</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="*************-4af2-97f5-957e94f5c661">
									<SHORT-NAME>vItaSipUseCaseWdg</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The WDG use case demonstrates basic alive supervision for a supervised entity of the WdgM.

TRUE: Activate the WDG use case
FALSE: Deactivate the WDG use case</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="d328b9ac-bb66-4f8b-a994-89969e839133">
									<SHORT-NAME>vItaSipUseCaseXcp</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The XCP use case demonstrates basic upload and download via XCP.

TRUE: Activate the XCP use case
FALSE: Deactivate the XCP use case</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="25155731-dfb5-4a44-a274-60adb2bfc071">
									<SHORT-NAME>vItaSipUseCaseJ1939Diag</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The J1939DIAG use case demonstrates basic DemEvent Handling via J1939Dcm.

TRUE: Activate the J1939DIAG use case
FALSE: Deactivate the J1939DIAG use case</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="96c79fee-00c0-45db-9e54-a35336708054">
									<SHORT-NAME>vItaSipUseCaseScc</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The SCC use case demonstrates basic Smart Charging Communication protocol interaction between Electric Vehicle (EV) and Electric Vehicle Supply Equipment (EVSE).

TRUE: Activate the SCC use case
FALSE: Deactivate the SCC use case</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="3741df21-0b12-403a-a111-8c16f7bf795c">
									<SHORT-NAME>vItaSipUseCaseMultiCore</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The MultiCore use case demonstrates intercore sender/receiver communication between two or more SWCs running on different cores.

TRUE: Activate the MultiCore use case
FALSE: Deactivate the MultiCore use case

NOTE: 
The configuration of the MultiCore use case can be done in vItaSipMultiCore.
It's required to add at least one MultiCore test element, which references to a slave core.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="1ae48fc0-0499-4450-9a9e-085bfcf7dff8">
									<SHORT-NAME>vItaSipUseCaseMemoryProtection</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The MemoryProtection use case demonstrates the basic usage of the memory protection feature.

TRUE: Activate the MemoryProtection use case
FALSE: Deactivate the MemoryProtection use case</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="f67a6b90-797f-4fb9-8cc3-4a2a11342258">
									<SHORT-NAME>vItaSipUseCaseFbl</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The FBL use case demonstrates that the Start Application binary can be flashed to the ECU.

TRUE: Activate the FBL use case
FALSE: Deactivate the FBL use case

NOTE: 
It's required to add additional implementation steps on DUT source code and configure a flashpack with vFlash.
More details see vItaSip documentation.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
								<ECUC-BOOLEAN-PARAM-DEF UUID="618afa90-98c3-4b50-8e9a-b95fd5eeb7f3">
									<SHORT-NAME>vItaSipUseCaseEth</SHORT-NAME>
									<DESC>
										<L-2 L="EN">The ETH use case demonstrates that PDU transmission as well as reception over UDP via SoAd CDD works on runnable level.

TRUE: Activate the ETH use case
FALSE: Deactivate the ETH use case

NOTE:
It is required to configure the Ethernet Channel, on which the Start Application SoAd PDUs are configured, as vItaSipCANoeChannel.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:variantMultiplicityPBL">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<IMPLEMENTATION-CONFIG-CLASSES>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
										<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
											<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
										</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
									</IMPLEMENTATION-CONFIG-CLASSES>
									<ORIGIN>Vector Informatik</ORIGIN>
									<REQUIRES-INDEX>false</REQUIRES-INDEX>
									<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
									<DEFAULT-VALUE>false</DEFAULT-VALUE>
								</ECUC-BOOLEAN-PARAM-DEF>
							</PARAMETERS>
							<SUB-CONTAINERS>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="7bdcf3c2-9df5-4f22-94ec-51c7b49f3ee3">
									<SHORT-NAME>vItaSipCom</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container contains the configuration of COM use case.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">true</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<PARAMETERS>
										<ECUC-BOOLEAN-PARAM-DEF UUID="ba1f4301-5eee-4c19-8852-da7310ca3917">
											<SHORT-NAME>vItaSipComSecOCSupport</SHORT-NAME>
											<LONG-NAME>
												<L-4 L="EN">Com SecOC Support</L-4>
											</LONG-NAME>
											<DESC>
												<L-2 L="EN">Enable the SecOC support.

TRUE: Allow usage of SecOC signals for control communication and the COM use case.
FALSE or parameter does not exist: SecOC signals are not allowed for control communication and the COM use case .

NOTE:
If SecOC support is enabled, additional configuration steps on CANoe side are required. More details see vItaSip documentation.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
											<DEFAULT-VALUE>false</DEFAULT-VALUE>
										</ECUC-BOOLEAN-PARAM-DEF>
									</PARAMETERS>
									<REFERENCES>
										<ECUC-CHOICE-REFERENCE-DEF UUID="864ee47e-b41f-4e69-9322-bf4b5e524bc6">
											<SHORT-NAME>vItaSipComTestElementRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference the ComSignals or ComSignalGroups used in COM use case.

Following data types with at least 8bit are supported:
- UINT8
- UINT16
- UINT32
- SINT16
- SINT32

Following combination of ComTxModeMode and ComTransferProperty for Tx direction are supported:
ComTxModeMode (ComTxModeTrue):
- 'DIRECT' with ComTransferProperty all 'TRIGGERED*' variants
- 'PERIODIC' with ComTransferProperty all 'TRIGGERED*' variants and 'PENDING'
- 'MIXED' with ComTransferProperty all 'TRIGGERED*' variants and 'PENDING'
- 'NONE' only in case of FrIfNoneMode is set to true

Following bus types are supported:
Can, Lin, FlexRay

Next to a directly routing between ComIPdu and BusIf, following additional modules inbetween are supported:
IpduM (by using I-PDU Multiplexing)
SecOC (by using the SecOC configuration for the Start Application)</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY-INFINITE>true</UPPER-MULTIPLICITY-INFINITE>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REFS>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Com/ComConfig/ComSignal</DESTINATION-REF>
												<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Com/ComConfig/ComSignalGroup</DESTINATION-REF>
											</DESTINATION-REFS>
										</ECUC-CHOICE-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
								<ECUC-PARAM-CONF-CONTAINER-DEF UUID="03eedbaf-d8d4-4669-8d38-5d32057f4747">
									<SHORT-NAME>vItaSipMultiCore</SHORT-NAME>
									<DESC>
										<L-2 L="EN">This container contains the configuration of MultiCore use case.</L-2>
									</DESC>
									<ADMIN-DATA>
										<SDGS>
											<SDG GID="DV:CfgPostBuild">
												<SD GID="DV:postBuildSelectableChangeable">true</SD>
												<SD GID="DV:postBuildNotDeletable">false</SD>
											</SDG>
										</SDGS>
									</ADMIN-DATA>
									<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
									<UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
									<POST-BUILD-CHANGEABLE>false</POST-BUILD-CHANGEABLE>
									<REFERENCES>
										<ECUC-REFERENCE-DEF UUID="97c7d6c6-833c-4773-aff3-078884c3af60">
											<SHORT-NAME>vItaSipMultiCoreTestElementRef</SHORT-NAME>
											<DESC>
												<L-2 L="EN">Reference the slaves core on which the slave core SWC shall run. Ensure not to choose the same core as the master core or as other test elements.</L-2>
											</DESC>
											<ADMIN-DATA>
												<SDGS>
													<SDG GID="DV:CfgPostBuild">
														<SD GID="DV:variantMultiplicityPBL">false</SD>
													</SDG>
												</SDGS>
											</ADMIN-DATA>
											<LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
											<UPPER-MULTIPLICITY>8</UPPER-MULTIPLICITY>
											<IMPLEMENTATION-CONFIG-CLASSES>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-POST-BUILD-SELECTABLE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
												<ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
													<CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
													<CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
												</ECUC-IMPLEMENTATION-CONFIGURATION-CLASS>
											</IMPLEMENTATION-CONFIG-CLASSES>
											<ORIGIN>Vector Informatik</ORIGIN>
											<REQUIRES-INDEX>false</REQUIRES-INDEX>
											<DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucHardware/EcucCoreDefinition</DESTINATION-REF>
										</ECUC-REFERENCE-DEF>
									</REFERENCES>
								</ECUC-PARAM-CONF-CONTAINER-DEF>
							</SUB-CONTAINERS>
						</ECUC-PARAM-CONF-CONTAINER-DEF>
					</CONTAINERS>
				</ECUC-MODULE-DEF>
			</ELEMENTS>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>