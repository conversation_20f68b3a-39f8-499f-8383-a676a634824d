/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *  Copyright (c) 1999 - 2006 cv cryptovision GmbH.                                                All rights reserved.
 *
 *  For modifications by Vector Informatik GmbH:
 *  Copyright (c) 2020 by Vector Informatik GmbH.                                                  All rights reserved.
 *
 *                This software is protected under intellectual property laws and proprietary to cv cryptovision GmbH
 *                and/or Vector Informatik GmbH.
 *                No right or license is granted save as expressly set out in the applicable license conditions.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -----------------------------------------------------------------------------------------------------------------*/
/*!       \file  actIRSAPrivateCRT.c
 *        \brief Implementation file for actIRSA.h
 *
 *      \details Currently the actClib version is used.
 *               This file is part of the embedded systems library cvActLib/ES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  REVISION HISTORY
 *  -------------------------------------------------------------------------------------------------------------------
 *  Refer to the module's header file.
 *
 *  FILE VERSION
 *  -------------------------------------------------------------------------------------------------------------------
 *  Refer to module's header file.
 *********************************************************************************************************************/
#define ACTIRSAPRIVATECRT_SOURCE

#include "actIRSA.h"
#include "actIRSAExp.h"
#include "actBigNum.h"
#include "actUtilities.h"
#include "actWatchdog.h"

#if (VSECPRIM_ACTIRSAPRIVATECRT_ENABLED == STD_ON) /* COV_VSECPRIM_NO_SAFE_CONFIG XF */

/* PRQA S 5209 EOF */ /* MD_VSECPRIM_USE_OF_BASIC_TYPES */

# define VSECPRIM_START_SEC_CODE
# include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 * actRSAInitPrivateKeyOperationCRT()
 *********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 *
 */
VSECPRIM_FUNC(actRETURNCODE) actRSAInitPrivateKeyOperationCRT(
  VSECPRIM_P2CONST_PARA(actU8) prime_p,
  actLengthType prime_p_len,
  VSECPRIM_P2CONST_PARA(actU8) prime_q,
  actLengthType prime_q_len,
  VSECPRIM_P2CONST_PARA(actU8) private_exponent_dp,
  actLengthType private_exponent_dp_len,
  VSECPRIM_P2CONST_PARA(actU8) private_exponent_dq,
  actLengthType private_exponent_dq_len,
  VSECPRIM_P2CONST_PARA(actU8) q_inverse_mod_p,
  actLengthType q_inverse_mod_p_len,
  VSECPRIM_P2VAR_PARA(actRSACRTSTRUCT) wsCRTStruct,
  VSECPRIM_P2FUNC(VSECPRIM_NONE, void, watchdog)(void))
{
   /*=========================================================================
      Used by   | Workspace             |   Length in digits (offset in bytes)
      ========================================================================
      -----------------------------------
      CRTInit   | offset: CRT struct    |   sizeof(actRSACRTSTRUCT)
      -----------------------------------
      RSAModExp | offset: ring struct   |   sizeof(actBNRING)
                -------------------------
                | t1                    |   max_length (p, q)
                -------------------------
                | t2                    |   max_length (R^2)
                -------------------------
                | t3                    |   max_length (dp, dq)
                -------------------------
                | t4                    |   max_length (cp, cq)
                -------------------------
                | t5                    |   max_length+1 (exp result)
                -------------------------
                | t5+1                  |   max_length+1 (exp tmp)
                ...                         ...
                | t5+actRSA_MOD_EXP_TMP |   max_length+1 (exp tmp)
     ------------------------------------
     CRT        | t6+actRSA_MOD_EXP_TMP |   max_length (CRT tmp)
     ------------------------------------
     Total size:
     ===========
     offset + 5*max_length + (1+actRSA_MOD_EXP_TMP)*(max_length+1)
   =========================================================================*/

   VSECPRIM_P2VAR_PARA(actRSACRTSTRUCT) crt_param = wsCRTStruct;
   actLengthType p_length = actBNGetDigitLength(prime_p_len);
   actLengthType q_length = actBNGetDigitLength(prime_q_len);

   VSECPRIM_P2VAR_PARA(actBNDIGIT) p = wsCRTStruct->pqBuffer;
   VSECPRIM_P2VAR_PARA(actBNDIGIT) q = &p[p_length];
   VSECPRIM_P2VAR_PARA(actBNDIGIT) dp = wsCRTStruct->dpdqBuffer;
   VSECPRIM_P2VAR_PARA(actBNDIGIT) dq = &dp[p_length];
   VSECPRIM_P2VAR_PARA(actBNDIGIT) qInv = wsCRTStruct->qInvBuffer;
   VSECPRIM_P2VAR_PARA(actBNDIGIT) t6 = wsCRTStruct->modulusN;
   VSECPRIM_P2VAR_PARA(actBNDIGIT) one;

   actL1trigger(watchdog); /* PRQA S 1338, 2983, 3112  */ /* MD_MSR_DummyStmt */ /*lint -e438 */  /* SBSW_VSECPRIM_FUNCTION_CALL_WATCHDOG */

   /* length checks  */
   if (private_exponent_dp_len>prime_p_len) { return actEXCEPTION_PRIVKEY;}
   if (private_exponent_dq_len>prime_q_len) { return actEXCEPTION_PRIVKEY;}
   if (q_inverse_mod_p_len>prime_p_len) { return actEXCEPTION_PRIVKEY;}

   /* check workspace length for both exponentiations (refer to actRSAInitExponentiation) */
   /* Check prime sizes individually: One of the two primes can be 1 (2/4) digits longer than 
   *  modulus_length_in_digits/2, and we do not know whether p or q is longer */
   if ((p_length > actRSA_CRT_PRIME_SIZE) || (q_length > actRSA_CRT_PRIME_SIZE))
   {
     return actEXCEPTION_MEMORY;
   }

   /* Check prime sizes combined: In sum, p and q measured in digits are only allowed to
   *  be one digit longer than the modulus */
   if ((p_length + q_length) > actRSA_CRT_PRIMEPAIR_SIZE)
   {
     return actEXCEPTION_MEMORY;
   }

   /* BigNum initialization of p, q, dp, dq, qInv */
   actBNSetOctetStringROM(p, p_length, prime_p, prime_p_len);
   actBNSetOctetStringROM(q, q_length, prime_q, prime_q_len);
   actBNSetOctetStringROM(dp, p_length, private_exponent_dp, private_exponent_dp_len);
   actBNSetOctetStringROM(dq, q_length, private_exponent_dq, private_exponent_dq_len);
   actBNSetOctetStringROM(qInv, p_length, q_inverse_mod_p, q_inverse_mod_p_len);

   actL1trigger(watchdog); /* PRQA S 1338, 2983, 3112  */ /* MD_MSR_DummyStmt */ /*lint -e438 */  /* SBSW_VSECPRIM_FUNCTION_CALL_WATCHDOG */

   /* calculate n = p*q */
   /* n is saved in t6 for size checks in actRSAPrivateKeyOperationCRT() function */
   actBNMult(p, p_length, q, q_length, t6, VSECPRIM_FUNC_NULL_PTR);
   /* calculate exact byte length of n */
   crt_param->n_bytes = (actBNGetBitLength(t6, p_length+q_length)+7)/8;

   /* check 0 < qInv < p  before continue */
   if (actBNIsZero(qInv, p_length) == TRUE)
   {
      return actEXCEPTION_PRIVKEY;
   }
   if (actBNCompare(qInv, p, p_length) >= actCOMPARE_EQUAL)
   {
      return actEXCEPTION_PRIVKEY;
   }

   one = qInv;/* use qInv space as 1-Element of the field */
   actBNSetZero(one, actMax(p_length, q_length));
   one[0] = (actBNDIGIT)1;

   /* check 0 < dp < (p-1)  before continue */
   actBNSub(p, one, p, p_length); /* p = p-1 */

   if (actBNIsZero(dp, p_length) == TRUE)
   {
     return actEXCEPTION_PRIVKEY;
   }

   if (actBNCompare(dp, p, p_length) >= actCOMPARE_EQUAL)
   {
     return actEXCEPTION_PRIVKEY;
   }

    /* check 0 < dq < (q-1)  before continue */
   actBNSub(q, one, q, q_length); /* q = q-1 */

   if (actBNIsZero(dq, q_length) == TRUE)
   {
     return actEXCEPTION_PRIVKEY;
   }

   if (actBNCompare(dq, q, q_length) >= actCOMPARE_EQUAL)
   {
     return actEXCEPTION_PRIVKEY;
   }


   crt_param->p = prime_p; crt_param->p_bytes = prime_p_len;
   crt_param->q = prime_q; crt_param->q_bytes = prime_q_len;
   crt_param->dp = private_exponent_dp; crt_param->dp_bytes = private_exponent_dp_len;
   crt_param->dq = private_exponent_dq; crt_param->dq_bytes = private_exponent_dq_len;
   crt_param->q_inv = q_inverse_mod_p; crt_param->q_inv_bytes = q_inverse_mod_p_len;

   actL1trigger(watchdog); /* PRQA S 1338, 2983, 3112  */ /* MD_MSR_DummyStmt */ /*lint -e438 */  /* SBSW_VSECPRIM_FUNCTION_CALL_WATCHDOG */

   return actOK;
}

/**********************************************************************************************************************
 * actRSAPrivateKeyOperationCRT()
 *********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 *
 */
VSECPRIM_FUNC(actRETURNCODE) actRSAPrivateKeyOperationCRT(
  VSECPRIM_P2CONST_PARA(actU8) cipher,
  actLengthType cipher_len,
  VSECPRIM_P2VAR_PARA(actU8) message,
  VSECPRIM_P2VAR_PARA(actLengthType) message_len,
  VSECPRIM_P2VAR_PARA(actRSACRTSTRUCT) wsCRT,
  VSECPRIM_P2VAR_PARA(actRSACRTTMPSTRUCT) wsCRTBuf,
  VSECPRIM_P2VAR_PARA(actRSACRTPRIMSTRUCT) wsRSAPrimBuf,
  VSECPRIM_P2FUNC(VSECPRIM_NONE, void, watchdog)(void))
{
  /* NOTE: see actRSAInitPrivateKeyOperationCRT() for workspace layout */

  actRETURNCODE rc;
  VSECPRIM_P2VAR_PARA(actRSACRTSTRUCT) crt_param = wsCRT;

  actLengthType p_bytes = crt_param->p_bytes;
  actLengthType q_bytes = crt_param->q_bytes;
  actLengthType n_bytes = crt_param->n_bytes;

  actLengthType p_length = actBNGetDigitLength(p_bytes);
  actLengthType q_length = actBNGetDigitLength(q_bytes);
  actLengthType n_length = actBNGetDigitLength(n_bytes);

  VSECPRIM_P2VAR_PARA(actBNRING) ring;

  /* assign pointers to array for messages m1 and m2 */
  VSECPRIM_P2VAR_PARA(actBNDIGIT) m1 = wsCRTBuf->m12;
  VSECPRIM_P2VAR_PARA(actBNDIGIT) m2 = &m1[p_length];

  /* assign pointers to the arrays in workspace, which will be passed on to the RSA primitives */
  actRSAPRIMSTRUCTPOINTERS wksp_ptrs;
  wksp_ptrs.wsRSARing = &(wsRSAPrimBuf->wsRSARing);
  wksp_ptrs.modulus = wsRSAPrimBuf->modulusBuffer;
  wksp_ptrs.RR = wsRSAPrimBuf->RRBuffer;
  wksp_ptrs.exponent = wsRSAPrimBuf->exponentBuffer;
  wksp_ptrs.cipher = wsRSAPrimBuf->cipherBuffer;
  wksp_ptrs.message = wsRSAPrimBuf->messageBuffer;
  wksp_ptrs.tmpVar = wsRSAPrimBuf->tmpVarBuffer;

  ring = wksp_ptrs.wsRSARing;

  actL1trigger(watchdog); /* PRQA S 1338, 2983, 3112  */ /* MD_MSR_DummyStmt */ /*lint -e438 */  /* SBSW_VSECPRIM_FUNCTION_CALL_WATCHDOG */

  /* check cipher length */
  if (cipher_len > n_bytes) { return actRSA_PARAM_OUT_OF_RANGE; }
  /* check output buffer length */
  if (*message_len < n_bytes) { return actEXCEPTION_MEMORY; }

  /*
  CRT STEP 1: calculate m2 = cq^dq mod q
  */

  /* init q_ring exponentiation */
  /* modulus, buffer and exponent will be initialized here (q, R^2, dq: each of q_length) */
  rc = actRSAInitExponentiation(crt_param->q, q_bytes, crt_param->dq, crt_param->dq_bytes, &wksp_ptrs,
    actRSA_PRIVATE_KEY_OPERATION_CRT, watchdog);
  if (rc != actOK)
  {
    return rc;
  }

  /* save original cipher in actBIGNUM format in tmp and check 0 < cipher c < n */
  actBNSetOctetString(wsCRTBuf->tmp, n_length, cipher, cipher_len);

  if (actBNIsZero(wsCRTBuf->tmp, n_length) == TRUE)
  {
    return actRSA_PARAM_OUT_OF_RANGE;
  }
  /* the original modulus n is saved in wsCRT->modulusN by actRSAInitPrivateKeyOperationCRT() function */
  if (actBNCompare(wsCRTBuf->tmp, wsCRT->modulusN, n_length) >= actCOMPARE_EQUAL)
  {
    return actRSA_PARAM_OUT_OF_RANGE;
  }

  /* calculate the reduced cipher cq = c mod q and store the result in tmp2;
  actBNReduce returns ok, because q_length>=2 is checked in actRSAInitExponentiation */
  actBNReduce(wsCRTBuf->tmp, n_length, ring->m, q_length, wsCRTBuf->tmp2, watchdog);

  /*
  CRT STEP 1: calculate m2 = cq^dq mod q
  */

  /* transform reduced cipher from actBIGNUM format to actU8 format */
  actBNOctetString(wsCRTBuf->reducedCipherInByte, q_bytes, wsCRTBuf->tmp2, q_length);

  actRSAExponentiation(wsCRTBuf->reducedCipherInByte, q_bytes, (VSECPRIM_P2VAR_PARA(actU8)) 0,
    (VSECPRIM_P2VAR_PARA(actLengthType)) 0, &wksp_ptrs, watchdog);

  /* no output buffer is passed, copy result to m2, the second part of the buffer wsCRTBuf->m12 */
  actBNCopy(m2, wksp_ptrs.message, q_length);

  /*
  CRT STEP 2: calculate m1 = cp^dp mod p
  */

  /* init p_ring exponentiation */
  /* modulus, buffer and exponent will be initialized here (p, R^2, dp: each of p_length) */
  rc = actRSAInitExponentiation(crt_param->p, p_bytes, crt_param->dp, crt_param->dp_bytes, &wksp_ptrs,
    actRSA_PRIVATE_KEY_OPERATION_CRT, watchdog);
  if (rc != actOK)
  {
    return rc;
  }

  /* since actBNReduce may change the first input parameter, it is necessary to refresh the
  original cipher value in tmp */
  actBNSetOctetString(wsCRTBuf->tmp, n_length, cipher, cipher_len);

  /* calculate the reduced cipher tmp2 = cp = c mod p,
  actBNReduce returns ok, because q_length>=2 is checked in actRSAInitExponentiation */
  actBNReduce(wsCRTBuf->tmp, n_length, ring->m, p_length, wsCRTBuf->tmp2, watchdog);

  /* transform reduced cipher from actBIGNUM format to actU8 format */
  actBNOctetString(wsCRTBuf->reducedCipherInByte, p_bytes, wsCRTBuf->tmp2, p_length);

  actRSAExponentiation(wsCRTBuf->reducedCipherInByte, p_bytes, (VSECPRIM_P2VAR_PARA(actU8)) 0,
    (VSECPRIM_P2VAR_PARA(actLengthType)) 0, &wksp_ptrs, watchdog);

  /* no output buffer is passed, copy result to m1, the first part of the buffer wsCRTBuf->m12 */
  actBNCopy(m1, wksp_ptrs.message, p_length);

  actL1trigger(watchdog); /* PRQA S 1338, 2983, 3112  */ /* MD_MSR_DummyStmt */ /*lint -e438 */  /* SBSW_VSECPRIM_FUNCTION_CALL_WATCHDOG */

  /*
  CRT STEP 3: calculate h = (m1-m2)*qInv mod p
  */

  /* save unreduced m2 to m2SizeModulus since the value in the m2-buffer could be modified by actBNReduce;
  then reduce m2 mod p, store result in tmp3 */
  actBNCopy(wsCRTBuf->tmp, m2, q_length);
  /* actBNReduce returns ok, because p_length>=2 is checked in actRSAInitExponentiation */
  actBNReduce(m2, q_length, ring->m, p_length, wsCRTBuf->tmp3, watchdog);

  /* calculate  m1 = m1-m2 mod p (store result in m1 to save memory)
  This does not overwrite values in m2, as m1 is p_length long and m1-m2 mod p is at most p_length long, too.
  However, m2 is not used hereafter anymore. 
  Prerequisites for actBNModSub: m1 < ring->m (= p) by definition of m1, tmp3 < ring->m (= p) due to actBNReduce */
  actBNModSub(m1, wsCRTBuf->tmp3, m1, ring, VSECPRIM_FUNC_NULL_PTR);

  /* calculate h = (m1-m2)*qInv mod p */

  /* store q_inv in tmp3 in actBIGNUM format, reuse tmp3 to save memory */
  actBNSetOctetStringROM(wsCRTBuf->tmp3, p_length, crt_param->q_inv, crt_param->q_inv_bytes);
  /* tmp2 = (m1-m2)*qInv*R^(-1) mod p, reuse tmp2 to save memory */
  actBNMontMul(m1, wsCRTBuf->tmp3, wsCRTBuf->tmp2, ring, VSECPRIM_FUNC_NULL_PTR);
  /* tmp3 = (m1-m2)*qInv mod p = h */
  actBNMontMul(wsCRTBuf->tmp2, ring->RR, wsCRTBuf->tmp3, ring, VSECPRIM_FUNC_NULL_PTR);

  /*
  CRT STEP 4: calculate m = m2 + q*h
  */

  /* store q in wsCRTBuf->tmp2 in actBIGNUM format, reuse tmp2 to save memory */
  actBNSetOctetStringROM(wsCRTBuf->tmp2, q_length, crt_param->q, crt_param->q_bytes);
  /* wsCRTBuf->m12 = q * h */
  actBNMult(wsCRTBuf->tmp2, q_length, wsCRTBuf->tmp3, p_length, wsCRTBuf->m12, VSECPRIM_FUNC_NULL_PTR);

  /* pad the original message m2 with leading zeros to n_length */
  actBNSetZero(wsCRTBuf->tmp + q_length, n_length - q_length);
  /* m12 += m2 */
  (void)actBNAdd(wsCRTBuf->m12, wsCRTBuf->tmp, wsCRTBuf->m12, n_length);

  /* write m12 to message buffer */
  actBNOctetString(message, n_bytes, wsCRTBuf->m12, n_length);
  *message_len = n_bytes;

  actL1trigger(watchdog); /* PRQA S 1338, 2983, 3112  */ /* MD_MSR_DummyStmt */ /*lint -e438 */  /* SBSW_VSECPRIM_FUNCTION_CALL_WATCHDOG */

  return actOK;
}

# define VSECPRIM_STOP_SEC_CODE
# include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#endif /* (VSECPRIM_ACTIRSAPRIVATECRT_ENABLED == STD_ON) */
