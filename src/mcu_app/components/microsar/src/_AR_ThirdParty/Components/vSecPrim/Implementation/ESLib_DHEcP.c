 /**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *  Copyright (c) 1999 - 2016 cv cryptovision GmbH.                                                All rights reserved.
 *
 *  For modifications by Vector Informatik GmbH:
 *  Copyright (c) 2020 by Vector Informatik GmbH.                                                  All rights reserved.
 *
 *                This software is protected under intellectual property laws and proprietary to cv cryptovision GmbH
 *                and/or Vector Informatik GmbH.
 *                No right or license is granted save as expressly set out in the applicable license conditions.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -----------------------------------------------------------------------------------------------------------------*/
 /**        \file  ESLib_DHEcP.c
 *        \brief  DHEcP implementation.
 *
 *      \details Currently the actClib version is used. 
 *               This file is part of the embedded systems library cvActLib/ES
 *********************************************************************************************************************/

 /**********************************************************************************************************************
 *  REVISION HISTORY
 *  -------------------------------------------------------------------------------------------------------------------
 *  Refer to the module's header file.
 * 
 *  FILE VERSION
 *  -------------------------------------------------------------------------------------------------------------------
 *  Refer to module's header file.
 *********************************************************************************************************************/

#define ESLIB_DHECP_SOURCE

#include "ESLib.h"
#include "ESLib_types.h"

/* actCLib includes */
#include "actIECDH.h"

#if (VSECPRIM_ECDH_GENERIC_ENABLED == STD_ON) /* COV_VSECPRIM_NO_SAFE_CONFIG XF */

/* PRQA S 5209 EOF */ /* MD_VSECPRIM_USE_OF_BASIC_TYPES */

# define VSECPRIM_START_SEC_CODE
# include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/****************************************************************************
 ** Global Functions
 ***************************************************************************/

/****************************************************************************
 * esl_initGenerateSharedSecretDHEcP_prim
 ***************************************************************************/
/*!
 *
 * Internal comment removed.
 *
 *
 *
 */
VSECPRIM_FUNC(eslt_ErrorCode) esl_initGenerateSharedSecretDHEcP_prim(VSECPRIM_P2VAR_PARA(eslt_WorkSpaceEcP) workSpace,
                                                                     VSECPRIM_P2ROMCONST_PARA(eslt_EccDomain) domain, VSECPRIM_P2ROMCONST_PARA(eslt_EccDomainExt) domainExt)
{
  /* init actCLib EC-DH primitive */
  actRETURNCODE result = actECDHInitGetSecret(domain, domainExt, &workSpace->wsEcP, (int)(workSpace->header.size));
  if (result != actOK)
  {
    if (result == actEXCEPTION_NULL)
    {
      return ESL_ERC_PARAMETER_INVALID;
    }
    else if (result == actEXCEPTION_MEMORY)
    {
      return ESL_ERC_WS_TOO_SMALL;
    }
    else if (result == actEXCEPTION_DOMAIN)
    {
      return ESL_ERC_ECC_DOMAIN_INVALID;
    }
    else if (result == actEXCEPTION_DOMAIN_EXT)
    {
      return ESL_ERC_ECC_DOMAINEXT_INVALID;
    }
  }

  /* Set workSpace state */
  workSpace->header.status = (ESL_WST_ALGO_ECP | ESL_WST_M_RUNNING | ESL_WST_M_CRITICAL);

  return ESL_ERC_NO_ERROR;
}

/****************************************************************************
 * esl_generateSharedSecretDHEcP_prim
 ***************************************************************************/
/*!
 *
 * Internal comment removed.
 *
 *
 *
 */
VSECPRIM_FUNC(eslt_ErrorCode) esl_generateSharedSecretDHEcP_prim(VSECPRIM_P2VAR_PARA(eslt_WorkSpaceEcP) workSpace, VSECPRIM_P2CONST_PARA(eslt_Byte) privateKey,
                                                                 VSECPRIM_P2CONST_PARA(eslt_Byte) publicKey_x, VSECPRIM_P2CONST_PARA(eslt_Byte) publicKey_y,
                                                                 VSECPRIM_P2VAR_PARA(eslt_Byte) secret_x, VSECPRIM_P2VAR_PARA(eslt_Byte) secret_y)
{
  actRETURNCODE result;

  /* Check workSpace */
  if ((workSpace->header.status & ESL_WST_M_ALGO) != ESL_WST_ALGO_ECP)
  {
    return ESL_ERC_WS_STATE_INVALID;
  }
  if (!(workSpace->header.status & ESL_WST_M_RUNNING))
  {
    return ESL_ERC_WS_STATE_INVALID;
  }

  /* call actCLib EC-DH secret generation */
  result = actECDHGetSecret(privateKey, publicKey_x, publicKey_y, secret_x, secret_y, &workSpace->wsEcP);
  if (result != actOK)
  {
    if (result == actEXCEPTION_NULL)
    {
      return ESL_ERC_PARAMETER_INVALID;
    }
    else if (result == actEXCEPTION_PUBKEY)
    {
      return ESL_ERC_ECC_PUBKEY_INVALID;
    }
    else if (result == actEXCEPTION_PRIVKEY)
    {
      return ESL_ERC_ECC_PRIVKEY_INVALID;
    }
    else if (result == actEXCEPTION_UNKNOWN)
    {
      return ESL_ERC_ECC_INTERNAL_ERROR;
    }
  }

  return ESL_ERC_NO_ERROR;
}

/****************************************************************************
 * esl_initExchangeKeyDHEcP_key
 ***************************************************************************/
/*!
 *
 * Internal comment removed.
 *
 *
 *
 */
VSECPRIM_FUNC(eslt_ErrorCode) esl_initExchangeKeyDHEcP_key(VSECPRIM_P2VAR_PARA(eslt_WorkSpaceEcP) workSpace,
                                                           VSECPRIM_P2ROMCONST_PARA(eslt_EccDomain) domain, VSECPRIM_P2ROMCONST_PARA(eslt_EccDomainExt) domainExt)
{
  /* init actCLib EC-DH */
  actRETURNCODE result = actECDHInitKeyDerive(domain, domainExt, &workSpace->wsEcP, (int)(workSpace->header.size));
  if (result != actOK)
  {
    if (result == actEXCEPTION_NULL)
    {
      return ESL_ERC_PARAMETER_INVALID;
    }
    else if (result == actEXCEPTION_MEMORY)
    {
      return ESL_ERC_WS_TOO_SMALL;
    }
    else if (result == actEXCEPTION_DOMAIN)
    {
      return ESL_ERC_ECC_DOMAIN_INVALID;
    }
    else if (result == actEXCEPTION_DOMAIN_EXT)
    {
      return ESL_ERC_ECC_DOMAINEXT_INVALID;
    }
  }

  /* Set workSpace state */
  workSpace->header.status = (ESL_WST_ALGO_ECP | ESL_WST_M_RUNNING | ESL_WST_M_CRITICAL);

  return ESL_ERC_NO_ERROR;
}

/****************************************************************************
 * esl_exchangeKeyDHEcP_key
 ***************************************************************************/
/*!
 *
 * Internal comment removed.
 *
 *
 *
 */
VSECPRIM_FUNC(eslt_ErrorCode) esl_exchangeKeyDHEcP_key(VSECPRIM_P2VAR_PARA(eslt_WorkSpaceEcP) workSpace,
                                                       VSECPRIM_P2CONST_PARA(eslt_Byte) privateKey,
                                                       VSECPRIM_P2CONST_PARA(eslt_Byte) publicKey_x, VSECPRIM_P2CONST_PARA(eslt_Byte) publicKey_y,
                                                       eslt_Length infoLength, VSECPRIM_P2CONST_PARA(eslt_Byte) info,
                                                       eslt_Length iterationCount, eslt_Length keyLength, VSECPRIM_P2VAR_PARA(eslt_Byte) key)
{
  actRETURNCODE result;

  /* Check workSpace */
  if ((workSpace->header.status & ESL_WST_M_ALGO) != ESL_WST_ALGO_ECP)
  {
    return ESL_ERC_WS_STATE_INVALID;
  }
  if (!(workSpace->header.status & ESL_WST_M_RUNNING))
  {
    return ESL_ERC_WS_STATE_INVALID;
  }

  /* call actCLib EC-DH secret generation */
  result = actECDHKeyDerive(privateKey, publicKey_x, publicKey_y, (int)iterationCount, info, (int)infoLength, key, (int)keyLength, &workSpace->wsEcP);
  if (result != actOK)
  {
    if (result == actEXCEPTION_NULL)
    {
      return ESL_ERC_PARAMETER_INVALID;
    }
    else if (result == actEXCEPTION_PUBKEY)
    {
      return ESL_ERC_ECC_PUBKEY_INVALID;
    }
    else if (result == actEXCEPTION_PRIVKEY)
    {
      return ESL_ERC_ECC_PRIVKEY_INVALID;
    }
    else if (result == actEXCEPTION_UNKNOWN)
    {
      return ESL_ERC_ECC_INTERNAL_ERROR;
    }
  }

  return ESL_ERC_NO_ERROR;
}

# define VSECPRIM_STOP_SEC_CODE
# include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#endif /* (VSECPRIM_ECDH_GENERIC_ENABLED == STD_ON) */
/**********************************************************************************************************************
 *  END OF FILE: ESLib_DHEcP.c
 *********************************************************************************************************************/
