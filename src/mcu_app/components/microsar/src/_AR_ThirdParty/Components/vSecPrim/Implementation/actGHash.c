/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *  Copyright (c) 1999 - 2016 cv cryptovision GmbH.                                                All rights reserved.
 *
 *  For modifications by Vector Informatik GmbH:
 *  Copyright (c) 2020 by Vector Informatik GmbH.                                                  All rights reserved.
 *
 *                This software is protected under intellectual property laws and proprietary to cv cryptovision GmbH
 *                and/or Vector Informatik GmbH.
 *                No right or license is granted save as expressly set out in the applicable license conditions.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -----------------------------------------------------------------------------------------------------------------*/
/**        \file  actGHash.c
 *        \brief  GCM GHash implementation.
 *
 *      \details Currently the actClib version is used. 
 *               This file is part of the embedded systems library actCLib
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  REVISION HISTORY
 *  -------------------------------------------------------------------------------------------------------------------
 *  Refer to the module's header file.
 * 
 *  FILE VERSION
 *  -------------------------------------------------------------------------------------------------------------------
 *  Refer to module's header file.
 *********************************************************************************************************************/
#define ACTGHASH_SOURCE

/**********************************************************************************************************************
 *  INCLUDES
 *********************************************************************************************************************/
#include "actConfig.h"
#include "actIGHash.h"
#include "actUtilities.h"
#include "actWatchdog.h"

#if (VSECPRIM_ACTGHASH_ENABLED == STD_ON)

/**********************************************************************************************************************
 *  LOCAL DATA
 *********************************************************************************************************************/
#define VSECPRIM_START_SEC_CONST_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/*---------------------------------------------------------------------------*/
#if (actGHASH_SPEED_UP == 0)                              /* COV_VSECPRIM_GHASH_SPEED_UP XF */
/*---------------------------------------------------------------------------*/
/* no precomputation! */

/*---------------------------------------------------------------------------*/
#elif (actGHASH_SPEED_UP < 4)   /* 1..3 */                /* COV_VSECPRIM_GHASH_SPEED_UP TX */
/*---------------------------------------------------------------------------*/
/* Reduction table R:
 * This table was generated by computing the 2^i possible different 
 * XOR-combinations of the k-bit-shifted reduction polynomial 0xe1
 * for i = 4 and 0 <= k < i. 
 * Having this, one can shift by one blocksize of i bits and reduce by 
 * just XORing the table item with the out-shifted value as table index.
 */
VSECPRIM_ROM(VSECPRIM_LOCAL, actU16) R[16] = {    /* PRQA S 3218 */ /* MD_VSECPRIM_8.7 */
      0x0000, 0x1c20, 0x3840, 0x2460, 0x7080, 0x6ca0, 0x48c0, 0x54e0,
      0xe100, 0xfd20, 0xd940, 0xc560, 0x9180, 0x8da0, 0xa9c0, 0xb5e0
};

/*---------------------------------------------------------------------------*/
#else /* actGHASH_SPEED_UP >= 4 */
/*---------------------------------------------------------------------------*/
/* Reduction table R:
 * This table was generated by computing the 2^i possible different 
 * XOR-combinations of the k-bit-shifted reduction polynomial 0xe1
 * for i = 8 and 0 <= k < i. 
 * Having this, one can shift by one blocksize of i bits and reduce by 
 * just XORing the table item with the out-shifted value as table index.
 */
VSECPRIM_ROM(VSECPRIM_LOCAL, actU16) R[256] =     /* PRQA S 3218 */ /* MD_VSECPRIM_8.7 */
{
      0x0000, 0x01c2, 0x0384, 0x0246, 0x0708, 0x06ca, 0x048c, 0x054e,
      0x0e10, 0x0fd2, 0x0d94, 0x0c56, 0x0918, 0x08da, 0x0a9c, 0x0b5e,
      0x1c20, 0x1de2, 0x1fa4, 0x1e66, 0x1b28, 0x1aea, 0x18ac, 0x196e,
      0x1230, 0x13f2, 0x11b4, 0x1076, 0x1538, 0x14fa, 0x16bc, 0x177e,
      0x3840, 0x3982, 0x3bc4, 0x3a06, 0x3f48, 0x3e8a, 0x3ccc, 0x3d0e,
      0x3650, 0x3792, 0x35d4, 0x3416, 0x3158, 0x309a, 0x32dc, 0x331e,
      0x2460, 0x25a2, 0x27e4, 0x2626, 0x2368, 0x22aa, 0x20ec, 0x212e,
      0x2a70, 0x2bb2, 0x29f4, 0x2836, 0x2d78, 0x2cba, 0x2efc, 0x2f3e,
      0x7080, 0x7142, 0x7304, 0x72c6, 0x7788, 0x764a, 0x740c, 0x75ce,
      0x7e90, 0x7f52, 0x7d14, 0x7cd6, 0x7998, 0x785a, 0x7a1c, 0x7bde,
      0x6ca0, 0x6d62, 0x6f24, 0x6ee6, 0x6ba8, 0x6a6a, 0x682c, 0x69ee,
      0x62b0, 0x6372, 0x6134, 0x60f6, 0x65b8, 0x647a, 0x663c, 0x67fe,
      0x48c0, 0x4902, 0x4b44, 0x4a86, 0x4fc8, 0x4e0a, 0x4c4c, 0x4d8e,
      0x46d0, 0x4712, 0x4554, 0x4496, 0x41d8, 0x401a, 0x425c, 0x439e,
      0x54e0, 0x5522, 0x5764, 0x56a6, 0x53e8, 0x522a, 0x506c, 0x51ae,
      0x5af0, 0x5b32, 0x5974, 0x58b6, 0x5df8, 0x5c3a, 0x5e7c, 0x5fbe,
      0xe100, 0xe0c2, 0xe284, 0xe346, 0xe608, 0xe7ca, 0xe58c, 0xe44e,
      0xef10, 0xeed2, 0xec94, 0xed56, 0xe818, 0xe9da, 0xeb9c, 0xea5e,
      0xfd20, 0xfce2, 0xfea4, 0xff66, 0xfa28, 0xfbea, 0xf9ac, 0xf86e,
      0xf330, 0xf2f2, 0xf0b4, 0xf176, 0xf438, 0xf5fa, 0xf7bc, 0xf67e,
      0xd940, 0xd882, 0xdac4, 0xdb06, 0xde48, 0xdf8a, 0xddcc, 0xdc0e,
      0xd750, 0xd692, 0xd4d4, 0xd516, 0xd058, 0xd19a, 0xd3dc, 0xd21e,
      0xc560, 0xc4a2, 0xc6e4, 0xc726, 0xc268, 0xc3aa, 0xc1ec, 0xc02e,
      0xcb70, 0xcab2, 0xc8f4, 0xc936, 0xcc78, 0xcdba, 0xcffc, 0xce3e,
      0x9180, 0x9042, 0x9204, 0x93c6, 0x9688, 0x974a, 0x950c, 0x94ce,
      0x9f90, 0x9e52, 0x9c14, 0x9dd6, 0x9898, 0x995a, 0x9b1c, 0x9ade,
      0x8da0, 0x8c62, 0x8e24, 0x8fe6, 0x8aa8, 0x8b6a, 0x892c, 0x88ee,
      0x83b0, 0x8272, 0x8034, 0x81f6, 0x84b8, 0x857a, 0x873c, 0x86fe,
      0xa9c0, 0xa802, 0xaa44, 0xab86, 0xaec8, 0xaf0a, 0xad4c, 0xac8e,
      0xa7d0, 0xa612, 0xa454, 0xa596, 0xa0d8, 0xa11a, 0xa35c, 0xa29e,
      0xb5e0, 0xb422, 0xb664, 0xb7a6, 0xb2e8, 0xb32a, 0xb16c, 0xb0ae,
      0xbbf0, 0xba32, 0xb874, 0xb9b6, 0xbcf8, 0xbd3a, 0xbf7c, 0xbebe
};

#endif

#define VSECPRIM_STOP_SEC_CONST_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


#define VSECPRIM_START_SEC_CODE
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 *  LOCAL FUNCTION PROTOTYPES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  gf128_zero()
 **********************************************************************************************************************/
/*! \brief         Zeroes a GF128 block.
 *  \details       -
 *  \param[in,out] Z  The GF128 block that shall be zeroed.
 *  \pre           Z must point to a valid buffer of at least the size (4 * sizeof(actU32)) bytes
 *  \context       ANY
 *  \reentrant     TRUE
 *  \synchronous   TRUE
 *********************************************************************************************************************/
VSECPRIM_LOCAL_FUNC(void) gf128_zero(VSECPRIM_P2VAR_PARA(actU32) Z);

/**********************************************************************************************************************
 *  gf128_add()
 **********************************************************************************************************************/
/*! \brief         XOR two blocks in GF(2^128).
 *  \details       This function computes Z = X + Y for full blocks X and Y, which is a simple XOR in GF(2^128).
 *  \param[out]    Z  Result of XOR operation.
 *  \param[in]     X  First operand
 *  \param[in]     Y  Second operand
 *  \pre           Z, X, Y must point to valid buffers each of at least the size (4 * sizeof(actU32)) bytes
 *  \context       ANY
 *  \reentrant     TRUE
 *  \synchronous   TRUE
 *********************************************************************************************************************/
VSECPRIM_LOCAL_FUNC(void) gf128_add(
  VSECPRIM_P2VAR_PARA(actU32) Z,
  VSECPRIM_P2CONST_PARA(actU32) X,
  VSECPRIM_P2CONST_PARA(actU32) Y);

/**********************************************************************************************************************
 *  gf128_double()
 **********************************************************************************************************************/
/*! \brief         Bitshift in GF(2^128).
 *  \details       Compute Z = 2 * X for a full block X, which is a simple bitshift in GF(2^128).
 *                 If the result is greater than the field polynomial, it is reduced.
 *  \param[out]    Z  Output block
 *  \param[in]     X  Input block
 *  \pre           Z, X must must point to valid buffers each of at least the size (4 * sizeof(actU32)) bytes
 *  \context       ANY
 *  \reentrant     TRUE
 *  \synchronous   TRUE
 *********************************************************************************************************************/
VSECPRIM_LOCAL_FUNC(void) gf128_double(
  VSECPRIM_P2VAR_PARA(actU32) Z,
  VSECPRIM_P2CONST_PARA(actU32) X);

/*---------------------------------------------------------------------------*/
#if (actGHASH_SPEED_UP == 0)                              /* COV_VSECPRIM_GHASH_SPEED_UP XF */
/*---------------------------------------------------------------------------*/

/**********************************************************************************************************************
 *  actGF128Mul()
 **********************************************************************************************************************/
/*! \brief         Performs a multiplication in GF(2^128).
 *  \details       This function performs a multiplication Z = X * H, with Z in GF(2^128),
 *                 using the GCM reduction polynomial R = 1 + a + a^2 + a^7 + a^128.
 *  \param[in]     info  pointer to context structure
 *  \param[out]    Z  pointer to memory location for the result, Z = X * H with H being the internally stored sub-key
 *  \param[in]     X  pointer to the polynomial to multiply
 *  \pre           info must be a valid workspace pointer
 *                 Z, X must point to valid buffers each of at least the size (4 * sizeof(actU32)) bytes
 *  \context       ANY
 *  \reentrant     TRUE
 *  \synchronous   TRUE
 *********************************************************************************************************************/
VSECPRIM_LOCAL_FUNC(void) actGF128Mul(
  VSECPRIM_P2CONST_PARA(actGHASHSTRUCT) info,
  VSECPRIM_P2VAR_PARA(actU32) Z,
  VSECPRIM_P2CONST_PARA(actU8) X);

/*---------------------------------------------------------------------------*/
#elif (actGHASH_SPEED_UP < 4)   /* 1..3 */                /* COV_VSECPRIM_GHASH_SPEED_UP TX */
/*---------------------------------------------------------------------------*/

/**********************************************************************************************************************
 *  gf128_double()
 **********************************************************************************************************************/
/*! \brief         Rightshift by 4 bit positions.
 *  \details       Rightshift by 4 bit positions. Instead of reducing directly, return the
 *                 bits that were shifted out.
 *  \param[in,out] block  Output block
 *  \pre           block must must point to valid buffers of at least the size (4 * sizeof(actU32)) bytes
 *  \context       ANY
 *  \reentrant     TRUE
 *  \synchronous   TRUE
 *********************************************************************************************************************/
VSECPRIM_LOCAL_FUNC(actU8) gf128_rightshift_4(VSECPRIM_P2VAR_PARA(actU32) block);

/**********************************************************************************************************************
 *  actGF128Mul()
 **********************************************************************************************************************/
/*! \brief         Performs a multiplication in GF(2^128) speeded up by using a rightshift by 4 bit positions.
 *  \details       This function performs a multiplication Z = X * H, with Z in GF(2^128),
 *                 using the GCM reduction polynomial R = 1 + a + a^2 + a^7 + a^128. Speeding up is achieved by using 
 *                 a rightshift by 4 bit positions.
 *  \param[in]     info  pointer to context structure
 *  \param[out]    Z  pointer to memory location for the result, Z = X * H with H being the internally stored sub-key
 *  \param[in]     X  pointer to the polynomial to multiply
 *  \pre           info must be a valid workspace pointer
 *                 Z, X must point to valid buffers each of at least the size (4 * sizeof(actU32)) bytes
 *  \context       ANY
 *  \reentrant     TRUE
 *  \synchronous   TRUE
 *********************************************************************************************************************/
VSECPRIM_LOCAL_FUNC(void) actGF128Mul(
  VSECPRIM_P2CONST_PARA(actGHASHSTRUCT) info,
  VSECPRIM_P2VAR_PARA(actU32) Z,
  VSECPRIM_P2CONST_PARA(actU8) X);

/*---------------------------------------------------------------------------*/
#else /* actGHASH_SPEED_UP >= 4 */
/*---------------------------------------------------------------------------*/

/* Rightshift by 8 bit positions. Instead of reducing directly, return the
 * bits that were shifted out.
 */
/**********************************************************************************************************************
 *  gf128_double()
 **********************************************************************************************************************/
/*! \brief         Bitshift in GF(2^128).
 *  \details       Compute Z = 2 * X for a full block X, which is a simple bitshift in GF(2^128).
 *                 If the result is greater than the field polynomial, it is reduced.
 *  \param[out]    Z  Output block
 *  \param[in]     X  Input block
 *  \pre           Z, X must must point to valid buffers each of at least the size (4 * sizeof(actU32)) bytes
 *  \context       ANY
 *  \reentrant     TRUE
 *  \synchronous   TRUE
 *********************************************************************************************************************/
VSECPRIM_LOCAL_FUNC(actU8) gf128_rightshift_8(VSECPRIM_P2VAR_PARA(actU32) block);

/**********************************************************************************************************************
 *  actGF128Mul()
 **********************************************************************************************************************/
/*! \brief         Performs a multiplication in GF(2^128) speeded up by using a rightshift by 8 bit positions.
 *  \details       This function performs a multiplication Z = X * H, with Z in GF(2^128),
 *                 using the GCM reduction polynomial R = 1 + a + a^2 + a^7 + a^128. Speeding up is achieved by using 
 *                 a rightshift by 8 bit positions.
 *  \param[in]     info  pointer to context structure
 *  \param[out]    Z  pointer to memory location for the result, Z = X * H with H being the internally stored sub-key
 *  \param[in]     X  pointer to the polynomial to multiply
 *  \pre           info must be a valid workspace pointer
 *                 Z, X must point to valid buffers each of at least the size (4 * sizeof(actU32)) bytes
 *  \context       ANY
 *  \reentrant     TRUE
 *  \synchronous   TRUE
 *********************************************************************************************************************/
VSECPRIM_LOCAL_FUNC(void) actGF128Mul(
  VSECPRIM_P2CONST_PARA(actGHASHSTRUCT) info,
  VSECPRIM_P2VAR_PARA(actU32) Z,
  VSECPRIM_P2CONST_PARA(actU8) X);

/*---------------------------------------------------------------------------*/
#endif
/*---------------------------------------------------------------------------*/

/**********************************************************************************************************************
 *  actGHashTransformBlock()
 **********************************************************************************************************************/
/*! \brief         GHashes a complete input block.
 *  \details       This function GHashes a complete input block and stores the result internally.
 *  \param[in,out] info  pointer to context structure
 *  \param[in]     X  pointer to input block, which is expected to be a complete block of actGHASH_BLOCK_SIZE
 *  \pre           info must be a valid workspace pointer
 *                 X must be a valid pointer
 *  \context       ANY
 *  \reentrant     TRUE, for different workspaces
 *  \synchronous   TRUE
 *********************************************************************************************************************/
VSECPRIM_LOCAL_FUNC(void) actGHashTransformBlock(
  VSECPRIM_P2VAR_PARA(actGHASHSTRUCT) info,
  VSECPRIM_P2CONST_PARA(actU8) X);

/**********************************************************************************************************************
 *  LOCAL FUNCTIONS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  gf128_zero()
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 */
VSECPRIM_LOCAL_FUNC(void) gf128_zero(VSECPRIM_P2VAR_PARA(actU32) Z)
{
  Z[3] = 0; /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
  Z[2] = 0; /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
  Z[1] = 0; /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
  Z[0] = 0; /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
}

/**********************************************************************************************************************
 *  gf128_add()
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 */
VSECPRIM_LOCAL_FUNC(void) gf128_add (
   VSECPRIM_P2VAR_PARA(actU32) Z,
   VSECPRIM_P2CONST_PARA(actU32) X,
   VSECPRIM_P2CONST_PARA(actU32) Y)
{
  Z[3] = X[3] ^ Y[3]; /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
  Z[2] = X[2] ^ Y[2]; /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
  Z[1] = X[1] ^ Y[1]; /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
  Z[0] = X[0] ^ Y[0]; /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
}

/**********************************************************************************************************************
 *  gf128_double()
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 *
 */
VSECPRIM_LOCAL_FUNC(void) gf128_double (
   VSECPRIM_P2VAR_PARA(actU32) Z,
   VSECPRIM_P2CONST_PARA(actU32) X)
{
  actU8 carry = (actU8)(X[3] & 1uL);

  Z[3] = (X[3] >> 1) | ((X[2] & 1uL) << 31);  /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
  Z[2] = (X[2] >> 1) | ((X[1] & 1uL) << 31);  /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
  Z[1] = (X[1] >> 1) | ((X[0] & 1uL) << 31);  /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
  Z[0] = (X[0] >> 1);                         /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
  if (carry > 0u)
  {
    Z[0] ^= GF128POLY;                        /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
  }
}

/*---------------------------------------------------------------------------*/
#if (actGHASH_SPEED_UP == 0)                              /* COV_VSECPRIM_GHASH_SPEED_UP XF */
/*---------------------------------------------------------------------------*/

/**********************************************************************************************************************
 *  actGF128Mul()
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 *
 */
VSECPRIM_LOCAL_FUNC(void) actGF128Mul (
   VSECPRIM_P2CONST_PARA(actGHASHSTRUCT) info,
   VSECPRIM_P2VAR_PARA(actU32) Z,
   VSECPRIM_P2CONST_PARA(actU8) X)
{
  /* hash sub-key */
   actU32 H [actGHASH_WORDS_PER_BLOCK];
   actU8 i;
   actU8 j;
   actU8 X_i;

   /* Z = 0 */
   gf128_zero (Z);                /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER */
   /* H = 0 + H */
   gf128_add  (H, Z, info->H);    /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER */

   /* loop over X, MSB...LSB */
   for (i = 0; i < actGHASH_BLOCK_SIZE; i++)
   {

      /* next byte */
      X_i = X [i];
      j = actGCM_BYTE_SIZE;
      /* loop over next byte, MSbit...LSbit */
      do
      {
         j--;
         if ((X_i & 0x80u) == 0x80u)
         {
           /* if (bit set) Z += H */
            gf128_add(Z, Z, H);   /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER */
         }
         X_i <<= 1;
         /* H *= 2 */
         gf128_double(H, H);      /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER */
      } while (j > 0u);
   }
}

/*---------------------------------------------------------------------------*/
#elif (actGHASH_SPEED_UP < 4)   /* 1..3 */                /* COV_VSECPRIM_GHASH_SPEED_UP TX */
/*---------------------------------------------------------------------------*/

/**********************************************************************************************************************
 *  gf128_rightshift_4()
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 *
 */
VSECPRIM_LOCAL_FUNC(actU8) gf128_rightshift_4(VSECPRIM_P2VAR_PARA(actU32) block)
{
  actU8 v = (actU8)(block[actGHASH_WORDS_PER_BLOCK - 1u] & 0xFu);

   /* Right-shift the given block by 4 bit positions. 
    * Do not reduce, but return the bits that were shifted out! */
   block[3] = (block[3] >> 4) | ((block[2] & 0xFuL) << 28); /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
   block[2] = (block[2] >> 4) | ((block[1] & 0xFuL) << 28); /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
   block[1] = (block[1] >> 4) | ((block[0] & 0xFuL) << 28); /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
   block[0] >>= 4;                                          /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */

   return v;
}

/**********************************************************************************************************************
 *  actGF128Mul()
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 *
 *
 */
VSECPRIM_LOCAL_FUNC(void) actGF128Mul (
  VSECPRIM_P2CONST_PARA(actGHASHSTRUCT) info,
   VSECPRIM_P2VAR_PARA(actU32) Z,
   VSECPRIM_P2CONST_PARA(actU8) X)
{
  actU8 i;
  actU8 X_i, A;

  /* Z = 0 */
  gf128_zero (Z);                           /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER */

  i = actGHASH_BLOCK_SIZE;
  do /* loop over X: MSB ... LSB */
  {
    /* next byte */
    i--;
    X_i = X[i];

    /* Z *= 16 */
    A = gf128_rightshift_4 (Z);             /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER */
    /* handle carry bits */
    Z [0] ^= (((actU32) R [A]) << 16);      /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
    /* Z += (X * (4 MSbits)) */
    gf128_add (Z, Z, info->M [X_i & 0xfu]); /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER */

    /* Z *= 16 */
    A = gf128_rightshift_4 (Z);             /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER */
    /* handle carry bits */
    Z [0] ^= (((actU32) R [A]) << 16);      /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
    /* Z += (X * (4 next bits)) */
    gf128_add (Z, Z, info->M [X_i >> 4]);   /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER */
  } while (i > 0u);
}

/*---------------------------------------------------------------------------*/
#else /* actGHASH_SPEED_UP >= 4 */
/*---------------------------------------------------------------------------*/

/**********************************************************************************************************************
 *  gf128_rightshift_8()
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 *
 */
VSECPRIM_LOCAL_FUNC(actU8) gf128_rightshift_8(VSECPRIM_P2VAR_PARA(actU32) block)
{
  actU8 v = (actU8)(block[actGHASH_WORDS_PER_BLOCK - 1u] & 0xffu);

  /* Right-shift the given block by 8 bit positions. 
   * Do not reduce, but return the bits that were shifted out! */
  block[3] = (block[3] >> 8) | ((block[2] & 0xffu) << 24);  /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
  block[2] = (block[2] >> 8) | ((block[1] & 0xffu) << 24);  /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
  block[1] = (block[1] >> 8) | ((block[0] & 0xffu) << 24);  /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
  block[0] >>= 8;                                           /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */

  return v;
}

/**********************************************************************************************************************
 *  actGF128Mul()
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 *
 *
 */
VSECPRIM_LOCAL_FUNC(void) actGF128Mul (
  VSECPRIM_P2CONST_PARA(actGHASHSTRUCT) info,
  VSECPRIM_P2VAR_PARA(actU32) Z,
  VSECPRIM_P2CONST_PARA(actU8) X)
{
  actU8 i, A;

  /* Z = 0 */
  gf128_zero(Z);                      /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER */

  i = actGHASH_BLOCK_SIZE;
  /* loop over X, MSB...LSB */
  do
  {
    i--;
    /* Z *= 256 */
    A = gf128_rightshift_8(Z);        /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER */
    /* handle carry bits */
    Z[0] ^= (((actU32)R[A]) << 16);   /* SBSW_VSECPRIM_ARRAY_WRITE_ACCESS_PASSED_BUFFER_FIXED_INDEX */
    /* Z += (X * (next byte)) */
    gf128_add(Z, Z, info->M[X[i]]);   /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER */
  } while (i > 0u);
}

/*---------------------------------------------------------------------------*/
#endif
/*---------------------------------------------------------------------------*/

/**********************************************************************************************************************
 *  actGHashTransformBlock()
 **********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 *
 */
VSECPRIM_LOCAL_FUNC(void) actGHashTransformBlock (
   VSECPRIM_P2VAR_PARA(actGHASHSTRUCT) info,
   VSECPRIM_P2CONST_PARA(actU8) X)
{
  actU32 tmpY[actGHASH_WORDS_PER_BLOCK];

  /* Y_i = Y_i-1 XOR X_i */
  actXOR(info->Y, X, actGHASH_BLOCK_SIZE); /* PRQA S 0315 */ /* MD_VSECPRIM_P2VOID_CAST */ /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER_FIXED_SIZE */
  /* Y_i = Y_i * H */
  actGF128Mul(info, tmpY, info->Y); /* SBSW_VSECPRIM_FCT_CALL_LOCAL_BUFFER */

  /* Convert actU32 to actU8 and store internally */
  actCpyU32toBE (info->Y, tmpY, actGHASH_WORDS_PER_BLOCK); /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER_FIXED_SIZE */
}

/**********************************************************************************************************************
 *  GLOBAL FUNCTIONS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  actGHashReset()
 *********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 */
VSECPRIM_FUNC(actRETURNCODE) actGHashReset(VSECPRIM_P2VAR_PARA(actGHASHSTRUCT) info)
{
  /* Y = 0^128 */
  actMemset(info->Y, 0, actGHASH_BLOCK_SIZE); /* PRQA S 0315 */ /* MD_VSECPRIM_P2VOID_CAST */ /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER_FIXED_SIZE */

  info->buffer_used = 0; /* SBSW_VSECPRIM_PTR_WRITE_ACCESS_PASSED_BUFFER */

  return actOK;
}

/**********************************************************************************************************************
 *  actGHashInit()
 *********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 *
 *
 */
VSECPRIM_FUNC(actRETURNCODE) actGHashInit (
   VSECPRIM_P2VAR_PARA(actGHASHSTRUCT) info,
   VSECPRIM_P2CONST_PARA(actU8) H,
   VSECPRIM_P2FUNC(VSECPRIM_NONE, void, watchdog)(void))
{
#if (actGHASH_SPEED_UP > 0)                               /* COV_VSECPRIM_GHASH_SPEED_UP TX */
  actU16 i, j;
#endif

   /* Store the subkey internally. */
   actCpyBEtoU32 (info->H, H, actGHASH_BLOCK_SIZE); /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER_FIXED_SIZE */

/*---------------------------------------------------------------------------*/
#if (actGHASH_SPEED_UP == 0)                              /* COV_VSECPRIM_GHASH_SPEED_UP XF */
/*---------------------------------------------------------------------------*/
  /* no precomputation! */

/*---------------------------------------------------------------------------*/
#elif (actGHASH_SPEED_UP < 4)   /* 1..3 */                /* COV_VSECPRIM_GHASH_SPEED_UP TX */
/*---------------------------------------------------------------------------*/
   /* Algorithm 3 from the paper for pre-computing the lookup-table */
   /* somewhat awkward indexing, as the bit-order of the index is inverted (little-endian) */
   /* must be adhered to for the powers of two only, because an addition is just an XOR! */
   gf128_zero    (info->M [0]);                           /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER_FIXED_SUBADDRESS */
   actCpyBEtoU32 (info->M [8], H, actGHASH_BLOCK_SIZE);   /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER_FIXED_SUBADDRESS_FIXED_SIZE */

   /* Powers of 2 by shifting */
   for (i = 4u; i > 0u; i /= 2u)
   {
      gf128_double(info->M[i], info->M[2u * i]);          /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER_VARIABLE_SUBADDRESS */
   }
   /* Compose the rest by adding */
   for (i = 2u; i <= 8u; i *= 2u)
   {
      for (j = 1u; j < i; j++)
      {
         gf128_add(info->M[i+j], info->M[i], info->M[j]); /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER_VARIABLE_SUBADDRESS */
      }
   }

/*---------------------------------------------------------------------------*/
#else /* actGHASH_SPEED_UP >= 4 */
/*---------------------------------------------------------------------------*/
   /* Algorithm 3 from the paper for pre-computing the lookup-table */
   /* somewhat awkward indexing, as the bit-order of the index is inverted (little-endian) */
   /* must be adhered to for the powers of two only, because an addition is just an XOR! */
   gf128_zero    (info->M [0]);                           /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER_FIXED_SUBADDRESS */
   actCpyBEtoU32 (info->M [128], H, actGHASH_BLOCK_SIZE); /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER_FIXED_SUBADDRESS_FIXED_SIZE */

   /* Powers of 2 by shifting */
   for (i = 64; i > 0u; i /= 2u)
   {
      gf128_double(info->M[i], info->M[2u * i]);          /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER_VARIABLE_SUBADDRESS */
   }
   /* Compose the rest by adding */
   for (i = 2; i <= 128u; i *= 2u)
   {
      for (j = 1; j < i; j++)
      {
         gf128_add(info->M[i+j], info->M[i], info->M[j]); /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER_VARIABLE_SUBADDRESS */
      }
   }
/*---------------------------------------------------------------------------*/
#endif
/*---------------------------------------------------------------------------*/

  actL1trigger(watchdog); /* PRQA S 1338, 2983, 3112  */ /* MD_MSR_DummyStmt */ /*lint -e438 */  /* SBSW_VSECPRIM_FUNCTION_CALL_WATCHDOG */
  return actGHashReset(info); /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER */
}

/**********************************************************************************************************************
 *  actGHashUpdate()
 *********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 *
 *
 */
VSECPRIM_FUNC(actRETURNCODE) actGHashUpdate (
   VSECPRIM_P2VAR_PARA(actGHASHSTRUCT) info,
   VSECPRIM_P2CONST_PARA(actU8) X,
   actLengthType X_len,
   VSECPRIM_P2FUNC(VSECPRIM_NONE, void, watchdog)(void))
{
  actLengthType diff = 0;
  boolean isUpdateFinished = FALSE;

  actL1trigger(watchdog); /* PRQA S 1338, 2983, 3112  */ /* MD_MSR_DummyStmt */ /*lint -e438 */  /* SBSW_VSECPRIM_FUNCTION_CALL_WATCHDOG */

  /* left overs of the last update */
  if (info->buffer_used > 0u)
  {
    diff = actMin(X_len, (actLengthType)(actGHASH_BLOCK_SIZE - info->buffer_used));
    actMemcpy(&info->buffer[info->buffer_used], X, diff); /* PRQA S 0315 */ /* MD_VSECPRIM_P2VOID_CAST */ /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER_VARIABLE_SUBADDRESS_VARIABLE_SIZE */
    info->buffer_used += diff; /* SBSW_VSECPRIM_PTR_WRITE_ACCESS_PASSED_BUFFER */
    if (info->buffer_used < actGHASH_BLOCK_SIZE)
    {
      isUpdateFinished = TRUE;
    }
    else
    {
      actGHashTransformBlock(info, info->buffer); /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER */
      actL2trigger(watchdog); /* PRQA S 1338, 2983, 3112  */ /* MD_MSR_DummyStmt */ /*lint -e438 */ /* SBSW_VSECPRIM_FUNCTION_CALL_WATCHDOG */
    }
  }

  if (isUpdateFinished == FALSE)
  {
    actLengthType i, blocks, X_idx;

    /* loop through (remaining) X, blockwise */
    blocks = (X_len - diff) / actGHASH_BLOCK_SIZE;
    X_idx = diff;

    for (i = blocks; i > 0u; --i)
    {
      actGHashTransformBlock(info, &X[X_idx]); /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER */
      X_idx += actGHASH_BLOCK_SIZE;
      actL2trigger(watchdog); /* PRQA S 1338, 2983, 3112  */ /* MD_MSR_DummyStmt */ /*lint -e438 */ /* SBSW_VSECPRIM_FUNCTION_CALL_WATCHDOG */
    }

    /* copy left overs into buffer */
    info->buffer_used = X_len - (diff + (actGHASH_BLOCK_SIZE * blocks)); /* SBSW_VSECPRIM_PTR_WRITE_ACCESS_PASSED_BUFFER */
    actMemcpy(info->buffer, &X[X_idx], info->buffer_used); /* PRQA S 0315 */ /* MD_VSECPRIM_P2VOID_CAST */ /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER_VARIABLE_SIZE */

    actL1trigger(watchdog); /* PRQA S 1338, 2983, 3112  */ /* MD_MSR_DummyStmt */ /*lint -e438 */  /* SBSW_VSECPRIM_FUNCTION_CALL_WATCHDOG */
  }

  return actOK;
}

/**********************************************************************************************************************
 *  actGHashZeroPad()
 *********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 *
 */
VSECPRIM_FUNC(actRETURNCODE) actGHashZeroPad (
   VSECPRIM_P2VAR_PARA(actGHASHSTRUCT) info,
   VSECPRIM_P2FUNC(VSECPRIM_NONE, void, watchdog)(void))
{
  if (info->buffer_used > 0u)
  {
    /* Pad buffer with zeroes */
    actMemset(&info->buffer[info->buffer_used], 0, actGHASH_BLOCK_SIZE - info->buffer_used); /* PRQA S 0315 */ /* MD_VSECPRIM_P2VOID_CAST */ /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER_VARIABLE_SUBADDRESS_VARIABLE_SIZE */
    /* Hash the buffer block */
    actGHashTransformBlock(info, info->buffer);/* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER */
    /*    actL2trigger(watchdog);  */ /* WD is triggered at end of function anyway! */
    /* Reset buffer_used */
    info->buffer_used = 0; /* SBSW_VSECPRIM_PTR_WRITE_ACCESS_PASSED_BUFFER */
  }

  actL1trigger(watchdog); /* PRQA S 1338, 2983, 3112  */ /* MD_MSR_DummyStmt */ /*lint -e438 */  /* SBSW_VSECPRIM_FUNCTION_CALL_WATCHDOG */
  return actOK;
}

/**********************************************************************************************************************
 *  actGHashFinalize()
 *********************************************************************************************************************/
/*!
 * Internal comment removed.
 *
 *
 *
 */
VSECPRIM_FUNC(actRETURNCODE) actGHashFinalize (
   VSECPRIM_P2VAR_PARA(actGHASHSTRUCT) info,
   actU8 hash[actGHASH_BLOCK_SIZE],
   VSECPRIM_P2FUNC(VSECPRIM_NONE, void, watchdog)(void))
{
  /* zero-pad and hash possible leftovers in the buffer */
  if (info->buffer_used > 0u)
  {
    (void)actGHashZeroPad(info, watchdog); /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER */
  }

  actMemcpy(hash, info->Y, actGHASH_BLOCK_SIZE); /* PRQA S 0315 */ /* MD_VSECPRIM_P2VOID_CAST */ /* SBSW_VSECPRIM_FCT_CALL_PASSED_BUFFER_FIXED_SIZE */

  actL1trigger(watchdog); /* PRQA S 1338, 2983, 3112  */ /* MD_MSR_DummyStmt */ /*lint -e438 */  /* SBSW_VSECPRIM_FUNCTION_CALL_WATCHDOG */
  return actOK;
}

#define VSECPRIM_STOP_SEC_CODE
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#endif /* (VSECPRIM_ACTGHASH_ENABLED == STD_ON) */

/**********************************************************************************************************************
 *  END OF FILE: actGHash.c
 *********************************************************************************************************************/
