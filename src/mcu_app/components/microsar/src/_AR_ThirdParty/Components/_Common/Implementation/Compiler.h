/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *  Copyright (c) 2020 by Vector Informatik GmbH.                                              All rights reserved.
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *         File:  Compiler.h
 *    Component:  -
 *       Module:  -
 *    Generator:  -
 *
 *  Description:  This file provides the AUTOSAR compiler abstraction for the TI compiler
 *                   Supported compiler:           TI
 *                   Supported compiler version:   4.4.3 and 4.4.4
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  REVISION HISTORY
 *  -------------------------------------------------------------------------------------------------------------------
 *  Version   Date        Author  Change Id     Description
 *  -------------------------------------------------------------------------------------------------------------------
 *  01.00.00  2007-08-09  Jk                    Initial creation
 *  01.00.01  2007-10-22  kbr                   customized for TI TMS570
 *  01.00.02  2007-11-27  Jk                    Version defines for CFG management added
 *  01.01.00  2009-05-19  Ht                    Support ASR 3.0
 *  01.01.01  2012-04-05  Pl                    Support of FUNC_P2CONST and FUNC_P2VAR added
 *  01.01.02  2012-07-12  Pl                    Support of LOCAL_INLINE added
 *  02.00.00  2015-03-27  vispl                 Setup for CommonAsr_Arm32
 *                                              Adaption to newest template (v1.05.01)
 *  02.01.00  2020-11-19  visbwa                Removed AUTHOR IDENTITY, changed filter mechanism (Product instead of
 *                                              AutosarVersion)
 *********************************************************************************************************************/

#ifndef COMPILER_H
#define COMPILER_H

/* PRQA S 3453 COMPILER_3453_TAG */ /* MD_MSR_19.7 */

/**********************************************************************************************************************
 * INCLUDES
 *********************************************************************************************************************/
#include "Compiler_Cfg.h"
#if defined(ZX_IDC_SOC_RENESAS_V4H) || defined(ZX_IDC_SOC_HOBOT_J6E)
#include "arm_cr.h"
#endif

/**********************************************************************************************************************
 *  GLOBAL CONSTANT MACROS
 *********************************************************************************************************************/
/* ##V_CFG_MANAGEMENT ##CQProject : CommonAsr_Arm32 CQComponent : Impl_CompAbstract_TexasInstruments */
#define COMMONASR_ARM32_IMPL_COMPABSTRACT_VERSION 0x0201
#define COMMONASR_ARM32_IMPL_COMPABSTRACT_RELEASE_VERSION 0x00

#define COMPILER_VENDOR_ID    30u   /* SREQ00015523 */
#define COMPILER_MODULE_ID    198u  /* SREQ00015523 */

/* AUTOSAR Software Specification Release Version Information */
/*            AUTOSAR Release 4.0 R3                          */
#define COMPILER_AR_RELEASE_MAJOR_VERSION       (4u)
#define COMPILER_AR_RELEASE_MINOR_VERSION       (0u)
#define COMPILER_AR_RELEASE_REVISION_VERSION    (3u)

/* Component Version Information */
#define COMPILER_SW_MAJOR_VERSION       (2u)
#define COMPILER_SW_MINOR_VERSION       (1u)
#define COMPILER_SW_PATCH_VERSION       (0u)

# define _TEXAS_INSTRUMENTS_C_ARM32_

/* AUTOMATIC used for the declaration of local pointers */
#define AUTOMATIC

/* TYPEDEF shall be used within type definitions, where no memory qualifier can be specified.*/
#define TYPEDEF

#ifndef STATIC
  #define STATIC  static
#endif

/* NULL_PTR define with a void pointer to zero definition*/
#ifndef NULL_PTR
  #define NULL_PTR  ((void *)0)
#endif

/* INLINE define for abstraction of the keyword inline */
#ifndef INLINE
  #if defined(ZX_IDC_SOC_RENESAS_V4H) || defined(ZX_IDC_SOC_HOBOT_J6E)
    #define INLINE  inline
  #else
    #define INLINE __inline
  #endif
#endif

/* LOCAL_INLINE define for abstraction of the keyword inline in functions with "static" scope.
   Different compilers may require a different sequence of the keywords "static" and "inline"
   if this is supported at all. */
#ifndef LOCAL_INLINE
  #if defined(ZX_IDC_SOC_RENESAS_V4H) || defined(ZX_IDC_SOC_HOBOT_J6E)
    #define LOCAL_INLINE    static inline
  #else
    #define LOCAL_INLINE    static
  #endif
#endif

#define NO_PROLOGUE  "ghs noprologue"

#define PRAGMA(x)    _Pragma(#x)

#define _INTERRUPT_

/* FUNC macro for the declaration and definition of functions, that ensures correct syntax of function declarations
   rettype     return type of the function
   memclass    classification of the function itself*/
#if (defined(ZX_IDC_SOC_RENESAS_V4H) || defined(ZX_IDC_SOC_HOBOT_J6E)) && defined(_USE_MEMCLASS_) 
  #define FUNC(rettype, memclass) memclass rettype
#else
  #define FUNC(rettype, memclass) rettype /* PRQA S 3410 */ /* MD_Compiler_19.10 */
#endif

/* FUNC_P2CONST macro for declaration and definition of functions returning a pointer to a constant, that ensures 
     correct syntax of function declarations.
   rettype     return type of the function
   ptrclass    defines the classification of the pointer�s distance
   memclass    classification of the function itself*/
#if (defined(ZX_IDC_SOC_RENESAS_V4H) || defined(ZX_IDC_SOC_HOBOT_J6E)) && defined(_USE_MEMCLASS_)
  #define FUNC_P2CONST(rettype, ptrclass, memclass) const ptrclass rettype * memclass
#else
  #define FUNC_P2CONST(rettype, ptrclass, memclass) const rettype* /* PRQA S 3410 */ /* MD_Compiler_19.10 */
#endif

/* FUNC_P2VAR macro for the declaration and definition of functions returning a pointer to a variable, that ensures
     correct syntax of function declarations
   rettype     return type of the function
   ptrclass    defines the classification of the pointer�s distance 
   memclass    classification of the function itself*/
#if (defined(ZX_IDC_SOC_RENESAS_V4H) || defined(ZX_IDC_SOC_HOBOT_J6E)) && defined(_USE_MEMCLASS_)
  #define FUNC_P2VAR(rettype, ptrclass, memclass) ptrclass rettype * memclass
#else
  #define FUNC_P2VAR(rettype, ptrclass, memclass) rettype* /* PRQA S 3410 */ /* MD_Compiler_19.10 */
#endif

/* P2VAR macro for the declaration and definition of pointers in RAM, pointing to variables
   ptrtype     type of the referenced variable memory class
   memclass    classification of the pointer variable itself
   ptrclass    defines the classification of the pointer�s distance
 */
#define P2VAR(ptrtype, memclass, ptrclass) ptrtype* /* PRQA S 3410 */ /* MD_Compiler_19.10 */

/* P2CONST macro for the declaration and definition of pointers in RAM pointing to constants
   ptrtype     type of the referenced data
   memclass    classification of the pointer variable itself
   ptrclass    defines the classification of the pointer distance
 */
#define P2CONST(ptrtype, memclass, ptrclass) const ptrtype* /* PRQA S 3410 */ /* MD_Compiler_19.10 */

/* CONSTP2VAR macro for the declaration and definition of constant pointers accessing variables
   ptrtype     type of the referenced data
   memclass    classification of the pointer variable itself
   ptrclass    defines the classification of the pointer distance
 */
#define CONSTP2VAR(ptrtype, memclass, ptrclass) ptrtype* const /* PRQA S 3410 */ /* MD_Compiler_19.10 */

/* CONSTP2CONST macro for the declaration and definition of constant pointers accessing constants
   ptrtype     type of the referenced data
   memclass    classification of the pointer variable itself
   ptrclass    defines the classification of the pointer distance
 */
#define CONSTP2CONST(ptrtype, memclass, ptrclass) const ptrtype* const /* PRQA S 3410 */ /* MD_Compiler_19.10 */

/* P2FUNC macro for the type definition of pointers to functions
   rettype     return type of the function
   ptrclass    defines the classification of the pointer distance
   fctname     function name respectively name of the defined type
 */
#define P2FUNC(rettype, ptrclass, fctname) rettype (* fctname) /* PRQA S 3410 */ /* MD_Compiler_19.10 */

/* CONSTP2FUNC macro for the type definition of pointers to functions
   rettype     return type of the function
   ptrclass    defines the classification of the pointer distance
   fctname     function name respectively name of the defined type
   Example (PowerPC): #define CONSTP2FUNC(rettype, ptrclass, fctname) rettype (* const fctname)
   Example (IAR, R32C): #define CONSTP2FUNC(rettype, ptrclass, fctname) rettype (ptrclass * const fctname)
 */
#define CONSTP2FUNC(rettype, ptrclass, fctname) rettype (* const fctname) /* PRQA S 3410 */ /* MD_Compiler_19.10 */

/* CONST macro for the declaration and definition of constants
   type        type of the constant
   memclass    classification of the constant itself
 */
#if (defined(ZX_IDC_SOC_RENESAS_V4H) || defined(ZX_IDC_SOC_HOBOT_J6E)) && defined(_USE_MEMCLASS_)
  #define CONST(type, memclass) memclass const type /* PRQA S 3410 */ /* MD_Compiler_19.10 */
#else
  #define CONST(type, memclass) const type /* PRQA S 3410 */ /* MD_Compiler_19.10 */
#endif

/* VAR macro for the declaration and definition of variables
   vartype        type of the variable
   memclass    classification of the variable itself
 */
#if (defined(ZX_IDC_SOC_RENESAS_V4H) || defined(ZX_IDC_SOC_HOBOT_J6E)) && defined(_USE_MEMCLASS_)
  #define VAR(vartype, memclass) memclass vartype
#else
  #define VAR(vartype, memclass) vartype /* PRQA S 3410 */ /* MD_Compiler_19.10 */
#endif

/* Inline assembler support for HALT instruction */
#define ASM_HALT() __asm("halt")

/* Inline assembler support for NOP instruction */
#define ASM_NOP() __asm("nop")

#define ENABLE_INTERRUPT()             __asm("cpsie i\n\t")
#define DISABLE_INTERRUPT()            __asm("cpsid i\n\t")

/* Inline assembler support for SYNCI instruction */
#define EXECUTE_SYNCI() __asm("nop")

/* Inline assembler support for syncp instruction */
#define EXECUTE_SYNCP()           __asm("nop")

/* Inline assembler support for syncm instruction */
#define EXECUTE_SYNCM()           __asm("nop")

#define __HAS_FPU__

/**********************************************************************************************************************
 *  GLOBAL FUNCTION MACROS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL DATA TYPES AND STRUCTURES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL DATA PROTOTYPES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL FUNCTION PROTOTYPES
 *********************************************************************************************************************/
/* PRQA L:COMPILER_3453_TAG */

/****************************************************************************/
/*  MISRA deviations                                                        */
/****************************************************************************/
/* Justification for module-specific MISRA deviations:
MD_Compiler_19.10:
  Reason: Macro used in structures, which depend on used compiler and which do not allow brackets e.g. declaration of functions and variables.
  Risk: None atomic parameter lead to compile errors.
  Prevention: Used parameter is always atomic.
*/

#endif /* COMPILER_H */

/**********************************************************************************************************************
 *  END OF FILE: Compiler.h
 *********************************************************************************************************************/
