/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *  Copyright (c) 2007 - 2013 by Vector Informatik GmbH.                                           All rights reserved.
 *
 *                Please note, that this file contains example configuration used by the 
 *                MICROSAR BSW. This code may influence the behaviour of the MICROSAR BSW
 *                in principle. Therefore, great care must be taken to verify
 *                the correctness of the implementation.
 *
 *                The contents of the originally delivered files are only examples respectively 
 *                implementation proposals. With regard to the fact that these functions
 *                are meant for demonstration purposes only, the liability of Vector Informatik
 *                shall be expressly excluded in cases of ordinary negligence, 
 *                to the extent admissible by law or statute.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *         File:  _Compiler_Cfg.h
 *    Component:  -
 *       Module:  -
 *    Generator:  -
 *
 *  Description:  This File is a template for the Compiler_Cfg.h
 *                This file has to be extended with the memory and pointer classes for all BSW modules
 *                which where used.
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  MISRA VIOLATIONS
 *  -------------------------------------------------------------------------------------------------------------------
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  AUTHOR IDENTITY
 *  -------------------------------------------------------------------------------------------------------------------
 *  Name                          Initials      Company
 *  -------------------------------------------------------------------------------------------------------------------
 *  Joachim Kalmbach              Jk            Vector Informatik GmbH
 *  Heike Honert                  Ht            Vector Informatik GmbH
 *  Eugen Stripling               Seu           Vector Informatik GmbH
 *  -------------------------------------------------------------------------------------------------------------------
 *  REVISION HISTORY
 *  -------------------------------------------------------------------------------------------------------------------
 *  Version   Date        Author  Change Id     Description
 *  -------------------------------------------------------------------------------------------------------------------
 *  01.00.00  2007-08-01  Jk                    Initial creation
 *  01.01.00  2007-12-14  Jk                    Component specific defines filtering added
 *  01.01.01  2008-12-17  Ht                    Improve list of components  (Tp_AsrTpCan,Tp_AsrTpFr,DrvMcu,DrvIcu added)
 *  01.01.02  2009-04-27  Ht                    support OEM specific _compiler_cfg.inc file, improve list of components 
 *                                              (Cp_XcpOnCanAsr, Il_AsrIpduM, If_VxFblDcm, If_VxFblVpm_Volvo_ab, DrvFls added)
 *  01.01.03  2009-04-24  Msr                   Renamed J1939_AsrBase as TpJ1939_AsrBase
 *  01.01.04  2009-06-03  Ht                    Improve list of components (Adc, Dio, Gpt, Pwm, Spi, Wdg, Fls, Port, Fim)
 *  01.02.00  2009-08-01  Ht                    Improve list of components (Fee_30_Inst2, Can, ...Sub)
 *                                              Support filtering for RTE
 *  01.02.01  2009-09-02  Lo                    add external Flash driver support
 *  01.02.02  2009-09-21  Lo                    add DrvFls_Mcs12xFslftm01ExtVx
 *                        Ht                    Improve list of components (CanTrcv_30_Tja1040dio,
 *                                                Eth, EthTrcv, EthIf, SoAd, TcpIp, EthSM)
 *  01.03.00  2009-10-30  Ht                    support R8: change EthTrcv to EthTrcv_30_Canoeemu
 *                                              support EthTrcv_30_Dp83848
 *                                              change CanTrcv_30_Xdio to CanTrcv_30___Your_Trcv__
 *                                              change CanTrcv_30_Tja1040dio to CanTrcv_30_Tja1041
 *                                              change name FrTrcv to FrTrcv_30_Tja1080dio
 *                        Lo                    add Cp_AsrXcp
 *                        Ht                    add Cp_XcpOnFrAsr
 *  01.03.01  2010-01-13  Ht                    support SysService_AsrCal
 *  01.03.02  2010-02-15  Ht                   support SysService_SswRcs_Daimler, SysService_Tls, Tp_Http, 
 *                                                      SysService_Dns, SysService_Json, DrvTrans_GenericLindioAsr
 *                        Lo                    add Diag_AsrDem for all OEMs
 *                                              rename internal variables and filter methods
 *  01.04.00  2010-03-04  Ht                    change name FrTrcv_30_Tja1080dio to FrTrcv_30_Tja1080
 *  01.04.01  2010-03-10  Ht                    support DrvTrans_GenericFrAsr, DrvTrans_As8223FrspiAsr, DrvEep and If_AsrIfEa
 *  01.04.02  2010-04-07  Lo                    change IfFee to real components and add If_AsrIfWdV85xNec01Sub
 *  01.04.03  2010-06-11  Ht                    add CanTrcv_30_Tja1043
 *                        Lo                    add Il_AsrIpduMEbBmwSub
 *  01.04.04  2010-08-24  Ht                    add CanTrcv_30_Tle62512G, DrvEep_XAt25128EAsr, Tp_AsrTpFrEbBmwSub
 *  01.05.00  2010-08-24  Ht                    support R10:
 *                                              change LinTrcv_30_Tle7259dio to LinTrcv_30_Tle7259
 *  01.05.01  2010-10-14  Ht                    add VStdLib, SysService_SswScc, SysService_IpBase, SysService_Crypto
 *  01.05.02  2010-10-20  Ht                    support comments for Package Merge Tool
 *  01.05.03  2010-11-03  Ht                    add SysService_E2eLibTttechSub, SysService_E2ePwTttechSub
 *  01.05.04  2010-11-16  Ht                    add SysService_Exi, DrvTrans_Int6400EthAsr, Cdd_AsrCdd_Fiat, Diag_AsrDem_Fiat,
 *  01.05.05  2010-12-17  Ht                    add SysService_AsrSchM, DrvEep_XXStubAsr, DrvIcu_Tms570Tinhet01ExtVx
 *                                                  DrvWd_XTle4278gEAsr, DrvWd_XXStubAsr
 *  01.05.06  2011-02-17  Ht                    add DrvEed, SysService_AsrBswM
 *  01.05.07  2011-03-04  Ht                    add DrvTrans_Tja1055CandioAsr
 *                                              rename CanTrcv_30_Tja1040dio to CanTrcv_30_Tja1040
 *                                              add SysService_XmlEngine
 *  01.06.00  2011-03-04  Ht                    support ASR4.0
 *                                              add Ccl_Asr4ComM, Ccl_Asr4SmCan, Nm_Asr4NmIf, Nm_AsrNmDirOsek
 *  01.06.01  2011-04-15  Ht                    add Diag_AsrDcm_<OEM>
 *  01.06.02  2011-06-17  Ht                    correct Diag_AsrDcm_<OEM>
 *                                              add Monitoring_AsrDlt and Monitoring_GenericMeasurement
 *  01.06.03  2011-09-01  Ht                    add DrvTrans_Tja1145CanSpiAsr, DrvTrans_E52013CanspiAsr, DrvFls_XXStubAsr,
 *                                              If_AsrIfFeeV85xNec05Sub, If_AsrIfFeeV85xNec06Sub, If_AsrIfFeeV85xNec07Sub
 *                                              SysService_AsrWdMTttechSub and If_AsrIfWdTttechSub
 *  01.06.04  2011-10-20  Ht                    ESCAN00054334: add If_AsrIfFeeTiSub
 *                                              ESCAN00054719: add Cdd_AsrCdd
 *  01.06.05  2011-12-09  Ht                    add Tp_IpV4, Tp_IpV6
 *  01.06.06  2011-12-14  Ht                    add Monitoring_RuntimeMeasurement
 *  01.06.07  2012-01-03  Ht                    add DrvI2c, SysService_Asr4BswM
 *  01.06.08  2012-01-31  Ht                    add DrvTrans_Ar7000EthAsr, DrvTrans_GenericEthmiiAsr
 *  01.06.09  2012-03-06  Ht                    add If_AsrIfFeeMb9df126Fuji01Sub, 
 *                                              Infineon_Tc1767Inf01Sub, Infineon_Tc178xInf01Sub, Infineon_Tc1797Inf01Sub, Infineon_Tc1797Inf02Sub
 *  01.06.10  2012-03-13  Ht                    add Gw_AsrPduRCfg5, Il_AsrComCfg5, Il_AsrIpduMCfg5, Cdd_AsrCddCfg5,
 *                                              Tp_Asr4TpCan, Diag_Asr4Dcm, Diag_Asr4Dem
 *  01.06.11  2012-03-20  Ht                    add Cp_AsrCcp, Cp_XcpOnTcpIpAsr
 *  01.07.00  2012-07-26  Ht                    add Nm_Asr4NmCan, Nm_Asr4NmFr, Infineon_Xc2000Inf01Sub, Ccl_Asr4ComMCfg5, SysService_Asr4BswMCfg5, SysService_Asr4EcuM, SysService_AsrRamTst,
 *                                                  Ccl_Asr4SmLin
 *                                              add define REGSPACE - add support for ASR specification 4.0 R3
 *  01.07.01  2012-10-23  Seu                   add SysService_XmlSecurity
 *  01.07.02  2013-12-16  Seu                   MISRA compliance: usage of character "'" removed, typos corrected
 *********************************************************************************************************************/
#ifndef COMPILER_CFG_H
#define COMPILER_CFG_H

/**********************************************************************************************************************
 * INCLUDES
 *********************************************************************************************************************/
/* Package Merger: Start Section CompilerCfgIncludes */


#include "Rte_Compiler_Cfg.h"

#include "Compiler_Cfg_dep.h"

/* Package Merger: Stop Section CompilerCfgIncludes */

/**********************************************************************************************************************
 *  GLOBAL CONSTANT MACROS
 *********************************************************************************************************************/

#define AUTOSAR_COMSTACKDATA

#define MSR_REGSPACE  REGSPACE

/* Configurable memory class for pointers to registers (e.g. static volatile CONSTP2VAR(uint16, PWM_CONST, REGSPACE)). */
#define REGSPACE


/* due to compatibility to ASR 2.1 */
#define _STATIC_   STATIC
#define _INLINE_   INLINE

/* Package Merger: Start Section CompilerCfgModuleList */



/**********************************************************************************************************************
 *  COMM START 
 *********************************************************************************************************************/

#define COMM_CODE

#define COMM_CONST
#define COMM_PBCFG

#define COMM_VAR_NOINIT
#define COMM_VAR_ZERO_INIT
#define COMM_VAR_PBCFG

#define COMM_APPL_VAR
#define COMM_NVM_DATA

/**********************************************************************************************************************
 *  COMM END
 *********************************************************************************************************************/



/**********************************************************************************************************************
 *  CanSM START
 *********************************************************************************************************************/

#define CANSM_CODE

#define CANSM_CONST


#define CANSM_APPL_CODE

#define CANSM_VAR_NOINIT
#define CANSM_VAR_ZERO_INIT

#define CANSM_APPL_VAR


/*******  FAST sections **********************************************************************************************/

#define CANSM_CONST_FAST

#define CANSM_VAR_NOINIT_FAST
#define CANSM_VAR_ZERO_INIT_FAST


/**********************************************************************************************************************
 *  CanSM END
 *********************************************************************************************************************/


/**********************************************************************************************************************
 *  ETHSM START 
 *********************************************************************************************************************/

#define ETHSM_CODE

#define ETHSM_CONST
#define ETHSM_APPL_CONST
#define ETHSM_PBCFG
#define ETHSM_APPL_DATA

#define ETHSM_VAR_NOINIT
#define ETHSM_VAR_ZERO_INIT

/**********************************************************************************************************************
 *  ETHSM END
 *********************************************************************************************************************/



/**********************************************************************************************************************
 *  Cdd_AsrCdd START 
 *********************************************************************************************************************/

/* Copy the compiler abstraction defines for each of your configured CDDs and replace the prefix _CDD with the MSN of your configured CDD as higher case. */

#define _CDD_CODE
#define _CDD_APPL_DATA

/* Add additional compiler abstraction defines for each of you configured CDDs here. */

/**********************************************************************************************************************
 *  Cdd_AsrCdd END
 *********************************************************************************************************************/


/**********************************************************************************************************************
 *  XLOCK START 
 *********************************************************************************************************************/

#define XLOCK_CODE                                                    /* code */
#define XLOCK_CONST                                                   /* const */
#define XLOCK_DATA                                                    /* variables/buffers outside of Xlock */
#define XLOCK_APPL_VAR                                                /* Variables used for arguments to RTE ports*/
#define XLOCK_VAR_NOINIT                                              /* uninitialized global/static variables */
#define XLOCK_VAR_ZERO_INIT                                           /* Zero-initialized global/static variables */

/**********************************************************************************************************************
 *  XLOCK END
 *********************************************************************************************************************/



/**********************************************************************************************************************
 *  XCP START 
 *********************************************************************************************************************/

#define XCP_CODE
#define XCP_CONST
#define XCP_PBCFG

#define XCP_VAR_INIT
#define XCP_VAR_NOINIT
#define XCP_VAR_ZERO_INIT
#define XCP_VAR_NOINIT_NOCACHE
#define XCP_APPL_DATA
#define XCP_APPL_VAR



/**********************************************************************************************************************
 *  XCP END
 *********************************************************************************************************************/



/**********************************************************************************************************************
 *  CANXCP START 
 *********************************************************************************************************************/

#define CANXCP_VAR_INIT
#define CANXCP_VAR_NOINIT
#define CANXCP_CONST
#define CANXCP_PBCFG
#define CANXCP_CODE
#define CANXCP_APPL_VAR

/*-------------------------------------------------------------------------------------------------------------------*/
/* CANXCP END                                                                                                        */
/*-------------------------------------------------------------------------------------------------------------------*/




/**********************************************************************************************************************
 *  TCPIPXCP START 
 *********************************************************************************************************************/

#define TCPIPXCP_VAR_NOINIT
#define TCPIPXCP_VAR_INIT
#define TCPIPXCP_CONST
#define TCPIPXCP_PBCFG
#define TCPIPXCP_CODE
#define TCPIPXCP_APPL_VAR
#define TCPIPXCP_APPL_DATA

/*-------------------------------------------------------------------------------------------------------------------*/
/* TCPIPXCP END                                                                                                        */
/*-------------------------------------------------------------------------------------------------------------------*/



#define DCM_CODE                /* code */
#define DCM_CONST               /* global/static constants */
#define DCM_CAL_PRM             /* calibrateable constants */
#define DCM_APPL_CODE           /* callback functions outside of Dcm */
#define DCM_APPL_CONST          /* constants outside Dcm */ 
#define DCM_APPL_DATA           /* variables/buffers outside of Dcm */
#define DCM_CALLOUT_CODE        /* callback functions outside of Dcm but part of Dcm (callouts) */
#define DCM_VAR_INIT            /* initialized global/static variables */
#define DCM_VAR_NOINIT          /* uninitialized global/static variables */
#define DCM_VAR_PBCFG           /* global/static variable for PostBuildLoadable */
#define DCM_PBCFG               /* global/static constants for PostBuildLoadable */



/*==== DEM ==================================================================*/
#define DEM_CODE                /* code */
#define DEM_VAR_INIT            /* initialized global/static variables */
#define DEM_VAR_ZERO_INIT       /* initialized global/static variables with initialization value 0 */
#define DEM_VAR_NOINIT          /* uninitialized global/static variables */
#define DEM_VAR_UNCACHED        /* uninitialized global/static un-cached variables */
#define DEM_CONST               /* global/static constants */
#define DEM_CONST_ROOT          /* global/static constants */
#define DEM_PBCFG               /* global/static constants for PostBuild */
#define DEM_PBCFG_ROOT          /* global/static constants for PostBuild */
#define DEM_VAR_PBCFG           /* post-buildable RAM */
#define DEM_DCM_DATA            /* variables/buffers passed by Dcm to Dem */
#define DEM_J1939DCM_DATA       /* variables/buffers passed by J1939Dcm to Dem */
#define DEM_DLT_DATA            /* variables/buffers passed by Dlt to Dem */
#define DEM_NVM_DATA            /* variables/buffers managed by NvM */
#define DEM_APPL_CODE           /* callback functions outside of Dem */
#define DEM_APPL_DATA           /* variables/buffers outside of Dem */
#define DEM_APPL_CONST          /* constants outside Dem */
#define DEM_SHARED_DATA         /* variables/buffers shared by all */
#define DEM_CAL_PRM             /* calibrateable constants */
#define DEM_NVM_DATA_NOINIT     DEM_NVM_DATA
#define DEM_VAR_SATELLITE       /* variables/buffers of Dem satellites */
/*===========================================================================*/


/*==== VDEM42 ================================================================*/
#define VDEM42_CODE             /* code */
#define VDEM42_APPL_DATA        /* data variables/buffers outside of VDEM42 */
#define VDEM42_APPL_VAR         /* variables/buffers outside of VDEM42 */
/*============================================================================*/


/* Kernbauer Version: 1.14 Konfiguration: DrvCan_Arm32Mcan Erzeugungsgangnummer: 600 */

/*        STARTSINGLE_OF_MULTIPLE_COMMENT      */


/**********************************************************************************************************************
 *  Can START    DRVCAN_MPC5700MCANASR
 *********************************************************************************************************************/

#define CAN_30_MCAN_CODE                                   /* CAN modules code qualifier               */ /* PRQA S 0883 */ /* Appears only while testing */
#define CAN_30_MCAN_STATIC_CODE                            /* CAN local code qualifier                 */
#define CAN_30_MCAN_CONST                                  /* constant memory                          */
#define CAN_30_MCAN_CONST_PBCFG                            /* postbuild generated constant/flash       */
#define CAN_30_MCAN_VAR_NOINIT                             /* none initialized variables               */
#define CAN_30_MCAN_VAR_INIT                               /* initialized variables                    */
#define CAN_30_MCAN_VAR_PBCFG                              /* postbuild generated variables/dynamic RAM (CFG5 only) */
#define CAN_30_MCAN_INT_CTRL                               /* access to Interrupt controller registers */
#define CAN_30_MCAN_REG_CANCELL                            /* CAN cell register qualifier              */
#define CAN_30_MCAN_RX_TX_DATA                             /* pointer width >= CAN_30_MCAN_REG_CANCELL  / CAN rx/tx data / RAM or SFR    (rx data pointer for Indication and PreCopy functions to higher layers / tx data pointers / sdu  from application)   */
#define CAN_30_MCAN_APPL_CODE                              /* Application code qualifier               */
#define CAN_30_MCAN_APPL_CONST                             /* Application constant memory              */
#define CAN_30_MCAN_APPL_VAR                               /* Application variables                    */
#define CAN_30_MCAN_VAR_FAR                                /* far local variable qualifier */
#define CAN_30_MCAN_VAR_NEAR                               /* near local variable qualifier */

#define CAN_30_MCAN_PBCFG  CAN_30_MCAN_CONST_PBCFG       /* used for compatibility (used by upper layer EcuM) */

/**********************************************************************************************************************
 *  Can END    DRVCAN_MPC5700MCANASR
 *********************************************************************************************************************/

/*  STOPSINGLE_OF_MULTIPLE_COMMENT  */
/* Kernbauer Version: 1.14 Konfiguration: DrvCan_Arm32Mcan Erzeugungsgangnummer: 600 */

/* Kernbauer Version: 1.14 Konfiguration: DrvCan_Arm32Mcan Erzeugungsgangnummer: 600 */

/* Kernbauer Version: 1.14 Konfiguration: DrvCan_Arm32Mcan Erzeugungsgangnummer: 600 */

/* Kernbauer Version: 1.14 Konfiguration: DrvCan_Arm32Mcan Erzeugungsgangnummer: 600 */

/* Kernbauer Version: 1.14 Konfiguration: DrvCan_Arm32Mcan Erzeugungsgangnummer: 600 */

/* Kernbauer Version: 1.14 Konfiguration: DrvCan_Arm32Mcan Erzeugungsgangnummer: 600 */

/* Kernbauer Version: 1.14 Konfiguration: DrvCan_Arm32Mcan Erzeugungsgangnummer: 600 */

/* Kernbauer Version: 1.14 Konfiguration: DrvCan_Arm32Mcan Erzeugungsgangnummer: 600 */

/* Kernbauer Version: 1.14 Konfiguration: DrvCan_Arm32Mcan Erzeugungsgangnummer: 600 */

/* Kernbauer Version: 1.14 Konfiguration: DrvCan_Arm32Mcan Erzeugungsgangnummer: 600 */

/* Kernbauer Version: 1.14 Konfiguration: DrvCan_Arm32Mcan Erzeugungsgangnummer: 600 */

/* Kernbauer Version: 1.14 Konfiguration: DrvCan_Arm32Mcan Erzeugungsgangnummer: 600 */

/* Kernbauer Version: 1.14 Konfiguration: DrvCan_Arm32Mcan Erzeugungsgangnummer: 600 */

/* Kernbauer Version: 1.14 Konfiguration: DrvCan_Arm32Mcan Erzeugungsgangnummer: 600 */

/* Kernbauer Version: 1.14 Konfiguration: DrvCan_Arm32Mcan Erzeugungsgangnummer: 600 */

/* Kernbauer Version: 1.14 Konfiguration: DrvCan_Arm32Mcan Erzeugungsgangnummer: 600 */

/* Kernbauer Version: 1.14 Konfiguration: DrvCan_Arm32Mcan Erzeugungsgangnummer: 600 */

/* Kernbauer Version: 1.14 Konfiguration: DrvCan_Arm32Mcan Erzeugungsgangnummer: 600 */

/* Kernbauer Version: 1.14 Konfiguration: DrvCan_Arm32Mcan Erzeugungsgangnummer: 600 */



/**********************************************************************************************************************
 *  CRYPTO_30_LIBCV START
 *********************************************************************************************************************/

#define CRYPTO_30_LIBCV_CODE
#define CRYPTO_30_LIBCV_APPL_CODE

#define CRYPTO_30_LIBCV_CONST

#define CRYPTO_30_LIBCV_VAR_NOINIT
#define CRYPTO_30_LIBCV_VAR_ZERO_INIT
#define CRYPTO_30_LIBCV_APPL_VAR
#define CRYPTO_30_LIBCV_APPL_DATA

#define CRYPTO_30_LIBCV_CRYPTOCV_APPL_VAR

/**********************************************************************************************************************
 *  CRYPTO_30_LIBCV END
 *********************************************************************************************************************/



/**********************************************************************************************************************
 *  CANTRCV_30_TJA1043 START 
 *********************************************************************************************************************/

#define CANTRCV_30_TJA1043_CODE
#define CANTRCV_30_TJA1043_CODE_FAST
#define CANTRCV_30_TJA1043_CODE_ISR

#define CANTRCV_30_TJA1043_CONST
#define CANTRCV_30_TJA1043_CONST_FAST
#define CANTRCV_30_TJA1043_PBCFG

#define CANTRCV_30_TJA1043_VAR_PBCFG /* ESCAN00065502 */

#define CANTRCV_30_TJA1043_VAR_INIT
#define CANTRCV_30_TJA1043_VAR_NOINIT
#define CANTRCV_30_TJA1043_APPL_VAR
#define CANTRCV_30_TJA1043_APPL_CODE
#define CANTRCV_30_TJA1043_VAR_ZERO_INIT
#define CANTRCV_30_TJA1043_VAR_INIT_FAST
#define CANTRCV_30_TJA1043_VAR_NOINIT_FAST
#define CANTRCV_30_TJA1043_VAR_ZERO_INIT_FAST

#define CANTRCV_30_TJA1043_VAR_INIT_NOCACHE
#define CANTRCV_30_TJA1043_VAR_NOINIT_NOCACHE
#define CANTRCV_30_TJA1043_VAR_ZERO_INIT_NOCACHE



/**********************************************************************************************************************
 *  CANTRCV_30_TJA1043 END
 *********************************************************************************************************************/ /* PRQA S 0883 */ /* Appears only while testing */



/**********************************************************************************************************************
 *  EcuAb_AsrIoHwAb START
 *********************************************************************************************************************/

#define IOHWAB_CODE

#define IOHWAB_VAR

#define IOHWAB_APPL_DATA
#define IOHWAB_APPL_CODE

#define IOHWAB_CONST

/**********************************************************************************************************************
 *  EcuAb_AsrIoHwAb END
 *********************************************************************************************************************/


/**********************************************************************************************************************
 *  PDUR START 
 *********************************************************************************************************************/

#define PDUR_CODE
#define PDUR_VAR_NOINIT
#define PDUR_VAR_ZERO_INIT
#define PDUR_VAR
#define PDUR_CONST
#define PDUR_PBCFG
#define PDUR_VAR_PBCFG
#define PDUR_APPL_DATA
#define PDUR_APPL_CODE

/**********************************************************************************************************************
 *  PDUR END
 *********************************************************************************************************************/



/**********************************************************************************************************************
 *  If_Asr4IfWd START
 *********************************************************************************************************************/

#define WDGIF_CODE
#define WDGIF_CONST
#define WDGIF_VAR
#define WDGIF_APPL_DATA
#define WDGIF_APPL_CONST

/**********************************************************************************************************************
 *  If_Asr4IfWd END
 *********************************************************************************************************************/



/**********************************************************************************************************************
 *  CANIF START 
 *********************************************************************************************************************/

#define CANIF_CODE
#define CANIF_CODE_FAST
#define CANIF_CODE_ISR


#define CANIF_CONST
#define CANIF_CONST_FAST
#define CANIF_PBCFG

#define CANIF_VAR_PBCFG /* ESCAN00065502 */

#define CANIF_VAR_INIT
#define CANIF_VAR_NOINIT
#define CANIF_VAR_ZERO_INIT

#define CANIF_VAR_INIT_FAST
#define CANIF_VAR_NOINIT_FAST
#define CANIF_VAR_ZERO_INIT_FAST

#define CANIF_VAR_INIT_NOCACHE
#define CANIF_VAR_NOINIT_NOCACHE
#define CANIF_VAR_ZERO_INIT_NOCACHE


#define CANIF_APPL_CODE
#define CANIF_APPL_VAR
#define CANIF_APPL_PBCFG


/*-------------------------------------------------------------------------------------------------------------------*/

/* Has to be kept in default section -> Default access */
#define CANIF_VAR_STACK


/* VAR section of higher layers (TP / NM / PduR / CanSM / EcuM) automatically mapped to APPL_VAR */
#define CANIF_APPL_STATE_VAR   CANIF_APPL_VAR
#define CANIF_APPL_MSG_VAR     CANIF_APPL_VAR

/* VAR section of lower layers (CAN Driver / Transceiver Driver) automatically mapped to APPL_VAR */
#define CANIF_CBK_VAR          CANIF_APPL_VAR

/* #define CANIF_CBK_TRCV_VAR     CANIF_CBK_VAR    not used yet */
#define CANIF_CBK_DRV_VAR      CANIF_CBK_VAR

/* Code sections - DO NOT MODIFY */
#define CANIF_CBK_TRCV_CODE     CANIF_APPL_CODE
#define CANIF_CBK_CANDRV_CODE   CANIF_APPL_CODE
#define CANIF_APPL_STATE_CODE   CANIF_APPL_CODE
#define CANIF_APPL_MSG_CODE     CANIF_APPL_CODE


/* Upper layer data pointer */
#define CANIF_UL_STANDARD_VAR    CANIF_APPL_VAR
#define CANIF_UL_ADVANCED_VAR    CANIF_APPL_VAR
#define CANIF_UL_OSEKNM_VAR      CANIF_APPL_VAR

/**********************************************************************************************************************
 *  CANIF END
 *********************************************************************************************************************/


/**********************************************************************************************************************
 *  CRYIF START
 *********************************************************************************************************************/

#define CRYIF_CODE
#define CRYIF_CONST

#define CRYIF_VAR_ZERO_INIT
#define CRYIF_APPL_VAR
#define CRYIF_APPL_DATA

/**********************************************************************************************************************
 *  CRYIF END
 *********************************************************************************************************************/




/**********************************************************************************************************************
 *  If_AsrIfEa START 
 *********************************************************************************************************************/

#define EA_PUBLIC_CODE
#define EA_PRIVATE_CODE

#define EA_APPL_DATA
#define EA_FAST_DATA

#define EA_PRIVATE_CONST
#define EA_PUBLIC_CONST

#define EA_NVM_CODE

/* In ASR4 SchM/RTE declares Ea_MainFunction, using EA_CODE *
 * It MUST be defined to EA_PUBLIC_CODE!                     */
#define EA_CODE EA_PUBLIC_CODE

/**********************************************************************************************************************
 *  If_AsrIfEa END
 *********************************************************************************************************************/


/**********************************************************************************************************************
 *  ETHIF START 
 *********************************************************************************************************************/

#define ETHIF_CODE
#define ETHIF_CODE_ISR
#define ETHIF_ETHCTRL_INLINE_CODE
#define ETHIF_ETHTRCV_INLINE_CODE
#define ETHIF_ETHSWT_INLINE_CODE
#define ETHIF_GW_INLINE_CODE
#define ETHIF_LINK_INLINE_CODE
#define ETHIF_MIRROR_INLINE_CODE
#define ETHIF_MODE_INLINE_CODE
#define ETHIF_RX_INLINE_CODE
#define ETHIF_STATS_INLINE_CODE
#define ETHIF_TX_INLINE_CODE
#define ETHIF_UTILS_INLINE_CODE
#define ETHIF_ZEROCOPY_INLINE_CODE

#define ETHIF_CONST

#define ETHIF_APPL_VAR
#define ETHIF_APPL_DATA
#define ETHIF_APPL_CODE

#define ETHIF_VAR_NOINIT
#define ETHIF_VAR_NOINIT_FAST

#define ETHIF_VAR_INIT

/**********************************************************************************************************************
 *  ETHIF END
 *********************************************************************************************************************/



/**********************************************************************************************************************
 *  FEE START 
 *********************************************************************************************************************/

#define FEE_API_CODE
#define FEE_APPL_CODE
#define FEE_APPL_CONFIG
#define FEE_APPL_DATA
#define FEE_CONST
#define FEE_PRIVATE_CODE
#define FEE_PRIVATE_CONST
#define FEE_PRIVATE_DATA
#define FEE_VAR
#define FEE_VAR_NOINIT

/* In ASR4 SchM/RTE declares NvM_MainFunction, using FEE_CODE *
 * It MUST be defined to FEE_API_CODE!                     */
#define FEE_CODE FEE_API_CODE

/**********************************************************************************************************************
 *  FEE END
 *********************************************************************************************************************/


#define MEMIF_CODE
#define MEMIF_PRIVATE_CODE
#define MEMIF_CONST
#define MEMIF_APPL_DATA


/**********************************************************************************************************************
 *  SOAD START 
 *********************************************************************************************************************/

#define SOAD_CODE

#define SOAD_APPL_DATA
#define SOAD_APPL_VAR

#define SOAD_CONST
#define SOAD_PBCFG

#define SOAD_VAR_NOINIT
#define SOAD_VAR_ZERO_INIT
#define SOAD_VAR_PBCFG

/**********************************************************************************************************************
 *  SOAD END
 *********************************************************************************************************************/


/**********************************************************************************************************************
 *  Com START 
 *********************************************************************************************************************/

/* Module Constant Data */
#define COM_CONST
/* Module Constant Data of the Postbuild Configuration */
#define COM_PBCFG
/* Module Var Data of the Postbuild Configuration */
#define COM_VAR_PBCFG


/* Module Implementation */
#define COM_CODE

/* Module Variables which are initialized by the startup code or by the call of Com_InitMemory() */
#define COM_VAR_NOINIT
/* Module Variables which are initialized by call of Com_Init() */
#define COM_VAR_ZERO_INIT
/* Module Variables which are initialized by call of Com_Init() */
#define COM_VAR_INIT

/* Application Code Implementation (e.g. Callbacks) */
#define COM_APPL_CODE
/* Application Buffer which is located in RAM */
#define COM_APPL_VAR
/* Application Buffer which is located in ROM or RAM */
#define COM_APPL_DATA

/**********************************************************************************************************************
 *  Com END
 *********************************************************************************************************************/


/**********************************************************************************************************************
 *  IpduM START 
 *********************************************************************************************************************/

#define IPDUM_CODE
#define IPDUM_CONST
#define IPDUM_PBCFG
#define IPDUM_VAR_PBCFG
#define IPDUM_VAR_INIT
#define IPDUM_VAR_ZERO_INIT
#define IPDUM_VAR_NOINIT
#define IPDUM_APPL_DATA

/**********************************************************************************************************************
 *  IpduM END
 *********************************************************************************************************************/



/**********************************************************************************************************************
 *  LDCOM START
 *********************************************************************************************************************/
/* Module Implementation */
#define LDCOM_CODE

/* Module Constant Data */
#define LDCOM_CONST
/* Module Constant Data of the Postbuild Configuration */
#define LDCOM_PBCFG
/* Module Var Data of the Postbuild Configuration */
#define LDCOM_VAR_PBCFG

/* Module Variables which are initialized by call of LdCom_Init() */
#define LDCOM_VAR_INIT
/* Module Variables which are initialized by the startup code or by the call of LdCom_InitMemory() */
#define LDCOM_VAR_NOINIT
/* Module Variables which are initialized by call of LdCom_Init() */
#define LDCOM_VAR_ZERO_INIT

/* Application Code Implementation (e.g. Callbacks) */
#define LDCOM_APPL_CODE
/* Application Buffer which is located in RAM */
#define LDCOM_APPL_VAR
/* Application Buffer which is located in ROM or RAM */
#define LDCOM_APPL_DATA

/**********************************************************************************************************************
 *  LDCOM END
 *********************************************************************************************************************/



/**********************************************************************************************************************
 *  SECOC START 
 *********************************************************************************************************************/
/* Module Implementation */
#define SECOC_CODE

/* Module Constant Data */
#define SECOC_CONST
/* Module Constant Data of the Postbuild Configuration */
#define SECOC_PBCFG
/* Module Var Data of the Postbuild Configuration */
#define SECOC_VAR_PBCFG


/* Module Variables which are initialized by call of SecOC_Init() */
#define SECOC_VAR_INIT
/* Module Variables which are initialized by the startup code or by the call of SecOC_InitMemory() */
#define SECOC_VAR_NOINIT
/* Module Variables which are initialized by call of SecOC_Init() */
#define SECOC_VAR_ZERO_INIT

/* Application Code Implementation (e.g. Callbacks) */
#define SECOC_APPL_CODE
/* Application Buffer which is located in RAM */
#define SECOC_APPL_VAR
/* Application Buffer which is located in ROM or RAM */
#define SECOC_APPL_DATA

/**********************************************************************************************************************
 *  SECOC END
 *********************************************************************************************************************/




/**********************************************************************************************************************
 *  NvM START 
 *********************************************************************************************************************/

#define NVM_APPL_DATA
#define NVM_APPL_CODE
#define NVM_APPL_CONST

#define NVM_CRC_APPL_DATA

#define NVM_CONFIG_DATA
#define NVM_CONFIG_CONST

#define NVM_FAST_DATA

#define NVM_PRIVATE_CODE
#define NVM_PRIVATE_CONST
#define NVM_PRIVATE_DATA

#define NVM_PUBLIC_CODE
#define NVM_PUBLIC_CONST

/* SchM/RTE declares NvM_MainFunction, using NVM_CODE *
 * It MUST be defined to NVM_PUBLIC_CODE!             */
#define NVM_CODE NVM_PUBLIC_CODE

/**********************************************************************************************************************
 *  NvM END
 *********************************************************************************************************************/



/**********************************************************************************************************************
 *  CANNM START 
 *********************************************************************************************************************/

#define CANNM_CODE

#define CANNM_CONST
#define CANNM_PBCFG

#define CANNM_VAR_NOINIT
#define CANNM_VAR_INIT
#define CANNM_VAR_ZERO_INIT
#define CANNM_VAR_PBCFG
#define CANNM_APPL_VAR

/**********************************************************************************************************************
 *  CANNM END
 *********************************************************************************************************************/



/**********************************************************************************************************************
 *  NM START 
 *********************************************************************************************************************/
 
#define NM_CODE
#define NM_APPL_CODE

#define NM_CONST

#define NM_VAR_NO_INIT
#define NM_VAR_INIT
#define NM_APPL_VAR

/**********************************************************************************************************************
 *  NM END
 *********************************************************************************************************************/



/**********************************************************************************************************************
 *  UDPNM START 
 *********************************************************************************************************************/

#define UDPNM_CODE

#define UDPNM_CONST
#define UDPNM_APPL_VAR
#define UDPNM_PBCFG

#define UDPNM_VAR_NOINIT
#define UDPNM_VAR_INIT
#define UDPNM_VAR_ZERO_INIT
#define UDPNM_VAR_PBCFG

/**********************************************************************************************************************
 *  UDPNM END
 *********************************************************************************************************************/




/**********************************************************************************************************************
 *  OS START
 *********************************************************************************************************************/

#define OS_CODE                         /* Regular OS code. */
#define OS_CODE_FAST                    /* Time critical OS code. (currently not used) */
#define OS_CODE_SLOW                    /* Not time critical OS code. (currently not used) */
#define OS_CODE_ISR                     /* OS ISRs. (currently not used) */

#define OS_PANICHOOK_CODE               /* Panic Hook */
#define OS_PRETASKHOOK_CODE             /* PreTask Hook */
#define OS_POSTTASKHOOK_CODE            /* PostTask Hook */
#define OS_STARTUPHOOK_CODE             /* Startup Hook */
#define OS_ERRORHOOK_CODE               /* Error Hook */
#define OS_PROTECTIONHOOK_CODE          /* Protection Hook */
#define OS_SHUTDOWNHOOK_CODE            /* Shutdown Hook */

#define OS_CONST                        /* Constant data. */
#define OS_CONST_FAST                   /* Constant data with fast access. (currently not used) */

#define OS_VAR_INIT                     /* Initialized dynamic data. (Not used by the OS) */
#define OS_VAR_NOINIT                   /* Not initialized dynamic data. */
#define OS_VAR_ZERO_INIT                /* Zero initialized dynamic data. */
#define OS_VAR_INIT_FAST                /* Initialized dynamic data with fast access. (Not used by the OS) */
#define OS_VAR_NOINIT_FAST              /* Not initialized dynamic data with fast access. */
#define OS_VAR_ZERO_INIT_FAST           /* Zero initialized dynamic data with fast access. */

#define OS_VAR_INIT_NOCACHE             /* Initialized dynamic data not cached. (Not used by the OS) */
#define OS_VAR_NOINIT_NOCACHE           /* Not initialized dynamic data not cached. */
#define OS_VAR_ZERO_INIT_NOCACHE        /* Zero initialized dynamic data not cached. */

#define OS_APPL_VAR                     /* Dynamic data from user space (always in RAM). */
#define OS_APPL_DATA                    /* Data from user space (maybe in RAM or ROM). */


#include "Os_Compiler_Cfg.h"

/**********************************************************************************************************************
 *  OS END
 *********************************************************************************************************************/



/**********************************************************************************************************************
 *  CSM START
 *********************************************************************************************************************/

#define CSM_CODE
#define CSM_APPL_CODE

#define CSM_CONST

#define CSM_VAR_NOINIT
#define CSM_VAR_ZERO_INIT
#define CSM_APPL_VAR
#define CSM_APPL_CONST

/**********************************************************************************************************************
 *  CSM END
 *********************************************************************************************************************/



/**********************************************************************************************************************
 *  VSECPRIM START
 *********************************************************************************************************************/

#define VSECPRIM_CODE
#define VSECPRIM_CONST
#define VSECPRIM_APPL_CONST
#define VSECPRIM_APPL_VAR
#define VSECPRIM_APPL_DATA


/**********************************************************************************************************************
 *  VSECPRIM END
 *********************************************************************************************************************/



/**********************************************************************************************************************
 *  BSWM START 
 *********************************************************************************************************************/

#define BSWM_CODE
#define BSWM_CONST
#define BSWM_PBCFG
#define BSWM_VAR_ZERO_INIT
#define BSWM_APPL_DATA
#define BSWM_VAR_NOINIT
#define BSWM_VAR_PBCFG

/**********************************************************************************************************************
 *  BSWM END
 *********************************************************************************************************************/




/**********************************************************************************************************************
 *  SYSSERVICE_ASR4ECUM START 
 *********************************************************************************************************************/
#define ECUM_APPL_DATA 
#define ECUM_CODE
#define ECUM_CONST
#define ECUM_PBCFG
#define ECUM_VAR_NOINIT 

/**********************************************************************************************************************
 *  SYSSERVICE_ASR4ECUM END
 *********************************************************************************************************************/


/* -------------------- FiM Compiler Abstraction -------------------- */
#define FIM_CODE
#define FIM_CONST
#define FIM_PBCFG
#define FIM_PBCFG_ROOT
#define FIM_VAR_NOINIT
#define FIM_VAR_INIT
#define FIM_VAR_ZERO_INIT
#define FIM_APPL_DATA
#define FIM_DEM_DATA



/**********************************************************************************************************************
 *  SysService_Asr4WdM START 
 *********************************************************************************************************************/

#define WDGM_CODE
#define WDGM_APPL_CODE
#define WDGM_CONST
#define WDGM_VAR_INIT
#define WDGM_VAR_NOINIT
#define WDGM_VAR_ZERO_INIT
#define WDGM_APPL_DATA
#define WDGM_APPL_CONST

/**********************************************************************************************************************
 *  SysService_Asr4WdM END
 *********************************************************************************************************************/



/**********************************************************************************************************************
 *  SYSSERVICE_ASRCRC START 
 *********************************************************************************************************************/

#define CRC_APPL_DATA
#define CRC_PRIVATE_CODE
#define CRC_CODE
#define CRC_CONST

/**********************************************************************************************************************
 *  SYSSERVICE_ASRCRC END
 *********************************************************************************************************************/


# define DET_CODE
# define DET_VAR
# define DET_APPL_DATA
# define DET_CONST



/**********************************************************************************************************************
 *  ETM START 
 *********************************************************************************************************************/

#define ETM_CODE
#define ETM_CODE_FAST
#define ETM_CODE_ISR

#define ETM_APPL_VAR
#define ETM_APPL_DATA
#define ETM_APPL_CONST

#define ETM_CONST
#define ETM_CONST_FAST
#define ETM_PBCFG

#define ETM_VAR_PBCFG /* ESCAN00065502 */

#define ETM_VAR_INIT
#define ETM_VAR_NOINIT
#define ETM_VAR_ZERO_INIT

#define ETM_VAR_INIT_FAST
#define ETM_VAR_NOINIT_FAST
#define ETM_VAR_ZERO_INIT_FAST

#define ETM_VAR_INIT_NOCACHE
#define ETM_VAR_NOINIT_NOCACHE
#define ETM_VAR_ZERO_INIT_NOCACHE

/**********************************************************************************************************************
 *  ETM END
 *********************************************************************************************************************/



/**********************************************************************************************************************
 *  SD START 
 *********************************************************************************************************************/

#define SD_CODE

#define SD_APPL_DATA
#define SD_APPL_VAR

#define SD_CONST
#define SD_PBCFG

#define SD_VAR_NOINIT
#define SD_VAR_INIT
#define SD_VAR_ZERO_INIT
#define SD_VAR_PBCFG

/**********************************************************************************************************************
 *  SD END
 *********************************************************************************************************************/


/**********************************************************************************************************************
 *  SysService_E2eLib START 
 *********************************************************************************************************************/

#define E2E_CODE
#define E2E_VAR_FAST
#define E2E_VAR
#define E2E_CONST
#define E2E_DATA
#define E2E_APPL_DATA
#define E2E_APPL_CONST
#define E2E_APPL_CODE

/**********************************************************************************************************************
 *  SysService_E2eLib END
 *********************************************************************************************************************/





/**********************************************************************************************************************
 *  E2E START 
 *********************************************************************************************************************/


#define E2EPW_CODE
#define E2EPW_VAR_NOINIT
#define E2EPW_VAR_INIT
#define E2EPW_VAR_POWER_ON_INIT
#define E2EPW_VAR_FAST
#define E2EPW_VAR
#define E2EPW_CONST
#define E2EPW_APPL_DATA
#define E2EPW_APPL_CONST
#define E2EPW_APPL_CODE
#define E2EPW_APPL_VAR


/**********************************************************************************************************************
 *  E2E END
 *********************************************************************************************************************/



/**********************************************************************************************************************
 *  IPBASE START
 *********************************************************************************************************************/

#define IPBASE_CODE

#define IPBASE_CONST
#define IPBASE_APPL_CONST
#define IPBASE_PBCFG
#define IPBASE_APPL_DATA

#define IPBASE_VAR_NOINIT

/**********************************************************************************************************************
 *  IPBASE END
 *********************************************************************************************************************/


/**********************************************************************************************************************
 *  CANTP START 
 *********************************************************************************************************************/

#define CANTP_CODE
#define CANTP_CONST

#define CANTP_PBCFG
#define CANTP_VAR_PBCFG

#define CANTP_VAR_INIT
#define CANTP_VAR_NOINIT

#define CANTP_APPL_CODE
#define CANTP_APPL_DATA

/**********************************************************************************************************************
 *  CANTP END
 *********************************************************************************************************************/


/**********************************************************************************************************************
 *  DOIP START
 *********************************************************************************************************************/

#define DOIP_CODE

#define DOIP_APPL_CONST
#define DOIP_APPL_DATA
#define DOIP_APPL_VAR
#define DOIP_CONST
#define DOIP_PBCFG

#define DOIP_VAR_NOINIT
#define DOIP_VAR_ZERO_INIT

/**********************************************************************************************************************
 *  DOIP END
 *********************************************************************************************************************/



/***********************************************************************************************************************
 *  SOMEIPTP START 
 **********************************************************************************************************************/

#define SOMEIPTP_CODE

#define SOMEIPTP_CONST

#define SOMEIPTP_APPL_VAR
#define SOMEIPTP_APPL_DATA
#define SOMEIPTP_APPL_CODE

#define SOMEIPTP_VAR_ZERO_INIT
#define SOMEIPTP_VAR_NOINIT

#define SOMEIPTP_PBCFG
#define SOMEIPTP_VAR_PBCFG

/***********************************************************************************************************************
 *  SOMEIPTP END
 **********************************************************************************************************************/


/**********************************************************************************************************************
 *  TCPIP START 
 *********************************************************************************************************************/

#define TCPIP_CODE
#define TCPIP_CODE_ISR

#define TCPIP_CONST
#define TCPIP_PBCFG

#define TCPIP_APPL_CONST
#define TCPIP_APPL_DATA
#define TCPIP_APPL_VAR

#define TCPIP_VAR_INIT
#define TCPIP_VAR_ZERO_INIT
#define TCPIP_VAR_NOINIT


#define IPV4_CODE

#define IPV4_CONST
#define IPV4_PBCFG
#define IPV4_PBCFG_ROOT

#define IPV4_VAR_INIT
#define IPV4_VAR_ZERO_INIT
#define IPV4_VAR_NOINIT

#define IPV4_APPL_CODE
#define IPV4_APPL_DATA
#define IPV4_APPL_CONST
#define IPV4_APPL_PBCFG
#define IPV4_APPL_VAR

#define IPV6_CODE

#define IPV6_CONST
#define IPV6_PBCFG
#define IPV6_PBCFG_ROOT

#define IPV6_VAR_INIT
#define IPV6_VAR_ZERO_INIT
#define IPV6_VAR_NOINIT

#define IPV6_APPL_CODE
#define IPV6_APPL_DATA
#define IPV6_APPL_CONST
#define IPV6_APPL_PBCFG
#define IPV6_APPL_VAR

/**********************************************************************************************************************
 *  TCPIP END
 *********************************************************************************************************************/



/**********************************************************************************************************************
 *  VSTDLIB START
 *********************************************************************************************************************/

#define VSTDLIB_CODE
#define VSTDLIB_VAR_FAR
#define VSTDLIB_APPL_VAR

/**********************************************************************************************************************
 *  VSTDLIB END
 *********************************************************************************************************************/



/**********************************************************************************************************************
 *  CANTSYN START 
 *********************************************************************************************************************/

/* Module Constant Data */
#define CANTSYN_CONST

/* Module Implementation */
#define CANTSYN_CODE

/* Module Variables which are initialized by the startup code */
#define CANTSYN_VAR_ZERO_INIT
/* Module Variables which are initialized by call of CanTSyn_Init() */
#define CANTSYN_VAR_NOINIT

/* Application Buffer which is located in RAM */
#define CANTSYN_APPL_VAR
/* Application Buffer which is located in ROM or RAM */
#define CANTSYN_APPL_DATA

/**********************************************************************************************************************
 *  CANTSYN END
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  STBM START
 *********************************************************************************************************************/

/* Module Constant Data */
#define STBM_CONST
#define STBM_PBCFG

/* Module Implementation */
#define STBM_CODE

/* Module Variables which are initialized by the startup code */
#define STBM_VAR_ZERO_INIT
/* Module Variables which are initialized by call of StbM_Init() */
#define STBM_VAR_NOINIT

#define STBM_VAR_PBCFG

/* Application Code Implementation (e.g. Callbacks) */
#define STBM_APPL_CODE
/* Application Buffer which is located in RAM */
#define STBM_APPL_VAR
/* Application Buffer which is located in ROM or RAM */
#define STBM_APPL_DATA

/**********************************************************************************************************************
 *  STBM END
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  ETH  START 
 *********************************************************************************************************************/

 #define ETH_30_TC3XX_CODE
 #define ETHTRCV_CODE
 #define ETHTRCV_30_TJA1100_CODE
 
/**********************************************************************************************************************
 *  ETH  END
 *********************************************************************************************************************/


/* Package Merger: Stop Section CompilerCfgModuleList */

/**********************************************************************************************************************
 *  GLOBAL FUNCTION MACROS
 *********************************************************************************************************************/


/**********************************************************************************************************************
 *  GLOBAL DATA TYPES AND STRUCTURES
 *********************************************************************************************************************/


/**********************************************************************************************************************
 *  GLOBAL DATA PROTOTYPES
 *********************************************************************************************************************/


/**********************************************************************************************************************
 *  GLOBAL FUNCTION PROTOTYPES
 *********************************************************************************************************************/

/** \brief Dummy defines for MCU Build */
#define ADC_CONFIG_DATA_SECTION_NON_CONST
#define ADC_CONFIG_DATA_SECTION_CONST
#define ADC_VAR_CONST_8_SECTION
#define ADC_VAR_CONST_16_SECTION
#define ADC_VAR_CONST_32_SECTION
#define ADC_VAR_CONST_PTR_SECTION
#define ADC_VAR_CONST_UNSPECIFIED_SECTION
#define ADC_VAR_DATA_NO_INIT_8_SECTION
#define ADC_VAR_DATA_NO_INIT_16_SECTION
#define ADC_VAR_DATA_NO_INIT_32_SECTION
#define ADC_VAR_DATA_NO_INIT_PTR_SECTION
#define ADC_VAR_DATA_NO_INIT_UNSPECIFIED_SECTION
#define ADC_VAR_DATA_INIT_8_SECTION
#define ADC_VAR_DATA_INIT_16_SECTION
#define ADC_VAR_DATA_INIT_32_SECTION
#define ADC_VAR_DATA_INIT_PTR_SECTION
#define ADC_VAR_DATA_INIT_UNSPECIFIED_SECTION
#define ADC_FUNC_TEXT_SECTION
#define ADC_ISR_TEXT_SECTION
#define ADC_FUNC_CALLOUT_TEXT_SECTION

/** \brief Dummy defines for MCU Build */
#define CAN_CONFIG_DATA_SECTION_NON_CONST
#define CAN_CONFIG_DATA_SECTION_CONST
#define CAN_VAR_CONST_8_SECTION
#define CAN_VAR_CONST_16_SECTION
#define CAN_VAR_CONST_32_SECTION
#define CAN_VAR_CONST_PTR_SECTION
#define CAN_VAR_CONST_UNSPECIFIED_SECTION
#define CAN_VAR_DATA_NO_INIT_8_SECTION
#define CAN_VAR_DATA_NO_INIT_16_SECTION
#define CAN_VAR_DATA_NO_INIT_32_SECTION
#define CAN_VAR_DATA_NO_INIT_PTR_SECTION
#define CAN_VAR_DATA_NO_INIT_UNSPECIFIED_SECTION
#define CAN_VAR_DATA_INIT_8_SECTION
#define CAN_VAR_DATA_INIT_16_SECTION
#define CAN_VAR_DATA_INIT_32_SECTION
#define CAN_VAR_DATA_INIT_PTR_SECTION
#define CAN_VAR_DATA_INIT_UNSPECIFIED_SECTION
#define CAN_FUNC_TEXT_SECTION
#define CAN_ISR_TEXT_SECTION
#define CAN_FUNC_CALLOUT_TEXT_SECTION

#define CDD_IPC_CONFIG_DATA_SECTION
#define CDD_IPC_VAR_CONST_8_SECTION
#define CDD_IPC_VAR_CONST_16_SECTION
#define CDD_IPC_VAR_CONST_32_SECTION
#define CDD_IPC_VAR_CONST_PTR_SECTION
#define CDD_IPC_VAR_CONST_UNSPECIFIED_SECTION
#define CDD_IPC_VAR_DATA_NO_INIT_8_SECTION
#define CDD_IPC_VAR_DATA_NO_INIT_8_SECTION_ALIGN_8B
#define CDD_IPC_VAR_DATA_NO_INIT_16_SECTION
#define CDD_IPC_VAR_DATA_NO_INIT_32_SECTION
#define CDD_IPC_VAR_DATA_NO_INIT_PTR_SECTION
#define CDD_IPC_VAR_DATA_NO_INIT_UNSPECIFIED_SECTION
#define CDD_IPC_VAR_DATA_INIT_8_SECTION
#define CDD_IPC_VAR_DATA_INIT_16_SECTION
#define CDD_IPC_VAR_DATA_INIT_32_SECTION
#define CDD_IPC_VAR_DATA_INIT_PTR_SECTION
#define CDD_IPC_VAR_DATA_INIT_UNSPECIFIED_SECTION
#define CDD_IPC_FUNC_TEXT_SECTION
#define CDD_IPC_ISR_TEXT_SECTION
#define CDD_IPC_FUNC_CALLOUT_TEXT_SECTION

/** \brief Dummy defines for M4 Build */
#define DIO_CONFIG_DATA_SECTION_CONST
#define DIO_CONFIG_DATA_SECTION_NON_CONST
#define DIO_VAR_CONST_8_SECTION
#define DIO_VAR_CONST_16_SECTION
#define DIO_VAR_CONST_32_SECTION
#define DIO_VAR_CONST_PTR_SECTION
#define DIO_VAR_CONST_UNSPECIFIED_SECTION
#define DIO_VAR_DATA_NO_INIT_8_SECTION
#define DIO_VAR_DATA_NO_INIT_16_SECTION
#define DIO_VAR_DATA_NO_INIT_32_SECTION
#define DIO_VAR_DATA_NO_INIT_PTR_SECTION
#define DIO_VAR_DATA_NO_INIT_UNSPECIFIED_SECTION
#define DIO_VAR_DATA_INIT_8_SECTION
#define DIO_VAR_DATA_INIT_16_SECTION
#define DIO_VAR_DATA_INIT_32_SECTION
#define DIO_VAR_DATA_INIT_PTR_SECTION
#define DIO_VAR_DATA_INIT_UNSPECIFIED_SECTION
#define DIO_FUNC_TEXT_SECTION
#define DIO_ISR_TEXT_SECTION
#define DIO_FUNC_CALLOUT_TEXT_SECTION

/** \brief Dummy defines for M4 Build */
#define ETH_CONFIG_DATA_SECTION
#define ETH_VAR_CONST_8_SECTION
#define ETH_VAR_CONST_16_SECTION
#define ETH_VAR_CONST_32_SECTION
#define ETH_VAR_CONST_PTR_SECTION
#define ETH_VAR_CONST_UNSPECIFIED_SECTION
#define ETH_VAR_DATA_NO_INIT_8_SECTION
#define ETH_VAR_DATA_NO_INIT_16_SECTION
#define ETH_VAR_DATA_NO_INIT_32_SECTION
#define ETH_VAR_DATA_NO_INIT_PTR_SECTION
#define ETH_VAR_DATA_NO_INIT_UNSPECIFIED_SECTION
#define ETH_VAR_DATA_INIT_8_SECTION
#define ETH_VAR_DATA_INIT_16_SECTION
#define ETH_VAR_DATA_INIT_32_SECTION
#define ETH_VAR_DATA_INIT_PTR_SECTION
#define ETH_VAR_DATA_INIT_UNSPECIFIED_SECTION
#define ETH_FUNC_TEXT_SECTION
#define ETH_ISR_TEXT_SECTION
#define ETH_FUNC_CALLOUT_TEXT_SECTION

/** \brief Dummy defines for M4 Build */
#define FLS_CONFIG_DATA_SECTION
#define FLS_VAR_CONST_8_SECTION
#define FLS_VAR_CONST_16_SECTION
#define FLS_VAR_CONST_32_SECTION
#define FLS_VAR_CONST_PTR_SECTION
#define FLS_VAR_CONST_UNSPECIFIED_SECTION
#define FLS_VAR_DATA_NO_INIT_8_SECTION
#define FLS_VAR_DATA_NO_INIT_16_SECTION
#define FLS_VAR_DATA_NO_INIT_32_SECTION
#define FLS_VAR_DATA_NO_INIT_PTR_SECTION
#define FLS_VAR_DATA_NO_INIT_UNSPECIFIED_SECTION
#define FLS_VAR_DATA_INIT_8_SECTION
#define FLS_VAR_DATA_INIT_16_SECTION
#define FLS_VAR_DATA_INIT_32_SECTION
#define FLS_VAR_DATA_INIT_PTR_SECTION
#define FLS_VAR_DATA_INIT_UNSPECIFIED_SECTION
#define FLS_FUNC_TEXT_SECTION
#define FLS_ISR_TEXT_SECTION
#define FLS_FUNC_CALLOUT_TEXT_SECTION

/** \brief Dummy defines for M4 Build */
#define GPT_CONFIG_DATA_SECTION
#define GPT_VAR_CONST_8_SECTION
#define GPT_VAR_CONST_16_SECTION
#define GPT_VAR_CONST_32_SECTION
#define GPT_VAR_CONST_PTR_SECTION
#define GPT_VAR_CONST_UNSPECIFIED_SECTION
#define GPT_VAR_DATA_NO_INIT_8_SECTION
#define GPT_VAR_DATA_NO_INIT_16_SECTION
#define GPT_VAR_DATA_NO_INIT_32_SECTION
#define GPT_VAR_DATA_NO_INIT_PTR_SECTION
#define GPT_VAR_DATA_NO_INIT_UNSPECIFIED_SECTION
#define GPT_VAR_DATA_INIT_8_SECTION
#define GPT_VAR_DATA_INIT_16_SECTION
#define GPT_VAR_DATA_INIT_32_SECTION
#define GPT_VAR_DATA_INIT_PTR_SECTION
#define GPT_VAR_DATA_INIT_UNSPECIFIED_SECTION
#define GPT_FUNC_TEXT_SECTION
#define GPT_ISR_TEXT_SECTION
#define GPT_FUNC_CALLOUT_TEXT_SECTION

/** \brief Dummy defines for M4 Build */
#define PWM_CONFIG_DATA_SECTION
#define PWM_VAR_CONST_8_SECTION
#define PWM_VAR_CONST_16_SECTION
#define PWM_VAR_CONST_32_SECTION
#define PWM_VAR_CONST_PTR_SECTION
#define PWM_VAR_CONST_UNSPECIFIED_SECTION
#define PWM_VAR_DATA_NO_INIT_8_SECTION
#define PWM_VAR_DATA_NO_INIT_16_SECTION
#define PWM_VAR_DATA_NO_INIT_32_SECTION
#define PWM_VAR_DATA_NO_INIT_PTR_SECTION
#define PWM_VAR_DATA_NO_INIT_UNSPECIFIED_SECTION
#define PWM_VAR_DATA_INIT_8_SECTION
#define PWM_VAR_DATA_INIT_16_SECTION
#define PWM_VAR_DATA_INIT_32_SECTION
#define PWM_VAR_DATA_INIT_PTR_SECTION
#define PWM_VAR_DATA_INIT_UNSPECIFIED_SECTION
#define PWM_FUNC_TEXT_SECTION
#define PWM_ISR_TEXT_SECTION
#define PWM_FUNC_CALLOUT_TEXT_SECTION

/** \brief Dummy defines for M4 Build */
#define SPI_CONFIG_DATA_SECTION
#define SPI_VAR_CONST_8_SECTION
#define SPI_VAR_CONST_16_SECTION
#define SPI_VAR_CONST_32_SECTION
#define SPI_VAR_CONST_PTR_SECTION
#define SPI_VAR_CONST_UNSPECIFIED_SECTION
#define SPI_VAR_DATA_NO_INIT_8_SECTION
#define SPI_VAR_DATA_NO_INIT_16_SECTION
#define SPI_VAR_DATA_NO_INIT_32_SECTION
#define SPI_VAR_DATA_NO_INIT_PTR_SECTION
#define SPI_VAR_DATA_NO_INIT_UNSPECIFIED_SECTION
#define SPI_VAR_DATA_INIT_8_SECTION
#define SPI_VAR_DATA_INIT_16_SECTION
#define SPI_VAR_DATA_INIT_32_SECTION
#define SPI_VAR_DATA_INIT_PTR_SECTION
#define SPI_VAR_DATA_INIT_UNSPECIFIED_SECTION
#define SPI_FUNC_TEXT_SECTION
#define SPI_ISR_TEXT_SECTION
#define SPI_FUNC_CALLOUT_TEXT_SECTION

/** \brief Dummy defines for M4 Build */
#define WDG_CONFIG_DATA_SECTION
#define WDG_VAR_CONST_8_SECTION
#define WDG_VAR_CONST_16_SECTION
#define WDG_VAR_CONST_32_SECTION
#define WDG_VAR_CONST_PTR_SECTION
#define WDG_VAR_CONST_UNSPECIFIED_SECTION
#define WDG_VAR_DATA_NO_INIT_8_SECTION
#define WDG_VAR_DATA_NO_INIT_16_SECTION
#define WDG_VAR_DATA_NO_INIT_32_SECTION
#define WDG_VAR_DATA_NO_INIT_PTR_SECTION
#define WDG_VAR_DATA_NO_INIT_UNSPECIFIED_SECTION
#define WDG_VAR_DATA_INIT_8_SECTION
#define WDG_VAR_DATA_INIT_16_SECTION
#define WDG_VAR_DATA_INIT_32_SECTION
#define WDG_VAR_DATA_INIT_PTR_SECTION
#define WDG_VAR_DATA_INIT_UNSPECIFIED_SECTION
#define WDG_FUNC_TEXT_SECTION
#define WDG_ISR_TEXT_SECTION
#define WDG_FUNC_CALLOUT_TEXT_SECTION


#define ADC_APPL_CODE
#define ADC_CALLOUT_CODE
#define ADC_VAR_NOINIT
#define ADC_VAR_FAST
#define ADC_VAR
#define ADC_REGSPACE
#define ADC_CODE
#define ADC_CODE_FAST
#define ADC_CODE_SLOW
#define ADC_CONST
#define ADC_CALIB
#define ADC_CONFIG_DATA
#define ADC_APPL_DATA
#define ADC_APPL_CONST
#define ADC_VAR_NO_INIT
#define ADC_VAR_CLEARED
#define ADC_VAR_POWER_ON_CLEARED
#define ADC_VAR_INIT
#define ADC_VAR_POWER_ON_INIT
#define ADC_VAR_FAST_NO_INIT
#define ADC_VAR_FAST_CLEARED
#define ADC_VAR_FAST_POWER_ON_CLEARED
#define ADC_VAR_FAST_INIT
#define ADC_VAR_FAST_POWER_ON_INIT
#define ADC_VAR_SLOW_NO_INIT
#define ADC_VAR_SLOW_CLEARED
#define ADC_VAR_SLOW_POWER_ON_CLEARED
#define ADC_VAR_SLOW_INIT
#define ADC_VAR_SLOW_POWER_ON_INIT
#define ADC_INTERNAL_VAR_NO_INIT
#define ADC_INTERNAL_VAR_CLEARED
#define ADC_INTERNAL_VAR_POWER_ON_CLEARED
#define ADC_INTERNAL_VAR_INIT
#define ADC_INTERNAL_VAR_POWER_ON_INIT

#define CAN_APPL_CODE
#define CAN_CALLOUT_CODE
#define CAN_VAR_NOINIT
#define CAN_VAR_FAST
#define CAN_VAR
#define CAN_REGSPACE
#define CAN_CODE
#define CAN_CODE_FAST
#define CAN_CODE_SLOW
#define CAN_CONST
#define CAN_CALIB
#define CAN_APPL_DATA
#define CAN_APPL_CONST
#define CAN_VAR_NO_INIT
#define CAN_VAR_CLEARED
#define CAN_VAR_POWER_ON_CLEARED
#define CAN_VAR_INIT
#define CAN_VAR_POWER_ON_INIT
#define CAN_VAR_FAST_NO_INIT
#define CAN_VAR_FAST_CLEARED
#define CAN_VAR_FAST_POWER_ON_CLEARED
#define CAN_VAR_FAST_INIT
#define CAN_VAR_FAST_POWER_ON_INIT
#define CAN_VAR_SLOW_NO_INIT
#define CAN_VAR_SLOW_CLEARED
#define CAN_VAR_SLOW_POWER_ON_CLEARED
#define CAN_VAR_SLOW_INIT
#define CAN_VAR_SLOW_POWER_ON_INIT
#define CAN_INTERNAL_VAR_NO_INIT
#define CAN_INTERNAL_VAR_CLEARED
#define CAN_INTERNAL_VAR_POWER_ON_CLEARED
#define CAN_INTERNAL_VAR_INIT
#define CAN_INTERNAL_VAR_POWER_ON_INIT

#define CDD_IPC_APPL_CODE
#define CDD_IPC_CALLOUT_CODE
#define CDD_IPC_VAR_NOINIT
#define CDD_IPC_VAR_FAST
#define CDD_IPC_VAR
#define CDD_IPC_REGSPACE
#define CDD_IPC_REGSPACE
#define CDD_IPC_CODE
#define CDD_IPC_CODE_FAST
#define CDD_IPC_CODE_SLOW
#define CDD_IPC_CONST
#define CDD_IPC_CALIB
#define CDD_IPC_CONFIG_DATA
#define CDD_IPC_APPL_DATA
#define CDD_IPC_APPL_CONST
#define CDD_IPC_VAR_NO_INIT
#define CDD_IPC_VAR_CLEARED
#define CDD_IPC_VAR_POWER_ON_CLEARED
#define CDD_IPC_VAR_INIT
#define CDD_IPC_VAR_POWER_ON_INIT
#define CDD_IPC_VAR_FAST_NO_INIT
#define CDD_IPC_VAR_FAST_CLEARED
#define CDD_IPC_VAR_FAST_POWER_ON_CLEARED
#define CDD_IPC_VAR_FAST_INIT
#define CDD_IPC_VAR_FAST_POWER_ON_INIT
#define CDD_IPC_VAR_SLOW_NO_INIT
#define CDD_IPC_VAR_SLOW_CLEARED
#define CDD_IPC_VAR_SLOW_POWER_ON_CLEARED
#define CDD_IPC_VAR_SLOW_INIT
#define CDD_IPC_VAR_SLOW_POWER_ON_INIT
#define CDD_IPC_INTERNAL_VAR_NO_INIT
#define CDD_IPC_INTERNAL_VAR_CLEARED
#define CDD_IPC_INTERNAL_VAR_POWER_ON_CLEARED
#define CDD_IPC_INTERNAL_VAR_INIT
#define CDD_IPC_INTERNAL_VAR_POWER_ON_INIT

#define DIO_CALLOUT_CODE
#define DIO_VAR_NOINIT
#define DIO_VAR_FAST
#define DIO_REGSPACE
#define DIO_CODE
#define DIO_CALIB
#define DIO_VAR_CLEARED
#define DIO_VAR_POWER_ON_CLEARED
#define DIO_VAR_POWER_ON_INIT
#define DIO_VAR_FAST_CLEARED
#define DIO_VAR_FAST_POWER_ON_CLEARED
#define DIO_VAR_FAST_INIT
#define DIO_VAR_FAST_POWER_ON_INIT
#define DIO_VAR_SLOW_NO_INIT
#define DIO_VAR_SLOW_CLEARED
#define DIO_VAR_SLOW_POWER_ON_CLEARED
#define DIO_VAR_SLOW_INIT
#define DIO_VAR_SLOW_POWER_ON_INIT
#define DIO_INTERNAL_VAR_NO_INIT
#define DIO_INTERNAL_VAR_CLEARED
#define DIO_INTERNAL_VAR_POWER_ON_CLEARED
#define DIO_INTERNAL_VAR_INIT
#define DIO_INTERNAL_VAR_POWER_ON_INIT

#define ETH_CALLOUT_CODE
#define ETH_VAR_NOINIT
#define ETH_VAR_FAST
#define ETH_REGSPACE
#define ETH_CODE
#define ETH_CODE_SLOW
#define ETH_CALIB
#define ETH_VAR_CLEARED
#define ETH_VAR_POWER_ON_CLEARED
#define ETH_VAR_POWER_ON_INIT
#define ETH_VAR_FAST_CLEARED
#define ETH_VAR_FAST_POWER_ON_CLEARED
#define ETH_VAR_FAST_INIT
#define ETH_VAR_FAST_POWER_ON_INIT
#define ETH_VAR_SLOW_NO_INIT
#define ETH_VAR_SLOW_CLEARED
#define ETH_VAR_SLOW_POWER_ON_CLEARED
#define ETH_VAR_SLOW_INIT
#define ETH_VAR_SLOW_POWER_ON_INIT
#define ETH_INTERNAL_VAR_NO_INIT
#define ETH_INTERNAL_VAR_CLEARED
#define ETH_INTERNAL_VAR_POWER_ON_CLEARED
#define ETH_INTERNAL_VAR_INIT
#define ETH_INTERNAL_VAR_POWER_ON_INIT
#define ETH_VAR_NO_INIT_UDMA

#define FLS_CODE
#define FLS_CALIB
#define FLS_VAR_CLEARED
#define FLS_VAR_POWER_ON_CLEARED
#define FLS_VAR_POWER_ON_INIT
#define FLS_VAR_FAST_CLEARED
#define FLS_VAR_FAST_POWER_ON_CLEARED
#define FLS_VAR_FAST_INIT
#define FLS_VAR_FAST_POWER_ON_INIT
#define FLS_VAR_SLOW_NO_INIT
#define FLS_VAR_SLOW_CLEARED
#define FLS_VAR_SLOW_POWER_ON_CLEARED
#define FLS_VAR_SLOW_INIT
#define FLS_VAR_SLOW_POWER_ON_INIT
#define FLS_INTERNAL_VAR_NO_INIT
#define FLS_INTERNAL_VAR_CLEARED
#define FLS_INTERNAL_VAR_POWER_ON_CLEARED
#define FLS_INTERNAL_VAR_INIT
#define FLS_INTERNAL_VAR_POWER_ON_INIT

#define GPT_CALLOUT_CODE
#define GPT_VAR_NOINIT
#define GPT_VAR_FAST
#define GPT_REGSPACE
#define GPT_CODE
#define GPT_CALIB
#define GPT_VAR_CLEARED
#define GPT_VAR_POWER_ON_CLEARED
#define GPT_VAR_POWER_ON_INIT
#define GPT_VAR_FAST_CLEARED
#define GPT_VAR_FAST_POWER_ON_CLEARED
#define GPT_VAR_FAST_INIT
#define GPT_VAR_FAST_POWER_ON_INIT
#define GPT_VAR_SLOW_NO_INIT
#define GPT_VAR_SLOW_CLEARED
#define GPT_VAR_SLOW_POWER_ON_CLEARED
#define GPT_VAR_SLOW_INIT
#define GPT_VAR_SLOW_POWER_ON_INIT
#define GPT_INTERNAL_VAR_NO_INIT
#define GPT_INTERNAL_VAR_CLEARED
#define GPT_INTERNAL_VAR_POWER_ON_CLEARED
#define GPT_INTERNAL_VAR_INIT
#define GPT_INTERNAL_VAR_POWER_ON_INIT

#define PWM_APPL_CODE
#define PWM_CALLOUT_CODE
#define PWM_VAR_NOINIT
#define PWM_VAR_FAST
#define PWM_VAR
#define PWM_REGSPACE
#define PWM_CODE
#define PWM_CODE_FAST
#define PWM_CODE_SLOW
#define PWM_CONST
#define PWM_CALIB
#define PWM_CONFIG_DATA
#define PWM_APPL_DATA
#define PWM_APPL_CONST
#define PWM_VAR_NO_INIT
#define PWM_VAR_CLEARED
#define PWM_VAR_POWER_ON_CLEARED
#define PWM_VAR_INIT
#define PWM_VAR_POWER_ON_INIT
#define PWM_VAR_FAST_NO_INIT
#define PWM_VAR_FAST_CLEARED
#define PWM_VAR_FAST_POWER_ON_CLEARED
#define PWM_VAR_FAST_INIT
#define PWM_VAR_FAST_POWER_ON_INIT
#define PWM_VAR_SLOW_NO_INIT
#define PWM_VAR_SLOW_CLEARED
#define PWM_VAR_SLOW_POWER_ON_CLEARED
#define PWM_VAR_SLOW_INIT
#define PWM_VAR_SLOW_POWER_ON_INIT
#define PWM_INTERNAL_VAR_NO_INIT
#define PWM_INTERNAL_VAR_CLEARED
#define PWM_INTERNAL_VAR_POWER_ON_CLEARED
#define PWM_INTERNAL_VAR_INIT
#define PWM_INTERNAL_VAR_POWER_ON_INIT

#define SPI_CALLOUT_CODE
#define SPI_VAR_NOINIT
#define SPI_VAR_FAST
#define SPI_REGSPACE
#define SPI_CODE
#define SPI_CALIB
#define SPI_VAR_CLEARED
#define SPI_VAR_POWER_ON_CLEARED
#define SPI_VAR_POWER_ON_INIT
#define SPI_VAR_FAST_CLEARED
#define SPI_VAR_FAST_POWER_ON_CLEARED
#define SPI_VAR_FAST_INIT
#define SPI_VAR_FAST_POWER_ON_INIT
#define SPI_VAR_SLOW_NO_INIT
#define SPI_VAR_SLOW_CLEARED
#define SPI_VAR_SLOW_POWER_ON_CLEARED
#define SPI_VAR_SLOW_INIT
#define SPI_VAR_SLOW_POWER_ON_INIT
#define SPI_INTERNAL_VAR_NO_INIT
#define SPI_INTERNAL_VAR_CLEARED
#define SPI_INTERNAL_VAR_POWER_ON_CLEARED
#define SPI_INTERNAL_VAR_INIT
#define SPI_INTERNAL_VAR_POWER_ON_INIT

#define WDG_CALLOUT_CODE
#define WDG_VAR_NOINIT
#define WDG_VAR_FAST
#define WDG_REGSPACE
#define WDG_CODE
#define WDG_CALIB
#define WDG_VAR_CLEARED
#define WDG_VAR_POWER_ON_CLEARED
#define WDG_VAR_POWER_ON_INIT
#define WDG_VAR_FAST_NO_INIT
#define WDG_VAR_FAST_CLEARED
#define WDG_VAR_FAST_POWER_ON_CLEARED
#define WDG_VAR_FAST_INIT
#define WDG_VAR_FAST_POWER_ON_INIT
#define WDG_VAR_SLOW_NO_INIT
#define WDG_VAR_SLOW_CLEARED
#define WDG_VAR_SLOW_POWER_ON_CLEARED
#define WDG_VAR_SLOW_INIT
#define WDG_VAR_SLOW_POWER_ON_INIT
#define WDG_INTERNAL_VAR_NO_INIT
#define WDG_INTERNAL_VAR_CLEARED
#define WDG_INTERNAL_VAR_POWER_ON_CLEARED
#define WDG_INTERNAL_VAR_INIT
#define WDG_INTERNAL_VAR_POWER_ON_INIT

/* ---------------------------------------------------------------------------*/
/*                   MCU                                                      */
/* ---------------------------------------------------------------------------*/
/* API functions */
#define MCU_PUBLIC_CODE                COMPILER_ATTRIBUTE_GENERAL
/* API constants */
#define MCU_PUBLIC_CONST               COMPILER_ATTRIBUTE_GENERAL

/* Internal functions */
#define MCU_PRIVATE_CODE               COMPILER_ATTRIBUTE_GENERAL
/* Internal ROM Data */
#define MCU_PRIVATE_CONST              COMPILER_ATTRIBUTE_GENERAL

/* callbacks of the Application */
#define MCU_APPL_CODE                  COMPILER_ATTRIBUTE_GENERAL
/* Applications' ROM Data */
#define MCU_APPL_CONST                 COMPILER_ATTRIBUTE_GENERAL
/* Applications' RAM Data */
#define MCU_APPL_DATA                  COMPILER_ATTRIBUTE_GENERAL
/* 'Near' RAM Data */
#define MCU_VAR_FAST_NO_INIT           COMPILER_ATTRIBUTE_GENERAL

/* API functions */
#define MCU_CODE_FAST                  COMPILER_ATTRIBUTE_GENERAL
/* API constants */
#define MCU_CONST                      COMPILER_ATTRIBUTE_GENERAL

/* Desc. Tables -> Config-dependent Config. dependent (reg. size) data */
#define MCU_CONFIG_DATA                COMPILER_ATTRIBUTE_GENERAL

/* Data which is initialized during Startup */
#define MCU_VAR_INIT                   COMPILER_ATTRIBUTE_GENERAL

/* Data which is not initialized during Startup */
#define MCU_VAR_NO_INIT                COMPILER_ATTRIBUTE_GENERAL

/* Data Constants */
#define MCU_CONST                      COMPILER_ATTRIBUTE_GENERAL
/* Memory class for global variables which are initialized after every reset */
#define MCU_VAR                        COMPILER_ATTRIBUTE_GENERAL

/* ---------------------------------------------------------------------------*/
/*                   GPT                                                      */
/* ---------------------------------------------------------------------------*/
/* API functions */
#define GPT_PUBLIC_CODE                COMPILER_ATTRIBUTE_GENERAL
/* API constants */
#define GPT_PUBLIC_CONST               COMPILER_ATTRIBUTE_GENERAL

/* Internal functions */
#define GPT_PRIVATE_CODE               COMPILER_ATTRIBUTE_GENERAL

/* Internal ROM Data */
#define GPT_PRIVATE_CONST              COMPILER_ATTRIBUTE_GENERAL

/* callbacks of the Application */
#define GPT_APPL_CODE                  COMPILER_ATTRIBUTE_GENERAL
/* Applications' ROM Data */
#define GPT_APPL_CONST                 COMPILER_ATTRIBUTE_GENERAL
/* Applications' RAM Data */
#define GPT_APPL_DATA                  COMPILER_ATTRIBUTE_GENERAL
/* 'Near' RAM Data */
#define GPT_VAR_FAST_NO_INIT           COMPILER_ATTRIBUTE_GENERAL

/* API functions */
#define GPT_CODE_FAST                  COMPILER_ATTRIBUTE_GENERAL
/* API constants */
#define GPT_CONST                      COMPILER_ATTRIBUTE_GENERAL

/* Desc. Tables -> Config-dependent Config. dependent (reg. size) data */
#define GPT_CONFIG_DATA                COMPILER_ATTRIBUTE_GENERAL

/* Data which is initialized during Startup */
#define GPT_VAR_INIT                   COMPILER_ATTRIBUTE_GENERAL

/* Data which is not initialized during Startup */
#define GPT_VAR_NO_INIT                COMPILER_ATTRIBUTE_GENERAL

/* Data Constants */
//#define GPT_CONST                      COMPILER_ATTRIBUTE_GENERAL
/* Memory class for global variables which are initialized after every reset */
#define GPT_VAR                        COMPILER_ATTRIBUTE_GENERAL

/* ---------------------------------------------------------------------------*/
/*                   WDG                                                      */
/* ---------------------------------------------------------------------------*/
/* API functions */
#define WDG_PUBLIC_CODE                COMPILER_ATTRIBUTE_GENERAL
/* API constants */
#define WDG_PUBLIC_CONST               COMPILER_ATTRIBUTE_GENERAL

/* Internal functions */
#define WDG_PRIVATE_CODE               COMPILER_ATTRIBUTE_GENERAL

/* Module internal data */
#define WDG_PRIVATE_DATA               COMPILER_ATTRIBUTE_GENERAL
/* Internal ROM Data */
#define WDG_PRIVATE_CONST              COMPILER_ATTRIBUTE_GENERAL

/* callbacks of the Application */
#define WDG_APPL_CODE                  COMPILER_ATTRIBUTE_GENERAL
/* Applications' ROM Data */
#define WDG_APPL_CONST                 COMPILER_ATTRIBUTE_GENERAL
/* Applications' RAM Data */
#define WDG_APPL_DATA                  COMPILER_ATTRIBUTE_GENERAL
/* 'Near' RAM Data */
#define WDG_FAST_DATA                  COMPILER_ATTRIBUTE_GENERAL

/* API functions */
#define WDG_CODE_FAST                  COMPILER_ATTRIBUTE_GENERAL
/* API constants */
#define WDG_FAST_CONST                 COMPILER_ATTRIBUTE_GENERAL

/* Desc. Tables -> Config-dependent */
#define WDG_CONFIG_CONST               COMPILER_ATTRIBUTE_GENERAL
/* Config. dependent (reg. size) data */
#define WDG_CONFIG_DATA                COMPILER_ATTRIBUTE_GENERAL

/* Data which is initialized during Startup */
#define WDG_INIT_DATA                  COMPILER_ATTRIBUTE_GENERAL
/* Data which is not initialized during Startup */
#define WDG_NO_INIT_DATA               COMPILER_ATTRIBUTE_GENERAL

/* Data Constants */
#define WDG_CONST                      COMPILER_ATTRIBUTE_GENERAL
/* Memory class for global variables which are initialized after every reset */
#define WDG_VAR                        COMPILER_ATTRIBUTE_GENERAL

/* Memory class for global variables which are not initialized by driver */
#define WDG_VAR_NO_INIT                COMPILER_ATTRIBUTE_GENERAL
/* Memory class for global variables which are initialized by driver */
#define WDG_VAR_INIT                   COMPILER_ATTRIBUTE_GENERAL

/* ---------------------------------------------------------------------------*/
/*                   PORT                                                     */
/* ---------------------------------------------------------------------------*/
/* API functions */
#define PORT_PUBLIC_CODE               COMPILER_ATTRIBUTE_GENERAL
/* API constants */
#define PORT_PUBLIC_CONST              COMPILER_ATTRIBUTE_GENERAL

/* Internal functions */
#define PORT_PRIVATE_CODE              COMPILER_ATTRIBUTE_GENERAL
/* Internal ROM Data */
#define PORT_PRIVATE_CONST             COMPILER_ATTRIBUTE_GENERAL

/* callbacks of the Application */
#define PORT_APPL_CODE                 COMPILER_ATTRIBUTE_GENERAL
/* Applications' ROM Data */
#define PORT_APPL_CONST                COMPILER_ATTRIBUTE_GENERAL
/* Applications' RAM Data */
#define PORT_APPL_DATA                 COMPILER_ATTRIBUTE_GENERAL
/* 'Near' RAM Data */
#define PORT_VAR_FAST_NO_INIT          COMPILER_ATTRIBUTE_GENERAL

/* API functions */
#define PORT_CODE_FAST                 COMPILER_ATTRIBUTE_GENERAL
/* API constants */
#define PORT_CONST                     COMPILER_ATTRIBUTE_GENERAL

/* Desc. Tables -> Config-dependent Config. dependent (reg. size) data */
#define PORT_CONFIG_DATA               COMPILER_ATTRIBUTE_GENERAL

/* Data which is initialized during Startup */
#define PORT_VAR_INIT                  COMPILER_ATTRIBUTE_GENERAL
/* Data which is not initialized during Startup */
#define PORT_VAR_NO_INIT               COMPILER_ATTRIBUTE_GENERAL

/* Data Constants */
#define PORT_CONST                     COMPILER_ATTRIBUTE_GENERAL
/* Memory class for global variables which are initialized after every reset */
#define PORT_VAR                       COMPILER_ATTRIBUTE_GENERAL

/* ---------------------------------------------------------------------------*/
/*                   DIO                                                      */
/* ---------------------------------------------------------------------------*/
/* API functions */
#define DIO_PUBLIC_CODE                COMPILER_ATTRIBUTE_GENERAL
/* API constants */
#define DIO_PUBLIC_CONST               COMPILER_ATTRIBUTE_GENERAL

/* Internal functions */
#define DIO_PRIVATE_CODE               COMPILER_ATTRIBUTE_GENERAL

/* Internal ROM Data */
#define DIO_PRIVATE_CONST              COMPILER_ATTRIBUTE_GENERAL

/* callbacks of the Application */
#define DIO_APPL_CODE                  COMPILER_ATTRIBUTE_GENERAL
/* Applications' ROM Data */
#define DIO_APPL_CONST                 COMPILER_ATTRIBUTE_GENERAL
/* Applications' RAM Data */
#define DIO_APPL_DATA                  COMPILER_ATTRIBUTE_GENERAL
/* 'Near' RAM Data */
#define DIO_VAR_FAST_NO_INIT           COMPILER_ATTRIBUTE_GENERAL

/* API functions */
#define DIO_CODE_FAST                  COMPILER_ATTRIBUTE_GENERAL
#define DIO_CODE_SLOW                  COMPILER_ATTRIBUTE_GENERAL
/* API constants */
#define DIO_CONST                      COMPILER_ATTRIBUTE_GENERAL

/* Desc. Tables -> Config-dependent Config. dependent (reg. size) data */
#define DIO_CONFIG_DATA                COMPILER_ATTRIBUTE_GENERAL

/* Data which is initialized during Startup */
#define DIO_VAR_INIT                   COMPILER_ATTRIBUTE_GENERAL
/* Data which is not initialized during Startup */
#define DIO_VAR_NO_INIT                COMPILER_ATTRIBUTE_GENERAL

/* Data Constants */
//#define DIO_CONST                      COMPILER_ATTRIBUTE_GENERAL
/* Memory class for global variables which are initialized after every reset */
#define DIO_VAR                        COMPILER_ATTRIBUTE_GENERAL

/* ---------------------------------------------------------------------------*/
/*                   SPI                                                      */
/* ---------------------------------------------------------------------------*/
/* API functions */
#define SPI_PUBLIC_CODE                COMPILER_ATTRIBUTE_GENERAL
/* API constants */
#define SPI_PUBLIC_CONST               COMPILER_ATTRIBUTE_GENERAL

/* Internal functions */
#define SPI_PRIVATE_CODE               COMPILER_ATTRIBUTE_GENERAL
/* Internal ROM Data */
#define SPI_PRIVATE_CONST              COMPILER_ATTRIBUTE_GENERAL

/* callbacks of the Application */
#define SPI_APPL_CODE                  COMPILER_ATTRIBUTE_GENERAL
/* Applications' ROM Data */
#define SPI_APPL_CONST                 COMPILER_ATTRIBUTE_GENERAL
/* Applications' RAM Data */
#define SPI_APPL_DATA                  COMPILER_ATTRIBUTE_GENERAL
/* 'Near' RAM Data */
#define SPI_VAR_FAST_NO_INIT           COMPILER_ATTRIBUTE_GENERAL

/* API functions */
#define SPI_CODE_FAST                  COMPILER_ATTRIBUTE_GENERAL
/* API constants */
#define SPI_CONST                      COMPILER_ATTRIBUTE_GENERAL

/* Desc. Tables -> Config-dependent Config. dependent (reg. size) data */
#define SPI_CONFIG_DATA                COMPILER_ATTRIBUTE_GENERAL

/* Data which is initialized during Startup */
#define SPI_VAR_INIT                   COMPILER_ATTRIBUTE_GENERAL
/* Data which is not initialized during Startup */
#define SPI_VAR_NO_INIT                COMPILER_ATTRIBUTE_GENERAL

/* Data Constants */
//#define SPI_CONST                      COMPILER_ATTRIBUTE_GENERAL
/* Memory class for global variables which are initialized after every reset */
#define SPI_VAR                        COMPILER_ATTRIBUTE_GENERAL

/* ---------------------------------------------------------------------------*/
/*                   CAN                                                      */
/* ---------------------------------------------------------------------------*/
/* API functions */
#define CAN_RSCAN_PUBLIC_CODE          COMPILER_ATTRIBUTE_GENERAL
/* API constants */
#define CAN_RSCAN_PUBLIC_CONST         COMPILER_ATTRIBUTE_GENERAL

/* Internal functions */
#define CAN_RSCAN_PRIVATE_CODE         COMPILER_ATTRIBUTE_GENERAL

/* Internal ROM Data */
#define CAN_RSCAN_PRIVATE_CONST        COMPILER_ATTRIBUTE_GENERAL

/* callbacks of the Application */
#define CAN_RSCAN_APPL_CODE            COMPILER_ATTRIBUTE_GENERAL
/* Applications' ROM Data */
#define CAN_RSCAN_APPL_CONST           COMPILER_ATTRIBUTE_GENERAL
/* Applications' RAM Data */
#define CAN_RSCAN_APPL_DATA            COMPILER_ATTRIBUTE_GENERAL

/* API functions */
#define CAN_RSCAN_CODE_FAST            COMPILER_ATTRIBUTE_GENERAL
/* API constants, Data Constants */
#define CAN_RSCAN_CONST                COMPILER_ATTRIBUTE_GENERAL

/* Desc. Tables -> Config-dependent Config. dependent (reg. size) data */
#define CAN_RSCAN_CONFIG_DATA          COMPILER_ATTRIBUTE_GENERAL
#define CAN_CONFIG_DATA                COMPILER_ATTRIBUTE_GENERAL

/* Data which is initialized during Startup */
#define CAN_RSCAN_VAR_INIT             COMPILER_ATTRIBUTE_GENERAL

/* Data which is not initialized during Startup */
#define CAN_RSCAN_VAR_NO_INIT          COMPILER_ATTRIBUTE_GENERAL

/* Memory class for global variables which are initialized after every reset */
#define CAN_RSCAN_VAR                  COMPILER_ATTRIBUTE_GENERAL


/* ---------------------------------------------------------------------------*/
/*                   FLS                                                      */
/* ---------------------------------------------------------------------------*/
/* API functions */
#define FLS_PUBLIC_CODE                COMPILER_ATTRIBUTE_GENERAL
/* API constants */
#define FLS_PUBLIC_CONST               COMPILER_ATTRIBUTE_GENERAL
/* API functions */
#define FLS_CODE_SLOW                  COMPILER_ATTRIBUTE_GENERAL

/* Internal functions */
#define FLS_PRIVATE_CODE               COMPILER_ATTRIBUTE_GENERAL

/* Internal ROM Data */
#define FLS_PRIVATE_CONST              COMPILER_ATTRIBUTE_GENERAL

/* callbacks of the Application */
#define FLS_APPL_CODE                  COMPILER_ATTRIBUTE_GENERAL
/* Applications' ROM Data */
#define FLS_APPL_CONST                 COMPILER_ATTRIBUTE_GENERAL
/* Applications' RAM Data */
#define FLS_APPL_DATA                  COMPILER_ATTRIBUTE_GENERAL
/* 'Near' RAM Data */
#define FLS_VAR_FAST_NO_INIT           COMPILER_ATTRIBUTE_GENERAL

/* API functions */
#define FLS_CODE_FAST                  COMPILER_ATTRIBUTE_GENERAL
/* API constants */
#define FLS_CONST                      COMPILER_ATTRIBUTE_GENERAL

/* Desc. Tables -> Config-dependent Config. dependent (reg. size) data */
#define FLS_CONFIG_DATA                COMPILER_ATTRIBUTE_GENERAL

/* Data which is initialized during Startup */
#define FLS_VAR_INIT                   COMPILER_ATTRIBUTE_GENERAL
/* Data which is not initialized during Startup */
#define FLS_VAR_NO_INIT                COMPILER_ATTRIBUTE_GENERAL
/* Data Constants                      */
//#define FLS_CONST                      COMPILER_ATTRIBUTE_GENERAL

/* Memory class for global variables which are initialized after every reset */
#define FLS_VAR                        COMPILER_ATTRIBUTE_GENERAL

/* ---------------------------------------------------------------------------*/
/*                   ETH                                                      */
/* ---------------------------------------------------------------------------*/
/* API functions */
#define ETH_PUBLIC_CODE                COMPILER_ATTRIBUTE_GENERAL
/* API constants */
#define ETH_PUBLIC_CONST               COMPILER_ATTRIBUTE_GENERAL

/* ETH URAM descriptor */
#define ETH_PORT_RAM_0                 COMPILER_ATTRIBUTE_GENERAL
/* ETH URAM descriptor */
#define ETH_PORT_RAM_1                 COMPILER_ATTRIBUTE_GENERAL
/* ETH URAM descriptor */
#define ETH_PORT_RAM_2                 COMPILER_ATTRIBUTE_GENERAL
/* ETH URAM descriptor */
#define ETH_VAR_TX_RAM                 COMPILER_ATTRIBUTE_GENERAL
/* ETH URAM descriptor */
#define ETH_VAR_RX_RAM                 COMPILER_ATTRIBUTE_GENERAL

/* Internal functions */
#define ETH_PRIVATE_CODE               COMPILER_ATTRIBUTE_GENERAL
/* Internal ROM Data */
#define ETH_PRIVATE_CONST              COMPILER_ATTRIBUTE_GENERAL

/* callbacks of the Application */
#define ETH_APPL_CODE                  COMPILER_ATTRIBUTE_GENERAL
/* Applications' ROM Data */
#define ETH_APPL_CONST                 COMPILER_ATTRIBUTE_GENERAL
/* Applications' RAM Data */
#define ETH_APPL_DATA                  COMPILER_ATTRIBUTE_GENERAL
/* 'Near' RAM Data */
#define ETH_VAR_FAST_NO_INIT           COMPILER_ATTRIBUTE_GENERAL

/* API functions */
#define ETH_CODE_FAST                  COMPILER_ATTRIBUTE_GENERAL
/* API constants */
#define ETH_CONST                      COMPILER_ATTRIBUTE_GENERAL

/* Desc. Tables -> Config-dependent Config. dependent (reg. size) data */
#define ETH_CONFIG_DATA                COMPILER_ATTRIBUTE_GENERAL

/* Data which is initialized during Startup */
#define ETH_VAR_INIT                   COMPILER_ATTRIBUTE_GENERAL
/* Data which is not initialized during Startup */
#define ETH_VAR_NO_INIT                COMPILER_ATTRIBUTE_GENERAL
/* Data Constants                      */
//#define ETH_CONST                      COMPILER_ATTRIBUTE_GENERAL
/* Memory class for global variables which are initialized after every reset */
#define ETH_VAR                        COMPILER_ATTRIBUTE_GENERAL

/* ---------------------------------------------------------------------------*/
/*                   CDDICCOM                                                 */
/* ---------------------------------------------------------------------------*/
/* API functions */
#define CDDICCOM_CODE                  COMPILER_ATTRIBUTE_GENERAL
/* API functions */
#define CDDICCOM_CODE_SLOW             COMPILER_ATTRIBUTE_GENERAL
/* API functions */
#define CDDICCOM_CODE_FAST             COMPILER_ATTRIBUTE_GENERAL
/* callbacks of the Application */
#define CDDICCOM_APPL_CODE             COMPILER_ATTRIBUTE_GENERAL
/* Applications' ROM Data */
#define CDDICCOM_APPL_CONST            COMPILER_ATTRIBUTE_GENERAL
/* Applications' RAM Data */
#define CDDICCOM_APPL_DATA             COMPILER_ATTRIBUTE_GENERAL
/* Config. dependent (reg. size) data */
#define CDDICCOM_CONFIG_DATA           COMPILER_ATTRIBUTE_GENERAL
/* Data which is initialized during Startup */
#define CDDICCOM_VAR_INIT              COMPILER_ATTRIBUTE_GENERAL
/* Data which is not initialized during Startup */
#define CDDICCOM_VAR_NO_INIT           COMPILER_ATTRIBUTE_GENERAL
/* Data which is cleared during Startup */
#define CDDICCOM_VAR_CLEARED           COMPILER_ATTRIBUTE_GENERAL
/* Data Constants */
#define CDDICCOM_CONST                 COMPILER_ATTRIBUTE_GENERAL
/* Memory class for global variables */
#define CDDICCOM_VAR                   COMPILER_ATTRIBUTE_GENERAL
/* Memory class for runnable entities */
#define CddIccom_CODE                  COMPILER_ATTRIBUTE_GENERAL

/* ---------------------------------------------------------------------------*/
/*                   CDDIIC                                                   */
/* ---------------------------------------------------------------------------*/
/* API functions */
#define CDDIIC_CODE                    COMPILER_ATTRIBUTE_GENERAL
/* API functions */
#define CDDIIC_CODE_SLOW               COMPILER_ATTRIBUTE_GENERAL
/* API functions */
#define CDDIIC_CODE_FAST               COMPILER_ATTRIBUTE_GENERAL
/* callbacks of the Application */
#define CDDIIC_APPL_CODE               COMPILER_ATTRIBUTE_GENERAL
/* Applications' ROM Data */
#define CDDIIC_APPL_CONST              COMPILER_ATTRIBUTE_GENERAL
/* Applications' RAM Data */
#define CDDIIC_APPL_DATA               COMPILER_ATTRIBUTE_GENERAL
/* Config. dependent (reg. size) data */
#define CDDIIC_CONFIG_DATA             COMPILER_ATTRIBUTE_GENERAL
/* Data which is initialized during Startup */
#define CDDIIC_VAR_INIT                COMPILER_ATTRIBUTE_GENERAL
/* Data which is not initialized during Startup */
#define CDDIIC_VAR_NO_INIT             COMPILER_ATTRIBUTE_GENERAL
/* Data which is cleared during Startup */
#define CDDIIC_VAR_CLEARED             COMPILER_ATTRIBUTE_GENERAL
/* Data Constants */
#define CDDIIC_CONST                   COMPILER_ATTRIBUTE_GENERAL
/* Memory class for global variables */
#define CDDIIC_VAR                     COMPILER_ATTRIBUTE_GENERAL
/* Memory class for runnable entities */
#define CddIic_CODE                    COMPILER_ATTRIBUTE_GENERAL

/* ---------------------------------------------------------------------------*/
/*                   CDDTHS                                                   */
/* ---------------------------------------------------------------------------*/
#define CDD_THS_CODE_SLOW              COMPILER_ATTRIBUTE_GENERAL
#define CDDTHS_CODE_SLOW                CDD_THS_CODE_SLOW
/* API functions*/
#define CDD_THS_APPL_CODE              COMPILER_ATTRIBUTE_GENERAL
/* notification callbacks of the Application */
#define CDD_THS_APPL_CONST             COMPILER_ATTRIBUTE_GENERAL
#define CDDTHS_APPL_CONST                CDD_THS_APPL_CONST
/* Applications' ROM Data */
#define CDD_THS_APPL_DATA              COMPILER_ATTRIBUTE_GENERAL
#define CDDTHS_APPL_DATA               CDD_THS_APPL_DATA
/* Applications' RAM Data */
#define CDD_THS_CODE_FAST              COMPILER_ATTRIBUTE_GENERAL
/* API functions */
#define CDD_THS_CONFIG_DATA            COMPILER_ATTRIBUTE_GENERAL
/* Config. dependent (reg. size) data */
#define CDD_THS_CONST                  COMPILER_ATTRIBUTE_GENERAL
/* Data Constants */
#define CDD_THS_VAR_INIT               COMPILER_ATTRIBUTE_GENERAL
#define CDDTHS_VAR_INIT                 CDD_THS_VAR_INIT
/* Memory class for global variables which are initialized after every reset */
#define CDD_THS_VAR_CLEARED            COMPILER_ATTRIBUTE_GENERAL
/* Memory class for global variables which are cleared after every reset */
#define CDD_THS_VAR_POWER_ON_CLEARED   COMPILER_ATTRIBUTE_GENERAL
/* Memory class for global variables which are cleared only after power */
/* on reset */
#define CDD_THS_VAR_POWER_ON_INIT      COMPILER_ATTRIBUTE_GENERAL
/* Memory class for global variables which are initialized only after */
/* power on reset */
#define CDD_THS_VAR_NO_INIT            COMPILER_ATTRIBUTE_GENERAL
/* Memory class for global variables which are initialized by driver */
#define CDD_THS_VAR                    COMPILER_ATTRIBUTE_GENERAL
/* Memory class for global variables   */

/* ---------------------------------------------------------------------------*/
/*                   CDDRFSO                                                  */
/* ---------------------------------------------------------------------------*/
/* API functions */
#define CDDRFSO_CODE                   COMPILER_ATTRIBUTE_GENERAL
#define CDDRFSO_CODE_SLOW              COMPILER_ATTRIBUTE_GENERAL
#define CDDRFSO_CODE_FAST              COMPILER_ATTRIBUTE_GENERAL
/* Callback of the Application */
#define CDDRFSO_APPL_CODE              COMPILER_ATTRIBUTE_GENERAL
/* Applications' ROM Data */
#define CDDRFSO_APPL_CONST
/* Applications' RAM Data */
#define CDDRFSO_APPL_DATA
/* Config. dependent (reg. size) data */
#define CDDRFSO_CONFIG_DATA
/* Data which is initialized during Startup */
#define CDDRFSO_VAR_INIT               COMPILER_ATTRIBUTE_GENERAL
/* Data which is not initialized during Startup */
#define CDDRFSO_VAR_NO_INIT            COMPILER_ATTRIBUTE_GENERAL
/* Data which is cleared during Startup */
#define CDDRFSO_VAR_CLEARED            COMPILER_ATTRIBUTE_GENERAL
/* Data Constants */
#define CDDRFSO_CONST                  COMPILER_ATTRIBUTE_GENERAL
/* Memory class for global variables */
#define CDDRFSO_VAR                    COMPILER_ATTRIBUTE_GENERAL

/* ---------------------------------------------------------------------------*/
/*                   CDDIPMMU                                                 */
/* ---------------------------------------------------------------------------*/
/* API functions */
#define CDDIPMMU_CODE                  COMPILER_ATTRIBUTE_GENERAL
#define CDDIPMMU_CODE_SLOW             COMPILER_ATTRIBUTE_GENERAL
#define CDDIPMMU_CODE_FAST             COMPILER_ATTRIBUTE_GENERAL

/* Callback of the Application */
#define CDDIPMMU_APPL_CODE             COMPILER_ATTRIBUTE_GENERAL

/* Applications' ROM Data */
#define CDDIPMMU_APPL_CONST

/* Applications' RAM Data */
#define CDDIPMMU_APPL_DATA

/* Config. dependent (reg. size) data */
#define CDDIPMMU_CONFIG_DATA

/* Data which is initialized during Startup */
#define CDDIPMMU_VAR_INIT              COMPILER_ATTRIBUTE_GENERAL

/* Data which is not initialized during Startup */
#define CDDIPMMU_VAR_NO_INIT           COMPILER_ATTRIBUTE_GENERAL

/* Data which is cleared during Startup */
#define CDDIPMMU_VAR_CLEARED           COMPILER_ATTRIBUTE_GENERAL

/* Data Constants */
#define CDDIPMMU_CONST                 COMPILER_ATTRIBUTE_GENERAL

/* Memory class for global variables */
#define CDDIPMMU_VAR                   COMPILER_ATTRIBUTE_GENERAL

/* ---------------------------------------------------------------------------*/
/*                   CDDCRC                                                   */
/* ---------------------------------------------------------------------------*/
/* API functions */
#define CDDCRC_CODE                    COMPILER_ATTRIBUTE_GENERAL
#define CDDCRC_CODE_SLOW               COMPILER_ATTRIBUTE_GENERAL
#define CDDCRC_CODE_FAST               COMPILER_ATTRIBUTE_GENERAL
#define CDDCRC_PUBLIC_CODE             COMPILER_ATTRIBUTE_GENERAL
/* Callback of the Application */
#define CDDCRC_APPL_CODE               COMPILER_ATTRIBUTE_GENERAL
/* Applications' ROM Data */
#define CDDCRC_APPL_CONST              COMPILER_ATTRIBUTE_GENERAL
/* Applications' RAM Data */
#define CDDCRC_APPL_DATA               COMPILER_ATTRIBUTE_GENERAL
/* Internal functions */
#define CDDCRC_PRIVATE_CODE            COMPILER_ATTRIBUTE_GENERAL
/* Module internal data */
#define CDDCRC_PRIVATE_DATA            COMPILER_ATTRIBUTE_GENERAL
/* Internal ROM Data */
#define CDDCRC_PRIVATE_CONST           COMPILER_ATTRIBUTE_GENERAL
/* Config. dependent (reg. size) data */
#define CDDCRC_CONFIG_DATA             COMPILER_ATTRIBUTE_GENERAL
/* Data which is initialized during Startup */
#define CDDCRC_VAR_INIT                COMPILER_ATTRIBUTE_GENERAL
/* Data which is not initialized during Startup */
#define CDDCRC_VAR_NO_INIT             COMPILER_ATTRIBUTE_GENERAL
/* Data which is cleared during Startup */
#define CDDCRC_VAR_CLEARED             COMPILER_ATTRIBUTE_GENERAL
/* Data Constants */
#define CDDCRC_CONST                   COMPILER_ATTRIBUTE_GENERAL
/* Memory class for global variables */
#define CDDCRC_VAR                     COMPILER_ATTRIBUTE_GENERAL

/* ---------------------------------------------------------------------------*/
/*                   CDDEMM                                                   */
/* ---------------------------------------------------------------------------*/
/* API functions */
#define CDDEMM_CODE                    COMPILER_ATTRIBUTE_GENERAL
#define CDDEMM_CODE_SLOW               COMPILER_ATTRIBUTE_GENERAL
#define CDDEMM_CODE_FAST               COMPILER_ATTRIBUTE_GENERAL

/* Callback of the Application */
#define CDDEMM_APPL_CODE               COMPILER_ATTRIBUTE_GENERAL

/* Applications' ROM Data */
#define CDDEMM_APPL_CONST              COMPILER_ATTRIBUTE_GENERAL

/* Applications' RAM Data */
#define CDDEMM_APPL_DATA               COMPILER_ATTRIBUTE_GENERAL

/* Config. dependent (reg. size) data */
#define CDDEMM_CONFIG_DATA             COMPILER_ATTRIBUTE_GENERAL

/* Data which is initialized during Startup */
#define CDDEMM_VAR_INIT                COMPILER_ATTRIBUTE_GENERAL

/* Data which is not initialized during Startup */
#define CDDEMM_VAR_NO_INIT             COMPILER_ATTRIBUTE_GENERAL

/* Data which is cleared during Startup */
#define CDDEMM_VAR_CLEARED             COMPILER_ATTRIBUTE_GENERAL

/* Data Constants */
#define CDDEMM_CONST                   COMPILER_ATTRIBUTE_GENERAL

/* Memory class for global variables */
#define CDDEMM_VAR                     COMPILER_ATTRIBUTE_GENERAL

/* ---------------------------------------------------------------------------*/
/*                   FuSa                                                     */
/* ---------------------------------------------------------------------------*/
/* API functions */
#define FUSA_CODE_SLOW                 COMPILER_ATTRIBUTE_GENERAL
/* API functions */
#define FUSA_CODE_FAST                 COMPILER_ATTRIBUTE_GENERAL

#include "MemMap_User_Cfg.h"

#endif  /* COMPILER_CFG_H */

/**********************************************************************************************************************
 *  END OF FILE: Compiler_Cfg.h
 *********************************************************************************************************************/
