/*======================================================================================================================
 *  COPYRIGHT NOTICE
 *
 *  Copyright (C) 2023-2025 Horizon Robotics, Inc.
 *
 *  All rights reserved.
 *
 *  This program contains proprietary information belonging to Horizon Robotics. Passing on and copying of this 
 *  document, use and communication of its contents is not permitted without prior written authorization.
========================================================================================================================
 *  Project              : J6
 *  Platform             : CORTEXR
 *  Peripheral           : ModuleName
 *  Dependencies         : MCU
 *
 *  SW Version           :
 *  Build Version        : 
 *  Author               :
 *  Vendor               : Horizon Robotics
 *
 *  Autosar Version      : 4.4.0
 *  Autosar Revision     : ASR_REL_4_4_REV_0000
 *  Autosar Conf.Variant :
======================================================================================================================*/
#ifndef DEVICE_REGISTERS_H
#define DEVICE_REGISTERS_H

/**
* @page misra_violations MISRA-C:2012 violations
*
* @section [global]
* Violates MISRA 2012 Advisory Rule 2.5, global macro not referenced.
* The macro defines the device currently in use and may be used by components for specific checks.
*
*/


/*
 * Include the cpu specific register header files.
 *
 * The CPU macro should be declared in the project or makefile.
 */

#define J6_SERIES
#include "j6_core_cr52.h"
/* Register definitions */
#include "J6e.h"
/* CPU specific feature definitions */
#include "J6e_features.h"


/* Register redefines, for module testing */
#ifdef MODULETEST
#include "mock_device_registers.h"
#endif

#endif /* DEVICE_REGISTERS_H */

/*******************************************************************************
 * EOF
 ******************************************************************************/
