/*============================================================================*/
/* Project      = AUTOSAR Renesas CPF MCAL Components                         */
/* Module       = arm_cr_mpu.h                                                */
/* SW-VERSION   = 1.0.0                                                       */
/*============================================================================*/
/*                                  COPYRIGHT                                 */
/*============================================================================*/
/* Copyright(c) 2021 Renesas Electronics Corporation.                         */
/*============================================================================*/
/* Purpose:                                                                   */
/* Macros for ARMv8 Cortex-R MPU                                              */
/*                                                                            */
/*============================================================================*/
/*                                                                            */
/* Unless otherwise agreed upon in writing between your company and           */
/* Renesas Electronics Corporation the following shall apply!                 */
/*                                                                            */
/* Warranty Disclaimer                                                        */
/*                                                                            */
/* There is no warranty of any kind whatsoever granted by Renesas. Any        */
/* warranty is expressly disclaimed and excluded by Renesas, either expressed */
/* or implied, including but not limited to those for non-infringement of     */
/* intellectual property, merchantability and/or fitness for the particular   */
/* purpose.                                                                   */
/*                                                                            */
/* Renesas shall not have any obligation to maintain, service or provide bug  */
/* fixes for the supplied Product(s) and/or the Application.                  */
/*                                                                            */
/* Each User is solely responsible for determining the appropriateness of     */
/* using the Product(s) and assumes all risks associated with its exercise    */
/* of rights under this Agreement, including, but not limited to the risks    */
/* and costs of program errors, compliance with applicable laws, damage to    */
/* or loss of data, programs or equipment, and unavailability or              */
/* interruption of operations.                                                */
/*                                                                            */
/* Limitation of Liability                                                    */
/*                                                                            */
/* In no event shall Renesas be liable to the User for any incidental,        */
/* consequential, indirect, or punitive damage (including but not limited     */
/* to lost profits) regardless of whether such liability is based on breach   */
/* of contract, tort, strict liability, breach of warranties, failure of      */
/* essential purpose or otherwise and even if advised of the possibility of   */
/* such damages. Renesas shall not be liable for any services or products     */
/* provided by third party vendors, developers or consultants identified or   */
/* referred to the User by Renesas in connection with the Product(s) and/or   */
/* the Application.                                                           */
/*                                                                            */
/*============================================================================*/
/* Environment:                                                               */
/*              Devices:        CPF                                           */
/*============================================================================*/

/*******************************************************************************
**                      Revision Control History                              **
*******************************************************************************/
/*
 * 1.0.0:     20/08/2021 :    Initial Version
 */
/******************************************************************************/

#ifndef ARM_CR_MPU_V8_H
#define ARM_CR_MPU_V8_H

/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/

/*******************************************************************************
**                      Version Information                                   **
*******************************************************************************/

/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/

/*******************************************************************************
**                      Global Data Types                                     **
*******************************************************************************/

/*******************************************************************************
**                      Function Prototypes                                   **
*******************************************************************************/

/*******************************************************************************
**                      Macro                                                 **
*******************************************************************************/

/* Enable EL1 MPU */
__STATIC_INLINE void EL1_MPU_Enable(void)
{
  __set_SCTLR( __get_SCTLR() | SCTLR_M_Msk);
  __ISB();
}

/* Disable EL1 MPU */
__STATIC_INLINE void EL1_MPU_Disable(void)
{
  __set_SCTLR( __get_SCTLR() & (~SCTLR_M_Msk));
  __ISB();
}

#define MPU_Enable    EL1_MPU_Enable
#define MPU_Disable   EL1_MPU_Disable

#if (defined(__ARM_ARCH_8R__ ) || defined(__ARM8))
/* MPU region enable control */
#define     MPU_REG_EN          1U

/* XN: Execution control */
#define     MPU_XN_ALWAYS      0U
#define     MPU_XN_NEVER       1U

/* AP: Acess permission EL1-EL0 or EL2-EL1 */
#define     MPU_AP_RW_NONE     0U
#define     MPU_AP_RW_RW       1U
#define     MPU_AP_RO_NONE     2U
#define     MPU_AP_RO_RO       3U

/* SH: Sharability */
#define     MPU_SH_NON_SHARE   0U
#define     MPU_SH_OUT_SHARE   2U
#define     MPU_SH_IN_SHARE    3U

/* XN + AP + SH */
#define     MPU_XN_AP_SH(XN,AP,SH) \
              ((XN<<PRBAR_XN_Pos)|(AP<<PRBAR_AP_Pos)|(SH<<PRBAR_SH_Pos))

/* Region Attribute */
/* Device memory type non Gathering, non Re-ordering,
   non Early Write Acknowledgement */
#define     MPU_ATTR_DEVICE_nGnRnE  0U
/* Device memory type non Gathering, non Re-ordering,
   Early Write Acknowledgement */
#define     MPU_ATTR_DEVICE_nGnRE   4U
/* Device memory type non Gathering, Re-ordering,
   Early Write Acknowledgement */
#define     MPU_ATTR_DEVICE_nGRE    8U
/* Device memory type Gathering, Re-ordering,
   Early Write Acknowledgement */
#define     MPU_ATTR_DEVICE_GRE     12U

/* Outer - Inner */
#define     MPU_ATTR_MEMORY(O,I)    ((O<<4U)|I)
/* Non Transient - Write Back - Read Allocation - Write Allocation */
#define     MPU_ATTR(NT,WB,RA,WA)   ((NT<<3U)|(WB<<2U)|(RA<<1U)|WA)
/* Non-cacheable*/
#define     MPU_ATTR_NON_CACHEABLE  4U

/* Enable EL1 MPU background region */
__STATIC_INLINE void EL1_MPU_BR_Enable(void)
{
  __set_SCTLR( __get_SCTLR() | SCTLR_BR_Msk);
  __ISB();
}

/* Disable EL1 MPU background region */
__STATIC_INLINE void EL1_MPU_BR_Disable(void)
{
  __set_SCTLR( __get_SCTLR() & (~SCTLR_BR_Msk));
  __ISB();
}

/* Enable EL2 MPU */
__STATIC_INLINE void EL2_MPU_Enable(void)
{
  __set_HSCTLR( __get_HSCTLR() | SCTLR_M_Msk);
  __ISB();
}

/* Disable EL2 MPU */
__STATIC_INLINE void EL2_MPU_Disable(void)
{
  __set_HSCTLR( __get_HSCTLR() & (~SCTLR_M_Msk));
  __ISB();
}

/* Enable EL2 MPU background region */
__STATIC_INLINE void EL2_MPU_BR_Enable(void)
{
  __set_HSCTLR( __get_HSCTLR() | SCTLR_BR_Msk);
  __ISB();
}

/* Disable EL2 MPU background region */
__STATIC_INLINE void EL2_MPU_BR_Disable(void)
{
  __set_HSCTLR( __get_HSCTLR() & (~SCTLR_BR_Msk));
  __ISB();
}

/* Set region attribute */
__STATIC_INLINE void __EL_MPU_CalculateAttrs(uint32 *v0, uint32 *v1, MPU_Attr *attrs)
{
  uint8 i = 0;

  while(i < 4)
  {
    *v0 |= attrs[i] << MAIR_ATTR_Pos(i);
    *v1 |= attrs[i+4] << MAIR_ATTR_Pos(i);
    i++;
  }
}

/* Set region attribute */
__STATIC_INLINE void __EL1_MPU_SetAttrs(void)
{
  uint32 v0 = 0, v1 = 0;

  __EL_MPU_CalculateAttrs(&v0, &v1, mpu_attr_tbl);
  __set_MAIR0(v0);
  __set_MAIR1(v1);

  __ISB();
}

/* Set region attribute */
__STATIC_INLINE void __EL2_MPU_SetAttrs(void)
{
  uint32 v0 = 0, v1 = 0;

  __EL_MPU_CalculateAttrs(&v0, &v1, mpu_attr_tbl2);
  __set_HMAIR0(v0);
  __set_HMAIR1(v1);

  __ISB();
}

/* Setup EL1 MPU region */
__STATIC_INLINE void EL1_MPU_SetRegion(MPU_Region prop)
{
  uint32 val = 0;
  /* Select region */
  __set_PRSELR(prop.region_id);
  __ISB();
  /* Set XN, AP, SH, BASE */
  val = ((prop.region_start_addr & PRBAR_BASE_Msk) | prop.region_attribute);
  __set_PRBAR(val);
  __ISB();
  /* Set LIMIT, attribute index, enable */
  val = ((prop.region_end_addr & PRLAR_LIMIT_Msk) | \
          (prop.region_attr_idx << PRLAR_ATTR_IDX_Pos) | 1U);
  __set_PRLAR(val);
  __ISB();
}

/* Setup EL2 MPU region */
__STATIC_INLINE void EL2_MPU_SetRegion(MPU_Region prop)
{
  uint32 val = 0;
  /* Select region */
  __set_HPRSELR(prop.region_id);
  __ISB();
  /* Set XN, AP, SH, BASE */
  val = ((prop.region_start_addr & PRBAR_BASE_Msk) | prop.region_attribute);
  __set_HPRBAR(val);
  __ISB();
  /* Set LIMIT, attribute index, enable */
  val = ((prop.region_end_addr & PRLAR_LIMIT_Msk) | \
          (prop.region_attr_idx << PRLAR_ATTR_IDX_Pos) | 1U);
  __set_HPRLAR(val);
  __ISB();
}

/* Disable stub region */
__STATIC_INLINE void EL1_MPU_DisableStubRegion()
{
  /* Select region */
  __set_PRSELR(23);
  __ISB();
  /* Disable */
  __set_PRLAR(0);
  __ISB();
}

/* Disable stub region */
__STATIC_INLINE void EL2_MPU_DisableStubRegion()
{
  /* Select region */
  __set_HPRSELR(23);
  __ISB();
  /* Disable */
  __set_HPRLAR(0);
  __ISB();
}

/* Setup full EL1 MPU */
__STATIC_INLINE void EL1_MPU_Setup()
{
  uint8 i = 0;
  __DSB();
  __ISB();
  __EL1_MPU_SetAttrs();
  for (i = 0; i < mpu_setting_tbl.num_region; i++)
  {
    EL1_MPU_SetRegion(mpu_setting_tbl.table[i]);
  }
  EL1_MPU_DisableStubRegion();
}

/* Setup full EL2 MPU */
__STATIC_INLINE void EL2_MPU_Setup()
{
  uint8 i = 0;
  __DSB();
  __ISB();
  __EL2_MPU_SetAttrs();
  for (i = 0; i < mpu_setting_tbl2.num_region; i++)
  {
    EL2_MPU_SetRegion(mpu_setting_tbl2.table[i]);
  }
  EL2_MPU_DisableStubRegion();
}

/* Setup full MPU */
#define MPU_Setup     EL1_MPU_Setup

#else
/* MPU Size */
#define     MPU_SIZE_4G         (0x1F << 1)
#define     MPU_SIZE_2G         (0x1E << 1)
#define     MPU_SIZE_1G         (0x1D << 1)
#define     MPU_SIZE_512M       (0x1C << 1)
#define     MPU_SIZE_32M        (0x18 << 1)
#define     MPU_SIZE_64M        (0x19 << 1)
#define     MPU_SIZE_2M         (0x14 << 1)
#define     MPU_SIZE_16M        (0x17 << 1)
#define     MPU_SIZE_1M         (0x13 << 1)
#define     MPU_SIZE_512K       (0x12 << 1)
#define     MPU_SIZE_256K       (0x11 << 1)
#define     MPU_SIZE_128K       (0x10 << 1)
#define     MPU_SIZE_64K        (0x0F << 1)
#define     MPU_SIZE_32K        (0x0E << 1)

/* MPU region enable control */
#define     MPU_REG_EN          1
#define     MPU_REG_DIS         0

/* MPU_ACCESS_CNTRL */
/* XN: Execution control */
#define     MPU_XN_ALWAYS       0
#define     MPU_XN_NEVER        (0x1 << 12)

/* AP: Privileged/User Read/Write control */
#define     MPU_NO_ACCESS       (0x0 << 8)
#define     MPU_RP_WP           (0x1 << 8)
#define     MPU_RPU_WP          (0x2 << 8)
#define     MPU_RPU_WPU         (0x3 << 8)
#define     MPU_RP              (0x5 << 8)
#define     MPU_RPU             (0x6 << 8)

/* S: Sharability */
#define     MPU_NON_SHAREABLE   0
#define     MPU_SHAREABLE       (0x1 << 2)

/* C: Cacheability */
#define     MPU_C_0             0
#define     MPU_C_1             (0x1 << 1)

/* B: Bufferability */
#define     MPU_B_0             0
#define     MPU_B_1             1

/* TEX: Type Extension Attribute */
#define     MPU_TEX_000         0
#define     MPU_TEX_001         (0x1 << 3)
#define     MPU_TEX_010         (0x2 << 3)
#define     MPU_TEX_011         (0x3 << 3)
#define     MPU_TEX_100         (0x4 << 3)
#define     MPU_TEX_101         (0x5 << 3)
#define     MPU_TEX_110         (0x6 << 3)

/* Region Access Control (Table 8-2 TEX[2:0], C, and B encodings) */
#define     MPU_STRONGLY_ORDERED \
                                (MPU_TEX_000 | MPU_C_0 | MPU_B_0)
#define     MPU_SHAREABLE_DEVICE \
                                (MPU_TEX_000 | MPU_C_0 | MPU_B_1)
#define     MPU_NORMAL_OUT_IN_NON_CACHEABLE \
                                (MPU_TEX_001 | MPU_C_0 | MPU_B_0)
#define     MPU_NORMAL_OUT_IN_WRITE_BACK_ALLOCATE \
                                (MPU_TEX_001 | MPU_C_1 | MPU_B_1)
#define     MPU_NON_SHAREABLE_DEVICE \
                                (MPU_TEX_010 | MPU_C_0 | MPU_B_0)

/* Setup MPU region */
__STATIC_INLINE void MPU_SetRegion(uint32 num, uint32 addr, uint32 size, uint32 attr)
{
  __set_RGNR(num);
  __set_DRBAR(addr);
  __set_DRACR(attr);
  __set_DRSR(size|MPU_REG_EN);
  __ISB();
}

/* Disable stub region */
__STATIC_INLINE void MPU_DisableStubRegion()
{
  __set_RGNR(15);
  __set_DRSR(0);
  __ISB();
}

/* Setup MPU */
__STATIC_INLINE void MPU_Setup(void)
{
  uint8 i = 0;
  __DSB();
  __ISB();
  for (i = 0; i < mpu_setting_tbl.num_region; i++)
  {
    MPU_SetRegion(mpu_setting_tbl.table[i].region_id, \
                  mpu_setting_tbl.table[i].region_start_addr, \
                  mpu_setting_tbl.table[i].region_size, \
                  mpu_setting_tbl.table[i].region_attribute);
  }
  MPU_DisableStubRegion();
}
#endif /* (defined(__ARM_ARCH_8R__ ) || defined(__ARM8)) */

#endif /* ARM_CR_MPU_V8_H */
/*******************************************************************************
**                      End of File                                           **
*******************************************************************************/
