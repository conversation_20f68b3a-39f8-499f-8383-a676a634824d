/*======================================================================================================================
 *  COPYRIGHT NOTICE
 *
 *  Copyright (C) 2023-2025 Horizon Robotics, Inc.
 *
 *  All rights reserved.
 *
 *  This program contains proprietary information belonging to Horizon Robotics. Passing on and copying of this 
 *  document, use and communication of its contents is not permitted without prior written authorization.
========================================================================================================================
 *  Project              : J6
 *  Platform             : CORTEXR
 *  Peripheral           : ModuleName
 *  Dependencies         : MCU
 *
 *  SW Version           :
 *  Build Version        : 
 *  Author               :
 *  Vendor               : Horizon Robotics
 *
 *  Autosar Version      : 4.4.0
 *  Autosar Revision     : ASR_REL_4_4_REV_0000
 *  Autosar Conf.Variant :
======================================================================================================================*/

/*!
 * @file j6_core_cr52.h
 *
 * @page misra_violations MISRA-C:2012 violations
 *
 * @section [global]
 * Violates MISRA 2012 Advisory Directive 4.9, Function-like macro
 * Function-like macros are used instead of inline functions in order to ensure
 * that the performance will not be decreased if the functions will not be
 * inlined by the compiler.
 *
 * @section [global]
 * Violates MISRA 2012 Advisory Rule 2.5, Global macro not referenced.
 * The macros defined are used only on some of the drivers, so this might be reported
 * when the analysis is made only on one driver.
 */

/*
 * Tool Chains:
 *   GNUC flag is defined also by ARM compiler - it shows the current major version of the compatible GCC version 
 *   __GNUC__   : GNU Compiler Collection 
 *   __ghs__    : Green Hills ARM Compiler
 *   __ICCARM__ : IAR ARM Compiler
 *   __ARMCC_VERSION    : ARM Compiler
 */

#if !defined (CORE_CR52_H)
#define CORE_CR52_H

#include <stdint.h>

#if defined(__ghs__)
    #include "arm_ghs.h"
#endif

#ifdef __cplusplus
extern "C" {
#endif

/** \brief  BKPT_ASM
 *
 *   Macro to be used to trigger an debug interrupt
 */
#define BKPT_ASM __asm volatile("hlt #0x0\n\t")
        

/** \brief  Enable FPU
 *
 *   ENABLE_FPU indicates whether SystemInit will enable the Floating point unit (FPU)
 */
#if defined (__GNUC__) || defined (__ARMCC_VERSION)
#if defined (__VFP_FP__) && !defined (__SOFTFP__)
#define ENABLE_FPU
#endif

#elif defined (__ICCARM__)
#if defined __ARMVFP__
#define ENABLE_FPU
#endif

#elif defined (__ghs__)
#if defined (__VFP__)
#define ENABLE_FPU
#endif
#endif /* if defined (__GNUC__) */

/** \brief  Enable interrupts
 */
#if defined (__GNUC__)
#define ENABLE_INTERRUPTS() __asm volatile ("cpsie i" : : : "memory");
#else
#define ENABLE_INTERRUPTS() __asm("cpsie i")
#endif


/** \brief  Disable interrupts
 */
#if defined (__GNUC__)
#define DISABLE_INTERRUPTS() __asm volatile ("cpsid i" : : : "memory");
#else
#define DISABLE_INTERRUPTS() __asm("cpsid i")
#endif


/** \brief  Enter low-power standby state
 *    WFI (Wait For Interrupt) makes the processor suspend execution (Clock is stopped) until an IRQ interrupts.
 */
#if defined (__GNUC__)
#define STANDBY() __asm volatile ("wfi")
#else
#define STANDBY() __asm("wfi")
#endif

/** \brief  Data Memory Barrier
 *    Data Memory Barrier acts as a memory barrier. It ensures that all explicit memory accesses that appear
 * in program order before the DMB instruction are observed before any explicit memory accesses that appear
 * in program order after the DMB instruction. It does not affect the ordering of any other instructions
 * executing on the processor.
 */
#if defined (__GNUC__)
#define DMB() __asm volatile ("dmb")
#else
#define DMB() __asm("dmb")
#endif


/** \brief  Reverse byte order in a word.
 */
#if defined (__GNUC__) || defined (__ICCARM__) || defined (__ghs__) || defined (__ARMCC_VERSION)
#define REV_BYTES_32(a, b) __asm volatile ("rev %0, %1" : "=r" (b) : "r" (a))
#else
#define REV_BYTES_32(a, b) (b = ((a & 0xFF000000U) >> 24U) | ((a & 0xFF0000U) >> 8U) \
                                | ((a & 0xFF00U) << 8U) | ((a & 0xFFU) << 24U))
#endif

/** \brief  Reverse byte order in each halfword independently.
 */
#if defined (__GNUC__) || defined (__ICCARM__) || defined (__ghs__) || defined (__ARMCC_VERSION)
#define REV_BYTES_16(a, b) __asm volatile ("rev16 %0, %1" : "=r" (b) : "r" (a))
#else
#define REV_BYTES_16(a, b) (b = ((a & 0xFF000000U) >> 8U) | ((a & 0xFF0000U) << 8U) \
                                | ((a & 0xFF00U) >> 8U) | ((a & 0xFFU) << 8U))
#endif

/** \brief  Places a function in RAM.
 */
#if defined ( __GNUC__ )
    #define START_FUNCTION_DECLARATION_RAMSECTION
    #define END_FUNCTION_DECLARATION_RAMSECTION        __attribute__((section (".code_ram")));
#elif defined ( __ghs__ )
    #define START_FUNCTION_DECLARATION_RAMSECTION      _Pragma("ghs callmode=far")
    #define END_FUNCTION_DECLARATION_RAMSECTION        __attribute__((section (".code_ram")));\
                                                       _Pragma("ghs callmode=default")
#elif defined ( __ICCARM__ )
    #define START_FUNCTION_DECLARATION_RAMSECTION      __ramfunc
    #define END_FUNCTION_DECLARATION_RAMSECTION        ;
#else
    /* Keep compatibility with software analysis tools */
    #define START_FUNCTION_DECLARATION_RAMSECTION      
    #define END_FUNCTION_DECLARATION_RAMSECTION        ;
#endif
                                                   
    /* For GCC, IAR, GHS, Diab and ARMC there is no need to specify the section when
    defining a function, it is enough to specify it at the declaration. This
    also enables compatibility with software analysis tools. */
    #define START_FUNCTION_DEFINITION_RAMSECTION
    #define END_FUNCTION_DEFINITION_RAMSECTION

#if defined (__ICCARM__)
    #define DISABLE_CHECK_RAMSECTION_FUNCTION_CALL     _Pragma("diag_suppress=Ta022")
    #define ENABLE_CHECK_RAMSECTION_FUNCTION_CALL      _Pragma("diag_default=Ta022")
#else
    #define DISABLE_CHECK_RAMSECTION_FUNCTION_CALL
    #define ENABLE_CHECK_RAMSECTION_FUNCTION_CALL
#endif

/** \brief GetCoreID.
*/
#define ReadMPIDR(a) __asm volatile("mrc p15, 0, %0, c0, c0, 5" : "=r" (a): : "cc")

/** \brief  Get Core ID
 *
 *   GET_CORE_ID returns the processor identification number for cr52
 */
#if defined ( __GNUC__ ) || defined ( __DCC__ )
    #define GET_CORE_ID()    (__extension__\
                            ({uint32_t tmp; \
                                ReadMPIDR(tmp); \
                                tmp & 0x01UL; \
                            }))
    #define GET_CLUSTER_ID() (__extension__\
                            ({uint32_t tmp; \
                                ReadMPIDR(tmp); \
                                (tmp >> 8UL) & 0x1UL; \
                                }))
#elif defined ( __ghs__ )
    #define GET_CORE_ID()    ((__MRC(15, 0, 0, 0, 5)) & 0x01UL) 
    #define GET_CLUSTER_ID() (((__MRC(15, 0, 0, 0, 5)) >> 8UL) & 0x01UL) 
#else
    #define GET_CORE_ID() 0UL
    #define GET_CLUSTER_ID() 0UL
#endif
/** \brief  Data alignment.
 */
#if defined ( __GNUC__ ) || defined ( __ghs__ ) || defined (__ARMCC_VERSION)
    #define ALIGNED(x)      __attribute__((aligned(x)))
#elif defined ( __ICCARM__ )
    #define stringify(s) tostring(s)
    #define tostring(s) #s
    #define ALIGNED(x)      _Pragma(stringify(data_alignment=x))
#else
    /* Keep compatibility with software analysis tools */
    #define ALIGNED(x)
#endif

/** \brief  Exception Handler Stack Save  
 */
#if defined ( __GNUC__ ) || defined ( __ghs__ ) || defined (__ICCARM__)
    /* Save stack context */
    #define EXCEPTION_STACK_SAVE __asm("tst    lr, #4\n\t"\
                                 "ite    eq\n\t"\
                                 "mrseq  r0, msp\n\t"\
                                 "mrsne  r0, psp\n\t"\
                                 "mov    r1, lr");  
#else
    #define EXCEPTION_STACK_SAVE
#endif
 
/** \brief  Endianness.
 */
#define CORE_LITTLE_ENDIAN


/** /brief Cache management instructions
 */
#if defined (__GNUC__) || defined (__ICCARM__) || defined (__ghs__) || defined (__ARMCC_VERSION) || defined (__DCC__)
    #define READ_SCR(a)   __asm volatile ("mrc p15, 0, %0, c1, c0, 0" : "=r" (a))
    #define WRITE_SCR(a)  __asm volatile ("mcr p15, 0, %0, c1, c0, 0" : : "r" (a))
    #define DCIVAC(a)     __asm volatile ("mcr p15, 0, %0, c7, c6, 1" : : "r" (a))
    #define DCISW(a)      __asm volatile ("mcr p15, 0, %0, c7, c6, 2" : : "r" (a))
    #define DCCIMVAC(a)   __asm volatile ("mcr p15, 0, %0, c7, c14, 1" : : "r" (a))
    #define DCCISW(a)     __asm volatile ("mcr p15, 0, %0, c7, c14, 2" : : "r" (a))
    #define DCCMVAU(a)    __asm volatile ("mcr p15, 0, %0, c7, c11, 1" : : "r" (a))
    #define DCCSW(a)      __asm volatile ("mcr p15, 0, %0, c7, c10, 2" : : "r" (a))
    #define ICIALLU(a)    __asm volatile ("mcr p15, 0, %0, c7, c5, 0" : : "r" (a))
#else
    #define READ_SCR(a)   (void)a
    #define WRITE_SCR(a)  (void)a
    #define DCIVAC(a)     (void)a
    #define DCISW(a)      (void)a
    #define DCCIMVAC(a)   (void)a
    #define DCCISW(a)     (void)a
    #define DCCMVAU(a)    (void)a
    #define DCCSW(a)      (void)a
    #define ICIALLU(a)    (void)a
#endif

#ifdef __cplusplus
}
#endif

#endif /* CORE_CR52_H */

/*******************************************************************************
 * EOF
 ******************************************************************************/
