/*============================================================================*/
/* Project      = AUTOSAR Renesas CPF MCAL Components                         */
/* Module       = device_cfg.h                                                */
/* SW-VERSION   = 1.0.3                                                       */
/*============================================================================*/
/*                                  COPYRIGHT                                 */
/*============================================================================*/
/* Copyright(c) 2021-2022 Renesas Electronics Corporation.                    */
/*============================================================================*/
/* Purpose:                                                                   */
/* This file contains configuration for initialization setting of V4H         */
/*                                                                            */
/*============================================================================*/
/*                                                                            */
/* Unless otherwise agreed upon in writing between your company and           */
/* Renesas Electronics Corporation the following shall apply!                 */
/*                                                                            */
/* Warranty Disclaimer                                                        */
/*                                                                            */
/* There is no warranty of any kind whatsoever granted by Renesas. Any        */
/* warranty is expressly disclaimed and excluded by Renesas, either expressed */
/* or implied, including but not limited to those for non-infringement of     */
/* intellectual property, merchantability and/or fitness for the particular   */
/* purpose.                                                                   */
/*                                                                            */
/* Renesas shall not have any obligation to maintain, service or provide bug  */
/* fixes for the supplied Product(s) and/or the Application.                  */
/*                                                                            */
/* Each User is solely responsible for determining the appropriateness of     */
/* using the Product(s) and assumes all risks associated with its exercise    */
/* of rights under this Agreement, including, but not limited to the risks    */
/* and costs of program errors, compliance with applicable laws, damage to    */
/* or loss of data, programs or equipment, and unavailability or              */
/* interruption of operations.                                                */
/*                                                                            */
/* Limitation of Liability                                                    */
/*                                                                            */
/* In no event shall Renesas be liable to the User for any incidental,        */
/* consequential, indirect, or punitive damage (including but not limited     */
/* to lost profits) regardless of whether such liability is based on breach   */
/* of contract, tort, strict liability, breach of warranties, failure of      */
/* essential purpose or otherwise and even if advised of the possibility of   */
/* such damages. Renesas shall not be liable for any services or products     */
/* provided by third party vendors, developers or consultants identified or   */
/* referred to the User by Renesas in connection with the Product(s) and/or   */
/* the Application.                                                           */
/*                                                                            */
/*============================================================================*/
/* Environment:                                                               */
/*              Devices:        CPF                                           */
/*============================================================================*/
/*******************************************************************************
**                      Revision Control History                              **
*******************************************************************************/
/*
 * 1.0.3:     05-05-2022 :       Disable CR52 multi-cores supporting
 * 1.0.2:     25-03-2022 :       Support for HSCIF
 * 1.0.1:     24-12-2021 :       Support multi cores CR52
 * 1.0.0:     06-11-2021 :       Initial Version
 */

#ifndef DEVICE_CFG_H_
#define DEVICE_CFG_H_

#include "Platform_Types.h"
#include "Gic_V3.h"

/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/
/* Support Multi Core CR52 */
#define CR52_MULTICORE_SUPPORT

#ifdef CR52_MULTICORE_SUPPORT
/* Which core to be used */
#define CR52_CPU1_USED
//#define CR52_CPU2_USED

/* Core ID */
#define CR52_CPU0                         0
#define CR52_CPU1                         1
#define CR52_CPU2                         2

#endif /* #ifdef CR52_MULTICORE_SUPPORT */

/* MFIS/ECM and CPG */
#define MFIS_ECM_WACNTR    (*((volatile uint32*)0xE6250A04UL))
#define MFIS_ECM_WPCNTR    (*((volatile uint32*)0xE6250A00UL))
#define CPG_WPCR           (*((volatile uint32*)0xE6150004UL))
#define CPG_WPR            (*((volatile uint32*)0xE6150000UL))

/* CPG protection */
#define CPG_PROTECTION                      0
/* MFIS/ECM protection */
#define MFIS_ECM_PROTECTION                 0
#define MFIS_ECM_PROTECTION_REGISTER        1

/* SCIF base */
#define SCIF_BASE_ADDR                      0xE6E60000 /* SCIF0 */
#define SCIF_CLK_VALUE                      0x0000

/* HSCIF base */
#define HSCIF_BASE_ADDR                     0xE6540000 /* HSCIF0 */
#define HSCIF_CLK_VALUE                     0x0000

/* Pclk(66MHz)/1, 115.2kbps*/
/* N = 66/(66/2*115200)*10^4-1 =17=> 0x11 */
#define SCIF_SCBRR_115200BPS                (uint8)(0x11U)
/* Pclk(266MHz)/1, 921.6kbps*/
/* N = 266/(8*2*921600)*10^6-1 =17=> 0x11 */
#define HSCIF_SCBRR_921600BPS               (uint8)(0x11U)
/* Pclk(266MHz)/1, 1.8432Mbps*/
/* N = 266/(8*2*1843200)*10^6-1 =8=> 0x08 */
#define HSCIF_SCBRR_1843200BPS              (uint8)(0x08U)

/* Macro to select HWIP for MCAL console print function */
#define RCAR_SCIF                           0xA5A5A5A5
#define RCAR_HSCIF                          0x5A5A5A5A
#define RCAR_SCIF_115200BPS                 0x00000000
#define RCAR_HSCIF_921600BPS                0x00000001
#define RCAR_HSCIF_1843200BPS               0x00000002

/* Select SCIF or HSCIF depended on each device */
#define RCAR_MCAL_LOG_SELECT                RCAR_HSCIF_921600BPS

#if (RCAR_MCAL_LOG_SELECT == RCAR_SCIF_115200BPS)
#define CONSOLE_HWIP                        RCAR_SCIF
#define SCIF_BAUDRATE                       SCIF_SCBRR_115200BPS
#elif (RCAR_MCAL_LOG_SELECT == RCAR_HSCIF_921600BPS)
#define CONSOLE_HWIP                        RCAR_HSCIF
#define HSCIF_BAUDRATE                      HSCIF_SCBRR_921600BPS
#else
#define CONSOLE_HWIP                        RCAR_HSCIF
#define HSCIF_BAUDRATE                      HSCIF_SCBRR_1843200BPS
#endif

/* GIC base (address 0xF1000000UL in HW Manual is incorrect)*/
// #define GICD_BASE                           0xF0000000UL
#define GICR_BASE                           GICD_BASE
#define GICC_BASE

/* WDG */
#define RST_WDTRSTCR       *((volatile uint32 *)(0xE6160010))
#define RST_WDTRSTCR_CODE  ((uint32)(0xA55A << 16))
#define RST_WDTRSTCR_RWDT  ((uint32)(1 << 0))

/* PFC base */
#define PFC_GP0_BASE        (0xE6050000UL)
#define PFC_GP1_BASE        (0xE6050800UL)
#define PFC_GP2_BASE        (0xE6058000UL)
#define PFC_GP3_BASE        (0xE6058800UL)
#define PFC_GP4_BASE        (0xE6060000UL)
#define PFC_GP5_BASE        (0xE6060800UL)
#define PFC_GP6_BASE        (0xE6061000UL)
#define PFC_GP7_BASE        (0xE6061800UL)
#define PFC_GP8_BASE        (0xE6068000UL)

/* Secure */
#define SEC_SRC            *((volatile uint32 *)(0xFFC43018))
#define CR_SECURE          ((uint32)(1 << 0))

/* APMU */
#define APMU_BASE            (0xE6170000UL)
/* Cortex-R Reset Control Register    */
#define APMU_CR52RSTCTRL(r)     (APMU_BASE + 0x0304 + (0x40 * r))
/* Cortex-R Boot Address Register Protected   */
#define APMU_CR52BARP(r)        (APMU_BASE + 0x033C + (0x40 * r))

/* Device Initialization */
void Device_Init();

#endif /* DEVICE_CFG_H_ */
