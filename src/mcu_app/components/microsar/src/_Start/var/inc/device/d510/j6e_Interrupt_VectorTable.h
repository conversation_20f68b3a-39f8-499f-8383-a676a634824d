/**
 * @file Interrupt_VectorTable_J6E.h
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * @version 1.0
 * @date 2024-11-06
 * 
 * 
 */


#ifndef INTERRUPT_VECTORTABLE_H
#define INTERRUPT_VECTORTABLE_H

/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/

/*******************************************************************************
**                      Version Information                                   **
*******************************************************************************/

/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/
typedef void (*FUNCT)(void);                /* define a pointer to a function */

extern const FUNCT IntVectors[];

#define IRQ_Handler void __attribute__((interrupt("IRQ")))

#endif /* INTERRUPT_VECTORTABLE_H */
