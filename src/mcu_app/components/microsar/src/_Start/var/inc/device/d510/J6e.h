/*======================================================================================================================
 *  COPYRIGHT NOTICE
 *
 *  Copyright (C) 2023-2025 Horizon Robotics, Inc.
 *
 *  All rights reserved.
 *
 *  This program contains proprietary information belonging to Horizon Robotics. Passing on and copying of this
 *  document, use and communication of its contents is not permitted without prior written authorization.
========================================================================================================================
 *  Project              : J6
 *  Platform             : CORTEXR
 *  Peripheral           : ModuleName
 *  Dependencies         : MCU
 *
 *  SW Version           :
 *  Build Version        :
 *  Author               :
 *  Vendor               : Horizon Robotics
 *
 *  Autosar Version      : 4.4.0
 *  Autosar Revision     : ASR_REL_4_4_REV_0000
 *  Autosar Conf.Variant :
======================================================================================================================*/


/*!
 * @file J6e.h
 * @version 1.0
 * @date 2019-04-17
 * @brief Peripheral Access Layer for J6e
 *
 * This file contains register definitions and macros for easy access to their
 * bit fields.
 *
 * This file assumes LITTLE endian system.
 */

/**
* @page misra_violations MISRA-C:2012 violations
*
* @section [global]
* Violates MISRA 2012 Advisory Rule 2.3, local typedef not referenced
* The SoC header defines typedef for all modules.
*
* @section [global]
* Violates MISRA 2012 Advisory Rule 2.5, local macro not referenced
* The SoC header defines macros for all modules and registers.
*
* @section [global]
* Violates MISRA 2012 Advisory Directive 4.9, Function-like macro
* These are generated macros used for accessing the bit-fields from registers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.1, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.2, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.4, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.5, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 21.1, defined macro '__I' is reserved to the compiler
* This type qualifier is needed to ensure correct I/O access and addressing.
*/


/* ----------------------------------------------------------------------------
   -- MCU activation
   ---------------------------------------------------------------------------- */

/* Prevention from multiple including the same memory map */
#if !defined(J6E_H_)  /* Check if memory map has not been already included */
#define J6E_H_
#define MCU_J6E

/* Check if another memory map has not been also included */
#if (defined(MCU_ACTIVE))
  #error J6E memory map: There is already included another memory map. Only one memory map can be included.
#endif /* (defined(MCU_ACTIVE)) */
#define MCU_ACTIVE

#include <stdint.h>

/** Memory map major version (memory maps with equal major version number are
 * compatible) */
#define MCU_MEM_MAP_VERSION 0x0100U
/** Memory map minor version */
#define MCU_MEM_MAP_VERSION_MINOR 0x0000U

/* ----------------------------------------------------------------------------
   -- Generic macros
   ---------------------------------------------------------------------------- */

/* IO definitions (access restrictions to peripheral registers) */
/**
*   IO Type Qualifiers are used
*   \li to specify the access to peripheral variables.
*   \li for automatic generation of peripheral register debug information.
*/
#ifndef __IO
#ifdef __cplusplus
  #define   __I     volatile             /*!< Defines 'read only' permissions                 */
#else
  #define   __I     volatile const       /*!< Defines 'read only' permissions                 */
#endif
#define     __O     volatile             /*!< Defines 'write only' permissions                */
#define     __IO    volatile             /*!< Defines 'read / write' permissions              */
#endif

#ifndef CBAR
#define CBAR              0x22000000u
#endif
#include <Gic_V3.h>
#include <J6e_Interrupts.h>

/**
* @brief 32 bits memory read macro.
*/
#if !defined(REG_READ32)
  #define REG_READ32(address)               (*(volatile uint32_t*)(address))
#endif

/**
* @brief 32 bits memory write macro.
*/
#if !defined(REG_WRITE32)
  #define REG_WRITE32(address, value)       ((*(volatile uint32_t*)(address))= (uint32_t)(value))
#endif

/**
* @brief 32 bits bits setting macro.
*/
#if !defined(REG_BIT_SET32)
  #define REG_BIT_SET32(address, mask)      ((*(volatile uint32_t*)(address))|= (uint32_t)(mask))
#endif

/**
* @brief 32 bits bits clearing macro.
*/
#if !defined(REG_BIT_CLEAR32)
  #define REG_BIT_CLEAR32(address, mask)    ((*(volatile uint32_t*)(address))&= ((uint32_t)~((uint32_t)(mask))))
#endif

/**
* @brief 32 bit clear bits and set with new value
* @note It is user's responsability to make sure that value has only "mask" bits set - (value&~mask)==0
*/
#if !defined(REG_RMW32)
  #define REG_RMW32(address, mask, value)   (REG_WRITE32((address), ((REG_READ32(address)& ((uint32_t)~((uint32_t)(mask))))| ((uint32_t)(value)))))
#endif


/* ----------------------------------------------------------------------------
   -- Device Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup Peripheral_access_layer Device Peripheral Access Layer
 * @{
 */
/* ----------------------------------------------------------------------------
   -- syscnt Access
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup SYSCNT_Peripheral_Access_Layer SYSCNT Peripheral Access Layer
 * @{
 */

/** SYSCNT - Register Layout Typedef */
typedef struct {
  __IO uint32_t SYSCNT_CTRL;                             /**< SYSCNT Control Register, offset: 0x0 */
  __IO uint32_t SYSCNT_STAT;                             /**< Oscillator Status Register, offset: 0x4 */
  __IO uint32_t SYSCNT_CNTCVL;                             /**< Oscillator Status Register, offset: 0x4 */
  __IO uint32_t SYSCNT_CNTCVU;                             /**< Oscillator Status Register, offset: 0x4 */
  __IO uint32_t SYSCNT_CNTFID0;
} SYSCNT_Type, *SYSCNT_MemMapPtr;

/* SYSCNT - Peripheral instance base addresses */
/** Peripheral SYSCNT base address */
#define SYSCNT_BASE                               (0x23110000u)
/** Peripheral SYSCNT base pointer */
#define SYSCNT                                    ((SYSCNT_Type *)SYSCNT_BASE)
/** Array initializer of SYSCNT peripheral base addresses */
#define SYSCNT_BASE_ADDRS                         { SYSCNT_BASE }
/** Array initializer of SYSCNT peripheral base pointers */
#define SYSCNT_BASE_PTRS                          { SYSCNT }

/* ----------------------------------------------------------------------------
   -- SYSCNT Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup SYSCNT_Register_Masks SYSCNT Register Masks
 * @{
 */

/*! @name SYSCNT_CTRL - SYSCNT Control Register */
/*! @{ */
#define SYSCNT_SYSCNT_CTRL_OSCON_MASK              (0x1U)
#define SYSCNT_SYSCNT_CTRL_OSCON_SHIFT             (0U)
#define SYSCNT_SYSCNT_CTRL_OSCON_WIDTH             (1U)
#define SYSCNT_SYSCNT_CTRL_OSCON(x)                (((uint32_t)(((uint32_t)(x)) << SYSCNT_SYSCNT_CTRL_OSCON_SHIFT)) & SYSCNT_SYSCNT_CTRL_OSCON_MASK)
#define SYSCNT_SYSCNT_CTRL_HALTONDEBUG_MASK              (0x2U)
#define SYSCNT_SYSCNT_CTRL_HALTONDEBUG_SHIFT             (1U)
#define SYSCNT_SYSCNT_CTRL_HALTONDEBUG_WIDTH             (1U)
#define SYSCNT_SYSCNT_CTRL_HALTONDEBUG(x)                (((uint32_t)(((uint32_t)(x)) << SYSCNT_SYSCNT_CTRL_OSCON_SHIFT)) & SYSCNT_SYSCNT_CTRL_OSCON_MASK)

/*! @} */

/*! @name SYSCNT_STAT - Oscillator Status Register */
/*! @{ */

/*! @} */
/*! @name SYSCNT_FID0 - Oscillator Status Register */
/*! @{ */
#define SYSCNT_SYSCNT_FID0_FREQUENCY              (6250000)//6.25M
/*! @} */

/*!
 * @}
 */ /* end of group SYSCNT_Register_Masks */


/*!
 * @}
 */ /* end of group SYSCNT_Peripheral_Access_Layer */
#endif  /* #if !defined(J6E_H_) */
