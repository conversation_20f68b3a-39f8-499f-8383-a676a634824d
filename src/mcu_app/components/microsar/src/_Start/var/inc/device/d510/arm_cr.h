/*============================================================================*/
/* Project      = AUTOSAR Renesas CPF MCAL Components                         */
/* Module       = arm_cr.h                                                    */
/* SW-VERSION   = 1.0.3                                                       */
/*============================================================================*/
/*                                  COPYRIGHT                                 */
/*============================================================================*/
/* Copyright(c) 2021-2022 Renesas Electronics Corporation.                    */
/*============================================================================*/
/* Purpose:                                                                   */
/* Macros for ARM Cortex R                                                    */
/*                                                                            */
/*============================================================================*/
/*                                                                            */
/* Unless otherwise agreed upon in writing between your company and           */
/* Renesas Electronics Corporation the following shall apply!                 */
/*                                                                            */
/* Warranty Disclaimer                                                        */
/*                                                                            */
/* There is no warranty of any kind whatsoever granted by Renesas. Any        */
/* warranty is expressly disclaimed and excluded by Renesas, either expressed */
/* or implied, including but not limited to those for non-infringement of     */
/* intellectual property, merchantability and/or fitness for the particular   */
/* purpose.                                                                   */
/*                                                                            */
/* Renesas shall not have any obligation to maintain, service or provide bug  */
/* fixes for the supplied Product(s) and/or the Application.                  */
/*                                                                            */
/* Each User is solely responsible for determining the appropriateness of     */
/* using the Product(s) and assumes all risks associated with its exercise    */
/* of rights under this Agreement, including, but not limited to the risks    */
/* and costs of program errors, compliance with applicable laws, damage to    */
/* or loss of data, programs or equipment, and unavailability or              */
/* interruption of operations.                                                */
/*                                                                            */
/* Limitation of Liability                                                    */
/*                                                                            */
/* In no event shall Renesas be liable to the User for any incidental,        */
/* consequential, indirect, or punitive damage (including but not limited     */
/* to lost profits) regardless of whether such liability is based on breach   */
/* of contract, tort, strict liability, breach of warranties, failure of      */
/* essential purpose or otherwise and even if advised of the possibility of   */
/* such damages. Renesas shall not be liable for any services or products     */
/* provided by third party vendors, developers or consultants identified or   */
/* referred to the User by Renesas in connection with the Product(s) and/or   */
/* the Application.                                                           */
/*                                                                            */
/*============================================================================*/
/* Environment:                                                               */
/*              Devices:        CPF                                           */
/*============================================================================*/

/*******************************************************************************
**                      Revision Control History                              **
*******************************************************************************/
/*
 * 1.0.3:     22/09/2022 :    Remove inline asmembler wfi, wfe, dsb, isb and
 *                            move to Compiler.h support SML structure solution
 * 1.0.2:     13/04/2022 :    Add inline asmembler wfi, wfe, dsb, isb
 * 1.0.1:     24/12/2021 :    Support multi cores CR52
 * 1.0.0:     18/10/2021 :    Initial Version
 */
/******************************************************************************/

#ifndef ARM_CR_H
#define ARM_CR_H

/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/
#include "Compiler.h"
#include "Platform_Types.h"

/*******************************************************************************
**                      Version Information                                   **
*******************************************************************************/

/*
 * File version information
 */
#define ARM_CR_SW_MAJOR_VERSION  1
#define ARM_CR_SW_MINOR_VERSION  0
#define ARM_CR_SW_PATCH_VERSION  0

/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/

/*******************************************************************************
**                      Global Data Types                                     **
*******************************************************************************/

/*******************************************************************************
**                      Function Prototypes                                   **
*******************************************************************************/

/*******************************************************************************
**                      Macro                                                 **
*******************************************************************************/
#ifndef __ARM_ARCH_8R__
	#define __ARM_ARCH_8R__
#endif
#ifndef   __ASM
  #define __ASM                     __asm
#endif
#ifndef   __INLINE
  #define __INLINE                  inline
#endif
#ifndef   __STATIC_INLINE
  #define __STATIC_INLINE           static inline
#endif

/* No Operation */
#define __NOP()                             __ASM volatile ("nop")

/* Wait For Interrupt */
#define __WFI()                             __ASM volatile ("wfi":::"memory")

/* Wait For Event */
#define __WFE()                             __ASM volatile ("wfe":::"memory")

/* Send Event */
#define __SEV()                             __ASM volatile ("sev")

/* Instruction Synchronization Barrier */
#define __ISB()                             __ASM volatile ("isb":::"memory")

/* Data Synchronization Barrier */
#define __DSB()                             __ASM volatile ("dsb":::"memory")

/* Data Memory Barrier */
#define __DMB()                             __ASM volatile ("dmb":::"memory")

/* Inline assembler support for WFI instruction */
#define ASM_WFI() __asm("wfi")


/* Inline assembler support for WFE instruction */
#define ASM_WFE() __asm("wfe")


/* Inline assembler support for DSB instruction */
#define ASM_DSB() __asm("dsb")


/* Inline assembler support for ISB instruction */
#define ASM_ISB() __asm("isb")

/* Get CPSR Register */
__STATIC_INLINE uint32 __get_CPSR(void)
{
  uint32 ret = 0;
  __ASM volatile("mrs %0, cpsr" : "=r" (ret) );
  return ret;
}

/* Set CPSR Register */
__STATIC_INLINE void __set_CPSR(uint32 cpsr)
{
  __ASM volatile ("msr cpsr, %0" : : "r" (cpsr) : "cc", "memory");
}

/* Enable IRQ Interrupts */
__STATIC_INLINE void __enable_irq()
{
  __ASM volatile ("cpsie i" : : : "memory");
  // __set_CPSR(__get_CPSR() & (~(1 << 7)));
}

/* Disable IRQ Interrupts */
__STATIC_INLINE  void __disable_irq()
{
  __ASM volatile ("cpsid i" : : : "memory");
  // __set_CPSR(__get_CPSR() | (1 << 7));
}

/* Get Processor Mode */
__STATIC_INLINE uint32 __get_mode(void)
{
  return (__get_CPSR() & 0x1FU);
}

/* Set Processor Mode */
#define __set_mode(mode) \
  __ASM volatile("msr  cpsr_c, %0" : : "r" (mode) : "memory")

/* Get Stack Pointer */
__STATIC_INLINE uint32 __get_SP(void)
{
  uint32 ret = 0;
  __ASM volatile("mov  %0, sp" : "=r" (ret) : : "memory");
  return ret;
}

/* Set Stack Pointer */
#define __set_SP(stack) \
  __ASM volatile("mov  sp, %0" : : "r" (stack) : "memory")

#if (defined(__ARM_ARCH_8R__ ) || defined(__ARM8))
/* Set LR Hypervisor */
#define __set_elr_hyp(addr) \
  __ASM volatile("msr  elr_hyp, %0" : : "r" (addr) : "memory")
/* EL2 -> EL1 */
#define __ERET()                             __ASM volatile ("eret")
#endif /* (defined(__ARM_ARCH_8R__ ) || defined(__ARM8)) */

/* Set SPSR Register */
#define __set_SPSR(t, spsr) \
  __ASM volatile ("msr spsr" # t ", %0" : : "r" (spsr) : "cc", "memory")

/*
 * Include common core functions to access Coprocessor 15 registers
 */

#define __get_CP(cp, op1, Rt, CRn, CRm, op2) \
  __ASM volatile("mrc p" # cp ", " # op1 ", %0, c" # CRn ", c" # CRm ", " # op2 : "=r" (Rt) : : "memory" )
#define __set_CP(cp, op1, Rt, CRn, CRm, op2) \
  __ASM volatile("mcr p" # cp ", " # op1 ", %0, c" # CRn ", c" # CRm ", " # op2 : : "r" (Rt) : "memory" )

/* Mask and shift a bit field value for use in a register bit range. */
#define _VAL2FLD(field, value)    (((uint32)(value) << field ## _Pos) & field ## _Msk)

/* Mask and shift a register value to extract a bit filed value. */
#define _FLD2VAL(field, value)    (((uint32)(value) & field ## _Msk) >> field ## _Pos)

/* following defines should be used for structure members */
#define     __RO     volatile const      /* Defines 'read only' structure member permissions */
#define     __WO     volatile            /* Defines 'write only' structure member permissions */
#define     __RW     volatile            /* Defines 'read / write' structure member permissions */
#define RESERVED(N, T) T RESERVED##N;    /* placeholder struct members used for "reserved" areas */

/* Check Interrupt mode status (CPSR.MODE) */
#define GET_INTMODE  __get_IntMode

__STATIC_INLINE int __get_IntMode(void)
{
  int LRet;

  /* Check CPSR.MODE */
  if(0x00000012 == __get_mode())
  {
    LRet = 1;
  }
  else
  {
    LRet = 0;
  }

  return LRet;
}

#include "arm_cr_cp15.h"
#include "arm_cr_reg.h"

/* Get CR CPU Core ID */
__STATIC_INLINE uint32 __get_CRCoreID(void)
{
  //return (__get_MPIDR() & 0x0000000FU);
  return ((__get_MPIDR() & 0x0000FF00U) >> 8); // Cluster ID for V4H-R52
}
/*******************************************************************************
**                      Cache                                                 **
*******************************************************************************/

/* Enable L1 I-Cache */
__STATIC_INLINE void L1C_EnableICache(void)
{
  __set_SCTLR( __get_SCTLR() | SCTLR_I_Msk);
  __ISB();
}

/* Disable L1 I-Cache */
__STATIC_INLINE void L1C_DisableICache(void)
{
  __set_SCTLR( __get_SCTLR() & (~SCTLR_I_Msk));
  __ISB();
}

/* Enable L1 D-Cache */
__STATIC_INLINE void L1C_EnableDCache(void)
{
  __set_SCTLR( __get_SCTLR() | SCTLR_C_Msk);
  __ISB();
}

/* Disable L1 D-Cache */
__STATIC_INLINE void L1C_DisableDCache(void)
{
  __set_SCTLR( __get_SCTLR() & (~SCTLR_C_Msk));
  __ISB();
}

/* Enable Branch Prediction */
__STATIC_INLINE void L1C_EnableBP(void) {
  __set_SCTLR( __get_SCTLR() | SCTLR_Z_Msk);
  __ISB();
}

/* Disable Branch Prediction */
__STATIC_INLINE void L1C_DisableBP(void) {
  __set_SCTLR( __get_SCTLR() & (~SCTLR_Z_Msk));
  __ISB();
}

/* Invalidate entire branch predictor array */
__STATIC_INLINE void L1C_InvalidateBP(void) {
  __set_BPIALL(0);
  __DSB();     //ensure completion of the invalidation
  __ISB();     //ensure instruction fetch path sees new state
}

/* Invalidate the whole instruction cache */
__STATIC_INLINE void L1C_InvalidateICacheAll(void) {
  __set_ICIALLU(0);
  __DSB();     //ensure completion of the invalidation
  __ISB();     //ensure instruction fetch path sees new I cache state
}

/* Calculate logarit 2 and round up */
__STATIC_INLINE uint32 __log_2_n_roundup(uint32 val)
{
  uint32 ret = 0, additional = 0;

  if (val & (val - 1))
  {
    additional = 1;
  }
  while (val > 1)
  {
    ret += 1;
    val = val >> 1;
  }
  return (ret + additional);
}

/* Maintain data cache by set/way */
__STATIC_INLINE void __L1C_MaintainDCacheSetWay(uint32 level, uint32 maint)
{
  uint32 Dummy;
  uint32 ccsidr;
  uint32 num_sets;
  uint32 num_ways;
  uint32 shift_way;
  uint32 log2_linesize;
  uint32 log2_num_ways;
  uint32 way, set;

  Dummy = level << 1U;
  /* set csselr, select ccsidr register */
  __set_CSSELR(Dummy);
  /* get current ccsidr register */
  ccsidr = __get_CCSIDR();
  num_sets = ((ccsidr & 0x0FFFE000U) >> 13U) + 1U;
  num_ways = ((ccsidr & 0x00001FF8U) >> 3U) + 1U;
  /* Calculate L (based on line length) */
  /* From B6.1.7 CCSIDR, Cache Size ID Registers, PMSA and
          B6.2.1 Cache and branch predictor maintenance operations, PMSA
      - L = log2(LINELEN) with LINELEN is bytes
      - LINELEN = line_size x 4 with line_size is got from CCSIDR
      - line_size is calculated as below :
        + CSSIDR_LineSize = (log2(line_size))-2
            with CSSIDR_LineSize : value from CCSIDR
          => line_size = 2^(CSSIDR_LineSize + 2)
      => LINELEN = (2^(CSSIDR_LineSize + 2)) x 4 = 16 x 2^(CSSIDR_LineSize)
      => L = log2(16 x 2^(CSSIDR_LineSize)) = 4 + CSSIDR_LineSize
  */
  log2_linesize = (ccsidr & 0x00000007U) + 2U + 2U;
  /* Calculate A (based on num_ways) */
  log2_num_ways = __log_2_n_roundup(num_ways);
  shift_way = 32U - (uint32)log2_num_ways;
  for(way = 0; way < num_ways; way++)
  {
    for(set = 0; set < num_sets; set++)
    {
      Dummy = (level << 1U) | (((uint32)set) << log2_linesize) | (((uint32)way) << shift_way);
      switch (maint)
      {
        case 0U: __set_DCISW(Dummy);  break;
        case 1U: __set_DCCSW(Dummy);  break;
        default: __set_DCCISW(Dummy); break;
      }
    }
  }
  __DMB();
}

/* Clean and Invalidate the entire data or unified cache */
__STATIC_INLINE void L1C_MaintainDCacheSetWay(uint32 op) {
  uint32 clidr;
  uint32 cache_type;
  uint32 i = 0U;
  clidr =  __get_CLIDR();
  for(i = 0U; i < 7U; i++)
  {
    cache_type = (clidr >> i*3U) & 0x7UL;
    if ((cache_type >= 2U) && (cache_type <= 4U))
    {
      __L1C_MaintainDCacheSetWay(i, op);
    }
  }
}

/* Maintain data cache by address */
__STATIC_INLINE void __L1C_MaintainDCacheAddress(uint32 level, uint32 maint, \
                                                      uint32 addr, uint32 size)
{
  uint32 Dummy, ccsidr;
  uint32 line_size = 0, pow = 1, num_lines = 0;

  Dummy = level << 1U;
  /* set csselr, select ccsidr register */
  __set_CSSELR(Dummy);
  /* get current ccsidr register */
  ccsidr = __get_CCSIDR();
  line_size = (ccsidr & 0x00000007U);
  /* Convert to number of bytes */
  while (line_size--)
  {
    pow *= 2;
  }
  line_size = 16 * pow;
  /* Calculate num lines which need to be maintained */
  if (size % line_size)
  {
    num_lines = (size / line_size) + 1;
  }
  else
  {
    num_lines = (size / line_size);
  }

  while (num_lines--)
  {
    switch (maint)
    {
      case 0U: __set_DCIMVAC(addr);  break;
      case 1U: __set_DCCMVAC(addr);  break;
      default: __set_DCCIMVAC(addr); break;
    }
    addr += line_size;
  }

  __DMB();
}

/* Clean data cache line by address */
__STATIC_INLINE void L1C_CleanDCacheAddress(uint32 addr, uint32 size) {
  __L1C_MaintainDCacheAddress(1, 1, addr, size);
  __DMB();     //ensure the ordering of data cache maintenance operations and their effects
}

/* Invalidate data cache line by address */
__STATIC_INLINE void L1C_InvalidateDCacheAddress(uint32 addr, uint32 size) {
  __L1C_MaintainDCacheAddress(1, 0, addr, size);
  __DMB();     //ensure the ordering of data cache maintenance operations and their effects
}

/* Clean and Invalidate data cache by address */
__STATIC_INLINE void L1C_CleanInvalidateDCacheAddress(uint32 addr, uint32 size) {
  __L1C_MaintainDCacheAddress(1, 2, addr, size);
  __DMB();     //ensure the ordering of data cache maintenance operations and their effects
}

/* Invalidate the whole data cache */
__STATIC_INLINE void L1C_InvalidateDCacheAll(void) {
  L1C_MaintainDCacheSetWay(0);
}

/* Clean the whole data cache */
__STATIC_INLINE void L1C_CleanDCacheAll(void) {
  L1C_MaintainDCacheSetWay(1);
}

/* Clean and invalidate the whole data cache */
__STATIC_INLINE void L1C_CleanInvalidateDCacheAll(void) {
  L1C_MaintainDCacheSetWay(2);
}

/*******************************************************************************
**                      MPU                                                   **
*******************************************************************************/

#if (defined(__ARM_ARCH_8R__ ) || defined(__ARM8))
typedef uint32        MPU_Attr;
extern  MPU_Attr      mpu_attr_tbl[]; /* maximum 8 elements */
#define mpu_attr_tbl2 mpu_attr_tbl    /* same MPU settings for EL1 and EL2 */
#endif /* (defined(__ARM_ARCH_8R__ ) || defined(__ARM8)) */

/* MPU setting structure */
typedef struct
{
  uint32 region_id;         /* Region ID            */
  uint32 region_start_addr; /* Region start address */
#if (defined(__ARM_ARCH_8R__ ) || defined(__ARM8))
  uint32 region_end_addr;   /* Region end address   */
  uint32 region_attr_idx;   /* Region attribute index */
#else
  uint32 region_size;       /* Region size          */
#endif /* (defined(__ARM_ARCH_8R__ ) || defined(__ARM8)) */
  uint32 region_attribute;  /* Region attribute     */
} MPU_Region;

/* MPU setting table */
typedef struct
{
  MPU_Region  *table;       /* MPU table for RCar   */
  uint32      num_region;   /* Number of region     */
} MPU_Setting;

extern MPU_Setting mpu_setting_tbl;
#if (defined(__ARM_ARCH_8R__ ) || defined(__ARM8))
#define mpu_setting_tbl2 mpu_setting_tbl/* same MPU settings for EL1 and EL2 */
#endif /* (defined(__ARM_ARCH_8R__ ) || defined(__ARM8)) */

#include "arm_cr_mpu.h"

/*******************************************************************************
**                      VFP                                                   **
*******************************************************************************/

/* Enable VFP */
__STATIC_INLINE void VFP_Enable(void)
{
  __set_CPACR( __get_CPACR() | CPACR_CP_Msk);
  __ASM volatile ("vmsr fpexc, %0" : : "r" (0x40000000) : "memory");
  __ISB();
}

/* Enable VFP */
__STATIC_INLINE void VFP_Disable(void)
{
  __ASM volatile ("vmsr fpexc, %0" : : "r" (0x00000000) : "memory");
  __set_CPACR( __get_CPACR() & (~CPACR_CP_Msk));
  __ISB();
}

/*******************************************************************************
**                      GIC                                                   **
*******************************************************************************/

#include "device_cfg.h"
#include "arm_gic.h"

#endif /* ARM_CR_H */
/*******************************************************************************
**                      End of File                                           **
*******************************************************************************/
