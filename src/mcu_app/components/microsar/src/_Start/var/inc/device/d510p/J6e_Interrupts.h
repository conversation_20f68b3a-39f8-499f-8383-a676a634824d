/*======================================================================================================================
 *  COPYRIGHT NOTICE
 *
 *  Copyright (C) 2023-2025 Horizon Robotics, Inc.
 *
 *  All rights reserved.
 *
 *  This program contains proprietary information belonging to Horizon Robotics. Passing on and copying of this
 *  document, use and communication of its contents is not permitted without prior written authorization.
========================================================================================================================
 *  Project              : J6
 *  Platform             : CORTEXR
 *  Peripheral           : ModuleName
 *  Dependencies         : MCU
 *
 *  SW Version           :
 *  Build Version        :
 *  Author               :
 *  Vendor               : Horizon Robotics
 *
 *  Autosar Version      : 4.4.0
 *  Autosar Revision     : ASR_REL_4_4_REV_0000
 *  Autosar Conf.Variant :
======================================================================================================================*/
/**
 * @file Interrupt_J6e.h
 *
 * @NO{S01E18C01}
 *
 * @ASIL{D}
 */
#ifndef INTERRUPT_J6E_H_
#define INTERRUPT_J6E_H_

/*======================================================================================================================
 *                                                   INCLUDE FILES
======================================================================================================================*/
#include <Std_Types.h>

/* ----------------------------------------------------------------------------
   -- Interrupt vector numbers
   ---------------------------------------------------------------------------- */

/** Interrupt Number Definitions */
#define NUMBER_OF_INT_VECTORS 571u /**< Number of interrupts in the Vector table */
#define AE_IRQ_OFFSET ((uint32_t)2048u)
#define AE_IRQ_MULTIPLIER ((uint32_t)256u)
#define MCU_SPI_OFFSET ((uint32_t)46u)
#define MCU_IRQ2SPI(x) ((uint32_t)(x + MCU_SPI_OFFSET))

/**
 * @brief Defines the Interrupt Numbers definitions
 *
 * This enumeration is used to configure the interrupts.
 *
 * Implements : IRQn_Type_Class
 */
typedef enum
{
    /* Auxiliary constants */
    NotAvail_IRQn = -128, /**< Not available device specific interrupt */

    /* Software Generated Interrupts (SGI) */
    SGI0_IRQn = 0u,   /**< Software generated Interrupt 0 */
    SGI1_IRQn = 1u,   /**< Software generated Interrupt 1 */
    SGI2_IRQn = 2u,   /**< Software generated Interrupt 2 */
    SGI3_IRQn = 3u,   /**< Software generated Interrupt 3 */
    SGI4_IRQn = 4u,   /**< Software generated Interrupt 4 */
    SGI5_IRQn = 5u,   /**< Software generated Interrupt 5 */
    SGI6_IRQn = 6u,   /**< Software generated Interrupt 6 */
    SGI7_IRQn = 7u,   /**< Software generated Interrupt 7 */
    SGI8_IRQn = 8u,   /**< Software generated Interrupt 8 */
    SGI9_IRQn = 9u,   /**< Software generated Interrupt 9 */
    SGI10_IRQn = 10u, /**< Software generated Interrupt 10 */
    SGI11_IRQn = 11u, /**< Software generated Interrupt 11 */
    SGI12_IRQn = 12u, /**< Software generated Interrupt 12 */
    SGI13_IRQn = 13u, /**< Software generated Interrupt 13 */
    SGI14_IRQn = 14u, /**< Software generated Interrupt 14 */
    SGI15_IRQn = 15u, /**< Software generated Interrupt 15 */

    /* Private Peripheral Interrupts (PPI) */
    CLUSTER_AXI_IRQn = 16u, /**< Per-core interrupt: R52: Error indicator for AXI transactions with a write response
                               error condition - Edge-triggered interrupt */
    COMM_IRQn = 22u,        /**< Per-core interrupt: Communications channel receive or transmit interrupt request  */
    PMU_IRQn = 23u,         /**< Per-core interrupt: PMU interrupt request  */
    CTI_IRQn = 24u,         /**< Per-core interrupt: Cross trigger interface (CTI) interrupt */
    MAINTENANCE_IRQn = 25u, /**< Per-core interrupt: Virtual CPU Interface Maintenance interrupt */
    HYPERV_TIM_IRQn = 26u,  /**< Per-core interrupt: Hypervisor timer interrupt */
    VIRTUAL_TIM_IRQn = 27u, /**< Per-core interrupt: Virtual timer interrupt */
    EL1_TIM_IRQn = 30u,     /**< Per-core interrupt: Non-secure EL1 physical timer */

    /* Shared Peripheral Interrupts (SPI) - Directly to MCUSYS GICD */
    STCU_MCU_INTR = 32u,
    STCU_MEDIATOP_INTR = 33u,
    STCU_VIDEO_INTR = 34u,
    STCU_VDSP_INTR = 35u,
    STCU_HSIS_INTR = 36u,
    STCU_GPU_INTR = 37u,
    STCU_DDR2_INTR = 38u,
    STCU_DDR1_INTR = 39u,
    STCU_DDR0_INTR = 40u,
    STCU_CPUMP4_INTR = 41u,
    STCU_CPUMP2_INTR = 42u,
    STCU_CAM_INTR = 43u,
    STCU_BPU0_INTR = 44u,

    HSM_IPC1_INTR0 = 241u,
    HSM_IPC1_INTR1 = 242u,
    HSM_IPC1_INTR2 = 243u,
    HSM_IPC1_INTR3 = 244u,

    AON_RTC_INTR = 360u,
    AON_WAKEUP_GPIO_INTR = 361u,
    AON_GPIO_INTR = 362u,
    AON_PMU_REQ_MODE = 363u,

    /* Shared Peripheral Interrupts (SPI) - MCUSYS to MCU */
    MCUSYS_UART0_INTR = 45u,
    MCUSYS_UART1_INTR = 46u,
    MCUSYS_UART2_INTR = 47u,
    MCUSYS_ADC0_CTRL_WD_INTR_0 = 48u,
    MCUSYS_ADC0_CTRL_WD_INTR_1 = 49u,
    MCUSYS_ADC0_CTRL_WD_INTR_2 = 50u,
    MCUSYS_ADC0_CTRL_WD_INTR_3 = 51u,
    MCUSYS_ADC0_CTRL_WD_INTR_4 = 52u,
    MCUSYS_ADC0_CTRL_WD_INTR_5 = 53u,
    MCUSYS_ADC0_CTRL_WD_INTR_6 = 54u,
    MCUSYS_ADC0_CTRL_WD_INTR_7 = 55u,
    MCUSYS_ADC0_CTRL_WD_INTR_8 = 56u,
    MCUSYS_ADC0_CTRL_WD_INTR_9 = 57u,
    MCUSYS_ADC0_CTRL_WD_INTR_10 = 58u,
    MCUSYS_ADC0_CTRL_WD_INTR_11 = 59u,
    MCUSYS_ADC0_CTRL_WD_INTR_12 = 60u,
    MCUSYS_ADC0_CTRL_WD_INTR_13 = 61u,
    MCUSYS_ADC0_CTRL_INJ_INTR = 62u,
    MCUSYS_ADC0_CTRL_INTR = 63u,
    MCUSYS_I2C0_INTR = 64u,
    MCUSYS_I2C1_INTR = 65u,
    MCUSYS_I2C2_INTR = 66u,
    MCUSYS_I2C3_INTR = 67u,
    MCUSYS_GPIO0_INTR = 68u,
    MCUSYS_GPIO1_INTR = 69u,
    MCUSYS_GPIO2_INTR = 70u,
    MCUSYS_WWDT0_SYS_RST_INTR = 71u,
    MCUSYS_WWDT0_INTR = 72u,
    MCUSYS_WWDT1_SYS_RST_INTR = 73u,
    MCUSYS_WWDT1_INTR = 74u,
    MCUSYS_WWDT2_SYS_RST_INTR = 75u,
    MCUSYS_WWDT2_INTR = 76u,
    MCUSYS_OTFCRC0_FUNC_INTR = 77u,
    MCUSYS_CRC0_FUNC_INTR = 78u,
    MCUSYS_TIMER0_INTR_0 = 79u,
    MCUSYS_TIMER0_INTR_1 = 80u,
    MCUSYS_TIMER0_INTR_2 = 81u,
    MCUSYS_TIMER0_INTR_3 = 82u,
    MCUSYS_TIMER1_INTR_0 = 83u,
    MCUSYS_TIMER1_INTR_1 = 84u,
    MCUSYS_TIMER1_INTR_2 = 85u,
    MCUSYS_TIMER1_INTR_3 = 86u,
    MCUSYS_TIMER2_INTR_0 = 87u,
    MCUSYS_TIMER2_INTR_1 = 88u,
    MCUSYS_TIMER2_INTR_2 = 89u,
    MCUSYS_TIMER2_INTR_3 = 90u,
    MCUSYS_TIMER3_INTR_0 = 91u,
    MCUSYS_TIMER3_INTR_1 = 92u,
    MCUSYS_TIMER3_INTR_2 = 93u,
    MCUSYS_TIMER3_INTR_3 = 94u,
    MCUSYS_TIMER4_INTR_0 = 95u,
    MCUSYS_TIMER4_INTR_1 = 96u,
    MCUSYS_TIMER4_INTR_2 = 97u,
    MCUSYS_TIMER4_INTR_3 = 98u,
    MCUSYS_TIMER5_INTR_0 = 99u,
    MCUSYS_TIMER5_INTR_1 = 100u,
    MCUSYS_TIMER5_INTR_2 = 101u,
    MCUSYS_TIMER5_INTR_3 = 102u,
    MCUSYS_PMU_INTR = 103u,
    MCUSYS_BIFSPI_INTR = 104u,
    MCUSYS_PVT_INTR = 105u,
    MCUSYS_L1FCHM_MISSION_INTR = 106u,
    MCUSYS_L1FCHM_SX_CE_CORE_INTR = 107u,
    MCUSYS_L1FCHM_CORE_INTR = 108u,
    MCUSYS_CMM0_INTR = 109u,
    MCUSYS_CMM1_INTR = 110u,
    MCUSYS_PWM0_DOUT_INTR = 111u,
    MCUSYS_XSPI_INTR = 112u,
    MCUSYS_CANFD0_INT_TIMESTAMP_INTR = 113u,
    MCUSYS_CANFD0_IPI_WAKE_INTR = 114u,
    MCUSYS_CANFD0_IPI_ERR_INTR = 115u,
    MCUSYS_CANFD0_IPI_MB_INTR = 116u,
    MCUSYS_CANFD1_INT_TIMESTAMP_INTR = 117u,
    MCUSYS_CANFD1_IPI_WAKE_INTR = 118u,
    MCUSYS_CANFD1_IPI_ERR_INTR = 119u,
    MCUSYS_CANFD1_IPI_MB_INTR = 120u,
    MCUSYS_CANFD2_INT_TIMESTAMP_INTR = 121u,
    MCUSYS_CANFD2_IPI_WAKE_INTR = 122u,
    MCUSYS_CANFD2_IPI_ERR_INTR = 123u,
    MCUSYS_CANFD2_IPI_MB_INTR = 124u,
    MCUSYS_CANFD3_INT_TIMESTAMP_INTR = 125u,
    MCUSYS_CANFD3_IPI_WAKE_INTR = 126u,
    MCUSYS_CANFD3_IPI_ERR_INTR = 127u,
    MCUSYS_CANFD3_IPI_MB_INTR = 128u,
    MCUSYS_CANFD4_INT_TIMESTAMP_INTR = 129u,
    MCUSYS_CANFD4_IPI_WAKE_INTR = 130u,
    MCUSYS_CANFD4_IPI_ERR_INTR = 131u,
    MCUSYS_CANFD4_IPI_MB_INTR = 132u,
    MCUSYS_CANFD5_INT_TIMESTAMP_INTR = 133u,
    MCUSYS_CANFD5_IPI_WAKE_INTR = 134u,
    MCUSYS_CANFD5_IPI_ERR_INTR = 135u,
    MCUSYS_CANFD5_IPI_MB_INTR = 136u,
    MCUSYS_CANFD6_INT_TIMESTAMP_INTR = 137u,
    MCUSYS_CANFD6_IPI_WAKE_INTR = 138u,
    MCUSYS_CANFD6_IPI_ERR_INTR = 139u,
    MCUSYS_CANFD6_IPI_MB_INTR = 140u,
    MCUSYS_CANFD7_INT_TIMESTAMP_INTR = 141u,
    MCUSYS_CANFD7_IPI_WAKE_INTR = 142u,
    MCUSYS_CANFD7_IPI_ERR_INTR = 143u,
    MCUSYS_CANFD7_IPI_MB_INTR = 144u,
    MCUSYS_CANFD8_INT_TIMESTAMP_INTR = 145u,
    MCUSYS_CANFD8_IPI_WAKE_INTR = 146u,
    MCUSYS_CANFD8_IPI_ERR_INTR = 147u,
    MCUSYS_CANFD8_IPI_MB_INTR = 148u,
    MCUSYS_CANFD9_INT_TIMESTAMP_INTR = 149u,
    MCUSYS_CANFD9_IPI_WAKE_INTR = 150u,
    MCUSYS_CANFD9_IPI_ERR_INTR = 151u,
    MCUSYS_CANFD9_IPI_MB_INTR = 152u,
    MCUSYS_ETH_SBD_PERCH_TX_INTR_0 = 153u,
    MCUSYS_ETH_SBD_PERCH_TX_INTR_1 = 154u,
    MCUSYS_ETH_SBD_PERCH_TX_INTR_2 = 155u,
    MCUSYS_ETH_SBD_PERCH_TX_INTR_3 = 156u,
    MCUSYS_ETH_SBD_PERCH_TX_INTR_4 = 157u,
    MCUSYS_ETH_SBD_PERCH_TX_INTR_5 = 158u,
    MCUSYS_ETH_SBD_PERCH_RX_INTR_0 = 159u,
    MCUSYS_ETH_SBD_PERCH_RX_INTR_1 = 160u,
    MCUSYS_ETH_SBD_PERCH_RX_INTR_2 = 161u,
    MCUSYS_ETH_SBD_PERCH_RX_INTR_3 = 162u,
    MCUSYS_ETH_SBD_PERCH_RX_INTR_4 = 163u,
    MCUSYS_ETH_SBD_PERCH_RX_INTR_5 = 164u,
    MCUSYS_ETH_SBD_INTR = 165u,
    MCUSYS_ETH_PMT_INTR = 166u,
    MCUSYS_ETH_LPI_INTR = 167u,
    MCUSYS_LIN0_IPI_INT_ALL_INTR = 168u,
    MCUSYS_LIN1_IPI_INT_ALL_INTR = 169u,
    MCUSYS_LIN2_IPI_INT_ALL_INTR = 170u,
    MCUSYS_SPI0_INTR = 171u,
    MCUSYS_SPI1_INTR = 172u,
    MCUSYS_SPI2_INTR = 173u,
    MCUSYS_SPI3_INTR = 174u,
    MCUSYS_SPI4_INTR = 175u,
    MCUSYS_SPI5_INTR = 176u,
    MCUSYS_IPC_INTR_0 = 177u,
    MCUSYS_IPC_INTR_1 = 178u,
    MCUSYS_IPC_INTR_2 = 179u,
    MCUSYS_IPC_INTR_3 = 180u,
    MCUSYS_IPC_INTR_4 = 181u,
    MCUSYS_IPC_INTR_5 = 182u,
    MCUSYS_IPC_INTR_6 = 183u,
    MCUSYS_IPC_INTR_7 = 184u,
    MCUSYS_IPC_INTR_8 = 185u,
    MCUSYS_IPC_INTR_9 = 186u,
    MCUSYS_IPC_INTR_10 = 187u,
    MCUSYS_IPC_INTR_11 = 188u,
    MCUSYS_IPC_INTR_12 = 189u,
    MCUSYS_IPC_INTR_13 = 190u,
    MCUSYS_IPC_INTR_14 = 191u,
    MCUSYS_IPC_INTR_15 = 192u,
    MCUSYS_IPC_INTR_16 = 193u,
    MCUSYS_IPC_INTR_17 = 194u,
    MCUSYS_IPC_INTR_18 = 195u,
    MCUSYS_IPC_INTR_19 = 196u,
    MCUSYS_IPC_INTR_20 = 197u,
    MCUSYS_IPC_INTR_21 = 198u,
    MCUSYS_IPC_INTR_22 = 199u,
    MCUSYS_IPC_INTR_23 = 200u,
    MCUSYS_IPC_INTR_24 = 201u,
    MCUSYS_IPC_INTR_25 = 202u,
    MCUSYS_IPC_INTR_26 = 203u,
    MCUSYS_IPC_INTR_27 = 204u,
    MCUSYS_IPC_INTR_28 = 205u,
    MCUSYS_IPC_INTR_29 = 206u,
    MCUSYS_IPC_INTR_30 = 207u,
    MCUSYS_IPC_INTR_31 = 208u,
    MCUSYS_MDMA0_INTR_0 = 209u,
    MCUSYS_MDMA0_INTR_1 = 210u,
    MCUSYS_MDMA1_INTR_0 = 211u,
    MCUSYS_MDMA1_INTR_1 = 212u,
    MCUSYS_PDMA0_INTR_0 = 213u,
    MCUSYS_PDMA0_INTR_1 = 214u,
    MCUSYS_PDMA0_INTR_2 = 215u,
    MCUSYS_PDMA0_INTR_3 = 216u,
    MCUSYS_PDMA0_INTR_4 = 217u,
    MCUSYS_PDMA0_INTR_5 = 218u,
    MCUSYS_PMC0_INTR = 219u,
    MCUSYS_PMC1_INTR = 220u,
    MCUSYS_AON_RTC_TRIGGER_PPS_INTR = 221u,
    MCUSYS_PPS_SYNC_INTR_0 = 222u,
    MCUSYS_PPS_SYNC_INTR_1 = 223u,
    MCUSYS_PPS_SYNC_INTR_2 = 224u,
    MCUSYS_PCIE0_PPS_INTR = 225u,
    MCUSYS_PCIE1_PPS_INTR = 226u,
    MCUSYS_GMAC0_PPS_INTR = 227u,
    MCUSYS_GMAC1_PPS_INTR = 228u,
    MCUSYS_MCU_EMAC_GPS_PPS_INTR = 229u,
    MCUSYS_HSM_IPC0_INTR_4 = 230u,
    MCUSYS_HSM_IPC0_INTR_5 = 231u,
    MCUSYS_HSM_IPC0_INTR_6 = 232u,
    MCUSYS_HSM_IPC0_INTR_7 = 233u,

    /* Shared Peripheral Interrupts (SPI) - CPUSYS to MCU */
    CPUSYS_CLUSTER_MP4_CMM_INTR = 245u,
    CPUSYS_CLUSTER_MP4_POWERMU_INTR_0 = 246u,
    CPUSYS_CLUSTER_MP4_POWERMU_INTR_1 = 247u,
    CPUSYS_CLUSTER_MP2_CMM_INTR = 248u,
    CPUSYS_CLUSTER_MP2_POWERMU_INTR_0 = 249u,
    CPUSYS_CLUSTER_MP2_POWERMU_INTR_1 = 250u,
    CPUSYS_IPC1_INTR_0 = 251u,
    CPUSYS_IPC1_INTR_1 = 252u,
    CPUSYS_IPC1_INTR_2 = 253u,
    CPUSYS_IPC0_INTR_0 = 254u,
    CPUSYS_IPC0_INTR_1 = 255u,
    CPUSYS_IPC0_INTR_2 = 256u,
    CPUSYS_IPC0_INTR_3 = 257u,
    CPUSYS_IPC0_INTR_4 = 258u,
    CPUSYS_IPC0_INTR_5 = 259u,
    CPUSYS_IPC0_INTR_6 = 260u,
    CPUSYS_IPC0_INTR_7 = 261u,
    CPUSYS_IPC0_INTR_8 = 262u,
    CPUSYS_IPC0_INTR_9 = 263u,
    CPUSYS_IPC0_INTR_10 = 264u,
    CPUSYS_IPC0_INTR_11 = 265u,
    CPUSYS_IPC0_INTR_12 = 266u,
    CPUSYS_IPC0_INTR_13 = 267u,
    CPUSYS_IPC0_INTR_14 = 268u,
    CPUSYS_IPC0_INTR_15 = 269u,
    CPUSYS_SW1_TRIG_INTR_0 = 270u,
    CPUSYS_SW1_TRIG_INTR_1 = 271u,
    CPUSYS_SW1_TRIG_INTR_2 = 272u,
    CPUSYS_SW1_TRIG_INTR_3 = 273u,

    /* Shared Peripheral Interrupts (SPI) - DDRSYS0 to MCU */
    DDRSYS0_CMM_INTR = 291u,

    /* Shared Peripheral Interrupts (SPI) - DDRSYS1 to MCU */
    DDRSYS1_CMM_INTR = 294u,

    /* Shared Peripheral Interrupts (SPI) - DDRSYS2 to MCU */
    DDRSYS2_CMM_INTR = 297u,

    /* Shared Peripheral Interrupts (SPI) - PERISYS to MCU */
    PERISYS_I2C0_INTR = 300u,
    PERISYS_I2C1_INTR = 301u,
    PERISYS_I2C2_INTR = 302u,
    PERISYS_I2C3_INTR = 303u,
    PERISYS_I2C4_INTR = 304u,
    PERISYS_I2C5_INTR = 305u,
    PERISYS_USB_INTR = 306u,
    PERISYS_CMM_INTR = 307u,

    /* Shared Peripheral Interrupts (SPI) - CAMERASYS to MCU */
    CAMERASYS_ISP0_INTR_0 = 308u,
    CAMERASYS_ISP0_INTR_1 = 309u,
    CAMERASYS_ISP0_INTR_2 = 310u,
    CAMERASYS_ISP0_INTR_3 = 311u,
    CAMERASYS_CPE0_PYM_INTR = 312u,
    CAMERASYS_CPE0_MIPI_RX_CSI_INTR = 313u,
    CAMERASYS_CPE0_CIM_INTR = 314u,
    CAMERASYS_CPE0_PYM_PRE_UV_INTR = 315u,
    CAMERASYS_CPE0_PYM_PRE_Y_INTR = 316u,
    CAMERASYS_CPE0_CMM_INTR = 317u,
    CAMERASYS_ISP1_INTR_0 = 318u,
    CAMERASYS_ISP1_INTR_1 = 319u,
    CAMERASYS_ISP1_INTR_2 = 320u,
    CAMERASYS_ISP1_INTR_3 = 321u,
    CAMERASYS_CPE1_YNR_INTR = 322u,
    CAMERASYS_CPE1_PYM_INTR = 323u,
    CAMERASYS_CPE1_MIPI_RX_CSI_INTR = 324u,
    CAMERASYS_CPE1_CIM_INTR = 325u,
    CAMERASYS_CPE1_PYM_PRE_UV_INTR = 326u,
    CAMERASYS_CPE1_PYM_PRE_Y_INTR = 327u,
    CAMERASYS_CPE1_CMM_INTR = 328u,
    CAMERASYS_STITCH_INTR = 329u,
    CAMERASYS_CPE_LITE_MIPI_RX_CSI_INTR = 330u,
    CAMERASYS_GDC0_INTR = 331u,
    CAMERASYS_CPE_LITE_PYM_INTR = 332u,
    CAMERASYS_CPE_LITE_PYM_PRE_UV_INTR = 333u,
    CAMERASYS_CPE_LITE_PYM_PRE_Y_INTR = 334u,
    CAMERASYS_CPE_LITE_CIM_INTR = 335u,
    CAMERASYS_CPE_LITE_CMM_INTR = 336u,
    CAMERASYS_MIPI_TX1_DSI_INTR = 337u,
    CAMERASYS_MIPI_TX1_CSI2_INTR = 338u,
    CAMERASYS_MIPI_TX0_DSI_INTR = 339u,
    CAMERASYS_MIPI_TX0_CSI2_INTR = 340u,
    CAMERASYS_IDU0_INTR = 341u,
    CAMERASYS_IDU1_INTR = 342u,
    CAMERASYS_IDE_CMM_INTR = 343u,
    CAMERASYS_GPIO_INTR = 344u,
    CAMERASYS_TOP_CMM_INTR = 345u,

    /* Shared Peripheral Interrupts (SPI) - VIDEOSYS to MCU */
    VIDEOSYS_VPU_INTR = 346u,
    VIDEOSYS_JPU_INTR = 347u,
    VIDEOSYS_CMM_INTR = 348u,
    VIDEOSYS_GPIO_INTR = 349u,

    /* Shared Peripheral Interrupts (SPI) - VDSPSYS to MCU */
    VDSPSYS_CMM_INTR = 350u,
    VDSPSYS_Q8_EARLY_RESET_REQ_INTR = 351u,
    VDSPSYS_Q8_RESET_REQ_INTR = 352u,

    /* Shared Peripheral Interrupts (SPI) - HSISYS to MCU */
    HSISYS_CMM_INTR = 353u,

    /* Shared Peripheral Interrupts (SPI) - BPUSYS to MCU */
    BPUSYS_BPU30_VM0_INFORM_BETWEEN_HOST_INTR_2 = 354u,
    BPUSYS_BPU30_VM1_INFORM_BETWEEN_HOST_INTR_2 = 355u,
    BPUSYS_BPU30_HYP_INFORM_BETWEEN_HOST_INTR_2 = 356u,
    BPUSYS_BPU30_PVT_TEMPERATURE_SENSOR_REPORT_INTR = 357u,
    BPUSYS_CLOCK_MONITOR_TEST_DONE_INTR = 358u,

    /* Shared Peripheral Interrupts (SPI) - GPUSYS to MCU */
    GPUSYS_CMM_TOP_INTR = 359u,

    /* Shared Peripheral Interrupts (SPI) - MEDIABOTSYS to MCU */
    MEDIABOTSYS_CMM_INTR = 364u,

    /* Shared Peripheral Interrupts (SPI) - MEDIATOPSYS to MCU */
    MEDIATOPSYS_CMM_INTR = 366u,

    /* Shared Peripheral Interrupts (SPI) - CMNSYS to MCU */
    CMNSYS_CMM_DONE_INTR = 368u,
    CMNSYS_PVTC_INTR = 369u,
    CMNSYS_PPU_INTR = 370u,

} IRQn_Type, IrqNum_t;

#endif /* INTERRUPT_J6E_H_ */
