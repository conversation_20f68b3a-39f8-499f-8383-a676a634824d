/*============================================================================*/
/* Project      = AUTOSAR Renesas CPF MCAL Components                         */
/* Module       = arm_gic.h                                                   */
/* SW-VERSION   = 1.0.0                                                       */
/*============================================================================*/
/*                                  COPYRIGHT                                 */
/*============================================================================*/
/* Copyright(c) 2021 Renesas Electronics Corporation.                         */
/*============================================================================*/
/* Purpose:                                                                   */
/* Macros for ARM GIC (v2-v3-v4)                                              */
/*                                                                            */
/*============================================================================*/
/*                                                                            */
/* Unless otherwise agreed upon in writing between your company and           */
/* Renesas Electronics Corporation the following shall apply!                 */
/*                                                                            */
/* Warranty Disclaimer                                                        */
/*                                                                            */
/* There is no warranty of any kind whatsoever granted by Renesas. Any        */
/* warranty is expressly disclaimed and excluded by Renesas, either expressed */
/* or implied, including but not limited to those for non-infringement of     */
/* intellectual property, merchantability and/or fitness for the particular   */
/* purpose.                                                                   */
/*                                                                            */
/* Renesas shall not have any obligation to maintain, service or provide bug  */
/* fixes for the supplied Product(s) and/or the Application.                  */
/*                                                                            */
/* Each User is solely responsible for determining the appropriateness of     */
/* using the Product(s) and assumes all risks associated with its exercise    */
/* of rights under this Agreement, including, but not limited to the risks    */
/* and costs of program errors, compliance with applicable laws, damage to    */
/* or loss of data, programs or equipment, and unavailability or              */
/* interruption of operations.                                                */
/*                                                                            */
/* Limitation of Liability                                                    */
/*                                                                            */
/* In no event shall Renesas be liable to the User for any incidental,        */
/* consequential, indirect, or punitive damage (including but not limited     */
/* to lost profits) regardless of whether such liability is based on breach   */
/* of contract, tort, strict liability, breach of warranties, failure of      */
/* essential purpose or otherwise and even if advised of the possibility of   */
/* such damages. Renesas shall not be liable for any services or products     */
/* provided by third party vendors, developers or consultants identified or   */
/* referred to the User by Renesas in connection with the Product(s) and/or   */
/* the Application.                                                           */
/*                                                                            */
/*============================================================================*/
/* Environment:                                                               */
/*              Devices:        CPF                                           */
/*============================================================================*/

/*******************************************************************************
**                      Revision Control History                              **
*******************************************************************************/
/*
 * 1.0.0:     20/08/2021 :    Initial Version
 */
/******************************************************************************/

#ifndef ARM_GIC_H
#define ARM_GIC_H

/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/

/*******************************************************************************
**                      Version Information                                   **
*******************************************************************************/

/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/

/* Generic Interrupt Controller Distributor (GICD) */
#define GICD_CTLR              *((volatile uint32 *)(GICD_BASE + 0x000))
#define GICD_TYPER             *((volatile const uint32 *)(GICD_BASE + 0x004))
#define GICD_IIDR              *((volatile const uint32 *)(GICD_BASE + 0x008))
#define GICD_STATUSR           *((volatile uint32 *)(GICD_BASE + 0x010))
#define GICD_SETSPI_NSR        *((volatile uint32 *)(GICD_BASE + 0x040))
#define GICD_CLRSPI_NSR        *((volatile uint32 *)(GICD_BASE + 0x048))
#define GICD_SETSPI_SR         *((volatile uint32 *)(GICD_BASE + 0x050))
#define GICD_CLRSPI_SR         *((volatile uint32 *)(GICD_BASE + 0x058))
#define GICD_IGROUPR(n)        *((volatile uint32 *)(GICD_BASE + 0x080 + 4*(n)))
#define GICD_ISENABLER(n)      *((volatile uint32 *)(GICD_BASE + 0x100 + 4*(n)))
#define GICD_ICENABLER(n)      *((volatile uint32 *)(GICD_BASE + 0x180 + 4*(n)))
#define GICD_ISPENDR(n)        *((volatile uint32 *)(GICD_BASE + 0x200 + 4*(n)))
#define GICD_ICPENDR(n)        *((volatile uint32 *)(GICD_BASE + 0x280 + 4*(n)))
#define GICD_ISACTIVER(n)      *((volatile uint32 *)(GICD_BASE + 0x300 + 4*(n)))
#define GICD_ICACTIVER(n)      *((volatile uint32 *)(GICD_BASE + 0x380 + 4*(n)))
#define GICD_IPRIORITYR(n)     *((volatile uint32 *)(GICD_BASE + 0x400 + 4*(n)))
#define GICD_ITARGETSR(n)      *((volatile uint32 *)(GICD_BASE + 0x800 + 4*(n)))
#define GICD_ICFGR(n)          *((volatile uint32 *)(GICD_BASE + 0xC00 + 4*(n)))
#define GICD_IGRPMODR(n)       *((volatile uint32 *)(GICD_BASE + 0xD00 + 4*(n)))
#define GICD_NSACR(n)          *((volatile uint32 *)(GICD_BASE + 0xE00 + 4*(n)))
#define GICD_SGIR              *((volatile uint32 *)(GICD_BASE + 0xF00))
#define GICD_CPENDSGIR(n)      *((volatile uint32 *)(GICD_BASE + 0xF10 + 4*(n)))
#define GICD_SPENDSGIR(n)      *((volatile uint32 *)(GICD_BASE + 0xF20 + 4*(n)))

/* Generic Interrupt Controller Redistributor (GICR) */
#define GICR_CTLR              *((volatile uint32 *)(GICR_BASE + 0x100000 + 0x0000))
#define GICR_IIDR              *((volatile uint32 *)(GICR_BASE + 0x100000 + 0x0004))
#define GICR_WAKER             *((volatile uint32 *)(GICR_BASE + 0x100000 + 0x0014))

/* Generic Interrupt Controller Interface (GICC) */
#define GICC_CTLR              *((volatile uint32 *)(GICC_BASE + 0x000))
#define GICC_PMR               *((volatile uint32 *)(GICC_BASE + 0x004))
#define GICC_BPR               *((volatile uint32 *)(GICC_BASE + 0x008))
#define GICC_IAR               *((volatile const uint32 *)(GICC_BASE + 0x00C))
#define GICC_EOIR              *((volatile uint32 *)(GICC_BASE + 0x010))
#define GICC_RPR               *((volatile const uint32 *)(GICC_BASE + 0x014))
#define GICC_HPPIR             *((volatile const uint32 *)(GICC_BASE + 0x018))
#define GICC_ABPR              *((volatile uint32 *)(GICC_BASE + 0x01C))
#define GICC_AIAR              *((volatile const uint32 *)(GICC_BASE + 0x020))
#define GICC_AEOIR             *((volatile uint32 *)(GICC_BASE + 0x024))
#define GICC_AHPPIR            *((volatile const uint32 *)(GICC_BASE + 0x028))
#define GICC_STATUSR           *((volatile uint32 *)(GICC_BASE + 0x02C))
#define GICC_APR(n)            *((volatile uint32 *)(GICC_BASE + 0x0D0 + 4*(n)))
#define GICC_NSAPR(n)          *((volatile uint32 *)(GICC_BASE + 0x0E0 + 4*(n)))
#define GICC_IIDR              *((volatile const uint32 *)(GICC_BASE + 0x0FC))
#define GICC_DIR               *((volatile uint32 *)(GICC_BASE + 0x1000))

/*******************************************************************************
**                      Global Data Types                                     **
*******************************************************************************/

/*******************************************************************************
**                      Function Prototypes                                   **
*******************************************************************************/

/*******************************************************************************
**                      Macro                                                 **
*******************************************************************************/

/* Read the GIC's TYPER register */
__STATIC_INLINE uint32 GIC_DistributorInfo()
{
  return (GICD_TYPER);
}

/*  Enable the interrupt distributor using the GIC's CTLR register */
__STATIC_INLINE void GIC_EnableDistributor()
{
  GICD_CTLR |= 3U;
}

/* Disable the interrupt distributor using the GIC's CTLR register */
__STATIC_INLINE void GIC_DisableDistributor()
{
  GICD_CTLR &= 0xFFFFFFFCU;
}

/* Reads the GIC's IIDR register */
__STATIC_INLINE uint32 GIC_DistributorImplementer()
{
  return (GICD_IIDR);
}

/* Sets the GIC's ITARGETSR register for the given interrupt */
__STATIC_INLINE void GIC_SetTarget(uint32 IRQn, uint32 cpu_target)
{
  uint32 mask = GICD_ITARGETSR(IRQn / 4U) & ~(0xFFUL << ((IRQn % 4U) * 8U));
  GICD_ITARGETSR(IRQn / 4U) = mask | ((cpu_target & 0xFFUL) << ((IRQn % 4U) * 8U));
}

/* Read the GIC's ITARGETSR register */
__STATIC_INLINE uint32 GIC_GetTarget(uint32 IRQn)
{
  return (GICD_ITARGETSR(IRQn / 4U) >> ((IRQn % 4U) * 8U)) & 0xFFUL;
}

/* Enables the given interrupt using GIC's ISENABLER register */
__STATIC_INLINE void GIC_EnableIRQ(uint32 IRQn)
{
  GICD_ISENABLER(IRQn / 32U) = 1U << (IRQn % 32U);
}

/* Get interrupt enable status using GIC's ISENABLER register */
__STATIC_INLINE uint32 GIC_GetEnableIRQ(uint32 IRQn)
{
  return (GICD_ISENABLER(IRQn / 32U) >> (IRQn % 32U)) & 1UL;
}

/* Disables the given interrupt using GIC's ICENABLER register */
__STATIC_INLINE void GIC_DisableIRQ(uint32 IRQn)
{
  GICD_ICENABLER(IRQn / 32U) = 1U << (IRQn % 32U);
}

/* Get interrupt pending status from GIC's ISPENDR register */
__STATIC_INLINE uint32 GIC_GetPendingIRQ(uint32 IRQn)
{
  uint32 pend;

  if (IRQn >= 16U) {
    pend = (GICD_ISPENDR(IRQn / 32U) >> (IRQn % 32U)) & 1UL;
  } else {
    /* INTID 0-15 Software Generated Interrupt */
    pend = (GICD_SPENDSGIR(IRQn / 4U) >> ((IRQn % 4U) * 8U)) & 0xFFUL;
    /* No CPU identification offered */
    if (pend != 0U) {
      pend = 1U;
    } else {
      pend = 0U;
    }
  }

  return (pend);
}

/* Sets the given interrupt as pending using GIC's ISPENDR register */
__STATIC_INLINE void GIC_SetPendingIRQ(uint32 IRQn)
{
  if (IRQn >= 16U) {
    GICD_ISPENDR(IRQn / 32U) = 1U << (IRQn % 32U);
  } else {
    /* INTID 0-15 Software Generated Interrupt */
    GICD_SPENDSGIR(IRQn / 4U) = 1U << ((IRQn % 4U) * 8U);
  }
}

/* Clears the given interrupt from being pending using GIC's ICPENDR register */
__STATIC_INLINE void GIC_ClearPendingIRQ(uint32 IRQn)
{
  if (IRQn >= 16U) {
    GICD_ICPENDR(IRQn / 32U) = 1U << (IRQn % 32U);
  } else {
    /* INTID 0-15 Software Generated Interrupt */
    GICD_CPENDSGIR(IRQn / 4U) = 1U << ((IRQn % 4U) * 8U);
  }
}

/* Sets the interrupt configuration using GIC's ICFGR register */
__STATIC_INLINE void GIC_SetConfiguration(uint32 IRQn, uint32 int_config)
{
  uint32 icfgr = GICD_ICFGR(IRQn / 16U);
  uint32 shift = (IRQn % 16U) << 1U;

  icfgr &= (~(3U         << shift));
  icfgr |= (  int_config << shift);

  GICD_ICFGR(IRQn / 16U) = icfgr;
}

/* Get the interrupt configuration from the GIC's ICFGR register */
__STATIC_INLINE uint32 GIC_GetConfiguration(uint32 IRQn)
{
  return (GICD_ICFGR(IRQn / 16U) >> ((IRQn % 16U) >> 1U));
}

/* Set the priority for the given interrupt in the GIC's IPRIORITYR register */
__STATIC_INLINE void GIC_SetPriority(uint32 IRQn, uint32 priority)
{
  uint32 mask = GICD_IPRIORITYR(IRQn / 4U) & ~(0xFFUL << ((IRQn % 4U) * 8U));
  GICD_IPRIORITYR(IRQn / 4U) = mask | ((priority & 0xFFUL) << ((IRQn % 4U) * 8U));
}

/* Read the current interrupt priority from GIC's IPRIORITYR register */
__STATIC_INLINE uint32 GIC_GetPriority(uint32 IRQn)
{
  return (GICD_IPRIORITYR(IRQn / 4U) >> ((IRQn % 4U) * 8U)) & 0xFFUL;
}

/* Get the status for a given interrupt */
__STATIC_INLINE uint32 GIC_GetIRQStatus(uint32 IRQn)
{
  uint32 pending, active;

  active = ((GICD_ISACTIVER(IRQn / 32U))  >> (IRQn % 32U)) & 1UL;
  pending = ((GICD_ISPENDR(IRQn / 32U)) >> (IRQn % 32U)) & 1UL;

  return ((active<<1U) | pending);
}

/* Generate a software interrupt using GIC's SGIR register */
__STATIC_INLINE void GIC_SendSGI(uint32 IRQn, uint32 target_list, uint32 filter_list)
{
  GICD_SGIR = ((filter_list & 3U) << 24U) | ((target_list & 0xFFUL) << 16U) | (IRQn & 0x0FUL);
}

/* Set the interrupt group from the GIC's IGROUPR register */
__STATIC_INLINE void GIC_SetGroup(uint32 IRQn, uint32 group)
{
  uint32 igroupr = GICD_IGROUPR(IRQn / 32U);
  uint32 shift   = (IRQn % 32U);

  igroupr &= (~(1U          << shift));
  igroupr |= ( (group & 1U) << shift);

  GICD_IGROUPR(IRQn / 32U) = igroupr;
}
#define GIC_SetSecurity         GIC_SetGroup

/* Get the interrupt group from the GIC's IGROUPR register */
__STATIC_INLINE uint32 GIC_GetGroup(uint32 IRQn)
{
  return (GICD_IGROUPR(IRQn / 32U) >> (IRQn % 32U)) & 1UL;
}
#define GIC_GetSecurity         GIC_GetGroup

#if (defined(__ARM_ARCH_8R__ ) || defined(__ARM8))
/*  Enable the interrupt redistributor wakeup */
__STATIC_INLINE void GIC_WakeupRedistributor()
{
  GICR_WAKER = 0U;
}

/* Enable the CPU's interrupt interface */
__STATIC_INLINE void GIC_EnableInterface()
{
  // __set_ICC_IGRPEN0(1U); //enable interface grp0
  __set_ICC_IGRPEN1(1U); //enable interface grp1
}

/* Disable the CPU's interrupt interface */
__STATIC_INLINE void GIC_DisableInterface()
{
  // __set_ICC_IGRPEN0(0U); //disable interface grp0
  __set_ICC_IGRPEN1(0U); //disable interface grp1
}

/* Read the CPU's IAR register */
/* Group 1 only */
__STATIC_INLINE uint32 GIC_AcknowledgePending()
{
  return (uint32)(__get_ICC_IAR1());
}

/* Read the CPU's IAR register */
/* Group 0 only */
__STATIC_INLINE uint32 GIC_AcknowledgePending0()
{
  return (uint32)(__get_ICC_IAR0());
}

/* Writes the given interrupt number to the CPU's EOIR register */
/* Group 1 only */
__STATIC_INLINE void GIC_EndInterrupt(uint32 IRQn)
{
  __set_ICC_EOIR1(IRQn);
}

/* Set the interrupt priority mask using CPU's PMR register */
/* Group 1 only */
__STATIC_INLINE void GIC_SetInterfacePriorityMask(uint32 priority)
{
  __set_ICC_PMR(priority << 3); //set priority mask
}

/* Read the current interrupt priority mask from CPU's PMR register */
/* Group 1 only */
__STATIC_INLINE uint32 GIC_GetInterfacePriorityMask()
{
  return (__get_ICC_PMR() >> 3);
}

/* Configures the group priority and subpriority split point using CPU's BPR register */
/* Group 1 only */
__STATIC_INLINE void GIC_SetBinaryPoint(uint32 binary_point)
{
  __set_ICC_BPR1(binary_point & 7U); //set binary point
}

/* Read the current group priority and subpriority split point from CPU's BPR register */
/* Group 1 only */
__STATIC_INLINE uint32 GIC_GetBinaryPoint()
{
  return __get_ICC_BPR1();
}

/* Get the interrupt number of the highest interrupt pending from CPU's HPPIR register */
/* Group 1 only */
__STATIC_INLINE uint32 GIC_GetHighPendingIRQ()
{
  return __get_ICC_HPPIR1();
}

/* Initialize the CPU's interrupt interface (using group 1 only) */
__STATIC_INLINE void GIC_CPUInterfaceInit()
{
  /* Enable interface */
  GIC_EnableInterface();
  /* Set binary point to 3 */
  GIC_SetBinaryPoint(3U);
  /* Set priority mask */
  GIC_SetInterfacePriorityMask(0xFFU);
}
#else
/* Enable the CPU's interrupt interface */
__STATIC_INLINE void GIC_EnableInterface()
{
  GICC_CTLR |= 1U; //enable interface
}

/* Disable the CPU's interrupt interface */
__STATIC_INLINE void GIC_DisableInterface()
{
  GICC_CTLR &=~1U; //disable interface
}

/* Read the CPU's IAR register */
__STATIC_INLINE uint32 GIC_AcknowledgePending()
{
  return (uint32)(GICC_IAR);
}

/* Writes the given interrupt number to the CPU's EOIR register */
__STATIC_INLINE void GIC_EndInterrupt(uint32 IRQn)
{
  GICC_EOIR = IRQn;
}

/* Set the interrupt priority mask using CPU's PMR register */
__STATIC_INLINE void GIC_SetInterfacePriorityMask(uint32 priority)
{
  GICC_PMR = priority & 0xFFUL; //set priority mask
}

/* Read the current interrupt priority mask from CPU's PMR register */
__STATIC_INLINE uint32 GIC_GetInterfacePriorityMask()
{
  return GICC_PMR;
}

/* Configures the group priority and subpriority split point using CPU's BPR register */
__STATIC_INLINE void GIC_SetBinaryPoint(uint32 binary_point)
{
  GICC_BPR = binary_point & 7U; //set binary point
}

/* Read the current group priority and subpriority split point from CPU's BPR register */
__STATIC_INLINE uint32 GIC_GetBinaryPoint()
{
  return GICC_BPR;
}

/* Get the interrupt number of the highest interrupt pending from CPU's HPPIR register */
__STATIC_INLINE uint32 GIC_GetHighPendingIRQ()
{
  return GICC_HPPIR;
}

/* Provides information about the implementer and revision of the CPU interface */
__STATIC_INLINE uint32 GIC_GetInterfaceId()
{
  return GICC_IIDR;
}

/* Initialize the CPU's interrupt interface */
__STATIC_INLINE void GIC_CPUInterfaceInit()
{
  uint32 i;
  uint32 priority_field;

  /* A reset sets all bits in the IGROUPRs corresponding to the SPIs to 0,
  configuring all of the interrupts as Secure. */

  /* Disable interrupt forwarding */
  GIC_DisableInterface();

  /* Priority level is implementation defined.
   To determine the number of priority bits implemented write 0xFF to an IPRIORITYR
   priority field and read back the value stored.*/
  GIC_SetPriority((uint32)0U, 0xFFU);
  priority_field = GIC_GetPriority((uint32)0U);

  /* SGI and PPI */
  for (i = 0U; i < 32U; i++)
  {
    if(i > 15U) {
      /* Set level-sensitive (and N-N model) for PPI */
      GIC_SetConfiguration((uint32)i, 0U);
    }
    /* Disable SGI and PPI interrupts */
    GIC_DisableIRQ((uint32)i);
    /* Set priority */
    GIC_SetPriority((uint32)i, priority_field/2U);
  }
  /* Enable interface */
  GIC_EnableInterface();
  /* Set binary point to 3 */
  GIC_SetBinaryPoint(3U);
  /* Set priority mask */
  GIC_SetInterfacePriorityMask(0xFFU);
}
#endif /* (defined(__ARM_ARCH_8R__ ) || defined(__ARM8)) */

/* Initialize the interrupt distributor */
__STATIC_INLINE void GIC_DistInit()
{
  uint32 i;
  uint32 num_irq = 0U;
  uint32 priority_field;

  /* A reset sets all bits in the IGROUPRs corresponding to the SPIs to 0,
    configuring all of the interrupts as Secure. */

  /* Disable interrupt forwarding */
  GIC_DisableDistributor();
  /* Get the maximum number of interrupts that the GIC supports */
  num_irq = 32U * ((GIC_DistributorInfo() & 0x1FU) + 1U);

  /* Priority level is implementation defined.
   To determine the number of priority bits implemented write 0xFF to an IPRIORITYR
   priority field and read back the value stored.*/
  GIC_SetPriority((uint32)32U, 0xFFU);
  priority_field = GIC_GetPriority((uint32)32U);

  for (i = 32U; i < num_irq; i++)
  {
    /* Disable the SPI interrupt */
    GIC_DisableIRQ((uint32)i);
    /* Clear pending interrupt */
    GIC_ClearPendingIRQ((uint32)i);
    /* Set level-sensitive (and N-N model) */
    GIC_SetConfiguration((uint32)i, 0U);
    /* Set priority */
    GIC_SetPriority((uint32)i, priority_field/2U);
#if (defined(__ARM_ARCH_7R__) || defined(__CORE_CORTEXR7__))
    /* Set target list to CPU0 */
    GIC_SetTarget((uint32)i, 1U);
#endif /* (defined(__ARM_ARCH_7R__) || defined(__CORE_CORTEXR7__)) */
    /* Set group 1 (since RT domain is non-secure) */
    GIC_SetGroup((uint32)i, 1U);
  }
  /* Enable distributor */
  //GIC_EnableDistributor();
}

/* Initialize and enable the GIC */
__STATIC_INLINE void GIC_Enable()
{
  GIC_DistInit();
  GIC_CPUInterfaceInit();
#if (defined(__ARM_ARCH_8R__) || defined(__ARM8))
  GIC_WakeupRedistributor();
#endif /* (defined(__ARM_ARCH_8R__) || defined(__ARM8)) */
}

#endif /* ARM_GIC_H */
/*******************************************************************************
**                      End of File                                           **
*******************************************************************************/
