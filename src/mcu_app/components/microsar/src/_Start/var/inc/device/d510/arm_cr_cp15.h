/*============================================================================*/
/* Project      = AUTOSAR Renesas CPF MCAL Components                         */
/* Module       = arm_cr_cp15.h                                               */
/* SW-VERSION   = 1.0.0                                                       */
/*============================================================================*/
/*                                  COPYRIGHT                                 */
/*============================================================================*/
/* Copyright(c) 2021 Renesas Electronics Corporation.                         */
/*============================================================================*/
/* Purpose:                                                                   */
/* Macros for ARM Cortex-R CP15                                               */
/*                                                                            */
/*============================================================================*/
/*                                                                            */
/* Unless otherwise agreed upon in writing between your company and           */
/* Renesas Electronics Corporation the following shall apply!                 */
/*                                                                            */
/* Warranty Disclaimer                                                        */
/*                                                                            */
/* There is no warranty of any kind whatsoever granted by Renesas. Any        */
/* warranty is expressly disclaimed and excluded by Renesas, either expressed */
/* or implied, including but not limited to those for non-infringement of     */
/* intellectual property, merchantability and/or fitness for the particular   */
/* purpose.                                                                   */
/*                                                                            */
/* Renesas shall not have any obligation to maintain, service or provide bug  */
/* fixes for the supplied Product(s) and/or the Application.                  */
/*                                                                            */
/* Each User is solely responsible for determining the appropriateness of     */
/* using the Product(s) and assumes all risks associated with its exercise    */
/* of rights under this Agreement, including, but not limited to the risks    */
/* and costs of program errors, compliance with applicable laws, damage to    */
/* or loss of data, programs or equipment, and unavailability or              */
/* interruption of operations.                                                */
/*                                                                            */
/* Limitation of Liability                                                    */
/*                                                                            */
/* In no event shall Renesas be liable to the User for any incidental,        */
/* consequential, indirect, or punitive damage (including but not limited     */
/* to lost profits) regardless of whether such liability is based on breach   */
/* of contract, tort, strict liability, breach of warranties, failure of      */
/* essential purpose or otherwise and even if advised of the possibility of   */
/* such damages. Renesas shall not be liable for any services or products     */
/* provided by third party vendors, developers or consultants identified or   */
/* referred to the User by Renesas in connection with the Product(s) and/or   */
/* the Application.                                                           */
/*                                                                            */
/*============================================================================*/
/* Environment:                                                               */
/*              Devices:        CPF                                           */
/*============================================================================*/

/*******************************************************************************
**                      Revision Control History                              **
*******************************************************************************/
/*
 * 1.0.0:     20/08/2021 :    Initial Version
 */
/******************************************************************************/

#ifndef ARM_CR_CP15_H
#define ARM_CR_CP15_H

/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/

/*******************************************************************************
**                      Version Information                                   **
*******************************************************************************/

/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/

/*******************************************************************************
**                      Global Data Types                                     **
*******************************************************************************/

/*******************************************************************************
**                      Function Prototypes                                   **
*******************************************************************************/

/*******************************************************************************
**                      Macro                                                 **
*******************************************************************************/

/* Get ACTLR */
__STATIC_INLINE uint32 __get_ACTLR(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 1, 0, 1);
  return(ret);
}

/* Set ACTLR */
__STATIC_INLINE void __set_ACTLR(uint32 actlr)
{
  __set_CP(15, 0, actlr, 1, 0, 1);
}

/* Get CPACR */
__STATIC_INLINE uint32 __get_CPACR(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 1, 0, 2);
  return ret;
}

/* Set CPACR */
__STATIC_INLINE void __set_CPACR(uint32 cpacr)
{
  __set_CP(15, 0, cpacr, 1, 0, 2);
}

/* Get DFSR */
__STATIC_INLINE uint32 __get_DFSR(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 5, 0, 0);
  return ret;
}

/* Set DFSR */
__STATIC_INLINE void __set_DFSR(uint32 dfsr)
{
  __set_CP(15, 0, dfsr, 5, 0, 0);
}

/* Get IFSR */
__STATIC_INLINE uint32 __get_IFSR(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 5, 0, 1);
  return ret;
}

/* Set IFSR */
__STATIC_INLINE void __set_IFSR(uint32 ifsr)
{
  __set_CP(15, 0, ifsr, 5, 0, 1);
}

#if (defined(__ARM_ARCH_8R__ ) || defined(__ARM8))
/* Get ISR */
__STATIC_INLINE uint32 __get_ISR(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 12, 1, 0);
  return ret;
}

/* Set HSCTLR */
__STATIC_INLINE void __set_HSCTLR(uint32 hsctlr)
{
  __set_CP(15, 4, hsctlr, 1, 0, 0);
}

/* Get HSCTLR */
__STATIC_INLINE uint32 __get_HSCTLR(void)
{
  uint32 ret = 0;
  __get_CP(15, 4, ret, 1, 0, 0);
  return ret;
}
#endif /* (defined(__ARM_ARCH_8R__ ) || defined(__ARM8)) */

/* Set SCTLR */
__STATIC_INLINE void __set_SCTLR(uint32 sctlr)
{
  __set_CP(15, 0, sctlr, 1, 0, 0);
}

/* Get SCTLR */
__STATIC_INLINE uint32 __get_SCTLR(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 1, 0, 0);
  return ret;
}

/* Get MPIDR */
__STATIC_INLINE uint32 __get_MPIDR(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 0, 0, 5);
  return ret;
}

#if (defined(__ARM_ARCH_7R__ ) || defined(__CORE_CORTEXR7__ ))
/* Set RGNR */
__STATIC_INLINE void __set_RGNR(uint32 val)
{
  __set_CP(15, 0, val, 6, 2, 0);
}

/* Set DRBAR */
__STATIC_INLINE void __set_DRBAR(uint32 val)
{
  __set_CP(15, 0, val, 6, 1, 0);
}

/* Set DRSR */
__STATIC_INLINE void __set_DRSR(uint32 val)
{
  __set_CP(15, 0, val, 6, 1, 2);
}

/* Set DRACR */
__STATIC_INLINE void __set_DRACR(uint32 val)
{
  __set_CP(15, 0, val, 6, 1, 4);
}
#endif /* ((defined(__ARM_ARCH_7R__ ) || defined(__CORE_CORTEXR7__ )) */

#if (defined(__ARM_ARCH_8R__ ) || defined(__ARM8))
/* Get VBAR */
__STATIC_INLINE uint32 __get_VBAR(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 12, 0, 0);
  return ret;
}

/* Set VBAR */
__STATIC_INLINE void __set_VBAR(uint32 vbar)
{
  __set_CP(15, 0, vbar, 12, 0, 0);
}

/* Get HVBAR */
__STATIC_INLINE uint32 __get_HVBAR(void)
{
  uint32 ret = 0;
  __get_CP(15, 4, ret, 12, 0, 0);
  return ret;
}

/* Set HVBAR */
__STATIC_INLINE void __set_HVBAR(uint32 hvbar)
{
  __set_CP(15, 4, hvbar, 12, 0, 0);
}

/* Set CNTFRQ */
__STATIC_INLINE void __set_CNTFRQ(uint32 value)
{
  __set_CP(15, 0, value, 14, 0, 0);
}

/* Get CNTFRQ */
__STATIC_INLINE uint32 __get_CNTFRQ(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 14, 0 , 0);
  return ret;
}

/* Set CNTP_TVAL */
__STATIC_INLINE void __set_CNTP_TVAL(uint32 value)
{
  __set_CP(15, 0, value, 14, 2, 0);
}

/* Get CNTP_TVAL */
__STATIC_INLINE uint32 __get_CNTP_TVAL(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 14, 2, 0);
  return ret;
}

/* Set CNTP_CTL */
__STATIC_INLINE void __set_CNTP_CTL(uint32 value)
{
  __set_CP(15, 0, value, 14, 2, 1);
}

/* Get CNTP_CTL register */
__STATIC_INLINE uint32 __get_CNTP_CTL(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 14, 2, 1);
  return ret;
}
#endif /* ((defined(__ARM_ARCH_8R__ ) || defined(__ARM8)) */


/* Set BPIALL */
__STATIC_INLINE void __set_BPIALL(uint32 value)
{
  __set_CP(15, 0, value, 7, 5, 6);
}

/* Set ICIALLU */
__STATIC_INLINE void __set_ICIALLU(uint32 value)
{
  __set_CP(15, 0, value, 7, 5, 0);
}

/* Set DCCMVAC */
__STATIC_INLINE void __set_DCCMVAC(uint32 value)
{
  __set_CP(15, 0, value, 7, 10, 1);
}

/* Set DCIMVAC */
__STATIC_INLINE void __set_DCIMVAC(uint32 value)
{
  __set_CP(15, 0, value, 7, 6, 1);
}

/* Set DCCIMVAC  */
__STATIC_INLINE void __set_DCCIMVAC(uint32 value)
{
  __set_CP(15, 0, value, 7, 14, 1);
}

/* Set CSSELR */
__STATIC_INLINE void __set_CSSELR(uint32 value)
{
  __set_CP(15, 2, value, 0, 0, 0);
}

/* Get CSSELR */
__STATIC_INLINE uint32 __get_CSSELR(void)
{
  uint32 ret = 0;
  __get_CP(15, 2, ret, 0, 0, 0);
  return ret;
}

/* Get CCSIDR */
__STATIC_INLINE uint32 __get_CCSIDR(void)
{
  uint32 ret = 0;
  __get_CP(15, 1, ret, 0, 0, 0);
  return ret;
}

/* Get CLIDR */
__STATIC_INLINE uint32 __get_CLIDR(void)
{
  uint32 ret = 0;
  __get_CP(15, 1, ret, 0, 0, 1);
  return ret;
}

/* Set DCISW */
__STATIC_INLINE void __set_DCISW(uint32 value)
{
  __set_CP(15, 0, value, 7, 6, 2);
}

/* Set DCCSW */
__STATIC_INLINE void __set_DCCSW(uint32 value)
{
  __set_CP(15, 0, value, 7, 10, 2);
}

/* Set DCCISW */
__STATIC_INLINE void __set_DCCISW(uint32 value)
{
  __set_CP(15, 0, value, 7, 14, 2);
}

#if (defined(__ARM_ARCH_8R__ ) || defined(__ARM8))
/* Set PRSELR */
__STATIC_INLINE void __set_PRSELR(uint32 value)
{
  __set_CP(15, 0, value, 6, 2, 1);
}

/* Get PRSELR */
__STATIC_INLINE uint32 __get_PRSELR(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 6, 2, 1);
  return ret;
}

/* Set PRBAR */
__STATIC_INLINE void __set_PRBAR(uint32 value)
{
  __set_CP(15, 0, value, 6, 3, 0);
}

/* Get PRBAR */
__STATIC_INLINE uint32 __get_PRBAR(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 6, 3, 0);
  return ret;
}

/* Set PRLAR */
__STATIC_INLINE void __set_PRLAR(uint32 value)
{
  __set_CP(15, 0, value, 6, 3, 1);
}

/* Get PRLAR */
__STATIC_INLINE uint32 __get_PRLAR(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 6, 3, 1);
  return ret;
}

/* Set MAIR0 */
__STATIC_INLINE void __set_MAIR0(uint32 value)
{
  __set_CP(15, 0, value, 10, 2, 0);
}

/* Get MAIR0 */
__STATIC_INLINE uint32 __get_MAIR0(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 10, 2, 0);
  return ret;
}

/* Set MAIR1 */
__STATIC_INLINE void __set_MAIR1(uint32 value)
{
  __set_CP(15, 0, value, 10, 2, 1);
}

/* Get MAIR1 */
__STATIC_INLINE uint32 __get_MAIR1(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 10, 2, 1);
  return ret;
}

/* Set HPRSELR */
__STATIC_INLINE void __set_HPRSELR(uint32 value)
{
  __set_CP(15, 4, value, 6, 2, 1);
}

/* Get HPRSELR */
__STATIC_INLINE uint32 __get_HPRSELR(void)
{
  uint32 ret = 0;
  __get_CP(15, 4, ret, 6, 2, 1);
  return ret;
}

/* Set HPRBAR */
__STATIC_INLINE void __set_HPRBAR(uint32 value)
{
  __set_CP(15, 4, value, 6, 3, 0);
}

/* Get HPRBAR */
__STATIC_INLINE uint32 __get_HPRBAR(void)
{
  uint32 ret = 0;
  __get_CP(15, 4, ret, 6, 3, 0);
  return ret;
}

/* Set HPRLAR */
__STATIC_INLINE void __set_HPRLAR(uint32 value)
{
  __set_CP(15, 4, value, 6, 3, 1);
}

/* Get HPRLAR */
__STATIC_INLINE uint32 __get_HPRLAR(void)
{
  uint32 ret = 0;
  __get_CP(15, 4, ret, 6, 3, 1);
  return ret;
}

/* Set HMAIR0 */
__STATIC_INLINE void __set_HMAIR0(uint32 value)
{
  __set_CP(15, 4, value, 10, 2, 0);
}

/* Get HMAIR0 */
__STATIC_INLINE uint32 __get_HMAIR0(void)
{
  uint32 ret = 0;
  __get_CP(15, 4, ret, 10, 2, 0);
  return ret;
}

/* Set HMAIR1 */
__STATIC_INLINE void __set_HMAIR1(uint32 value)
{
  __set_CP(15, 4, value, 10, 2, 1);
}

/* Get HMAIR1 */
__STATIC_INLINE uint32 __get_HMAIR1(void)
{
  uint32 ret = 0;
  __get_CP(15, 4, ret, 10, 2, 1);
  return ret;
}

/* Get ICC_IAR0 */
__STATIC_INLINE uint32 __get_ICC_IAR0(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 12, 8, 0);
  return ret;
}

/* Get ICC_IAR1 */
__STATIC_INLINE uint32 __get_ICC_IAR1(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 12, 12, 0);
  return ret;
}

/* Set ICC_EOIR0 */
__STATIC_INLINE void __set_ICC_EOIR0(uint32 value)
{
  __set_CP(15, 0, value, 12, 8, 1);
}

/* Set ICC_EOIR1 */
__STATIC_INLINE void __set_ICC_EOIR1(uint32 value)
{
  __set_CP(15, 0, value, 12, 12, 1);
}

/* Get ICC_HPPIR0 */
__STATIC_INLINE uint32 __get_ICC_HPPIR0(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 12, 8, 2);
  return ret;
}

/* Get ICC_HPPIR1 */
__STATIC_INLINE uint32 __get_ICC_HPPIR1(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 12, 12, 2);
  return ret;
}

/* Get ICC_BPR0 */
__STATIC_INLINE uint32 __get_ICC_BPR0(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 12, 8, 3);
  return ret;
}

/* Set ICC_BPR0 */
__STATIC_INLINE void __set_ICC_BPR0(uint32 value)
{
  __set_CP(15, 0, value, 12, 8, 3);
}

/* Get ICC_BPR1 */
__STATIC_INLINE uint32 __get_ICC_BPR1(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 12, 12, 3);
  return ret;
}

/* Set ICC_BPR1 */
__STATIC_INLINE void __set_ICC_BPR1(uint32 value)
{
  __set_CP(15, 0, value, 12, 12, 3);
}

/* Get ICC_PMR */
__STATIC_INLINE uint32 __get_ICC_PMR(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 4, 6, 0);
  return ret;
}

/* Set ICC_PMR */
__STATIC_INLINE void __set_ICC_PMR(uint32 value)
{
  __set_CP(15, 0, value, 4, 6, 0);
}

/* Get ICC_CTLR */
__STATIC_INLINE uint32 __get_ICC_CTLR(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 12, 12, 4);
  return ret;
}

/* Set ICC_CTLR */
__STATIC_INLINE void __set_ICC_CTLR(uint32 value)
{
  __set_CP(15, 0, value, 12, 12, 4);
}

/* Get ICC_IGRPEN0 */
__STATIC_INLINE uint32 __get_ICC_IGRPEN0(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 12, 12, 6);
  return ret;
}

/* Set ICC_IGRPEN0 */
__STATIC_INLINE void __set_ICC_IGRPEN0(uint32 value)
{
  __set_CP(15, 0, value, 12, 12, 6);
}

/* Get ICC_IGRPEN1 */
__STATIC_INLINE uint32 __get_ICC_IGRPEN1(void)
{
  uint32 ret = 0;
  __get_CP(15, 0, ret, 12, 12, 7);
  return ret;
}

/* Set ICC_IGRPEN1 */
__STATIC_INLINE void __set_ICC_IGRPEN1(uint32 value)
{
  __set_CP(15, 0, value, 12, 12, 7);
}
#endif /* (defined(__ARM_ARCH_8R__ ) || defined(__ARM8)) */

#endif /* ARM_CR_CP15_H */
/*******************************************************************************
**                      End of File                                           **
*******************************************************************************/
