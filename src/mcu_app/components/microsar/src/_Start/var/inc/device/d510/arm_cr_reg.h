/*============================================================================*/
/* Project      = AUTOSAR Renesas CPF MCAL Components                         */
/* Module       = arm_cr_reg.h                                                */
/* SW-VERSION   = 1.0.0                                                       */
/*============================================================================*/
/*                                  COPYRIGHT                                 */
/*============================================================================*/
/* Copyright(c) 2021 Renesas Electronics Corporation.                         */
/*============================================================================*/
/* Purpose:                                                                   */
/* Registers definition for ARM Cortex R                                      */
/*                                                                            */
/*============================================================================*/
/*                                                                            */
/* Unless otherwise agreed upon in writing between your company and           */
/* Renesas Electronics Corporation the following shall apply!                 */
/*                                                                            */
/* Warranty Disclaimer                                                        */
/*                                                                            */
/* There is no warranty of any kind whatsoever granted by Renesas. Any        */
/* warranty is expressly disclaimed and excluded by Renesas, either expressed */
/* or implied, including but not limited to those for non-infringement of     */
/* intellectual property, merchantability and/or fitness for the particular   */
/* purpose.                                                                   */
/*                                                                            */
/* Renesas shall not have any obligation to maintain, service or provide bug  */
/* fixes for the supplied Product(s) and/or the Application.                  */
/*                                                                            */
/* Each User is solely responsible for determining the appropriateness of     */
/* using the Product(s) and assumes all risks associated with its exercise    */
/* of rights under this Agreement, including, but not limited to the risks    */
/* and costs of program errors, compliance with applicable laws, damage to    */
/* or loss of data, programs or equipment, and unavailability or              */
/* interruption of operations.                                                */
/*                                                                            */
/* Limitation of Liability                                                    */
/*                                                                            */
/* In no event shall Renesas be liable to the User for any incidental,        */
/* consequential, indirect, or punitive damage (including but not limited     */
/* to lost profits) regardless of whether such liability is based on breach   */
/* of contract, tort, strict liability, breach of warranties, failure of      */
/* essential purpose or otherwise and even if advised of the possibility of   */
/* such damages. Renesas shall not be liable for any services or products     */
/* provided by third party vendors, developers or consultants identified or   */
/* referred to the User by Renesas in connection with the Product(s) and/or   */
/* the Application.                                                           */
/*                                                                            */
/*============================================================================*/
/* Environment:                                                               */
/*              Devices:        CPF                                           */
/*============================================================================*/

/*******************************************************************************
**                      Revision Control History                              **
*******************************************************************************/
/*
 * 1.0.0:     20/08/2021 :    Initial Version
 */
/******************************************************************************/

#ifndef ARM_CR_REG_H
#define ARM_CR_REG_H

/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/

/*******************************************************************************
**                      Version Information                                   **
*******************************************************************************/

/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/

/*******************************************************************************
**                      Global Data Types                                     **
*******************************************************************************/

/*******************************************************************************
**                      Function Prototypes                                   **
*******************************************************************************/

/*******************************************************************************
**                      Macro                                                 **
*******************************************************************************/

/* CPSR Register Definitions */
#define CPSR_N_Pos                       31U                                    /* CPSR: N Position */
#define CPSR_N_Msk                       (1UL << CPSR_N_Pos)                    /* CPSR: N Mask */

#define CPSR_Z_Pos                       30U                                    /* CPSR: Z Position */
#define CPSR_Z_Msk                       (1UL << CPSR_Z_Pos)                    /* CPSR: Z Mask */

#define CPSR_C_Pos                       29U                                    /* CPSR: C Position */
#define CPSR_C_Msk                       (1UL << CPSR_C_Pos)                    /* CPSR: C Mask */

#define CPSR_V_Pos                       28U                                    /* CPSR: V Position */
#define CPSR_V_Msk                       (1UL << CPSR_V_Pos)                    /* CPSR: V Mask */

#define CPSR_Q_Pos                       27U                                    /* CPSR: Q Position */
#define CPSR_Q_Msk                       (1UL << CPSR_Q_Pos)                    /* CPSR: Q Mask */

#define CPSR_IT0_Pos                     25U                                    /* CPSR: IT0 Position */
#define CPSR_IT0_Msk                     (3UL << CPSR_IT0_Pos)                  /* CPSR: IT0 Mask */

#define CPSR_J_Pos                       24U                                    /* CPSR: J Position */
#define CPSR_J_Msk                       (1UL << CPSR_J_Pos)                    /* CPSR: J Mask */

#define CPSR_GE_Pos                      16U                                    /* CPSR: GE Position */
#define CPSR_GE_Msk                      (0xFUL << CPSR_GE_Pos)                 /* CPSR: GE Mask */

#define CPSR_IT1_Pos                     10U                                    /* CPSR: IT1 Position */
#define CPSR_IT1_Msk                     (0x3FUL << CPSR_IT1_Pos)               /* CPSR: IT1 Mask */

#define CPSR_E_Pos                       9U                                     /* CPSR: E Position */
#define CPSR_E_Msk                       (1UL << CPSR_E_Pos)                    /* CPSR: E Mask */

#define CPSR_A_Pos                       8U                                     /* CPSR: A Position */
#define CPSR_A_Msk                       (1UL << CPSR_A_Pos)                    /* CPSR: A Mask */

#define CPSR_I_Pos                       7U                                     /* CPSR: I Position */
#define CPSR_I_Msk                       (1UL << CPSR_I_Pos)                    /* CPSR: I Mask */

#define CPSR_F_Pos                       6U                                     /* CPSR: F Position */
#define CPSR_F_Msk                       (1UL << CPSR_F_Pos)                    /* CPSR: F Mask */

#define CPSR_T_Pos                       5U                                     /* CPSR: T Position */
#define CPSR_T_Msk                       (1UL << CPSR_T_Pos)                    /* CPSR: T Mask */

#define CPSR_M_Pos                       0U                                     /* CPSR: M Position */
#define CPSR_M_Msk                       (0x1FUL << CPSR_M_Pos)                 /* CPSR: M Mask */

#define CPSR_M_USR                       0x10U                                  /* CPSR: M User mode (PL0) */
#define CPSR_M_FIQ                       0x11U                                  /* CPSR: M Fast Interrupt mode (PL1) */
#define CPSR_M_IRQ                       0x12U                                  /* CPSR: M Interrupt mode (PL1) */
#define CPSR_M_SVC                       0x13U                                  /* CPSR: M Supervisor mode (PL1) */
#define CPSR_M_MON                       0x16U                                  /* CPSR: M Monitor mode (PL1) */
#define CPSR_M_ABT                       0x17U                                  /* CPSR: M Abort mode (PL1) */
#define CPSR_M_HYP                       0x1AU                                  /* CPSR: M Hypervisor mode (PL2) */
#define CPSR_M_UND                       0x1BU                                  /* CPSR: M Undefined mode (PL1) */
#define CPSR_M_SYS                       0x1FU                                  /* CPSR: M System mode (PL1) */

/* SCTLR Register Definitions */
#define SCTLR_IE_Pos                     31U                                    /* SCTLR: IE Position */
#define SCTLR_IE_Msk                     (1UL << SCTLR_IE_Pos)                  /* SCTLR: IE Mask */

#define SCTLR_TE_Pos                     30U                                    /* SCTLR: TE Position */
#define SCTLR_TE_Msk                     (1UL << SCTLR_TE_Pos)                  /* SCTLR: TE Mask */

#define SCTLR_AFE_Pos                    29U                                    /* SCTLR: AFE Position */
#define SCTLR_AFE_Msk                    (1UL << SCTLR_AFE_Pos)                 /* SCTLR: AFE Mask */

#define SCTLR_TRE_Pos                    28U                                    /* SCTLR: TRE Position */
#define SCTLR_TRE_Msk                    (1UL << SCTLR_TRE_Pos)                 /* SCTLR: TRE Mask */

#define SCTLR_NMFI_Pos                   27U                                    /* SCTLR: NMFI Position */
#define SCTLR_NMFI_Msk                   (1UL << SCTLR_NMFI_Pos)                /* SCTLR: NMFI Mask */

#define SCTLR_EE_Pos                     25U                                    /* SCTLR: EE Position */
#define SCTLR_EE_Msk                     (1UL << SCTLR_EE_Pos)                  /* SCTLR: EE Mask */

#define SCTLR_VE_Pos                     24U                                    /* SCTLR: VE Position */
#define SCTLR_VE_Msk                     (1UL << SCTLR_VE_Pos)                  /* SCTLR: VE Mask */

#define SCTLR_FI_Pos                     21U                                    /* SCTLR: FI Position */
#define SCTLR_FI_Msk                     (1UL << SCTLR_FI_Pos)                  /* SCTLR: FI Mask */

#define SCTLR_UWXN_Pos                   20U                                    /* SCTLR: UWXN Position */
#define SCTLR_UWXN_Msk                   (1UL << SCTLR_UWXN_Pos)                /* SCTLR: UWXN Mask */

#define SCTLR_DZ_Pos                     19U                                    /* SCTLR: DZ Position */
#define SCTLR_DZ_Msk                     (1UL << SCTLR_DZ_Pos)                  /* SCTLR: DZ Mask */

#define SCTLR_WXN_Pos                    19U                                    /* SCTLR: WXN Position */
#define SCTLR_WXN_Msk                    (1UL << SCTLR_WXN_Pos)                 /* SCTLR: WXN Mask */

#define SCTLR_NTWE_Pos                   18U                                    /* SCTLR: NTWE Position */
#define SCTLR_NTWE_Msk                   (1UL << SCTLR_NTWE_Pos)                /* SCTLR: NTWE Mask */

#define SCTLR_BR_Pos                     17U                                    /* SCTLR: BR Position */
#define SCTLR_BR_Msk                     (1UL << SCTLR_BR_Pos)                  /* SCTLR: BR Mask */

#define SCTLR_NTWI_Pos                   16U                                    /* SCTLR: NTWI Position */
#define SCTLR_NTWI_Msk                   (1UL << SCTLR_NTWI_Pos)                /* SCTLR: NTWI Mask */

#define SCTLR_RR_Pos                     14U                                    /* SCTLR: RR Position */
#define SCTLR_RR_Msk                     (1UL << SCTLR_RR_Pos)                  /* SCTLR: RR Mask */

#define SCTLR_V_Pos                      13U                                    /* SCTLR: V Position */
#define SCTLR_V_Msk                      (1UL << SCTLR_V_Pos)                   /* SCTLR: V Mask */

#define SCTLR_I_Pos                      12U                                    /* SCTLR: I Position */
#define SCTLR_I_Msk                      (1UL << SCTLR_I_Pos)                   /* SCTLR: I Mask */

#define SCTLR_Z_Pos                      11U                                    /* SCTLR: Z Position */
#define SCTLR_Z_Msk                      (1UL << SCTLR_Z_Pos)                   /* SCTLR: Z Mask */

#define SCTLR_SW_Pos                     10U                                    /* SCTLR: SW Position */
#define SCTLR_SW_Msk                     (1UL << SCTLR_SW_Pos)                  /* SCTLR: SW Mask */

#define SCTLR_SED_Pos                    8U                                     /* SCTLR: SED Position */
#define SCTLR_SED_Msk                    (1UL << SCTLR_SED_Pos)                 /* SCTLR: SED Mask */

#define SCTLR_ITD_Pos                    7U                                     /* SCTLR: ITD Position */
#define SCTLR_ITD_Msk                    (1UL << SCTLR_ITD_Pos)                 /* SCTLR: ITD Mask */

#define SCTLR_CP15BEN_Pos                5U                                     /* SCTLR: CP15BEN Position */
#define SCTLR_CP15BEN_Msk                (1UL << SCTLR_CP15BEN_Pos)             /* SCTLR: CP15BEN Mask */

#define SCTLR_C_Pos                      2U                                     /* SCTLR: C Position */
#define SCTLR_C_Msk                      (1UL << SCTLR_C_Pos)                   /* SCTLR: C Mask */

#define SCTLR_A_Pos                      1U                                     /* SCTLR: A Position */
#define SCTLR_A_Msk                      (1UL << SCTLR_A_Pos)                   /* SCTLR: A Mask */

#define SCTLR_M_Pos                      0U                                     /* SCTLR: M Position */
#define SCTLR_M_Msk                      (1UL << SCTLR_M_Pos)                   /* SCTLR: M Mask */

/* ACTLR Register Definition */
#define ACTLR_DICDI_Pos                  31U                                     /* ACTLR: DICDI Position */
#define ACTLR_DICDI_Msk                  (1UL << ACTLR_DICDI_Pos)                /* ACTLR: DICDI Mask */

#define ACTLR_DIB2DI_Pos                 30U                                     /* ACTLR: DIB2DI Position */
#define ACTLR_DIB2DI_Msk                 (1UL << ACTLR_DIB2DI_Pos)               /* ACTLR: DIB2DI Mask */

#define ACTLR_DIB1DI_Pos                 29U                                     /* ACTLR: DIB1DI Position */
#define ACTLR_DIB1DI_Msk                 (1UL << ACTLR_DIB1DI_Pos)               /* ACTLR: DIB1DI Mask */

#define ACTLR_DIADI_Pos                  28U                                     /* ACTLR: DIADI Position */
#define ACTLR_DIADI_Msk                  (1UL << ACTLR_DIADI_Pos)                /* ACTLR: DIADI Mask */

#define ACTLR_B1TCMPCEN_Pos              27U                                     /* ACTLR: B1TCMPCEN Position */
#define ACTLR_B1TCMPCEN_Msk              (1UL << ACTLR_B1TCMPCEN_Pos)            /* ACTLR: B1TCMPCEN Mask */

#define ACTLR_B0TCMPCEN_Pos              26U                                     /* ACTLR: B0TCMPCEN Position */
#define ACTLR_B0TCMPCEN_Msk              (1UL << ACTLR_B0TCMPCEN_Pos)            /* ACTLR: B0TCMPCEN Mask */

#define ACTLR_ATCMPCEN_Pos               25U                                     /* ACTLR: ATCMPCEN Position */
#define ACTLR_ATCMPCEN_Msk               (1UL << ACTLR_ATCMPCEN_Pos)             /* ACTLR: ATCMPCEN Mask */

#define ACTLR_AXISCEN_Pos                24U                                     /* ACTLR: AXISCEN Position */
#define ACTLR_AXISCEN_Msk                (1UL << ACTLR_AXISCEN_Pos)              /* ACTLR: AXISCEN Mask */

#define ACTLR_AXISCUEN_Pos               23U                                     /* ACTLR: AXISCUEN Position */
#define ACTLR_AXISCUEN_Msk               (1UL << ACTLR_AXISCUEN_Pos)             /* ACTLR: AXISCUEN Mask */

#define ACTLR_DILSM_Pos                  22U                                     /* ACTLR: DILSM Position */
#define ACTLR_DILSM_Msk                  (1UL << ACTLR_DILSM_Pos)                /* ACTLR: DILSM Mask */

#define ACTLR_DEOLP_Pos                  21U                                     /* ACTLR: DEOLP Position */
#define ACTLR_DEOLP_Msk                  (1UL << ACTLR_DEOLP_Pos)                /* ACTLR: DEOLP Mask */

#define ACTLR_DBHE_Pos                   20U                                     /* ACTLR: DBHE Position */
#define ACTLR_DBHE_Msk                   (1UL << ACTLR_DBHE_Pos)                 /* ACTLR: DBHE Mask */

#define ACTLR_FRCDIS_Pos                 19U                                     /* ACTLR: FRCDIS Position */
#define ACTLR_FRCDIS_Msk                 (1UL << ACTLR_FRCDIS_Pos)               /* ACTLR: FRCDIS Mask */

#define ACTLR_RSDIS_Pos                  17U                                     /* ACTLR: RSDIS Position */
#define ACTLR_RSDIS_Msk                  (1UL << ACTLR_RSDIS_Pos)                /* ACTLR: RSDIS Mask */

#define ACTLR_BP_Pos                     15U                                     /* ACTLR: BP Position */
#define ACTLR_BP_Msk                     (3UL << ACTLR_BP_Pos)                   /* ACTLR: BP Mask */

#define ACTLR_DBWR_Pos                   14U                                     /* ACTLR: DBWR Position */
#define ACTLR_DBWR_Msk                   (1UL << ACTLR_DBWR_Pos)                 /* ACTLR: DBWR Mask */

#define ACTLR_DLFO_Pos                   13U                                     /* ACTLR: DLFO Position */
#define ACTLR_DLFO_Msk                   (1UL << ACTLR_DLFO_Pos)                 /* ACTLR: DLFO Mask */

#define ACTLR_ERPEG_Pos                  12U                                     /* ACTLR: ERPEG Position */
#define ACTLR_ERPEG_Msk                  (1UL << ACTLR_ERPEG_Pos)                /* ACTLR: ERPEG Mask */

#define ACTLR_DNCH_Pos                   11U                                     /* ACTLR: DNCH Position */
#define ACTLR_DNCH_Msk                   (1UL << ACTLR_DNCH_Pos)                 /* ACTLR: DNCH Mask */

#define ACTLR_QOSEN_Pos                  11U                                     /* ACTLR: QOSEN Position */
#define ACTLR_QOSEN_Msk                  (1UL << ACTLR_QOSEN_Pos)                /* ACTLR: QOSEN Mask */

#define ACTLR_FORA_Pos                   10U                                     /* ACTLR: FORA Position */
#define ACTLR_FORA_Msk                   (1UL << ACTLR_FORA_Pos)                 /* ACTLR: FORA Mask */

#define ACTLR_ITCMECEN_Pos               10U                                     /* ACTLR: ITCMECEN Position */
#define ACTLR_ITCMECEN_Msk               (1UL << ACTLR_ITCMECEN_Pos)             /* ACTLR: ITCMECEN Mask */

#define ACTLR_FWT_Pos                    9U                                      /* ACTLR: FWT Position */
#define ACTLR_FWT_Msk                    (1UL << ACTLR_FWT_Pos)                  /* ACTLR: FWT Mask */

#define ACTLR_DTCMECEN_Pos               9U                                      /* ACTLR: DTCMECEN Position */
#define ACTLR_DTCMECEN_Msk               (1UL << ACTLR_DTCMECEN_Pos)             /* ACTLR: DTCMECEN Mask */

#define ACTLR_FDSNS_Pos                  8U                                      /* ACTLR: FDSNS Position */
#define ACTLR_FDSNS_Msk                  (1UL << ACTLR_FDSNS_Pos)                /* ACTLR: FDSNS Mask */

#define ACTLR_AOW_Pos                    8U                                      /* ACTLR: AOW Position */
#define ACTLR_AOW_Msk                    (1UL << ACTLR_AOW_Pos)                  /* ACTLR: AOW Mask */

#define ACTLR_SMOV_Pos                   7U                                      /* ACTLR: SMOV Position */
#define ACTLR_SMOV_Msk                   (1UL << ACTLR_SMOV_Pos)                 /* ACTLR: SMOV Mask */

#define ACTLR_DILS_Pos                   6U                                      /* ACTLR: DILS Position */
#define ACTLR_DILS_Msk                   (1UL << ACTLR_DILS_Pos)                 /* ACTLR: DILS Mask */

#define ACTLR_SMP_Pos                    6U                                      /* ACTLR: SMP Position */
#define ACTLR_SMP_Msk                    (1UL << ACTLR_SMP_Pos)                  /* ACTLR: SMP Mask */

#define ACTLR_CEC_Pos                    3U                                      /* ACTLR: CEC Position */
#define ACTLR_CEC_Msk                    (7UL << ACTLR_CEC_Pos)                  /* ACTLR: CEC Mask */

#define ACTLR_MRPEN_Pos                  3U                                      /* ACTLR: MRPEN Position */
#define ACTLR_MRPEN_Msk                  (1UL << ACTLR_MRPEN_Pos)                /* ACTLR: MRPEN Mask */

#define ACTLR_B1TCMECEN_Pos              2U                                      /* ACTLR: B1TCMECEN Position */
#define ACTLR_B1TCMECEN_Msk              (1UL << ACTLR_B1TCMECEN_Pos)            /* ACTLR: B1TCMECEN Mask */

#define ACTLR_B0TCMECEN_Pos              1U                                      /* ACTLR: B0TCMECEN Position */
#define ACTLR_B0TCMECEN_Msk              (1UL << ACTLR_B0TCMECEN_Pos)            /* ACTLR: B0TCMECEN Mask */

#define ACTLR_ATCMECEN_Pos               0U                                      /* ACTLR: ATCMECEN Position */
#define ACTLR_ATCMECEN_Msk               (1UL << ACTLR_ATCMECEN_Pos)             /* ACTLR: ATCMECEN Mask */

#define ACTLR_FW_Pos                     0U                                      /* ACTLR: FW Position */
#define ACTLR_FW_Msk                     (1UL << ACTLR_FW_Pos)                   /* ACTLR: FW Mask */

/* DFSR Register Definition */
#define DFSR_FnV_Pos                     16U                                    /* DFSR: FnV Position */
#define DFSR_FnV_Msk                     (1UL << DFSR_FnV_Pos)                  /* DFSR: FnV Mask */

#define DFSR_CM_Pos                      13U                                    /* DFSR: CM Position */
#define DFSR_CM_Msk                      (1UL << DFSR_CM_Pos)                   /* DFSR: CM Mask */

#define DFSR_Ext_Pos                     12U                                    /* DFSR: Ext Position */
#define DFSR_Ext_Msk                     (1UL << DFSR_Ext_Pos)                  /* DFSR: Ext Mask */

#define DFSR_WnR_Pos                     11U                                    /* DFSR: WnR Position */
#define DFSR_WnR_Msk                     (1UL << DFSR_WnR_Pos)                  /* DFSR: WnR Mask */

#define DFSR_FS1_Pos                     10U                                    /* DFSR: FS1 Position */
#define DFSR_FS1_Msk                     (1UL << DFSR_FS1_Pos)                  /* DFSR: FS1 Mask */

#define DFSR_LPAE_Pos                    9U                                     /* DFSR: LPAE Position */
#define DFSR_LPAE_Msk                    (1UL << DFSR_LPAE_Pos)                 /* DFSR: LPAE Mask */

#define DFSR_Domain_Pos                  4U                                     /* DFSR: Domain Position */
#define DFSR_Domain_Msk                  (0xFUL << DFSR_Domain_Pos)             /* DFSR: Domain Mask */

#define DFSR_FS0_Pos                     0U                                     /* DFSR: FS0 Position */
#define DFSR_FS0_Msk                     (0xFUL << DFSR_FS0_Pos)                /* DFSR: FS0 Mask */

#define DFSR_STATUS_Pos                  0U                                     /* DFSR: STATUS Position */
#define DFSR_STATUS_Msk                  (0x3FUL << DFSR_STATUS_Pos)            /* DFSR: STATUS Mask */

/* IFSR Register Definition */
#define IFSR_FnV_Pos                     16U                                    /* IFSR: FnV Position */
#define IFSR_FnV_Msk                     (1UL << IFSR_FnV_Pos)                  /* IFSR: FnV Mask */

#define IFSR_ExT_Pos                     12U                                    /* IFSR: ExT Position */
#define IFSR_ExT_Msk                     (1UL << IFSR_ExT_Pos)                  /* IFSR: ExT Mask */

#define IFSR_FS1_Pos                     10U                                    /* IFSR: FS1 Position */
#define IFSR_FS1_Msk                     (1UL << IFSR_FS1_Pos)                  /* IFSR: FS1 Mask */

#define IFSR_LPAE_Pos                    9U                                     /* IFSR: LPAE Position */
#define IFSR_LPAE_Msk                    (0x1UL << IFSR_LPAE_Pos)               /* IFSR: LPAE Mask */

#define IFSR_FS0_Pos                     0U                                     /* IFSR: FS0 Position */
#define IFSR_FS0_Msk                     (0xFUL << IFSR_FS0_Pos)                /* IFSR: FS0 Mask */

#define IFSR_STATUS_Pos                  0U                                     /* IFSR: STATUS Position */
#define IFSR_STATUS_Msk                  (0x3FUL << IFSR_STATUS_Pos)            /* IFSR: STATUS Mask */

/* ISR Register Definition */
#define ISR_A_Pos                        13U                                    /* ISR: A Position */
#define ISR_A_Msk                        (1UL << ISR_A_Pos)                     /* ISR: A Mask */

#define ISR_I_Pos                        12U                                    /* ISR: I Position */
#define ISR_I_Msk                        (1UL << ISR_I_Pos)                     /* ISR: I Mask */

#define ISR_F_Pos                        11U                                    /* ISR: F Position */
#define ISR_F_Msk                        (1UL << ISR_F_Pos)                     /* ISR: F Mask */

/* DACR Register Defnition */
#define DACR_D_Pos_(n)                   (2U*n)                                 /* DACR: Dn Position */
#define DACR_D_Msk_(n)                   (3UL << DACR_D_Pos_(n))                /* DACR: Dn Mask */
#define DACR_Dn_NOACCESS                 0U                                     /* DACR Dn field: No access */
#define DACR_Dn_CLIENT                   1U                                     /* DACR Dn field: Client */
#define DACR_Dn_MANAGER                  3U                                     /* DACR Dn field: Manager */

/* CPACR Register Defnition */
#define CPACR_CP_Pos                     (20U)                                  /* CPACR: CP Position */
#define CPACR_CP_Msk                     (15UL << CPACR_CP_Pos)                 /* CPACR: CP Mask */

/* PRSELR Register Defnition */
#define PRSELR_REGION_Pos                (0U)                                   /* PRSELR: REGION Position */
#define PRSELR_REGION_16_Msk             (15UL << PRSELR_REGION_Pos)            /* PRSELR: REGION Mask (16 regions) */
#define PRSELR_REGION_24_Msk             (31UL << PRSELR_REGION_Pos)            /* PRSELR: REGION Mask (24 regions) */

/* PRBAR Register Defnition */
#define PRBAR_XN_Pos                     (0U)                                   /* PRBAR: XN Position */
#define PRBAR_XN_Msk                     (1UL << PRBAR_XN_Pos)                  /* PRBAR: XN Mask */
#define PRBAR_AP_Pos                     (1U)                                   /* PRBAR: AP Position */
#define PRBAR_AP_Msk                     (3UL << PRBAR_AP_Pos)                  /* PRBAR: AP Mask */
#define PRBAR_SH_Pos                     (3U)                                   /* PRBAR: SH Position */
#define PRBAR_SH_Msk                     (3UL << PRBAR_SH_Pos)                  /* PRBAR: SH Mask */
#define PRBAR_BASE_Pos                   (6U)                                   /* PRBAR: BASE Position */
#define PRBAR_BASE_Msk                   (0x3FFFFFFUL << PRBAR_BASE_Pos)        /* PRBAR: BASE Mask */

/* PRLAR Register Defnition */
#define PRLAR_EN_Pos                     (0U)                                   /* PRLAR: EN Position */
#define PRLAR_EN_Msk                     (1UL << PRLAR_EN_Pos)                  /* PRLAR: EN Mask */
#define PRLAR_ATTR_IDX_Pos               (1U)                                   /* PRLAR: AttrIndx Position */
#define PRLAR_ATTR_IDX_Msk               (7UL << PRLAR_ATTR_IDX_Pos)            /* PRLAR: AttrIndx Mask */
#define PRLAR_LIMIT_Pos                  (6U)                                   /* PRLAR: LIMIT Position */
#define PRLAR_LIMIT_Msk                  (0x3FFFFFFUL << PRLAR_LIMIT_Pos)       /* PRLAR: LIMIT Mask */

/* MAIR Register Defnition */
#define MAIR_ATTR_Pos(n)                 (8U*n)                                 /* MAIR: Attr Position */
#define MAIR_ATTR_Msk(n)                 (255UL << MAIR_ATTR_Pos(n))            /* MAIR: Attr Mask */

#endif /* ARM_CR_REG_H */
/*******************************************************************************
**                      End of File                                           **
*******************************************************************************/
