/**
 * @file j6e_timer.h
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * @version 1.0
 * @date 2024-11-12
 * 
 * Copyright (c) by iMotion AI. All rights reserved.
 * 
 */

#ifndef _J6E_TIMER_H_
#define _J6E_TIMER_H_

#define GPT_LLD_INSTANCE_SIZE                 (0x10000U)
#define GPT_LLD_CHANNEL_SIZE                  (0x14U)
#define GPT_LLD_BASE_OFFSET(n)                ((0x22310000) + (GPT_LLD_INSTANCE_SIZE) * (n))
#define GPT_LLD_CHANNEL_OFFEST(n, m)          ((GPT_LLD_BASE_OFFSET(n)) + (GPT_LLD_CHANNEL_SIZE) * (m)) 


#define TIMER1_CHNNAL0_BASE                    0x22320000ul
#define TIMER1_CHNNAL0_COUNTER                 0x22320000ul
#define TIMER1_CHNNAL0_CTL                     0x22320008ul           
#define TIMER1_CHNNAL0_R_CLR                   0x2232000Cul  

#define TIMER1_CHNNAL1_BASE                    0x22320014ul
#define TIMER1_CHNNAL1_COUNTER                 0x22320014ul
#define TIMER1_CHNNAL1_CTL                     0x2232001Cul           
#define TIMER1_CHNNAL1_R_CLR                   0x22320020ul  

#define TIMER1_CHNNAL2_BASE                    0x22320028ul
#define TIMER1_CHNNAL2_COUNTER                 0x22320028ul
#define TIMER1_CHNNAL2_CTL                     0x22320030ul           
#define TIMER1_CHNNAL2_R_CLR                   0x22320034ul  

#define TIMER1_CHNNAL3_BASE                    0x2232003Cul
#define TIMER1_CHNNAL3_COUNTER                 0x2232003Cul
#define TIMER1_CHNNAL3_CTL                     0x22320044ul           
#define TIMER1_CHNNAL3_R_CLR                   0x22320048ul 

#define TIMER1_CHNNAL2_ISR                     85
#define TIMER1_CHNNAL3_ISR                     86

#define TIMER_ISR_Core0                        TIMER1_CHNNAL2_ISR
#define TIMER_ISR_Core1                        TIMER1_CHNNAL3_ISR

#endif
