/*======================================================================================================================
 *  COPYRIGHT NOTICE
 *
 *  Copyright (C) 2023-2025 Horizon Robotics, Inc.
 *
 *  All rights reserved.
 *
 *  This program contains proprietary information belonging to Horizon Robotics. Passing on and copying of this
 *  document, use and communication of its contents is not permitted without prior written authorization.
========================================================================================================================
 *  Project              : J6
 *  Platform             : CORTEXR
 *  Peripheral           : ModuleName
 *  Dependencies         : MCU
 *
 *  SW Version           : 1.0.0
 *  Build Version        : 1.0.0
 *  Author               :
 *  Vendor               : Horizon Robotics
 *
 *  Autosar Version      : 4.4.0
 *  Autosar Revision     : ASR_REL_4_4_REV_0000
 *  Autosar Conf.Variant :
======================================================================================================================*/
/**
 * @file Gic_V3.h
 *
 * COPYRIGHT NOTICE
 * Copyright 2023 Horizon Robotics, Inc.
 * All rights reserved.
 */
#ifndef GIC_V3_H_
#define GIC_V3_H_
/*======================================================================================================================
 *                                                   INCLUDE FILES
======================================================================================================================*/
#include <Std_Types.h>
#include <stdint.h>
/*======================================================================================================================
 *                                                   MACRO FUNCTIONS
======================================================================================================================*/

#ifndef __IO
#ifdef __cplusplus
  #define   __I     volatile             /*!< Defines 'read only' permissions                 */
#else
  #define   __I     volatile const       /*!< Defines 'read only' permissions                 */
#endif
#define     __O     volatile             /*!< Defines 'write only' permissions                */
#define     __IO    volatile             /*!< Defines 'read / write' permissions              */
#endif

/** \brief  Get Core ID
 *
 *   GIC_GET_CORE_ID returns the processor identification number for cr52
 */
#if defined ( __GNUC__ ) || defined ( __DCC__ )
    #define ReadMPIDR(a) __asm volatile("mrc p15, 0, %0, c0, c0, 5" : "=r" (a): : "cc")
    #define GIC_GET_CORE_ID()    (__extension__\
                            ({uint32_t tmp; \
                                ReadMPIDR(tmp); \
                                tmp & 0x01UL; \
                            }))
    #define GIC_GET_CLUSTER_ID() (__extension__\
                            ({uint32_t tmp; \
                                ReadMPIDR(tmp); \
                                (tmp >> 8UL) & 0x1UL; \
                                }))
#elif defined (__ghs__)
    extern unsigned int __MRC(__ghs_c_int__ coproc, __ghs_c_int__ opcode1, __ghs_c_int__ CRn, __ghs_c_int__ CRm, __ghs_c_int__ opcode2);
    #define GIC_GET_CORE_ID()    ((__MRC(15, 0, 0, 0, 5)) & 0x01UL)
    #define GIC_GET_CLUSTER_ID() (((__MRC(15, 0, 0, 0, 5)) >> 8UL) & 0x01UL)
#else
    #define GIC_GET_CORE_ID() 0UL
    #define GIC_GET_CLUSTER_ID() 0UL
#endif

/*======================================================================================================================
 *                                          SOURCE FILE VERSION INFORMATION
======================================================================================================================*/


/*======================================================================================================================
 *                                               FILE VERSION CHECKS
======================================================================================================================*/


/*======================================================================================================================
 *                                                       MACROS
======================================================================================================================*/

#ifndef CBAR
#define CBAR (0x22000000u)
#endif

#ifdef FEATURE_INTERRUPT_GIC_PRIO_BITS
#define GIC_PRIO_BITS FEATURE_INTERRUPT_GIC_PRIO_BITS
#else
#define GIC_PRIO_BITS (5)
#endif/* FEATURE_INTERRUPT_GIC_PRIO_BITS */

#define MAX_PER_CORE_IRQ (32)
#ifdef FEATURE_INTERRUPT_IRQ_MAX
#define IRQ_MAX_NUM FEATURE_INTERRUPT_IRQ_MAX
#else
#define R52P_SPI_MAX (960)
#define IRQ_MAX_NUM (MAX_PER_CORE_IRQ) + (R52P_SPI_MAX)
#endif /* FEATURE_INTERRUPT_IRQ_MAX */

/* ----------------------------------------------------------------------------
   -- GICD Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * GICD_Peripheral_Access_Layer GICD Peripheral Access Layer
 */

/** GICD - Register Layout Typedef */
typedef struct {
  __IO uint32_t CTLR;                         /* +0x000 - RW - Distributor Control Register                    */
  __IO uint32_t TYPER;                        /* +0x004 - RO - Interrupt Controller Type Register              */
  __IO uint32_t IIDR;                         /* +0x008 - RO - Distributor Implementer Identification Register */
  __IO uint32_t RESERVED_0[29];               /* Reserved */
  __IO uint32_t IGROUPR[32];                  /* +0x080 - RW - Interrupt Groupt Registers (Security Registers in GICv1) 0 - 31 */
  __IO uint32_t ISENABLER[32];                /* +0x100 - RW - Interrupt Set-Enable Registers 0 - 31 */
  __IO uint32_t ICENABLER[32];                /* +0x180 - RW - Interrupt Clear-Enable Registers 0 - 31 */
  __IO uint32_t ISPENDR[32];                  /* +0x200 - RW - Interrupt Set-Pending Registers 0 - 31 */
  __IO uint32_t ICPENDR[32];                  /* +0x280 - RW - Interrupt Clear-Pending Registers 0 - 31 */
  __IO uint32_t ISACTIVER[32];                /* +0x300 - RW - Interrupt Set-Active Register 0 - 31 */
  __IO uint32_t ICACTIVER[32];                /* +0x380 - RW - Interrupt Clear-Active Register 0 - 31 */
  __IO uint8_t IPRIORITYR[992];               /* +0x400 - RW - Interrupt Priority Registers 0 - 247 */
  __IO uint32_t RESERVED_8[264];              /* Reserved */
  __IO uint32_t ICFGR[62];                    /* +0xC00 - RW - Interrupt Configuration Registers 0 - 61 */
  __IO uint32_t RESERVED_9[5314];             /* Reserved */
  __IO uint64_t IROUTER[992];                 /* +0x6000 - RW - Interrupt Routing Registers 0 - 991 */
  __IO uint32_t RESERVED_10[8244];            /* Reserved */
  __IO uint32_t PIDR4;                        /* +0xFFD0 - RO - Identification Register */
  __IO uint32_t PIDR5;                        /* +0xFFD4 - RO - Identification Register */
  __IO uint32_t PIDR6;                        /* +0xFFD8 - RO - Identification Register */
  __IO uint32_t PIDR7;                        /* +0xFFDC - RO - Identification Register */
  __IO uint32_t PIDR0;                        /* +0xFFE0 - RO - Identification Register */
  __IO uint32_t PIDR1;                        /* +0xFFE4 - RO - Identification Register */
  __IO uint32_t PIDR2;                        /* +0xFFE8 - RO - Identification Register */
  __IO uint32_t PIDR3;                        /* +0xFFEC - RO - Identification Register */
  __IO uint32_t CIDR0;                        /* +0xFFF0 - RO - Component Identification Register */
  __IO uint32_t CIDR1;                        /* +0xFFF4 - RO - Component Identification Register */
  __IO uint32_t CIDR2;                        /* +0xFFF8 - RO - Component Identification Register */
  __IO uint32_t CIDR3;                        /* +0xFFFC - RO - Component Identification Register */
} GICD_Type, *GICD_MemMapPtr;

/* GICD - Peripheral instance base addresses */
/** Peripheral GICD base address */
#define GICD_BASE                                 (CBAR)
/** Peripheral GICD base pointer */
#define GICD                                      ((GICD_Type *) CBAR)
/** Array initializer of GICD peripheral base addresses */
#define GICD_BASE_ADDRS                           { GICD_BASE }
/** Array initializer of GICD peripheral base pointers */
#define GICD_BASE_PTRS                            { GICD }

/* ----------------------------------------------------------------------------
   -- GICD Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * GICD_Register_Masks GICD Register Masks
 */

/*! @name GICD_CTRL - Distributor Control Register */
#define GICD_CTLR_EG0_MASK           (0x1U)
#define GICD_CTLR_EG0_SHIFT          (0U)
#define GICD_CTLR_EG0_WIDTH          (1U)
#define GICD_CTLR_EG0(x)             (((uint32_t)(((uint32_t)(x)) << GICD_CTLR_EG0_SHIFT)) & GICD_CTLR_EG0_MASK)
#define GICD_CTLR_EG1_MASK           (0x2U)
#define GICD_CTLR_EG1_SHIFT          (1U)
#define GICD_CTLR_EG1_WIDTH          (1U)
#define GICD_CTLR_EG1(x)             (((uint32_t)(((uint32_t)(x)) << GICD_CTLR_EG1_SHIFT)) & GICD_CTLR_EG1_MASK)
#define GICD_CTLR_RWP_MASK           (0x80000000U)
#define GICD_CTLR_RWP_SHIFT          (31U)
#define GICD_CTLR_RWP_WIDTH          (1U)
#define GICD_CTLR_RWP(x)             (((uint32_t)(((uint32_t)(x)) << GICD_CTLR_RWP_SHIFT)) & GICD_CTLR_RWP_MASK)
#define GICD_CTRL_SET_IRQ_EDGE_TRIGGERED(x) (GICD->ICFGR[(((uint32_t)x)/(uint32_t)16)] |= \
                                      (uint32_t)0x1u << \
                                      (((((uint32_t)x) % (uint32_t)16) << (uint32_t)1) + (uint32_t)1)) /* x: SPI_ID */

/* end of group GICD_Register_Masks */
/* end of group GICD_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- GICR Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * GICR_Peripheral_Access_Layer GICR Peripheral Access Layer
 */
#ifdef NUMBER_OF_CORES
#define GICR_MAX NUMBER_OF_CORES
#else
#define GIRC_R52P_MAX 2
#define GICR_MAX GIRC_R52P_MAX
#endif
/** GICR - Register Layout Typedef */
typedef struct {
  __IO uint32_t CTLR;                    /* +0x000 - RO - Redistributor Control Register */
  __IO uint32_t IIDR;                    /* +0x004 - RO - Redistributor Implementer Identification Register */
  __IO uint32_t TYPER;                   /* +0x008 - RO - Interrupt Controller Type Register */
  __IO uint32_t RESERVED_0[2];           /* +0x00C - Reserved */
  __IO uint32_t WAKER;                   /* +0x014 - RW - Interrupt Controller Wake Register */
  __IO uint32_t RESERVED_1[26];          /* +0x018 - Reserved */
  __IO uint32_t IGROUPR[32];             /* +0x080 - RW - Interrupt Groupt Registers (Security Registers in GICv1) 0 - 31 */
  __IO uint32_t ISENABLER[32];           /* +0x100 - RW - Interrupt Set-Enable Registers 0 - 31 */
  __IO uint32_t ICENABLER[32];           /* +0x180 - RW - Interrupt Clear-Enable Registers 0 - 31 */
  __IO uint32_t ISPENDR[32];             /* +0x200 - RW - Interrupt Set-Pending Registers 0 - 31 */
  __IO uint32_t ICPENDR[32];             /* +0x280 - RW - Interrupt Clear-Pending Registers 0 - 31 */
  __IO uint32_t ISACTIVER[32];           /* +0x300 - RW - Interrupt Set-Active Register 0 - 31 */
  __IO uint32_t ICACTIVER[32];           /* +0x380 - RW - Interrupt Clear-Active Register 0 - 31 */
  __IO uint8_t IPRIORITYR[992];          /* +0x400 - RW - Interrupt Priority Registers 0 - 247 */
  __IO uint32_t RESERVED_2[264];         /* Reserved */
  __IO uint32_t ICFGR[62];               /* +0xC00 - RW - Interrupt Configuration Registers 0 - 61 */
  __IO uint32_t RESERVED_3[15542];       /* Reserved */
  __IO uint32_t PIDR4;                   /* +0xFFD0 - RO - Identification Register */
  __IO uint32_t PIDR5;                   /* +0xFFD4 - RO - Identification Register */
  __IO uint32_t PIDR6;                   /* +0xFFD8 - RO - Identification Register */
  __IO uint32_t PIDR7;                   /* +0xFFDC - RO - Identification Register */
  __IO uint32_t PIDR0;                   /* +0xFFE0 - RO - Identification Register */
  __IO uint32_t PIDR1;                   /* +0xFFE4 - RO - Identification Register */
  __IO uint32_t PIDR2;                   /* +0xFFE8 - RO - Identification Register */
  __IO uint32_t PIDR3;                   /* +0xFFEC - RO - Identification Register */
  __IO uint32_t CIDR0;                   /* +0xFFF0 - RO - Component Identification Register */
  __IO uint32_t CIDR1;                   /* +0xFFF4 - RO - Component Identification Register */
  __IO uint32_t CIDR2;                   /* +0xFFF8 - RO - Component Identification Register */
  __IO uint32_t CIDR3;                   /* +0xFFFC - RO - Component Identification Register */
} GICR_Type, *GICR_MemMapPtr;

/* GICR - Peripheral instance base addresses */
/** Peripheral GICR base address */
#define GICR_0_CTRL_BASE                                 (GICD_BASE +  0x100000u)
/** Peripheral GICR base pointer */
#define GICR_0_CTRL                                      ((GICR_Type *)GICR_0_CTRL_BASE)
/** Peripheral GICR base address */
#define GICR_0_INT_BASE                                  (GICD_BASE +  0x110000u)
/** Peripheral GICR base pointer */
#define GICR_0_INT                                       ((GICR_Type *)GICR_0_INT_BASE)
/** Peripheral GICR base address */
#define GICR_1_CTRL_BASE                                 (GICD_BASE +  0x120000u)
/** Peripheral GICR base pointer */
#define GICR_1_CTRL                                      ((GICR_Type *)GICR_1_CTRL_BASE)
/** Peripheral GICR base address */
#define GICR_1_INT_BASE                                  (GICD_BASE +  0x130000u)
/** Peripheral GICR base pointer */
#define GICR_1_INT                                       ((GICR_Type *)GICR_1_INT_BASE)
/** Peripheral GICR base address */
#define GICR_2_CTRL_BASE                                 (GICD_BASE +  0x140000u)
/** Peripheral GICR base pointer */
#define GICR_2_CTRL                                      ((GICR_Type *)GICR_2_CTRL_BASE)
/** Peripheral GICR base address */
#define GICR_2_INT_BASE                                  (GICD_BASE +  0x150000u)
/** Peripheral GICR base pointer */
#define GICR_2_INT                                       ((GICR_Type *)GICR_2_INT_BASE)
/** Peripheral GICR base address */
#define GICR_3_CTRL_BASE                                 (GICD_BASE +  0x160000u)
/** Peripheral GICR base pointer */
#define GICR_3_CTRL                                      ((GICR_Type *)GICR_3_CTRL_BASE)
/** Peripheral GICR base address */
#define GICR_3_INT_BASE                                  (GICD_BASE +  0x170000u)
/** Peripheral GICR base pointer */
#define GICR_3_INT                                       ((GICR_Type *)GICR_3_INT_BASE)
/** Array initializer of GICR peripheral base addresses */
#define GICR_CTRL_BASE_ADDRS                             { GICR_0_CTRL_BASE, GICR_1_CTRL_BASE }
/** Array initializer of GICR peripheral base addresses */
#define GICR_INT_BASE_ADDRS                              { GICR_0_INT_BASE, GICR_1_INT_BASE }
/** Array initializer of GICR peripheral base pointers */
#define GICR_CTRL_BASE_PTRS                              { GICR_0_CTRL, GICR_1_CTRL }
/** Array initializer of GICR peripheral base pointers */
#define GICR_INT_BASE_PTRS                               { GICR_0_INT, GICR_1_INT }

/* ----------------------------------------------------------------------------
   -- GICR Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * GICR_Register_Masks GICR Register Masks
 */

/*! @name GICR_WAKER - Interrupt Controller Wake Register */
#define GICR_WAKER_PS_MASK           (0x2U)
#define GICR_WAKER_PS_SHIFT          (1U)
#define GICR_WAKER_PS_WIDTH          (1U)
#define GICR_WAKER_PS(x)             (((uint32_t)(((uint32_t)(x)) << GICR_WAKER_PS_SHIFT)) & GICR_WAKER_PS_MASK)
#define GICR_WAKER_CA_MASK           (0x4U)
#define GICR_WAKER_CA_SHIFT          (2U)
#define GICR_WAKER_CA_WIDTH          (1U)
#define GICR_WAKER_CA(x)             (((uint32_t)(((uint32_t)(x)) << GICR_WAKER_CA_SHIFT)) & GICR_WAKER_CA_MASK)

/* end of group GICR_Register_Masks */
/* end of group GICR_Peripheral_Access_Layer */
/*======================================================================================================================
 *                                        TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
======================================================================================================================*/


/*======================================================================================================================
 *                                                 GLOBAL CONSTANTS
======================================================================================================================*/


/*======================================================================================================================
 *                                                 GLOBAL VARIABLES
======================================================================================================================*/


/*======================================================================================================================
 *                                                FUNCTION DEFINITIONS
======================================================================================================================*/


#endif /* GIC_V3_H_ */
