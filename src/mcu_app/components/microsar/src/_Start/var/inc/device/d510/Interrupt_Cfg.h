/*============================================================================*/
/* Project      = AUTOSAR Renesas CPF MCAL Components                         */
/* Module       = Interrupt_Cfg.h                                             */
/* SW-VERSION   = 1.0.9                                                       */
/*============================================================================*/
/*                                  COPYRIGHT                                 */
/*============================================================================*/
/* Copyright(c) 2021-2023 Renesas Electronics Corporation.                    */
/*============================================================================*/
/* Purpose:                                                                   */
/* Provision of external declaration of APIs and Service IDs.                 */
/*                                                                            */
/*============================================================================*/
/*                                                                            */
/* Unless otherwise agreed upon in writing between your company and           */
/* Renesas Electronics Corporation the following shall apply!                 */
/*                                                                            */
/* Warranty Disclaimer                                                        */
/*                                                                            */
/* There is no warranty of any kind whatsoever granted by Renesas. Any        */
/* warranty is expressly disclaimed and excluded by Renesas, either expressed */
/* or implied, including but not limited to those for non-infringement of     */
/* intellectual property, merchantability and/or fitness for the particular   */
/* purpose.                                                                   */
/*                                                                            */
/* Renesas shall not have any obligation to maintain, service or provide bug  */
/* fixes for the supplied Product(s) and/or the Application.                  */
/*                                                                            */
/* Each User is solely responsible for determining the appropriateness of     */
/* using the Product(s) and assumes all risks associated with its exercise    */
/* of rights under this Agreement, including, but not limited to the risks    */
/* and costs of program errors, compliance with applicable laws, damage to    */
/* or loss of data, programs or equipment, and unavailability or              */
/* interruption of operations.                                                */
/*                                                                            */
/* Limitation of Liability                                                    */
/*                                                                            */
/* In no event shall Renesas be liable to the User for any incidental,        */
/* consequential, indirect, or punitive damage (including but not limited     */
/* to lost profits) regardless of whether such liability is based on breach   */
/* of contract, tort, strict liability, breach of warranties, failure of      */
/* essential purpose or otherwise and even if advised of the possibility of   */
/* such damages. Renesas shall not be liable for any services or products     */
/* provided by third party vendors, developers or consultants identified or   */
/* referred to the User by Renesas in connection with the Product(s) and/or   */
/* the Application.                                                           */
/*                                                                            */
/*============================================================================*/
/* Environment:                                                               */
/*              Devices:        CPF                                           */
/*============================================================================*/

/*******************************************************************************
**                      Revision Control History                              **
*******************************************************************************/
/*
 * 1.0.10:     15/02/2023 :      Added macro MFIS_xIICR1_ISR for ICCOM
 *                               Add macro SPI_162_ISR
 * 1.0.9:      02/11/2022 :      Added macro ETH_AVBn_DATA_ISR,
 *                               ETH_AVBn_ERR_ISR, ETH_AVBn_MAC_ISR (n = 0..2)
 * 1.0.8:      14/10/2022 :      Added macro ETH_CTRL_ENABLE_TX_INTERRUPT and
 *                               ETH_CTRL_ENABLE_RX_INTERRUPT == STD_ON for
 *                               ETH_AVBn_DATA_ISR (n=0..2).
 *             12/10/2022 :      Removed ETH_AVBn_CH00_ISR to ETH_AVBn_CH21_ISR
 *                               (n=0..2) unuse.
 *                               Modified ETH_AVBn_CH22_ISR to ETH_AVBn_DATA_ISR
 *                               for handle all transmission interrupt,
 *                               reception interrupt and timeStamp interrupt
 *                               updated for line 0A or Line 2A (n=0..2)
 *                               by using common ISR.
 *                               Modified ETH_AVBn_CH23_ISR to
 *                               ETH_AVBn_ERROR_ISR for handle error interrupt
 *                               line 1B (n=0..2).
 *                               Modified ETH_AVBn_CH24_ISR to ETH_AVBn_MAC_ISR
 *                               for handle MAC interrupt line 3 (n=0..2).
 * 1.0.7:      15/08/2022 :      Add macro CAN_PUBLIC_ICOM_SUPPORT for
 *                               SPI_413_ISR.
 * 1.0.6:      15/07/2022 :      Change SPI_289_ISR to SPI_287_ISR for ICCOM
 * 1.0.5:      24/06/2022 :      Add definition from SPI_082_ISR upto
 *                               SPI_115_ISR to support SPI SYS-DMAC interrupts
 * 1.0.4:      24/05/2022 :      Add macro ETHAVB0 for SPI_335_ISR to
 *                               SPI_359_ISR
 *                               Update Interrupt Config for CRC module
 *                               Map ISR CAN_GLOBAL_ISR to interrupt address
 * 1.0.3:      18/04/2022 :      Update Interrupt Config for ETH AVB (V4H),
 *                               SPI, WDG and MCU module
 *                               Map ISR CAN_CHANNEL_ISR to interrupt address
 * 1.0.2:      10/02/2022 :      Update Interrupt Config for CAN, ETH, THS, GPT,
 *                               IPMMU, EMM and RFSO module
 * 1.0.1:      14/01/2022 :      Update Interrupt Config  for I2C module
 * 1.0.0:      06/11/2021 :      Initial Version
 */
/******************************************************************************/

#ifndef INTERRUPT_CFG_H
#define INTERRUPT_CFG_H

/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/
#if defined(CAN_MODULE_SAMPLE) || defined(GLOBAL_MODULE_TEST) || \
  defined(CAN_MODULE)
#define CAN_INT_TABLE
#endif

#if defined(ETH_MODULE_SAMPLE) || defined(ETH_MODULE_TEST) || \
  defined(ETH_RSW2_MODULE_SAMPLE)
#define ETH_INT_TABLE
#endif

#if defined(CDDICCOM_MODULE_SAMPLE) || defined(CDDICCOM_MODULE_TEST)
#define CDDICCOM_INT_TABLE
#endif

#if defined(CDDIIC_MODULE_SAMPLE) || defined(CDDIIC_MODULE_TEST)
#define CDDIIC_INT_TABLE
#endif

#if defined(CDDTHS_MODULE_SAMPLE) || defined(CDDTHS_MODULE_TEST)
#define CDDTHS_INT_TABLE
#endif

#if defined(CDDIPMMU_MODULE_SAMPLE) || defined(CDDIPMMU_MODULE_TEST)
#define CDDIPMMU_INT_TABLE
#endif

#if defined(GPT_MODULE_SAMPLE) || defined(GPT_MODULE_TEST)
#define GPT_INT_TABLE
#endif

#if defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST)
#define CDDEMM_INT_TABLE
#endif

#if defined(CDDRFSO_MODULE_SAMPLE) || defined(CDDRFSO_MODULE_TEST)
#define CDDRFSO_INT_TABLE
#endif

#if defined(MCU_MODULE_SAMPLE)
#define MCU_INT_TABLE
#endif

#if defined(SPI_MODULE_SAMPLE) || defined(SPI_MODULE_TEST)
#define SPI_INT_TABLE
#endif

#if defined(CDDCRC_MODULE_SAMPLE) || defined(CDDCRC_MODULE_TEST)
#define CDDCRC_INT_TABLE
#endif
/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/
#ifdef CAN_INT_TABLE
#include "App_Can_Common_Sample.h"
#include "Can.h"
#include "Can_Irq.h"
#endif

#ifdef ETH_INT_TABLE
#include "Eth.h"
#ifdef ETH_AVB_IF
#include "Eth_AVB_Irq.h"
#else
#include "Eth_RSW2_Irq.h"
#endif
#endif

#ifdef CDDICCOM_INT_TABLE
#include "CDD_Iccom.h"
#include "App_CDD_ICCOM_Common_Sample.h"
#include "CDD_Iccom_MFIS_Irq.h"
#endif

#ifdef CDDIIC_INT_TABLE
#include "CDD_Iic.h"
#include "CDD_Iic_Irq.h"
#endif

#ifdef CDDTHS_INT_TABLE
#include "CDD_Ths.h"
#include "App_CDD_THS_Common_Sample.h"
#endif

#ifdef CDDIPMMU_INT_TABLE
#include "CDD_Ipmmu.h"
#include "CDD_Ipmmu_Irq.h"
#endif

#ifdef GPT_INT_TABLE
#include "Gpt_TMU_Irq.h"
#include "Gpt.h"
#endif

#ifdef CDDEMM_MODULE_SAMPLE
#include "CDD_Emm.h"
#include "CDD_Emm_Irq.h"
#endif

#ifdef CDDRFSO_INT_TABLE
#include "CDD_Rfso.h"
#include "CDD_Rfso_Irq.h"
#include "App_CDD_RFSO_V4H_Sample.h"
#endif

#ifdef MCU_INT_TABLE
#include "App_MCU_V4H_Sample.h"
#endif

#ifdef SPI_INT_TABLE
#include "Spi.h"
#include "Spi_MSIOF_Irq.h"
#include "Spi_SYSDMAC_Irq.h"
#endif

#ifdef WDG_MODULE_SAMPLE
#include "App_WDG_Common_Sample.h"
#endif

#ifdef WDG_MODULE_TEST
#include "WDG_DIT_Test_Specific.h"
#endif

#ifdef CDDCRC_INT_TABLE
#include "CDD_Crc.h"
#include "CDD_Crc_Irq.h"
#endif
/*******************************************************************************
**                      ISR Definition                                        **
*******************************************************************************/
/* CAN */
#ifdef CAN_INT_TABLE
#define SPI_289_ISR Timer_Task /* 289 */
#if (CAN_ISR_CATEGORY_2 == STD_ON)
/* Defines the CAT2 interrupt mapping */
#else
#if ((defined CAN_CONTROLLER_TX_INTERRUPT_ON) || (defined CAN_CONTROLLER_RX_INTERRUPT_ON) || \
 (defined CAN_CONTROLLER_BUSOFF_INTERRUPT_ON))
#define SPI_412_ISR CAN_CHANNEL_ISR /* 412 */
#endif

#if ((CAN_RSCAN0_RXFIFO_INTERRUPT == STD_ON) || (CAN_PUBLIC_ICOM_SUPPORT == STD_ON))
#define SPI_413_ISR CAN_GLOBAL_ISR /* 413 */
#endif

#endif
#endif

/* ETH */
#ifdef ETH_INT_TABLE
#if defined ETH_AVB_IF
  #if (ETH_ISR_CATEGORY_2 == STD_OFF)
  /* AVB0 */
  #if (ETH_AVB0_DATA_ISR == STD_ON)
  #define SPI_357_ISR ETH_AVB0DATAISR
  #endif /* (ETH_AVB0_DATA_ISR == STD_ON) */

  #if (ETH_AVB0_ERR_ISR == STD_ON)
  #define SPI_358_ISR ETH_AVB0ERRISR
  #endif /* (ETH_AVB0_ERR_ISR == STD_ON) */

  #if (ETH_AVB0_MAC_ISR == STD_ON)
  #define SPI_359_ISR ETH_AVB0MACISR
  #endif /* (ETH_AVB0_MAC_ISR == STD_ON) */

  /* AVB1 */
  #if (ETH_AVB1_DATA_ISR == STD_ON)
  #define SPI_382_ISR ETH_AVB1DATAISR
  #endif /* (ETH_AVB1_DATA_ISR == STD_ON) */

  #if (ETH_AVB1_ERR_ISR == STD_ON)
  #define SPI_383_ISR ETH_AVB1ERRISR
  #endif /* (ETH_AVB1_ERR_ISR == STD_ON) */

  #if (ETH_AVB1_MAC_ISR == STD_ON)
  #define SPI_384_ISR ETH_AVB1MACISR
  #endif /* (ETH_AVB1_MAC_ISR == STD_ON) */

  /* AVB2 */
  #if (ETH_AVB2_DATA_ISR == STD_ON)
  #define SPI_407_ISR ETH_AVB2DATAISR
  #endif /* (ETH_AVB2_DATA_ISR == STD_ON) */

  #if (ETH_AVB2_ERR_ISR == STD_ON)
  #define SPI_408_ISR ETH_AVB2ERRISR
  #endif /* (ETH_AVB2_ERR_ISR == STD_ON) */

    #if (ETH_AVB2_MAC_ISR == STD_ON)
  #define SPI_409_ISR ETH_AVB2MACISR
  #endif /* (ETH_AVB0_MAC_ISR == STD_ON) */
  #endif
#endif

#if defined ETHTSN
  #if (ETH_GWCA0_DATA_ISR == STD_ON)
    #define SPI_280_ISR ETH_GWCA0DISISR
  #endif
  #if (ETH_GWCA1_DATA_ISR == STD_ON)
    #define SPI_288_ISR ETH_GWCA1DISISR
  #endif
#endif
#endif

/* CDDICCOM */
#ifdef CDDICCOM_INT_TABLE
#if (CDDICCOM_CR_NUMBER == 0)
#define SPI_124_ISR MFIS_xIICR0_ISR
#define SPI_126_ISR MFIS_xIICR1_ISR
#elif (CDDICCOM_CR_NUMBER == 1)
#define SPI_917_ISR MFIS_xIICR0_ISR
#define SPI_919_ISR MFIS_xIICR1_ISR
#else
#define SPI_132_ISR MFIS_xIICR0_ISR
#define SPI_134_ISR MFIS_xIICR1_ISR
#endif
#define SPI_287_ISR Appl_Scheduler_Task
#endif

/* CDDIIC */
/* I2C0 */
#ifdef CDDIIC_INT_TABLE
#if (CDDIIC_HW_IIC0_USED == STD_ON)
#define SPI_610_ISR IIC_HW_CH0_ISR
#endif
/* I2C1 */
#if (CDDIIC_HW_IIC1_USED == STD_ON)
#define SPI_611_ISR IIC_HW_CH1_ISR
#endif
/* I2C2 */
#if (CDDIIC_HW_IIC2_USED == STD_ON)
#define SPI_612_ISR IIC_HW_CH2_ISR
#endif
/* I2C3 */
#if (CDDIIC_HW_IIC3_USED == STD_ON)
#define SPI_613_ISR IIC_HW_CH3_ISR
#endif
/* I2C4 */
#if (CDDIIC_HW_IIC4_USED == STD_ON)
#define SPI_614_ISR IIC_HW_CH4_ISR
#endif
/* I2C5 */
#if (CDDIIC_HW_IIC5_USED == STD_ON)
#define SPI_615_ISR IIC_HW_CH5_ISR
#endif
#endif

/* THS */
#ifdef CDDTHS_INT_TABLE
#if (CDD_THS_THERMAL_INTERRUPTION == STD_ON)
#define SPI_156_ISR ECM_Error_Handler
#endif
#endif

/* IPMMU */
#ifdef CDDIPMMU_INT_TABLE
#define SPI_210_ISR CDDIPMMU_ERROR_ISR
#endif

/* GPT */
#ifdef GPT_INT_TABLE
#if (GPT_TMU_CH00_ISR_API == STD_ON)
#if (defined (TEST_GPT_ETC_105) || defined (TEST_GPT_ETC_054) || defined (TEST_GPT_ETC_091))
#define SPI_289_ISR DUMMY_TMU_CH00_ISR
#elif (defined (TEST_GPT_ETC_SR_208))
#define SPI_289_ISR DUMMY_TMU_TESTCASE_208_CH00_ISR
#else
#define SPI_289_ISR TMU_CH00_ISR
#endif
#endif
#endif

#ifdef GPT_INT_TABLE
#if (GPT_TMU_CH01_ISR_API == STD_ON)
#if(defined (TEST_GPT_ETC_SR_208))
#define SPI_290_ISR DUMMY_TMU_TESTCASE_208_CH01_ISR
#else
#define SPI_290_ISR TMU_CH01_ISR
#endif
#endif
#endif

#ifdef GPT_INT_TABLE
#if (GPT_TMU_CH02_ISR_API == STD_ON)
#if(defined (TEST_GPT_ETC_SR_208))
#define SPI_291_ISR DUMMY_TMU_TESTCASE_208_CH02_ISR
#else
#define SPI_291_ISR TMU_CH02_ISR
#endif
#endif
#endif

#ifdef GPT_INT_TABLE
#if (GPT_TMU_CH03_ISR_API == STD_ON)
#if (defined TEST_GPT_ETC_105)
#define SPI_292_ISR DUMMY_TMU_CH03_ISR
#elif(defined (TEST_GPT_ETC_SR_208))
#define SPI_292_ISR DUMMY_TMU_TESTCASE_208_CH03_ISR
#else
#define SPI_292_ISR TMU_CH03_ISR
#endif
#endif
#endif

#ifdef  GPT_INT_TABLE
#if (GPT_TMU_CH04_ISR_API == STD_ON)
#if(defined (TEST_GPT_ETC_SR_208))
#define SPI_293_ISR DUMMY_TMU_TESTCASE_208_CH04_ISR
#else
#define SPI_293_ISR TMU_CH04_ISR
#endif
#endif
#endif

#ifdef GPT_INT_TABLE
#if (GPT_TMU_CH05_ISR_API == STD_ON)
#if(defined (TEST_GPT_ETC_SR_208))
#define SPI_294_ISR DUMMY_TMU_TESTCASE_208_CH05_ISR
#else
#define SPI_294_ISR TMU_CH05_ISR
#endif
#endif
#endif

#ifdef GPT_INT_TABLE
#if (GPT_TMU_CH06_ISR_API == STD_ON)
#if(defined (TEST_GPT_ETC_SR_208))
#define SPI_296_ISR DUMMY_TMU_TESTCASE_208_CH06_ISR
#else
#define SPI_296_ISR TMU_CH06_ISR
#endif
#endif
#endif

#ifdef GPT_INT_TABLE
#if (GPT_TMU_CH07_ISR_API == STD_ON)
#if(defined (TEST_GPT_ETC_SR_208))
#define SPI_297_ISR DUMMY_TMU_TESTCASE_208_CH07_ISR
#else
#define SPI_297_ISR TMU_CH07_ISR
#endif
#endif
#endif

#ifdef GPT_INT_TABLE
#if (GPT_TMU_CH08_ISR_API == STD_ON)
#if(defined (TEST_GPT_ETC_SR_208))
#define SPI_298_ISR DUMMY_TMU_TESTCASE_208_CH08_ISR
#else
#define SPI_298_ISR TMU_CH08_ISR
#endif
#endif
#endif

#ifdef GPT_INT_TABLE
#if (GPT_TMU_CH09_ISR_API == STD_ON)
#if(defined (TEST_GPT_ETC_SR_208))
#define SPI_300_ISR DUMMY_TMU_TESTCASE_208_CH09_ISR
#else
#define SPI_300_ISR TMU_CH09_ISR
#endif
#endif
#endif

#ifdef GPT_INT_TABLE
#if (GPT_TMU_CH10_ISR_API == STD_ON)
#if(defined (TEST_GPT_ETC_SR_208))
#define SPI_301_ISR DUMMY_TMU_TESTCASE_208_CH10_ISR
#else
#define SPI_301_ISR TMU_CH10_ISR
#endif
#endif
#endif

#ifdef GPT_INT_TABLE
#if (GPT_TMU_CH11_ISR_API == STD_ON)
#if(defined (TEST_GPT_ETC_SR_208))
#define SPI_302_ISR DUMMY_TMU_TESTCASE_208_CH11_ISR
#else
#define SPI_302_ISR TMU_CH11_ISR
#endif
#endif
#endif

#ifdef GPT_INT_TABLE
#if (GPT_TMU_CH12_ISR_API == STD_ON)
#if(defined (TEST_GPT_ETC_SR_208))
#define SPI_304_ISR DUMMY_TMU_TESTCASE_208_CH12_ISR
#else
#define SPI_304_ISR TMU_CH12_ISR
#endif
#endif
#endif

#ifdef GPT_INT_TABLE
#if (GPT_TMU_CH13_ISR_API == STD_ON)
#if(defined (TEST_GPT_ETC_SR_208))
#define SPI_305_ISR DUMMY_TMU_TESTCASE_208_CH13_ISR
#else
#define SPI_305_ISR TMU_CH13_ISR
#endif
#endif
#endif

#ifdef GPT_INT_TABLE
#if (GPT_TMU_CH14_ISR_API == STD_ON)
#if(defined (TEST_GPT_ETC_SR_208))
#define SPI_306_ISR DUMMY_TMU_TESTCASE_208_CH14_ISR
#else
#define SPI_306_ISR TMU_CH14_ISR
#endif
#endif
#endif

/* CDDEMM */
#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN8_USE == STD_ON)
#define SPI_148_ISR CDDEMM_DOMAIN8_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN0_USE == STD_ON)
#define SPI_140_ISR CDDEMM_DOMAIN0_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN1_USE == STD_ON)
#define SPI_141_ISR CDDEMM_DOMAIN1_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN2_USE == STD_ON)
#define SPI_142_ISR CDDEMM_DOMAIN2_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN3_USE == STD_ON)
#define SPI_143_ISR CDDEMM_DOMAIN3_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN4_USE == STD_ON)
#define SPI_144_ISR CDDEMM_DOMAIN4_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN5_USE == STD_ON)
#define SPI_145_ISR CDDEMM_DOMAIN5_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN6_USE == STD_ON)
#define SPI_146_ISR CDDEMM_DOMAIN6_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN7_USE == STD_ON)
#define SPI_147_ISR CDDEMM_DOMAIN7_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN9_USE == STD_ON)
#define SPI_149_ISR CDDEMM_DOMAIN9_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN10_USE == STD_ON)
#define SPI_150_ISR CDDEMM_DOMAIN10_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN11_USE == STD_ON)
#define SPI_151_ISR CDDEMM_DOMAIN11_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN12_USE == STD_ON)
#define SPI_152_ISR CDDEMM_DOMAIN12_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN13_USE == STD_ON)
#define SPI_153_ISR CDDEMM_DOMAIN13_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN16_USE == STD_ON)
#define SPI_156_ISR CDDEMM_DOMAIN16_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN17_USE == STD_ON)
#define SPI_157_ISR CDDEMM_DOMAIN17_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN18_USE == STD_ON)
#define SPI_158_ISR CDDEMM_DOMAIN18_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN19_USE == STD_ON)
#define SPI_159_ISR CDDEMM_DOMAIN19_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN20_USE == STD_ON)
#define SPI_160_ISR CDDEMM_DOMAIN20_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN21_USE == STD_ON)
#define SPI_161_ISR CDDEMM_DOMAIN21_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN22_USE == STD_ON)
#define SPI_162_ISR CDDEMM_DOMAIN22_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN23_USE == STD_ON)
#define SPI_163_ISR CDDEMM_DOMAIN23_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN24_USE == STD_ON)
#define SPI_164_ISR CDDEMM_DOMAIN24_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN25_USE == STD_ON)
#define SPI_165_ISR CDDEMM_DOMAIN25_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN26_USE == STD_ON)
#define SPI_166_ISR CDDEMM_DOMAIN26_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN27_USE == STD_ON)
#define SPI_167_ISR CDDEMM_DOMAIN27_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN28_USE == STD_ON)
#define SPI_168_ISR CDDEMM_DOMAIN28_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN29_USE == STD_ON)
#define SPI_169_ISR CDDEMM_DOMAIN29_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN30_USE == STD_ON)
#define SPI_170_ISR CDDEMM_DOMAIN30_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN31_USE == STD_ON)
#define SPI_171_ISR CDDEMM_DOMAIN31_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN32_USE == STD_ON)
#define SPI_172_ISR CDDEMM_DOMAIN32_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN33_USE == STD_ON)
#define SPI_173_ISR CDDEMM_DOMAIN33_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN34_USE == STD_ON)
#define SPI_174_ISR CDDEMM_DOMAIN34_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN35_USE == STD_ON)
#define SPI_175_ISR CDDEMM_DOMAIN35_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN36_USE == STD_ON)
#define SPI_176_ISR CDDEMM_DOMAIN36_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN38_USE == STD_ON)
#define SPI_178_ISR CDDEMM_DOMAIN38_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN39_USE == STD_ON)
#define SPI_179_ISR CDDEMM_DOMAIN39_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN40_USE == STD_ON)
#define SPI_180_ISR CDDEMM_DOMAIN40_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN41_USE == STD_ON)
#define SPI_181_ISR CDDEMM_DOMAIN41_ISR
#endif
#endif

#if (defined(CDDEMM_MODULE_SAMPLE) || defined(CDDEMM_MODULE_TEST))
#if (CDDEMM_DOMAIN42_USE == STD_ON)
#define SPI_182_ISR CDDEMM_DOMAIN42_ISR
#endif
#endif

/* CDDRFSO */
#ifdef CDDRFSO_INT_TABLE
#define SPI_227_ISR CDDRFSO_CHANNEL0_ISR
#endif

#ifdef CDDRFSO_INT_TABLE
#define SPI_228_ISR CDDRFSO_CHANNEL1_ISR
#endif

#ifdef CDDRFSO_INT_TABLE
#define SPI_229_ISR CDDRFSO_CHANNEL2_ISR
#endif

#ifdef CDDRFSO_INT_TABLE
#define SPI_230_ISR CDDRFSO_CHANNEL3_ISR
#endif

#ifdef CDDRFSO_INT_TABLE
#define SPI_231_ISR CDDRFSO_CHANNEL4_ISR
#endif

#ifdef CDDRFSO_INT_TABLE
#define SPI_232_ISR CDDRFSO_CHANNEL5_ISR
#endif

#ifdef CDDRFSO_INT_TABLE
#define SPI_233_ISR CDDRFSO_CHANNEL6_ISR
#endif

#ifdef CDDRFSO_INT_TABLE
#define SPI_234_ISR CDDRFSO_CHANNEL7_ISR
#endif

#ifdef CDDRFSO_INT_TABLE
#define SPI_235_ISR CDDRFSO_CHANNEL8_ISR
#endif

#ifdef CDDRFSO_INT_TABLE
#define SPI_236_ISR CDDRFSO_CHANNEL9_ISR
#endif

#ifdef CDDRFSO_INT_TABLE
#define SPI_237_ISR CDDRFSO_CHANNEL10_ISR
#endif

#ifdef CDDRFSO_INT_TABLE
#define SPI_289_ISR TMU_CH00_ISR
#endif

#ifdef CDDRFSO_INT_TABLE
#define SPI_290_ISR TMU_CH01_ISR
#endif

#ifdef CDDRFSO_INT_TABLE
#define SPI_291_ISR TMU_CH02_ISR
#endif

#ifdef CDDRFSO_INT_TABLE
#define SPI_292_ISR TMU_CH03_ISR
#endif

/* MCU */
#ifdef MCU_INT_TABLE
#define SPI_289_ISR TMU_CH00_ISR
#endif

/* SPI */
#ifdef SPI_INT_TABLE
#if (SPI_MSIOF0_ISR_API == STD_ON)
#define SPI_239_ISR SPI_MSIOF0_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_MSIOF1_ISR_API == STD_ON)
#define SPI_240_ISR SPI_MSIOF1_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_MSIOF2_ISR_API == STD_ON)
#define SPI_241_ISR SPI_MSIOF2_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_MSIOF3_ISR_API == STD_ON)
#define SPI_242_ISR SPI_MSIOF3_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_MSIOF4_ISR_API == STD_ON)
#define SPI_243_ISR SPI_MSIOF4_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_MSIOF5_ISR_API == STD_ON)
#define SPI_244_ISR SPI_MSIOF5_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA0_00_ISR_API == STD_ON)
#define SPI_082_ISR SPI_DMA0_00_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA0_01_ISR_API == STD_ON)
#define SPI_083_ISR SPI_DMA0_01_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA0_02_ISR_API == STD_ON)
#define SPI_084_ISR SPI_DMA0_02_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA0_03_ISR_API == STD_ON)
#define SPI_085_ISR SPI_DMA0_03_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA0_04_ISR_API == STD_ON)
#define SPI_086_ISR SPI_DMA0_04_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA0_05_ISR_API == STD_ON)
#define SPI_087_ISR SPI_DMA0_05_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA0_06_ISR_API == STD_ON)
#define SPI_088_ISR SPI_DMA0_06_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA0_07_ISR_API == STD_ON)
#define SPI_089_ISR SPI_DMA0_07_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA0_08_ISR_API == STD_ON)
#define SPI_090_ISR SPI_DMA0_08_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA0_09_ISR_API == STD_ON)
#define SPI_091_ISR SPI_DMA0_09_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA0_10_ISR_API == STD_ON)
#define SPI_092_ISR SPI_DMA0_10_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA0_11_ISR_API == STD_ON)
#define SPI_093_ISR SPI_DMA0_11_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA0_12_ISR_API == STD_ON)
#define SPI_094_ISR SPI_DMA0_12_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA0_13_ISR_API == STD_ON)
#define SPI_095_ISR SPI_DMA0_13_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA0_14_ISR_API == STD_ON)
#define SPI_096_ISR SPI_DMA0_14_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA0_15_ISR_API == STD_ON)
#define SPI_097_ISR SPI_DMA0_15_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA1_00_ISR_API == STD_ON)
#define SPI_100_ISR SPI_DMA1_00_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA1_01_ISR_API == STD_ON)
#define SPI_101_ISR SPI_DMA1_01_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA1_02_ISR_API == STD_ON)
#define SPI_102_ISR SPI_DMA1_02_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA1_03_ISR_API == STD_ON)
#define SPI_103_ISR SPI_DMA1_03_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA1_04_ISR_API == STD_ON)
#define SPI_104_ISR SPI_DMA1_04_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA1_05_ISR_API == STD_ON)
#define SPI_105_ISR SPI_DMA1_05_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA1_06_ISR_API == STD_ON)
#define SPI_106_ISR SPI_DMA1_06_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA1_07_ISR_API == STD_ON)
#define SPI_107_ISR SPI_DMA1_07_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA1_08_ISR_API == STD_ON)
#define SPI_108_ISR SPI_DMA1_08_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA1_09_ISR_API == STD_ON)
#define SPI_109_ISR SPI_DMA1_09_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA1_10_ISR_API == STD_ON)
#define SPI_110_ISR SPI_DMA1_10_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA1_11_ISR_API == STD_ON)
#define SPI_111_ISR SPI_DMA1_11_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA1_12_ISR_API == STD_ON)
#define SPI_112_ISR SPI_DMA1_12_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA1_13_ISR_API == STD_ON)
#define SPI_113_ISR SPI_DMA1_13_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA1_14_ISR_API == STD_ON)
#define SPI_114_ISR SPI_DMA1_14_ISR
#endif
#endif

#ifdef SPI_INT_TABLE
#if (SPI_DMA1_15_ISR_API == STD_ON)
#define SPI_115_ISR SPI_DMA1_15_ISR
#endif
#endif

/* WDG */
#if (defined (WDG_MODULE_SAMPLE) || defined (WDG_MODULE_TEST))
#define SPI_292_ISR TMU3_Isr
#endif

/* CRC */
#if (defined (CDDCRC_MODULE_SAMPLE) || defined (CDDCRC_MODULE_TEST))
/* RT-DMAC0 */
#if (CDDCRC_DMA00_USED == STD_ON)
#define SPI_010_ISR CDDCRC_DMA00_ISR
#endif
#if (CDDCRC_DMA01_USED == STD_ON)
#define SPI_011_ISR CDDCRC_DMA01_ISR
#endif
#if (CDDCRC_DMA02_USED == STD_ON)
#define SPI_012_ISR CDDCRC_DMA02_ISR
#endif
#if (CDDCRC_DMA03_USED == STD_ON)
#define SPI_013_ISR CDDCRC_DMA03_ISR
#endif
#if (CDDCRC_DMA04_USED == STD_ON)
#define SPI_014_ISR CDDCRC_DMA04_ISR
#endif
#if (CDDCRC_DMA05_USED == STD_ON)
#define SPI_015_ISR CDDCRC_DMA05_ISR
#endif
#if (CDDCRC_DMA06_USED == STD_ON)
#define SPI_016_ISR CDDCRC_DMA06_ISR
#endif
#if (CDDCRC_DMA07_USED == STD_ON)
#define SPI_017_ISR CDDCRC_DMA07_ISR
#endif
#if (CDDCRC_DMA08_USED == STD_ON)
#define SPI_018_ISR CDDCRC_DMA08_ISR
#endif
#if (CDDCRC_DMA09_USED == STD_ON)
#define SPI_019_ISR CDDCRC_DMA09_ISR
#endif
#if (CDDCRC_DMA10_USED == STD_ON)
#define SPI_020_ISR CDDCRC_DMA10_ISR
#endif
#if (CDDCRC_DMA11_USED == STD_ON)
#define SPI_021_ISR CDDCRC_DMA11_ISR
#endif
#if (CDDCRC_DMA12_USED == STD_ON)
#define SPI_022_ISR CDDCRC_DMA12_ISR
#endif
#if (CDDCRC_DMA13_USED == STD_ON)
#define SPI_023_ISR CDDCRC_DMA13_ISR
#endif
#if (CDDCRC_DMA14_USED == STD_ON)
#define SPI_024_ISR CDDCRC_DMA14_ISR
#endif
#if (CDDCRC_DMA15_USED == STD_ON)
#define SPI_025_ISR CDDCRC_DMA15_ISR
#endif
/* RT-DMAC1 */
#if (CDDCRC_DMA16_USED == STD_ON)
#define SPI_028_ISR CDDCRC_DMA16_ISR
#endif
#if (CDDCRC_DMA17_USED == STD_ON)
#define SPI_029_ISR CDDCRC_DMA17_ISR
#endif
#if (CDDCRC_DMA18_USED == STD_ON)
#define SPI_030_ISR CDDCRC_DMA18_ISR
#endif
#if (CDDCRC_DMA19_USED == STD_ON)
#define SPI_031_ISR CDDCRC_DMA19_ISR
#endif
#if (CDDCRC_DMA20_USED == STD_ON)
#define SPI_032_ISR CDDCRC_DMA20_ISR
#endif
#if (CDDCRC_DMA21_USED == STD_ON)
#define SPI_033_ISR CDDCRC_DMA21_ISR
#endif
#if (CDDCRC_DMA22_USED == STD_ON)
#define SPI_034_ISR CDDCRC_DMA22_ISR
#endif
#if (CDDCRC_DMA23_USED == STD_ON)
#define SPI_035_ISR CDDCRC_DMA23_ISR
#endif
#if (CDDCRC_DMA24_USED == STD_ON)
#define SPI_036_ISR CDDCRC_DMA24_ISR
#endif
#if (CDDCRC_DMA25_USED == STD_ON)
#define SPI_037_ISR CDDCRC_DMA25_ISR
#endif
#if (CDDCRC_DMA26_USED == STD_ON)
#define SPI_038_ISR CDDCRC_DMA26_ISR
#endif
#if (CDDCRC_DMA27_USED == STD_ON)
#define SPI_039_ISR CDDCRC_DMA27_ISR
#endif
#if (CDDCRC_DMA28_USED == STD_ON)
#define SPI_040_ISR CDDCRC_DMA28_ISR
#endif
#if (CDDCRC_DMA29_USED == STD_ON)
#define SPI_041_ISR CDDCRC_DMA29_ISR
#endif
#if (CDDCRC_DMA30_USED == STD_ON)
#define SPI_042_ISR CDDCRC_DMA30_ISR
#endif
#if (CDDCRC_DMA31_USED == STD_ON)
#define SPI_043_ISR CDDCRC_DMA31_ISR
#endif
/* RT-DMAC2 */
#if (CDDCRC_DMA32_USED == STD_ON)
#define SPI_046_ISR CDDCRC_DMA32_ISR
#endif
#if (CDDCRC_DMA33_USED == STD_ON)
#define SPI_047_ISR CDDCRC_DMA33_ISR
#endif
#if (CDDCRC_DMA34_USED == STD_ON)
#define SPI_048_ISR CDDCRC_DMA34_ISR
#endif
#if (CDDCRC_DMA35_USED == STD_ON)
#define SPI_049_ISR CDDCRC_DMA35_ISR
#endif
#if (CDDCRC_DMA36_USED == STD_ON)
#define SPI_050_ISR CDDCRC_DMA36_ISR
#endif
#if (CDDCRC_DMA37_USED == STD_ON)
#define SPI_051_ISR CDDCRC_DMA37_ISR
#endif
#if (CDDCRC_DMA38_USED == STD_ON)
#define SPI_052_ISR CDDCRC_DMA38_ISR
#endif
#if (CDDCRC_DMA39_USED == STD_ON)
#define SPI_053_ISR CDDCRC_DMA39_ISR
#endif
#if (CDDCRC_DMA40_USED == STD_ON)
#define SPI_054_ISR CDDCRC_DMA40_ISR
#endif
#if (CDDCRC_DMA41_USED == STD_ON)
#define SPI_055_ISR CDDCRC_DMA41_ISR
#endif
#if (CDDCRC_DMA42_USED == STD_ON)
#define SPI_056_ISR CDDCRC_DMA42_ISR
#endif
#if (CDDCRC_DMA43_USED == STD_ON)
#define SPI_057_ISR CDDCRC_DMA43_ISR
#endif
#if (CDDCRC_DMA44_USED == STD_ON)
#define SPI_058_ISR CDDCRC_DMA44_ISR
#endif
#if (CDDCRC_DMA45_USED == STD_ON)
#define SPI_059_ISR CDDCRC_DMA45_ISR
#endif
#if (CDDCRC_DMA46_USED == STD_ON)
#define SPI_060_ISR CDDCRC_DMA46_ISR
#endif
#if (CDDCRC_DMA47_USED == STD_ON)
#define SPI_061_ISR CDDCRC_DMA47_ISR
#endif
/* RT-DMAC3 */
#if (CDDCRC_DMA48_USED == STD_ON)
#define SPI_064_ISR CDDCRC_DMA48_ISR
#endif
#if (CDDCRC_DMA49_USED == STD_ON)
#define SPI_065_ISR CDDCRC_DMA49_ISR
#endif
#if (CDDCRC_DMA50_USED == STD_ON)
#define SPI_066_ISR CDDCRC_DMA50_ISR
#endif
#if (CDDCRC_DMA51_USED == STD_ON)
#define SPI_067_ISR CDDCRC_DMA51_ISR
#endif
#if (CDDCRC_DMA52_USED == STD_ON)
#define SPI_068_ISR CDDCRC_DMA52_ISR
#endif
#if (CDDCRC_DMA53_USED == STD_ON)
#define SPI_069_ISR CDDCRC_DMA53_ISR
#endif
#if (CDDCRC_DMA54_USED == STD_ON)
#define SPI_070_ISR CDDCRC_DMA54_ISR
#endif
#if (CDDCRC_DMA55_USED == STD_ON)
#define SPI_071_ISR CDDCRC_DMA55_ISR
#endif
#if (CDDCRC_DMA56_USED == STD_ON)
#define SPI_072_ISR CDDCRC_DMA56_ISR
#endif
#if (CDDCRC_DMA57_USED == STD_ON)
#define SPI_073_ISR CDDCRC_DMA57_ISR
#endif
#if (CDDCRC_DMA58_USED == STD_ON)
#define SPI_074_ISR CDDCRC_DMA58_ISR
#endif
#if (CDDCRC_DMA59_USED == STD_ON)
#define SPI_075_ISR CDDCRC_DMA59_ISR
#endif
#if (CDDCRC_DMA60_USED == STD_ON)
#define SPI_076_ISR CDDCRC_DMA60_ISR
#endif
#if (CDDCRC_DMA61_USED == STD_ON)
#define SPI_077_ISR CDDCRC_DMA61_ISR
#endif
#if (CDDCRC_DMA62_USED == STD_ON)
#define SPI_078_ISR CDDCRC_DMA62_ISR
#endif
#if (CDDCRC_DMA63_USED == STD_ON)
#define SPI_079_ISR CDDCRC_DMA63_ISR
#endif

#endif

#endif /* INTERRUPT_CFG_H */
/*******************************************************************************
**                      End of File                                           **
*******************************************************************************/

