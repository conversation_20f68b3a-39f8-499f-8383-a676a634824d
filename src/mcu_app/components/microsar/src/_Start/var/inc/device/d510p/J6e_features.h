/*======================================================================================================================
 *  COPYRIGHT NOTICE
 *
 *  Copyright (C) 2023-2025 Horizon Robotics, Inc.
 *
 *  All rights reserved.
 *
 *  This program contains proprietary information belonging to Horizon Robotics. Passing on and copying of this
 *  document, use and communication of its contents is not permitted without prior written authorization.
========================================================================================================================
 *  Project              : J6
 *  Platform             : CORTEXR
 *  Peripheral           : ModuleName
 *  Dependencies         : MCU
 *
 *  SW Version           :
 *  Build Version        :
 *  Author               :
 *  Vendor               : Horizon Robotics
 *
 *  Autosar Version      : 4.4.0
 *  Autosar Revision     : ASR_REL_4_4_REV_0000
 *  Autosar Conf.Variant :
======================================================================================================================*/


/*!
 * @file J6e_features.h
 *
 * @page misra_violations MISRA-C:2012 violations
 *
 * @section [global]
 * Violates MISRA 2012 Advisory Rule 2.3, Global typedef not referenced.
 * Type used only in some modules of the SDK.
 *
 * @section [global]
 * Violates MISRA 2012 Advisory Rule 2.5, Global macro not referenced.
 * The macros defined are used to define features for each driver, so this might be reported
 * when the analysis is made only on one driver.
 *
 * @section [global]
 * Violates MISRA 2012 Advisory Directive 4.9, Function-like macro
 * These are very simple macros used for abstracting hw implementation.
 * They help make the code easy to understand.
 *
 * @section [global]
 * Violates MISRA 2012 Required Rule 5.1, identifier clash
 * The supported compilers use more than 31 significant characters for identifiers.
 *
 * @section [global]
 * Violates MISRA 2012 Required Rule 5.2, identifier clash
 * The supported compilers use more than 31 significant characters for identifiers.
 *
 * @section [global]
 * Violates MISRA 2012 Required Rule 5.4, identifier clash
 * The supported compilers use more than 31 significant characters for identifiers.
 *
 * @section [global]
 * Violates MISRA 2012 Required Rule 5.5, identifier clash
 * The supported compilers use more than 31 significant characters for identifiers.
 *
 * @section [global]
 * Violates MISRA 2012 Required Rule 8.1, function has no explicit type
 * The type is defined in some place.
 *
 */

#if !defined(J6E_FEATURES_H)
#define J6E_FEATURES_H

/* include device_registers.h for SIUL2 module configuration structures */
#include "device_registers.h"

/* @brief Interrupt vector is not stored in hardware */
#define FEATURE_SOFTWARE_INT_VECTOR

/* @brief Number of cores */
#define NUMBER_OF_CORES (1U)

/* Interrupt module features */

/* @brief Lowest interrupt request number. */
#define FEATURE_INTERRUPT_IRQ_MIN (SGI0_IRQn)
/* @brief Highest interrupt request number. */
#define FEATURE_INTERRUPT_IRQ_MAX (NUMBER_OF_INT_VECTORS)
/* @brief Highest core interrupt request number. */
#define FEATURE_INTERRUPT_CORE_IRQ_MAX  (EL1_TIM_IRQn)
/**< Number of priority bits implemented in the NVIC */
#define FEATURE_INTERRUPT_GIC_PRIO_BITS    (5U)
/* @brief Has pending interrupt state. */
#define FEATURE_INTERRUPT_HAS_PENDING_STATE (1u)
/* @brief Has active interrupt state. */
#define FEATURE_INTERRUPT_HAS_ACTIVE_STATE  (1u)

#endif /* J6E_FEATURES_H */

/*******************************************************************************
 * EOF
 ******************************************************************************/
