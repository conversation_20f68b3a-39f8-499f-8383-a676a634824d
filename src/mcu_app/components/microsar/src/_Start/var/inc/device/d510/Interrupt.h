/*============================================================================*/
/* Project      = AUTOSAR Renesas CPF MCAL Components                         */
/* Module       = Interrupt.h                                                 */
/* SW-VERSION   = 1.0.1                                                       */
/*============================================================================*/
/*                                  COPYRIGHT                                 */
/*============================================================================*/
/* Copyright(c) 2021-2022 Renesas Electronics Corporation.                    */
/*============================================================================*/
/* Purpose:                                                                   */
/* R-Car interrupt controller                                                 */
/*                                                                            */
/*============================================================================*/
/*                                                                            */
/* Unless otherwise agreed upon in writing between your company and           */
/* Renesas Electronics Corporation the following shall apply!                 */
/*                                                                            */
/* Warranty Disclaimer                                                        */
/*                                                                            */
/* There is no warranty of any kind whatsoever granted by Renesas. Any        */
/* warranty is expressly disclaimed and excluded by Renesas, either expressed */
/* or implied, including but not limited to those for non-infringement of     */
/* intellectual property, merchantability and/or fitness for the particular   */
/* purpose.                                                                   */
/*                                                                            */
/* Renesas shall not have any obligation to maintain, service or provide bug  */
/* fixes for the supplied Product(s) and/or the Application.                  */
/*                                                                            */
/* Each User is solely responsible for determining the appropriateness of     */
/* using the Product(s) and assumes all risks associated with its exercise    */
/* of rights under this Agreement, including, but not limited to the risks    */
/* and costs of program errors, compliance with applicable laws, damage to    */
/* or loss of data, programs or equipment, and unavailability or              */
/* interruption of operations.                                                */
/*                                                                            */
/* Limitation of Liability                                                    */
/*                                                                            */
/* In no event shall Renesas be liable to the User for any incidental,        */
/* consequential, indirect, or punitive damage (including but not limited     */
/* to lost profits) regardless of whether such liability is based on breach   */
/* of contract, tort, strict liability, breach of warranties, failure of      */
/* essential purpose or otherwise and even if advised of the possibility of   */
/* such damages. Renesas shall not be liable for any services or products     */
/* provided by third party vendors, developers or consultants identified or   */
/* referred to the User by Renesas in connection with the Product(s) and/or   */
/* the Application.                                                           */
/*                                                                            */
/*============================================================================*/
/* Environment:                                                               */
/*              Devices:        CPF                                           */
/*============================================================================*/

/*******************************************************************************
**                      Revision Control History                              **
*******************************************************************************/
/*
 * 1.0.1:      01/10/2022 :   Update macro's value of Interrupt_SetPriority()
 * 1.0.0:      20/08/2021 :   Initial Version
 */
/******************************************************************************/

#ifndef INTERRUPT_H
#define INTERRUPT_H

/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/

#include "arm_cr.h"

/*******************************************************************************
**                      Version Information                                   **
*******************************************************************************/

/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/

/*******************************************************************************
**                      Global Data Types                                     **
*******************************************************************************/

/*******************************************************************************
**                      Function Prototypes                                   **
*******************************************************************************/

/* Enable */
#define Interrupt_Enable(intid)             \
    GIC_EnableIRQ((uint32)intid)

/* Disable */
#define Interrupt_Disable(intid)            \
    GIC_DisableIRQ((uint32)intid)

/* Set group */
#define Interrupt_SetGroup(intid,grpid)     \
    GIC_SetGroup((uint32)intid,(uint32)grpid)

/* Set priority */
#define Interrupt_SetPriority(intid,pri)    \
    GIC_SetPriority((uint32)intid,(uint32)pri)

/* Interrupt configuration */
#define  Interrupt_Config()                 GIC_Enable()

/* Install a handler to vector table */
void Interrupt_InstallHandler(void (*f)(), uint32 intid);

#endif /* INTERRUPT_H */
/*******************************************************************************
**                      End of File                                           **
*******************************************************************************/
