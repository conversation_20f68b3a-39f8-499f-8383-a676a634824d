/**
 * @file j6e_Interrupt_VectorTable.c
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * @version 1.0
 * @date 2024-11-07
 * 
 * Copyright (c) by iMotion AI. All rights reserved.
 * 
 */

#include "j6e_Interrupt_VectorTable.h"
#include "Platform_Types.h"
/* Include for ISR definition and module ISR header inclusion */
#include "Interrupt_Cfg.h"
#include "arm_cr.h"

/* Set the linker to place in the opportune location (Deafult=0x200)  */
/*****************************************************************************/
/*                         ISR Definition                                    */
/*****************************************************************************/
void Dummy_Handler(void)
{
  __NOP();
}

/******************************************************************************/
/*                          Interrupt Vector Table                            */
/******************************************************************************/
extern void Os_Isr_Dummy_Handler(void);

extern void Os_Isr_Core0_Interrupt_85(void);   // timer1 ch2
extern void Os_Isr_Core0_Interrupt_174(void);   // spi5
extern void Os_Isr_Core0_Interrupt_237(void);  //core1 --> core0 ISR
extern void Os_Isr_Core0_Interrupt_265(void);   // ipc  ota
extern void Os_Isr_Core0_Interrupt_241(void);   // ipc  hsm
extern void Os_Isr_Core0_Interrupt_251(void);   //
extern void Os_Isr_Core0_Interrupt_252(void);   //
extern void Os_Isr_Core0_Interrupt_253(void);   //
extern void Os_Isr_Core0_Interrupt_209(void);   //
extern void Os_Isr_Core0_Interrupt_210(void);   //

extern void Os_Isr_Core0_Interrupt_254(void);  //
extern void Os_Isr_Core0_Interrupt_255(void);  //
extern void Os_Isr_Core0_Interrupt_256(void);  //
extern void Os_Isr_Core0_Interrupt_257(void);  //
extern void Os_Isr_Core0_Interrupt_258(void);  //
extern void Os_Isr_Core0_Interrupt_259(void);  //
extern void Os_Isr_Core0_Interrupt_260(void);  //
extern void Os_Isr_Core0_Interrupt_261(void);  //
extern void Os_Isr_Core0_Interrupt_262(void);  //
extern void Os_Isr_Core0_Interrupt_211(void);  //
extern void Os_Isr_Core0_Interrupt_212(void);  //

extern void Os_Isr_Core0_Interrupt_242(void);  //
extern void Os_Isr_Core0_Interrupt_263(void);  //
extern void Os_Isr_Core0_Interrupt_264(void);  //
extern void Os_Isr_Core0_Interrupt_266(void);  //
extern void Os_Isr_Core0_Interrupt_267(void);  //
extern void Os_Isr_Core0_Interrupt_268(void);  //
extern void Os_Isr_Core0_Interrupt_269(void);  //

extern void Os_Isr_Core1_Interrupt_68(void);   // j6e gpio0
extern void Os_Isr_Core1_Interrupt_86(void);   // timer1 ch3
extern void Os_Isr_Core1_Interrupt_87(void);   // timer2, instance2 ch0 
extern void Os_Isr_Core1_Interrupt_88(void);   // timer3, instance2 ch1
// extern void Os_Isr_Core1_Interrupt_213(void);  // pdmach0
extern void Os_Isr_Core1_Interrupt_214(void);  // pdmach1
extern void Os_Isr_Core1_Interrupt_238(void);  //core0 --> core1 ISR

extern void Os_Isr_Core0_Interrupt_153(void);
extern void Os_Isr_Core0_Interrupt_159(void);
extern void Os_Isr_Core0_Interrupt_165(void);
extern void Os_Isr_Core0_Interrupt_166(void);
extern void Os_Isr_Core0_Interrupt_167(void);

extern void Os_Isr_Core0_Interrupt_106(void);
extern void Os_Isr_Core0_Interrupt_107(void);
extern void Os_Isr_Core0_Interrupt_108(void);
#ifdef ZX_CPJ_CHERY_E0X
extern void Os_Isr_Core0_Interrupt_45(void);  // uart channel 0
extern void Os_Isr_Core0_Interrupt_213(void); // pdmach0 for uart channel 0
#endif
/* The table of interrupt handlers for core0 */
const FUNCT IntVectors_Core0[] =
{
  /* ID: 0, Type: SGI, Type ID: 0 */
  #ifdef SGI_000_ISR
  SGI_000_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 1, Type: SGI, Type ID: 1 */
  #ifdef SGI_001_ISR
  SGI_001_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 2, Type: SGI, Type ID: 2 */
  #ifdef SGI_002_ISR
  SGI_002_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 3, Type: SGI, Type ID: 3 */
  #ifdef SGI_003_ISR
  SGI_003_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 4, Type: SGI, Type ID: 4 */
  #ifdef SGI_004_ISR
  SGI_004_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 5, Type: SGI, Type ID: 5 */
  #ifdef SGI_005_ISR
  SGI_005_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 6, Type: SGI, Type ID: 6 */
  #ifdef SGI_006_ISR
  SGI_006_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 7, Type: SGI, Type ID: 7 */
  #ifdef SGI_007_ISR
  SGI_007_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 8, Type: SGI, Type ID: 8 */
  #ifdef SGI_008_ISR
  SGI_008_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 9, Type: SGI, Type ID: 9 */
  #ifdef SGI_009_ISR
  SGI_009_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 10, Type: SGI, Type ID: 10 */
  #ifdef SGI_010_ISR
  SGI_010_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 11, Type: SGI, Type ID: 11 */
  #ifdef SGI_011_ISR
  SGI_011_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 12, Type: SGI, Type ID: 12 */
  #ifdef SGI_012_ISR
  SGI_012_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 13, Type: SGI, Type ID: 13 */
  #ifdef SGI_013_ISR
  SGI_013_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 14, Type: SGI, Type ID: 14 */
  #ifdef SGI_014_ISR
  SGI_014_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 15, Type: SGI, Type ID: 15 */
  #ifdef SGI_015_ISR
  SGI_015_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 16, Type: PPI, Type ID: 0 */
  #ifdef PPI_000_ISR
  PPI_000_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 17, Type: PPI, Type ID: 1 */
  #ifdef PPI_001_ISR
  PPI_001_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 18, Type: PPI, Type ID: 2 */
  #ifdef PPI_002_ISR
  PPI_002_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 19, Type: PPI, Type ID: 3 */
  #ifdef PPI_003_ISR
  PPI_003_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 20, Type: PPI, Type ID: 4 */
  #ifdef PPI_004_ISR
  PPI_004_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 21, Type: PPI, Type ID: 5 */
  #ifdef PPI_005_ISR
  PPI_005_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 22, Type: PPI, Type ID: 6 */
  #ifdef PPI_006_ISR
  PPI_006_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 23, Type: PPI, Type ID: 7 */
  #ifdef PPI_007_ISR
  PPI_007_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 24, Type: PPI, Type ID: 8 */
  #ifdef PPI_008_ISR
  PPI_008_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 25, Type: PPI, Type ID: 9 */
  #ifdef PPI_009_ISR
  PPI_009_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 26, Type: PPI, Type ID: 10 */
  #ifdef PPI_010_ISR
  PPI_010_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 27, Type: PPI, Type ID: 11 */
  #ifdef PPI_011_ISR
  PPI_011_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 28, Type: PPI, Type ID: 12 */
  #ifdef PPI_012_ISR
  PPI_012_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 29, Type: PPI, Type ID: 13 */
  #ifdef PPI_013_ISR
  PPI_013_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 30, Type: PPI, Type ID: 14 */
  #ifdef PPI_014_ISR
  PPI_014_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 31, Type: PPI, Type ID: 15 */
  #ifdef PPI_015_ISR
  PPI_015_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 32, Type: SPI, Type ID: 0 */
  #ifdef SPI_000_ISR
  SPI_000_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 33, Type: SPI, Type ID: 1 */
  #ifdef SPI_001_ISR
  SPI_001_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 34, Type: SPI, Type ID: 2 */
  #ifdef SPI_002_ISR
  SPI_002_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 35, Type: SPI, Type ID: 3 */
  #ifdef SPI_003_ISR
  SPI_003_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 36, Type: SPI, Type ID: 4 */
  #ifdef SPI_004_ISR
  SPI_004_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 37, Type: SPI, Type ID: 5 */
  #ifdef SPI_005_ISR
  SPI_005_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 38, Type: SPI, Type ID: 6 */
  #ifdef SPI_006_ISR
  SPI_006_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 39, Type: SPI, Type ID: 7 */
  #ifdef SPI_007_ISR
  SPI_007_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 40, Type: SPI, Type ID: 8 */
  #ifdef SPI_008_ISR
  SPI_008_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 41, Type: SPI, Type ID: 9 */
  #ifdef SPI_009_ISR
  SPI_009_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 42, Type: SPI, Type ID: 10 */
  #ifdef SPI_010_ISR
  SPI_010_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 43, Type: SPI, Type ID: 11 */
  #ifdef SPI_011_ISR
  SPI_011_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 44, Type: SPI, Type ID: 12 */
  #ifdef SPI_012_ISR
  SPI_012_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 45, Type: SPI, Type ID: 13 */
  #ifdef SPI_013_ISR
  SPI_013_ISR,
  #else
    #ifdef ZX_CPJ_CHERY_E0X
    Os_Isr_Core0_Interrupt_45,
    #else
    Dummy_Handler,
    #endif
  #endif

  /* ID: 46, Type: SPI, Type ID: 14 */
  #ifdef SPI_014_ISR
  SPI_014_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 47, Type: SPI, Type ID: 15 */
  #ifdef SPI_015_ISR
  SPI_015_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 48, Type: SPI, Type ID: 16 */
  #ifdef SPI_016_ISR
  SPI_016_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 49, Type: SPI, Type ID: 17 */
  #ifdef SPI_017_ISR
  SPI_017_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 50, Type: SPI, Type ID: 18 */
  #ifdef SPI_018_ISR
  SPI_018_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 51, Type: SPI, Type ID: 19 */
  #ifdef SPI_019_ISR
  SPI_019_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 52, Type: SPI, Type ID: 20 */
  #ifdef SPI_020_ISR
  SPI_020_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 53, Type: SPI, Type ID: 21 */
  #ifdef SPI_021_ISR
  SPI_021_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 54, Type: SPI, Type ID: 22 */
  #ifdef SPI_022_ISR
  SPI_022_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 55, Type: SPI, Type ID: 23 */
  #ifdef SPI_023_ISR
  SPI_023_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 56, Type: SPI, Type ID: 24 */
  #ifdef SPI_024_ISR
  SPI_024_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 57, Type: SPI, Type ID: 25 */
  #ifdef SPI_025_ISR
  SPI_025_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 58, Type: SPI, Type ID: 26 */
  #ifdef SPI_026_ISR
  SPI_026_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 59, Type: SPI, Type ID: 27 */
  #ifdef SPI_027_ISR
  SPI_027_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 60, Type: SPI, Type ID: 28 */
  #ifdef SPI_028_ISR
  SPI_028_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 61, Type: SPI, Type ID: 29 */
  #ifdef SPI_029_ISR
  SPI_029_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 62, Type: SPI, Type ID: 30 */
  #ifdef SPI_030_ISR
  SPI_030_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 63, Type: SPI, Type ID: 31 */
  #ifdef SPI_031_ISR
  SPI_031_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 64, Type: SPI, Type ID: 32 */
  #ifdef SPI_032_ISR
  SPI_032_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 65, Type: SPI, Type ID: 33 */
  #ifdef SPI_033_ISR
  SPI_033_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 66, Type: SPI, Type ID: 34 */
  #ifdef SPI_034_ISR
  SPI_034_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 67, Type: SPI, Type ID: 35 */
  #ifdef SPI_035_ISR
  SPI_035_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 68, Type: SPI, Type ID: 36 */
  #ifdef SPI_036_ISR
  SPI_036_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 69, Type: SPI, Type ID: 37 */
  #ifdef SPI_037_ISR
  SPI_037_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 70, Type: SPI, Type ID: 38 */
  #ifdef SPI_038_ISR
  SPI_038_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 71, Type: SPI, Type ID: 39 */
  #ifdef SPI_039_ISR
  SPI_039_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 72, Type: SPI, Type ID: 40 */
  #ifdef SPI_040_ISR
  SPI_040_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 73, Type: SPI, Type ID: 41 */
  #ifdef SPI_041_ISR
  SPI_041_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 74, Type: SPI, Type ID: 42 */
  #ifdef SPI_042_ISR
  SPI_042_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 75, Type: SPI, Type ID: 43 */
  #ifdef SPI_043_ISR
  SPI_043_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 76, Type: SPI, Type ID: 44 */
  #ifdef SPI_044_ISR
  SPI_044_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 77, Type: SPI, Type ID: 45 */
  #ifdef SPI_045_ISR
  SPI_045_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 78, Type: SPI, Type ID: 46 */
  #ifdef SPI_046_ISR
  SPI_046_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 79, Type: SPI, Type ID: 47 */
  #ifdef SPI_047_ISR
  SPI_047_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 80, Type: SPI, Type ID: 48 */
  #ifdef SPI_048_ISR
  SPI_048_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 81, Type: SPI, Type ID: 49 */
  #ifdef SPI_049_ISR
  SPI_049_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 82, Type: SPI, Type ID: 50 */
  #ifdef SPI_050_ISR
  SPI_050_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 83, Type: SPI, Type ID: 51 */
  #ifdef SPI_051_ISR
  SPI_051_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 84, Type: SPI, Type ID: 52 */
  #ifdef SPI_052_ISR
  SPI_052_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 85, Type: SPI, Type ID: 53 */
  #ifdef SPI_053_ISR
  SPI_053_ISR,
  #else
  Os_Isr_Core0_Interrupt_85,
  #endif

  /* ID: 86, Type: SPI, Type ID: 54 */
  #ifdef SPI_054_ISR
  SPI_054_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 87, Type: SPI, Type ID: 55 */
  #ifdef SPI_055_ISR
  SPI_055_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 88, Type: SPI, Type ID: 56 */
  #ifdef SPI_056_ISR
  SPI_056_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 89, Type: SPI, Type ID: 57 */
  #ifdef SPI_057_ISR
  SPI_057_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 90, Type: SPI, Type ID: 58 */
  #ifdef SPI_058_ISR
  SPI_058_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 91, Type: SPI, Type ID: 59 */
  #ifdef SPI_059_ISR
  SPI_059_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 92, Type: SPI, Type ID: 60 */
  #ifdef SPI_060_ISR
  SPI_060_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 93, Type: SPI, Type ID: 61 */
  #ifdef SPI_061_ISR
  SPI_061_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 94, Type: SPI, Type ID: 62 */
  #ifdef SPI_062_ISR
  SPI_062_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 95, Type: SPI, Type ID: 63 */
  #ifdef SPI_063_ISR
  SPI_063_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 96, Type: SPI, Type ID: 64 */
  #ifdef SPI_064_ISR
  SPI_064_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 97, Type: SPI, Type ID: 65 */
  #ifdef SPI_065_ISR
  SPI_065_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 98, Type: SPI, Type ID: 66 */
  #ifdef SPI_066_ISR
  SPI_066_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 99, Type: SPI, Type ID: 67 */
  #ifdef SPI_067_ISR
  SPI_067_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 100, Type: SPI, Type ID: 68 */
  #ifdef SPI_068_ISR
  SPI_068_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 101, Type: SPI, Type ID: 69 */
  #ifdef SPI_069_ISR
  SPI_069_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 102, Type: SPI, Type ID: 70 */
  #ifdef SPI_070_ISR
  SPI_070_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 103, Type: SPI, Type ID: 71 */
  #ifdef SPI_071_ISR
  SPI_071_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 104, Type: SPI, Type ID: 72 */
  #ifdef SPI_072_ISR
  SPI_072_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 105, Type: SPI, Type ID: 73 */
  #ifdef SPI_073_ISR
  SPI_073_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 106, Type: SPI, Type ID: 74 */
  #ifdef SPI_074_ISR
  SPI_074_ISR,
  #else
  Os_Isr_Core0_Interrupt_106,
  #endif

  /* ID: 107, Type: SPI, Type ID: 75 */
  #ifdef SPI_075_ISR
  SPI_075_ISR,
  #else
  Os_Isr_Core0_Interrupt_107,
  #endif

  /* ID: 108, Type: SPI, Type ID: 76 */
  #ifdef SPI_076_ISR
  SPI_076_ISR,
  #else
  Os_Isr_Core0_Interrupt_108,
  #endif

  /* ID: 109, Type: SPI, Type ID: 77 */
  #ifdef SPI_077_ISR
  SPI_077_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 110, Type: SPI, Type ID: 78 */
  #ifdef SPI_078_ISR
  SPI_078_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 111, Type: SPI, Type ID: 79 */
  #ifdef SPI_079_ISR
  SPI_079_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 112, Type: SPI, Type ID: 80 */
  #ifdef SPI_080_ISR
  SPI_080_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 113, Type: SPI, Type ID: 81 */
  #ifdef SPI_081_ISR
  SPI_081_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 114, Type: SPI, Type ID: 82 */
  #ifdef SPI_082_ISR
  SPI_082_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 115, Type: SPI, Type ID: 83 */
  #ifdef SPI_083_ISR
  SPI_083_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 116, Type: SPI, Type ID: 84 */
  #ifdef SPI_084_ISR
  SPI_084_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 117, Type: SPI, Type ID: 85 */
  #ifdef SPI_085_ISR
  SPI_085_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 118, Type: SPI, Type ID: 86 */
  #ifdef SPI_086_ISR
  SPI_086_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 119, Type: SPI, Type ID: 87 */
  #ifdef SPI_087_ISR
  SPI_087_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 120, Type: SPI, Type ID: 88 */
  #ifdef SPI_088_ISR
  SPI_088_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 121, Type: SPI, Type ID: 89 */
  #ifdef SPI_089_ISR
  SPI_089_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 122, Type: SPI, Type ID: 90 */
  #ifdef SPI_090_ISR
  SPI_090_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 123, Type: SPI, Type ID: 91 */
  #ifdef SPI_091_ISR
  SPI_091_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 124, Type: SPI, Type ID: 92 */
  #ifdef SPI_092_ISR
  SPI_092_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 125, Type: SPI, Type ID: 93 */
  #ifdef SPI_093_ISR
  SPI_093_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 126, Type: SPI, Type ID: 94 */
  #ifdef SPI_094_ISR
  SPI_094_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 127, Type: SPI, Type ID: 95 */
  #ifdef SPI_095_ISR
  SPI_095_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 128, Type: SPI, Type ID: 96 */
  #ifdef SPI_096_ISR
  SPI_096_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 129, Type: SPI, Type ID: 97 */
  #ifdef SPI_097_ISR
  SPI_097_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 130, Type: SPI, Type ID: 98 */
  #ifdef SPI_098_ISR
  SPI_098_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 131, Type: SPI, Type ID: 99 */
  #ifdef SPI_099_ISR
  SPI_099_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 132, Type: SPI, Type ID: 100 */
  #ifdef SPI_100_ISR
  SPI_100_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 133, Type: SPI, Type ID: 101 */
  #ifdef SPI_101_ISR
  SPI_101_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 134, Type: SPI, Type ID: 102 */
  #ifdef SPI_102_ISR
  SPI_102_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 135, Type: SPI, Type ID: 103 */
  #ifdef SPI_103_ISR
  SPI_103_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 136, Type: SPI, Type ID: 104 */
  #ifdef SPI_104_ISR
  SPI_104_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 137, Type: SPI, Type ID: 105 */
  #ifdef SPI_105_ISR
  SPI_105_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 138, Type: SPI, Type ID: 106 */
  #ifdef SPI_106_ISR
  SPI_106_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 139, Type: SPI, Type ID: 107 */
  #ifdef SPI_107_ISR
  SPI_107_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 140, Type: SPI, Type ID: 108 */
  #ifdef SPI_108_ISR
  SPI_108_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 141, Type: SPI, Type ID: 109 */
  #ifdef SPI_109_ISR
  SPI_109_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 142, Type: SPI, Type ID: 110 */
  #ifdef SPI_110_ISR
  SPI_110_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 143, Type: SPI, Type ID: 111 */
  #ifdef SPI_111_ISR
  SPI_111_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 144, Type: SPI, Type ID: 112 */
  #ifdef SPI_112_ISR
  SPI_112_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 145, Type: SPI, Type ID: 113 */
  #ifdef SPI_113_ISR
  SPI_113_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 146, Type: SPI, Type ID: 114 */
  #ifdef SPI_114_ISR
  SPI_114_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 147, Type: SPI, Type ID: 115 */
  #ifdef SPI_115_ISR
  SPI_115_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 148, Type: SPI, Type ID: 116 */
  #ifdef SPI_116_ISR
  SPI_116_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 149, Type: SPI, Type ID: 117 */
  #ifdef SPI_117_ISR
  SPI_117_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 150, Type: SPI, Type ID: 118 */
  #ifdef SPI_118_ISR
  SPI_118_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 151, Type: SPI, Type ID: 119 */
  #ifdef SPI_119_ISR
  SPI_119_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 152, Type: SPI, Type ID: 120 */
  #ifdef SPI_120_ISR
  SPI_120_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 153, Type: SPI, Type ID: 121 */
  #ifdef SPI_121_ISR
  SPI_121_ISR,
  #else
  Os_Isr_Core0_Interrupt_153,
  #endif

  /* ID: 154, Type: SPI, Type ID: 122 */
  #ifdef SPI_122_ISR
  SPI_122_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 155, Type: SPI, Type ID: 123 */
  #ifdef SPI_123_ISR
  SPI_123_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 156, Type: SPI, Type ID: 124 */
  #ifdef SPI_124_ISR
  SPI_124_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 157, Type: SPI, Type ID: 125 */
  #ifdef SPI_125_ISR
  SPI_125_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 158, Type: SPI, Type ID: 126 */
  #ifdef SPI_126_ISR
  SPI_126_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 159, Type: SPI, Type ID: 127 */
  #ifdef SPI_127_ISR
  SPI_127_ISR,
  #else
  Os_Isr_Core0_Interrupt_159,
  #endif

  /* ID: 160, Type: SPI, Type ID: 128 */
  #ifdef SPI_128_ISR
  SPI_128_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 161, Type: SPI, Type ID: 129 */
  #ifdef SPI_129_ISR
  SPI_129_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 162, Type: SPI, Type ID: 130 */
  #ifdef SPI_130_ISR
  SPI_130_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 163, Type: SPI, Type ID: 131 */
  #ifdef SPI_131_ISR
  SPI_131_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 164, Type: SPI, Type ID: 132 */
  #ifdef SPI_132_ISR
  SPI_132_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 165, Type: SPI, Type ID: 133 */
  #ifdef SPI_133_ISR
  SPI_133_ISR,
  #else
  Os_Isr_Core0_Interrupt_165,
  #endif

  /* ID: 166, Type: SPI, Type ID: 134 */
  #ifdef SPI_134_ISR
  SPI_134_ISR,
  #else
  Os_Isr_Core0_Interrupt_166,
  #endif

  /* ID: 167, Type: SPI, Type ID: 135 */
  #ifdef SPI_135_ISR
  SPI_135_ISR,
  #else
  Os_Isr_Core0_Interrupt_167,
  #endif

  /* ID: 168, Type: SPI, Type ID: 136 */
  #ifdef SPI_136_ISR
  SPI_136_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 169, Type: SPI, Type ID: 137 */
  #ifdef SPI_137_ISR
  SPI_137_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 170, Type: SPI, Type ID: 138 */
  #ifdef SPI_138_ISR
  SPI_138_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 171, Type: SPI, Type ID: 139 */
  #ifdef SPI_139_ISR
  SPI_139_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 172, Type: SPI, Type ID: 140 */
  #ifdef SPI_140_ISR
  SPI_140_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 173, Type: SPI, Type ID: 141 */
  #ifdef SPI_141_ISR
  SPI_141_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 174, Type: SPI, Type ID: 142 */
  #ifdef SPI_142_ISR
  SPI_142_ISR,
  #else
  Os_Isr_Core0_Interrupt_174,
  #endif

  /* ID: 175, Type: SPI, Type ID: 143 */
  #ifdef SPI_143_ISR
  SPI_143_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 176, Type: SPI, Type ID: 144 */
  #ifdef SPI_144_ISR
  SPI_144_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 177, Type: SPI, Type ID: 145 */
  #ifdef SPI_145_ISR
  SPI_145_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 178, Type: SPI, Type ID: 146 */
  #ifdef SPI_146_ISR
  SPI_146_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 179, Type: SPI, Type ID: 147 */
  #ifdef SPI_147_ISR
  SPI_147_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 180, Type: SPI, Type ID: 148 */
  #ifdef SPI_148_ISR
  SPI_148_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 181, Type: SPI, Type ID: 149 */
  #ifdef SPI_149_ISR
  SPI_149_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 182, Type: SPI, Type ID: 150 */
  #ifdef SPI_150_ISR
  SPI_150_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 183, Type: SPI, Type ID: 151 */
  #ifdef SPI_151_ISR
  SPI_151_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 184, Type: SPI, Type ID: 152 */
  #ifdef SPI_152_ISR
  SPI_152_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 185, Type: SPI, Type ID: 153 */
  #ifdef SPI_153_ISR
  SPI_153_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 186, Type: SPI, Type ID: 154 */
  #ifdef SPI_154_ISR
  SPI_154_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 187, Type: SPI, Type ID: 155 */
  #ifdef SPI_155_ISR
  SPI_155_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 188, Type: SPI, Type ID: 156 */
  #ifdef SPI_156_ISR
  SPI_156_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 189, Type: SPI, Type ID: 157 */
  #ifdef SPI_157_ISR
  SPI_157_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 190, Type: SPI, Type ID: 158 */
  #ifdef SPI_158_ISR
  SPI_158_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 191, Type: SPI, Type ID: 159 */
  #ifdef SPI_159_ISR
  SPI_159_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 192, Type: SPI, Type ID: 160 */
  #ifdef SPI_160_ISR
  SPI_160_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 193, Type: SPI, Type ID: 161 */
  #ifdef SPI_161_ISR
  SPI_161_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 194, Type: SPI, Type ID: 162 */
  #ifdef SPI_162_ISR
  SPI_162_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 195, Type: SPI, Type ID: 163 */
  #ifdef SPI_163_ISR
  SPI_163_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 196, Type: SPI, Type ID: 164 */
  #ifdef SPI_164_ISR
  SPI_164_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 197, Type: SPI, Type ID: 165 */
  #ifdef SPI_165_ISR
  SPI_165_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 198, Type: SPI, Type ID: 166 */
  #ifdef SPI_166_ISR
  SPI_166_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 199, Type: SPI, Type ID: 167 */
  #ifdef SPI_167_ISR
  SPI_167_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 200, Type: SPI, Type ID: 168 */
  #ifdef SPI_168_ISR
  SPI_168_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 201, Type: SPI, Type ID: 169 */
  #ifdef SPI_169_ISR
  SPI_169_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 202, Type: SPI, Type ID: 170 */
  #ifdef SPI_170_ISR
  SPI_170_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 203, Type: SPI, Type ID: 171 */
  #ifdef SPI_171_ISR
  SPI_171_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 204, Type: SPI, Type ID: 172 */
  #ifdef SPI_172_ISR
  SPI_172_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 205, Type: SPI, Type ID: 173 */
  #ifdef SPI_173_ISR
  SPI_173_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 206, Type: SPI, Type ID: 174 */
  #ifdef SPI_174_ISR
  SPI_174_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 207, Type: SPI, Type ID: 175 */
  #ifdef SPI_175_ISR
  SPI_175_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 208, Type: SPI, Type ID: 176 */
  #ifdef SPI_176_ISR
  SPI_176_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 209, Type: SPI, Type ID: 177 */
  #ifdef SPI_177_ISR
  SPI_177_ISR,
  #else
  Os_Isr_Core0_Interrupt_209,
  #endif

  /* ID: 210, Type: SPI, Type ID: 178 */
  #ifdef SPI_178_ISR
  SPI_178_ISR,
  #else
  Os_Isr_Core0_Interrupt_210,
  #endif

  /* ID: 211, Type: SPI, Type ID: 179 */
  #ifdef SPI_179_ISR
  SPI_179_ISR,
  #else
  Os_Isr_Core0_Interrupt_211,
  #endif

  /* ID: 212, Type: SPI, Type ID: 180 */
  #ifdef SPI_180_ISR
  SPI_180_ISR,
  #else
  Os_Isr_Core0_Interrupt_212,
  #endif

  /* ID: 213, Type: SPI, Type ID: 181 */
  #ifdef SPI_181_ISR
  SPI_181_ISR,
  #else
    #ifdef ZX_CPJ_CHERY_E0X
    Os_Isr_Core0_Interrupt_213,
    #else
    Dummy_Handler,
    #endif
  #endif

  /* ID: 214, Type: SPI, Type ID: 182 */
  #ifdef SPI_182_ISR
  SPI_182_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 215, Type: SPI, Type ID: 183 */
  #ifdef SPI_183_ISR
  SPI_183_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 216, Type: SPI, Type ID: 184 */
  #ifdef SPI_184_ISR
  SPI_184_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 217, Type: SPI, Type ID: 185 */
  #ifdef SPI_185_ISR
  SPI_185_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 218, Type: SPI, Type ID: 186 */
  #ifdef SPI_186_ISR
  SPI_186_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 219, Type: SPI, Type ID: 187 */
  #ifdef SPI_187_ISR
  SPI_187_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 220, Type: SPI, Type ID: 188 */
  #ifdef SPI_188_ISR
  SPI_188_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 221, Type: SPI, Type ID: 189 */
  #ifdef SPI_189_ISR
  SPI_189_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 222, Type: SPI, Type ID: 190 */
  #ifdef SPI_190_ISR
  SPI_190_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 223, Type: SPI, Type ID: 191 */
  #ifdef SPI_191_ISR
  SPI_191_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 224, Type: SPI, Type ID: 192 */
  #ifdef SPI_192_ISR
  SPI_192_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 225, Type: SPI, Type ID: 193 */
  #ifdef SPI_193_ISR
  SPI_193_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 226, Type: SPI, Type ID: 194 */
  #ifdef SPI_194_ISR
  SPI_194_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 227, Type: SPI, Type ID: 195 */
  #ifdef SPI_195_ISR
  SPI_195_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 228, Type: SPI, Type ID: 196 */
  #ifdef SPI_196_ISR
  SPI_196_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 229, Type: SPI, Type ID: 197 */
  #ifdef SPI_197_ISR
  SPI_197_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 230, Type: SPI, Type ID: 198 */
  #ifdef SPI_198_ISR
  SPI_198_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 231, Type: SPI, Type ID: 199 */
  #ifdef SPI_199_ISR
  SPI_199_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 232, Type: SPI, Type ID: 200 */
  #ifdef SPI_200_ISR
  SPI_200_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 233, Type: SPI, Type ID: 201 */
  #ifdef SPI_201_ISR
  SPI_201_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 234, Type: SPI, Type ID: 202 */
  #ifdef SPI_202_ISR
  SPI_202_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 235, Type: SPI, Type ID: 203 */
  #ifdef SPI_203_ISR
  SPI_203_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 236, Type: SPI, Type ID: 204 */
  #ifdef SPI_204_ISR
  SPI_204_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 237, Type: SPI, Type ID: 205 */
  #ifdef SPI_205_ISR
  SPI_205_ISR,
  #else
  Os_Isr_Core0_Interrupt_237,
  #endif

  /* ID: 238, Type: SPI, Type ID: 206 */
  #ifdef SPI_206_ISR
  SPI_206_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 239, Type: SPI, Type ID: 207 */
  #ifdef SPI_207_ISR
  SPI_207_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 240, Type: SPI, Type ID: 208 */
  #ifdef SPI_208_ISR
  SPI_208_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 241, Type: SPI, Type ID: 209 */
  #ifdef SPI_209_ISR
  SPI_209_ISR,
  #else
  Os_Isr_Core0_Interrupt_241,
  #endif

  /* ID: 242, Type: SPI, Type ID: 210 */
  #ifdef SPI_210_ISR
  SPI_210_ISR,
  #else
  Os_Isr_Core0_Interrupt_242,
  #endif

  /* ID: 243, Type: SPI, Type ID: 211 */
  #ifdef SPI_211_ISR
  SPI_211_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 244, Type: SPI, Type ID: 212 */
  #ifdef SPI_212_ISR
  SPI_212_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 245, Type: SPI, Type ID: 213 */
  #ifdef SPI_213_ISR
  SPI_213_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 246, Type: SPI, Type ID: 214 */
  #ifdef SPI_214_ISR
  SPI_214_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 247, Type: SPI, Type ID: 215 */
  #ifdef SPI_215_ISR
  SPI_215_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 248, Type: SPI, Type ID: 216 */
  #ifdef SPI_216_ISR
  SPI_216_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 249, Type: SPI, Type ID: 217 */
  #ifdef SPI_217_ISR
  SPI_217_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 250, Type: SPI, Type ID: 218 */
  #ifdef SPI_218_ISR
  SPI_218_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 251, Type: SPI, Type ID: 219 */
  #ifdef SPI_219_ISR
  SPI_219_ISR,
  #else
  Os_Isr_Core0_Interrupt_251,
  #endif

  /* ID: 252, Type: SPI, Type ID: 220 */
  #ifdef SPI_220_ISR
  SPI_220_ISR,
  #else
  Os_Isr_Core0_Interrupt_252,
  #endif

  /* ID: 253, Type: SPI, Type ID: 221 */
  #ifdef SPI_221_ISR
  SPI_221_ISR,
  #else
  Os_Isr_Core0_Interrupt_253,
  #endif

  /* ID: 254, Type: SPI, Type ID: 222 */
  #ifdef SPI_222_ISR
  SPI_222_ISR,
  #else
  Os_Isr_Core0_Interrupt_254,
  #endif

  /* ID: 255, Type: SPI, Type ID: 223 */
  #ifdef SPI_223_ISR
  SPI_223_ISR,
  #else
  Os_Isr_Core0_Interrupt_255,
  #endif

  /* ID: 256, Type: SPI, Type ID: 224 */
  #ifdef SPI_224_ISR
  SPI_224_ISR,
  #else
  Os_Isr_Core0_Interrupt_256,
  #endif

  /* ID: 257, Type: SPI, Type ID: 225 */
  #ifdef SPI_225_ISR
  SPI_225_ISR,
  #else
  Os_Isr_Core0_Interrupt_257,
  #endif

  /* ID: 258, Type: SPI, Type ID: 226 */
  #ifdef SPI_226_ISR
  SPI_226_ISR,
  #else
  Os_Isr_Core0_Interrupt_258,
  #endif

  /* ID: 259, Type: SPI, Type ID: 227 */
  #ifdef SPI_227_ISR
  SPI_227_ISR,
  #else
  Os_Isr_Core0_Interrupt_259,
  #endif

  /* ID: 260, Type: SPI, Type ID: 228 */
  #ifdef SPI_228_ISR
  SPI_228_ISR,
  #else
  Os_Isr_Core0_Interrupt_260,
  #endif

  /* ID: 261, Type: SPI, Type ID: 229 */
  #ifdef SPI_229_ISR
  SPI_229_ISR,
  #else
  Os_Isr_Core0_Interrupt_261,
  #endif

  /* ID: 262, Type: SPI, Type ID: 230 */
  #ifdef SPI_230_ISR
  SPI_230_ISR,
  #else
  Os_Isr_Core0_Interrupt_262,
  #endif

  /* ID: 263, Type: SPI, Type ID: 231 */
  #ifdef SPI_231_ISR
  SPI_231_ISR,
  #else
  Os_Isr_Core0_Interrupt_263,
  #endif

  /* ID: 264, Type: SPI, Type ID: 232 */
  #ifdef SPI_232_ISR
  SPI_232_ISR,
  #else
  Os_Isr_Core0_Interrupt_264,
  #endif

  /* ID: 265, Type: SPI, Type ID: 233 */
  #ifdef SPI_233_ISR
  SPI_233_ISR,
  #else
  Os_Isr_Core0_Interrupt_265,
  #endif

  /* ID: 266, Type: SPI, Type ID: 234 */
  #ifdef SPI_234_ISR
  SPI_234_ISR,
  #else
  Os_Isr_Core0_Interrupt_266,
  #endif

  /* ID: 267, Type: SPI, Type ID: 235 */
  #ifdef SPI_235_ISR
  SPI_235_ISR,
  #else
  Os_Isr_Core0_Interrupt_267,
  #endif

  /* ID: 268, Type: SPI, Type ID: 236 */
  #ifdef SPI_236_ISR
  SPI_236_ISR,
  #else
  Os_Isr_Core0_Interrupt_268,
  #endif

  /* ID: 269, Type: SPI, Type ID: 237 */
  #ifdef SPI_237_ISR
  SPI_237_ISR,
  #else
  Os_Isr_Core0_Interrupt_269,
  #endif

  /* ID: 270, Type: SPI, Type ID: 238 */
  #ifdef SPI_238_ISR
  SPI_238_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 271, Type: SPI, Type ID: 239 */
  #ifdef SPI_239_ISR
  SPI_239_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 272, Type: SPI, Type ID: 240 */
  #ifdef SPI_240_ISR
  SPI_240_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 273, Type: SPI, Type ID: 241 */
  #ifdef SPI_241_ISR
  SPI_241_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 274, Type: SPI, Type ID: 242 */
  #ifdef SPI_242_ISR
  SPI_242_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 275, Type: SPI, Type ID: 243 */
  #ifdef SPI_243_ISR
  SPI_243_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 276, Type: SPI, Type ID: 244 */
  #ifdef SPI_244_ISR
  SPI_244_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 277, Type: SPI, Type ID: 245 */
  #ifdef SPI_245_ISR
  SPI_245_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 278, Type: SPI, Type ID: 246 */
  #ifdef SPI_246_ISR
  SPI_246_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 279, Type: SPI, Type ID: 247 */
  #ifdef SPI_247_ISR
  SPI_247_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 280, Type: SPI, Type ID: 248 */
  #ifdef SPI_248_ISR
  SPI_248_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 281, Type: SPI, Type ID: 249 */
  #ifdef SPI_249_ISR
  SPI_249_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 282, Type: SPI, Type ID: 250 */
  #ifdef SPI_250_ISR
  SPI_250_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 283, Type: SPI, Type ID: 251 */
  #ifdef SPI_251_ISR
  SPI_251_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 284, Type: SPI, Type ID: 252 */
  #ifdef SPI_252_ISR
  SPI_252_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 285, Type: SPI, Type ID: 253 */
  #ifdef SPI_253_ISR
  SPI_253_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 286, Type: SPI, Type ID: 254 */
  #ifdef SPI_254_ISR
  SPI_254_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 287, Type: SPI, Type ID: 255 */
  #ifdef SPI_255_ISR
  SPI_255_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 288, Type: SPI, Type ID: 256 */
  #ifdef SPI_256_ISR
  SPI_256_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 289, Type: SPI, Type ID: 257 */
  #ifdef SPI_257_ISR
  SPI_257_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 290, Type: SPI, Type ID: 258 */
  #ifdef SPI_258_ISR
  SPI_258_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 291, Type: SPI, Type ID: 259 */
  #ifdef SPI_259_ISR
  SPI_259_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 292, Type: SPI, Type ID: 260 */
  #ifdef SPI_260_ISR
  SPI_260_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 293, Type: SPI, Type ID: 261 */
  #ifdef SPI_261_ISR
  SPI_261_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 294, Type: SPI, Type ID: 262 */
  #ifdef SPI_262_ISR
  SPI_262_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 295, Type: SPI, Type ID: 263 */
  #ifdef SPI_263_ISR
  SPI_263_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 296, Type: SPI, Type ID: 264 */
  #ifdef SPI_264_ISR
  SPI_264_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 297, Type: SPI, Type ID: 265 */
  #ifdef SPI_265_ISR
  SPI_265_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 298, Type: SPI, Type ID: 266 */
  #ifdef SPI_266_ISR
  SPI_266_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 299, Type: SPI, Type ID: 267 */
  #ifdef SPI_267_ISR
  SPI_267_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 300, Type: SPI, Type ID: 268 */
  #ifdef SPI_268_ISR
  SPI_268_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 301, Type: SPI, Type ID: 269 */
  #ifdef SPI_269_ISR
  SPI_269_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 302, Type: SPI, Type ID: 270 */
  #ifdef SPI_270_ISR
  SPI_270_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 303, Type: SPI, Type ID: 271 */
  #ifdef SPI_271_ISR
  SPI_271_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 304, Type: SPI, Type ID: 272 */
  #ifdef SPI_272_ISR
  SPI_272_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 305, Type: SPI, Type ID: 273 */
  #ifdef SPI_273_ISR
  SPI_273_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 306, Type: SPI, Type ID: 274 */
  #ifdef SPI_274_ISR
  SPI_274_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 307, Type: SPI, Type ID: 275 */
  #ifdef SPI_275_ISR
  SPI_275_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 308, Type: SPI, Type ID: 276 */
  #ifdef SPI_276_ISR
  SPI_276_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 309, Type: SPI, Type ID: 277 */
  #ifdef SPI_277_ISR
  SPI_277_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 310, Type: SPI, Type ID: 278 */
  #ifdef SPI_278_ISR
  SPI_278_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 311, Type: SPI, Type ID: 279 */
  #ifdef SPI_279_ISR
  SPI_279_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 312, Type: SPI, Type ID: 280 */
  #ifdef SPI_280_ISR
  SPI_280_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 313, Type: SPI, Type ID: 281 */
  #ifdef SPI_281_ISR
  SPI_281_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 314, Type: SPI, Type ID: 282 */
  #ifdef SPI_282_ISR
  SPI_282_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 315, Type: SPI, Type ID: 283 */
  #ifdef SPI_283_ISR
  SPI_283_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 316, Type: SPI, Type ID: 284 */
  #ifdef SPI_284_ISR
  SPI_284_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 317, Type: SPI, Type ID: 285 */
  #ifdef SPI_285_ISR
  SPI_285_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 318, Type: SPI, Type ID: 286 */
  #ifdef SPI_286_ISR
  SPI_286_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 319, Type: SPI, Type ID: 287 */
  #ifdef SPI_287_ISR
  SPI_287_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 320, Type: SPI, Type ID: 288 */
  #ifdef SPI_288_ISR
  SPI_288_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 321, Type: SPI, Type ID: 289 */
  #ifdef SPI_289_ISR
  SPI_289_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 322, Type: SPI, Type ID: 290 */
  #ifdef SPI_290_ISR
  SPI_290_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 323, Type: SPI, Type ID: 291 */
  #ifdef SPI_291_ISR
  SPI_291_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 324, Type: SPI, Type ID: 292 */
  #ifdef SPI_292_ISR
  SPI_292_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 325, Type: SPI, Type ID: 293 */
  #ifdef SPI_293_ISR
  SPI_293_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 326, Type: SPI, Type ID: 294 */
  #ifdef SPI_294_ISR
  SPI_294_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 327, Type: SPI, Type ID: 295 */
  #ifdef SPI_295_ISR
  SPI_295_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 328, Type: SPI, Type ID: 296 */
  #ifdef SPI_296_ISR
  SPI_296_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 329, Type: SPI, Type ID: 297 */
  #ifdef SPI_297_ISR
  SPI_297_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 330, Type: SPI, Type ID: 298 */
  #ifdef SPI_298_ISR
  SPI_298_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 331, Type: SPI, Type ID: 299 */
  #ifdef SPI_299_ISR
  SPI_299_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 332, Type: SPI, Type ID: 300 */
  #ifdef SPI_300_ISR
  SPI_300_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 333, Type: SPI, Type ID: 301 */
  #ifdef SPI_301_ISR
  SPI_301_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 334, Type: SPI, Type ID: 302 */
  #ifdef SPI_302_ISR
  SPI_302_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 335, Type: SPI, Type ID: 303 */
  #ifdef SPI_303_ISR
  SPI_303_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 336, Type: SPI, Type ID: 304 */
  #ifdef SPI_304_ISR
  SPI_304_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 337, Type: SPI, Type ID: 305 */
  #ifdef SPI_305_ISR
  SPI_305_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 338, Type: SPI, Type ID: 306 */
  #ifdef SPI_306_ISR
  SPI_306_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 339, Type: SPI, Type ID: 307 */
  #ifdef SPI_307_ISR
  SPI_307_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 340, Type: SPI, Type ID: 308 */
  #ifdef SPI_308_ISR
  SPI_308_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 341, Type: SPI, Type ID: 309 */
  #ifdef SPI_309_ISR
  SPI_309_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 342, Type: SPI, Type ID: 310 */
  #ifdef SPI_310_ISR
  SPI_310_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 343, Type: SPI, Type ID: 311 */
  #ifdef SPI_311_ISR
  SPI_311_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 344, Type: SPI, Type ID: 312 */
  #ifdef SPI_312_ISR
  SPI_312_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 345, Type: SPI, Type ID: 313 */
  #ifdef SPI_313_ISR
  SPI_313_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 346, Type: SPI, Type ID: 314 */
  #ifdef SPI_314_ISR
  SPI_314_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 347, Type: SPI, Type ID: 315 */
  #ifdef SPI_315_ISR
  SPI_315_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 348, Type: SPI, Type ID: 316 */
  #ifdef SPI_316_ISR
  SPI_316_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 349, Type: SPI, Type ID: 317 */
  #ifdef SPI_317_ISR
  SPI_317_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 350, Type: SPI, Type ID: 318 */
  #ifdef SPI_318_ISR
  SPI_318_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 351, Type: SPI, Type ID: 319 */
  #ifdef SPI_319_ISR
  SPI_319_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 352, Type: SPI, Type ID: 320 */
  #ifdef SPI_320_ISR
  SPI_320_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 353, Type: SPI, Type ID: 321 */
  #ifdef SPI_321_ISR
  SPI_321_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 354, Type: SPI, Type ID: 322 */
  #ifdef SPI_322_ISR
  SPI_322_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 355, Type: SPI, Type ID: 323 */
  #ifdef SPI_323_ISR
  SPI_323_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 356, Type: SPI, Type ID: 324 */
  #ifdef SPI_324_ISR
  SPI_324_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 357, Type: SPI, Type ID: 325 */
  #ifdef SPI_325_ISR
  SPI_325_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 358, Type: SPI, Type ID: 326 */
  #ifdef SPI_326_ISR
  SPI_326_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 359, Type: SPI, Type ID: 327 */
  #ifdef SPI_327_ISR
  SPI_327_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 360, Type: SPI, Type ID: 328 */
  #ifdef SPI_328_ISR
  SPI_328_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 361, Type: SPI, Type ID: 329 */
  #ifdef SPI_329_ISR
  SPI_329_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 362, Type: SPI, Type ID: 330 */
  #ifdef SPI_330_ISR
  SPI_330_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 363, Type: SPI, Type ID: 331 */
  #ifdef SPI_331_ISR
  SPI_331_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 364, Type: SPI, Type ID: 332 */
  #ifdef SPI_332_ISR
  SPI_332_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 365, Type: SPI, Type ID: 333 */
  #ifdef SPI_333_ISR
  SPI_333_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 366, Type: SPI, Type ID: 334 */
  #ifdef SPI_334_ISR
  SPI_334_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 367, Type: SPI, Type ID: 335 */
  #ifdef SPI_335_ISR
  SPI_335_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 368, Type: SPI, Type ID: 336 */
  #ifdef SPI_336_ISR
  SPI_336_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 369, Type: SPI, Type ID: 337 */
  #ifdef SPI_337_ISR
  SPI_337_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 370, Type: SPI, Type ID: 338 */
  #ifdef SPI_338_ISR
  SPI_338_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 371, Type: SPI, Type ID: 339 */
  #ifdef SPI_339_ISR
  SPI_339_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 372, Type: SPI, Type ID: 340 */
  #ifdef SPI_340_ISR
  SPI_340_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 373, Type: SPI, Type ID: 341 */
  #ifdef SPI_341_ISR
  SPI_341_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 374, Type: SPI, Type ID: 342 */
  #ifdef SPI_342_ISR
  SPI_342_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 375, Type: SPI, Type ID: 343 */
  #ifdef SPI_343_ISR
  SPI_343_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 376, Type: SPI, Type ID: 344 */
  #ifdef SPI_344_ISR
  SPI_344_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 377, Type: SPI, Type ID: 345 */
  #ifdef SPI_345_ISR
  SPI_345_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 378, Type: SPI, Type ID: 346 */
  #ifdef SPI_346_ISR
  SPI_346_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 379, Type: SPI, Type ID: 347 */
  #ifdef SPI_347_ISR
  SPI_347_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 380, Type: SPI, Type ID: 348 */
  #ifdef SPI_348_ISR
  SPI_348_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 381, Type: SPI, Type ID: 349 */
  #ifdef SPI_349_ISR
  SPI_349_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 382, Type: SPI, Type ID: 350 */
  #ifdef SPI_350_ISR
  SPI_350_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 383, Type: SPI, Type ID: 351 */
  #ifdef SPI_351_ISR
  SPI_351_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 384, Type: SPI, Type ID: 352 */
  #ifdef SPI_352_ISR
  SPI_352_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 385, Type: SPI, Type ID: 353 */
  #ifdef SPI_353_ISR
  SPI_353_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 386, Type: SPI, Type ID: 354 */
  #ifdef SPI_354_ISR
  SPI_354_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 387, Type: SPI, Type ID: 355 */
  #ifdef SPI_355_ISR
  SPI_355_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 388, Type: SPI, Type ID: 356 */
  #ifdef SPI_356_ISR
  SPI_356_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 389, Type: SPI, Type ID: 357 */
  #ifdef SPI_357_ISR
  SPI_357_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 390, Type: SPI, Type ID: 358 */
  #ifdef SPI_358_ISR
  SPI_358_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 391, Type: SPI, Type ID: 359 */
  #ifdef SPI_359_ISR
  SPI_359_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 392, Type: SPI, Type ID: 360 */
  #ifdef SPI_360_ISR
  SPI_360_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 393, Type: SPI, Type ID: 361 */
  #ifdef SPI_361_ISR
  SPI_361_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 394, Type: SPI, Type ID: 362 */
  #ifdef SPI_362_ISR
  SPI_362_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 395, Type: SPI, Type ID: 363 */
  #ifdef SPI_363_ISR
  SPI_363_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 396, Type: SPI, Type ID: 364 */
  #ifdef SPI_364_ISR
  SPI_364_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 397, Type: SPI, Type ID: 365 */
  #ifdef SPI_365_ISR
  SPI_365_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 398, Type: SPI, Type ID: 366 */
  #ifdef SPI_366_ISR
  SPI_366_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 399, Type: SPI, Type ID: 367 */
  #ifdef SPI_367_ISR
  SPI_367_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 400, Type: SPI, Type ID: 368 */
  #ifdef SPI_368_ISR
  SPI_368_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 401, Type: SPI, Type ID: 369 */
  #ifdef SPI_369_ISR
  SPI_369_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 402, Type: SPI, Type ID: 370 */
  #ifdef SPI_370_ISR
  SPI_370_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 403, Type: SPI, Type ID: 371 */
  #ifdef SPI_371_ISR
  SPI_371_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 404, Type: SPI, Type ID: 372 */
  #ifdef SPI_372_ISR
  SPI_372_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 405, Type: SPI, Type ID: 373 */
  #ifdef SPI_373_ISR
  SPI_373_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 406, Type: SPI, Type ID: 374 */
  #ifdef SPI_374_ISR
  SPI_374_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 407, Type: SPI, Type ID: 375 */
  #ifdef SPI_375_ISR
  SPI_375_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 408, Type: SPI, Type ID: 376 */
  #ifdef SPI_376_ISR
  SPI_376_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 409, Type: SPI, Type ID: 377 */
  #ifdef SPI_377_ISR
  SPI_377_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 410, Type: SPI, Type ID: 378 */
  #ifdef SPI_378_ISR
  SPI_378_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 411, Type: SPI, Type ID: 379 */
  #ifdef SPI_379_ISR
  SPI_379_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 412, Type: SPI, Type ID: 380 */
  #ifdef SPI_380_ISR
  SPI_380_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 413, Type: SPI, Type ID: 381 */
  #ifdef SPI_381_ISR
  SPI_381_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 414, Type: SPI, Type ID: 382 */
  #ifdef SPI_382_ISR
  SPI_382_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 415, Type: SPI, Type ID: 383 */
  #ifdef SPI_383_ISR
  SPI_383_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 416, Type: SPI, Type ID: 384 */
  #ifdef SPI_384_ISR
  SPI_384_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 417, Type: SPI, Type ID: 385 */
  #ifdef SPI_385_ISR
  SPI_385_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 418, Type: SPI, Type ID: 386 */
  #ifdef SPI_386_ISR
  SPI_386_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 419, Type: SPI, Type ID: 387 */
  #ifdef SPI_387_ISR
  SPI_387_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 420, Type: SPI, Type ID: 388 */
  #ifdef SPI_388_ISR
  SPI_388_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 421, Type: SPI, Type ID: 389 */
  #ifdef SPI_389_ISR
  SPI_389_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 422, Type: SPI, Type ID: 390 */
  #ifdef SPI_390_ISR
  SPI_390_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 423, Type: SPI, Type ID: 391 */
  #ifdef SPI_391_ISR
  SPI_391_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 424, Type: SPI, Type ID: 392 */
  #ifdef SPI_392_ISR
  SPI_392_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 425, Type: SPI, Type ID: 393 */
  #ifdef SPI_393_ISR
  SPI_393_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 426, Type: SPI, Type ID: 394 */
  #ifdef SPI_394_ISR
  SPI_394_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 427, Type: SPI, Type ID: 395 */
  #ifdef SPI_395_ISR
  SPI_395_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 428, Type: SPI, Type ID: 396 */
  #ifdef SPI_396_ISR
  SPI_396_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 429, Type: SPI, Type ID: 397 */
  #ifdef SPI_397_ISR
  SPI_397_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 430, Type: SPI, Type ID: 398 */
  #ifdef SPI_398_ISR
  SPI_398_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 431, Type: SPI, Type ID: 399 */
  #ifdef SPI_399_ISR
  SPI_399_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 432, Type: SPI, Type ID: 400 */
  #ifdef SPI_400_ISR
  SPI_400_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 433, Type: SPI, Type ID: 401 */
  #ifdef SPI_401_ISR
  SPI_401_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 434, Type: SPI, Type ID: 402 */
  #ifdef SPI_402_ISR
  SPI_402_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 435, Type: SPI, Type ID: 403 */
  #ifdef SPI_403_ISR
  SPI_403_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 436, Type: SPI, Type ID: 404 */
  #ifdef SPI_404_ISR
  SPI_404_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 437, Type: SPI, Type ID: 405 */
  #ifdef SPI_405_ISR
  SPI_405_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 438, Type: SPI, Type ID: 406 */
  #ifdef SPI_406_ISR
  SPI_406_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 439, Type: SPI, Type ID: 407 */
  #ifdef SPI_407_ISR
  SPI_407_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 440, Type: SPI, Type ID: 408 */
  #ifdef SPI_408_ISR
  SPI_408_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 441, Type: SPI, Type ID: 409 */
  #ifdef SPI_409_ISR
  SPI_409_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 442, Type: SPI, Type ID: 410 */
  #ifdef SPI_410_ISR
  SPI_410_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 443, Type: SPI, Type ID: 411 */
  #ifdef SPI_411_ISR
  SPI_411_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 444, Type: SPI, Type ID: 412 */
  #ifdef SPI_412_ISR
  SPI_412_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 445, Type: SPI, Type ID: 413 */
  #ifdef SPI_413_ISR
  SPI_413_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 446, Type: SPI, Type ID: 414 */
  #ifdef SPI_414_ISR
  SPI_414_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 447, Type: SPI, Type ID: 415 */
  #ifdef SPI_415_ISR
  SPI_415_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 448, Type: SPI, Type ID: 416 */
  #ifdef SPI_416_ISR
  SPI_416_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 449, Type: SPI, Type ID: 417 */
  #ifdef SPI_417_ISR
  SPI_417_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 450, Type: SPI, Type ID: 418 */
  #ifdef SPI_418_ISR
  SPI_418_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 451, Type: SPI, Type ID: 419 */
  #ifdef SPI_419_ISR
  SPI_419_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 452, Type: SPI, Type ID: 420 */
  #ifdef SPI_420_ISR
  SPI_420_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 453, Type: SPI, Type ID: 421 */
  #ifdef SPI_421_ISR
  SPI_421_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 454, Type: SPI, Type ID: 422 */
  #ifdef SPI_422_ISR
  SPI_422_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 455, Type: SPI, Type ID: 423 */
  #ifdef SPI_423_ISR
  SPI_423_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 456, Type: SPI, Type ID: 424 */
  #ifdef SPI_424_ISR
  SPI_424_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 457, Type: SPI, Type ID: 425 */
  #ifdef SPI_425_ISR
  SPI_425_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 458, Type: SPI, Type ID: 426 */
  #ifdef SPI_426_ISR
  SPI_426_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 459, Type: SPI, Type ID: 427 */
  #ifdef SPI_427_ISR
  SPI_427_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 460, Type: SPI, Type ID: 428 */
  #ifdef SPI_428_ISR
  SPI_428_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 461, Type: SPI, Type ID: 429 */
  #ifdef SPI_429_ISR
  SPI_429_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 462, Type: SPI, Type ID: 430 */
  #ifdef SPI_430_ISR
  SPI_430_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 463, Type: SPI, Type ID: 431 */
  #ifdef SPI_431_ISR
  SPI_431_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 464, Type: SPI, Type ID: 432 */
  #ifdef SPI_432_ISR
  SPI_432_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 465, Type: SPI, Type ID: 433 */
  #ifdef SPI_433_ISR
  SPI_433_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 466, Type: SPI, Type ID: 434 */
  #ifdef SPI_434_ISR
  SPI_434_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 467, Type: SPI, Type ID: 435 */
  #ifdef SPI_435_ISR
  SPI_435_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 468, Type: SPI, Type ID: 436 */
  #ifdef SPI_436_ISR
  SPI_436_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 469, Type: SPI, Type ID: 437 */
  #ifdef SPI_437_ISR
  SPI_437_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 470, Type: SPI, Type ID: 438 */
  #ifdef SPI_438_ISR
  SPI_438_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 471, Type: SPI, Type ID: 439 */
  #ifdef SPI_439_ISR
  SPI_439_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 472, Type: SPI, Type ID: 440 */
  #ifdef SPI_440_ISR
  SPI_440_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 473, Type: SPI, Type ID: 441 */
  #ifdef SPI_441_ISR
  SPI_441_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 474, Type: SPI, Type ID: 442 */
  #ifdef SPI_442_ISR
  SPI_442_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 475, Type: SPI, Type ID: 443 */
  #ifdef SPI_443_ISR
  SPI_443_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 476, Type: SPI, Type ID: 444 */
  #ifdef SPI_444_ISR
  SPI_444_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 477, Type: SPI, Type ID: 445 */
  #ifdef SPI_445_ISR
  SPI_445_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 478, Type: SPI, Type ID: 446 */
  #ifdef SPI_446_ISR
  SPI_446_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 479, Type: SPI, Type ID: 447 */
  #ifdef SPI_447_ISR
  SPI_447_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 480, Type: SPI, Type ID: 448 */
  #ifdef SPI_448_ISR
  SPI_448_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 481, Type: SPI, Type ID: 449 */
  #ifdef SPI_449_ISR
  SPI_449_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 482, Type: SPI, Type ID: 450 */
  #ifdef SPI_450_ISR
  SPI_450_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 483, Type: SPI, Type ID: 451 */
  #ifdef SPI_451_ISR
  SPI_451_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 484, Type: SPI, Type ID: 452 */
  #ifdef SPI_452_ISR
  SPI_452_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 485, Type: SPI, Type ID: 453 */
  #ifdef SPI_453_ISR
  SPI_453_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 486, Type: SPI, Type ID: 454 */
  #ifdef SPI_454_ISR
  SPI_454_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 487, Type: SPI, Type ID: 455 */
  #ifdef SPI_455_ISR
  SPI_455_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 488, Type: SPI, Type ID: 456 */
  #ifdef SPI_456_ISR
  SPI_456_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 489, Type: SPI, Type ID: 457 */
  #ifdef SPI_457_ISR
  SPI_457_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 490, Type: SPI, Type ID: 458 */
  #ifdef SPI_458_ISR
  SPI_458_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 491, Type: SPI, Type ID: 459 */
  #ifdef SPI_459_ISR
  SPI_459_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 492, Type: SPI, Type ID: 460 */
  #ifdef SPI_460_ISR
  SPI_460_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 493, Type: SPI, Type ID: 461 */
  #ifdef SPI_461_ISR
  SPI_461_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 494, Type: SPI, Type ID: 462 */
  #ifdef SPI_462_ISR
  SPI_462_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 495, Type: SPI, Type ID: 463 */
  #ifdef SPI_463_ISR
  SPI_463_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 496, Type: SPI, Type ID: 464 */
  #ifdef SPI_464_ISR
  SPI_464_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 497, Type: SPI, Type ID: 465 */
  #ifdef SPI_465_ISR
  SPI_465_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 498, Type: SPI, Type ID: 466 */
  #ifdef SPI_466_ISR
  SPI_466_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 499, Type: SPI, Type ID: 467 */
  #ifdef SPI_467_ISR
  SPI_467_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 500, Type: SPI, Type ID: 468 */
  #ifdef SPI_468_ISR
  SPI_468_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 501, Type: SPI, Type ID: 469 */
  #ifdef SPI_469_ISR
  SPI_469_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 502, Type: SPI, Type ID: 470 */
  #ifdef SPI_470_ISR
  SPI_470_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 503, Type: SPI, Type ID: 471 */
  #ifdef SPI_471_ISR
  SPI_471_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 504, Type: SPI, Type ID: 472 */
  #ifdef SPI_472_ISR
  SPI_472_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 505, Type: SPI, Type ID: 473 */
  #ifdef SPI_473_ISR
  SPI_473_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 506, Type: SPI, Type ID: 474 */
  #ifdef SPI_474_ISR
  SPI_474_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 507, Type: SPI, Type ID: 475 */
  #ifdef SPI_475_ISR
  SPI_475_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 508, Type: SPI, Type ID: 476 */
  #ifdef SPI_476_ISR
  SPI_476_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 509, Type: SPI, Type ID: 477 */
  #ifdef SPI_477_ISR
  SPI_477_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 510, Type: SPI, Type ID: 478 */
  #ifdef SPI_478_ISR
  SPI_478_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 511, Type: SPI, Type ID: 479 */
  #ifdef SPI_479_ISR
  SPI_479_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 512, Type: SPI, Type ID: 480 */
  #ifdef SPI_480_ISR
  SPI_480_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 513, Type: SPI, Type ID: 481 */
  #ifdef SPI_481_ISR
  SPI_481_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 514, Type: SPI, Type ID: 482 */
  #ifdef SPI_482_ISR
  SPI_482_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 515, Type: SPI, Type ID: 483 */
  #ifdef SPI_483_ISR
  SPI_483_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 516, Type: SPI, Type ID: 484 */
  #ifdef SPI_484_ISR
  SPI_484_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 517, Type: SPI, Type ID: 485 */
  #ifdef SPI_485_ISR
  SPI_485_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 518, Type: SPI, Type ID: 486 */
  #ifdef SPI_486_ISR
  SPI_486_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 519, Type: SPI, Type ID: 487 */
  #ifdef SPI_487_ISR
  SPI_487_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 520, Type: SPI, Type ID: 488 */
  #ifdef SPI_488_ISR
  SPI_488_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 521, Type: SPI, Type ID: 489 */
  #ifdef SPI_489_ISR
  SPI_489_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 522, Type: SPI, Type ID: 490 */
  #ifdef SPI_490_ISR
  SPI_490_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 523, Type: SPI, Type ID: 491 */
  #ifdef SPI_491_ISR
  SPI_491_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 524, Type: SPI, Type ID: 492 */
  #ifdef SPI_492_ISR
  SPI_492_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 525, Type: SPI, Type ID: 493 */
  #ifdef SPI_493_ISR
  SPI_493_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 526, Type: SPI, Type ID: 494 */
  #ifdef SPI_494_ISR
  SPI_494_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 527, Type: SPI, Type ID: 495 */
  #ifdef SPI_495_ISR
  SPI_495_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 528, Type: SPI, Type ID: 496 */
  #ifdef SPI_496_ISR
  SPI_496_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 529, Type: SPI, Type ID: 497 */
  #ifdef SPI_497_ISR
  SPI_497_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 530, Type: SPI, Type ID: 498 */
  #ifdef SPI_498_ISR
  SPI_498_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 531, Type: SPI, Type ID: 499 */
  #ifdef SPI_499_ISR
  SPI_499_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 532, Type: SPI, Type ID: 500 */
  #ifdef SPI_500_ISR
  SPI_500_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 533, Type: SPI, Type ID: 501 */
  #ifdef SPI_501_ISR
  SPI_501_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 534, Type: SPI, Type ID: 502 */
  #ifdef SPI_502_ISR
  SPI_502_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 535, Type: SPI, Type ID: 503 */
  #ifdef SPI_503_ISR
  SPI_503_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 536, Type: SPI, Type ID: 504 */
  #ifdef SPI_504_ISR
  SPI_504_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 537, Type: SPI, Type ID: 505 */
  #ifdef SPI_505_ISR
  SPI_505_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 538, Type: SPI, Type ID: 506 */
  #ifdef SPI_506_ISR
  SPI_506_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 539, Type: SPI, Type ID: 507 */
  #ifdef SPI_507_ISR
  SPI_507_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 540, Type: SPI, Type ID: 508 */
  #ifdef SPI_508_ISR
  SPI_508_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 541, Type: SPI, Type ID: 509 */
  #ifdef SPI_509_ISR
  SPI_509_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 542, Type: SPI, Type ID: 510 */
  #ifdef SPI_510_ISR
  SPI_510_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 543, Type: SPI, Type ID: 511 */
  #ifdef SPI_511_ISR
  SPI_511_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 544, Type: SPI, Type ID: 512 */
  #ifdef SPI_512_ISR
  SPI_512_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 545, Type: SPI, Type ID: 513 */
  #ifdef SPI_513_ISR
  SPI_513_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 546, Type: SPI, Type ID: 514 */
  #ifdef SPI_514_ISR
  SPI_514_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 547, Type: SPI, Type ID: 515 */
  #ifdef SPI_515_ISR
  SPI_515_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 548, Type: SPI, Type ID: 516 */
  #ifdef SPI_516_ISR
  SPI_516_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 549, Type: SPI, Type ID: 517 */
  #ifdef SPI_517_ISR
  SPI_517_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 550, Type: SPI, Type ID: 518 */
  #ifdef SPI_518_ISR
  SPI_518_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 551, Type: SPI, Type ID: 519 */
  #ifdef SPI_519_ISR
  SPI_519_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 552, Type: SPI, Type ID: 520 */
  #ifdef SPI_520_ISR
  SPI_520_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 553, Type: SPI, Type ID: 521 */
  #ifdef SPI_521_ISR
  SPI_521_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 554, Type: SPI, Type ID: 522 */
  #ifdef SPI_522_ISR
  SPI_522_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 555, Type: SPI, Type ID: 523 */
  #ifdef SPI_523_ISR
  SPI_523_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 556, Type: SPI, Type ID: 524 */
  #ifdef SPI_524_ISR
  SPI_524_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 557, Type: SPI, Type ID: 525 */
  #ifdef SPI_525_ISR
  SPI_525_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 558, Type: SPI, Type ID: 526 */
  #ifdef SPI_526_ISR
  SPI_526_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 559, Type: SPI, Type ID: 527 */
  #ifdef SPI_527_ISR
  SPI_527_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 560, Type: SPI, Type ID: 528 */
  #ifdef SPI_528_ISR
  SPI_528_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 561, Type: SPI, Type ID: 529 */
  #ifdef SPI_529_ISR
  SPI_529_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 562, Type: SPI, Type ID: 530 */
  #ifdef SPI_530_ISR
  SPI_530_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 563, Type: SPI, Type ID: 531 */
  #ifdef SPI_531_ISR
  SPI_531_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 564, Type: SPI, Type ID: 532 */
  #ifdef SPI_532_ISR
  SPI_532_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 565, Type: SPI, Type ID: 533 */
  #ifdef SPI_533_ISR
  SPI_533_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 566, Type: SPI, Type ID: 534 */
  #ifdef SPI_534_ISR
  SPI_534_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 567, Type: SPI, Type ID: 535 */
  #ifdef SPI_535_ISR
  SPI_535_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 568, Type: SPI, Type ID: 536 */
  #ifdef SPI_536_ISR
  SPI_536_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 569, Type: SPI, Type ID: 537 */
  #ifdef SPI_537_ISR
  SPI_537_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 570, Type: SPI, Type ID: 538 */
  #ifdef SPI_538_ISR
  SPI_538_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 571, Type: SPI, Type ID: 539 */
  #ifdef SPI_539_ISR
  SPI_539_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 572, Type: SPI, Type ID: 540 */
  #ifdef SPI_540_ISR
  SPI_540_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 573, Type: SPI, Type ID: 541 */
  #ifdef SPI_541_ISR
  SPI_541_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 574, Type: SPI, Type ID: 542 */
  #ifdef SPI_542_ISR
  SPI_542_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 575, Type: SPI, Type ID: 543 */
  #ifdef SPI_543_ISR
  SPI_543_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 576, Type: SPI, Type ID: 544 */
  #ifdef SPI_544_ISR
  SPI_544_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 577, Type: SPI, Type ID: 545 */
  #ifdef SPI_545_ISR
  SPI_545_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 578, Type: SPI, Type ID: 546 */
  #ifdef SPI_546_ISR
  SPI_546_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 579, Type: SPI, Type ID: 547 */
  #ifdef SPI_547_ISR
  SPI_547_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 580, Type: SPI, Type ID: 548 */
  #ifdef SPI_548_ISR
  SPI_548_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 581, Type: SPI, Type ID: 549 */
  #ifdef SPI_549_ISR
  SPI_549_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 582, Type: SPI, Type ID: 550 */
  #ifdef SPI_550_ISR
  SPI_550_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 583, Type: SPI, Type ID: 551 */
  #ifdef SPI_551_ISR
  SPI_551_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 584, Type: SPI, Type ID: 552 */
  #ifdef SPI_552_ISR
  SPI_552_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 585, Type: SPI, Type ID: 553 */
  #ifdef SPI_553_ISR
  SPI_553_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 586, Type: SPI, Type ID: 554 */
  #ifdef SPI_554_ISR
  SPI_554_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 587, Type: SPI, Type ID: 555 */
  #ifdef SPI_555_ISR
  SPI_555_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 588, Type: SPI, Type ID: 556 */
  #ifdef SPI_556_ISR
  SPI_556_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 589, Type: SPI, Type ID: 557 */
  #ifdef SPI_557_ISR
  SPI_557_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 590, Type: SPI, Type ID: 558 */
  #ifdef SPI_558_ISR
  SPI_558_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 591, Type: SPI, Type ID: 559 */
  #ifdef SPI_559_ISR
  SPI_559_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 592, Type: SPI, Type ID: 560 */
  #ifdef SPI_560_ISR
  SPI_560_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 593, Type: SPI, Type ID: 561 */
  #ifdef SPI_561_ISR
  SPI_561_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 594, Type: SPI, Type ID: 562 */
  #ifdef SPI_562_ISR
  SPI_562_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 595, Type: SPI, Type ID: 563 */
  #ifdef SPI_563_ISR
  SPI_563_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 596, Type: SPI, Type ID: 564 */
  #ifdef SPI_564_ISR
  SPI_564_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 597, Type: SPI, Type ID: 565 */
  #ifdef SPI_565_ISR
  SPI_565_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 598, Type: SPI, Type ID: 566 */
  #ifdef SPI_566_ISR
  SPI_566_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 599, Type: SPI, Type ID: 567 */
  #ifdef SPI_567_ISR
  SPI_567_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 600, Type: SPI, Type ID: 568 */
  #ifdef SPI_568_ISR
  SPI_568_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 601, Type: SPI, Type ID: 569 */
  #ifdef SPI_569_ISR
  SPI_569_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 602, Type: SPI, Type ID: 570 */
  #ifdef SPI_570_ISR
  SPI_570_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 603, Type: SPI, Type ID: 571 */
  #ifdef SPI_571_ISR
  SPI_571_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 604, Type: SPI, Type ID: 572 */
  #ifdef SPI_572_ISR
  SPI_572_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 605, Type: SPI, Type ID: 573 */
  #ifdef SPI_573_ISR
  SPI_573_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 606, Type: SPI, Type ID: 574 */
  #ifdef SPI_574_ISR
  SPI_574_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 607, Type: SPI, Type ID: 575 */
  #ifdef SPI_575_ISR
  SPI_575_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 608, Type: SPI, Type ID: 576 */
  #ifdef SPI_576_ISR
  SPI_576_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 609, Type: SPI, Type ID: 577 */
  #ifdef SPI_577_ISR
  SPI_577_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 610, Type: SPI, Type ID: 578 */
  #ifdef SPI_578_ISR
  SPI_578_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 611, Type: SPI, Type ID: 579 */
  #ifdef SPI_579_ISR
  SPI_579_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 612, Type: SPI, Type ID: 580 */
  #ifdef SPI_580_ISR
  SPI_580_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 613, Type: SPI, Type ID: 581 */
  #ifdef SPI_581_ISR
  SPI_581_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 614, Type: SPI, Type ID: 582 */
  #ifdef SPI_582_ISR
  SPI_582_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 615, Type: SPI, Type ID: 583 */
  #ifdef SPI_583_ISR
  SPI_583_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 616, Type: SPI, Type ID: 584 */
  #ifdef SPI_584_ISR
  SPI_584_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 617, Type: SPI, Type ID: 585 */
  #ifdef SPI_585_ISR
  SPI_585_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 618, Type: SPI, Type ID: 586 */
  #ifdef SPI_586_ISR
  SPI_586_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 619, Type: SPI, Type ID: 587 */
  #ifdef SPI_587_ISR
  SPI_587_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 620, Type: SPI, Type ID: 588 */
  #ifdef SPI_588_ISR
  SPI_588_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 621, Type: SPI, Type ID: 589 */
  #ifdef SPI_589_ISR
  SPI_589_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 622, Type: SPI, Type ID: 590 */
  #ifdef SPI_590_ISR
  SPI_590_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 623, Type: SPI, Type ID: 591 */
  #ifdef SPI_591_ISR
  SPI_591_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 624, Type: SPI, Type ID: 592 */
  #ifdef SPI_592_ISR
  SPI_592_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 625, Type: SPI, Type ID: 593 */
  #ifdef SPI_593_ISR
  SPI_593_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 626, Type: SPI, Type ID: 594 */
  #ifdef SPI_594_ISR
  SPI_594_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 627, Type: SPI, Type ID: 595 */
  #ifdef SPI_595_ISR
  SPI_595_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 628, Type: SPI, Type ID: 596 */
  #ifdef SPI_596_ISR
  SPI_596_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 629, Type: SPI, Type ID: 597 */
  #ifdef SPI_597_ISR
  SPI_597_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 630, Type: SPI, Type ID: 598 */
  #ifdef SPI_598_ISR
  SPI_598_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 631, Type: SPI, Type ID: 599 */
  #ifdef SPI_599_ISR
  SPI_599_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 632, Type: SPI, Type ID: 600 */
  #ifdef SPI_600_ISR
  SPI_600_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 633, Type: SPI, Type ID: 601 */
  #ifdef SPI_601_ISR
  SPI_601_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 634, Type: SPI, Type ID: 602 */
  #ifdef SPI_602_ISR
  SPI_602_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 635, Type: SPI, Type ID: 603 */
  #ifdef SPI_603_ISR
  SPI_603_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 636, Type: SPI, Type ID: 604 */
  #ifdef SPI_604_ISR
  SPI_604_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 637, Type: SPI, Type ID: 605 */
  #ifdef SPI_605_ISR
  SPI_605_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 638, Type: SPI, Type ID: 606 */
  #ifdef SPI_606_ISR
  SPI_606_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 639, Type: SPI, Type ID: 607 */
  #ifdef SPI_607_ISR
  SPI_607_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 640, Type: SPI, Type ID: 608 */
  #ifdef SPI_608_ISR
  SPI_608_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 641, Type: SPI, Type ID: 609 */
  #ifdef SPI_609_ISR
  SPI_609_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 642, Type: SPI, Type ID: 610 */
  #ifdef SPI_610_ISR
  SPI_610_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 643, Type: SPI, Type ID: 611 */
  #ifdef SPI_611_ISR
  SPI_611_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 644, Type: SPI, Type ID: 612 */
  #ifdef SPI_612_ISR
  SPI_612_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 645, Type: SPI, Type ID: 613 */
  #ifdef SPI_613_ISR
  SPI_613_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 646, Type: SPI, Type ID: 614 */
  #ifdef SPI_614_ISR
  SPI_614_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 647, Type: SPI, Type ID: 615 */
  #ifdef SPI_615_ISR
  SPI_615_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 648, Type: SPI, Type ID: 616 */
  #ifdef SPI_616_ISR
  SPI_616_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 649, Type: SPI, Type ID: 617 */
  #ifdef SPI_617_ISR
  SPI_617_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 650, Type: SPI, Type ID: 618 */
  #ifdef SPI_618_ISR
  SPI_618_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 651, Type: SPI, Type ID: 619 */
  #ifdef SPI_619_ISR
  SPI_619_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 652, Type: SPI, Type ID: 620 */
  #ifdef SPI_620_ISR
  SPI_620_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 653, Type: SPI, Type ID: 621 */
  #ifdef SPI_621_ISR
  SPI_621_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 654, Type: SPI, Type ID: 622 */
  #ifdef SPI_622_ISR
  SPI_622_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 655, Type: SPI, Type ID: 623 */
  #ifdef SPI_623_ISR
  SPI_623_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 656, Type: SPI, Type ID: 624 */
  #ifdef SPI_624_ISR
  SPI_624_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 657, Type: SPI, Type ID: 625 */
  #ifdef SPI_625_ISR
  SPI_625_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 658, Type: SPI, Type ID: 626 */
  #ifdef SPI_626_ISR
  SPI_626_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 659, Type: SPI, Type ID: 627 */
  #ifdef SPI_627_ISR
  SPI_627_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 660, Type: SPI, Type ID: 628 */
  #ifdef SPI_628_ISR
  SPI_628_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 661, Type: SPI, Type ID: 629 */
  #ifdef SPI_629_ISR
  SPI_629_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 662, Type: SPI, Type ID: 630 */
  #ifdef SPI_630_ISR
  SPI_630_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 663, Type: SPI, Type ID: 631 */
  #ifdef SPI_631_ISR
  SPI_631_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 664, Type: SPI, Type ID: 632 */
  #ifdef SPI_632_ISR
  SPI_632_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 665, Type: SPI, Type ID: 633 */
  #ifdef SPI_633_ISR
  SPI_633_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 666, Type: SPI, Type ID: 634 */
  #ifdef SPI_634_ISR
  SPI_634_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 667, Type: SPI, Type ID: 635 */
  #ifdef SPI_635_ISR
  SPI_635_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 668, Type: SPI, Type ID: 636 */
  #ifdef SPI_636_ISR
  SPI_636_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 669, Type: SPI, Type ID: 637 */
  #ifdef SPI_637_ISR
  SPI_637_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 670, Type: SPI, Type ID: 638 */
  #ifdef SPI_638_ISR
  SPI_638_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 671, Type: SPI, Type ID: 639 */
  #ifdef SPI_639_ISR
  SPI_639_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 672, Type: SPI, Type ID: 640 */
  #ifdef SPI_640_ISR
  SPI_640_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 673, Type: SPI, Type ID: 641 */
  #ifdef SPI_641_ISR
  SPI_641_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 674, Type: SPI, Type ID: 642 */
  #ifdef SPI_642_ISR
  SPI_642_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 675, Type: SPI, Type ID: 643 */
  #ifdef SPI_643_ISR
  SPI_643_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 676, Type: SPI, Type ID: 644 */
  #ifdef SPI_644_ISR
  SPI_644_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 677, Type: SPI, Type ID: 645 */
  #ifdef SPI_645_ISR
  SPI_645_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 678, Type: SPI, Type ID: 646 */
  #ifdef SPI_646_ISR
  SPI_646_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 679, Type: SPI, Type ID: 647 */
  #ifdef SPI_647_ISR
  SPI_647_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 680, Type: SPI, Type ID: 648 */
  #ifdef SPI_648_ISR
  SPI_648_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 681, Type: SPI, Type ID: 649 */
  #ifdef SPI_649_ISR
  SPI_649_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 682, Type: SPI, Type ID: 650 */
  #ifdef SPI_650_ISR
  SPI_650_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 683, Type: SPI, Type ID: 651 */
  #ifdef SPI_651_ISR
  SPI_651_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 684, Type: SPI, Type ID: 652 */
  #ifdef SPI_652_ISR
  SPI_652_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 685, Type: SPI, Type ID: 653 */
  #ifdef SPI_653_ISR
  SPI_653_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 686, Type: SPI, Type ID: 654 */
  #ifdef SPI_654_ISR
  SPI_654_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 687, Type: SPI, Type ID: 655 */
  #ifdef SPI_655_ISR
  SPI_655_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 688, Type: SPI, Type ID: 656 */
  #ifdef SPI_656_ISR
  SPI_656_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 689, Type: SPI, Type ID: 657 */
  #ifdef SPI_657_ISR
  SPI_657_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 690, Type: SPI, Type ID: 658 */
  #ifdef SPI_658_ISR
  SPI_658_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 691, Type: SPI, Type ID: 659 */
  #ifdef SPI_659_ISR
  SPI_659_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 692, Type: SPI, Type ID: 660 */
  #ifdef SPI_660_ISR
  SPI_660_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 693, Type: SPI, Type ID: 661 */
  #ifdef SPI_661_ISR
  SPI_661_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 694, Type: SPI, Type ID: 662 */
  #ifdef SPI_662_ISR
  SPI_662_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 695, Type: SPI, Type ID: 663 */
  #ifdef SPI_663_ISR
  SPI_663_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 696, Type: SPI, Type ID: 664 */
  #ifdef SPI_664_ISR
  SPI_664_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 697, Type: SPI, Type ID: 665 */
  #ifdef SPI_665_ISR
  SPI_665_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 698, Type: SPI, Type ID: 666 */
  #ifdef SPI_666_ISR
  SPI_666_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 699, Type: SPI, Type ID: 667 */
  #ifdef SPI_667_ISR
  SPI_667_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 700, Type: SPI, Type ID: 668 */
  #ifdef SPI_668_ISR
  SPI_668_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 701, Type: SPI, Type ID: 669 */
  #ifdef SPI_669_ISR
  SPI_669_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 702, Type: SPI, Type ID: 670 */
  #ifdef SPI_670_ISR
  SPI_670_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 703, Type: SPI, Type ID: 671 */
  #ifdef SPI_671_ISR
  SPI_671_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 704, Type: SPI, Type ID: 672 */
  #ifdef SPI_672_ISR
  SPI_672_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 705, Type: SPI, Type ID: 673 */
  #ifdef SPI_673_ISR
  SPI_673_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 706, Type: SPI, Type ID: 674 */
  #ifdef SPI_674_ISR
  SPI_674_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 707, Type: SPI, Type ID: 675 */
  #ifdef SPI_675_ISR
  SPI_675_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 708, Type: SPI, Type ID: 676 */
  #ifdef SPI_676_ISR
  SPI_676_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 709, Type: SPI, Type ID: 677 */
  #ifdef SPI_677_ISR
  SPI_677_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 710, Type: SPI, Type ID: 678 */
  #ifdef SPI_678_ISR
  SPI_678_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 711, Type: SPI, Type ID: 679 */
  #ifdef SPI_679_ISR
  SPI_679_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 712, Type: SPI, Type ID: 680 */
  #ifdef SPI_680_ISR
  SPI_680_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 713, Type: SPI, Type ID: 681 */
  #ifdef SPI_681_ISR
  SPI_681_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 714, Type: SPI, Type ID: 682 */
  #ifdef SPI_682_ISR
  SPI_682_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 715, Type: SPI, Type ID: 683 */
  #ifdef SPI_683_ISR
  SPI_683_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 716, Type: SPI, Type ID: 684 */
  #ifdef SPI_684_ISR
  SPI_684_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 717, Type: SPI, Type ID: 685 */
  #ifdef SPI_685_ISR
  SPI_685_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 718, Type: SPI, Type ID: 686 */
  #ifdef SPI_686_ISR
  SPI_686_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 719, Type: SPI, Type ID: 687 */
  #ifdef SPI_687_ISR
  SPI_687_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 720, Type: SPI, Type ID: 688 */
  #ifdef SPI_688_ISR
  SPI_688_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 721, Type: SPI, Type ID: 689 */
  #ifdef SPI_689_ISR
  SPI_689_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 722, Type: SPI, Type ID: 690 */
  #ifdef SPI_690_ISR
  SPI_690_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 723, Type: SPI, Type ID: 691 */
  #ifdef SPI_691_ISR
  SPI_691_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 724, Type: SPI, Type ID: 692 */
  #ifdef SPI_692_ISR
  SPI_692_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 725, Type: SPI, Type ID: 693 */
  #ifdef SPI_693_ISR
  SPI_693_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 726, Type: SPI, Type ID: 694 */
  #ifdef SPI_694_ISR
  SPI_694_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 727, Type: SPI, Type ID: 695 */
  #ifdef SPI_695_ISR
  SPI_695_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 728, Type: SPI, Type ID: 696 */
  #ifdef SPI_696_ISR
  SPI_696_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 729, Type: SPI, Type ID: 697 */
  #ifdef SPI_697_ISR
  SPI_697_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 730, Type: SPI, Type ID: 698 */
  #ifdef SPI_698_ISR
  SPI_698_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 731, Type: SPI, Type ID: 699 */
  #ifdef SPI_699_ISR
  SPI_699_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 732, Type: SPI, Type ID: 700 */
  #ifdef SPI_700_ISR
  SPI_700_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 733, Type: SPI, Type ID: 701 */
  #ifdef SPI_701_ISR
  SPI_701_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 734, Type: SPI, Type ID: 702 */
  #ifdef SPI_702_ISR
  SPI_702_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 735, Type: SPI, Type ID: 703 */
  #ifdef SPI_703_ISR
  SPI_703_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 736, Type: SPI, Type ID: 704 */
  #ifdef SPI_704_ISR
  SPI_704_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 737, Type: SPI, Type ID: 705 */
  #ifdef SPI_705_ISR
  SPI_705_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 738, Type: SPI, Type ID: 706 */
  #ifdef SPI_706_ISR
  SPI_706_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 739, Type: SPI, Type ID: 707 */
  #ifdef SPI_707_ISR
  SPI_707_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 740, Type: SPI, Type ID: 708 */
  #ifdef SPI_708_ISR
  SPI_708_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 741, Type: SPI, Type ID: 709 */
  #ifdef SPI_709_ISR
  SPI_709_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 742, Type: SPI, Type ID: 710 */
  #ifdef SPI_710_ISR
  SPI_710_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 743, Type: SPI, Type ID: 711 */
  #ifdef SPI_711_ISR
  SPI_711_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 744, Type: SPI, Type ID: 712 */
  #ifdef SPI_712_ISR
  SPI_712_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 745, Type: SPI, Type ID: 713 */
  #ifdef SPI_713_ISR
  SPI_713_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 746, Type: SPI, Type ID: 714 */
  #ifdef SPI_714_ISR
  SPI_714_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 747, Type: SPI, Type ID: 715 */
  #ifdef SPI_715_ISR
  SPI_715_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 748, Type: SPI, Type ID: 716 */
  #ifdef SPI_716_ISR
  SPI_716_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 749, Type: SPI, Type ID: 717 */
  #ifdef SPI_717_ISR
  SPI_717_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 750, Type: SPI, Type ID: 718 */
  #ifdef SPI_718_ISR
  SPI_718_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 751, Type: SPI, Type ID: 719 */
  #ifdef SPI_719_ISR
  SPI_719_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 752, Type: SPI, Type ID: 720 */
  #ifdef SPI_720_ISR
  SPI_720_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 753, Type: SPI, Type ID: 721 */
  #ifdef SPI_721_ISR
  SPI_721_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 754, Type: SPI, Type ID: 722 */
  #ifdef SPI_722_ISR
  SPI_722_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 755, Type: SPI, Type ID: 723 */
  #ifdef SPI_723_ISR
  SPI_723_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 756, Type: SPI, Type ID: 724 */
  #ifdef SPI_724_ISR
  SPI_724_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 757, Type: SPI, Type ID: 725 */
  #ifdef SPI_725_ISR
  SPI_725_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 758, Type: SPI, Type ID: 726 */
  #ifdef SPI_726_ISR
  SPI_726_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 759, Type: SPI, Type ID: 727 */
  #ifdef SPI_727_ISR
  SPI_727_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 760, Type: SPI, Type ID: 728 */
  #ifdef SPI_728_ISR
  SPI_728_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 761, Type: SPI, Type ID: 729 */
  #ifdef SPI_729_ISR
  SPI_729_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 762, Type: SPI, Type ID: 730 */
  #ifdef SPI_730_ISR
  SPI_730_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 763, Type: SPI, Type ID: 731 */
  #ifdef SPI_731_ISR
  SPI_731_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 764, Type: SPI, Type ID: 732 */
  #ifdef SPI_732_ISR
  SPI_732_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 765, Type: SPI, Type ID: 733 */
  #ifdef SPI_733_ISR
  SPI_733_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 766, Type: SPI, Type ID: 734 */
  #ifdef SPI_734_ISR
  SPI_734_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 767, Type: SPI, Type ID: 735 */
  #ifdef SPI_735_ISR
  SPI_735_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 768, Type: SPI, Type ID: 736 */
  #ifdef SPI_736_ISR
  SPI_736_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 769, Type: SPI, Type ID: 737 */
  #ifdef SPI_737_ISR
  SPI_737_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 770, Type: SPI, Type ID: 738 */
  #ifdef SPI_738_ISR
  SPI_738_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 771, Type: SPI, Type ID: 739 */
  #ifdef SPI_739_ISR
  SPI_739_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 772, Type: SPI, Type ID: 740 */
  #ifdef SPI_740_ISR
  SPI_740_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 773, Type: SPI, Type ID: 741 */
  #ifdef SPI_741_ISR
  SPI_741_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 774, Type: SPI, Type ID: 742 */
  #ifdef SPI_742_ISR
  SPI_742_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 775, Type: SPI, Type ID: 743 */
  #ifdef SPI_743_ISR
  SPI_743_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 776, Type: SPI, Type ID: 744 */
  #ifdef SPI_744_ISR
  SPI_744_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 777, Type: SPI, Type ID: 745 */
  #ifdef SPI_745_ISR
  SPI_745_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 778, Type: SPI, Type ID: 746 */
  #ifdef SPI_746_ISR
  SPI_746_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 779, Type: SPI, Type ID: 747 */
  #ifdef SPI_747_ISR
  SPI_747_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 780, Type: SPI, Type ID: 748 */
  #ifdef SPI_748_ISR
  SPI_748_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 781, Type: SPI, Type ID: 749 */
  #ifdef SPI_749_ISR
  SPI_749_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 782, Type: SPI, Type ID: 750 */
  #ifdef SPI_750_ISR
  SPI_750_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 783, Type: SPI, Type ID: 751 */
  #ifdef SPI_751_ISR
  SPI_751_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 784, Type: SPI, Type ID: 752 */
  #ifdef SPI_752_ISR
  SPI_752_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 785, Type: SPI, Type ID: 753 */
  #ifdef SPI_753_ISR
  SPI_753_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 786, Type: SPI, Type ID: 754 */
  #ifdef SPI_754_ISR
  SPI_754_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 787, Type: SPI, Type ID: 755 */
  #ifdef SPI_755_ISR
  SPI_755_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 788, Type: SPI, Type ID: 756 */
  #ifdef SPI_756_ISR
  SPI_756_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 789, Type: SPI, Type ID: 757 */
  #ifdef SPI_757_ISR
  SPI_757_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 790, Type: SPI, Type ID: 758 */
  #ifdef SPI_758_ISR
  SPI_758_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 791, Type: SPI, Type ID: 759 */
  #ifdef SPI_759_ISR
  SPI_759_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 792, Type: SPI, Type ID: 760 */
  #ifdef SPI_760_ISR
  SPI_760_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 793, Type: SPI, Type ID: 761 */
  #ifdef SPI_761_ISR
  SPI_761_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 794, Type: SPI, Type ID: 762 */
  #ifdef SPI_762_ISR
  SPI_762_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 795, Type: SPI, Type ID: 763 */
  #ifdef SPI_763_ISR
  SPI_763_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 796, Type: SPI, Type ID: 764 */
  #ifdef SPI_764_ISR
  SPI_764_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 797, Type: SPI, Type ID: 765 */
  #ifdef SPI_765_ISR
  SPI_765_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 798, Type: SPI, Type ID: 766 */
  #ifdef SPI_766_ISR
  SPI_766_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 799, Type: SPI, Type ID: 767 */
  #ifdef SPI_767_ISR
  SPI_767_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 800, Type: SPI, Type ID: 768 */
  #ifdef SPI_768_ISR
  SPI_768_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 801, Type: SPI, Type ID: 769 */
  #ifdef SPI_769_ISR
  SPI_769_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 802, Type: SPI, Type ID: 770 */
  #ifdef SPI_770_ISR
  SPI_770_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 803, Type: SPI, Type ID: 771 */
  #ifdef SPI_771_ISR
  SPI_771_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 804, Type: SPI, Type ID: 772 */
  #ifdef SPI_772_ISR
  SPI_772_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 805, Type: SPI, Type ID: 773 */
  #ifdef SPI_773_ISR
  SPI_773_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 806, Type: SPI, Type ID: 774 */
  #ifdef SPI_774_ISR
  SPI_774_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 807, Type: SPI, Type ID: 775 */
  #ifdef SPI_775_ISR
  SPI_775_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 808, Type: SPI, Type ID: 776 */
  #ifdef SPI_776_ISR
  SPI_776_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 809, Type: SPI, Type ID: 777 */
  #ifdef SPI_777_ISR
  SPI_777_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 810, Type: SPI, Type ID: 778 */
  #ifdef SPI_778_ISR
  SPI_778_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 811, Type: SPI, Type ID: 779 */
  #ifdef SPI_779_ISR
  SPI_779_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 812, Type: SPI, Type ID: 780 */
  #ifdef SPI_780_ISR
  SPI_780_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 813, Type: SPI, Type ID: 781 */
  #ifdef SPI_781_ISR
  SPI_781_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 814, Type: SPI, Type ID: 782 */
  #ifdef SPI_782_ISR
  SPI_782_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 815, Type: SPI, Type ID: 783 */
  #ifdef SPI_783_ISR
  SPI_783_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 816, Type: SPI, Type ID: 784 */
  #ifdef SPI_784_ISR
  SPI_784_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 817, Type: SPI, Type ID: 785 */
  #ifdef SPI_785_ISR
  SPI_785_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 818, Type: SPI, Type ID: 786 */
  #ifdef SPI_786_ISR
  SPI_786_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 819, Type: SPI, Type ID: 787 */
  #ifdef SPI_787_ISR
  SPI_787_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 820, Type: SPI, Type ID: 788 */
  #ifdef SPI_788_ISR
  SPI_788_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 821, Type: SPI, Type ID: 789 */
  #ifdef SPI_789_ISR
  SPI_789_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 822, Type: SPI, Type ID: 790 */
  #ifdef SPI_790_ISR
  SPI_790_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 823, Type: SPI, Type ID: 791 */
  #ifdef SPI_791_ISR
  SPI_791_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 824, Type: SPI, Type ID: 792 */
  #ifdef SPI_792_ISR
  SPI_792_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 825, Type: SPI, Type ID: 793 */
  #ifdef SPI_793_ISR
  SPI_793_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 826, Type: SPI, Type ID: 794 */
  #ifdef SPI_794_ISR
  SPI_794_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 827, Type: SPI, Type ID: 795 */
  #ifdef SPI_795_ISR
  SPI_795_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 828, Type: SPI, Type ID: 796 */
  #ifdef SPI_796_ISR
  SPI_796_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 829, Type: SPI, Type ID: 797 */
  #ifdef SPI_797_ISR
  SPI_797_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 830, Type: SPI, Type ID: 798 */
  #ifdef SPI_798_ISR
  SPI_798_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 831, Type: SPI, Type ID: 799 */
  #ifdef SPI_799_ISR
  SPI_799_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 832, Type: SPI, Type ID: 800 */
  #ifdef SPI_800_ISR
  SPI_800_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 833, Type: SPI, Type ID: 801 */
  #ifdef SPI_801_ISR
  SPI_801_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 834, Type: SPI, Type ID: 802 */
  #ifdef SPI_802_ISR
  SPI_802_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 835, Type: SPI, Type ID: 803 */
  #ifdef SPI_803_ISR
  SPI_803_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 836, Type: SPI, Type ID: 804 */
  #ifdef SPI_804_ISR
  SPI_804_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 837, Type: SPI, Type ID: 805 */
  #ifdef SPI_805_ISR
  SPI_805_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 838, Type: SPI, Type ID: 806 */
  #ifdef SPI_806_ISR
  SPI_806_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 839, Type: SPI, Type ID: 807 */
  #ifdef SPI_807_ISR
  SPI_807_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 840, Type: SPI, Type ID: 808 */
  #ifdef SPI_808_ISR
  SPI_808_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 841, Type: SPI, Type ID: 809 */
  #ifdef SPI_809_ISR
  SPI_809_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 842, Type: SPI, Type ID: 810 */
  #ifdef SPI_810_ISR
  SPI_810_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 843, Type: SPI, Type ID: 811 */
  #ifdef SPI_811_ISR
  SPI_811_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 844, Type: SPI, Type ID: 812 */
  #ifdef SPI_812_ISR
  SPI_812_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 845, Type: SPI, Type ID: 813 */
  #ifdef SPI_813_ISR
  SPI_813_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 846, Type: SPI, Type ID: 814 */
  #ifdef SPI_814_ISR
  SPI_814_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 847, Type: SPI, Type ID: 815 */
  #ifdef SPI_815_ISR
  SPI_815_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 848, Type: SPI, Type ID: 816 */
  #ifdef SPI_816_ISR
  SPI_816_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 849, Type: SPI, Type ID: 817 */
  #ifdef SPI_817_ISR
  SPI_817_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 850, Type: SPI, Type ID: 818 */
  #ifdef SPI_818_ISR
  SPI_818_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 851, Type: SPI, Type ID: 819 */
  #ifdef SPI_819_ISR
  SPI_819_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 852, Type: SPI, Type ID: 820 */
  #ifdef SPI_820_ISR
  SPI_820_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 853, Type: SPI, Type ID: 821 */
  #ifdef SPI_821_ISR
  SPI_821_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 854, Type: SPI, Type ID: 822 */
  #ifdef SPI_822_ISR
  SPI_822_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 855, Type: SPI, Type ID: 823 */
  #ifdef SPI_823_ISR
  SPI_823_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 856, Type: SPI, Type ID: 824 */
  #ifdef SPI_824_ISR
  SPI_824_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 857, Type: SPI, Type ID: 825 */
  #ifdef SPI_825_ISR
  SPI_825_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 858, Type: SPI, Type ID: 826 */
  #ifdef SPI_826_ISR
  SPI_826_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 859, Type: SPI, Type ID: 827 */
  #ifdef SPI_827_ISR
  SPI_827_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 860, Type: SPI, Type ID: 828 */
  #ifdef SPI_828_ISR
  SPI_828_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 861, Type: SPI, Type ID: 829 */
  #ifdef SPI_829_ISR
  SPI_829_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 862, Type: SPI, Type ID: 830 */
  #ifdef SPI_830_ISR
  SPI_830_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 863, Type: SPI, Type ID: 831 */
  #ifdef SPI_831_ISR
  SPI_831_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 864, Type: SPI, Type ID: 832 */
  #ifdef SPI_832_ISR
  SPI_832_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 865, Type: SPI, Type ID: 833 */
  #ifdef SPI_833_ISR
  SPI_833_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 866, Type: SPI, Type ID: 834 */
  #ifdef SPI_834_ISR
  SPI_834_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 867, Type: SPI, Type ID: 835 */
  #ifdef SPI_835_ISR
  SPI_835_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 868, Type: SPI, Type ID: 836 */
  #ifdef SPI_836_ISR
  SPI_836_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 869, Type: SPI, Type ID: 837 */
  #ifdef SPI_837_ISR
  SPI_837_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 870, Type: SPI, Type ID: 838 */
  #ifdef SPI_838_ISR
  SPI_838_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 871, Type: SPI, Type ID: 839 */
  #ifdef SPI_839_ISR
  SPI_839_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 872, Type: SPI, Type ID: 840 */
  #ifdef SPI_840_ISR
  SPI_840_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 873, Type: SPI, Type ID: 841 */
  #ifdef SPI_841_ISR
  SPI_841_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 874, Type: SPI, Type ID: 842 */
  #ifdef SPI_842_ISR
  SPI_842_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 875, Type: SPI, Type ID: 843 */
  #ifdef SPI_843_ISR
  SPI_843_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 876, Type: SPI, Type ID: 844 */
  #ifdef SPI_844_ISR
  SPI_844_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 877, Type: SPI, Type ID: 845 */
  #ifdef SPI_845_ISR
  SPI_845_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 878, Type: SPI, Type ID: 846 */
  #ifdef SPI_846_ISR
  SPI_846_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 879, Type: SPI, Type ID: 847 */
  #ifdef SPI_847_ISR
  SPI_847_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 880, Type: SPI, Type ID: 848 */
  #ifdef SPI_848_ISR
  SPI_848_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 881, Type: SPI, Type ID: 849 */
  #ifdef SPI_849_ISR
  SPI_849_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 882, Type: SPI, Type ID: 850 */
  #ifdef SPI_850_ISR
  SPI_850_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 883, Type: SPI, Type ID: 851 */
  #ifdef SPI_851_ISR
  SPI_851_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 884, Type: SPI, Type ID: 852 */
  #ifdef SPI_852_ISR
  SPI_852_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 885, Type: SPI, Type ID: 853 */
  #ifdef SPI_853_ISR
  SPI_853_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 886, Type: SPI, Type ID: 854 */
  #ifdef SPI_854_ISR
  SPI_854_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 887, Type: SPI, Type ID: 855 */
  #ifdef SPI_855_ISR
  SPI_855_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 888, Type: SPI, Type ID: 856 */
  #ifdef SPI_856_ISR
  SPI_856_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 889, Type: SPI, Type ID: 857 */
  #ifdef SPI_857_ISR
  SPI_857_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 890, Type: SPI, Type ID: 858 */
  #ifdef SPI_858_ISR
  SPI_858_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 891, Type: SPI, Type ID: 859 */
  #ifdef SPI_859_ISR
  SPI_859_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 892, Type: SPI, Type ID: 860 */
  #ifdef SPI_860_ISR
  SPI_860_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 893, Type: SPI, Type ID: 861 */
  #ifdef SPI_861_ISR
  SPI_861_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 894, Type: SPI, Type ID: 862 */
  #ifdef SPI_862_ISR
  SPI_862_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 895, Type: SPI, Type ID: 863 */
  #ifdef SPI_863_ISR
  SPI_863_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 896, Type: SPI, Type ID: 864 */
  #ifdef SPI_864_ISR
  SPI_864_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 897, Type: SPI, Type ID: 865 */
  #ifdef SPI_865_ISR
  SPI_865_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 898, Type: SPI, Type ID: 866 */
  #ifdef SPI_866_ISR
  SPI_866_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 899, Type: SPI, Type ID: 867 */
  #ifdef SPI_867_ISR
  SPI_867_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 900, Type: SPI, Type ID: 868 */
  #ifdef SPI_868_ISR
  SPI_868_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 901, Type: SPI, Type ID: 869 */
  #ifdef SPI_869_ISR
  SPI_869_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 902, Type: SPI, Type ID: 870 */
  #ifdef SPI_870_ISR
  SPI_870_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 903, Type: SPI, Type ID: 871 */
  #ifdef SPI_871_ISR
  SPI_871_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 904, Type: SPI, Type ID: 872 */
  #ifdef SPI_872_ISR
  SPI_872_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 905, Type: SPI, Type ID: 873 */
  #ifdef SPI_873_ISR
  SPI_873_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 906, Type: SPI, Type ID: 874 */
  #ifdef SPI_874_ISR
  SPI_874_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 907, Type: SPI, Type ID: 875 */
  #ifdef SPI_875_ISR
  SPI_875_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 908, Type: SPI, Type ID: 876 */
  #ifdef SPI_876_ISR
  SPI_876_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 909, Type: SPI, Type ID: 877 */
  #ifdef SPI_877_ISR
  SPI_877_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 910, Type: SPI, Type ID: 878 */
  #ifdef SPI_878_ISR
  SPI_878_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 911, Type: SPI, Type ID: 879 */
  #ifdef SPI_879_ISR
  SPI_879_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 912, Type: SPI, Type ID: 880 */
  #ifdef SPI_880_ISR
  SPI_880_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 913, Type: SPI, Type ID: 881 */
  #ifdef SPI_881_ISR
  SPI_881_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 914, Type: SPI, Type ID: 882 */
  #ifdef SPI_882_ISR
  SPI_882_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 915, Type: SPI, Type ID: 883 */
  #ifdef SPI_883_ISR
  SPI_883_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 916, Type: SPI, Type ID: 884 */
  #ifdef SPI_884_ISR
  SPI_884_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 917, Type: SPI, Type ID: 885 */
  #ifdef SPI_885_ISR
  SPI_885_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 918, Type: SPI, Type ID: 886 */
  #ifdef SPI_886_ISR
  SPI_886_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 919, Type: SPI, Type ID: 887 */
  #ifdef SPI_887_ISR
  SPI_887_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 920, Type: SPI, Type ID: 888 */
  #ifdef SPI_888_ISR
  SPI_888_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 921, Type: SPI, Type ID: 889 */
  #ifdef SPI_889_ISR
  SPI_889_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 922, Type: SPI, Type ID: 890 */
  #ifdef SPI_890_ISR
  SPI_890_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 923, Type: SPI, Type ID: 891 */
  #ifdef SPI_891_ISR
  SPI_891_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 924, Type: SPI, Type ID: 892 */
  #ifdef SPI_892_ISR
  SPI_892_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 925, Type: SPI, Type ID: 893 */
  #ifdef SPI_893_ISR
  SPI_893_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 926, Type: SPI, Type ID: 894 */
  #ifdef SPI_894_ISR
  SPI_894_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 927, Type: SPI, Type ID: 895 */
  #ifdef SPI_895_ISR
  SPI_895_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 928, Type: SPI, Type ID: 896 */
  #ifdef SPI_896_ISR
  SPI_896_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 929, Type: SPI, Type ID: 897 */
  #ifdef SPI_897_ISR
  SPI_897_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 930, Type: SPI, Type ID: 898 */
  #ifdef SPI_898_ISR
  SPI_898_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 931, Type: SPI, Type ID: 899 */
  #ifdef SPI_899_ISR
  SPI_899_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 932, Type: SPI, Type ID: 900 */
  #ifdef SPI_900_ISR
  SPI_900_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 933, Type: SPI, Type ID: 901 */
  #ifdef SPI_901_ISR
  SPI_901_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 934, Type: SPI, Type ID: 902 */
  #ifdef SPI_902_ISR
  SPI_902_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 935, Type: SPI, Type ID: 903 */
  #ifdef SPI_903_ISR
  SPI_903_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 936, Type: SPI, Type ID: 904 */
  #ifdef SPI_904_ISR
  SPI_904_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 937, Type: SPI, Type ID: 905 */
  #ifdef SPI_905_ISR
  SPI_905_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 938, Type: SPI, Type ID: 906 */
  #ifdef SPI_906_ISR
  SPI_906_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 939, Type: SPI, Type ID: 907 */
  #ifdef SPI_907_ISR
  SPI_907_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 940, Type: SPI, Type ID: 908 */
  #ifdef SPI_908_ISR
  SPI_908_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 941, Type: SPI, Type ID: 909 */
  #ifdef SPI_909_ISR
  SPI_909_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 942, Type: SPI, Type ID: 910 */
  #ifdef SPI_910_ISR
  SPI_910_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 943, Type: SPI, Type ID: 911 */
  #ifdef SPI_911_ISR
  SPI_911_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 944, Type: SPI, Type ID: 912 */
  #ifdef SPI_912_ISR
  SPI_912_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 945, Type: SPI, Type ID: 913 */
  #ifdef SPI_913_ISR
  SPI_913_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 946, Type: SPI, Type ID: 914 */
  #ifdef SPI_914_ISR
  SPI_914_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 947, Type: SPI, Type ID: 915 */
  #ifdef SPI_915_ISR
  SPI_915_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 948, Type: SPI, Type ID: 916 */
  #ifdef SPI_916_ISR
  SPI_916_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 949, Type: SPI, Type ID: 917 */
  #ifdef SPI_917_ISR
  SPI_917_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 950, Type: SPI, Type ID: 918 */
  #ifdef SPI_918_ISR
  SPI_918_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 951, Type: SPI, Type ID: 919 */
  #ifdef SPI_919_ISR
  SPI_919_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 952, Type: SPI, Type ID: 920 */
  #ifdef SPI_920_ISR
  SPI_920_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 953, Type: SPI, Type ID: 921 */
  #ifdef SPI_921_ISR
  SPI_921_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 954, Type: SPI, Type ID: 922 */
  #ifdef SPI_922_ISR
  SPI_922_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 955, Type: SPI, Type ID: 923 */
  #ifdef SPI_923_ISR
  SPI_923_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 956, Type: SPI, Type ID: 924 */
  #ifdef SPI_924_ISR
  SPI_924_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 957, Type: SPI, Type ID: 925 */
  #ifdef SPI_925_ISR
  SPI_925_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 958, Type: SPI, Type ID: 926 */
  #ifdef SPI_926_ISR
  SPI_926_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 959, Type: SPI, Type ID: 927 */
  #ifdef SPI_927_ISR
  SPI_927_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 960, Type: SPI, Type ID: 928 */
  #ifdef SPI_928_ISR
  SPI_928_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 961, Type: SPI, Type ID: 929 */
  #ifdef SPI_929_ISR
  SPI_929_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 962, Type: SPI, Type ID: 930 */
  #ifdef SPI_930_ISR
  SPI_930_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 963, Type: SPI, Type ID: 931 */
  #ifdef SPI_931_ISR
  SPI_931_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 964, Type: SPI, Type ID: 932 */
  #ifdef SPI_932_ISR
  SPI_932_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 965, Type: SPI, Type ID: 933 */
  #ifdef SPI_933_ISR
  SPI_933_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 966, Type: SPI, Type ID: 934 */
  #ifdef SPI_934_ISR
  SPI_934_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 967, Type: SPI, Type ID: 935 */
  #ifdef SPI_935_ISR
  SPI_935_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 968, Type: SPI, Type ID: 936 */
  #ifdef SPI_936_ISR
  SPI_936_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 969, Type: SPI, Type ID: 937 */
  #ifdef SPI_937_ISR
  SPI_937_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 970, Type: SPI, Type ID: 938 */
  #ifdef SPI_938_ISR
  SPI_938_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 971, Type: SPI, Type ID: 939 */
  #ifdef SPI_939_ISR
  SPI_939_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 972, Type: SPI, Type ID: 940 */
  #ifdef SPI_940_ISR
  SPI_940_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 973, Type: SPI, Type ID: 941 */
  #ifdef SPI_941_ISR
  SPI_941_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 974, Type: SPI, Type ID: 942 */
  #ifdef SPI_942_ISR
  SPI_942_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 975, Type: SPI, Type ID: 943 */
  #ifdef SPI_943_ISR
  SPI_943_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 976, Type: SPI, Type ID: 944 */
  #ifdef SPI_944_ISR
  SPI_944_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 977, Type: SPI, Type ID: 945 */
  #ifdef SPI_945_ISR
  SPI_945_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 978, Type: SPI, Type ID: 946 */
  #ifdef SPI_946_ISR
  SPI_946_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 979, Type: SPI, Type ID: 947 */
  #ifdef SPI_947_ISR
  SPI_947_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 980, Type: SPI, Type ID: 948 */
  #ifdef SPI_948_ISR
  SPI_948_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 981, Type: SPI, Type ID: 949 */
  #ifdef SPI_949_ISR
  SPI_949_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 982, Type: SPI, Type ID: 950 */
  #ifdef SPI_950_ISR
  SPI_950_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 983, Type: SPI, Type ID: 951 */
  #ifdef SPI_951_ISR
  SPI_951_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 984, Type: SPI, Type ID: 952 */
  #ifdef SPI_952_ISR
  SPI_952_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 985, Type: SPI, Type ID: 953 */
  #ifdef SPI_953_ISR
  SPI_953_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 986, Type: SPI, Type ID: 954 */
  #ifdef SPI_954_ISR
  SPI_954_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 987, Type: SPI, Type ID: 955 */
  #ifdef SPI_955_ISR
  SPI_955_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 988, Type: SPI, Type ID: 956 */
  #ifdef SPI_956_ISR
  SPI_956_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 989, Type: SPI, Type ID: 957 */
  #ifdef SPI_957_ISR
  SPI_957_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 990, Type: SPI, Type ID: 958 */
  #ifdef SPI_958_ISR
  SPI_958_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 991, Type: SPI, Type ID: 959 */
  #ifdef SPI_959_ISR
  SPI_959_ISR,
  #else
  Dummy_Handler,
  #endif
};



/* The table of interrupt handlers */
const FUNCT IntVectors_Core1[] =
{
  /* ID: 0, Type: SGI, Type ID: 0 */
  #ifdef SGI_000_ISR
  SGI_000_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 1, Type: SGI, Type ID: 1 */
  #ifdef SGI_001_ISR
  SGI_001_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 2, Type: SGI, Type ID: 2 */
  #ifdef SGI_002_ISR
  SGI_002_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 3, Type: SGI, Type ID: 3 */
  #ifdef SGI_003_ISR
  SGI_003_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 4, Type: SGI, Type ID: 4 */
  #ifdef SGI_004_ISR
  SGI_004_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 5, Type: SGI, Type ID: 5 */
  #ifdef SGI_005_ISR
  SGI_005_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 6, Type: SGI, Type ID: 6 */
  #ifdef SGI_006_ISR
  SGI_006_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 7, Type: SGI, Type ID: 7 */
  #ifdef SGI_007_ISR
  SGI_007_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 8, Type: SGI, Type ID: 8 */
  #ifdef SGI_008_ISR
  SGI_008_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 9, Type: SGI, Type ID: 9 */
  #ifdef SGI_009_ISR
  SGI_009_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 10, Type: SGI, Type ID: 10 */
  #ifdef SGI_010_ISR
  SGI_010_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 11, Type: SGI, Type ID: 11 */
  #ifdef SGI_011_ISR
  SGI_011_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 12, Type: SGI, Type ID: 12 */
  #ifdef SGI_012_ISR
  SGI_012_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 13, Type: SGI, Type ID: 13 */
  #ifdef SGI_013_ISR
  SGI_013_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 14, Type: SGI, Type ID: 14 */
  #ifdef SGI_014_ISR
  SGI_014_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 15, Type: SGI, Type ID: 15 */
  #ifdef SGI_015_ISR
  SGI_015_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 16, Type: PPI, Type ID: 0 */
  #ifdef PPI_000_ISR
  PPI_000_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 17, Type: PPI, Type ID: 1 */
  #ifdef PPI_001_ISR
  PPI_001_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 18, Type: PPI, Type ID: 2 */
  #ifdef PPI_002_ISR
  PPI_002_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 19, Type: PPI, Type ID: 3 */
  #ifdef PPI_003_ISR
  PPI_003_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 20, Type: PPI, Type ID: 4 */
  #ifdef PPI_004_ISR
  PPI_004_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 21, Type: PPI, Type ID: 5 */
  #ifdef PPI_005_ISR
  PPI_005_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 22, Type: PPI, Type ID: 6 */
  #ifdef PPI_006_ISR
  PPI_006_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 23, Type: PPI, Type ID: 7 */
  #ifdef PPI_007_ISR
  PPI_007_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 24, Type: PPI, Type ID: 8 */
  #ifdef PPI_008_ISR
  PPI_008_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 25, Type: PPI, Type ID: 9 */
  #ifdef PPI_009_ISR
  PPI_009_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 26, Type: PPI, Type ID: 10 */
  #ifdef PPI_010_ISR
  PPI_010_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 27, Type: PPI, Type ID: 11 */
  #ifdef PPI_011_ISR
  PPI_011_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 28, Type: PPI, Type ID: 12 */
  #ifdef PPI_012_ISR
  PPI_012_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 29, Type: PPI, Type ID: 13 */
  #ifdef PPI_013_ISR
  PPI_013_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 30, Type: PPI, Type ID: 14 */
  #ifdef PPI_014_ISR
  PPI_014_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 31, Type: PPI, Type ID: 15 */
  #ifdef PPI_015_ISR
  PPI_015_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 32, Type: SPI, Type ID: 0 */
  #ifdef SPI_000_ISR
  SPI_000_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 33, Type: SPI, Type ID: 1 */
  #ifdef SPI_001_ISR
  SPI_001_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 34, Type: SPI, Type ID: 2 */
  #ifdef SPI_002_ISR
  SPI_002_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 35, Type: SPI, Type ID: 3 */
  #ifdef SPI_003_ISR
  SPI_003_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 36, Type: SPI, Type ID: 4 */
  #ifdef SPI_004_ISR
  SPI_004_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 37, Type: SPI, Type ID: 5 */
  #ifdef SPI_005_ISR
  SPI_005_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 38, Type: SPI, Type ID: 6 */
  #ifdef SPI_006_ISR
  SPI_006_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 39, Type: SPI, Type ID: 7 */
  #ifdef SPI_007_ISR
  SPI_007_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 40, Type: SPI, Type ID: 8 */
  #ifdef SPI_008_ISR
  SPI_008_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 41, Type: SPI, Type ID: 9 */
  #ifdef SPI_009_ISR
  SPI_009_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 42, Type: SPI, Type ID: 10 */
  #ifdef SPI_010_ISR
  SPI_010_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 43, Type: SPI, Type ID: 11 */
  #ifdef SPI_011_ISR
  SPI_011_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 44, Type: SPI, Type ID: 12 */
  #ifdef SPI_012_ISR
  SPI_012_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 45, Type: SPI, Type ID: 13 */
  #ifdef SPI_013_ISR
  SPI_013_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 46, Type: SPI, Type ID: 14 */
  #ifdef SPI_014_ISR
  SPI_014_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 47, Type: SPI, Type ID: 15 */
  #ifdef SPI_015_ISR
  SPI_015_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 48, Type: SPI, Type ID: 16 */
  #ifdef SPI_016_ISR
  SPI_016_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 49, Type: SPI, Type ID: 17 */
  #ifdef SPI_017_ISR
  SPI_017_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 50, Type: SPI, Type ID: 18 */
  #ifdef SPI_018_ISR
  SPI_018_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 51, Type: SPI, Type ID: 19 */
  #ifdef SPI_019_ISR
  SPI_019_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 52, Type: SPI, Type ID: 20 */
  #ifdef SPI_020_ISR
  SPI_020_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 53, Type: SPI, Type ID: 21 */
  #ifdef SPI_021_ISR
  SPI_021_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 54, Type: SPI, Type ID: 22 */
  #ifdef SPI_022_ISR
  SPI_022_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 55, Type: SPI, Type ID: 23 */
  #ifdef SPI_023_ISR
  SPI_023_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 56, Type: SPI, Type ID: 24 */
  #ifdef SPI_024_ISR
  SPI_024_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 57, Type: SPI, Type ID: 25 */
  #ifdef SPI_025_ISR
  SPI_025_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 58, Type: SPI, Type ID: 26 */
  #ifdef SPI_026_ISR
  SPI_026_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 59, Type: SPI, Type ID: 27 */
  #ifdef SPI_027_ISR
  SPI_027_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 60, Type: SPI, Type ID: 28 */
  #ifdef SPI_028_ISR
  SPI_028_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 61, Type: SPI, Type ID: 29 */
  #ifdef SPI_029_ISR
  SPI_029_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 62, Type: SPI, Type ID: 30 */
  #ifdef SPI_030_ISR
  SPI_030_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 63, Type: SPI, Type ID: 31 */
  #ifdef SPI_031_ISR
  SPI_031_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 64, Type: SPI, Type ID: 32 */
  #ifdef SPI_032_ISR
  SPI_032_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 65, Type: SPI, Type ID: 33 */
  #ifdef SPI_033_ISR
  SPI_033_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 66, Type: SPI, Type ID: 34 */
  #ifdef SPI_034_ISR
  SPI_034_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 67, Type: SPI, Type ID: 35 */
  #ifdef SPI_035_ISR
  SPI_035_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 68, Type: SPI, Type ID: 36 */
  #ifdef SPI_036_ISR
  SPI_036_ISR,
  #else
  Os_Isr_Core1_Interrupt_68,
  #endif

  /* ID: 69, Type: SPI, Type ID: 37 */
  #ifdef SPI_037_ISR
  SPI_037_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 70, Type: SPI, Type ID: 38 */
  #ifdef SPI_038_ISR
  SPI_038_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 71, Type: SPI, Type ID: 39 */
  #ifdef SPI_039_ISR
  SPI_039_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 72, Type: SPI, Type ID: 40 */
  #ifdef SPI_040_ISR
  SPI_040_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 73, Type: SPI, Type ID: 41 */
  #ifdef SPI_041_ISR
  SPI_041_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 74, Type: SPI, Type ID: 42 */
  #ifdef SPI_042_ISR
  SPI_042_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 75, Type: SPI, Type ID: 43 */
  #ifdef SPI_043_ISR
  SPI_043_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 76, Type: SPI, Type ID: 44 */
  #ifdef SPI_044_ISR
  SPI_044_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 77, Type: SPI, Type ID: 45 */
  #ifdef SPI_045_ISR
  SPI_045_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 78, Type: SPI, Type ID: 46 */
  #ifdef SPI_046_ISR
  SPI_046_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 79, Type: SPI, Type ID: 47 */
  #ifdef SPI_047_ISR
  SPI_047_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 80, Type: SPI, Type ID: 48 */
  #ifdef SPI_048_ISR
  SPI_048_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 81, Type: SPI, Type ID: 49 */
  #ifdef SPI_049_ISR
  SPI_049_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 82, Type: SPI, Type ID: 50 */
  #ifdef SPI_050_ISR
  SPI_050_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 83, Type: SPI, Type ID: 51 */
  #ifdef SPI_051_ISR
  SPI_051_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 84, Type: SPI, Type ID: 52 */
  #ifdef SPI_052_ISR
  SPI_052_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 85, Type: SPI, Type ID: 53 */
  #ifdef SPI_053_ISR
  SPI_053_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 86, Type: SPI, Type ID: 54 */
  #ifdef SPI_054_ISR
  SPI_054_ISR,
  #else
  Os_Isr_Core1_Interrupt_86,
  #endif

  /* ID: 87, Type: SPI, Type ID: 55 */
  #ifdef SPI_055_ISR
  SPI_055_ISR,
  #else
  Os_Isr_Core1_Interrupt_87,
  #endif

  /* ID: 88, Type: SPI, Type ID: 56 */
  #ifdef SPI_056_ISR
  SPI_056_ISR,
  #else
  Os_Isr_Core1_Interrupt_88,
  #endif

  /* ID: 89, Type: SPI, Type ID: 57 */
  #ifdef SPI_057_ISR
  SPI_057_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 90, Type: SPI, Type ID: 58 */
  #ifdef SPI_058_ISR
  SPI_058_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 91, Type: SPI, Type ID: 59 */
  #ifdef SPI_059_ISR
  SPI_059_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 92, Type: SPI, Type ID: 60 */
  #ifdef SPI_060_ISR
  SPI_060_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 93, Type: SPI, Type ID: 61 */
  #ifdef SPI_061_ISR
  SPI_061_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 94, Type: SPI, Type ID: 62 */
  #ifdef SPI_062_ISR
  SPI_062_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 95, Type: SPI, Type ID: 63 */
  #ifdef SPI_063_ISR
  SPI_063_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 96, Type: SPI, Type ID: 64 */
  #ifdef SPI_064_ISR
  SPI_064_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 97, Type: SPI, Type ID: 65 */
  #ifdef SPI_065_ISR
  SPI_065_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 98, Type: SPI, Type ID: 66 */
  #ifdef SPI_066_ISR
  SPI_066_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 99, Type: SPI, Type ID: 67 */
  #ifdef SPI_067_ISR
  SPI_067_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 100, Type: SPI, Type ID: 68 */
  #ifdef SPI_068_ISR
  SPI_068_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 101, Type: SPI, Type ID: 69 */
  #ifdef SPI_069_ISR
  SPI_069_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 102, Type: SPI, Type ID: 70 */
  #ifdef SPI_070_ISR
  SPI_070_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 103, Type: SPI, Type ID: 71 */
  #ifdef SPI_071_ISR
  SPI_071_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 104, Type: SPI, Type ID: 72 */
  #ifdef SPI_072_ISR
  SPI_072_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 105, Type: SPI, Type ID: 73 */
  #ifdef SPI_073_ISR
  SPI_073_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 106, Type: SPI, Type ID: 74 */
  #ifdef SPI_074_ISR
  SPI_074_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 107, Type: SPI, Type ID: 75 */
  #ifdef SPI_075_ISR
  SPI_075_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 108, Type: SPI, Type ID: 76 */
  #ifdef SPI_076_ISR
  SPI_076_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 109, Type: SPI, Type ID: 77 */
  #ifdef SPI_077_ISR
  SPI_077_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 110, Type: SPI, Type ID: 78 */
  #ifdef SPI_078_ISR
  SPI_078_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 111, Type: SPI, Type ID: 79 */
  #ifdef SPI_079_ISR
  SPI_079_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 112, Type: SPI, Type ID: 80 */
  #ifdef SPI_080_ISR
  SPI_080_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 113, Type: SPI, Type ID: 81 */
  #ifdef SPI_081_ISR
  SPI_081_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 114, Type: SPI, Type ID: 82 */
  #ifdef SPI_082_ISR
  SPI_082_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 115, Type: SPI, Type ID: 83 */
  #ifdef SPI_083_ISR
  SPI_083_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 116, Type: SPI, Type ID: 84 */
  #ifdef SPI_084_ISR
  SPI_084_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 117, Type: SPI, Type ID: 85 */
  #ifdef SPI_085_ISR
  SPI_085_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 118, Type: SPI, Type ID: 86 */
  #ifdef SPI_086_ISR
  SPI_086_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 119, Type: SPI, Type ID: 87 */
  #ifdef SPI_087_ISR
  SPI_087_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 120, Type: SPI, Type ID: 88 */
  #ifdef SPI_088_ISR
  SPI_088_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 121, Type: SPI, Type ID: 89 */
  #ifdef SPI_089_ISR
  SPI_089_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 122, Type: SPI, Type ID: 90 */
  #ifdef SPI_090_ISR
  SPI_090_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 123, Type: SPI, Type ID: 91 */
  #ifdef SPI_091_ISR
  SPI_091_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 124, Type: SPI, Type ID: 92 */
  #ifdef SPI_092_ISR
  SPI_092_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 125, Type: SPI, Type ID: 93 */
  #ifdef SPI_093_ISR
  SPI_093_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 126, Type: SPI, Type ID: 94 */
  #ifdef SPI_094_ISR
  SPI_094_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 127, Type: SPI, Type ID: 95 */
  #ifdef SPI_095_ISR
  SPI_095_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 128, Type: SPI, Type ID: 96 */
  #ifdef SPI_096_ISR
  SPI_096_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 129, Type: SPI, Type ID: 97 */
  #ifdef SPI_097_ISR
  SPI_097_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 130, Type: SPI, Type ID: 98 */
  #ifdef SPI_098_ISR
  SPI_098_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 131, Type: SPI, Type ID: 99 */
  #ifdef SPI_099_ISR
  SPI_099_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 132, Type: SPI, Type ID: 100 */
  #ifdef SPI_100_ISR
  SPI_100_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 133, Type: SPI, Type ID: 101 */
  #ifdef SPI_101_ISR
  SPI_101_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 134, Type: SPI, Type ID: 102 */
  #ifdef SPI_102_ISR
  SPI_102_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 135, Type: SPI, Type ID: 103 */
  #ifdef SPI_103_ISR
  SPI_103_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 136, Type: SPI, Type ID: 104 */
  #ifdef SPI_104_ISR
  SPI_104_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 137, Type: SPI, Type ID: 105 */
  #ifdef SPI_105_ISR
  SPI_105_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 138, Type: SPI, Type ID: 106 */
  #ifdef SPI_106_ISR
  SPI_106_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 139, Type: SPI, Type ID: 107 */
  #ifdef SPI_107_ISR
  SPI_107_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 140, Type: SPI, Type ID: 108 */
  #ifdef SPI_108_ISR
  SPI_108_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 141, Type: SPI, Type ID: 109 */
  #ifdef SPI_109_ISR
  SPI_109_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 142, Type: SPI, Type ID: 110 */
  #ifdef SPI_110_ISR
  SPI_110_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 143, Type: SPI, Type ID: 111 */
  #ifdef SPI_111_ISR
  SPI_111_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 144, Type: SPI, Type ID: 112 */
  #ifdef SPI_112_ISR
  SPI_112_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 145, Type: SPI, Type ID: 113 */
  #ifdef SPI_113_ISR
  SPI_113_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 146, Type: SPI, Type ID: 114 */
  #ifdef SPI_114_ISR
  SPI_114_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 147, Type: SPI, Type ID: 115 */
  #ifdef SPI_115_ISR
  SPI_115_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 148, Type: SPI, Type ID: 116 */
  #ifdef SPI_116_ISR
  SPI_116_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 149, Type: SPI, Type ID: 117 */
  #ifdef SPI_117_ISR
  SPI_117_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 150, Type: SPI, Type ID: 118 */
  #ifdef SPI_118_ISR
  SPI_118_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 151, Type: SPI, Type ID: 119 */
  #ifdef SPI_119_ISR
  SPI_119_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 152, Type: SPI, Type ID: 120 */
  #ifdef SPI_120_ISR
  SPI_120_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 153, Type: SPI, Type ID: 121 */
  #ifdef SPI_121_ISR
  SPI_121_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 154, Type: SPI, Type ID: 122 */
  #ifdef SPI_122_ISR
  SPI_122_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 155, Type: SPI, Type ID: 123 */
  #ifdef SPI_123_ISR
  SPI_123_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 156, Type: SPI, Type ID: 124 */
  #ifdef SPI_124_ISR
  SPI_124_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 157, Type: SPI, Type ID: 125 */
  #ifdef SPI_125_ISR
  SPI_125_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 158, Type: SPI, Type ID: 126 */
  #ifdef SPI_126_ISR
  SPI_126_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 159, Type: SPI, Type ID: 127 */
  #ifdef SPI_127_ISR
  SPI_127_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 160, Type: SPI, Type ID: 128 */
  #ifdef SPI_128_ISR
  SPI_128_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 161, Type: SPI, Type ID: 129 */
  #ifdef SPI_129_ISR
  SPI_129_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 162, Type: SPI, Type ID: 130 */
  #ifdef SPI_130_ISR
  SPI_130_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 163, Type: SPI, Type ID: 131 */
  #ifdef SPI_131_ISR
  SPI_131_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 164, Type: SPI, Type ID: 132 */
  #ifdef SPI_132_ISR
  SPI_132_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 165, Type: SPI, Type ID: 133 */
  #ifdef SPI_133_ISR
  SPI_133_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 166, Type: SPI, Type ID: 134 */
  #ifdef SPI_134_ISR
  SPI_134_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 167, Type: SPI, Type ID: 135 */
  #ifdef SPI_135_ISR
  SPI_135_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 168, Type: SPI, Type ID: 136 */
  #ifdef SPI_136_ISR
  SPI_136_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 169, Type: SPI, Type ID: 137 */
  #ifdef SPI_137_ISR
  SPI_137_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 170, Type: SPI, Type ID: 138 */
  #ifdef SPI_138_ISR
  SPI_138_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 171, Type: SPI, Type ID: 139 */
  #ifdef SPI_139_ISR
  SPI_139_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 172, Type: SPI, Type ID: 140 */
  #ifdef SPI_140_ISR
  SPI_140_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 173, Type: SPI, Type ID: 141 */
  #ifdef SPI_141_ISR
  SPI_141_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 174, Type: SPI, Type ID: 142 */
  #ifdef SPI_142_ISR
  SPI_142_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 175, Type: SPI, Type ID: 143 */
  #ifdef SPI_143_ISR
  SPI_143_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 176, Type: SPI, Type ID: 144 */
  #ifdef SPI_144_ISR
  SPI_144_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 177, Type: SPI, Type ID: 145 */
  #ifdef SPI_145_ISR
  SPI_145_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 178, Type: SPI, Type ID: 146 */
  #ifdef SPI_146_ISR
  SPI_146_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 179, Type: SPI, Type ID: 147 */
  #ifdef SPI_147_ISR
  SPI_147_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 180, Type: SPI, Type ID: 148 */
  #ifdef SPI_148_ISR
  SPI_148_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 181, Type: SPI, Type ID: 149 */
  #ifdef SPI_149_ISR
  SPI_149_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 182, Type: SPI, Type ID: 150 */
  #ifdef SPI_150_ISR
  SPI_150_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 183, Type: SPI, Type ID: 151 */
  #ifdef SPI_151_ISR
  SPI_151_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 184, Type: SPI, Type ID: 152 */
  #ifdef SPI_152_ISR
  SPI_152_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 185, Type: SPI, Type ID: 153 */
  #ifdef SPI_153_ISR
  SPI_153_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 186, Type: SPI, Type ID: 154 */
  #ifdef SPI_154_ISR
  SPI_154_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 187, Type: SPI, Type ID: 155 */
  #ifdef SPI_155_ISR
  SPI_155_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 188, Type: SPI, Type ID: 156 */
  #ifdef SPI_156_ISR
  SPI_156_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 189, Type: SPI, Type ID: 157 */
  #ifdef SPI_157_ISR
  SPI_157_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 190, Type: SPI, Type ID: 158 */
  #ifdef SPI_158_ISR
  SPI_158_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 191, Type: SPI, Type ID: 159 */
  #ifdef SPI_159_ISR
  SPI_159_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 192, Type: SPI, Type ID: 160 */
  #ifdef SPI_160_ISR
  SPI_160_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 193, Type: SPI, Type ID: 161 */
  #ifdef SPI_161_ISR
  SPI_161_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 194, Type: SPI, Type ID: 162 */
  #ifdef SPI_162_ISR
  SPI_162_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 195, Type: SPI, Type ID: 163 */
  #ifdef SPI_163_ISR
  SPI_163_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 196, Type: SPI, Type ID: 164 */
  #ifdef SPI_164_ISR
  SPI_164_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 197, Type: SPI, Type ID: 165 */
  #ifdef SPI_165_ISR
  SPI_165_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 198, Type: SPI, Type ID: 166 */
  #ifdef SPI_166_ISR
  SPI_166_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 199, Type: SPI, Type ID: 167 */
  #ifdef SPI_167_ISR
  SPI_167_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 200, Type: SPI, Type ID: 168 */
  #ifdef SPI_168_ISR
  SPI_168_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 201, Type: SPI, Type ID: 169 */
  #ifdef SPI_169_ISR
  SPI_169_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 202, Type: SPI, Type ID: 170 */
  #ifdef SPI_170_ISR
  SPI_170_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 203, Type: SPI, Type ID: 171 */
  #ifdef SPI_171_ISR
  SPI_171_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 204, Type: SPI, Type ID: 172 */
  #ifdef SPI_172_ISR
  SPI_172_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 205, Type: SPI, Type ID: 173 */
  #ifdef SPI_173_ISR
  SPI_173_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 206, Type: SPI, Type ID: 174 */
  #ifdef SPI_174_ISR
  SPI_174_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 207, Type: SPI, Type ID: 175 */
  #ifdef SPI_175_ISR
  SPI_175_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 208, Type: SPI, Type ID: 176 */
  #ifdef SPI_176_ISR
  SPI_176_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 209, Type: SPI, Type ID: 177 */
  #ifdef SPI_177_ISR
  SPI_177_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 210, Type: SPI, Type ID: 178 */
  #ifdef SPI_178_ISR
  SPI_178_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 211, Type: SPI, Type ID: 179 */
  #ifdef SPI_179_ISR
  SPI_179_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 212, Type: SPI, Type ID: 180 */
  #ifdef SPI_180_ISR
  SPI_180_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 213, Type: SPI, Type ID: 181 */
  #ifdef SPI_181_ISR
  SPI_181_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 214, Type: SPI, Type ID: 182 */
  #ifdef SPI_182_ISR
  SPI_182_ISR,
  #else
  Os_Isr_Core1_Interrupt_214,
  #endif

  /* ID: 215, Type: SPI, Type ID: 183 */
  #ifdef SPI_183_ISR
  SPI_183_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 216, Type: SPI, Type ID: 184 */
  #ifdef SPI_184_ISR
  SPI_184_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 217, Type: SPI, Type ID: 185 */
  #ifdef SPI_185_ISR
  SPI_185_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 218, Type: SPI, Type ID: 186 */
  #ifdef SPI_186_ISR
  SPI_186_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 219, Type: SPI, Type ID: 187 */
  #ifdef SPI_187_ISR
  SPI_187_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 220, Type: SPI, Type ID: 188 */
  #ifdef SPI_188_ISR
  SPI_188_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 221, Type: SPI, Type ID: 189 */
  #ifdef SPI_189_ISR
  SPI_189_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 222, Type: SPI, Type ID: 190 */
  #ifdef SPI_190_ISR
  SPI_190_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 223, Type: SPI, Type ID: 191 */
  #ifdef SPI_191_ISR
  SPI_191_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 224, Type: SPI, Type ID: 192 */
  #ifdef SPI_192_ISR
  SPI_192_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 225, Type: SPI, Type ID: 193 */
  #ifdef SPI_193_ISR
  SPI_193_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 226, Type: SPI, Type ID: 194 */
  #ifdef SPI_194_ISR
  SPI_194_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 227, Type: SPI, Type ID: 195 */
  #ifdef SPI_195_ISR
  SPI_195_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 228, Type: SPI, Type ID: 196 */
  #ifdef SPI_196_ISR
  SPI_196_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 229, Type: SPI, Type ID: 197 */
  #ifdef SPI_197_ISR
  SPI_197_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 230, Type: SPI, Type ID: 198 */
  #ifdef SPI_198_ISR
  SPI_198_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 231, Type: SPI, Type ID: 199 */
  #ifdef SPI_199_ISR
  SPI_199_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 232, Type: SPI, Type ID: 200 */
  #ifdef SPI_200_ISR
  SPI_200_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 233, Type: SPI, Type ID: 201 */
  #ifdef SPI_201_ISR
  SPI_201_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 234, Type: SPI, Type ID: 202 */
  #ifdef SPI_202_ISR
  SPI_202_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 235, Type: SPI, Type ID: 203 */
  #ifdef SPI_203_ISR
  SPI_203_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 236, Type: SPI, Type ID: 204 */
  #ifdef SPI_204_ISR
  SPI_204_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 237, Type: SPI, Type ID: 205 */
  #ifdef SPI_205_ISR
  SPI_205_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 238, Type: SPI, Type ID: 206 */
  #ifdef SPI_206_ISR
  SPI_206_ISR,
  #else
  Os_Isr_Core1_Interrupt_238,
  #endif

  /* ID: 239, Type: SPI, Type ID: 207 */
  #ifdef SPI_207_ISR
  SPI_207_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 240, Type: SPI, Type ID: 208 */
  #ifdef SPI_208_ISR
  SPI_208_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 241, Type: SPI, Type ID: 209 */
  #ifdef SPI_209_ISR
  SPI_209_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 242, Type: SPI, Type ID: 210 */
  #ifdef SPI_210_ISR
  SPI_210_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 243, Type: SPI, Type ID: 211 */
  #ifdef SPI_211_ISR
  SPI_211_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 244, Type: SPI, Type ID: 212 */
  #ifdef SPI_212_ISR
  SPI_212_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 245, Type: SPI, Type ID: 213 */
  #ifdef SPI_213_ISR
  SPI_213_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 246, Type: SPI, Type ID: 214 */
  #ifdef SPI_214_ISR
  SPI_214_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 247, Type: SPI, Type ID: 215 */
  #ifdef SPI_215_ISR
  SPI_215_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 248, Type: SPI, Type ID: 216 */
  #ifdef SPI_216_ISR
  SPI_216_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 249, Type: SPI, Type ID: 217 */
  #ifdef SPI_217_ISR
  SPI_217_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 250, Type: SPI, Type ID: 218 */
  #ifdef SPI_218_ISR
  SPI_218_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 251, Type: SPI, Type ID: 219 */
  #ifdef SPI_219_ISR
  SPI_219_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 252, Type: SPI, Type ID: 220 */
  #ifdef SPI_220_ISR
  SPI_220_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 253, Type: SPI, Type ID: 221 */
  #ifdef SPI_221_ISR
  SPI_221_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 254, Type: SPI, Type ID: 222 */
  #ifdef SPI_222_ISR
  SPI_222_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 255, Type: SPI, Type ID: 223 */
  #ifdef SPI_223_ISR
  SPI_223_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 256, Type: SPI, Type ID: 224 */
  #ifdef SPI_224_ISR
  SPI_224_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 257, Type: SPI, Type ID: 225 */
  #ifdef SPI_225_ISR
  SPI_225_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 258, Type: SPI, Type ID: 226 */
  #ifdef SPI_226_ISR
  SPI_226_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 259, Type: SPI, Type ID: 227 */
  #ifdef SPI_227_ISR
  SPI_227_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 260, Type: SPI, Type ID: 228 */
  #ifdef SPI_228_ISR
  SPI_228_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 261, Type: SPI, Type ID: 229 */
  #ifdef SPI_229_ISR
  SPI_229_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 262, Type: SPI, Type ID: 230 */
  #ifdef SPI_230_ISR
  SPI_230_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 263, Type: SPI, Type ID: 231 */
  #ifdef SPI_231_ISR
  SPI_231_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 264, Type: SPI, Type ID: 232 */
  #ifdef SPI_232_ISR
  SPI_232_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 265, Type: SPI, Type ID: 233 */
  #ifdef SPI_233_ISR
  SPI_233_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 266, Type: SPI, Type ID: 234 */
  #ifdef SPI_234_ISR
  SPI_234_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 267, Type: SPI, Type ID: 235 */
  #ifdef SPI_235_ISR
  SPI_235_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 268, Type: SPI, Type ID: 236 */
  #ifdef SPI_236_ISR
  SPI_236_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 269, Type: SPI, Type ID: 237 */
  #ifdef SPI_237_ISR
  SPI_237_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 270, Type: SPI, Type ID: 238 */
  #ifdef SPI_238_ISR
  SPI_238_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 271, Type: SPI, Type ID: 239 */
  #ifdef SPI_239_ISR
  SPI_239_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 272, Type: SPI, Type ID: 240 */
  #ifdef SPI_240_ISR
  SPI_240_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 273, Type: SPI, Type ID: 241 */
  #ifdef SPI_241_ISR
  SPI_241_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 274, Type: SPI, Type ID: 242 */
  #ifdef SPI_242_ISR
  SPI_242_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 275, Type: SPI, Type ID: 243 */
  #ifdef SPI_243_ISR
  SPI_243_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 276, Type: SPI, Type ID: 244 */
  #ifdef SPI_244_ISR
  SPI_244_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 277, Type: SPI, Type ID: 245 */
  #ifdef SPI_245_ISR
  SPI_245_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 278, Type: SPI, Type ID: 246 */
  #ifdef SPI_246_ISR
  SPI_246_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 279, Type: SPI, Type ID: 247 */
  #ifdef SPI_247_ISR
  SPI_247_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 280, Type: SPI, Type ID: 248 */
  #ifdef SPI_248_ISR
  SPI_248_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 281, Type: SPI, Type ID: 249 */
  #ifdef SPI_249_ISR
  SPI_249_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 282, Type: SPI, Type ID: 250 */
  #ifdef SPI_250_ISR
  SPI_250_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 283, Type: SPI, Type ID: 251 */
  #ifdef SPI_251_ISR
  SPI_251_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 284, Type: SPI, Type ID: 252 */
  #ifdef SPI_252_ISR
  SPI_252_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 285, Type: SPI, Type ID: 253 */
  #ifdef SPI_253_ISR
  SPI_253_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 286, Type: SPI, Type ID: 254 */
  #ifdef SPI_254_ISR
  SPI_254_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 287, Type: SPI, Type ID: 255 */
  #ifdef SPI_255_ISR
  SPI_255_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 288, Type: SPI, Type ID: 256 */
  #ifdef SPI_256_ISR
  SPI_256_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 289, Type: SPI, Type ID: 257 */
  #ifdef SPI_257_ISR
  SPI_257_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 290, Type: SPI, Type ID: 258 */
  #ifdef SPI_258_ISR
  SPI_258_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 291, Type: SPI, Type ID: 259 */
  #ifdef SPI_259_ISR
  SPI_259_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 292, Type: SPI, Type ID: 260 */
  #ifdef SPI_260_ISR
  SPI_260_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 293, Type: SPI, Type ID: 261 */
  #ifdef SPI_261_ISR
  SPI_261_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 294, Type: SPI, Type ID: 262 */
  #ifdef SPI_262_ISR
  SPI_262_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 295, Type: SPI, Type ID: 263 */
  #ifdef SPI_263_ISR
  SPI_263_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 296, Type: SPI, Type ID: 264 */
  #ifdef SPI_264_ISR
  SPI_264_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 297, Type: SPI, Type ID: 265 */
  #ifdef SPI_265_ISR
  SPI_265_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 298, Type: SPI, Type ID: 266 */
  #ifdef SPI_266_ISR
  SPI_266_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 299, Type: SPI, Type ID: 267 */
  #ifdef SPI_267_ISR
  SPI_267_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 300, Type: SPI, Type ID: 268 */
  #ifdef SPI_268_ISR
  SPI_268_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 301, Type: SPI, Type ID: 269 */
  #ifdef SPI_269_ISR
  SPI_269_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 302, Type: SPI, Type ID: 270 */
  #ifdef SPI_270_ISR
  SPI_270_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 303, Type: SPI, Type ID: 271 */
  #ifdef SPI_271_ISR
  SPI_271_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 304, Type: SPI, Type ID: 272 */
  #ifdef SPI_272_ISR
  SPI_272_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 305, Type: SPI, Type ID: 273 */
  #ifdef SPI_273_ISR
  SPI_273_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 306, Type: SPI, Type ID: 274 */
  #ifdef SPI_274_ISR
  SPI_274_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 307, Type: SPI, Type ID: 275 */
  #ifdef SPI_275_ISR
  SPI_275_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 308, Type: SPI, Type ID: 276 */
  #ifdef SPI_276_ISR
  SPI_276_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 309, Type: SPI, Type ID: 277 */
  #ifdef SPI_277_ISR
  SPI_277_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 310, Type: SPI, Type ID: 278 */
  #ifdef SPI_278_ISR
  SPI_278_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 311, Type: SPI, Type ID: 279 */
  #ifdef SPI_279_ISR
  SPI_279_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 312, Type: SPI, Type ID: 280 */
  #ifdef SPI_280_ISR
  SPI_280_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 313, Type: SPI, Type ID: 281 */
  #ifdef SPI_281_ISR
  SPI_281_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 314, Type: SPI, Type ID: 282 */
  #ifdef SPI_282_ISR
  SPI_282_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 315, Type: SPI, Type ID: 283 */
  #ifdef SPI_283_ISR
  SPI_283_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 316, Type: SPI, Type ID: 284 */
  #ifdef SPI_284_ISR
  SPI_284_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 317, Type: SPI, Type ID: 285 */
  #ifdef SPI_285_ISR
  SPI_285_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 318, Type: SPI, Type ID: 286 */
  #ifdef SPI_286_ISR
  SPI_286_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 319, Type: SPI, Type ID: 287 */
  #ifdef SPI_287_ISR
  SPI_287_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 320, Type: SPI, Type ID: 288 */
  #ifdef SPI_288_ISR
  SPI_288_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 321, Type: SPI, Type ID: 289 */
  #ifdef SPI_289_ISR
  SPI_289_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 322, Type: SPI, Type ID: 290 */
  #ifdef SPI_290_ISR
  SPI_290_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 323, Type: SPI, Type ID: 291 */
  #ifdef SPI_291_ISR
  SPI_291_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 324, Type: SPI, Type ID: 292 */
  #ifdef SPI_292_ISR
  SPI_292_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 325, Type: SPI, Type ID: 293 */
  #ifdef SPI_293_ISR
  SPI_293_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 326, Type: SPI, Type ID: 294 */
  #ifdef SPI_294_ISR
  SPI_294_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 327, Type: SPI, Type ID: 295 */
  #ifdef SPI_295_ISR
  SPI_295_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 328, Type: SPI, Type ID: 296 */
  #ifdef SPI_296_ISR
  SPI_296_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 329, Type: SPI, Type ID: 297 */
  #ifdef SPI_297_ISR
  SPI_297_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 330, Type: SPI, Type ID: 298 */
  #ifdef SPI_298_ISR
  SPI_298_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 331, Type: SPI, Type ID: 299 */
  #ifdef SPI_299_ISR
  SPI_299_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 332, Type: SPI, Type ID: 300 */
  #ifdef SPI_300_ISR
  SPI_300_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 333, Type: SPI, Type ID: 301 */
  #ifdef SPI_301_ISR
  SPI_301_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 334, Type: SPI, Type ID: 302 */
  #ifdef SPI_302_ISR
  SPI_302_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 335, Type: SPI, Type ID: 303 */
  #ifdef SPI_303_ISR
  SPI_303_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 336, Type: SPI, Type ID: 304 */
  #ifdef SPI_304_ISR
  SPI_304_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 337, Type: SPI, Type ID: 305 */
  #ifdef SPI_305_ISR
  SPI_305_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 338, Type: SPI, Type ID: 306 */
  #ifdef SPI_306_ISR
  SPI_306_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 339, Type: SPI, Type ID: 307 */
  #ifdef SPI_307_ISR
  SPI_307_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 340, Type: SPI, Type ID: 308 */
  #ifdef SPI_308_ISR
  SPI_308_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 341, Type: SPI, Type ID: 309 */
  #ifdef SPI_309_ISR
  SPI_309_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 342, Type: SPI, Type ID: 310 */
  #ifdef SPI_310_ISR
  SPI_310_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 343, Type: SPI, Type ID: 311 */
  #ifdef SPI_311_ISR
  SPI_311_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 344, Type: SPI, Type ID: 312 */
  #ifdef SPI_312_ISR
  SPI_312_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 345, Type: SPI, Type ID: 313 */
  #ifdef SPI_313_ISR
  SPI_313_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 346, Type: SPI, Type ID: 314 */
  #ifdef SPI_314_ISR
  SPI_314_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 347, Type: SPI, Type ID: 315 */
  #ifdef SPI_315_ISR
  SPI_315_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 348, Type: SPI, Type ID: 316 */
  #ifdef SPI_316_ISR
  SPI_316_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 349, Type: SPI, Type ID: 317 */
  #ifdef SPI_317_ISR
  SPI_317_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 350, Type: SPI, Type ID: 318 */
  #ifdef SPI_318_ISR
  SPI_318_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 351, Type: SPI, Type ID: 319 */
  #ifdef SPI_319_ISR
  SPI_319_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 352, Type: SPI, Type ID: 320 */
  #ifdef SPI_320_ISR
  SPI_320_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 353, Type: SPI, Type ID: 321 */
  #ifdef SPI_321_ISR
  SPI_321_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 354, Type: SPI, Type ID: 322 */
  #ifdef SPI_322_ISR
  SPI_322_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 355, Type: SPI, Type ID: 323 */
  #ifdef SPI_323_ISR
  SPI_323_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 356, Type: SPI, Type ID: 324 */
  #ifdef SPI_324_ISR
  SPI_324_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 357, Type: SPI, Type ID: 325 */
  #ifdef SPI_325_ISR
  SPI_325_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 358, Type: SPI, Type ID: 326 */
  #ifdef SPI_326_ISR
  SPI_326_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 359, Type: SPI, Type ID: 327 */
  #ifdef SPI_327_ISR
  SPI_327_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 360, Type: SPI, Type ID: 328 */
  #ifdef SPI_328_ISR
  SPI_328_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 361, Type: SPI, Type ID: 329 */
  #ifdef SPI_329_ISR
  SPI_329_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 362, Type: SPI, Type ID: 330 */
  #ifdef SPI_330_ISR
  SPI_330_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 363, Type: SPI, Type ID: 331 */
  #ifdef SPI_331_ISR
  SPI_331_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 364, Type: SPI, Type ID: 332 */
  #ifdef SPI_332_ISR
  SPI_332_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 365, Type: SPI, Type ID: 333 */
  #ifdef SPI_333_ISR
  SPI_333_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 366, Type: SPI, Type ID: 334 */
  #ifdef SPI_334_ISR
  SPI_334_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 367, Type: SPI, Type ID: 335 */
  #ifdef SPI_335_ISR
  SPI_335_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 368, Type: SPI, Type ID: 336 */
  #ifdef SPI_336_ISR
  SPI_336_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 369, Type: SPI, Type ID: 337 */
  #ifdef SPI_337_ISR
  SPI_337_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 370, Type: SPI, Type ID: 338 */
  #ifdef SPI_338_ISR
  SPI_338_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 371, Type: SPI, Type ID: 339 */
  #ifdef SPI_339_ISR
  SPI_339_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 372, Type: SPI, Type ID: 340 */
  #ifdef SPI_340_ISR
  SPI_340_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 373, Type: SPI, Type ID: 341 */
  #ifdef SPI_341_ISR
  SPI_341_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 374, Type: SPI, Type ID: 342 */
  #ifdef SPI_342_ISR
  SPI_342_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 375, Type: SPI, Type ID: 343 */
  #ifdef SPI_343_ISR
  SPI_343_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 376, Type: SPI, Type ID: 344 */
  #ifdef SPI_344_ISR
  SPI_344_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 377, Type: SPI, Type ID: 345 */
  #ifdef SPI_345_ISR
  SPI_345_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 378, Type: SPI, Type ID: 346 */
  #ifdef SPI_346_ISR
  SPI_346_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 379, Type: SPI, Type ID: 347 */
  #ifdef SPI_347_ISR
  SPI_347_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 380, Type: SPI, Type ID: 348 */
  #ifdef SPI_348_ISR
  SPI_348_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 381, Type: SPI, Type ID: 349 */
  #ifdef SPI_349_ISR
  SPI_349_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 382, Type: SPI, Type ID: 350 */
  #ifdef SPI_350_ISR
  SPI_350_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 383, Type: SPI, Type ID: 351 */
  #ifdef SPI_351_ISR
  SPI_351_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 384, Type: SPI, Type ID: 352 */
  #ifdef SPI_352_ISR
  SPI_352_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 385, Type: SPI, Type ID: 353 */
  #ifdef SPI_353_ISR
  SPI_353_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 386, Type: SPI, Type ID: 354 */
  #ifdef SPI_354_ISR
  SPI_354_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 387, Type: SPI, Type ID: 355 */
  #ifdef SPI_355_ISR
  SPI_355_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 388, Type: SPI, Type ID: 356 */
  #ifdef SPI_356_ISR
  SPI_356_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 389, Type: SPI, Type ID: 357 */
  #ifdef SPI_357_ISR
  SPI_357_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 390, Type: SPI, Type ID: 358 */
  #ifdef SPI_358_ISR
  SPI_358_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 391, Type: SPI, Type ID: 359 */
  #ifdef SPI_359_ISR
  SPI_359_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 392, Type: SPI, Type ID: 360 */
  #ifdef SPI_360_ISR
  SPI_360_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 393, Type: SPI, Type ID: 361 */
  #ifdef SPI_361_ISR
  SPI_361_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 394, Type: SPI, Type ID: 362 */
  #ifdef SPI_362_ISR
  SPI_362_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 395, Type: SPI, Type ID: 363 */
  #ifdef SPI_363_ISR
  SPI_363_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 396, Type: SPI, Type ID: 364 */
  #ifdef SPI_364_ISR
  SPI_364_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 397, Type: SPI, Type ID: 365 */
  #ifdef SPI_365_ISR
  SPI_365_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 398, Type: SPI, Type ID: 366 */
  #ifdef SPI_366_ISR
  SPI_366_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 399, Type: SPI, Type ID: 367 */
  #ifdef SPI_367_ISR
  SPI_367_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 400, Type: SPI, Type ID: 368 */
  #ifdef SPI_368_ISR
  SPI_368_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 401, Type: SPI, Type ID: 369 */
  #ifdef SPI_369_ISR
  SPI_369_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 402, Type: SPI, Type ID: 370 */
  #ifdef SPI_370_ISR
  SPI_370_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 403, Type: SPI, Type ID: 371 */
  #ifdef SPI_371_ISR
  SPI_371_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 404, Type: SPI, Type ID: 372 */
  #ifdef SPI_372_ISR
  SPI_372_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 405, Type: SPI, Type ID: 373 */
  #ifdef SPI_373_ISR
  SPI_373_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 406, Type: SPI, Type ID: 374 */
  #ifdef SPI_374_ISR
  SPI_374_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 407, Type: SPI, Type ID: 375 */
  #ifdef SPI_375_ISR
  SPI_375_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 408, Type: SPI, Type ID: 376 */
  #ifdef SPI_376_ISR
  SPI_376_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 409, Type: SPI, Type ID: 377 */
  #ifdef SPI_377_ISR
  SPI_377_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 410, Type: SPI, Type ID: 378 */
  #ifdef SPI_378_ISR
  SPI_378_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 411, Type: SPI, Type ID: 379 */
  #ifdef SPI_379_ISR
  SPI_379_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 412, Type: SPI, Type ID: 380 */
  #ifdef SPI_380_ISR
  SPI_380_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 413, Type: SPI, Type ID: 381 */
  #ifdef SPI_381_ISR
  SPI_381_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 414, Type: SPI, Type ID: 382 */
  #ifdef SPI_382_ISR
  SPI_382_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 415, Type: SPI, Type ID: 383 */
  #ifdef SPI_383_ISR
  SPI_383_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 416, Type: SPI, Type ID: 384 */
  #ifdef SPI_384_ISR
  SPI_384_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 417, Type: SPI, Type ID: 385 */
  #ifdef SPI_385_ISR
  SPI_385_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 418, Type: SPI, Type ID: 386 */
  #ifdef SPI_386_ISR
  SPI_386_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 419, Type: SPI, Type ID: 387 */
  #ifdef SPI_387_ISR
  SPI_387_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 420, Type: SPI, Type ID: 388 */
  #ifdef SPI_388_ISR
  SPI_388_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 421, Type: SPI, Type ID: 389 */
  #ifdef SPI_389_ISR
  SPI_389_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 422, Type: SPI, Type ID: 390 */
  #ifdef SPI_390_ISR
  SPI_390_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 423, Type: SPI, Type ID: 391 */
  #ifdef SPI_391_ISR
  SPI_391_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 424, Type: SPI, Type ID: 392 */
  #ifdef SPI_392_ISR
  SPI_392_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 425, Type: SPI, Type ID: 393 */
  #ifdef SPI_393_ISR
  SPI_393_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 426, Type: SPI, Type ID: 394 */
  #ifdef SPI_394_ISR
  SPI_394_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 427, Type: SPI, Type ID: 395 */
  #ifdef SPI_395_ISR
  SPI_395_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 428, Type: SPI, Type ID: 396 */
  #ifdef SPI_396_ISR
  SPI_396_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 429, Type: SPI, Type ID: 397 */
  #ifdef SPI_397_ISR
  SPI_397_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 430, Type: SPI, Type ID: 398 */
  #ifdef SPI_398_ISR
  SPI_398_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 431, Type: SPI, Type ID: 399 */
  #ifdef SPI_399_ISR
  SPI_399_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 432, Type: SPI, Type ID: 400 */
  #ifdef SPI_400_ISR
  SPI_400_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 433, Type: SPI, Type ID: 401 */
  #ifdef SPI_401_ISR
  SPI_401_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 434, Type: SPI, Type ID: 402 */
  #ifdef SPI_402_ISR
  SPI_402_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 435, Type: SPI, Type ID: 403 */
  #ifdef SPI_403_ISR
  SPI_403_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 436, Type: SPI, Type ID: 404 */
  #ifdef SPI_404_ISR
  SPI_404_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 437, Type: SPI, Type ID: 405 */
  #ifdef SPI_405_ISR
  SPI_405_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 438, Type: SPI, Type ID: 406 */
  #ifdef SPI_406_ISR
  SPI_406_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 439, Type: SPI, Type ID: 407 */
  #ifdef SPI_407_ISR
  SPI_407_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 440, Type: SPI, Type ID: 408 */
  #ifdef SPI_408_ISR
  SPI_408_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 441, Type: SPI, Type ID: 409 */
  #ifdef SPI_409_ISR
  SPI_409_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 442, Type: SPI, Type ID: 410 */
  #ifdef SPI_410_ISR
  SPI_410_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 443, Type: SPI, Type ID: 411 */
  #ifdef SPI_411_ISR
  SPI_411_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 444, Type: SPI, Type ID: 412 */
  #ifdef SPI_412_ISR
  SPI_412_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 445, Type: SPI, Type ID: 413 */
  #ifdef SPI_413_ISR
  SPI_413_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 446, Type: SPI, Type ID: 414 */
  #ifdef SPI_414_ISR
  SPI_414_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 447, Type: SPI, Type ID: 415 */
  #ifdef SPI_415_ISR
  SPI_415_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 448, Type: SPI, Type ID: 416 */
  #ifdef SPI_416_ISR
  SPI_416_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 449, Type: SPI, Type ID: 417 */
  #ifdef SPI_417_ISR
  SPI_417_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 450, Type: SPI, Type ID: 418 */
  #ifdef SPI_418_ISR
  SPI_418_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 451, Type: SPI, Type ID: 419 */
  #ifdef SPI_419_ISR
  SPI_419_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 452, Type: SPI, Type ID: 420 */
  #ifdef SPI_420_ISR
  SPI_420_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 453, Type: SPI, Type ID: 421 */
  #ifdef SPI_421_ISR
  SPI_421_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 454, Type: SPI, Type ID: 422 */
  #ifdef SPI_422_ISR
  SPI_422_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 455, Type: SPI, Type ID: 423 */
  #ifdef SPI_423_ISR
  SPI_423_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 456, Type: SPI, Type ID: 424 */
  #ifdef SPI_424_ISR
  SPI_424_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 457, Type: SPI, Type ID: 425 */
  #ifdef SPI_425_ISR
  SPI_425_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 458, Type: SPI, Type ID: 426 */
  #ifdef SPI_426_ISR
  SPI_426_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 459, Type: SPI, Type ID: 427 */
  #ifdef SPI_427_ISR
  SPI_427_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 460, Type: SPI, Type ID: 428 */
  #ifdef SPI_428_ISR
  SPI_428_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 461, Type: SPI, Type ID: 429 */
  #ifdef SPI_429_ISR
  SPI_429_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 462, Type: SPI, Type ID: 430 */
  #ifdef SPI_430_ISR
  SPI_430_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 463, Type: SPI, Type ID: 431 */
  #ifdef SPI_431_ISR
  SPI_431_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 464, Type: SPI, Type ID: 432 */
  #ifdef SPI_432_ISR
  SPI_432_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 465, Type: SPI, Type ID: 433 */
  #ifdef SPI_433_ISR
  SPI_433_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 466, Type: SPI, Type ID: 434 */
  #ifdef SPI_434_ISR
  SPI_434_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 467, Type: SPI, Type ID: 435 */
  #ifdef SPI_435_ISR
  SPI_435_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 468, Type: SPI, Type ID: 436 */
  #ifdef SPI_436_ISR
  SPI_436_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 469, Type: SPI, Type ID: 437 */
  #ifdef SPI_437_ISR
  SPI_437_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 470, Type: SPI, Type ID: 438 */
  #ifdef SPI_438_ISR
  SPI_438_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 471, Type: SPI, Type ID: 439 */
  #ifdef SPI_439_ISR
  SPI_439_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 472, Type: SPI, Type ID: 440 */
  #ifdef SPI_440_ISR
  SPI_440_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 473, Type: SPI, Type ID: 441 */
  #ifdef SPI_441_ISR
  SPI_441_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 474, Type: SPI, Type ID: 442 */
  #ifdef SPI_442_ISR
  SPI_442_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 475, Type: SPI, Type ID: 443 */
  #ifdef SPI_443_ISR
  SPI_443_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 476, Type: SPI, Type ID: 444 */
  #ifdef SPI_444_ISR
  SPI_444_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 477, Type: SPI, Type ID: 445 */
  #ifdef SPI_445_ISR
  SPI_445_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 478, Type: SPI, Type ID: 446 */
  #ifdef SPI_446_ISR
  SPI_446_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 479, Type: SPI, Type ID: 447 */
  #ifdef SPI_447_ISR
  SPI_447_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 480, Type: SPI, Type ID: 448 */
  #ifdef SPI_448_ISR
  SPI_448_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 481, Type: SPI, Type ID: 449 */
  #ifdef SPI_449_ISR
  SPI_449_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 482, Type: SPI, Type ID: 450 */
  #ifdef SPI_450_ISR
  SPI_450_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 483, Type: SPI, Type ID: 451 */
  #ifdef SPI_451_ISR
  SPI_451_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 484, Type: SPI, Type ID: 452 */
  #ifdef SPI_452_ISR
  SPI_452_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 485, Type: SPI, Type ID: 453 */
  #ifdef SPI_453_ISR
  SPI_453_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 486, Type: SPI, Type ID: 454 */
  #ifdef SPI_454_ISR
  SPI_454_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 487, Type: SPI, Type ID: 455 */
  #ifdef SPI_455_ISR
  SPI_455_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 488, Type: SPI, Type ID: 456 */
  #ifdef SPI_456_ISR
  SPI_456_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 489, Type: SPI, Type ID: 457 */
  #ifdef SPI_457_ISR
  SPI_457_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 490, Type: SPI, Type ID: 458 */
  #ifdef SPI_458_ISR
  SPI_458_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 491, Type: SPI, Type ID: 459 */
  #ifdef SPI_459_ISR
  SPI_459_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 492, Type: SPI, Type ID: 460 */
  #ifdef SPI_460_ISR
  SPI_460_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 493, Type: SPI, Type ID: 461 */
  #ifdef SPI_461_ISR
  SPI_461_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 494, Type: SPI, Type ID: 462 */
  #ifdef SPI_462_ISR
  SPI_462_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 495, Type: SPI, Type ID: 463 */
  #ifdef SPI_463_ISR
  SPI_463_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 496, Type: SPI, Type ID: 464 */
  #ifdef SPI_464_ISR
  SPI_464_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 497, Type: SPI, Type ID: 465 */
  #ifdef SPI_465_ISR
  SPI_465_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 498, Type: SPI, Type ID: 466 */
  #ifdef SPI_466_ISR
  SPI_466_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 499, Type: SPI, Type ID: 467 */
  #ifdef SPI_467_ISR
  SPI_467_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 500, Type: SPI, Type ID: 468 */
  #ifdef SPI_468_ISR
  SPI_468_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 501, Type: SPI, Type ID: 469 */
  #ifdef SPI_469_ISR
  SPI_469_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 502, Type: SPI, Type ID: 470 */
  #ifdef SPI_470_ISR
  SPI_470_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 503, Type: SPI, Type ID: 471 */
  #ifdef SPI_471_ISR
  SPI_471_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 504, Type: SPI, Type ID: 472 */
  #ifdef SPI_472_ISR
  SPI_472_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 505, Type: SPI, Type ID: 473 */
  #ifdef SPI_473_ISR
  SPI_473_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 506, Type: SPI, Type ID: 474 */
  #ifdef SPI_474_ISR
  SPI_474_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 507, Type: SPI, Type ID: 475 */
  #ifdef SPI_475_ISR
  SPI_475_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 508, Type: SPI, Type ID: 476 */
  #ifdef SPI_476_ISR
  SPI_476_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 509, Type: SPI, Type ID: 477 */
  #ifdef SPI_477_ISR
  SPI_477_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 510, Type: SPI, Type ID: 478 */
  #ifdef SPI_478_ISR
  SPI_478_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 511, Type: SPI, Type ID: 479 */
  #ifdef SPI_479_ISR
  SPI_479_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 512, Type: SPI, Type ID: 480 */
  #ifdef SPI_480_ISR
  SPI_480_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 513, Type: SPI, Type ID: 481 */
  #ifdef SPI_481_ISR
  SPI_481_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 514, Type: SPI, Type ID: 482 */
  #ifdef SPI_482_ISR
  SPI_482_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 515, Type: SPI, Type ID: 483 */
  #ifdef SPI_483_ISR
  SPI_483_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 516, Type: SPI, Type ID: 484 */
  #ifdef SPI_484_ISR
  SPI_484_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 517, Type: SPI, Type ID: 485 */
  #ifdef SPI_485_ISR
  SPI_485_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 518, Type: SPI, Type ID: 486 */
  #ifdef SPI_486_ISR
  SPI_486_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 519, Type: SPI, Type ID: 487 */
  #ifdef SPI_487_ISR
  SPI_487_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 520, Type: SPI, Type ID: 488 */
  #ifdef SPI_488_ISR
  SPI_488_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 521, Type: SPI, Type ID: 489 */
  #ifdef SPI_489_ISR
  SPI_489_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 522, Type: SPI, Type ID: 490 */
  #ifdef SPI_490_ISR
  SPI_490_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 523, Type: SPI, Type ID: 491 */
  #ifdef SPI_491_ISR
  SPI_491_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 524, Type: SPI, Type ID: 492 */
  #ifdef SPI_492_ISR
  SPI_492_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 525, Type: SPI, Type ID: 493 */
  #ifdef SPI_493_ISR
  SPI_493_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 526, Type: SPI, Type ID: 494 */
  #ifdef SPI_494_ISR
  SPI_494_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 527, Type: SPI, Type ID: 495 */
  #ifdef SPI_495_ISR
  SPI_495_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 528, Type: SPI, Type ID: 496 */
  #ifdef SPI_496_ISR
  SPI_496_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 529, Type: SPI, Type ID: 497 */
  #ifdef SPI_497_ISR
  SPI_497_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 530, Type: SPI, Type ID: 498 */
  #ifdef SPI_498_ISR
  SPI_498_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 531, Type: SPI, Type ID: 499 */
  #ifdef SPI_499_ISR
  SPI_499_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 532, Type: SPI, Type ID: 500 */
  #ifdef SPI_500_ISR
  SPI_500_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 533, Type: SPI, Type ID: 501 */
  #ifdef SPI_501_ISR
  SPI_501_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 534, Type: SPI, Type ID: 502 */
  #ifdef SPI_502_ISR
  SPI_502_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 535, Type: SPI, Type ID: 503 */
  #ifdef SPI_503_ISR
  SPI_503_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 536, Type: SPI, Type ID: 504 */
  #ifdef SPI_504_ISR
  SPI_504_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 537, Type: SPI, Type ID: 505 */
  #ifdef SPI_505_ISR
  SPI_505_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 538, Type: SPI, Type ID: 506 */
  #ifdef SPI_506_ISR
  SPI_506_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 539, Type: SPI, Type ID: 507 */
  #ifdef SPI_507_ISR
  SPI_507_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 540, Type: SPI, Type ID: 508 */
  #ifdef SPI_508_ISR
  SPI_508_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 541, Type: SPI, Type ID: 509 */
  #ifdef SPI_509_ISR
  SPI_509_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 542, Type: SPI, Type ID: 510 */
  #ifdef SPI_510_ISR
  SPI_510_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 543, Type: SPI, Type ID: 511 */
  #ifdef SPI_511_ISR
  SPI_511_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 544, Type: SPI, Type ID: 512 */
  #ifdef SPI_512_ISR
  SPI_512_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 545, Type: SPI, Type ID: 513 */
  #ifdef SPI_513_ISR
  SPI_513_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 546, Type: SPI, Type ID: 514 */
  #ifdef SPI_514_ISR
  SPI_514_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 547, Type: SPI, Type ID: 515 */
  #ifdef SPI_515_ISR
  SPI_515_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 548, Type: SPI, Type ID: 516 */
  #ifdef SPI_516_ISR
  SPI_516_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 549, Type: SPI, Type ID: 517 */
  #ifdef SPI_517_ISR
  SPI_517_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 550, Type: SPI, Type ID: 518 */
  #ifdef SPI_518_ISR
  SPI_518_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 551, Type: SPI, Type ID: 519 */
  #ifdef SPI_519_ISR
  SPI_519_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 552, Type: SPI, Type ID: 520 */
  #ifdef SPI_520_ISR
  SPI_520_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 553, Type: SPI, Type ID: 521 */
  #ifdef SPI_521_ISR
  SPI_521_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 554, Type: SPI, Type ID: 522 */
  #ifdef SPI_522_ISR
  SPI_522_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 555, Type: SPI, Type ID: 523 */
  #ifdef SPI_523_ISR
  SPI_523_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 556, Type: SPI, Type ID: 524 */
  #ifdef SPI_524_ISR
  SPI_524_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 557, Type: SPI, Type ID: 525 */
  #ifdef SPI_525_ISR
  SPI_525_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 558, Type: SPI, Type ID: 526 */
  #ifdef SPI_526_ISR
  SPI_526_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 559, Type: SPI, Type ID: 527 */
  #ifdef SPI_527_ISR
  SPI_527_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 560, Type: SPI, Type ID: 528 */
  #ifdef SPI_528_ISR
  SPI_528_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 561, Type: SPI, Type ID: 529 */
  #ifdef SPI_529_ISR
  SPI_529_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 562, Type: SPI, Type ID: 530 */
  #ifdef SPI_530_ISR
  SPI_530_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 563, Type: SPI, Type ID: 531 */
  #ifdef SPI_531_ISR
  SPI_531_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 564, Type: SPI, Type ID: 532 */
  #ifdef SPI_532_ISR
  SPI_532_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 565, Type: SPI, Type ID: 533 */
  #ifdef SPI_533_ISR
  SPI_533_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 566, Type: SPI, Type ID: 534 */
  #ifdef SPI_534_ISR
  SPI_534_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 567, Type: SPI, Type ID: 535 */
  #ifdef SPI_535_ISR
  SPI_535_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 568, Type: SPI, Type ID: 536 */
  #ifdef SPI_536_ISR
  SPI_536_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 569, Type: SPI, Type ID: 537 */
  #ifdef SPI_537_ISR
  SPI_537_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 570, Type: SPI, Type ID: 538 */
  #ifdef SPI_538_ISR
  SPI_538_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 571, Type: SPI, Type ID: 539 */
  #ifdef SPI_539_ISR
  SPI_539_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 572, Type: SPI, Type ID: 540 */
  #ifdef SPI_540_ISR
  SPI_540_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 573, Type: SPI, Type ID: 541 */
  #ifdef SPI_541_ISR
  SPI_541_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 574, Type: SPI, Type ID: 542 */
  #ifdef SPI_542_ISR
  SPI_542_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 575, Type: SPI, Type ID: 543 */
  #ifdef SPI_543_ISR
  SPI_543_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 576, Type: SPI, Type ID: 544 */
  #ifdef SPI_544_ISR
  SPI_544_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 577, Type: SPI, Type ID: 545 */
  #ifdef SPI_545_ISR
  SPI_545_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 578, Type: SPI, Type ID: 546 */
  #ifdef SPI_546_ISR
  SPI_546_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 579, Type: SPI, Type ID: 547 */
  #ifdef SPI_547_ISR
  SPI_547_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 580, Type: SPI, Type ID: 548 */
  #ifdef SPI_548_ISR
  SPI_548_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 581, Type: SPI, Type ID: 549 */
  #ifdef SPI_549_ISR
  SPI_549_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 582, Type: SPI, Type ID: 550 */
  #ifdef SPI_550_ISR
  SPI_550_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 583, Type: SPI, Type ID: 551 */
  #ifdef SPI_551_ISR
  SPI_551_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 584, Type: SPI, Type ID: 552 */
  #ifdef SPI_552_ISR
  SPI_552_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 585, Type: SPI, Type ID: 553 */
  #ifdef SPI_553_ISR
  SPI_553_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 586, Type: SPI, Type ID: 554 */
  #ifdef SPI_554_ISR
  SPI_554_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 587, Type: SPI, Type ID: 555 */
  #ifdef SPI_555_ISR
  SPI_555_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 588, Type: SPI, Type ID: 556 */
  #ifdef SPI_556_ISR
  SPI_556_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 589, Type: SPI, Type ID: 557 */
  #ifdef SPI_557_ISR
  SPI_557_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 590, Type: SPI, Type ID: 558 */
  #ifdef SPI_558_ISR
  SPI_558_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 591, Type: SPI, Type ID: 559 */
  #ifdef SPI_559_ISR
  SPI_559_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 592, Type: SPI, Type ID: 560 */
  #ifdef SPI_560_ISR
  SPI_560_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 593, Type: SPI, Type ID: 561 */
  #ifdef SPI_561_ISR
  SPI_561_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 594, Type: SPI, Type ID: 562 */
  #ifdef SPI_562_ISR
  SPI_562_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 595, Type: SPI, Type ID: 563 */
  #ifdef SPI_563_ISR
  SPI_563_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 596, Type: SPI, Type ID: 564 */
  #ifdef SPI_564_ISR
  SPI_564_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 597, Type: SPI, Type ID: 565 */
  #ifdef SPI_565_ISR
  SPI_565_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 598, Type: SPI, Type ID: 566 */
  #ifdef SPI_566_ISR
  SPI_566_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 599, Type: SPI, Type ID: 567 */
  #ifdef SPI_567_ISR
  SPI_567_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 600, Type: SPI, Type ID: 568 */
  #ifdef SPI_568_ISR
  SPI_568_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 601, Type: SPI, Type ID: 569 */
  #ifdef SPI_569_ISR
  SPI_569_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 602, Type: SPI, Type ID: 570 */
  #ifdef SPI_570_ISR
  SPI_570_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 603, Type: SPI, Type ID: 571 */
  #ifdef SPI_571_ISR
  SPI_571_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 604, Type: SPI, Type ID: 572 */
  #ifdef SPI_572_ISR
  SPI_572_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 605, Type: SPI, Type ID: 573 */
  #ifdef SPI_573_ISR
  SPI_573_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 606, Type: SPI, Type ID: 574 */
  #ifdef SPI_574_ISR
  SPI_574_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 607, Type: SPI, Type ID: 575 */
  #ifdef SPI_575_ISR
  SPI_575_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 608, Type: SPI, Type ID: 576 */
  #ifdef SPI_576_ISR
  SPI_576_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 609, Type: SPI, Type ID: 577 */
  #ifdef SPI_577_ISR
  SPI_577_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 610, Type: SPI, Type ID: 578 */
  #ifdef SPI_578_ISR
  SPI_578_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 611, Type: SPI, Type ID: 579 */
  #ifdef SPI_579_ISR
  SPI_579_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 612, Type: SPI, Type ID: 580 */
  #ifdef SPI_580_ISR
  SPI_580_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 613, Type: SPI, Type ID: 581 */
  #ifdef SPI_581_ISR
  SPI_581_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 614, Type: SPI, Type ID: 582 */
  #ifdef SPI_582_ISR
  SPI_582_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 615, Type: SPI, Type ID: 583 */
  #ifdef SPI_583_ISR
  SPI_583_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 616, Type: SPI, Type ID: 584 */
  #ifdef SPI_584_ISR
  SPI_584_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 617, Type: SPI, Type ID: 585 */
  #ifdef SPI_585_ISR
  SPI_585_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 618, Type: SPI, Type ID: 586 */
  #ifdef SPI_586_ISR
  SPI_586_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 619, Type: SPI, Type ID: 587 */
  #ifdef SPI_587_ISR
  SPI_587_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 620, Type: SPI, Type ID: 588 */
  #ifdef SPI_588_ISR
  SPI_588_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 621, Type: SPI, Type ID: 589 */
  #ifdef SPI_589_ISR
  SPI_589_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 622, Type: SPI, Type ID: 590 */
  #ifdef SPI_590_ISR
  SPI_590_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 623, Type: SPI, Type ID: 591 */
  #ifdef SPI_591_ISR
  SPI_591_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 624, Type: SPI, Type ID: 592 */
  #ifdef SPI_592_ISR
  SPI_592_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 625, Type: SPI, Type ID: 593 */
  #ifdef SPI_593_ISR
  SPI_593_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 626, Type: SPI, Type ID: 594 */
  #ifdef SPI_594_ISR
  SPI_594_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 627, Type: SPI, Type ID: 595 */
  #ifdef SPI_595_ISR
  SPI_595_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 628, Type: SPI, Type ID: 596 */
  #ifdef SPI_596_ISR
  SPI_596_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 629, Type: SPI, Type ID: 597 */
  #ifdef SPI_597_ISR
  SPI_597_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 630, Type: SPI, Type ID: 598 */
  #ifdef SPI_598_ISR
  SPI_598_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 631, Type: SPI, Type ID: 599 */
  #ifdef SPI_599_ISR
  SPI_599_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 632, Type: SPI, Type ID: 600 */
  #ifdef SPI_600_ISR
  SPI_600_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 633, Type: SPI, Type ID: 601 */
  #ifdef SPI_601_ISR
  SPI_601_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 634, Type: SPI, Type ID: 602 */
  #ifdef SPI_602_ISR
  SPI_602_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 635, Type: SPI, Type ID: 603 */
  #ifdef SPI_603_ISR
  SPI_603_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 636, Type: SPI, Type ID: 604 */
  #ifdef SPI_604_ISR
  SPI_604_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 637, Type: SPI, Type ID: 605 */
  #ifdef SPI_605_ISR
  SPI_605_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 638, Type: SPI, Type ID: 606 */
  #ifdef SPI_606_ISR
  SPI_606_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 639, Type: SPI, Type ID: 607 */
  #ifdef SPI_607_ISR
  SPI_607_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 640, Type: SPI, Type ID: 608 */
  #ifdef SPI_608_ISR
  SPI_608_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 641, Type: SPI, Type ID: 609 */
  #ifdef SPI_609_ISR
  SPI_609_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 642, Type: SPI, Type ID: 610 */
  #ifdef SPI_610_ISR
  SPI_610_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 643, Type: SPI, Type ID: 611 */
  #ifdef SPI_611_ISR
  SPI_611_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 644, Type: SPI, Type ID: 612 */
  #ifdef SPI_612_ISR
  SPI_612_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 645, Type: SPI, Type ID: 613 */
  #ifdef SPI_613_ISR
  SPI_613_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 646, Type: SPI, Type ID: 614 */
  #ifdef SPI_614_ISR
  SPI_614_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 647, Type: SPI, Type ID: 615 */
  #ifdef SPI_615_ISR
  SPI_615_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 648, Type: SPI, Type ID: 616 */
  #ifdef SPI_616_ISR
  SPI_616_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 649, Type: SPI, Type ID: 617 */
  #ifdef SPI_617_ISR
  SPI_617_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 650, Type: SPI, Type ID: 618 */
  #ifdef SPI_618_ISR
  SPI_618_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 651, Type: SPI, Type ID: 619 */
  #ifdef SPI_619_ISR
  SPI_619_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 652, Type: SPI, Type ID: 620 */
  #ifdef SPI_620_ISR
  SPI_620_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 653, Type: SPI, Type ID: 621 */
  #ifdef SPI_621_ISR
  SPI_621_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 654, Type: SPI, Type ID: 622 */
  #ifdef SPI_622_ISR
  SPI_622_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 655, Type: SPI, Type ID: 623 */
  #ifdef SPI_623_ISR
  SPI_623_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 656, Type: SPI, Type ID: 624 */
  #ifdef SPI_624_ISR
  SPI_624_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 657, Type: SPI, Type ID: 625 */
  #ifdef SPI_625_ISR
  SPI_625_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 658, Type: SPI, Type ID: 626 */
  #ifdef SPI_626_ISR
  SPI_626_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 659, Type: SPI, Type ID: 627 */
  #ifdef SPI_627_ISR
  SPI_627_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 660, Type: SPI, Type ID: 628 */
  #ifdef SPI_628_ISR
  SPI_628_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 661, Type: SPI, Type ID: 629 */
  #ifdef SPI_629_ISR
  SPI_629_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 662, Type: SPI, Type ID: 630 */
  #ifdef SPI_630_ISR
  SPI_630_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 663, Type: SPI, Type ID: 631 */
  #ifdef SPI_631_ISR
  SPI_631_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 664, Type: SPI, Type ID: 632 */
  #ifdef SPI_632_ISR
  SPI_632_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 665, Type: SPI, Type ID: 633 */
  #ifdef SPI_633_ISR
  SPI_633_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 666, Type: SPI, Type ID: 634 */
  #ifdef SPI_634_ISR
  SPI_634_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 667, Type: SPI, Type ID: 635 */
  #ifdef SPI_635_ISR
  SPI_635_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 668, Type: SPI, Type ID: 636 */
  #ifdef SPI_636_ISR
  SPI_636_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 669, Type: SPI, Type ID: 637 */
  #ifdef SPI_637_ISR
  SPI_637_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 670, Type: SPI, Type ID: 638 */
  #ifdef SPI_638_ISR
  SPI_638_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 671, Type: SPI, Type ID: 639 */
  #ifdef SPI_639_ISR
  SPI_639_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 672, Type: SPI, Type ID: 640 */
  #ifdef SPI_640_ISR
  SPI_640_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 673, Type: SPI, Type ID: 641 */
  #ifdef SPI_641_ISR
  SPI_641_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 674, Type: SPI, Type ID: 642 */
  #ifdef SPI_642_ISR
  SPI_642_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 675, Type: SPI, Type ID: 643 */
  #ifdef SPI_643_ISR
  SPI_643_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 676, Type: SPI, Type ID: 644 */
  #ifdef SPI_644_ISR
  SPI_644_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 677, Type: SPI, Type ID: 645 */
  #ifdef SPI_645_ISR
  SPI_645_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 678, Type: SPI, Type ID: 646 */
  #ifdef SPI_646_ISR
  SPI_646_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 679, Type: SPI, Type ID: 647 */
  #ifdef SPI_647_ISR
  SPI_647_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 680, Type: SPI, Type ID: 648 */
  #ifdef SPI_648_ISR
  SPI_648_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 681, Type: SPI, Type ID: 649 */
  #ifdef SPI_649_ISR
  SPI_649_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 682, Type: SPI, Type ID: 650 */
  #ifdef SPI_650_ISR
  SPI_650_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 683, Type: SPI, Type ID: 651 */
  #ifdef SPI_651_ISR
  SPI_651_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 684, Type: SPI, Type ID: 652 */
  #ifdef SPI_652_ISR
  SPI_652_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 685, Type: SPI, Type ID: 653 */
  #ifdef SPI_653_ISR
  SPI_653_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 686, Type: SPI, Type ID: 654 */
  #ifdef SPI_654_ISR
  SPI_654_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 687, Type: SPI, Type ID: 655 */
  #ifdef SPI_655_ISR
  SPI_655_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 688, Type: SPI, Type ID: 656 */
  #ifdef SPI_656_ISR
  SPI_656_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 689, Type: SPI, Type ID: 657 */
  #ifdef SPI_657_ISR
  SPI_657_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 690, Type: SPI, Type ID: 658 */
  #ifdef SPI_658_ISR
  SPI_658_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 691, Type: SPI, Type ID: 659 */
  #ifdef SPI_659_ISR
  SPI_659_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 692, Type: SPI, Type ID: 660 */
  #ifdef SPI_660_ISR
  SPI_660_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 693, Type: SPI, Type ID: 661 */
  #ifdef SPI_661_ISR
  SPI_661_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 694, Type: SPI, Type ID: 662 */
  #ifdef SPI_662_ISR
  SPI_662_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 695, Type: SPI, Type ID: 663 */
  #ifdef SPI_663_ISR
  SPI_663_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 696, Type: SPI, Type ID: 664 */
  #ifdef SPI_664_ISR
  SPI_664_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 697, Type: SPI, Type ID: 665 */
  #ifdef SPI_665_ISR
  SPI_665_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 698, Type: SPI, Type ID: 666 */
  #ifdef SPI_666_ISR
  SPI_666_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 699, Type: SPI, Type ID: 667 */
  #ifdef SPI_667_ISR
  SPI_667_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 700, Type: SPI, Type ID: 668 */
  #ifdef SPI_668_ISR
  SPI_668_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 701, Type: SPI, Type ID: 669 */
  #ifdef SPI_669_ISR
  SPI_669_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 702, Type: SPI, Type ID: 670 */
  #ifdef SPI_670_ISR
  SPI_670_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 703, Type: SPI, Type ID: 671 */
  #ifdef SPI_671_ISR
  SPI_671_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 704, Type: SPI, Type ID: 672 */
  #ifdef SPI_672_ISR
  SPI_672_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 705, Type: SPI, Type ID: 673 */
  #ifdef SPI_673_ISR
  SPI_673_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 706, Type: SPI, Type ID: 674 */
  #ifdef SPI_674_ISR
  SPI_674_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 707, Type: SPI, Type ID: 675 */
  #ifdef SPI_675_ISR
  SPI_675_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 708, Type: SPI, Type ID: 676 */
  #ifdef SPI_676_ISR
  SPI_676_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 709, Type: SPI, Type ID: 677 */
  #ifdef SPI_677_ISR
  SPI_677_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 710, Type: SPI, Type ID: 678 */
  #ifdef SPI_678_ISR
  SPI_678_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 711, Type: SPI, Type ID: 679 */
  #ifdef SPI_679_ISR
  SPI_679_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 712, Type: SPI, Type ID: 680 */
  #ifdef SPI_680_ISR
  SPI_680_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 713, Type: SPI, Type ID: 681 */
  #ifdef SPI_681_ISR
  SPI_681_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 714, Type: SPI, Type ID: 682 */
  #ifdef SPI_682_ISR
  SPI_682_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 715, Type: SPI, Type ID: 683 */
  #ifdef SPI_683_ISR
  SPI_683_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 716, Type: SPI, Type ID: 684 */
  #ifdef SPI_684_ISR
  SPI_684_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 717, Type: SPI, Type ID: 685 */
  #ifdef SPI_685_ISR
  SPI_685_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 718, Type: SPI, Type ID: 686 */
  #ifdef SPI_686_ISR
  SPI_686_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 719, Type: SPI, Type ID: 687 */
  #ifdef SPI_687_ISR
  SPI_687_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 720, Type: SPI, Type ID: 688 */
  #ifdef SPI_688_ISR
  SPI_688_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 721, Type: SPI, Type ID: 689 */
  #ifdef SPI_689_ISR
  SPI_689_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 722, Type: SPI, Type ID: 690 */
  #ifdef SPI_690_ISR
  SPI_690_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 723, Type: SPI, Type ID: 691 */
  #ifdef SPI_691_ISR
  SPI_691_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 724, Type: SPI, Type ID: 692 */
  #ifdef SPI_692_ISR
  SPI_692_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 725, Type: SPI, Type ID: 693 */
  #ifdef SPI_693_ISR
  SPI_693_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 726, Type: SPI, Type ID: 694 */
  #ifdef SPI_694_ISR
  SPI_694_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 727, Type: SPI, Type ID: 695 */
  #ifdef SPI_695_ISR
  SPI_695_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 728, Type: SPI, Type ID: 696 */
  #ifdef SPI_696_ISR
  SPI_696_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 729, Type: SPI, Type ID: 697 */
  #ifdef SPI_697_ISR
  SPI_697_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 730, Type: SPI, Type ID: 698 */
  #ifdef SPI_698_ISR
  SPI_698_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 731, Type: SPI, Type ID: 699 */
  #ifdef SPI_699_ISR
  SPI_699_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 732, Type: SPI, Type ID: 700 */
  #ifdef SPI_700_ISR
  SPI_700_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 733, Type: SPI, Type ID: 701 */
  #ifdef SPI_701_ISR
  SPI_701_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 734, Type: SPI, Type ID: 702 */
  #ifdef SPI_702_ISR
  SPI_702_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 735, Type: SPI, Type ID: 703 */
  #ifdef SPI_703_ISR
  SPI_703_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 736, Type: SPI, Type ID: 704 */
  #ifdef SPI_704_ISR
  SPI_704_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 737, Type: SPI, Type ID: 705 */
  #ifdef SPI_705_ISR
  SPI_705_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 738, Type: SPI, Type ID: 706 */
  #ifdef SPI_706_ISR
  SPI_706_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 739, Type: SPI, Type ID: 707 */
  #ifdef SPI_707_ISR
  SPI_707_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 740, Type: SPI, Type ID: 708 */
  #ifdef SPI_708_ISR
  SPI_708_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 741, Type: SPI, Type ID: 709 */
  #ifdef SPI_709_ISR
  SPI_709_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 742, Type: SPI, Type ID: 710 */
  #ifdef SPI_710_ISR
  SPI_710_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 743, Type: SPI, Type ID: 711 */
  #ifdef SPI_711_ISR
  SPI_711_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 744, Type: SPI, Type ID: 712 */
  #ifdef SPI_712_ISR
  SPI_712_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 745, Type: SPI, Type ID: 713 */
  #ifdef SPI_713_ISR
  SPI_713_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 746, Type: SPI, Type ID: 714 */
  #ifdef SPI_714_ISR
  SPI_714_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 747, Type: SPI, Type ID: 715 */
  #ifdef SPI_715_ISR
  SPI_715_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 748, Type: SPI, Type ID: 716 */
  #ifdef SPI_716_ISR
  SPI_716_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 749, Type: SPI, Type ID: 717 */
  #ifdef SPI_717_ISR
  SPI_717_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 750, Type: SPI, Type ID: 718 */
  #ifdef SPI_718_ISR
  SPI_718_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 751, Type: SPI, Type ID: 719 */
  #ifdef SPI_719_ISR
  SPI_719_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 752, Type: SPI, Type ID: 720 */
  #ifdef SPI_720_ISR
  SPI_720_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 753, Type: SPI, Type ID: 721 */
  #ifdef SPI_721_ISR
  SPI_721_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 754, Type: SPI, Type ID: 722 */
  #ifdef SPI_722_ISR
  SPI_722_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 755, Type: SPI, Type ID: 723 */
  #ifdef SPI_723_ISR
  SPI_723_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 756, Type: SPI, Type ID: 724 */
  #ifdef SPI_724_ISR
  SPI_724_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 757, Type: SPI, Type ID: 725 */
  #ifdef SPI_725_ISR
  SPI_725_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 758, Type: SPI, Type ID: 726 */
  #ifdef SPI_726_ISR
  SPI_726_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 759, Type: SPI, Type ID: 727 */
  #ifdef SPI_727_ISR
  SPI_727_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 760, Type: SPI, Type ID: 728 */
  #ifdef SPI_728_ISR
  SPI_728_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 761, Type: SPI, Type ID: 729 */
  #ifdef SPI_729_ISR
  SPI_729_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 762, Type: SPI, Type ID: 730 */
  #ifdef SPI_730_ISR
  SPI_730_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 763, Type: SPI, Type ID: 731 */
  #ifdef SPI_731_ISR
  SPI_731_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 764, Type: SPI, Type ID: 732 */
  #ifdef SPI_732_ISR
  SPI_732_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 765, Type: SPI, Type ID: 733 */
  #ifdef SPI_733_ISR
  SPI_733_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 766, Type: SPI, Type ID: 734 */
  #ifdef SPI_734_ISR
  SPI_734_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 767, Type: SPI, Type ID: 735 */
  #ifdef SPI_735_ISR
  SPI_735_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 768, Type: SPI, Type ID: 736 */
  #ifdef SPI_736_ISR
  SPI_736_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 769, Type: SPI, Type ID: 737 */
  #ifdef SPI_737_ISR
  SPI_737_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 770, Type: SPI, Type ID: 738 */
  #ifdef SPI_738_ISR
  SPI_738_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 771, Type: SPI, Type ID: 739 */
  #ifdef SPI_739_ISR
  SPI_739_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 772, Type: SPI, Type ID: 740 */
  #ifdef SPI_740_ISR
  SPI_740_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 773, Type: SPI, Type ID: 741 */
  #ifdef SPI_741_ISR
  SPI_741_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 774, Type: SPI, Type ID: 742 */
  #ifdef SPI_742_ISR
  SPI_742_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 775, Type: SPI, Type ID: 743 */
  #ifdef SPI_743_ISR
  SPI_743_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 776, Type: SPI, Type ID: 744 */
  #ifdef SPI_744_ISR
  SPI_744_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 777, Type: SPI, Type ID: 745 */
  #ifdef SPI_745_ISR
  SPI_745_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 778, Type: SPI, Type ID: 746 */
  #ifdef SPI_746_ISR
  SPI_746_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 779, Type: SPI, Type ID: 747 */
  #ifdef SPI_747_ISR
  SPI_747_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 780, Type: SPI, Type ID: 748 */
  #ifdef SPI_748_ISR
  SPI_748_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 781, Type: SPI, Type ID: 749 */
  #ifdef SPI_749_ISR
  SPI_749_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 782, Type: SPI, Type ID: 750 */
  #ifdef SPI_750_ISR
  SPI_750_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 783, Type: SPI, Type ID: 751 */
  #ifdef SPI_751_ISR
  SPI_751_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 784, Type: SPI, Type ID: 752 */
  #ifdef SPI_752_ISR
  SPI_752_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 785, Type: SPI, Type ID: 753 */
  #ifdef SPI_753_ISR
  SPI_753_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 786, Type: SPI, Type ID: 754 */
  #ifdef SPI_754_ISR
  SPI_754_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 787, Type: SPI, Type ID: 755 */
  #ifdef SPI_755_ISR
  SPI_755_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 788, Type: SPI, Type ID: 756 */
  #ifdef SPI_756_ISR
  SPI_756_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 789, Type: SPI, Type ID: 757 */
  #ifdef SPI_757_ISR
  SPI_757_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 790, Type: SPI, Type ID: 758 */
  #ifdef SPI_758_ISR
  SPI_758_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 791, Type: SPI, Type ID: 759 */
  #ifdef SPI_759_ISR
  SPI_759_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 792, Type: SPI, Type ID: 760 */
  #ifdef SPI_760_ISR
  SPI_760_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 793, Type: SPI, Type ID: 761 */
  #ifdef SPI_761_ISR
  SPI_761_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 794, Type: SPI, Type ID: 762 */
  #ifdef SPI_762_ISR
  SPI_762_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 795, Type: SPI, Type ID: 763 */
  #ifdef SPI_763_ISR
  SPI_763_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 796, Type: SPI, Type ID: 764 */
  #ifdef SPI_764_ISR
  SPI_764_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 797, Type: SPI, Type ID: 765 */
  #ifdef SPI_765_ISR
  SPI_765_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 798, Type: SPI, Type ID: 766 */
  #ifdef SPI_766_ISR
  SPI_766_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 799, Type: SPI, Type ID: 767 */
  #ifdef SPI_767_ISR
  SPI_767_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 800, Type: SPI, Type ID: 768 */
  #ifdef SPI_768_ISR
  SPI_768_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 801, Type: SPI, Type ID: 769 */
  #ifdef SPI_769_ISR
  SPI_769_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 802, Type: SPI, Type ID: 770 */
  #ifdef SPI_770_ISR
  SPI_770_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 803, Type: SPI, Type ID: 771 */
  #ifdef SPI_771_ISR
  SPI_771_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 804, Type: SPI, Type ID: 772 */
  #ifdef SPI_772_ISR
  SPI_772_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 805, Type: SPI, Type ID: 773 */
  #ifdef SPI_773_ISR
  SPI_773_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 806, Type: SPI, Type ID: 774 */
  #ifdef SPI_774_ISR
  SPI_774_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 807, Type: SPI, Type ID: 775 */
  #ifdef SPI_775_ISR
  SPI_775_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 808, Type: SPI, Type ID: 776 */
  #ifdef SPI_776_ISR
  SPI_776_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 809, Type: SPI, Type ID: 777 */
  #ifdef SPI_777_ISR
  SPI_777_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 810, Type: SPI, Type ID: 778 */
  #ifdef SPI_778_ISR
  SPI_778_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 811, Type: SPI, Type ID: 779 */
  #ifdef SPI_779_ISR
  SPI_779_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 812, Type: SPI, Type ID: 780 */
  #ifdef SPI_780_ISR
  SPI_780_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 813, Type: SPI, Type ID: 781 */
  #ifdef SPI_781_ISR
  SPI_781_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 814, Type: SPI, Type ID: 782 */
  #ifdef SPI_782_ISR
  SPI_782_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 815, Type: SPI, Type ID: 783 */
  #ifdef SPI_783_ISR
  SPI_783_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 816, Type: SPI, Type ID: 784 */
  #ifdef SPI_784_ISR
  SPI_784_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 817, Type: SPI, Type ID: 785 */
  #ifdef SPI_785_ISR
  SPI_785_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 818, Type: SPI, Type ID: 786 */
  #ifdef SPI_786_ISR
  SPI_786_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 819, Type: SPI, Type ID: 787 */
  #ifdef SPI_787_ISR
  SPI_787_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 820, Type: SPI, Type ID: 788 */
  #ifdef SPI_788_ISR
  SPI_788_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 821, Type: SPI, Type ID: 789 */
  #ifdef SPI_789_ISR
  SPI_789_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 822, Type: SPI, Type ID: 790 */
  #ifdef SPI_790_ISR
  SPI_790_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 823, Type: SPI, Type ID: 791 */
  #ifdef SPI_791_ISR
  SPI_791_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 824, Type: SPI, Type ID: 792 */
  #ifdef SPI_792_ISR
  SPI_792_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 825, Type: SPI, Type ID: 793 */
  #ifdef SPI_793_ISR
  SPI_793_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 826, Type: SPI, Type ID: 794 */
  #ifdef SPI_794_ISR
  SPI_794_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 827, Type: SPI, Type ID: 795 */
  #ifdef SPI_795_ISR
  SPI_795_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 828, Type: SPI, Type ID: 796 */
  #ifdef SPI_796_ISR
  SPI_796_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 829, Type: SPI, Type ID: 797 */
  #ifdef SPI_797_ISR
  SPI_797_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 830, Type: SPI, Type ID: 798 */
  #ifdef SPI_798_ISR
  SPI_798_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 831, Type: SPI, Type ID: 799 */
  #ifdef SPI_799_ISR
  SPI_799_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 832, Type: SPI, Type ID: 800 */
  #ifdef SPI_800_ISR
  SPI_800_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 833, Type: SPI, Type ID: 801 */
  #ifdef SPI_801_ISR
  SPI_801_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 834, Type: SPI, Type ID: 802 */
  #ifdef SPI_802_ISR
  SPI_802_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 835, Type: SPI, Type ID: 803 */
  #ifdef SPI_803_ISR
  SPI_803_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 836, Type: SPI, Type ID: 804 */
  #ifdef SPI_804_ISR
  SPI_804_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 837, Type: SPI, Type ID: 805 */
  #ifdef SPI_805_ISR
  SPI_805_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 838, Type: SPI, Type ID: 806 */
  #ifdef SPI_806_ISR
  SPI_806_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 839, Type: SPI, Type ID: 807 */
  #ifdef SPI_807_ISR
  SPI_807_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 840, Type: SPI, Type ID: 808 */
  #ifdef SPI_808_ISR
  SPI_808_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 841, Type: SPI, Type ID: 809 */
  #ifdef SPI_809_ISR
  SPI_809_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 842, Type: SPI, Type ID: 810 */
  #ifdef SPI_810_ISR
  SPI_810_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 843, Type: SPI, Type ID: 811 */
  #ifdef SPI_811_ISR
  SPI_811_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 844, Type: SPI, Type ID: 812 */
  #ifdef SPI_812_ISR
  SPI_812_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 845, Type: SPI, Type ID: 813 */
  #ifdef SPI_813_ISR
  SPI_813_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 846, Type: SPI, Type ID: 814 */
  #ifdef SPI_814_ISR
  SPI_814_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 847, Type: SPI, Type ID: 815 */
  #ifdef SPI_815_ISR
  SPI_815_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 848, Type: SPI, Type ID: 816 */
  #ifdef SPI_816_ISR
  SPI_816_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 849, Type: SPI, Type ID: 817 */
  #ifdef SPI_817_ISR
  SPI_817_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 850, Type: SPI, Type ID: 818 */
  #ifdef SPI_818_ISR
  SPI_818_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 851, Type: SPI, Type ID: 819 */
  #ifdef SPI_819_ISR
  SPI_819_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 852, Type: SPI, Type ID: 820 */
  #ifdef SPI_820_ISR
  SPI_820_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 853, Type: SPI, Type ID: 821 */
  #ifdef SPI_821_ISR
  SPI_821_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 854, Type: SPI, Type ID: 822 */
  #ifdef SPI_822_ISR
  SPI_822_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 855, Type: SPI, Type ID: 823 */
  #ifdef SPI_823_ISR
  SPI_823_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 856, Type: SPI, Type ID: 824 */
  #ifdef SPI_824_ISR
  SPI_824_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 857, Type: SPI, Type ID: 825 */
  #ifdef SPI_825_ISR
  SPI_825_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 858, Type: SPI, Type ID: 826 */
  #ifdef SPI_826_ISR
  SPI_826_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 859, Type: SPI, Type ID: 827 */
  #ifdef SPI_827_ISR
  SPI_827_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 860, Type: SPI, Type ID: 828 */
  #ifdef SPI_828_ISR
  SPI_828_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 861, Type: SPI, Type ID: 829 */
  #ifdef SPI_829_ISR
  SPI_829_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 862, Type: SPI, Type ID: 830 */
  #ifdef SPI_830_ISR
  SPI_830_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 863, Type: SPI, Type ID: 831 */
  #ifdef SPI_831_ISR
  SPI_831_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 864, Type: SPI, Type ID: 832 */
  #ifdef SPI_832_ISR
  SPI_832_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 865, Type: SPI, Type ID: 833 */
  #ifdef SPI_833_ISR
  SPI_833_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 866, Type: SPI, Type ID: 834 */
  #ifdef SPI_834_ISR
  SPI_834_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 867, Type: SPI, Type ID: 835 */
  #ifdef SPI_835_ISR
  SPI_835_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 868, Type: SPI, Type ID: 836 */
  #ifdef SPI_836_ISR
  SPI_836_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 869, Type: SPI, Type ID: 837 */
  #ifdef SPI_837_ISR
  SPI_837_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 870, Type: SPI, Type ID: 838 */
  #ifdef SPI_838_ISR
  SPI_838_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 871, Type: SPI, Type ID: 839 */
  #ifdef SPI_839_ISR
  SPI_839_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 872, Type: SPI, Type ID: 840 */
  #ifdef SPI_840_ISR
  SPI_840_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 873, Type: SPI, Type ID: 841 */
  #ifdef SPI_841_ISR
  SPI_841_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 874, Type: SPI, Type ID: 842 */
  #ifdef SPI_842_ISR
  SPI_842_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 875, Type: SPI, Type ID: 843 */
  #ifdef SPI_843_ISR
  SPI_843_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 876, Type: SPI, Type ID: 844 */
  #ifdef SPI_844_ISR
  SPI_844_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 877, Type: SPI, Type ID: 845 */
  #ifdef SPI_845_ISR
  SPI_845_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 878, Type: SPI, Type ID: 846 */
  #ifdef SPI_846_ISR
  SPI_846_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 879, Type: SPI, Type ID: 847 */
  #ifdef SPI_847_ISR
  SPI_847_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 880, Type: SPI, Type ID: 848 */
  #ifdef SPI_848_ISR
  SPI_848_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 881, Type: SPI, Type ID: 849 */
  #ifdef SPI_849_ISR
  SPI_849_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 882, Type: SPI, Type ID: 850 */
  #ifdef SPI_850_ISR
  SPI_850_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 883, Type: SPI, Type ID: 851 */
  #ifdef SPI_851_ISR
  SPI_851_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 884, Type: SPI, Type ID: 852 */
  #ifdef SPI_852_ISR
  SPI_852_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 885, Type: SPI, Type ID: 853 */
  #ifdef SPI_853_ISR
  SPI_853_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 886, Type: SPI, Type ID: 854 */
  #ifdef SPI_854_ISR
  SPI_854_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 887, Type: SPI, Type ID: 855 */
  #ifdef SPI_855_ISR
  SPI_855_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 888, Type: SPI, Type ID: 856 */
  #ifdef SPI_856_ISR
  SPI_856_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 889, Type: SPI, Type ID: 857 */
  #ifdef SPI_857_ISR
  SPI_857_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 890, Type: SPI, Type ID: 858 */
  #ifdef SPI_858_ISR
  SPI_858_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 891, Type: SPI, Type ID: 859 */
  #ifdef SPI_859_ISR
  SPI_859_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 892, Type: SPI, Type ID: 860 */
  #ifdef SPI_860_ISR
  SPI_860_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 893, Type: SPI, Type ID: 861 */
  #ifdef SPI_861_ISR
  SPI_861_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 894, Type: SPI, Type ID: 862 */
  #ifdef SPI_862_ISR
  SPI_862_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 895, Type: SPI, Type ID: 863 */
  #ifdef SPI_863_ISR
  SPI_863_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 896, Type: SPI, Type ID: 864 */
  #ifdef SPI_864_ISR
  SPI_864_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 897, Type: SPI, Type ID: 865 */
  #ifdef SPI_865_ISR
  SPI_865_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 898, Type: SPI, Type ID: 866 */
  #ifdef SPI_866_ISR
  SPI_866_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 899, Type: SPI, Type ID: 867 */
  #ifdef SPI_867_ISR
  SPI_867_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 900, Type: SPI, Type ID: 868 */
  #ifdef SPI_868_ISR
  SPI_868_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 901, Type: SPI, Type ID: 869 */
  #ifdef SPI_869_ISR
  SPI_869_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 902, Type: SPI, Type ID: 870 */
  #ifdef SPI_870_ISR
  SPI_870_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 903, Type: SPI, Type ID: 871 */
  #ifdef SPI_871_ISR
  SPI_871_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 904, Type: SPI, Type ID: 872 */
  #ifdef SPI_872_ISR
  SPI_872_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 905, Type: SPI, Type ID: 873 */
  #ifdef SPI_873_ISR
  SPI_873_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 906, Type: SPI, Type ID: 874 */
  #ifdef SPI_874_ISR
  SPI_874_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 907, Type: SPI, Type ID: 875 */
  #ifdef SPI_875_ISR
  SPI_875_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 908, Type: SPI, Type ID: 876 */
  #ifdef SPI_876_ISR
  SPI_876_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 909, Type: SPI, Type ID: 877 */
  #ifdef SPI_877_ISR
  SPI_877_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 910, Type: SPI, Type ID: 878 */
  #ifdef SPI_878_ISR
  SPI_878_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 911, Type: SPI, Type ID: 879 */
  #ifdef SPI_879_ISR
  SPI_879_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 912, Type: SPI, Type ID: 880 */
  #ifdef SPI_880_ISR
  SPI_880_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 913, Type: SPI, Type ID: 881 */
  #ifdef SPI_881_ISR
  SPI_881_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 914, Type: SPI, Type ID: 882 */
  #ifdef SPI_882_ISR
  SPI_882_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 915, Type: SPI, Type ID: 883 */
  #ifdef SPI_883_ISR
  SPI_883_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 916, Type: SPI, Type ID: 884 */
  #ifdef SPI_884_ISR
  SPI_884_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 917, Type: SPI, Type ID: 885 */
  #ifdef SPI_885_ISR
  SPI_885_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 918, Type: SPI, Type ID: 886 */
  #ifdef SPI_886_ISR
  SPI_886_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 919, Type: SPI, Type ID: 887 */
  #ifdef SPI_887_ISR
  SPI_887_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 920, Type: SPI, Type ID: 888 */
  #ifdef SPI_888_ISR
  SPI_888_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 921, Type: SPI, Type ID: 889 */
  #ifdef SPI_889_ISR
  SPI_889_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 922, Type: SPI, Type ID: 890 */
  #ifdef SPI_890_ISR
  SPI_890_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 923, Type: SPI, Type ID: 891 */
  #ifdef SPI_891_ISR
  SPI_891_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 924, Type: SPI, Type ID: 892 */
  #ifdef SPI_892_ISR
  SPI_892_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 925, Type: SPI, Type ID: 893 */
  #ifdef SPI_893_ISR
  SPI_893_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 926, Type: SPI, Type ID: 894 */
  #ifdef SPI_894_ISR
  SPI_894_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 927, Type: SPI, Type ID: 895 */
  #ifdef SPI_895_ISR
  SPI_895_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 928, Type: SPI, Type ID: 896 */
  #ifdef SPI_896_ISR
  SPI_896_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 929, Type: SPI, Type ID: 897 */
  #ifdef SPI_897_ISR
  SPI_897_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 930, Type: SPI, Type ID: 898 */
  #ifdef SPI_898_ISR
  SPI_898_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 931, Type: SPI, Type ID: 899 */
  #ifdef SPI_899_ISR
  SPI_899_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 932, Type: SPI, Type ID: 900 */
  #ifdef SPI_900_ISR
  SPI_900_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 933, Type: SPI, Type ID: 901 */
  #ifdef SPI_901_ISR
  SPI_901_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 934, Type: SPI, Type ID: 902 */
  #ifdef SPI_902_ISR
  SPI_902_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 935, Type: SPI, Type ID: 903 */
  #ifdef SPI_903_ISR
  SPI_903_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 936, Type: SPI, Type ID: 904 */
  #ifdef SPI_904_ISR
  SPI_904_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 937, Type: SPI, Type ID: 905 */
  #ifdef SPI_905_ISR
  SPI_905_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 938, Type: SPI, Type ID: 906 */
  #ifdef SPI_906_ISR
  SPI_906_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 939, Type: SPI, Type ID: 907 */
  #ifdef SPI_907_ISR
  SPI_907_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 940, Type: SPI, Type ID: 908 */
  #ifdef SPI_908_ISR
  SPI_908_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 941, Type: SPI, Type ID: 909 */
  #ifdef SPI_909_ISR
  SPI_909_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 942, Type: SPI, Type ID: 910 */
  #ifdef SPI_910_ISR
  SPI_910_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 943, Type: SPI, Type ID: 911 */
  #ifdef SPI_911_ISR
  SPI_911_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 944, Type: SPI, Type ID: 912 */
  #ifdef SPI_912_ISR
  SPI_912_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 945, Type: SPI, Type ID: 913 */
  #ifdef SPI_913_ISR
  SPI_913_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 946, Type: SPI, Type ID: 914 */
  #ifdef SPI_914_ISR
  SPI_914_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 947, Type: SPI, Type ID: 915 */
  #ifdef SPI_915_ISR
  SPI_915_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 948, Type: SPI, Type ID: 916 */
  #ifdef SPI_916_ISR
  SPI_916_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 949, Type: SPI, Type ID: 917 */
  #ifdef SPI_917_ISR
  SPI_917_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 950, Type: SPI, Type ID: 918 */
  #ifdef SPI_918_ISR
  SPI_918_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 951, Type: SPI, Type ID: 919 */
  #ifdef SPI_919_ISR
  SPI_919_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 952, Type: SPI, Type ID: 920 */
  #ifdef SPI_920_ISR
  SPI_920_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 953, Type: SPI, Type ID: 921 */
  #ifdef SPI_921_ISR
  SPI_921_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 954, Type: SPI, Type ID: 922 */
  #ifdef SPI_922_ISR
  SPI_922_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 955, Type: SPI, Type ID: 923 */
  #ifdef SPI_923_ISR
  SPI_923_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 956, Type: SPI, Type ID: 924 */
  #ifdef SPI_924_ISR
  SPI_924_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 957, Type: SPI, Type ID: 925 */
  #ifdef SPI_925_ISR
  SPI_925_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 958, Type: SPI, Type ID: 926 */
  #ifdef SPI_926_ISR
  SPI_926_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 959, Type: SPI, Type ID: 927 */
  #ifdef SPI_927_ISR
  SPI_927_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 960, Type: SPI, Type ID: 928 */
  #ifdef SPI_928_ISR
  SPI_928_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 961, Type: SPI, Type ID: 929 */
  #ifdef SPI_929_ISR
  SPI_929_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 962, Type: SPI, Type ID: 930 */
  #ifdef SPI_930_ISR
  SPI_930_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 963, Type: SPI, Type ID: 931 */
  #ifdef SPI_931_ISR
  SPI_931_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 964, Type: SPI, Type ID: 932 */
  #ifdef SPI_932_ISR
  SPI_932_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 965, Type: SPI, Type ID: 933 */
  #ifdef SPI_933_ISR
  SPI_933_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 966, Type: SPI, Type ID: 934 */
  #ifdef SPI_934_ISR
  SPI_934_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 967, Type: SPI, Type ID: 935 */
  #ifdef SPI_935_ISR
  SPI_935_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 968, Type: SPI, Type ID: 936 */
  #ifdef SPI_936_ISR
  SPI_936_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 969, Type: SPI, Type ID: 937 */
  #ifdef SPI_937_ISR
  SPI_937_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 970, Type: SPI, Type ID: 938 */
  #ifdef SPI_938_ISR
  SPI_938_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 971, Type: SPI, Type ID: 939 */
  #ifdef SPI_939_ISR
  SPI_939_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 972, Type: SPI, Type ID: 940 */
  #ifdef SPI_940_ISR
  SPI_940_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 973, Type: SPI, Type ID: 941 */
  #ifdef SPI_941_ISR
  SPI_941_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 974, Type: SPI, Type ID: 942 */
  #ifdef SPI_942_ISR
  SPI_942_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 975, Type: SPI, Type ID: 943 */
  #ifdef SPI_943_ISR
  SPI_943_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 976, Type: SPI, Type ID: 944 */
  #ifdef SPI_944_ISR
  SPI_944_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 977, Type: SPI, Type ID: 945 */
  #ifdef SPI_945_ISR
  SPI_945_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 978, Type: SPI, Type ID: 946 */
  #ifdef SPI_946_ISR
  SPI_946_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 979, Type: SPI, Type ID: 947 */
  #ifdef SPI_947_ISR
  SPI_947_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 980, Type: SPI, Type ID: 948 */
  #ifdef SPI_948_ISR
  SPI_948_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 981, Type: SPI, Type ID: 949 */
  #ifdef SPI_949_ISR
  SPI_949_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 982, Type: SPI, Type ID: 950 */
  #ifdef SPI_950_ISR
  SPI_950_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 983, Type: SPI, Type ID: 951 */
  #ifdef SPI_951_ISR
  SPI_951_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 984, Type: SPI, Type ID: 952 */
  #ifdef SPI_952_ISR
  SPI_952_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 985, Type: SPI, Type ID: 953 */
  #ifdef SPI_953_ISR
  SPI_953_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 986, Type: SPI, Type ID: 954 */
  #ifdef SPI_954_ISR
  SPI_954_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 987, Type: SPI, Type ID: 955 */
  #ifdef SPI_955_ISR
  SPI_955_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 988, Type: SPI, Type ID: 956 */
  #ifdef SPI_956_ISR
  SPI_956_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 989, Type: SPI, Type ID: 957 */
  #ifdef SPI_957_ISR
  SPI_957_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 990, Type: SPI, Type ID: 958 */
  #ifdef SPI_958_ISR
  SPI_958_ISR,
  #else
  Dummy_Handler,
  #endif

  /* ID: 991, Type: SPI, Type ID: 959 */
  #ifdef SPI_959_ISR
  SPI_959_ISR,
  #else
  Dummy_Handler,
  #endif


};

/******************************************************************************
**                          End of File                                      **
*******************************************************************************/
