
/**********************************************************************************************************************
  COPYRIGHT
-----------------------------------------------------------------------------------------------------------------------
  \par      copyright
  \verbatim
  Copyright (c) 2020 by Vector Informatik GmbH.                                                  All rights reserved.

                This software is copyright protected and proprietary to Vector Informatik GmbH.
                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
                All other rights remain with Vector Informatik GmbH.
  \endverbatim
-----------------------------------------------------------------------------------------------------------------------
  FILE DESCRIPTION
-----------------------------------------------------------------------------------------------------------------------
  \file  File:  BrsMain.c
      Project:  Vector Basic Runtime System
       Module:  BrsMain

  \brief Description:  Main file of BRS contains
                       - Main function (called from StartUpCode and calls stack entry EcuM_Init())
                       - Call of BrsHw HW initialization routines
                       - 1ms handler, with support of cycle functions with several cycle times
                       - Exception handling
                       - Default_Init_Task, Main and Background Task which can be used by the operating system

  \attention Please note:
    The demo and example programs only show special aspects of the software. With regard to the fact
    that these programs are meant for demonstration purposes only, Vector Informatik liability shall be
    expressly excluded in cases of ordinary negligence, to the extent admissible by law or statute.
**********************************************************************************************************************/

/**********************************************************************************************************************
  REVISION HISTORY
 ----------------------------------------------------------------------------------------------------------------------
  Version   Date        Author  Description
  --------  ----------  ------  ---------------------------------------------------------------------------------------
  01.00.00  2018-03-20  visbwa  Initial creation of new BRS code basis
            2018-05-08  visbwa  Introduced support for Generic Testsuite (BRS_ENABLE_TESTSUITE_SUPPORT),
                                removed support for all other testsuites,
                                introduced BRS_ENABLE_1MS_HANDLER and brsMain_CallCounter1sec
  01.00.01  2018-06-22  visbwa  Added CAN stack exclusive area workaround from former BswInit_Callout_Stubs.c
  01.00.02  2018-06-25  visbwa  Added configuration check for OS_MS2TICKS_SystemTimer into BrsMain.h
  01.00.03  2018-07-18  visbwa  Moved BrsHwDisableInterruptAtPowerOn() from BrsMainInit() into main()
                                (OS specifies, this has to be called before Os_Init() and for every Core)
  01.00.04  2018-07-30  visbwa  Encapsuled call of BrsHwDisableInterruptAtPowerOn() with usecase != VTT
  01.01.00  2018-10-08  visbwa  Changed include of BrsCfg.h into vBrsCfg.h in BrsMain.h (vBaseEnv 1.1.0)
  01.01.01  2018-10-10  visbwa  Moved BrsTestsuiteInit() from BrsMainInit() into Default_Init_Task(),
                                removed usage of BRS_ENABLE_HLPTEST_SUPPORT (generic Testsuite concept!)
  01.01.02  2018-11-05  visbwa  Fixed include order (Can.h previous to BrsTestsuite.h)
  01.01.03  2018-11-13  visbwa  Implemented Default_Init_Tasks for projects with up to 6 Cores,
                                fixed VTT support within IdleTasks (CANoeAPI_ConsumeTicks)
  01.02.00  2018-11-21  visbwa  Added testsuite callouts into every function
  01.02.01  2018-11-30  visbwa  Removed include of Rte_Main.h (Rte_Start() not called any more)
  01.03.00  2018-12-21  visbwa  Added call of new API Os_InitialEnableInterruptSources() within every Default_Init_Task
  01.03.01  2019-03-06  visbwa  Added hint for DrvCan using infix, added wrapper for Can_30_Mcan
  01.03.02  2019-06-13  vismaa  Added additional Default_Init_Task_Core6
  01.03.03  2019-06-26  visbwa  Moved call of BrsMainInit() previous to call of Os_Init()
  01.04.00  2019-07-02  visbwa  Enhanced FBL support (encapsulations, call of fblmain())
  01.04.01  2019-07-29  visbwa  Added implementation of BrsMainExceptionStartup() for calls from assembler StartUpCode
  01.05.00  2019-08-08  visbwa  Implemented support for different FBL usecases Legacy, WithoutOSScheduling and
                                OSScheduling, added BrsMainExceptionHandler module code for BrsMainStartup, changed FBL
                                call from fblmain() to FblMain() for legacy mode, removed include of fbl_main.h
  01.06.00  2019-08-28  visbwa  Changed encapsulation of task implementations (not used for FBL usecases Legacy and
                                WithoutOSScheduling any more)
            2019-10-09  visbwa  Enhanced support for VTT platform
  01.06.01  2019-11-04  visbwa  Changed include of Can.h into CanIf.h, to get rid of DrvCan with infix (Can_30_Mcan.h),
                                enhanced MemMap + SchM exclusive area abstraction for Mcan driver (uses infixes)
  01.07.00  2019-11-25  visbwa  Implemented Default_Init_Task_Trusted and Default_Init_Task_Corex_Trusted to allow the
                                SafeContext partitioning UseCase
            2019-11-27  visbwa  Changed include structure for vBrsCfg.h in BrsMain.h (does now also exist for VTT)
            2019-11-29  visbwa  Enhanced encapsulation with _MICROSOFT_C_VTT_ for DualTarget UseCase (no BrsHw!)
            2019-12-04  visbwa  Fixed encapsulation of DrvCan parts
  01.08.00  2020-02-11  visbwa  Support for UseCase HSM (BRS_ENABLE_HSM_SUPPORT, no EcuM),
                                added filtering of MultiCore parts through C_BRSASR_USECASE_SINGLECORE_COMMENT,
                                support for generated DrvCan macros to support modules with infix names
  01.09.00  2020-03-12  visbwa  Encapsulation of SchM implementation with MemMap defines for UseCase FBL,
                                encapsulation of struct brsMain_Cyclic_Callbacks with MemMap and 1ms-handler defines
  01.09.01  2020-04-22  visbwa  Added conditional call of BrsHwDisableEccErrorReporting() for FBL UseCase
  02.00.00  2020-05-29  visbwa  Major updates for vBaseEnv 2.0.0
                                - added BrsMainTogglePin() (moved from BrsHw.c BrsHwTogglePin())
  02.01.00  2020-06-29  visbwa  Added support for preferred PLL and Watchdog init in BrsMainInit()
  02.01.01  2020-07-24  visbwa  Soft increase for FBL_Legacy support
  02.02.00  2020-08-10  visbwa  Support for FBL Legacy UseCase w/o OS, usage of new FBL UseCase defines;
                                MemMap encapsulation of BrsMainExceptionHandler(), enabled 1ms handler also for FBL,
                                BrsMainCyclic1ms() usable from extern, removed usage of BrsAsrApplCanInterruptLockCtr
                                for FBL (FBL is always in polling mode)
  02.02.01  2020-10-30  visbwa  Added include of Os.h for VTT (usage of ShutdownOS()), removed AUTHOR IDENTITY
**********************************************************************************************************************/

/**********************************************************************************************************************
 *  EXAMPLE CODE ONLY
 *  -------------------------------------------------------------------------------------------------------------------
 *  This Example Code is only intended for illustrating an example of a possible BSW integration and BSW configuration.
 *  The Example Code has not passed any quality control measures and may be incomplete. The Example Code is neither
 *  intended nor qualified for use in series production. The Example Code as well as any of its modifications and/or
 *  implementations must be tested with diligent care and must comply with all quality requirements which are necessary
 *  according to the state of the art before their use.
 *********************************************************************************************************************/

/**********************************************************************************************************************
  INCLUDES
**********************************************************************************************************************/
#include "BrsMain.h"

#if !defined(_MICROSOFT_C_VTT_)
#include <stdint.h>
#include "scif.h"
#else
#include "CANoeAPI.h"
#include <stdio.h>
#include "Os.h"
#endif

#if defined(BRS_ENABLE_CAN_SUPPORT)
#include BRS_DRVCAN_HEADER_FILENAME
#endif

#if defined(BRS_ENABLE_TESTSUITE_SUPPORT)
#include "BrsTestsuite.h"
#endif

#include "Os_Hal_Interrupt.h"
#include "device_registers.h"
#include "arm_cr.h"
#include "J6e_features.h"
#include "device_registers.h"
#include "Gic_V3.h" 
#include "j6e_timer.h"

extern void INT_SYS_SetPriority(uint32, uint8);
extern void InitializeCrossCoreISR(uint8);
extern void INT_SYS_EnableIRQ(uint32);

/**********************************************************************************************************************
  VERSION CHECK
**********************************************************************************************************************/
#if (BRSMAIN_VERSION != 0x0202u)
#error "Header and source file are inconsistent!"
#endif
#if (BRSMAIN_BUGFIX_VERSION != 0x01u)
#error "Different versions of bugfix in Header and Source used!"
#endif

/**********************************************************************************************************************
  CONFIGURATION CHECK
**********************************************************************************************************************/

/**********************************************************************************************************************
  DEFINITION + MACROS
**********************************************************************************************************************/

/**********************************************************************************************************************
  GLOBAL VARIABLES
**********************************************************************************************************************/
#if defined(BRS_ENABLE_1MS_HANDLER)
#define BRSMAIN_CYCLIC_MAX_CALLBACKS 2u

#define START_SEC_VAR_NOINIT_UNSPECIFIED
#include "MemMap.h"
typedef struct
{
    void (*FunctionPointer[BRSMAIN_CYCLIC_MAX_CALLBACKS])(void);
    uint8 FunctionCounter;
} brsMain_Cyclic_Callbacks;
#define STOP_SEC_VAR
#include "MemMap.h"
#endif /*BRS_ENABLE_1MS_HANDLER*/

#if defined(BRS_ENABLE_CAN_SUPPORT) && !defined(BRS_ENABLE_FBL_SUPPORT)
#define START_SEC_VAR_NOINIT_8BIT
#include "MemMap.h"
static uint8 BrsAsrApplCanInterruptLockCtr;
#define STOP_SEC_VAR
#include "MemMap.h"
#endif /*BRS_ENABLE_CAN_SUPPORT&&!BRS_ENABLE_FBL_SUPPORT*/

/**********************************************************************************************************************
  GLOBAL CONST VARIABLES
**********************************************************************************************************************/

/**********************************************************************************************************************
  LOCAL VARIABLES
**********************************************************************************************************************/
#if defined(BRS_ENABLE_1MS_HANDLER)
#define START_SEC_VAR_ZERO_INIT_16BIT
#include "MemMap.h"
/**
 * \var brsMain_CallCounter1ms
 *      Counter for calls of the function BrsMainCyclic1ms
 */
static volatile uint16 brsMain_CallCounter1ms;

/**
 * \var brsMain_CallCounter1sec
 *      Counter for 1000 calls of the function BrsMainCyclic1ms
 */
static volatile uint16 brsMain_CallCounter1sec;
#define STOP_SEC_VAR
#include "MemMap.h"

#define START_SEC_VAR_NOINIT_UNSPECIFIED
#include "MemMap.h"
static brsMain_Cyclic_Callbacks brsMain_Cyclic_Callbacks_Background;
static brsMain_Cyclic_Callbacks brsMain_Cyclic_Callbacks_1ms;
static brsMain_Cyclic_Callbacks brsMain_Cyclic_Callbacks_10ms;
static brsMain_Cyclic_Callbacks brsMain_Cyclic_Callbacks_100ms;
static brsMain_Cyclic_Callbacks brsMain_Cyclic_Callbacks_250ms;
static brsMain_Cyclic_Callbacks brsMain_Cyclic_Callbacks_500ms;
static brsMain_Cyclic_Callbacks brsMain_Cyclic_Callbacks_1000ms;
#define STOP_SEC_VAR
#include "MemMap.h"
#endif /*BRS_ENABLE_1MS_HANDLER*/

/**********************************************************************************************************************
  LOCAL CONST VARIABLES
**********************************************************************************************************************/
#if defined(_MICROSOFT_C_VTT_)
#define BRSMAIN_ERRORBUFFERSIZE 120
#endif

/**********************************************************************************************************************
  PROTOTYPES OF GLOBAL FUNCTIONS
**********************************************************************************************************************/
#if defined(BRS_FBL_NO_ECUMINIT)
extern void FblMain(void);
#endif

/**********************************************************************************************************************
  PROTOTYPES OF LOCAL FUNCTIONS
**********************************************************************************************************************/

/**********************************************************************************************************************
  FUNCTION DEFINITIONS
**********************************************************************************************************************/
#if !defined(_MICROSOFT_C_VTT_)
#if defined(BRS_ENABLE_SUPPORT_LEDS)
void BrsMainWrapperTogglePinLED(void) { BrsMainTogglePin(BRSMAIN_TOGGLEPIN_LED); }
#endif

#if defined(BRS_ENABLE_SUPPORT_TOGGLE_WD_PIN)
void BrsMainWrapperTogglePinWD(void) { BrsMainTogglePin(BRSMAIN_TOGGLEPIN_WD); }
#endif

#if defined(BRS_ENABLE_SUPPORT_TOGGLE_CUSTOM_PIN)
void BrsMainWrapperTogglePinCustom(void) { BrsMainTogglePin(BRSMAIN_TOGGLEPIN_CUSTOM); }
#endif
#endif /*!_MICROSOFT_C_VTT_*/

/*****************************************************************************/
/**
 * @brief      Main initialization routine.
 *             Contains initialisation of BRSModules and BrsMain specific initialization
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    Function is called from main
 */
/*****************************************************************************/
void BrsMainInit(void)
{
#if defined(BRSMAIN_BRSMAININIT_CALLOUT)
    BrsTestsuite_BrsMain_BrsMainInit();
#endif

#if defined(BRS_ENABLE_CAN_SUPPORT) && !defined(BRS_ENABLE_FBL_SUPPORT)
    /* UserDefined ExclusiveArea handling for CAN channels, according to AN-ISC-8-1149_ErrorHook_E_OS_DISABLED_INT.pdf
     */
    BrsAsrApplCanInterruptLockCtr = 0u;
#endif

#if !defined(BRS_FBL_NO_ECUMINIT) && !defined(BRS_FBL_WITH_ECUMINIT) && !defined(_MICROSOFT_C_VTT_)
#if !defined(BRS_ENABLE_PREFER_PLL_WATCHDOG_INIT)
    /* If preferred feature is acivated, initialization will take place in BrsMain_MemoryInit_StageOne_Hook(). */
#if defined(BRSHW_PREINIT_AVAILABLE)
    BrsHwPreInitPowerOn();
#endif

#if defined(BRS_ENABLE_WATCHDOG)
    BrsHwWatchdogInitPowerOn();
#endif

#if defined(BRS_ENABLE_PLLCLOCKS)
    BrsHwPllInitPowerOn();
#endif
#endif /*!BRS_ENABLE_PREFER_PLL_WATCHDOG_INIT*/

#if defined(BRS_ENABLE_PORT)
    BrsHwPortInitPowerOn();
#endif

#if defined(BRS_ENABLE_FBL_SUPPORT)
#if defined(BRSHW_DISABLE_ECC_AVAILABLE)
    BrsHwDisableEccErrorReporting();
#endif
#endif
#endif /*!BRS_FBL_NO_ECUMINIT&&!BRS_FBL_WITH_ECUMINIT&&!_MICROSOFT_C_VTT_*/

#if defined(BRS_ENABLE_1MS_HANDLER)
    brsMain_CallCounter1ms  = 0u;
    brsMain_CallCounter1sec = 0u;

    brsMain_Cyclic_Callbacks_Background.FunctionCounter = 0u;
    brsMain_Cyclic_Callbacks_1ms.FunctionCounter        = 0u;
    brsMain_Cyclic_Callbacks_10ms.FunctionCounter       = 0u;
    brsMain_Cyclic_Callbacks_100ms.FunctionCounter      = 0u;
    brsMain_Cyclic_Callbacks_250ms.FunctionCounter      = 0u;
    brsMain_Cyclic_Callbacks_500ms.FunctionCounter      = 0u;
    brsMain_Cyclic_Callbacks_1000ms.FunctionCounter     = 0u;
#endif /*BRS_ENABLE_1MS_HANDLER*/

#if !defined(_MICROSOFT_C_VTT_)
#if defined(BRS_ENABLE_SUPPORT_LEDS)
    BrsMainRegisterCyclic(BrsMainWrapperTogglePinLED, BRSMAIN_CYCLETIME_500MS);
#endif

#if defined(BRS_ENABLE_SUPPORT_TOGGLE_WD_PIN)
    BrsMainRegisterCyclic(BrsMainWrapperTogglePinWD, BRSMAIN_CYCLETIME_250MS);
#endif

#if defined(BRS_ENABLE_SUPPORT_TOGGLE_CUSTOM_PIN)
    BrsMainRegisterCyclic(BrsMainWrapperTogglePinCustom, BRSMAIN_CYCLETIME_1000MS);
#endif
#endif /*!_MICROSOFT_C_VTT_*/
}

#if defined(BRS_ENABLE_1MS_HANDLER)
/*****************************************************************************/
/**
 * @brief      Routine to register cyclic callbacks.
 * @pre        Initialization of BrsMain was done threw call of BrsMainInit().
 * @param[in]  FunctionPointer has to be a pointer to a function of type
 *             void function(void).
 * @param[in]  Cycletime described the cycletime, the callback should be triggered.
 * @param[out] -
 * @return     -
 * @context    Function is called from modules that need cyclic callbacks.
 */
/*****************************************************************************/
void BrsMainRegisterCyclic(void (*FunctionPointer)(void), brsMain_Cyclic_Cycletime Cycletime)
{
#if defined(BRSMAIN_BRSMAINREGISTERCYCLIC_CALLOUT)
    BrsTestsuite_BrsMain_BrsMainRegisterCyclic(FunctionPointer, Cycletime);
#endif

    switch (Cycletime)
    {
    case BRSMAIN_CYCLETIME_BACKGROUND:
        if (brsMain_Cyclic_Callbacks_Background.FunctionCounter >= BRSMAIN_CYCLIC_MAX_CALLBACKS)
            BrsMainExceptionHandler(kBrsIllegalParameter, BRSERROR_MODULE_BRSMAIN, (uint16)(__LINE__));
        brsMain_Cyclic_Callbacks_Background.FunctionPointer[brsMain_Cyclic_Callbacks_Background.FunctionCounter] =
            FunctionPointer;
        brsMain_Cyclic_Callbacks_Background.FunctionCounter++;
        break;

    case BRSMAIN_CYCLETIME_1MS:
        if (brsMain_Cyclic_Callbacks_1ms.FunctionCounter >= BRSMAIN_CYCLIC_MAX_CALLBACKS)
            BrsMainExceptionHandler(kBrsIllegalParameter, BRSERROR_MODULE_BRSMAIN, (uint16)(__LINE__));
        brsMain_Cyclic_Callbacks_1ms.FunctionPointer[brsMain_Cyclic_Callbacks_1ms.FunctionCounter] = FunctionPointer;
        brsMain_Cyclic_Callbacks_1ms.FunctionCounter++;
        break;

    case BRSMAIN_CYCLETIME_10MS:
        if (brsMain_Cyclic_Callbacks_10ms.FunctionCounter >= BRSMAIN_CYCLIC_MAX_CALLBACKS)
            BrsMainExceptionHandler(kBrsIllegalParameter, BRSERROR_MODULE_BRSMAIN, (uint16)(__LINE__));
        brsMain_Cyclic_Callbacks_10ms.FunctionPointer[brsMain_Cyclic_Callbacks_10ms.FunctionCounter] = FunctionPointer;
        brsMain_Cyclic_Callbacks_10ms.FunctionCounter++;
        break;

    case BRSMAIN_CYCLETIME_100MS:
        if (brsMain_Cyclic_Callbacks_100ms.FunctionCounter >= BRSMAIN_CYCLIC_MAX_CALLBACKS)
            BrsMainExceptionHandler(kBrsIllegalParameter, BRSERROR_MODULE_BRSMAIN, (uint16)(__LINE__));
        brsMain_Cyclic_Callbacks_100ms.FunctionPointer[brsMain_Cyclic_Callbacks_100ms.FunctionCounter] =
            FunctionPointer;
        brsMain_Cyclic_Callbacks_100ms.FunctionCounter++;
        break;

    case BRSMAIN_CYCLETIME_250MS:
        if (brsMain_Cyclic_Callbacks_250ms.FunctionCounter >= BRSMAIN_CYCLIC_MAX_CALLBACKS)
            BrsMainExceptionHandler(kBrsIllegalParameter, BRSERROR_MODULE_BRSMAIN, (uint16)(__LINE__));
        brsMain_Cyclic_Callbacks_250ms.FunctionPointer[brsMain_Cyclic_Callbacks_250ms.FunctionCounter] =
            FunctionPointer;
        brsMain_Cyclic_Callbacks_250ms.FunctionCounter++;
        break;

    case BRSMAIN_CYCLETIME_500MS:
        if (brsMain_Cyclic_Callbacks_500ms.FunctionCounter >= BRSMAIN_CYCLIC_MAX_CALLBACKS)
            BrsMainExceptionHandler(kBrsIllegalParameter, BRSERROR_MODULE_BRSMAIN, (uint16)(__LINE__));
        brsMain_Cyclic_Callbacks_500ms.FunctionPointer[brsMain_Cyclic_Callbacks_500ms.FunctionCounter] =
            FunctionPointer;
        brsMain_Cyclic_Callbacks_500ms.FunctionCounter++;
        break;

    case BRSMAIN_CYCLETIME_1000MS:
        if (brsMain_Cyclic_Callbacks_1000ms.FunctionCounter >= BRSMAIN_CYCLIC_MAX_CALLBACKS)
            BrsMainExceptionHandler(kBrsIllegalParameter, BRSERROR_MODULE_BRSMAIN, (uint16)(__LINE__));
        brsMain_Cyclic_Callbacks_1000ms.FunctionPointer[brsMain_Cyclic_Callbacks_1000ms.FunctionCounter] =
            FunctionPointer;
        brsMain_Cyclic_Callbacks_1000ms.FunctionCounter++;
        break;

    default:
        BrsMainExceptionHandler(kBrsIllegalParameter, BRSERROR_MODULE_BRSMAIN, (uint16)(__LINE__));
        break;
    }
}
#endif /*BRS_ENABLE_1MS_HANDLER*/

#if !defined(BRS_ENABLE_FBL_SUPPORT)
/*****************************************************************************/
/**
 * @brief      InitTask to call EcuM_StartupTwo().
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    The task is started once by the OS.
 */
/*****************************************************************************/
void Os_Task_Default_Init_Task(void)
{
#if defined(BRSMAIN_DEFAULT_INIT_TASK_CALLOUT)
    BrsTestsuite_BrsMain_Default_Init_Task();
#endif

#if defined(BRS_ENABLE_OS_MULTICORESUPPORT)
    /* Workaround for RTE ESCAN00078832 */
    /* Use this code, if you get a Det Error at the end of Rte_Start() on MasterCore */
    /* Rte_Start() on the SlaveCores has to be called first, before Rte_Start() on MasterCore */
    /* SET THIS InitTask TO FULL PREEMPTIVE (OsTaskSchedule) within OsConfig! */
    /*while(Rte_InitState_1 != RTE_STATE_INIT)
    {
      (void)Schedule();
    }*/
#endif /*BRS_ENABLE_OS_MULTICORESUPPORT*/

#if defined(BRS_ENABLE_TESTSUITE_SUPPORT)
    BrsTestsuiteInit();
#endif

#if defined(BRS_ENABLE_HSM_SUPPORT)
    vHsm_StartupTwo();
#else
    InitializeCrossCoreISR(0);

    // INT_SYS_SetPriority(30, 8);
    // INT_SYS_EnableIRQ(30);
	// // INT_SYS_EnableIRQ(237);
    // // Os_Hal_INTC_EnableSource(237, 8, 0); //Enable xsignal ISR
    // Os_Hal_INTC_EnableSource(30, 8, 0); // Enable tick ISR

    INT_SYS_SetPriority(TIMER_ISR_Core0, 8);
    INT_SYS_EnableIRQ(TIMER_ISR_Core0);
    Os_Hal_INTC_EnableSource(TIMER_ISR_Core0, 8, 0); // Enable tick ISR

    INT_SYS_SetPriority(30, 8);
    INT_SYS_EnableIRQ(30);
    Os_Hal_INTC_DisableSourceNum(30, 8, 0); //disable generic-timer ISR
    Os_Hal_INTC_DisableSourceNum(238, 8, 0); //disable cross 238 ISR

    INT_SYS_SetPriority(120, 8);
    INT_SYS_EnableIRQ(120);
    Os_Hal_INTC_EnableSource(120, 8, 0); // Can1_isr
    
    INT_SYS_SetPriority(124, 8);
    INT_SYS_EnableIRQ(124);
    Os_Hal_INTC_EnableSource(124, 8, 0); // Can2_isr

    INT_SYS_SetPriority(144, 8);
    INT_SYS_EnableIRQ(144);
    Os_Hal_INTC_EnableSource(144, 8, 0); // Can7_isr

	INT_SYS_SetPriority(209, 8);
    INT_SYS_EnableIRQ(209);
	Os_Hal_INTC_EnableSource(209, 8, 0); // Mdma0Ch0

	INT_SYS_SetPriority(210, 8);
    INT_SYS_EnableIRQ(210);
	Os_Hal_INTC_EnableSource(210, 8, 0); // Mdma0Ch1

	INT_SYS_SetPriority(251, 8);
    INT_SYS_EnableIRQ(251);
	Os_Hal_INTC_EnableSource(251, 8, 0); // CpuIPC1Ch0  Scmi

	INT_SYS_SetPriority(252, 8);
    INT_SYS_EnableIRQ(252);
	Os_Hal_INTC_EnableSource(252, 8, 0); // CpuIPC1Ch1  Scmi
	
	INT_SYS_SetPriority(253, 8);
    INT_SYS_EnableIRQ(253);
	Os_Hal_INTC_EnableSource(253, 8, 0); // CpuIPC1Ch2  Scmi

	INT_SYS_SetPriority(241, 8);
    INT_SYS_EnableIRQ(241);
    Os_Hal_INTC_EnableSource(241, 8, 0); // ipc

	INT_SYS_SetPriority(265, 8);
    INT_SYS_EnableIRQ(265);
    Os_Hal_INTC_EnableSource(265, 8, 0); // ipc

    INT_SYS_SetPriority(254, 8);
    INT_SYS_EnableIRQ(254);
    Os_Hal_INTC_EnableSource(254, 8, 0); // ipc

    INT_SYS_SetPriority(255, 8);
    INT_SYS_EnableIRQ(255);
    Os_Hal_INTC_EnableSource(255, 8, 0); // ipc

	INT_SYS_SetPriority(256, 8);
    INT_SYS_EnableIRQ(256);
    Os_Hal_INTC_EnableSource(256, 8, 0); // ipc

	INT_SYS_SetPriority(257, 8);
    INT_SYS_EnableIRQ(257);
    Os_Hal_INTC_EnableSource(257, 8, 0); // ipc

	INT_SYS_SetPriority(258, 8);
    INT_SYS_EnableIRQ(258);
    Os_Hal_INTC_EnableSource(258, 8, 0); // ipc

	INT_SYS_SetPriority(259, 8);
    INT_SYS_EnableIRQ(259);
    Os_Hal_INTC_EnableSource(259, 8, 0); // ipc

	INT_SYS_SetPriority(260, 8);
    INT_SYS_EnableIRQ(260);
    Os_Hal_INTC_EnableSource(260, 8, 0); // ipc

	INT_SYS_SetPriority(261, 8);
    INT_SYS_EnableIRQ(261);
    Os_Hal_INTC_EnableSource(261, 8, 0); // ipc

	INT_SYS_SetPriority(262, 8);
    INT_SYS_EnableIRQ(262);
    Os_Hal_INTC_EnableSource(262, 8, 0); // ipc

	INT_SYS_SetPriority(242, 8);
    INT_SYS_EnableIRQ(242);
    Os_Hal_INTC_EnableSource(242, 8, 0); // ipc

	INT_SYS_SetPriority(263, 8);
    INT_SYS_EnableIRQ(263);
    Os_Hal_INTC_EnableSource(263, 8, 0); // ipc

	INT_SYS_SetPriority(264, 8);
    INT_SYS_EnableIRQ(264);
    Os_Hal_INTC_EnableSource(264, 8, 0); // ipc

	INT_SYS_SetPriority(266, 8);
    INT_SYS_EnableIRQ(266);
    Os_Hal_INTC_EnableSource(266, 8, 0); // ipc

	INT_SYS_SetPriority(267, 8);
    INT_SYS_EnableIRQ(267);
    Os_Hal_INTC_EnableSource(267, 8, 0); // ipc

	INT_SYS_SetPriority(268, 8);
    INT_SYS_EnableIRQ(268);
    Os_Hal_INTC_EnableSource(268, 8, 0); // ipc

	INT_SYS_SetPriority(269, 8);
    INT_SYS_EnableIRQ(269);
    Os_Hal_INTC_EnableSource(269, 8, 0); // ipc

	INT_SYS_SetPriority(211, 8);
    INT_SYS_EnableIRQ(211);
	Os_Hal_INTC_EnableSource(211, 8, 0); // Mdma1Ch0

	INT_SYS_SetPriority(212, 8);
    INT_SYS_EnableIRQ(212);
	Os_Hal_INTC_EnableSource(212, 8, 0); // Mdma1Ch1

    Os_Hal_INTC_EnableSource(174, 8, 0); // Enable spi5 for tcan1145 ISR
    
    
	INT_SYS_SetPriority(153, 8);
    INT_SYS_EnableIRQ(153);
	Os_Hal_INTC_EnableSource(153, 8, 0); // Gmac_TxCh0Isr
    
	INT_SYS_SetPriority(159, 8);
    INT_SYS_EnableIRQ(159);
	Os_Hal_INTC_EnableSource(159, 8, 0); // Gmac_RxCh0Isr

	INT_SYS_SetPriority(165, 8);
    INT_SYS_EnableIRQ(165);
	Os_Hal_INTC_EnableSource(165, 8, 0); // Gmac_SbdIsr

	INT_SYS_SetPriority(166, 8);
    INT_SYS_EnableIRQ(166);
	Os_Hal_INTC_EnableSource(166, 8, 0); // Gmac_PmtIsr
    
	INT_SYS_SetPriority(167, 8);
    INT_SYS_EnableIRQ(167);
	Os_Hal_INTC_EnableSource(167, 8, 0); // Gmac_LpiIsr

	INT_SYS_SetPriority(106, 8);
    INT_SYS_EnableIRQ(106);
    Os_Hal_INTC_EnableSource(106, 8, 0);

    INT_SYS_SetPriority(107, 8);
    INT_SYS_EnableIRQ(107);
    Os_Hal_INTC_EnableSource(107, 8, 0);

    INT_SYS_SetPriority(108, 8);
    INT_SYS_EnableIRQ(108);
    Os_Hal_INTC_EnableSource(108, 8, 0);
	
    EcuM_StartupTwo();
#endif

    (void)TerminateTask();
}

void tmu_isr(void)
{
    __NOP();
}

/*****************************************************************************/
/**
 * @brief      InitTask to call Os_InitialEnableInterruptSources().
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    The task is started once by the OS. It is separated from
 *             Default_Init_Task() to allow the SafeContext partitioning UseCase.
 */
/*****************************************************************************/
extern void irq_enable(uint32 IrqIdx);
void Os_Task_Default_Init_Task_Trusted(void)
{
#if defined(BRSMAIN_DEFAULT_INIT_TASK_TRUSTED_CALLOUT)
    BrsTestsuite_BrsMain_Default_Init_Task_Trusted();
#endif

    //irq_enable(322);
#if 1
	//Fix GTC CLK 200M
    //TISCI_DEV_GTC0: 61
	//TISCI_DEV_GTC0_GTC_CLK : 1
#endif
    (void)TerminateTask();
}

#if defined(BRS_ENABLE_OS_MULTICORESUPPORT)
/*****************************************************************************/
/**
 * @brief      InitTask to call EcuM_StartupTwo() on the 2nd Core.
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    The task is started once by the OS on the 2nd Core.
 */
/*****************************************************************************/
void Os_Task_Default_Init_Task_Core1(void)
{
#if defined(BRSMAIN_DEFAULT_INIT_TASK_CORE1_CALLOUT)
    BrsTestsuite_BrsMain_Default_Init_Task_Core1();
#endif

    InitializeCrossCoreISR(1);
    INT_SYS_SetPriority(30, 8);
    INT_SYS_SetPriority(238, 8);
    INT_SYS_EnableIRQ(30);
    INT_SYS_EnableIRQ(238);
    Os_Hal_INTC_DisableSourceNum(30, 8, 0); //Enable tick ISR
    Os_Hal_INTC_EnableSource(238, 8, 0); //Enable xsignal ISR

    INT_SYS_SetPriority(TIMER_ISR_Core1, 8);
    INT_SYS_EnableIRQ(TIMER_ISR_Core1);
    Os_Hal_INTC_EnableSource(TIMER_ISR_Core1, 8, 0); // Enable tick ISR

    Os_Hal_INTC_EnableSource(68, 8, 0); // Enable gpio0 ISR
    Os_Hal_INTC_EnableSource(87, 8, 0); // Enable timer2 ch0 ISR
    Os_Hal_INTC_EnableSource(88, 8, 0); // Enable timer2 ch1 ISR
    // Os_Hal_INTC_EnableSource(213, 8, 0); // Enable PDMA ch0 ISR
    Os_Hal_INTC_EnableSource(214, 8, 0); // Enable PDMA ch1 ISR

    EcuM_StartupTwo();

    (void)TerminateTask();
}

/*****************************************************************************/
/**
 * @brief      InitTask to call Os_InitialEnableInterruptSources() on the 2nd Core.
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    The task is started once by the OS on the 2nd Core. It is separated from
 *             Default_Init_Task_Core1() to allow the SafeContext partitioning UseCase.
 */
/*****************************************************************************/
void Os_Task_Default_Init_Task_Core1_Trusted(void)
{
#if defined(BRSMAIN_DEFAULT_INIT_TASK_CORE1_TRUSTED_CALLOUT)
    BrsTestsuite_BrsMain_Default_Init_Task_Core1_Trusted();
#endif

    //irq_enable(324);

    (void)TerminateTask();
}

#ifndef REMOVE_FOR_NOW
/*****************************************************************************/
/**
 * @brief      InitTask to call EcuM_StartupTwo() on the 3rd Core.
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    The task is started once by the OS on the 3rd Core.
 */
/*****************************************************************************/
TASK(Default_Init_Task_Core2)
{
#if defined(BRSMAIN_DEFAULT_INIT_TASK_CORE2_CALLOUT)
    BrsTestsuite_BrsMain_Default_Init_Task_Core2();
#endif

    EcuM_StartupTwo();

    (void)TerminateTask();
}

/*****************************************************************************/
/**
 * @brief      InitTask to call Os_InitialEnableInterruptSources() on the 3rd Core.
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    The task is started once by the OS on the 3rd Core. It is separated from
 *             Default_Init_Task_Core2() to allow the SafeContext partitioning UseCase.
 */
/*****************************************************************************/
TASK(Default_Init_Task_Core2_Trusted)
{
#if defined(BRSMAIN_DEFAULT_INIT_TASK_CORE2_TRUSTED_CALLOUT)
    BrsTestsuite_BrsMain_Default_Init_Task_Core2_Trusted();
#endif

    Os_InitialEnableInterruptSources(FALSE);

    (void)TerminateTask();
}

/*****************************************************************************/
/**
 * @brief      InitTask to call EcuM_StartupTwo() on the 4th Core.
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    The task is started once by the OS on the 4th Core.
 */
/*****************************************************************************/
TASK(Default_Init_Task_Core3)
{
#if defined(BRSMAIN_DEFAULT_INIT_TASK_CORE3_CALLOUT)
    BrsTestsuite_BrsMain_Default_Init_Task_Core3();
#endif

    EcuM_StartupTwo();

    (void)TerminateTask();
}

/*****************************************************************************/
/**
 * @brief      InitTask to call Os_InitialEnableInterruptSources() on the 4th Core.
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    The task is started once by the OS on the 4th Core. It is separated from
 *             Default_Init_Task_Core3() to allow the SafeContext partitioning UseCase.
 */
/*****************************************************************************/
TASK(Default_Init_Task_Core3_Trusted)
{
#if defined(BRSMAIN_DEFAULT_INIT_TASK_CORE3_TRUSTED_CALLOUT)
    BrsTestsuite_BrsMain_Default_Init_Task_Core3_Trusted();
#endif

    Os_InitialEnableInterruptSources(FALSE);

    (void)TerminateTask();
}

/*****************************************************************************/
/**
 * @brief      InitTask to call EcuM_StartupTwo() on the 5th Core.
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    The task is started once by the OS on the 5th Core.
 */
/*****************************************************************************/
TASK(Default_Init_Task_Core4)
{
#if defined(BRSMAIN_DEFAULT_INIT_TASK_CORE4_CALLOUT)
    BrsTestsuite_BrsMain_Default_Init_Task_Core4();
#endif

    EcuM_StartupTwo();

    (void)TerminateTask();
}

/*****************************************************************************/
/**
 * @brief      InitTask to call Os_InitialEnableInterruptSources() on the 5th Core.
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    The task is started once by the OS on the 5th Core. It is separated from
 *             Default_Init_Task_Core4() to allow the SafeContext partitioning UseCase.
 */
/*****************************************************************************/
TASK(Default_Init_Task_Core4_Trusted)
{
#if defined(BRSMAIN_DEFAULT_INIT_TASK_CORE4_TRUSTED_CALLOUT)
    BrsTestsuite_BrsMain_Default_Init_Task_Core4_Trusted();
#endif

    Os_InitialEnableInterruptSources(FALSE);

    (void)TerminateTask();
}

/*****************************************************************************/
/**
 * @brief      InitTask to call EcuM_StartupTwo() on the 6th Core.
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    The task is started once by the OS on the 6th Core.
 */
/*****************************************************************************/
TASK(Default_Init_Task_Core5)
{
#if defined(BRSMAIN_DEFAULT_INIT_TASK_CORE5_CALLOUT)
    BrsTestsuite_BrsMain_Default_Init_Task_Core5();
#endif

    EcuM_StartupTwo();

    (void)TerminateTask();
}

/*****************************************************************************/
/**
 * @brief      InitTask to call Os_InitialEnableInterruptSources() on the 6th Core.
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    The task is started once by the OS on the 6th Core. It is separated from
 *             Default_Init_Task_Core5() to allow the SafeContext partitioning UseCase.
 */
/*****************************************************************************/
TASK(Default_Init_Task_Core5_Trusted)
{
#if defined(BRSMAIN_DEFAULT_INIT_TASK_CORE5_TRUSTED_CALLOUT)
    BrsTestsuite_BrsMain_Default_Init_Task_Core5_Trusted();
#endif

    Os_InitialEnableInterruptSources(FALSE);

    (void)TerminateTask();
}

/*****************************************************************************/
/**
 * @brief      InitTask to call EcuM_StartupTwo() on the 7th Core.
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    The task is started once by the OS on the 7th Core.
 */
/*****************************************************************************/
TASK(Default_Init_Task_Core6)
{
#if defined(BRSMAIN_DEFAULT_INIT_TASK_CORE6_CALLOUT)
    BrsTestsuite_BrsMain_Default_Init_Task_Core6();
#endif

    EcuM_StartupTwo();

    (void)TerminateTask();
}

/*****************************************************************************/
/**
 * @brief      InitTask to call Os_InitialEnableInterruptSources() on the 7th Core.
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    The task is started once by the OS on the 7th Core. It is separated from
 *             Default_Init_Task_Core6() to allow the SafeContext partitioning UseCase.
 */
/*****************************************************************************/
TASK(Default_Init_Task_Core6_Trusted)
{
#if defined(BRSMAIN_DEFAULT_INIT_TASK_CORE6_TRUSTED_CALLOUT)
    BrsTestsuite_BrsMain_Default_Init_Task_Core6_Trusted();
#endif

    Os_InitialEnableInterruptSources(FALSE);

    (void)TerminateTask();
}
#endif /*BRS_ENABLE_OS_MULTICORESUPPORT*/
#endif /*REMOVE_FOR_NOW*/
#endif /*!BRS_ENABLE_FBL_SUPPORT*/

#if defined(BRS_ENABLE_1MS_HANDLER)
/*****************************************************************************/
/**
 * @brief      One millisecond handler for BrsMain
 *               - Executes retransmission of BRS TCC messages
 *               - Toggling of LED (alive signal)
 *               - BRS Test code (1s cyclic negative TCC response message)
 * @pre        Initialization of BrsMain was done threw call of BrsMainInit().
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    Function is called each millisecond either from the main loop or from the BRS main task (TASK(mainTask))
 */
/*****************************************************************************/
void BrsMainCyclic1ms(void)
{
    uint8 cycliccounter;

#if defined(BRSMAIN_BRSMAINCYCLIC1MS_CALLOUT)
    BrsTestsuite_BrsMain_BrsMainCyclic1ms();
#endif

    brsMain_CallCounter1ms++;

    for (cycliccounter = 0u; cycliccounter < brsMain_Cyclic_Callbacks_1ms.FunctionCounter; cycliccounter++)
        brsMain_Cyclic_Callbacks_1ms.FunctionPointer[cycliccounter]();

    if (brsMain_CallCounter1ms % 10 == 0)
    {
        for (cycliccounter = 0u; cycliccounter < brsMain_Cyclic_Callbacks_10ms.FunctionCounter; cycliccounter++)
            brsMain_Cyclic_Callbacks_10ms.FunctionPointer[cycliccounter]();
    }

    if (brsMain_CallCounter1ms % 100 == 0)
    {
        for (cycliccounter = 0u; cycliccounter < brsMain_Cyclic_Callbacks_100ms.FunctionCounter; cycliccounter++)
            brsMain_Cyclic_Callbacks_100ms.FunctionPointer[cycliccounter]();
    }

    if (brsMain_CallCounter1ms % 250 == 0)
    {
        for (cycliccounter = 0u; cycliccounter < brsMain_Cyclic_Callbacks_250ms.FunctionCounter; cycliccounter++)
            brsMain_Cyclic_Callbacks_250ms.FunctionPointer[cycliccounter]();
    }

    if (brsMain_CallCounter1ms % 500 == 0)
    {
        for (cycliccounter = 0u; cycliccounter < brsMain_Cyclic_Callbacks_500ms.FunctionCounter; cycliccounter++)
            brsMain_Cyclic_Callbacks_500ms.FunctionPointer[cycliccounter]();
    }

    if (brsMain_CallCounter1ms % 1000 == 0)
    {
        for (cycliccounter = 0u; cycliccounter < brsMain_Cyclic_Callbacks_1000ms.FunctionCounter; cycliccounter++)
            brsMain_Cyclic_Callbacks_1000ms.FunctionPointer[cycliccounter]();
        brsMain_CallCounter1ms = 0u;
        brsMain_CallCounter1sec++;
    }
}

#if !defined(BRS_ENABLE_FBL_SUPPORT)
/*****************************************************************************/
/**
 * @brief      BrsMainTask executes the 1 millisecond handler
 *             The function initiates calls to BrsMainCyclic1ms
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    The task is started once by the OS and activated by
 *             the OS event EvCyclicAlarm_1ms
 */
/*****************************************************************************/
TASK(BrsMainTask)
{
    EventMaskType ev;

#if defined(BRSMAIN_BRSMAINTASK_CALLOUT)
    BrsTestsuite_BrsMain_BrsMainTask();
#endif

    (void)SetRelAlarm(BrsCyclicAlarm_1ms, OS_MS2TICKS_SystemTimer(1), OS_MS2TICKS_SystemTimer(1));

    for (;;)
    {
        (void)WaitEvent(BrsEvCyclicAlarm_1ms);
        (void)GetEvent(BrsMainTask, &ev);
        (void)ClearEvent(ev);
        if (ev & BrsEvCyclicAlarm_1ms)
        {
            /* 1ms event detected, call the ms handler */
            BrsMainCyclic1ms();
        }
    }
}

TASK(BrsMainBackgroundTask)
{
    uint8 cycliccounter;

#if defined(BRSMAIN_BRSMAINBACKGROUNDTASK_CALLOUT)
    BrsTestsuite_BrsMain_BrsMainBackgroundTask();
#endif

    for (;;)
    {
#if defined(_MICROSOFT_C_VTT_)
        CANoeAPI_ConsumeTicks(100);
#endif

        for (cycliccounter = 0u; cycliccounter < brsMain_Cyclic_Callbacks_Background.FunctionCounter; cycliccounter++)
            brsMain_Cyclic_Callbacks_Background.FunctionPointer[cycliccounter]();

        (void)Schedule();
    }
}
#endif /*!BRS_ENABLE_FBL_SUPPORT*/
#endif /*BRS_ENABLE_1MS_HANDLER*/

#if defined(BRS_ENABLE_SUPPORT_LEDS) || defined(BRS_ENABLE_SUPPORT_TOGGLE_WD_PIN) ||                                   \
    defined(BRS_ENABLE_SUPPORT_TOGGLE_CUSTOM_PIN)
#if !defined(BRS_ENABLE_PORT)
#define BRSHW_PORT_LOGIC_HIGH STD_HIGH
#define BRSHW_PORT_LOGIC_LOW STD_LOW
#endif
#if defined(BRS_ENABLE_FBL_SUPPORT)
#define BRS_START_SEC_RAM_CODE
#include "Brs_MemMap.h"
#endif
/*****************************************************************************/
/**
 * @brief      This API is used to toggle a PortPin.
 *             Per default, the following parameters are available:
 *               BRSMAIN_TOGGLEPIN_LED
 *               BRSMAIN_TOGGLEPIN_WD
 *               BRSMAIN_TOGGLEPIN_CUSTOM
 *             Depending pins must be configured and initialized within BrsHw.
 * @pre        -
 * @param[in]  Pin to be toggled
 * @param[out] -
 * @return     -
 * @context    Function is called from all modules to set or clear a PortPin
 */
/*****************************************************************************/
void BrsMainTogglePin(brsMain_TogglePin Pin)
{
#define START_SEC_VAR_NOINIT_UNSPECIFIED
#include "MemMap.h"
#if defined(BRS_ENABLE_SUPPORT_LEDS)
    static uint8 BrsMain_ToggleSwitch_LED = BRSHW_PORT_LOGIC_HIGH;
#endif
#if defined(BRS_ENABLE_SUPPORT_TOGGLE_WD_PIN)
    static uint8 BrsMain_ToggleSwitch_WD = BRSHW_PORT_LOGIC_HIGH;
#endif
#if defined(BRS_ENABLE_SUPPORT_TOGGLE_CUSTOM_PIN)
    static uint8 BrsMain_ToggleSwitch_CUSTOM = BRSHW_PORT_LOGIC_HIGH;
#endif
#define STOP_SEC_VAR
#include "MemMap.h"

    switch (Pin)
    {
#if defined(BRS_ENABLE_SUPPORT_LEDS)
    case BRSMAIN_TOGGLEPIN_LED:
#if defined(BRS_ENABLE_PORT)
        BrsHwPort_SetLevel(BRSHW_PORT_LED, BrsMain_ToggleSwitch_LED & 0x01);
#else
        Dio_WriteChannel(BrsHw_DioChannel_ToggleLED, BrsMain_ToggleSwitch_LED & 0x01);
#endif
        BrsMain_ToggleSwitch_LED++;
        break;
#endif /*BRS_ENABLE_SUPPORT_LEDS*/
#if defined(BRS_ENABLE_SUPPORT_TOGGLE_WD_PIN)
    case BRSMAIN_TOGGLEPIN_WD:
#if defined(BRS_ENABLE_PORT)
        BrsHwPort_SetLevel(BRSHW_PORT_TOGGLE_WD, BrsMain_ToggleSwitch_WD & 0x01);
#else
        Dio_WriteChannel(BrsHw_DioChannel_ToggleWdPin, BrsMain_ToggleSwitch_WD & 0x01);
#endif
        BrsMain_ToggleSwitch_WD++;
        break;
#endif /*BRS_ENABLE_SUPPORT_TOGGLE_WD_PIN*/
#if defined(BRS_ENABLE_SUPPORT_TOGGLE_CUSTOM_PIN)
    case BRSMAIN_TOGGLEPIN_CUSTOM:
#if defined(BRS_ENABLE_PORT)
        BrsHwPort_SetLevel(BRSHW_PORT_TOGGLE_CUSTOM, BrsMain_ToggleSwitch_CUSTOM & 0x01);
#else
        Dio_WriteChannel(BrsHw_DioChannel_ToggleCustomPin, BrsMain_ToggleSwitch_CUSTOM & 0x01);
#endif
        BrsMain_ToggleSwitch_CUSTOM++;
        break;
#endif /*BRS_ENABLE_SUPPORT_TOGGLE_CUSTOM_PIN*/
    default:
        BrsMainExceptionHandler(kBrsIllegalParameter, BRSERROR_MODULE_BRSHW, (uint16)(__LINE__));
        break;
    }
}
#if defined(BRS_ENABLE_FBL_SUPPORT)
#define BRS_STOP_SEC_RAM_CODE
#include "Brs_MemMap.h"
#endif
#endif /*BRS_ENABLE_SUPPORT_LEDS||BRS_ENABLE_SUPPORT_TOGGLE_WD_PIN||BRS_ENABLE_SUPPORT_TOGGLE_CUSTOM_PIN*/

#if defined(BRS_ENABLE_FBL_SUPPORT)
#define BRS_START_SEC_RAM_CODE
#include "Brs_MemMap.h"
#endif
/*****************************************************************************/
/**
 * @brief      This is the central exeption handler of BRS.
 *             All BRS modules and all CANbedded specific fatal error handler will
 *             call this API in case an assertion has failed.
 * @pre        -
 * @param[in]  ErrorCode shall hold the caller specific error code (uint8)
 * @param[in]  ModuleCode shall describe the caller; please use the CI constant as parameter (uint8)
 * @param[in]  LineNumber shall be the line where the assertion has failed, or,
 *             if not available on caller location, the line where this API is
 *             called from
 * @param[out] -
 * @return     -
 * @context    This function implements an endless loop with locked interrupts.
 *             The recommendation is to set a breakpoint on top of this function
 *             to see if any assertion has failed during the code execution.
 *             Due to an assertion has failed and the endless loop runs with
 *             locked global interrupts there will be no life after the call ...
 */
/*****************************************************************************/
void BrsMainExceptionHandler(uint8 ErrorCode, uint8 ModuleCode, uint16 LineNumber)
{
#if defined(BRSMAIN_BRSMAINEXCEPTIONHANDLER_CALLOUT)
    BrsTestsuite_BrsMain_BrsMainExceptionHandler();
#endif

#if defined(BRS_ENABLE_SAFECTXSUPPORT)
    while (1)
    {
    }

#else
#if defined(_MICROSOFT_C_VTT_)
    char error[BRSMAIN_ERRORBUFFERSIZE];

    sprintf_s(
        error,
        BRSMAIN_ERRORBUFFERSIZE,
        "BrsMainExceptionHandler Code: [0x%x] ModuleCode: [0x%x] LineNumber: [0x%x]",
        ErrorCode,
        ModuleCode,
        LineNumber);

    CANoeAPI_WriteString(error);
    ShutdownOS(0);

#else
    volatile uint8 BrsMain_Continue;
    BrsMain_Continue = 0;

#if !defined(BRS_ENABLE_FBL_SUPPORT) /* FBL is always running in polling mode with interrupts disabled */
    DisableAllInterrupts();
#endif

    while (BrsMain_Continue == 0)
    {
        /* Set BrsMain_Continue to 1 to continue here.
         *  If the debugger is not able to show the stack properly, this mechanism can be used to find the
         *  source of the exception. */
    }
#endif /*_MICROSOFT_C_VTT_*/
#endif /*BRS_ENABLE_SAFECTXSUPPORT*/
}
#if defined(BRS_ENABLE_FBL_SUPPORT)
#define BRS_STOP_SEC_RAM_CODE
#include "Brs_MemMap.h"
#endif

/*****************************************************************************/
/**
 * @brief      This is just a wrapper to ease calls to
 *             BrsMainExceptionHandler() from assembler StartUpCode.
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    This function will call BrsMainExceptionHandler() with the
 *             error code for reaching illegal assembler in StartUpCpde.
 */
/*****************************************************************************/
void BrsMainExceptionStartup(void)
{
    BrsMainExceptionHandler(kBrsIllegalAssemblerReached, BRSERROR_MODULE_BRSSTARTUP, (uint16)0);
}

/*****************************************************************************/
/**
 * @brief      Main function
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     always 0 as the function is not expected to return
 * @context    Called by the startup code
 */
/*****************************************************************************/
// volatile unsigned int delay_flag = 0xffffff;
extern void StartOS(uint8);

int main(void)
{
#if defined(BRSMAIN_MAIN_CALLOUT)
    BrsTestsuite_BrsMain_main();
#endif

#if defined(BRS_FBL_NO_ECUMINIT)
    FblMain();

#else
#if !defined(BRS_ENABLE_FBL_SUPPORT)
    Os_InitMemory();
    Os_Init();
#endif


#if defined(BRS_ENABLE_HSM_SUPPORT)
    vHsm_Init();
#else
    // scif_init();
    EcuM_Init(); /* never returns */

	// PutStr("  ",1);
	// PutStr("Dummy RTOS Program",1);
	// PutStr("Dummy RTOS Program boot end",1);

    while(1)
	{
		// EcuM_MainFunction();
	}
#endif
 #endif /*else BRS_FBL_NO_ECUMINIT*/

    return 0;
}

/***********************************************************************************************************************
 *  Additional UserCode can be placed here
 **********************************************************************************************************************/

#if defined(BRS_ENABLE_CAN_SUPPORT)
/***********************************************************************************************************************
 * UserDefined ExclusiveArea handling for CAN channels, according to AN-ISC-8-1149_ErrorHook_E_OS_DISABLED_INT.pdf
 **********************************************************************************************************************/
/* Macro is generated in vBrsCfg.h, to support drivers with infix.
   Sample w/o infix: void SchM_Enter_Can_CAN_EXCLUSIVE_AREA_0(void)
                     Can_DisableControllerInterrupts(0u);
   Sample w/ infix:  void SchM_Enter_Can_30_Mcan_CAN_30_MCAN_EXCLUSIVE_AREA_0(void)
                     Can_30_Mcan_DisableControllerInterrupts(0u);*/
#if defined(BRS_ENABLE_FBL_SUPPORT)
#define BRS_START_SEC_RAM_CODE
#include "Brs_MemMap.h"
#endif
BRS_DRVCAN_EXCLUSIVE_AREA_INFIX(Enter, EXCLUSIVE_AREA_0)
{
#if !defined(BRS_ENABLE_FBL_SUPPORT)
    if (++BrsAsrApplCanInterruptLockCtr == 1u)
    {
        BRS_DRVCAN_ControllerInterrupts_INFIX(Disable)(0u);
    }
#endif
}

BRS_DRVCAN_EXCLUSIVE_AREA_INFIX(Exit, EXCLUSIVE_AREA_0)
{
#if !defined(BRS_ENABLE_FBL_SUPPORT)
    if (--BrsAsrApplCanInterruptLockCtr == 0u)
    {
        BRS_DRVCAN_ControllerInterrupts_INFIX(Enable)(0u);
    }
#endif
}

BRS_DRVCAN_EXCLUSIVE_AREA_INFIX(Enter, EXCLUSIVE_AREA_6)
{
#if !defined(BRS_ENABLE_FBL_SUPPORT)
    if (++BrsAsrApplCanInterruptLockCtr == 1u)
    {
        BRS_DRVCAN_ControllerInterrupts_INFIX(Disable)(0u);
    }
#endif
}

BRS_DRVCAN_EXCLUSIVE_AREA_INFIX(Exit, EXCLUSIVE_AREA_6)
{
#if !defined(BRS_ENABLE_FBL_SUPPORT)
    if (--BrsAsrApplCanInterruptLockCtr == 0u)
    {
        BRS_DRVCAN_ControllerInterrupts_INFIX(Enable)(0u);
    }
#endif
}
#if defined(BRS_ENABLE_FBL_SUPPORT)
#define BRS_STOP_SEC_RAM_CODE
#include "Brs_MemMap.h"
#endif
#endif /*BRS_ENABLE_CAN_SUPPORT*/

#if defined(BRS_ENABLE_CAN_SUPPORT)
#if defined(BRS_ENABLE_FBL_SUPPORT)
#define BRS_START_SEC_RAM_CODE
#include "Brs_MemMap.h"
#endif
void SchM_Enter_CanIf_CANIF_EXCLUSIVE_AREA_0(void)
{
#if !defined(BRS_ENABLE_FBL_SUPPORT)
    if (++BrsAsrApplCanInterruptLockCtr == 1u)
    {
        BRS_DRVCAN_ControllerInterrupts_INFIX(Disable)(0u);
    }
#endif
}

void SchM_Exit_CanIf_CANIF_EXCLUSIVE_AREA_0(void)
{
#if !defined(BRS_ENABLE_FBL_SUPPORT)
    if (--BrsAsrApplCanInterruptLockCtr == 0u)
    {
        BRS_DRVCAN_ControllerInterrupts_INFIX(Enable)(0u);
    }
#endif
}
#if defined(BRS_ENABLE_FBL_SUPPORT)
#define BRS_STOP_SEC_RAM_CODE
#include "Brs_MemMap.h"
#endif
#endif /*BRS_ENABLE_CAN_SUPPORT*/

#if defined(BRS_ENABLE_CAN_SUPPORT)
#if defined(BRS_ENABLE_FBL_SUPPORT)
#define BRS_START_SEC_RAM_CODE
#include "Brs_MemMap.h"
#endif
void SchM_Enter_CanSM_CANSM_EXCLUSIVE_AREA_4(void)
{
#if !defined(BRS_ENABLE_FBL_SUPPORT)
    if (++BrsAsrApplCanInterruptLockCtr == 1u)
    {
        BRS_DRVCAN_ControllerInterrupts_INFIX(Disable)(0u);
    }
#endif
}

void SchM_Exit_CanSM_CANSM_EXCLUSIVE_AREA_4(void)
{
#if !defined(BRS_ENABLE_FBL_SUPPORT)
    if (--BrsAsrApplCanInterruptLockCtr == 0u)
    {
        BRS_DRVCAN_ControllerInterrupts_INFIX(Enable)(0u);
    }
#endif
}

void SchM_Enter_CanSM_CANSM_EXCLUSIVE_AREA_1(void)
{
    /* Protects against task interruptions -> no locking needed, because all main functions are on same task */
}

void SchM_Exit_CanSM_CANSM_EXCLUSIVE_AREA_1(void)
{
    /* Protects against task interruptions -> no locking needed, because all main functions are on same task */
}
#if defined(BRS_ENABLE_FBL_SUPPORT)
#define BRS_STOP_SEC_RAM_CODE
#include "Brs_MemMap.h"
#endif
#endif /*BRS_ENABLE_CAN_SUPPORT*/

#if defined(BRS_ENABLE_FBL_SUPPORT)
#define BRS_START_SEC_RAM_CODE
#include "Brs_MemMap.h"
#endif
void SchM_Enter_ComM_COMM_EXCLUSIVE_AREA_1(void)
{
    /* Protects against task interruptions -> no locking needed, because all main functions are on same task */
}

void SchM_Exit_ComM_COMM_EXCLUSIVE_AREA_1(void)
{
    /* Protects against task interruptions -> no locking needed, because all main functions are on same task */
}
#if defined(BRS_ENABLE_FBL_SUPPORT)
#define BRS_STOP_SEC_RAM_CODE
#include "Brs_MemMap.h"
#endif
