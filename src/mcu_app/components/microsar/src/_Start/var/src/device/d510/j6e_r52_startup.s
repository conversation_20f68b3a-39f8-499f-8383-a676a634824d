/* derived from target\hobot\freertos & hobot_lite */
/* use arm mode */
#define CR_START_ADDRESS                   0x0C800000UL
#define SOC_TYPE_SYSCNT_CLK                40000000UL   /* according to Os_Counter_Lcfg.c */

    .global Start
    .global EL2_Vectors
    .global OsCfg_Hal_Core_OsCore0_ExceptionTable
    .global OsCfg_Hal_Core_OsCore1_ExceptionTable
/* Handlers */
    .global EL1_Reset_Handler
    .global EL1_Undefined_Handler
    .global EL1_Svc_Handler
    .global EL1_Prefetch_Handler
    .global EL1_Abort_Handler
    .global EL1_DefaultISR
    .global Os_Hal_IrqInterruptEntry_GIC_Mcu_Core0
    .global Os_Hal_IrqInterruptEntry_GIC_Mcu_Core1
    .global EL1_FIQ_Handler

    .section .EL1_core_exceptions_table, "ax"
    .align 5
Start:
    .arm
EL2_Vectors:
  b   EL2_Reset_Handler
  b   EL1_Undefined_Handler
  b   handler_hvc
  b   EL1_Prefetch_Handler
  b   EL1_Abort_Handler
  b   handler_hvt
  b   EL1_DefaultISR
  b   EL1_DefaultISR
OsCfg_Hal_Core_OsCore0_ExceptionTable:
    b   EL1_Reset_Handler                       /* Reset Handler */
    b   EL1_Undefined_Handler                   /* Undefined Handler */
    b   EL1_Svc_Handler                         /* SVC Handler */
    b   EL1_Prefetch_Handler                    /* Prefetch Handler */
    b   EL1_Abort_Handler                       /* Abort Handler */
    b   EL1_DefaultISR                          /* Reserved */
    b   Os_Hal_IrqInterruptEntry_GIC_Mcu_Core0  /* IRQ Handler */
    b   Os_Hal_IrqInterruptEntry_GIC_Mcu_Core0_FIQ                         /* FIQ Handler */

OsCfg_Hal_Core_OsCore1_ExceptionTable:
    b   EL1_Reset_Handler                       /* Reset Handler */
    b   EL1_Undefined_Handler                   /* Undefined Handler */
    b   EL1_Svc_Handler                         /* SVC Handler */
    b   EL1_Prefetch_Handler                    /* Prefetch Handler */
    b   EL1_Abort_Handler                       /* Abort Handler */
    b   EL1_DefaultISR                          /* Reserved */
    b   Os_Hal_IrqInterruptEntry_GIC_Mcu_Core1  /* IRQ Handler */
    b   Os_Hal_IrqInterruptEntry_GIC_Mcu_Core1_FIQ                          /* FIQ Handler */

    /* .endf    Start */


/*  Reset Handler */  
    .text
    .align 5
    .section .EL2_Reset_Handler, "ax"
    .globl  EL2_Reset_Handler
    /* .type   EL2_Reset_Handler, $function */
EL2_Reset_Handler:
    mov r0, #0
    mov r1, r0
    mov r2, r0
    mov r3, r0
    mov r4, r0
    mov r5, r0
    mov r6, r0
    mov r7, r0
    mov r8, r0
    mov r9, r0
    mov r10, r0
    mov r11, r0
    mov r12, r0
    mov r13, r0
    mov r14, r0

    /* enable peripheral port at EL2/EL1/EL0 */
    ldr r0, =0x23000003
    MCR p15, 0, r0, c15, c0, 0

    /* enable aborts */
    cpsie a

MPU_Init:
    mrc p15, 4, r0, c1, c0, 0 /* Read HSCTLR */
    and r0, r0, #0xFFFFFFFE /* set HSCTLR.M bit to 0, disable EL2 MPU */
    mcr p15, 4, r0, c1, c0, 0 /* write HSCTLR */

    mrc p15, 0, r0, c1, c0, 0 /* Read SCTLR */
    and r0, r0, #0xFFFFFFFE /* set SCTLR.M bit to 0, disable EL1 MPU */
    mcr p15, 0, r0, c1, c0, 0 /* write SCTLR */

    mrc p15, 0, r0, c1, c0, 0
    orr r0, r0, #(0x1 << 12)    /*icache */
    orr r0, r0, #(0x1 << 2)     /*dcache */
    mcr p15, 0, r0, c1, c0, 0
disalbe_mpu_region_loop:
    mov r0, r3  /* select region */
    mcr p15, 4, r0, c6, c2, 1 /* Write HPRSELR */
    mcr p15, 0, r0, c6, c2, 1 /* Write PRSELR */

    mrc p15, 4, r0, c6, c3, 1 /* Read From HPRLAR */
    and r0, r0, #0xFFFFFFFE
    mcr p15, 4, r0, c6, c3, 1 /* Write HPRLAR */

    mrc p15, 0, r0, c6, c3, 1 /* Read From PRLAR */
    and r0, r0, #0xFFFFFFFE
    mcr p15, 0, r0, c6, c3, 1 /* Write PRLAR */

    add r3, r3, #1
    cmp r3, #24             /* mcu has only 24 mpu regions */
    blt disalbe_mpu_region_loop

    /* region 0 cluster0/cluster1 tcm */
    /* normal memory attribute        */
    ldr r0, =0                /* Region 0 */
    mcr p15, 4, r0, c6, c2, 1 /* Write HPRSELR */
    mcr p15, 0, r0, c6, c2, 1 /* Write PRSELR */

    ldr r0, =0x08000000       /* Start address */
    orr r0, r0, #0x2          /* SH=0, AP=1, XN=0*/
    mcr p15, 4, r0, c6, c3, 0 /* Write HPRBAR */
    mcr p15, 0, r0, c6, c3, 0 /* Write PRBAR */

    ldr r0, =0x0AFFFFFF      /* End address */
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x3          /* AttrIndex=1, non-cacheable, enable region */
    mcr p15, 4, r0, c6, c3, 1 /* Write HPRLAR */
    mcr p15, 0, r0, c6, c3, 1 /* Write PRLAR */

    /*---------------region 1 mcu sram SRAM_STARTUP-------*/
    /* normal memory attribute */
    ldr r0, =1                /* Region 1 */
    mcr p15, 4, r0, c6, c2, 1 /* Write HPRSELR */
    mcr p15, 0, r0, c6, c2, 1 /* Write PRSELR */

    ldr r0, =0x0C800000       /* Start address */
    orr r0, r0, #0x6          /* SH=0, AP=1, XN=0, R/W: 0x2*/
                              /* SH=0, AP=3, XN=0, RO:  0x6*/
    mcr p15, 4, r0, c6, c3, 0 /* Write HPRBAR */
    mcr p15, 0, r0, c6, c3, 0 /* Write PRBAR */

    ldr r0, =__SBIST_START    /* End address */
    sub r0, r0, #0x40         /* Align it 64 bit*/
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x1          /* AttrIndex=0, cacheable, enable region */
    mcr p15, 4, r0, c6, c3, 1 /* Write HPRLAR */
    mcr p15, 0, r0, c6, c3, 1 /* Write PRLAR */

    /*---------------region 2 mcu sram SBIST--------*/
    /* normal memory attribute */
    ldr r0, =2                /* Region 2 */
    mcr p15, 4, r0, c6, c2, 1 /* Write HPRSELR */
    mcr p15, 0, r0, c6, c2, 1 /* Write PRSELR */

    ldr r0, =__SBIST_START    /* Start address */
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x2          /* SH=0, AP=1, XN=0*/
    mcr p15, 4, r0, c6, c3, 0 /* Write HPRBAR */
    mcr p15, 0, r0, c6, c3, 0 /* Write PRBAR */

    ldr r0, =__SBIST_END      /* End address */
    sub r0, r0, #0x1
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x1          /* AttrIndex=0, cacheable, enable region */
    mcr p15, 4, r0, c6, c3, 1 /* Write HPRLAR */
    mcr p15, 0, r0, c6, c3, 1 /* Write PRLAR */

    /*---------------region 3 mcu sram SRAM_Common----------*/
    /* normal memory attribute */
    ldr r0, =3                /* Region 3 */
    mcr p15, 4, r0, c6, c2, 1 /* Write HPRSELR */
    mcr p15, 0, r0, c6, c2, 1 /* Write PRSELR */

    ldr r0, =_Startup_Code_START       /* Start address */
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x2          /* SH=0, AP=1, XN=0*/
    mcr p15, 4, r0, c6, c3, 0 /* Write HPRBAR */
    mcr p15, 0, r0, c6, c3, 0 /* Write PRBAR */

    ldr r0, =__USER_INFO_START__-1       /* End address */
    sub r0, r0, #0x1
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x1          /* AttrIndex=0, cacheable, enable region */
    mcr p15, 4, r0, c6, c3, 1 /* Write HPRLAR */
    mcr p15, 0, r0, c6, c3, 1 /* Write PRLAR */

    /*---------------region 4 mcu sram SRAM_NonCache-------*/
    /* normal memory attribute */
    ldr r0, =4                /* Region 4 */
    mcr p15, 4, r0, c6, c2, 1 /* Write HPRSELR */
    mcr p15, 0, r0, c6, c2, 1 /* Write PRSELR */

    ldr r0, =__USER_INFO_START__       /* Start address */
    sub r0, r0, #0x1
    add r0, r0, #0x40         /* Align it 64 bit*/
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x2          /* SH=0, AP=1, XN=0*/
    mcr p15, 4, r0, c6, c3, 0 /* Write HPRBAR */
    mcr p15, 0, r0, c6, c3, 0 /* Write PRBAR */

    ldr r0, =_text_START-1       /* End address */
    sub r0, r0, #0x40         /* Align it 64 bit*/
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x3          /* AttrIndex=1, non-cacheable, enable region */
    mcr p15, 4, r0, c6, c3, 1 /* Write HPRLAR */
    mcr p15, 0, r0, c6, c3, 1 /* Write PRLAR */

    /*---------------region 5 mcu sram SRAM_Cache------*/
    /* normal memory attribute */
    ldr r0, =5                /* Region 5 */
    mcr p15, 4, r0, c6, c2, 1 /* Write HPRSELR */
    mcr p15, 0, r0, c6, c2, 1 /* Write PRSELR */

    ldr r0, =_text_START       /* Start address */
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x2          /* SH=0, AP=1, XN=0*/
    mcr p15, 4, r0, c6, c3, 0 /* Write HPRBAR */
    mcr p15, 0, r0, c6, c3, 0 /* Write PRBAR */

    ldr r0, =__sram_bss_noncache_start-1      /* End address */
    sub r0, r0, #0x1
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x1          /* AttrIndex=0, cacheable, enable region */
    mcr p15, 4, r0, c6, c3, 1 /* Write HPRLAR */
    mcr p15, 0, r0, c6, c3, 1 /* Write PRLAR */

    /*---------------region 6 mcu sram SRAM_Reserve_NonCache-------*/
    /* normal memory attribute */
    ldr r0, =6                /* Region 6 */
    mcr p15, 4, r0, c6, c2, 1 /* Write HPRSELR */
    mcr p15, 0, r0, c6, c2, 1 /* Write PRSELR */

    ldr r0, =__APP_HEAP_Reserved_START__       /* Start address */
    sub r0, r0, #0x1
    add r0, r0, #0x40         /* Align it 64 bit*/
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x2          /* SH=0, AP=1, XN=0*/
    mcr p15, 4, r0, c6, c3, 0 /* Write HPRBAR */
    mcr p15, 0, r0, c6, c3, 0 /* Write PRBAR */

    ldr r0, =0x0CDFFFFF            /* End address */
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x3          /* AttrIndex=1, non-cacheable, enable region */
    mcr p15, 4, r0, c6, c3, 1 /* Write HPRLAR */
    mcr p15, 0, r0, c6, c3, 1 /* Write PRLAR */

    /*---------------region 7 internal gic & peripheral---------------*/
    /* device memory attribute            */
    ldr r0, =7                /* Region 7 */
    mcr p15, 4, r0, c6, c2, 1 /* Write HPRSELR */
    mcr p15, 0, r0, c6, c2, 1 /* Write PRSELR */

    ldr r0, =0x22000000       /* Start address */
    orr r0, r0, #0x13         /* SH=2, AP=1, XN=1*/
    mcr p15, 4, r0, c6, c3, 0 /* Write HPRBAR */
    mcr p15, 0, r0, c6, c3, 0 /* Write PRBAR */

    ldr r0, =0x223FFFFF       /* End address */
    sub r0, r0, #1
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x7          /* AttrIndex=3, device memory, enable region */
    mcr p15, 4, r0, c6, c3, 1 /* Write HPRLAR */
    mcr p15, 0, r0, c6, c3, 1 /* Write PRLAR */

    /*---------------region 8 peripheral---------------*/
    /* device memory attribute */
    ldr r0, =8                /* Region 8 */
    mcr p15, 4, r0, c6, c2, 1 /* Write HPRSELR */
    mcr p15, 0, r0, c6, c2, 1 /* Write PRSELR */

    ldr r0, =0x23000000       /* Start address */
    orr r0, r0, #0x13         /* SH=2, AP=1, XN=1*/
    mcr p15, 4, r0, c6, c3, 0 /* Write HPRBAR */
    mcr p15, 0, r0, c6, c3, 0 /* Write PRBAR */

    ldr r0, =0x25FFFFFF       /* End address */
    sub r0, r0, #1
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x7          /* AttrIndex=3, device memory, enable region */
    mcr p15, 4, r0, c6, c3, 1 /* Write HPRLAR */
    mcr p15, 0, r0, c6, c3, 1 /* Write PRLAR */

   /*---------------region 9 CPUSYS------------------*/
    ldr r0, =9                /* Region 9 */
    mcr p15, 4, r0, c6, c2, 1 /* Write HPRSELR */
    mcr p15, 0, r0, c6, c2, 1 /* Write PRSELR */

    ldr r0, =0x26000000       /* Start address */
    orr r0, r0, #0x12          /* SH=2, AP=1, XN=0*/
    mcr p15, 4, r0, c6, c3, 0 /* Write HPRBAR */
    mcr p15, 0, r0, c6, c3, 0 /* Write PRBAR */

    ldr r0, =0x7FFFFFFF     /* End address */
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x7          /* AttrIndex=3, device memory, enable region */
    mcr p15, 4, r0, c6, c3, 1 /* Write HPRLAR */
    mcr p15, 0, r0, c6, c3, 1 /* Write PRLAR */

    /*---------------region 10 DDR nonCache1-------------------*/
    ldr r0, =10               /* Region 10 */
    mcr p15, 4, r0, c6, c2, 1 /* Write HPRSELR */
    mcr p15, 0, r0, c6, c2, 1 /* Write PRSELR */

    ldr r0, =0x80000000       /* Start address */
    orr r0, r0, #0x2          /* SH=0, AP=1, XN=0*/
    mcr p15, 4, r0, c6, c3, 0 /* Write HPRBAR */
    mcr p15, 0, r0, c6, c3, 0 /* Write PRBAR */

    ldr r0, =_ddrRam_START-1     /* End address */
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x3          /* AttrIndex=1, enable region */
    mcr p15, 4, r0, c6, c3, 1 /* Write HPRLAR */
    mcr p15, 0, r0, c6, c3, 1 /* Write PRLAR */

    /*---------------region 11 xspi-------------------*/
    /* device memory attribute            */
    ldr r0, =11               /* Region 11 */
    mcr p15, 4, r0, c6, c2, 1 /* Write HPRSELR */
    mcr p15, 0, r0, c6, c2, 1 /* Write PRSELR */

    ldr r0, =0x18000000         /* Start address */
    orr r0, r0, #0x13          /* SH=2, AP=1, XN=1*/
    mcr p15, 4, r0, c6, c3, 0 /* Write HPRBAR */
    mcr p15, 0, r0, c6, c3, 0 /* Write PRBAR */

    ldr r0, =0x1FFFFFFF       /* End address */
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x7          /* AttrIndex=3, device memory, enable region */
    mcr p15, 4, r0, c6, c3, 1 /* Write HPRLAR */
    mcr p15, 0, r0, c6, c3, 1 /* Write PRLAR */

    /*---------------region 12 mcu sram BSS_NonCache-------*/
    /* normal memory attribute */
    ldr r0, =12                /* Region 12 */
    mcr p15, 4, r0, c6, c2, 1 /* Write HPRSELR */
    mcr p15, 0, r0, c6, c2, 1 /* Write PRSELR */

    ldr r0, = __sram_bss_noncache_start       /* Start address */
    sub r0, r0, #0x1
    add r0, r0, #0x40         /* Align it 64 bit*/
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x2         /* SH=0, AP=1, XN=0 */
    mcr p15, 4, r0, c6, c3, 0 /* Write HPRBAR */
    mcr p15, 0, r0, c6, c3, 0 /* Write PRBAR */

    ldr r0, =__sram_bss_noncache_end-1       /* End address */
    sub r0, r0, #0x40         /* Align it 64 bit*/
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x3          /* AttrIndex=1, non-cacheable, enable region */
    mcr p15, 4, r0, c6, c3, 1 /* Write HPRLAR */
    mcr p15, 0, r0, c6, c3, 1 /* Write PRLAR */
    
    /*---------------region 13 DDR Cache-------------------*/
    ldr r0, =13               /* Region 13 */
    mcr p15, 4, r0, c6, c2, 1 /* Write HPRSELR */
    mcr p15, 0, r0, c6, c2, 1 /* Write PRSELR */

    ldr r0, =_ddrRam_START       /* Start address */
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x2          /* SH=0, AP=1, XN=0*/
    mcr p15, 4, r0, c6, c3, 0 /* Write HPRBAR */
    mcr p15, 0, r0, c6, c3, 0 /* Write PRBAR */

    ldr r0, =_ddrBssShm_START-1      /* End address */
    sub r0, r0, #0x1
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x1          /* AttrIndex=0, cacheable, enable region */
    mcr p15, 4, r0, c6, c3, 1 /* Write HPRLAR */
    mcr p15, 0, r0, c6, c3, 1 /* Write PRLAR */

    /*---------------region 14 DDR nonCache2-------------------*/
    ldr r0, =14               /* Region 14 */
    mcr p15, 4, r0, c6, c2, 1 /* Write HPRSELR */
    mcr p15, 0, r0, c6, c2, 1 /* Write PRSELR */

    ldr r0, =_ddrBssShm_START       /* Start address */
    orr r0, r0, #0x2          /* SH=0, AP=1, XN=0*/
    mcr p15, 4, r0, c6, c3, 0 /* Write HPRBAR */
    mcr p15, 0, r0, c6, c3, 0 /* Write PRBAR */

    ldr r0, =0xFFFFFFFF     /* End address */
    and r0, r0, #0xFFFFFFC0
    orr r0, r0, #0x3          /* AttrIndex=1, enable region */
    mcr p15, 4, r0, c6, c3, 1 /* Write HPRLAR */
    mcr p15, 0, r0, c6, c3, 1 /* Write PRLAR */

    /*---------------SET MAIR/HMAIR-------------------*/
    ldr r0, =0x44FF            /* attr0(FF): normal memory, write-back non-transient */
                               /* attr1(44): normal memory, non-cacheable */
                               /* attr2~3(00): Device-nGnRnE memory */
    mcr p15, 4, r0, c10, c2, 0 /* Write HMAIR0 */
    mcr p15, 0, r0, c10, c2, 0 /* Write MAIR0 */

    ldr r0, =0x04              /* attr4~7(04/00): Device-nGnRnE memory */
    mcr p15, 4, r0, c10, c2, 1 /* Write HMAIR1 */
    mcr p15, 0, r0, c10, c2, 1 /* Write MAIR1 */
    
mpu_enable:
    mrc p15, 4, r0, c1, c0, 0 /*  Read HSCTLR */  
    orr r0, r0, #0x1          /*  Enable MPU */  
    dsb
    mcr p15, 4, r0, c1, c0, 0 /*  Write HSCTLR */  
    isb

    mrc p15, 0, r0, c1, c0, 0 /*  Read SCTLR */  
    orr r0, r0, #0x1          /*  Enable MPU */  
    dsb
    mcr p15, 0, r0, c1, c0, 0 /*  Write SCTLR */  
    isb

    /*  enable prefetch and speculative access */  
enable_prefetch:
    mrrc p15, 0, r0, r1, c15 /*  Read CPUACTLR */  
    mov r2, #0x01
    bfi r0, r2, #11, #1 /*  L1IPFCTL */  
    mov r2, #0x03
    bfi r0, r2, #13, #3 /*  L1DPFCTL */  
    mov r2, #0x01
    bfi r0, r2, #19, #2 /*  DPFSTRCTL */  
    mcrr p15, 0, r0, r1, c15 /*  Write CPUACTLR */  

    /*  Enable branch prediction at EL2/EL1/EL0 */  
    mrc p15, 1, r0, c9, c1, 1 /*  Read IMP_BPCTLR into Rt */  
    mov r2, #0x00
    bfi r0, r2, #0, #3 /*  set BIT0/1/2 to 0 */  
    mcr p15, 1, r0, c9, c1, 1 /*  Write Rt to IMP_BPCTLR */  


    /*  let EL1 be able to access IMP_BUSTIMEOUTR */  
    mrc p15, 4, r0, c1, c0, 1 /*  Read HACTLR into r0 */  
    orr r0, r0, #0x400        /*  Enable access to IMP_BUSTIMEOUTR at EL1 */  
    mcr p15, 4, r0, c1, c0, 1 /*  Write r0 to HACTLR */  

    /*  set IMP_BUSTIMEOUTR to disable FLASH/LLPP/AXIM interface timeout */  
    mrc p15, 1, r0, c15, c3, 2 /*  Read IMP_BUSTIMEOUTR into r0 */  
    and r0, r0, #0xFFFFFFF8 /*  Set the last 3 bits as 0 */  
    mcr p15,1, r0, c15, c3, 2 /*  Write r0 to IMP_BUSTIMEOUTR */  

    /*  set All mcusys peripherals as secure */  
    /*  this must after MPU init */  
enable_peri_secure:
    ldr r0, =0x2320B180
    ldr r1, =0x15555555
    str r1, [r0]

/*----------------------------------------------------------------
// Enable access to VFP by enabling access to Coprocessors 10 and 11.
// Enables Full Access from EL0 and EL1
// correspoding register name in cortex-r52 is CPACR
//----------------------------------------------------------------*/
    mrc    p15, 0, r0, c1, c0, 2
    orr    r0, r0, #(0x03 << 20)
    orr    r0, r0, #(0x03 << 22)
    mcr    p15, 0, r0, c1, c0, 2
    isb

/*----------------------------------------------------------------
// Switch on the VFP hardware
// this will global enable floating-point support
//----------------------------------------------------------------*/
    mov    r0, #(0x01 << 30)
    vmsr   fpexc, r0

/*----------------------------------------------------------------
// Set R52 generic timer freq register CNTFRQ
// this register only writable in EL2
//----------------------------------------------------------------*/
    ldr r0, =SOC_TYPE_SYSCNT_CLK
    mcr p15, 0, r0, c14, c0, 0

/*  ARMv8-R cores are in EL2 (hypervisor mode) after reset, descend to EL1 (supervisor mode) */  
enter_el1_mode:
    mrs     r0, cpsr    /*  read cpsr */  
    mov r1, #0x13		/* spsr_hyp must at arm mode */
	bfi r0, r1, #0, #5
	msr spsr_hyp, r0

    ldr r0, =CR_START_ADDRESS
    mcr p15, 0, r0, c12, c0, 0

    // location to branch to when leaving hypervisor
    ldr r0, =EL1_Reset_Handler
    msr     elr_hyp, r0

    mov     r0, r13
    dsb
    isb
    eret
    /* .endf   EL2_Reset_Handler */

/*  note this must be 16byte boundray or cpu will run in problem */  
    .text
    .align 5
	.section .EL1_Reset_Handler, "ax"
    .globl  EL1_Reset_Handler
    /* .type   EL1_Reset_Handler, $function */
EL1_Reset_Handler:
    /* mov     r13, r0 */

/* TCM size init */
    ldr r0, =0x0000001F
    mcr p15, 0, r0, c9, c1, 0 /*  write IMP_ATCMREGIONR 64KB */  
    ldr r0, =0x0010001B
    mcr p15, 0, r0, c9, c1, 0 /*  write IMP_BTCMREGIONR 32KB */  
    ldr r0, =0x0020001B
    mcr p15, 0, r0, c9, c1, 0 /*  write IMP_CTCMREGIONR 32KB */  

/* get cluster id and init stack according to it. */
/* cluster id used as core id, for cluster0 and cluster 1 sahll configured as lockstep */   
    /*  MPIDR bit0~7 aff0 core id, bit8~15 aff1 cluster id */  
    mrc p15, 0, r5, c0, c0, 5 /*  read MPIDR into r5 */  
    and r5, r5, #0x0F00
    mov r5, r5, lsr #8
checkcluster:
    cmp r5, #0
    beq stack_svc_init_c0
    cmp r5, #1
    beq stack_svc_init_c1
    b   .       /*  wrong cluster, we stop here */  

stack_svc_init_c0:  
    /* Initialize stack for SVC core0 */
    ldr r0, .svc_stack_c0
    mov sp, r0
    /* Set EL1 Vectors */
    ldr r0, =OsCfg_Hal_Core_OsCore0_ExceptionTable
    mcr p15, 0, r0, c12, c0, 0
    b stack_svc_init_end

stack_svc_init_c1:  
    /* Initialize stack for SVC core1 */
    ldr r0, .svc_stack_c1
    mov sp, r0
    /* Set EL1 Vectors */
    ldr r0, =OsCfg_Hal_Core_OsCore1_ExceptionTable
    mcr p15, 0, r0, c12, c0, 0
    b stack_svc_init_end

stack_svc_init_end:

    /* Disable forwarding interrupt */
    mov r0, #0
    mrc p15, #0, r0, c12, c12, #7

    /* Zero init vlinkgen early groups, whitch are stack areas */
    ldr r1, =vLinkGen_ZeroInit_Early_Groups
startup_area_zero_init_start:
    mov r2, r1
    add r1, r1, #16
    ldr r3, [r2]      /* vLinkGen_ZeroInit_Early_Groups->start */
    ldr r4, [r2, #4]  /* vLinkGen_ZeroInit_Early_Groups->end */
    ldr r0, [r2, #8]  /* vLinkGen_ZeroInit_Early_Groups->core */
    /* Alignment parameter actually not used here */
    /* Verify if the end of struct vLinkGen_ZeroInit_Early_Groups is reached, by checking if start == 0, end == 0 and core == 0 */
    mov r6, #0        /* If InitCore is not running -> go to the next array entry */
    cmp r3, r4        /* If Start and End address are equal -> Finished */
    beq startup_area_zero_init_end
    cmp r5, r0
    bne startup_area_zero_init_start
startup_area_zero_init_loop_start:
    str r6, [r3]      /* must be an aligned memory access! */
    add r3, r3, #4
    cmp r3, r4        /* If Start is same with End address-> Finished. */
    beq startup_area_zero_init_start
    b startup_area_zero_init_loop_start
startup_area_zero_init_end: 
    /* Jump to C code */
    b handler_reset

    /*  Should never get here */  
	b .
    /* .endf   EL1_Reset_Handler */

    .global __svc_stack_c0_end
    .svc_stack_c0: .word __svc_stack_c0_end
    .global __svc_stack_c1_end
    .svc_stack_c1: .word __svc_stack_c1_end
    .global __svc_stack_c2_end
    .svc_stack_c2: .word __svc_stack_c2_end

.end
