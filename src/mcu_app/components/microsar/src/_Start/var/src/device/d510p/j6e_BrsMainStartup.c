/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/
#include "Compiler.h"
#include "Platform_Types.h"
#include "vLinkGen_Lcfg.h"
#include "BrsMain.h"
#include "Os_Hal_Interrupt.h"
#include "device_registers.h"
#include "arm_cr.h"
#include "J6e_features.h"
#include "device_registers.h"
#include "Gic_V3.h"
#include "j6e_timer.h"
// #include "CoreDump/CoreDump.h"

/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/
extern uint32 __und_stack_c0_end;
extern uint32 __abt_stack_c0_end;
extern uint32 __irq_stack_c0_end;
extern uint32 __fiq_stack_c0_end;
extern uint32 __sys_stack_c0_end;

extern uint32 __und_stack_c1_end;
extern uint32 __abt_stack_c1_end;
extern uint32 __irq_stack_c1_end;
extern uint32 __fiq_stack_c1_end;
extern uint32 __sys_stack_c1_end;

extern void EL2_Reset_Handler(void);

/*******************************************************************************
**                      Global Data Types                                     **
*******************************************************************************/
uint32 GulCnt = 100000;

#define BRS_INIT_PATTERN_BLOCKS (0x0UL)
#define BRS_INIT_PATTERN_HARDRESET_BLOCKS (0x0UL)
#define BRS_INIT_PATTERN_AREAS (0x0UL)
#define BRS_INIT_PATTERN_HARDRESET_AREAS (0x0UL)

static GICR_Type * const s_GICRIntBase[NUMBER_OF_CORES] = {GICR_0_INT};

/*******************************************************************************
**                      Exception handlers                                    **
*******************************************************************************/
void System_Init(void);
void INT_SYS_EnableIRQGlobal(void);
void INT_SYS_SetPriority(uint32 irqNumber, uint8_t priority);
void INT_SYS_EnableIRQ(uint32 irqNumber);
void Cluster1_Wakeup(void);
#ifdef CR52_MULTICORE_SUPPORT
void main_dummy(void);
#endif

// extern void os_mcu10_ipcCfg_PreInit();

extern int main(void);

//# pragma SET_CODE_SECTION (".brsMainStartup")
void handler_reset() __attribute__((noreturn));
extern void R52BrsMain_MemoryInit_StageZero_Hook(uint32);
extern void R52BrsMain_MemoryInit_StageOne_Hook(uint32);
extern void R52BrsMain_MemoryInit_StageHardReset_Hook(uint32);
extern void R52BrsMain_MemoryInit_StageTwo_Hook(uint32);
extern void R52BrsMain_MemoryInit_StageThree_Hook(uint32);
extern void R52BrsMain_PreMainHook(uint32);
void Brs_PreMainStartup(void);
void Brs_MemoryZeroInit(const vLinkGen_MemAreaSet *, uint32, uint32);
void Brs_MemoryInit(const vLinkGen_RamMemAreaSet *, uint32);

extern void (*__init_array_start []) (void) __attribute__((weak));
extern void (*__init_array_end []) (void) __attribute__((weak));
extern void InitializeCrossCoreISR(uint8);

void call_constructors() {
    int count = __init_array_end - __init_array_start;
    for (int i = 0; i < count; i++) {
        if (__init_array_start[i]) {
            __init_array_start[i]();
        }
    }
}

volatile static uint32 __attribute__((section(".mcu_shm_noncache"))) main_debug = 1;

/* Reset handler */
void handler_reset()
{
    uint32 ulCoreID;
    ulCoreID = __get_CRCoreID();

    if(CR52_CPU0 == ulCoreID)
    {
        /* Setup stacks */
        __set_mode(CPSR_M_UND);
        __set_SP(&__und_stack_c0_end);
        __set_mode(CPSR_M_ABT);
        __set_SP(&__abt_stack_c0_end);
        __set_mode(CPSR_M_IRQ);
        __set_SP(&__irq_stack_c0_end);
        __set_mode(CPSR_M_FIQ);
        __set_SP(&__fiq_stack_c0_end);
        __set_mode(CPSR_M_SYS);
        __set_SP(&__sys_stack_c0_end);    
    }
    else if(CR52_CPU1 == ulCoreID)
    {
          /* Setup stacks */
        __set_mode(CPSR_M_UND);
        __set_SP(&__und_stack_c1_end);
        __set_mode(CPSR_M_ABT);
        __set_SP(&__abt_stack_c1_end);
        __set_mode(CPSR_M_IRQ);
        __set_SP(&__irq_stack_c1_end);
        __set_mode(CPSR_M_FIQ);
        __set_SP(&__fiq_stack_c1_end);
        __set_mode(CPSR_M_SYS);
        __set_SP(&__sys_stack_c1_end);  
    }
    else
    {
        /* undefined core */
        while (1);
    }
    
    /* Autosar Brs_PreMainStartup, all cores needed */
    Brs_PreMainStartup();

    System_Init();

    #ifdef CR52_MULTICORE_SUPPORT
    if (CR52_CPU0 == ulCoreID)
    #endif
    {
        /*Start other cores*/
        Cluster1_Wakeup();
		

        /* Simple loop to avoid conflict when using serial port */
        while (GulCnt--)
        {
          __NOP();
        }

		call_constructors();

        // while(main_debug);
        /* Jump to main */
        main();
    }


    #ifdef CR52_CPU1_USED
    if (CR52_CPU1 == ulCoreID)
    { 
        /* Jump to main */
        main();
    }
    #endif

    /* Never exit */
    while (1);
}

#ifdef CR52_MULTICORE_SUPPORT
/* Dummy main function for core other than 0 */
void main_dummy(void)
{
    while (1);
}
#endif
		

void __attribute__((interrupt("FIQ"))) EL1_FIQ_Handler(void);
void EL1_FIQ_Handler()
{
  uint32 intid = GIC_AcknowledgePending0();

  if (intid == 30)
  {
	Os_Hal_INTC_ClearPendingFiq(30);
  }
  else
  {
	;
  }
}

void EL1_Svc_Handler()
{
	// Svc_CoreDumpSave();
    while (1);
}

void EL1_Prefetch_Handler()
{
	// PreAbt_CoreDumpSave();
    while (1);
}

void EL1_Abort_Handler()
{
	// DataAbt_CoreDumpSave();
    while (1);
}

void EL1_Undefined_Handler()
{
	// Und_CoreDumpSave();
    while (1);
}

void EL1_DefaultISR()
{
    while (1);
}

void handler_hvc()
{
    while (1);
}

void handler_hvt()
{
    while (1);
}

void Brs_PreMainStartup(void)
{
    uint32 coreID;

    coreID = __get_CRCoreID();

    R52BrsMain_MemoryInit_StageZero_Hook(coreID);

#if (VLINKGEN_CFG_NUM_ZERO_INIT_ZERO_GROUPS>1uL)
  /* vLinkGen_ZeroInit_Zero_GroupsSet contains vLinkGen VarSectionGroups, configured with Init Policy "ZERO_INIT" and Init Stage "ZERO" */
    Brs_MemoryZeroInit(&vLinkGen_ZeroInit_Zero_GroupsSet, BRS_INIT_PATTERN_AREAS, coreID);
#endif

#if (VLINKGEN_CFG_NUM_INIT_ZERO_GROUPS>1uL)
  /* vLinkGen_Init_Zero_GroupsSet contains vLinkGen VarSectionGroups, configured with Init Policy "INIT" and Init Stage "ZERO" */
    Brs_MemoryInit(&vLinkGen_Init_Zero_GroupsSet, coreID);
#endif

    R52BrsMain_MemoryInit_StageOne_Hook(coreID);

#if (VLINKGEN_CFG_NUM_ZERO_INIT_ONE_BLOCKS>1uL)
  /* vLinkGen_ZeroInit_One_BlocksSet contains vLinkGen memory region blocks, configured with Init Stage "ONE" */
    Brs_MemoryZeroInit(&vLinkGen_ZeroInit_One_BlocksSet, BRS_INIT_PATTERN_BLOCKS, coreID);
#endif

#if (VLINKGEN_CFG_NUM_ZERO_INIT_ONE_GROUPS>1uL)
  /* vLinkGen_ZeroInit_One_GroupsSet contains vLinkGen VarSectionGroups, configured with Init Policy "ZERO_INIT" and Init Stage "ONE" */
    Brs_MemoryZeroInit(&vLinkGen_ZeroInit_One_GroupsSet, BRS_INIT_PATTERN_AREAS, coreID);
#endif

#if (VLINKGEN_CFG_NUM_INIT_ONE_GROUPS>1uL)
  /* vLinkGen_Init_One_GroupsSet contains vLinkGen VarSectionGroups, configured with Init Policy "INIT" and Init Stage "ONE" */
    Brs_MemoryInit(&vLinkGen_Init_One_GroupsSet, coreID);
#endif

    R52BrsMain_MemoryInit_StageHardReset_Hook(coreID);

    R52BrsMain_MemoryInit_StageTwo_Hook(coreID);

#if (VLINKGEN_CFG_NUM_ZERO_INIT_TWO_GROUPS>1uL)
    /* vLinkGen_ZeroInit_Two_GroupsSet contains vLinkGen VarSectionGroups, configured with Init Policy "ZERO_INIT" and Init Stage "TWO" */
    Brs_MemoryZeroInit(&vLinkGen_ZeroInit_Two_GroupsSet, BRS_INIT_PATTERN_AREAS, coreID);
#endif

#if (VLINKGEN_CFG_NUM_INIT_TWO_GROUPS>1uL)
    /* vLinkGen_Init_Two_GroupsSet contains vLinkGen VarSectionGroups, configured with Init Policy "INIT" and Init Stage "TWO" */
    Brs_MemoryInit(&vLinkGen_Init_Two_GroupsSet, coreID);
#endif

    R52BrsMain_MemoryInit_StageThree_Hook(coreID);

#if (VLINKGEN_CFG_NUM_ZERO_INIT_THREE_GROUPS>1uL)
    /* vLinkGen_ZeroInit_Three_GroupsSet contains vLinkGen VarSectionGroups, configured with Init Policy "ZERO_INIT" and Init Stage "THREE" */
    Brs_MemoryZeroInit(&vLinkGen_ZeroInit_Three_GroupsSet, BRS_INIT_PATTERN_AREAS, coreID);
#endif

#if (VLINKGEN_CFG_NUM_INIT_THREE_GROUPS>1uL)
    /* vLinkGen_Init_Three_GroupsSet contains vLinkGen VarSectionGroups, configured with Init Policy "INIT" and Init Stage "THREE" */
    Brs_MemoryInit(&vLinkGen_Init_Three_GroupsSet, coreID);
#endif

    R52BrsMain_PreMainHook(coreID);

    /* call C++ constructor */
}


/*****************************************************************************/
/**
 * @brief      Generic routine for RAM zeroing.
 * @pre        -
 * @param[in]  memAreaSet shall point to the first element of a vLinkGen_MemAreaSet struct array
 * @param[in]  InitPattern describes the pattern, to be used for the memory initialization
 * @param[in]  coreId shall describe the ID of the current CPU
 * @param[out] -
 * @return     -
 * @context    Function is called from Brs_PreMainStartup() to initialize memory ares,
 *             generated out of vLinkGen configuration
 */
/*****************************************************************************/
void Brs_MemoryZeroInit(const vLinkGen_MemAreaSet *memAreaSet, uint32 InitPattern, uint32 coreId)
{
    uint8 i;
#if !defined (BRSHW_ASM_MEMORY_ZERO_INIT_LOOP_AVAILABLE)
    volatile uint32 *memPtr;
#endif

    for (i=0; i<memAreaSet->Num; i++)
    {
        if (memAreaSet->Areas[i].Core==coreId && (memAreaSet->Areas[i].End - memAreaSet->Areas[i].Start) > 0)
        {
#if defined (BRSHW_ASM_MEMORY_ZERO_INIT_LOOP_AVAILABLE)
            BrsHw_AsmMemoryZeroInitLoop(memAreaSet->Areas[i].Start, memAreaSet->Areas[i].End, InitPattern);

#else
            memPtr = (volatile uint32*)memAreaSet->Areas[i].Start;
            while ((uint32)memPtr < memAreaSet->Areas[i].End)
            {
                *memPtr = InitPattern;
                memPtr++;
            }
#endif /*else BRSHW_ASM_MEMORY_ZERO_INIT_LOOP_AVAILABLE*/
        }
    }
}

/*****************************************************************************/
/**
 * @brief      Generic routine for ROM to RAM initialization.
 * @pre        -
 * @param[in]  memAreasSet shall point to the first element of a vLinkGen_RamMemAreaSet struct array
 * @param[in]  coreId shall describe the ID of the current CPU
 * @param[out] -
 * @return     -
 * @context    Function is called from Brs_PreMainStartup() to initialize memory ares,
 *             generated out of vLinkGen configuration
 */
/*****************************************************************************/
void Brs_MemoryInit(const vLinkGen_RamMemAreaSet *memAreasSet, uint32 coreId)
{
    volatile uint32 *memPtr;
    volatile uint32 *romPtr;
    uint8 i;

    for (i=0; i<memAreasSet->Num; i++)
    {
        if (memAreasSet->Areas[i].Core==coreId && ((memAreasSet->Areas[i].End - memAreasSet->Areas[i].Start) > 0))
        {
            /* run size might smaller than load size, because the trampoline code section will be added to the load area in link time.
            these trampolines should also be copied to the run area. */
            if ((memAreasSet->Areas[i].End - memAreasSet->Areas[i].Start) > (memAreasSet->Areas[i].Romend - memAreasSet->Areas[i].Romstart))
            {
                /* Defined size in rom does not match ram size */
#ifndef ZX_IDC_SOC_HOBOT_J6E
                BrsMainExceptionHandler(kBrsIllegalParameter, BRSERROR_MODULE_BRSMAINSTARTUP, (uint16)(__LINE__));
#else
                BrsMainExceptionHandler(kBrsIllegalParameter, BRSERROR_MODULE_BRSMAINSTARTUP, (uint16)(0xffffu));
#endif
            }

            memPtr = (volatile uint32*)memAreasSet->Areas[i].Start;
            romPtr = (volatile uint32*)memAreasSet->Areas[i].Romstart;
            /* trampolines should also be copied to the run area. */
            while ((uint32)romPtr < memAreasSet->Areas[i].Romend)
            {
                *memPtr = *romPtr;
                memPtr++;
                romPtr++;
            }
        }
    }
}

/*FUNCTION**********************************************************************
 *
 * Function Name : SystemInit
 * Description   : Typically this function enables FPU,
 * initializes interrupt controller for current core and disable WDOG.
 * SystemInit is called from startup_device file.
 *
 * Implements    : SystemInit_Activity
 *END**************************************************************************/
void System_Init(void) 
{
    /* NUMBER_OF_CORES SHOULD BE 1, the 2 cores in a cluster work as lockstep */
    static GICR_Type * const s_GICRCtrlBase[NUMBER_OF_CORES] = {GICR_0_CTRL};
    uint8_t core = 0;
    GICR_Type * base;
    /* Enable the interrupts */
    /* Enable group 0 and group 1 interrupt routing */
    GICD->CTLR |= (uint32_t) (GICD_CTLR_EG0(1U) | GICD_CTLR_EG1(1U));
    /* Poll until the register has been written */
    while ((GICD->CTLR & GICD_CTLR_RWP_MASK) != 0x0U) {}

    /* Initialize the GIC Redistributor for all cores */
    for (core = 0; core < NUMBER_OF_CORES; core++)
    {
        base = s_GICRCtrlBase[core];
        /* Clear the ProcessorSleep bitfield */
        base->WAKER &= (uint32_t) (~(GICR_WAKER_PS(1U)));
        /* Poll until the processor has been awaken */
        while ((base->WAKER & GICR_WAKER_CA_MASK) != 0x0U) {}
    }
    /* Enable DiPort signal on AE subsystem */
}

/*FUNCTION**********************************************************************
 *
 * Function Name : INT_SYS_EnableIRQGlobal
 * Description   : Enable system interrupt
 * This function will enable the global interrupt by calling the core API
 * Implements INT_SYS_EnableIRQGlobal_Activity
 *
 *END**************************************************************************/
void INT_SYS_EnableIRQGlobal(void)
{
    /* Enable the GIC Distributor */
    /* Enable group 0 and group 1 interrupt routing */
    GICD->CTLR |= (uint32_t) (GICD_CTLR_EG0(1U) | GICD_CTLR_EG1(1U));

    /* Poll until the register has been written */
    while ((GICD->CTLR & GICD_CTLR_RWP_MASK) != 0x0U) {}
}

/*FUNCTION**********************************************************************
 *
 * Function Name : INT_SYS_SetPriority
 * Description   : Set the priority of an interrupt
 * This function will set the priority of an interrupt.
 * Note: The priority cannot be set for every core interrupt.
 * Implements INT_SYS_SetPriority_Activity
 *
 *END**************************************************************************/
void INT_SYS_SetPriority(uint32 irqNumber, uint8_t priority)
{
    /* Check IRQ number - dev_irqNumber is used to avoid compiler warning */
    int32_t dev_irqNumber = (int32_t)irqNumber;
    /* core id is always 0, for we use lockstep in both cluster */
    uint8_t current_core = (uint8_t)0;
    GICR_Type * GicrIntBase;

    uint8_t shift = (uint8_t) (8U - FEATURE_INTERRUPT_GIC_PRIO_BITS);

    if (dev_irqNumber <= (int32_t)FEATURE_INTERRUPT_CORE_IRQ_MAX)
    {
        GicrIntBase = s_GICRIntBase[current_core];
        /* Set Priority for SGI (software) or PPI (core) interrupts */
        GicrIntBase->IPRIORITYR[(uint32_t)(dev_irqNumber)] = (uint8_t)(((((uint32_t)priority) << shift)) & 0xFFUL);
    }
    else
    {
        /* Set Priority for Shared Peripheral Interrupts */
        GICD->IPRIORITYR[(uint32_t)(dev_irqNumber)] = (uint8_t)(((((uint32_t)priority) << shift)) & 0xFFUL);
    }
}

/*FUNCTION**********************************************************************
 *
 * Function Name : INT_SYS_EnableIRQ
 * Description   : Enables an interrupt for a given IRQ number.
 * It calls the system GIC API to access the interrupt control
 * register. The input IRQ number does include the SGI
 * (software generated interrupts) and PPI (private peripheral
 * interrupts), from 0 to a maximum supported IRQ.
 * Implements INT_SYS_EnableIRQ_Activity
 *END**************************************************************************/
void INT_SYS_EnableIRQ(uint32 irqNumber)
{
    /* Check IRQ number - dev_irqNumber is used to avoid compiler warning */
    int32_t dev_irqNumber = (int32_t)irqNumber;
    /* core id is always 0, for we use lockstep in both cluster */
    uint8_t current_core = (uint8_t)0;
    GICR_Type * GicrIntBase;

    if (dev_irqNumber <= (int32_t)FEATURE_INTERRUPT_CORE_IRQ_MAX)
    {
        GicrIntBase = s_GICRIntBase[current_core];

        /* Set interrupt in group 1 (IRQ) */
        GicrIntBase->IGROUPR[0] |= (uint32_t)(1UL << ((uint32_t)(dev_irqNumber)));

        /* Enable SGI (software) or PPI (core) interrupt */
        GicrIntBase->ISENABLER[0] = (uint32_t)(1UL << ((uint32_t)(dev_irqNumber)));
    }
    else
    {
        /* Set destination to current core */
        // GICD->IROUTER[(uint32_t)(dev_irqNumber)] |= current_core;

        /* Set interrupt in group 1 (IRQ) */
        GICD->IGROUPR[(uint32_t)(dev_irqNumber) >> 5U] |= (uint32_t)(1UL << ((uint32_t)(dev_irqNumber) & (uint32_t)0x1FU));

        /* Enable SPI (peripheral) interrupt */
        GICD->ISENABLER[(uint32_t)(dev_irqNumber) >> 5U] = (uint32_t)(1UL << ((uint32_t)(dev_irqNumber) & (uint32_t)0x1FU));
    }
}

void Cluster1_Wakeup(void)
{
    uint32 i = 0;
    uint32 flag = 0;
    // cfg cluster1 as lock-step, change bit6 to 0
    *(volatile uint32 *)(0x230f0078) &= ~(0x00000040);
    // we set slave core's vectortable, this time only one core in cluster1
    // cluster1 core0
    *(volatile uint32 *)(0x230f0070) = (uint32)&EL2_Reset_Handler;
    __asm("DSB");
    __asm("ISB");
    // enable cluster1 clk ,set cmg_cluster1_en bit, bit15=1
    i = *(volatile uint32 *)(0x23100020);
    i = i | 0x8000; // set bit15=1
    *(volatile uint32 *)(0x23100020) = i;
    __asm("DSB");
    __asm("ISB");
    // wakeup req en, bit7
    // cluster1 core0
    *(volatile uint32 *)(0x22241374) |= 0x80;
    __asm("DSB");
    __asm("ISB");
    while (i < 10000) i++;
    // wakeup req, bit0
    // cluster1 core0
    *(volatile uint32 *)(0x22241374) |= 0x01;
    __asm("DSB");
    __asm("ISB");
    // check power status,
    // bit7 for cluster1 core0 run status
    i = *(volatile uint32 *)(0x22241384);
    // check cluster1 core0 in runmode before change clk to 1.2G
    while (flag == 0) {
    if ((i & 0x0080) == 0x0080) {
            // cluster1 core0 in run mode
            flag = 1;
    }
    i = *(volatile uint32 *)(0x22241384);
    }
    // relase slave cores halt
    // cluster1 core0 will run code
    *(volatile uint32 *)(0x230f0078) &= ~(0x02);
    __asm("DSB");
    __asm("ISB");
    // change cpu clk to 1.2G
    // set bit0=1, bit4=0
    i = *(volatile uint32 *)(0x23100020);
    i = i | 0x01; // bit0=1
    i = i & 0xFFFFFFEF; // bit4=0
    *(volatile uint32 *)(0x23100020) = i;
    __asm("DSB");
    __asm("ISB");
}
// #pragma section .brsMainStartup end

/*******************************************************************************
**                      End of File                                           **
*******************************************************************************/
