/*============================================================================*/
/* Project      = AUTOSAR Renesas X2x, R-Car S4 MCAL Components               */
/* File name    = R52BrsMainStartup.c                                         */
/* SW-VERSION   = x.x.x                                                       */
/* Date         = 23/03/2023                                                  */
/*============================================================================*/
/*                                  COPYRIGHT                                 */
/*============================================================================*/
/* (c) 2021 Renesas Electronics Corporation. All rights reserved.             */
/*============================================================================*/
/* Purpose:                                                                   */
/* Startup code for R-Car devices                                             */
/*                                                                            */
/*============================================================================*/
/*                                                                            */
/* Unless otherwise agreed upon in writing between your company and           */
/* Renesas Electronics Corporation the following shall apply!                 */
/*                                                                            */
/* Warranty Disclaimer                                                        */
/*                                                                            */
/* There is no warranty of any kind whatsoever granted by Renesas. Any        */
/* warranty is expressly disclaimed and excluded by Renesas, either expressed */
/* or implied, including but not limited to those for non-infringement of     */
/* intellectual property, merchantability and/or fitness for the particular   */
/* purpose.                                                                   */
/*                                                                            */
/* Renesas shall not have any obligation to maintain, service or provide bug  */
/* fixes for the supplied Product(s) and/or the Application.                  */
/*                                                                            */
/* Each User is solely responsible for determining the appropriateness of     */
/* using the Product(s) and assumes all risks associated with its exercise    */
/* of rights under this Agreement, including, but not limited to the risks    */
/* and costs of program errors, compliance with applicable laws, damage to    */
/* or loss of data, programs or equipment, and unavailability or              */
/* interruption of operations.                                                */
/*                                                                            */
/* Limitation of Liability                                                    */
/*                                                                            */
/* In no event shall Renesas be liable to the User for any incidental,        */
/* consequential, indirect, or punitive damage (including but not limited     */
/* to lost profits) regardless of whether such liability is based on breach   */
/* of contract, tort, strict liability, breach of warranties, failure of      */
/* essential purpose or otherwise and even if advised of the possibility of   */
/* such damages. Renesas shall not be liable for any services or products     */
/* provided by third party vendors, developers or consultants identified or   */
/* referred to the User by Renesas in connection with the Product(s) and/or   */
/* the Application.                                                           */
/*                                                                            */
/*============================================================================*/
/* Environment:                                                               */
/*              Devices:        R-Car                                         */
/*============================================================================*/

/*******************************************************************************
**                      Revision Control History                              **
*******************************************************************************/
/*
 * x.x.x      23-Mar-2023     yian: Stitch somthing else...
 * 1.0.1      24-Dec-2021     Support multi cores CR52
 * 1.0.0      20-Aug-2021     Initial Version
 */

/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/
#include "arm_cr.h"
#include "utils.h"
#include "vLinkGen_Lcfg.h"
#include "BrsMain.h"
#include "Os_Hal_Interrupt.h"
#include "CoreDump/CoreDump.h"

/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/
extern uint32 __und_stack_c0_end;
extern uint32 __abt_stack_c0_end;
extern uint32 __irq_stack_c0_end;
extern uint32 __fiq_stack_c0_end;
extern uint32 __sys_stack_c0_end;

extern uint32 __und_stack_c1_end;
extern uint32 __abt_stack_c1_end;
extern uint32 __irq_stack_c1_end;
extern uint32 __fiq_stack_c1_end;
extern uint32 __sys_stack_c1_end;

extern uint32 __und_stack_c2_end;
extern uint32 __abt_stack_c2_end;
extern uint32 __irq_stack_c2_end;
extern uint32 __fiq_stack_c2_end;
extern uint32 __sys_stack_c2_end;

extern uint32 EL1_Vectors;

/*******************************************************************************
**                      Global Data Types                                     **
*******************************************************************************/
uint32 GulCnt = 100000;
uint32 GulCnt1 = 100000;
uint32 GulCnt2 = 100000;

#define BRS_INIT_PATTERN_BLOCKS (0x0UL)
#define BRS_INIT_PATTERN_HARDRESET_BLOCKS (0x0UL)
#define BRS_INIT_PATTERN_AREAS (0x0UL)
#define BRS_INIT_PATTERN_HARDRESET_AREAS (0x0UL)

uint32 g_DataDummy4Bin __attribute__((section(".data_bin_dummy"))) = 0xA5;

/*******************************************************************************
**                      Exception handlers                                    **
*******************************************************************************/
extern void System_Init(void);
#ifdef CR52_MULTICORE_SUPPORT
extern void Start(void);    /* Start lable in r52_startup.s */
extern void CR52_Wakeup(uint32 core_id, uint32 boot_addr);
void main_dummy(void);
#endif

extern int main(void);

//# pragma SET_CODE_SECTION (".brsMainStartup")
void handler_reset() __attribute__((noreturn));
extern void R52BrsMain_MemoryInit_StageZero_Hook(uint32);
extern void R52BrsMain_MemoryInit_StageOne_Hook(uint32);
extern void R52BrsMain_MemoryInit_StageHardReset_Hook(uint32);
extern void R52BrsMain_MemoryInit_StageTwo_Hook(uint32);
extern void R52BrsMain_MemoryInit_StageThree_Hook(uint32);
extern void R52BrsMain_PreMainHook(uint32);
void Brs_PreMainStartup(void);
void Brs_MemoryZeroInit(const vLinkGen_MemAreaSet *, uint32, uint32);
void Brs_MemoryInit(const vLinkGen_RamMemAreaSet *, uint32);

#define SEC_SRC            *((volatile uint32 *)(0xFFC43018))
#define CR_SECURE          ((uint32)(1 << 0))

extern void (*__init_array_start []) (void) __attribute__((weak));
extern void (*__init_array_end []) (void) __attribute__((weak));

void call_constructors() {
    int count = __init_array_end - __init_array_start;
    for (int i = 0; i < count; i++) {
        if (__init_array_start[i]) {
            __init_array_start[i]();
        }
    }
}

extern void os_mcu10_ipcCfg_PreInit();
unsigned int g_swapInfo_u32 = 0;
/* Reset handler */
void handler_reset()
{
    uint32 ulCoreID;
    ulCoreID = __get_CRCoreID();

    if(CR52_CPU0 == ulCoreID)
    {
        /* Setup stacks */
        __set_mode(CPSR_M_UND);
        __set_SP(&__und_stack_c0_end);
        __set_mode(CPSR_M_ABT);
        __set_SP(&__abt_stack_c0_end);
        __set_mode(CPSR_M_IRQ);
        __set_SP(&__irq_stack_c0_end);
        __set_mode(CPSR_M_FIQ);
        __set_SP(&__fiq_stack_c0_end);
        __set_mode(CPSR_M_SYS);
        __set_SP(&__sys_stack_c0_end);    
    }
    else if(CR52_CPU1 == ulCoreID)
    {
          /* Setup stacks */
        __set_mode(CPSR_M_UND);
        __set_SP(&__und_stack_c1_end);
        __set_mode(CPSR_M_ABT);
        __set_SP(&__abt_stack_c1_end);
        __set_mode(CPSR_M_IRQ);
        __set_SP(&__irq_stack_c1_end);
        __set_mode(CPSR_M_FIQ);
        __set_SP(&__fiq_stack_c1_end);
        __set_mode(CPSR_M_SYS);
        __set_SP(&__sys_stack_c1_end);  
    }
    else if(CR52_CPU2 == ulCoreID)
    {
          /* Setup stacks */
        __set_mode(CPSR_M_UND);
        __set_SP(&__und_stack_c2_end);
        __set_mode(CPSR_M_ABT);
        __set_SP(&__abt_stack_c2_end);
        __set_mode(CPSR_M_IRQ);
        __set_SP(&__irq_stack_c2_end);
        __set_mode(CPSR_M_FIQ);
        __set_SP(&__fiq_stack_c2_end);
        __set_mode(CPSR_M_SYS);
        __set_SP(&__sys_stack_c2_end);  
    }
    else
    {
        /* undefined core */
        while (1);
    }

    /* Only need to be initialized by one core */

    // #ifdef CR52_MULTICORE_SUPPORT
    // if (CR52_CPU0 == ulCoreID)
    // #endif
    {
        /* Setup MPU */
        MPU_Setup();
        /* Clear bss */

        /* Enable VFP */
        VFP_Enable();
        /* System Init */
        System_Init();
		
		if (CR52_CPU1 == ulCoreID)
		{

			GIC_SetPriority((uint32)651, 6);
			GIC_SetPriority((uint32)655, 6);
			GIC_SetPriority((uint32)659, 6);

			// GIC_SetGroup((uint32)651, 0U);
			// GIC_SetGroup((uint32)655, 0U);
			// GIC_SetGroup((uint32)659, 0U);
		}
		
        GIC_EnableDistributor();

		Device_Init();

		/* Non-secure mode */
    	SEC_SRC &= ~CR_SECURE;
    }
    
    /* Autosar Brs_PreMainStartup */
    Brs_PreMainStartup();

    /* Invalidate all caches */
    L1C_InvalidateICacheAll();
    L1C_InvalidateDCacheAll();
    /* Enable caches */
    L1C_EnableICache();
    L1C_EnableDCache();
    L1C_EnableBP();

    #ifdef CR52_MULTICORE_SUPPORT
    if (CR52_CPU0 == ulCoreID)
    #endif
    {
        /*Start other cores*/
        #ifdef CR52_CPU1_USED
        // CR52_Wakeup(CR52_CPU1, (uint32)&Start); /* other rcores wakeup in cx loader */
        #endif
        #ifdef CR52_CPU2_USED
        // CR52_Wakeup(CR52_CPU2, (uint32)&Start); /* other rcores wakeup in cx loader */
        #endif
        /* Simple loop to avoid conflict when using serial port */
        while (GulCnt--)
        {
          __NOP();
        }
		call_constructors();
        /* Jump to main */
        Os_Hal_INTC_EnableSource(149, 8, OS_HAL_ISR_INTC_ROOT); // Enable xsignal ISR
        Os_Hal_INTC_EnableSource(645, 8, OS_HAL_ISR_INTC_ROOT); //Enable i2c3
        Os_Hal_INTC_EnableSource(647, 8, OS_HAL_ISR_INTC_ROOT); //Enable i2c5
        Os_Hal_INTC_EnableSource(276, 8, OS_HAL_ISR_INTC_ROOT); //Enable spi5
        Os_Hal_INTC_EnableSource(651, 8, OS_HAL_ISR_INTC_ROOT); 
        // Os_Hal_INTC_EnableSource(444, 8, OS_HAL_ISR_INTC_ROOT); //CANFD channel interrupt 
        // Os_Hal_INTC_EnableSource(445, 8, OS_HAL_ISR_INTC_ROOT); //CANFD global interrupt
        os_mcu10_ipcCfg_PreInit();

        /* make sure g_DataDummy4Bin exists */
        g_DataDummy4Bin++;

        g_swapInfo_u32 = *((uint32 *)0xEB23FC04U);

        main();
    }


    #ifdef CR52_CPU1_USED
    if (CR52_CPU1 == ulCoreID)
    {
        /* Simple loop to avoid conflict when using serial port */
        while (GulCnt1--)
        {
          __NOP();
        }
        /* Jump to main */
        // main_dummy();
        Os_Hal_INTC_EnableSource(644, 8, OS_HAL_ISR_INTC_ROOT); //Enable i2c2	
		Os_Hal_INTC_EnableSource(122, 8, OS_HAL_ISR_INTC_ROOT); //Enable spi_dma8
        Os_Hal_INTC_EnableSource(124, 8, OS_HAL_ISR_INTC_ROOT); //Enable spi_dma10
        Os_Hal_INTC_EnableSource(126, 8, OS_HAL_ISR_INTC_ROOT); //Enable spi_dma12
        Os_Hal_INTC_EnableSource(271, 8, OS_HAL_ISR_INTC_ROOT); //Enable spi0
        Os_Hal_INTC_EnableSource(272, 8, OS_HAL_ISR_INTC_ROOT); //Enable spi1
        Os_Hal_INTC_EnableSource(275, 8, OS_HAL_ISR_INTC_ROOT); //Enable spi4
        Os_Hal_INTC_EnableSource(151, 8, OS_HAL_ISR_INTC_ROOT); //Enable xsignal ISR

        
        main();
    }
    #endif

    #ifdef CR52_CPU2_USED
    if (CR52_CPU2 == ulCoreID)
    {
        /* Simple loop to avoid conflict when using serial port */
        while (GulCnt2--)
        {
          __NOP();
        }
        /* Jump to main */
        // main_dummy();
        main();
    }
    #endif

    /* Never exit */
    while (1);
}

#ifdef CR52_MULTICORE_SUPPORT
/* Dummy main function for core other than 0 */
void main_dummy(void)
{
    while (1);
}
#endif

extern void Port0IrqHandler(void);
extern void Port1IrqHandler(void);
extern void Port2IrqHandler(void);

/* Default handler */
void __attribute__((interrupt("FIQ"))) handler_fiq(void);
void handler_fiq()
{
  uint32 intid = GIC_AcknowledgePending0();

  if (intid == 651)
  {
	
	Port0IrqHandler();
	Os_Hal_INTC_ClearPendingFiq(651);
  }
  else if (intid == 655)
  {
	
	Port1IrqHandler();
	Os_Hal_INTC_ClearPendingFiq(655);
  }
  else if(intid == 659)
  {
	
	Port2IrqHandler();
	Os_Hal_INTC_ClearPendingFiq(659);
  }
  else
  {
	;
  }
}

/* Default handler */
void handler_svc()
{
	Svc_CoreDumpSave();
    while (1);
}

/* Default handler */
void handler_pre_abt()
{
	PreAbt_CoreDumpSave();
    while (1);
}

/* Default handler */
void handler_data_abt()
{
	DataAbt_CoreDumpSave();
    while (1);
}

/* Default handler */
void handler_und()
{
	Und_CoreDumpSave();
    while (1);
}

/* Default handler */
void handler_hvc()
{
    while (1);
}

/* Default handler */
void handler_hvt()
{
    while (1);
}

/* Default handler */
void handler_reserved()
{
    while (1);
}

void Brs_PreMainStartup(void)
{
    uint32 coreID;

    coreID = __get_CRCoreID();

    R52BrsMain_MemoryInit_StageZero_Hook(coreID);

#if (VLINKGEN_CFG_NUM_ZERO_INIT_ZERO_GROUPS>1uL)
  /* vLinkGen_ZeroInit_Zero_GroupsSet contains vLinkGen VarSectionGroups, configured with Init Policy "ZERO_INIT" and Init Stage "ZERO" */
    Brs_MemoryZeroInit(&vLinkGen_ZeroInit_Zero_GroupsSet, BRS_INIT_PATTERN_AREAS, coreID);
#endif

#if (VLINKGEN_CFG_NUM_INIT_ZERO_GROUPS>1uL)
  /* vLinkGen_Init_Zero_GroupsSet contains vLinkGen VarSectionGroups, configured with Init Policy "INIT" and Init Stage "ZERO" */
    Brs_MemoryInit(&vLinkGen_Init_Zero_GroupsSet, coreID);
#endif

    R52BrsMain_MemoryInit_StageOne_Hook(coreID);

#if (VLINKGEN_CFG_NUM_ZERO_INIT_ONE_BLOCKS>1uL)
  /* vLinkGen_ZeroInit_One_BlocksSet contains vLinkGen memory region blocks, configured with Init Stage "ONE" */
    Brs_MemoryZeroInit(&vLinkGen_ZeroInit_One_BlocksSet, BRS_INIT_PATTERN_BLOCKS, coreID);
#endif

#if (VLINKGEN_CFG_NUM_ZERO_INIT_ONE_GROUPS>1uL)
  /* vLinkGen_ZeroInit_One_GroupsSet contains vLinkGen VarSectionGroups, configured with Init Policy "ZERO_INIT" and Init Stage "ONE" */
    Brs_MemoryZeroInit(&vLinkGen_ZeroInit_One_GroupsSet, BRS_INIT_PATTERN_AREAS, coreID);
#endif

#if (VLINKGEN_CFG_NUM_INIT_ONE_GROUPS>1uL)
  /* vLinkGen_Init_One_GroupsSet contains vLinkGen VarSectionGroups, configured with Init Policy "INIT" and Init Stage "ONE" */
    Brs_MemoryInit(&vLinkGen_Init_One_GroupsSet, coreID);
#endif

    R52BrsMain_MemoryInit_StageHardReset_Hook(coreID);

    R52BrsMain_MemoryInit_StageTwo_Hook(coreID);

#if (VLINKGEN_CFG_NUM_ZERO_INIT_TWO_GROUPS>1uL)
    /* vLinkGen_ZeroInit_Two_GroupsSet contains vLinkGen VarSectionGroups, configured with Init Policy "ZERO_INIT" and Init Stage "TWO" */
    Brs_MemoryZeroInit(&vLinkGen_ZeroInit_Two_GroupsSet, BRS_INIT_PATTERN_AREAS, coreID);
#endif

#if (VLINKGEN_CFG_NUM_INIT_TWO_GROUPS>1uL)
    /* vLinkGen_Init_Two_GroupsSet contains vLinkGen VarSectionGroups, configured with Init Policy "INIT" and Init Stage "TWO" */
    Brs_MemoryInit(&vLinkGen_Init_Two_GroupsSet, coreID);
#endif

    R52BrsMain_MemoryInit_StageThree_Hook(coreID);

#if (VLINKGEN_CFG_NUM_ZERO_INIT_THREE_GROUPS>1uL)
    /* vLinkGen_ZeroInit_Three_GroupsSet contains vLinkGen VarSectionGroups, configured with Init Policy "ZERO_INIT" and Init Stage "THREE" */
    Brs_MemoryZeroInit(&vLinkGen_ZeroInit_Three_GroupsSet, BRS_INIT_PATTERN_AREAS, coreID);
#endif

#if (VLINKGEN_CFG_NUM_INIT_THREE_GROUPS>1uL)
    /* vLinkGen_Init_Three_GroupsSet contains vLinkGen VarSectionGroups, configured with Init Policy "INIT" and Init Stage "THREE" */
    Brs_MemoryInit(&vLinkGen_Init_Three_GroupsSet, coreID);
#endif

    R52BrsMain_PreMainHook(coreID);

    /* call C++ constructor */
}


/*****************************************************************************/
/**
 * @brief      Generic routine for RAM zeroing.
 * @pre        -
 * @param[in]  memAreaSet shall point to the first element of a vLinkGen_MemAreaSet struct array
 * @param[in]  InitPattern describes the pattern, to be used for the memory initialization
 * @param[in]  coreId shall describe the ID of the current CPU
 * @param[out] -
 * @return     -
 * @context    Function is called from Brs_PreMainStartup() to initialize memory ares,
 *             generated out of vLinkGen configuration
 */
/*****************************************************************************/
void Brs_MemoryZeroInit(const vLinkGen_MemAreaSet *memAreaSet, uint32 InitPattern, uint32 coreId)
{
    uint8 i;
#if !defined (BRSHW_ASM_MEMORY_ZERO_INIT_LOOP_AVAILABLE)
    volatile uint32 *memPtr;
#endif

    for (i=0; i<memAreaSet->Num; i++)
    {
        if (memAreaSet->Areas[i].Core==coreId && (memAreaSet->Areas[i].End - memAreaSet->Areas[i].Start) > 0)
        {
#if defined (BRSHW_ASM_MEMORY_ZERO_INIT_LOOP_AVAILABLE)
            BrsHw_AsmMemoryZeroInitLoop(memAreaSet->Areas[i].Start, memAreaSet->Areas[i].End, InitPattern);

#else
            memPtr = (volatile uint32*)memAreaSet->Areas[i].Start;
            while ((uint32)memPtr < memAreaSet->Areas[i].End)
            {
                *memPtr = InitPattern;
                memPtr++;
            }
#endif /*else BRSHW_ASM_MEMORY_ZERO_INIT_LOOP_AVAILABLE*/
        }
    }
}

/*****************************************************************************/
/**
 * @brief      Generic routine for ROM to RAM initialization.
 * @pre        -
 * @param[in]  memAreasSet shall point to the first element of a vLinkGen_RamMemAreaSet struct array
 * @param[in]  coreId shall describe the ID of the current CPU
 * @param[out] -
 * @return     -
 * @context    Function is called from Brs_PreMainStartup() to initialize memory ares,
 *             generated out of vLinkGen configuration
 */
/*****************************************************************************/
void Brs_MemoryInit(const vLinkGen_RamMemAreaSet *memAreasSet, uint32 coreId)
{
    volatile uint32 *memPtr;
    volatile uint32 *romPtr;
    uint8 i;

    for (i=0; i<memAreasSet->Num; i++)
    {
        if (memAreasSet->Areas[i].Core==coreId && ((memAreasSet->Areas[i].End - memAreasSet->Areas[i].Start) > 0))
        {
            /* run size might smaller than load size, because the trampoline code section will be added to the load area in link time.
            these trampolines should also be copied to the run area. */
            if ((memAreasSet->Areas[i].End - memAreasSet->Areas[i].Start) > (memAreasSet->Areas[i].Romend - memAreasSet->Areas[i].Romstart))
            {
                /* Defined size in rom does not match ram size */
#ifndef ZX_IDC_SOC_RENESAS_V4H
                BrsMainExceptionHandler(kBrsIllegalParameter, BRSERROR_MODULE_BRSMAINSTARTUP, (uint16)(__LINE__));
#else
                BrsMainExceptionHandler(kBrsIllegalParameter, BRSERROR_MODULE_BRSMAINSTARTUP, (uint16)(0xffffu));
#endif
            }

            memPtr = (volatile uint32*)memAreasSet->Areas[i].Start;
            romPtr = (volatile uint32*)memAreasSet->Areas[i].Romstart;
            /* trampolines should also be copied to the run area. */
            while ((uint32)romPtr < memAreasSet->Areas[i].Romend)
            {
                *memPtr = *romPtr;
                memPtr++;
                romPtr++;
            }
        }
    }
}

// #pragma section .brsMainStartup end

/*******************************************************************************
**                      End of File                                           **
*******************************************************************************/
