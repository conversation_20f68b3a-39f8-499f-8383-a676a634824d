/**
 * @file Interrupt_CrossCore_J6E.c
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 1.0
 * @date 2024-11-06
 *
 *
 */

#include "Platform_Types.h"

#define OS_J6E_INT_ROUTER_BASE 0x23150000U
#define OS_J6E_INT_ROUTER_SEL0 0x23151000U
#define OS_J6E_INT_ROUTER_SEL192 *((volatile uint32*)(OS_J6E_INT_ROUTER_SEL0 + 0x0300U))
#define OS_J6E_INT_ROUTER_SEL193 *((volatile uint32*)(OS_J6E_INT_ROUTER_SEL0 + 0x0304U))
#define OS_J6E_INT_ROUTER_SEL194 *((volatile uint32*)(OS_J6E_INT_ROUTER_SEL0 + 0x0308U))
#define OS_J6E_INT_ROUTER_SW_TRIG_INT0 *((volatile uint32*)(OS_J6E_INT_ROUTER_BASE + 0x4000U))
#define OS_J6E_INT_ROUTER_SW_TRIG_INT1 *((volatile uint32*)(OS_J6E_INT_ROUTER_BASE + 0x4004U))
#define OS_J6E_INT_ROUTER_SW_TRIG_INT2 *((volatile uint32*)(OS_J6E_INT_ROUTER_BASE + 0x4008U))

void InitializeCrossCoreISR(uint8 core)
{
    switch (core)
    {
    case 0: /* Logical core 0, physical core 0, INTID237 */
        OS_J6E_INT_ROUTER_SEL192 = 512U;
        break;
    case 1: /* Logical core 1, physical core 2, INTID238 */
        OS_J6E_INT_ROUTER_SEL193 = 513U;
        break;
    case 2: /* Logical core 2, physical core 3, INTID239 */
        OS_J6E_INT_ROUTER_SEL194 = 514U;
        break;
    default:
        /* Core not in use */
        break;
    }
}

void RaiseCrossCoreISR(uint8 core)
{
    switch (core)
    {
    case 0: /* Logical core 0, physical core 0, INTID237 */
        OS_J6E_INT_ROUTER_SW_TRIG_INT0 = 0x1;
        break;
    case 1: /* Logical core 1, physical core 2, INTID238 */
        OS_J6E_INT_ROUTER_SW_TRIG_INT1 = 0x1;
        break;
    case 2: /* Logical core 2, physical core 3, INTID239 */
        OS_J6E_INT_ROUTER_SW_TRIG_INT2 = 0x1;
        break;
    default:
        /* Core not in use */
        break;
    }
    __asm("DSB");
}

void ClearCrossCoreISR0(void)
{
    OS_J6E_INT_ROUTER_SW_TRIG_INT0 = 0x0;
    __asm("DSB");
}

void ClearCrossCoreISR1(void)
{
    OS_J6E_INT_ROUTER_SW_TRIG_INT1 = 0x0;
    __asm("DSB");
}

void ClearCrossCoreISR2(void)
{
    OS_J6E_INT_ROUTER_SW_TRIG_INT2 = 0x0;
    __asm("DSB");
}
