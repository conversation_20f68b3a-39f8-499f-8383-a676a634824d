/*============================================================================*/
/* Project      = AUTOSAR Renesas CPF MCAL Components                         */
/* Module       = start.s                                                     */
/* SW-VERSION   = x.x.x                                                       */
/*============================================================================*/
/*                                  COPYRIGHT                                 */
/*============================================================================*/
/* (c) 2021-2022 Renesas Electronics Corporation. All rights reserved.        */
/*============================================================================*/
/* Purpose:                                                                   */
/* Startup code for R-Car devices (ARMv8)                                     */
/*                                                                            */
/*============================================================================*/
/*                                                                            */
/* Unless otherwise agreed upon in writing between your company and           */
/* Renesas Electronics Corporation the following shall apply!                 */
/*                                                                            */
/* Warranty Disclaimer                                                        */
/*                                                                            */
/* There is no warranty of any kind whatsoever granted by Renesas. Any        */
/* warranty is expressly disclaimed and excluded by Renesas, either expressed */
/* or implied, including but not limited to those for non-infringement of     */
/* intellectual property, merchantability and/or fitness for the particular   */
/* purpose.                                                                   */
/*                                                                            */
/* Renesas shall not have any obligation to maintain, service or provide bug  */
/* fixes for the supplied Product(s) and/or the Application.                  */
/*                                                                            */
/* Each User is solely responsible for determining the appropriateness of     */
/* using the Product(s) and assumes all risks associated with its exercise    */
/* of rights under this Agreement, including, but not limited to the risks    */
/* and costs of program errors, compliance with applicable laws, damage to    */
/* or loss of data, programs or equipment, and unavailability or              */
/* interruption of operations.                                                */
/*                                                                            */
/* Limitation of Liability                                                    */
/*                                                                            */
/* In no event shall Renesas be liable to the User for any incidental,        */
/* consequential, indirect, or punitive damage (including but not limited     */
/* to lost profits) regardless of whether such liability is based on breach   */
/* of contract, tort, strict liability, breach of warranties, failure of      */
/* essential purpose or otherwise and even if advised of the possibility of   */
/* such damages. Renesas shall not be liable for any services or products     */
/* provided by third party vendors, developers or consultants identified or   */
/* referred to the User by Renesas in connection with the Product(s) and/or   */
/* the Application.                                                           */
/*                                                                            */
/*============================================================================*/
/* Environment:                                                               */
/*              Devices:        CPF                                           */
/*============================================================================*/

/*******************************************************************************
**                      Revision Control History                              **
*******************************************************************************/
/*
 * x.x.x      22-Mar-2023     yian: Stitch somthing else...
 * 1.0.3      13-Jun-2022     Support enable/disable Console_Print function
 * 1.0.2      07-Apr-2022     Update base address for V4H to support new IPL
 * 1.0.1      06-Jan-2022     Separate start and MPU area addresses for V4H
 * 1.0.0      18-Oct-2021     Initial Version
 */

/* S4, V4H */
/* Boot address of CR core */
#define CR_START_ADDRESS                   0xE2100000UL
#define MPU_AREA0_BASE_ADDRESS             0xE2100000UL
/* (BASE + 0x5FFFFF) & ~(0x3F) */
#define MPU_AREA0_LIMIT_ADDRESS            0xE27FFFC0UL

/* Start address of non vbuf for data etc. */
#define MPU_AREA1_BASE_ADDRESS             0xB4000000UL
/* (BASE + 0x5FFFFF) & ~(0x3F) */
#define MPU_AREA1_LIMIT_ADDRESS            0xB40FFFC0UL

#define RW_Access         0b01
#define Non_Shareable     0b00
#define ENable            0b1
#define AttrIndx1         0b001

  /* Handlers */
  .global handler_reserved
  .global handler_und
  .global handler_hvc
  .global handler_pre_abt
  .global handler_data_abt
  .global handler_hvt
  .global handler_irq
  .global Os_Hal_IrqInterruptEntry_GIC_Mcu_Core0
  .global Os_Hal_IrqInterruptEntry_GIC_Mcu_Core1
  .global handler_fiq
  .global handler_reset

  /* Entry point for reset handler */
  .global Start
  .global EL2_Vectors
  .global OsCfg_Hal_Core_OsCore0_ExceptionTable
  .global OsCfg_Hal_Core_OsCore1_ExceptionTable
Start:
  .arm

  .align 5
EL2_Vectors:
  b   EL2
  b   handler_und
  b   handler_hvc
  b   handler_pre_abt
  b   handler_data_abt
  b   handler_hvt
  b   Os_Hal_IrqInterruptEntry_GIC_Mcu_Core0
  b   handler_fiq

.global OsCfg_Hal_Core_OsCore0_ExceptionTable
OsCfg_Hal_Core_OsCore0_ExceptionTable:
  b   handler_reserved
  b   handler_und
  b   handler_svc
  b   handler_pre_abt
  b   handler_data_abt
  b   handler_reserved
  b   Os_Hal_IrqInterruptEntry_GIC_Mcu_Core0
  b   handler_fiq

.global OsCfg_Hal_Core_OsCore1_ExceptionTable
OsCfg_Hal_Core_OsCore1_ExceptionTable:
  b   handler_reserved
  b   handler_und
  b   handler_svc
  b   handler_pre_abt
  b   handler_data_abt
  b   handler_reserved
  b   Os_Hal_IrqInterruptEntry_GIC_Mcu_Core1
  b   handler_fiq

EL2:
  /* Initialize registers */
  mov r0,  #0
  mov r1,  #0
  mov r2,  #0
  mov r3,  #0
  mov r4,  #0
  mov r5,  #0
  mov r6,  #0
  mov r7,  #0
  mov r8,  #0
  mov r9,  #0
  mov r10, #0
  mov r11, #0
  mov r12, #0
  mov lr,  #0

  /* Get Core Id to r5 */
  mrc p15, #0, r5, c0, c0, #5
  and r5, r5, #0x0F00
  mov r5, r5, lsr #8

  /* Branch to the core Id specific initialize stack for EL2 (Hyp) */
  cmp r5, #0
  beq stack_hyp_init_c0
  cmp r5, #1
  beq stack_hyp_init_c1
  cmp r5, #2
  beq stack_hyp_init_c2

stack_hyp_init_c0: 
  /* Initialize stack for EL2 (Hyp) core0 */
  ldr r0, .hyp_stack_c0
  mov sp, r0
  b stack_hyp_init_end
  
stack_hyp_init_c1: 
  /* Initialize stack for EL2 (Hyp) core1 */
  ldr r0, .hyp_stack_c1
  mov sp, r0
  b stack_hyp_init_end
  
stack_hyp_init_c2: 
  /* Initialize stack for EL2 (Hyp) core2 */
  ldr r0, .hyp_stack_c2
  mov sp, r0
  b stack_hyp_init_end
stack_hyp_init_end:
  /* MPU setting only on init core -- not anymore */
  /* cmp r5, #0 */
  /* bne to_svc_mode */

  /* Disable all regions */
  mov r0, #0
  mov r1, #0
  mov r2, #24
loop:
  mcr p15, #0, r0, c6, c2, #1
  mcr p15, #0, r1, c6, c3, #1
  add r0, r0, #1
  cmp r0, r2
  bne loop

  /* Attribute index 1, normal, cache */
  mov r0, #0xFF00
  mcr p15, #0, r0, c10, c2, #0

  /* Select Region 0 via PRSELR */
  mov r0, #0
  mcr p15, #0, r0, c6, c2, #1

  /* BASE - PRBAR[31:6] */
  ldr r0, =MPU_AREA0_BASE_ADDRESS
  /* PRBAR[4:3] SH=b'00 - Normal memory, Non-shareable,
     PRBAR[2:1] AP=b'01 - Read/Write Allocation
     PRBAR[0]   XN=b'0  - Instruction permitted */
  ldr r1, =((Non_Shareable<<3) | (RW_Access<<1))
  orr r0, r0, r1
  /* Set XN, AP, SH, BASE */
  mcr p15, #0, r0, c6, c3, #0

  /* BASE - PRLAR[31:6] */
  ldr r0, =MPU_AREA0_LIMIT_ADDRESS
  /* PRLAR[3:1] AttrIndx=1 - Write-Back */
  ldr r1, =((AttrIndx1<<1) | (ENable))
  orr r0, r0, r1
  /* Set LIMIT, attribute index, enable */
  mcr p15, #0, r0, c6, c3, #1

  /* Select Region 1 via PRSELR */
  mov r0, #1
  mcr p15, #0, r0, c6, c2, #1

  /* BASE - PRBAR[31:6] */
  ldr r0, =MPU_AREA1_BASE_ADDRESS
  /* PRBAR[4:3] SH=b'00 - Normal memory, Non-shareable,
     PRBAR[2:1] AP=b'01 - Read/Write Allocation
     PRBAR[0]   XN=b'0  - Instruction permitted */
  ldr r1, =((Non_Shareable<<3) | (RW_Access<<1))
  orr r0, r0, r1
  /* Set XN, AP, SH, BASE */
  mcr p15, #0, r0, c6, c3, #0

  /* BASE - PRLAR[31:6] */
  ldr r0, =MPU_AREA1_LIMIT_ADDRESS
  /* PRLAR[3:1] AttrIndx=1 - Write-Back */
  ldr r1, =((AttrIndx1<<1) | (ENable))
  orr r0, r0, r1
  /* Set LIMIT, attribute index, enable */
  mcr p15, #0, r0, c6, c3, #1
  
  /* Enable MPU */
  mrc p15, #0, r0, c1, c0, #0
  orr r0, r0, #1
  dsb
  mcr p15, #0, r0, c1, c0, #0
  isb
to_svc_mode:
  /* Jump to SVC mode */
  mrs r0, cpsr
  mov r1, #0x13
  bfi r0, r1, #0, #5
  msr spsr_hyp, r0

  ldr r0, =CR_START_ADDRESS
  mcr p15, 0, r0, c12, c0, 0

  ldr r0, =EL1
  msr elr_hyp, r0
  dsb
  isb
  eret
EL1:
  /* Branch to the core Id specific initialize stack for SVC */
  cmp r5, #0
  beq stack_svc_init_c0
  cmp r5, #1
  beq stack_svc_init_c1
  cmp r5, #2
  beq stack_svc_init_c2

stack_svc_init_c0:  
  /* Initialize stack for SVC core0 */
  ldr r0, .svc_stack_c0
  mov sp, r0
  /* Set EL1 Vectors */
  ldr r0, =OsCfg_Hal_Core_OsCore0_ExceptionTable
  mcr p15, 0, r0, c12, c0, 0
  b stack_svc_init_end

stack_svc_init_c1:  
  /* Initialize stack for SVC core1 */
  ldr r0, .svc_stack_c1
  mov sp, r0
  /* Set EL1 Vectors */
  ldr r0, =OsCfg_Hal_Core_OsCore0_ExceptionTable
  mcr p15, 0, r0, c12, c0, 0
  b stack_svc_init_end
  
stack_svc_init_c2:  
  /* Initialize stack for SVC core2 */
  ldr r0, .svc_stack_c2
  mov sp, r0
  /* Set EL1 Vectors */
  ldr r0, =OsCfg_Hal_Core_OsCore1_ExceptionTable
  mcr p15, 0, r0, c12, c0, 0
  b stack_svc_init_end  
stack_svc_init_end:

  /* Disable forwarding interrupt */
  mov r0, #0
  mrc p15, #0, r0, c12, c12, #7

  /* Zero init vlinkgen early groups, whitch are stack areas */
  ldr r1, =vLinkGen_ZeroInit_Early_Groups
startup_area_zero_init_start:
  mov r2, r1
  add r1, r1, #16
  ldr r3, [r2]      /* vLinkGen_ZeroInit_Early_Groups->start */
  ldr r4, [r2, #4]  /* vLinkGen_ZeroInit_Early_Groups->end */
  ldr r0, [r2, #8]  /* vLinkGen_ZeroInit_Early_Groups->core */
  /* Alignment parameter actually not used here */
  /* Verify if the end of struct vLinkGen_ZeroInit_Early_Groups is reached, by checking if start == 0, end == 0 and core == 0 */
  mov r6, #0        /* If InitCore is not running -> go to the next array entry */
  cmp r3, r4        /* If Start and End address are equal -> Finished */
  beq startup_area_zero_init_end
  cmp r5, r0
  bne startup_area_zero_init_start
startup_area_zero_init_loop_start:
  str r6, [r3]      /* must be an aligned memory access! */
  add r3, r3, #4
  cmp r3, r4        /* If Start is same with End address-> Finished. */
  beq startup_area_zero_init_start
  b startup_area_zero_init_loop_start
startup_area_zero_init_end:  

 /* Jump to C code */
  bl handler_reset

/* Print function */
.global Console_Print
.type Console_Print, %function
Console_Print:
  #ifdef MCAL_LOG_PRINT
  push {r0-r3}
  mov r0, r13
  push {r14}
  bl Log_Print
  pop {r12}
  pop {r0-r3}
  mov pc, r12
  #else
  bx r14
  #endif


  .global __hyp_stack_c0_end
  .hyp_stack_c0: .word __hyp_stack_c0_end
  .global __hyp_stack_c1_end
  .hyp_stack_c1: .word __hyp_stack_c1_end
  .global __hyp_stack_c2_end
  .hyp_stack_c2: .word __hyp_stack_c2_end
  .global __svc_stack_c0_end
  .svc_stack_c0: .word __svc_stack_c0_end
  .global __svc_stack_c1_end
  .svc_stack_c1: .word __svc_stack_c1_end
  .global __svc_stack_c2_end
  .svc_stack_c2: .word __svc_stack_c2_end


