/**********************************************************************************************************************
 *  FILE REQUIRES USER MODIFICATIONS
 *  Template Scope: sections marked with Start and End comments
 *  -------------------------------------------------------------------------------------------------------------------
 *  This file includes template code that must be completed and/or adapted during BSW integration.
 *  The template code is incomplete and only intended for providing a signature and an empty implementation.
 *  It is neither intended nor qualified for use in series production without applying suitable quality measures.
 *  The template code must be completed as described in the instructions given within this file and/or in the.
 *  Technical Reference.
 *  The completed implementation must be tested with diligent care and must comply with all quality requirements which.
 *  are necessary according to the state of the art before its use.
 *********************************************************************************************************************/
/**********************************************************************************************************************
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  BSW.c
 *           Config:  D:/0_Project/1_iMotion_Driving_Computer/EEZI/idc_mcu_app/cpj_eezi/microsar_cfg/build/SipAddon/Demo.dpa
 *        SW-C Type:  BSW
 *  Generation Time:  2022-04-22 17:45:07
 *
 *        Generator:  MICROSAR RTE Generator Version 4.23.0
 *                    RTE Core Version 1.23.0
 *          License:  CBD2000702
 *
 *      Description:  C-Code implementation template for SW-C <BSW>
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << Start of version logging area >>                DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/

/* PRQA S 0777, 0779 EOF */ /* MD_MSR_Rule5.1, MD_MSR_Rule5.2 */

/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << End of version logging area >>                  DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/

#include "Rte_BSW.h"
#include "Os.h"
#include "Uart.h"
#include "Log.h"
#include "Dma.h"

/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << Start of include and declaration area >>        DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/
#include "hal_cfg/hal_hsi_cfg.h"
// #include "PinMultiplexing.h"
#include "Power_Proc.h"
#include "OtaFlash_Ipc.h"
#include "Crypto.h"
#include "Boot.h"
#include "TempM_api.h"
#include "VoltM_api.h"
#include "TMP75_api.h"
#include "KL15_Api.h"
#include "LowPower.h"
#include "ComplexCan_Api.h"
#include "CoreTemp_Api.h"
#include "HW_Version.h"
// #if (NA226 == TRUE) 
//     #include "NA226_api.h"
// #endif
// #if(TCA9539_MAXNUM != 0U)
//     #include "TCA9539_api.h"
// #endif
#include "I2c.h"
#include "TCA9539.h"
#include "Mcu_Adc.h"
#include "Sip_Check.h"
#include "Log.h"
// #include "Dio_Cfg.h"
// #include "raa271005_api.h"
#include "Port.h"
#include "Pmic.h"
// #include "CDD_Iic.h"
// #include "CDD_Ths.h"
#include "Pwm.h"
// #include "ads7142_api.h"
#include "NetM_api.h"
// #include "Cmt_api.h"
// #include "Tmu_api.h"
// #include "iTrace/app_serial.h"
#include "Net_Cfg.h"
#include "iNet_com.h"
#define RTE_CORE
#include "Rte_StbM.h"

#include "SchM_Fls.h"
#include "Fee.h"
#include "NvM.h"
// #include "BootFlash_api.h"

#include "Uart.h"
#include "Log.h"
#include "Gpt.h"
// #include "Port.h"

#include "Dma.h"
#include "Dma_Prv_Mdma.h"

#include "Uart.h"
#include "Uart_Private.h"

#include "Fls.h"
#include "Rtc.h"

#include "Ipc.h"

#include "Gpt_PBcfg.h"
#include "Target.h"
#include "hb_CAN2IPC.h"
#include "virt_xspi_hal.h"
#include "Eth_30_Tc3xx.h" 
#include "ComM.h"
#include "ComM_EcuMBswM.h"
#include "ComM_Cfg.h"
#include "Dio.h"
#include "time_sync/time_sync_api.h"
#include "Safety_Extern.h"
#include "Shell_Port.h"
#include "Pvt.h"
#include "Camera_Autosar.h"
#include "Camera_PreInit.h"
#include "camera_common_lpwm.h"
#include "sys_stateMonitor.h"
#include "Qga.h"
#include "fan.h"
#include "IoHwAb_Dio.h"
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << End of include and declaration area >>          DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/
#define BSW_START_SEC_CODE
#include "BSW_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */
#include "Os.h"
#include "Wdg_Ext.h"

#if defined OS_MS2TICKS_SystemTimer
#define RTE_MSEC_SystemTimer OS_MS2TICKS_SystemTimer
#else
#define RTE_MSEC_SystemTimer(val)                                                                                      \
    ((TickType)RTE_CONST_MSEC_SystemTimer_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
#endif
/**********************************************************************************************************************
 *
 * Runnable Entity Name: BSW_Init
 *
 *---------------------------------------------------------------------------------------------------------------------
 *
 * Executed once after the RTE is started
 *
 *********************************************************************************************************************/
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << Start of documentation area >>                  DO NOT CHANGE THIS COMMENT!
 * Symbol: BSW_Init_doc
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << End of documentation area >>                    DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/
volatile uint32 Task_Counter_u32;
extern Pvt_ConfigType Pvt_Config;
extern const Port_ConfigType Port_Config_sip;

FUNC(void, BSW_CODE) BSW_Init(void) /* PRQA S 0624, 3206 */ /* MD_Rte_0624, MD_Rte_3206 */
{
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << Start of runnable implementation >>             DO NOT CHANGE THIS COMMENT!
 * Symbol: BSW_Init
 *********************************************************************************************************************/
    hal_get_boardId();
     // This function cannot be missing, otherwise there will be exceptions when Acore starts
    PreOS_Firewall_Config(); // important
    Mcu_ModuleInit(); // important

    if(g_isJ6SocBoard_b)
    {
        Port_Init(&Port_Config);
    }
    else
    {
        Port_Init(&Port_Config_sip);
    }

    Uart_Init();
    Log_Init();
    Pvt_Init(&Pvt_Config);
#ifdef SHELL_ENABLE
      Shell_Init();
#endif

	#if (STD_ON == SOC_ENABLE_SAFETYLIB)
        Safety_FchmRelatedInit();
    #endif
	
    Dma_Init();
    Dio_WriteChannel(DioName_DioChannel_J6E_MCU_GPIO39_DP83TC813R_1_RESET, STD_HIGH);
    LogSync("MCU BUILD:%s\r\n",__DATE__" "__TIME__);
#if (COMPLEXCAN_DEVICE_NUMBER != 0)
    uint8 ret=ComplexCan_OpenDevice();
    LogSync("can1145 init ret=%d\r\n",ret);
#endif

    McuAdc_init();
    I2c_Init(NULL_PTR);
    TCA9539_Port_Init();
    
    hb_CAN2IPC_Init();

    PowerProc_Init();
    PowerProc_SetMode(POWER_UP);
    LowPower_Init();
    VoltM_Init();
    TempM_Init();

    TMP75_OpenDevice(0u);
    TMP75_OpenDevice(1u);
    TMP75_OpenDevice(2u);
    TMP75_OpenDevice(3u);
    Safety_Sip_Check();
    Pmic_Init(NULL);

    NetM_Init();

#ifdef NET_CFG_USE_STBM
    StbM_SynchronizedTimeBaseType timeBaseId = 0;
    StbM_TimeStampType            timeStamp  = {0, 0, 0, 0};
    StbM_UserDataType             usrData    = {0, 0, 0, 0};
    StbM_SetGlobalTime(timeBaseId, &timeStamp, &usrData);
	// Trace_SerialInit();
#endif

    Fls_Init(NULL_PTR);
    OtaFlashIpc_Init();

    // Encryption-related functions, relying on ipc communication with hsm
    // There are sequential requirements, don't move
    Ipc_Init(&Ipc_PrivShmCfgInstance0, IpcConf_Priv_IpcInstance_IpcInstance_0);
    Crypto_Init(NULL_PTR);

    Rtc_Init(NULL_PTR);
    Rtc_PPSOut(0, TRUE);

    Pwm_Init(&Pwm_Config[0]);
    Pwm_SetOutputToIdle(0);//set no output
    fan_init_v();

    // Gpt_StartTimer(GptConf_GptChannelConfiguration_GptChannelConfiguration_6,0xFFFFFFFF);//add for can tsync
    Qga_Init();
    Acore_PowerUpInit();
    /**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << End of runnable implementation >>               DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/
}

/**********************************************************************************************************************
 *
 * Runnable Entity Name: BSW_Runnable_T1
 *
 *---------------------------------------------------------------------------------------------------------------------
 *
 * Executed if at least one of the following trigger conditions occurred:
 *   - triggered on TimingEvent every 1ms
 *
 *********************************************************************************************************************/
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << Start of documentation area >>                  DO NOT CHANGE THIS COMMENT!
 * Symbol: BSW_Runnable_T1_doc
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << End of documentation area >>                    DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/
extern BootStageType BootStage;
FUNC(void, BSW_CODE) BSW_Runnable_T1(void) /* PRQA S 0624, 3206 */ /* MD_Rte_0624, MD_Rte_3206 */
{
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << Start of runnable implementation >>             DO NOT CHANGE THIS COMMENT!
 * Symbol: BSW_Runnable_T1
 *********************************************************************************************************************/
    if(BootStage >= MCUBOOT_INIT)    // Avoid conflicts with loading SPL
    {
        NvM_MainFunction();
        Fee_MainFunction();
        Fls_MainFunction();
    }

    Acore_PowerManager();
    /**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << End of runnable implementation >>               DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/
}

/**********************************************************************************************************************
 *
 * Runnable Entity Name: BSW_Runnable_T10
 *
 *---------------------------------------------------------------------------------------------------------------------
 *
 * Executed if at least one of the following trigger conditions occurred:
 *   - triggered on TimingEvent every 10ms
 *
 *********************************************************************************************************************/
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << Start of documentation area >>                  DO NOT CHANGE THIS COMMENT!
 * Symbol: BSW_Runnable_T10_doc
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << End of documentation area >>                    DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/
FUNC(void, BSW_CODE) BSW_Runnable_T10(void) /* PRQA S 0624, 3206 */ /* MD_Rte_0624, MD_Rte_3206 */
{
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << Start of runnable implementation >>             DO NOT CHANGE THIS COMMENT!
 * Symbol: BSW_Runnable_T10
 *********************************************************************************************************************/
//     raa271005_pmic_main_function_v();
    VoltM_MainFunction();
    TempM_MainFunction();
    NetCom_MainFunction();
    Sys_StateMonitor_MainFunction();
// IoHwAb_WritePin(DioName_DioChannel_J6E_SHUTDOWN_AON_GPIO9,STD_HIGH);
//      /* common */
// #ifdef HAL_USING_TPS62873
//      TPS62873_mainfunction_v();
// #endif
    TimeSync_MainFunction();
#ifdef SHELL_ENABLE
    Shell_Handler();
#endif

	#if (SOC_ENABLE_SAFETYLIB == STD_ON)

    SafetyTask_NeedHighPriority();

    if ((Task_Counter_u32 % 5) == 0)
    {
        SafetyTask_RelativelyLowPriority();
    }

    #endif

    Task_Counter_u32++;
     /**********************************************************************************************************************
      * DO NOT CHANGE THIS COMMENT!           << End of runnable implementation >>               DO NOT CHANGE THIS
      *COMMENT!
      *********************************************************************************************************************/
}

/**********************************************************************************************************************
 *
 * Runnable Entity Name: BSW_Runnable_T100
 *
 *---------------------------------------------------------------------------------------------------------------------
 *
 * Executed if at least one of the following trigger conditions occurred:
 *   - triggered on TimingEvent every 100ms
 *
 *********************************************************************************************************************/
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << Start of documentation area >>                  DO NOT CHANGE THIS COMMENT!
 * Symbol: BSW_Runnable_T100_doc
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << End of documentation area >>                    DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/
// sint16 GssCurrentTemperature[CDD_THS_HW_UNIT_MAX];
// uint8  g_chipName_u8 = 0xFF;
// uint8  g_retVal_enm  = 0xFF;
uint32 counter_100ms = 0;
FUNC(void, BSW_CODE) BSW_Runnable_T100(void) /* PRQA S 0624, 3206 */ /* MD_Rte_0624, MD_Rte_3206 */
{
    /**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << Start of runnable implementation >>             DO NOT CHANGE THIS COMMENT!
 * Symbol: BSW_Runnable_T100
 *********************************************************************************************************************/
    TMP75_MainFunction();
    CoreTemp_MainFunction();
    OtaFlashIpc_OpenInstance();
    OtaFlash_MainFunction();
    fan_main_v();

    if (counter_100ms++ == 50)
    {
        ComM_CommunicationAllowed(ComMConf_ComMChannel_CN_ETH_PUB, TRUE);
        (void)ComM_RequestComMode(ComMConf_ComMUser_CN_ETH_PUB_USER, COMM_FULL_COMMUNICATION);
    }
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << End of runnable implementation >>               DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/
}

/**********************************************************************************************************************
 *
 * Runnable Entity Name: BSW_Runnable_T5
 *
 *---------------------------------------------------------------------------------------------------------------------
 *
 * Executed if at least one of the following trigger conditions occurred:
 *   - triggered on TimingEvent every 5ms
 *
 *********************************************************************************************************************/
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << Start of documentation area >>                  DO NOT CHANGE THIS COMMENT!
 * Symbol: BSW_Runnable_T5_doc
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << End of documentation area >>                    DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/
// extern void TraceTxHandler();
FUNC(void, BSW_CODE) BSW_Runnable_T5(void) /* PRQA S 0624, 3206 */ /* MD_Rte_0624, MD_Rte_3206 */
{
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << Start of runnable implementation >>             DO NOT CHANGE THIS COMMENT!
 * Symbol: BSW_Runnable_T5
 *********************************************************************************************************************/
    HW_VersionCheck();
    hb_CAN2IPC_MainFunction();
#if (COMPLEXCAN_DEVICE_NUMBER != 0)
    ComplexCan_MainFunction();
#endif
    KL15_MainFunction();
    LowPower_MainFunction();
    PowerProc_MainFunction();
//     TCA9539_MainTask();
//     ads7142_mainFunction_v();
// 	//TraceTxHandler();
    Adc_MainFunction();
// #ifdef ZX_VECTOR_USING_HORIZON_JOURNEY_3
//     HorizonSPI_MainFunction();
    // j3_error_pin_monitor_v(); Temporary deactive
// #endif
    if (counter_100ms > 60)
    {
        Eth_RxStatusType rxStatusPtr;
        Eth_Receive(0, 0, &rxStatusPtr);
    }

    /**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << End of runnable implementation >>               DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/
}

/**********************************************************************************************************************
 *
 * Runnable Entity Name: BSW_Runnable_T50
 *
 *---------------------------------------------------------------------------------------------------------------------
 *
 * Executed if at least one of the following trigger conditions occurred:
 *   - triggered on TimingEvent every 50ms
 *
 *********************************************************************************************************************/
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << Start of documentation area >>                  DO NOT CHANGE THIS COMMENT!
 * Symbol: BSW_Runnable_T50_doc
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << End of documentation area >>                    DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/
 extern Std_ReturnType LoadImage_BootSrcIsFlash(void);
FUNC(void, BSW_CODE) BSW_Runnable_T50(void) /* PRQA S 0624, 3206 */ /* MD_Rte_0624, MD_Rte_3206 */
{
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << Start of runnable implementation >>             DO NOT CHANGE THIS COMMENT!
 * Symbol: BSW_Runnable_T50
 *********************************************************************************************************************/
    Rtc_MainLoop();
    if ((PMIC_WDG == TRUE) && (LoadImage_BootSrcIsFlash() == E_OK))
    {
        Wdg_Ext_Trigger();
    }
    
    /**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << End of runnable implementation >>               DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/
}

/**********************************************************************************************************************
 *
 * Runnable Entity Name: BSW_Runnable_ChkHs
 *
 *---------------------------------------------------------------------------------------------------------------------
 *
 * Executed if at least one of the following trigger conditions occurred:
 *   - triggered on TimingEvent every 50ms
 *
 *********************************************************************************************************************/
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << Start of documentation area >>                  DO NOT CHANGE THIS COMMENT!
 * Symbol: BSW_Runnable_ChkHs_doc
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << End of documentation area >>                    DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/
FUNC(void, BSW_CODE) BSW_Runnable_ChkHs(void) /* PRQA S 0624, 3206 */ /* MD_Rte_0624, MD_Rte_3206 */
{
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << Start of runnable implementation >>             DO NOT CHANGE THIS COMMENT!
 * Symbol: BSW_Runnable_ChkHs
 *********************************************************************************************************************/
    Acore_PowerUp();
    if(BOOTSUC == BootStage)
    {
        Camera_PreInit();
        Lpwm_Init(1,0);
        Lpwm_SWTrigger(1);
        Camera_Init();
        IoHwAb_WritePin(DioName_DioChannel_J6_I2C_MUX_SEL,STD_LOW);
        (void)CancelAlarm(Rte_Al_TE_BSW_BSW_Runnable_ChkHs); /* PRQA S 3417 */ /* MD_Rte_Os */
        TerminateTask();
    }
    else
    {
        //do nothing
    }
    /**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << End of runnable implementation >>               DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/
}

/**********************************************************************************************************************
 *
 * Runnable Entity Name: BSW_FastTransfer_T10
 *
 *---------------------------------------------------------------------------------------------------------------------
 *
 * Executed if at least one of the following trigger conditions occurred:
 *   - triggered on TimingEvent every 10ms
 *
 *********************************************************************************************************************/
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << Start of documentation area >>                  DO NOT CHANGE THIS COMMENT!
 * Symbol: BSW_FastTransfer_T10_doc
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << End of documentation area >>                    DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/
extern void Net_FastTransfer_Aeb();
FUNC(void, BSW_CODE) BSW_FastTransfer_T10(void) /* PRQA S 0624, 3206 */ /* MD_Rte_0624, MD_Rte_3206 */
{
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << Start of runnable implementation >>             DO NOT CHANGE THIS COMMENT!
 * Symbol: BSW_FastTransfer_T10
 *********************************************************************************************************************/
#if NET_CFG_USE_FTC
    Net_FastTransfer_Aeb();
#endif

    /**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << End of runnable implementation >>               DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/
}

void TraceTxHandler(void)
{
//   static uint32 m_intlock_state = 0x00000000;
//   SERIAL_ERR err;	

//     uint32 ulCoreID;
//     ulCoreID = __get_CRCoreID();

//     if(CR52_CPU0 == ulCoreID)
//     {
//         __asm volatile("MRS %[result],CPSR" : [result] "=r"(m_intlock_state));
//         __asm volatile("CPSID if");

//         Serial_WrWaitFor(App_SerTraceIF_Nbr, &err);

//         uint32_t fiq_was_masked = m_intlock_state & 0x40;
//         uint32_t irq_was_masked = m_intlock_state & 0x80;

//             if (!fiq_was_masked)
//                 __asm volatile("CPSIE f");
//             if (!irq_was_masked)
//                 __asm volatile("CPSIE i");
//     }
}

#define BSW_STOP_SEC_CODE
#include "BSW_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << Start of function definition area >>            DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << End of function definition area >>              DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << Start of removed code area >>                   DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           << End of removed code area >>                     DO NOT CHANGE THIS COMMENT!
 *********************************************************************************************************************/

/**********************************************************************************************************************
 MISRA 2012 violations and justifications
 *********************************************************************************************************************/

/* module specific MISRA deviations:
   MD_Rte_0624:  MISRA rule: Rule8.3
     Reason:     This MISRA violation is a consequence from the RTE requirements [SWS_Rte_01007] [SWS_Rte_01150].
                 The typedefs are never used in the same context.
     Risk:       No functional risk. Only a cast to uint8* is performed.
     Prevention: Not required.

   MD_Rte_3206:  MISRA rule: Rule2.7
     Reason:     The parameter are not used by the code in all possible code variants.
     Risk:       No functional risk.
     Prevention: Not required.

*/
