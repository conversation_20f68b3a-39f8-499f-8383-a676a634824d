/**
 * @file j6e_GenericTimer.c
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * @version 1.0
 * @date 2024-11-07
 * 
 * Copyright (c) by iMotion AI. All rights reserved.
 * 
 */

#include "arm_cr.h"

#define SYSCNT_BASE (0x23110000U) /**< May vary on different SoCs and needs to be confirmed */
#define SYSCNT ((SYSCNT_Type*)SYSCNT_BASE)
#define SYSCNT_CTRL_OSCON_MASK (0x1U)
#define SYSCNT_CTRL_OSCON_SHIFT (0U)
#define SYSCNT_CTRL_OSCON_WIDTH (1U)
#define SYSCNT_CTRL_OSCON(x) (((uint32_t)(((uint32_t)(x)) << SYSCNT_CTRL_OSCON_SHIFT)) & SYSCNT_CTRL_OSCON_MASK)
#define SYSCNT_CTRL_HALTONDEBUG_MASK (0x2U)
#define SYSCNT_CTRL_HALTONDEBUG_SHIFT (1U)
#define SYSCNT_CTRL_HALTONDEBUG_WIDTH (1U)
#define SYSCNT_CTRL_HALTONDEBUG(x) (((uint32_t)(((uint32_t)(x)) << SYSCNT_CTRL_OSCON_SHIFT)) & SYSCNT_CTRL_OSCON_MASK)
#define SOC_TYPE_SYSCNT_CLK                                                                                            \
    (40000000U) /**< syscnt clk 40M Hz, May vary on different SoCs and needs to be confirmed                           \
                 */
#define SYSCNT_FID0_FREQUENCY SOC_TYPE_SYSCNT_CLK
#define configTICK_RATE_HZ ((1000U)) // every tick is 1ms
#define TICK_INTERVAL ((SYSCNT_FID0_FREQUENCY / configTICK_RATE_HZ) - 1)

#define OS_CORE_ID_MASTER (0U)

/** SYSCNT - Register Layout Typedef */
typedef struct
{
    volatile uint32 SYSCNT_CTRL;   /**< SYSCNT Control Register, offset: 0x0 */
    volatile uint32 SYSCNT_STAT;   /**< Oscillator Status Register, offset: 0x4 */
    volatile uint32 SYSCNT_CNTCVL; /**< Oscillator Status Register, offset: 0x4 */
    volatile uint32 SYSCNT_CNTCVU; /**< Oscillator Status Register, offset: 0x4 */
    uint8           Reserved[16];
    volatile uint32 SYSCNT_CNTFID0;
} SYSCNT_Type;

uint64 SYSTIMER_Read_CNTPCT(void)
{
    uint32 Rd, Rd2;
    uint64 retVal;

    __asm(" mrrc p15, 0, %0, %1, c14 " : "=r"(Rd), "=r"(Rd2) :);
    retVal = ((uint64)Rd2);
    retVal = (retVal << 32);
    retVal |= Rd;
    return retVal;
}

void SYSTIMER_Write_CNTP_TVAL(uint32 value)  
{  
    __asm(" mcr p15, 0, %0, c14, c2, 0" : : "r" (value));  
}

void SYSTIMER_Write_CNTP_CVAL(uint32 val, uint32 val2) 
{ 
	__asm(" mcrr p15, 2, %0, %1, c14 " : : "r"(val), "r"(val2)); 
}

void SYSTIMER_Write_CNTP_CTL(uint32 val) 
{
	 __asm(" mcr p15, 0, %0, c14, c2, 1 " : : "r"(val)); 
}

// Enable the core's generic timer
void InitAndRunGenericTimer(void)
{
    uint32 val, val2;
    uint64 cnt_val;
	
    if (__get_CRCoreID() == OS_CORE_ID_MASTER)
    {
        SYSCNT->SYSCNT_CNTFID0 = SYSCNT_FID0_FREQUENCY;
        SYSCNT->SYSCNT_CTRL |= SYSCNT_CTRL_OSCON_MASK;
    }
    cnt_val = SYSTIMER_Read_CNTPCT();
    cnt_val += (uint64)TICK_INTERVAL;
    val  = cnt_val;
    val2 = (cnt_val >> 32);
    SYSTIMER_Write_CNTP_CVAL(val, val2);
    SYSTIMER_Write_CNTP_CTL(1);
}

// reload compare value
void ReloadGenericTimer(void)
{
    uint64 cnt_val = SYSTIMER_Read_CNTPCT();
    uint32 val, val2;

    cnt_val += (uint64)TICK_INTERVAL;
    val  = cnt_val;
    val2 = (cnt_val >> 32);
    SYSTIMER_Write_CNTP_CVAL(val, val2);
}
