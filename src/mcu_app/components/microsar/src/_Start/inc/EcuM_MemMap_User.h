/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *  Copyright (c) 2017 by Vector Informatik GmbH.                                              All rights reserved.
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -----------------------------------------------------------------------------------------------------------------*/
/**        \file  EcuM_MemMap_User.h
 *        \brief  TODO
 *
 *      \details  TODO
 *
 *  \attention    Please note:
 *                The demo and example programs only show special aspects of the software. With regard to the fact
 *                that these programs are meant for demonstration purposes only, Vector Informatik liability shall be
 *                expressly excluded in cases of ordinary negligence, to the extent admissible by law or statute.
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  AUTHOR IDENTITY
 *  -------------------------------------------------------------------------------------------------------------------
 *  Name                          Initials      Company
 *  -------------------------------------------------------------------------------------------------------------------
 *  TODO                          virrro        Vector Informatik GmbH
 *  -------------------------------------------------------------------------------------------------------------------
 *  REVISION HISTORY
 *  -------------------------------------------------------------------------------------------------------------------
 *  Version   Date        Author  Change Id     Description
 *  -------------------------------------------------------------------------------------------------------------------
 *  01.00.00  YYYY-MM-DD  virrro TODO          TODO
 *********************************************************************************************************************/
#ifdef ECUM_START_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE0_NOCACHE_NOINIT_32BIT
#undef ECUM_START_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE0_NOCACHE_NOINIT_32BIT
#define OS_START_SEC_OsApplication_NonTrusted_Core0_VAR_NOINIT_32BIT
#endif

#ifdef ECUM_STOP_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE0_NOCACHE_NOINIT_32BIT
#undef ECUM_STOP_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE0_NOCACHE_NOINIT_32BIT
#define OS_STOP_SEC_OsApplication_NonTrusted_Core0_VAR_NOINIT_32BIT
#endif

#ifdef ECUM_START_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE1_NOCACHE_NOINIT_32BIT
#undef ECUM_START_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE1_NOCACHE_NOINIT_32BIT
#define OS_START_SEC_OsApplication_NonTrusted_Core1_VAR_NOINIT_32BIT
#endif

#ifdef ECUM_STOP_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE1_NOCACHE_NOINIT_32BIT
#undef ECUM_STOP_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE1_NOCACHE_NOINIT_32BIT
#define OS_STOP_SEC_OsApplication_NonTrusted_Core1_VAR_NOINIT_32BIT
#endif

#ifdef ECUM_START_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE2_NOCACHE_NOINIT_32BIT
#undef ECUM_START_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE2_NOCACHE_NOINIT_32BIT
#define OS_START_SEC_OsApplication_NonTrusted_Core2_VAR_NOINIT_32BIT
#endif

#ifdef ECUM_STOP_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE2_NOCACHE_NOINIT_32BIT
#undef ECUM_STOP_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE2_NOCACHE_NOINIT_32BIT
#define OS_STOP_SEC_OsApplication_NonTrusted_Core2_VAR_NOINIT_32BIT
#endif

#ifdef ECUM_START_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE3_NOCACHE_NOINIT_32BIT
#undef ECUM_START_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE3_NOCACHE_NOINIT_32BIT
#define OS_START_SEC_OsApplication_NonTrusted_Core3_VAR_NOINIT_32BIT
#endif

#ifdef ECUM_STOP_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE3_NOCACHE_NOINIT_32BIT
#undef ECUM_STOP_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE3_NOCACHE_NOINIT_32BIT
#define OS_STOP_SEC_OsApplication_NonTrusted_Core3_VAR_NOINIT_32BIT
#endif

#ifdef ECUM_START_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE4_NOCACHE_NOINIT_32BIT
#undef ECUM_START_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE4_NOCACHE_NOINIT_32BIT
#define OS_START_SEC_OsApplication_NonTrusted_Core4_VAR_NOINIT_32BIT
#endif

#ifdef ECUM_STOP_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE4_NOCACHE_NOINIT_32BIT
#undef ECUM_STOP_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE4_NOCACHE_NOINIT_32BIT
#define OS_STOP_SEC_OsApplication_NonTrusted_Core4_VAR_NOINIT_32BIT
#endif

#ifdef ECUM_START_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE5_NOCACHE_NOINIT_32BIT
#undef ECUM_START_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE5_NOCACHE_NOINIT_32BIT
#define OS_START_SEC_OsApplication_NonTrusted_Core5_VAR_NOINIT_32BIT
#endif

#ifdef ECUM_STOP_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE5_NOCACHE_NOINIT_32BIT
#undef ECUM_STOP_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE5_NOCACHE_NOINIT_32BIT
#define OS_STOP_SEC_OsApplication_NonTrusted_Core5_VAR_NOINIT_32BIT
#endif

#ifdef ECUM_START_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE6_NOCACHE_NOINIT_32BIT
#undef ECUM_START_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE6_NOCACHE_NOINIT_32BIT
#define OS_START_SEC_OsApplication_NonTrusted_Core6_VAR_NOINIT_32BIT
#endif

#ifdef ECUM_STOP_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE6_NOCACHE_NOINIT_32BIT
#undef ECUM_STOP_SEC_VAR_PARTITION_OSAPPLICATION_NONTRUSTED_CORE6_NOCACHE_NOINIT_32BIT
#define OS_STOP_SEC_OsApplication_NonTrusted_Core6_VAR_NOINIT_32BIT
#endif
