
/**********************************************************************************************************************
  COPYRIGHT
-----------------------------------------------------------------------------------------------------------------------
  \par      copyright
  \verbatim
  Copyright (c) 2020 by Vector Informatik GmbH.                                                  All rights reserved.

                This software is copyright protected and proprietary to Vector Informatik GmbH.
                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
                All other rights remain with Vector Informatik GmbH.
  \endverbatim
-----------------------------------------------------------------------------------------------------------------------
  FILE DESCRIPTION
-----------------------------------------------------------------------------------------------------------------------
  \file  File:  ARMBrsHw.h
      Project:  Vector Basic Runtime System
       Module:  BrsHw for all platforms with ARM core Cortex-A and Cortex-R

  \brief Description:  This is a global, platform-independent header file for the ARM-BRS.
                       This file includes all non-platform dependent functions.
                       All the (platform depending) rest needs to be defined in BrsHw.c

  \attention Please note:
    The demo and example programs only show special aspects of the software. With regard to the fact
    that these programs are meant for demonstration purposes only, Vector Informatik liability shall be
    expressly excluded in cases of ordinary negligence, to the extent admissible by law or statute.
**********************************************************************************************************************/

#ifndef _ARMBRSHW_CORTEXA_H_
#define _ARMBRSHW_CORTEXA_H_

/**********************************************************************************************************************
  INCLUDES
**********************************************************************************************************************/
/*
 * Description: The BrsCfg header is used to configure different types of
 *              tests and system setups. Therefore it must be included first
 *              in each BRS and test module.
 *              This file is part of the BRS.
 */
#include "vBrsCfg.h"

// #ifdef __ARM_32BIT_STATE
// #define BRS_COMP_TI
// #define BRS_PLATFORM_ARM
// #endif

/**********************************************************************************************************************
  MODULE VERSION
**********************************************************************************************************************/
/*
 * Description: This is the BrsHw main and bug fix version. The version numbers are BCD coded.
 *              E.g. a main version of 1.23 is coded with 0x0123, a bug fix version of 9 is coded 0x09.
 */
#define ARMBRSHW_CORTEXA_VERSION        0x0101u
#define ARMBRSHW_CORTEXA_BUGFIX_VERSION 0x01u

/**********************************************************************************************************************
  CONFIGURATION CHECK
**********************************************************************************************************************/
/* Configuration checks performed within platform specific code */

/**********************************************************************************************************************
  GLOBAL CONSTANT MACROS
**********************************************************************************************************************/
/*
 * Description: Macro for access to IO addresses
 */
#define BRSHW_IOS(type, address) (*((volatile type *)(address)))

#define BRSHWNOP10() do { \
  __asm(" NOP");          \
  __asm(" NOP");          \
  __asm(" NOP");          \
  __asm(" NOP");          \
  __asm(" NOP");          \
  __asm(" NOP");          \
  __asm(" NOP");          \
  __asm(" NOP");          \
  __asm(" NOP");          \
  __asm(" NOP");          \
  } while(0)

/**********************************************************************************************************************
  Compiler abstraction
**********************************************************************************************************************/
#if defined (BRS_COMP_TI)
  #define ___asm(c)                __asm_(c)
  #define __asm_(c)                __asm(" " #c);
  #define __as1(c, d)              __as1_(c, d)
  #define __as1_(c, d)             __asm(" " #c " , " #d);
  #define __as2(c, d, e)           __as2_(c, d, e)
  #define __as2_(c, d, e)          __asm(" " #c " , " #d " , " #e);
  #define __as3(c, d, e, f)        __as3_(c, d, e, f)
  #define __as3_(c, d, e, f)       __asm(" " #c " , " #d " , " #e " , " #f);
  #define __as4(c, d, e, f, g)     __as4_(c, d, e, f, g)
  #define __as4_(c, d, e, f, g)    __asm(" " #c " , " #d " , " #e " , " #f  " , " #g);
  #define __as5(c, d, e, f, g, h)  __as5_(c, d, e, f, g, h)
  #define __as5_(c, d, e, f, g, h) __asm(" " #c " , " #d " , " #e " , " #f  " , " #g " , " #h);

  #define BRS_MULTILINE_ASM_BEGIN()
  #define BRS_MULTILINE_ASM_END()

  #define BRS_ASM_EQU(Label, Value) __asm(" " #Label " .equ "  #Value);

  #define BRS_ISR_KEYWORD         __interrupt

  #define BRS_SECTION_CODE(c)     __asm(" .sect ." #c);
  #define BRS_GLOBAL(c)           __asm(" .global " #c);
  #define BRS_LABEL(c)            __asm(#c ":");
  #define BRS_LOCAL_LABEL(c)      __asm("$1: .word " #c);
  #define BRS_NEWBLOCK()          __asm(" .newblock"); 
  #define BRS_GLOBAL_END()
  
  #define BRS_IMPORT(Label)         __asm("  .global " #Label); /* Used to explicitly declare Label as defined in another module */
  #define BRS_DEFINE_WORD(Value)    __asm("  .long " #Value);   /* Used to place a 32 bit constant in memory. */
  #define BRS_EXPORT(Label)         __asm("  .def " #Label);    /* Causes the identifier Label to be visible externally */

  #define BRS_IMPORT(Label)         __asm("  .global " #Label); /* Used to explicitly declare Label as defined in another module */
  #define BRS_DEFINE_WORD(Value)    __asm("  .long " #Value);   /* Used to place a 32 bit constant in memory. */
  #define BRS_EXPORT(Label)         __asm("  .def " #Label);    /* Causes the identifier Label to be visible externally */
  
  /* Unconditional branch to c */
  #define BRS_BRANCH(c)           ___asm(B c)
  
  /*! \brief
  */
  # define BRS_ASM_MACRO_WITHOUT_PARAM_BEGIN(MacroName)      __asm(#MacroName " .macro ");
  
  /*! \brief    Directive used to mark the end of a macro
  */
  # define BRS_ASM_MACRO_END                        __asm(" .endm ");

# if defined (BRS_CPU_CORE_CORTEX_M0) || defined (BRS_CPU_CORE_CORTEX_M0PLUS)
  #define BRS_EXTERN_BRANCH(c)    __as1(LDR R1, $1)       \
                                  ___asm(BX R1)           \
                                  __asm("$1: .word " #c); \
                                  __asm(" .newblock");

# else
  #define BRS_EXTERN_BRANCH(c)    ___asm(B c)
# endif

  /* Branch to e if c and d are equal */
  #define BRS_BRANCH_EQUAL(c,d,e)  __as1(CMP   c, d) \
                                   ___asm(BEQ   e)

  /* Branch to e if c and d are NOT equal */
  #define BRS_BRANCH_NOT_EQUAL(c,d,e)  __as1(CMP   c, d) \
                                       ___asm(BNE   e)

  /* Branch to e if c is greater than d */
  #define BRS_BRANCH_GREATER_THAN(c,d,e)  __as1(CMP   c, d) \
                                          ___asm(BGT   e)

#else
  #error "Compiler not yet supported"
#endif /*BRS_COMP_x*/

/**********************************************************************************************************************
  BrsHW configuration
**********************************************************************************************************************/

/**********************************************************************************************************************
  Global variables
**********************************************************************************************************************/

/**********************************************************************************************************************
  Global const variables
**********************************************************************************************************************/

/**********************************************************************************************************************
  Global function prototypes
**********************************************************************************************************************/

/*****************************************************************************/
/**
 * @brief      Disable the global system interrupt.
 * @pre        Must be the first function call in main@BrsMain
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    Function is called from main@BrsMain at power on initialization
 */
/*****************************************************************************/
void BrsHwDisableInterruptAtPowerOn(void);

/*****************************************************************************/
/**
 * @brief      This API is used for the BRS time measurement support to get a
 *             default time value for all measurements with this platform to
 *             be able to compare time measurements on different dates based
 *             on this time result.
 * @pre        Should be called with interrupts global disabled
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    Function is called from e.g. component testsuits for calibration
 */
/*****************************************************************************/
void BrsHwTime100NOP(void);

#endif /*_ARMBRSHW_CORTEXA_H_*/
