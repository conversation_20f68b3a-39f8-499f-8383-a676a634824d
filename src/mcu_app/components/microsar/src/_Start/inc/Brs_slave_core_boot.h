/**
 *  \file   sbl_slave_core_boot.h
 *
 *  \brief  This file contains prototypes of functions for slave core bring up.
 *
 */

/*
 * Copyright (C) 2018-2019 Texas Instruments Incorporated - http://www.ti.com/
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 *
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the
 * distribution.
 *
 * Neither the name of Texas Instruments Incorporated nor the names of
 * its contributors may be used to endorse or promote products derived
 * from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef SBL_SLAVE_CORE_BOOT_H_
#define SBL_SLAVE_CORE_BOOT_H_


#include <ti/csl/soc.h>
#include <ti/csl/csl_vtm.h>
#include <ti/drv/sciclient/sciclient.h>
#include <ti/csl/soc/j721s2/src/cslr_wkup_ctrl_mmr.h>
#include <ti/csl/soc/j721s2/src/cslr_mcu_pll_mmr.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *    SOC core definitions
 */
typedef uint32_t cpu_core_id_t;
#define MPU1_CPU0_ID                    (0U)
#define MPU1_CPU1_ID                    (1U)
#define MPU1_CPU2_ID                    (2U)
#define MPU1_CPU3_ID                    (3U)
#define MPU2_CPU0_ID                    (4U)
#define MPU2_CPU1_ID                    (5U)
#define MPU2_CPU2_ID                    (6U)
#define MPU2_CPU3_ID                    (7U)
#define MCU1_CPU0_ID                    (8U)
#define MCU1_CPU1_ID                    (9U)
#define MCU2_CPU0_ID                    (10U)
#define MCU2_CPU1_ID                    (11U)
#define MCU3_CPU0_ID                    (12U)
#define MCU3_CPU1_ID                    (13U)
#define MCU4_CPU0_ID                    (14U)
#define MCU4_CPU1_ID                    (15U)
#define DSP1_C66X_ID                    (16U)
#define DSP2_C66X_ID                    (17U)
#define DSP1_C7X_ID                     (18U)
#define DSP2_C7X_ID                     (19U)
#define DSP3_C7X_ID                     (20U)
#define DSP4_C7X_ID                     (21U)
#define DSP1_C7X_HOSTEMU_ID             (22U)
#define HSM_CPU_ID                      (23U)
/* Last core in the list of supported cores */
#define SBL_LAST_CORE_ID         (HSM_CPU_ID)
/* add additional MPU/MCU cores before this */
/* only SMP core ID should be after this */
#define MPU1_SMP_ID                     (24U)
#define MPU2_SMP_ID                     (25U)
#define MPU_SMP_ID                      (26U)
#define MCU1_SMP_ID                     (27U)
#define MCU2_SMP_ID                     (28U)
#define MCU3_SMP_ID                     (29U)
#define MCU4_SMP_ID                     (30U)
#define ONLY_LOAD_ID                    (31U)
#define NUM_CORES                       (32U)

#define SBL_DONT_REQUEST_CORE           (0U)
#define SBL_REQUEST_CORE                (1U)

#define SBL_INVALID_ID                  (0x0BAD0000)

#define SBL_INVALID_ENTRY_ADDR ((uint32_t)0xFFFFFFFEU)

#define SBL_MCU_ATCM_BASE      (CSL_MCU_R5FSS0_ATCM_BASE)
#define SBL_MCU_ATCM_SIZE      (CSL_MCU_R5FSS0_ATCM_SIZE)
#define SBL_MCU_BTCM_BASE      (CSL_MCU_R5FSS0_BTCM_BASE)
#define SBL_MCU_BTCM_SIZE      (CSL_MCU_R5FSS0_BTCM_SIZE)

#define SBL_MCU1_CPU0_ATCM_BASE_ADDR_SOC    (CSL_MCU_R5FSS0_CORE0_ATCM_BASE)
#define SBL_MCU1_CPU1_ATCM_BASE_ADDR_SOC    (CSL_MCU_R5FSS0_CORE1_ATCM_BASE)
#define SBL_MCU2_CPU0_ATCM_BASE_ADDR_SOC    (CSL_R5FSS0_CORE0_ATCM_BASE)
#define SBL_MCU2_CPU1_ATCM_BASE_ADDR_SOC    (CSL_R5FSS0_CORE1_ATCM_BASE)
#define SBL_MCU3_CPU0_ATCM_BASE_ADDR_SOC    (CSL_R5FSS1_CORE0_ATCM_BASE)
#define SBL_MCU3_CPU1_ATCM_BASE_ADDR_SOC    (CSL_R5FSS1_CORE1_ATCM_BASE)
#define SBL_MCU4_CPU0_ATCM_BASE_ADDR_SOC    (SBL_INVALID_ID)
#define SBL_MCU4_CPU1_ATCM_BASE_ADDR_SOC    (SBL_INVALID_ID)

#define SBL_MCU1_CPU0_BTCM_BASE_ADDR_SOC    (CSL_MCU_R5FSS0_CORE0_BTCM_BASE)
#define SBL_MCU1_CPU1_BTCM_BASE_ADDR_SOC    (CSL_MCU_R5FSS0_CORE1_BTCM_BASE)
#define SBL_MCU2_CPU0_BTCM_BASE_ADDR_SOC    (CSL_R5FSS0_CORE0_BTCM_BASE)
#define SBL_MCU2_CPU1_BTCM_BASE_ADDR_SOC    (CSL_R5FSS0_CORE1_BTCM_BASE)
#define SBL_MCU3_CPU0_BTCM_BASE_ADDR_SOC    (CSL_R5FSS1_CORE0_BTCM_BASE)
#define SBL_MCU3_CPU1_BTCM_BASE_ADDR_SOC    (CSL_R5FSS1_CORE1_BTCM_BASE)
#define SBL_MCU4_CPU0_BTCM_BASE_ADDR_SOC    (SBL_INVALID_ID)
#define SBL_MCU4_CPU1_BTCM_BASE_ADDR_SOC    (SBL_INVALID_ID)


#define SBL_PROC_ID_MPU1_CPU0       (SCICLIENT_PROC_ID_A72SS0_CORE0)
#define SBL_DEV_ID_MPU1_CPU0        (TISCI_DEV_A72SS0_CORE0)
#define SBL_CLK_ID_MPU1_CPU0        (TISCI_DEV_A72SS0_CORE0_ARM_CLK_CLK)
#define SBL_MPU1_CPU0_FREQ_HZ       (2000000000)

#define SBL_PROC_ID_MPU1_CPU1       (SCICLIENT_PROC_ID_A72SS0_CORE1)
#define SBL_DEV_ID_MPU1_CPU1        (TISCI_DEV_A72SS0_CORE1)
#define SBL_CLK_ID_MPU1_CPU1        (TISCI_DEV_A72SS0_CORE1_ARM_CLK_CLK)
#define SBL_MPU1_CPU1_FREQ_HZ       (2000000000)

#define SBL_PROC_ID_MPU1_CPU2       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU1_CPU2        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU1_CPU2        (SBL_INVALID_ID)
#define SBL_MPU1_CPU2_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU1_CPU3       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU1_CPU3        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU1_CPU3        (SBL_INVALID_ID)
#define SBL_MPU1_CPU3_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU2_CPU0       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU2_CPU0        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU2_CPU0        (SBL_INVALID_ID)
#define SBL_MPU2_CPU0_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU2_CPU1       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU2_CPU1        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU2_CPU1        (SBL_INVALID_ID)
#define SBL_MPU2_CPU1_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU2_CPU2       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU2_CPU2        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU2_CPU2        (SBL_INVALID_ID)
#define SBL_MPU2_CPU2_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU2_CPU3       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU2_CPU3        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU2_CPU3        (SBL_INVALID_ID)
#define SBL_MPU2_CPU3_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MCU1_CPU0       (SCICLIENT_PROC_ID_MCU_R5FSS0_CORE0)
#define SBL_DEV_ID_MCU1_CPU0        (TISCI_DEV_MCU_R5FSS0_CORE0)
#define SBL_CLK_ID_MCU1_CPU0        (TISCI_DEV_MCU_R5FSS0_CORE0_CPU_CLK)
#define SBL_MCU1_CPU0_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU1_CPU1       (SCICLIENT_PROC_ID_MCU_R5FSS0_CORE1)
#define SBL_DEV_ID_MCU1_CPU1        (TISCI_DEV_MCU_R5FSS0_CORE1)
#define SBL_CLK_ID_MCU1_CPU1        (TISCI_DEV_MCU_R5FSS0_CORE1_CPU_CLK)
#define SBL_MCU1_CPU1_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU2_CPU0       (SCICLIENT_PROC_ID_R5FSS0_CORE0)
#define SBL_DEV_ID_MCU2_CPU0        (TISCI_DEV_R5FSS0_CORE0)
#define SBL_CLK_ID_MCU2_CPU0        (TISCI_DEV_R5FSS0_CORE0_CPU_CLK)
#define SBL_MCU2_CPU0_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU2_CPU1       (SCICLIENT_PROC_ID_R5FSS0_CORE1)
#define SBL_DEV_ID_MCU2_CPU1        (TISCI_DEV_R5FSS0_CORE1)
#define SBL_CLK_ID_MCU2_CPU1        (TISCI_DEV_R5FSS0_CORE1_CPU_CLK)
#define SBL_MCU2_CPU1_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU3_CPU0       (SCICLIENT_PROC_ID_R5FSS1_CORE0)
#define SBL_DEV_ID_MCU3_CPU0        (TISCI_DEV_R5FSS1_CORE0)
#define SBL_CLK_ID_MCU3_CPU0        (TISCI_DEV_R5FSS1_CORE0_CPU_CLK)
#define SBL_MCU3_CPU0_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU3_CPU1       (SCICLIENT_PROC_ID_R5FSS1_CORE1)
#define SBL_DEV_ID_MCU3_CPU1        (TISCI_DEV_R5FSS1_CORE1)
#define SBL_CLK_ID_MCU3_CPU1        (TISCI_DEV_R5FSS1_CORE1_CPU_CLK)
#define SBL_MCU3_CPU1_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU4_CPU0       (SBL_INVALID_ID)
#define SBL_DEV_ID_MCU4_CPU0        (SBL_INVALID_ID)
#define SBL_CLK_ID_MCU4_CPU0        (SBL_INVALID_ID)
#define SBL_MCU4_CPU0_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MCU4_CPU1       (SBL_INVALID_ID)
#define SBL_DEV_ID_MCU4_CPU1        (SBL_INVALID_ID)
#define SBL_CLK_ID_MCU4_CPU1        (SBL_INVALID_ID)
#define SBL_MCU4_CPU1_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_DSP1_C66X       (SBL_INVALID_ID)
#define SBL_DEV_ID_DSP1_C66X        (SBL_INVALID_ID)
#define SBL_CLK_ID_DSP1_C66X        (SBL_INVALID_ID)
#define SBL_DSP1_C66X_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_DSP2_C66X       (SBL_INVALID_ID)
#define SBL_DEV_ID_DSP2_C66X        (SBL_INVALID_ID)
#define SBL_CLK_ID_DSP2_C66X        (SBL_INVALID_ID)
#define SBL_DSP2_C66X_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_DSP1_C7X        (SCICLIENT_PROC_ID_COMPUTE_CLUSTER0_C71SS0_0)
#define SBL_DEV_ID_DSP1_C7X         (TISCI_DEV_COMPUTE_CLUSTER0_C71SS0_0)
#define SBL_CLK_ID_DSP1_C7X         (TISCI_DEV_COMPUTE_CLUSTER0_C71SS0_0_C7X_CLK)
#define SBL_DSP1_C7X_FREQ_HZ        (1000000000)

#define SBL_PROC_ID_DSP2_C7X        (SCICLIENT_PROC_ID_COMPUTE_CLUSTER0_C71SS1_0)
#define SBL_DEV_ID_DSP2_C7X         (TISCI_DEV_COMPUTE_CLUSTER0_C71SS1_0)
#define SBL_CLK_ID_DSP2_C7X         (TISCI_DEV_COMPUTE_CLUSTER0_C71SS1_0_C7X_CLK)
#define SBL_DSP2_C7X_FREQ_HZ        (1000000000)

#define SBL_PROC_ID_DSP3_C7X        (SBL_INVALID_ID)
#define SBL_DEV_ID_DSP3_C7X         (SBL_INVALID_ID)
#define SBL_CLK_ID_DSP3_C7X         (SBL_INVALID_ID)
#define SBL_DSP3_C7X_FREQ_HZ        (SBL_INVALID_ID)

#define SBL_PROC_ID_DSP4_C7X        (SBL_INVALID_ID)
#define SBL_DEV_ID_DSP4_C7X         (SBL_INVALID_ID)
#define SBL_CLK_ID_DSP4_C7X         (SBL_INVALID_ID)
#define SBL_DSP4_C7X_FREQ_HZ        (SBL_INVALID_ID)

#define SBL_PROC_ID_HSM_M4          (SCICLIENT_PROC_ID_WKUP_HSM0)
#define SBL_DEV_ID_HSM_M4           (SBL_INVALID_ID)
#define SBL_CLK_ID_HSM_M4           (SBL_INVALID_ID)
#define SBL_HSM_M4_FREQ_HZ          (SBL_INVALID_ID)

#define SBL_HYPERFLASH_BASE_ADDRESS      (CSL_MCU_FSS0_DAT_REG1_BASE)
#define SBL_HYPERFLASH_CTLR_BASE_ADDRESS (CSL_MCU_FSS0_HPB_CTRL_BASE)

/* Structure holding the entry address of the applications for different cores. */
typedef struct sblEntryPoint
{
    /* Holds Entry point of each core. */
    uint32_t    CpuEntryPoint[NUM_CORES];
}sblEntryPoint_t;

/* Structure holding information about a core that is needed to reset it. */
typedef struct
{
    /* Proc id of a core. */
    int32_t    tisci_proc_id;
    /* Device id of a core. */
    int32_t    tisci_dev_id;
    /* Clk id of a core. */
    int32_t    tisci_clk_id;
    /* Startup freq of a core in Hz */
    int32_t    slave_clk_freq_hz;

}sblSlaveCoreInfo_t;

/**
 * \brief    SBL_SlaveCoreBoot function sets the entry point, sets up clocks
 *           and enable to core to start executing from entry point.
 *
 * \param    CoreID = Core ID. Differs depending on SOC, refer to cpu_core_id enum
 *           freqHz = Speed of core at boot up, 0 indicates use SBL default freqs.
 *           pAppEntry = SBL entry point struct
 *           requestCoresFlag = Specify whether cores should be requested/released
 *               from within SBL_SlaveCoreBoot. Accepts the values SBL_REQUEST_CORE
 *               and SBL_DONT_REQUEST_CORE.
 *
 **/
void brs_SlaveCoreMCU21Boot(cpu_core_id_t core_id, uint32_t freqHz, uint32_t AppEntryAddr, uint32_t requestCoresFlag);
uint32_t brs_query_mcu20_wake(void);
void brs_get_core_state(void);

#ifdef __cplusplus
}
#endif

#endif
