/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte_BSW.h
 *           Config:  D:/0_Project/1_iMotion_Driving_Computer/EEZI/idc_mcu_app/cpj_eezi/microsar_cfg/build/SipAddon/Demo.dpa
 *        SW-C Type:  BSW
 *  Generation Time:  2022-04-22 17:45:07
 *
 *        Generator:  MICROSAR RTE Generator Version 4.23.0
 *                    RTE Core Version 1.23.0
 *          License:  CBD2000702
 *
 *      Description:  Application header file for SW-C <BSW> (Contract Phase)
 *********************************************************************************************************************/

/* double include prevention */
#ifndef RTE_BSW_H
# define RTE_BSW_H

# ifndef RTE_CORE
#  ifdef RTE_APPLICATION_HEADER_FILE
#   error Multiple application header files included.
#  endif
#  define RTE_APPLICATION_HEADER_FILE
#  ifndef RTE_PTR2ARRAYBASETYPE_PASSING
#   define RTE_PTR2ARRAYBASETYPE_PASSING
#  endif
# endif

# ifdef __cplusplus
extern "C"
{
# endif /* __cplusplus */

/* include files */

# include "Rte_BSW_Type.h"
# include "Rte_DataHandleType.h"


/**********************************************************************************************************************
 * Component Data Structures and Port Data Structures
 *********************************************************************************************************************/

struct Rte_CDS_BSW
{
  /* dummy entry */
  uint8 _dummy;
};

# define RTE_START_SEC_CONST_UNSPECIFIED
# include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern CONSTP2CONST(struct Rte_CDS_BSW, RTE_CONST, RTE_CONST) Rte_Inst_BSW;

# define RTE_STOP_SEC_CONST_UNSPECIFIED
# include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

typedef P2CONST(struct Rte_CDS_BSW, TYPEDEF, RTE_CONST) Rte_Instance; /* PRQA S 1507 */ /* MD_Rte_1507 */


# define BSW_START_SEC_CODE
# include "BSW_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


/**********************************************************************************************************************
 *
 * Runnable Entity Name: BSW_Init
 *
 *---------------------------------------------------------------------------------------------------------------------
 *
 * Executed once after the RTE is started
 *
 *********************************************************************************************************************/

# define RTE_RUNNABLE_BSW_Init BSW_Init
FUNC(void, BSW_CODE) BSW_Init(void);

/**********************************************************************************************************************
 *
 * Runnable Entity Name: BSW_Runnable_T1
 *
 *---------------------------------------------------------------------------------------------------------------------
 *
 * Executed if at least one of the following trigger conditions occurred:
 *   - triggered on TimingEvent every 1ms
 *
 *********************************************************************************************************************/

# define RTE_RUNNABLE_BSW_Runnable_T1 BSW_Runnable_T1
FUNC(void, BSW_CODE) BSW_Runnable_T1(void);

/**********************************************************************************************************************
 *
 * Runnable Entity Name: BSW_Runnable_T10
 *
 *---------------------------------------------------------------------------------------------------------------------
 *
 * Executed if at least one of the following trigger conditions occurred:
 *   - triggered on TimingEvent every 10ms
 *
 *********************************************************************************************************************/

# define RTE_RUNNABLE_BSW_Runnable_T10 BSW_Runnable_T10
FUNC(void, BSW_CODE) BSW_Runnable_T10(void);

/**********************************************************************************************************************
 *
 * Runnable Entity Name: BSW_Runnable_T100
 *
 *---------------------------------------------------------------------------------------------------------------------
 *
 * Executed if at least one of the following trigger conditions occurred:
 *   - triggered on TimingEvent every 100ms
 *
 *********************************************************************************************************************/

# define RTE_RUNNABLE_BSW_Runnable_T100 BSW_Runnable_T100
FUNC(void, BSW_CODE) BSW_Runnable_T100(void);

/**********************************************************************************************************************
 *
 * Runnable Entity Name: BSW_Runnable_T5
 *
 *---------------------------------------------------------------------------------------------------------------------
 *
 * Executed if at least one of the following trigger conditions occurred:
 *   - triggered on TimingEvent every 5ms
 *
 *********************************************************************************************************************/

# define RTE_RUNNABLE_BSW_Runnable_T5 BSW_Runnable_T5
FUNC(void, BSW_CODE) BSW_Runnable_T5(void);

# define BSW_STOP_SEC_CODE
# include "BSW_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

# ifdef __cplusplus
} /* extern "C" */
# endif /* __cplusplus */

#endif /* RTE_BSW_H */

/**********************************************************************************************************************
 MISRA 2012 violations and justifications
 *********************************************************************************************************************/

/* module specific MISRA deviations:
   MD_Rte_1507:  MISRA rule: Rule5.6
     Reason:     This MISRA violation is a consequence from the RTE requirements [SWS_Rte_01007] [SWS_Rte_01150].
                 The typedefs are never used in the same context.
     Risk:       No functional risk.
     Prevention: Not required.

*/
