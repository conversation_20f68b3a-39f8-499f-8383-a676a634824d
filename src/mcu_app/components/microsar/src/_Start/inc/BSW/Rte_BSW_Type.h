/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte_BSW_Type.h
 *           Config:  D:/0_Project/1_iMotion_Driving_Computer/EEZI/idc_mcu_app/cpj_eezi/microsar_cfg/build/SipAddon/Demo.dpa
 *        SW-C Type:  BSW
 *  Generation Time:  2022-04-22 17:45:07
 *
 *        Generator:  MICROSAR RTE Generator Version 4.23.0
 *                    RTE Core Version 1.23.0
 *          License:  CBD2000702
 *
 *      Description:  Application types header file for SW-C <BSW> (Contract Phase)
 *********************************************************************************************************************/

/* double include prevention */
#ifndef RTE_BSW_TYPE_H
# define RTE_BSW_TYPE_H

# ifdef __cplusplus
extern "C"
{
# endif /* __cplusplus */

# include "Rte_Type.h"


# ifdef __cplusplus
} /* extern "C" */
# endif /* __cplusplus */

#endif /* RTE_BSW_TYPE_H */
