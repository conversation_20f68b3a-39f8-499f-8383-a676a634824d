
/**********************************************************************************************************************
  COPYRIGHT
-----------------------------------------------------------------------------------------------------------------------
  \par      copyright
  \verbatim
  Copyright (c) 2020 by Vector Informatik GmbH.                                                  All rights reserved.

                This software is copyright protected and proprietary to Vector Informatik GmbH.
                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
                All other rights remain with Vector Informatik GmbH.
  \endverbatim
-----------------------------------------------------------------------------------------------------------------------
  FILE DESCRIPTION
-----------------------------------------------------------------------------------------------------------------------
  \file  File:  Appl_SecMod.c
      Project:  Vector Basic Runtime System for MICROSAR4
       Module:  BrsMain

  \brief Description:  Template for SecMod Application callout implementation

  \attention Please note:
    The demo and example programs only show special aspects of the software. With regard to the fact
    that these programs are meant for demonstration purposes only, Vector Informatik liability shall be
    expressly excluded in cases of ordinary negligence, to the extent admissible by law or statute.
**********************************************************************************************************************/

/**********************************************************************************************************************
  AUTHOR IDENTITY
 ----------------------------------------------------------------------------------------------------------------------
  Name                          Initials      Company
  ----------------------------  ------------  -------------------------------------------------------------------------
  Roland Reinl                  virrro        Vector Informatik GmbH
  Benjamin Walter               visbwa        Vector Informatik GmbH
  Sascha Mauser                 vismaa        Vector Informatik GmbH
-----------------------------------------------------------------------------------------------------------------------
  REVISION HISTORY
 ----------------------------------------------------------------------------------------------------------------------
  Version   Date        Author  Description
  --------  ----------  ------  ---------------------------------------------------------------------------------------
  01.00.00  2017-10-18  virrro  Initial creation
  01.00.01  2017-12-06  visbwa  Reworked sample/example code disclaimer
  02.00.00  2018-10-01  visbwa  Changed implementation to work for new MSR4 R21 module vSecPrim (successor of CryptoCv)
  02.00.01  2018-10-04  visbwa  Removed module specific macros to support both
  02.00.02  2020-02-06  vismaa  Added version check for vSecPrim
**********************************************************************************************************************/

/**********************************************************************************************************************
*  EXAMPLE CODE ONLY
*  -------------------------------------------------------------------------------------------------------------------
*  This Example Code is only intended for illustrating an example of a possible BSW integration and BSW configuration.
*  The Example Code has not passed any quality control measures and may be incomplete. The Example Code is neither
*  intended nor qualified for use in series production. The Example Code as well as any of its modifications and/or
*  implementations must be tested with diligent care and must comply with all quality requirements which are necessary
*  according to the state of the art before their use.
*********************************************************************************************************************/

#include "ESLib.h"
#include "ESLib_version.h"

#if (SYSSERVICE_CRYPTOCV_ESLIB_VERSION < 0x0302u)
/* old version */
eslt_ErrorCode esl_getBytesRNG(actU16 target_length, actU8 target)
{
  return ESL_ERC_ERROR;
}

#else
/*New version */
VSECPRIM_FUNC(eslt_ErrorCode) esl_getBytesRNG(
  const eslt_Length targetLength,
  VSECPRIM_P2VAR_PARA(eslt_Byte) target)
{
  return ESL_ERC_ERROR;
}
#endif /*SYSSERVICE_CRYPTOCV_ESLIB_VERSION*/
