/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *  Copyright (c) 2020 by Vector Informatik GmbH.                                              All rights reserved.
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -----------------------------------------------------------------------------------------------------------------*/
/**        \file  Cdd_Com.c
 *        \brief  TODO
 *
 *      \details  TODO
 *
 *  \attention    Please note:
 *                The demo and example programs only show special aspects of the software. With regard to the fact
 *                that these programs are meant for demonstration purposes only, Vector Informatik liability shall be
 *                expressly excluded in cases of ordinary negligence, to the extent admissible by law or statute.
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  AUTHOR IDENTITY
 *  -------------------------------------------------------------------------------------------------------------------
 *  Name                          Initials      Company
 *  -------------------------------------------------------------------------------------------------------------------
 *  TODO                          virrro        Vector Informatik GmbH
 *  -------------------------------------------------------------------------------------------------------------------
 *  REVISION HISTORY
 *  -------------------------------------------------------------------------------------------------------------------
 *  Version   Date        Author  Change Id     Description
 *  -------------------------------------------------------------------------------------------------------------------
 *  01.00.00  YYYY-MM-DD  virrro TODO          TODO
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  INCLUDES
 *********************************************************************************************************************/
#include "PduR.h"
/**********************************************************************************************************************
 *  LOCAL CONSTANT MACROS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL FUNCTION MACROS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL DATA TYPES AND STRUCTURES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL DATA PROTOTYPES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL DATA
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL FUNCTION PROTOTYPES
 *********************************************************************************************************************/
FUNC(void, CDD_COM_CODE) Cdd_Com_RxIndication(PduIdType RxPduId, P2CONST(PduInfoType, AUTOMATIC, CDD_COM_APPL_DATA) PduInfoPtr)
{
}

FUNC(void, CDD_COM_CODE) Cdd_Com_TxConfirmation(PduIdType TxPduId)
{
}

#ifdef CDD_COM_PDUR_UL_COMIF_TRIGGERTRANSMIT
#if (CDD_COM_PDUR_UL_COMIF_TRIGGERTRANSMIT == STD_ON)
FUNC(Std_ReturnType, CDD_COM_CODE) Cdd_Com_TriggerTransmit(PduIdType TxPduId, P2VAR(PduInfoType, AUTOMATIC, CDD_COM_APPL_DATA) PduInfoPtr)
{
  return E_NOT_OK;
}
#endif /* #if (CDD_COM_PDUR_UL_COMIF_TRIGGERTRANSMIT == STD_ON) */
#endif /* #ifdef CDD_COM_PDUR_UL_COMIF_TRIGGERTRANSMIT*/
/**********************************************************************************************************************
 *  END OF FILE: Cdd_Com.c
 *********************************************************************************************************************/
