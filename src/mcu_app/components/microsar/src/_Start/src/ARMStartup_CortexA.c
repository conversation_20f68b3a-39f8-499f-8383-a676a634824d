
/**********************************************************************************************************************
  COPYRIGHT
-----------------------------------------------------------------------------------------------------------------------
  \par      copyright
  \verbatim
  Copyright (c) 2020 by Vector Informatik GmbH.                                                  All rights reserved.

                This software is copyright protected and proprietary to Vector Informatik GmbH.
                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
                All other rights remain with Vector Informatik GmbH.
  \endverbatim
-----------------------------------------------------------------------------------------------------------------------
  FILE DESCRIPTION
-----------------------------------------------------------------------------------------------------------------------
  \file  File:  ARMStartup_CortexA.c
      Project:  Vector Basic Runtime System
       Module:  BrsHw for TI Jacinto7
     Template:  This file is reviewed according to Brs_Template@Implementation[1.00.00]

  \brief Description:  This file contains the assembler part of the BRS StartUpCode.

  \attention Please note:
    The demo and example programs only show special aspects of the software. With regard to the fact
    that these programs are meant for demonstration purposes only, Vector Informatik liability shall be
    expressly excluded in cases of ordinary negligence, to the extent admissible by law or statute.
**********************************************************************************************************************/

/**********************************************************************************************************************
  AUTHOR IDENTITY
 ----------------------------------------------------------------------------------------------------------------------
  Name                          Initials      Company
  ----------------------------  ------------  -------------------------------------------------------------------------
  Benjamin Walter               visbwa        Vector Informatik GmbH
-----------------------------------------------------------------------------------------------------------------------
  REVISION HISTORY
 ----------------------------------------------------------------------------------------------------------------------
  Version   Date        Author  Description
  --------  ----------  ------  ---------------------------------------------------------------------------------------
  01.00.00  2020-07-01  visbwa  Initial version, based on CustomerDE from TI, adapted to fit with vBaseEnv 2.0,
                                usage of single stack for all modes of core0, limited multicore support
            2020-07-03  visbwa  Enabled usage of gCslR5MpuCfg, fixed call of __mpu_init(), disable TCM
**********************************************************************************************************************/

/**********************************************************************************************************************
*  EXAMPLE CODE ONLY
*  -------------------------------------------------------------------------------------------------------------------
*  This Example Code is only intended for illustrating an example of a possible BSW integration and BSW configuration.
*  The Example Code has not passed any quality control measures and may be incomplete. The Example Code is neither
*  intended nor qualified for use in series production. The Example Code as well as any of its modifications and/or
*  implementations must be tested with diligent care and must comply with all quality requirements which are necessary
*  according to the state of the art before their use.
*********************************************************************************************************************/

/**********************************************************************************************************************
  INCLUDES
**********************************************************************************************************************/
#include "BrsHw.h"
#include "BrsMain.h"
#include "BrsMainStartup.h"
#include "vLinkGen_Lcfg.h"

#include <ti/csl/arch/r5/csl_arm_r5_mpu.h>

#if (defined (BRS_CPU_CORE_CORTEX_A5)  || defined (BRS_CPU_CORE_CORTEX_A7)  || defined (BRS_CPU_CORE_CORTEX_A8) || \
     defined (BRS_CPU_CORE_CORTEX_A9)  || defined(BRS_CPU_CORE_CORTEX_A15)  || \
     defined (BRS_CPU_CORE_CORTEX_R4)  || defined (BRS_CPU_CORE_CORTEX_R4F) || defined (BRS_CPU_CORE_CORTEX_R5) || \
     defined (BRS_CPU_CORE_CORTEX_R5F) || defined (BRS_CPU_CORE_CORTEX_R7)  || defined (BRS_CPU_CORE_CORTEX_R52))

/**********************************************************************************************************************
  CONFIGURATION CHECK
**********************************************************************************************************************/
#if defined (BRS_COMP_TI)

#else
  #error "Unknown compiler specified!"
#endif

#if (BRS_CPU_CORE_AMOUNT > 6)
  #error "Only up to 6 cores are supported by this StartupCode yet!"
#endif

#if (VLINKGEN_CFG_MAJOR_VERSION != 2u)
  #error "This StartUpCode is dependent to the vLinkGen version! vLinkGen major version does not fit!"
#else
# if (VLINKGEN_CFG_MINOR_VERSION < 0u)
  #error "This StartUpCode is dependent to the vLinkGen version! Your vLinkGen minor version is too old!"
# endif
#endif

const CSL_ArmR5MpuRegionCfg gCslR5MpuCfg[CSL_ARM_R5F_MPU_REGIONS_MAX] =
{
  {
    /* Region 0 configuration: complete 32 bit address space = 4Gbits */
    .regionId         = 0U,
    .enable           = 1U,
    .baseAddr         = 0x0U,
    .size             = CSL_ARM_R5_MPU_REGION_SIZE_4GB,
    .subRegionEnable  = CSL_ARM_R5_MPU_SUB_REGION_ENABLE_ALL,
    .exeNeverControl  = 1U,
    .accessPermission = CSL_ARM_R5_ACC_PERM_PRIV_USR_RD_WR,
    .shareable        = 0U,
    .cacheable        = (uint32_t)FALSE,
    .cachePolicy      = 0U,
    .memAttr          = 0U,
  },
  {
    /* Region 1 configuration: 128 bytes memory for exception vector execution */
    .regionId         = 1U,
    .enable           = 1U,
    .baseAddr         = 0x0U,
    .size             = CSL_ARM_R5_MPU_REGION_SIZE_128B,
    .subRegionEnable  = CSL_ARM_R5_MPU_SUB_REGION_ENABLE_ALL,
    .exeNeverControl  = 0U,
    .accessPermission = CSL_ARM_R5_ACC_PERM_PRIV_USR_RD_WR,
    .shareable        = 0U,
    .cacheable        = (uint32_t)TRUE,
    .cachePolicy      = CSL_ARM_R5_CACHE_POLICY_WB_WA,
    .memAttr          = 0U,
  },
  {
    /* Region 2 configuration: 512 KB OCMS RAM */
    .regionId         = 2U,
    .enable           = 1U,
    .baseAddr         = 0x41C00000,
    .size             = CSL_ARM_R5_MPU_REGION_SIZE_1MB,
    .subRegionEnable  = CSL_ARM_R5_MPU_SUB_REGION_ENABLE_ALL,
    .exeNeverControl  = 0U,
    .accessPermission = CSL_ARM_R5_ACC_PERM_PRIV_USR_RD_WR,
    .shareable        = 0U,
    .cacheable        = (uint32_t)TRUE,
    .cachePolicy      = CSL_ARM_R5_CACHE_POLICY_WB_WA,
    .memAttr          = 0U,
  },
  {
    /* Region 3 configuration: 2 MB MCMS3 RAM */
    .regionId         = 3U,
    .enable           = 1U,
    .baseAddr         = 0x70000000,
    .size             = CSL_ARM_R5_MPU_REGION_SIZE_8MB,
    .subRegionEnable  = CSL_ARM_R5_MPU_SUB_REGION_ENABLE_ALL,
    .exeNeverControl  = 0U,
    .accessPermission = CSL_ARM_R5_ACC_PERM_PRIV_USR_RD_WR,
    .shareable        = 0U,
    .cacheable        = (uint32_t)TRUE,
    .cachePolicy      = CSL_ARM_R5_CACHE_POLICY_WB_WA,
    .memAttr          = 0U,
  },
  {
    /* Region 4 configuration: 2 GB DDR RAM */
    .regionId         = 4U,
    .enable           = 1U,
    .baseAddr         = 0x80000000,
    .size             = CSL_ARM_R5_MPU_REGION_SIZE_2GB,
    .subRegionEnable  = CSL_ARM_R5_MPU_SUB_REGION_ENABLE_ALL,
    .exeNeverControl  = 0U,
    .accessPermission = CSL_ARM_R5_ACC_PERM_PRIV_USR_RD_WR,
    .shareable        = 0U,
    .cacheable        = (uint32_t)TRUE,
    .cachePolicy      = CSL_ARM_R5_CACHE_POLICY_WB_WA,
    .memAttr          = 0U,
  },
  {
    /* Region 5 configuration: 32 KB BTCM */
    /* Address of ATCM/BTCM are configured via MCU_SEC_MMR registers
       It can either be '0x0' or '0x41010000'. Application/Boot-loader shall
       take care this configurations and linker command file shall be
       in sync with this. For either of the above configurations,
       MPU configurations will not changes as both regions will have same
       set of permissions in almost all scenarios.
       Application can chose to overwrite this MPU configuration if needed.
       The same is true for the region corresponding to ATCM. */
    .regionId         = 5U,
    .enable           = 1U,
    .baseAddr         = 0x41010000,
    .size             = CSL_ARM_R5_MPU_REGION_SIZE_32KB,
    .subRegionEnable  = CSL_ARM_R5_MPU_SUB_REGION_ENABLE_ALL,
    .exeNeverControl  = 0U,
    .accessPermission = CSL_ARM_R5_ACC_PERM_PRIV_USR_RD_WR,
    .shareable        = 0U,
    .cacheable        = (uint32_t)TRUE,
    .cachePolicy      = CSL_ARM_R5_CACHE_POLICY_NON_CACHEABLE,
    .memAttr          = 0U,
  },
  {
    /* Region 6 configuration: 32 KB BTCM */
    .regionId         = 6U,
    .enable           = 1U,
    .baseAddr         = 0x41C4B000,
    .size             = CSL_ARM_R5_MPU_REGION_SIZE_1KB,
    .subRegionEnable  = CSL_ARM_R5_MPU_SUB_REGION_ENABLE_ALL,
    .exeNeverControl  = 0U,
    .accessPermission = CSL_ARM_R5_ACC_PERM_PRIV_USR_RD_WR,
    .shareable        = 1U,
    .cacheable        = (uint32_t)FALSE,
    .cachePolicy      = CSL_ARM_R5_CACHE_POLICY_NON_CACHEABLE,
    .memAttr          = 0U,
  },
  {
    /* Region 7 configuration: 32 KB ATCM */
    .regionId         = 7U,
    .enable           = 1U,
    .baseAddr         = 0x0,
    .size             = CSL_ARM_R5_MPU_REGION_SIZE_32KB,
    .subRegionEnable  = CSL_ARM_R5_MPU_SUB_REGION_ENABLE_ALL,
    .exeNeverControl  = 0U,
    .accessPermission = CSL_ARM_R5_ACC_PERM_PRIV_USR_RD_WR,
    .shareable        = 0U,
    .cacheable        = (uint32_t)TRUE,
    .cachePolicy      = CSL_ARM_R5_CACHE_POLICY_NON_CACHEABLE,
    .memAttr          = 0U,
  },
};

/* =========================================================================== */
/* Definitions                                                                 */
/* =========================================================================== */

/* =========================================================================== */
/*                                                                             */
/* Description: Entry point for all cores                                      */
/*                                                                             */
/* =========================================================================== */


BRS_SECTION_CODE(brsStartup)
/* reset */
 BRS_GLOBAL(brsStartupEntry)
BRS_MULTILINE_ASM_BEGIN()
BRS_LABEL(brsStartupEntry)

 BRS_BRANCH(brsPreAsmStartupHook)
BRS_MULTILINE_ASM_END()
BRS_GLOBAL_END()

/* =========================================================================== */
/*                                                                             */
/* Description: Optional hook for platform specific tasks                      */
/*                                                                             */
/* =========================================================================== */
 BRS_GLOBAL(brsPreAsmStartupHook)
BRS_MULTILINE_ASM_BEGIN()
BRS_LABEL(brsPreAsmStartupHook)
/* Nothing to do here */

 BRS_BRANCH(coreRegisterInit)
BRS_MULTILINE_ASM_END()
BRS_GLOBAL_END()

/* =========================================================================== */
/*                                                                             */
/* Description: Initialize core ID independent core registers                  */
/*                                                                             */
/* =========================================================================== */
 BRS_GLOBAL(coreRegisterInit)
BRS_MULTILINE_ASM_BEGIN()
BRS_LABEL(coreRegisterInit)

#if defined (BRS_COMP_TI)
 BRS_GLOBAL(BrsStartupInstSetInit)
 __as1(MOVW  R0, BrsStartupInstSetInit)
 __as1(MOVT  R0, BrsStartupInstSetInit)
#else
 __as1(LDR  r0, =BrsStartupInstSetInit)
#endif

#if defined (BRS_INSTRUCTION_SET_THUMB)
 __as2(ORR  r0 ,r0, #1)
 ___asm(BX  r0)

#elif defined (BRS_INSTRUCTION_SET_ARM)
 __as2(ORR  r0 ,r0, #0)
 ___asm(BX  r0)
#endif /*BRS_INSTRUCTION_SET_x*/

BRS_LABEL(BrsStartupInstSetInit)

__asm(" MRC      p15,#0x0,r0,c1,c0,#2 ");
__asm(" MOV      r3,#0xf00000 ");
__asm(" ORR      r0,r0,r3 ");
__asm(" MCR      p15,#0x0,r0,c1,c0,#2 ");

__asm(" MOV      r0,#0x40000000 ");
__asm(" FMXR     FPEXC,r0 ");

__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F ");//  ; CLEAR MODES
__asm(" ORR     r0, r0, #0x1F "); // ; SET SYS MODE
__asm(" MSR     cpsr_cf, r0 ");

 BRS_BRANCH(coreRegisterInit2)
BRS_MULTILINE_ASM_END()
BRS_GLOBAL_END()

/* =========================================================================== */
/*                                                                             */
/* Description: Initialize all core ID dependent core registers                */
/*              Configure INTBP and EBASE address.                             */
/*                                                                             */
/* =========================================================================== */
 BRS_GLOBAL(coreRegisterInit2)
BRS_MULTILINE_ASM_BEGIN()
BRS_LABEL(coreRegisterInit2)
/* Nothing to do for Cortex-M */

 BRS_BRANCH(brsStartupZeroInitLoop)
BRS_MULTILINE_ASM_END()
BRS_GLOBAL_END()

/* =========================================================================== */
/*                                                                             */
/* Description: Initialize memory blocks and areas with zero                   */
/*                                                                             */
/* =========================================================================== */
 BRS_GLOBAL(brsStartupZeroInitLoop)
BRS_MULTILINE_ASM_BEGIN()
BRS_LABEL(brsStartupZeroInitLoop)

#if defined (BRS_COMP_TI)
 BRS_READ_COREID(r0, #0)
#else
 BRS_READ_COREID(R0)
#endif

/* Initialize memory sections with zeros */
#if defined (VLINKGEN_CFG_NUM_ZERO_INIT_EARLY_BLOCKS)
# if (VLINKGEN_CFG_NUM_ZERO_INIT_EARLY_BLOCKS>1uL)
 BRS_GLOBAL(vLinkGen_ZeroInit_Early_Blocks)
 __as1(MOVW  R1, vLinkGen_ZeroInit_Early_Blocks)
 __as1(MOVT  R1, vLinkGen_ZeroInit_Early_Blocks)

BRS_LABEL(startup_block_zero_init_start)
 __as1(MOV  R2, R1)

 __as2(ADD  R1, R1, #16)

 __as1(LDR  R3, [R2])     /* vLinkGen_ZeroInit_Early_Blocks->start */
 __as2(LDR  R4, [R2,#4])  /* vLinkGen_ZeroInit_Early_Blocks->end */
 __as2(LDR  R5, [R2, #8]) /* vLinkGen_ZeroInit_Early_Blocks->core */
 /* Alignment parameter actually not used here */

/* Verify if the end of struct vLinkGen_ZeroInit_Early_Blocks is reached, by checking if start == 0, end == 0 and core == 0 */
 __as1(MOV  R6, #0)

 BRS_BRANCH_NOT_EQUAL(R0, R5, startup_block_zero_init_start) /* If InitCore is not running -> go to the next array entry */
 BRS_BRANCH_EQUAL(R3, R4,  startup_block_zero_init_end) /* If Start and End address are equal -> Finished */

BRS_LABEL(startup_block_zero_init_loop_start)
 __as1(STR  R6, [R3]) /* must be an aligned memory access! */

 __as2(ADD  R3, R3, #4)

 BRS_BRANCH_EQUAL(R3, R4, startup_block_zero_init_start) /* If Start is same with End address-> Finished. */

BRS_BRANCH(startup_block_zero_init_loop_start)
BRS_LABEL(startup_block_zero_init_end)
# endif /*VLINKGEN_CFG_NUM_ZERO_INIT_EARLY_BLOCKS>1uL*/

#else
  #error "Mandatory define VLINKGEN_CFG_NUM_ZERO_INIT_EARLY_BLOCKS missing within vLinkGen configuration!"
#endif /*VLINKGEN_CFG_NUM_ZERO_INIT_EARLY_BLOCKS*/

#if defined (VLINKGEN_CFG_NUM_ZERO_INIT_EARLY_GROUPS)
# if (VLINKGEN_CFG_NUM_ZERO_INIT_EARLY_GROUPS>1uL)
#  if defined (BRS_COMP_TI)
 BRS_GLOBAL(vLinkGen_ZeroInit_Early_Groups)
 __as1(MOVW  R1, vLinkGen_ZeroInit_Early_Groups)
 __as1(MOVT  R1, vLinkGen_ZeroInit_Early_Groups)
#  else
 __as1(LDR  R1, =vLinkGen_ZeroInit_Early_Groups)
#  endif

BRS_LABEL(startup_area_zero_init_start)
 __as1(MOV  R2, R1)

 __as2(ADD  R1, R1, #16)

 __as1(LDR  R3, [R2])     /* vLinkGen_ZeroInit_Early_Groups->start */
 __as2(LDR  R4, [R2,#4])  /* vLinkGen_ZeroInit_Early_Groups->end */
 __as2(LDR  R5, [R2, #8]) /* vLinkGen_ZeroInit_Early_Groups->core */
 /* Alignment parameter actually not used here */

/* Verify if the end of struct vLinkGen_ZeroInit_Early_Groups is reached, by checking if start == 0, end == 0 and core == 0 */
 __as1(MOV  R6, #0)

 BRS_BRANCH_NOT_EQUAL(R0, R5, startup_area_zero_init_start) /* If InitCore is not running -> go to the next array entry */
 BRS_BRANCH_EQUAL(R3, R4,  startup_area_zero_init_end) /* If Start and End address are equal -> Finished */

BRS_LABEL(startup_area_zero_init_loop_start)
 __as1(STR  R6, [R3]) /* must be an aligned memory access! */

 __as2(ADD  R3, R3, #4)

 BRS_BRANCH_EQUAL(R3, R4, startup_area_zero_init_start) /* If Start is same with End address-> Finished. */

 BRS_BRANCH(startup_area_zero_init_loop_start)
BRS_LABEL(startup_area_zero_init_end)
# endif /*VLINKGEN_CFG_NUM_ZERO_INIT_EARLY_GROUPS>1uL*/

#else
  #error "Mandatory define VLINKGEN_CFG_NUM_ZERO_INIT_EARLY_GROUPS missing within vLinkGen configuration!"
#endif /*VLINKGEN_CFG_NUM_ZERO_INIT_EARLY_GROUPS*/

/* Branch to the core ID specific stack pointer init routines */
#if defined (BRS_ENABLE_OS_MULTICORESUPPORT)
 BRS_BRANCH_EQUAL(r0,#5,StackPointerInit_c5)
 BRS_BRANCH_EQUAL(r0,#4,StackPointerInit_c4)
 BRS_BRANCH_EQUAL(r0,#3,StackPointerInit_c3)
 BRS_BRANCH_EQUAL(r0,#2,StackPointerInit_c2)
 BRS_BRANCH_EQUAL(r0,#1,StackPointerInit_c1)
 BRS_BRANCH_EQUAL(r0,#0,StackPointerInit_c0)

 /* Still here? ID is not supported yet -> BrsMainExceptionStartup */
 BRS_EXTERN_BRANCH(BrsMainExceptionStartup)
#endif /*BRS_ENABLE_OS_MULTICORESUPPORT*/

/* Jump to Core0 init function for singlecore derivatives */
 BRS_BRANCH(StackPointerInit_c0)
BRS_MULTILINE_ASM_END()
BRS_GLOBAL_END()

#if defined (BRS_ENABLE_OS_MULTICORESUPPORT)
/* =========================================================================== */
/*                                                                             */
/* Description: Initialize all core registers with specific init values        */
/*              (Core 5).                                                      */
/*                                                                             */
/* =========================================================================== */
 BRS_GLOBAL(StackPointerInit_c5)
BRS_LABEL(StackPointerInit_c5)

/* SET TO IRQ MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x12 "); //SET IRQ MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE IRQ MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c5_irqmode_end)
 __as1(MOVW  R0, __section_stack_c5_irqmode_end)
 __as1(MOVT  R0, __section_stack_c5_irqmode_end)
#else
 __as1(LDR  R0, =__section_stack_c5_irqmode_end)
#endif
 __as1(MOV  SP, R0)

/* SET TO FIQ MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x11 "); //SET FIQ MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE FIQ MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c5_fiqmode_end)
 __as1(MOVW  R0, __section_stack_c5_fiqmode_end)
 __as1(MOVT  R0, __section_stack_c5_fiqmode_end)
#else
 __as1(LDR  R0, =__section_stack_c5_fiqmode_end)
#endif
 __as1(MOV  SP, R0)

/* SET TO ABORT` MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x17 "); //SET ABORT MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE ABORT MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c5_abtmode_end)
 __as1(MOVW  R0, __section_stack_c5_abtmode_end)
 __as1(MOVT  R0, __section_stack_c5_abtmode_end)
#else
 __as1(LDR  R0, =__section_stack_c5_abtmode_end)
#endif
 __as1(MOV  SP, R0)
 
/* SET TO UNDEFINED MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x1B "); //SET UNDF MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE UNDEFINED MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c5_undfmode_end)
 __as1(MOVW  R0, __section_stack_c5_undfmode_end)
 __as1(MOVT  R0, __section_stack_c5_undfmode_end)
#else
 __as1(LDR  R0, =__section_stack_c5_undfmode_end)
#endif
 __as1(MOV  SP, R0)
 
/* SET TO SUPERVISOR MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x13 "); //SET SUPERVISOR MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE SUPERVISOR MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c5_spvmode_end)
 __as1(MOVW  R0, __section_stack_c5_spvmode_end)
 __as1(MOVT  R0, __section_stack_c5_spvmode_end)
#else
 __as1(LDR  R0, =__section_stack_c5_spvmode_end)
#endif
 __as1(MOV  SP, R0)

/* SET TO SYSTEM MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x1F "); //SET SYSTEM MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE USER MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c5_sysmode_end)
 __as1(MOVW  R0, __section_stack_c5_sysmode_end)
 __as1(MOVT  R0, __section_stack_c5_sysmode_end)
#else
 __as1(LDR  R0, =__section_stack_c5_sysmode_end)
#endif
 __as1(MOV  SP, R0)

 BRS_BRANCH(coreRegisterInit3)
 
/* =========================================================================== */
/*                                                                             */
/* Description: Initialize all core registers with specific init values        */
/*              (Core 4).                                                      */
/*                                                                             */
/* =========================================================================== */
 BRS_GLOBAL(StackPointerInit_c4)
BRS_LABEL(StackPointerInit_c4)

/* SET TO IRQ MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x12 "); //SET IRQ MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE IRQ MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c4_irqmode_end)
 __as1(MOVW  R0, __section_stack_c4_irqmode_end)
 __as1(MOVT  R0, __section_stack_c4_irqmode_end)
#else
 __as1(LDR  R0, =__section_stack_c4_irqmode_end)
#endif
 __as1(MOV  SP, R0)
 
/* SET TO FIQ MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x11 "); //SET FIQ MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE FIQ MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c4_fiqmode_end)
 __as1(MOVW  R0, __section_stack_c4_fiqmode_end)
 __as1(MOVT  R0, __section_stack_c4_fiqmode_end)
#else
 __as1(LDR  R0, =__section_stack_c4_fiqmode_end)
#endif
 __as1(MOV  SP, R0)

/* SET TO ABORT` MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x17 "); //SET ABORT MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE ABORT MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c4_abtmode_end)
 __as1(MOVW  R0, __section_stack_c4_abtmode_end)
 __as1(MOVT  R0, __section_stack_c4_abtmode_end)
#else
 __as1(LDR  R0, =__section_stack_c4_abtmode_end)
#endif
 __as1(MOV  SP, R0)
 
/* SET TO UNDEFINED MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x1B "); //SET UNDF MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE UNDEFINED MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c4_undfmode_end)
 __as1(MOVW  R0, __section_stack_c4_undfmode_end)
 __as1(MOVT  R0, __section_stack_c4_undfmode_end)
#else
 __as1(LDR  R0, =__section_stack_c4_undfmode_end)
#endif
 __as1(MOV  SP, R0)
 
/* SET TO SUPERVISOR MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x13 "); //SET SUPERVISOR MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE SUPERVISOR MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c4_spvmode_end)
 __as1(MOVW  R0, __section_stack_c4_spvmode_end)
 __as1(MOVT  R0, __section_stack_c4_spvmode_end)
#else
 __as1(LDR  R0, =__section_stack_c4_spvmode_end)
#endif
 __as1(MOV  SP, R0)

/* SET TO SYSTEM MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x1F "); //SET SYSTEM MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE USER MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c4_sysmode_end)
 __as1(MOVW  R0, __section_stack_c4_sysmode_end)
 __as1(MOVT  R0, __section_stack_c4_sysmode_end)
#else
 __as1(LDR  R0, =__section_stack_c4_sysmode_end)
#endif
 __as1(MOV  SP, R0)

 BRS_BRANCH(coreRegisterInit3)

/* =========================================================================== */
/*                                                                             */
/* Description: Initialize all core registers with specific init values        */
/*              (Core 3).                                                      */
/*                                                                             */
/* =========================================================================== */
 BRS_GLOBAL(StackPointerInit_c3)
BRS_LABEL(StackPointerInit_c3)

/* SET TO IRQ MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x12 "); //SET IRQ MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE IRQ MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c3_irqmode_end)
 __as1(MOVW  R0, __section_stack_c3_irqmode_end)
 __as1(MOVT  R0, __section_stack_c3_irqmode_end)
#else
 __as1(LDR  R0, =__section_stack_c3_irqmode_end)
#endif
 __as1(MOV  SP, R0)
 
/* SET TO FIQ MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x11 "); //SET FIQ MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE FIQ MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c3_fiqmode_end)
 __as1(MOVW  R0, __section_stack_c3_fiqmode_end)
 __as1(MOVT  R0, __section_stack_c3_fiqmode_end)
#else
 __as1(LDR  R0, =__section_stack_c3_fiqmode_end)
#endif

/* SET TO ABORT` MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x17 "); //SET ABORT MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE ABORT MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c3_abtmode_end)
 __as1(MOVW  R0, __section_stack_c3_abtmode_end)
 __as1(MOVT  R0, __section_stack_c3_abtmode_end)
#else
 __as1(LDR  R0, =__section_stack_c3_abtmode_end)
#endif
 __as1(MOV  SP, R0)
 
/* SET TO UNDEFINED MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x1B "); //SET UNDF MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE UNDEFINED MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c3_undfmode_end)
 __as1(MOVW  R0, __section_stack_c3_undfmode_end)
 __as1(MOVT  R0, __section_stack_c3_undfmode_end)
#else
 __as1(LDR  R0, =__section_stack_c3_undfmode_end)
#endif
 __as1(MOV  SP, R0)
 
/* SET TO SUPERVISOR MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x13 "); //SET SUPERVISOR MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE SUPERVISOR MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c3_spvmode_end)
 __as1(MOVW  R0, __section_stack_c3_spvmode_end)
 __as1(MOVT  R0, __section_stack_c3_spvmode_end)
#else
 __as1(LDR  R0, =__section_stack_c3_spvmode_end)
#endif
 __as1(MOV  SP, R0)

/* SET TO SYSTEM MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x1F "); //SET SYSTEM MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE USER MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c3_sysmode_end)
 __as1(MOVW  R0, __section_stack_c3_sysmode_end)
 __as1(MOVT  R0, __section_stack_c3_sysmode_end)
#else
 __as1(LDR  R0, =__section_stack_c3_sysmode_end)
#endif
 __as1(MOV  SP, R0)

 BRS_BRANCH(coreRegisterInit3)

/* =========================================================================== */
/*                                                                             */
/* Description: Initialize all core registers with specific init values        */
/*              (Core 2).                                                      */
/*                                                                             */
/* =========================================================================== */
 BRS_GLOBAL(StackPointerInit_c2)
BRS_LABEL(StackPointerInit_c2)

/* SET TO IRQ MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x12 "); //SET IRQ MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE IRQ MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c2_irqmode_end)
 __as1(MOVW  R0, __section_stack_c2_irqmode_end)
 __as1(MOVT  R0, __section_stack_c2_irqmode_end)
#else
 __as1(LDR  R0, =__section_stack_c2_irqmode_end)
#endif
 __as1(MOV  SP, R0)
 
/* SET TO FIQ MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x11 "); //SET FIQ MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE FIQ MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c2_fiqmode_end)
 __as1(MOVW  R0, __section_stack_c2_fiqmode_end)
 __as1(MOVT  R0, __section_stack_c2_fiqmode_end)
#else
 __as1(LDR  R0, =__section_stack_c2_fiqmode_end)
#endif
 __as1(MOV  SP, R0)

/* SET TO ABORT` MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x17 "); //SET ABORT MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE ABORT MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c2_abtmode_end)
 __as1(MOVW  R0, __section_stack_c2_abtmode_end)
 __as1(MOVT  R0, __section_stack_c2_abtmode_end)
#else
 __as1(LDR  R0, =__section_stack_c2_abtmode_end)
#endif
 __as1(MOV  SP, R0)
 
/* SET TO UNDEFINED MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x1B "); //SET UNDF MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE UNDEFINED MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c2_undfmode_end)
 __as1(MOVW  R0, __section_stack_c2_undfmode_end)
 __as1(MOVT  R0, __section_stack_c2_undfmode_end)
#else
 __as1(LDR  R0, =__section_stack_c2_undfmode_end)
#endif
 __as1(MOV  SP, R0)
 
/* SET TO SUPERVISOR MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x13 "); //SET SUPERVISOR MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE SUPERVISOR MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c2_spvmode_end)
 __as1(MOVW  R0, __section_stack_c2_spvmode_end)
 __as1(MOVT  R0, __section_stack_c2_spvmode_end)
#else
 __as1(LDR  R0, =__section_stack_c2_spvmode_end)
#endif
 __as1(MOV  SP, R0)

/* SET TO SYSTEM MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x1F "); //SET SYSTEM MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE USER MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c2_sysmode_end)
 __as1(MOVW  R0, __section_stack_c2_sysmode_end)
 __as1(MOVT  R0, __section_stack_c2_sysmode_end)
#else
 __as1(LDR  R0, =__section_stack_c2_sysmode_end)
#endif
 __as1(MOV  SP, R0)

 BRS_BRANCH(coreRegisterInit3)

/* =========================================================================== */
/*                                                                             */
/* Description: Initialize all core registers with specific init values        */
/*              (Core 1).                                                      */
/*                                                                             */
/* =========================================================================== */
 BRS_GLOBAL(StackPointerInit_c1)
BRS_LABEL(StackPointerInit_c1)

/* SET TO IRQ MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x12 "); //SET IRQ MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE IRQ MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c1_irqmode_end)
 __as1(MOVW  R0, __section_stack_c1_irqmode_end)
 __as1(MOVT  R0, __section_stack_c1_irqmode_end)
#else
 __as1(LDR  R0, =__section_stack_c1_irqmode_end)
#endif
 __as1(MOV  SP, R0)
 
/* SET TO FIQ MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x11 "); //SET FIQ MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE FIQ MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c1_fiqmode_end)
 __as1(MOVW  R0, __section_stack_c1_fiqmode_end)
 __as1(MOVT  R0, __section_stack_c1_fiqmode_end)
#else
 __as1(LDR  R0, =__section_stack_c1_fiqmode_end)
#endif
 __as1(MOV  SP, R0)

/* SET TO ABORT` MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x17 "); //SET ABORT MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE ABORT MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c1_abtmode_end)
 __as1(MOVW  R0, __section_stack_c1_abtmode_end)
 __as1(MOVT  R0, __section_stack_c1_abtmode_end)
#else
 __as1(LDR  R0, =__section_stack_c1_abtmode_end)
#endif
 __as1(MOV  SP, R0)
 
/* SET TO UNDEFINED MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x1B "); //SET UNDF MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE UNDEFINED MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c1_undfmode_end)
 __as1(MOVW  R0, __section_stack_c1_undfmode_end)
 __as1(MOVT  R0, __section_stack_c1_undfmode_end)
#else
 __as1(LDR  R0, =__section_stack_c1_undfmode_end)
#endif
 __as1(MOV  SP, R0)
 
/* SET TO SUPERVISOR MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x13 "); //SET SUPERVISOR MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE SUPERVISOR MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c1_spvmode_end)
 __as1(MOVW  R0, __section_stack_c1_spvmode_end)
 __as1(MOVT  R0, __section_stack_c1_spvmode_end)
#else
 __as1(LDR  R0, =__section_stack_c1_spvmode_end)
#endif
 __as1(MOV  SP, R0)

/* SET TO SYSTEM MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x1F "); //SET SYSTEM MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE USER MODE STACK */
#if defined (BRS_COMP_TI)
 BRS_GLOBAL(__section_stack_c1_sysmode_end)
 __as1(MOVW  R0, __section_stack_c1_sysmode_end)
 __as1(MOVT  R0, __section_stack_c1_sysmode_end)
#else
 __as1(LDR  R0, =__section_stack_c1_sysmode_end)
#endif
 __as1(MOV  SP, R0)

 BRS_BRANCH(coreRegisterInit3)
#endif /*BRS_ENABLE_OS_MULTICORESUPPORT*/

/* =========================================================================== */
/*                                                                             */
/* Description: Initialize all core registers with specific init values        */
/*              (Core 0). Also used for singlecore derivatives                 */
/*                                                                             */
/* =========================================================================== */
 BRS_GLOBAL(StackPointerInit_c0)
BRS_LABEL(StackPointerInit_c0)

/* SET TO IRQ MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x12 "); //SET IRQ MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE IRQ MODE STACK */
 BRS_GLOBAL(__section_stack_c0_end)
 __as1(MOVW  R0, __section_stack_c0_end)
 __as1(MOVT  R0, __section_stack_c0_end)

 __as1(MOV  SP, R0)
 
/* SET TO FIQ MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x11 "); //SET FIQ MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE FIQ MODE STACK */
 BRS_GLOBAL(__section_stack_c0_end)
 __as1(MOVW  R0, __section_stack_c0_end)
 __as1(MOVT  R0, __section_stack_c0_end)

 __as1(MOV  SP, R0)

/* SET TO ABORT` MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x17 "); //SET ABORT MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE ABORT MODE STACK */
 BRS_GLOBAL(__section_stack_c0_end)
 __as1(MOVW  R0, __section_stack_c0_end)
 __as1(MOVT  R0, __section_stack_c0_end)

 __as1(MOV  SP, R0)
 
/* SET TO UNDEFINED MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x1B "); //SET UNDF MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE UNDEFINED MODE STACK */
 BRS_GLOBAL(__section_stack_c0_end)
 __as1(MOVW  R0, __section_stack_c0_end)
 __as1(MOVT  R0, __section_stack_c0_end)

 __as1(MOV  SP, R0)
 
/* SET TO SUPERVISOR MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x13 "); //SET SUPERVISOR MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE SUPERVISOR MODE STACK */
 BRS_GLOBAL(__section_stack_c0_end)
 __as1(MOVW  R0, __section_stack_c0_end)
 __as1(MOVT  R0, __section_stack_c0_end)

 __as1(MOV  SP, R0)

/* SET TO SYSTEM MODE */
__asm(" MRS     r0, cpsr ");
__asm(" BIC     r0, r0, #0x1F "); //CLEAR MODES
__asm(" ORR     r0, r0, #0x1F "); //SET SYSTEM MODE
__asm(" MSR     cpsr_cf, r0 ");

/* INITIALIZE THE USER MODE STACK */
 BRS_GLOBAL(__section_stack_c0_end)
 __as1(MOVW  R0, __section_stack_c0_end)
 __as1(MOVT  R0, __section_stack_c0_end)

 __as1(MOV  SP, R0)

 BRS_BRANCH(coreRegisterInit3)

/* =========================================================================== */
/*                                                                             */
/* Description: Initialize additional core registers                           */
/*                                                                             */
/* =========================================================================== */
 BRS_GLOBAL(coreRegisterInit3)
BRS_LABEL(coreRegisterInit3)

// BRS_GLOBAL(__mpu_init)
 // ___asm(BL __mpu_init)

/* Disable TCM */
 __as1(MOV  R0, #0)
 __asm(" MCR      p15,#0x0,r0,c9,c1,#1 ");

/* =========================================================================== */
/*                                                                             */
/* Description: Jump to Brs_PreMainStartup() (BrsMainStartup.c)                */
/*                                                                             */
/* =========================================================================== */
BRS_GLOBAL(Brs_PreMainStartup)
 BRS_EXTERN_BRANCH(Brs_PreMainStartup)

/* =========================================================================== */
/*                                                                             */
/* Description: Jump to BrsMainExceptionStartup() in case of an unexpected     */
/*              return from PreMain/main                                       */
/*                                                                             */
/* =========================================================================== */
BRS_GLOBAL(BrsMainExceptionStartup)
 BRS_EXTERN_BRANCH(BrsMainExceptionStartup)

#endif /*BRS_CPU_CORE_CORTEX_A/R*/
