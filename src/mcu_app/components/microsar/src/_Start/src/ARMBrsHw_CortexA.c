
/**********************************************************************************************************************
  COPYRIGHT
-----------------------------------------------------------------------------------------------------------------------
  \par      copyright
  \verbatim
  Copyright (c) 2020 by Vector Informatik GmbH.                                                  All rights reserved.

                This software is copyright protected and proprietary to Vector Informatik GmbH.
                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
                All other rights remain with Vector Informatik GmbH.
  \endverbatim
-----------------------------------------------------------------------------------------------------------------------
  FILE DESCRIPTION
-----------------------------------------------------------------------------------------------------------------------
  \file  File:  ARMBrsHw_CortexA.c
      Project:  Vector Basic Runtime System
       Module:  BrsHw for all platforms with ARM core Cortex-A and Cortex-R
     Template:  This file is reviewed according to Brs_Template@Implementation[1.01.01]

  \brief Description:  This is a global, platform-independent file for the ARM-BRS.
                       This file includes all non-platform dependent functions.
                       All the (platform depending) rest needs to be defined in BrsHw.c

  \attention Please note:
    The demo and example programs only show special aspects of the software. With regard to the fact
    that these programs are meant for demonstration purposes only, Vector Informatik liability shall be
    expressly excluded in cases of ordinary negligence, to the extent admissible by law or statute.
**********************************************************************************************************************/

/**********************************************************************************************************************
-----------------------------------------------------------------------------------------------------------------------
  REVISION HISTORY
 ----------------------------------------------------------------------------------------------------------------------
  Version   Date        Author  Description
  --------  ----------  ------  ---------------------------------------------------------------------------------------
  01.00.00  2020-15-19  visbwa  New branch for vBaseEnv 2.0, based on zBrs_ArmCommon ARMBrsHw_CortexA.c 4.07.01
  01.00.01  2020-06-08  visbwa  Fixed Tasking support and vLinkGen_MemArea alignment in StartUpCode.
                                No changes in this file.
  01.00.02  2020-06-09  visbwa  Fixed IAR support in StartUpCode. No changes in this file.
  01.00.03  2020-07-03  visbwa  Added disabling of abort handler and VIC for TDA4VM88@TI
  01.01.00  2020-09-18  visbwa  Review according to Brs_Template 1.01.00
  01.01.01  2020-10-21  vishci  Fixed BRS_ISR_KEYWORD of Arm5, Gnu and Iar compiler abstraction in ARMBrsHw_CortexA.h,
                                update to Brs_Template 1.01.01, removed AUTHOR IDENTITY
**********************************************************************************************************************/

/**********************************************************************************************************************
*  EXAMPLE CODE ONLY
*  -------------------------------------------------------------------------------------------------------------------
*  This Example Code is only intended for illustrating an example of a possible BSW integration and BSW configuration.
*  The Example Code has not passed any quality control measures and may be incomplete. The Example Code is neither
*  intended nor qualified for use in series production. The Example Code as well as any of its modifications and/or
*  implementations must be tested with diligent care and must comply with all quality requirements which are necessary
*  according to the state of the art before their use.
*********************************************************************************************************************/

/**********************************************************************************************************************
  INCLUDES
**********************************************************************************************************************/
#include "BrsHw.h"

#if !defined (BRS_ENABLE_FBL_SUPPORT)
  #include "Os.h"
#endif

/**********************************************************************************************************************
  VERSION CHECK
**********************************************************************************************************************/
#if (ARMBRSHW_CORTEXA_VERSION != 0x0101u)
  #error "Header and source file are inconsistent!"
#endif
#if (ARMBRSHW_CORTEXA_BUGFIX_VERSION != 0x01u)
  #error "Different versions of bugfix in Header and Source used!"
#endif

/**********************************************************************************************************************
  CONFIGURATION CHECK
**********************************************************************************************************************/
#if defined (BRS_COMP_TI)      

#else
  #error "Unknown compiler specified!"
#endif

/**********************************************************************************************************************
  DEFINITION + MACROS
**********************************************************************************************************************/


/**********************************************************************************************************************
  GLOBAL VARIABLES
**********************************************************************************************************************/

/**********************************************************************************************************************
  GLOBAL CONST VARIABLES
**********************************************************************************************************************/

/**********************************************************************************************************************
  LOCAL VARIABLES AND LOCAL HW REGISTERS
**********************************************************************************************************************/

/**********************************************************************************************************************
  CONTROLLER CONFIGURATION REGISTERS
**********************************************************************************************************************/

/**********************************************************************************************************************
  LOCAL VARIABLES
**********************************************************************************************************************/

/**********************************************************************************************************************
  LOCAL CONST VARIABLES
**********************************************************************************************************************/

/**********************************************************************************************************************
  PROTOTYPES OF LOCAL FUNCTIONS
**********************************************************************************************************************/

/**********************************************************************************************************************
  FUNCTION DEFINITIONS
**********************************************************************************************************************/

/*****************************************************************************/
/**
 * @brief      Disable the global system interrupt.
 * @pre        Must be the first function call in main@BrsMain
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    Function is called from main@BrsMain at power on initialization
 */
/*****************************************************************************/
void BrsHwDisableInterruptAtPowerOn(void)
{
#if defined (BRS_COMP_TI)
  asm(" MRS R0, CPSR ");       /* Read CPSR Register */
  asm(" ORR R0, R0, #0x040 "); /* Set Asynchronous FIQ Mask bit */
  asm(" ORR R0, R0, #0x080 "); /* Set Asynchronous IRQ Mask bit */
  asm(" MSR CPSR_c, R0 ");     /* Write CPSR Register (only bits [7:0]) */

# if defined (BRS_DERIVATIVE_TDA4VM88)
  /* Disable Data Abort Handler */
  asm(" MRS R0, CPSR ");       /* Read CPSR Register */
  asm(" ORR R0, R0, #0x100 "); /* Set Asynchronous Abort Mask bit */
  asm(" MSR CPSR_x, R0 ");     /* Write CPSR Register (only bits [15:8]) */

  /* Disable VIC */
  asm(" MRS R0, CPSR ");
  asm(" ORR R0, R0, #0x1f ");
  asm(" MSR CPSR_cxsf, R0 ");

  asm(" MRC p15, #0, r1, c1, c0, #0 ");
  asm(" BIC  r1, r1, #(1<<24) ");
  // asm(" BIC  r1, r1, #(0<<24) ");
  asm(" MCR  p15, #0, r1, c1, c0, #0 ");
# endif /*BRS_DERIVATIVE_TDA4VM88*/
#endif /*BRS_COMP_TI*/


}

/*****************************************************************************/
/**
 * @brief      This API is used for the BRS time measurement support to get a
 *             default time value for all measurements with this platform to
 *             be able to compare time measurements on different dates based
 *             on this time result.
 * @pre        Should be called with interrupts global disabled
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    Function is called from e.g. component testsuits for calibration
 */
/*****************************************************************************/
void BrsHwTime100NOP(void)
{
  BRSHWNOP10();
  BRSHWNOP10();
  BRSHWNOP10();
  BRSHWNOP10();
  BRSHWNOP10();
  BRSHWNOP10();
  BRSHWNOP10();
  BRSHWNOP10();
  BRSHWNOP10();
  BRSHWNOP10();
}
