/**
 *  \file   sbl_slave_core_boot.c
 *
 *  \brief  This file contain functions related to slave core boot-up.
 *
 */

/*
 * Copyright (C) 2018-2022 Texas Instruments Incorporated - http://www.ti.com/
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 *
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the
 * distribution.
 *
 * Neither the name of Texas Instruments Incorporated nor the names of
 * its contributors may be used to endorse or promote products derived
 * from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/* ========================================================================== */
/*                             Include Files                                  */
/* ========================================================================== */

 #include <stdint.h>
 #include <string.h>
 #include <ti/csl/csl_types.h>
 #include <ti/csl/cslr_device.h>
 #include <ti/csl/hw_types.h>
 #include <ti/csl/arch/csl_arch.h>
 #include <ti/drv/uart/UART_stdio.h>
#include "Brs_slave_core_boot.h"


/* Don't forget to update parameter OPP of the AVS   */
/* setup function in SBL_SocLateInit if the CPU freq */
/* are changed to a higher or lower operating  point */
static const sblSlaveCoreInfo_t sbl_slave_core_info[] =
{
    /* MPU1_CPU0 info */
    {
    SBL_PROC_ID_MPU1_CPU0,
    SBL_DEV_ID_MPU1_CPU0,
    SBL_CLK_ID_MPU1_CPU0,
    SBL_MPU1_CPU0_FREQ_HZ,
    },
    /* MPU1_CPU1 info */
    {
    SBL_PROC_ID_MPU1_CPU1,
    SBL_DEV_ID_MPU1_CPU1,
    SBL_CLK_ID_MPU1_CPU1,
    SBL_MPU1_CPU1_FREQ_HZ,
    },
    /* MPU1_CPU2 info */
    {
    SBL_PROC_ID_MPU1_CPU2,
    SBL_DEV_ID_MPU1_CPU2,
    SBL_CLK_ID_MPU1_CPU2,
    SBL_MPU1_CPU2_FREQ_HZ,
    },
    /* MPU1_CPU3 info */
    {
    SBL_PROC_ID_MPU1_CPU3,
    SBL_DEV_ID_MPU1_CPU3,
    SBL_CLK_ID_MPU1_CPU3,
    SBL_MPU1_CPU3_FREQ_HZ,
    },
    /* MPU2_CPU0 info */
    {
    SBL_PROC_ID_MPU2_CPU0,
    SBL_DEV_ID_MPU2_CPU0,
    SBL_CLK_ID_MPU2_CPU0,
    SBL_MPU2_CPU0_FREQ_HZ,
    },
    /* MPU2_CPU1 info */
    {
    SBL_PROC_ID_MPU2_CPU1,
    SBL_DEV_ID_MPU2_CPU1,
    SBL_CLK_ID_MPU2_CPU1,
    SBL_MPU2_CPU1_FREQ_HZ,
    },
    /* MPU2_CPU2 info */
    {
    SBL_PROC_ID_MPU2_CPU2,
    SBL_DEV_ID_MPU2_CPU2,
    SBL_CLK_ID_MPU2_CPU2,
    SBL_MPU2_CPU2_FREQ_HZ,
    },
    /* MPU2_CPU3 info */
    {
    SBL_PROC_ID_MPU2_CPU3,
    SBL_DEV_ID_MPU2_CPU3,
    SBL_CLK_ID_MPU2_CPU3,
    SBL_MPU2_CPU3_FREQ_HZ,
    },
    /* MCU1_CPU0 info */
    {
    SBL_PROC_ID_MCU1_CPU0,
    SBL_DEV_ID_MCU1_CPU0,
    SBL_CLK_ID_MCU1_CPU0,
    SBL_MCU1_CPU0_FREQ_HZ,
    },
    /* MCU1_CPU1 info */
    {
    SBL_PROC_ID_MCU1_CPU1,
    SBL_DEV_ID_MCU1_CPU1,
    SBL_CLK_ID_MCU1_CPU1,
    SBL_MCU1_CPU1_FREQ_HZ,
    },
    /* MCU2_CPU0 info */
    {
    SBL_PROC_ID_MCU2_CPU0,
    SBL_DEV_ID_MCU2_CPU0,
    SBL_CLK_ID_MCU2_CPU0,
    SBL_MCU2_CPU0_FREQ_HZ,
    },
    /* MCU2_CPU1 info */
    {
    SBL_PROC_ID_MCU2_CPU1,
    SBL_DEV_ID_MCU2_CPU1,
    SBL_CLK_ID_MCU2_CPU1,
    SBL_MCU2_CPU1_FREQ_HZ,
    },
    /* MCU3_CPU0 info */
    {
    SBL_PROC_ID_MCU3_CPU0,
    SBL_DEV_ID_MCU3_CPU0,
    SBL_CLK_ID_MCU3_CPU0,
    SBL_MCU3_CPU0_FREQ_HZ,
    },
    /* MCU3_CPU1 info */
    {
    SBL_PROC_ID_MCU3_CPU1,
    SBL_DEV_ID_MCU3_CPU1,
    SBL_CLK_ID_MCU3_CPU1,
    SBL_MCU3_CPU1_FREQ_HZ,
    },
    /* MCU4_CPU0 info */
    {
    SBL_PROC_ID_MCU4_CPU0,
    SBL_DEV_ID_MCU4_CPU0,
    SBL_CLK_ID_MCU4_CPU0,
    SBL_MCU4_CPU0_FREQ_HZ,
    },
    /* MCU4_CPU1 info */
    {
    SBL_PROC_ID_MCU4_CPU1,
    SBL_DEV_ID_MCU4_CPU1,
    SBL_CLK_ID_MCU4_CPU1,
    SBL_MCU4_CPU1_FREQ_HZ,
    },
    /* DSP1_C66X info */
    {
    SBL_PROC_ID_DSP1_C66X,
    SBL_DEV_ID_DSP1_C66X,
    SBL_CLK_ID_DSP1_C66X,
    SBL_DSP1_C66X_FREQ_HZ,
    },
    /* DSP2_C66X info */
    {
    SBL_PROC_ID_DSP2_C66X,
    SBL_DEV_ID_DSP2_C66X,
    SBL_CLK_ID_DSP2_C66X,
    SBL_DSP2_C66X_FREQ_HZ,
    },
    /* DSP1_C7X info */
    {
    SBL_PROC_ID_DSP1_C7X,
    SBL_DEV_ID_DSP1_C7X,
    SBL_CLK_ID_DSP1_C7X,
    SBL_DSP1_C7X_FREQ_HZ,
    },
    /* DSP2_C7X info */
    {
    SBL_PROC_ID_DSP2_C7X,
    SBL_DEV_ID_DSP2_C7X,
    SBL_CLK_ID_DSP2_C7X,
    SBL_DSP2_C7X_FREQ_HZ,
    },
    /* DSP3_C7X info */
    {
    SBL_PROC_ID_DSP3_C7X,
    SBL_DEV_ID_DSP3_C7X,
    SBL_CLK_ID_DSP3_C7X,
    SBL_DSP3_C7X_FREQ_HZ,
    },
    /* DSP4_C7X info */
    {
    SBL_PROC_ID_DSP4_C7X,
    SBL_DEV_ID_DSP4_C7X,
    SBL_CLK_ID_DSP4_C7X,
    SBL_DSP4_C7X_FREQ_HZ,
    },
    /* DSP1_C7X HOST_EMU (invalid) */
    {
    SBL_INVALID_ID,
    SBL_INVALID_ID,
    SBL_INVALID_ID,
    SBL_INVALID_ID,
    },
    /* HSM M4F */
    {
    SBL_PROC_ID_HSM_M4,
    SBL_DEV_ID_HSM_M4,
    SBL_CLK_ID_HSM_M4,
    SBL_HSM_M4_FREQ_HZ
    }
};

static const uint32_t SblAtcmAddr[] =
{
SBL_MCU_ATCM_BASE,
SBL_MCU1_CPU1_ATCM_BASE_ADDR_SOC,
SBL_MCU2_CPU0_ATCM_BASE_ADDR_SOC,
SBL_MCU2_CPU1_ATCM_BASE_ADDR_SOC,
SBL_MCU3_CPU0_ATCM_BASE_ADDR_SOC,
SBL_MCU3_CPU1_ATCM_BASE_ADDR_SOC,
SBL_MCU4_CPU0_ATCM_BASE_ADDR_SOC,
SBL_MCU4_CPU1_ATCM_BASE_ADDR_SOC
};

static const uint32_t SblBtcmAddr[] =
{
SBL_MCU_BTCM_BASE,
SBL_MCU1_CPU1_BTCM_BASE_ADDR_SOC,
SBL_MCU2_CPU0_BTCM_BASE_ADDR_SOC,
SBL_MCU2_CPU1_BTCM_BASE_ADDR_SOC,
SBL_MCU3_CPU0_BTCM_BASE_ADDR_SOC,
SBL_MCU3_CPU1_BTCM_BASE_ADDR_SOC,
SBL_MCU4_CPU0_BTCM_BASE_ADDR_SOC,
SBL_MCU4_CPU1_BTCM_BASE_ADDR_SOC
};
extern void uart_mcu10_printf_v(const int8_t* p_string_ps8, ...);
/* ========================================================================== */
/*                           Internal Functions                               */
/* ========================================================================== */
static void brs_RequestCore(cpu_core_id_t core_id)
{
    int32_t proc_id = sbl_slave_core_info[core_id].tisci_proc_id;
    int32_t status = CSL_EFAIL;

    if (proc_id != SBL_INVALID_ID)
    {
        // uart_mcu10_printf_v( "Calling Sciclient_procBootRequestProcessor, ProcId 0x%x... \n", proc_id);

        status = Sciclient_procBootRequestProcessor(proc_id, SCICLIENT_SERVICE_WAIT_FOREVER);
        if (status != CSL_PASS)
        {
            // uart_mcu10_printf_v("Sciclient_procBootRequestProcessor, ProcId 0x%x...FAILED \n", proc_id);
            // SblErrLoop(__FILE__, __LINE__);
        }
    }

    return;
}


static void brs_ReleaseCore (cpu_core_id_t core_id, uint32_t reqFlag)
{
    int32_t proc_id = sbl_slave_core_info[core_id].tisci_proc_id;
    int32_t status = CSL_EFAIL;

    if(proc_id != SBL_INVALID_ID)
    {
        // uart_mcu10_printf_v("Sciclient_procBootReleaseProcessor, ProcId 0x%x...\n", proc_id);
        status = Sciclient_procBootReleaseProcessor(proc_id, reqFlag, SCICLIENT_SERVICE_WAIT_FOREVER);

        if (status != CSL_PASS)
        {
            // uart_mcu10_printf_v("Sciclient_procBootReleaseProcessor, ProcId 0x%x...FAILED \n", proc_id);
            // SblErrLoop(__FILE__, __LINE__);
        }
    }
    return;
}

/**
 * \brief    SBL_SlaveCoreBoot function sets the entry point, sets up clocks
 *           and enable to core to start executing from entry point.
 *
 * \param    core_id = Selects a core on the SOC, refer to cpu_core_id_t enum
 *           freqHz = Speed of core at boot up, 0 indicates use SBL default freqs.
 *           pAppEntry = SBL entry point struct
 *           requestCoresFlag = Specify whether cores should be requested/released
 *               from within SBL_SlaveCoreBoot. Accepts the values SBL_REQUEST_CORE
 *               and SBL_DONT_REQUEST_CORE.
 *
 **/

void brs_SlaveCoreMCU21Boot(cpu_core_id_t core_id, uint32_t freqHz, uint32_t AppEntryAddr, uint32_t requestCoresFlag)
{
    int32_t status = CSL_EFAIL;
    struct tisci_msg_proc_set_config_req  proc_set_config_req;
    const sblSlaveCoreInfo_t *sblSlaveCoreInfoPtr = &(sbl_slave_core_info[core_id]);
    brs_RequestCore(core_id);

    /* Set entry point as boot vector */
    proc_set_config_req.processor_id = sblSlaveCoreInfoPtr->tisci_proc_id;
    proc_set_config_req.bootvector_lo = 0x00000000;
    proc_set_config_req.bootvector_hi = 0x0;
    proc_set_config_req.config_flags_1_set = 0;
    proc_set_config_req.config_flags_1_clear = 0;

    if (AppEntryAddr <  SBL_INVALID_ENTRY_ADDR) /* Set entry point only is valid */
    {
        // uart_mcu10_printf_v( "Sciclient_procBootSetProcessorCfg, ProcId 0x%x, EntryPoint 0x%x...\n", proc_set_config_req.processor_id, proc_set_config_req.bootvector_lo);
        status =  Sciclient_procBootSetProcessorCfg(&proc_set_config_req,  SCICLIENT_SERVICE_WAIT_FOREVER);
        if (status != CSL_PASS)
        {
            // uart_mcu10_printf_v( "Sciclient_procBootSetProcessorCfg...FAILED \n");
            // SblErrLoop(__FILE__, __LINE__);
        }

        if (sblSlaveCoreInfoPtr->tisci_dev_id != SBL_INVALID_ID)
        {
           //uart_mcu10_printf_v("Sciclient_pmSetModuleClkFreq, DevId 0x%x @ %dHz... \n", sblSlaveCoreInfoPtr->tisci_dev_id, sblSlaveCoreInfoPtr->slave_clk_freq_hz);
            Sciclient_pmSetModuleClkFreq(sblSlaveCoreInfoPtr->tisci_dev_id,
                                         sblSlaveCoreInfoPtr->tisci_clk_id,
                                         sblSlaveCoreInfoPtr->slave_clk_freq_hz,
                                         TISCI_MSG_FLAG_AOP,
                                         SCICLIENT_SERVICE_WAIT_FOREVER);
        }
    }
    else
    {
        // uart_mcu10_printf_v("Skipping Sciclient_procBootSetProcessorCfg for ProcId 0x%x, EntryPoint 0x%x...\n", proc_set_config_req.processor_id, proc_set_config_req.bootvector_lo);
    }
    /* Power down and then power up each core*/
    
    if (AppEntryAddr <  SBL_INVALID_ENTRY_ADDR)
    {
        // /* Skip copy if R5 app entry point is already 0 */
        if (AppEntryAddr)
        {
            // uart_mcu10_printf_v("Copying first 128 bytes from app to MCU ATCM @ 0x%x for core %d\n", SblAtcmAddr[core_id - MCU1_CPU0_ID], core_id);
            // memcpy(((void *)(SblAtcmAddr[core_id - MCU1_CPU0_ID])), (void *)(proc_set_config_req.bootvector_lo), 128);
            memcpy(((void *)(SblAtcmAddr[core_id - MCU1_CPU0_ID])), (void *)(AppEntryAddr), 0x8000);
        }
        // uart_mcu10_printf_v("Clearing HALT for ProcId 0x%x...\n", sblSlaveCoreInfoPtr->tisci_proc_id);
        status =  Sciclient_procBootSetSequenceCtrl(sblSlaveCoreInfoPtr->tisci_proc_id, 0, TISCI_MSG_VAL_PROC_BOOT_CTRL_FLAG_R5_CORE_HALT, TISCI_MSG_FLAG_AOP, SCICLIENT_SERVICE_WAIT_FOREVER);
        if (status != CSL_PASS)
        {
            // uart_mcu10_printf_v("Sciclient_procBootSetSequenceCtrl...FAILED \n");
        }
    }

  
    brs_ReleaseCore(core_id, TISCI_MSG_FLAG_AOP);
}


uint32_t brs_query_mcu20_wake(void)
{
    int32_t status = CSL_EFAIL;
    uint32_t ret = 0;
    const sblSlaveCoreInfo_t *sblSlaveCoreInfoPtr;
    struct tisci_msg_proc_get_status_resp cpuStatus;
    uint32_t moduleState0;
    uint32_t resetState0;
    uint32_t contextLossState0;
    
    sblSlaveCoreInfoPtr = &(sbl_slave_core_info[MCU2_CPU0_ID]);

    status = Sciclient_pmGetModuleState(sblSlaveCoreInfoPtr->tisci_proc_id, &moduleState0, &resetState0, &contextLossState0, SCICLIENT_SERVICE_WAIT_FOREVER);
    if (status == CSL_PASS)
    {
        // uart_mcu10_printf_v( "mcu2_0 Sciclient_pmGetModuleState, moduleState0: 0x%x   resetState0: 0x%x  contextLossState0: 0x%x\n", moduleState0, resetState0 ,contextLossState0);
    
       if((moduleState0 ==1) && (resetState0 ==0))
       {
            status = Sciclient_procBootGetProcessorState(sblSlaveCoreInfoPtr->tisci_proc_id, &cpuStatus, SCICLIENT_SERVICE_WAIT_FOREVER);

            if (status == CSL_PASS)
            {
                // uart_mcu10_printf_v("cpuStatus.status_flags_1 0x%x cpuStatus.control_flags_1 0x%x\n",cpuStatus.status_flags_1, cpuStatus.control_flags_1);
                if ((cpuStatus.control_flags_1 & 0x00000001U) == 0)
                {
                    ret = 1; 
                }
                
            }
       }
        
    }
    // uart_mcu10_printf_v("SBL_query_mcu20_wake %d... \n", ret);
    return ret;
}



void brs_get_core_state(void)
{
    struct tisci_msg_proc_get_status_resp cpuStatus1;
    struct tisci_msg_proc_get_status_resp cpuStatus2;

    uint32_t moduleState0;
    uint32_t resetState0;
    uint32_t contextLossState0;

    uint32_t moduleState1;
    uint32_t resetState1;
    uint32_t contextLossState1;
    int32_t status = CSL_EFAIL;

    // uart_mcu10_printf_v("Calling SBL_get_core_state, mcu2_0 \n");
    status = Sciclient_procBootGetProcessorState(0x06, &cpuStatus1, SCICLIENT_SERVICE_WAIT_FOREVER);
    if (status != CSL_PASS)
    {
        uart_mcu10_printf_v("SBL_get_core_state mcu2_0...FAILED \n");
    }
    else
    {
        uart_mcu10_printf_v( "mcu2_0 status_flags_1: 0x%x, control_flags_1: 0x%x  config_flags_1: 0x%x \n", cpuStatus1.status_flags_1, cpuStatus1.control_flags_1,cpuStatus1.config_flags_1);
    }
    // uart_mcu10_printf_v("Calling SBL_get_core_state, mcu2_1 \n");
    status = Sciclient_procBootGetProcessorState(0x07, &cpuStatus2, SCICLIENT_SERVICE_WAIT_FOREVER);
    if (status != CSL_PASS)
    {
        uart_mcu10_printf_v("SBL_get_core_state mcu2_1...FAILED \n");
    }
    else
    {
        uart_mcu10_printf_v( "mcu2_1 status_flags_1: 0x%x, control_flags_1: 0x%x   config_flags_1: 0x%x\n", cpuStatus2.status_flags_1, cpuStatus2.control_flags_1,cpuStatus2.config_flags_1);
    }

    status = Sciclient_pmGetModuleState(SBL_DEV_ID_MCU2_CPU0, &moduleState0, &resetState0, &contextLossState0, SCICLIENT_SERVICE_WAIT_FOREVER);
    if (status != CSL_PASS)
    {
        uart_mcu10_printf_v("Sciclient_pmGetModuleState mcu2_0...FAILED \n");
    }
    else
    {
        uart_mcu10_printf_v( "mcu2_0 Sciclient_pmGetModuleState, moduleState0: 0x%x   resetState0: 0x%x  contextLossState0: 0x%x\n", moduleState0, resetState0 ,contextLossState0);
    }

    status = Sciclient_pmGetModuleState(SBL_DEV_ID_MCU2_CPU1, &moduleState1, &resetState1, &contextLossState1, SCICLIENT_SERVICE_WAIT_FOREVER);
    if (status != CSL_PASS)
    {
        uart_mcu10_printf_v("Sciclient_pmGetModuleState mcu2_1...FAILED \n");
    }
    else
    {
        uart_mcu10_printf_v( "mcu2_1 Sciclient_pmGetModuleState moduleState1: 0x%x   resetState1: 0x%x  contextLossState1: 0x%x\n", moduleState1, resetState1 ,contextLossState1);
    }
    
}
