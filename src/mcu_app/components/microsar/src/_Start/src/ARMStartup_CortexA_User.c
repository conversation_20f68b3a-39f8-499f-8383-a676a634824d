
/**********************************************************************************************************************
  COPYRIGHT
-----------------------------------------------------------------------------------------------------------------------
  \par      copyright
  \verbatim
  Copyright (c) 2020 by Vector Informatik GmbH.                                                  All rights reserved.

                This software is copyright protected and proprietary to Vector Informatik GmbH.
                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
                All other rights remain with Vector Informatik GmbH.
  \endverbatim
-----------------------------------------------------------------------------------------------------------------------
  FILE DESCRIPTION
-----------------------------------------------------------------------------------------------------------------------
  \file  File:  ARMStartup_CortexA.c
      Project:  Vector Basic Runtime System
       Module:  BrsHw for TI Jacinto7
     Template:  This file is reviewed according to Brs_Template@Implementation[1.00.00]

  \brief Description:  This file contains the assembler part of the BRS StartUpCode.

  \attention Please note:
    The demo and example programs only show special aspects of the software. With regard to the fact
    that these programs are meant for demonstration purposes only, Vector Informatik liability shall be
    expressly excluded in cases of ordinary negligence, to the extent admissible by law or statute.
**********************************************************************************************************************/

/**********************************************************************************************************************
  AUTHOR IDENTITY
 ----------------------------------------------------------------------------------------------------------------------
  Name                          Initials      Company
  ----------------------------  ------------  -------------------------------------------------------------------------
  Benjamin Walter               visbwa        Vector Informatik GmbH
-----------------------------------------------------------------------------------------------------------------------
  REVISION HISTORY
 ----------------------------------------------------------------------------------------------------------------------
  Version   Date        Author  Description
  --------  ----------  ------  ---------------------------------------------------------------------------------------
  01.00.00  2020-07-01  visbwa  Initial version, based on CustomerDE from TI, adapted to fit with vBaseEnv 2.0,
                                usage of single stack for all modes of core0, limited multicore support
            2020-07-03  visbwa  Enabled usage of gCslR5MpuCfg, fixed call of __mpu_init(), disable TCM
**********************************************************************************************************************/

/**********************************************************************************************************************
*  EXAMPLE CODE ONLY
*  -------------------------------------------------------------------------------------------------------------------
*  This Example Code is only intended for illustrating an example of a possible BSW integration and BSW configuration.
*  The Example Code has not passed any quality control measures and may be incomplete. The Example Code is neither
*  intended nor qualified for use in series production. The Example Code as well as any of its modifications and/or
*  implementations must be tested with diligent care and must comply with all quality requirements which are necessary
*  according to the state of the art before their use.
*********************************************************************************************************************/

/**********************************************************************************************************************
  INCLUDES
**********************************************************************************************************************/
#include "BrsHw.h"
#include "BrsMain.h"
#include "BrsMainStartup.h"
#include "vLinkGen_Lcfg.h"

#if (defined (BRS_CPU_CORE_CORTEX_A5)  || defined (BRS_CPU_CORE_CORTEX_A7)  || defined (BRS_CPU_CORE_CORTEX_A8) || \
     defined (BRS_CPU_CORE_CORTEX_A9)  || defined(BRS_CPU_CORE_CORTEX_A15)  || \
     defined (BRS_CPU_CORE_CORTEX_R4)  || defined (BRS_CPU_CORE_CORTEX_R4F) || defined (BRS_CPU_CORE_CORTEX_R5) || \
     defined (BRS_CPU_CORE_CORTEX_R5F) || defined (BRS_CPU_CORE_CORTEX_R7)  || defined (BRS_CPU_CORE_CORTEX_R52))

/**********************************************************************************************************************
  CONFIGURATION CHECK
**********************************************************************************************************************/
#if defined (BRS_COMP_TI)

#else
  #error "Unknown compiler specified!"
#endif

#if (BRS_CPU_CORE_AMOUNT > 6)
  #error "Only up to 6 cores are supported by this StartupCode yet!"
#endif

#if (VLINKGEN_CFG_MAJOR_VERSION != 2u)
  #error "This StartUpCode is dependent to the vLinkGen version! vLinkGen major version does not fit!"
#else
# if (VLINKGEN_CFG_MINOR_VERSION < 0u)
  #error "This StartUpCode is dependent to the vLinkGen version! Your vLinkGen minor version is too old!"
# endif
#endif


/* =========================================================================== */
/* Definitions                                                                 */
/* =========================================================================== */
interrupt void brsStartup_Isr_NoReturn(void);
void brsStartup_Isr_NoReturn2(void) __attribute__((used));

/* =========================================================================== */
/*                                                                             */
/* Description: Entry point for all cores                                      */
/*                                                                             */
/* =========================================================================== */
#pragma CODE_SECTION(brsStartup_Isr_NoReturn, "UserBrsStartup")
interrupt void brsStartup_Isr_NoReturn(void)
{
    // __asm(" mov     pc, #brsStartupEntry ");
    Brs_PreMainStartup();
}

void brsStartup_Isr_NoReturn2(void)
{
    // __asm(" mov     pc, #brsStartupEntry ");
    Brs_PreMainStartup();
}

#endif /*BRS_CPU_CORE_CORTEX_A/R*/
