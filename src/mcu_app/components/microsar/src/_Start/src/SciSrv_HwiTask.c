#include "Rte_Type.h"
#include "Os_Lcfg.h"
#include "Os.h"

#include "Rte_CtApSciserverLow.h"
#include "Os_Types_Lcfg.h"

#include "ti/drv/sciclient/sciserver.h"
#if defined(SOC_J721E)
#include "ti/drv/sciclient/src/sciserver/j721e/sciserver_hwiData.h"
#elif defined(SOC_J721S2)
#include "ti/drv/sciclient/src/sciserver/j721s2/sciserver_hwiData.h"
#endif

static const Sciserver_taskData* utdHigh          = NULL;
static volatile int        highIsrEnableVal = 0;

static const Sciserver_taskData* utdLow          = NULL;
static volatile int        lowIsrEnableVal = 0;

#define USER_MCU_NAV_HIGH (0)
#define USER_MAIN_NAV_HIGH (1)
#define USER_MCU_NAV_LOW (2)
#define USER_MAIN_NAV_LOW (3)

FUNC(void, OS_SCISERVER_TIRTOSUSERMSGHWIFXN1_CODE) Os_Isr_Sciserver_tirtosUserMsgHwiFxn1(void)
{
    const Sciserver_hwiData* uhd        = &sciserver_hwi_list[USER_MCU_NAV_HIGH];
    int32_t            ret        = CSL_PASS;
    bool               soft_error = false;

    /* TI RTOS: Osal_DisableInterrupt(0, (int32_t) uhd->irq_num); */
    *(volatile unsigned int*)(0x40F80000 + 0x400 + (CSLR_MCU_R5FSS0_CORE0_INTR_MCU_NAVSS0_INTR_ROUTER_0_OUTL_INTR_6 / 32) * 0x20 + 0x0C) =
        0x40;
    ret = Sciserver_interruptHandler(uhd, &soft_error);
    if ((ret != CSL_PASS) && (soft_error == true))
    {
        /* TI RTOS: Osal_EnableInterrupt(0, (int32_t) uhd->irq_num); */
        *(volatile unsigned int*)(0x40F80000 + 0x400 + (CSLR_MCU_R5FSS0_CORE0_INTR_MCU_NAVSS0_INTR_ROUTER_0_OUTL_INTR_6 / 32) * 0x20 + 0x08) =
            0x40;
    }
    else
    {
        /* TI RTOS: (void) SemaphoreP_post(gSciserverUserSemHandles[uhd->semaphore_id]); */
        (void)SetEvent(
            SciServerHighOsTask,
            Rte_Ev_Run_CtApSciserverHigh_CtApSciserverHighRunnable_SciserverTrigger_UserHi_Trigger);
    }
    /* TI RTOS: Osal_ClearInterrupt(0, (int32_t) uhd->irq_num); */
    *(volatile unsigned int*)(0x40F80000 + 0x400 + (CSLR_MCU_R5FSS0_CORE0_INTR_MCU_NAVSS0_INTR_ROUTER_0_OUTL_INTR_6 / 32) * 0x20 + 0x04) =
        0x40;
}

FUNC(void, OS_SCISERVER_TIRTOSUSERMSGHWIFXN1_CODE) Os_Isr_Sciserver_tirtosUserMsgHwiFxn2(void)
{
    const Sciserver_hwiData* uhd        = NULL;
    int32_t            ret        = CSL_PASS;
    bool               soft_error = false;

    uhd = &sciserver_hwi_list[USER_MAIN_NAV_HIGH];

    /* TI RTOS: Osal_DisableInterrupt(0, (int32_t) uhd->irq_num); */
    *(volatile unsigned int*)(0x40F80000 + 0x400 + (CSLR_MCU_R5FSS0_CORE0_INTR_MCU_NAVSS0_INTR_ROUTER_0_OUTL_INTR_7 / 32) * 0x20 + 0x0C) =
        0x80;

    ret = Sciserver_interruptHandler(uhd, &soft_error);

    if ((ret != CSL_PASS) && (soft_error == 1))
    {
        /* TI RTOS: Osal_EnableInterrupt(0, (int32_t) uhd->irq_num); */
        *(volatile unsigned int*)(0x40F80000 + 0x400 + (CSLR_MCU_R5FSS0_CORE0_INTR_MCU_NAVSS0_INTR_ROUTER_0_OUTL_INTR_7 / 32) * 0x20 + 0x08) =
            0x80;
    }
    else
    {
        /* TI RTOS: (void) SemaphoreP_post(gSciserverUserSemHandles[uhd->semaphore_id]); */
        (void)SetEvent(
            SciServerHighOsTask,
            Rte_Ev_Run_CtApSciserverHigh_CtApSciserverHighRunnable_SciserverTrigger_UserHi_Trigger);
    }

    /* TI RTOS: Osal_ClearInterrupt(0, (int32_t) uhd->irq_num); */
    *(volatile unsigned int*)(0x40F80000 + 0x400 + (CSLR_MCU_R5FSS0_CORE0_INTR_MCU_NAVSS0_INTR_ROUTER_0_OUTL_INTR_7 / 32) * 0x20 + 0x04) =
        0x80;
}

FUNC(void, OS_SCISERVER_TIRTOSUSERMSGHWIFXN1_CODE) Os_Isr_Sciserver_tirtosUserMsgHwiFxn3(void)
{
    const Sciserver_hwiData* uhd = &sciserver_hwi_list[USER_MCU_NAV_LOW];

    int32_t ret        = CSL_PASS;
    bool    soft_error = false;

    /* TI RTOS: Osal_DisableInterrupt(0, (int32_t) uhd->irq_num); */
    *(volatile unsigned int*)(0x40F80000 + 0x400 + (CSLR_MCU_R5FSS0_CORE0_INTR_MCU_NAVSS0_INTR_ROUTER_0_OUTL_INTR_8 / 32) * 0x20 + 0x0C) =
        0x100;
    ret = Sciserver_interruptHandler(uhd, &soft_error);
    if ((ret != CSL_PASS) && (soft_error == 1))
    {
        /* TI RTOS: Osal_EnableInterrupt(0, (int32_t) uhd->irq_num); */
        *(volatile unsigned int*)(0x40F80000 + 0x400 + (CSLR_MCU_R5FSS0_CORE0_INTR_MCU_NAVSS0_INTR_ROUTER_0_OUTL_INTR_8 / 32) * 0x20 + 0x08) =
            0x100;
    }
    else
    {
        /* TI RTOS: (void) SemaphoreP_post(gSciserverUserSemHandles[uhd->semaphore_id]); */
        (void)SetEvent(
            SciServerLowOsTask, Rte_Ev_Run_CtApSciserverLow_CtApSciserverLowRunnable_SciserverTrigger_UserLo_Trigger);
    }

    /* TI RTOS: Osal_ClearInterrupt(0, (int32_t) uhd->irq_num); */
    *(volatile unsigned int*)(0x40F80000 + 0x400 + (CSLR_MCU_R5FSS0_CORE0_INTR_MCU_NAVSS0_INTR_ROUTER_0_OUTL_INTR_8 / 32) * 0x20 + 0x04) =
        0x100;
}

FUNC(void, OS_SCISERVER_TIRTOSUSERMSGHWIFXN1_CODE) Os_Isr_Sciserver_tirtosUserMsgHwiFxn4(void)
{
    const Sciserver_hwiData* uhd        = &sciserver_hwi_list[USER_MAIN_NAV_LOW];
    int32_t            ret        = CSL_PASS;
    bool               soft_error = false;

    /* TI RTOS: Osal_DisableInterrupt(0, (int32_t) uhd->irq_num); */
    *(volatile unsigned int*)(0x40F80000 + 0x400 + (CSLR_MCU_R5FSS0_CORE0_INTR_MCU_NAVSS0_INTR_ROUTER_0_OUTL_INTR_9 / 32) * 0x20 + 0x0C) =
        0x200;

    ret = Sciserver_interruptHandler(uhd, &soft_error);

    if ((ret != CSL_PASS) && (soft_error == 1))
    {
        /* TI RTOS: Osal_EnableInterrupt(0, (int32_t) uhd->irq_num); */
        *(volatile unsigned int*)(0x40F80000 + 0x400 + (CSLR_MCU_R5FSS0_CORE0_INTR_MCU_NAVSS0_INTR_ROUTER_0_OUTL_INTR_9 / 32) * 0x20 + 0x08) =
            0x200;
    }
    else
    {
        /* TI RTOS: (void) SemaphoreP_post(gSciserverUserSemHandles[uhd->semaphore_id]); */
        (void)SetEvent(
            SciServerLowOsTask, Rte_Ev_Run_CtApSciserverLow_CtApSciserverLowRunnable_SciserverTrigger_UserLo_Trigger);
    }

    /* TI RTOS: Osal_ClearInterrupt(0, (int32_t) uhd->irq_num); */
    *(volatile unsigned int*)(0x40F80000 + 0x400 + (CSLR_MCU_R5FSS0_CORE0_INTR_MCU_NAVSS0_INTR_ROUTER_0_OUTL_INTR_9 / 32) * 0x20 + 0x04) =
        0x200;
}

FUNC(void, CtApSciserverLow_CODE) CtApSciserverLow_Init(void) /* PRQA S 0624, 3206 */ /* MD_Rte_0624, MD_Rte_3206 */
{

    utdLow = &gSciserverTaskList[SCISERVER_TASK_USER_LO];
    /* Set the pending State first */
    utdLow->state->state = SCISERVER_TASK_PENDING;
}

FUNC(void, CtApSciserverLow_CODE) CtApSciserverLowRunnable(void) /* PRQA S 0624, 3206 */ /* MD_Rte_0624, MD_Rte_3206 */
{
    sint32 ret;

    GetResource(OsResource_SciserverSync);

    ret = Sciserver_processtask(utdLow);
    if (ret != CSL_PASS)
    {
        /* Failed to process message and failed to send nak response */
        /* TI-RTOS: BIOS_exit(0); */
        ReleaseResource(OsResource_SciserverSync);
        (void)TerminateTask();
    }
    else
    {
        /* TI-RTOS:
        Osal_EnableInterrupt(0, sciserver_hwi_list[2U * utd->task_id +
        utd->state->current_buffer_idx].irq_num); */
        lowIsrEnableVal =
            1 << ((sciserver_hwi_list[2U * utdLow->task_id + utdLow->state->current_buffer_idx].irq_num) % 32);
        *(volatile unsigned int*)(0x40F80000 + 0x400 + (CSLR_MCU_R5FSS0_CORE0_INTR_MCU_NAVSS0_INTR_ROUTER_0_OUTL_INTR_8 / 32) * 0x20 + 0x08) =
            lowIsrEnableVal;
    }

    ReleaseResource(OsResource_SciserverSync);
}

FUNC(void, CtApSciserverHigh_CODE) CtApSciserverHigh_Init(void) /* PRQA S 0624, 3206 */ /* MD_Rte_0624, MD_Rte_3206 */
{

    utdHigh = &gSciserverTaskList[SCISERVER_TASK_USER_HI];
    /* Set the pending State first */
    utdHigh->state->state = SCISERVER_TASK_PENDING;
}

FUNC(void, CtApSciserverHigh_CODE)
CtApSciserverHighRunnable(void) /* PRQA S 0624, 3206 */ /* MD_Rte_0624, MD_Rte_3206 */
{
    sint32 ret;

    GetResource(OsResource_SciserverSync);

    ret = Sciserver_processtask(utdHigh);
    if (ret != CSL_PASS)
    {
        /* Failed to process message and failed to send nak response */
        /* TI-RTOS: BIOS_exit(0); */
        ReleaseResource(OsResource_SciserverSync);
        (void)TerminateTask();
    }
    else
    {
        /* TI-RTOS:
        Osal_EnableInterrupt(0, sciserver_hwi_list[2U * utd->task_id +
        utd->state->current_buffer_idx].irq_num); */
        highIsrEnableVal =
            1 << ((sciserver_hwi_list[2U * utdHigh->task_id + utdHigh->state->current_buffer_idx].irq_num) % 32);
        *(volatile unsigned int*)(0x40F80000 + 0x400 + (CSLR_MCU_R5FSS0_CORE0_INTR_MCU_NAVSS0_INTR_ROUTER_0_OUTL_INTR_7 / 32) * 0x20 + 0x08) =
            highIsrEnableVal;
    }
    ReleaseResource(OsResource_SciserverSync);
}
