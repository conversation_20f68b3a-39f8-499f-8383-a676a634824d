//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
//  Copyright (c) by iMotion AI. All rights reserved.
//=============================================================================
//  D E S C R I P T I O N
//-----------------------------------------------------------------------------
//  Project Name: imo_com
//  Target System(s):
//  Compiler(s):
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//  Name: xiang.li
//=============================================================================
#ifndef IMO_COM_DDK_RECEIVERPORT_HPP_
#define IMO_COM_DDK_RECEIVERPORT_HPP_
#include "ddk_receiverportBase.hpp"

namespace imo_com
{
namespace ddk
{
template <typename PortDataType>
class TNewestReceiverPort : public TExtensionCircBuffLock<PortDataType, 1U>
{
  public:
    typedef TExtensionCircBuffLock<PortDataType, 1U>     Base_t;
    typedef const PortDataContainer<const PortDataType*> PortDataContainer_t;

    TNewestReceiverPort() : Base_t()
    {
        // Set subscriber type
        Base_t::setSubscriberType(ESubscriberType::NEWEST);
    }

    /// @brief update data container
    inline void update()
    {
        Base_t::fillDataContainer();
        Base_t::update();
    }

    /// @brief cleanup data container
    inline void cleanup()
    {
        // since the semantic is NEWEST the current data is released and will not
        // be available next time.
        Base_t::returnCurrentPortData();
        Base_t::cleanup();
        Base_t::fillDataContainer();
    }

    /// @brief release all received portdata
    inline void releasePortData()
    {
        Base_t::fillDataContainer();
        Base_t::returnCurrentPortData();
        Base_t::releasePortData();
    }

    PortDataContainer_t getData() const
    {
        return PortDataContainer_t(static_cast<const PortDataType*>(Base_t::getDataBase()));
    }
};

template <typename PortDataType>
class TLatestReceiverPort : public TExtensionCircBuffLock<PortDataType, 1U>
{
  public:
    typedef TExtensionCircBuffLock<PortDataType, 1U>     Base_t;
    typedef const PortDataContainer<const PortDataType*> PortDataContainer_t;

    TLatestReceiverPort() : Base_t()
    {
        // Set subscriber type
        Base_t::setSubscriberType(ESubscriberType::LATEST);
    }

    /// @brief update data container
    inline void update()
    {
        Base_t::fillDataContainer();
        Base_t::update();
    }

    /// @brief cleanup data container
    inline void cleanup()
    {
        Base_t::cleanup();
        Base_t::fillDataContainer();
    }

    /// @brief release all received portdata
    inline void releasePortData()
    {
        Base_t::fillDataContainer();
        Base_t::returnCurrentPortData();
        Base_t::releasePortData();
    }

    PortDataContainer_t getData() const
    {
        return PortDataContainer_t(static_cast<const PortDataType*>(Base_t::getDataBase()));
    }
};

template <typename PortDataType, uint32_t DataContainerSize>
class TNewXReceiverPort : public TExtensionCircBuffLock<PortDataType, DataContainerSize>
{
  public:
    typedef TExtensionCircBuffLock<PortDataType, DataContainerSize>      Base_t;
    typedef TFixedCircularBuffer<const PortDataType*, DataContainerSize> PortDataContainer_t;

    TNewXReceiverPort() : Base_t()
    {
        // Set subscriber type
        Base_t::setSubscriberType(ESubscriberType::NEWX);
    }

    /// @brief update data container
    inline void update()
    {
        Base_t::fillDataContainer();
        Base_t::update();
    }

    /// @brief cleanup after processing
    inline void cleanup()
    {
        // since the semantic is NEWX the current data is released and will not
        // be available next time.
        Base_t::returnCurrentPortData();
        Base_t::cleanup();
        // fetch concurrently delivered items and store them in the data container
        // to force early release of the oldest data items
        Base_t::fillDataContainer();
    }

    /// @brief release all received portdata
    inline void releasePortData()
    {
        // purge entries from assign container copy to data container
        Base_t::fillDataContainer();
        // release all entries from data container
        Base_t::returnCurrentPortData();
        // delegate remaining tasks to mixin
        Base_t::releasePortData();
    }

    /// @brief return the current data
    PortDataContainer_t& getData() { return *reinterpret_cast<PortDataContainer_t*>(&Base_t::getDataContainer()); }
};

template <typename PortDataType, uint32_t DataContainerSize>
class TLastXReceiverPort : public TExtensionCircBuffLock<PortDataType, DataContainerSize>
{
  public:
    typedef TExtensionCircBuffLock<PortDataType, DataContainerSize>      Base_t;
    typedef TFixedCircularBuffer<const PortDataType*, DataContainerSize> PortDataContainer_t;

    TLastXReceiverPort() : Base_t()
    {
        // Set subscriber type
        Base_t::setSubscriberType(ESubscriberType::LASTX);
    }

    /// @brief update data container
    inline void update()
    {
        Base_t::fillDataContainer();
        Base_t::update();
    }

    /// @brief cleanup after processing
    inline void cleanup()
    {
        Base_t::cleanup();
        // fetch concurrently delivered items and store them in the data container
        // to force early release of the oldest data items
        Base_t::fillDataContainer();
    }

    /// @brief release all received portdata
    inline void releasePortData()
    {
        // purge entries from assign container copy to data container
        Base_t::fillDataContainer();
        // release all entries from data container
        Base_t::returnCurrentPortData();
        // delegate remaining tasks to mixin
        Base_t::releasePortData();
    }

    /// @brief return the current data
    PortDataContainer_t& getData() { return *reinterpret_cast<PortDataContainer_t*>(&Base_t::getDataContainer()); }
};

// TODO: deprecate this alias
template <typename PortDataType>
using TNewestConcurrentReceiverPort = TNewestReceiverPort<PortDataType>;

} // namespace ddk
} // namespace imo_com

#endif // IMO_COM_DDK_RECEIVERPORT_HPP_
