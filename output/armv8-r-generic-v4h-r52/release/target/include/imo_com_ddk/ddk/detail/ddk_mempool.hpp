//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
//  Copyright (c) by iMotion AI. All rights reserved.
//=============================================================================
//  D E S C R I P T I O N
//-----------------------------------------------------------------------------
//  Project Name: imo_com
//  Target System(s):
//  Compiler(s):
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//  Name: xiang.li
//=============================================================================
#ifndef IMO_COM_DDK_MEMPOOL_HPP_
#define IMO_COM_DDK_MEMPOOL_HPP_

#include "ddk_utils.hpp"
#include "ddk_porttype.hpp"

namespace imo_com
{
namespace ddk
{

extern void const* g_shmVirtAddr_p;

class CInternal
{
  public:
    CInternal() : m_referenceCounter(0U), m_reserved{0U, 0U, 0U} {}

  public:
    uint16_t m_referenceCounter;
    uint16_t m_reserved[3]; // alignment to 8 bytes
};

enum EAllocType : uint8_t
{
    EAllocType_Normal = 0U,
    EAllocType_Cool,
    EAllocType_Hero,
    EAllocType_FixedCool,
    EAllocType_FixedCool_Forwarder,
};

class CChangeableMemPoolStorage
{
  public:
    /// @brief  Constructor.
    CChangeableMemPoolStorage();

    /// @brief  Destructor.
    ~CChangeableMemPoolStorage() = default;

    /// @brief Initialize the memory pool.
    /// @param memPoolBase_p Pointer to the start of the memory pool.
    /// @param memPoolEnd_p  Pointer to the end of the memory pool.
    /// @param chunkSize    Size of each chunk in the memory pool.
    /// @param allocType Type of allocation to use.
    void initMempool(
        void*              memPoolBase_p,
        const void* const  memPoolEnd_p,
        const uint32_t     chunkSize,
        const uint32_t     chunkCount,
        const std::string& portDataType,
        const EAllocType   allocType = EAllocType_Normal);

    /// @brief  Get a chunk of memory from the memory pool.
    /// @return  Pointer to the free chunk of memory.
    void* getChunk();

    /// @brief  Release a chunk of memory back to the memory pool.
    /// @param chunk2Release_p  Pointer to the chunk to release.
    /// @return  True if the chunk was successfully released, false otherwise.
    bool releaseChunk(void* chunk2Release_p);

    void* cloneChunk(void* const chunk2Clone_p);

    uint16_t getChunkCount() const { return m_chunkCount; }

    uint32_t getChunkSize() const { return m_chunkSize; }

    /// @brief  check if the memory pool is empty
    /// @return true if the memory pool is empty
    bool empty() const;

    void setPortName(const std::string &portName) { m_portType.setPortName(portName); }

    const char *getPortName() const { return m_portType.c_str(); }

    EAllocType getAllocType() const { return m_allocType; }

    uint16_t getMinFreeEver() const { return m_minfreeEver; }

  private:
    /// @brief initialize the memory pool
    void initializer();

    /// @brief get the base address of the memory pool
    /// @return Pointer to the start of the memory pool.
    int8_t* validMemBase();

    const int8_t* validMemBase() const;

    const int8_t* validMemEnd() const;

    /// @brief  check if the memory pool is valid
    /// @param chunk2Validate_p Pointer to the chunk.
    /// @return true if the chunk is valid
    bool validate(const void* const chunk2Validate_p) const;

    uint16_t getChunkID(const void* const chunk_p) const;

  private:
    CDdkPortType  m_portType; // Port type
    int8_t*       m_memPoolBase_p;
    const int8_t* m_memPoolEnd_p;
    EAllocType    m_allocType;
    uint16_t      m_freeListHeadID;
    uint16_t      m_chunkCount;
    uint16_t      m_minfreeEver;
    uint16_t      m_minfreeCurrent;
    uint32_t      m_chunkSize;
};

class CChangeableMemPoolBase : public CChangeableMemPoolStorage
{
  public:
    CChangeableMemPoolBase(uint32_t measurementID) : m_measurementID(measurementID) {}

    uint32_t getMeasurementID() const { return m_measurementID; }

    void* getChunk() { return static_cast<void*>(CChangeableMemPoolStorage::getChunk()); }

    void releaseChunk(void* chunk) { CChangeableMemPoolStorage::releaseChunk(chunk); }

    void setRef(void* chunk, uint16_t subscriberCount);

    bool releaseRef(void* chunk, uint16_t subscriberCount);

  private:
    const int32_t m_measurementID; // Measurement ID
};

template <typename PortDataType, uint16_t NumberOfChunks>
class TChangeableMemPool : public CChangeableMemPoolBase
{
  public:
    typedef ParamByteAlign<TAlignmentOf<PortDataType>::value> ParamAlign;
    enum
    {
        ALIGN_BYTES      = (TMax<ParamAlign::ALIGNMENT_IN_BYTE, default_alignment>::value),
        CHUNK_SIZE       = TAlignUp<sizeof(CInternal) + sizeof(PortDataType), ALIGN_BYTES>::value,
        NUMBER_OF_CHUNKS = NumberOfChunks,
    };

    TChangeableMemPool(uint32_t measurementID, const EAllocType allocType)
        : CChangeableMemPoolBase(measurementID)
    {
        initMempool(m_pool.begin(), m_pool.cend(), CHUNK_SIZE, NumberOfChunks, GET_CLASS_NAME<PortDataType>(), allocType);
    }

    uint32_t getChunkSize() const { return static_cast<uint32_t>(CHUNK_SIZE); }

  private:
    TAlignedStorage<CHUNK_SIZE * NUMBER_OF_CHUNKS, ALIGN_BYTES> m_pool; //// Memory pool
};

template <
    typename PortDataType,
    uint16_t   NumberOfChunks,
    EAllocType AllocType = EAllocType_Normal,
    typename             = typename std::enable_if<
                    (EAllocType_Normal == AllocType) ||
                    (std::is_trivially_copyable<PortDataType>::value || std::is_standard_layout<PortDataType>::value)>::type>
class TMemPool : public TChangeableMemPool<PortDataType, NumberOfChunks>
{
  public:
    typedef TChangeableMemPool<PortDataType, NumberOfChunks> Base_t;

    TMemPool(uint32_t measurementID = 0) : TChangeableMemPool<PortDataType, NumberOfChunks>(measurementID, AllocType)
    {
#if !defined(ZX_COMPUTE_TARGET_RPU)
      // Set port name
      Base_t::setPortName(GET_CLASS_NAME<PortDataType>());
#endif
    }
};

} // namespace ddk
} // namespace imo_com

#endif // IMO_COM_DDK_MEMPOOL_HPP_
