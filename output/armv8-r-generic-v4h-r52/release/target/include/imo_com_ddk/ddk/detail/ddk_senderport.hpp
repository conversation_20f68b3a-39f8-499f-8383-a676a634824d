//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
//  Copyright (c) by iMotion AI. All rights reserved.
//=============================================================================
//  D E S C R I P T I O N
//-----------------------------------------------------------------------------
//  Project Name: imo_com
//  Target System(s):
//  Compiler(s):
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//  Name: xiang.li
//=============================================================================
#ifndef IMO_COM_DDK_SENDERPORT_HPP_
#define IMO_COM_DDK_SENDERPORT_HPP_
#include "ddk_senderportBase.hpp"
#include "ddk_receiverportBase.hpp"
#include "ddk_monitor.hpp"
#include <algorithm>

namespace imo_com
{
namespace ddk
{
template <typename PortDataType>
class TSenderPortBase : public CSenderPortBase
{
  public:
    typedef CSenderPortBase Base_t;

    TSenderPortBase() : m_currentStorage_p(nullptr)
    {
#if !defined(ZX_COMPUTE_TARGET_RPU)
        Base_t::setPortName(GET_CLASS_NAME<PortDataType>());
#endif
    }

    template <typename SubscriberType>
    void connect(SubscriberType& subscriber)
    {
        if (strcmp(subscriber.getPortName(), Base_t::getPortName()) != 0)
        {
            setError(
                "port name mismatch, mempool PortName[%s] sender PortName[%s]\n",
                subscriber.getPortName(),
                Base_t::getPortName());
            return;
        }
        m_subscriberList.push_back(&subscriber);
        subscriber.setConnected();
        // register to monitor
#if !defined(ZX_COMPUTE_TARGET_RPU)
        CDdkMonitor::getInstance().registerPort(this, &subscriber);
#endif
    }

    inline bool isConnected() const { return !m_subscriberList.empty(); }

    template <typename SubscriberType>
    void disconnect(SubscriberType& subscriber)
    {
        auto it = std::find(m_subscriberList.begin(), m_subscriberList.end(), &subscriber);
        if (it != m_subscriberList.end())
        {
            m_subscriberList.erase(it);
            subscriber.disconnect();
        }
    }

    void connectMemPool(CChangeableMemPoolBase& mempool)
    {
        if (strcmp(mempool.getPortName(), Base_t::getPortName()) != 0)
        {
            setError(
                "port name mismatch, mempool PortName[%s] sender PortName[%s]\n",
                mempool.getPortName(),
                Base_t::getPortName());
            return;
        }
        Base_t::connectMemPool(&mempool);
    }

    bool hasFreeChunk() { return Base_t::getMemPool()->empty() == false; }

    inline void* reserveIntern()
    {
        if (nullptr == m_currentStorage_p)
        {
            collectDeposits();
            m_currentStorage_p = Base_t::getChunk();
            if (nullptr == m_currentStorage_p)
            {
                ddk_log_error("Don't get chunk from mempool");
            }
        }
        return m_currentStorage_p;
    }

    inline PortDataType& reserve()
    {
#if !defined(ZX_COMPUTE_TARGET_RPU)
        if (unlikely(m_threadId == 0U))
        {
            m_threadId = pthread_self();
        }
        else if (unlikely(m_threadId != pthread_self()))
        {
            setError(
                "reserve() can only be called from the same thread, first thread[%lx] current thread[%lx]",
                m_threadId,
                pthread_self());
        }
#endif
        PortDataType* chunk = static_cast<PortDataType*>(reserveIntern());
        if (chunk)
        {
            new (chunk) PortDataType;
        }
        return *chunk;
    }

    inline PortDataType* reservePtr()
    {
        PortDataType* chunk = static_cast<PortDataType*>(reserveIntern());
        if (chunk)
        {
            new (chunk) PortDataType;
        }
        return chunk;
    }

    inline PortDataType& reserveLastDelivery()
    {
        if (isReserved())
        {
            PortDataType* chunk = static_cast<PortDataType*>(reserveIntern());
            return *chunk;
        }
        else if (nullptr == getLastDelivery())
        {
            return reserve();
        }
        else
        {
            PortDataType*             chunk_p              = static_cast<PortDataType*>(reserveIntern());
            const PortDataType* const lastDeliveredChunk_p = static_cast<const PortDataType*>(getLastDelivery());
            if (lastDeliveredChunk_p != chunk_p)
            {
                new (chunk_p) PortDataType(*lastDeliveredChunk_p);
            }
            return *chunk_p;
        }
    }

    inline PortDataType* getReserveData() { return static_cast<PortDataType*>(m_currentStorage_p); }

    inline bool isReserved() const { return (nullptr != m_currentStorage_p); }

    void deliver()
    {
        if (nullptr != m_currentStorage_p)
        {
            deliverIntern(m_currentStorage_p);
            m_currentStorage_p = nullptr;
        }
        else
        {
            ddk_log_error("deliver() called without reserve()");
        }
    }

    void deliver(const PortDataType* data_p) { deliverIntern(Base_t::cloneChunk(const_cast<PortDataType*>(data_p))); }

  private:
    inline void collectDeposits()
    {
        for (size_t i = 0; i < m_subscriberList.size(); i++)
        {
            const void* deposit_p = nullptr;
            while (m_subscriberList[i]->isReleased(deposit_p))
            {
                handleDeposits(deposit_p);
            }
        }
    }
    void handleDeposits(const void* portData_p) { getMemPool()->releaseRef(const_cast<void*>(portData_p), 1U); }

    inline void deliverIntern(void* currentStorage)
    {
        setLastDelivery(currentStorage);

        collectDeposits();

        dispatch(m_subscriberList, currentStorage);
    }

    void dispatch(const SubscriberList_t& subscriberList, const void* portData)
    {
        uint16_t dispatchCount = 0;
        for (size_t i = 0; i < subscriberList.size(); i++)
        {
            if (subscriberList[i]->assign(portData))
            {
                dispatchCount++;
            }
        }
        handleDispatches(dispatchCount, portData);
    }

    void handleDispatches(uint16_t dispatchCount, const void* portData)
    {
        if (0U < dispatchCount)
        {
            getMemPool()->setRef(const_cast<void*>(portData), dispatchCount);
        }
        else
        {
            getMemPool()->releaseChunk(const_cast<void*>(portData));
        }
    }

  private:
    SubscriberList_t m_subscriberList;
    void*            m_currentStorage_p;
#if !defined(ZX_COMPUTE_TARGET_RPU)
    pthread_t m_threadId{0U};
#endif
};

} // namespace ddk
} // namespace imo_com

#endif // IMO_COM_DDK_SENDERPORT_HPP_
