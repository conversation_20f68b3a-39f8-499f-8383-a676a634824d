//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
//  Copyright (c) by iMotion AI. All rights reserved.
//=============================================================================
//  D E S C R I P T I O N
//-----------------------------------------------------------------------------
//  Project Name: imo_com
//  Target System(s):
//  Compiler(s):
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//  Name: xiang.li
//=============================================================================
#ifndef IMO_COM_DDK_PORTTYPE_HPP_
#define IMO_COM_DDK_PORTTYPE_HPP_

#include <cxxabi.h>
#include <string>
#include <string.h>

namespace imo_com
{
namespace ddk
{
class CDdkPortType
{
public:
  void setPortName(const std::string &port_name)
  {
#if !defined(ZX_COMPUTE_TARGET_RPU)
    m_portName = port_name;
#endif
  }
  const char *c_str() const
  {
#if !defined(ZX_COMPUTE_TARGET_RPU)
    return m_portName.c_str();
#else
    return "undefined";
#endif
  }
private:
#if !defined(ZX_COMPUTE_TARGET_RPU)
  std::string m_portName;
#endif
};

} // namespace ddk
} // namespace imo_com

#endif // IMO_COM_DDK_PORTTYPE_HPP_

