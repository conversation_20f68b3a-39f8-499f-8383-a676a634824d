//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
//  Copyright (c) by iMotion AI. All rights reserved.
//=============================================================================
//  D E S C R I P T I O N
//-----------------------------------------------------------------------------
//  Project Name: imo_com
//  Target System(s):
//  Compiler(s):
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//  Name: xiang.li
//=============================================================================
#ifndef IMO_COM_DDK_RECEIVERPORT_BASE_HPP_
#define IMO_COM_DDK_RECEIVERPORT_BASE_HPP_
#include "ddk_utils.hpp"
#include "ddk_porttype.hpp"

namespace imo_com
{
namespace ddk
{

enum ESubscriberType : uint8_t
{
    NO_DEF = 0U,
    NEWEST,
    LATEST,
    NEWX,
    LASTX,
};

class CReceiverPortBase
{
  public:
    CReceiverPortBase() : m_isConnected(false), m_newData(false), m_subscriberType(ESubscriberType::NO_DEF) {}

    virtual ~CReceiverPortBase() {}

    inline void setConnected()
    {
        if (m_isConnected)
        {
            ddk_log_error("port_type is double connect");
            return;
        }
        m_isConnected = true;
    }

    inline void disconnect() { m_isConnected = false; }

    inline bool isConnected() const { return m_isConnected; }

    inline void newDataReceived() { m_newData = true; }

    inline void releasePortData() { m_newData = false; }

    inline bool hasNewData() const { return m_newData; }

    inline void setPortName(const std::string &port_name) { m_portType.setPortName(port_name); }

    inline const char *getPortName() const { return m_portType.c_str(); }

    inline void setSubscriberType(ESubscriberType subscriberType) { m_subscriberType = subscriberType; }

    inline ESubscriberType getSubscriberType() const { return m_subscriberType; }

    inline void update() {}

    inline void cleanup() { m_newData = false; }

    inline void setCoolCapID(uint32_t capID) { m_coolCapID = capID; }

    inline uint32_t getCoolCapID() { return m_coolCapID; }

    inline void setPortID(uint32_t portID) { m_portID = portID; }

    inline uint32_t getPortID() { return m_portID; }

    inline uint32_t getTotalAssignCount() { return m_totalAssignCount; }

    inline void incTotalAssignCount() { m_totalAssignCount++; }

    inline uint32_t getTotalDataCount() { return m_totalDataCount; }

    inline void incTotalDataCount() { m_totalDataCount++; }

    virtual bool isReleased(const void*& portData) = 0;

    virtual bool assign(const void* const portData) = 0;

  private:
    CDdkPortType    m_portType;    /// port type
    bool            m_isConnected;    /// port is connected
    bool            m_newData;        /// port has new data
    ESubscriberType m_subscriberType; /// subscriber type
    uint32_t        m_coolCapID{0xFFFFFFFF};
    uint32_t        m_portID{0xFFFFFFFF};
    uint32_t        m_totalAssignCount{0};
    uint32_t        m_totalDataCount{0};
};
typedef CReceiverPortBase          Subscriber_t;
typedef std::vector<Subscriber_t*> SubscriberList_t;

//==========================================================
template <typename PortDataType, uint32_t DataContainerSize>
class TExtensionCircBuffLock : public CReceiverPortBase
{
  public:
    typedef CReceiverPortBase                                    Base_t;
    typedef TFixedCircularBuffer<const void*, DataContainerSize> DataInternalContainer_t;
    typedef TFifo<const void*, 2 * DataContainerSize + 1>        ReleaseContainer_t;

    TExtensionCircBuffLock() : m_dataContainer(), m_assignContainer(), m_releaseContainer()
    {
#if !defined(ZX_COMPUTE_TARGET_RPU)
      // Set port name
      Base_t::setPortName(GET_CLASS_NAME<PortDataType>());
#endif
    }

    bool assign(const void* const portData)
    {
        if (portData == nullptr)
        {
            ddk_log_error("Input data is null pointer, Port[%s]", getPortName());
            return false;
        }
        bool returnValue = true;
        bool l_errorFlag = false;
        m_lock.lock();
        if (m_assignContainer.size() == DataContainerSize)
        {
            auto value = m_assignContainer.front();
            if (false == m_releaseContainer.push(value))
            {
                l_errorFlag = true;
                returnValue = false;
            }
        }
        if (false == l_errorFlag)
        {
            m_assignContainer.push(portData);
            Base_t::incTotalAssignCount();
        }
        else
        {
            ddk_log_error("Port[%s] assign failed, release container is full", getPortName());
        }
        m_lock.release();
        return returnValue;
    }

    inline bool isReleased(const void*& portData)
    {
        bool res = m_releaseContainer.pop(portData);
        return res;
    }

    inline bool peekNewData() const
    {
        m_lock.lock();
        bool res = !m_assignContainer.empty();
        m_lock.release();
        return res;
    }

    inline void fillAllDataContainer()
    {
        m_lock.lock();
        for (int i = 0; i < m_assignContainer.size(); i++)
        {
            m_dataContainer.push(m_assignContainer[i]);
            Base_t::incTotalDataCount();
            Base_t::newDataReceived();
        }
        m_assignContainer.clear();
        m_lock.release();
    }

    inline void fillDataContainer()
    {
        m_lock.lock();
        bool l_errorFlag = false;
        for (int i = 0; i < m_assignContainer.size(); i++)
        {
            if (m_dataContainer.capacity() == m_dataContainer.size())
            {
                if (false == m_releaseContainer.push(m_dataContainer.front()))
                {
                    l_errorFlag = true;
                    break;
                }
            }
            m_dataContainer.push(m_assignContainer[i]);
            incTotalDataCount();
            Base_t::newDataReceived();
        }
        if (false == l_errorFlag)
        {
            m_assignContainer.clear();
        }
        else
        {
            ddk_log_error(
                "fillDataContainer, Release container is full and cannot release data, Port: %s",
                getPortName());
        }
        m_lock.release();
    }

    inline bool hasData() const { return !m_dataContainer.empty(); }

    inline void returnCurrentPortData()
    {
        bool l_errorFlag = false;
        m_lock.lock();

        for (int i = 0; i < m_dataContainer.size(); i++)
        {
            if (m_releaseContainer.push(m_dataContainer[i]) == false)
            {
                l_errorFlag = true;
                break;
            }
        }
        if (false == l_errorFlag)
        {
            m_dataContainer.clear();
        }
        else
        {
            ddk_log_error(
                "returnCurrentPortData: Release container is full and cannot release data, Port: %s",
                getPortName());
        }
        m_lock.release();
    }

    inline bool isAddressAvailable(const void* const portData)
    {
        if (nullptr == portData)
        {
            return false;
        }
        for (int i = 0; i < m_dataContainer.size(); i++)
        {
            if (m_dataContainer[i] == portData)
            {
                return true;
            }
        }
        return false;
    }

    inline void clearDataContainer() { m_dataContainer.clear(); }

    const void* getDataBase() const
    {
        if (hasData())
        {
            return m_dataContainer.front();
        }
        else
        {
            return nullptr;
        }
    }

    DataInternalContainer_t& getDataContainer() { return m_dataContainer; }

  private:
    DataInternalContainer_t m_dataContainer;
    DataInternalContainer_t m_assignContainer;
    ReleaseContainer_t      m_releaseContainer;
    mutable CSpinLock       m_lock;
};

} // namespace ddk
} // namespace imo_com

#endif // IMO_COM_DDK_RECEIVERPORT_BASE_HPP_
