//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_CONFIG_SUFFIX_HPP_INCLUDED
#define ZX_VFC_CONFIG_SUFFIX_HPP_INCLUDED
//=============================================================================

#ifndef VFC_CONFIG_HPP_INCLUDED
static_assert(false, "vfc_config_suffix.hpp must not be included directly, include vfc_config.hpp instead");
#endif

/// @def ZX_BUILD_SCA
/// Flag to indicate, that the code is parsed by a static code analysis (SCA). SCA is not intended to generate an
/// executable, but to warn about potentially unsafe code. The problem with it is that it sometimes doesn't understand
/// low-level instructions. Using this flag those instructions can be replaced by unproblematic C++-code. It is
/// important to switch as little code as possible to keep the difference to production code minimal.
///
///     int fastMultiply( int a, int b )
///     {
///         #if( ZX_BUILD_SCA() )
///             return a * b;
///         #else
///             return __comp_intrinsic( &a, &b );
///         #end
///     }
///
/// The macro is defined as function-style macro for enhanced safety: if undefined or defined as object-style macro the
/// compilation fails.
#ifndef ZX_BUILD_SCA
// Default is false
#define ZX_BUILD_SCA() false
#elif (ZX_BUILD_SCA() != true) &&                                                                                      \
    (ZX_BUILD_SCA() != false) // PRQA S 1036 # Technical debt L7 id=00c1020e099a2dac444f-d1857932cf41816c
static_assert(false, "ZX_BUILD_SCA() macro should be either true or false.");
#endif

/// @def ZX_BUILD_SCA_HELIX
/// Macro which is true if the SCA run is particularly by Helix.
#ifndef ZX_BUILD_SCA_HELIX
// Default is false
#define ZX_BUILD_SCA_HELIX() false
#elif (ZX_BUILD_SCA_HELIX() != true) &&                                                                                \
    (ZX_BUILD_SCA_HELIX() != false) // PRQA S 1036 # Technical debt L7 id=00c123d9b267b4ed404e-92cad0019ab858ae
static_assert(false, "ZX_BUILD_SCA_HELIX() macro should be either true or false.");
#endif

#ifndef VFC_DECL_FORCEINLINE
#define VFC_DECL_FORCEINLINE inline
#endif

#ifndef VFC_UNITTEST_THROWING_DESTRUCTOR
#define VFC_UNITTEST_THROWING_DESTRUCTOR noexcept
#endif

/// @def VFC_CONTAINER_USE_CHECKED_ITERATOR
/// Function-style bool macro to enable checked iterators for all iterators
/// that would otherwise use plain pointers.
/// When enabled by definition through the VFC_USER_CONFIG mechanism or
/// by defining it on the compiler command line, the API of TCArray, TFixedVector,
/// and TSpan is changed to return a TCheckedIterator.
/// @sa TCheckedIterator
#ifndef VFC_CONTAINER_USE_CHECKED_ITERATOR
// Default is false
#define VFC_CONTAINER_USE_CHECKED_ITERATOR() false
#elif (VFC_CONTAINER_USE_CHECKED_ITERATOR() != true) && (VFC_CONTAINER_USE_CHECKED_ITERATOR() != false)
static_assert(false, "VFC_CONTAINER_USE_CHECKED_ITERATOR() macro should be either true or false.");
#endif

#if (VFC_CONTAINER_USE_CHECKED_ITERATOR() == true) // PRQA S 1036 # Technical debt L7
                                                   // id=00c10191a7d5c9cf4e65-93d7b3e809071418
#if (defined(VFC_NO_ASSERTS) && !defined(VFC_ASSERTFUNC))
static_assert(false, "VFC_CONTAINER_USE_CHECKED_ITERATOR not supported with disabled assertions");
#endif
#endif

/// @def VFC_MEM_BLOCK_ACCESS_TYPE
/// define a type to be used for performing optimized memory fetches/stores.
/// Should be 8 or 16 byte long and have a compatible alignment for vfc::float32_t
#ifndef VFC_MEM_BLOCK_ACCESS_TYPE
#ifdef VFC_HAS_LONG_LONG
#define VFC_MEM_BLOCK_ACCESS_TYPE vfc::uint64_t
#else
#define VFC_MEM_BLOCK_ACCESS_TYPE vfc::uint32_t
#endif
#endif

/// @def VFC_STL_ALIAS
/// Use this to define the stl-namespace (math included) and to switch to another stl-implementation.
/// When not defined, it maps to ::std.
/// It is possible to define only the vfc-intern-math-namespace, see VFC_INTERN_MATH_ALIAS.
#ifdef VFC_STL_ALIAS
#ifdef VFC_INTERN_MATH_ALIAS
static_assert(false, "Defining both namespaces stl and intern-math is not possible.");
#endif
#define VFC_INTERN_MATH_ALIAS VFC_STL_ALIAS
namespace VFC_STL_ALIAS
{
}
namespace stlalias = VFC_STL_ALIAS;
#else
namespace std
{
}
namespace stlalias = ::std;
#endif

/// @def VFC_INTERN_MATH_ALIAS
/// Set VFC_INTERN_MATH_ALIAS to the namespace of your math implementation.
/// Also pre-define your namespace, e.g. add a line like "namespace VFC_INTERN_MATH_ALIAS {}"
/// is sufficient or several lines if you have a nested namespace.
/// It is possible to set also the macro VFC_INCLUDE_INTERN_MATH_IMPL to the file,
/// which defines the math functions in the namespace of your VFC_INTERN_MATH_ALIAS.
/// When not defined, it maps to the vfc default namespace.
#ifdef VFC_INTERN_MATH_ALIAS
// namespace VFC_INTERN_MATH_ALIAS {} // has to be done by customer
namespace vfc_intern_math_alias = VFC_INTERN_MATH_ALIAS;
#else
namespace vfc
{
namespace intern
{
namespace math_impl
{
}
} // namespace intern
} // namespace vfc
namespace vfc_intern_math_alias = ::vfc::intern::math_impl;
#endif

/// @def VFC_INCLUDE_INTERN_MATH_IMPL
/// Set also the include macro VFC_INCLUDE_INTERN_MATH_IMPL to the file,
/// which defines the math functions in the namespace of your VFC_INTERN_MATH_ALIAS.
/// It is possible to use the default file as template.
/// Please follow the instructions how to create your own math include file.
#ifndef VFC_INCLUDE_INTERN_MATH_IMPL
#define VFC_INCLUDE_INTERN_MATH_IMPL "vfc/core/config/vfc_intern_math_impl.hpp"
#endif

/// @def VFC_ENABLE_FLOAT64
/// Flag if mathematical functions and types based on float64_t should be provided.

/// @def VFC_ENABLE_FLOAT64_TYPE
/// Flag if the typedef float64_t should be defined. It is implied by VFC_ENABLE_FLOAT64.
#if defined(VFC_ENABLE_FLOAT64) && !defined(VFC_ENABLE_FLOAT64_TYPE)
#define VFC_ENABLE_FLOAT64_TYPE
#endif

/// @def VFC_ENABLE_QM_MATH
/// Activate the namespace only_qm for non qualified 64-bit methods
#if defined(VFC_ENABLE_FLOAT64) && !defined(VFC_ENABLE_QM_MATH)
#define VFC_ENABLE_QM_MATH
#endif

/// @def VFC_ENABLE_FLOAT64_TYPE
/// Flag if the typedef float64_t should be defined. It is implied by VFC_ENABLE_QM_MATH.
#if defined(VFC_ENABLE_QM_MATH) && !defined(VFC_ENABLE_FLOAT64_TYPE)
#define VFC_ENABLE_FLOAT64_TYPE
#endif

/// @def VFC_NOEXCEPT
/// macro to specify that a function does not throw exceptions, maps to C++11 keyword 'noexcept'
/// when available and exceptions are enabled.
#ifndef VFC_NOEXCEPT
#if defined(VFC_NO_EXCEPTIONS)
#define VFC_NOEXCEPT
#else
#define VFC_NOEXCEPT noexcept
#endif
#endif

/// @def VFC_CONSTEXPR14_FUNCTION
/// macro to mark functions and methods as constexpr according to the C++14 standard
#if !defined(VFC_CONSTEXPR14_FUNCTION)
#if defined(VFC_USE_CPP14_FEATURES)
#define VFC_CONSTEXPR14_FUNCTION constexpr
#else
#define VFC_CONSTEXPR14_FUNCTION
#endif
#endif

//=============================================================================
//  DOXYGEN DEFGROUP vfc_group_core_generic_attribute_specifier BEGIN
//-----------------------------------------------------------------------------
/// @defgroup vfc_group_core_generic_attribute_specifier Attribute Specifier
/// @ingroup vfc_group_core_generic
/// @brief attribute specifier of VFC
/// Attributes are used to provide a generic/unified standard syntax which are
/// ussally provided by compiler language extensions e.g. MSVS __declspec(...)
/// or ARM __attribute__((...)).
///
/// Attribute can be used to describe a software behavior or characteristics
/// which are considered by the compiler and this commandline output. For
/// example a function can be marked as deprecated to indicated that this
/// function is removed by next software release.
///
/// As describeted at https://en.cppreference.com/w/cpp/language/attributes
/// "An attribute can be used almost everywhere in the C++ program, and can
///  be applied to almost everything: to types, to variables, to functions,
///  to names, to code blocks, to entire translation units, although each
///  particular attribute is only valid where it is permitted by the
///  implementation"
///
/// @note ATR = attribute
///
/// The compiler specifics are implemented in vfc/include/core/config/compiler.
/// If no compiler specific implemention is found, the following generic
/// implementation will be used. This will be ensured via the config file,
/// vfc/include/core/vfc_config.hpp
//=============================================================================

//=============================================================================
//    VFC_ATR_DEPRECATED
//-----------------------------------------------------------------------------
/// @def VFC_ATR_DEPRECATED
///
/// Define a declaration of name or entity as deprecated. If a deprecated
/// name or entity is used e.g. call a deprecated function a compiler warning
/// will be produced.
///
/// The VFC_ATR_DEPRECATED can be applied to the following names or entities:
///  - class/struct/union: struct VFC_ATR_DEPRECATED(S);
///  - non-static data member: union U { VFC_ATR_DEPRECATED(int n); };
///  - function: VFC_ATR_DEPRECATED(void f());
///  - variable, including static data member: VFC_ATR_DEPRECATED(int x);
///  - enumeration: enum VFC_ATR_DEPRECATED(E) {};
///  - template specialization: template<> struct VFC_ATR_DEPRECATED(X<int>) {};
///  - typedef: VFC_ATR_DEPRECATED(typedef S* PS);
///
/// Deprecation of namespaces is not supported (this would required c++14).
///
/// In case of enum the deprecation attributes has to placed after the entity.
/// Therefore the macro VFC_ATR_POST_DEPRECATED shall be used.
///
/// @note Known compiler limitations for msvc 2010:
///        - does not support the deprecation attribute at all -> no warning will be printed
///
/// @note Known compiler limitations for msvc 2015:
///        - class/struct/union: struct VFC_ATR_DEPRECATED(S) -> no warning will be printed
///
/// @note Known compiler limitations for clang:
///        - VFC_ATR_DEPRECATED won't report deprecation warnings, if the c++14 isn't enabled for the build
///
/// @param EXP function which should be marked as deprecated

//=============================================================================

#ifndef VFC_ATR_DEPRECATED
#define VFC_ATR_DEPRECATED(EXP) EXP
#endif

//=============================================================================
//    VFC_ATR_DEPRECATED2
//-----------------------------------------------------------------------------
/// @def VFC_ATR_DEPRECATED2
///
/// Same as VFC_ATR_DEPRECATED but with an additional message. The message will
/// be printed as compiler output, if the deprecated expression is used.
///
/// @param EXP function which should be marked as deprecated
/// @param MSG message which will printed in case of deprecated EXP is used

//=============================================================================

#ifndef VFC_ATR_DEPRECATED2
#define VFC_ATR_DEPRECATED2(EXP, MSG) EXP
#endif

//=============================================================================
//    VFC_ATR_POST_DEPRECATED
//-----------------------------------------------------------------------------
/// @def VFC_ATR_POST_DEPRECATED
///
/// Same as VFC_ATR_DEPRECATED but for some names or entities it is required to
/// place the deprecation attribute after the expresion.
///
/// The VFC_ATR_POST_DEPRECATED can be applied to the following names or entities:
///  - enum { VFC_ATR_POST_DEPRECATED(enumerator) };
///
/// @note Post deprecations are currently not supported by ghs compiler!
///
/// @param EXP function which should be marked as deprecated

//=============================================================================

#ifndef VFC_ATR_POST_DEPRECATED
#define VFC_ATR_POST_DEPRECATED(EXP) EXP
#endif

//=============================================================================
//    VFC_ATR_POST_DEPRECATED2
//-----------------------------------------------------------------------------
/// @def VFC_ATR_POST_DEPRECATED2
///
/// Same as VFC_ATR_POST_DEPRECATED2 but with an additional message. The message
/// will be printed as compiler output, if the deprecated expression is used.
///
/// @note Post deprecations are currently not supported by ghs compiler!
///
/// @param EXP function which should be marked as deprecated
/// @param MSG message which will printed in case of deprecated EXP is used

//=============================================================================

#ifndef VFC_ATR_POST_DEPRECATED2
#define VFC_ATR_POST_DEPRECATED2(EXP, MSG) EXP
#endif

//=============================================================================
//    VFC_ATR_DEPRECATED_ENUM_VALUE
//-----------------------------------------------------------------------------
/// @def VFC_ATR_DEPRECATED_ENUM_VALUE
///
/// Define a enum value as deprecated and a compiler warning will be produced.
///
/// @note Deprecations are currently only supported for arm, clang, gcc and visual c++ compiler.
///
/// @param EXP Enum value which should be marked as deprecated.
//=============================================================================

#ifndef VFC_ATR_DEPRECATED_ENUM_VALUE
#define VFC_ATR_DEPRECATED_ENUM_VALUE(EXP) EXP
#endif

//=============================================================================
//    VFC_ATR_DEPRECATED_ENUM_VALUE2
//-----------------------------------------------------------------------------
/// @def VFC_ATR_DEPRECATED_ENUM_VALUE2
///
/// Same as VFC_ATR_DEPRECATED_ENUM_VALUE but with an additional message. The message
/// will be printed as compiler output, if the deprecated expression is used.
///
/// @note Enum value deprecations are currently not supported by ghs compiler!
///
/// @param EXP function which should be marked as deprecated
/// @param MSG message which will be printed in case of deprecated EXP is used

//=============================================================================
#ifndef VFC_ATR_DEPRECATED_ENUM_VALUE2
#define VFC_ATR_DEPRECATED_ENUM_VALUE2(EXP, MSG) EXP
#endif

//=============================================================================
//    VFC_ATR_NODISCARD
//-----------------------------------------------------------------------------
/// @def VFC_ATR_NODISCARD
///
/// Function return paramter have to be procces by the caller of the function.
/// @param func Function which return paramter should be marked as no discard

//=============================================================================

#ifndef VFC_ATR_NODISCARD
#define VFC_ATR_NODISCARD(func) func
#endif

//=============================================================================
//    VFC_ATR_NOINLINE_REQUEST
//-----------------------------------------------------------------------------
/// @def VFC_ATR_NOINLINE_REQUEST
///
/// Functions decorated with VFC_ATR_NOINLINE_REQUEST are meant not to be
/// inlined and this request is passed to the compiler by non-standard means.
/// As this is not supported by all compilers, the usage of this feature reduces
/// portability.
///
//=============================================================================

#ifndef VFC_ATR_NOINLINE_REQUEST
#define VFC_ATR_NOINLINE_REQUEST
#endif

//=============================================================================
//    C++11 check
//-----------------------------------------------------------------------------
/// VFC is supporting only compilers with C++11 and beyond.
/// As a result, other statements can be derived from this.
/// Beware that with MSVC (1900) the macro __cplusplus didn't report C++11,
/// because some features weren't supported.
//=============================================================================
#if !defined(__cplusplus) || ((__cplusplus < 201103L) && !defined(VFC_COMPILER_VISUALC))
static_assert(false, "VFC requires at minimum C++11.");
#endif

/// Defining the C++11 macro for now for compatibility reasons.
#define VFC_USE_CPP11_FEATURES

#if !defined(VFC_HAS_LONG_LONG)
static_assert(false, "VFC config error, 'long long' should be available.");
#endif

#if !defined(VFC_HAS_STDINT_H)
static_assert(false, "VFC config error, <stdint.h> should be available.");
#endif

//=============================================================================
//  DOXYGEN DEFGROUP vfc_group_core_generic_attribute_specifier END
//-----------------------------------------------------------------------------
/// @}
//=============================================================================

#endif // ZX_VFC_CONFIG_SUFFIX_HPP_INCLUDED

