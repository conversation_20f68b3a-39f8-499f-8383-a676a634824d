//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_VERSION_HPP_INCLUDED
#define VFC_VERSION_HPP_INCLUDED

// vfc/core includes
#include "vfc/core/vfc_config.hpp"
#include "vfc/core/vfc_types.hpp"

namespace vfc
{
using VersionInt = uint8_t;

// Forward declarations
struct CSemanticVersion;
template <uint8_t MajorValue, uint8_t MinorValue, uint8_t PatchValue>
struct TSemanticVersion;
template <typename VersionType, VersionInt MajorValue2, VersionInt MinorValue2>
struct IsVersionCompatible;
template <typename VersionType, VersionInt MajorValue2, VersionInt MinorValue2>
struct IsVersionAtLeast;
template <typename VersionType, VersionInt MajorValue2, VersionInt MinorValue2, VersionInt PatchValue2>
struct IsVersionExactly;

///=============================================================================
/// CSemanticVersion
///-----------------------------------------------------------------------------
/// A struct with a semantic version, usable at runtime or ROMable with constexpr.
///
/// The intention of the version numbers follow the page https://semver.org/
/// A semantic version communicates dependencies and compatibility, and having
/// this in code enables automatic checks. In brief:
/// * Major version must be incremented if any backwards incompatible changes to
///   the interface are introduced (breaking changes).
/// * Minor version must be incremented if the interface has new backwards compatible
///   functionality.
/// * Patch version is incremented with internal changes and backwards
///   compatible bug fixes.
///
/// FAQ:
/// * How to version deliveries?
///   Semantic versions document APIs and work best on smallish scope, e.g. the
///   API of a class. It's not that suitable for identifying branches,
///   releases and deliveries of whole libraries like VFC or Daddy. But it can be
///   a helpful additional information in a release of e.g. "Charlie-1".
/// * Don't I increment too many times?
///   Increments can 'saturate' when happening in the same release of the
///   surrounding lib. Two internal changes lead to only one 'Patch' increment,
///   a 'Major' increment covers also all 'Minor' and 'Patch' changes (-> X.0.0).
///   A good idea is to document the previous version so that devs know what
///   doesn't need to be incremented anymore.
/// * Is deprecation an increment?
///   The page above says yes, the minor version. This will depend on the release
///   of the library. Objective is that downstream projects should have a grace
///   period to adapt, and the minor version is signalling a change, but it is
///   compatible.
///
///

/// @ingroup                vfc_group_core_types
///=============================================================================
struct CSemanticVersion
{
    const VersionInt m_major;
    const VersionInt m_minor;
    const VersionInt m_patch;

    /// Ctor with given values, used by TSemanticVersion::getObj()
    constexpr CSemanticVersion(VersionInt major, VersionInt minor, VersionInt revision)
        : m_major(major), m_minor(minor), m_patch(revision)
    {
    }

    /// Template ctor with argument detection.
    template <VersionInt MajorValue, VersionInt MinorValue, VersionInt PatchValue>
    constexpr CSemanticVersion(
        TSemanticVersion<MajorValue, MinorValue, PatchValue>); // PRQA S 2180 # Technical debt L2
                                                               // id=00c1edd6235435c24b2f-da6e47a8ad200a45

    // Otherwise trivial struct, so copy-ctor, dtor etc are default.
};

///=============================================================================
/// TSemanticVersion
///-----------------------------------------------------------------------------
/// A trivial template struct providing a semantic version at compile time.
/// About semantic version, see @sa CSemanticVersion.
///
/// This can be used as first place to document the version also for humans,
///
/// * e.g. at a central place:
///     using VfcVersion = vfc::TSemanticVersion<1,0,3>;
///     using PreviousVfcVersion = vfc::TSemanticVersion<1,0,0>;
/// * e.g. in a class:
///     class MyClass
///     {
///     public:
///         using Version = vfc::TSemanticVersion<2,3,0>;
///         using PreviousVersion = vfc::TSemanticVersion<2,2,5>;
///     };
///
/// @tparam MajorValue      Major version number.
/// @tparam MinorValue      Minor version number.
/// @tparam PatchValue      Patch version number.

/// @ingroup                vfc_group_core_types
///=============================================================================
template <VersionInt MajorValue, VersionInt MinorValue, VersionInt PatchValue>
struct TSemanticVersion
{
    static constexpr VersionInt Major = MajorValue;
    static constexpr VersionInt Minor = MinorValue;
    static constexpr VersionInt Patch = PatchValue;

    /// Returns an object containing version numbers.
    static constexpr CSemanticVersion getObj();

    /// Some useful static checks for consumer of such version.
    /// Can be used e.g.
    ///     struct ApiCheck : SomeSemVer::IsCompatibleTo<1, 0> {};
    using this_type = TSemanticVersion<Major, Minor, Patch>;

    template <VersionInt MajorValue2, VersionInt MinorValue2>
    using IsCompatibleTo = IsVersionCompatible<this_type, MajorValue2, MinorValue2>;

    template <VersionInt MajorValue2, VersionInt MinorValue2>
    using IsAtLeast = IsVersionAtLeast<this_type, MajorValue2, MinorValue2>;

    template <VersionInt MajorValue2, VersionInt MinorValue2, VersionInt PatchValue2>
    using IsExactly = IsVersionExactly<this_type, MajorValue2, MinorValue2, PatchValue2>;
};

/// Inline function
constexpr inline bool operator==(CSemanticVersion lhs, CSemanticVersion rhs)
{
    return (lhs.m_major == rhs.m_major) && (lhs.m_minor == rhs.m_minor) && (lhs.m_patch == rhs.m_patch);
}

constexpr inline bool operator!=(CSemanticVersion lhs, CSemanticVersion rhs) { return !operator==(lhs, rhs); }

template <VersionInt MajorValue, VersionInt MinorValue, VersionInt PatchValue>
inline constexpr CSemanticVersion::CSemanticVersion(TSemanticVersion<MajorValue, MinorValue, PatchValue>)
    : m_major(MajorValue), m_minor(MinorValue), m_patch(PatchValue)
{
}

template <VersionInt MajorValue, VersionInt MinorValue, VersionInt PatchValue>
inline constexpr CSemanticVersion TSemanticVersion<MajorValue, MinorValue, PatchValue>::getObj()
{
    return CSemanticVersion(MajorValue, MinorValue, PatchValue);
}

constexpr inline bool isVersionCompatible(CSemanticVersion api, CSemanticVersion expected)
{
    return (api.m_major == expected.m_major) && (api.m_minor >= expected.m_minor);
}

constexpr inline bool isVersionAtLeast(CSemanticVersion api, CSemanticVersion expected)
{
    return isVersionCompatible(api, expected) || (api.m_major > expected.m_major);
}

constexpr inline bool isVersionExactly(CSemanticVersion api, CSemanticVersion expected) { return api == expected; }

// An external struct to test how to use it.
template <typename VersionType, VersionInt MajorValue2, VersionInt MinorValue2>
struct IsVersionCompatible
{
    static constexpr bool value = isVersionCompatible(VersionType(), CSemanticVersion(MajorValue2, MinorValue2, 0));
    static_assert(value, "Compatible version needed. See compiler error message how it was instantiated.");
};

template <typename VersionType, VersionInt MajorValue2, VersionInt MinorValue2>
struct IsVersionAtLeast
{
    static constexpr bool value = isVersionAtLeast(VersionType(), CSemanticVersion(MajorValue2, MinorValue2, 0));
    static_assert(value, "Higher version needed. See compiler error message how it was instantiated.");
};

template <typename VersionType, VersionInt MajorValue2, VersionInt MinorValue2, VersionInt PatchValue2>
struct IsVersionExactly
{
    static constexpr bool value =
        isVersionExactly(VersionType(), CSemanticVersion(MajorValue2, MinorValue2, PatchValue2));
    static_assert(value, "Exact same version needed. See compiler error message how it was instantiated.");
};

} // namespace vfc
#endif // VFC_VERSION_HPP_INCLUDED

