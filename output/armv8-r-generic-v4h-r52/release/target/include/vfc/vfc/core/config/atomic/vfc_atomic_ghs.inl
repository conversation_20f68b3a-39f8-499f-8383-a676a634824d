//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_ATOMIC_GHS_INL_INCLUDED
#define ZX_VFC_ATOMIC_GHS_INL_INCLUDED

#if !defined(VFC_COMPILER_GHS)
static_assert(false, "A _ghs.inl included not using Greenhills.");
#endif

#if defined(VFC_PLATFORM_IVS_TRICORE)
#include <intrinsics.h>
#elif defined(VFC_PLATFORM_RH850)
#include <ghs_barrier.h>
#include <v800_ghs.h>
#include <ghs_barrier.h>
#endif

namespace vfc
{
namespace intern
{

#if defined(VFC_PLATFORM_IVS_TRICORE)

template <>
inline bool AtomicOps<uint32_t>::compare_exchange_weak(
    uint32_t&         memory,
    uint32_t&         expected,
    uint32_t          desired,
    vfc::EMemoryOrder successOrder,
    vfc::EMemoryOrder failureOrder)
{
    // GHS intrinsics, "MULTI: Building Applications for Embedded TriCore" p. 728:
    // int __cmpswapw(int *memword, int value, int cmpval);

    static_assert(sizeof(int) == sizeof(uint32_t), "sizeof(int) is unexpected on Tricore!");

    // We cast because we really want to just have bitpatterns, no conversions!
    int* const mem_int      = reinterpret_cast<int*>(&memory);
    const int  desired_int  = static_cast<int>(desired);
    const int  expected_int = static_cast<int>(expected);

    const int mem_before = __cmpswapw(mem_int, desired_int, expected_int);

    if (mem_before == expected_int)
    {
        return true;
    }
    else
    {
        expected = static_cast<uint32_t>(mem_before);
        return false;
    }
}

template <>
inline uint32_t AtomicOps<uint32_t>::exchange(uint32_t& memory, uint32_t desired, vfc::EMemoryOrder order)
{
    // GHS intrinsics, "MULTI: Building Applications for Embedded TriCore" p. 728:
    // int __swapw(int *memword, int value);

    static_assert(sizeof(int) == sizeof(uint32_t), "sizeof(int) is unexpected on Tricore!");

    // We cast because we really want to just have bitpatterns, no conversions!
    int* const mem_int     = reinterpret_cast<int*>(&memory);
    const int  desired_int = static_cast<int>(desired);

    const int ret = __swapw(mem_int, desired_int);

    return static_cast<uint32_t>(ret);
}
#elif defined(VFC_PLATFORM_RH850)

inline int compare_exchange_weak_intern(uint32_t& memory, uint32_t& expected, uint32_t desired)
{
    // GHS intrinsics, "MULTI: Building Applications for Embedded V850 and RH850" p. 838:
    // int __CAXI(int *, int, int);

    static_assert(sizeof(int) == sizeof(uint32_t), "sizeof(int) is unexpected on RH850!");

    // We cast because we really want to just have bitpatterns, no conversions!
    int* const mem_int      = reinterpret_cast<int*>(&memory);
    const int  desired_int  = static_cast<int>(desired);
    const int  expected_int = static_cast<int>(expected);

    const int mem_before = __CAXI(mem_int, expected_int, desired_int);

    return static_cast<uint32_t>(mem_before);
}

template <>
inline bool AtomicOps<uint32_t>::compare_exchange_weak(
    uint32_t&         memory,
    uint32_t&         expected,
    uint32_t          desired,
    vfc::EMemoryOrder successOrder,
    vfc::EMemoryOrder failureOrder)
{
    const uint32_t mem_before = compare_exchange_weak_intern(memory, expected, desired);

    if (mem_before == expected)
    {
        return true;
    }
    else
    {
        expected = mem_before;
        return false;
    }
}

template <>
inline uint32_t AtomicOps<uint32_t>::exchange(uint32_t& memory, uint32_t desired, vfc::EMemoryOrder order)
{
    return compare_exchange_weak_intern(memory, memory, desired);
}
#else
// other plattforms not yet supported!
#endif

#if defined(VFC_PLATFORM_RH850)
template <>
inline void Fence<DummyType>::seqCst()
{
    // "MULTI: Building Applications for Embedded V850 and RH850" p. 853:
    // Adds a barrier that forces all loads, stores, and low-level operations sequenced before the barrier to occur
    // in instruction execution order before the barrier, and all loads, stores, and low-level operations sequenced
    // after the barrier to occur in instruction execution order after the barrier.
    //__memory_changed();

    // Old ASM instruction:
    __asm volatile("syncm");
}
#elif defined(VFC_PLATFORM_IVS_TRICORE)
template <>
inline void Fence<DummyType>::seqCst()
{
    // GHS intrinsics, "MULTI: Building Applications for Embedded TriCore" p. 727:
    // The instruction inserted has the same name as the intrinsic function.
    __dsync();
}
#elif defined(VFC_PLATFORM_IVS_ARM)
template <>
inline void Fence<DummyType>::seqCst()
{
    __asm volatile("dmb" ::: "memory");
}
#elif defined(VFC_PLATFORM_EPPC)
template <>
inline void Fence<DummyType>::seqCst()
{
    asm("msync");
}
#endif

template <>
inline void Fence<DummyType>::acqRel()
{
    Fence<>::seqCst();
}

} // namespace intern
} // namespace vfc

#endif

