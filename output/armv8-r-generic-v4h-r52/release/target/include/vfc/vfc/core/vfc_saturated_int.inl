//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_SATURATED_INT_INL_INCLUDED
#define VFC_SATURATED_INT_INL_INCLUDED

#include "vfc/core/vfc_math.hpp"  // vfc::numeric_limits
#include "vfc/core/vfc_types.hpp" // vfc::int32_t
#include "vfc_saturated_int.hpp"

template <class ValueType>
inline vfc::TSaturatedInt<ValueType>::TSaturatedInt() : m_value()
{
    // intentionally left blank
}

template <class ValueType>
inline vfc::TSaturatedInt<ValueType>::TSaturatedInt(
    const ValueType& f_value) // PRQA S 2180 # Technical debt L2 id=00c1d0fd2bbe3991492a-df745d4ba7aa4330 // PRQA S 3800
                              // # Technical debt L4 id=00c17fa95e59ae334a4c-fcda0f0a52714e93
    : m_value(f_value)
{
    // intentionally left blank, assignment in initializer
}

template <class ValueType>
template <class OtherValueType>
inline vfc::TSaturatedInt<ValueType>::TSaturatedInt(
    const OtherValueType& f_value) // PRQA S 2180 # Technical debt L2 id=00c1fc8dfe816b584063-48bd70cac4677648
{
    VFC_STATIC_ASSERT(1 == vfc::TIsIntegral<OtherValueType>::value); // Only integer are supported
    VFC_STATIC_ASSERT(8 > sizeof(OtherValueType));                   // 64 bit or greater are not supported
    VFC_STATIC_ASSERT(0 == TIsPointer<OtherValueType>::value);       // Pointer are not supported

    // when both source and dest are smaller then int32_t then int32 is enough, else we need to go int64_t
    const bool l_smallTypes =
        (sizeof(OtherValueType) < 4) &&
        (sizeof(ValueType) < 4); // PRQA S 2427 # Technical debt L2 id=00c102e695d474914c85-b0861bb37bbe795e
    using l_value_type_extended = typename vfc::TIf<l_smallTypes, vfc::int32_t, vfc::int64_t>::type;

    m_value = limitRange<l_value_type_extended, ValueType>(static_cast<l_value_type_extended>(f_value));
}

template <class ValueType>
template <class OtherValueType>
inline vfc::TSaturatedInt<ValueType>::TSaturatedInt(const TSaturatedInt<OtherValueType>& f_rhs)
{
    VFC_STATIC_ASSERT(sizeof(OtherValueType) <= sizeof(ValueType));

    const bool l_valueTypeIsUnsigned =
        static_cast<bool>(vfc::TIsUnsignedArithmetic<ValueType>::value); // PRQA S 4209 # Technical debt L2
                                                                         // id=00c1ff84306d85a94bc8-2522f73d2ade3820
    const bool l_otherValueTypeIsUnsigned = static_cast<bool>(
        vfc::TIsUnsignedArithmetic<OtherValueType>::value); // PRQA S 4209 # Technical debt L2
                                                            // id=00c1633dab279ddf48eb-ba5534b23538d4bf
    VFC_STATIC_ASSERT(l_valueTypeIsUnsigned == l_otherValueTypeIsUnsigned);

    m_value = vfc::numeric_cast<ValueType>(f_rhs.value());
}

template <class ValueType>
inline ValueType vfc::TSaturatedInt<ValueType>::value() const
{
    return m_value;
}

template <class ValueType>
inline vfc::TSaturatedInt<ValueType>& vfc::TSaturatedInt<ValueType>::operator>>=(vfc::uint32_t f_shift)
{
    static_assert(static_cast<int32_t>(-1) >> 1 == -1, "Not using arithmetical right-shift");
    VFC_REQUIRE2(
        (static_cast<vfc::uint32_t>(f_shift) < static_cast<vfc::uint32_t>(sizeof(ValueType) * CHAR_BIT)),
        "f_shift_i shall not be greater than or equal to the width of the promoted m_value, otherwise undefined "
        "behavior to shift");

    m_value >>= f_shift; // PRQA S 3003 # Technical debt L2 id=00c1fd80e7080104442f-ed491f5abd1d6e9f // PRQA S 3000 #
                         // Technical debt L2 id=00c1c840566f0b7f4548-bec55c506035e999 // PRQA S 3010 # Technical debt
                         // L2 id=00c1b3fa02e4da5e4104-dc8ee00a673eb431
    return (*this);
}

template <class ValueType>
inline vfc::TSaturatedInt<ValueType> vfc::TSaturatedInt<ValueType>::operator>>(
    vfc::uint32_t f_shift) // PRQA S 4211 # Technical debt L4 id=00c10f2002d7c3c34461-e6228f478179bbdd
{
    self_type l_result(m_value);
    l_result >>= f_shift;
    return l_result;
}

template <class ValueType>
inline vfc::TSaturatedInt<ValueType>& vfc::TSaturatedInt<ValueType>::operator<<=(vfc::uint32_t f_shift)
{
    static_assert(
        numeric_limits<ValueType>::is_specialized, "value type must have proper specialization of numeric limits");

    if (0 == m_value) // Shift to zero is zero
    {
        return (*this);
    }

    if (1 == sizeof(ValueType) && f_shift > 7) // Overflow check for 8 bit value type
    {
        if (isNegative(m_value)) // PRQA S 2880 # Technical debt L2 id=00c16abc0c8551fc419f-836041d260e9b583
        {
            m_value = vfc::numeric_limits<ValueType>::min();
        }
        else
        {
            m_value = vfc::numeric_limits<ValueType>::max();
        }
    }
    else if (2 == sizeof(ValueType) && f_shift > 15) // Overflow check for 16 bit value type
    {
        if (isNegative(m_value)) // PRQA S 2880 # Technical debt L2 id=00c16f36bce57b434c21-836041d260e9b583
        {
            m_value = vfc::numeric_limits<ValueType>::min();
        }
        else
        {
            m_value = vfc::numeric_limits<ValueType>::max();
        }
    }
    else if (4 == sizeof(ValueType) && f_shift > 31) // Overflow check for 32 bit value type
    {
        if (isNegative(m_value)) // PRQA S 2880 # Technical debt L2 id=00c102d14304adb74761-836041d260e9b583
        {
            m_value = vfc::numeric_limits<ValueType>::min();
        }
        else
        {
            m_value = vfc::numeric_limits<ValueType>::max();
        }
    }
    else
    {
        value_type_extended l_sum = vfc::numeric_cast<value_type_extended>(m_value);

        if (isNegative(m_value))
        {
            l_sum = -l_sum;
            l_sum <<= f_shift; // PRQA S 3003 # Technical debt L2 id=00c105dae8f5e5644727-01f41f33155799f3 // PRQA S
                               // 3000 # Technical debt L2 id=00c1f43323c3199d47e4-1aa93667531deb53
            l_sum = -l_sum;
        }
        else
        {
            l_sum <<= f_shift; // PRQA S 3003 # Technical debt L2 id=00c13f4a92bdcaa4428e-01f41f33155799f3 // PRQA S
                               // 3000 # Technical debt L2 id=00c1d13d537e53954071-1aa93667531deb53
        }

        m_value = limitRange<value_type_extended, ValueType>(l_sum);
    }

    return (*this);
}

template <class ValueType>
inline vfc::TSaturatedInt<ValueType> vfc::TSaturatedInt<ValueType>::operator<<(
    vfc::uint32_t f_shift) // PRQA S 4211 # Technical debt L4 id=00c1eab228a40ee348d4-a95a3c6ada7767fa
{
    self_type l_result(m_value);
    l_result <<= f_shift;
    return l_result;
}

template <class ValueType>
inline bool vfc::TSaturatedInt<ValueType>::operator==(const TSaturatedInt& f_rhs) const
{
    return (this->m_value == f_rhs.m_value);
}

template <class ValueType>
inline bool vfc::TSaturatedInt<ValueType>::operator!=(const TSaturatedInt& f_rhs) const
{
    return !(*this == f_rhs);
}

template <class ValueType>
inline bool vfc::TSaturatedInt<ValueType>::operator<(const TSaturatedInt& f_rhs) const
{
    return (this->m_value < f_rhs.m_value);
}

template <class ValueType>
inline bool vfc::TSaturatedInt<ValueType>::operator>(const TSaturatedInt& f_rhs) const
{
    return (this->m_value > f_rhs.m_value);
}

template <class ValueType>
inline bool vfc::TSaturatedInt<ValueType>::operator<=(const TSaturatedInt& f_rhs) const
{
    return (this->m_value <= f_rhs.m_value);
}

template <class ValueType>
inline bool vfc::TSaturatedInt<ValueType>::operator>=(const TSaturatedInt& f_rhs) const
{
    return (this->m_value >= f_rhs.m_value);
}

template <class ValueType>
inline vfc::TSaturatedInt<ValueType>& vfc::TSaturatedInt<ValueType>::operator+=(
    const TSaturatedInt& f_rhs) // PRQA S 2637 # Technical debt L4 id=00c1b113672f28c144bd-f27a5c05119baf00
{
    ValueType l_value = f_rhs.value();

    value_type_extended l_sum =
        vfc::numeric_cast<value_type_extended>(m_value) + vfc::numeric_cast<value_type_extended>(l_value);

    m_value = limitRange<value_type_extended, ValueType>(l_sum);

    return (*this);
}

template <class ValueType>
inline vfc::TSaturatedInt<ValueType> vfc::TSaturatedInt<ValueType>::operator+(
    const TSaturatedInt& f_rhs) // PRQA S 4211 # Technical debt L4 id=00c1d9d48d15c42f4f9f-ff9c268a82e5a07f
{
    self_type l_result(m_value);

    l_result += f_rhs;

    return l_result;
}

template <class ValueType>
inline vfc::TSaturatedInt<ValueType>& vfc::TSaturatedInt<ValueType>::operator-=(
    const TSaturatedInt& f_rhs) // PRQA S 2637 # Technical debt L4 id=00c1645e1a4ef46e4bf4-574ac1ea5663dc5b
{
    ValueType l_value = f_rhs.value();

    value_type_extended l_sum =
        vfc::numeric_cast<value_type_extended>(m_value) - vfc::numeric_cast<value_type_extended>(l_value);

    m_value = limitRange<value_type_extended, ValueType>(l_sum);

    return (*this);
}

template <class ValueType>
inline vfc::TSaturatedInt<ValueType> vfc::TSaturatedInt<ValueType>::operator-(
    const TSaturatedInt& f_rhs) // PRQA S 4211 # Technical debt L4 id=00c165cb756d9eb34800-4cd193192b90b63f
{
    self_type l_result(m_value);

    l_result -= f_rhs;

    return l_result;
}

template <class ValueType>
inline vfc::TSaturatedInt<ValueType>& vfc::TSaturatedInt<ValueType>::operator*=(
    const TSaturatedInt& f_rhs) // PRQA S 2637 # Technical debt L4 id=00c133e47bfd562d44f7-a6c1682c09929e6e
{
    // Exclusive border for the checks in multiplication,
    // whether the result type will fit in a 32-bit-integer,
    // Otherwise 64-bit-integer will be taken.
    // 1-Byte and 2-Byte integers will fit in a 32-bit-integer, but 3-Byte not:
    // 2^24 * 2^24 = 2^48  => Bigger than 32-bit-integer
    static constexpr vfc::int32_t MultiplicationBorderExclusive = 3;

    using l_value_type_extended = typename vfc::TIf<
        (!vfc::TIsUnsignedArithmetic<ValueType>::value),
        typename vfc::TIf<(sizeof(ValueType) < MultiplicationBorderExclusive), vfc::int32_t, vfc::int64_t>::type,
        typename vfc::TIf<(sizeof(ValueType) < MultiplicationBorderExclusive), vfc::uint32_t, vfc::uint64_t>::type>::
        type;

    ValueType l_value = f_rhs.value();

    l_value_type_extended l_sum =
        vfc::numeric_cast<l_value_type_extended>(m_value) * vfc::numeric_cast<l_value_type_extended>(l_value);

    m_value = limitRange<l_value_type_extended, ValueType>(l_sum);

    return (*this);
}

template <class ValueType>
inline vfc::TSaturatedInt<ValueType> vfc::TSaturatedInt<ValueType>::operator*(
    const TSaturatedInt& f_rhs) // PRQA S 4211 # Technical debt L4 id=00c1915f3c3e757e4599-46e8948a4e977be0
{
    self_type l_result(m_value);

    l_result *= f_rhs;

    return l_result;
}

template <class ValueType>
inline vfc::TSaturatedInt<ValueType>& vfc::TSaturatedInt<ValueType>::operator/=(
    const TSaturatedInt& f_rhs) // PRQA S 2637 # Technical debt L4 id=00c1d46b84303bcb4fa3-9f1cc10fb661e41f
{
    ValueType l_value = f_rhs.value();
    VFC_REQUIRE2(0 != l_value, "division by zero is not supported");

    value_type_extended l_quot = static_cast<value_type_extended>(m_value);
    l_quot /= static_cast<value_type_extended>(l_value);

    m_value = limitRange<value_type_extended, ValueType>(l_quot);

    return (*this);
}

template <class ValueType>
inline vfc::TSaturatedInt<ValueType> vfc::TSaturatedInt<ValueType>::operator/(
    const TSaturatedInt& f_rhs) // PRQA S 4211 # Technical debt L4 id=00c142e17ec27a4642aa-ca997ec82eef56a0
{
    self_type l_result(m_value);

    l_result /= f_rhs;

    return l_result;
}

template <class ValueType>
template <class OtherValueType>
inline vfc::TSaturatedInt<OtherValueType> vfc::TSaturatedInt<ValueType>::extend_to() const

{
    VFC_STATIC_ASSERT(sizeof(OtherValueType) > sizeof(ValueType));

    OtherValueType l_value = numeric_cast<OtherValueType>(m_value);

    if (true == vfc::TIsUnsignedArithmetic<OtherValueType>::value && isNegative(m_value))
    {
        l_value = 0; // PRQA S 2880 # Technical debt L2 id=00c1e9e65f48fe22409d-f1fac72f8879212d
    }

    return TSaturatedInt<OtherValueType>(l_value);
}

template <class ValueType>
template <class OtherValueType>
inline vfc::TSaturatedInt<OtherValueType> vfc::TSaturatedInt<ValueType>::truncate_to() const
{
    VFC_STATIC_ASSERT(sizeof(ValueType) >= sizeof(OtherValueType));
    return truncate_to<OtherValueType>(typename TInt2Boolean<sizeof(ValueType) == sizeof(OtherValueType)>::type());
}

template <class ValueType>
template <class OtherValueType>
inline vfc::TSaturatedInt<OtherValueType> vfc::TSaturatedInt<ValueType>::truncate_to(true_t) const
{
    static_assert(
        numeric_limits<OtherValueType>::is_specialized,
        "other value type must have proper specialization of numeric limits");

    OtherValueType l_value = vfc::numeric_cast<OtherValueType>(m_value);

    const bool l_otherValueTypeIsUnsigned = static_cast<bool>(vfc::TIsUnsignedArithmetic<OtherValueType>::value);

    if (l_otherValueTypeIsUnsigned) // OtherValueType is unsigned
    {
        if (m_value <= 0) // PRQA S 2880 # Technical debt L2 id=00c1577e4c7e60c74ae8-2c78d8b3bea6eb9e
        {
            l_value = 0;
        }
    }
    else // OtherValueType is signed
    {
        value_type_extended l_m_value = vfc::numeric_cast<value_type_extended>(
            m_value); // PRQA S 2880 # Technical debt L2 id=00c1423f5db2cba34b8e-74e74a042e114c2d

        if (l_m_value >= vfc::numeric_cast<value_type_extended>(vfc::numeric_limits<OtherValueType>::max()))
        {
            l_value = vfc::numeric_limits<OtherValueType>::max();
        }
    }

    return TSaturatedInt<OtherValueType>(l_value);
}

template <class ValueType>
template <class OtherValueType>
inline vfc::TSaturatedInt<OtherValueType> vfc::TSaturatedInt<ValueType>::truncate_to(false_t) const
{
    OtherValueType l_value =
        limitRange<value_type_extended, OtherValueType>(vfc::numeric_cast<value_type_extended>(m_value));

    return TSaturatedInt<OtherValueType>(l_value);
}

template <class ValueType>
template <class InputValueType, class ReturnValueType>
inline ReturnValueType vfc::TSaturatedInt<ValueType>::limitRange(const InputValueType& f_value)
{
    static_assert(
        numeric_limits<ReturnValueType>::is_specialized,
        "return value type must have proper specialization of numeric limits");

    // Saturation logic only works for a larger input data type than the return data type
    VFC_STATIC_ASSERT(sizeof(ReturnValueType) < sizeof(InputValueType));
    // Numeric cast from signed arithmetic to unsigned arithmetic is not supported
    VFC_STATIC_ASSERT(
        0 ==
        (vfc::TIsUnsignedArithmetic<InputValueType>::value && !vfc::TIsUnsignedArithmetic<ReturnValueType>::value));

    ReturnValueType l_value = vfc::numeric_cast<ReturnValueType>(f_value);

    if (f_value >= vfc::numeric_cast<InputValueType>(vfc::numeric_limits<ReturnValueType>::max()))
    {
        l_value = vfc::numeric_limits<ReturnValueType>::max();
    }
    else if (f_value <= vfc::numeric_cast<InputValueType>(vfc::numeric_limits<ReturnValueType>::min()))
    {
        l_value = vfc::numeric_limits<ReturnValueType>::min();
    }
    else
    {
    }

    return l_value;
}

#endif // VFC_SATURATED_INT_INL_INCLUDED

