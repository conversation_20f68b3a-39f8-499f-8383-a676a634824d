//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_FUNCTIONAL_HPP_INCLUDED
#define VFC_FUNCTIONAL_HPP_INCLUDED

#include "vfc/core/vfc_metaprog.hpp"

namespace vfc
{ // namespace vfc opened

//=============================================================================
//  DOXYGEN DEFGROUP vfc_group_core_generic_functional BEGIN
//-----------------------------------------------------------------------------
/// @defgroup vfc_group_core_generic_functional
/// @ingroup vfc_group_core_generic
/// @brief Analogous to standar libary "functional" this group also provides
/// strong arbitrary function.
/// @{
//=============================================================================

//=============================================================================
// isGreaterEnum
//-----------------------------------------------------------------------------
/// Compares two enum values of the same enum type and checks wether the lhs
/// value is greater than the rhs value (lhs > rhs). Can be used for template
/// Metaprogramming. Template type deduction is applicable for this function and
/// can be used as follows.
///
/// Only enum types are allowed as templater paramter. This will be ensured via
/// an assert.
///
/// enum MyEnum {
///     vfc::uint32_t value_9 = 9,
///     vfc::uint32_t value_7 = 7
/// }
///
/// std::cout << vfc::isGreaterEnum(value_9, value_7); /// will print true
///
/// @tparam Type enum type of the lhs and rhs values
/// @param lhs first enum value to check (to check greater than rhs)
/// @param rhs second enum value to check
/// @return true if the first enum value is greater than the second, otherwise false

//=============================================================================

template <typename Type>
constexpr bool isGreaterEnum(const Type& lhs, const Type& rhs)
{
    static_assert(TIsEnumType<Type>::value, "EnumType must be of type enum");
    return (lhs > rhs);
}

//=============================================================================
// isEqualEnum
//-----------------------------------------------------------------------------
/// Compares two enum values of the same enum type and checks wether the lhs
/// value rhs value are equal (lhs == rhs). Can be used for template
/// Metaprogramming. Template type deduction is applicable for this function and
/// can be used as follows.
///
/// Only enum types are allowed as templater paramter. This will be ensured via
/// an assert.
///
/// enum MyEnum {
///     vfc::uint32_t value_9 = 9
/// }
///
/// std::cout << vfc::isEqualEnum(value_9, value_9); /// will print true
///
/// @tparam FirstType enum type of the lhs value
/// @tparam SecondType enum type of the rhs value
/// @param lhs first enum value
/// @param rhs second enum value
/// @return true if the enum values are equal, otherwise false

//=============================================================================

template <typename FirstType, typename SecondType>
constexpr bool isEqualEnum(FirstType lhs, SecondType rhs)
{
    static_assert((TIsSameType<FirstType, SecondType>::value), "EnumTypes must be not equal");
    static_assert(TIsEnumType<FirstType>::value, "FirstType must be of type enum");
    static_assert(TIsEnumType<SecondType>::value, "SecondType must be of type enum");
    return (lhs == rhs);
}

//=============================================================================
//  DOXYGEN DEFGROUP vfc_group_core_generic_functional END
//-----------------------------------------------------------------------------
/// @}
//=============================================================================

} // namespace vfc

#endif // VFC_FUNCTIONAL_HPP_INCLUDED

//=============================================================================

