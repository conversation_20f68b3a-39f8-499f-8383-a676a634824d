//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_TYPES_HPP_INCLUDED
#define ZX_VFC_TYPES_HPP_INCLUDED

// vfc includes
#include "vfc/core/vfc_config.hpp"

//=========================================================================
// DOXYGEN ADDTOGROUP vfc_group_core_types
//-------------------------------------------------------------------------
/// @par Reference:
///
/// The header vfc_types.hpp provides the type definitions useful for writing
/// portable code that requires certain integer widths.
/// Use it in preference to <stdint.h> for enhanced portability
///
/// Following type definitions are provided by vfc:
///
/// - int8_t
/// - uint8_t
/// - int16_t
/// - uint16_t
/// - int32_t
/// - uint32_t
/// - int64_t
/// - uint64_t
/// .
///
/// The type definition int@#_t, with @# replaced by the width, designates a signed
/// integer type of exactly @# bits.\n
/// Similarly, the type definition uint@#_t designates and unsigned integer type of
/// exactly @# bits.
///
/// @code
/// vfc::int8_t myInt_i8; // denotes an 8-bit signed integer type.
/// vfc::uint8_t myUInt_u8; // denotes an 8-bit unsigned integer type.
/// @endcode
///
/// The 64-bit types required by the C standard are not required in the vfc
/// header, and may not be supplied in all implementations,
/// because long long is not [yet] included in the C++ standard. If the 64bit
/// types are not available on your platform, the macro VFC_NO_INT64 is
/// defined by vfc, see also @ref vfc_group_core_config_platform .
///
/// This implementation may #include the compiler supplied <stdint.h>,
/// if present.
/// @par Error handling:
/// In case of a manual creation of the basic types, a compiler error is given in case
/// any of the types of size 8,16 or 32 could not be identified.
/// @par Timing/Scheduling constraints:
/// On 32 bit architectures, all types up to 32 bits are supposed to be thread safe,
/// when properly aligned. 64 bit types (int and double) are not thread safe.
/// $Source: vfc_types.hpp $

/// @defgroup vfc_group_core_types_c99 C99 Integral Types
/// @ingroup vfc_group_core_types
//=========================================================================

///////////////////////////////////////
// start with integral types 8..32bit
///////////////////////////////////////
#include <climits>
#include <cstdint>
#include <cstddef> // std::max_align_t

namespace vfc
{
// signed first
using ::std::int16_t;
using ::std::int32_t;
using ::std::int8_t;

// unsigned
using ::std::uint16_t;
using ::std::uint32_t;
using ::std::uint8_t;
} // namespace vfc

///////////////////////////////////////
// now 64bit (optional)
///////////////////////////////////////

/// C++11 has fixed-width integer types like `uint8_t`, `int32_t` etc., but they are optional. VFC requires all types to
/// be functional. This checks for the definition of `int64_t` and `uint64_t`. @see ISO/IEC 14882:2011(E) 18.4.1
/// [cstdint]
#if defined(UINT64_MAX) && (UINT64_MAX == 18446744073709551615ULL)
namespace vfc
{
using int64_t  = ::int64_t;
using uint64_t = ::uint64_t;
} // namespace vfc

// Is also easy - check for visualc __int64
#elif defined VFC_HAS_MS_INT64
namespace vfc
{ // open namespace vfc

using int64_t  = __int64;
using uint64_t = unsigned __int64;

} // namespace vfc

// long long has to be present, now check if limits are right
#elif (defined(ULLONG_MAX) && ULLONG_MAX == 18446744073709551615ULL) ||                                                \
    (defined(_MSL_SIZEOF_LONG_LONG) && (_MSL_SIZEOF_LONG_LONG == 8)) ||                                                \
    (defined(ULONG_LONG_MAX) && ULONG_LONG_MAX == 18446744073709551615ULL) ||                                          \
    (defined(ULONGLONG_MAX) && ULONGLONG_MAX == 18446744073709551615ULL)
namespace vfc
{ // open namespace vfc

using int64_t  = long long;
using uint64_t = unsigned long long;

} // namespace vfc

// last try, is long type big enough?
#elif ULONG_MAX == 18446744073709551615U // 2^64-1
namespace vfc
{ // open namespace vfc

using int64_t  = long;
using uint64_t = unsigned long;

} // namespace vfc

#else
  // Nothing worked, so give up.
static_assert(false, "VFC needs int64_t and uint64_t.");
#endif

///////////////////////////////////////
// floating point - assuming IEEE
///////////////////////////////////////

namespace vfc
{

/// single-precision (32bit) floating point type.
/// @ingroup vfc_group_core_types
using float32_t = float;

#ifdef VFC_ENABLE_FLOAT64_TYPE
/// double-precision (64bit) floating point type.
/// @ingroup vfc_group_core_types
using float64_t = double;
#endif

} // namespace vfc

///////////////////////////////////////
// remaining types
///////////////////////////////////////

namespace vfc
{

// promotes size_t to vfc namespace.
// we can't document using declarations with doxygen!
using ::std::size_t;

// promotes ptrdiff_t to vfc namespace.
// we can't document using declarations with doxygen!
using ::std::ptrdiff_t;

/// don't assume anything for char type, except sizeof(char_t)==1.
/// use it only for string literals and compatibility with libc.
/// @ingroup vfc_group_core_types
using char_t = char;

// Integer type capable of holding a value converted from a void pointer
using ::std::uintptr_t;

// !! no boolean type definition, because boolean is a fundamental type in c++ !!
// type definition bool bool_t

//  TIntegralConstant
/// Template wrapping a static constant, making it a type. Used for template metaprogramming. Note that using
/// `TIntegralConstant` in a way, that a reference to the memory of `value` is required ("ODR-use"), leads to a
/// memory allocation in the linker section for constants. In conjunction with a high number arbitrary values this
/// can become a memory problem.
/// @tparam VType Type of the value.
/// @tparam Value Value given at compile time.
template <typename VType, VType Value>
struct TIntegralConstant
{
    using ValueType                  = VType;
    static constexpr ValueType value = Value;
    using type                       = TIntegralConstant<VType, Value>;
};

/// Definition of member 'value'.
template <typename VType, VType Value>
constexpr VType TIntegralConstant<VType, Value>::value;

//  bool_constant_t
/// Alias to `TIntegralConstant` of type `bool`. The two possible values are again aliased below.
/// @tparam BValue Value of the integral constant of type bool.
template <bool BValue>
using bool_constant_t = TIntegralConstant<bool, BValue>;

/// 'true' type for template metaprogramming.
/// Identifier is intentionally not conform to coding rules. Note that it is an alias to not require conversions.
/// @ingroup vfc_group_core_types vfc_group_core_metaprogram
using true_t = bool_constant_t<true>;

/// 'false' type for template metaprogramming.
/// Identifier is intentionally not conform to coding rules. Note that it is an alias to not require conversions.
/// @ingroup vfc_group_core_types vfc_group_core_metaprogram
using false_t = bool_constant_t<false>;

/// max_aligned_t, a renamed max_align_t
using max_aligned_t = ::std::max_align_t;

} // namespace vfc

///////////////////////////////////////
// tags
///////////////////////////////////////

namespace vfc
{
/// Tag struct for signaling that no initialization should take place.
/// For use in constructors and container functions.
struct uninitialized_tag
{
};

} // namespace vfc

#endif // ZX_VFC_TYPES_HPP_INCLUDED

