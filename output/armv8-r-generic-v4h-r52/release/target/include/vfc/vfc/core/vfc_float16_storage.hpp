//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_FLOAT16_STORAGE_HPP_INCLUDED
#define VFC_FLOAT16_STORAGE_HPP_INCLUDED

#include "vfc/core/vfc_assert.hpp"
#include "vfc/core/vfc_types.hpp"  // used for fundamental types
#include "vfc/core/vfc_config.hpp" // used for predefines for compiler-checks

namespace vfc_cppunit
{
class vfc_float16;
}

namespace vfc
{ // namespace vfc opened

enum EcfRound : vfc::int32_t
{
    NearestToEven, ///< bankers rounding: On tie case round to next even number
    ToInfinity,    ///< for positive numbers round to positive infinity for negative numbers to negative infinity
    // deprecated members
    VFC_ATR_DEPRECATED_ENUM_VALUE(NEAREST_TO_EVEN) = NearestToEven, ///< deprecated, use NearestToEven
    VFC_ATR_DEPRECATED_ENUM_VALUE(TO_INFINITY)     = ToInfinity     ///< deprecated, use ToInfinity
};

using VFC_ATR_POST_DEPRECATED(ECF_ROUND) = EcfRound;

//=============================================================================
// E X P O R T E D   C L A S S
//
// CFloat16Storage
//
//-----------------------------------------------------------------------------
/// @brief storage class for low-precision float values, no overloaded operators
///
/// Most hardware with floating point support does support float32_t but
/// sometimes that amount of precision is not needed for a specific
/// application. Several architectures support the 16bit half-float format
/// which has 10 mantissa, 5 exponent and 1 sign bit. Using
/// CFloat16Storage the stored half-float values can be converted to
/// float32_t values and back.
/// Attention: Conversion from float32_t bit to float16_t bit follows
/// with loss of accuracy!
///
/// @see ISO/IEC/IEEE 60559, Edition 1.0, 2011-06, Chapter 3.6, Table 3.5
//=============================================================================

class CFloat16Storage
{
  public:
    /// default constructor
    /// @dspec{1954}     The SW component "vfc" shall construct a Float16 storage instance which is intialized to the
    /// value zero.
    inline CFloat16Storage() : m_value(0U)
    {
        // intentionally left blank
    }

    /// construct from float32_t, allow implicit construction
    /// @dspec{423}     The SW component "vfc" shall construct a Float16 storage instance from a given float32_t
    ///                 value and a given rounding mode.
    /// @param f_value  the 32bit floating point number to construct the 16bit float from
    /// @param f_roundMode  the rounding mode used during construction
    inline CFloat16Storage(
        float32_t f_value); // PRQA S 2180 # Technical debt L2 id=00c1349e9cd2a98f4225-17dfff3d375798c3

    /// construct from float32_t, allow implicit construction
    /// @dspec{423}     The SW component "vfc" shall construct a Float16 storage instance from a given float32_t
    ///                 value and a given rounding mode.
    /// @param f_value  the 32bit floating point number to construct the 16bit float from
    /// @param f_roundMode  the rounding mode used during construction
    inline CFloat16Storage(float32_t f_value, EcfRound f_roundMode);

    /// contructor method from integer
    /// @dspec{424}     The SW component "vfc" shall construct and return a Float16 storage instance from a given
    ///                 uint16_t representation value.
    /// @param f_value  the 16bit integer number to construct the 16bit float from
    /// @return the constructed 16bit float
    static inline auto fromUint16(const uint16_t& f_value) -> CFloat16Storage;

    /// conversion to float32_t, return extended value with less
    /// mantissa significant bits set to 0
    /// @dspecref{425}
    inline auto toFloat32() const -> float32_t;

    /// conversion operator to float32_t
    /// @dspec{425}     The SW component "vfc " shall convert the given Float16 storage instance to a value of
    ///                 type float32_t and return the converted value.
    inline VFC_ATR_DEPRECATED2(operator float32_t, "Use float16_storage_t::toFloat32() instead")()
        const; // PRQA S 2181 # Technical debt L8 id=00c1566859b9acbf46fc-142a2d0968593895

    /// check if stored number is negative, mostly for tests
    /// @dspec{426}     The SW component "vfc" shall return the bool value "true" if a given Float16 storage
    ///                 instance represents a negative value, otherwise it shall return the bool value "false".
    /// @return true if 16bit is negative, otherwise false
    inline bool isNegative() const;

  private:
    /// apply enabled rounding mode for tie case
    static inline int32_t calcRoundOffset(uint32_t f_check_pattern, uint32_t f_check_bit, EcfRound f_roundMode);

    enum class EConstant : vfc::uint32_t
    {
        NoSignMask32   = 0x7FFFFFFFU,               ///< mask for non-sign bits for float32
        NoSignMask16   = 0x7FFFU,                   ///< mask for non-sign bits for float16
        SignMask32     = 0x80000000U,               ///< mask for sign bit for float32
        SignMask16     = 0x8000U,                   ///< mask for sign bit for float16
        ExpMask32      = 0x7F800000U,               ///< mask for exponent bits for float32
        ExpMask16      = 0x7C00U,                   ///< mask for exponent bits for float16
        MantissaMask32 = 0x007FFFFFU,               ///< mask for mantissa bits for float32
        MantissaMask16 = 0x03FFU,                   ///< mask for mantissa bits for float16
        ImplicitMask32 = 0x00800000U,               ///< implicit mantissa bit for float32
        ImplicitMask16 = 0x0400U,                   ///< implicit mantissa bit for float16
        InfBits16      = 0x7C00U,                   ///< signless bitpattern for infinity
        NaN32          = 0x7FC00000U,               ///< NaN mask for float32
        NaN16          = 0x7E00U,                   ///< NaN mask for float16
        SignShift      = 0x10U,                     ///< number of bits to shift sign bit from float32 to float16
        MaxExp16       = 0x1FU,                     ///< maximum exponent for float16
        ExpShift32     = 0x17U,                     ///< number of bits exponents in float32 are shifted
        ExpShift16     = 0x0AU,                     ///< number of bits exponents in float16 are shifted
        ExpShiftDiff   = (ExpShift32 - ExpShift16), ///< difference in shift distance from float32 to float16
        ExpBias32      = 0x7FU,                     ///< bias of exponent value in float32
        ExpBias16      = 0x0FU,                     ///< bias of exponent value in float16
        ExpBiasDiff    = (ExpBias32 - ExpBias16),   ///< difference in bias from float32 to float16
        RoundMask      = 0x00001FFFU,               ///< bit which indicates rounding when converting float32 to float16
        EvenBit        = 0x2000U,                   ///< bit to check for the bankers rounding case
        TieCheck       = 0x01000U,                  ///< bit mask used to check for round up, round down or tie case
        RoundMaskUnderflow =
            0x00FFFFFFU,            ///< bit which indicates rounding when converting float32 to float16, underflow case
        EvenBitUnderflow = 0x0001U, ///< bit to check for the bankers rounding case, underflow case
        TieCheckUnderflow =
            0x00800000U, ///< bit mask used to check for round up, round down or tie case, underflow case
    };

    /// union for alias-safe conversions between float32_t and uint32_t
    union Ufui32_t // PRQA S 2176 # Technical debt L2 id=00c1aa2395911c9e467a-4ef8c218878c4a3e // PRQA S 2406 #
                   // Technical debt L4 id=00c1254bc29af7284778-4204d1f1e654cc1f
    {
        float32_t m_float32;
        uint32_t  m_uint32;
    };

    /// templated explicit conversion of enum
    template <typename T>
    static auto as(const EConstant f_const)
        -> T // PRQA S 1724 # Technical debt L2 id=00c1f22d5948149940a2-1660304a27690543
    {
        return static_cast<T>(f_const);
    }

    uint16_t m_value;
    friend class ::vfc_cppunit::vfc_float16; // PRQA S 2107 # Technical debt L2 id=00c163d9622a47da4253-9455fb9e3682a512
};                                           // class CFloat16Storage

#ifdef VFC_NATIVE_FLOAT16_TYPE
/// wrapper to align interface to CFloat16Storage
/// disallow arithmetic operations
class CF16NativeStorage
{
  public:
    /// default constructor
    /// @dspecref{1954}
    inline CF16NativeStorage() : m_value(0.0F)
    {
        // intentionally left blank
    }

    /// construct from float32_t, allow implicit construction
    /// @dspecref{423}
    /// @param f_value  the 32bit floating point number to construct the 16bit float from
    inline CF16NativeStorage(float32_t f_value) : m_value(f_value)
    {
        // intentionally left blank
    }

    /// constructor with explicit rounding mode is not available in CF16NativeStorage
    CF16NativeStorage(float32_t f_value, EcfRound f_roundMode) = delete;

    /// conversion form uint16_t not available in CF16NativeStorage
    static inline auto fromUint16(const uint16_t& f_value) -> CFloat16Storage = delete;

    /// conversion to float32_t, return extended value with less
    /// mantissa significant bits set to 0
    /// @dspecref{425}
    inline auto toFloat32() const -> float32_t { return static_cast<float32_t>(m_value); }

    /// conversion to float32_t, return extended value with less
    /// mantissa significant bits set to 0
    /// @dspecref{425}
    inline VFC_ATR_DEPRECATED2(operator float32_t, "Use float16_storage_t::toFloat32() instead")() const
    {
        return toFloat32();
    }

    /// check if stored number is negative, mostly for tests
    /// @dspecref{426}
    /// @return true if 16bit is negative, otherwise false
    inline bool isNegative() const { return toFloat32() < 0.0F; }

  private:
    VFC_NATIVE_FLOAT16_TYPE m_value;
};

using float16_storage_t = CF16NativeStorage;
#else
using float16_storage_t = CFloat16Storage;

/// Overload function for vfc::isNAN for the type float16_storage_t
/// @param f_value to be checked for isNAN.
/// @return true, if f_value is NAN, else false
inline bool isNAN(const float16_storage_t& f_value);

#endif

} // namespace vfc

#include "vfc/core/vfc_float16_storage.inl"

#endif // VFC_FLOAT16_STORAGE_HPP_INCLUDED

