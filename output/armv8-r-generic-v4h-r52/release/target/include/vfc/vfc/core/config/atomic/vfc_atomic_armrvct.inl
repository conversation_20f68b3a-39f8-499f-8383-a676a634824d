//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_ATOMIC_ARMRVCTGHS_INL_INCLUDED
#define ZX_VFC_ATOMIC_ARMRVCTGHS_INL_INCLUDED

#if !defined(VFC_COMPILER_ARMRVCT) && !defined(VFC_COMPILER_AC6)
static_assert(false, "A _armrvct.inl included not using ARM compiler.");
#endif

#ifdef VFC_COMPILER_AC6
#include <arm_acle.h>
#endif

namespace vfc
{
namespace intern
{
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

template <>
inline bool AtomicOps<uint32_t>::compare_exchange_weak(
    uint32_t&         memory,
    uint32_t&         expected,
    uint32_t          desired,
    vfc::EMemoryOrder successOrder,
    vfc::EMemoryOrder failureOrder)
{
    return __atomic_compare_exchange_n(&memory, &expected, desired, true, successOrder, failureOrder);
}

template <>
inline bool AtomicOps<uint64_t>::compare_exchange_weak(
    uint64_t&         memory,
    uint64_t&         expected,
    uint64_t          desired,
    vfc::EMemoryOrder successOrder,
    vfc::EMemoryOrder failureOrder)
{
    return __atomic_compare_exchange_n(&memory, &expected, desired, true, successOrder, failureOrder);
}

template <>
inline uint32_t AtomicOps<uint32_t>::exchange(uint32_t& memory, uint32_t desired, vfc::EMemoryOrder order)
{
    return __atomic_exchange_n(&memory, desired, order);
}

template <>
inline uint64_t AtomicOps<uint64_t>::exchange(uint64_t& memory, uint64_t desired, vfc::EMemoryOrder order)
{
    return __atomic_exchange_n(&memory, desired, order);
}

#if defined(VFC_COMPILER_ARMRVCT)
template <>
inline void Fence<DummyType>::seqCst()
{
    __schedule_barrier(); // schedule barrier to prevent reordering of the dmb instruction
    __dmb(0xf);           // full system any-any ordering
    __schedule_barrier();
}
#elif defined(VFC_COMPILER_AC6)
template <>
inline void Fence<DummyType>::seqCst()
{
    // no schedule barrier as with AC6 the dmb also acts
    // as a schedule barrier according to the ARM support
    __dmb(0xf); // full system any-any ordering
}
#endif

template <>
inline void Fence<DummyType>::acqRel()
{
    Fence<>::seqCst();
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
} // namespace intern
} // namespace vfc

#endif

