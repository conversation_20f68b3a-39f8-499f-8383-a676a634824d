//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_GCC_HPP_INCLUDED
#define ZX_VFC_GCC_HPP_INCLUDED

#include <cstddef>
#include "vfc/core/vfc_preprocessor.hpp"

////////////////////////////////////
// GCC (and derived) compilers
//
// QNX platform uses GCC too. Command to get all compiler predefined macros is:
// /c/TCC/Tools/qnx_build_tools/7.0.0.SGA201702151847-safety2.0b-1_WIN64/qnx700/host/win64/x86_64/usr/bin/x86_64-pc-nto-qnx7.0.0-g++.exe
// -dM -E -x c++ - < /dev/null
////////////////////////////////////

////////////////////////////////////
// processor identification
////////////////////////////////////

#ifdef __i386__
#define VFC_PROCESSOR_IX86
#elif defined(__x86_64__)
#define VFC_PROCESSOR_IX86_64
#elif defined(__PPC)
#define VFC_PROCESSOR_PPC
#define VFC_EPPC_DETECTED
#elif defined(__MICROBLAZE__)
#define VFC_PROCESSOR_MICROBLAZE
#define VFC_EMB_DETECTED
#elif defined(__arm__)
#if defined(__ARM_ARCH_6__) || defined(__ARM_ARCH_6J__) || defined(__ARM_ARCH_6k__) || defined(__ARM_ARCH_6Z__) ||     \
    defined(__ARM_ARCH_6ZK__) || defined(__ARM_ARCH_6T2__)
#define VFC_ARM_DETECTED
#define VFC_PROCESSOR_ARM
#define VFC_PROCESSOR_ARMV6
#elif (__ARM_ARCH == 7)
#define VFC_ARM_DETECTED
#define VFC_PROCESSOR_ARM
#define VFC_PROCESSOR_ARMV7
#if (mcpu == cortex - a8)
#define VFC_PROCESSOR_ARM_CORTEXA8
#endif
#elif (__ARM_ARCH == 8)
#define VFC_ARM_DETECTED
#define VFC_PROCESSOR_ARM
#define VFC_PROCESSOR_ARMV8
#else
static_assert(false, "this arm processor is not supported");
#endif
#elif defined(__aarch64__)
#define VFC_ARM64_DETECTED
#define VFC_PROCESSOR_ARM64
#define VFC_PROCESSOR_ARMV8
#elif defined(__TRICORE__)
#define VFC_PROCESSOR_TRICORE
#define VFC_TRICORE_DETECTED
#else
static_assert(false, "processor not supported");
#endif

//////////////////////////////
// versions check
//////////////////////////////

namespace vfc
{
namespace intern
{
// we don't know gcc prior to version 3.4:
#if (__GNUC__ < 3) || ((__GNUC__ == 3) && (__GNUC_MINOR__ < 4))
#error "Compiler not supported or configured"
#endif
//
//    last known and checked version is 12.2.1:
#if (__GNUC__ > 12) || ((__GNUC__ == 12) && (__GNUC_MINOR__ > 3))
#error "Unknown compiler version"
#endif

// detected compiler version
using CompilerVersion = vfc::TSemanticVersion<__GNUC__, __GNUC_MINOR__, __GNUC_PATCHLEVEL__>;

// we don't know gcc prior to version 4.8:
using CompilerVersionMin = vfc::TSemanticVersion<4, 8, 0>;
static_assert(
    majorMinorLessEqual<CompilerVersionMin, CompilerVersion>(),
    "GCC compiler version not supported or configured, need at least version 4.8");

//    last known and checked version is 12.2.1:
using CompilerVersionMax = vfc::TSemanticVersion<12, 3, 0>;
static_assert(
    majorMinorLessEqual<CompilerVersion, CompilerVersionMax>(),
    "Unknown GCC compiler version, latest known version is 12.2");
} // namespace intern
} // namespace vfc

//////////////////////////////
// C++ version check
//////////////////////////////

// helper definition for readable version numbers, see
// https://gcc.gnu.org/onlinedocs/cpp/Common-Predefined-Macros.html
#define VFC_GCC_VERSION                                                                                                \
    (__GNUC__ * 10000 + __GNUC_MINOR__ * 100 +                                                                         \
     __GNUC_PATCHLEVEL__) // PRQA S 1026 # Technical debt L8 id=00e112ba0701f8a24d8e-e1061209daffbedf

// the first gcc version to support all c++14 features seem to be 5
// see https://gcc.gnu.org/projects/cxx-status.html#cxx14
#if (__cplusplus >= 201402L) && (VFC_GCC_VERSION > 50000)
#define VFC_USE_CPP14_FEATURES
#endif

///////////////////////////////
//  GNU C++ compiler setup
///////////////////////////////

#define VFC_COMPILER_STRING "GNU C++ version " __VERSION__

#ifndef VFC_COMPILER_GCC
#define VFC_COMPILER_GCC
#endif

#if defined(__HIGHTEC__)
#define VFC_COMPILER_HIGHTEC
#endif

//////////////////////////////
// exception handling
//////////////////////////////

#ifndef __EXCEPTIONS
#define VFC_NO_EXCEPTIONS
#endif

//////////////////////////////
// stdint.h support
//////////////////////////////

#if defined(_GLIBCXX_HAVE_STDINT_H) || defined(__QNX__)
  // QNX on Windows seems to have a stdint.h without defining _GLIBCXX_HAVE_STDINT_H
#define VFC_HAS_STDINT_H
#endif

//////////////////////////////
// long long support
//////////////////////////////

#if defined(_GLIBCXX_USE_LONG_LONG) || defined(__LONG_LONG_MAX__)
#define VFC_HAS_LONG_LONG
#endif

//////////////////////////////
// endianess setup
//////////////////////////////
#ifdef VFC_EPPC_DETECTED
#define VFC_BIG_ENDIAN
#endif

// macro abstraction for linker statements
#define VFC_LINKER_SECT(sect_name) __attribute__((section(sect_name)))

#ifndef VFC_DECL_FORCEINLINE
#define VFC_DECL_FORCEINLINE inline __attribute__((always_inline))
#endif

#ifndef VFC_ATR_NOINLINE_REQUEST
#define VFC_ATR_NOINLINE_REQUEST __attribute__((noinline))
#endif

#define VFC_TYPE_ALIGNMENT_PREFIX(val)
#define VFC_TYPE_ALIGNMENT_SUFFIX(val) __attribute__((aligned(val)))

#ifndef VFC_ATR_DEPRECATED
#define VFC_ATR_DEPRECATED(EXP) __attribute__((deprecated)) EXP
#endif

#ifndef VFC_ATR_DEPRECATED2
#define VFC_ATR_DEPRECATED2(EXP, MSG) __attribute__((deprecated(MSG))) EXP
#endif

#if (__GNUC__ > 6)

#ifndef VFC_ATR_POST_DEPRECATED
#define VFC_ATR_POST_DEPRECATED(EXP) EXP __attribute__((deprecated))
#endif

#ifndef VFC_ATR_POST_DEPRECATED2
#define VFC_ATR_POST_DEPRECATED2(EXP, MSG) EXP __attribute__((deprecated(MSG)))
#endif

#ifndef VFC_ATR_DEPRECATED_ENUM_VALUE
#define VFC_ATR_DEPRECATED_ENUM_VALUE(EXP) VFC_ATR_POST_DEPRECATED(EXP)
#endif

#ifndef VFC_ATR_DEPRECATED_ENUM_VALUE2
#define VFC_ATR_DEPRECATED_ENUM_VALUE2(EXP, MSG) VFC_ATR_POST_DEPRECATED2(EXP, MSG)
#endif

#endif

#ifndef VFC_ATR_NODISCARD
#define VFC_ATR_NODISCARD __attribute__((warn_unused_result))
#endif

#endif // ZX_VFC_GCC_HPP_INCLUDED
