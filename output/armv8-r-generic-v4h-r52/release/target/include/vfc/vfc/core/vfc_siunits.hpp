//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef SIUNITS_HPP_INCLUDED
#define SIUNITS_HPP_INCLUDED

#include "vfc/core/vfc_types.hpp"          // vfc::int32_t
#include "vfc/core/vfc_metaprog.hpp"       // TIf
#include "vfc/core/vfc_type_traits.hpp"    // TIsFundamental
#include "vfc/core/vfc_math.hpp"           // vfc::divide
#include "vfc/core/vfc_trig.hpp"           // validate_range
#include "vfc/core/vfc_siunits_helper.hpp" // For UnitInfoType & conversions
#include "vfc/core/vfc_util.hpp"           // for vfc::ignoreReturn

namespace vfc
{ // open namespace vfc

//=============================================================================
//  TSIUnits <>
//-----------------------------------------------------------------------------
/// TSIUnits class
/// The TSIUnits class is a generic template class that can be used to create
/// many siunit types with a desired datatype
/// $Source: vfc_siunits.hpp $
/// @tparam ValueType            Defines the DataType
/// @tparam UnitInfoType         Defines the type of SIUnit
/// @tparam RBInfoType           New specific Info type
/// @tparam UserType             Defines a specific UserType SIUnit
/// @tparam ConvertPolicyType    Defines the convert policy type

/// @ingroup                    vfc_core
//=============================================================================
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType, // new specific Info type
    class UserType                                         = vfc::CDefaultType,
    template <class, class, class> class ConvertPolicyType = vfc::TConvert>
class TSIUnits
{

  public:
    // typdefes
    using value_type   = ValueType;
    using user_type    = UserType;
    using self_type    = TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>;
    using unit_type    = UnitInfoType;
    using rbunit_type  = RBInfoType;
    using convert_type = ConvertPolicyType<UnitInfoType, UnitInfoType, ValueType>;

    using sqrt_promoted_type = TSIUnits<
        ValueType,
        typename vfc::TSIUnitSqrtPromote<UnitInfoType>::unit_info_type,
        typename vfc::TSIUnitRBSqrtPromote<RBInfoType>::unit_info_type,
        UserType,
        ConvertPolicyType>;

    using sqr_promoted_type = TSIUnits<
        ValueType,
        typename vfc::TSIUnitSqrPromote<UnitInfoType>::unit_info_type,
        typename vfc::TSIUnitRBSqrPromote<RBInfoType>::unit_info_type,
        UserType,
        ConvertPolicyType>;

    using trig_promoted_type = TSIUnits<
        ValueType,
        typename vfc::TSIUnitSinCosPromote<UnitInfoType>::unit_info_type,
        RBInfoType,
        UserType,
        ConvertPolicyType>;

    //---------------------------------------------------------------------
    /// Default constructor
    /// Calls default constructor of ValueType.
    /// If no initialization is intended use the constructor with
    /// uninitialized_tag.
    /// @dspec{1526}     The SW component "vfc" shall construct a new siunit instance
    ///                  where the value is default-initialized.
    /// $Source: vfc_siunits.hpp $

    //---------------------------------------------------------------------
    TSIUnits() : m_value()
    {
        static_assert(
            TIsValidArithmetic<ValueType>::value, "invalid arithmetic type used for SIUnit default constructor");
    }

    //---------------------------------------------------------------------
    /// Non-initializing constructor
    /// @dspec{1527}     The SW component "vfc" shall construct a new siunit instance
    ///                  where the value is not initialized
    /// $Source: vfc_siunits.hpp $

    //---------------------------------------------------------------------
    TSIUnits(uninitialized_tag) // PRQA S 2180 # Technical debt L2 id=00c10158a92809ed42b5-4ca8aa387e2e6ebd // PRQA S
                                // 4206 # Technical debt L2 id=00c1f83d15fa69244073-360fc2fd36180038
    // intentionally no m_value initialization
    {
        static_assert(
            TIsValidArithmetic<ValueType>::value,
            "invalid arithmetic type used for SIUnit non-initializing constructor");
    }

    //---------------------------------------------------------------------
    /// Explicit    Constructs with the specified value
    /// @dspec{1528}     The SW component "vfc" shall construct a new siunit instance
    ///                  with the given value.
    /// @param  f_value_r   Data to be stored
    /// $Source: vfc_siunits.hpp $

    //---------------------------------------------------------------------
    explicit constexpr TSIUnits(const ValueType& f_value_r) : m_value(f_value_r)
    {
        static_assert(
            TIsValidArithmetic<ValueType>::value, "invalid arithmetic type used for SIUnit ValueType constructor");
    }

    /// @brief Request default copy constructor for non type-changing copies.
    TSIUnits(const TSIUnits& f_value_r) = default;

    //---------------------------------------------------------------------
    /// Explicit    Constructs with the SIUnits type
    /// @dspec{1952}     The SW component "vfc" shall construct a new siunit instance
    ///                  with a value that is converted from a compatible siunit instance.
    /// @tparam OtherUnitInfoType defines the other type of SIUnit
    /// @param  f_value_r   Data to be stored
    /// $Source: vfc_siunits.hpp $

    //---------------------------------------------------------------------
    template <class OtherUnitInfoType>
    TSIUnits(const vfc::TSIUnits<
             ValueType, // PRQA S 2180 # Technical debt L2 id=00c1817012790d1a4f9c-27cc2ff2d08541bd
             OtherUnitInfoType,
             RBInfoType,
             UserType,
             ConvertPolicyType>& f_value_r)
        : m_value(f_value_r.value())
    {
        static_assert(
            TIsValidArithmetic<ValueType>::value,
            "invalid arithmetic type used for SIUnit UnitInfoType converting copy constructor");
        TSICompatibleCheck<UnitInfoType, OtherUnitInfoType>::check();

        ConvertPolicyType<
            OtherUnitInfoType,
            UnitInfoType,
            typename vfc::TIf<(vfc::TIsFloating<ValueType>::value == 1), CFloatingType, CIntegralType>::type>::
            performValueConversion(m_value);
    }

    //---------------------------------------------------------------------
    /// Explicit    Constructs with the SIUnits type
    /// @dspecref{1952}
    /// @tparam OtherRBInfoType defines the other type of RBUnit
    /// @param  f_value_r   Data to be stored
    /// $Source: vfc_siunits.hpp $

    //---------------------------------------------------------------------
    template <class OtherRBInfoType>
    TSIUnits(const vfc::TSIUnits<
             ValueType, // PRQA S 2180 # Technical debt L2 id=00c13457bd5605884fd6-27cc2ff2d08541bd
             UnitInfoType,
             OtherRBInfoType,
             UserType,
             ConvertPolicyType>& f_value_r)
        : m_value(f_value_r.value())
    {
        static_assert(
            TIsValidArithmetic<ValueType>::value,
            "invalid arithmetic type used for SIUnit RBInfoType converting copy constructor");
        TRBConvert<
            OtherRBInfoType,
            RBInfoType,
            typename vfc::TIf<(vfc::TIsFloating<ValueType>::value == 1), CFloatingType, CIntegralType>::type>::
            performValueConversion(m_value);
    }

    /// @brief Request default destructor.
    ~TSIUnits() = default;

    //---------------------------------------------------------------------
    /// Returns the contained value
    /// @dspec{1290}     The SW component "vfc" shall return a scalar value of a
    ///                  given siunit value.
    /// $Source: vfc_siunits.hpp $
    /// @return     returns value of data type

    //---------------------------------------------------------------------
    constexpr ValueType value() const { return m_value; }

    //---------------------------------------------------------------------
    /// Returns the reference of the contained value
    /// $Source: vfc_siunits.hpp $
    /// @return     returns reference of the value

    //---------------------------------------------------------------------
    ValueType& value()
    {
        return m_value; // PRQA S 4024 # Technical debt L2 id=00c13193243c28234efc-05da233a2e6eb15e
    }

    //---------------------------------------------------------------------
    /// Negation operator
    /// @dspec{1600}     The SW component "vfc" shall negate a given siunit value and
    ///                  return the negated value.
    /// @return returns subtracted TSIUnits
    /// $Source: vfc_siunits.hpp $

    //---------------------------------------------------------------------
    TSIUnits operator-() const
    {
        static_assert(TIsValidArithmetic<ValueType>::value, "invalid arithmetic type used for TSIUnits::operator-");
        const self_type result(-(this->m_value));
        return result;
    }

    //---------------------------------------------------------------------
    /// Decrement operator
    /// @dspec{1286}     The SW component "vfc" shall decrement a given siunit value by one.
    /// @return returns reference to decremented TSIUnits
    /// $Source: vfc_siunits.hpp $

    //---------------------------------------------------------------------
    TSIUnits& operator--()
    {
        static_assert(
            TIsValidArithmetic<ValueType>::value, "invalid arithmetic type used for prefix TSIUnits::operator--");
        --m_value;
        return *this;
    }

    //---------------------------------------------------------------------
    /// Post decrement operator
    /// @dspecref{1286}
    /// @return returns decremented TSIUnits
    /// $Source: vfc_siunits.hpp $

    //---------------------------------------------------------------------
    TSIUnits operator--(int)
    {
        static_assert(
            TIsValidArithmetic<ValueType>::value, "invalid arithmetic type used for postfix TSIUnits::operator--");
        const self_type result(*this);
        vfc::ignoreReturn(--(
            *this)); // Dubious QACPP-3804 // PRQA S 3362 # Technical debt L2 id=00c1e3e56d20d84e4e22-5ea68fdb2ec5c479
        return result;
    }

    //---------------------------------------------------------------------
    /// Increment operator
    /// @dspec{1287}     The SW component "vfc" shall increment a given siunit value by one.
    /// @return returns reference to incremented TSIUnits
    /// $Source: vfc_siunits.hpp $
    /// @return    returns result of siunit_type types.

    //---------------------------------------------------------------------
    TSIUnits& operator++()
    {
        static_assert(
            TIsValidArithmetic<ValueType>::value, "invalid arithmetic type used for prefix TSIUnits::operator++");
        ++m_value;
        return *this;
    }

    //---------------------------------------------------------------------
    /// Post increment operator
    /// @dspecref{1287}
    /// @return returns incremented TSIUnits
    /// $Source: vfc_siunits.hpp $
    /// @return    returns result of siunit_type types.

    //---------------------------------------------------------------------
    TSIUnits operator++(int)
    {
        static_assert(
            TIsValidArithmetic<ValueType>::value, "invalid arithmetic type used for postfix TSIUnits::operator++");
        const self_type result(*this);
        vfc::ignoreReturn(++(
            *this)); // Dubious QACPP-3804 // PRQA S 3360 # Technical debt L2 id=00c1388ad59cf42a4225-2e998f52601036a0
        return result;
    }
    //---------------------------------------------------------------------
    /// Comparision Operator
    /// @dspec{1285}     The SW component "vfc" shall compare two given values and check
    ///                  if the first given value is smaller than, smaller or equal than,
    ///                  equal to, not equal to, larger than, or larger or equal than the
    ///                  other given value an return the boolean value "true" if the comparision
    ///                  was true, otherwise it shall retrun the boolean value "false".
    /// @param     f_rhs_r  data to be comapared
    /// $Source: vfc_siunits.hpp $
    /// @return    returns if the data contained within is equal to that of f_rhs_r

    //---------------------------------------------------------------------
    bool operator==(
        const TSIUnits& f_rhs_r) const // PRQA S 2066 # Technical debt L4 id=00c191871b0ea11648f6-b814762beb549364
    {
        static_assert(TIsValidArithmetic<ValueType>::value, "invalid arithmetic type used for TSIUnits::operator==");
        return (
            this->m_value ==
            f_rhs_r.m_value); // Warning qacpp-3270 - if warning is triggered here consider using function isEqual //
                              // PRQA S 3270 # Technical debt L2 id=0127b877fc28826344ea-2d6497d0961ecad7
    }

    //---------------------------------------------------------------------
    /// Is not equal to comparision Operator
    /// @dspecref{1285}
    /// @param     f_rhs_r  data to be comapared
    /// $Source: vfc_siunits.hpp $
    /// @return    returns if the data contained within is inequal to that of f_rhs_r

    //---------------------------------------------------------------------
    bool operator!=(
        const TSIUnits& f_rhs_r) const // PRQA S 2066 # Technical debt L4 id=00c1a1996bb6ceee4222-1e20d9ad2c1f4b90
    {
        static_assert(TIsValidArithmetic<ValueType>::value, "invalid arithmetic type used for TSIUnits::operator!=");
        return (!((*this) == f_rhs_r));
    }

    //---------------------------------------------------------------------
    /// Lesser than Operator
    /// @dspecref{1285}
    /// @param     f_rhs_r  data to be comapared
    /// $Source: vfc_siunits.hpp $
    /// @return    returns if the data contained within is less than that of f_rhs_r

    //---------------------------------------------------------------------
    bool
    operator<(const TSIUnits& f_rhs_r) const // PRQA S 2066 # Technical debt L4 id=00c1474271be8a664335-0c17375cb08859af
    {
        static_assert(
            TIsValidArithmetic<ValueType>::value,
            "invalid arithmetic type used for TSIUnits::operator<TSIUnits::operator>");
        return (this->m_value < f_rhs_r.m_value);
    }

    //---------------------------------------------------------------------
    /// Greater than Operator
    /// @dspecref{1285}
    /// @param     f_rhs_r  data to be comapared
    /// $Source: vfc_siunits.hpp $
    /// @return    returns if the data contained within is greater than that of f_rhs_r

    //---------------------------------------------------------------------
    bool
    operator>(const TSIUnits& f_rhs_r) const // PRQA S 2066 # Technical debt L4 id=00c1db83e419171a448a-105759b2b11082d2
    {
        static_assert(TIsValidArithmetic<ValueType>::value, "invalid arithmetic type used for TSIUnits::operator>");
        return (f_rhs_r < (*this));
    }

    //---------------------------------------------------------------------
    /// Lesser than Or Equal to Operator
    /// @dspecref{1285}
    /// @note Implemented expression negates, comparison correct.
    /// @param     f_rhs_r  data to be comapared
    /// $Source: vfc_siunits.hpp $
    /// @return    returns if the data contained within is less than or equal to that of f_rhs_r

    //---------------------------------------------------------------------
    bool operator<=(
        const TSIUnits& f_rhs_r) const // PRQA S 2066 # Technical debt L4 id=00c1efc9ddebea764e86-f74acde8eb9ab1d8
    {
        static_assert(TIsValidArithmetic<ValueType>::value, "invalid arithmetic type used for TSIUnits::operator<=");
        return (!(f_rhs_r < (*this)));
    }

    //---------------------------------------------------------------------
    /// Greater than Or Equal to Operator
    /// @dspecref{1285}
    /// @note Implemented expression negates, comparison correct.
    /// @param     f_rhs_r  data to be comapared
    /// $Source: vfc_siunits.hpp $
    /// @return    returns if the data contained within is greater than or equal to that of f_rhs_r

    //---------------------------------------------------------------------
    bool operator>=(
        const TSIUnits& f_rhs_r) const // PRQA S 2066 # Technical debt L4 id=00c1dce889811d9b4d79-1d23602cea05cbaf
    {
        static_assert(TIsValidArithmetic<ValueType>::value, "invalid arithmetic type used for TSIUnits::operator>=");
        return (!((*this) < f_rhs_r));
    }

    //---------------------------------------------------------------------
    /// Addition Assignment Operator
    /// @dspec{1965}     The SW component "vfc" shall add the first given siunit value
    ///                  to the second given siunit value and store the resulting value
    ///                  in the first given siunit.
    /// @param     f_rhs_r  data to be added
    /// @return    returns reference to added this
    /// $Source: vfc_siunits.hpp $

    //---------------------------------------------------------------------
    template <class OtherUnitInfoType>
    TSIUnits& operator+=( // PRQA S 2637 # Technical debt L4 id=00c1e95c893c78f44f2c-e866981d9d360294
        const TSIUnits<ValueType, OtherUnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_rhs_r)
    {
        static_assert(TIsValidArithmetic<ValueType>::value, "invalid arithmetic type used for SIUnit");
        // Operation allowed only with the same power of the types
        TSICompatibleCheck<UnitInfoType, OtherUnitInfoType>::check();

        ValueType value = f_rhs_r.value();
        ConvertPolicyType<
            OtherUnitInfoType,
            UnitInfoType,
            typename vfc::TIf<(vfc::TIsIntegral<ValueType>::value == 1), CIntegralType, CFloatingType>::type>::
            performValueConversion(value);

        m_value += value;
        return (*this);
    }

    //---------------------------------------------------------------------
    /// Subtraction Assignment Operator
    /// @dspec{1966}     The SW component "vfc" shall subtract the second given siunit
    ///                  value from the first given siunit value and store the resulting
    ///                  difference value in the first given siunit value.
    /// @param     f_rhs_r  data to be subtracted
    /// @return    returns reference to subtracted this
    /// $Source: vfc_siunits.hpp $

    //---------------------------------------------------------------------
    template <class OtherUnitInfoType>
    TSIUnits& operator-=( // PRQA S 2637 # Technical debt L4 id=00c112d38b0e22824b9c-0870d64cd8ae8918
        const TSIUnits<ValueType, OtherUnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_rhs_r)
    {
        static_assert(TIsValidArithmetic<ValueType>::value, "invalid arithmetic type used for TSIUnits::operator-=");
        // Operation allowed only with the same power of the types
        TSICompatibleCheck<UnitInfoType, OtherUnitInfoType>::check();

        ValueType value = f_rhs_r.value();
        ConvertPolicyType<
            OtherUnitInfoType,
            UnitInfoType,
            typename vfc::TIf<(vfc::TIsFloating<ValueType>::value == 1), CFloatingType, CIntegralType>::type>::
            performValueConversion(value);

        m_value -= value;
        return (*this);
    }

    //---------------------------------------------------------------------
    /// Multiplication Assignment Operator
    /// @dspec{1967}     The SW component "vfc" shall multiply the first given siunit
    ///                  value with a second given siunit value that represents a dimensionless
    ///                  value or a scalar value and store the resulting value in the first
    ///                  given siunit value.
    /// @param     f_rhs_r  data to be multiplied
    /// @return    returns reference to multiplied this
    /// $Source: vfc_siunits.hpp $

    //---------------------------------------------------------------------
    TSIUnits& operator*=(const value_type& f_rhs_r)
    {
        static_assert(
            TIsValidArithmetic<ValueType>::value, "Invalid arithmetic type used for TSIUnits::operator*= (ValueType)");
        m_value *= f_rhs_r;
        return (*this);
    }

    //---------------------------------------------------------------------
    /// Multiplication Assignment Operator with other RBInfoType and other UnitInfoType TSIUnits as RHS.
    /// @dspecref{1967}
    /// @param     f_rhs_r  data to be multiplied
    /// @return    returns reference to multiplied this
    /// $Source: vfc_siunits.hpp $

    //---------------------------------------------------------------------
    template <class OtherUnitInfoType, class OtherRBInfoType>
    TSIUnits& operator*=( // PRQA S 2637 # Technical debt L4 id=00c10cfed31d99564370-3a0d9c438ab1e358
        const TSIUnits<ValueType, OtherUnitInfoType, OtherRBInfoType, UserType, ConvertPolicyType>& f_rhs_r)
    {
        static_assert(
            TIsValidArithmetic<ValueType>::value,
            "Invalid arithmetic type used for TSIUnits::operator*= (OtherUnitInfoType, OtherRBInfoType)");
        // Static assert for OtherUnitInfoType is a Nil type
        VFC_STATIC_ASSERT(OtherUnitInfoType::LENGTH_PREFIX_VALUE == vfc::BASE);
        VFC_STATIC_ASSERT(OtherUnitInfoType::LENGTH_POWER_VALUE == 0);

        VFC_STATIC_ASSERT(OtherUnitInfoType::MASS_PREFIX_VALUE == vfc::BASE);
        VFC_STATIC_ASSERT(OtherUnitInfoType::MASS_POWER_VALUE == 0);

        VFC_STATIC_ASSERT(OtherUnitInfoType::TIME_PREFIX_VALUE == vfc::BASE);
        VFC_STATIC_ASSERT(OtherUnitInfoType::TIME_POWER_VALUE == 0);

        VFC_STATIC_ASSERT(OtherUnitInfoType::CURRENT_PREFIX_VALUE == vfc::BASE);
        VFC_STATIC_ASSERT(OtherUnitInfoType::CURRENT_POWER_VALUE == 0);

        VFC_STATIC_ASSERT(OtherUnitInfoType::TEMPERATURE_PREFIX_VALUE == vfc::BASE);
        VFC_STATIC_ASSERT(OtherUnitInfoType::TEMPERATURE_POWER_VALUE == 0);

        VFC_STATIC_ASSERT(OtherUnitInfoType::AMOUNTOFSUBSTANCE_PREFIX_VALUE == vfc::BASE);
        VFC_STATIC_ASSERT(OtherUnitInfoType::AMOUNTOFSUBSTANCE_POWER_VALUE == 0);

        VFC_STATIC_ASSERT(OtherUnitInfoType::LUMINOUSINTENSITY_PREFIX_VALUE == vfc::BASE);
        VFC_STATIC_ASSERT(OtherUnitInfoType::LUMINOUSINTENSITY_POWER_VALUE == 0);

        VFC_STATIC_ASSERT(OtherUnitInfoType::ANGLE_PREFIX_VALUE == vfc::BASE);
        VFC_STATIC_ASSERT(OtherUnitInfoType::ANGLE_POWER_VALUE == 0);

        // Static assert for OtherRBInfoType is a Nil type
        VFC_STATIC_ASSERT(OtherRBInfoType::PIXEL_PREFIX_VALUE == vfc::BASE);
        VFC_STATIC_ASSERT(OtherRBInfoType::PIXEL_POWER_VALUE == 0);

        VFC_STATIC_ASSERT(OtherRBInfoType::PERCENTAGE_PREFIX_VALUE == vfc::BASE);
        VFC_STATIC_ASSERT(OtherRBInfoType::PERCENTAGE_POWER_VALUE == 0);

        this->operator*=(f_rhs_r.value());
        return (*this);
    }

    //---------------------------------------------------------------------
    /// Division Assignment Operator
    /// @dspec{1298}     The SW component "vfc" shall divide two given siunits and return
    ///                  their quotient value.
    /// @param     f_rhs_r  divisor data
    /// @return    returns reference to divided this
    /// $Source: vfc_siunits.hpp $

    //---------------------------------------------------------------------
    TSIUnits& operator/=(const value_type& f_rhs_r)
    {
        static_assert(
            TIsValidArithmetic<ValueType>::value, "Invalid arithmetic type used for TSIUnits::operator/= (ValueType)");
        m_value /= f_rhs_r;
        return (*this);
    }

    //---------------------------------------------------------------------
    /// Division Assignment Operator with other RBInfoType and other UnitInfoType TSIUnits as RHS.
    /// @dspecref{1298}
    /// @param     f_rhs_r  divisor data
    /// @return    returns reference to divided this
    /// $Source: vfc_siunits.hpp $

    //---------------------------------------------------------------------
    template <class OtherUnitInfoType, class OtherRBInfoType>
    TSIUnits&
    operator/=(const TSIUnits<ValueType, OtherUnitInfoType, OtherRBInfoType, UserType, ConvertPolicyType>& f_rhs_r)
    {
        static_assert(
            TIsValidArithmetic<ValueType>::value,
            "Invalid arithmetic type used for TSIUnits::operator/= (OtherUnitInfoType, OtherRBInfoType)");
        // Static assert for OtherUnitInfoType is a Nil type
        VFC_STATIC_ASSERT(OtherUnitInfoType::LENGTH_PREFIX_VALUE == vfc::BASE);
        VFC_STATIC_ASSERT(OtherUnitInfoType::LENGTH_POWER_VALUE == 0);

        VFC_STATIC_ASSERT(OtherUnitInfoType::MASS_PREFIX_VALUE == vfc::BASE);
        VFC_STATIC_ASSERT(OtherUnitInfoType::MASS_POWER_VALUE == 0);

        VFC_STATIC_ASSERT(OtherUnitInfoType::TIME_PREFIX_VALUE == vfc::BASE);
        VFC_STATIC_ASSERT(OtherUnitInfoType::TIME_POWER_VALUE == 0);

        VFC_STATIC_ASSERT(OtherUnitInfoType::CURRENT_PREFIX_VALUE == vfc::BASE);
        VFC_STATIC_ASSERT(OtherUnitInfoType::CURRENT_POWER_VALUE == 0);

        VFC_STATIC_ASSERT(OtherUnitInfoType::TEMPERATURE_PREFIX_VALUE == vfc::BASE);
        VFC_STATIC_ASSERT(OtherUnitInfoType::TEMPERATURE_POWER_VALUE == 0);

        VFC_STATIC_ASSERT(OtherUnitInfoType::AMOUNTOFSUBSTANCE_PREFIX_VALUE == vfc::BASE);
        VFC_STATIC_ASSERT(OtherUnitInfoType::AMOUNTOFSUBSTANCE_POWER_VALUE == 0);

        VFC_STATIC_ASSERT(OtherUnitInfoType::LUMINOUSINTENSITY_PREFIX_VALUE == vfc::BASE);
        VFC_STATIC_ASSERT(OtherUnitInfoType::LUMINOUSINTENSITY_POWER_VALUE == 0);

        VFC_STATIC_ASSERT(OtherUnitInfoType::ANGLE_PREFIX_VALUE == vfc::BASE);
        VFC_STATIC_ASSERT(OtherUnitInfoType::ANGLE_POWER_VALUE == 0);

        // Static assert for OtherRBInfoType is a Nil type
        VFC_STATIC_ASSERT(OtherRBInfoType::PIXEL_PREFIX_VALUE == vfc::BASE);
        VFC_STATIC_ASSERT(OtherRBInfoType::PIXEL_POWER_VALUE == 0);

        VFC_STATIC_ASSERT(OtherRBInfoType::PERCENTAGE_PREFIX_VALUE == vfc::BASE);
        VFC_STATIC_ASSERT(OtherRBInfoType::PERCENTAGE_POWER_VALUE == 0);

        this->operator/=(f_rhs_r.value());
        return (*this);
    }

    //---------------------------------------------------------------------
    /// Modulo Assignment Operator
    /// @param     f_rhs_r modulo divisor data on rhs
    /// @return    returns reference to executed modulo operation on this
    /// $Source: vfc_siunits.hpp $

    //---------------------------------------------------------------------
    template <class OtherUnitInfoType>
    TSIUnits& operator%=(const TSIUnits<ValueType, OtherUnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_rhs_r)
    {
        TSICompatibleCheck<UnitInfoType, OtherUnitInfoType>::check();

        // only Integers are accepted
        VFC_STATIC_ASSERT((TIsFloating<ValueType>::value == 0));

        this->m_value %= f_rhs_r.m_value;
        return (*this);
    }

  private:
    value_type m_value; ///< Member variable used store the data value
};

//-----------------------------------------------------------------------------
/// TSIUnits + operator @relatesalso TSIUnits
/// computes the sum of the two operands.
/// @dspec{1284}     The SW component "vfc" shall add two given siunit values and
///                  return the resulting siunit value.
/// $Source: vfc_siunits.hpp $
/// @param f_lhs_r              TSIUnits expression.
/// @param f_rhs_r              TSIUnits expression.
/// @return returns the sum of the two operands.
/// @note f_lhs_r and f_rhs_r should belong the same SI unit type

/// @ingroup                    vfc_core
//=============================================================================

template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
inline const TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> operator+(
    const TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_lhs_r,
    const TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_rhs_r)
{
    static_assert(
        TIsValidArithmetic<ValueType>::value, "invalid arithmetic type used for operator+(TSIUnits, TSIUnits)");
    vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> result(f_lhs_r);
    result.                                                                         operator+=(f_rhs_r);
    return result;
}

//-----------------------------------------------------------------------------
/// TSIUnits - operator @relatesalso TSIUnits
/// computes the difference between the two operands.
/// @dspec{1283}     The SW component "vfc" shall subtract the second given siunit
///                  value from the first given siunit value and return the resulting
///                  difference value.
/// $Source: vfc_siunits.hpp $
/// @param f_lhs_r              TSIUnits expression.
/// @param f_rhs_r              TSIUnits expression.
/// @return returns the difference of the two operands.
/// @note f_lhs_r and f_rhs_r should belong the same SI unit type

/// @ingroup                    vfc_core
//=============================================================================
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
inline const TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> operator-(
    const TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_lhs_r,
    const TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_rhs_r)
{
    static_assert(
        TIsValidArithmetic<ValueType>::value, "invalid arithmetic type used for operator-(TSIUnits, TSIUnits");
    vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> result(f_lhs_r);
    result.                                                                         operator-=(f_rhs_r);
    return result;
}

//-----------------------------------------------------------------------------
/// TSIUnits * operator.  @relatesalso TSIUnits
/// computes the multiplication of two operands.
/// @dspec{1282}     The SW component "vfc" shall multiply two given values where at least
///                  one of the values is a siunit value and return the resulting siunit value.
/// $Source: vfc_siunits.hpp $
/// @param f_val1_r             TSIUnits expression.
/// @param f_val2_r             TSIUnits expression.
/// @return returns the multiplication of the two operands.

/// @ingroup                    vfc_core
//=============================================================================
template <
    class ValueType,
    class UnitInfo1Type,
    class UnitInfo2Type,
    class RBInfo1Type,
    class RBInfo2Type,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
inline const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitMultiplicationPromote<UnitInfo1Type, UnitInfo2Type>::unit_info_type,
    typename vfc::TSIUnitRBMultiplicationPromote<RBInfo1Type, RBInfo2Type>::unit_info_type,
    UserType,
    ConvertPolicyType>
operator*( // PRQA S 4222 # Technical debt L2 id=00c16344164775384546-4787d14a683407a0
    const vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfo1Type, UserType, ConvertPolicyType>& f_val1_r,
    const vfc::TSIUnits<ValueType, UnitInfo2Type, RBInfo2Type, UserType, ConvertPolicyType>& f_val2_r)
{
    static_assert(
        TIsValidArithmetic<ValueType>::value,
        "invalid arithmetic type used for operator*(TSIUnits, TSIUnits) (different info types)");

    ValueType l_value2       = f_val2_r.value();
    using res_unit_info_type = typename TSIUnitMultiplicationPromote<UnitInfo1Type, UnitInfo2Type>::unit_info_type;

    ConvertPolicyType<
        UnitInfo2Type,
        vfc::TUnitInfoType<
            typename res_unit_info_type::length_unit_type,
            UnitInfo1Type::LENGTH_PREFIX_VALUE,
            UnitInfo2Type::LENGTH_POWER_VALUE,
            typename res_unit_info_type::mass_unit_type,
            UnitInfo1Type::MASS_PREFIX_VALUE,
            UnitInfo2Type::MASS_POWER_VALUE,
            typename res_unit_info_type::time_unit_type,
            UnitInfo1Type::TIME_PREFIX_VALUE,
            UnitInfo2Type::TIME_POWER_VALUE,
            typename res_unit_info_type::current_unit_type,
            UnitInfo1Type::CURRENT_PREFIX_VALUE,
            UnitInfo2Type::CURRENT_POWER_VALUE,
            typename res_unit_info_type::temperature_unit_type,
            UnitInfo1Type::TEMPERATURE_PREFIX_VALUE,
            UnitInfo2Type::TEMPERATURE_POWER_VALUE,
            typename res_unit_info_type::amountofsubstance_unit_type,
            UnitInfo1Type::AMOUNTOFSUBSTANCE_PREFIX_VALUE,
            UnitInfo2Type::AMOUNTOFSUBSTANCE_POWER_VALUE,
            typename res_unit_info_type::luminousintensity_unit_type,
            UnitInfo1Type::LUMINOUSINTENSITY_PREFIX_VALUE,
            UnitInfo2Type::LUMINOUSINTENSITY_POWER_VALUE,
            typename res_unit_info_type::angle_unit_type,
            UnitInfo1Type::ANGLE_PREFIX_VALUE,
            UnitInfo2Type::ANGLE_POWER_VALUE,
            typename UnitInfo2Type::unit_type>,
        typename vfc::TIf<(vfc::TIsIntegral<ValueType>::value == 1), CIntegralType, CFloatingType>::type>::
        performValueConversion(l_value2);

    return (vfc::TSIUnits<
            ValueType,
            typename TSIUnitMultiplicationPromote<UnitInfo1Type, UnitInfo2Type>::unit_info_type,
            typename TSIUnitRBMultiplicationPromote<RBInfo1Type, RBInfo2Type>::unit_info_type,
            UserType,
            ConvertPolicyType>(
        f_val1_r.value() * l_value2)); // PRQA S 3010 # Technical debt L2 id=00c1fbd2efc6967d42b1-7401fe3fd3b22529
}

//-----------------------------------------------------------------------------
/// TSIUnits * operator. @relatesalso TSIUnits
/// computes the multiplication of two operands (siunits_type * pod_type)
/// @dspecref{1282}
/// $Source: vfc_siunits.hpp $
/// @param f_lhs_r              TSIUnits expression.
/// @param f_rhs_r              POD      expression
/// @return returns the multiplication of the two operands.

/// @ingroup                    vfc_core
//=============================================================================
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
inline const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> operator*(
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_lhs_r,
    const ValueType&                                                                       f_rhs_r)
{
    static_assert(
        TIsValidArithmetic<ValueType>::value,
        "invalid arithmetic type used for operator*(TSIUnits, TSIUnits) (identical info types)");

    vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> result(f_lhs_r);
    result.                                                                         operator*=(f_rhs_r);
    return result;
}

//-----------------------------------------------------------------------------
/// TSIUnits * operator. @relatesalso TSIUnits
/// computes the multiplication of two operands (pod_type * siunit_type)
/// @dspecref{1282}
/// $Source: vfc_siunits.hpp $
/// @param f_lhs_r              POD      expression
/// @param f_rhs_r              TSIUnits expression.
/// @return returns the multiplication of the two operands.

/// @ingroup                    vfc_core
//=============================================================================
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
inline const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> operator*(
    const ValueType&                                                                       f_lhs_r,
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_rhs_r)
{
    static_assert(
        TIsValidArithmetic<ValueType>::value, "invalid arithmetic type used for operator*(ValueType, TSIUnits)");

    vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> result(f_rhs_r);
    result.                                                                         operator*=(f_lhs_r);
    return result;
}

//-----------------------------------------------------------------------------
/// TSIUnits / operator. @relatesalso TSIUnits
/// computes the division of two operands  (siunits_type / siunits_type)
/// @dspecref{1298}
/// $Source: vfc_siunits.hpp $
/// @param f_val1_r             TSIUnits expression.
/// @param f_val2_r             TSIUnits expression.
/// @return returns the division of the two operands.

/// @ingroup                    vfc_core
//=============================================================================
template <
    class ValueType,
    class UnitInfo1Type,
    class UnitInfo2Type,
    class RBInfo1Type,
    class RBInfo2Type,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
inline const vfc::TSIUnits<
    ValueType,
    typename TSIUnitDivisionPromote<UnitInfo1Type, UnitInfo2Type>::unit_info_type,
    typename TSIUnitRBDivisionPromote<RBInfo1Type, RBInfo2Type>::unit_info_type,
    UserType,
    ConvertPolicyType>
operator/( // PRQA S 4222 # Technical debt L2 id=00c19e43e44321fb47ba-e72a8af037fd74a1
    const vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfo1Type, UserType, ConvertPolicyType>& f_val1_r,
    const vfc::TSIUnits<ValueType, UnitInfo2Type, RBInfo2Type, UserType, ConvertPolicyType>& f_val2_r)
{
    static_assert(
        TIsValidArithmetic<ValueType>::value,
        "invalid arithmetic type used for operator/(TSIUnits, TSIUnits) (different info types)");

    ValueType l_value2 = f_val2_r.value();

    using res_unit_info_type = typename TSIUnitDivisionPromote<UnitInfo1Type, UnitInfo2Type>::unit_info_type;

    ConvertPolicyType<
        UnitInfo2Type,
        vfc::TUnitInfoType<
            typename res_unit_info_type::length_unit_type,
            UnitInfo1Type::LENGTH_PREFIX_VALUE,
            UnitInfo2Type::LENGTH_POWER_VALUE,
            typename res_unit_info_type::mass_unit_type,
            UnitInfo1Type::MASS_PREFIX_VALUE,
            UnitInfo2Type::MASS_POWER_VALUE,
            typename res_unit_info_type::time_unit_type,
            UnitInfo1Type::TIME_PREFIX_VALUE,
            UnitInfo2Type::TIME_POWER_VALUE,
            typename res_unit_info_type::current_unit_type,
            UnitInfo1Type::CURRENT_PREFIX_VALUE,
            UnitInfo2Type::CURRENT_POWER_VALUE,
            typename res_unit_info_type::temperature_unit_type,
            UnitInfo1Type::TEMPERATURE_PREFIX_VALUE,
            UnitInfo2Type::TEMPERATURE_POWER_VALUE,
            typename res_unit_info_type::amountofsubstance_unit_type,
            UnitInfo1Type::AMOUNTOFSUBSTANCE_PREFIX_VALUE,
            UnitInfo2Type::AMOUNTOFSUBSTANCE_POWER_VALUE,
            typename res_unit_info_type::luminousintensity_unit_type,
            UnitInfo1Type::LUMINOUSINTENSITY_PREFIX_VALUE,
            UnitInfo2Type::LUMINOUSINTENSITY_POWER_VALUE,
            typename res_unit_info_type::angle_unit_type,
            UnitInfo1Type::ANGLE_PREFIX_VALUE,
            UnitInfo2Type::ANGLE_POWER_VALUE,
            typename UnitInfo2Type::unit_type>,
        typename vfc::TIf<(vfc::TIsIntegral<ValueType>::value == 1), CIntegralType, CFloatingType>::type>::
        performValueConversion(l_value2);

    return vfc::TSIUnits<
        ValueType,
        typename TSIUnitDivisionPromote<UnitInfo1Type, UnitInfo2Type>::unit_info_type,
        typename TSIUnitRBDivisionPromote<RBInfo1Type, RBInfo2Type>::unit_info_type,
        UserType,
        ConvertPolicyType>(vfc::divide(f_val1_r.value(), l_value2));
}

//-----------------------------------------------------------------------------
/// TSIUnits / operator. @relatesalso TSIUnits
/// computes the division of two operands ( siunits_type / pod_type )
/// @dspecref{1298}
/// $Source: vfc_siunits.hpp $
/// @param f_lhs_r              TSIUnits expression.
/// @param f_rhs_r              POD      expression.
/// @return returns the division of the two operands.

/// @ingroup                    vfc_core
//=============================================================================
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
inline const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> operator/(
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_lhs_r,
    const ValueType&                                                                       f_rhs_r)
{
    static_assert(
        TIsValidArithmetic<ValueType>::value, "invalid arithmetic type used for operator/(TSIUnits, ValueType)");

    vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> result(f_lhs_r);
    result.                                                                         operator/=(f_rhs_r);
    return result;
}

//-----------------------------------------------------------------------------
/// TSIUnits / operator. @relatesalso TSIUnits
/// computes the division of two operands (pod_type / siunits_type )
/// @dspecref{1298}
/// $Source: vfc_siunits.hpp $
/// @param f_lhs_r              POD      expression.
/// @param f_rhs_r              TSIUnits expression.
/// @return returns the division of the two operands.

/// @ingroup                    vfc_core
//=============================================================================
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
inline const vfc::TSIUnits<
    ValueType,
    typename TSIUnitDivisionPromote<info_nil_type, UnitInfoType>::unit_info_type,
    typename TSIUnitRBDivisionPromote<info_rbnil_type, RBInfoType>::unit_info_type,
    UserType,
    ConvertPolicyType>
operator/( // PRQA S 4222 # Technical debt L2 id=00c18d922a04741045eb-e72a8af037fd74a1
    const ValueType&                                                                       f_lhs_r,
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_rhs_r)
{
    static_assert(
        TIsValidArithmetic<ValueType>::value, "invalid arithmetic type used for operator/(ValueType, TSIUnits)");

    ValueType l_rhsValue     = f_rhs_r.value();
    using my3_unit_info_type = vfc::TUnitInfoType<
        vfc::CLengthType,
        vfc::BASE,
        (0 - UnitInfoType::LENGTH_POWER_VALUE),
        vfc::CMassType,
        vfc::BASE,
        (0 - UnitInfoType::MASS_POWER_VALUE),
        vfc::CTimeType,
        vfc::BASE,
        (0 - UnitInfoType::TIME_POWER_VALUE),
        vfc::CCurrentType,
        vfc::BASE,
        (0 - UnitInfoType::CURRENT_POWER_VALUE),
        vfc::CTemperatureType,
        vfc::BASE,
        (0 - UnitInfoType::TEMPERATURE_POWER_VALUE),
        vfc::CAmountOfSubstanceType,
        vfc::BASE,
        (0 - UnitInfoType::AMOUNTOFSUBSTANCE_POWER_VALUE),
        vfc::CLuminousIntensityType,
        vfc::BASE,
        (0 - UnitInfoType::LUMINOUSINTENSITY_POWER_VALUE),
        vfc::CAngleType,
        vfc::BASE,
        (0 - UnitInfoType::ANGLE_POWER_VALUE),
        vfc::CBasicType>;
    ConvertPolicyType<
        UnitInfoType,
        my3_unit_info_type,
        typename vfc::TIf<(vfc::TIsIntegral<ValueType>::value == 1), CIntegralType, CFloatingType>::type>::
        performValueConversion(l_rhsValue);

    const vfc::TSIUnits<
        ValueType,
        typename TSIUnitDivisionPromote<info_nil_type, UnitInfoType>::unit_info_type,
        typename TSIUnitRBDivisionPromote<info_rbnil_type, RBInfoType>::unit_info_type,
        UserType,
        ConvertPolicyType>
        l_result((f_lhs_r / l_rhsValue));

    return l_result;
}

//-----------------------------------------------------------------------------
/// TSIUnits % operator @relatesalso TSIUnits
/// computes the modulus of two operands
/// @dspec{1297}     The SW component "vfc" shall apply the euclidean division (modulo)
///                  to two given siunit values and return the modulus value.
/// $Source: vfc_siunits.hpp $
/// @param f_lhs_r              TSIUnits expression.
/// @param f_rhs_r              TSIUnits expression.
/// @return returns the modulus of the two operands.

/// @ingroup                    vfc_core
//=============================================================================
template <
    class ValueType,
    class UnitInfo1Type,
    class UnitInfo2Type,
    class RBInfo1Type,
    class RBInfo2Type,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
inline const vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfo1Type, UserType, ConvertPolicyType>
operator%( // PRQA S 4222 # Technical debt L2 id=00c162741ca5c44d4604-8406016d60756213
    const vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfo1Type, UserType, ConvertPolicyType>& f_lhs_r,
    const vfc::TSIUnits<ValueType, UnitInfo2Type, RBInfo2Type, UserType, ConvertPolicyType>& f_rhs_r)
{
    // only Integers are accepted
    VFC_STATIC_ASSERT((TIsFloating<ValueType>::value == 0));

    return vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfo1Type, UserType, ConvertPolicyType>(
        f_lhs_r.value() % f_rhs_r.value());
}

/// Type trait: is argument an SI unit?
/// Primary template is in ``vfc_type_traits.hpp``.
template <
    typename ValueType,
    typename UnitInfoType,
    typename RBInfoType,
    typename UserType,
    template <class, class, class>
    class ConvertPolicyType>
struct TIsSIUnitType<TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>> : true_t
{
};

/// Type trait: TSIUnits is a fundamental type, if `ValueType` is fundamental.
template <
    typename ValueType,
    typename UnitInfoType,
    typename RBInfoType,
    typename UserType,
    template <class, class, class>
    class ConvertPolicyType>
struct TIsFundamental<TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>>
    : TIsFundamental<ValueType>
{
};

/// Type trait: TSIUnits is a floating type, if `ValueType` is a float type.
template <
    typename ValueType,
    typename UnitInfoType,
    typename RBInfoType,
    typename UserType,
    template <class, class, class>
    class ConvertPolicyType>
struct TIsFloating<TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>> : TIsFloating<ValueType>
{
};

/// Type trait: TSIUnits is among valid floating types, if `ValueType` is a valid float type.
template <
    typename ValueType,
    typename UnitInfoType,
    typename RBInfoType,
    typename UserType,
    template <class, class, class>
    class ConvertPolicyType>
struct TIsValidFloating<TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>>
    : TIsValidFloating<ValueType>
{
};

/// Type trait: TSIUnits is among valid arithmetic types, if `ValueType` is a valid arithmetic type.
template <
    typename ValueType,
    typename UnitInfoType,
    typename RBInfoType,
    typename UserType,
    template <class, class, class>
    class ConvertPolicyType>
struct TIsValidArithmetic<TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>>
    : TIsValidArithmetic<ValueType>
{
};

//---------------------------------------------------------------------
/// To calculate tan @relatesalso TSIUnits
/// @dspec{1278}     The SW component "vfc" shall calculate and return the value of the
///                  tangent function for a given siunit value.
/// @param     f_val  to calculate tan of
/// @return    returns tan function value
/// $Source: vfc_siunits.hpp $

//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitSinCosPromote<UnitInfoType>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
tan(vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> f_val);

//---------------------------------------------------------------------
/// To calculate sin @relatesalso TSIUnits
/// @dspec{1280}     The SW component "vfc" shall calculate and return the the value
///                  of the sine function for a given siunit value.
/// @param     f_val  to calculate sin of
/// @return    returns sin function value
/// $Source: vfc_siunits.hpp $

//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitSinCosPromote<UnitInfoType>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
sin(vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> f_val);

//---------------------------------------------------------------------
/// To calculate cos @relatesalso TSIUnits
/// @dspec{1279}     The SW component "vfc" shall calculate and return the value of the
///                  cosine function for a given siunit value.
/// @param     f_val  to calculate cos of
/// @return    returns cos function value
/// $Source: vfc_siunits.hpp $

//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitSinCosPromote<UnitInfoType>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
cos(vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> f_val);

//---------------------------------------------------------------------
/// To calculate atan @relatesalso TSIUnits
/// @dspec{1275}     The SW component "vfc" shall calculate and return the value of the
///                  inverse tangent function for a given siunit value.
/// @param f_val to calculate atan of
/// @return returns atan function value
/// $Source: vfc_siunits.hpp $

//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitAtanPromote<UnitInfoType>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
atan(vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> f_val);

//---------------------------------------------------------------------
/// calculate atan2, different units for x and y possible @relatesalso TSIUnits
/// @dspec{1274}     The SW component "vfc" shall calculate and return the value of the
///                  inverse tangent function for a given siunit x value and a given siunit
///                  y value.
/// @param f_y_val y coordinate of vector to calculate atan of
/// @param f_x_val x coordinate of vector to calculate atan of
/// @return returns atan2 function value
/// $Source: vfc_siunits.hpp $

//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfo1Type,
    class UnitInfo2Type,
    class RBInfo1Type,
    class RBInfo2Type,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitAtan2Promote<UnitInfo1Type, UnitInfo2Type>::unit_info_type,
    typename vfc::TSIUnitRBDivisionPromote<RBInfo1Type, RBInfo2Type>::unit_info_type,
    UserType,
    ConvertPolicyType>
atan2(
    vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfo1Type, UserType, ConvertPolicyType> f_y_val,
    vfc::TSIUnits<ValueType, UnitInfo2Type, RBInfo2Type, UserType, ConvertPolicyType> f_x_val);

//---------------------------------------------------------------------
/// To calculate asin @relatesalso TSIUnits
/// @dspec{1277}     The SW component "vfc" shall calculate and return the value of
///                  the inverse sine function for a given siunit value.
/// @pre The input parameter value has to be in range from -1 to 1.
/// @param f_val to calculate asin of
/// @return returns asin function value
/// $Source: vfc_siunits.hpp $

/// @note If the input value originates from an algebraical formula,
///  then it is possible that the numerical value exceeds the valid
///  range [-1,1] slightly which will cause this function to fail
///  with undefined behavior (usually a trap). In these cases it is
///  mandatory to clamp the value to the legal range.
//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitAtanPromote<UnitInfoType>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
asin(vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> f_val);

//---------------------------------------------------------------------
/// To calculate acos @relatesalso TSIUnits
/// @spec{1276}     The SW component "vfc" shall calculate and return the value of
///                 the inverse cosine function for a given siunit value.
/// @pre The input parameter value has to be in range from -1 to 1.
/// @param f_val to calculate asin of
/// @return returns acos function value
/// $Source: vfc_siunits.hpp $

/// @note If the input value originates from an algebraical formula,
///  then it is possible that the numerical value exceeds the valid
///  range [-1,1] slightly which will cause this function to fail
///  with undefined behavior (usually a trap). In these cases it is
///  mandatory to clamp the value to the legal range.
//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitAtanPromote<UnitInfoType>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
acos(vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> f_val);

//---------------------------------------------------------------------
/// To calculate floor @relatesalso TSIUnits
/// @dspec{1273}     The SW component "vfc" shall round and return a given siunit
///                  value down towards the integer that is smaller or equal to the given value.
/// @param     f_val  to calculate floor of
/// @return returns floor function value
/// $Source: vfc_siunits.hpp $

//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>
floor(vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> f_val);

//---------------------------------------------------------------------
/// To calculate sqr @relatesalso TSIUnits
/// @dspec{1270}     The SW component "vfc" shall calculate and return the square value
///                  of a given siunit value.
/// @param     f_val  to calculate square of
/// @return    returns square of input parameter
/// $Source: vfc_siunits.hpp $

//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitSqrPromote<UnitInfoType>::unit_info_type,
    typename vfc::TSIUnitRBSqrPromote<RBInfoType>::unit_info_type,
    UserType,
    ConvertPolicyType>
sqr(const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_val);

//---------------------------------------------------------------------
/// To calculate sqrt @relatesalso TSIUnits
/// @dspec{1269}     The SW component "vfc" shall calculate and return the square root
///                  value of a given siunit value.
/// @param     f_val  to calculate square root of
/// @return    returns square root of input parameter
/// $Source: vfc_siunits.hpp $

//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitSqrtPromote<UnitInfoType>::unit_info_type,
    typename vfc::TSIUnitRBSqrtPromote<RBInfoType>::unit_info_type,
    UserType,
    ConvertPolicyType>
sqrt(vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> f_val);

//---------------------------------------------------------------------
/// To calculate abs @relatesalso TSIUnits
/// @dspec{1292}     The SW component "vfc" shall calculate and return the absolute
///                  value of a given siunit value.
/// @param     f_val  to calculate absolute of
/// @return    returns absolute value
/// $Source: vfc_siunits.hpp $

//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>
abs(const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_val);

//---------------------------------------------------------------------
/// To Find NAN @relatesalso TSIUnits
/// @dspec{788}     The SW component "vfc" shall check if the floating point value of the
///                 given siunit represents NAN and return the boolean value "true" if it does,
///                 else it shall return the boolean value "false".
/// @param     f_nanValue_r  to find not a number
/// $Source: vfc_siunits.hpp $
/// @return    returns true if given value is NAN, false otherwise

//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool isNAN(const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_nanValue_r);

//---------------------------------------------------------------------
/// To find INF @relatesalso TSIUnits
/// @dspec{789}     The SW component "vfc" shall check if the floating point value of
///                 the given siunit represents an infinite value and return the boolean
///                 value "true" if it does, else it shall return the boolean value "false".
/// @param     f_infValue_r  to find infinity or not
/// $Source: vfc_siunits.hpp $
/// @return    returns true if given value is +INF or -INF, false otherwise.

//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool isINF(const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_infValue_r);

//---------------------------------------------------------------------
/// To find value is zero or not @relatesalso TSIUnits
/// @dspec{1306}     The SW component "vfc" shall check if the floating point value of
///                  the given siunit represents a numerically zero value and return the
///                  boolean value "true" if it does, else it shall return the boolean value "false".
/// @param     f_zeroValue_r  to find  value is zero or not
/// $Source: vfc_siunits.hpp $
/// @return    returns true if specified value equals zero, false otherwise.

//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool isZero(const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_zeroValue_r);

//---------------------------------------------------------------------
/// To find value is notzero @relatesalso TSIUnits
/// @dspec{1305}     The SW component "vfc" shall check if the floating point value of
///                  the given siunit represents a numerically non-zero value and return
///                  the boolean value "true" if it does, else it shall return the boolean value "false".
/// @param     f_notzeroValue_r  to find  value is notzero
/// $Source: vfc_siunits.hpp $
/// @return    returns true if specified value is NOT zero.

//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool notZero(const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_notzeroValue_r);

//---------------------------------------------------------------------
/// To find value is positive @relatesalso TSIUnits
/// @dspec{1304}     The SW component "vfc" shall check if the floating point value of
///                  the given siunit represents a numerically positive value and return
///                  the boolean value "true" if it does, else it shall return the boolean
///                  value "false".
/// @param     f_positiveValue_r  to find  value is positive
/// $Source: vfc_siunits.hpp $
/// @return    returns true if specified value is greater zero

//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool isPositive(
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_positiveValue_r);

//---------------------------------------------------------------------
/// To find value is notpositive @relatesalso TSIUnits
/// @dspec{1303}     The SW component "vfc" shall check if the floating point value of
///                  the given siunit represents a numerically not positive value and
///                  return the boolean value "true" if it does, else it shall return the
///                  boolean value "false".
/// @param     f_notpositiveValue_r  to find  value is notpositive
/// $Source: vfc_siunits.hpp $
/// @return    returns true if specified value is zero or less

//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool notPositive(
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_notpositiveValue_r);

//---------------------------------------------------------------------
/// To find value is negative @relatesalso TSIUnits
/// @dspec{1302}     The SW component "vfc" shall check if the floating point value
///                  of the given siunit represents a numerically negative value and
///                  return the boolean value "true" if it does, else it shall return
///                  the boolean value "false".
/// @param     f_negativeValue_r  to find  value is negative
/// $Source: vfc_siunits.hpp $
/// @return    returns true, if specified value is less zero

//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool isNegative(
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_negativeValue_r);

//---------------------------------------------------------------------
/// To find value is notnegative @relatesalso TSIUnits
/// @dspec{1301}     The SW component "vfc" shall check if the floating point value of
///                  the given siunit represents a numerically not negative value and
///                  return the boolean value "true" if it does, else it shall return
///                  the boolean value "false".
/// @param     f_notnegativeValue_r  to find  value is not negative
/// $Source: vfc_siunits.hpp $
/// @return    returns true, if specified value is zero or greater

//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool notNegative(
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_notnegativeValue_r);

//---------------------------------------------------------------------
/// To find values are equal @relatesalso TSIUnits
/// @dspec{1300}     The SW component "vfc" shall check if the two floating point values
///                  of two given siunits are numerically equivalent and return the boolean
///                  value "true" if they are, else it shall return the boolean value "false".
/// @param     f_testValueA_r  first value for comparision
/// @param     f_testValueB_r  second value for comparision
/// $Source: vfc_siunits.hpp $
/// @return    returns true if specified values are equal, false otherwise

//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool isEqual(
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_testValueA_r,
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_testValueB_r);

//---------------------------------------------------------------------
/// To find values are equal with epsilon @relatesalso TSIUnits
/// @dspecref{1300}
/// @param     f_testValueA_r  first value for comparision
/// @param     f_testValueB_r  second value for comparision
/// @param     f_testEpsilonValue_r  epsilon value for comparision
/// $Source: vfc_siunits.hpp $
/// @return    returns true if specified values are equal within given epsilon

//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool isEqual(
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_testValueA_r,
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_testValueB_r,
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_testEpsilonValue_r);

//---------------------------------------------------------------------
/// To find values are not equal @relatesalso TSIUnits
/// @dspec{1299}     The SW component "vfc" shall check if the two floating point
///                  values of two given siunits are not numerically equivalent and
///                  return the boolean value "true" if it they are not equivalent,
///                  else it shall return the boolean value "false".
/// @param     f_testValueA_r  first value for comparision
/// @param     f_testValueB_r  second value for comparision
/// $Source: vfc_siunits.hpp $
/// @return    returns true if specified values are NOT equal, false otherwise

//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool notEqual(
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_testValueA_r,
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_testValueB_r);

//---------------------------------------------------------------------
/// return the smaller of two given values if both given values are not NaN values otherwise it returns the value NaN.
/// @dspecref{3008}
/// @param a input value for comparison
/// @param b input value for comparison
/// @return returns by value
//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> minNaNPropagate(
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& a,
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& b);

//---------------------------------------------------------------------
/// return the greater of two given values if both given values are not NaN values otherwise it returns the value NaN.
/// @dspecref{3009}
/// @param a input value for comparison
/// @param b input value for comparison
/// @return returns by value
//---------------------------------------------------------------------
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> maxNaNPropagate(
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& a,
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& b);

} // namespace vfc

#include "vfc/core/vfc_siunits.inl"

#endif // SIUNITS_HPP_INCLUDED

