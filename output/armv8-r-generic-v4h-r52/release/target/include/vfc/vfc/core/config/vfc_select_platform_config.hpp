//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_SELECT_PLATFORM_CONFIG_HPP_INCLUDED
#define VFC_SELECT_PLATFORM_CONFIG_HPP_INCLUDED

#ifndef VFC_CONFIG_HPP_INCLUDED
static_assert(false, "vfc_select_platform_config.hpp must not be included directly, include vfc_config.hpp instead");
#endif

// linux:
#if defined(linux) || defined(__linux) || defined(__linux__)
#define VFC_PLATFORM_CONFIG "vfc/core/config/platform/vfc_linux.hpp"
// cygwin:
#elif defined(CYGWIN) || defined(__CYGWIN) || defined(__CYGWIN__)
#define VFC_PLATFORM_CONFIG "vfc/core/config/platform/vfc_cygwin.hpp"
// win32:
#elif defined(_WIN32) || defined(__WIN32__) || defined(WIN32)
#define VFC_PLATFORM_CONFIG "vfc/core/config/platform/vfc_win32.hpp"
// eppc (VFC_EPPC_DETECTED is currently set by vfc_mwerks.hpp and vfc_diab.hpp)
#elif defined(VFC_EPPC_DETECTED)
#define VFC_PLATFORM_CONFIG "vfc/core/config/platform/vfc_eppc.hpp"
// ST-TiC6X (VFC_TIC6X_DETECTED is currently set by vfc_ticcs.hpp)
#elif defined(VFC_TIC6X_DETECTED)
#define VFC_PLATFORM_CONFIG "vfc/core/config/platform/vfc_st_tic6x.hpp"
#elif defined(VFC_TIC7A8_DETECTED)
#define VFC_PLATFORM_CONFIG "vfc/core/config/platform/vfc_st_emb.hpp"
// ST-MicroBlaze (VFC_EMB_DETECTED is currently set by vfc_gcc.hpp)
#elif defined(VFC_EMB_DETECTED)
#define VFC_PLATFORM_CONFIG "vfc/core/config/platform/vfc_st_emb.hpp"
// arm (VFC_ARM_DETECTED is currently set by vfc_armrvct.hpp)
#elif defined(VFC_ARM_DETECTED)
#define VFC_PLATFORM_CONFIG "vfc/core/config/platform/vfc_ivs_arm.hpp"
#elif defined(VFC_ARM64_DETECTED)
#define VFC_PLATFORM_CONFIG "vfc/core/config/platform/vfc_ivs_arm64.hpp"
// rh850 (VFC_RH850_DETECTED is currently set by vfc_ghc.hpp)
#elif defined(VFC_RH850_DETECTED)
#define VFC_PLATFORM_CONFIG "vfc/core/config/platform/vfc_rh850.hpp"
// v850 (VFC_V850_DETECTED is currently set by vfc_ghc.hpp)
#elif defined(VFC_V850_DETECTED)
#define VFC_PLATFORM_CONFIG "vfc/core/config/platform/vfc_v8xx.hpp"
#elif defined(VFC_TRICORE_DETECTED)
#define VFC_PLATFORM_CONFIG "vfc/core/config/platform/vfc_ivs_tricore.hpp"
#elif defined(__QNX__)
#define VFC_PLATFORM_CONFIG "vfc/core/config/platform/vfc_qnx.hpp"
#else
static_assert(false, "Unknown platform!");
#endif

#endif // VFC_SELECT_PLATFORM_CONFIG_HPP_INCLUDED

//=============================================================================

