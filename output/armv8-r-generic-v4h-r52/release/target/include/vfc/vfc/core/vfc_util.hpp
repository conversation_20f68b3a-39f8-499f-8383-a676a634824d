//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_UTIL_HPP_INCLUDED
#define ZX_VFC_UTIL_HPP_INCLUDED

#include "vfc/core/vfc_types.hpp"

namespace vfc
{ // namespace vfc opened

/// No operation function (abbreviated with nop)
///
/// @details Use this function to avoid spurious compiler warnings e.g.
///          warnings about unused variabels or unused return values. In
///          general a suppression of compiler warnings isn't a good idea.
///          But there are some cases where it makes quite sense. In the
///          following example an additional local variable is used to
///          add some debugging data. In this special case an assertion
///          is thrown, if the image couldn't be constructed.
///
///          template <class PixelType, class AllocatorType> inline
///          vfc::TImage<PixelType,AllocatorType>::TImage(int32_t f_width,
///                                                       int32_t f_height,
///                                                       int32_t f_stride)
///          :   m_alloc(f_alloc), m_view(), m_capacity(0)
///          {
///              const bool resizeOk = resize(f_width, f_height, f_stride);
///              VFC_ASSERT2(resizeOk, "resize in TImage constructor failed");
///              vfc::nop(resizeOk);
///          }
///
/// @tparam ArgumentType - Type of argument for nop function
///
/// @note This function does not represent the assembler command 'nop'.

/// @ingroup vfc_group_core_misc
///
template <class ArgumentType>
inline void nop(const ArgumentType&)
{
}

/// @brief Equivalent to vfc::nop(), but with a different concept, as wrapping a function whose return value
/// we want to ignore around a vfc::nop() is not intuitive.
///
/// @tparam ArgumentType - Type of argument for nop function
/// @ingroup vfc_group_core_misc
template <class ArgumentType>
inline void ignoreReturn(const ArgumentType&)
{
}

namespace intern
{ // namespace intern opened
/// @brief Helper function comparing two pointer with each other and return the
/// boolean value of the result.
///
/// @tparam Valuetype1 - Type of first argument to be compared
/// @tparam Valuetype2 - Type of second argument to be compared
/// @param  f_value1 - First pointer to be compared
/// @param  f_value2 - Second pointer to be compared
/// @ingroup vfc_group_core_misc
template <typename Valuetype1, typename Valuetype2>
bool safePointerCompareGreaterEqual(Valuetype1 f_value1, Valuetype2 f_value2)
{
    return reinterpret_cast<uintptr_t>(f_value1) >=
           reinterpret_cast<uintptr_t>(
               f_value2); // PRQA S 3044 # Technical debt L2 id=00c1fa637c2342fb494e-a19716e72c6ac3cf
}

/// @brief Helper function comparing two pointer with each other and return the
/// boolean value of the result.
///
/// @tparam Valuetype1 - Type of first argument to be compared
/// @tparam Valuetype2 - Type of second argument to be compared
/// @param  f_value1 - First pointer to be compared
/// @param  f_value2 - Second pointer to be compared
/// @ingroup vfc_group_core_misc
template <typename Valuetype1, typename Valuetype2>
bool safePointerCompareSmallerEqual(Valuetype1 f_value1, Valuetype2 f_value2)
{
    return reinterpret_cast<uintptr_t>(f_value1) <=
           reinterpret_cast<uintptr_t>(
               f_value2); // PRQA S 3044 # Technical debt L2 id=00c1bd2b2eccfa574a21-2cf985d8acd020ac
}
} // namespace intern
} // namespace vfc

#endif // ZX_VFC_UTIL_HPP_INCLUDED

