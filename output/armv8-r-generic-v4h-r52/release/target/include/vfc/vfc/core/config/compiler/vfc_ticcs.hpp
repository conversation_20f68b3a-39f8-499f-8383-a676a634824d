//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
//  Copyright (c) by iMotion AI. All rights reserved.
//=============================================================================
//  D E S C R I P T I O N
//-----------------------------------------------------------------------------
//  Project Name: IDC2
//  Target System(s):
//  Compiler(s):
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//  Name: Xu Ronnie
//=============================================================================

#ifndef VFC_TICCS_HPP_INCLUDED
#define VFC_TICCS_HPP_INCLUDED

#include "vfc/core/vfc_preprocessor.hpp"

/////////////////////////////////////
// version check
/////////////////////////////////////

#define VFC_COMPILER_TICCS
#define VFC_COMPILER_VERSION __TI_COMPILER_VERSION__
#define VFC_COMPILER_STRING VFC_JOIN("Ti Code Composer Studio version ", VFC_STRINGIZE(VFC_COMPILER_VERSION))

// check for ST TIC6X platform
#if defined (_TMS320C6X)
#   define VFC_TIC6X_DETECTED
#endif    


#if defined (_TMS470V7A8)
#   define VFC_TIC7A8_DETECTED
#endif    

////////////////////////////////////
// RTTI support
////////////////////////////////////

#define VFC_NO_RTTI

////////////////////////////////////
// exception support
////////////////////////////////////

#define VFC_NO_EXCEPTIONS

//////////////////////////////
// long long / __int64 support
//////////////////////////////

// no long long support!

//////////////////////////////
// cstdint support
//////////////////////////////

#define VFC_HAS_STDINT_H

//////////////////////////////
// endianess
//////////////////////////////

#if defined (_BIG_ENDIAN)
#   define VFC_BIG_ENDIAN
#endif

//////////////////////////////
// long long support
//////////////////////////////

#if !defined __STRICT_ANSI__ || defined(__llvm__)
    #if defined (VFC_TIC7A8_DETECTED) ||  defined (__ARM_ARCH)
        #   define VFC_HAS_LONG_LONG
    #endif
#endif


////////////////////////////////////
// processor identification
////////////////////////////////////

// TI product range
#if defined(_TMS320C6200)
#   define VFC_PROCESSOR_TMS320C6200
#elif defined(_TMS320C6400)
#   define VFC_PROCESSOR_TMS320C6400
#elif defined(_TMS320C6400_PLUS)
#   define VFC_PROCESSOR_TMS320C6400_PLUS
#elif defined(_TMS320C6700)
#   define VFC_PROCESSOR_TMS320C6700
#elif defined(_TMS320C6700_PLUS)
#   define VFC_PROCESSOR_TMS320C6700_PLUS
#elif defined(_TMS320C6740)
#   define VFC_PROCESSOR_TMS320C6740
#elif defined(__TI_TMS470_V7A8__)
#   define VFC_PROCESSOR_TMS470V7A8
#elif defined (__ARM_ARCH)
#   if defined(__ARM_ARCH_6__) || defined(__ARM_ARCH_6J__) || defined(__ARM_ARCH_6k__) || defined(__ARM_ARCH_6Z__) || defined(__ARM_ARCH_6ZK__) || defined(__ARM_ARCH_6T2__)
#      define VFC_ARM_DETECTED
#      define VFC_PROCESSOR_ARM
#      define VFC_PROCESSOR_ARMV6
#   elif (__ARM_ARCH == 7)
#      define VFC_ARM_DETECTED
#      define VFC_PROCESSOR_ARM
#      define VFC_PROCESSOR_ARMV7
#      if (mcpu == cortex-a8)
#          define VFC_PROCESSOR_ARM_CORTEXA8
#      endif
#   elif (__ARM_ARCH == 8)
#      define VFC_ARM_DETECTED
#      define VFC_PROCESSOR_ARM
#      define VFC_PROCESSOR_ARMV8
#   else
#       error "this arm processor is not supported"
#   endif
#else
#   error "processor not supported!"
#endif

//macro abstraction for linker statements 
#define VFC_LINKER_SECT1 __attribute__
#define VFC_LINKER_SECT(sect_name) __attribute__((section(sect_name)))//VFC_JOIN(VFC_LINKER_SECT1,(section##(sect_name)##) volatile)

#ifndef VFC_DECL_FORCEINLINE
#   define VFC_DECL_FORCEINLINE inline         // no other force inlining option found, hence simple inline is opted.           
#endif 

// the first ti compilier version to support all c++11 features is: tbd, anyway the current version support it
#if (__cplusplus >= 201103L)  && (__TI_COMPILER_VERSION__ >= 18012004)
#    define VFC_USE_CPP11_FEATURES
#endif

// the first ti compilier to support all c++14 features seem to be: tbd, anyway the current version support it
#if (__cplusplus >= 201402L)  && (__TI_COMPILER_VERSION__ >= 18012004)
#    define VFC_USE_CPP14_FEATURES
#endif

#endif //VFC_TICCS_HPP_INCLUDED




