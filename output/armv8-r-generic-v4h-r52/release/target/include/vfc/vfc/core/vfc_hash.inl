//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_HASH_INL_INCLUDED
#define VFC_HASH_INL_INCLUDED

#include "vfc/core/vfc_static_assert.hpp"

template <class ValueType>
void vfc::serialHash(vfc::hash_type& f_prevhash, const ValueType& f_nextObjToHash)
{
    const vfc::THash<ValueType> l_hash{};

    // Magic number 0x9e3779b9 is the reciprocal of the golden ratio
    // 1/phi = 0,6180339887 as hexadecimal representation. It is inserted,
    // because the bits within phi are not correlated, to have a random change
    // of each bit, independent of the input seed:
    f_prevhash ^= l_hash.operator()(f_nextObjToHash) + 0x9e3779b9U + (f_prevhash << 6) +
                  (f_prevhash >> 2); // PRQA S 4422 # Technical debt L7 id=00c13e3662a0fced4794-76a679ca3e9efbfa // PRQA
                                     // S 2921 # Technical debt L2 id=00c147b56e84bf8e46c1-4a9dbb4cea0940b8

    // The implementation is bound to a 32-bit hash type:
    VFC_STATIC_ASSERT(static_cast<vfc::size_t>(4) == sizeof(vfc::hash_type));
}

template <class ValueType>
vfc::hash_type vfc::intern::TGenericContainerHash<ValueType>::operator()(const ValueType& f_val) const
{
    vfc::hash_type hv = 0;
    using Iter        = typename ValueType::const_iterator;
    const Iter end    = f_val.end();
    for (Iter it = f_val.begin(); it != end; ++it)
    {
        vfc::serialHash(hv, *it);
    }
    return hv;
}

#endif // VFC_HASH_INL_INCLUDED

