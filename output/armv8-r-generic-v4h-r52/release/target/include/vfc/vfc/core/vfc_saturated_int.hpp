//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_SATURATED_INT_HPP_INCLUDED
#define VFC_SATURATED_INT_HPP_INCLUDED

#include "vfc/core/vfc_static_assert.hpp"
#include "vfc/core/vfc_type_traits.hpp"
#include "vfc/core/vfc_metaprog.hpp"

namespace vfc
{ // open namespace vfc

//=============================================================================
//  TSaturatedInt <>
//-----------------------------------------------------------------------------
/// Saturated integer provides a protection against overflow during math operations.
/// If an overflow is detected, the result is set to corresponding limit.
/// @note This behaviour breaks the laws of associativity and distributivity!
/// @tparam ValueType            Defines the DataType
/// @ingroup                    vfc_core
//=============================================================================
template <class ValueType>
class TSaturatedInt
{
    VFC_STATIC_ASSERT(1 == vfc::TIsIntegral<ValueType>::value); // Only integer are supported
    VFC_STATIC_ASSERT(8 > sizeof(ValueType));                   // 64 bit or greater are not supported
    VFC_STATIC_ASSERT(0 == TIsPointer<ValueType>::value);       // Pointer are not supported

  public:
    //---------------------------------------------------------------------
    // typdefes
    //---------------------------------------------------------------------
    using value_type = ValueType;
    using self_type  = TSaturatedInt<ValueType>;

    //---------------------------------------------------------------------
    /// Default constructor
    /// @dspec{1636}     The SW component "vfc" shall create and return a saturated
    ///                  integer value where the value is provided by the default constructor
    ///                  of the value type.
    //---------------------------------------------------------------------
    TSaturatedInt();

    //---------------------------------------------------------------------
    /// Constructs a new instance with the given value
    /// @dspec{777}     The SW component "vfc" shall create a saturated integer value from a
    ///                 given value.
    /// @param f_value Data to be stored
    //---------------------------------------------------------------------
    TSaturatedInt(const ValueType& f_value);

    /// @brief Request default copy constructor for non type-changing copies.
    TSaturatedInt(const TSaturatedInt& f_value_r) = default;

    //---------------------------------------------------------------------
    /// Constructs with the specified other value type
    /// @dspecref{777}
    /// @tparam OtherValueType type of data to be stored
    /// @param f_value Data to be stored
    //---------------------------------------------------------------------
    template <class OtherValueType>
    TSaturatedInt(const OtherValueType& f_value);

    //---------------------------------------------------------------------
    /// Explicit Constructs with other value type
    /// @dspec{778}     The SW component "vfc" shall create and return a saturated integer
    ///                 instance by copying and converting the contents of a given saturated integer.
    /// @tparam OtherValueType type of data to be stored
    /// @param f_rhs Data to be stored
    //---------------------------------------------------------------------
    template <class OtherValueType>
    explicit TSaturatedInt<ValueType>(const TSaturatedInt<OtherValueType>& f_rhs);

    /// @brief Request default destructor.
    ~TSaturatedInt() = default;

    //---------------------------------------------------------------------
    /// Returns the contained value
    /// @dspec{1265}     The SW component "vfc" shall return the value of given saturated integer.
    /// @return returns value of data type
    //---------------------------------------------------------------------
    ValueType value() const;

    //---------------------------------------------------------------------
    /// Right shift assignment operator
    /// @dspec{1264}     The SW component "vfc" shall shift the representation bits of a
    ///                  given saturated integer by a given number of bits to the left and
    ///                  return the shifted value.
    /// @param f_shift shift bits
    /// @return a reference to saturated integer with right shifted bits
    //---------------------------------------------------------------------
    TSaturatedInt& operator>>=(vfc::uint32_t f_shift);

    //---------------------------------------------------------------------
    /// Right shift operator
    /// @dspecref{1264}
    /// @param f_shift shift bits
    /// @return saturated integer with right shifted bits
    //---------------------------------------------------------------------
    TSaturatedInt operator>>(vfc::uint32_t f_shift);

    //---------------------------------------------------------------------
    /// Left shift assignment operator
    /// @dspec{1263}     The SW component "vfc" shall shift the representation bits of
    ///                  a given saturated integer by a given number of bits to the right
    ///                  and return the shifted value.
    /// @param f_shift shift bits
    /// @return a reference to saturated integer with left shifted bits
    //---------------------------------------------------------------------
    TSaturatedInt& operator<<=(vfc::uint32_t f_shift);

    //---------------------------------------------------------------------
    /// Left shift operator
    /// @dspecref{1263}
    /// @param f_shift shift bits
    /// @return saturated integer with left shifted bits
    //---------------------------------------------------------------------
    TSaturatedInt operator<<(vfc::uint32_t f_shift);

    //---------------------------------------------------------------------
    /// Comparision Operator
    /// @dspec{1262}     The SW component "vfc" shall return the boolean value "true"
    ///                  if two given saturated integers are equal otherwise it shall
    ///                  return the boolean value "false".
    /// @param f_rhs data to be comapared
    /// @return returns if the data contained within is equal to that of f_rhs
    //---------------------------------------------------------------------
    bool operator==(
        const TSaturatedInt& f_rhs) const; // PRQA S 2066 # Technical debt L4 id=00c1ea0e1ce0abbc4102-499146bdc614cf1b

    //---------------------------------------------------------------------
    /// Comparision Operator
    /// @dspec{1261}     The SW component "vfc" shall return the boolean value "true" if
    ///                  two given saturated integers are NOT equal otherwise it shall return
    ///                  the boolean value "false".
    /// @param f_rhs data to be comapared
    /// @return returns if the data contained within is inequal to that of f_rhs
    //---------------------------------------------------------------------
    bool operator!=(
        const TSaturatedInt& f_rhs) const; // PRQA S 2066 # Technical debt L4 id=00c1352313d4fcff45b4-e2c7801b8052ce58

    //---------------------------------------------------------------------
    /// Lesser than Operator
    /// @dspec{1260}     The SW component "vfc" shall compare two given saturated integers
    ///                  and check if the first given saturated integer is smaller than,
    ///                  larger than, smaller or equal than, or larger or equal than the other
    ///                  given saturated integer and return a boolean value that signifies if
    ///                  the checked relation holds.
    /// @param f_rhs data to be comapared
    /// @return returns if the data contained within is less to that of f_rhs
    //---------------------------------------------------------------------
    bool operator<(
        const TSaturatedInt& f_rhs) const; // PRQA S 2066 # Technical debt L4 id=00c10610be291ab3451c-c0e49b5a476a39a6

    //---------------------------------------------------------------------
    /// Greater than Operator
    /// @dspecref{1260}
    /// @param f_rhs data to be comapared
    /// @return returns if the data contained within is greater to that of f_rhs
    //---------------------------------------------------------------------
    bool operator>(
        const TSaturatedInt& f_rhs) const; // PRQA S 2066 # Technical debt L4 id=00c1c5aa7d42a789452e-fb847b12105ec6ae

    //---------------------------------------------------------------------
    /// Lesser than Or Equal to Operator
    /// @dspecref{1260}
    /// @param f_rhs data to be comapared
    /// @return returns if the data contained within is less than or equal to that of f_rhs
    //---------------------------------------------------------------------
    bool operator<=(
        const TSaturatedInt& f_rhs) const; // PRQA S 2066 # Technical debt L4 id=00c11c9e50b3b06643a4-d9682637ac36d950

    //---------------------------------------------------------------------
    /// Greater than Or Equal to Operator
    /// @dspecref{1260}
    /// @param f_rhs data to be comapared
    /// @return returns if the data contained within is greater than or equal to that of f_rhs
    //---------------------------------------------------------------------
    bool operator>=(
        const TSaturatedInt& f_rhs) const; // PRQA S 2066 # Technical debt L4 id=00c15947833f60184a80-1decb24730dcd78f

    //---------------------------------------------------------------------
    /// Addition Assignment Operator
    /// @dspec{1259}     The SW component "vfc" shall add two given saturated integers
    ///                  and return a saturated integer representing their saturated sum.
    /// @param f_rhs data to be added
    /// @return sum as reference to saturated integer
    //---------------------------------------------------------------------
    TSaturatedInt& operator+=(const TSaturatedInt& f_rhs);

    //---------------------------------------------------------------------
    /// Addition Operator
    /// @dspecref{1259}
    /// @param f_rhs data to be added
    /// @return return sum as saturated integer
    //---------------------------------------------------------------------
    TSaturatedInt operator+(const TSaturatedInt& f_rhs);

    //---------------------------------------------------------------------
    /// Subtraction Assignment Operator
    /// @dspec{1258}     The SW component "vfc" shall subtract two given saturated
    ///                  integers and return a saturated integer representing their
    ///                  saturated difference.
    /// @param f_rhs data to be subtracted
    /// @return difference as reference to saturated integer
    //---------------------------------------------------------------------
    TSaturatedInt& operator-=(const TSaturatedInt& f_rhs);

    //---------------------------------------------------------------------
    /// Subtraction Operator
    /// @dspecref{1258}
    /// @param f_rhs data to be subtracted
    /// @return difference as saturated integer
    //---------------------------------------------------------------------
    TSaturatedInt operator-(const TSaturatedInt& f_rhs);

    //---------------------------------------------------------------------
    /// Multiplication Assignment Operator
    /// @dspec{1257}     The SW component "vfc" shall multiply two given saturated integers
    ///                  and return a saturated integer representing their saturated product.
    /// @param f_rhs data to be multiplied
    /// @return product as reference to saturated integer
    //---------------------------------------------------------------------
    TSaturatedInt& operator*=(const TSaturatedInt& f_rhs);

    //---------------------------------------------------------------------
    /// Multiplication Operator
    /// @dspecref{1257}
    /// @param f_rhs data to be multiplied
    /// @return product as to saturated integer
    //---------------------------------------------------------------------
    TSaturatedInt operator*(const TSaturatedInt& f_rhs);

    //---------------------------------------------------------------------
    /// Division Assignment Operator
    /// @dspec{1256}     The SW component "vfc" shall divide two given saturated integers
    ///                  and return a saturated integer representing their saturated quotient.
    /// @param f_rhs data to be divided
    /// @return quotient as reference to saturated integer
    //---------------------------------------------------------------------
    TSaturatedInt& operator/=(const TSaturatedInt& f_rhs);

    //---------------------------------------------------------------------
    /// Division Operator
    /// @dspecref{1256}
    /// @param f_rhs data to be divided
    /// @return quotient as saturated integer
    //---------------------------------------------------------------------
    TSaturatedInt operator/(const TSaturatedInt& f_rhs);

    //---------------------------------------------------------------------
    /// Extend to a type with higher precision
    /// @dspec{1255}     The SW component "vfc" shall extent a given saturated integer
    ///                  to a type with higher precision and return the extended value
    ///                  as a saturated integer.
    /// @tparam OtherValueType  a type with higher precision
    /// @return the saturated integer with higher precision
    //---------------------------------------------------------------------
    template <class OtherValueType>
    TSaturatedInt<OtherValueType> extend_to() const;

    //---------------------------------------------------------------------
    /// truncate to a type with lower or same precision
    /// @dspec{1254}     The SW component "vfc" shall truncate a given saturated integer
    ///                  to a type with lower or same precision and return the truncated
    ///                  value as a saturated integer.
    /// @tparam OtherValueType a type with lower or same precision
    /// @return the saturated integer with lower or same precision
    //---------------------------------------------------------------------
    template <class OtherValueType>
    TSaturatedInt<OtherValueType> truncate_to() const;

  private:
    //---------------------------------------------------------------------
    /// truncate to a type with same precision
    /// @dspecref{1254}
    //---------------------------------------------------------------------
    template <class OtherValueType>
    TSaturatedInt<OtherValueType> truncate_to(true_t) const;

    //---------------------------------------------------------------------
    /// truncate to a type with lower precision
    /// @dspecref{1254}
    //---------------------------------------------------------------------
    template <class OtherValueType>
    TSaturatedInt<OtherValueType> truncate_to(false_t) const;

    //---------------------------------------------------------------------
    /// A type that represent the next higher data type of the data type ValueType
    //---------------------------------------------------------------------
    using value_type_extended = typename vfc::TIf<(sizeof(ValueType) < 4), vfc::int32_t, vfc::int64_t>::type;

    //---------------------------------------------------------------------
    /// Saturates the input data when an overflow occurs,
    /// overflow check is applied to the data type ReturnValueType.
    /// @param f_value data to be checked
    /// @return returns the saturated input value
    //---------------------------------------------------------------------
    template <class InputValueType, class ReturnValueType>
    static ReturnValueType limitRange(const InputValueType& f_value);

    //---------------------------------------------------------------------
    /// Member variable used store the data value
    //---------------------------------------------------------------------
    ValueType m_value;
};

using TSatInt_i8_t   = vfc::TSaturatedInt<vfc::int8_t>;
using TSatInt_ui8_t  = vfc::TSaturatedInt<vfc::uint8_t>;
using TSatInt_i16_t  = vfc::TSaturatedInt<vfc::int16_t>;
using TSatInt_ui16_t = vfc::TSaturatedInt<vfc::uint16_t>;
using TSatInt_i32_t  = vfc::TSaturatedInt<vfc::int32_t>;
using TSatInt_ui32_t = vfc::TSaturatedInt<vfc::uint32_t>;

} // namespace vfc

#include "vfc/core/vfc_saturated_int.inl"

#endif // VFC_SATURATED_INT_HPP_INCLUDED

