//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_ATOMIC_SCA_INL_INCLUDED
#define ZX_VFC_ATOMIC_SCA_INL_INCLUDED

static_assert(ZX_BUILD_SCA(), "This file is just to be included during static code analysis.");

namespace vfc
{
namespace intern
{
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/// For static code analysis thread-safety and atomic operations are not needed.
/// It just has to understand the code and be able follow the data.

namespace pseudo
{
template <typename ValType>
bool compare_exchange_weak(ValType& memory, ValType& expected, ValType desired)
{
    const ValType prev = memory;
    if (prev == expected)
    {
        memory = desired;
        return true;
    }
    else
    {
        expected = prev;
        return false;
    }
}

template <typename ValType>
ValType exchange(ValType& memory, ValType desired)
{
    const ValType prev = memory;
    memory             = desired;
    return prev;
}
} // namespace pseudo

template <>
inline bool AtomicOps<uint32_t>::compare_exchange_weak(
    uint32_t&         memory,
    uint32_t&         expected,
    uint32_t          desired,
    vfc::EMemoryOrder successOrder,
    vfc::EMemoryOrder failureOrder)
{
    return pseudo::compare_exchange_weak(memory, expected, desired);
}

template <>
inline bool AtomicOps<uint64_t>::compare_exchange_weak(
    uint64_t&         memory,
    uint64_t&         expected,
    uint64_t          desired,
    vfc::EMemoryOrder successOrder,
    vfc::EMemoryOrder failureOrder)
{
    return pseudo::compare_exchange_weak(memory, expected, desired);
}

template <>
inline uint32_t AtomicOps<uint32_t>::exchange(uint32_t& memory, uint32_t desired, vfc::EMemoryOrder order)
{
    return pseudo::exchange(memory, desired);
}

template <>
inline uint64_t AtomicOps<uint64_t>::exchange(uint64_t& memory, uint64_t desired, vfc::EMemoryOrder order)
{
    return pseudo::exchange(memory, desired);
}

template <>
inline void AtomicOps<uint32_t>::storeRelease(uint32_t& memory, uint32_t val)
{
    memory = val;
}

template <>
inline void AtomicOps<uint64_t>::storeRelease(uint64_t& memory, uint64_t val)
{
    memory = val;
}

template <>
inline void AtomicOps<uint32_t>::storeSeqCst(uint32_t& memory, uint32_t val)
{
    memory = val;
}

template <>
inline void AtomicOps<uint64_t>::storeSeqCst(uint64_t& memory, uint64_t val)
{
    memory = val;
}

template <>
inline uint32_t AtomicOps<uint32_t>::loadAcquire(const uint32_t& memory)
{
    return memory;
}

template <>
inline uint64_t AtomicOps<uint64_t>::loadAcquire(const uint64_t& memory)
{
    return memory;
}

template <>
inline uint32_t AtomicOps<uint32_t>::loadSeqCst(const uint32_t& memory)
{
    return memory;
}

template <>
inline uint64_t AtomicOps<uint64_t>::loadSeqCst(const uint64_t& memory)
{
    return memory;
}

template <>
inline void Fence<DummyType>::seqCst()
{
    // No fences needed for SCA
}

template <>
inline void Fence<DummyType>::acqRel()
{
    // No fences needed for SCA
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
} // namespace intern
} // namespace vfc

#endif // ZX_VFC_ATOMIC_SCA_INL_INCLUDED

