//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_NAMEDTYPE_HPP_INCLUDED
#define ZX_VFC_NAMEDTYPE_HPP_INCLUDED

#include "vfc/core/vfc_types.hpp" // used for size_t

namespace vfc
{
/// Template to create strongly typed wrapper around fundamental types. APIs with simple fundamental types (e.g.
/// `uint32_t`) can cause ambiguity. For example the function `void foo(uint32_t width, uint32_t height)` can be called
/// correctly by `foo(2u, 5u)` or by mistake `foo(5u, 2u)`. Use this strong type wrapper to avoid this. Example: `using
/// Width = TNamedType<uint32_t, struct width_tag>;` `using Height = TNamedType<uint32_t, struct height_tag>;` `void
/// foo(Width width, Height height){ ... }` `foo(Width{2u}, Height{5u});`
///
/// @tparam Type        Fundamental type to create strong type of, e.g. uint32_t.
/// @tparam TypeTag     Tag needed to make instantiated type unique and independent of underlying type.
template <typename Type, typename TypeTag>
class TNamedType
{
  public:
    using type = Type;

    explicit constexpr TNamedType(Type const& value) : m_value(value) {}

    Type get() const { return m_value; }

  private:
    Type m_value;
};

///////////////////////////////////
// strong types available for usage
///////////////////////////////////

/// @brief Use SizeType for the number of current elements within a container.
using SizeType = TNamedType<int32_t, struct size_tag>;

} // namespace vfc

#endif // ZX_VFC_NAMEDTYPE_HPP_INCLUDED

