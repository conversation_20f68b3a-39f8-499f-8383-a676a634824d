//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_PLATFORM_ATTRIBUTES_HPP_INCLUDED
#define ZX_VFC_PLATFORM_ATTRIBUTES_HPP_INCLUDED
//=============================================================================

// Every modern platform we know, even if 64bit, has the 'int' defined as 32 bits. Note that 'int' is the type used for
// 'integral promotion'.
static_assert(sizeof(int) == 4, "The built-in type 'int' has to be 32 bits.");

// The type 'bool' is implementation defined, but usually occupies one byte.
static_assert(sizeof(bool) == 1, "The built-in type 'bool' has to be one byte.");

// Require that the right-shift on signed types is arithmetic. This is implementation defined by the standard, but usual
// compilers do an arithmetic shift instead of logical shift.
static_assert(((-4) >> 1) == -2, "Using arithmetic right-shift on negative values.");

// Require CPUs with two's complement arithmetic. -1 and ~0 are both signed integers, not unsigned, as it would be
// guaranteed by the standard that static_cast<unsigned int>(-1) == 0xFFFFFFFFU, and this wouldn't be a test.
static_assert((-1 == ~0), "Have to use two's complement.");

//=============================================================================
#endif // ZX_VFC_PLATFORM_ATTRIBUTES_HPP_INCLUDED

