//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_MATH_HPP_INCLUDED
#define ZX_VFC_MATH_HPP_INCLUDED

#include "vfc/core/vfc_types.hpp"  // used for fundamental types
#include "vfc/core/vfc_config.hpp" // used for predefines for compiler-checks
#include "vfc/core/vfc_metaprog.hpp"
#include "vfc/core/vfc_limits.hpp"

namespace vfc
{ // namespace vfc opened

//=============================================================================
//  DOXYGEN DEFGROUP vfc_group_core_algorithms_math BEGIN
//-----------------------------------------------------------------------------
/// @defgroup vfc_group_core_algorithms_math Math
/// @ingroup vfc_group_core_algorithms
/// @brief Math Functions and Constants.
/// @{
//=============================================================================

//=============================================================================
// definitions of useful mathematical constants
//=============================================================================

#ifdef VFC_ENABLE_FLOAT64_TYPE
using float_const_t = const float64_t;
using float_cast_t  = float64_t;
#else
using float_const_t = const float32_t;
using float_cast_t  = float32_t;
#endif

// trig
//-------------------------------------------------------------------------
float_const_t G_PI   = static_cast<float_cast_t>(3.1415926535897932384626433832795);      ///< yes, its really pi!
float_const_t G_2PI  = static_cast<float_cast_t>(2. * 3.1415926535897932384626433832795); ///< 2*pi
float_const_t G_PI_2 = static_cast<float_cast_t>(3.1415926535897932384626433832795 / 2.); ///< pi/2
float_const_t G_PI_4 = static_cast<float_cast_t>(3.1415926535897932384626433832795 / 4.); ///< pi/4
float_const_t G_1_PI = static_cast<float_cast_t>(1. / 3.1415926535897932384626433832795); ///< 1/pi
float_const_t G_2_PI = static_cast<float_cast_t>(2. / 3.1415926535897932384626433832795); ///< 2/pi

float_const_t G_DEG2RAD =
    static_cast<float_cast_t>(3.1415926535897932384626433832795 / 180.); ///< degree to radiant factor (pi/180)
float_const_t G_RAD2DEG =
    static_cast<float_cast_t>(180. / 3.1415926535897932384626433832795); ///< radiant to degree factor (180/pi)

float_const_t G_1_3 = static_cast<float_cast_t>(1. / 3.); ///< 1/3

// sqrt
//-------------------------------------------------------------------------
float_const_t G_SQRT2   = static_cast<float_cast_t>(1.4142135623730950488016887242096);      ///< sqrt(2)
float_const_t G_SQRT3   = static_cast<float_cast_t>(1.7320508075688772935274463415059);      ///< sqrt(3)
float_const_t G_1_SQRT2 = static_cast<float_cast_t>(1. / 1.4142135623730950488016887242096); ///< 1/sqrt(2)

// e
//-------------------------------------------------------------------------
float_const_t G_E      = static_cast<float_cast_t>(2.7182818284590452353602874713527); ///< e
float_const_t G_LOG2E  = static_cast<float_cast_t>(1.44269504088896340736);            ///< log2(e)
float_const_t G_LOG10E = static_cast<float_cast_t>(0.434294481903251827651);           ///< log10(e)

// ln
//-------------------------------------------------------------------------
float_const_t G_LN2  = static_cast<float_cast_t>(0.69314718055994530941723212145818); ///< ln(2)
float_const_t G_LN10 = static_cast<float_cast_t>(2.3025850929940456840179914546844);  ///< ln(10)

#ifndef VFC_PLATFORM_V8XX
#ifdef VFC_ENABLE_FLOAT64_TYPE
/// defines the difference between 1 and the smallest value greater than 1 that float64_t can represent.
const float64_t G_EPSILON_F64 = vfc::numeric_limits<float64_t>::epsilon();
#endif // VFC_ENABLE_FLOAT64_TYPE
/// defines the difference between 1 and the smallest value greater than 1 that float32_t can represent.
const float32_t G_EPSILON_F32 = vfc::numeric_limits<float32_t>::epsilon();
#endif

//=============================================================================
// To segregate float32_t and float64_t to perform sqrt operation
//=============================================================================
template <class ValueType>
class TSqrtTraits
{
  public:
#if (defined(VFC_ENABLE_FLOAT64_TYPE) || defined(VFC_ENABLE_QM_MATH))
    using ArgType =
        typename vfc::TIf<vfc::TIsSameType<ValueType, vfc::float64_t>::value, vfc::float64_t, vfc::float32_t>::type;
#else
    using ArgType = vfc::float32_t;
#endif
};

//=============================================================================
// typed constants
//=============================================================================

//-------------------------------------------------------------------------
/// returns zero with specified type.
/// @dspec{744}     The SW component "vfc" shall return a constant value of the given type
///                 representing the arithmetic value zero.
/// @par usage:
/// @code
/// float32_t zero_val = typedZero<float32_t>();
/// @tparam ValueType  type used for zero initialization, no enum types
/// @return instance initialized with zero
/// @endcode
//-------------------------------------------------------------------------
template <class ValueType>
constexpr inline ValueType typedZero();

//-------------------------------------------------------------------------
/// returns one with specified type.
/// @dspec{745}     The SW component "vfc" shall return a constant value of the given type
///                 representing the arithmetic value one.
/// @tparam ValueType  type used for initialization with 1, no enum or pointer types
/// @return instance initialized with 1
/// @sa typedZero()
//-------------------------------------------------------------------------
template <class ValueType>
constexpr inline ValueType typedOne();

//-------------------------------------------------------------------------
/// returns two with specified type.
/// @dspec{1213}     The SW component "vfc" shall return a constant value of the given type
///                  representing the arithmetic value two.
/// @tparam ValueType  type used for initialization with 2, no enum, pointer or bool types
/// @return instance initialized with 2
/// @sa typedZero(), typedOne()
//-------------------------------------------------------------------------
template <class ValueType>
constexpr inline ValueType typedTwo();

//-------------------------------------------------------------------------
/// returns pi with specified type.
/// @dspec{1212}     The SW component "vfc" shall return a constant value of the given type
///                  representing the mathematical constant Pi.
/// @tparam ValueType  type used for initialization with PI only for floating point types
/// @return instance initialized with PI
/// @sa typedZero(), typedOne(), typedTwo()
//-------------------------------------------------------------------------
template <class ValueType>
constexpr inline ValueType typedPi();

//-------------------------------------------------------------------------
/// returns degrees to radian conversion factor with specified type.
/// @dspec{1211}     The SW component "vfc" shall return the value of the given type
///                  representing the conversion factor from Degrees to Radians.
/// @tparam ValueType  type used for initialization with degrees to radian conversion factor
///         only for floating point types
/// @return instance initialized with degrees to radian conversion factor
/// @sa typedPi()
//-------------------------------------------------------------------------
template <class ValueType>
constexpr inline ValueType typedDegree2Radian();

//-------------------------------------------------------------------------
/// returns radian to degrees conversion factor with specified type.
/// @dspec{1210}     The SW component "vfc" shall return the value of the given type
///                  representing the conversion factor from Radians to Degrees.
/// @tparam ValueType  type used for initialization with radian to degrees conversion factor
///         only for floating point types
/// @return instance initialized with radian to degrees conversion factor
/// @sa typedPi()
//-------------------------------------------------------------------------
template <class ValueType>
constexpr inline ValueType typedRadian2Degree();

//=============================================================================
// test for NAN
//=============================================================================

//-------------------------------------------------------------------------
/// @dspec{1209}     The SW component "vfc" shall return the boolean value "true" if a
///                  given floating point value represents NAN else it shall return the
///                  boolean value "false".
/// @tparam T  the type used for the NAN check
/// @param f_value  the value which is checked for NAN
/// @return true if given value is NAN, false otherwise.
//-------------------------------------------------------------------------
template <class T>
constexpr bool isNAN(const T& f_value);

//=============================================================================
// test for +INF, -INF
//=============================================================================

//-------------------------------------------------------------------------
/// @dspec{1208}     The SW component "vfc" shall check if a given floating point value
///                  represents an infinite value and return the boolean value "true" if
///                  it does otherwise it shall return the boolean value "false".
/// @tparam T  the type used for the INF check
/// @param  f_value  the value which is checked for +INF or -INF
/// @return true if given value is +INF or -INF, false otherwise.
//-------------------------------------------------------------------------
template <class T>
bool isINF(const T& f_value);

//=============================================================================
// tests for ZERO
//=============================================================================

//-------------------------------------------------------------------------
/// @dspec{1207}     The SW component "vfc" shall check if a given floating point value
///                  represents a numerically zero value and return the boolean value "true"
///                  if it does otherwise it shall return the boolean value "false".
/// @tparam T  the type used for the zero check
/// @param  f_value  the value which is checked for zero.
/// @return true if specified value equals zero, false otherwise.
/// uses an appropriate epsilon for floating point values ]-epsilon,epsilon[
//-------------------------------------------------------------------------
template <class T>
bool isZero(const T& f_value);

//-------------------------------------------------------------------------
/// @dspec{1206}     The SW component "vfc" shall check if a given floating point value
///                  represents a numerically non-zero value and return the boolean value "true"
///                  if it does otherwise it shall return the boolean value "false".
/// @tparam T  the type used to check for zero inequality
/// @param f_value  the value which is checked for zero inequality
/// @return true if specified value is NOT zero, false otherwise
/// uses an appropriate epsilon for floating point values
/// [-inf,-epsilon] || [epsilon,+inf]
//-------------------------------------------------------------------------
template <class T>
bool notZero(const T& f_value);

//-------------------------------------------------------------------------
/// @dspec{1205}     The SW component "vfc" shall check if a given floating point value
///                  represents a numerically positive value and return the boolean value "true"
///                  if it does otherwise it shall return the boolean value "false".
/// @tparam T  the type used in the check for positive numbers
/// @param f_value  the value which is checked for positive numbers
/// @return true if specified value is greater zero ]0,+inf], false otherwise
/// uses an appropriate epsilon for floating point values [epsilon,+inf].
//-------------------------------------------------------------------------
template <class T>
constexpr bool isPositive(const T& f_value);

//-------------------------------------------------------------------------
/// @dspec{1204}     The SW component "vfc" shall check if a given floating point value
///                  represents a numerically non-positive value and return the boolean value
///                  "true" if it does otherwise it shall return the boolean value "false".
/// @tparam T  the type used in the check for negative numbers and zero
/// @param f_value  the value which is checked for negative numbers and zero
/// @return true if specified value is zero or less [-inf,0], false otherwise
/// uses an appropriate epsilon for floating point values [-inf,+epsilon[.
/// @note   keep in mind, that a floating point @a val can have a positive sign
///         although notPositive(@a val) evaluates to @a true;
//-------------------------------------------------------------------------
template <class T>
constexpr bool notPositive(const T& f_value);

//-------------------------------------------------------------------------
/// @dspec{1203}     The SW component "vfc" shall check if a given floating point value
///                  represents a numerically negative value and return the boolean value "true"
///                  if it does otherwise it shall return the boolean value "false".
/// @tparam T  the type used in the check for negative numbers
/// @param f_value  the value which is checked for negative numbers
/// @return true, if specified value is less zero [-inf,0[ , false otherwise
/// uses an appropriate epsilon for floating point values [-inf,-epsilon] .
//-------------------------------------------------------------------------
template <class T>
constexpr bool isNegative(const T& f_value);

//-------------------------------------------------------------------------
/// @dspec{1202}     The SW component "vfc" shall check if a given floating point value
///                  represents a numerically non-negative value and return the boolean value
///                  "true" if it does otherwise it shall return the boolean value "false".
/// @tparam T  the type used in the check for[0,+inf]
/// @param  f_value  the value which is checked for [0,+inf]
/// @return true, if specified value is zero or greater [0,+inf], false otherwise
/// uses an appropriate epsilon for floating point values ]-epsilon,+inf].
/// @note   keep in mind, that a floating point @a val can have a negative sign
///         although notNegative(@a val) evaluates to @a true;
//-------------------------------------------------------------------------
template <class T>
constexpr bool notNegative(const T& f_value);

//=============================================================================
// tests for EQUALITY
//=============================================================================

//-------------------------------------------------------------------------
/// @dspec{1201}     The SW component "vfc" shall check if two given floating point values
///                  are numerically equivalent and return the boolean value "true" if they
///                  are otherwise it shall return the boolean value "false".
/// @tparam T  the type used for equality check
/// @param f_value1 the first value used for equality check
/// @param f_value2 the second value used for equality check
/// @return true  if specified values are equal, false otherwise.
/// uses an adapting epsilon for floating point values.
/// @note When infinite floating point values are among the parameters the
/// computation may produce NaN floating point values internally.
/// @note If any argument is a NaN value the result is undefined for
/// performance reasons. In that case the result is deterministic but is
/// not suitable for detecting NaN values. To check if a value is NaN use
/// isNAN() or floatClassify().
/// @note For floating point values isEqual does not represent an
/// equivalence relation. Especially, due to its approximative nature it is
/// not transitive, i.e. isEqual(a,b) && isEqual(b,c) != isEqual(a,c)
/// @sa isNAN(), floatClassify()
//-------------------------------------------------------------------------
template <class T>
bool isEqual(const T& f_value1, const T& f_value2);

//-------------------------------------------------------------------------
/// @dspecref{1201}
/// @tparam T  the type used for equality check
/// @param f_value1 the first value used for equality check
/// @param f_value2 the second value used for equality check
/// @param f_epsilon very small value which is used to check for equality in small range
/// @return true  if specified values are equal, false otherwise.
/// returns true if specified values are equal within given epsilon.
//-------------------------------------------------------------------------
template <class T>
bool isEqual(const T& f_value1, const T& f_value2, const T& f_epsilon);

//-------------------------------------------------------------------------
/// @dspec{1200}     The SW component "vfc" shall check if two given floating point values are
///                  not numerically equivalent and return the boolean value "true" if they are
///                  not otherwise it shall return the boolean value "false".
/// @tparam T  the type used for inequality check
/// @param f_value1 the first value used for inequality check
/// @param f_value2 the second value used for inequality check
/// @return true if specified values are NOT equal, false otherwise.
/// uses an adapting epsilon for floating point values.
/// @note When infinite floating point values are among the parameters the
/// computation may produce NaN floating point values internally.
/// @note If any argument is a NaN value the result is undefined for
/// performance reasons. The result is always the negation of the corresponding
/// result of isEqual(). To check for NaN values use isNAN() or floatClassify().
/// @sa isEqual(), isNAN(), floatClassify()
//-------------------------------------------------------------------------
template <class T>
bool notEqual(const T& f_value1, const T& f_value2);

//=============================================================================
// checked divide
//=============================================================================

//-------------------------------------------------------------------------
/// @dspec{1199}     The SW component "vfc" shall compute the quotient of two given numerical
///                  values and return the quotient value.
/// @tparam T the type used for division
/// @param f_num the value used as the numerator
/// @param f_denom the value used as the denominator
/// @return numerator divided by denominator @f$ y= \frac{num}{denom} @f$
/// @pre f_denom it not zero (within an epsilon for floating point types)
/// @sa isZero()
//-------------------------------------------------------------------------
template <class T>
T divide(const T& f_num, const T& f_denom);

//=============================================================================
// more mathematical functions
//=============================================================================

//-------------------------------------------------------------------------
/// rounds up the input of type vfc::float32_t to the greatest integer less
/// than or equal to the input
/// @dspec{1884}     The SW component "vfc" shall compute and return the greatest integer
///                  value less or equal to the given input value.
/// @param f_value the value to be rounded
/// @return[float32_t] rounded value
/// @sa ceil, round, lround
//-------------------------------------------------------------------------
inline vfc::float32_t floor(vfc::float32_t f_value);

//-------------------------------------------------------------------------
/// rounds up the input of type vfc::float32_t to the least integer greater
/// than or equal to the input
/// @dspec{1885}     The SW component "vfc" shall compute and return the smallest integer
///                  value greater or equal to the given input value.
/// @param f_value the value to rounded
/// @return[float32_t] rounded value
/// @sa floor, round, lround
//-------------------------------------------------------------------------
inline vfc::float32_t ceil(vfc::float32_t f_value);

//-------------------------------------------------------------------------
/// rounds the input of type vfc::float32_t to the nearest integer
/// where halfway cases are rounded away from zero
/// and return the value as vfc::float32_t
/// @dspec{1886}     The SW component "vfc" shall compute and return the nearest integer
///                  value to a given input value where in cases where two such numbers
///                  exist the one with the larger absolute value shall be returned.
/// @param f_value  the value to be rounded
/// @return[float32_t] rounded value
///
/// @sa lround, ceil, floor
/// \see ISO/IEC 14882 26.8 C library, math functions
/// \see http://en.cppreference.com/w/cpp/numeric/math/round
//-------------------------------------------------------------------------
inline vfc::float32_t round(vfc::float32_t f_value);

//-------------------------------------------------------------------------
/// rounds the input of type vfc::float32_t to the nearest integer
/// where halfway cases are rounded away from zero
/// and return the value as vfc::int64_t
/// @dspecref{1886}
/// @param f_value  the value to be rounded
/// @return[int64_t] rounded value
///
/// @sa round, ceil, floor
/// \see ISO/IEC 14882 26.8 C library, math functions
/// \see http://en.cppreference.com/w/cpp/numeric/math/lround
//-------------------------------------------------------------------------
inline vfc::int64_t lround(vfc::float32_t f_value);

//-------------------------------------------------------------------------
/// clamps specified value of int32 to zero as lower bound
/// @dspec{1198}     The SW component "vfc" shall return the given input value if it is
///                  positive else it shall return the zero value.
/// @param f_value  the value to clamp
/// @return @f[  y= \left \{ \begin{array}{cl} 0 & ,x<0 \\ x & ,x>=0  \\ \end{array} \right \}  @f]
//-------------------------------------------------------------------------
inline int32_t clampNegValueToZero(int32_t f_value);

//-------------------------------------------------------------------------
/// clamps specified value of float32 to zero as lower bound
/// @dspecref{1198}
/// @param f_value  the value to clamp
/// @return @f[  y= \left \{ \begin{array}{cl} 0 & ,x<0 \\ x & ,x>=0  \\ \end{array} \right \}  @f]
//-------------------------------------------------------------------------
inline float32_t clampNegValueToZero(float32_t f_value);

//-------------------------------------------------------------------------
/// clamps specified value to zero as lower bound
/// @dspecref{1198}
/// @param f_value  the value to clamp
/// @return @f[  y= \left \{ \begin{array}{cl} 0 & ,x<0 \\ x & ,x>=0  \\ \end{array} \right \}  @f]
//-------------------------------------------------------------------------
template <class T>
T clampNegValueToZero(const T& f_value);

//-------------------------------------------------------------------------
/// clamps specified value to upper and lower bound.
/// @dspec{1197}     The SW component "vfc" shall return the given input value if it inside
///                  the range defined by the given minimum and given maximum value else if the
///                  given input value is smaller than the given minimum value it shall return
///                  the given minimum value, else it shall return the given maximum value.
/// @param f_min  the lower bound
/// @param f_value  the value to clamp
/// @param f_max  the upper bound
/// @return @f[  y= \left \{ \begin{array}{cl} min & ,x<=min \\ x & ,min<x<max  \\ max & ,x>= max \\ \end{array} \right
/// \}  @f]
//-------------------------------------------------------------------------
template <class T>
T clampValueToMinMax(const T& f_min, const T& f_value, const T& f_max);

//-------------------------------------------------------------------------
/// abs wrapper, uses fast_abs() for integral types
/// @dspec{1196}     The SW component "vfc" shall return the absolute value of a given value.
/// @tparam T  the type used to calculate the absolute value
/// @param f_value  the value of which the absolute value is calculated
/// @return @f[  y= \left \{ \begin{array}{rl} -x & ,x<0 \\ x & ,x>=0 \\ \end{array} \right \}  @f]
//-------------------------------------------------------------------------
template <class T>
T abs(const T& f_value);

//-------------------------------------------------------------------------
/// rounds floating point value to nearest integer.
/// The rounding method is round-to-nearest-even for the optimized path
/// ( {cw,vc}-win32-x86 ) and round-half-up for the stdc fallback.
/// @dspec{1195}     The SW component "vfc" shall return an integer that has the minimal
///                  distance to a given value.
/// @param f_value  the floating point value to be rounded to the nearest integer
/// @return The rounded integer value
/// @note  We will provide both rounding methods with dedicated functions
/// in an upcoming release.
//-------------------------------------------------------------------------
inline int32_t nint(float32_t f_value);

//-------------------------------------------------------------------------
/// return the next representable vfc::float32_t value after f_start
/// in the direction of f_toward.
/// @dspec{1878}     The SW component "vfc" shall return the next representable floating point
///                  number of a given floating point number in the direction of a second given
///                  floating point number.
/// @param f_start  the starting floating point value
/// @param f_toward  the direction in which the next representable vfc::float32_t shall be calculated
/// @return[float32_t] next representable value nearer to f_toward than f_start
/// \see http://en.cppreference.com/w/cpp/numeric/math/nextafter
//-------------------------------------------------------------------------
inline float32_t nextafter(float32_t f_start, float32_t f_toward);

/// Classification of float values
enum EFloatClassification : vfc::int32_t
{
    FloatClass_Infinite,  //< Plus and Minus Infinity
    FloatClass_NaN,       //< Not a Number, no distinction between signaling or non-signaling
    FloatClass_Normal,    //< finite normalized value
    FloatClass_SubNormal, //< finite subnormal value
    FloatClass_Zero       //< Zero value, i.e. +0 or -0
};

//-------------------------------------------------------------------------
/// return the Classification of the given float32_t value
/// @dspec{1879}     The SW component "vfc" shall return an enum value that specifies if a
///                  given floating point number is zero, a normalized number, a subnormal number,
///                  an infinite number, or not-a-number.
/// @param f_value  the floating point value to classify
/// @return[EFloatClassification] float class the input value belong to
/// \see http://en.cppreference.com/w/cpp/numeric/math/fpclassify
//-------------------------------------------------------------------------
inline EFloatClassification floatClassify(float32_t f_value);

//-------------------------------------------------------------------------
/// @dspec{1194}     The SW component "vfc" shall calculate and return the square root of a
///                  given value.
/// @param f_value  the integer value used in the square root check
/// @return an int32 approximation of square root of given int32 argument
/// See also hackers delight:
/// -# http://www.hackersdelight.org/hdcodetxt/isqrt.c.txt
//-------------------------------------------------------------------------
inline int32_t isqrt(int32_t f_value);

//-------------------------------------------------------------------------
/// @dspec{1193}     The SW component "vfc" shall calculate and return the reciprocal square root
///                  of a given value.
/// @param f_value  the floating point value from which the fast approximation of reciprocal square root is calculated
/// @return a fast approximation of reciprocal square root of given float32 argument
/// @f$ y \simeq \frac{1}{\sqrt{x}} @f$, only works with IEEE 754 floating point standard (32bit).
/// The bit magic is accurate to ~0.175%. (~4% before used newton step) @n
/// For an in depth discussion, see
/// -# http://www.math.purdue.edu/~clomont/Math/Papers/2003/InvSqrt.pdf
/// -# http://www.google.de/search?hl=de&q=0x5f3759df&meta=
/// .
/// @pre f_value has to be positive!
//-------------------------------------------------------------------------
inline float32_t fast_rsqrt(float32_t f_value);

//-------------------------------------------------------------------------
/// @dspecref{1193}
/// @param f_value  the floating point value from which the fast approximation of reciprocal square root is calculated
/// @return a fast approximation of reciprocal square root of given float64 argument
/// @f$ y \simeq \frac{1}{\sqrt{x}} @f$, only works with IEEE 754 floating point standard (64bit).
/// See
/// -# // http://en.wikipedia.org/wiki/Fast_inverse_square_root
/// .
/// @pre f_value has to be positive!
//-------------------------------------------------------------------------
#if !defined(VFC_NO_INT64) && (defined(VFC_ENABLE_FLOAT64) || defined(VFC_ENABLE_QM_MATH))
inline float64_t fast_rsqrt(float64_t f_value);
#endif

//-------------------------------------------------------------------------
/// @dspecref{1194}
/// @tparam ValueType  the type used for square root calculation
/// @param f_value  the value of which the square root is calculated
/// @return the square root of given value of type "ValueType" @f$ y= \sqrt{x} @f$.
/// @pre f_value has to be (exact) zero or positive
//-------------------------------------------------------------------------
template <class ValueType>
inline typename TSqrtTraits<ValueType>::ArgType sqrt(ValueType f_value);

//-------------------------------------------------------------------------
/// @tparam ValueType  the type used for square root calculation
/// @param  f_value  the value of which the square root is calculated
/// @param - unnamed parameter  used as function switch
/// @return the square root of given value of type float32_t
//-------------------------------------------------------------------------
template <class ValueType>
inline typename TSqrtTraits<ValueType>::ArgType internSqrt(ValueType f_value, vfc::false_t);

//-------------------------------------------------------------------------
/// @dspec{1881}     The SW component "vfc" shall calculate and return the value of the
///                  expoential function to the base e (Euler's number)  for a given exponent value.
/// @param f_exponent  value used as exponent
/// @return value of the exponential function to the basis e (euler constant)
/// for the given exponent.
///
/// \see ISO/IEC 14882 26.8 C library, math functions
/// \see http://en.cppreference.com/w/cpp/numeric/math/exp
//-------------------------------------------------------------------------
inline float32_t exp(float32_t f_exponent);

//-------------------------------------------------------------------------
/// @dspec{1882}     The SW component "vfc" shall calculate and return the value of the
///                  exponential function to the base 2 for a given exponent value.
/// @param f_exponent  value used as exponent
/// @return value of 2 to the power of a given value f_exponent.
///
/// \see ISO/IEC 14882 26.8 C library, math functions
/// \see http://en.cppreference.com/w/cpp/numeric/math/exp2
//-------------------------------------------------------------------------
inline float32_t exp2(float32_t f_exponent);

//-------------------------------------------------------------------------
/// @dspec{1883}     The SW component "vfc" shall calculate and return the remainder
///                  of the the division of a given divident and a given divisor such
///                  that the result has the same sign as the divident and such that the
///                  result has a smaller magnitude than the divisor.
/// @param f_dividend  the dividend
/// @param f_divisor  the divisor
/// @return the remainder of the division of f_dividend by f_divisor,
/// where the result has the same sign as f_dividend but a smaller
/// magnitude than f_divisor.
///
/// \see ISO/IEC 14882 26.8 C library, math functions
/// \see http://en.cppreference.com/w/cpp/numeric/math/fmod
//-------------------------------------------------------------------------
inline float32_t fmod(float32_t f_dividend, float32_t f_divisor);

//-------------------------------------------------------------------------
/// @dspec{1226}     The SW component "vfc" shall calculate and return the value of
///                  the exponentiation of a given base value to the power of a given
///                  exponent value.
/// @param f_base  the base of the power function
/// @param f_exponent  the exponent of the power function
/// @return base value raised to the power of exponent, float32_t overload
/// \see ISO/IEC 14882 26.8 C library, math functions
/// \see http://en.cppreference.com/w/cpp/numeric/math/pow
//-------------------------------------------------------------------------
inline float32_t pow(float32_t f_base, float32_t f_exponent);

//-------------------------------------------------------------------------
/// @dspec{1225}     The SW component "vfc" shall calculate and return the natural
///                  logarithm of a given value.
/// @param f_value  the value from which the natural logarithm is calculated
/// @return the natural logarithm (base e) of given value.
/// @pre f_value has to be positive
//-------------------------------------------------------------------------
inline float32_t log(float32_t f_value);

//-------------------------------------------------------------------------
/// @dspec{1880}     The SW component "vfc" shall calculate and return the logarithm
///                  to the base 2 of a given value.
/// @param f_value  the value from which the base-2 logarithm is calculated
/// @return the base-2 logarithm of specified value.
/// @pre f_value has to be positive
//-------------------------------------------------------------------------
inline float32_t log2(float32_t f_value);

//-------------------------------------------------------------------------
/// @dspec{1224}     The SW component "vfc" shall calculate and return the logarithm to the
///                  base 10 of a given value.
/// @param f_value  the value from which the base-10 logarithm is calculated
/// @return the base-10 logarithm of specified value.
/// @pre f_value has to be positive
//-------------------------------------------------------------------------
inline float32_t log10(float32_t f_value);

//-------------------------------------------------------------------------
/// @dspec{1223}     The SW component "vfc" shall calculate and return the cubic root of a
///                  given value.
/// @param f_value  the value from which the cubic root is calculated
/// @return the cubic root of given value.
//-------------------------------------------------------------------------
inline float32_t curt(float32_t f_value);

//-------------------------------------------------------------------------
/// @dspec{1873}     The SW componenz "vfc" shall calculate and return the value of the (Gauss)
///                  error function for a given value.
/// @param f_value  the input parameter of the error function
/// @return the value of the error function at the given position
/// \see https://en.wikipedia.org/wiki/Error_function
//-------------------------------------------------------------------------
inline float32_t erf(float32_t f_value);

//-------------------------------------------------------------------------
/// signum function.
/// @dspec{1222}     The SW component "vfc" shall calculate and return the value of the signum
///                  function for a given value.
/// @tparam T  the type used in the signum function
/// @param f_value  the input parameter of the signum function
/// @return -1 for negative values, 1 for positive and zero if specified value is zero.
/// @f[  y= \left \{ \begin{array}{rl} -1 & ,x<0 \\ 0 & ,x=0 \\ 1 & ,x>0 \\ \end{array} \right \}  @f]
//-------------------------------------------------------------------------
template <class T>
inline int32_t signum(const T& f_value);

//-------------------------------------------------------------------------
/// heaviside function.
/// @dspec{1221}     The SW component "vfc" shall calculate and return the value of the heaviside
///                  step function for a given value.
/// @tparam T  the type used in the heaviside function
/// @param f_value the input parameter of the heaviside function
/// @return 0.0 for negative values, 1.0 for positive and 0.5 if value is zero.
/// @f[  y= \left \{ \begin{array}{cl} 0.0 & ,x<0 \\ 0.5 & ,x=0 \\ 1.0 & ,x>0 \\ \end{array} \right \}  @f]
//-------------------------------------------------------------------------
template <class T>
inline float32_t heaviside(const T& f_value);

//-------------------------------------------------------------------------
/// @dspec{1520}     The SW component "vfc" shall calculate and return the squared value
///                  of a given value.
/// @tparam T the type used in square root
/// @param f_value input parameter square root
/// returns given argument squared @f$ y = x^2 @f$.
//-------------------------------------------------------------------------
template <class T>
inline T sqr(const T& f_value);

//=============================================================================
// numeric casts
//=============================================================================

//-------------------------------------------------------------------------
/// casts a arithmetic value to a specified type, rounds to nearest integer
/// if necessary.
/// @dspec{1220}     The SW component "vfc" shall convert a given numeric value to the
///                  value of a given numeric type representing the same value and return
///                  the converted value.
/// @par usage:
/// @code
/// @tparam ReturnType  the return type of the numeric_cast
/// @tparam ArgumentType  the argument type of the numeric_cast
/// @param f_value  input parameter for the numeric_cast
/// @return the casted input value
/// int32_t val_i32 = numeric_cast<int32_t>(3.789);
/// @endcode
//-------------------------------------------------------------------------
template <class ReturnType, class ArgumentType>
ReturnType numeric_cast(const ArgumentType& f_value);

//=============================================================================
// alignment
//=============================================================================

//-------------------------------------------------------------------------
/// @dspec{1219}     The SW component "vfc" shall check if the given value is a power of
///                  2 and return the boolean value "true" if it is a power of two, else it
///                  shall return the boolean value "false".
/// @param f_value  the value used in the check for power of 2
/// @return true if specified value is power of two.
//-------------------------------------------------------------------------
inline bool isPow2(size_t f_value);

//-------------------------------------------------------------------------
/// aligns value upwards the nearest multiple of alignment, alignment has
/// to be a power-of-two.
/// @dspec{1218}     The SW component "vfc" shall find the smallest value larger or equal
///                  than the given start value and divisible by the given alignment value
///                  which has to be a power of 2 and return the found value.
/// @param f_value  the value which is aligned upwards
/// @param f_alignment  the alignment value
/// @return the aligned input value
//-------------------------------------------------------------------------
inline size_t alignUp(size_t f_value, size_t f_alignment);

//-------------------------------------------------------------------------
/// aligns value downwards the nearest multiple of alignment, alignment has
/// to be a power-of-two.
/// @dspec{1217}     The SW component "vfc" shall find the largest value smaller or equal
///                  than the given start value and divisible by the given alignment value
///                  which has to be a power of 2 and return the found value.
/// @param f_value  the value which is aligned downwards
/// @param f_alignment  the alignment value
/// @return the aligned input value
//-------------------------------------------------------------------------
inline size_t alignDown(size_t f_value, size_t f_alignment);

//=============================================================================
// float64_t overloads
//=============================================================================
#ifdef VFC_ENABLE_FLOAT64_TYPE
//-------------------------------------------------------------------------
/// clamps specified value of float64 to zero as lower bound
/// @param f_value  the input value to be clamped
/// @return @f[  y= \left \{ \begin{array}{cl} 0 & ,x<0 \\ x & ,x>=0  \\ \end{array} \right \}  @f]
//-------------------------------------------------------------------------
inline float64_t clampNegValueToZero(float64_t f_value);

//-------------------------------------------------------------------------
/// rounds floating point value to nearest integer.
/// The rounding method is round-to-nearest-even for the optimized path
/// ( {cw,vc}-win32-x86 ) and round-half-up for the stdc fallback.
/// To avoid overflow and underflow the input value should be in the range:
/// [-2147483648.0, 2147483647.0]
/// @param f_value  the value to be rounded to the nearest integer
/// @return the rounded input value
/// @note  We will provide both rounding methods with dedicated functions
/// in an upcoming release.
//-------------------------------------------------------------------------
inline int32_t nint(float64_t f_value);

//-------------------------------------------------------------------------
/// rounds 64 bit floating point value to nearest integer.
/// To avoid overflow and underflow the input value should be in the range:
/// [-9223372036854775808.0, 9223372036854774784.0]
/// @param f_value  the value to be rounded to the nearest integer
/// @return the rounded input value
//-------------------------------------------------------------------------
inline int64_t nint64(float64_t f_value);

//-------------------------------------------------------------------------
/// returns the square root of given value of type float64_t
//-------------------------------------------------------------------------
template <class ValueType>
inline typename TSqrtTraits<ValueType>::ArgType internSqrt(ValueType f_value, vfc::true_t);
#endif // VFC_ENABLE_FLOAT64_TYPE

#ifdef VFC_ENABLE_QM_MATH
// Non qualified functions for ASIL moved to separate namespace
namespace only_qm
{
//-------------------------------------------------------------------------
/// \see ISO/IEC 14882 26.8 C library, math functions
/// \see http://en.cppreference.com/w/cpp/numeric/math/pow
/// @param f_base  the base value for the power function
/// @param f_exponent  the exponent value for the power function
/// @return base value raised to the power of exponent, float64_t overload
//-------------------------------------------------------------------------
inline float64_t pow(float64_t f_base, float64_t f_exponent);

//-------------------------------------------------------------------------
/// @param f_value  the value of which the natural logarithm is calculated
/// @return the natural logarithm (base e) of given value.
/// @pre f_value has to be positive
//-------------------------------------------------------------------------
inline float64_t log(float64_t f_value);

//-------------------------------------------------------------------------
/// @param f_value  the value of which the base-10 logarithm is calculated
/// @return the base-10 logarithm of specified value.
/// @pre f_value has to be positive
//-------------------------------------------------------------------------
inline float64_t log10(float64_t f_value);

//-------------------------------------------------------------------------
/// @param f_value  the value of which the cubic root is calculated
/// @return the cubic root of given value.
//-------------------------------------------------------------------------
inline float64_t curt(float64_t f_value);

//-------------------------------------------------------------------------
/// @param f_value  the input parameter of the error function
/// @return the value of the error function at the given position
/// \see https://en.wikipedia.org/wiki/Error_function
//-------------------------------------------------------------------------
inline float64_t erf(float64_t f_value);
} // namespace only_qm
#endif // VFC_ENABLE_QM_MATH

// To remain compatible with the VFC_ENABLE_FLOAT64 flag
#ifdef VFC_ENABLE_FLOAT64

using vfc::only_qm::pow;

using vfc::only_qm::log;

using vfc::only_qm::log10;

using vfc::only_qm::curt;

using vfc::only_qm::erf;

#endif // VFC_ENABLE_FLOAT64

//=============================================================================
//  DOXYGEN DEFGROUP vfc_group_core_algorithms_math END
//-------------------------------------------------------------------------
/// @}
//=============================================================================

} // namespace vfc

#include "vfc/core/vfc_math.inl"

#endif // ZX_VFC_MATH_HPP_INCLUDED

