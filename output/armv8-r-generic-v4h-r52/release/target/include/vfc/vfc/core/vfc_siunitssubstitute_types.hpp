//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_SIUNITS_TYPES_HPP_INCLUDED
#define VFC_SIUNITS_TYPES_HPP_INCLUDED

#include "vfc/core/vfc_siunits.hpp"

namespace vfc
{
struct CSubstituteInfoType
{
    using self_unit_type = CSubstituteInfoType;
    using base_unit_type = CSubstituteInfoType;

    struct CSubstitute
    {
    };

    using CDummyUnitType = vfc::TSIType<CSubstitute>;

    enum
    {
        SI_UNIT = 0
    };

    enum
    {
        SI_FUNDAMENTAL = 1
    };

    using length_unit_type            = CDummyUnitType;
    using mass_unit_type              = CDummyUnitType;
    using time_unit_type              = CDummyUnitType;
    using current_unit_type           = CDummyUnitType;
    using temperature_unit_type       = CDummyUnitType;
    using amountofsubstance_unit_type = CDummyUnitType;
    using luminousintensity_unit_type = CDummyUnitType;
    using angle_unit_type             = CDummyUnitType;
    using unit_type                   = vfc::CBasicType;

    static const vfc::int32_t LENGTH_PREFIX_VALUE = vfc::BASE;
    static const vfc::int32_t LENGTH_POWER_VALUE  = 0;

    static const vfc::int32_t MASS_PREFIX_VALUE = vfc::BASE;
    static const vfc::int32_t MASS_POWER_VALUE  = 0;

    static const vfc::int32_t TIME_PREFIX_VALUE = vfc::BASE;
    static const vfc::int32_t TIME_POWER_VALUE  = 0;

    static const vfc::int32_t CURRENT_PREFIX_VALUE = vfc::BASE;
    static const vfc::int32_t CURRENT_POWER_VALUE  = 0;

    static const vfc::int32_t TEMPERATURE_PREFIX_VALUE = vfc::BASE;
    static const vfc::int32_t TEMPERATURE_POWER_VALUE  = 0;

    static const vfc::int32_t AMOUNTOFSUBSTANCE_PREFIX_VALUE = vfc::BASE;
    static const vfc::int32_t AMOUNTOFSUBSTANCE_POWER_VALUE  = 0;

    static const vfc::int32_t LUMINOUSINTENSITY_PREFIX_VALUE = vfc::BASE;
    static const vfc::int32_t LUMINOUSINTENSITY_POWER_VALUE  = 0;

    static const vfc::int32_t ANGLE_PREFIX_VALUE = vfc::BASE;
    static const vfc::int32_t ANGLE_POWER_VALUE  = 0;
};

struct SSubstituteRBInfoType
{

    using self_unit_type = SSubstituteRBInfoType;
    using base_unit_type = SSubstituteRBInfoType;

    struct CSubstitutePixel
    {
    };

    struct CSubstitutePercentage
    {
    };

    using CDummyUnitPixelType      = vfc::TSIType<CSubstitutePixel>;
    using CDummyUnitPercentageType = vfc::TSIType<CSubstitutePercentage>;

    enum
    {
        SI_UNIT = 0
    };

    enum
    {
        SI_FUNDAMENTAL = 1
    };

    using pixel_unit_type      = CDummyUnitPixelType;
    using percentage_unit_type = CDummyUnitPercentageType;
    using unit_type            = vfc::CBasicType;

    static const vfc::int32_t PIXEL_PREFIX_VALUE      = vfc::BASE;
    static const vfc::int32_t PIXEL_POWER_VALUE       = 0;
    static const vfc::int32_t PERCENTAGE_PREFIX_VALUE = vfc::BASE;
    static const vfc::int32_t PERCENTAGE_POWER_VALUE  = 0;
};

template <>
struct TSIUnitMultiplicationPromote<CSubstituteInfoType, CSubstituteInfoType>
{
    using unit_info_type = CSubstituteInfoType;
};

template <>
struct TSIUnitRBMultiplicationPromote<SSubstituteRBInfoType, SSubstituteRBInfoType>
{
    using unit_info_type = SSubstituteRBInfoType;
};

/// @tparam ValueType The base data type of the si unit type
/// @tparam UserType Defines a specific UserType SIUnit
/// @tparam CnvertPolicyType Defines the convert policy type
template <
    class ValueType,
    class UserType                                         = vfc::CDefaultType,
    template <class, class, class> class ConvertPolicyType = vfc::TNoConvertConvert>
struct TSIUnitType
{
    ///////////////////////////////////////////////
    ///////////////// length /////////////////////
    ///////////////////////////////////////////////

    // power = 1

    using si_metre_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_kilo_metre_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_mega_metre_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_giga_metre_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_tera_metre_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_deci_metre_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_centi_metre_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_milli_metre_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_micro_metre_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_nano_metre_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    // power = 2

    using si_square_metre_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_square_kilo_metre_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_cubic_metre_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_cubic_kilo_metre_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_metre_per_cubic_second_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_metre_second_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_cubic_deci_metre_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_litre_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    ///////////////////////////////////////////////
    ///////////////// time /////////////////////
    ///////////////////////////////////////////////

    // power = 1

    using si_second_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_minute_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_hour_t = vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_milli_second_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_micro_second_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    // 1 / s
    using si_per_second_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    //  1 / hr
    using si_per_hour_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    //  1 / s2
    using si_per_square_second_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    //  1 / hr2
    using si_per_square_hour_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_day_t = vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_week_t = vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_month_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_year_t = vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;
    ///////////////////////////////////////////////
    ///////////////// Degree /////////////////////
    ///////////////////////////////////////////////

    using si_degree_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_degree_per_second_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_radian_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    ///////////////////////////////////////////////
    ///////////////// Mass /////////////////////
    ///////////////////////////////////////////////

    using si_gram_t = vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_kilo_gram_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    ///////////////////////////////////////////////
    ///////////////// temperature /////////////////////
    ///////////////////////////////////////////////

    //  temperature based
    using si_kelvin_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    ///////////////////////////////////////////////
    ///////////////// ampere /////////////////////
    ///////////////////////////////////////////////

    using si_ampere_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_kilo_ampere_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_mega_ampere_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_giga_ampere_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_tera_ampere_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_deci_ampere_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_centi_ampere_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_milli_ampere_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_micro_ampere_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_nano_ampere_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    // 1 / A
    using si_per_ampere_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    ///////////////////////////////////////////////
    ///////////////// candela /////////////////////
    ///////////////////////////////////////////////

    using si_candela_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_kilo_candela_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_mega_candela_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_giga_candela_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_tera_candela_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_deci_candela_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_centi_candela_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_milli_candela_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_micro_candela_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_nano_candela_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    // 1 / cd
    using si_per_candela_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    ///////////////////////////////////////////////////////////////////////////
    //////////////////// Commonly used types //////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////

    // Hz
    using si_hertz_t = si_per_second_t;

    using si_joule_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_watt_t = vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_kilo_watt_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_bar_t = vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_milli_bar_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_force_of_gravity_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_lux_t = vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_kilo_gram_metre_per_square_second_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    // newton metre
    using si_newton_metre_t = si_joule_t;

    // Newton = mass * acceleration
    // Newton = Kg * m/s2
    using si_newton_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    // KiloNewton  = Kilo*Kilo gram*metre/s2
    // KiloNewton  = Mega gram*metre/s2
    // KiloNewton  = Mg * m/s2

    using si_kilo_newton_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    //  velocity
    using si_metre_per_second_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_kilometre_per_hour_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    //  acceleration
    using si_metre_per_square_second_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    using si_kilometre_per_square_hour_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    // wave number
    using si_wavenumber_t =
        vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;

    // nil type
    using si_nil_t = vfc::TSIUnits<ValueType, CSubstituteInfoType, SSubstituteRBInfoType, UserType, ConvertPolicyType>;
};

/// @tparam UserType Defines a specific UserType SIUnit
/// @tparam CnvertPolicyType Defines the convert policy type
template <
    class UserType                                         = vfc::CDefaultType,
    template <class, class, class> class ConvertPolicyType = vfc::TNoConvertConvert>
class TSIUnitType_i32 : public TSIUnitType<vfc::int32_t, UserType, ConvertPolicyType>
{
};

/// @tparam UserType Defines a specific UserType SIUnit
/// @tparam CnvertPolicyType Defines the convert policy type
template <
    class UserType                                         = vfc::CDefaultType,
    template <class, class, class> class ConvertPolicyType = vfc::TNoConvertConvert>
class TSIUnitType_f32 : public TSIUnitType<vfc::float32_t, UserType, ConvertPolicyType>
{
};

#ifdef VFC_ENABLE_FLOAT64_TYPE
/// @tparam UserType Defines a specific UserType SIUnit
/// @tparam CnvertPolicyType Defines the convert policy type
template <
    class UserType                                         = vfc::CDefaultType,
    template <class, class, class> class ConvertPolicyType = vfc::TNoConvertConvert>
class TSIUnitType_f64 : public TSIUnitType<vfc::float64_t, UserType, ConvertPolicyType>
{
};
#endif // VFC_ENABLE_FLOAT64_TYPE

} // namespace vfc

#endif // VFC_SIUNITSSUBSTITUTE_TYPES_HPP_INCLUDED

//=============================================================================

