//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_HASH_HPP_INCLUDED
#define VFC_HASH_HPP_INCLUDED

#include "vfc/core/vfc_types.hpp"
#include "vfc/core/vfc_metaprog.hpp"

//=============================================================================
//  VFC_HASH_SPECIAL_IMPL
//-----------------------------------------------------------------------------
/// helper macro for defining a THash<> specialized class.
//=============================================================================
// qacpp-1030-R1: Parentheses not possible for compiler-specific attributes or syntax
#define VFC_HASH_SPECIAL_IMPL(TYPE)                                                                                    \
    template <>                                                                                                        \
    struct THash<TYPE> /* PRQA S 1030 # R1 */                                                                          \
    {                                                                                                                  \
        inline hash_type operator()(const TYPE& f_val) const { return vfc::intern::hashValue(f_val); }                 \
    } // PRQA S 1030 # R1

namespace vfc
{

//=========================================================================
//  DOXYGEN DEFGROUP vfc_group_core_algorithms_hash BEGIN
//-------------------------------------------------------------------------
/// @defgroup vfc_group_core_algorithms_hash Hash functions
/// @ingroup vfc_group_core_algorithms
/// @brief hashing algorithms.
/// see
//    Methods for Identifying Versioned and Plagiarised Documents (2003)
//        by Timothy C. Hoad , Justin Zobel , Timothy C. Hoad Justin Zobel
//        Venue: Journal of the American Society for Information Science and Technology
//
//    M.V. Ramakrishna and J. Zobel. Performance in practice of string hashing functions. In
//        Proc. Int. Conf. on Database Systems for Advanced Applications, pages 215-223, Melbourne,
//        Australia, April 1997.
/// @{
//-------------------------------------------------------------------------

//=========================================================================
// hash_type
//-------------------------------------------------------------------------
/// Declaration of the hash type. The implementation does only work with
/// a 32-bit hash type.
//=========================================================================
using hash_type = vfc::uint32_t;

//=========================================================================
// serialHash
//-------------------------------------------------------------------------
/// Declaration of the serialHash function.
/// This function computes contiguous hash values by taking the previous
/// hash value as seed in the first argument, and the object, for which the
/// next hash shall be computed, as second argument. Accumulate the previous
/// hash value with the new one.
/// @dspec{436}     The SW component "vfc" shall compute a serial hash value for a given input hash and given
///                 value of a given type and return the updated hash value.
/// @tparam ValueType       the type of the object used for computing hash value
/// @param f_prevhash       The previous hash value to accumulate with the next hash
/// @param f_nextObjToHash  The next object for hash computing
//=========================================================================
template <class ValueType>
void serialHash(vfc::hash_type& f_prevhash, const ValueType& f_nextObjToHash);

namespace intern
{

// Declare the hash_value function overloads for different types:

template <typename ValueType>
inline typename vfc::TEnableIf<
    vfc::TIsSameType<vfc::uint8_t, ValueType>::value || vfc::TIsSameType<vfc::int8_t, ValueType>::value ||
        vfc::TIsSameType<vfc::uint16_t, ValueType>::value || vfc::TIsSameType<vfc::int16_t, ValueType>::value ||
        vfc::TIsSameType<vfc::uint32_t, ValueType>::value || vfc::TIsSameType<vfc::int32_t, ValueType>::value,
    vfc::hash_type>::type
hashValue(ValueType f_val)
{
    return static_cast<vfc::hash_type>(f_val);
}

template <typename ValueType>
inline typename vfc::TEnableIf<vfc::TIsSameType<bool, ValueType>::value, vfc::hash_type>::type
hashValue(ValueType f_val)
{
    return static_cast<vfc::hash_type>(f_val);
}

inline vfc::hash_type hash64Bit(const vfc::uint64_t& f_val)
{
    return (
        hashValue(static_cast<vfc::hash_type>(f_val & 0xFFFFFFFFUL)) ^
        hashValue(static_cast<vfc::hash_type>(f_val >> 32)));
}

inline vfc::hash_type hash64Bit(const vfc::int64_t& f_val)
{
    return (
        hashValue(static_cast<vfc::hash_type>(static_cast<vfc::int64_t>(
            f_val & 0xFFFFFFFFUL))) // PRQA S 3003 # Technical debt L2 id=00c12c081fda10844b2c-eb9c8348365a849b
        ^ hashValue(static_cast<vfc::hash_type>(static_cast<vfc::int64_t>(
              f_val >> 32)))); // PRQA S 3003 # Technical debt L2 id=00c1afc126abcabd4beb-107f606f76e8eb7b
}

#ifdef VFC_HAS_LONG_LONG
template <typename ValueType>
inline typename vfc::TEnableIf<
    vfc::TIsSameType<vfc::uint64_t, ValueType>::value || vfc::TIsSameType<vfc::int64_t, ValueType>::value,
    vfc::hash_type>::type
hashValue(ValueType f_val)
{
    return hash64Bit(f_val);
}
#endif

template <typename ValueType>
inline typename vfc::TEnableIf<vfc::TIsSameType<vfc::float32_t, ValueType>::value, vfc::hash_type>::type
hashValue(ValueType f_val)
{
    return *static_cast<vfc::hash_type*>(
        static_cast<void*>(&f_val)); // PRQA S 3038 # Technical debt L2 id=00c1c5ef903623fe48af-14f9c3fe5dd37b2d
}

#ifdef VFC_ENABLE_FLOAT64_TYPE
template <typename ValueType>
inline typename vfc::TEnableIf<vfc::TIsSameType<vfc::float64_t, ValueType>::value, vfc::hash_type>::type
hashValue(ValueType f_val)
{
    const vfc::uint64_t l_rawbits = *static_cast<vfc::uint64_t*>(
        static_cast<void*>(&f_val)); // PRQA S 3038 # Technical debt L2 id=00c18e248df6394a4d1a-0327570ace07ed38
    return hash64Bit(l_rawbits);
}
#endif // VFC_ENABLE_FLOAT64_TYPE

//=========================================================================
// TGenericContainerHash<>
//-------------------------------------------------------------------------
/// Generic declaration of the hash function object, which iterates over
/// all items of a container.
//=========================================================================
template <class ValueType>
struct TGenericContainerHash
{
    // qacpp-2628-R1: Constructor cannot be replaced with '=default' due to an assertion in the body and QACPP
    // cannot be configured to analyze assertions. Note: This rationale needs an update by the ruleset. Using
    // `=default` for this constructor results in a compilation error for our D3 target stating that the class
    // "has no user-provided default constructor". Rationale (from
    // https://ofekshilon.com/2020/03/14/c-const-constructability/): A class type T is
    // const-default-constructible if default-initialization of T would invoke a user-provided constructor of T
    // (not inherited from a base class) or ... Leaving an empty constructor is enough, as the compiler does not
    // try to check whether the user provided ctor actually does everything it should. However, using `=default`
    // raises the error.
    TGenericContainerHash() {} // PRQA S 2628 # R1
    vfc::hash_type operator()(const ValueType& f_val) const;
};

} // namespace intern

//=========================================================================
// THash<>
//-------------------------------------------------------------------------
/// @struct vfc::THash<>
/// @tparam ValueType  type of the object used to compute hash value
/// Generic declaration of the hash function object.
///
/// Example of a user struct and the corresponding THash<> specialization:
///
/// \code
/// struct CUserdata
/// {
///     vfc::uint32_t                   m_member1;
///     vfc::TCArray<CTestdata2, 10>    m_member2;
///     vfc::uint32_t                   m_member3;
/// };
///
/// //! CUserdata hasher
/// template<>
/// struct THash<CUserdata>
/// {
///     using local_hash_type = CUserdata;
///
///     vfc::hash_type operator()(const local_hash_type& f_val) const
///     {
///         vfc::hash_type l_seed = 0;
///         serialHash(l_seed, f_val.m_member1);
///         serialHash(l_seed, f_val.m_member2);
///         serialHash(l_seed, f_val.m_member3);
///         return l_seed;
///     }
/// };
/// \endcode
//=========================================================================
template <typename ValueType>
struct THash
{
    /// My own hash object type.
    using local_hash_type = ValueType;

    // round bracket operator used to return hash value of given argument
    /// @param  f_val  object used for computing the hash value
    /// @return the calculated hash value
    inline vfc::hash_type operator()(const local_hash_type& f_val) const { return vfc::intern::hashValue(f_val); }
};

//=========================================================================
//  THash<> specializations
//-------------------------------------------------------------------------
/// @brief Create the specializations for each basic type.
//=========================================================================
VFC_HASH_SPECIAL_IMPL(bool);
VFC_HASH_SPECIAL_IMPL(vfc::uint8_t);
VFC_HASH_SPECIAL_IMPL(vfc::int8_t);
VFC_HASH_SPECIAL_IMPL(vfc::uint16_t);
VFC_HASH_SPECIAL_IMPL(vfc::int16_t);
VFC_HASH_SPECIAL_IMPL(vfc::uint32_t);
VFC_HASH_SPECIAL_IMPL(vfc::int32_t);
#ifdef VFC_HAS_LONG_LONG
VFC_HASH_SPECIAL_IMPL(vfc::uint64_t);
VFC_HASH_SPECIAL_IMPL(vfc::int64_t);
#endif
VFC_HASH_SPECIAL_IMPL(vfc::float32_t);
#ifdef VFC_ENABLE_FLOAT64_TYPE
VFC_HASH_SPECIAL_IMPL(vfc::float64_t);
#endif // VFC_ENABLE_FLOAT64_TYPE

//=========================================================================
//  DOXYGEN DEFGROUP vfc_group_core_algorithms_hash END
//-------------------------------------------------------------------------
/// @}
//-------------------------------------------------------------------------

} // namespace vfc

#include "vfc/core/vfc_hash.inl"

#endif // VFC_HASH_HPP_INCLUDED

//=============================================================================

