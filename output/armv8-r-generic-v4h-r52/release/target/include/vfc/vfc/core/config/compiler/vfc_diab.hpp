//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_DIAB_HPP_INCLUDED
#define VFC_DIAB_HPP_INCLUDED

#include "vfc/core/vfc_preprocessor.hpp"

//////////////////////////////
// versions check
//////////////////////////////

// we don't support diab prior to version 5:
#if (__VERSION_NUMBER__ < 5000)
static_assert(false, "Compiler not supported or configured");
#endif
//
//    last known and checked version is 5.8.0:
#if (__VERSION_NUMBER__ > 5800)
static_assert(false, "Unknown compiler version");
#endif

///////////////////////////////
//  Diab C++ compiler setup
///////////////////////////////

// use predefined macros,
// see wr_compiler_users_guide_ppc.pdf section 6.1

#define VFC_COMPILER_STRING VFC_JOIN("Diab version ", VFC_STRINGIZE(__VERSION__))

#if defined(__ppc)
#define VFC_EPPC_DETECTED
#endif

#ifndef VFC_COMPILER_DIAB
#define VFC_COMPILER_DIAB
#endif

//////////////////////////////
// exception handling
//////////////////////////////

#ifndef __EXCEPTIONS
#define VFC_NO_EXCEPTIONS
#endif

////////////////////////////////////
// RTTI support
////////////////////////////////////

#ifndef _RTTI
#define VFC_NO_RTTI
#endif

//////////////////////////////
// NO cstdint support!
//////////////////////////////

#ifdef VFC_HAS_STDINT_H
#undef VFC_HAS_STDINT_H
#endif

//////////////////////////////
// endianess
//////////////////////////////

#if !defined(__LITTLE_ENDIAN__)
#define VFC_BIG_ENDIAN
#endif

//////////////////////////////
// long long support
//////////////////////////////

// checked for x86 and PPC so far
#if (defined(__ppc) || defined(__i386))
#define VFC_HAS_LONG_LONG
#endif

////////////////////////////////////
// processor identification
////////////////////////////////////

#if defined(__ppc)
#define VFC_PROCESSOR_PPC
#else
static_assert(false, "processor not supported");
#endif

// macro abstraction for linker statements
#define VFC_LINKER_SECT1 __attribute__
#define VFC_LINKER_SECT(sect_name) VFC_JOIN(VFC_LINKER_SECT1, ((used, section##(sect_name)##)))

#ifndef VFC_DECL_FORCEINLINE
#define VFC_DECL_FORCEINLINE inline
#endif

#endif // VFC_DIAB_HPP_INCLUDED

//=============================================================================

