//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_VISUALC_HPP_INCLUDED
#define VFC_VISUALC_HPP_INCLUDED

#include <sal.h> // _Check_return_
#include "vfc/core/vfc_preprocessor.hpp"

//////////////////////////////
// versions check
//////////////////////////////

// Overview of Visual C++ versions and their MSC_VER definition
//
// Visual C++ 4.x          MSC_VER=1000
// Visual C++ 5            MSC_VER=1100
// Visual C++ 6            MSC_VER=1200
// Visual C++ .NET         MSC_VER=1300
// Visual C++ .NET 2003    MSC_VER=1310
// Visual C++ 2005 (8.0)   MSC_VER=1400
// Visual C++ 2008 (9.0)   MSC_VER=1500
// Visual C++ 2010 (10.0)  MSC_VER=1600
// Visual C++ 2012 (11.0)  MSC_VER=1700
// Visual C++ 2013 (12.0)  MSC_VER=1800
// Visual C++ 2015 (14.0)  MSC_VER=1900
// Visual C++ 2017 (14.10) MSC_VER=1910
// Visual C++ 2019 (14.20) MSC_VER=1920

// we don't support Visual C++ prior to version 14.0 (1900):
#if (_MSC_VER < 1900)
static_assert(false, "Compiler not supported or configured");
#endif
// we don't support Visual C++ newer as version 14.30 (1930)
#if (_MSC_VER > 1930)
static_assert(false, "Unknown compiler version");
#endif

////////////////////////////////////
//  Visual C++ compiler setup
////////////////////////////////////

#define VFC_COMPILER_VERSION _MSC_VER

#define VFC_COMPILER_STRING VFC_JOIN("Microsoft Visual C++ version ", VFC_STRINGIZE(VFC_COMPILER_VERSION))

#ifndef VFC_COMPILER_VISUALC
#define VFC_COMPILER_VISUALC
#endif

////////////////////////////////////
// multi/single threaded target
////////////////////////////////////

#ifdef _MT
#define VFC_MT
#endif

////////////////////////////////////
// RTTI support
////////////////////////////////////

#ifndef _CPPRTTI
#define VFC_NO_RTTI
#endif

////////////////////////////////////
// exception support
////////////////////////////////////

#ifndef _CPPUNWIND
#define VFC_NO_EXCEPTIONS
#endif

//////////////////////////////
// long long / __int64 support
//////////////////////////////

// all compiler vc >= 1200 support __int64
#define VFC_HAS_MS_INT64

// all compiler vc >= 1310 support long long
#if defined(_MSC_EXTENSIONS)
#define VFC_HAS_LONG_LONG
#endif

//////////////////////////////
// cstdint support
//////////////////////////////

// VC has no stdint.h support prior to ver 10.0
#if (_MSC_VER >= 1600)
#define VFC_HAS_STDINT_H
#endif

//////////////////////////////
// endianess
//////////////////////////////

// supports only x86 = little endian targets

////////////////////////////////////
// force inline
////////////////////////////////////

#ifndef VFC_DECL_FORCEINLINE
#define VFC_DECL_FORCEINLINE __forceinline
#endif

////////////////////////////////////
// noinline request
////////////////////////////////////

#ifndef VFC_ATR_NOINLINE_REQUEST
#define VFC_ATR_NOINLINE_REQUEST __declspec(noinline)
#endif

//////////////////////////////
// microsoft specific stuff
//////////////////////////////

// disable Win32 API's if compiler extensions are
// turned off:
#ifndef _MSC_EXTENSIONS
#define VFC_DISABLE_WIN32
#endif

#define VFC_MSVC _MSC_VER

///////////////////////////////////////////
// support for CLR / C++/CLI / managed code
///////////////////////////////////////////

// cf. http://en.wikipedia.org/wiki/Common_Language_Runtime
// and cf. http://en.wikipedia.org/wiki/C%2B%2B/CLI
// If CLR usage is enabled, native code generation (__asm) should not
// (or even may not) be used, as it cannot be represented in CIL (MSIL).
// Below expression is according to:
// http://msdn2.microsoft.com/en-us/library/468x0ea1(VS.80).aspx

#if defined(_MANAGED) || defined(_M_CEE)
#define VFC_CLR_DETECTED
#endif

////////////////////////////////////
// processor identification
////////////////////////////////////

#ifdef _M_IX86
#define VFC_PROCESSOR_IX86
#elif _M_X64
#define VFC_PROCESSOR_X64
#else
static_assert(false, "processor not supported");
#endif

// macro abstraction for linker statements
#define VFC_LINKER_SECT1 __pragma
#define VFC_LINKER_SECT(sect_name)                                                                                     \
    VFC_JOIN(                                                                                                          \
        VFC_LINKER_SECT1,                                                                                              \
        (section##(sect_name)##)) // PRQA S 1110 # Technical debt L4 id=00c1364742a7fc2a44bf-28546bca494c49d0 // PRQA S
                                  // 0024 # Technical debt L8 id=00c1c3975b3a3d434703-d9b11fc3ba5ad88e

#define VFC_TYPE_ALIGNMENT_PREFIX(val) __declspec(align(val))
#define VFC_TYPE_ALIGNMENT_SUFFIX(val)

#ifndef VFC_ATR_DEPRECATED
#if (_MSC_VER > 1916)
#define VFC_ATR_DEPRECATED(EXP) [[deprecated]] EXP
#endif
#endif

#ifndef VFC_ATR_DEPRECATED2
#if (_MSC_VER > 1916)
#define VFC_ATR_DEPRECATED2(EXP, MSG) [[deprecated(MSG)]] EXP
#endif
#endif
#ifndef VFC_ATR_POST_DEPRECATED
#if (_MSC_VER > 1916)
#define VFC_ATR_POST_DEPRECATED(EXP) EXP [[deprecated]]
#endif
#endif

#ifndef VFC_ATR_POST_DEPRECATED2
#if (_MSC_VER > 1916)
#define VFC_ATR_POST_DEPRECATED2(EXP, MSG) EXP [[deprecated(MSG)]]
#endif
#endif

#if !defined(VFC_ATR_DEPRECATED_ENUM_VALUE) && !defined(__QAS_CANTATA__)
#if (_MSC_VER > 1916)
#define VFC_ATR_DEPRECATED_ENUM_VALUE(EXP) VFC_ATR_POST_DEPRECATED(EXP)
#endif
#endif

#if !defined(VFC_ATR_DEPRECATED_ENUM_VALUE2) && !defined(__QAS_CANTATA__)
#if (_MSC_VER > 1916)
#define VFC_ATR_DEPRECATED_ENUM_VALUE2(EXP, MSG) VFC_ATR_POST_DEPRECATED2(EXP, MSG)
#endif
#endif

#ifndef VFC_ATR_NODISCARD
#define VFC_ATR_NODISCARD _Check_return_
#endif

#endif // VFC_VISUALC_HPP_INCLUDED

//=============================================================================

