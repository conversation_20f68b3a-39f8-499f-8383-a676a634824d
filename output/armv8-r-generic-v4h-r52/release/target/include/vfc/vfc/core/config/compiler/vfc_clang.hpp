//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_CLANG_HPP_INCLUDED
#define VFC_CLANG_HPP_INCLUDED

#include <cstddef>
#include "vfc/core/vfc_preprocessor.hpp"

////////////////////////////////////
// processor identification
////////////////////////////////////

#if defined(__amd64__)
#define VFC_PROCESSOR_IX86_64
#elif defined(__aarch64__)
#define VFC_ARM64_DETECTED
#define VFC_PROCESSOR_ARM64
#else
static_assert(false, "processor not supported");
#endif

//////////////////////////////
// versions check
//////////////////////////////

// helper definition for readable version numbers, see
// https://gcc.gnu.org/onlinedocs/cpp/Common-Predefined-Macros.html
#define VFC_CLANG_VERSION (__clang_major__ * 10000 + __clang_minor__ * 100 + __clang_patchlevel__)

// we don't know clang prior to version 3.5.0:
#if VFC_CLANG_VERSION < 30500
static_assert(false, "Compiler not supported or configured");
#endif
//
//    last known and checked version is 10.0.0:
#if VFC_CLANG_VERSION > 100000
//static_assert(false, "Unknown compiler version");//ndk use clang with version > 100000
#endif

///////////////////////////////
//  CLANG C++ compiler setup
///////////////////////////////

#define VFC_COMPILER_STRING "Clang version " VFC_STRINGIZE(__clang_major__)

#ifndef VFC_COMPILER_CLANG
#define VFC_COMPILER_CLANG
#endif

///////////////////////////////
//  C++ version setup
///////////////////////////////

// C++14 is supported by Clang >=3.4 see http://clang.llvm.org/cxx_status.html
#if (__cplusplus >= 201402L) && (VFC_CLANG_VERSION >= 30400)
#define VFC_USE_CPP14_FEATURES
#endif

//////////////////////////////
// exception handling
//////////////////////////////

#if !__has_feature(cxx_exceptions)
#define VFC_NO_EXCEPTIONS
#endif

//////////////////////////////
// stdint.h support
//////////////////////////////

#ifdef _GLIBCXX_HAVE_STDINT_H
#define VFC_HAS_STDINT_H
#endif

//////////////////////////////
// long long support
//////////////////////////////

#ifdef _GLIBCXX_USE_LONG_LONG
#define VFC_HAS_LONG_LONG
#endif

//dedicated for android ndk clang
#ifndef _LIBCPP_HAS_NO_LONG_LONG
#define VFC_HAS_LONG_LONG
#endif
#ifndef _LIBCPP_HAS_NO_STDINT
#define VFC_HAS_STDINT_H
#endif

//////////////////////////////
// endianess setup
//////////////////////////////

#ifdef VFC_EPPC_DETECTED
#define VFC_BIG_ENDIAN
#endif

// macro abstraction for linker statements
#define VFC_LINKER_SECT(sect_name) __attribute__((section(sect_name)))

#ifndef VFC_DECL_FORCEINLINE
#define VFC_DECL_FORCEINLINE inline __attribute__((always_inline))
#endif

#ifndef VFC_ATR_NOINLINE_REQUEST
#define VFC_ATR_NOINLINE_REQUEST __attribute__((noinline))
#endif

#define VFC_TYPE_ALIGNMENT_PREFIX(val)
#define VFC_TYPE_ALIGNMENT_SUFFIX(val) __attribute__((aligned(val)))

#ifndef VFC_ATR_DEPRECATED
#ifdef VFC_USE_CPP17_FEATURES
#define VFC_ATR_DEPRECATED(EXP) [[deprecated]] EXP
#else
#define VFC_ATR_DEPRECATED(EXP) __attribute((deprecated)) EXP
#endif
#endif

#ifndef VFC_ATR_DEPRECATED2
#ifdef VFC_USE_CPP17_FEATURES
#define VFC_ATR_DEPRECATED2(EXP, MSG) [[deprecated(MSG)]] EXP
#else
#define VFC_ATR_DEPRECATED2(EXP, MSG) __attribute((deprecated(MSG))) EXP
#endif
#endif

#ifndef VFC_ATR_POST_DEPRECATED
#ifdef VFC_USE_CPP17_FEATURES
#define VFC_ATR_POST_DEPRECATED(EXP) EXP [[deprecated]]
#else
#define VFC_ATR_POST_DEPRECATED(EXP) EXP __attribute((deprecated))
#endif
#endif

#ifndef VFC_ATR_POST_DEPRECATED2
#ifdef VFC_USE_CPP17_FEATURES
#define VFC_ATR_POST_DEPRECATED2(EXP, MSG) EXP [[deprecated(MSG)]]
#else
#define VFC_ATR_POST_DEPRECATED2(EXP, MSG) EXP __attribute((deprecated(MSG)))
#endif
#endif

#ifndef VFC_ATR_DEPRECATED_ENUM_VALUE
#define VFC_ATR_DEPRECATED_ENUM_VALUE(EXP) VFC_ATR_POST_DEPRECATED(EXP)
#endif

#ifndef VFC_ATR_DEPRECATED_ENUM_VALUE2
#define VFC_ATR_DEPRECATED_ENUM_VALUE2(EXP, MSG) VFC_ATR_POST_DEPRECATED2(EXP, MSG)
#endif

#ifndef VFC_ATR_NODISCARD
#define VFC_ATR_NODISCARD __attribute__((warn_unused_result))
#endif

#endif // VFC_CLANG_HPP_INCLUDED

//=============================================================================

