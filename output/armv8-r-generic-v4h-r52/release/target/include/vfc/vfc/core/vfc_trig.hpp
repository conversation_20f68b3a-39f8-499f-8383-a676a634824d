//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_TRIG_HPP_INCLUDED
#define VFC_TRIG_HPP_INCLUDED

#include <math.h> // used for trig funcs // PRQA S 1014 # Technical debt L4 id=00c10ff82f66354a4608-64a8d6b0b2f80808
#include "vfc/core/vfc_static_assert.hpp" // used for VFC_STATIC_ASSERT()
#include "vfc/core/vfc_types.hpp"         // used for fundamental types
#include "vfc/core/vfc_type_traits.hpp"   // used for type traits
#include "vfc/core/vfc_math.hpp"          // used for G_DEG2RAD and G_RAD2DEG constants, isEqual()
#include "vfc/core/vfc_util.hpp"          // used for nop

#include VFC_INCLUDE_INTERN_MATH_IMPL

namespace vfc
{ // namespace vfc opened

///////////////////////////////////////////////////////////////////////////
// based on ideas for safe radian/degree utilization
// from CR/AEM4-Jaeger
///////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////
// some forward declarations
///////////////////////////////////////////////////////////////////////////

template <class FloatType>
class TRadian;

template <class FloatType>
class TDegree;

//=========================================================================
// TRadian
//-------------------------------------------------------------------------
/// Type-Safe angle representation in radians.
/// @tparam FloatType   float type for member and interfaces
/// @sa CDegree
/// @ingroup vfc_group_core_types
//=========================================================================

template <class FloatType>
class TRadian
{
  public:
    VFC_STATIC_ASSERT((TIsFloating<FloatType>::value));

    // enable access to underlying type in algorithms
    using value_type = FloatType;

    /// c'tor, takes angle in radians
    /// @dspec{811}     The SW component "vfc" shall construct a value of a given angle
    ///                 unit type with a given scalar value.
    /// @param f_angleInRadians initial angle value of FloatType
    explicit constexpr TRadian(value_type f_angleInRadians = typedZero<value_type>()) : m_value(f_angleInRadians) {}

    /// implicit conversion from degree to radian angle
    /// @dspec{810}     The SW component "vfc" shall create a new instance for a given
    ///                 angle unit type representing the same angle as a given value of
    ///                 given angle unit instance of a different angle unit type.
    /// @param angle source angle given in TDegree<FloatType>
    TRadian(const TDegree<value_type>& angle);

    /// @dspec{1326}     The SW component "vfc" shall return a scalar value of a given angle
    ///                  unit type.
    /// @returns angle value in radians
    constexpr value_type value(void) const { return m_value; }
    /// add-assign operator
    /// @dspec{1325}     The SW component "vfc" shall add two given angle unit values
    ///                  and store the result in the first given value.
    /// @param rhs TRadian instance that will be added to this
    /// @return reference to modified TRadian
    TRadian& operator+=(const TRadian& rhs)
    {
        static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TRadian::operator+=");
        m_value += rhs.m_value;
        return *this;
    }
    /// subtract-assign operator
    /// @dspec{1324}     The SW Component "vfc" shall subtract the second given angle
    ///                  unit value from the first given unit value and store the result
    ///                  in the first given value
    /// @param rhs TRadian instance that will be subtracted from this
    /// @return reference to modified TRadian
    TRadian& operator-=(const TRadian& rhs)
    {
        static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TRadian::operator-=");
        m_value -= rhs.m_value;
        return *this;
    }
    /// multiply-assign operator
    /// @dspec{1323}     The SW component "vfc" shall multiply a given angle unit type
    ///                  instance with a given scalar value and store the resulting value
    ///                  in the given instance.
    /// @param f_rhs value_type that will be multiplied with this
    /// @return reference to modified TRadian
    TRadian& operator*=(value_type f_rhs)
    {
        static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TRadian::operator*=");
        m_value *= f_rhs;
        return *this;
    }

    /// Divide-assign operator
    /// @dspec{1322}     The SW component "vfc" shall divide a given angle unit type instance
    ///                  by a given scalar value and store the resulting value in the given
    ///                  instance.
    /// @param[in] f_rhs    value_type that will be divide the value of this
    /// @pre                @p f_rhs must not be zero
    /// @return             reference to modified TRadian
    TRadian& operator/=(value_type f_rhs)
    {
        static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TRadian::operator/=");
        VFC_REQUIRE(0.0f != f_rhs); // PRQA S 3270 # Technical debt L2 id=00c1e9a2f9db171041df-672ad69cbe5c7107
        m_value /= f_rhs;
        return *this;
    }

    /// extend to a type with higher precision
    /// @dspec{1848}     The SW component "vfc" shall extend the precision of the value
    ///                  of a given angle unit type instance and return the resulting angle
    ///                  unit type instance.
    /// @tparam OtherFloatType Floating point type of target TRadian type
    /// @return TRadian extended to float type of higher precision
    template <class OtherFloatType>
    TRadian<OtherFloatType> extend_to() const
    {
        VFC_STATIC_ASSERT((TIsFloating<OtherFloatType>::value));
        VFC_STATIC_ASSERT(sizeof(OtherFloatType) > sizeof(value_type));
        static_assert(TIsValidArithmetic<OtherFloatType>::value, "invalid arithmetic type used for TRadian::extend_to");
        return TRadian<OtherFloatType>(numeric_cast<OtherFloatType>(value()));
    }

    /// truncate to a type with lower precision
    /// @dspec{1849}     The SW component "vfc" shall reduce the precision of the vaule
    ///                  of a given angle unit type instance and return the resulting angle
    ///                  unit type instance.
    /// @tparam OtherFloatType Floating point type of target TRadian type
    /// @return TRadian truncated to float type of lower precision
    template <class OtherFloatType>
    TRadian<OtherFloatType> truncate_to() const
    {
        VFC_STATIC_ASSERT((TIsFloating<OtherFloatType>::value));
        VFC_STATIC_ASSERT(sizeof(value_type) > sizeof(OtherFloatType));
        static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TRadian::truncate_to");
        return TRadian<OtherFloatType>(numeric_cast<OtherFloatType>(value()));
    }

  private:
    value_type m_value;
};

using CRadian32 = TRadian<float32_t>;
/// type definition for backward compatibility/transition
#if defined(VFC_ENABLE_FLOAT64) || defined(VFC_ENABLE_QM_MATH)
using CRadian   = TRadian<float64_t>;
using CRadian64 = TRadian<float64_t>;
#elif defined(VFC_ENABLE_FLOAT64_TYPE)
using CRadian   = TRadian<float32_t>;
using CRadian64 = TRadian<float64_t>;
#else
using CRadian = TRadian<float32_t>;
#endif

// traits specialization
template <>
struct TIsPOD<CRadian32> : true_t
{
};
template <>
struct TIsFloating<CRadian32> : true_t
{
};
template <>
struct TIsValidFloating<CRadian32> : true_t
{
};

#ifdef VFC_ENABLE_FLOAT64_TYPE
template <>
struct TIsPOD<CRadian64> : true_t
{
};
template <>
struct TIsFloating<CRadian64> : true_t
{
};
template <>
struct TIsValidFloating<CRadian64> : true_t
{
};
#endif

/// @dspec{1914}     The SW component "vfc" shall compute the difference (lhs-rhs) of the
///                  given angle unit type instance lhs and the given angle unit type instance
///                  rhs and return the resulting angle unit type instance.
/// @tparam FloatType   FloatType of both TRadian instances in expression
/// @param lhs          TRadian value on the left-hand-side of expression
/// @param rhs          TRadian value on the right-hand-side of expression
/// @returns angle difference in rad. @relatesalso CRadian
template <class FloatType>
inline const TRadian<FloatType> operator-(const TRadian<FloatType>& lhs, const TRadian<FloatType>& rhs)
{
    return TRadian<FloatType>(lhs).operator-=(rhs);
}

/// @dspec{1915}     The SW component "vfc" shall compute the sum (lhs+rhs) of the given angle
///                  unit type instance lhs and the given angle unit type instance rhs and
///                  return the resulting angle unit type instance.
/// @tparam FloatType   FloatType of both TRadian instances in expression
/// @param lhs          TRadian value on the left-hand-side of expression
/// @param rhs          TRadian value on the right-hand-side of expression
/// @returns angle sum in rad. @relatesalso TRadian
template <class FloatType>
inline const TRadian<FloatType> operator+(const TRadian<FloatType>& lhs, const TRadian<FloatType>& rhs)
{
    return TRadian<FloatType>(lhs).operator+=(rhs);
}

/// @dspec{1916}     The SW component "vfc" shall compute the product of the given angle
///                  unit type instance and a given scalar value and return the resulting
///                  angle unit type instance.
/// @tparam FloatType   FloatType of TRadian instance and scalar in expression
/// @param lhs          TRadian value on the left-hand-side of expression
/// @param f_rhs        FloatType value on the right-hand-side of expression
/// @returns angle multiplies by given factor. @relatesalso TRadian
template <class FloatType>
inline const TRadian<FloatType> operator*(const TRadian<FloatType>& lhs, FloatType f_rhs)
{
    return TRadian<FloatType>(lhs).operator*=(f_rhs);
}

/// @dspecref{1916}
/// @tparam FloatType   FloatType of TRadian instance and scalar in expression
/// @param f_lhs        FloatType value on the left-hand-side of expression
/// @param rhs          TRadian value on the right-hand-side of expression
/// @returns angle multiplies by given factor. @relatesalso TRadian
template <class FloatType>
inline const TRadian<FloatType> operator*(FloatType f_lhs, const TRadian<FloatType>& rhs)
{
    return TRadian<FloatType>(rhs).operator*=(f_lhs);
}

/// @dspec{1917}     The SW component "vfc" shall compute the quotient (lhs/rhs) of the
///                  given angle unit type instance lhs and the scalar value rhs and return
///                  the resulting angle unit type instance.
/// @tparam FloatType   FloatType of TRadian instance and scalar in expression.
/// @param[in] lhs      TRadian value on the left-hand-side of expression.
/// @param[in] f_rhs    FloatType value on the right-hand-side of expression.
/// @pre                @p f_rhs must not be zero.
/// @returns            Angle divided by denom.
/// @relatesalso        TRadian
template <class FloatType>
inline const TRadian<FloatType> operator/(const TRadian<FloatType>& lhs, FloatType f_rhs)
{
    VFC_REQUIRE(0.0f != f_rhs); // PRQA S 3270 # Technical debt L2 id=00c121862341fee04cb8-672ad69cbe5c7107
    return TRadian<FloatType>(lhs).operator/=(f_rhs);
}

/// unary neg operator. @relatesalso TRadian
/// @dspec{1847}     The SW component "vfc" shall negate a given angle unit type instance
///                  and return the resulting angle unit type instance.
/// @tparam FloatType   FloatType of TRadian instance in expression
/// @param angle_rad    TRadian value on the left-hand-side of expression
/// @return             negated TRadian value
template <class FloatType>
inline constexpr const TRadian<FloatType> operator-(const TRadian<FloatType>& angle_rad)
{
    static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TRadian::operator-");
    return TRadian<FloatType>(-angle_rad.value());
}

/// overload of isEqual for TRadian type
/// @dspec{1321}     The SW component "vfc" shall return the boolean value "true" if the
///                  values of two given angle unit instances are equivalent otherwise it
///                  shall return the boolean value "false".
/// @tparam FloatType   FloatType of TRadian instances to be compared
/// @param lhs          TRadian value on the left-hand-side of comparison
/// @param rhs          TRadian value on the right-hand-side of comparison
/// @return true if the values are equal, else return false
template <class FloatType>
inline bool isEqual(const TRadian<FloatType>& lhs, const TRadian<FloatType>& rhs)
{
    return isEqual(lhs.value(), rhs.value());
}

/// overload of notEqual for TRadian type
/// @dspec{1320}     The SW component "vfc" shall return the boolean value "true" if the
///                  values of two given angle unit instances are NOT equivalent otherwise
///                  it shall return the boolean value "false".
/// @tparam FloatType   FloatType of TRadian instances to be compared
/// @param lhs          TRadian value on the left-hand-side of comparison
/// @param rhs          TRadian value on the right-hand-side of comparison
/// @return true if the values are not equal, else return false
template <class FloatType>
inline bool notEqual(const TRadian<FloatType>& lhs, const TRadian<FloatType>& rhs)
{
    return notEqual(lhs.value(), rhs.value());
}

// comparison operators

/// comparison operator for equality. @relatesalso TRadian
/// @dspecref{1321}
/// @tparam FloatType   FloatType of TRadian instances in comparison expression
/// @param lhs          TRadian value on the left-hand-side of expression
/// @param rhs          TRadian value on the right-hand-side of expression
/// @return true if the values are equal, else return false
template <class FloatType>
inline bool operator==(const TRadian<FloatType>& lhs, const TRadian<FloatType>& rhs)
{
    return isEqual(lhs.value(), rhs.value());
}

/// comparison operator for inequality. @relatesalso TRadian
/// @dspecref{1320}
/// @tparam FloatType   FloatType of TRadian instances in comparison expression
/// @param lhs          TRadian value on the left-hand-side of expression
/// @param rhs          TRadian value on the right-hand-side of expression
/// @return true if the values are not equal, else return false
template <class FloatType>
inline bool operator!=(const TRadian<FloatType>& lhs, const TRadian<FloatType>& rhs)
{
    return !(lhs == rhs);
}

/// comparison operator for less. @relatesalso TRadian
/// @dspec{1319}     The SW component "vfc" shall compare two given angle unit instances
///                  and check if the value of the first given instance is smaller than,
///                  larger than, smaller or equal, or larger or equal to the value of the
///                  other given instance and return a boolean value that signifies if the
///                  checked relation is true.
/// @tparam FloatType   FloatType of TRadian instances in comparison expression
/// @param lhs          TRadian value on the left-hand-side of expression
/// @param rhs          TRadian value on the right-hand-side of expression
/// @return true if the value on the left-hand-side is smaller than
///         the value on the right-hand-side, else return false
template <class FloatType>
inline constexpr bool operator<(const TRadian<FloatType>& lhs, const TRadian<FloatType>& rhs)
{
    static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TRadian::operator<");
    return lhs.value() < rhs.value();
}

/// comparison operator for greater. @relatesalso TRadian
/// @dspecref{1319}
/// @tparam FloatType   FloatType of TRadian instances in comparison expression
/// @param lhs          TRadian value on the left-hand-side of expression
/// @param rhs          TRadian value on the right-hand-side of expression
/// @return true if the value on the left-hand-side is greater than
///         the value on the right-hand-side, else return false
template <class FloatType>
inline constexpr bool operator>(const TRadian<FloatType>& lhs, const TRadian<FloatType>& rhs)
{
    static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TRadian::operator>");
    return lhs.value() > rhs.value();
}

/// comparison operator for less-equal. @relatesalso TRadian
/// @dspecref{1319}
/// @tparam FloatType   FloatType of TRadian instances in comparison expression
/// @param lhs          TRadian value on the left-hand-side of expression
/// @param rhs          TRadian value on the right-hand-side of expression
/// @return true if the value on the left-hand-side is smaller-or-equal than
///         the value on the right-hand-side, else return false
template <class FloatType>
inline constexpr bool operator<=(const TRadian<FloatType>& lhs, const TRadian<FloatType>& rhs)
{
    static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TRadian::operator<=");
    return lhs.value() <= rhs.value();
}

/// comparison operator for greater-equal. @relatesalso TRadian
/// @dspecref{1319}
/// @tparam FloatType   FloatType of TRadian instances in comparison expression
/// @param lhs          TRadian value on the left-hand-side of expression
/// @param rhs          TRadian value on the right-hand-side of expression
/// @return true if the value on the left-hand-side is greater-or-equal than
///         the value on the right-hand-side, else return false
template <class FloatType>
inline constexpr bool operator>=(const TRadian<FloatType>& lhs, const TRadian<FloatType>& rhs)
{
    static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TRadian::operator>=");
    return lhs.value() >= rhs.value();
}

//=========================================================================
// TDegree
//-------------------------------------------------------------------------
/// Type-Safe angle representation in degrees.
/// @tparam FloatType   float type for member and interfaces
/// @sa TRadian
/// @ingroup vfc_group_core_types
//=========================================================================

template <class FloatType>
class TDegree
{
  public:
    VFC_STATIC_ASSERT((TIsFloating<FloatType>::value));

    // enable access to underlying type in algorithms
    using value_type = FloatType;

    /// c'tor, takes angle in degrees
    /// @dspecref{811}
    /// @param f_angleInDegrees initial angle value of FloatType
    explicit constexpr TDegree(value_type f_angleInDegrees = typedZero<value_type>()) : m_value(f_angleInDegrees) {}

    /// implicit conversion from radian to degree angle
    /// @dspecref{810}
    /// @param angle source angle given in TRadian<FloatType>
    TDegree(const TRadian<value_type>& angle);

    /// @dspecref{1326}
    /// @returns angle value in degrees
    constexpr value_type value(void) const { return m_value; }
    /// add-assign operator
    /// @dspecref{1325}
    /// @param rhs TDegree instance that will be added to this
    /// @return reference to modified TDegree
    TDegree& operator+=(const TDegree& rhs)
    {
        static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TDegree::operator+=");
        m_value += rhs.m_value;
        return *this;
    }
    /// subtract-assign operator
    /// @dspecref{1324}
    /// @param rhs TDegree instance that will be subtracted from this
    /// @return reference to modified TDegree
    TDegree& operator-=(const TDegree& rhs)
    {
        static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TDegree::operator-=");
        m_value -= rhs.m_value;
        return *this;
    }
    /// multiply-assign operator
    /// @dspecref{1323}
    /// @param f_rhs value_type that will be multiplied with this
    /// @return reference to modified TDegree
    TDegree& operator*=(value_type f_rhs)
    {
        static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TDegree::operator*=");
        m_value *= f_rhs;
        return *this;
    }

    /// Divide-assign operator
    /// @dspecref{1322}
    /// @param[in] f_rhs    value_type that will be divide the value of this.
    /// @pre                @p f_rhs must not be zero.
    /// @return             reference to modified TDegree
    TDegree& operator/=(value_type f_rhs)
    {
        static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TDegree::operator/=");
        VFC_REQUIRE(0.0f != f_rhs); // PRQA S 3270 # Technical debt L2 id=00c1e808bb11f3b9465b-672ad69cbe5c7107
        m_value /= f_rhs;
        return *this;
    }

    /// extend to a type with higher precision
    /// @dspecref{1848}
    /// @tparam OtherFloatType Floating point type of target TDegree type
    /// @return TDegree extended to float type of higher precision
    template <class OtherFloatType>
    TDegree<OtherFloatType> extend_to() const
    {
        VFC_STATIC_ASSERT((TIsFloating<OtherFloatType>::value));
        VFC_STATIC_ASSERT(sizeof(OtherFloatType) > sizeof(value_type));
        static_assert(TIsValidArithmetic<OtherFloatType>::value, "invalid arithmetic type used for TDegree::extend_to");
        return TDegree<OtherFloatType>(numeric_cast<OtherFloatType>(value()));
    }

    /// truncate to a type with lower precision
    /// @dspecref{1849}
    /// @tparam OtherFloatType Floating point type of target TDegree type
    /// @return TDegree truncated to float type of lower precision
    template <class OtherFloatType>
    TDegree<OtherFloatType> truncate_to() const
    {
        VFC_STATIC_ASSERT((TIsFloating<OtherFloatType>::value));
        VFC_STATIC_ASSERT(sizeof(value_type) > sizeof(OtherFloatType));
        static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TDegree::truncate_to");
        return TDegree<OtherFloatType>(numeric_cast<OtherFloatType>(value()));
    }

  private:
    value_type m_value;
};

using CDegree32 = TDegree<float32_t>;
/// type definition for backward compatibility/transition
#if defined(VFC_ENABLE_FLOAT64) || defined(VFC_ENABLE_QM_MATH)
using CDegree   = TDegree<float64_t>;
using CDegree64 = TDegree<float64_t>;
#elif defined(VFC_ENABLE_FLOAT64_TYPE)
using CDegree   = TDegree<float32_t>;
using CDegree64 = TDegree<float64_t>;
#else
using CDegree = TDegree<float32_t>;
#endif

// conversion c'tor implementations
template <class FloatType>
inline TRadian<FloatType>::TRadian(const TDegree<FloatType>& angle)
    : m_value(
          angle.value() *
          typedDegree2Radian<FloatType>()) // PRQA S 2180 # Technical debt L2 id=00c10c7c7c319bb04573-090d0fb9d0f84c69
{
    static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TRadian::TRadian(TDegree)");
}

template <class FloatType>
inline TDegree<FloatType>::TDegree(const TRadian<FloatType>& angle)
    : m_value(
          angle.value() *
          typedRadian2Degree<FloatType>()) // PRQA S 2180 # Technical debt L2 id=00c1ec6c1e206fb4461d-8754ff9b3af85bc8
{
    static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TDegree::TDegree(TRadian)");
}

// traits specialization
template <>
struct TIsPOD<CDegree32> : true_t
{
};
template <>
struct TIsFloating<CDegree32> : true_t
{
};
template <>
struct TIsValidFloating<CDegree32> : true_t
{
};

#ifdef VFC_ENABLE_FLOAT64_TYPE
template <>
struct TIsPOD<CDegree64> : true_t
{
};
template <>
struct TIsFloating<CDegree64> : true_t
{
};
template <>
struct TIsValidFloating<CDegree64> : true_t
{
};
#endif

/// @dspecref{1914}
/// @tparam FloatType   FloatType of both TDegree instances in expression
/// @param lhs          TDegree value on the left-hand-side of expression
/// @param rhs          TDegree value on the right-hand-side of expression
/// @returns angle difference in degrees. @relatesalso TDegree
template <class FloatType>
inline const TDegree<FloatType> operator-(const TDegree<FloatType>& lhs, const TDegree<FloatType>& rhs)
{
    static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TDegree::operator-");
    return TDegree<FloatType>(lhs).operator-=(rhs);
}

/// @dspecref{1915}
/// @tparam FloatType   FloatType of both TDegree instances in expression
/// @param lhs          TDegree value on the left-hand-side of expression
/// @param rhs          TDegree value on the right-hand-side of expression
/// @returns angle sum in degrees. @relatesalso TDegree
template <class FloatType>
inline const TDegree<FloatType> operator+(const TDegree<FloatType>& lhs, const TDegree<FloatType>& rhs)
{
    return TDegree<FloatType>(lhs).operator+=(rhs);
}

/// @dspecref{1916}
/// @tparam FloatType   FloatType of TDegree instance and scalar in expression
/// @param lhs          TDegree value on the left-hand-side of expression
/// @param f_rhs        FloatType value on the right-hand-side of expression
/// @returns angle multiplies by given factor. @relatesalso TDegree
template <class FloatType>
inline const TDegree<FloatType> operator*(const TDegree<FloatType>& lhs, FloatType f_rhs)
{
    return TDegree<FloatType>(lhs).operator*=(f_rhs);
}

/// @dspecref{1916}
/// @tparam FloatType   FloatType of TDegree instance and scalar in expression
/// @param f_lhs        FloatType value on the left-hand-side of expression
/// @param rhs          TDegree value on the right-hand-side of expression
/// @returns angle multiplies by given factor. @relatesalso TDegree
template <class FloatType>
inline const TDegree<FloatType> operator*(FloatType f_lhs, const TDegree<FloatType>& rhs)
{
    return TDegree<FloatType>(rhs).operator*=(f_lhs);
}

/// @dspecref{1917}
/// @tparam FloatType   FloatType of TDegree instance and scalar in expression.
/// @param[in] lhs      TDegree value on the left-hand-side of expression.
/// @param[in] f_rhs    FloatType value on the right-hand-side of expression.
/// @pre                @p f_rhs must not be zero.
/// @returns            Angle divided by denom.
/// @relatesalso        TDegree
template <class FloatType>
inline const TDegree<FloatType> operator/(const TDegree<FloatType>& lhs, FloatType f_rhs)
{
    VFC_REQUIRE(0.0f != f_rhs); // PRQA S 3270 # Technical debt L2 id=00c16b010f1ad5124c3f-672ad69cbe5c7107
    return TDegree<FloatType>(lhs).operator/=(f_rhs);
}

/// @dspecref{1847}
/// unary neg operator. @relatesalso TDegree
/// @tparam FloatType   FloatType of TDegree instance in expression
/// @param angle_rad    TDegree value on the left-hand-side of expression
/// @return             negated TDegree value
template <class FloatType>
inline constexpr const TDegree<FloatType> operator-(const TDegree<FloatType>& angle_rad)
{
    static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TDegree::operator-");
    return TDegree<FloatType>(-angle_rad.value());
}

/// overload of isEqual for TDegree type
/// @dspecref{1321}
/// @tparam FloatType   FloatType of TDegree instances to be compared
/// @param lhs          TDegree value on the left-hand-side of comparison
/// @param rhs          TDegree value on the right-hand-side of comparison
/// @return true if the values are equal, else return false
template <class FloatType>
inline bool isEqual(const TDegree<FloatType>& lhs, const TDegree<FloatType>& rhs)
{
    return isEqual(lhs.value(), rhs.value());
}

/// overload of notEqual for TDegree type
/// @dspecref{1320}
/// @tparam FloatType   FloatType of TDegree instances to be compared
/// @param lhs          TDegree value on the left-hand-side of comparison
/// @param rhs          TDegree value on the right-hand-side of comparison
/// @return true if the values are not equal, else return false
template <class FloatType>
inline bool notEqual(const TDegree<FloatType>& lhs, const TDegree<FloatType>& rhs)
{
    return notEqual(lhs.value(), rhs.value());
}

/// comparison operator for equality
/// @dspecref{1321}
/// @tparam FloatType   FloatType of TDegree instances in comparison expression
/// @param lhs          TDegree value on the left-hand-side of expression
/// @param rhs          TDegree value on the right-hand-side of expression
/// @return true if the values are equal, else return false
template <class FloatType>
inline bool operator==(const TDegree<FloatType>& lhs, const TDegree<FloatType>& rhs)
{
    return isEqual(lhs.value(), rhs.value());
}

/// comparison operator for inequality. @relatesalso TDegree
/// @dspecref{1320}
/// @tparam FloatType   FloatType of TDegree instances in comparison expression
/// @param lhs          TDegree value on the left-hand-side of expression
/// @param rhs          TDegree value on the right-hand-side of expression
/// @return true if the values are not equal, else return false
template <class FloatType>
inline bool operator!=(const TDegree<FloatType>& lhs, const TDegree<FloatType>& rhs)
{
    return !(lhs == rhs);
}

/// comparison operator for less. @relatesalso TDegree
/// @dspecref{1319}
/// @tparam FloatType   FloatType of TDegree instances in comparison expression
/// @param lhs          TDegree value on the left-hand-side of expression
/// @param rhs          TDegree value on the right-hand-side of expression
/// @return true if the value on the left-hand-side is smaller than
///         the value on the right-hand-side, else return false
template <class FloatType>
inline constexpr bool operator<(const TDegree<FloatType>& lhs, const TDegree<FloatType>& rhs)
{
    static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TDegree::operator<");
    return lhs.value() < rhs.value();
}

/// comparison operator for greater. @relatesalso TDegree
/// @dspecref{1319}
/// @tparam FloatType   FloatType of TDegree instances in comparison expression
/// @param lhs          TDegree value on the left-hand-side of expression
/// @param rhs          TDegree value on the right-hand-side of expression
/// @return true if the value on the left-hand-side is greater than
///         the value on the right-hand-side, else return false
template <class FloatType>
inline constexpr bool operator>(const TDegree<FloatType>& lhs, const TDegree<FloatType>& rhs)
{
    static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TDegree::operator>");
    return lhs.value() > rhs.value();
}

/// comparison operator for less-equal. @relatesalso TDegree
/// @dspecref{1319}
/// @tparam FloatType   FloatType of TDegree instances in comparison expression
/// @param lhs          TDegree value on the left-hand-side of expression
/// @param rhs          TDegree value on the right-hand-side of expression
/// @return true if the value on the left-hand-side is smaller-or-equal than
///         the value on the right-hand-side, else return false
template <class FloatType>
inline constexpr bool operator<=(const TDegree<FloatType>& lhs, const TDegree<FloatType>& rhs)
{
    static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TDegree::operator<=");
    return lhs.value() <= rhs.value();
}

/// comparison operator for greater-equal. @relatesalso TDegree
/// @dspecref{1319}
/// @tparam FloatType   FloatType of TDegree instances in comparison expression
/// @param lhs          TDegree value on the left-hand-side of expression
/// @param rhs          TDegree value on the right-hand-side of expression
/// @return true if the value on the left-hand-side is greater-or-equal than
///         the value on the right-hand-side, else return false
template <class FloatType>
inline constexpr bool operator>=(const TDegree<FloatType>& lhs, const TDegree<FloatType>& rhs)
{
    static_assert(TIsValidArithmetic<FloatType>::value, "invalid arithmetic type used for TDegree::operator>=");
    return lhs.value() >= rhs.value();
}

///////////////////////////////////////////////////////////////////////////
// trig base templates and function wrappers for float32
///////////////////////////////////////////////////////////////////////////

/// sin function wrapper, float32. @relatesalso TRadian
/// @dspec{1318}     The SW component "vfc" shall calculate and return the sine function
///                  for a given angle unit value.
/// @param angle input angle value in CRadian32
/// @return sin function value of type float32_t
inline float32_t sin(const CRadian32& angle) { return vfc_intern_math_alias::sin_f(angle.value()); }

/// cos function wrapper, float32. @relatesalso TRadian
/// @dspec{1317}     The SW component "vfc" shall calculate and return the cosine function
///                  for a given angle unit value.
/// @param angle input angle value in CRadian32
/// @return cos function value of type float32_t
inline float32_t cos(const CRadian32& angle) { return vfc_intern_math_alias::cos_f(angle.value()); }

/// tan function wrapper, float32. @relatesalso TRadian
/// @dspec{1316}     The SW component "vfc" shall calculate and return the tangents function
///                  for a given angle unit value.
/// @param angle input angle value in CRadian32
/// @return tan function value of type float32_t
inline float32_t tan(const CRadian32& angle) { return vfc_intern_math_alias::tan_f(angle.value()); }

/// sinh function wrapper, float32. @relatesalso TRadian
/// @dspec{1315}     The SW component "vfc" shall calculate and return the hyperbolical
///                  sine function for a given angle unit value.
/// @param angle input angle value in CRadian32
/// @return sinh function value of type float32_t
inline float32_t sinh(const CRadian32& angle) { return vfc_intern_math_alias::sinh_f(angle.value()); }

/// cosh function wrapper, float32. @relatesalso TRadian
/// @dspec{1314}     The SW component "vfc" shall calculate and return the hyperbolical
///                  cosine function for a given angle unit value.
/// @param angle input angle value in CRadian32
/// @return cosh function value of type float32_t
inline float32_t cosh(const CRadian32& angle) { return vfc_intern_math_alias::cosh_f(angle.value()); }

/// tanh function wrapper, float32. @relatesalso TRadian
/// @dspec{1313}     The SW component "vfc" shall calculate and return the hyperbolical
///                  tangents function for a given angle unit value.
/// @param angle input angle value in CRadian32
/// @return tanh function value of type float32_t
inline float32_t tanh(const CRadian32& angle) { return vfc_intern_math_alias::tanh_f(angle.value()); }

namespace intern
{
/// Ensure the input value is in range.
/// @pre ValueType has to be valid arithmetic type.
/// @pre 'from' must be strictly smaller then 'to'
///
/// @tparam ValueType Type to be validated.
/// @param Value value to validate.
/// @param From value of lower bound to be checked, inclusive.
/// @param To value is upper bound to be checked, inclusive.
template <typename ValueType>
inline void validate_range(const ValueType& value, ValueType from, ValueType to)
{
    VFC_STATIC_ASSERT2(TIsValidArithmetic<ValueType>::value, "ValueType has to be valid arithmetic type.");
    VFC_REQUIRE2(
        (from < to),
        "Please ensure that the input value \'to\' is greater than the input value \'from\'."
        "Otherwise range check is invalid");
    VFC_REQUIRE2((from <= value && value <= to), "Please ensure that the input value is in range.");
    vfc::nop(value);
    vfc::nop(from);
    vfc::nop(to);
}
} // namespace intern

/// templated arcus sin function, must specify return type as template parameter
/// @dspec{1312}     The SW component "vfc" shall calculate the inverse sine function for a
///                  given value and return the result in the given angle unit type.
/// @pre The input parameter value has to be in range from -1 to 1.
/// @tparam AngleClass template-template parameter can be TRadian or TDegree
/// @tparam T float type used for AngleClass
/// @param value input value for arcus sin function
/// @return function value of arcus sin function of type AngleClass<T>
/// @note If the input value originates form an algebraical formula,
///  then it is possible that the numerical value exceeds the valid
///  range [-1,1] slightly which will cause this function to fail
///  with undefined behavior (usually a trap). In these cases it is
///  mandatory to clamp the value to the legal range.
template <template <typename> class AngleClass, typename T>
AngleClass<T> asin(const T& value);

template <>
inline CRadian32 asin<TRadian, float32_t>(const float32_t& value)
{
    intern::validate_range(value, -1.f, 1.f);
    return CRadian32(vfc_intern_math_alias::asin_f(value));
}

template <>
inline CDegree32 asin<TDegree, float32_t>(const float32_t& value)
{
    intern::validate_range(value, -1.f, 1.f);
    return CDegree32(asin<TRadian>(value));
}

/// templated arcus cos function, must specify return type as template parameter
/// @dspec{1311}     The SW component "vfc" shall calculate the inverse cosine function for a
///                  given value and return the result in the given angle unit type.
/// @pre The input parameter value has to be in range from -1 to 1.
/// @tparam AngleClass template-template parameter can be TRadian or TDegree
/// @tparam T float type used for AngleClass
/// @param value input value for arcus cos function
/// @return function value of arcus cos function of type AngleClass<T>
/// @note If the input value originates form an algebraical formula,
///  then it is possible that the numerical value exceeds the valid
///  range [-1,1] slightly which will cause this function to fail
///  with undefined behavior (usually a trap). In these cases it is
///  mandatory to clamp the value to the legal range.
template <template <typename> class AngleClass, typename T>
AngleClass<T> acos(const T& value);

template <>
inline CRadian32 acos<TRadian, float32_t>(const float32_t& value)
{
    intern::validate_range(value, -1.f, 1.f);
    return CRadian32(vfc_intern_math_alias::acos_f(value));
}

template <>
inline CDegree32 acos<TDegree, float32_t>(const float32_t& value)
{
    intern::validate_range(value, -1.f, 1.f);
    return CDegree32(acos<TRadian>(value));
}

/// templated arcus tangens function, must specify return type as template parameter
/// @dspec{1310}     The SW component "vfc" shall calculate the inverse tangents function for a
///                  given value and return the result in the given angle unit type.
/// @tparam AngleClass template-template parameter can be TRadian or TDegree
/// @tparam T float type used for AngleClass
/// @param value input value for arcus tangens function
/// @return function value of arcus tangens function of type AngleClass<T>
template <template <typename> class AngleClass, typename T>
AngleClass<T> atan(const T& value);

template <>
inline CRadian32 atan<TRadian, float32_t>(const float32_t& value)
{
    return CRadian32(vfc_intern_math_alias::atan_f(value));
}

template <>
inline CDegree32 atan<TDegree, float32_t>(const float32_t& value)
{
    return CDegree32(atan<TRadian>(value));
}

/// templated arcus tangens function with 2 parameters giving the correct quadrant
/// @dspec{1327}     The SW component "vfc" shall calculate the inverse tangents function for
///                  a x value and a y value and return the result in the given angle unit type.
/// @tparam AngleClass template-template parameter can be TRadian or TDegree
/// @tparam T float type used for AngleClass
/// @param y input value of y coordinate for arcus tangens function
/// @param x input value of x coordinate for arcus tangens function
/// @return function value of arcus tangens function of type AngleClass<T>
template <template <typename> class AngleClass, typename T>
AngleClass<T> atan2(const T& y, const T& x);

template <>
inline CRadian32 atan2<TRadian, float32_t>(const float32_t& y, const float32_t& x)
{
    return CRadian32(vfc_intern_math_alias::atan2_f(y, x));
}

template <>
inline CDegree32 atan2<TDegree, float32_t>(const float32_t& y, const float32_t& x)
{
    return CDegree32(atan2<TRadian>(y, x));
}

// To remain compatible with the VFC_ENABLE_FLOAT64 flag
#ifdef VFC_ENABLE_FLOAT64

/// sin function wrapper. @relatesalso TRadian
inline float64_t sin(const CRadian64& angle) { return vfc_intern_math_alias::sin_d(angle.value()); }

/// cos function wrapper. @relatesalso TRadian
inline float64_t cos(const CRadian64& angle) { return vfc_intern_math_alias::cos_d(angle.value()); }

/// tan function wrapper. @relatesalso TRadian
inline float64_t tan(const CRadian64& angle) { return vfc_intern_math_alias::tan_d(angle.value()); }

/// templated arcus sin function, must specify return type as template parameter
template <template <typename> class AngleClass, typename T = float64_t>
AngleClass<float64_t> asin(const float64_t& value);

template <>
inline CRadian64 asin<TRadian, float64_t>(const float64_t& value)
{
    intern::validate_range(value, -1.0, 1.0);
    return CRadian64(vfc_intern_math_alias::asin_d(value));
}

template <>
inline CDegree64 asin<TDegree, float64_t>(const float64_t& value)
{
    intern::validate_range(value, -1.0, 1.0);
    return CDegree64(asin<TRadian, float64_t>(value));
}

/// templated arcus cos function, must specify return type as template parameter
template <template <typename> class AngleClass, typename T = float64_t>
AngleClass<float64_t> acos(const float64_t& value);

template <>
inline CRadian64 acos<TRadian, float64_t>(const float64_t& value)
{
    intern::validate_range(value, -1.0, 1.0);
    return CRadian64(vfc_intern_math_alias::acos_d(value));
}

template <>
inline CDegree64 acos<TDegree, float64_t>(const float64_t& value)
{
    intern::validate_range(value, -1.0, 1.0);
    return CDegree64(acos<TRadian, float64_t>(value));
}

/// templated arcus tangens function, must specify return type as template parameter
template <template <typename> class AngleClass, typename T = float64_t>
AngleClass<float64_t> atan(const float64_t& value);

template <>
inline CRadian64 atan<TRadian, float64_t>(const float64_t& value)
{
    return CRadian64(vfc_intern_math_alias::atan_d(value));
}

template <>
inline CDegree64 atan<TDegree, float64_t>(const float64_t& value)
{
    return CDegree64(atan<TRadian, float64_t>(value));
}

/// templated arcus tangens function with 2 parameters giving the correct quadrant
template <template <typename> class AngleClass, typename T = float64_t>
AngleClass<float64_t> atan2(const float64_t& y, const float64_t& x);

template <>
inline CRadian64 atan2<TRadian, float64_t>(const float64_t& y, const float64_t& x)
{
    return CRadian64(vfc_intern_math_alias::atan2_d(y, x));
}

template <>
inline CDegree64 atan2<TDegree, float64_t>(const float64_t& y, const float64_t& x)
{
    return CDegree64(atan2<TRadian, float64_t>(y, x));
}
#endif

///////////////////////////////////////////////////////////////////////////
// trig function wrappers for float64
///////////////////////////////////////////////////////////////////////////
#if defined(VFC_ENABLE_QM_MATH) || defined(VFC_ENABLE_FLOAT64_TYPE)
// Non qualified functions for ASIL moved to separate namespace
namespace only_qm
{
#ifndef VFC_ENABLE_FLOAT64
/// sin function wrapper. @relatesalso TRadian
/// @param angle input angle value in CRadian64
/// @return sin function value of type float64_t
inline float64_t sin(const CRadian64& angle) { return vfc_intern_math_alias::sin_d(angle.value()); }

/// cos function wrapper. @relatesalso TRadian
/// @param angle input angle value in CRadian64
/// @return cos function value of type float64_t
inline float64_t cos(const CRadian64& angle) { return vfc_intern_math_alias::cos_d(angle.value()); }

/// tan function wrapper. @relatesalso TRadian
/// @param angle input angle value in CRadian64
/// @return tan function value of type float64_t
inline float64_t tan(const CRadian64& angle) { return vfc_intern_math_alias::tan_d(angle.value()); }

/// templated arcus sin function, must specify return type as template parameter
template <template <typename> class AngleClass, typename T = float64_t>
AngleClass<float64_t> asin(const float64_t& value);

template <>
inline CRadian64 asin<TRadian, float64_t>(const float64_t& value)
{
    intern::validate_range(value, -1.0, 1.0);
    return CRadian64(vfc_intern_math_alias::asin_d(value));
}

template <>
inline CDegree64 asin<TDegree, float64_t>(const float64_t& value)
{
    intern::validate_range(value, -1.0, 1.0);
    return CDegree64(asin<TRadian, float64_t>(value));
}

/// templated arcus cos function, must specify return type as template parameter
template <template <typename> class AngleClass, typename T = float64_t>
AngleClass<float64_t> acos(const float64_t& value);

template <>
inline CRadian64 acos<TRadian, float64_t>(const float64_t& value)
{
    intern::validate_range(value, -1.0, 1.0);
    return CRadian64(vfc_intern_math_alias::acos_d(value));
}

template <>
inline CDegree64 acos<TDegree, float64_t>(const float64_t& value)
{
    intern::validate_range(value, -1.0, 1.0);
    return CDegree64(acos<TRadian, float64_t>(value));
}

/// templated arcus tangens function, must specify return type as template parameter
template <template <typename> class AngleClass, typename T = float64_t>
AngleClass<float64_t> atan(const float64_t& value);

template <>
inline CRadian64 atan<TRadian, float64_t>(const float64_t& value)
{
    return CRadian64(vfc_intern_math_alias::atan_d(value));
}

template <>
inline CDegree64 atan<TDegree, float64_t>(const float64_t& value)
{
    return CDegree64(atan<TRadian, float64_t>(value));
}

/// templated arcus tangens function with 2 parameters giving the correct quadrant
template <template <typename> class AngleClass, typename T = float64_t>
AngleClass<float64_t> atan2(const float64_t& y, const float64_t& x);

template <>
inline CRadian64 atan2<TRadian, float64_t>(const float64_t& y, const float64_t& x)
{
    return CRadian64(vfc_intern_math_alias::atan2_d(y, x));
}

template <>
inline CDegree64 atan2<TDegree, float64_t>(const float64_t& y, const float64_t& x)
{
    return CDegree64(atan2<TRadian, float64_t>(y, x));
}
#else
using vfc::acos;
using vfc::asin;
using vfc::atan;
using vfc::atan2;
using vfc::cos;
using vfc::sin;
using vfc::tan;
#endif // !VFC_ENABLE_FLOAT64

/// sinh function wrapper. @relatesalso TRadian
/// @param angle input angle value in CRadian64
/// @return sinh function value of type float64_t
inline float64_t sinh(const CRadian64& angle) { return vfc_intern_math_alias::sinh_d(angle.value()); }

/// cosh function wrapper. @relatesalso TRadian
/// @param angle input angle value in CRadian64
/// @return cosh function value of type float64_t
inline float64_t cosh(const CRadian64& angle) { return vfc_intern_math_alias::cosh_d(angle.value()); }

/// tanh function wrapper. @relatesalso TRadian
/// @param angle input angle value in CRadian64
/// @return tanh function value of type float64_t
inline float64_t tanh(const CRadian64& angle) { return vfc_intern_math_alias::tanh_d(angle.value()); }
} // namespace only_qm
#endif // VFC_ENABLE_QM_MATH

// To remain compatible with the VFC_ENABLE_FLOAT64 flag
#ifdef VFC_ENABLE_FLOAT64
using vfc::only_qm::cosh;
using vfc::only_qm::sinh;
using vfc::only_qm::tanh;
#endif

#ifdef VFC_ENABLE_FLOAT64_TYPE
//
// full specializations of numeric_cast for changing precision
//

/// numeric_cast TDegree<float64_t> to TDegree<float32_t>
/// @param f_degree input angle value of type TDegree<float64_t>
/// @return converted value of type TDegree<float32_t>
template <>
inline TDegree<float32_t> numeric_cast<TDegree<float32_t>>(const TDegree<float64_t>& f_degree)
{
    return TDegree<float32_t>(numeric_cast<float32_t>(f_degree.value()));
}

/// numeric_cast TDegree<float32_t> to TDegree<float_64_t>
/// @param f_degree input angle value of type TDegree<float32_t>
/// @return converted value of type TDegree<float64_t>
template <>
inline TDegree<float64_t> numeric_cast<TDegree<float64_t>>(const TDegree<float32_t>& f_degree)
{
    return TDegree<float64_t>(numeric_cast<float64_t>(f_degree.value()));
}

/// numeric_cast TRadian<float64_t> to TRadian<float32_t>
/// @param f_degree input angle value of type TRadian<float64_t>
/// @return converted value of type TRadian<float32_t>
template <>
inline TRadian<float32_t> numeric_cast<TRadian<float32_t>>(const TRadian<float64_t>& f_radian)
{
    return TRadian<float32_t>(numeric_cast<float32_t>(f_radian.value()));
}

/// numeric_cast TRadian<float32_t> to TRadian<float_64_t>
/// @param f_degree input angle value of type TRadian<float32_t>
/// @return converted value of type TRadian<float64_t>
template <>
inline TRadian<float64_t> numeric_cast<TRadian<float64_t>>(const TRadian<float32_t>& f_radian)
{
    return TRadian<float64_t>(numeric_cast<float64_t>(f_radian.value()));
}

#endif // VFC_ENABLE_FLOAT64_TYPE

} // namespace vfc

// specializations of numeric_limits must happen in the std namespace
namespace std
{
//=============================================================================
//  numeric_limits<TRadian>
//-----------------------------------------------------------------------------
/// @brief Specialization of numeric_limits for TRadian types,
/// to provide the properties of the used arithmetic types.
/// @param ValueType            Value type
/// @ingroup                    vfc_core
//=============================================================================
template <class ValueType>
class numeric_limits<vfc::TRadian<ValueType>> : public std::numeric_limits<ValueType>
{

    using Radian_t = vfc::TRadian<ValueType>;

  public:
#ifndef VFC_COMPILER_VISUALC
    // will produce compiler warning C4516
    VFC_STATIC_ASSERT((numeric_limits<ValueType>::is_specialized));
#endif
    static constexpr Radian_t min() VFC_NOEXCEPT { return Radian_t(numeric_limits<ValueType>::min()); }
    static constexpr Radian_t max() VFC_NOEXCEPT { return Radian_t(numeric_limits<ValueType>::max()); }
    static constexpr Radian_t lowest() VFC_NOEXCEPT { return Radian_t(numeric_limits<ValueType>::lowest()); }
    static constexpr Radian_t epsilon() VFC_NOEXCEPT { return Radian_t(numeric_limits<ValueType>::epsilon()); }
    static constexpr Radian_t round_error() VFC_NOEXCEPT { return Radian_t(numeric_limits<ValueType>::round_error()); }
};

//=============================================================================
//  numeric_limits<TDegree>
//-----------------------------------------------------------------------------
/// @brief Specialization of numeric_limits for TDegree types,
/// to provide the properties of the used arithmetic types.
/// @param ValueType            Value type
/// @ingroup                    vfc_core
//=============================================================================
template <class ValueType>
class numeric_limits<vfc::TDegree<ValueType>> : public std::numeric_limits<ValueType>
{

    using Degree_t = vfc::TDegree<ValueType>;

  public:
#ifndef VFC_COMPILER_VISUALC
    // will produce compiler warning C4516
    VFC_STATIC_ASSERT((numeric_limits<ValueType>::is_specialized));
#endif
    static constexpr Degree_t min() VFC_NOEXCEPT { return Degree_t(numeric_limits<ValueType>::min()); }
    static constexpr Degree_t max() VFC_NOEXCEPT { return Degree_t(numeric_limits<ValueType>::max()); }
    static constexpr Degree_t lowest() VFC_NOEXCEPT { return Degree_t(numeric_limits<ValueType>::lowest()); }
    static constexpr Degree_t epsilon() VFC_NOEXCEPT { return Degree_t(numeric_limits<ValueType>::epsilon()); }
    static constexpr Degree_t round_error() VFC_NOEXCEPT { return Degree_t(numeric_limits<ValueType>::round_error()); }
};

} // namespace std

#endif // VFC_TRIG_HPP_INCLUDED

//=============================================================================

