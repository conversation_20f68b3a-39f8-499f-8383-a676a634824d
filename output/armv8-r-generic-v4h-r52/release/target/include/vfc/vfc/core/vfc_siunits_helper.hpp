//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_SIUNITS_HELPER_HPP_INCLUDED
#define ZX_VFC_SIUNITS_HELPER_HPP_INCLUDED

#include "vfc/core/vfc_types.hpp"         // vfc::int64_t
#include "vfc/core/vfc_static_assert.hpp" // VFC_STATIC_ASSERT
#include "vfc/core/vfc_math.hpp"          // G_DEG2RAD
#include "vfc/core/vfc_metaprog.hpp"      // TIf

namespace vfc
{

/// Domains for units. Covers SI units and RB extensions.
/// Used a parameter for helpers like @sa domainUnit_t and @sa domainPower_t
enum class EUnitDomain : int32_t
{
    Length,
    Mass,
    Time,
    Current,
    Temperature,
    AmountOfSubstance,
    LuminousIntensity,
    Angle,
    // RB extensions
    Pixel,
    Percentage,
    // Meta/Base type
    Unit
};

/// Info class for collecting infos regarding one domain.
/// @tparam UnitTypeParam domain specific unit type, usually TSIType<>
/// @tparam PrefixValueParam prefix (scaling) value
/// @tparam PowerValueParam integer exponent of corresponding unit
template <class UnitTypeParam, int32_t PrefixValueParam, int32_t PowerValueParam>
struct TDomainInfos
{
    using UnitType    = UnitTypeParam;
    using PrefixValue = TIntegralConstant<int32_t, PrefixValueParam>;
    using PowerValue  = TIntegralConstant<int32_t, PowerValueParam>;
};

/// Base template for extracting domain specific information from
/// TUnitInfoType and TRBInfoType. This must be a proper template class as
/// we need partial specialization.
template <EUnitDomain domain, class UnitInfoType>
struct TDomainInfosExtractor;

/// shorthand alias for accessing extracted domain infos
template <EUnitDomain domain, class UnitInfoType>
using domainInfos_t = TDomainInfosExtractor<domain, UnitInfoType>;

/// shorthand alias for domain type (no need for typename)
template <EUnitDomain domain, class UnitInfoType>
using domainUnit_t = typename domainInfos_t<domain, UnitInfoType>::UnitType;

/// shorthand alias to access domain power. @note the TIntegralConstant
/// member is returned, use domainPower_t<domain,InfoType>::value to access
/// the value.
template <EUnitDomain domain, class UnitInfoType>
using domainPower_t = typename domainInfos_t<domain, UnitInfoType>::PowerValue;

enum ESiPrefix : vfc::int32_t
{
    YOCTO = -24,
    ZEPTO = -21,
    ATTO  = -18,
    FEMTO = -15,
    PICO  = -12,
    NANO  = -9,
    MICRO = -6,
    MILLI = -3,
    CENTI = -2,
    DECI  = -1,
    NIL   = 0,
    BASE  = 0,
    DECA  = 1,
    HECTO = 2,
    KILO  = 3,
    MEGA  = 6,
    ZXGIGA  = 9,
    TERA  = 12,
    PETA  = 15,
    EXA   = 18,
    ZETTA = 21,
    YOTTA = 24
};

using integer_type = vfc::int64_t;

struct CHasZero
{
};

struct CHasOne
{
};

template <integer_type InputValue, class TrailingValueType>
struct TCountTrailingZerosIntern
{
    enum
    {
        ZERO_COUNT = 0
    };
};

template <integer_type InputValue>
struct TCountTrailingZerosIntern<InputValue, CHasZero>
{
    enum
    {
        ZERO_COUNT = 1 + TCountTrailingZerosIntern<
                             (InputValue >> 1),
                             typename vfc::TIf<(((InputValue >> 1) & 0x1) == 0), CHasZero, CHasOne>::type>::ZERO_COUNT
    };
};

//=============================================================================
//    TCountTrailingZeros<>
//-----------------------------------------------------------------------------
/// Template Metaprogram for counting the trailing zeroes of an integer value
/// at compile time
/// @tparam InputValue  the integral value of which the trailing zeroes will be counted
//=============================================================================
template <integer_type InputValue>
struct TCountTrailingZeros
{
    enum
    {
        ZERO_COUNT = TCountTrailingZerosIntern<
            InputValue,
            typename vfc::TIf<((InputValue & 0x1) == 0), CHasZero, CHasOne>::type>::ZERO_COUNT
    };
};

//=============================================================================
//    TIsDivisible<>
//-----------------------------------------------------------------------------
/// Template Metaprogram for checking if a fraction is divisible by a given divisor
/// @tparam NumValue  the numerator as integral value
/// @tparam DenValue  the denominator as integral value
/// @tparam DivisorValue  the divisor for which is checked if the numerator and denominator is divisible
//=============================================================================
template <integer_type NumValue, integer_type DenValue, integer_type DivisorValue>
struct TIsDivisible
{
    enum
    {
        NUM_IS_DIVISIBLE = ((NumValue % DivisorValue) == 0) ? 1 : 0
    };

    enum
    {
        DEN_IS_DIVISIBLE = ((DenValue % DivisorValue) == 0) ? 1 : 0
    };

    enum
    {
        IS_DIVISIBLE = (static_cast<bool>(NUM_IS_DIVISIBLE) && static_cast<bool>(DEN_IS_DIVISIBLE)) ? 1 : 0
    };

    using type = typename vfc::TIf<(IS_DIVISIBLE == 1), vfc::true_t, vfc::false_t>::type;
};

//=============================================================================
//    TIsMultPowAng<>
//-----------------------------------------------------------------------------
/// Template Metaprogram for checking if a fraction is divisible by a given divisor
/// Checks if the angle unit type must disappear after multiplication
/// ex: [m] * [rad] = [m]; [1/m] * [1/rad] = [1/m]; [rad] * [1/s] = [rad/s]; [s] * [1/rad] = [s/rad];
/// The power of any unit must have the opposite sign than the power of the angle, so that the angle unit stays
/// if (a * b <= 0) then they have opposite signs
/// @tparam UnitInfo1Type - First SIUnit type
/// @tparam UnitInfo2Type - Second SIUnit type
//=============================================================================
template <class UnitInfo1Type, class UnitInfo2Type>
struct TIsMultPowAng
{
    enum
    {
        value =
            ((((UnitInfo1Type::LENGTH_POWER_VALUE + UnitInfo2Type::LENGTH_POWER_VALUE) *
               (UnitInfo1Type::ANGLE_POWER_VALUE + UnitInfo2Type::ANGLE_POWER_VALUE)) <= 0) &&
             (((UnitInfo1Type::MASS_POWER_VALUE + UnitInfo2Type::MASS_POWER_VALUE) *
               (UnitInfo1Type::ANGLE_POWER_VALUE + UnitInfo2Type::ANGLE_POWER_VALUE)) <= 0) &&
             (((UnitInfo1Type::TIME_POWER_VALUE + UnitInfo2Type::TIME_POWER_VALUE) *
               (UnitInfo1Type::ANGLE_POWER_VALUE + UnitInfo2Type::ANGLE_POWER_VALUE)) <= 0) &&
             (((UnitInfo1Type::CURRENT_POWER_VALUE + UnitInfo2Type::CURRENT_POWER_VALUE) *
               (UnitInfo1Type::ANGLE_POWER_VALUE + UnitInfo2Type::ANGLE_POWER_VALUE)) <= 0) &&
             (((UnitInfo1Type::TEMPERATURE_POWER_VALUE + UnitInfo2Type::TEMPERATURE_POWER_VALUE) *
               (UnitInfo1Type::ANGLE_POWER_VALUE + UnitInfo2Type::ANGLE_POWER_VALUE)) <= 0) &&
             (((UnitInfo1Type::AMOUNTOFSUBSTANCE_POWER_VALUE + UnitInfo2Type::AMOUNTOFSUBSTANCE_POWER_VALUE) *
               (UnitInfo1Type::ANGLE_POWER_VALUE + UnitInfo2Type::ANGLE_POWER_VALUE)) <= 0) &&
             (((UnitInfo1Type::LUMINOUSINTENSITY_PREFIX_VALUE + UnitInfo2Type::LUMINOUSINTENSITY_PREFIX_VALUE) *
               (UnitInfo1Type::ANGLE_POWER_VALUE + UnitInfo2Type::ANGLE_POWER_VALUE)) <= 0))
    };
};

// Checks if the angle unit type must disappear after division
// ex: [s] / [rad] = [s/rad]; [rad] / [s] = [rad/s]; [rad] / [1/m] = [m]; [m] / [1/rad] = [m];
// The power of any unit must have the opposite sign than the power of the angle, so that the angle unit stays
// if (a * b <= 0) then they have opposite signs
///
///
///@tparam UnitInfo1Type - First SIUnit type
///@tparam UnitInfo2Type - Second SIUnit type
///
template <class UnitInfo1Type, class UnitInfo2Type>
struct TIsDivPowAng
{
    enum
    {
        value =
            ((((UnitInfo1Type::LENGTH_POWER_VALUE - UnitInfo2Type::LENGTH_POWER_VALUE) *
               (UnitInfo1Type::ANGLE_POWER_VALUE - UnitInfo2Type::ANGLE_POWER_VALUE)) <= 0) &&
             (((UnitInfo1Type::MASS_POWER_VALUE - UnitInfo2Type::MASS_POWER_VALUE) *
               (UnitInfo1Type::ANGLE_POWER_VALUE - UnitInfo2Type::ANGLE_POWER_VALUE)) <= 0) &&
             (((UnitInfo1Type::TIME_POWER_VALUE - UnitInfo2Type::TIME_POWER_VALUE) *
               (UnitInfo1Type::ANGLE_POWER_VALUE - UnitInfo2Type::ANGLE_POWER_VALUE)) <= 0) &&
             (((UnitInfo1Type::CURRENT_POWER_VALUE - UnitInfo2Type::CURRENT_POWER_VALUE) *
               (UnitInfo1Type::ANGLE_POWER_VALUE - UnitInfo2Type::ANGLE_POWER_VALUE)) <= 0) &&
             (((UnitInfo1Type::TEMPERATURE_POWER_VALUE - UnitInfo2Type::TEMPERATURE_POWER_VALUE) *
               (UnitInfo1Type::ANGLE_POWER_VALUE - UnitInfo2Type::ANGLE_POWER_VALUE)) <= 0) &&
             (((UnitInfo1Type::AMOUNTOFSUBSTANCE_POWER_VALUE - UnitInfo2Type::AMOUNTOFSUBSTANCE_POWER_VALUE) *
               (UnitInfo1Type::ANGLE_POWER_VALUE - UnitInfo2Type::ANGLE_POWER_VALUE)) <= 0) &&
             (((UnitInfo1Type::LUMINOUSINTENSITY_PREFIX_VALUE - UnitInfo2Type::LUMINOUSINTENSITY_PREFIX_VALUE) *
               (UnitInfo1Type::ANGLE_POWER_VALUE - UnitInfo2Type::ANGLE_POWER_VALUE)) <= 0))
    };
};

// Checks if the unit has only negative powers
///
///
///@tparam UnitInfoType - SIUnit type
///
template <class UnitInfoType>
struct TIsNegativePower
{
    enum
    {
        value =
            ((UnitInfoType::LENGTH_POWER_VALUE <= 0) && (UnitInfoType::MASS_POWER_VALUE <= 0) &&
             (UnitInfoType::TIME_POWER_VALUE <= 0) && (UnitInfoType::CURRENT_POWER_VALUE <= 0) &&
             (UnitInfoType::TEMPERATURE_POWER_VALUE <= 0) && (UnitInfoType::AMOUNTOFSUBSTANCE_POWER_VALUE <= 0) &&
             (UnitInfoType::LUMINOUSINTENSITY_PREFIX_VALUE <= 0) && (UnitInfoType::ANGLE_POWER_VALUE <= 0))
    };
};

// Checks if the unit is a valid angle dominant unit (ex: deg, rad/s, rad/m...)
///
///
///@tparam UnitInfoType - SIUnit type
///
template <class UnitInfoType>
struct TIsValidAngleDominant
{
    enum
    {
        value =
            ((UnitInfoType::ANGLE_POWER_VALUE > 0) && (UnitInfoType::LENGTH_POWER_VALUE <= 0) &&
             (UnitInfoType::MASS_POWER_VALUE <= 0) && (UnitInfoType::TIME_POWER_VALUE <= 0) &&
             (UnitInfoType::CURRENT_POWER_VALUE <= 0) && (UnitInfoType::TEMPERATURE_POWER_VALUE <= 0) &&
             (UnitInfoType::AMOUNTOFSUBSTANCE_POWER_VALUE <= 0) && (UnitInfoType::LUMINOUSINTENSITY_PREFIX_VALUE <= 0))
    };
};

///
///
///@tparam NumValue - the numerator as integral value
///@tparam DenValue - the denominator as integral value
///@tparam DivisorValue - the divisor for which is checked if the numerator and denominator is divisible
///
template <integer_type NumValue, integer_type DenValue, integer_type DivisorValue, class BooleanType = vfc::false_t>
struct TFactoriseWithValueIntern
{
    static const integer_type NUM = NumValue;
    static const integer_type DEN = DenValue;
};

///
///
///@tparam NumValue - the numerator as integral value
///@tparam DenValue - the denominator as integral value
///@tparam DivisorValue - the divisor for which is checked if the numerator and denominator is divisible
///
template <integer_type NumValue, integer_type DenValue, integer_type DivisorValue>
struct TFactoriseWithValueIntern<NumValue, DenValue, DivisorValue, vfc::true_t>
{
    static const integer_type NUM = TFactoriseWithValueIntern<
        NumValue / DivisorValue,
        DenValue / DivisorValue,
        DivisorValue,
        typename TIsDivisible<NumValue / DivisorValue, DenValue / DivisorValue, DivisorValue>::type>::NUM;

    static const integer_type DEN = TFactoriseWithValueIntern<
        NumValue / DivisorValue,
        DenValue / DivisorValue,
        DivisorValue,
        typename TIsDivisible<NumValue / DivisorValue, DenValue / DivisorValue, DivisorValue>::type>::DEN;
};

///
///
///@tparam NumValue - the numerator as integral value
///@tparam DenValue - the denominator as integral value
///@tparam DivisorValue - the divisor for which is checked if the numerator and denominator is divisible
///
template <integer_type NumValue, integer_type DenValue, integer_type DivisorValue>
struct TFactorizeWithValue
{
    static const integer_type NUM = TFactoriseWithValueIntern<
        NumValue,
        DenValue,
        DivisorValue,
        typename TIsDivisible<NumValue, DenValue, DivisorValue>::type>::NUM;

    static const integer_type DEN = TFactoriseWithValueIntern<
        NumValue,
        DenValue,
        DivisorValue,
        typename TIsDivisible<NumValue, DenValue, DivisorValue>::type>::DEN;
};

///
///
///@tparam NumValue - the numerator as integral value
///@tparam DenValue - the denominator as integral value
///
template <integer_type NumValue, integer_type DenValue>
struct TFactorize
{
    static const vfc::int32_t NUM_SHR_VALUE = TCountTrailingZeros<NumValue>::ZERO_COUNT;
    static const vfc::int32_t DEN_SHR_VALUE = TCountTrailingZeros<DenValue>::ZERO_COUNT;

    static const vfc::int32_t SHR_VALUE = (NUM_SHR_VALUE < DEN_SHR_VALUE) ? NUM_SHR_VALUE : DEN_SHR_VALUE;

    static const integer_type NUM = ((TFactorizeWithValue<NumValue, DenValue, 5>::NUM) >> SHR_VALUE);
    static const integer_type DEN = ((TFactorizeWithValue<NumValue, DenValue, 5>::DEN) >> SHR_VALUE);
};

///
///
///@tparam ValueType - SiUnit type
///
template <class ValueType>
struct TMaxValue
{
};

template <>
struct TMaxValue<vfc::int32_t>
{
    static const vfc::int32_t value = 0x7FFFFFFFL;
};

template <>
struct TMaxValue<vfc::int64_t>
{
    static const vfc::int64_t value = 0x7FFFFFFFFFFFFFFFLL;
};
///
///
///@tparam Input1Value - First value to multiply
///@tparam Input2Value - Second value to multiply
///
template <integer_type Input1Value, integer_type Input2Value>
struct TMultiply
{
    // Overflow due to multiply. Value too big.
    VFC_STATIC_ASSERT((Input1Value < (TMaxValue<integer_type>::value / Input2Value)));

    static const integer_type VALUE = (Input1Value * Input2Value);
};

///
///
///@tparam Num1Value - numinator to factorize
///@tparam Den1Value - denominator to factorize
///@tparam Num2Value - numinator to factorize
///@tparam Den2Value - denominator to factorize
///@tparam Num3Value - numinator
///@tparam Den3Value - denominator to factorize
///@tparam Num4Value - numinator to factorize
///@tparam Den4Value - denominator to factorize
///@tparam Num5Value - numinator to factorize
///@tparam Den5Value - denominator to factorize
///@tparam Num6Value - numinator to factorize
///@tparam Den6Value - denominator to factorize
///@tparam Num7Value - numinator to factorize
///@tparam Den7Value - denominator to factorize
///@tparam Num8Value - numinator to factorize
///@tparam Den8Value - denominator to factorize
///@tparam Num9Value - numinator to factorize
///@tparam Den9Value - denominator to factorize
///@tparam Num10Value - numinator to factorize
///@tparam Den10Value - denominator to factorize
///@tparam Num11Value - numinator to factorize
///@tparam Den11Value - denominator to factorize
///
template <
    integer_type Num1Value,
    integer_type Den1Value,
    integer_type Num2Value  = 1,
    integer_type Den2Value  = 1,
    integer_type Num3Value  = 1,
    integer_type Den3Value  = 1,
    integer_type Num4Value  = 1,
    integer_type Den4Value  = 1,
    integer_type Num5Value  = 1,
    integer_type Den5Value  = 1,
    integer_type Num6Value  = 1,
    integer_type Den6Value  = 1,
    integer_type Num7Value  = 1,
    integer_type Den7Value  = 1,
    integer_type Num8Value  = 1,
    integer_type Den8Value  = 1,
    integer_type Num9Value  = 1,
    integer_type Den9Value  = 1,
    integer_type Num10Value = 1,
    integer_type Den10Value = 1,
    integer_type Num11Value = 1,
    integer_type Den11Value = 1>
struct TFactorizeInputs
{
    // Factorizing Num1Value, Den1Value, Num2Value, Den2Value
    static const integer_type NUM_2 = TFactorize<
        TMultiply<TFactorize<Num1Value, Den2Value>::NUM, TFactorize<Num2Value, Den1Value>::NUM>::VALUE,
        TMultiply<TFactorize<Num1Value, Den2Value>::DEN, TFactorize<Num2Value, Den1Value>::DEN>::VALUE>::NUM;
    static const integer_type DEN_2 = TFactorize<
        TMultiply<TFactorize<Num1Value, Den2Value>::NUM, TFactorize<Num2Value, Den1Value>::NUM>::VALUE,
        TMultiply<TFactorize<Num1Value, Den2Value>::DEN, TFactorize<Num2Value, Den1Value>::DEN>::VALUE>::DEN;

    // Factorizing NUM_2, DEN_2, Num3Value, Den3Value
    static const integer_type NUM_3 = TFactorize<
        TMultiply<TFactorize<NUM_2, Den3Value>::NUM, TFactorize<Num3Value, DEN_2>::NUM>::VALUE,
        TMultiply<TFactorize<NUM_2, Den3Value>::DEN, TFactorize<Num3Value, DEN_2>::DEN>::VALUE>::NUM;
    static const integer_type DEN_3 = TFactorize<
        TMultiply<TFactorize<NUM_2, Den3Value>::NUM, TFactorize<Num3Value, DEN_2>::NUM>::VALUE,
        TMultiply<TFactorize<NUM_2, Den3Value>::DEN, TFactorize<Num3Value, DEN_2>::DEN>::VALUE>::DEN;

    // Factorizing NUM_3, DEN_3, Num4Value, Den4Value
    static const integer_type NUM_4 = TFactorize<
        TMultiply<TFactorize<NUM_3, Den4Value>::NUM, TFactorize<Num4Value, DEN_3>::NUM>::VALUE,
        TMultiply<TFactorize<NUM_3, Den4Value>::DEN, TFactorize<Num4Value, DEN_3>::DEN>::VALUE>::NUM;
    static const integer_type DEN_4 = TFactorize<
        TMultiply<TFactorize<NUM_3, Den4Value>::NUM, TFactorize<Num4Value, DEN_3>::NUM>::VALUE,
        TMultiply<TFactorize<NUM_3, Den4Value>::DEN, TFactorize<Num4Value, DEN_3>::DEN>::VALUE>::DEN;

    // Factorizing NUM_4, DEN_4, Num5Value, Den5Value
    static const integer_type NUM_5 = TFactorize<
        TMultiply<TFactorize<NUM_4, Den5Value>::NUM, TFactorize<Num5Value, DEN_4>::NUM>::VALUE,
        TMultiply<TFactorize<NUM_4, Den5Value>::DEN, TFactorize<Num5Value, DEN_4>::DEN>::VALUE>::NUM;
    static const integer_type DEN_5 = TFactorize<
        TMultiply<TFactorize<NUM_4, Den5Value>::NUM, TFactorize<Num5Value, DEN_4>::NUM>::VALUE,
        TMultiply<TFactorize<NUM_4, Den5Value>::DEN, TFactorize<Num5Value, DEN_4>::DEN>::VALUE>::DEN;

    // Factorizing NUM_5, DEN_5, Num6Value, Den6Value
    static const integer_type NUM_6 = TFactorize<
        TMultiply<TFactorize<NUM_5, Den6Value>::NUM, TFactorize<Num6Value, DEN_5>::NUM>::VALUE,
        TMultiply<TFactorize<NUM_5, Den6Value>::DEN, TFactorize<Num6Value, DEN_5>::DEN>::VALUE>::NUM;
    static const integer_type DEN_6 = TFactorize<
        TMultiply<TFactorize<NUM_5, Den6Value>::NUM, TFactorize<Num6Value, DEN_5>::NUM>::VALUE,
        TMultiply<TFactorize<NUM_5, Den6Value>::DEN, TFactorize<Num6Value, DEN_5>::DEN>::VALUE>::DEN;

    // Factorizing NUM_6, DEN_6, Num7Value, Den7Value
    static const integer_type NUM_7 = TFactorize<
        TMultiply<TFactorize<NUM_6, Den7Value>::NUM, TFactorize<Num7Value, DEN_6>::NUM>::VALUE,
        TMultiply<TFactorize<NUM_6, Den7Value>::DEN, TFactorize<Num7Value, DEN_6>::DEN>::VALUE>::NUM;
    static const integer_type DEN_7 = TFactorize<
        TMultiply<TFactorize<NUM_6, Den7Value>::NUM, TFactorize<Num7Value, DEN_6>::NUM>::VALUE,
        TMultiply<TFactorize<NUM_6, Den7Value>::DEN, TFactorize<Num7Value, DEN_6>::DEN>::VALUE>::DEN;

    // Factorizing NUM_7, DEN_7, Num8Value, Den8Value
    static const integer_type NUM_8 = TFactorize<
        TMultiply<TFactorize<NUM_7, Den8Value>::NUM, TFactorize<Num8Value, DEN_7>::NUM>::VALUE,
        TMultiply<TFactorize<NUM_7, Den8Value>::DEN, TFactorize<Num8Value, DEN_7>::DEN>::VALUE>::NUM;
    static const integer_type DEN_8 = TFactorize<
        TMultiply<TFactorize<NUM_7, Den8Value>::NUM, TFactorize<Num8Value, DEN_7>::NUM>::VALUE,
        TMultiply<TFactorize<NUM_7, Den8Value>::DEN, TFactorize<Num8Value, DEN_7>::DEN>::VALUE>::DEN;

    // Factorizing NUM_8, DEN_8, Num9Value, Den9Value
    static const integer_type NUM_9 = TFactorize<
        TMultiply<TFactorize<NUM_8, Den9Value>::NUM, TFactorize<Num9Value, DEN_8>::NUM>::VALUE,
        TMultiply<TFactorize<NUM_8, Den9Value>::DEN, TFactorize<Num9Value, DEN_8>::DEN>::VALUE>::NUM;
    static const integer_type DEN_9 = TFactorize<
        TMultiply<TFactorize<NUM_8, Den9Value>::NUM, TFactorize<Num9Value, DEN_8>::NUM>::VALUE,
        TMultiply<TFactorize<NUM_8, Den9Value>::DEN, TFactorize<Num9Value, DEN_8>::DEN>::VALUE>::DEN;

    // Factorizing NUM_9, DEN_9, Num10Value, Den10Value
    static const integer_type NUM_10 = TFactorize<
        TMultiply<TFactorize<NUM_9, Den10Value>::NUM, TFactorize<Num10Value, DEN_9>::NUM>::VALUE,
        TMultiply<TFactorize<NUM_9, Den10Value>::DEN, TFactorize<Num10Value, DEN_9>::DEN>::VALUE>::NUM;
    static const integer_type DEN_10 = TFactorize<
        TMultiply<TFactorize<NUM_9, Den10Value>::NUM, TFactorize<Num10Value, DEN_9>::NUM>::VALUE,
        TMultiply<TFactorize<NUM_9, Den10Value>::DEN, TFactorize<Num10Value, DEN_9>::DEN>::VALUE>::DEN;

    // Factorizing NUM_10, DEN_10, Num11Value, Den11Value
    static const integer_type NUM = TFactorize<
        TMultiply<TFactorize<NUM_10, Den11Value>::NUM, TFactorize<Num11Value, DEN_10>::NUM>::VALUE,
        TMultiply<TFactorize<NUM_10, Den11Value>::DEN, TFactorize<Num11Value, DEN_10>::DEN>::VALUE>::NUM;
    static const integer_type DEN = TFactorize<
        TMultiply<TFactorize<NUM_10, Den11Value>::NUM, TFactorize<Num11Value, DEN_10>::NUM>::VALUE,
        TMultiply<TFactorize<NUM_10, Den11Value>::DEN, TFactorize<Num11Value, DEN_10>::DEN>::VALUE>::DEN;
};

struct CBasicType
{
    struct CCompound
    {
    };

    using si_base_unit_type = CCompound;

    enum
    {
        CONVERSION_RATIONAL = 1,
        FLOATING_OFFSET     = 0
    };

    static const integer_type NUM    = 1;
    static const integer_type DEN    = 1;
    static const integer_type OFFSET = 0;
};

/// TFractionPower - compile-time power calculation for fractional values
/// For a fraction (a/b) and an integer n compute
/// (a/b)^n = (a^n/b^n) if n is positive or
/// (a/b)^n = (b/a)^(-n) = (b^(-n)/a^(-n)) if n is negative (hence -n is positive)
///
/// @tparam[integer_type] NumeratorValue - numerator of input fraction
/// @tparam[integer_type] DenominatorValue - denominator of input fraction
/// @tparam[integer_type] PowerValue - exponent power
/// @tparam[bool] IsPowerPositive - Selection for overloads
/// @return Resulting fraction is returned in NUMERATOR/DENOMINATOR
template <
    integer_type NumeratorValue,
    integer_type DenominatorValue,
    vfc::int32_t PowerValue,
    bool         IsPowerPositive = ((PowerValue >= 0) ? true : false)>
struct TFractionPower;

/// specialization for positive and zero powers
template <integer_type NumeratorValue, integer_type DenominatorValue, vfc::int32_t PowerValue>
struct TFractionPower<NumeratorValue, DenominatorValue, PowerValue, true>
{
    static const integer_type NUMERATOR   = TPower<NumeratorValue, PowerValue>::value_ui64;
    static const integer_type DENOMINATOR = TPower<DenominatorValue, PowerValue>::value_ui64;
};

/// specialization for negative powers
template <integer_type NumeratorValue, integer_type DenominatorValue, vfc::int32_t PowerValue>
struct TFractionPower<NumeratorValue, DenominatorValue, PowerValue, false>
{
    static const integer_type NUMERATOR   = TPower<DenominatorValue, (-PowerValue)>::value_ui64;
    static const integer_type DENOMINATOR = TPower<NumeratorValue, (-PowerValue)>::value_ui64;
};

template <
    integer_type NumeratorValue,
    integer_type DenominatorValue,
    vfc::int32_t PowerValue,
    typename IsPowerPositiveType>
class VFC_ATR_DEPRECATED2(TInvert, "TInvert is deprecated, temporary replacement is TFractionPower");

template <
    integer_type NumeratorValue,
    integer_type DenominatorValue,
    vfc::int32_t PowerValue,
    typename IsPowerPositiveType>
class TInvert : public TFractionPower<
                    NumeratorValue,
                    DenominatorValue,
                    PowerValue,
                    TIsSameType<IsPowerPositiveType, true_t>::value>
{
};

namespace sitime
{
const vfc::uint32_t WEEK_IN_DAYS   = 7;
const vfc::uint32_t MONTH_IN_DAYS  = 30;
const vfc::uint32_t YEAR_IN_DAYS   = 365;
const vfc::uint32_t YEAR_IN_MONTHS = 12;

enum ESiTimePrefix : vfc::int32_t
{
    NANO_SECOND  = -9,
    MICRO_SECOND = -6,
    MILLI_SECOND = -3,
    SECOND       = 0,
    MINUTE       = 60,
    HOUR         = 3600,
    DAY          = 3600 * 24,
    WEEK         = 3600 * 24 * WEEK_IN_DAYS,
    MONTH        = 3600 * 24 * MONTH_IN_DAYS,
    YEAR         = 3600 * 24 * YEAR_IN_DAYS
};

///
///
///@tparam InUnitScaleValue - Value to scale in SIUnit
///@tparam OutScaleValue - Output value for conversion
///@tparam enable - meta programming parameter used to eliminate ambiguities
///
template <vfc::int32_t InUnitScaleValue, vfc::int32_t OutScaleValue, typename enable = void>
struct TTimeConvertUnroll
{
  public:
    static const integer_type Y_VALUE = InUnitScaleValue - OutScaleValue;

    static const integer_type CONVERT_FACTOR_NUM = TPower<10, Y_VALUE>::value_ui64;

    static const integer_type CONVERT_FACTOR_DEN = 1;
};

template <vfc::int32_t Value>
struct TTimeConvertUnroll<Value, Value>
{
  public:
    static const integer_type CONVERT_FACTOR_NUM = 1;
    static const integer_type CONVERT_FACTOR_DEN = 1;
};

template <>
struct TTimeConvertUnroll<YEAR, MONTH>
{
    static const integer_type CONVERT_FACTOR_NUM = YEAR_IN_MONTHS;

    static const integer_type CONVERT_FACTOR_DEN = 1;
};

template <>
struct TTimeConvertUnroll<YEAR, WEEK>
{
    static const integer_type CONVERT_FACTOR_NUM = YEAR_IN_DAYS;

    static const integer_type CONVERT_FACTOR_DEN = WEEK_IN_DAYS;
};

template <vfc::int32_t val1, vfc::int32_t val2>
using enableIfNotSame_t = typename TEnableIf<val1 != val2, void>::type;

template <vfc::int32_t OutScaleValue>
struct TTimeConvertUnroll<YEAR, OutScaleValue, enableIfNotSame_t<YEAR, OutScaleValue>>
{
    static const integer_type CONVERT_FACTOR_NUM =
        YEAR / DAY * TTimeConvertUnroll<DAY, OutScaleValue>::CONVERT_FACTOR_NUM;

    static const integer_type CONVERT_FACTOR_DEN = 1 * TTimeConvertUnroll<DAY, OutScaleValue>::CONVERT_FACTOR_DEN;
};

template <>
struct TTimeConvertUnroll<MONTH, WEEK>
{
    static const integer_type CONVERT_FACTOR_NUM = MONTH_IN_DAYS;

    static const integer_type CONVERT_FACTOR_DEN = WEEK_IN_DAYS;
};

template <vfc::int32_t OutScaleValue>
struct TTimeConvertUnroll<MONTH, OutScaleValue, enableIfNotSame_t<MONTH, OutScaleValue>>
{
    static const integer_type CONVERT_FACTOR_NUM =
        (MONTH / DAY) * TTimeConvertUnroll<DAY, OutScaleValue>::CONVERT_FACTOR_NUM;

    static const integer_type CONVERT_FACTOR_DEN = 1 * TTimeConvertUnroll<DAY, OutScaleValue>::CONVERT_FACTOR_DEN;
};

template <vfc::int32_t OutScaleValue>
struct TTimeConvertUnroll<WEEK, OutScaleValue, enableIfNotSame_t<WEEK, OutScaleValue>>
{
    static const integer_type CONVERT_FACTOR_NUM =
        (WEEK / DAY) * TTimeConvertUnroll<DAY, OutScaleValue>::CONVERT_FACTOR_NUM;

    static const integer_type CONVERT_FACTOR_DEN = 1 * TTimeConvertUnroll<DAY, OutScaleValue>::CONVERT_FACTOR_DEN;
};

template <vfc::int32_t OutScaleValue>
struct TTimeConvertUnroll<DAY, OutScaleValue, enableIfNotSame_t<DAY, OutScaleValue>>
{
    static const integer_type CONVERT_FACTOR_NUM =
        (DAY / HOUR) * TTimeConvertUnroll<HOUR, OutScaleValue>::CONVERT_FACTOR_NUM;

    static const integer_type CONVERT_FACTOR_DEN = 1 * TTimeConvertUnroll<HOUR, OutScaleValue>::CONVERT_FACTOR_DEN;
};

template <>
struct TTimeConvertUnroll<HOUR, MINUTE>
{
    static const integer_type CONVERT_FACTOR_NUM = HOUR / MINUTE;

    static const integer_type CONVERT_FACTOR_DEN = 1;
};

template <vfc::int32_t OutScaleValue>
struct TTimeConvertUnroll<HOUR, OutScaleValue, enableIfNotSame_t<HOUR, OutScaleValue>>
{
  public:
    static const integer_type CONVERT_FACTOR_NUM = HOUR * TTimeConvertUnroll<SECOND, OutScaleValue>::CONVERT_FACTOR_NUM;

    static const integer_type CONVERT_FACTOR_DEN = 1 * TTimeConvertUnroll<SECOND, OutScaleValue>::CONVERT_FACTOR_DEN;
};

template <vfc::int32_t OutScaleValue>
struct TTimeConvertUnroll<MINUTE, OutScaleValue, enableIfNotSame_t<MINUTE, OutScaleValue>>
{
    static const integer_type CONVERT_FACTOR_NUM =
        MINUTE * TTimeConvertUnroll<SECOND, OutScaleValue>::CONVERT_FACTOR_NUM;

    static const integer_type CONVERT_FACTOR_DEN = 1 * TTimeConvertUnroll<SECOND, OutScaleValue>::CONVERT_FACTOR_DEN;
};

///
///@tparam PowerValue - value of pow to convert
///@tparam InUnitPrefixValue - SiUnit input value to convert
///@tparam OutUnitPrefixValue - SiUnit output value of conversion
///
template <vfc::int32_t PowerValue, vfc::int32_t InUnitPrefixValue, vfc::int32_t OutUnitPrefixValue>
class TConvertTime
{
  private:
    static const vfc::int32_t INUNITPREFIXVALUE_INTERNAL =
        (InUnitPrefixValue > OutUnitPrefixValue) ? InUnitPrefixValue : OutUnitPrefixValue;

    static const vfc::int32_t OUTUNITPREFIXVALUE_INTERNAL =
        (InUnitPrefixValue > OutUnitPrefixValue) ? OutUnitPrefixValue : InUnitPrefixValue;

    static const integer_type CONVERT_FACTOR_NUM =
        sitime::TTimeConvertUnroll<INUNITPREFIXVALUE_INTERNAL, OUTUNITPREFIXVALUE_INTERNAL>::CONVERT_FACTOR_NUM;

    static const integer_type CONVERT_FACTOR_DEN =
        sitime::TTimeConvertUnroll<INUNITPREFIXVALUE_INTERNAL, OUTUNITPREFIXVALUE_INTERNAL>::CONVERT_FACTOR_DEN;

    static const integer_type NUMERATOR_INTERNAL =
        (OutUnitPrefixValue > InUnitPrefixValue) ? CONVERT_FACTOR_DEN : CONVERT_FACTOR_NUM;

    static const integer_type DENOMINATOR_INTERNAL =
        (OutUnitPrefixValue < InUnitPrefixValue) ? CONVERT_FACTOR_DEN : CONVERT_FACTOR_NUM;

  public:
    static const integer_type NUM = TFractionPower<NUMERATOR_INTERNAL, DENOMINATOR_INTERNAL, PowerValue>::NUMERATOR;

    static const integer_type DEN = TFractionPower<NUMERATOR_INTERNAL, DENOMINATOR_INTERNAL, PowerValue>::DENOMINATOR;

    static const integer_type OFFSET = 0;
};

} // namespace sitime

///
///
///@tparam InputValue - type of input value to execute function on
///
template <integer_type InputValue>
class TNumDenPower
{
  public:
    static const integer_type NUM = (InputValue > 0) ? InputValue : 0;
    static const integer_type DEN = (InputValue > 0) ? 0 : -InputValue;
};

///
///
///@tparam SourceType - source type to convert
///@tparam DestinationType - destination type for conversion
///
template <class SourceType, class DestinationType>
struct TBasicTypeConvert
{

    using self_type = TBasicTypeConvert<SourceType, DestinationType>;

    static const integer_type NUM = SourceType::NUM * DestinationType::DEN;
    static const integer_type DEN = SourceType::DEN * DestinationType::NUM;

    enum
    {
        DO_MULTIPLY           = SourceType::CONVERSION_RATIONAL,
        DO_DIVIDE             = DestinationType::CONVERSION_RATIONAL,
        DO_OFFSET_ADDITION    = SourceType::FLOATING_OFFSET,
        DO_OFFSET_SUBTRACTION = DestinationType::FLOATING_OFFSET
    };

    template <class ValueType>
    inline static void performConversion(ValueType& f_value_r)
    {
        self_type::doMultiply(f_value_r, typename vfc::TIf<(DO_MULTIPLY == 1), vfc::false_t, vfc::true_t>::type());
        self_type::doDivide(f_value_r, typename vfc::TIf<(DO_DIVIDE == 1), vfc::false_t, vfc::true_t>::type());
    }

    template <class ValueType, int32_t ExponentPowerValue>
    inline static void performConversionWithPower(ValueType& f_value_r)
    {
        performConversionWithPowerCases<ValueType, ExponentPowerValue>(
            f_value_r,
            typename TIf<(0 == ExponentPowerValue), true_t, false_t>::type(), // isZero
            typename TIf<(0 <= ExponentPowerValue), true_t, false_t>::type()  // positive
        );
    }

    /// overload for non-zero positive exponent values
    template <class ValueType, int32_t ExponentPowerValue>
    inline static void performConversionWithPowerCases(ValueType& f_value_r, false_t, true_t)
    {
        self_type::performConversion(f_value_r);
        performConversionWithPowerCases<ValueType, ExponentPowerValue - 1>(
            f_value_r,
            typename TIf<(0 == ExponentPowerValue - 1), true_t, false_t>::type(), // isZero
            true_t()                                                              // still positive or zero
        );
    }

    /// overload for non-zero negative exponent values
    template <class ValueType, int32_t ExponentPowerValue>
    inline static void performConversionWithPowerCases(ValueType& f_value_r, false_t, false_t)
    {
        ValueType l_inverse = ValueType(1); // PRQA S 3082 # Technical debt L2 id=00c152ee6c9cbcad40f1-9345f556d66f3677
        performConversionWithPowerCases<ValueType, -ExponentPowerValue>(
            l_inverse,
            false_t(), // isZero == false, because only sign changes and was positive before
            true_t()   // now positive, as it was negative before
        );
        VFC_ASSERT2(notZero(l_inverse), "invalid inversion factor with negative power");
        f_value_r /= l_inverse;
    }

    /// overload for zero exponent value
    template <class ValueType, int32_t ExponentPowerValue, typename TrueOrFalse>
    inline static void performConversionWithPowerCases(ValueType&, true_t, TrueOrFalse)
    {
        // intentionally left blank
    }

    template <class ValueType>
    inline static void performOffsetConversion(ValueType& f_value_r)
    {
        self_type::doOffsetAddition(
            f_value_r, typename vfc::TIf<(DO_OFFSET_ADDITION == 1), vfc::true_t, vfc::false_t>::type());
        self_type::doOffsetSubtraction(
            f_value_r, typename vfc::TIf<(DO_OFFSET_SUBTRACTION == 1), vfc::true_t, vfc::false_t>::type());
    }

    template <class ValueType>
    inline static void doMultiply(ValueType& f_value_r, vfc::true_t)
    {
        SourceType::doMultiply(f_value_r);
    }

    template <class ValueType>
    inline static void doMultiply(ValueType&, vfc::false_t)
    {
        // intentionally left blank
    }

    template <class ValueType>
    inline static void doDivide(ValueType& f_value_r, vfc::true_t)
    {
        DestinationType::doDivide(f_value_r);
    }

    template <class ValueType>
    inline static void doDivide(ValueType&, vfc::false_t)
    {
        // intentionally left blank
    }

    template <class ValueType>
    inline static void doOffsetAddition(ValueType& f_value_r, vfc::true_t)
    {
        SourceType::doOffsetAddition(f_value_r);
    }

    template <class ValueType>
    inline static void doOffsetAddition(ValueType&, vfc::false_t)
    {
        // intentionally left blank
    }
    template <class ValueType>
    inline static void doOffsetSubtraction(ValueType& f_value_r, vfc::true_t)
    {
        DestinationType::doOffsetSubtraction(f_value_r);
    }

    template <class ValueType>
    inline static void doOffsetSubtraction(ValueType&, vfc::false_t)
    {
        // intentionally left blank
    }
};

///
///
///@tparam SourceType - Source type for conversion
///@tparam DestinationType - destination type for conversion
///
template <class SourceType, class DestinationType>
struct TComplexConvert
{
    using self_type = TComplexConvert<SourceType, DestinationType>;

    static const integer_type NUM = SourceType::NUM * DestinationType::DEN;
    static const integer_type DEN = SourceType::DEN * DestinationType::NUM;

    enum
    {
        DO_CONV = ((self_type::NUM != 1) || (self_type::DEN != 1)) ? 1 : 0
    };

    template <class ValueType>
    inline static void performConversion(ValueType& f_value_r)
    {
        self_type::performConversion(f_value_r, typename vfc::TIf<(DO_CONV == 1), vfc::true_t, vfc::false_t>::type());
    }

    template <class ValueType, int32_t ExponentPowerValue>
    inline static void performConversionWithPower(ValueType& f_value_r)
    {
        performConversionWithPowerCases<ValueType, ExponentPowerValue>(
            f_value_r,
            typename TIf<(0 == ExponentPowerValue), true_t, false_t>::type(), // isZero
            typename TIf<(0 <= ExponentPowerValue), true_t, false_t>::type()  // positive
        );
    }

    /// overload for non-zero positive exponent values
    template <class ValueType, int32_t ExponentPowerValue>
    inline static void performConversionWithPowerCases(ValueType& f_value_r, false_t, true_t)
    {
        self_type::performConversion(f_value_r);
        performConversionWithPowerCases<ValueType, ExponentPowerValue - 1>(
            f_value_r,
            typename TIf<(0 == ExponentPowerValue - 1), true_t, false_t>::type(), // isZero
            true_t()                                                              // still positive or zero
        );
    }

    /// overload for non-zero negative exponent values
    template <class ValueType, int32_t ExponentPowerValue>
    inline static void performConversionWithPowerCases(ValueType& f_value_r, false_t, false_t)
    {
        ValueType l_inverse = ValueType(1);
        performConversionWithPowerCases<ValueType, -ExponentPowerValue>(
            l_inverse,
            false_t(), // isZero == false, because only sign changes and was positive before
            true_t()   // now positive, as it was negative before
        );
        VFC_ASSERT2(notZero(l_inverse), "invalid inversion factor with negative power");
        f_value_r /= l_inverse;
    }

    /// overload for zero exponent value
    template <class ValueType, int32_t ExponentPowerValue, typename TrueOrFalse>
    inline static void performConversionWithPowerCases(ValueType&, true_t, TrueOrFalse)
    {
        // intentionally left blank
    }

    template <class ValueType>
    inline static void performConversion(ValueType& f_value_r, vfc::true_t)
    {
        DestinationType::performConversion(f_value_r);
    }

    template <class ValueType>
    inline static void performConversion(ValueType&, vfc::false_t)
    {
        // intentionally left blank
    }
};

struct CIntegralType
{
};

struct CFloatingType
{
};

///
///@tparam Unit1Type - SiUnit type for conversion
///@tparam Unit2Type - SiUnit type for conversion
///@tparam ExponentPowerValue - value of the exponent
///@tparam TConvertClass - class for conversion
///@tparam DataType - data type that is used for conversion
///
template <
    class Unit1Type,
    class Unit2Type,
    int32_t ExponentPowerValue,
    template <class, class>
    class TConvertClass,
    class DataType>
struct TUnitConversion
{
    using self_type = TUnitConversion<Unit1Type, Unit2Type, ExponentPowerValue, TConvertClass, DataType>;

    // Note if the conversion is irrational, the NUM & DEN will be 1
    enum
    {
        CONVERSION_RATIONAL =
            (static_cast<bool>(Unit1Type::CONVERSION_RATIONAL) && static_cast<bool>(Unit2Type::CONVERSION_RATIONAL))
                ? 1
                : 0,
        FLOATING_OFFSET =
            (static_cast<bool>(Unit1Type::FLOATING_OFFSET) || static_cast<bool>(Unit2Type::FLOATING_OFFSET)) ? 1 : 0
    };

    static const integer_type NumExp1 =
        (self_type::CONVERSION_RATIONAL == 1) ? TConvertClass<Unit1Type, Unit2Type>::NUM : 1;

    static const integer_type DenExp1 =
        (self_type::CONVERSION_RATIONAL == 1) ? TConvertClass<Unit1Type, Unit2Type>::DEN : 1;

    using Fraction_t = TFractionPower<NumExp1, DenExp1, ExponentPowerValue>;

    static const integer_type NUM = Fraction_t::NUMERATOR;
    static const integer_type DEN = Fraction_t::DENOMINATOR;

    template <class ValueType>
    inline static void performConversion(ValueType& f_value_r)
    {
        self_type::performConversion(
            f_value_r, typename vfc::TIf<(CONVERSION_RATIONAL == 1), vfc::false_t, vfc::true_t>::type());

        self_type::performOffsetConversion(
            f_value_r,
            typename vfc::TIf<(FLOATING_OFFSET == 1 && ExponentPowerValue != 0), vfc::true_t, vfc::false_t>::type());
    }

    template <class ValueType>
    inline static void performConversion(ValueType& f_value_r, vfc::true_t)
    {
        // comes here only if there is an irrational conversion
        TConvertClass<Unit1Type, Unit2Type>::template performConversionWithPower<ValueType, ExponentPowerValue>(
            f_value_r);
    }

    template <class ValueType>
    inline static void performConversion(ValueType&, vfc::false_t)
    {
        // intentionall left blank
    }

    template <class ValueType>
    inline static void performOffsetConversion(ValueType& f_value_r, vfc::true_t)
    {
        // comes here only if there is an offset conversion
        TConvertClass<Unit1Type, Unit2Type>::performOffsetConversion(f_value_r);
    }

    template <class ValueType>
    inline static void performOffsetConversion(ValueType&, vfc::false_t)
    {
        // intentionall left blank
    }
};

template <class Unit1Type, class Unit2Type, int32_t ExponentPowerValue, template <class, class> class TConvertClass>
struct TUnitConversion<Unit1Type, Unit2Type, ExponentPowerValue, TConvertClass, CIntegralType>
{
    VFC_STATIC_ASSERT(
        (vfc::TIsSameType<typename Unit1Type::si_base_unit_type, typename Unit2Type::si_base_unit_type>::value != 0));

    static const integer_type NumExp1 = TConvertClass<Unit1Type, Unit2Type>::NUM;
    static const integer_type DenExp1 = TConvertClass<Unit1Type, Unit2Type>::DEN;

    using Fraction_t = TFractionPower<NumExp1, DenExp1, ExponentPowerValue>;

    static const integer_type NUM = Fraction_t::NUMERATOR;
    static const integer_type DEN = Fraction_t::DENOMINATOR;
};

///
///
///@tparam UnitInfo1Type - SiUnit type for conversion
///@tparam UnitInfo2Type - SiUnit type for conversion
///@tparam DataType - data type for conversion
///
template <class UnitInfo1Type, class UnitInfo2Type, class DataType>
struct TNoConvert
{
    // Conversion from one type to another conversion is illegal when using TNoConvert
    VFC_STATIC_ASSERT(UnitInfo1Type::LENGTH_PREFIX_VALUE == UnitInfo2Type::LENGTH_PREFIX_VALUE);
    VFC_STATIC_ASSERT(UnitInfo1Type::MASS_PREFIX_VALUE == UnitInfo2Type::MASS_PREFIX_VALUE);
    VFC_STATIC_ASSERT(UnitInfo1Type::TIME_PREFIX_VALUE == UnitInfo2Type::TIME_PREFIX_VALUE);
    VFC_STATIC_ASSERT(UnitInfo1Type::CURRENT_PREFIX_VALUE == UnitInfo2Type::CURRENT_PREFIX_VALUE);
    VFC_STATIC_ASSERT(UnitInfo1Type::TEMPERATURE_PREFIX_VALUE == UnitInfo2Type::TEMPERATURE_PREFIX_VALUE);
    VFC_STATIC_ASSERT(UnitInfo1Type::AMOUNTOFSUBSTANCE_PREFIX_VALUE == UnitInfo2Type::AMOUNTOFSUBSTANCE_PREFIX_VALUE);
    VFC_STATIC_ASSERT(UnitInfo1Type::LUMINOUSINTENSITY_PREFIX_VALUE == UnitInfo2Type::LUMINOUSINTENSITY_PREFIX_VALUE);
    VFC_STATIC_ASSERT(UnitInfo1Type::ANGLE_PREFIX_VALUE == UnitInfo2Type::ANGLE_PREFIX_VALUE);

    template <class ValueType>
    inline static void performValueConversion(ValueType&)
    {
    }
};

///
///
///@tparam Unit1Type - SiUnit type for conversion
///@tparam Unit2Type - SiUnit type for conversion
///@tparam DataType - data type for conversion
///
template <class Unit1Type, class Unit2Type, class DataType>
class TDeduceOffset
{
  public:
    // base type for both siunits should be same
    VFC_STATIC_ASSERT(
        (vfc::TIsSameType<typename Unit1Type::si_base_unit_type, typename Unit2Type::si_base_unit_type>::value == 1));

    // check for the integer data type
    enum
    {
        INTEGER_DATA_TYPE = vfc::TIsSameType<DataType, CIntegralType>::value
    };

    // calculation of integer offset
    static const integer_type OFFSET =
        ((INTEGER_DATA_TYPE == 1) ?
                                  // if siunit type is integer, then calculate the difference of integer OFFSET
             (Unit1Type::OFFSET - Unit2Type::OFFSET)
                                  :
                                  // else check if both offsets are float, then resultant integer offset is zero
             (((Unit1Type::FLOATING_OFFSET == 1) && (Unit2Type::FLOATING_OFFSET == 1))
                  ? 0
                  :
                  // else check if unit1type's offset is float, then make it zero, and take the difference
                  ((Unit1Type::FLOATING_OFFSET == 1)
                       ? (0 - Unit2Type::OFFSET)
                       :
                       // else check if unit2type's offset is float, then make it zero, and take the difference
                       ((Unit2Type::FLOATING_OFFSET == 1) ? (Unit1Type::OFFSET - 0) :
                                                          // else calculate the difference of both integer OFFSET
                            (Unit1Type::OFFSET - Unit2Type::OFFSET)))));
};
///
///
///@tparam UnitInfo1Type - SiUnit type for conversion
///@tparam UnitInfo2Type - SiUnit type for conversion
///@tparam DataType - data type for conversion
///
template <class UnitInfo1Type, class UnitInfo2Type, class DataType>
class TSumOffsets
{
    template <EUnitDomain domain>
    using deduceOffset = TIntegralConstant<
        int32_t,
        domainPower_t<domain, UnitInfo1Type>::value == 0
            ? 0
            : TDeduceOffset<domainUnit_t<domain, UnitInfo1Type>, domainUnit_t<domain, UnitInfo2Type>, DataType>::
                  OFFSET>;

  public:
    // Summation of all integer offset
    static const integer_type OFFSET =
        (deduceOffset<EUnitDomain::Length>::value + deduceOffset<EUnitDomain::Mass>::value +
         deduceOffset<EUnitDomain::Time>::value + deduceOffset<EUnitDomain::Current>::value +
         deduceOffset<EUnitDomain::Temperature>::value + deduceOffset<EUnitDomain::AmountOfSubstance>::value +
         deduceOffset<EUnitDomain::LuminousIntensity>::value + deduceOffset<EUnitDomain::Angle>::value +
         deduceOffset<EUnitDomain::Unit>::value);
};

///
///
///@tparam UnitInfo1Type - SiUnit type for conversion
///@tparam UnitInfo2Type - SiUnit type for conversion
///
template <class UnitInfo1Type, class UnitInfo2Type>
class TSICompatibleCheck
{
  public:
    static void check()
    {
        // static asserts for verifying if the powers match
        VFC_STATIC_ASSERT(UnitInfo1Type::LENGTH_POWER_VALUE == UnitInfo2Type::LENGTH_POWER_VALUE);
        VFC_STATIC_ASSERT(UnitInfo1Type::TIME_POWER_VALUE == UnitInfo2Type::TIME_POWER_VALUE);
        VFC_STATIC_ASSERT(UnitInfo1Type::MASS_POWER_VALUE == UnitInfo2Type::MASS_POWER_VALUE);
        VFC_STATIC_ASSERT(UnitInfo1Type::CURRENT_POWER_VALUE == UnitInfo2Type::CURRENT_POWER_VALUE);
        VFC_STATIC_ASSERT(UnitInfo1Type::TEMPERATURE_POWER_VALUE == UnitInfo2Type::TEMPERATURE_POWER_VALUE);
        VFC_STATIC_ASSERT(UnitInfo1Type::AMOUNTOFSUBSTANCE_POWER_VALUE == UnitInfo2Type::AMOUNTOFSUBSTANCE_POWER_VALUE);
        VFC_STATIC_ASSERT(UnitInfo1Type::LUMINOUSINTENSITY_POWER_VALUE == UnitInfo2Type::LUMINOUSINTENSITY_POWER_VALUE);
        VFC_STATIC_ASSERT(UnitInfo1Type::ANGLE_POWER_VALUE == UnitInfo2Type::ANGLE_POWER_VALUE);
    }
};

///
///
///@tparam UnitInfo1Type - SiUnit type for conversion
///@tparam UnitInfo2Type - SiUnit type for conversion
///@tparam DataType - data type for conversion
///
template <class UnitInfo1Type, class UnitInfo2Type, class DataType>
class TConvert
{
  public:
    template <class ValueType>
    inline static void performValueConversion(ValueType& f_value_r)
    {
        self_type::performValueConversionIntern(f_value_r, DataType());
    }

  private:
    using Check_t = TSICompatibleCheck<UnitInfo1Type, UnitInfo2Type>;
    enum
    {
        IS_CHECKED = sizeof(TSICompatibleCheck<UnitInfo1Type, UnitInfo2Type>)
    };

    using self_type = TConvert<UnitInfo1Type, UnitInfo2Type, DataType>;

    static const integer_type RESULTANT_LENGTH =
        UnitInfo1Type::LENGTH_PREFIX_VALUE - UnitInfo2Type::LENGTH_PREFIX_VALUE;

    static const integer_type RESULTANT_MASS = UnitInfo1Type::MASS_PREFIX_VALUE - UnitInfo2Type::MASS_PREFIX_VALUE;

    static const integer_type RESULTANT_CURRENT =
        UnitInfo1Type::CURRENT_PREFIX_VALUE - UnitInfo2Type::CURRENT_PREFIX_VALUE;

    static const integer_type RESULTANT_TEMPERATURE =
        UnitInfo1Type::TEMPERATURE_PREFIX_VALUE - UnitInfo2Type::TEMPERATURE_PREFIX_VALUE;

    static const integer_type RESULTANT_AMOUNTOFSUBSTANCE =
        UnitInfo1Type::AMOUNTOFSUBSTANCE_PREFIX_VALUE - UnitInfo2Type::AMOUNTOFSUBSTANCE_PREFIX_VALUE;

    static const integer_type RESULTANT_LUMINOUSINTENSITY =
        UnitInfo1Type::LUMINOUSINTENSITY_PREFIX_VALUE - UnitInfo2Type::LUMINOUSINTENSITY_PREFIX_VALUE;

    static const integer_type RESULTANT_ANGLE = UnitInfo1Type::ANGLE_PREFIX_VALUE - UnitInfo2Type::ANGLE_PREFIX_VALUE;

    // Note: TIME_POWER_VALUE is missing as it is handled by TConvertTime
    static const integer_type POWER_VALUE =
        (self_type::RESULTANT_LENGTH * UnitInfo1Type::LENGTH_POWER_VALUE) +
        (self_type::RESULTANT_MASS * UnitInfo1Type::MASS_POWER_VALUE) +
        (self_type::RESULTANT_CURRENT * UnitInfo1Type::CURRENT_POWER_VALUE) +
        (self_type::RESULTANT_TEMPERATURE * UnitInfo1Type::TEMPERATURE_POWER_VALUE) +
        (self_type::RESULTANT_AMOUNTOFSUBSTANCE * UnitInfo1Type::AMOUNTOFSUBSTANCE_POWER_VALUE) +
        (self_type::RESULTANT_LUMINOUSINTENSITY * UnitInfo1Type::LUMINOUSINTENSITY_POWER_VALUE) +
        (self_type::RESULTANT_ANGLE * UnitInfo1Type::ANGLE_POWER_VALUE);

    static const integer_type OFFSET = TSumOffsets<UnitInfo1Type, UnitInfo2Type, DataType>::OFFSET;

    using LengthConversion_t = TUnitConversion<
        typename UnitInfo1Type::length_unit_type,
        typename UnitInfo2Type::length_unit_type,
        UnitInfo1Type::LENGTH_POWER_VALUE,
        TBasicTypeConvert,
        DataType>;
    using MassConversion_t = TUnitConversion<
        typename UnitInfo1Type::mass_unit_type,
        typename UnitInfo2Type::mass_unit_type,
        UnitInfo1Type::MASS_POWER_VALUE,
        TBasicTypeConvert,
        DataType>;
    using TimeConversion_t = TUnitConversion<
        typename UnitInfo1Type::time_unit_type,
        typename UnitInfo2Type::time_unit_type,
        UnitInfo1Type::TIME_POWER_VALUE,
        TBasicTypeConvert,
        DataType>;
    using CurrentConversion_t = TUnitConversion<
        typename UnitInfo1Type::current_unit_type,
        typename UnitInfo2Type::current_unit_type,
        UnitInfo1Type::CURRENT_POWER_VALUE,
        TBasicTypeConvert,
        DataType>;
    using TemperatureConversion_t = TUnitConversion<
        typename UnitInfo1Type::temperature_unit_type,
        typename UnitInfo2Type::temperature_unit_type,
        UnitInfo1Type::TEMPERATURE_POWER_VALUE,
        TBasicTypeConvert,
        DataType>;
    using AmountOfSubstanceConversion_t = TUnitConversion<
        typename UnitInfo1Type::amountofsubstance_unit_type,
        typename UnitInfo2Type::amountofsubstance_unit_type,
        UnitInfo1Type::AMOUNTOFSUBSTANCE_POWER_VALUE,
        TBasicTypeConvert,
        DataType>;
    using LuminousIntensityConversion_t = TUnitConversion<
        typename UnitInfo1Type::luminousintensity_unit_type,
        typename UnitInfo2Type::luminousintensity_unit_type,
        UnitInfo1Type::LUMINOUSINTENSITY_POWER_VALUE,
        TBasicTypeConvert,
        DataType>;
    using AngleConversion_t = TUnitConversion<
        typename UnitInfo1Type::angle_unit_type,
        typename UnitInfo2Type::angle_unit_type,
        UnitInfo1Type::ANGLE_POWER_VALUE,
        TBasicTypeConvert,
        DataType>;
    using TimeSpecialConversion_t = sitime::TConvertTime<
        UnitInfo1Type::TIME_POWER_VALUE,
        UnitInfo1Type::TIME_PREFIX_VALUE,
        UnitInfo2Type::TIME_PREFIX_VALUE>;
    using ComplexConversion_t = TUnitConversion<
        typename UnitInfo1Type::unit_type,
        typename UnitInfo2Type::unit_type,
        1,
        TComplexConvert,
        DataType>;

    // multiply and factorize all occuring constant integer factors from the conversions
    using Factorized_t = TFactorizeInputs<
        LengthConversion_t::NUM,
        LengthConversion_t::DEN,
        MassConversion_t::NUM,
        MassConversion_t::DEN,
        TimeConversion_t::NUM,
        TimeConversion_t::DEN,
        CurrentConversion_t::NUM,
        CurrentConversion_t::DEN,
        TemperatureConversion_t::NUM,
        TemperatureConversion_t::DEN,
        AmountOfSubstanceConversion_t::NUM,
        AmountOfSubstanceConversion_t::DEN,
        LuminousIntensityConversion_t::NUM,
        LuminousIntensityConversion_t::DEN,
        AngleConversion_t::NUM,
        AngleConversion_t::DEN,
        TimeSpecialConversion_t::NUM,
        TimeSpecialConversion_t::DEN, // handles minute/hour/day
        // summary power factors
        TPower<10, TNumDenPower<self_type::POWER_VALUE>::NUM>::value_ui64,
        TPower<10, TNumDenPower<self_type::POWER_VALUE>::DEN>::value_ui64,
        ComplexConversion_t::NUM,
        ComplexConversion_t::DEN>;

    static const integer_type NUM = Factorized_t::NUM;
    static const integer_type DEN = Factorized_t::DEN;

    // verify & assert if types involve both OFFSET & FACTORIZATION calculation.
    // example Kilogram/Celsius to Gram/Kelvin
    VFC_STATIC_ASSERT(!((OFFSET != 0) && ((NUM != 1) || (DEN != 1))));

    // for integers
    template <class ValueType>
    inline static void performValueConversionIntern(ValueType& f_value_r, CIntegralType)
    {
        // function to perform factorization calculation
        performValueIntegralConversionIntern(
            f_value_r, typename vfc::TInt2Boolean<(self_type::NUM == self_type::DEN)>::type());
        // function to perform offset calculation
        performValueIntegralOffsetIntern(f_value_r, typename vfc::TInt2Boolean<(self_type::OFFSET == 0)>::type());
    }

    template <class ValueType>
    inline static void performValueIntegralOffsetIntern(ValueType&, vfc::true_t)
    {
        // intentionally left blank
    }

    template <class ValueType>
    inline static void performValueIntegralOffsetIntern(ValueType& f_value_r, vfc::false_t)
    {
        f_value_r += OFFSET;
    }

    // for integers
    template <class ValueType>
    inline static void performValueIntegralConversionIntern(ValueType&, vfc::true_t)
    {
        // intentionally left blank
    }

    // for integers
    template <class ValueType>
    inline static void performValueIntegralConversionIntern(ValueType& f_value_r, vfc::false_t)
    {
        // verify & assert if there is an overflow
        static_assert(
            numeric_limits<ValueType>::is_specialized, "value type must have proper specialization of numeric limits");

        VFC_ASSERT((vfc::abs(f_value_r) < ((vfc::numeric_limits<ValueType>::max)() / static_cast<ValueType>(NUM))));

        doMultiplyNum(f_value_r, typename vfc::TInt2Boolean<(self_type::NUM != 1)>::type());

        doDivideDen(f_value_r, typename vfc::TInt2Boolean<(self_type::DEN != 1)>::type());
    }

    template <class ValueType>
    inline static void doMultiplyNum(ValueType& f_value_r, vfc::true_t)
    {
        f_value_r *= static_cast<ValueType>(self_type::NUM);
    }

    template <class ValueType>
    inline static void doMultiplyNum(ValueType&, vfc::false_t)
    {
        // intentionally left blank
    }

    template <class ValueType>
    inline static void doDivideDen(ValueType& f_value_r, vfc::true_t)
    {
        f_value_r /= static_cast<ValueType>(self_type::DEN);
    }

    template <class ValueType>
    inline static void doDivideDen(ValueType&, vfc::false_t)
    {
        // intentionally left blank
    }

    // for floating
    template <class ValueType>
    inline static void performValueConversionIntern(ValueType& f_value_r, CFloatingType)
    {
        // function to perform factorization calculation
        performValueFloatingConversionIntern(
            f_value_r, typename vfc::TInt2Boolean<(self_type::NUM == self_type::DEN)>::type());

        // function to perform offset calculation
        performValueFloatingOffsetIntern(f_value_r, typename vfc::TInt2Boolean<(self_type::OFFSET == 0)>::type());
    }

    // for integers
    template <class ValueType>
    inline static void performValueFloatingConversionIntern(ValueType& f_value_r, vfc::true_t)
    {
        self_type::performVaraiantConversion(f_value_r);
        self_type::performComplexConversion(f_value_r);
    }

    // for integers
    template <class ValueType>
    inline static void performValueFloatingConversionIntern(ValueType& f_value_r, vfc::false_t)
    {
        const integer_type num = self_type::NUM;
        const integer_type den = self_type::DEN;

        f_value_r *=
            (static_cast<ValueType>(num) / // PRQA S 2427 # Technical debt L2 id=00c1664679e499564bf8-4266459913219932
             static_cast<ValueType>(den)); // PRQA S 2427 # Technical debt L2 id=00c17f0756a865ea4238-e77b157bfe85dc88

        self_type::performVaraiantConversion(f_value_r);
        self_type::performComplexConversion(f_value_r);
    }

    template <class ValueType>
    inline static void performValueFloatingOffsetIntern(ValueType&, vfc::true_t)
    {
        // intentionally left blank
    }

    template <class ValueType>
    inline static void performValueFloatingOffsetIntern(ValueType& f_value_r, vfc::false_t)
    {
        f_value_r += static_cast<ValueType>(OFFSET);
    }

    template <class ValueType>
    inline static void performVaraiantConversion(ValueType& f_value_r)
    {
        LengthConversion_t::performConversion(f_value_r);
        MassConversion_t::performConversion(f_value_r);
        TimeConversion_t::performConversion(f_value_r);
        CurrentConversion_t::performConversion(f_value_r);
        TemperatureConversion_t::performConversion(f_value_r);
        AmountOfSubstanceConversion_t::performConversion(f_value_r);
        LuminousIntensityConversion_t::performConversion(f_value_r);
        AngleConversion_t::performConversion(f_value_r);
    }

    template <class ValueType>
    inline static void performComplexConversion(ValueType& f_value_r)
    {
        ComplexConversion_t::performConversion(f_value_r);
    }
};
///
///
///@tparam RBInfo1Type - RB special class type
///@tparam RBInfo2Type - RB special class type
///@tparam DataType - data type for conversion
///
template <class RBInfo1Type, class RBInfo2Type, class DataType>
class TRBConvert
{
  public:
    using self_type = TRBConvert<RBInfo1Type, RBInfo2Type, DataType>;

    // static asserts for verifying if the powers match
    VFC_STATIC_ASSERT(RBInfo1Type::PIXEL_POWER_VALUE == RBInfo2Type::PIXEL_POWER_VALUE);
    VFC_STATIC_ASSERT(RBInfo1Type::PERCENTAGE_POWER_VALUE == RBInfo2Type::PERCENTAGE_POWER_VALUE);

    static const integer_type RESULTANT_PIXLE = RBInfo1Type::PIXEL_PREFIX_VALUE - RBInfo2Type::PIXEL_PREFIX_VALUE;

    static const integer_type RESULTANT_PERCENTAGE =
        RBInfo1Type::PERCENTAGE_PREFIX_VALUE - RBInfo2Type::PERCENTAGE_PREFIX_VALUE;

    static const integer_type POWER_VALUE = (self_type::RESULTANT_PIXLE * RBInfo1Type::PIXEL_POWER_VALUE) +
                                            (self_type::RESULTANT_PERCENTAGE * RBInfo1Type::PERCENTAGE_POWER_VALUE);

    template <class ValueType>
    inline static void performValueConversion(ValueType&)
    {
    }
};

///
///
///@tparam LengthUnitType - unit type for length
///@tparam LengthPrefixValue - value for the length prefix
///@tparam LengthPowerValue - value for the length power
///@tparam MassUnitType - unit type for mass
///@tparam MassPrefixValue - value for the mass prefix
///@tparam MassPowerValue - value for the mass power
///@tparam TimeUnitType - unit type for time
///@tparam TimePrefixValue - value for the time prefix
///@tparam TimePowerValue - value for the time power
///@tparam TemperatureUnitType - unit type for temperature
///@tparam TemperaturePrefixValue - value for temperature prefix
///@tparam TemperaturePowerValue - value for temperature power
///@tparam AmountOfSubstanceUnitType - unit type for the amount of substance
///@tparam AmountOfSubstancePrefixValue - value for the amount of substance prefix
///@tparam AmountOfSubstancePowerValue - value for the amount of substance power
///@tparam LuminousIntensityUnitType - unit type for the luminous intensity
///@tparam LuminousIntensityPrefixValue - value for the luminous intensity prefix
///@tparam LuminousIntensityPowerValue - value for the luminous intensity power
///@tparam AngleUnitType - unit type for the angle
///@tparam AnglePrefixValue - value for the angle prefix
///@tparam AnglePowerValue - value for the angle power
///@tparam CBasicType - Basic type
///
template <
    class LengthUnitType,
    vfc::int32_t LengthPrefixValue,
    vfc::int32_t LengthPowerValue,
    class MassUnitType,
    vfc::int32_t MassPrefixValue,
    vfc::int32_t MassPowerValue,
    class TimeUnitType,
    vfc::int32_t TimePrefixValue,
    vfc::int32_t TimePowerValue,
    class CurrentUnitType,
    vfc::int32_t CurrentPrefixValue,
    vfc::int32_t CurrentPowerValue,
    class TemperatureUnitType,
    vfc::int32_t TemperaturePrefixValue,
    vfc::int32_t TemperaturePowerValue,
    class AmountOfSubstanceUnitType,
    vfc::int32_t AmountOfSubstancePrefixValue,
    vfc::int32_t AmountOfSubstancePowerValue,
    class LuminousIntensityUnitType,
    vfc::int32_t LuminousIntensityPrefixValue,
    vfc::int32_t LuminousIntensityPowerValue,
    class AngleUnitType,
    vfc::int32_t AnglePrefixValue,
    vfc::int32_t AnglePowerValue,
    class UnitType = CBasicType>
class TUnitInfoType
{
  public:
    using self_unit_type = TUnitInfoType<
        LengthUnitType,
        LengthPrefixValue,
        LengthPowerValue,
        MassUnitType,
        MassPrefixValue,
        MassPowerValue,
        TimeUnitType,
        TimePrefixValue,
        TimePowerValue,
        CurrentUnitType,
        CurrentPrefixValue,
        CurrentPowerValue,
        TemperatureUnitType,
        TemperaturePrefixValue,
        TemperaturePowerValue,
        AmountOfSubstanceUnitType,
        AmountOfSubstancePrefixValue,
        AmountOfSubstancePowerValue,
        LuminousIntensityUnitType,
        LuminousIntensityPrefixValue,
        LuminousIntensityPowerValue,
        AngleUnitType,
        AnglePrefixValue,
        AnglePowerValue>;

    using base_unit_type = TUnitInfoType<
        LengthUnitType,
        LengthPrefixValue,
        LengthPowerValue,
        MassUnitType,
        MassPrefixValue,
        MassPowerValue,
        TimeUnitType,
        TimePrefixValue,
        TimePowerValue,
        CurrentUnitType,
        CurrentPrefixValue,
        CurrentPowerValue,
        TemperatureUnitType,
        TemperaturePrefixValue,
        TemperaturePowerValue,
        AmountOfSubstanceUnitType,
        AmountOfSubstancePrefixValue,
        AmountOfSubstancePowerValue,
        LuminousIntensityUnitType,
        LuminousIntensityPrefixValue,
        LuminousIntensityPowerValue,
        AngleUnitType,
        AnglePrefixValue,
        AnglePowerValue,
        UnitType>;

    enum
    {
        SI_UNIT = 1
    };

    enum
    {
        SI_FUNDAMENTAL = vfc::TIsSameType<self_unit_type, base_unit_type>::value
    };

    using length_unit_type            = LengthUnitType;
    using mass_unit_type              = MassUnitType;
    using time_unit_type              = TimeUnitType;
    using current_unit_type           = CurrentUnitType;
    using temperature_unit_type       = TemperatureUnitType;
    using amountofsubstance_unit_type = AmountOfSubstanceUnitType;
    using luminousintensity_unit_type = LuminousIntensityUnitType;
    using angle_unit_type             = AngleUnitType;

    using unit_type = UnitType;

    static const vfc::int32_t LENGTH_PREFIX_VALUE = LengthPrefixValue;
    static const vfc::int32_t LENGTH_POWER_VALUE  = LengthPowerValue;

    static const vfc::int32_t MASS_PREFIX_VALUE = MassPrefixValue;
    static const vfc::int32_t MASS_POWER_VALUE  = MassPowerValue;

    static const vfc::int32_t TIME_PREFIX_VALUE = TimePrefixValue;
    static const vfc::int32_t TIME_POWER_VALUE  = TimePowerValue;

    static const vfc::int32_t CURRENT_PREFIX_VALUE = CurrentPrefixValue;
    static const vfc::int32_t CURRENT_POWER_VALUE  = CurrentPowerValue;

    static const vfc::int32_t TEMPERATURE_PREFIX_VALUE = TemperaturePrefixValue;
    static const vfc::int32_t TEMPERATURE_POWER_VALUE  = TemperaturePowerValue;

    static const vfc::int32_t AMOUNTOFSUBSTANCE_PREFIX_VALUE = AmountOfSubstancePrefixValue;
    static const vfc::int32_t AMOUNTOFSUBSTANCE_POWER_VALUE  = AmountOfSubstancePowerValue;

    static const vfc::int32_t LUMINOUSINTENSITY_PREFIX_VALUE = LuminousIntensityPrefixValue;
    static const vfc::int32_t LUMINOUSINTENSITY_POWER_VALUE  = LuminousIntensityPowerValue;

    static const vfc::int32_t ANGLE_PREFIX_VALUE = AnglePrefixValue;
    static const vfc::int32_t ANGLE_POWER_VALUE  = AnglePowerValue;
};

// partial specializations of TDomainInfosExtractor for all SI domains

template <class UnitInfoType>
struct TDomainInfosExtractor<EUnitDomain::Length, UnitInfoType> : TDomainInfos<
                                                                      typename UnitInfoType::length_unit_type,
                                                                      UnitInfoType::LENGTH_PREFIX_VALUE,
                                                                      UnitInfoType::LENGTH_POWER_VALUE>
{
};

template <class UnitInfoType>
struct TDomainInfosExtractor<EUnitDomain::Mass, UnitInfoType> : TDomainInfos<
                                                                    typename UnitInfoType::mass_unit_type,
                                                                    UnitInfoType::MASS_PREFIX_VALUE,
                                                                    UnitInfoType::MASS_POWER_VALUE>
{
};

template <class UnitInfoType>
struct TDomainInfosExtractor<EUnitDomain::Time, UnitInfoType> : TDomainInfos<
                                                                    typename UnitInfoType::time_unit_type,
                                                                    UnitInfoType::TIME_PREFIX_VALUE,
                                                                    UnitInfoType::TIME_POWER_VALUE>
{
};

template <class UnitInfoType>
struct TDomainInfosExtractor<EUnitDomain::Current, UnitInfoType> : TDomainInfos<
                                                                       typename UnitInfoType::current_unit_type,
                                                                       UnitInfoType::CURRENT_PREFIX_VALUE,
                                                                       UnitInfoType::CURRENT_POWER_VALUE>
{
};

template <class UnitInfoType>
struct TDomainInfosExtractor<EUnitDomain::Temperature, UnitInfoType> : TDomainInfos<
                                                                           typename UnitInfoType::temperature_unit_type,
                                                                           UnitInfoType::TEMPERATURE_PREFIX_VALUE,
                                                                           UnitInfoType::TEMPERATURE_POWER_VALUE>
{
};

template <class UnitInfoType>
struct TDomainInfosExtractor<EUnitDomain::AmountOfSubstance, UnitInfoType>
    : TDomainInfos<
          typename UnitInfoType::amountofsubstance_unit_type,
          UnitInfoType::AMOUNTOFSUBSTANCE_PREFIX_VALUE,
          UnitInfoType::AMOUNTOFSUBSTANCE_POWER_VALUE>
{
};

template <class UnitInfoType>
struct TDomainInfosExtractor<EUnitDomain::LuminousIntensity, UnitInfoType>
    : TDomainInfos<
          typename UnitInfoType::luminousintensity_unit_type,
          UnitInfoType::LUMINOUSINTENSITY_PREFIX_VALUE,
          UnitInfoType::LUMINOUSINTENSITY_POWER_VALUE>
{
};

template <class UnitInfoType>
struct TDomainInfosExtractor<EUnitDomain::Angle, UnitInfoType> : TDomainInfos<
                                                                     typename UnitInfoType::angle_unit_type,
                                                                     UnitInfoType::ANGLE_PREFIX_VALUE,
                                                                     UnitInfoType::ANGLE_POWER_VALUE>
{
};

template <class UnitInfoType>
struct TDomainInfosExtractor<EUnitDomain::Unit, UnitInfoType> : TDomainInfos<typename UnitInfoType::unit_type, 0, 0>
{
};

class CPercentage
{
};

///
///
///@tparam PixelUnitType - Pixel unit type
///@tparam PixelPrefixValue - prefix for pixel value
///@tparam PixelPowerValue - pixel power value
///@tparam PercentageUnitType - percentage unit type
///@tparam PercentagePrefixValue - percentage prefix value
///@tparam PercentagePowerValue - percentage power value
///@tparam CBasicType - basic type
///
template <
    class PixelUnitType,
    vfc::int32_t PixelPrefixValue,
    vfc::int32_t PixelPowerValue,
    class PercentageUnitType,
    vfc::int32_t PercentagePrefixValue,
    vfc::int32_t PercentagePowerValue,
    class UnitType = CBasicType>
class TRBInfoType
{
  public:
    using self_unit_type = TRBInfoType<
        PixelUnitType,
        PixelPrefixValue,
        PixelPowerValue,
        PercentageUnitType,
        PercentagePrefixValue,
        PercentagePowerValue>;

    using base_unit_type = TRBInfoType<
        PixelUnitType,
        PixelPrefixValue,
        PixelPowerValue,
        PercentageUnitType,
        PercentagePrefixValue,
        PercentagePowerValue,
        UnitType>;

    enum
    {
        SI_UNIT = 1
    };

    enum
    {
        SI_FUNDAMENTAL = vfc::TIsSameType<self_unit_type, base_unit_type>::value
    };

    using pixel_unit_type      = PixelUnitType;
    using percentage_unit_type = PercentageUnitType;
    using unit_type            = UnitType;

    static const vfc::int32_t PIXEL_PREFIX_VALUE      = PixelPrefixValue;
    static const vfc::int32_t PIXEL_POWER_VALUE       = PixelPowerValue;
    static const vfc::int32_t PERCENTAGE_PREFIX_VALUE = PercentagePrefixValue;
    static const vfc::int32_t PERCENTAGE_POWER_VALUE  = PercentagePowerValue;
};

// partial specializations of TDomainInfosExtractor for all RB domains

template <class UnitInfoType>
struct TDomainInfosExtractor<EUnitDomain::Pixel, UnitInfoType> : TDomainInfos<
                                                                     typename UnitInfoType::pixel_unit_type,
                                                                     UnitInfoType::PIXEL_PREFIX_VALUE,
                                                                     UnitInfoType::PIXEL_POWER_VALUE>
{
};

template <class UnitInfoType>
struct TDomainInfosExtractor<EUnitDomain::Percentage, UnitInfoType> : TDomainInfos<
                                                                          typename UnitInfoType::percentage_unit_type,
                                                                          UnitInfoType::PERCENTAGE_PREFIX_VALUE,
                                                                          UnitInfoType::PERCENTAGE_POWER_VALUE>
{
};

struct CBar
{
    enum
    {
        CONVERSION_RATIONAL = 1,
        FLOATING_OFFSET     = 0
    };

    static const integer_type NUM    = 10000;
    static const integer_type DEN    = 1;
    static const integer_type OFFSET = 0;
};

struct CGForce
{
    enum
    {
        CONVERSION_RATIONAL = 1,
        FLOATING_OFFSET     = 0
    };

    static const integer_type NUM    = 981;
    static const integer_type DEN    = 100;
    static const integer_type OFFSET = 0;
};

// si-unit operation sine and cosine must use unit promotion
///
///
///@tparam RBInfoType - information about the unitinfotype
///
template <class RBInfoType>
struct TSIUnitRBSinCosPromote
{
    using unit_info_type = vfc::TRBInfoType<
        typename RBInfoType::pixel_unit_type,
        RBInfoType::PIXEL_PREFIX_VALUE,
        (RBInfoType::PIXEL_POWER_VALUE),
        typename RBInfoType::percentage_unit_type,
        RBInfoType::PERCENTAGE_PREFIX_VALUE,
        (RBInfoType::PERCENTAGE_POWER_VALUE),
        typename RBInfoType::unit_type>;
};

// si-unit operation sine and cosine must use unit promotion
///
///
///@tparam UnitInfo1Type - information about the unitinfotype
///
template <class UnitInfo1Type>
struct TSIUnitSinCosPromote
{
    using unit_info_type = vfc::TUnitInfoType<
        typename UnitInfo1Type::length_unit_type,
        UnitInfo1Type::LENGTH_PREFIX_VALUE,
        (UnitInfo1Type::LENGTH_POWER_VALUE),
        typename UnitInfo1Type::mass_unit_type,
        UnitInfo1Type::MASS_PREFIX_VALUE,
        (UnitInfo1Type::MASS_POWER_VALUE),
        typename UnitInfo1Type::time_unit_type,
        UnitInfo1Type::TIME_PREFIX_VALUE,
        (UnitInfo1Type::TIME_POWER_VALUE),
        typename UnitInfo1Type::current_unit_type,
        UnitInfo1Type::CURRENT_PREFIX_VALUE,
        (UnitInfo1Type::CURRENT_POWER_VALUE),
        typename UnitInfo1Type::temperature_unit_type,
        UnitInfo1Type::TEMPERATURE_PREFIX_VALUE,
        (UnitInfo1Type::TEMPERATURE_POWER_VALUE),
        typename UnitInfo1Type::amountofsubstance_unit_type,
        UnitInfo1Type::AMOUNTOFSUBSTANCE_PREFIX_VALUE,
        (UnitInfo1Type::AMOUNTOFSUBSTANCE_POWER_VALUE),
        typename UnitInfo1Type::luminousintensity_unit_type,
        UnitInfo1Type::LUMINOUSINTENSITY_PREFIX_VALUE,
        (UnitInfo1Type::LUMINOUSINTENSITY_POWER_VALUE),
        typename UnitInfo1Type::angle_unit_type,
        UnitInfo1Type::ANGLE_PREFIX_VALUE,
        (UnitInfo1Type::ANGLE_POWER_VALUE - 1), // turns from angle to slope
        typename UnitInfo1Type::unit_type>;
};

// si-unit operation arcus tangent must use unit promotion
///
///
///@tparam RBInfoType - information about the RB infotype
///
template <class RBInfoType>
struct TSIUnitRBAtanPromote
{
    using unit_info_type = vfc::TRBInfoType<
        typename RBInfoType::pixel_unit_type,
        RBInfoType::PIXEL_PREFIX_VALUE,
        (RBInfoType::PIXEL_POWER_VALUE),
        typename RBInfoType::percentage_unit_type,
        RBInfoType::PERCENTAGE_PREFIX_VALUE,
        (RBInfoType::PERCENTAGE_POWER_VALUE),
        typename RBInfoType::unit_type>;
};

// si-unit operation arcus tangent must use unit promotion
///
///
///@tparam UnitInfo1Type - information about the unitinfotype
///
template <class UnitInfo1Type>
struct TSIUnitAtanPromote
{
    using unit_info_type = vfc::TUnitInfoType<
        typename UnitInfo1Type::length_unit_type,
        UnitInfo1Type::LENGTH_PREFIX_VALUE,
        (UnitInfo1Type::LENGTH_POWER_VALUE),
        typename UnitInfo1Type::mass_unit_type,
        UnitInfo1Type::MASS_PREFIX_VALUE,
        (UnitInfo1Type::MASS_POWER_VALUE),
        typename UnitInfo1Type::time_unit_type,
        UnitInfo1Type::TIME_PREFIX_VALUE,
        (UnitInfo1Type::TIME_POWER_VALUE),
        typename UnitInfo1Type::current_unit_type,
        UnitInfo1Type::CURRENT_PREFIX_VALUE,
        (UnitInfo1Type::CURRENT_POWER_VALUE),
        typename UnitInfo1Type::temperature_unit_type,
        UnitInfo1Type::TEMPERATURE_PREFIX_VALUE,
        (UnitInfo1Type::TEMPERATURE_POWER_VALUE),
        typename UnitInfo1Type::amountofsubstance_unit_type,
        UnitInfo1Type::AMOUNTOFSUBSTANCE_PREFIX_VALUE,
        (UnitInfo1Type::AMOUNTOFSUBSTANCE_POWER_VALUE),
        typename UnitInfo1Type::luminousintensity_unit_type,
        UnitInfo1Type::LUMINOUSINTENSITY_PREFIX_VALUE,
        (UnitInfo1Type::LUMINOUSINTENSITY_POWER_VALUE),
        typename UnitInfo1Type::angle_unit_type,
        UnitInfo1Type::ANGLE_PREFIX_VALUE,
        (UnitInfo1Type::ANGLE_POWER_VALUE + 1), // turns from slope to angle
        typename UnitInfo1Type::unit_type>;
};

/// Alias metaprogram to determine which unit-info to choose for binary
/// choices. By default we want the first type unless is has a zero power
/// and the second type has a non-zero power. In that case we choose the
/// second type.
template <EUnitDomain domain, typename UnitInfo1Type, typename UnitInfo2Type>
using dominantUnitType_t = typename vfc::TIf<
    (domainPower_t<domain, UnitInfo1Type>::value == 0 && domainPower_t<domain, UnitInfo2Type>::value != 0),
    domainUnit_t<domain, UnitInfo2Type>,
    domainUnit_t<domain, UnitInfo1Type>>::type;

///
///
///@tparam UnitInfo1Type - information about the unitinfotype
///@tparam UnitInfo2Type - information about the unitinfotype
///
template <class UnitInfo1Type, class UnitInfo2Type>
struct TSIUnitMultiplicationPromote
{
    using unit_info_type = vfc::TUnitInfoType<
        dominantUnitType_t<EUnitDomain::Length, UnitInfo1Type, UnitInfo2Type>,
        UnitInfo1Type::LENGTH_PREFIX_VALUE,
        (UnitInfo1Type::LENGTH_POWER_VALUE + UnitInfo2Type::LENGTH_POWER_VALUE),
        dominantUnitType_t<EUnitDomain::Mass, UnitInfo1Type, UnitInfo2Type>,
        UnitInfo1Type::MASS_PREFIX_VALUE,
        (UnitInfo1Type::MASS_POWER_VALUE + UnitInfo2Type::MASS_POWER_VALUE),
        dominantUnitType_t<EUnitDomain::Time, UnitInfo1Type, UnitInfo2Type>,
        UnitInfo1Type::TIME_PREFIX_VALUE,
        (UnitInfo1Type::TIME_POWER_VALUE + UnitInfo2Type::TIME_POWER_VALUE),
        dominantUnitType_t<EUnitDomain::Current, UnitInfo1Type, UnitInfo2Type>,
        UnitInfo1Type::CURRENT_PREFIX_VALUE,
        (UnitInfo1Type::CURRENT_POWER_VALUE + UnitInfo2Type::CURRENT_POWER_VALUE),
        dominantUnitType_t<EUnitDomain::Temperature, UnitInfo1Type, UnitInfo2Type>,
        UnitInfo1Type::TEMPERATURE_PREFIX_VALUE,
        (UnitInfo1Type::TEMPERATURE_POWER_VALUE + UnitInfo2Type::TEMPERATURE_POWER_VALUE),
        dominantUnitType_t<EUnitDomain::AmountOfSubstance, UnitInfo1Type, UnitInfo2Type>,
        UnitInfo1Type::AMOUNTOFSUBSTANCE_PREFIX_VALUE,
        (UnitInfo1Type::AMOUNTOFSUBSTANCE_POWER_VALUE + UnitInfo2Type::AMOUNTOFSUBSTANCE_POWER_VALUE),
        dominantUnitType_t<EUnitDomain::LuminousIntensity, UnitInfo1Type, UnitInfo2Type>,
        UnitInfo1Type::LUMINOUSINTENSITY_PREFIX_VALUE,
        (UnitInfo1Type::LUMINOUSINTENSITY_POWER_VALUE + UnitInfo2Type::LUMINOUSINTENSITY_POWER_VALUE),
        dominantUnitType_t<EUnitDomain::Angle, UnitInfo1Type, UnitInfo2Type>,
        UnitInfo1Type::ANGLE_PREFIX_VALUE,
        (UnitInfo1Type::ANGLE_POWER_VALUE + UnitInfo2Type::ANGLE_POWER_VALUE) *
            (vfc::TIsMultPowAng<UnitInfo1Type, UnitInfo2Type>::value),
        typename UnitInfo1Type::unit_type>;
};
template <class UnitInfo1Type, class UnitInfo2Type>
using VFC_ATR_POST_DEPRECATED2(TSIUnitMuliplicationPromote, "use TSIUnitMultiplicationPromote (typo missing 't')") =
    TSIUnitMultiplicationPromote<UnitInfo1Type, UnitInfo2Type>;

///
///
///@tparam UnitInfo1Type - information about the unitinfotype
///@tparam UnitInfo2Type - information about the unitinfotype
///
template <class UnitInfo1Type, class UnitInfo2Type>
struct TSIUnitDivisionPromote
{
    using unit_info_type = vfc::TUnitInfoType<
        dominantUnitType_t<EUnitDomain::Length, UnitInfo1Type, UnitInfo2Type>,
        UnitInfo1Type::LENGTH_PREFIX_VALUE,
        (UnitInfo1Type::LENGTH_POWER_VALUE - UnitInfo2Type::LENGTH_POWER_VALUE),
        dominantUnitType_t<EUnitDomain::Mass, UnitInfo1Type, UnitInfo2Type>,
        UnitInfo1Type::MASS_PREFIX_VALUE,
        (UnitInfo1Type::MASS_POWER_VALUE - UnitInfo2Type::MASS_POWER_VALUE),
        dominantUnitType_t<EUnitDomain::Time, UnitInfo1Type, UnitInfo2Type>,
        UnitInfo1Type::TIME_PREFIX_VALUE,
        (UnitInfo1Type::TIME_POWER_VALUE - UnitInfo2Type::TIME_POWER_VALUE),
        dominantUnitType_t<EUnitDomain::Current, UnitInfo1Type, UnitInfo2Type>,
        UnitInfo1Type::CURRENT_PREFIX_VALUE,
        (UnitInfo1Type::CURRENT_POWER_VALUE - UnitInfo2Type::CURRENT_POWER_VALUE),
        dominantUnitType_t<EUnitDomain::Temperature, UnitInfo1Type, UnitInfo2Type>,
        UnitInfo1Type::TEMPERATURE_PREFIX_VALUE,
        (UnitInfo1Type::TEMPERATURE_POWER_VALUE - UnitInfo2Type::TEMPERATURE_POWER_VALUE),
        dominantUnitType_t<EUnitDomain::AmountOfSubstance, UnitInfo1Type, UnitInfo2Type>,
        UnitInfo1Type::AMOUNTOFSUBSTANCE_PREFIX_VALUE,
        (UnitInfo1Type::AMOUNTOFSUBSTANCE_POWER_VALUE - UnitInfo2Type::AMOUNTOFSUBSTANCE_POWER_VALUE),
        dominantUnitType_t<EUnitDomain::LuminousIntensity, UnitInfo1Type, UnitInfo2Type>,
        UnitInfo1Type::LUMINOUSINTENSITY_PREFIX_VALUE,
        (UnitInfo1Type::LUMINOUSINTENSITY_POWER_VALUE - UnitInfo2Type::LUMINOUSINTENSITY_POWER_VALUE),
        dominantUnitType_t<EUnitDomain::Angle, UnitInfo1Type, UnitInfo2Type>,
        UnitInfo1Type::ANGLE_PREFIX_VALUE,
        (UnitInfo1Type::ANGLE_POWER_VALUE - UnitInfo2Type::ANGLE_POWER_VALUE) *
            (vfc::TIsDivPowAng<UnitInfo1Type, UnitInfo2Type>::value),
        typename UnitInfo1Type::unit_type>;
};
///
///
///@tparam UnitInfoType - information about the unitinfotype
///
template <class UnitInfoType>
struct TSIUnitSqrPromote
{
    using unit_info_type = vfc::TUnitInfoType<
        typename UnitInfoType::length_unit_type,
        UnitInfoType::LENGTH_PREFIX_VALUE,
        (UnitInfoType::LENGTH_POWER_VALUE * 2),
        typename UnitInfoType::mass_unit_type,
        UnitInfoType::MASS_PREFIX_VALUE,
        (UnitInfoType::MASS_POWER_VALUE * 2),
        typename UnitInfoType::time_unit_type,
        UnitInfoType::TIME_PREFIX_VALUE,
        (UnitInfoType::TIME_POWER_VALUE * 2),
        typename UnitInfoType::current_unit_type,
        UnitInfoType::CURRENT_PREFIX_VALUE,
        (UnitInfoType::CURRENT_POWER_VALUE * 2),
        typename UnitInfoType::temperature_unit_type,
        UnitInfoType::TEMPERATURE_PREFIX_VALUE,
        (UnitInfoType::TEMPERATURE_POWER_VALUE * 2),
        typename UnitInfoType::amountofsubstance_unit_type,
        UnitInfoType::AMOUNTOFSUBSTANCE_PREFIX_VALUE,
        (UnitInfoType::AMOUNTOFSUBSTANCE_POWER_VALUE * 2),
        typename UnitInfoType::luminousintensity_unit_type,
        UnitInfoType::LUMINOUSINTENSITY_PREFIX_VALUE,
        (UnitInfoType::LUMINOUSINTENSITY_POWER_VALUE * 2),
        typename UnitInfoType::angle_unit_type,
        UnitInfoType::ANGLE_PREFIX_VALUE,
        (UnitInfoType::ANGLE_POWER_VALUE * 2),
        typename UnitInfoType::unit_type>;
};

///
///
///@tparam UnitInfoType - information about the unitinfotype
///
template <class UnitInfoType>
struct TSIUnitSqrtPromote
{
    using unit_info_type = vfc::TUnitInfoType<
        typename UnitInfoType::length_unit_type,
        UnitInfoType::LENGTH_PREFIX_VALUE,
        (UnitInfoType::LENGTH_POWER_VALUE / 2),
        typename UnitInfoType::mass_unit_type,
        UnitInfoType::MASS_PREFIX_VALUE,
        (UnitInfoType::MASS_POWER_VALUE / 2),
        typename UnitInfoType::time_unit_type,
        UnitInfoType::TIME_PREFIX_VALUE,
        (UnitInfoType::TIME_POWER_VALUE / 2),
        typename UnitInfoType::current_unit_type,
        UnitInfoType::CURRENT_PREFIX_VALUE,
        (UnitInfoType::CURRENT_POWER_VALUE / 2),
        typename UnitInfoType::temperature_unit_type,
        UnitInfoType::TEMPERATURE_PREFIX_VALUE,
        (UnitInfoType::TEMPERATURE_POWER_VALUE / 2),
        typename UnitInfoType::amountofsubstance_unit_type,
        UnitInfoType::AMOUNTOFSUBSTANCE_PREFIX_VALUE,
        (UnitInfoType::AMOUNTOFSUBSTANCE_POWER_VALUE / 2),
        typename UnitInfoType::luminousintensity_unit_type,
        UnitInfoType::LUMINOUSINTENSITY_PREFIX_VALUE,
        (UnitInfoType::LUMINOUSINTENSITY_POWER_VALUE / 2),
        typename UnitInfoType::angle_unit_type,
        UnitInfoType::ANGLE_PREFIX_VALUE,
        (UnitInfoType::ANGLE_POWER_VALUE / 2),
        typename UnitInfoType::unit_type>;
};

// si-unit of atan2(y,x) must be compatible with atan(y/x)
///
///
///@tparam UnitInfoTypeY - SiUnit rb info type y
///@tparam UnitInfoTypeX - SiUnit rb info type x
///
template <class UnitInfoTypeY, class UnitInfoTypeX>
struct TSIUnitAtan2Promote
{
    VFC_STATIC_ASSERT(
        (vfc::TIsSameType<typename UnitInfoTypeY::angle_unit_type, typename UnitInfoTypeX::angle_unit_type>::value ==
         1));

    using yx_slope_type  = typename TSIUnitDivisionPromote<UnitInfoTypeY, UnitInfoTypeX>::unit_info_type;
    using unit_info_type = typename TSIUnitAtanPromote<yx_slope_type>::unit_info_type;
};

///
///
///@tparam RBInfo1Type - SiUnit rb info type
///@tparam RBInfo2Type - SiUnit rb info type
///
template <class RBInfo1Type, class RBInfo2Type>
struct TSIUnitRBMultiplicationPromote
{
    using unit_info_type = vfc::TRBInfoType<
        dominantUnitType_t<EUnitDomain::Pixel, RBInfo1Type, RBInfo2Type>,
        RBInfo1Type::PIXEL_PREFIX_VALUE,
        (RBInfo1Type::PIXEL_POWER_VALUE + RBInfo2Type::PIXEL_POWER_VALUE),
        dominantUnitType_t<EUnitDomain::Percentage, RBInfo1Type, RBInfo2Type>,
        RBInfo1Type::PERCENTAGE_PREFIX_VALUE,
        (RBInfo1Type::PERCENTAGE_POWER_VALUE + RBInfo2Type::PERCENTAGE_POWER_VALUE),
        typename RBInfo1Type::unit_type>;
};
template <class RBInfo1Type, class RBInfo2Type>
using VFC_ATR_POST_DEPRECATED2(TSIUnitRBMuliplicationPromote, "use TSIUnitRBMuliplicationPromote (typo missing 't')") =
    TSIUnitRBMultiplicationPromote<RBInfo1Type, RBInfo2Type>;

///
///
///@tparam RBInfo1Type - SiUnit rb info type
///@tparam RBInfo2Type - SiUnit rb info type
///
template <class RBInfo1Type, class RBInfo2Type>
struct TSIUnitRBDivisionPromote
{
    using unit_info_type = vfc::TRBInfoType<
        dominantUnitType_t<EUnitDomain::Pixel, RBInfo1Type, RBInfo2Type>,
        RBInfo1Type::PIXEL_PREFIX_VALUE,
        (RBInfo1Type::PIXEL_POWER_VALUE - RBInfo2Type::PIXEL_POWER_VALUE),
        dominantUnitType_t<EUnitDomain::Percentage, RBInfo1Type, RBInfo2Type>,
        RBInfo1Type::PERCENTAGE_PREFIX_VALUE,
        (RBInfo1Type::PERCENTAGE_POWER_VALUE - RBInfo2Type::PERCENTAGE_POWER_VALUE),
        typename RBInfo1Type::unit_type>;
};

///
///
///@tparam RBInfoType - SiUnit rb info type
///
template <class RBInfoType>
struct TSIUnitRBSqrPromote
{
    using unit_info_type = vfc::TRBInfoType<
        typename RBInfoType::pixel_unit_type,
        RBInfoType::PIXEL_PREFIX_VALUE,
        (RBInfoType::PIXEL_POWER_VALUE * 2),
        typename RBInfoType::percentage_unit_type,
        RBInfoType::PERCENTAGE_PREFIX_VALUE,
        (RBInfoType::PERCENTAGE_POWER_VALUE * 2),
        typename RBInfoType::unit_type>;
};

///
///
///@tparam RBInfoType - SiUnit type for sqrt
///
template <class RBInfoType>
struct TSIUnitRBSqrtPromote
{
    using unit_info_type = vfc::TRBInfoType<
        typename RBInfoType::pixel_unit_type,
        RBInfoType::PIXEL_PREFIX_VALUE,
        (RBInfoType::PIXEL_POWER_VALUE / 2),
        typename RBInfoType::percentage_unit_type,
        RBInfoType::PERCENTAGE_PREFIX_VALUE,
        (RBInfoType::PERCENTAGE_POWER_VALUE / 2),
        typename RBInfoType::unit_type>;
};

///
///
///@tparam SIBaseUnitType - SiUnit base type
///
template <class SIBaseUnitType>
struct TSIType
{
    using si_base_unit_type = SIBaseUnitType;

    enum
    {
        CONVERSION_RATIONAL = 1,
        FLOATING_OFFSET     = 0
    };
    static const vfc::int32_t NUM    = 1;
    static const vfc::int32_t DEN    = 1;
    static const vfc::int32_t OFFSET = 0;
};

// SI base units
class CLength
{
};
class CMass
{
};
class CTime
{
};
class CCurrent
{
};
class CTemperature
{
};
class CAmountOfSubstance
{
};
class CLuminousIntensity
{
};
class CAngle
{
};

// RB Types
class CPixel
{
};

// User type for DIN70k coordinate system with base in middle of front axle
class CDinFrontType
{
};
// User type for DIN70k coordinate system with base in middle of rear axle
class CDinRearType
{
};
// User type for DIN70k coordinate system with base in projection of middle of rear axle to the ground
class CDinWorldType
{
};
// User type for DIN70k coordinate system with base in principle point of camera
class CDinCamType
{
};
// User type for CV coordinate system with base in middle of front axle
class CCvFrontType
{
};
// User type for CV coordinate system with base in middle of rear axle
class CCvRearType
{
};
// User type for CV coordinate system with base in projection of middle of rear axle to the ground
class CCvWorldType
{
};
// User type for CV coordinate system with base in principle point of camera
class CCvCamType
{
};

using CLengthType            = TSIType<CLength>;
using CMassType              = TSIType<CMass>;
using CTimeType              = TSIType<CTime>;
using CCurrentType           = TSIType<CCurrent>;
using CTemperatureType       = TSIType<CTemperature>;
using CAmountOfSubstanceType = TSIType<CAmountOfSubstance>;
using CLuminousIntensityType = TSIType<CLuminousIntensity>;
using CAngleType             = TSIType<CAngle>;

using CPixelType      = TSIType<CPixel>;
using CPercentageType = TSIType<CPercentage>;

using info_nil_type = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0,
    vfc::CMassType,
    vfc::BASE,
    0,
    vfc::CTimeType,
    vfc::BASE,
    0,
    vfc::CCurrentType,
    vfc::BASE,
    0,
    vfc::CTemperatureType,
    vfc::BASE,
    0,
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0,
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0,
    vfc::CAngleType,
    vfc::BASE,
    0,
    vfc::CBasicType>;

using info_rbnil_type = vfc::TRBInfoType<
    vfc::CPixelType,
    vfc::BASE,
    0, // pixle
    vfc::CPercentageType,
    vfc::BASE,
    0, // Percentage
    vfc::CBasicType>;

struct CDefaultType
{
};

///
///
///@tparam UnitInfo1Type - SiUnit unit info type
///@tparam UnitInfo2Type - SiUnit unit info type
///@tparam DataType - data type for conversion
///
template <class UnitInfo1Type, class UnitInfo2Type, class DataType>
class TNoConvertConvert
{
  public:
    // static asserts for verifying if the prefixes match
    VFC_STATIC_ASSERT(UnitInfo1Type::LENGTH_PREFIX_VALUE == UnitInfo2Type::LENGTH_PREFIX_VALUE);
    VFC_STATIC_ASSERT(UnitInfo1Type::MASS_PREFIX_VALUE == UnitInfo2Type::MASS_PREFIX_VALUE);
    VFC_STATIC_ASSERT(UnitInfo1Type::TIME_PREFIX_VALUE == UnitInfo2Type::TIME_PREFIX_VALUE);
    VFC_STATIC_ASSERT(UnitInfo1Type::CURRENT_PREFIX_VALUE == UnitInfo2Type::CURRENT_PREFIX_VALUE);
    VFC_STATIC_ASSERT(UnitInfo1Type::TEMPERATURE_PREFIX_VALUE == UnitInfo2Type::TEMPERATURE_PREFIX_VALUE);
    VFC_STATIC_ASSERT(UnitInfo1Type::AMOUNTOFSUBSTANCE_PREFIX_VALUE == UnitInfo2Type::AMOUNTOFSUBSTANCE_PREFIX_VALUE);
    VFC_STATIC_ASSERT(UnitInfo1Type::LUMINOUSINTENSITY_PREFIX_VALUE == UnitInfo2Type::LUMINOUSINTENSITY_PREFIX_VALUE);
    VFC_STATIC_ASSERT(UnitInfo1Type::ANGLE_PREFIX_VALUE == UnitInfo2Type::ANGLE_PREFIX_VALUE);

    template <class ValueType>
    inline static void performValueConversion(ValueType&)
    {
    }
};
} // namespace vfc

#endif // ZX_VFC_SIUNITS_HELPER_HPP_INCLUDED

