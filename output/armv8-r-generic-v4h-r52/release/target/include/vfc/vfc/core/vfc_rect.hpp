//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_RECT_HPP_INCLUDED
#define ZX_VFC_RECT_HPP_INCLUDED

#include "vfc/core/vfc_types.hpp" // used for int32_t
#include "vfc/core/vfc_type_traits.hpp"

namespace vfc
{ // namespace vfc opened

//=========================================================================
// CPoint
//-------------------------------------------------------------------------
/// The CPoint structure defines the integer x- and y-coordinates of a
/// 2d point.

/// @ingroup vfc_group_core_types
//=========================================================================

class CPoint
{
  public:
    /// default c'tor, initializes x and y member variables with zero
    /// @dspec{1595}     The SW component "vfc" shall create a new point instance.
    CPoint(void);

    /// c'tor, initializes x and y member variables
    /// @dspec{755}     The SW component "vfc" shall create a point instance where the x
    ///                 coordinate will be set to the given x value and the y coordinate will
    ///                 be set to the given y value.
    /// @param f_x_i32 value for x-axis
    /// @param f_y_i32 value for y-axis
    CPoint(int32_t f_x_i32, int32_t f_y_i32);

    /// offsets CPoint by adding specified position
    /// @dspec{1229}     The SW component "vfc" shall add the coordinates of two given points
    ///                  and write the added coordinates to the first given point.
    /// @param f_rhs CPoint instance that will be added to this
    /// @return reference to modified CPoint
    CPoint& operator+=(const CPoint& f_rhs);

    /// offsets CPoint by subtracting specified position
    /// @dspec{1228}     The SW component "vfc" shall subtract the coordinates of the second
    ///                  given point from the first given point and write the subtracted coordinates
    ///                  to the first given point.
    /// @param f_rhs CPoint instance that will be subtracted from this
    /// @return reference to modified CPoint
    CPoint& operator-=(const CPoint& f_rhs);

    /// Get value of x axis
    /// @dspec{756}     The SW component "vfc" shall return the x coordinate of a given point.
    /// @return value for x-axis
    int32_t& x(void) { return m_x; } // PRQA S 4024 # Technical debt L2 id=00c1fc163f2dcf074947-13aaa2f9c51d80ef

    /// Get value of x axis. const interface
    /// @dspecref{756}
    /// @return value for x-axis
    int32_t x(void) const { return m_x; }

    /// Get value of y axis
    /// @dspec{1964}     The SW component "vfc" shall return the y coordinate of a given point.
    /// @return value for y-axis
    int32_t& y(void) { return m_y; } // PRQA S 4024 # Technical debt L2 id=00c1bf813c315ff048d5-7958411921e7a181

    /// Get value of y axis. const interface
    /// @dspecref{1964}
    /// @return value for y-axis
    int32_t y(void) const { return m_y; }

  private:
    int32_t m_x; ///< x-coordinate
    int32_t m_y; ///< y-coordinate
};

// traits specialization
template <>
struct THasTrivialDTor<CPoint> : true_t
{
};
template <>
struct THasTrivialCopy<CPoint> : true_t
{
};
template <>
struct THasTrivialSetZero<CPoint> : true_t
{
};

/// Returns the sum of two points.
/// @dspec{1642}     The SW component "vfc" shall add the coordinates of two given points and
///                  return a new point instance with the resulting coordinates.
/// @param f_op1 CPoint value on the left-hand-side of expression
/// @param f_op2 CPoint value on the right-hand-side of expression
/// @return const value CPoint of sum
inline const CPoint operator+(const CPoint& f_op1, const CPoint& f_op2);

/// Returns the difference of two points.
/// @dspec{1643}     The SW component "vfc" shall subtract the coordinates of the second given
///                  point from the first given point and return a new point instance with the
///                  resulting coordinates.
/// @param f_op1 CPoint value on the left-hand-side of expression
/// @param f_op2 CPoint value on the right-hand-side of expression
/// @return const value CPoint of difference
inline const CPoint operator-(const CPoint& f_op1, const CPoint& f_op2);

//=========================================================================
// CSize
//-------------------------------------------------------------------------
/// The CSize class implements a relative coordinate or extent (eg the
/// width and height of a rectangle).
/// @sa CRect

/// @ingroup vfc_group_core_types
//=========================================================================

class CSize
{
  public:
    /// default c'tor, initializes cx and cy member variables with zero
    /// @dspec{1639}     The SW component "vfc" shall create and return a size instance where
    ///                  both dimensions are zero.
    CSize(void);

    /// c'tor, initializes cx and cy member variables
    /// @dspec{799}     The SW component "vfc" shall create and return a size instance
    ///                 from two given values which represent the x and y dimensions.
    /// @param f_cx_i32 value for width
    /// @param f_cy_i32 value for height
    CSize(int32_t f_cx_i32, int32_t f_cy_i32);

    /// Adds a size to CSize.
    /// @dspec{1309}     The SW component "vfc" shall add two given size instances component-wise
    ///                  and store the resulting dimension in the first given instance.
    /// @param f_rhs CSize instance that will be added to this
    /// @return reference to modified CSize
    CSize& operator+=(const CSize& f_rhs);

    /// Subtracts a size from CSize.
    /// @dspec{1308}     The SW component "vfc" shall subtract from the first given size
    ///                  instance another given size instance component-wise and store the
    ///                  resulting dimensions in the first given instance.
    /// @param f_rhs CSize instance that will be subtracted to this
    /// @return reference to modified CSize
    CSize& operator-=(const CSize& f_rhs);

    /// Get value of x axis
    /// @dspec{1307}     The SW component "vfc" shall return the size dimension parameters
    ///                  of a given size instance by means of cx and cy.
    /// @return value by reference for x-axis
    int32_t& cx(void) { return m_cx; } // PRQA S 4024 # Technical debt L2 id=00c1ad30b5e52a5c4491-2efdb2b8c1e0bb99

    /// Get value of x axis. const interface
    /// @dspecref{1307}
    /// @return value for x-axis
    int32_t cx(void) const { return m_cx; }

    /// Get value of y axis
    /// @dspecref{1307}
    /// @return value by reference for y-axis
    int32_t& cy(void) { return m_cy; } // PRQA S 4024 # Technical debt L2 id=00c13dacbef185264408-3ffa80d34dfa719f

    /// Get value of y axis. const interface
    /// @dspecref{1307}
    /// @return value for y-axis
    int32_t cy(void) const { return m_cy; }

  private:
    int32_t m_cx; ///< extent in x-direction
    int32_t m_cy; ///< extent in y-direction
};

// traits specialization
template <>
struct THasTrivialDTor<CSize> : true_t
{
};
template <>
struct THasTrivialCopy<CSize> : true_t
{
};
template <>
struct THasTrivialSetZero<CSize> : true_t
{
};

/// Returns the sum of two sizes.
/// @dspec{1640}     The SW component "vfc" shall add two given size instances
///                  component-wise and return a new size instance with the resulting dimensions.
/// @param f_op1 CPoint value on the left-hand-side of expression
/// @param f_op2 CPoint value on the right-hand-side of expression
/// @return const value CSize of sum
inline const CSize operator+(const CSize& f_op1, const CSize& f_op2);

/// Returns the difference of two sizes.
/// @dspec{1641}     The SW component "vfc" shall subtract from the first given size
///                  instance another given size instance component-wise and return a
///                  new size instance with the resulting dimensions.
/// @param f_op1 CPoint value on the left-hand-side of expression
/// @param f_op2 CPoint value on the right-hand-side of expression
/// @return const value CSize of difference
inline const CSize operator-(const CSize& f_op1, const CSize& f_op2);

//=========================================================================
// CRect
//-------------------------------------------------------------------------
/// A CRect defines the coordinates of the (inside) upper-left and (outside)
/// lower-right corners of a rectangle.
/// Keep in mind that the upper left corner is inside the rectangle, while
/// the lower right is 1 pixel to the right and below the last pixel in the
/// rectangle and therefore outside of the rectangle area.
/// @par
/// @image html core-rect.png
///
/// When specifying a CRect, you must be careful to construct it so that it
/// is normalized in other words, such that the value of the left coordinate
/// is less than the right and the top is less than the bottom.
/// For example, a top left of (10,10) and bottom right of (20,20) defines a
/// normalized rectangle but a top left of (20,20) and bottom right of (10,10)
/// defines a non-normalized rectangle.
/// If the rectangle is not normalized, many CRect member functions may
/// return incorrect results.
/// Especially an isEmpty() for a not normalized CRect will return true, as
/// a not normalized rect is interpreted as "not existing" aka "a hole".
/// Before you call a function that requires normalized rectangles, you can
/// normalize non-normalized rectangles by calling the normalize() function.
/// @sa
/// - CPoint
/// - CSize

/// @ingroup vfc_group_core_types
//=========================================================================

class CRect
{
  public:
    /// default, c'tor - sets top-left to origin and width and height to zero.
    /// @dspec{1599}     The SW component "vfc" shall create a new rectangle instance
    ///                  with zero origin coordinates and zero height and width.
    CRect(void);

    /// constructs CRect with top-left corner and specified extent.
    /// @dspec{766}     The SW component "vfc" shall create a rectangle instance with a
    ///                 given left position, a given top position, a given width and a given height.
    /// @param  f_left      Specifies the left position of CRect.
    /// @param  f_top       Specifies the top of CRect.
    /// @param  f_width     Specifies the extent in x-direction.
    /// @param  f_height    Specifies the extent in y-direction.

    CRect(int32_t f_left, int32_t f_top, int32_t f_width, int32_t f_height);

    /// constructs CRect with top-left corner and specified extent.
    /// @dspec{767}     The SW component "vfc" shall create a rectangle instance with a
    ///                 given point and size.
    /// @param  f_topLeft   Specifies the origin point for the rectangle.
    ///                     Corresponds to the top-left corner.
    /// @param  f_size      Specifies the displacement from the top-left
    ///                     corner to the bottom-right corner.

    CRect(const CPoint& f_topLeft, const CSize& f_size);

    /// constructs CRect with top-left (inside) and bottom-right (outside)
    /// corners.
    /// @dspec{1248}     The SW component "vfc" shall create a rectangle instance from
    ///                  a given point that specifies the top left corner and a given point
    ///                  that specifies the bottom right corner of the rectangle.

    /// @param  f_topLeft       Specifies the top-left (inside) position
    ///                         of CRect.
    /// @param  f_bottomRight   Specifies the bottom-right (outside)
    ///                         position of CRect.

    CRect(const CPoint& f_topLeft, const CPoint& f_bottomRight);

    /// returns the left position (inside) of CRect (read only).
    /// @dspec{1247}     The SW component "vfc" shall return the positional parameters
    ///                  of a given rectangle by means of left, top, right, bottom.
    /// @return value of the left position
    int32_t left(void) const { return m_left; }

    /// returns the left position (inside) of CRect (read/write).
    /// @dspecref{1247}
    /// @return value by reference of the left position
    int32_t& left(void) { return m_left; } // PRQA S 4024 # Technical debt L2 id=00c1d9c7b318e0184d86-bccd273ee9d0e5e4

    /// returns the top (inside) of CRect (read only).
    /// @dspecref{1247}
    /// @return value of the top position
    int32_t top(void) const { return m_top; }

    /// returns the top (inside) of CRect (read/write).
    /// @dspecref{1247}
    /// @return value by reference of the top position
    int32_t& top(void) { return m_top; } // PRQA S 4024 # Technical debt L2 id=00c144a5ecaa1946451c-78a865783a96e8cd

    /// returns the width of CRect (read only).
    /// @dspec{1245}     The SW component "vfc" shall return the values of the dimension
    ///                  parameters of a given rectangle by means of width, and height.
    /// @return value of the width
    int32_t width(void) const { return m_width; }

    /// returns the width of CRect (read/write).
    /// @dspecref{1245}
    /// @return value by reference of the width
    int32_t& width(void) { return m_width; } // PRQA S 4024 # Technical debt L2 id=00c17b416cc0e271453b-85d160491a82c2ae

    /// returns the height of CRect (read only).
    /// @dspecref{1245}
    /// @return value of the height
    int32_t height(void) const { return m_height; }

    /// returns the height of CRect (read/write).
    /// @dspecref{1245}
    /// @return value by reference of the height
    int32_t& height(void)
    {
        return m_height;
    } // PRQA S 4024 # Technical debt L2 id=00c1ef729018328c425a-533738cf069ecaa3

    /// returns the top-left position of the rect.
    /// @dspec{1633}     The SW component "vfc" shall return the corner points of the
    ///                  rectangle by means of  topLeft and bottomRight.
    /// @return value of the top-left position
    CPoint topLeft(void) const;

    /// returns the bottom-right position of the rect.
    /// @dspecref{1633}
    /// @return value by reference of the bottom-right position
    CPoint bottomRight(void) const;

    /// returns the centerpoint C((right+left)/2,(bottom+top)/2) of the
    /// rectangle .
    /// @dspec{1246}     The SW component "vfc" shall return the point at the center position.
    /// @return value of the centerpoint
    CPoint center(void) const;

    /// returns the right position (outside) of CRect.
    /// @dspecref{1247}
    /// @return value of the right position
    int32_t right(void) const { return m_left + m_width; }

    /// returns the bottom (outside) of CRect.
    /// @dspecref{1247}
    /// @return value of the bottom position
    int32_t bottom(void) const { return m_top + m_height; }

    /// returns the size of the rect.
    /// @dspec{1634}     The SW component "vfc" shall return the Size instance describing
    ///                  the dimensions of a given rectangle.
    /// @return value of the size
    CSize size(void) const;

    /// returns the rect's area area = width*height.
    /// Do not use area() to check if the rect is valid.
    /// @dspec{1244}     The SW component "vfc" shall compute and return the area of a
    ///                  given rectangle instance
    /// @return value of the area
    int32_t area(void) const;

    /// determines whether CRect is empty. CRect is empty if the
    /// width and/or height are 0, or if the rect is not normalized,
    /// i.e. width and/or height are negative.
    /// @dspec{1243}     The SW component "vfc" shall return the boolean value "true" if a
    ///                  given rectangle is empty otherwise it shall return the boolean value "false".
    /// @return true to indicate rect is empty and false otherwise.
    bool isEmpty(void) const;

    /// determines whether the specified rect lies inside this rect,
    /// identical coordinates are treated as inside.
    /// @dspec{1242}     The SW component "vfc" shall return the boolean value "true" if a
    ///                  given rectangle contains another given rectangle or point otherwise
    ///                  it shall return the boolean value "false".
    /// @param f_otherRect Specifies the TRect instance
    /// @return true to indicate rect contains rect and false otherwise.
    bool contains(const CRect& f_otherRect) const;

    /// determines whether the specified point lies inside or outside
    /// of this rect.
    /// @dspecref{1242}
    /// @param f_point Specifies the CPoint instance
    /// @return true to indicate rect contains point and false otherwise.
    bool contains(const CPoint& f_point) const;

    /// determines whether the specified point p(x,y) lies inside or
    /// outside of this rect.
    /// @dspecref{1242}
    /// @param  f_px Specifies the extent in x-direction.
    /// @param  f_py Specifies the extent in y-direction.
    /// @return true to indicate rect contains point and false otherwise.
    bool contains(int32_t f_px, int32_t f_py) const;

    /// changes the size of the rect without changing the position of the
    /// top-left corner.
    /// @dspec{1241}     The SW component "vfc" shall resize a given rectangle to the
    ///                  given dimensions without changing it's top-left position.
    /// @param  f_cx Specifies the extent in x-direction.
    /// @param  f_cy Specifies the extent in y-direction.
    void resize(int32_t f_cx, int32_t f_cy);

    /// changes the size of the rect without changing the position of the
    /// top-left corner.
    /// @dspecref{1241}
    /// @param f_newSize Specifies the CSize instance
    void resize(const CSize& f_newSize);

    /// Standardizes the height and width of CRect.
    /// NormalizeRect adds m_width to m_left and subtracts m_with fromt itself
    /// if m_width is less than zero.
    /// Similarly, adds m_height to m_top and subtracts m_height fromt itself
    /// if m_height is less than zero.
    /// This function is useful when dealing with inverted rectangles.
    /// @dspec{1240}     The SW component "vfc" shall normalize a given rectangle so that
    ///                  the value for the bottom position is greater than/equals the top value
    ///                  and the right value is greater than/equals the left value and store the
    ///                  modified parameters in the given rectangle.
    void normalize(void);

    /// Inflate rectangle's width and height by dx units to the left and
    /// right ends of the rectangle and dy units to the top and bottom.
    /// @dspec{1239}     The SW component "vfc" shall inflate, i.e. enlarge a given rectangle
    ///                  by moving the sides by given increments away from the center.
    /// @param  f_dx    Specifies the number of units to inflate the left and
    ///                 right sides of CRect.
    /// @param  f_dy    Specifies the number of units to inflate the top and
    ///                 bottom of CRect.
    void inflate(int32_t f_dx, int32_t f_dy);

    /// Inflate rectangle's width and height by moving individual sides.
    /// Left side is moved to the left, right side is moved to the right,
    /// top is moved up and bottom is moved down.
    /// @dspecref{1239}
    /// @param  f_dleft     Specifies the number of units to inflate the
    ///                     left side of CRect.
    /// @param  f_dtop      Specifies the number of units to inflate the
    ///                     top side of CRect.
    /// @param  f_dright    Specifies the number of units to inflate the
    ///                     right side of CRect.
    /// @param  f_dbottom   Specifies the number of units to inflate the
    ///                     bottom side of CRect.

    void inflate(int32_t f_dleft, int32_t f_dtop, int32_t f_dright, int32_t f_dbottom);

    /// Deflate rectangle's width and height by dx units to the left and
    /// right ends of the rectangle and dy units to the top and bottom.
    /// @dspec{1238}     The SW component "vfc" shall deflate, i.e. shrink a given rectangle
    ///                  by moving the sides by given increments towards the center.
    /// @param  f_dx    Specifies the number of units to deflate the left
    ///                 and right sides of CRect.
    /// @param  f_dy    Specifies the number of units to deflate the top
    ///                 and bottom of CRect.

    void deflate(int32_t f_dx, int32_t f_dy);

    /// Deflate rectangle's width and height by moving individual sides.
    /// Left side is moved to the right, right side is moved to the left,
    /// top is moved down and bottom is moved up.
    /// @dspecref{1238}
    /// @param  f_dleft     Specifies the number of units to deflate the
    ///                     left side of CRect.
    /// @param  f_dtop      Specifies the number of units to deflate the
    ///                     top side of CRect.
    /// @param  f_dright    Specifies the number of units to deflate the
    ///                     right side of CRect.
    /// @param  f_dbottom   Specifies the number of units to deflate the
    ///                     bottom side of CRect.

    void deflate(int32_t f_dleft, int32_t f_dtop, int32_t f_dright, int32_t f_dbottom);

    /// Moves CRect by the specified offsets.
    /// @dspec{1237}     The SW component "vfc" shall move a given rectangle by given offset
    ///                  coordinate values.
    void offset(int32_t f_dx, int32_t f_dy);

    /// Moves CRect to the specified x- and y-coordinates.
    /// @param  f_x The absolute x-coordinate for the upper-left corner
    ///             of the rectangle.
    /// @param  f_y The absolute y-coordinate for the upper-left corner
    ///             of the rectangle.

    /// @dspec{1236}     The SW component "vfc" shall move the upper left corner of a
    ///                  given rectangle to a given absolute position.
    void moveTo(int32_t f_x, int32_t f_y);

    /// Moves CRect to the specified x- and y-coordinates.
    /// @dspecref{1236}     The SW component "vfc" shall move the upper left corner of a
    ///                     given rectangle to a given absolute position.
    /// @param   f_point    A CPoint specifying the absolute upper-left
    ///                     corner of the rectangle.
    void moveTo(const CPoint& f_point);

  private:
    int32_t m_left;
    int32_t m_top;
    int32_t m_width;
    int32_t m_height;
};

// traits specialization
template <>
struct THasTrivialDTor<CRect> : true_t
{
};
template <>
struct THasTrivialCopy<CRect> : true_t
{
};
template <>
struct THasTrivialSetZero<CRect> : true_t
{
};

//-------------------------------------------------------------------------
/// returns a rect which is equal to the union of the two specified
/// rectangles.
/// The union is the smallest rectangle that contains both source
/// rectangles.
/// @dspec{1235}     The SW component "vfc" shall create and return the smallest rectangle
///                  which contains the union of two given rectangles.
/// @param f_op1 Object of first CRect
/// @param f_op2 Object of second CRect
/// @return Returns a rect which is equal to the union
/// @relatesalso CRect

//-------------------------------------------------------------------------
inline CRect union_rect(const CRect& f_op1, const CRect& f_op2);

//-------------------------------------------------------------------------
/// Returns a rect which is equal to the intersection of the two
/// specified rectangles.
/// The intersection is the largest rectangle contained in both existing
/// rectangles.
/// If the two specified rectangles don't intersect, the resulting
/// rectangle may become denormalized.
/// This can be checked with CRect::isEmpty().
/// Do not use area() to check validity as the result from a denormalized
/// rectangle is undetermined (might be <,>,= 0,
/// depending on signs of width and height).
/// @dspec{1234}     The SW component "vfc" shall create and return the largest rectangle
///                  which is contained in two given rectangles.
/// @note always check the resulting rectangle for denormalization!
/// @param f_op1 Object of first CRect
/// @param f_op2 Object of second CRect
/// @return Returns a rect which is equal to the intersection
/// @relatesalso CRect

//-------------------------------------------------------------------------
inline CRect intersect_rect(const CRect& f_op1, const CRect& f_op2);

//-------------------------------------------------------------------------
/// returns true if every of the four values (top, left, width, height)
/// of one rectangle is equal the its corresponding value in the other
/// rectangle.
/// @dspec{1233}     The SW component "vfc" shall return the boolean value "true" if two
///                  given rectangles have the same top, the same left, the same width, and
///                  the same height values otherwise it shall return the boolean value "false".
/// @param f_lhs CRect value on the left-hand-side of expression
/// @param f_rhs CRect value on the right-hand-side of expression
/// @return Returns true if rects are equal and false otherwise.
/// @relatesalso CRect

//-------------------------------------------------------------------------
inline bool operator==(const CRect& f_lhs, const CRect& f_rhs);

//-------------------------------------------------------------------------
/// returns true if any of the four values (top, left, width, height) of
/// one of the rectangles is not equal to the corresponding value in
/// the other rectangle.
/// @dspec{1232}     The SW component "vfc" shall return the boolean value "false" if two given
///                  rectangles have the same top, the same left, the same width, and the same
///                  height values otherwise it shall return the boolean value "true".
/// @param f_lhs CRect value on the left-hand-side of expression
/// @param f_rhs CRect value on the right-hand-side of expression
/// @return Returns true if rects are NOT equal and false otherwise.
/// @relatesalso CRect

//-------------------------------------------------------------------------
inline bool operator!=(const CRect& f_lhs, const CRect& f_rhs);

} // namespace vfc

#include "vfc/core/vfc_rect.inl"

#endif // ZX_VFC_RECT_HPP_INCLUDED

