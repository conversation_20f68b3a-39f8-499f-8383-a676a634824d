//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_METROWERKS_HPP_INCLUDED
#define VFC_METROWERKS_HPP_INCLUDED

#include "vfc/core/vfc_preprocessor.hpp"

/////////////////////////////////////
// try to detect eppc compiler
// amazingly  __embedded__ ist NOT
// defined on CW8.1 EPPC compiler ?!
/////////////////////////////////////
#if (defined(__POWERPC__) && !defined(macintosh))
#define VFC_EPPC_DETECTED
#endif

/////////////////////////////////////
// versions check
/////////////////////////////////////

#if defined(VFC_EPPC_DETECTED)
#if (__MWERKS__ < 0x2407)
static_assert(false, "Compiler not supported or configured");
#endif
#if (__MWERKS__ > 0x4010)
static_assert(false, "Unknown compiler version");
#endif
#else
static_assert(false, "Unknown compiler version");
#endif

/////////////////////////////////////
//  Metrowerks C++ compiler setup
/////////////////////////////////////

// embedded PPC
#if defined(VFC_EPPC_DETECTED)
#if __MWERKS__ == 0x2407
#define VFC_COMPILER_VERSION VFC_STRINGIZE(EPPC_7 .0)
#elif __MWERKS__ == 0x3004
#define VFC_COMPILER_VERSION VFC_STRINGIZE(EPPC_8 .0)
#elif __MWERKS__ == 0x4010
#define VFC_COMPILER_VERSION VFC_STRINGIZE(EPPC_8 .5)
#else
#define VFC_COMPILER_VERSION __MWERKS__
#endif
// all others
#else
#define VFC_COMPILER_VERSION __MWERKS__
#endif

#define VFC_COMPILER_STRING VFC_JOIN("Metrowerks CodeWarrior C++ version ", VFC_STRINGIZE(VFC_COMPILER_VERSION))

#ifndef VFC_COMPILER_MWERKS
#define VFC_COMPILER_MWERKS
#endif

////////////////////////////////////
// multi/single threaded target
////////////////////////////////////

#if __ide_target("lib-multi-debug") || __ide_target("lib-multi-release") || __ide_target("lib-multi-dll-debug") ||     \
    __ide_target("lib-multi-dll-release")
#define VFC_MT
#endif

////////////////////////////////////
// RTTI support
////////////////////////////////////

#if !__option(RTTI)
#define VFC_NO_RTTI
#endif

////////////////////////////////////
// exception support
////////////////////////////////////

#if !__option(exceptions)
#define VFC_NO_EXCEPTIONS
#endif

//////////////////////////////
// long long / __int64 support
//////////////////////////////

#if __option(longlong)
#define VFC_HAS_LONG_LONG
#endif

//////////////////////////////
// cstdint support
//////////////////////////////

// MSL defines stdint types only with C99 support (see <stdint.h>)
#if (defined(_MSL_C99) && (0 != _MSL_C99))
#define VFC_HAS_STDINT_H
#endif

//////////////////////////////
// endianess
//////////////////////////////

#if !__option(little_endian)
#define VFC_BIG_ENDIAN
#endif

////////////////////////////////////
// processor identification
////////////////////////////////////

#if defined(__i386__)
#define VFC_PROCESSOR_IX86
#elif defined(__POWERPC__)
#define VFC_PROCESSOR_PPC
#else
static_assert(false, "processor not supported");
#endif

// macro abstraction for linker statements
#define VFC_LINKER_SECT(sect_name) __declspec(section sect_name)

#ifndef VFC_DECL_FORCEINLINE
#define VFC_DECL_FORCEINLINE inline
#endif

#endif // VFC_METROWERKS_HPP_INCLUDED

//=============================================================================

