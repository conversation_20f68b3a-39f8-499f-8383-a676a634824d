//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_TYPELIST_HPP_INCLUDED
#define VFC_TYPELIST_HPP_INCLUDED

#include "vfc/core/vfc_types.hpp"

namespace vfc
{
//=========================================================================
//  DOXYGEN DEFGROUP vfc_group_core_generic_typelist BEGIN
//-------------------------------------------------------------------------
/// @defgroup vfc_group_core_generic_typelist Typelist
/// @ingroup vfc_group_core_generic
/// @brief vfc typelist implementation.
/// Typelists are an important generic programming technique.\n
/// They add new capabilities for library writers: expressing and
/// manipulating arbitrarily large collections of types, generating data
/// structures and code from such collections, and more.\n
/// Typelists are useful when you have to write the same code - either
/// declarative or imperative - for a collection of types. They enable you
/// to abstract and generalite entities that escape all other template
/// programming techniques. For this reason, typelists are the enabling
/// means for genuinely new idioms and library implementations.\n
/// At compile time, typelists offer most primitive functions that lists of
/// values typically implement: add (TTypeListAppend), access (TTypeListTypeAt),
/// search (TTypeListIndexOf), erase, replace and more. The code that
/// implements typelist manipulation is confined to a pure functional style
/// because there are no compile-time mutable values - a type or compile-
/// time constant, once defines, cannot be changed. For this reason most
/// typelist processing relies on recursive templates and pattern matching
/// through partial template specialization.\n
/// @sa
/// - TTypeList
/// - TTypeListSize
/// - TTypeListAppend
/// - TTypeListTypeAt
/// - TTypeListIndexOf
/// - TGenTypeList1
/// .
/// @{
//=========================================================================

//=========================================================================
// TTypeList<>
//-------------------------------------------------------------------------
/// Typelist list node.
/// Typelists hold two types. They are accessible through the Head and Tail
/// inner names.\n
/// Typelists are devoid of any value: Their bodies are empty,
/// they don't hold any state, and they don't define any functionality.
/// at runtime typelists don't carry any value at all. Their only reason is
/// to carry type information.\n
/// There is a little problem, though. We can express typelists of two types
/// or more, but we're unable to express typelists containing zero or one
/// type. What's needed is a null list type, and the CTypeListNullType is
/// exactly suited for such use. We establish the convention that every
/// typelist must end with a CTypeListNullType. \n
/// Now we can define a typelist of only one element:
/// @par
/// @code
/// using OneTypeOnly = TTypeList<int, CTypeListNullType>;
/// @endcode
///
/// For linearizing typelist creation we provide the TGenTypeList1 types
/// generators.
/// @sa
/// - CTypeListNullType
/// - TGenTypeList1

//=========================================================================
///
///
///@tparam HeadType - Head Type of TTypeList (see above)
///@tparam TailType - Tail Type of TTypeList (see above)
///
template <typename HeadType, typename TailType>
struct TTypeList
{
    using Head = HeadType;
    using Tail = TailType;
};

//=========================================================================
// CTypeListNullType
//-------------------------------------------------------------------------
/// Typelist termination marker.
/// Every typelelist must end with a CTypeListNullType. CTypeListNullType
/// serves as a useful termination marker, much like '\\0' that helps
/// traditional C string functions.
/// @sa TTypeList

//=========================================================================

struct CTypeListNullType
{
};

//=========================================================================
// TTypeListSize<>
//-------------------------------------------------------------------------
/// @struct vfc::TTypeListSize
/// @brief Returns the length of the specified type list.
/// The idea underlying most typelists manipulations is to exploit
/// recursive templates, which are templates that uses instantiations of
/// themselves as part of their definition. The implementation of
/// TTypeListSize uses partial template specialization to distinguish
/// between a CTypeListNullType and a TTypeList. When the Tail becomes
/// CTypeListNullType the recursion is stopped and so is the length
/// calculation which comes back nicely with the result.
/// @return the list size stored in TTypeListSize<>::value
/// @sa TTypeList

//=========================================================================

///
///@tparam ListType - Type of type list
///
template <typename ListType>
struct TTypeListSize;

//=========================================================================
// TTypeListIndexOf<>
//-------------------------------------------------------------------------
/// @struct vfc::TTypeListIndexOf
/// @brief Returns the index of the first occurence of the type in a typelist.
/// TIndexOf::value holds either the index or -1 if the type was not found.
/// @return the index or -1 (not found) in TTypeListIndexOf<>::value.
/// @sa TTypeList

//=========================================================================

///
///@tparam ListType - Type of type list
///@tparam SearchType - Type of search item in the list
///
template <typename ListType, typename SearchType>
struct TTypeListIndexOf;

//=========================================================================
// TTypeListTypeAt<>
//-------------------------------------------------------------------------
/// @struct vfc::TTypeListTypeAt
/// @brief Returns the type at specified index.
/// Having access by index to the elements of a typelist is a desireable
/// feature. It linearizes typelist access, making it easier to manipulate
/// typelists comfortably.
/// @return the type at specified index in TTypeListTypeAt<>::type
/// @note results in a compile time error if specified IndexValue is
/// out-of-bounds.
/// @sa TTypeList

//=========================================================================

///
///
///@tparam ListType - Type of type list
///@tparam IndexValue - Type of item index
///
template <class ListType, int32_t IndexValue>
struct TTypeListTypeAt;

//=========================================================================
// TTypeListAppend<>
//-------------------------------------------------------------------------
/// @struct vfc::TTypeListAppend
/// @brief Appends a type or a typelist to specified type list.
/// @return the enlarged type list in TTypeListAppend<>::type
/// @sa TTypeList

//=========================================================================
///
///
///@tparam ListType - Type of type list
///@tparam AppendType - Type of item to append
///
template <class ListType, class AppendType>
struct TTypeListAppend;

//=========================================================================
//  TGenTypeList1
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation of one type only.
/// Right of the bat, typelists are just too LISP-ish to be easy to use.
/// LISP-style constructs are great for LISP-programmers, but they don't
/// dovetail nicely with C++. For instance, here's a typelist of integral
/// types:
/// @par
/// @code
/// using type = TTypeList   <
///                     signed char,
///                     TTypeList   <
///                                 short int,
///                                 TTypeList   <
///                                             int,
///                                             TTypeList   <
///                                                         long int,
///                                                         CTypeListNullType
///                                                         >
///                                             >
///                                 >
///                     > SignedIntegrals;
/// @endcode
///
/// Typelists might be a cool concept, but they definitely need a nicer
/// packaging.\n
/// In order to linearize typelist creation, vfc's typelist library
/// defines several generator types that transform the recursion into
/// simple enumeration, at the expense of tedious repetition. This is not
/// a problem, however. The repitition is done only once and it scales
/// typelists to a large library-defined number (24).\n
/// Now the earlier type definition can be expressed in a more pleasant
/// way:
/// @par
/// @code
/// using type = TGenTypeList4   <   signed char,
///                             short int,
///                             int,
///                             long int
///                         >::type SignedIntegrals;
/// @endcode
/// @sa
/// -   TGenTypeList2
/// -   TGenTypeList3
/// -   TGenTypeList4
/// -   TGenTypeList5
/// -   TGenTypeList6
/// -   TGenTypeList7
/// -   TGenTypeList8
/// -   TGenTypeList9
/// -   TGenTypeList10
/// -   TGenTypeList11
/// -   TGenTypeList12
/// -   TGenTypeList13
/// -   TGenTypeList14
/// -   TGenTypeList15
/// -   TGenTypeList16
/// -   TGenTypeList17
/// -   TGenTypeList18
/// -   TGenTypeList19
/// -   TGenTypeList20
/// -   TGenTypeList21
/// -   TGenTypeList22
/// -   TGenTypeList23
/// -   TGenTypeList24
/// -   TTypeList

//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1- Type of parameter to linearize 1
///
template <class T1>
struct TGenTypeList1
{
    using type = vfc::TTypeList<T1, vfc::CTypeListNullType>;
};

//=========================================================================
//  TGenTypeList2
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation of two types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///
template <class T1, class T2>
struct TGenTypeList2
{
    using type = vfc::TTypeList<T1, typename vfc::TGenTypeList1<T2>::type>;
};

//=========================================================================
//  TGenTypeList3
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation of three types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///
template <class T1, class T2, class T3>
struct TGenTypeList3
{
    using type = vfc::TTypeList<T1, typename vfc::TGenTypeList2<T2, T3>::type>;
};

//=========================================================================
//  TGenTypeList4
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation of four types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///@tparam T4 - Type of parameter to linearize 4
///
template <class T1, class T2, class T3, class T4>
struct TGenTypeList4
{
    using type = vfc::TTypeList<T1, typename vfc::TGenTypeList3<T2, T3, T4>::type>;
};

//=========================================================================
//  TGenTypeList5
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation of five types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///@tparam T4 - Type of parameter to linearize 4
///@tparam T5 - Type of parameter to linearize 5
///
template <class T1, class T2, class T3, class T4, class T5>
struct TGenTypeList5
{
    using type = vfc::TTypeList<T1, typename vfc::TGenTypeList4<T2, T3, T4, T5>::type>;
};

//=========================================================================
//  TGenTypeList6
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation of six types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///@tparam T4 - Type of parameter to linearize 4
///@tparam T5 - Type of parameter to linearize 5
///@tparam T6 - Type of parameter to linearize 6
///
template <class T1, class T2, class T3, class T4, class T5, class T6>
struct TGenTypeList6
{
    using type = vfc::TTypeList<T1, typename vfc::TGenTypeList5<T2, T3, T4, T5, T6>::type>;
};

//=========================================================================
//  TGenTypeList7
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation of seven types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///@tparam T4 - Type of parameter to linearize 4
///@tparam T5 - Type of parameter to linearize 5
///@tparam T6 - Type of parameter to linearize 6
///@tparam T7 - Type of parameter to linearize 7
///
template <class T1, class T2, class T3, class T4, class T5, class T6, class T7>
struct TGenTypeList7
{
    using type = vfc::TTypeList<T1, typename vfc::TGenTypeList6<T2, T3, T4, T5, T6, T7>::type>;
};

//=========================================================================
//  TGenTypeList8
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation of eight types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///@tparam T4 - Type of parameter to linearize 4
///@tparam T5 - Type of parameter to linearize 5
///@tparam T6 - Type of parameter to linearize 6
///@tparam T7 - Type of parameter to linearize 7
///@tparam T8 - Type of parameter to linearize 8
///
template <class T1, class T2, class T3, class T4, class T5, class T6, class T7, class T8>
struct TGenTypeList8
{
    using type = vfc::TTypeList<T1, typename vfc::TGenTypeList7<T2, T3, T4, T5, T6, T7, T8>::type>;
};

//=========================================================================
//  TGenTypeList9
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation of nine types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///@tparam T4 - Type of parameter to linearize 4
///@tparam T5 - Type of parameter to linearize 5
///@tparam T6 - Type of parameter to linearize 6
///@tparam T7 - Type of parameter to linearize 7
///@tparam T8 - Type of parameter to linearize 8
///@tparam T9 - Type of parameter to linearize 9
///
template <class T1, class T2, class T3, class T4, class T5, class T6, class T7, class T8, class T9>
struct TGenTypeList9
{
    using type = vfc::TTypeList<T1, typename vfc::TGenTypeList8<T2, T3, T4, T5, T6, T7, T8, T9>::type>;
};

//=========================================================================
//  TGenTypeList10
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation of ten types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///@tparam T4 - Type of parameter to linearize 4
///@tparam T5 - Type of parameter to linearize 5
///@tparam T6 - Type of parameter to linearize 6
///@tparam T7 - Type of parameter to linearize 7
///@tparam T8 - Type of parameter to linearize 8
///@tparam T9 - Type of parameter to linearize 9
///@tparam T10 - Type of parameter to linearize 10
///
template <class T1, class T2, class T3, class T4, class T5, class T6, class T7, class T8, class T9, class T10>
struct TGenTypeList10
{
    using type = vfc::TTypeList<T1, typename vfc::TGenTypeList9<T2, T3, T4, T5, T6, T7, T8, T9, T10>::type>;
};

//=========================================================================
//  TGenTypeList11
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation of eleven types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///@tparam T4 - Type of parameter to linearize 4
///@tparam T5 - Type of parameter to linearize 5
///@tparam T6 - Type of parameter to linearize 6
///@tparam T7 - Type of parameter to linearize 7
///@tparam T8 - Type of parameter to linearize 8
///@tparam T9 - Type of parameter to linearize 9
///@tparam T10 - Type of parameter to linearize 10
///@tparam T11 - Type of parameter to linearize 11
///
template <
    class T1,
    class T2,
    class T3,
    class T4,
    class T5,
    class T6,
    class T7,
    class T8,
    class T9,
    class T10,
    class T11>
struct TGenTypeList11
{
    using type = vfc::TTypeList<T1, typename vfc::TGenTypeList10<T2, T3, T4, T5, T6, T7, T8, T9, T10, T11>::type>;
};

//=========================================================================
//  TGenTypeList12
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation of twelfe types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///@tparam T4 - Type of parameter to linearize 4
///@tparam T5 - Type of parameter to linearize 5
///@tparam T6 - Type of parameter to linearize 6
///@tparam T7 - Type of parameter to linearize 7
///@tparam T8 - Type of parameter to linearize 8
///@tparam T9 - Type of parameter to linearize 9
///@tparam T10 - Type of parameter to linearize 10
///@tparam T11 - Type of parameter to linearize 11
///@tparam T12 - Type of parameter to linearize 12
///
template <
    class T1,
    class T2,
    class T3,
    class T4,
    class T5,
    class T6,
    class T7,
    class T8,
    class T9,
    class T10,
    class T11,
    class T12>
struct TGenTypeList12
{
    using type = vfc::TTypeList<T1, typename vfc::TGenTypeList11<T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12>::type>;
};

//=========================================================================
//  TGenTypeList13
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation
/// of thirteen types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///@tparam T4 - Type of parameter to linearize 4
///@tparam T5 - Type of parameter to linearize 5
///@tparam T6 - Type of parameter to linearize 6
///@tparam T7 - Type of parameter to linearize 7
///@tparam T8 - Type of parameter to linearize 8
///@tparam T9 - Type of parameter to linearize 9
///@tparam T10 - Type of parameter to linearize 10
///@tparam T11 - Type of parameter to linearize 11
///@tparam T12 - Type of parameter to linearize 12
///@tparam T13 - Type of parameter to linearize 13
///
template <
    class T1,
    class T2,
    class T3,
    class T4,
    class T5,
    class T6,
    class T7,
    class T8,
    class T9,
    class T10,
    class T11,
    class T12,
    class T13>
struct TGenTypeList13
{
    using type =
        vfc::TTypeList<T1, typename vfc::TGenTypeList12<T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13>::type>;
};

//=========================================================================
//  TGenTypeList14
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation
/// of fourteen types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///@tparam T4 - Type of parameter to linearize 4
///@tparam T5 - Type of parameter to linearize 5
///@tparam T6 - Type of parameter to linearize 6
///@tparam T7 - Type of parameter to linearize 7
///@tparam T8 - Type of parameter to linearize 8
///@tparam T9 - Type of parameter to linearize 9
///@tparam T10 - Type of parameter to linearize 10
///@tparam T11 - Type of parameter to linearize 11
///@tparam T12 - Type of parameter to linearize 12
///@tparam T13 - Type of parameter to linearize 13
///@tparam T14 - Type of parameter to linearize 14
///
template <
    class T1,
    class T2,
    class T3,
    class T4,
    class T5,
    class T6,
    class T7,
    class T8,
    class T9,
    class T10,
    class T11,
    class T12,
    class T13,
    class T14>
struct TGenTypeList14
{
    using type =
        vfc::TTypeList<T1, typename vfc::TGenTypeList13<T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14>::type>;
};

//=========================================================================
//  TGenTypeList15
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation
/// of fifteen types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///@tparam T4 - Type of parameter to linearize 4
///@tparam T5 - Type of parameter to linearize 5
///@tparam T6 - Type of parameter to linearize 6
///@tparam T7 - Type of parameter to linearize 7
///@tparam T8 - Type of parameter to linearize 8
///@tparam T9 - Type of parameter to linearize 9
///@tparam T10 - Type of parameter to linearize 10
///@tparam T11 - Type of parameter to linearize 11
///@tparam T12 - Type of parameter to linearize 12
///@tparam T13 - Type of parameter to linearize 13
///@tparam T14 - Type of parameter to linearize 14
///@tparam T15 - Type of parameter to linearize 15
///
template <
    class T1,
    class T2,
    class T3,
    class T4,
    class T5,
    class T6,
    class T7,
    class T8,
    class T9,
    class T10,
    class T11,
    class T12,
    class T13,
    class T14,
    class T15>
struct TGenTypeList15
{
    using type = vfc::
        TTypeList<T1, typename vfc::TGenTypeList14<T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15>::type>;
};

//=========================================================================
//  TGenTypeList16
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation
/// of sixteen types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///@tparam T4 - Type of parameter to linearize 4
///@tparam T5 - Type of parameter to linearize 5
///@tparam T6 - Type of parameter to linearize 6
///@tparam T7 - Type of parameter to linearize 7
///@tparam T8 - Type of parameter to linearize 8
///@tparam T9 - Type of parameter to linearize 9
///@tparam T10 - Type of parameter to linearize 10
///@tparam T11 - Type of parameter to linearize 11
///@tparam T12 - Type of parameter to linearize 12
///@tparam T13 - Type of parameter to linearize 13
///@tparam T14 - Type of parameter to linearize 14
///@tparam T15 - Type of parameter to linearize 15
///@tparam T16 - Type of parameter to linearize 16
///
template <
    class T1,
    class T2,
    class T3,
    class T4,
    class T5,
    class T6,
    class T7,
    class T8,
    class T9,
    class T10,
    class T11,
    class T12,
    class T13,
    class T14,
    class T15,
    class T16>
struct TGenTypeList16
{
    using type = vfc::TTypeList<
        T1,
        typename vfc::TGenTypeList15<T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15, T16>::type>;
};

//=========================================================================
//  TGenTypeList17
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation
/// of seventeen types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///@tparam T4 - Type of parameter to linearize 4
///@tparam T5 - Type of parameter to linearize 5
///@tparam T6 - Type of parameter to linearize 6
///@tparam T7 - Type of parameter to linearize 7
///@tparam T8 - Type of parameter to linearize 8
///@tparam T9 - Type of parameter to linearize 9
///@tparam T10 - Type of parameter to linearize 10
///@tparam T11 - Type of parameter to linearize 11
///@tparam T12 - Type of parameter to linearize 12
///@tparam T13 - Type of parameter to linearize 13
///@tparam T14 - Type of parameter to linearize 14
///@tparam T15 - Type of parameter to linearize 15
///@tparam T16 - Type of parameter to linearize 16
///@tparam T17 - Type of parameter to linearize 17
///
template <
    class T1,
    class T2,
    class T3,
    class T4,
    class T5,
    class T6,
    class T7,
    class T8,
    class T9,
    class T10,
    class T11,
    class T12,
    class T13,
    class T14,
    class T15,
    class T16,
    class T17>
struct TGenTypeList17
{
    using type = vfc::TTypeList<
        T1,
        typename vfc::TGenTypeList16<T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15, T16, T17>::type>;
};

//=========================================================================
//  TGenTypeList18
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation
/// of eighteen types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///@tparam T4 - Type of parameter to linearize 4
///@tparam T5 - Type of parameter to linearize 5
///@tparam T6 - Type of parameter to linearize 6
///@tparam T7 - Type of parameter to linearize 7
///@tparam T8 - Type of parameter to linearize 8
///@tparam T9 - Type of parameter to linearize 9
///@tparam T10 - Type of parameter to linearize 10
///@tparam T11 - Type of parameter to linearize 11
///@tparam T12 - Type of parameter to linearize 12
///@tparam T13 - Type of parameter to linearize 13
///@tparam T14 - Type of parameter to linearize 14
///@tparam T15 - Type of parameter to linearize 15
///@tparam T16 - Type of parameter to linearize 16
///@tparam T17 - Type of parameter to linearize 17
///@tparam T18 - Type of parameter to linearize 18
///
template <
    class T1,
    class T2,
    class T3,
    class T4,
    class T5,
    class T6,
    class T7,
    class T8,
    class T9,
    class T10,
    class T11,
    class T12,
    class T13,
    class T14,
    class T15,
    class T16,
    class T17,
    class T18>
struct TGenTypeList18
{
    using type = vfc::TTypeList<
        T1,
        typename vfc::TGenTypeList17<T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15, T16, T17, T18>::
            type>;
};

//=========================================================================
//  TGenTypeList19
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation
/// of nineteen types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///@tparam T4 - Type of parameter to linearize 4
///@tparam T5 - Type of parameter to linearize 5
///@tparam T6 - Type of parameter to linearize 6
///@tparam T7 - Type of parameter to linearize 7
///@tparam T8 - Type of parameter to linearize 8
///@tparam T9 - Type of parameter to linearize 9
///@tparam T10 - Type of parameter to linearize 10
///@tparam T11 - Type of parameter to linearize 11
///@tparam T12 - Type of parameter to linearize 12
///@tparam T13 - Type of parameter to linearize 13
///@tparam T14 - Type of parameter to linearize 14
///@tparam T15 - Type of parameter to linearize 15
///@tparam T16 - Type of parameter to linearize 16
///@tparam T17 - Type of parameter to linearize 17
///@tparam T18 - Type of parameter to linearize 18
///@tparam T19 - Type of parameter to linearize 19
///
template <
    class T1,
    class T2,
    class T3,
    class T4,
    class T5,
    class T6,
    class T7,
    class T8,
    class T9,
    class T10,
    class T11,
    class T12,
    class T13,
    class T14,
    class T15,
    class T16,
    class T17,
    class T18,
    class T19>
struct TGenTypeList19
{
    using type = vfc::TTypeList<
        T1,
        typename vfc::TGenTypeList18<T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15, T16, T17, T18, T19>::
            type>;
};

//=========================================================================
//  TGenTypeList20
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation
/// of twenty types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///@tparam T4 - Type of parameter to linearize 4
///@tparam T5 - Type of parameter to linearize 5
///@tparam T6 - Type of parameter to linearize 6
///@tparam T7 - Type of parameter to linearize 7
///@tparam T8 - Type of parameter to linearize 8
///@tparam T9 - Type of parameter to linearize 9
///@tparam T10 - Type of parameter to linearize 10
///@tparam T11 - Type of parameter to linearize 11
///@tparam T12 - Type of parameter to linearize 12
///@tparam T13 - Type of parameter to linearize 13
///@tparam T14 - Type of parameter to linearize 14
///@tparam T15 - Type of parameter to linearize 15
///@tparam T16 - Type of parameter to linearize 16
///@tparam T17 - Type of parameter to linearize 17
///@tparam T18 - Type of parameter to linearize 18
///@tparam T19 - Type of parameter to linearize 19
///@tparam T20 - Type of parameter to linearize 20
///
template <
    class T1,
    class T2,
    class T3,
    class T4,
    class T5,
    class T6,
    class T7,
    class T8,
    class T9,
    class T10,
    class T11,
    class T12,
    class T13,
    class T14,
    class T15,
    class T16,
    class T17,
    class T18,
    class T19,
    class T20>
struct TGenTypeList20
{
    using type = vfc::TTypeList<
        T1,
        typename vfc::
            TGenTypeList19<T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15, T16, T17, T18, T19, T20>::
                type>;
};

//=========================================================================
//  TGenTypeList21
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation
/// of twenty-one types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///@tparam T4 - Type of parameter to linearize 4
///@tparam T5 - Type of parameter to linearize 5
///@tparam T6 - Type of parameter to linearize 6
///@tparam T7 - Type of parameter to linearize 7
///@tparam T8 - Type of parameter to linearize 8
///@tparam T9 - Type of parameter to linearize 9
///@tparam T10 - Type of parameter to linearize 10
///@tparam T11 - Type of parameter to linearize 11
///@tparam T12 - Type of parameter to linearize 12
///@tparam T13 - Type of parameter to linearize 13
///@tparam T14 - Type of parameter to linearize 14
///@tparam T15 - Type of parameter to linearize 15
///@tparam T16 - Type of parameter to linearize 16
///@tparam T17 - Type of parameter to linearize 17
///@tparam T18 - Type of parameter to linearize 18
///@tparam T19 - Type of parameter to linearize 19
///@tparam T20 - Type of parameter to linearize 20
///@tparam T21 - Type of parameter to linearize 21
///
template <
    class T1,
    class T2,
    class T3,
    class T4,
    class T5,
    class T6,
    class T7,
    class T8,
    class T9,
    class T10,
    class T11,
    class T12,
    class T13,
    class T14,
    class T15,
    class T16,
    class T17,
    class T18,
    class T19,
    class T20,
    class T21>
struct TGenTypeList21
{
    using type = vfc::TTypeList<
        T1,
        typename vfc::
            TGenTypeList20<T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15, T16, T17, T18, T19, T20, T21>::
                type>;
};

//=========================================================================
//  TGenTypeList22
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation
/// of twenty-two types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///@tparam T4 - Type of parameter to linearize 4
///@tparam T5 - Type of parameter to linearize 5
///@tparam T6 - Type of parameter to linearize 6
///@tparam T7 - Type of parameter to linearize 7
///@tparam T8 - Type of parameter to linearize 8
///@tparam T9 - Type of parameter to linearize 9
///@tparam T10 - Type of parameter to linearize 10
///@tparam T11 - Type of parameter to linearize 11
///@tparam T12 - Type of parameter to linearize 12
///@tparam T13 - Type of parameter to linearize 13
///@tparam T14 - Type of parameter to linearize 14
///@tparam T15 - Type of parameter to linearize 15
///@tparam T16 - Type of parameter to linearize 16
///@tparam T17 - Type of parameter to linearize 17
///@tparam T18 - Type of parameter to linearize 18
///@tparam T19 - Type of parameter to linearize 19
///@tparam T20 - Type of parameter to linearize 20
///@tparam T21 - Type of parameter to linearize 21
///@tparam T22 - Type of parameter to linearize 22
///
template <
    class T1,
    class T2,
    class T3,
    class T4,
    class T5,
    class T6,
    class T7,
    class T8,
    class T9,
    class T10,
    class T11,
    class T12,
    class T13,
    class T14,
    class T15,
    class T16,
    class T17,
    class T18,
    class T19,
    class T20,
    class T21,
    class T22>
struct TGenTypeList22
{
    using type = vfc::TTypeList<
        T1,
        typename vfc::TGenTypeList21<
            T2,
            T3,
            T4,
            T5,
            T6,
            T7,
            T8,
            T9,
            T10,
            T11,
            T12,
            T13,
            T14,
            T15,
            T16,
            T17,
            T18,
            T19,
            T20,
            T21,
            T22>::type>;
};

//=========================================================================
//  TGenTypeList23
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation
/// of twenty-three types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///@tparam T4 - Type of parameter to linearize 4
///@tparam T5 - Type of parameter to linearize 5
///@tparam T6 - Type of parameter to linearize 6
///@tparam T7 - Type of parameter to linearize 7
///@tparam T8 - Type of parameter to linearize 8
///@tparam T9 - Type of parameter to linearize 9
///@tparam T10 - Type of parameter to linearize 10
///@tparam T11 - Type of parameter to linearize 11
///@tparam T12 - Type of parameter to linearize 12
///@tparam T13 - Type of parameter to linearize 13
///@tparam T14 - Type of parameter to linearize 14
///@tparam T15 - Type of parameter to linearize 15
///@tparam T16 - Type of parameter to linearize 16
///@tparam T17 - Type of parameter to linearize 17
///@tparam T18 - Type of parameter to linearize 18
///@tparam T19 - Type of parameter to linearize 19
///@tparam T20 - Type of parameter to linearize 20
///@tparam T21 - Type of parameter to linearize 21
///@tparam T22 - Type of parameter to linearize 22
///@tparam T23 - Type of parameter to linearize 23
///
template <
    class T1,
    class T2,
    class T3,
    class T4,
    class T5,
    class T6,
    class T7,
    class T8,
    class T9,
    class T10,
    class T11,
    class T12,
    class T13,
    class T14,
    class T15,
    class T16,
    class T17,
    class T18,
    class T19,
    class T20,
    class T21,
    class T22,
    class T23>
struct TGenTypeList23
{
    using type = vfc::TTypeList<
        T1,
        typename vfc::TGenTypeList22<
            T2,
            T3,
            T4,
            T5,
            T6,
            T7,
            T8,
            T9,
            T10,
            T11,
            T12,
            T13,
            T14,
            T15,
            T16,
            T17,
            T18,
            T19,
            T20,
            T21,
            T22,
            T23>::type>;
};

//=========================================================================
//  TGenTypeList24
//-------------------------------------------------------------------------
/// typelist generator for linearizing typelist creation
/// of twenty-four types.
/// @sa TGenTypeList1
//=========================================================================
///
///
///@tparam T1 - Type of parameter to linearize 1
///@tparam T2 - Type of parameter to linearize 2
///@tparam T3 - Type of parameter to linearize 3
///@tparam T4 - Type of parameter to linearize 4
///@tparam T5 - Type of parameter to linearize 5
///@tparam T6 - Type of parameter to linearize 6
///@tparam T7 - Type of parameter to linearize 7
///@tparam T8 - Type of parameter to linearize 8
///@tparam T9 - Type of parameter to linearize 9
///@tparam T10 - Type of parameter to linearize 10
///@tparam T11 - Type of parameter to linearize 11
///@tparam T12 - Type of parameter to linearize 12
///@tparam T13 - Type of parameter to linearize 13
///@tparam T14 - Type of parameter to linearize 14
///@tparam T15 - Type of parameter to linearize 15
///@tparam T16 - Type of parameter to linearize 16
///@tparam T17 - Type of parameter to linearize 17
///@tparam T18 - Type of parameter to linearize 18
///@tparam T19 - Type of parameter to linearize 19
///@tparam T20 - Type of parameter to linearize 20
///@tparam T21 - Type of parameter to linearize 21
///@tparam T22 - Type of parameter to linearize 22
///@tparam T23 - Type of parameter to linearize 23
///@tparam T24 - Type of parameter to linearize 24
///
template <
    class T1,
    class T2,
    class T3,
    class T4,
    class T5,
    class T6,
    class T7,
    class T8,
    class T9,
    class T10,
    class T11,
    class T12,
    class T13,
    class T14,
    class T15,
    class T16,
    class T17,
    class T18,
    class T19,
    class T20,
    class T21,
    class T22,
    class T23,
    class T24>
struct TGenTypeList24
{
    using type = vfc::TTypeList<
        T1,
        typename vfc::TGenTypeList23<
            T2,
            T3,
            T4,
            T5,
            T6,
            T7,
            T8,
            T9,
            T10,
            T11,
            T12,
            T13,
            T14,
            T15,
            T16,
            T17,
            T18,
            T19,
            T20,
            T21,
            T22,
            T23,
            T24>::type>;
};

//=========================================================================
//  DOXYGEN DEFGROUP vfc_group_core_generic_typelist END
//-------------------------------------------------------------------------
/// @}
//=========================================================================

} // namespace vfc

#include "vfc/core/vfc_typelist.inl"

#endif // VFC_TYPELIST_HPP_INCLUDED

//=============================================================================

