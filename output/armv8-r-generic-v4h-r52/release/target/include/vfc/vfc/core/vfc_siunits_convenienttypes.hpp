//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_SIUNITS_CONVENIENTTYPES_HPP_INCLUDED
#define VFC_SIUNITS_CONVENIENTTYPES_HPP_INCLUDED

#include "vfc/core/vfc_siunits.hpp"
#include "vfc/core/vfc_siunits_types.hpp"
#include "vfc/core/vfc_preprocessor.hpp"

// workaround for (potential) GHS bug in 201815 which is triggered
// by the unknown sizeof of the convenience types in virtual base-classes
#if defined(VFC_COMPILER_GHS) && (__GHS_VERSION_NUMBER >= 201815)
#ifdef VFC_ENABLE_FLOAT64_TYPE
#define VFC_FORCE_CONVENIENCE_TYPES_SIZEOF(NAME)                                                                       \
    ;                                                                                                                  \
    enum ESIConvenienceSizes##NAME : vfc::int32_t                                                                      \
    {                                                                                                                  \
        SIZE_##NAME##_i8_t = sizeof(si_##NAME##_i8_t), SIZE_##NAME##_ui8_t = sizeof(si_##NAME##_ui8_t),                \
        SIZE_##NAME##_i16_t = sizeof(si_##NAME##_i16_t), SIZE_##NAME##_ui16_t = sizeof(si_##NAME##_ui16_t),            \
        SIZE_##NAME##_i32_t = sizeof(si_##NAME##_i32_t), SIZE_##NAME##_ui32_t = sizeof(si_##NAME##_ui32_t),            \
        SIZE_##NAME##_f32_t = sizeof(si_##NAME##_f32_t), SIZE_##NAME##_ui64_t = sizeof(si_##NAME##_ui64_t),            \
        SIZE_##NAME##_f64_t = sizeof(si_##NAME##_f64_t)                                                                \
    }
#else
#define VFC_FORCE_CONVENIENCE_TYPES_SIZEOF(NAME)                                                                       \
    ;                                                                                                                  \
    enum ESIConvenienceSizes##NAME : vfc::int32_t                                                                      \
    {                                                                                                                  \
        SIZE_##NAME##_i8_t = sizeof(si_##NAME##_i8_t), SIZE_##NAME##_ui8_t = sizeof(si_##NAME##_ui8_t),                \
        SIZE_##NAME##_i16_t = sizeof(si_##NAME##_i16_t), SIZE_##NAME##_ui16_t = sizeof(si_##NAME##_ui16_t),            \
        SIZE_##NAME##_i32_t = sizeof(si_##NAME##_i32_t), SIZE_##NAME##_ui32_t = sizeof(si_##NAME##_ui32_t),            \
        SIZE_##NAME##_f32_t = sizeof(si_##NAME##_f32_t), SIZE_##NAME##_ui64_t = sizeof(si_##NAME##_ui64_t)             \
    }
#endif // VFC_ENABLE_FLOAT64_TYPE
#else
#define VFC_FORCE_CONVENIENCE_TYPES_SIZEOF(NAME)
#endif

// qacpp-1110-concat: The result of the concatenation operator (##) is independent of the order of evaluation.

#ifdef VFC_ENABLE_FLOAT64_TYPE
#define CONVENIENCE_TYPE(UNIT, RBINFO, NAME) /* PRQA S 1110 # concat */                                                \
    using si_##NAME##_i8_t =                                                                                           \
        vfc::TSIUnits<vfc::int8_t, vfc::info_##UNIT, vfc::info_##RBINFO, vfc::CDefaultType, vfc::TConvert>;            \
    using si_##NAME##_ui8_t =                                                                                          \
        vfc::TSIUnits<vfc::uint8_t, vfc::info_##UNIT, vfc::info_##RBINFO, vfc::CDefaultType, vfc::TConvert>;           \
    using si_##NAME##_i16_t =                                                                                          \
        vfc::TSIUnits<vfc::int16_t, vfc::info_##UNIT, vfc::info_##RBINFO, vfc::CDefaultType, vfc::TConvert>;           \
    using si_##NAME##_ui16_t =                                                                                         \
        vfc::TSIUnits<vfc::uint16_t, vfc::info_##UNIT, vfc::info_##RBINFO, vfc::CDefaultType, vfc::TConvert>;          \
    using si_##NAME##_i32_t =                                                                                          \
        vfc::TSIUnits<vfc::int32_t, vfc::info_##UNIT, vfc::info_##RBINFO, vfc::CDefaultType, vfc::TConvert>;           \
    using si_##NAME##_ui32_t =                                                                                         \
        vfc::TSIUnits<vfc::uint32_t, vfc::info_##UNIT, vfc::info_##RBINFO, vfc::CDefaultType, vfc::TConvert>;          \
    using si_##NAME##_f32_t =                                                                                          \
        vfc::TSIUnits<vfc::float32_t, vfc::info_##UNIT, vfc::info_##RBINFO, vfc::CDefaultType, vfc::TConvert>;         \
    using si_##NAME##_ui64_t =                                                                                         \
        vfc::TSIUnits<vfc::uint64_t, vfc::info_##UNIT, vfc::info_##RBINFO, vfc::CDefaultType, vfc::TConvert>;          \
    using si_##NAME##_f64_t =                                                                                          \
        vfc::TSIUnits<vfc::float64_t, vfc::info_##UNIT, vfc::info_##RBINFO, vfc::CDefaultType, vfc::TConvert>          \
            VFC_FORCE_CONVENIENCE_TYPES_SIZEOF(NAME)
#else
#define CONVENIENCE_TYPE(UNIT, RBINFO, NAME) /* PRQA S 1110 # concat */                                                \
    using si_##NAME##_i8_t =                                                                                           \
        vfc::TSIUnits<vfc::int8_t, vfc::info_##UNIT, vfc::info_##RBINFO, vfc::CDefaultType, vfc::TConvert>;            \
    using si_##NAME##_ui8_t =                                                                                          \
        vfc::TSIUnits<vfc::uint8_t, vfc::info_##UNIT, vfc::info_##RBINFO, vfc::CDefaultType, vfc::TConvert>;           \
    using si_##NAME##_i16_t =                                                                                          \
        vfc::TSIUnits<vfc::int16_t, vfc::info_##UNIT, vfc::info_##RBINFO, vfc::CDefaultType, vfc::TConvert>;           \
    using si_##NAME##_ui16_t =                                                                                         \
        vfc::TSIUnits<vfc::uint16_t, vfc::info_##UNIT, vfc::info_##RBINFO, vfc::CDefaultType, vfc::TConvert>;          \
    using si_##NAME##_i32_t =                                                                                          \
        vfc::TSIUnits<vfc::int32_t, vfc::info_##UNIT, vfc::info_##RBINFO, vfc::CDefaultType, vfc::TConvert>;           \
    using si_##NAME##_ui32_t =                                                                                         \
        vfc::TSIUnits<vfc::uint32_t, vfc::info_##UNIT, vfc::info_##RBINFO, vfc::CDefaultType, vfc::TConvert>;          \
    using si_##NAME##_f32_t =                                                                                          \
        vfc::TSIUnits<vfc::float32_t, vfc::info_##UNIT, vfc::info_##RBINFO, vfc::CDefaultType, vfc::TConvert>;         \
    using si_##NAME##_ui64_t =                                                                                         \
        vfc::TSIUnits<vfc::uint64_t, vfc::info_##UNIT, vfc::info_##RBINFO, vfc::CDefaultType, vfc::TConvert>           \
            VFC_FORCE_CONVENIENCE_TYPES_SIZEOF(NAME)
#endif // VFC_ENABLE_FLOAT64_TYPE

namespace vfc
{
//============================================================
// SIUnits common type definitions, frequently used
//============================================================

namespace CSI
{
///////////////////////////////////////////////
///////////////// slope ///////////////////////
///////////////////////////////////////////////
CONVENIENCE_TYPE(slope_t, rbnil_type, slope);
// the word scalar should to be more intuitive for most developers
CONVENIENCE_TYPE(scalar_t, rbnil_type, scalar);

///////////////////////////////////////////////
///////////////// pixel ///////////////////////
///////////////////////////////////////////////
CONVENIENCE_TYPE(nil_type, pixel_t, pixel);
CONVENIENCE_TYPE(nil_type, square_pixel_t, square_pixel);
CONVENIENCE_TYPE(nil_type, per_pixel_t, per_pixel);
CONVENIENCE_TYPE(nil_type, per_square_pixel_t, per_square_pixel);
CONVENIENCE_TYPE(per_radian_t, pixel_t, pixel_per_radian);
CONVENIENCE_TYPE(metre_t, pixel_t, metre_pixel);

///////////////////////////////////////////////
///////////////// length //////////////////////
///////////////////////////////////////////////
// power = 1
CONVENIENCE_TYPE(metre_t, rbnil_type, metre);
CONVENIENCE_TYPE(kilo_metre_t, rbnil_type, kilo_metre);
CONVENIENCE_TYPE(mega_metre_t, rbnil_type, mega_metre);
CONVENIENCE_TYPE(giga_metre_t, rbnil_type, giga_metre);
CONVENIENCE_TYPE(tera_metre_t, rbnil_type, tera_metre);
CONVENIENCE_TYPE(deci_metre_t, rbnil_type, deci_metre);
CONVENIENCE_TYPE(centi_metre_t, rbnil_type, centi_metre);
CONVENIENCE_TYPE(milli_metre_t, rbnil_type, milli_metre);
CONVENIENCE_TYPE(micro_metre_t, rbnil_type, micro_metre);
CONVENIENCE_TYPE(nano_metre_t, rbnil_type, nano_metre);
// power = 2
CONVENIENCE_TYPE(square_metre_t, rbnil_type, square_metre);
CONVENIENCE_TYPE(square_kilo_metre_t, rbnil_type, square_kilo_metre);
// power = 3
CONVENIENCE_TYPE(cubic_metre_t, rbnil_type, cubic_metre);
CONVENIENCE_TYPE(cubic_kilo_metre_t, rbnil_type, cubic_kilo_metre);
// power = -1
CONVENIENCE_TYPE(per_metre_t, rbnil_type, per_metre);
// power = -2
CONVENIENCE_TYPE(per_square_metre_t, rbnil_type, per_square_metre);

// imperial
CONVENIENCE_TYPE(mile_t, rbnil_type, mile);

///////////////////////////////////////////////
///////////////// time ////////////////////////
///////////////////////////////////////////////
// power = 1
CONVENIENCE_TYPE(second_t, rbnil_type, second);
CONVENIENCE_TYPE(minute_t, rbnil_type, minute);
CONVENIENCE_TYPE(hour_t, rbnil_type, hour);
CONVENIENCE_TYPE(nano_second_t, rbnil_type, nano_second);
CONVENIENCE_TYPE(milli_second_t, rbnil_type, milli_second);
CONVENIENCE_TYPE(micro_second_t, rbnil_type, micro_second);
// power = -1
CONVENIENCE_TYPE(per_second_t, rbnil_type, per_second);
CONVENIENCE_TYPE(per_minute_t, rbnil_type, per_minute);
CONVENIENCE_TYPE(per_hour_t, rbnil_type, per_hour);
CONVENIENCE_TYPE(per_milli_second_t, rbnil_type, per_milli_second);
CONVENIENCE_TYPE(per_micro_second_t, rbnil_type, per_micro_second);
// power = -2
CONVENIENCE_TYPE(per_square_second_t, rbnil_type, per_square_second);
CONVENIENCE_TYPE(per_square_hour_t, rbnil_type, per_square_hour);
// power = 2
CONVENIENCE_TYPE(square_second_t, rbnil_type, square_second);

///////////////////////////////////////////////
///////////////// angles //////////////////////
///////////////////////////////////////////////
CONVENIENCE_TYPE(degree_t, rbnil_type, degree);
CONVENIENCE_TYPE(radian_t, rbnil_type, radian);
CONVENIENCE_TYPE(square_radian_t, rbnil_type, square_radian);
CONVENIENCE_TYPE(per_square_radian_t, rbnil_type, per_square_radian);
CONVENIENCE_TYPE(per_radian_t, rbnil_type, per_radian);
CONVENIENCE_TYPE(radian_per_second_t, rbnil_type, radian_per_second);
CONVENIENCE_TYPE(degree_per_second_t, rbnil_type, degree_per_second);
CONVENIENCE_TYPE(radian_per_square_second_t, rbnil_type, radian_per_square_second);
CONVENIENCE_TYPE(square_radian_per_square_second_t, rbnil_type, square_radian_per_square_second);

///////////////////////////////////////////////
///////////////// mass ////////////////////////
///////////////////////////////////////////////
CONVENIENCE_TYPE(gram_t, rbnil_type, gram);
CONVENIENCE_TYPE(kilo_gram_t, rbnil_type, kilo_gram);
CONVENIENCE_TYPE(ton_t, rbnil_type, ton);
CONVENIENCE_TYPE(milli_gram_t, rbnil_type, milli_gram);
CONVENIENCE_TYPE(micro_gram_t, rbnil_type, micro_gram);

///////////////////////////////////////////////
///////////////// temperature /////////////////
///////////////////////////////////////////////
CONVENIENCE_TYPE(kelvin_t, rbnil_type, kelvin);
CONVENIENCE_TYPE(celsius_t, rbnil_type, celsius);

///////////////////////////////////////////////
///////////////// current /////////////////////
///////////////////////////////////////////////
CONVENIENCE_TYPE(ampere_t, rbnil_type, ampere);
CONVENIENCE_TYPE(deci_ampere_t, rbnil_type, deci_ampere);
CONVENIENCE_TYPE(centi_ampere_t, rbnil_type, centi_ampere);
CONVENIENCE_TYPE(kilo_ampere_t, rbnil_type, kilo_ampere);
CONVENIENCE_TYPE(mega_ampere_t, rbnil_type, mega_ampere);
CONVENIENCE_TYPE(giga_ampere_t, rbnil_type, giga_ampere);
CONVENIENCE_TYPE(tera_ampere_t, rbnil_type, tera_ampere);
CONVENIENCE_TYPE(milli_ampere_t, rbnil_type, milli_ampere);
CONVENIENCE_TYPE(micro_ampere_t, rbnil_type, micro_ampere);
CONVENIENCE_TYPE(nano_ampere_t, rbnil_type, nano_ampere);
CONVENIENCE_TYPE(per_ampere_t, rbnil_type, per_ampere);

///////////////////////////////////////////////
//////////// luminous intensity ///////////////
///////////////////////////////////////////////
CONVENIENCE_TYPE(candela_t, rbnil_type, candela);
CONVENIENCE_TYPE(deci_candela_t, rbnil_type, deci_candela);
CONVENIENCE_TYPE(centi_candela_t, rbnil_type, centi_candela);
CONVENIENCE_TYPE(kilo_candela_t, rbnil_type, kilo_candela);
CONVENIENCE_TYPE(mega_candela_t, rbnil_type, mega_candela);
CONVENIENCE_TYPE(giga_candela_t, rbnil_type, giga_candela);
CONVENIENCE_TYPE(tera_candela_t, rbnil_type, tera_candela);
CONVENIENCE_TYPE(milli_candela_t, rbnil_type, milli_candela);
CONVENIENCE_TYPE(micro_candela_t, rbnil_type, micro_candela);
CONVENIENCE_TYPE(nano_candela_t, rbnil_type, nano_candela);
CONVENIENCE_TYPE(per_candela_t, rbnil_type, per_candela);

///////////////////////////////////////////////
////////////////// force //////////////////////
///////////////////////////////////////////////
CONVENIENCE_TYPE(newton_t, rbnil_type, newton);

///////////////////////////////////////////////////////////////////////////
//////////////////// Commonly used types //////////////////////////////////
///////////////////////////////////////////////////////////////////////////

CONVENIENCE_TYPE(joule_t, rbnil_type, joule);
CONVENIENCE_TYPE(lux_t, rbnil_type, lux);
CONVENIENCE_TYPE(metre_per_second_t, rbnil_type, metre_per_second);
CONVENIENCE_TYPE(miles_per_hour_t, rbnil_type, miles_per_hour);
CONVENIENCE_TYPE(per_metre_second_t, rbnil_type, per_metre_second);
CONVENIENCE_TYPE(metre_per_cubic_second_t, rbnil_type, metre_per_cubic_second);
CONVENIENCE_TYPE(kilometre_per_hour_t, rbnil_type, kilometre_per_hour);
CONVENIENCE_TYPE(metre_per_square_second_t, rbnil_type, metre_per_square_second);
CONVENIENCE_TYPE(per_square_metre_second_t, rbnil_type, per_square_metre_second);
CONVENIENCE_TYPE(kilometre_per_square_hour_t, rbnil_type, kilometre_per_square_hour);
CONVENIENCE_TYPE(per_second_t, rbnil_type, hertz);
CONVENIENCE_TYPE(joule_t, rbnil_type, newton_metre);
CONVENIENCE_TYPE(per_metre_t, rbnil_type, wavenumber);
CONVENIENCE_TYPE(nil_type, percentage_t, percent);
CONVENIENCE_TYPE(per_second_t, percentage_t, percent_per_second);
CONVENIENCE_TYPE(force_of_gravity_t, percentage_t, percent_of_force_of_gravity);
CONVENIENCE_TYPE(square_metre_per_square_second_t, rbnil_type, square_metre_per_square_second);

using si_pix_per_rad = si_pixel_per_radian_f32_t;

} // namespace CSI

} // namespace vfc

#endif // VFC_SIUNITS_CONVENIENTTYPES_HPP_INCLUDED

//=============================================================================

