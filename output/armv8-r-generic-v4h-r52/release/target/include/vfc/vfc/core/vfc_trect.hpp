//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_TRECT_HPP_INCLUDED
#define VFC_TRECT_HPP_INCLUDED

//-----------------------------------------------------------------------------
// system includes
//-----------------------------------------------------------------------------

//-----------------------------------------------------------------------------
// includes for identifiers needed to be known by definition
//-----------------------------------------------------------------------------
#include "vfc/core/vfc_types.hpp"       // used for int32_t
#include "vfc/core/vfc_type_traits.hpp" // used for type traits

//-----------------------------------------------------------------------------
// forward declarations for identifiers needed to be known by name only
//-----------------------------------------------------------------------------

//  === BEGIN_SPEC =============================================================================
/// vfc templated rectangular class
//  --------------------------------------------------------------------------------------------
/// @brief vfc templated rectangular class
/// @par Reference (CS-CRM / DOORS ID / EA ...):
///   link(s) to related requirement(s), work package(s), test case(s), architecture etc
/// @par Description:
///   A TRect defines the coordinates of the (inside) upper-left and (outside)
///      lower-right corners of a rectangle.
/// @par Error handling:
/// @par Timing/Scheduling constraints:
/// @pre
/// @post
/// @par Input:
/// @par Output:
/// @par Return Value:
/// $Source: vfc_trect.hpp $

/// @ingroup           vfc_group_core_types
//  === END_SPEC ================================================================================

//-----------------------------------------------------------------------------
// function/class definitions
//-----------------------------------------------------------------------------

namespace vfc
{
// namespace vfc opened

//======================================================
// TPoint
//------------------------------------------------------
/// The TPoint structure defines the integer x- and y-coordinates
/// of a 2d point.
/// @par Reference (CS-CRM / DOORS ID / EA):
/// $Source: vfc_trect.hpp $

/// @ingroup vfc_group_core_types
/// @tparam ValueType data type for member and interfaces
//======================================================
template <class ValueType>
class TPoint
{
  public:
    using value_type = ValueType;

    //---------------------------------------------------------------------
    /// Default constructor
    /// default c'tor, initializes x and y member variables with zero.
    /// @dspec{1595}     The SW component "vfc" shall create a new point instance.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    TPoint(void);

    //---------------------------------------------------------------------
    /// Constructor
    /// c'tor, initializes x and y member variables.
    /// @dspec{755}     The SW component "vfc" shall create a point instance where the x
    ///                 coordinate will be set to the given x value and the y coordinate will
    ///                 be set to the given y value.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @param f_x value for x-axis
    /// @param f_y value for y-axis
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    TPoint(value_type f_x, value_type f_y);

    //---------------------------------------------------------------------
    /// operator +=
    /// Offsets TPoint by adding specified position
    /// @dspec{1229}     The SW component "vfc" shall add the coordinates of two given points
    ///                  and write the added coordinates to the first given point.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @param f_rhs TPoint instance that will be added to this
    /// @return const reference to modified TPoint
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    const TPoint& operator+=(const TPoint& f_rhs);

    //---------------------------------------------------------------------
    /// operator -=
    /// Offsets TPoint by subtracting specified position
    /// @dspec{1228}     The SW component "vfc" shall subtract the coordinates of the second
    ///                  given point from the first given point and write the subtracted coordinates
    ///                  to the first given point.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @param f_rhs TPoint instance that will be subtracted from this
    /// @return const reference to modified TPoint
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    const TPoint& operator-=(const TPoint& f_rhs);

    //---------------------------------------------------------------------
    /// Get value of x axis
    /// @dspec{756}     The SW component "vfc" shall return the x coordinate of a given point.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return value by reference for x-axis
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    value_type& x(void) { return m_x; } // PRQA S 4024 # Technical debt L2 id=00c1742773961c4d4939-4055283d805fe17a

    //---------------------------------------------------------------------
    /// Get value of x axis. const interface
    /// @dspecref{756}
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return value for x-axis
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    value_type x(void) const { return m_x; }

    //---------------------------------------------------------------------
    /// Get value of y axis
    /// @dspec{1964}     The SW component "vfc" shall return the y coordinate of a given point.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return value by reference for y-axis
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    value_type& y(void) { return m_y; } // PRQA S 4024 # Technical debt L2 id=00c18a217c5cc65f4f48-d6b8ef45ea7d400f

    //---------------------------------------------------------------------
    /// Get value of y axis. const interface
    /// @dspecref{1964}
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return value for y-axis
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    value_type y(void) const { return m_y; }

  private:
    value_type m_x; ///< x-coordinate
    value_type m_y; ///< y-coordinate
};

// traits specialization
template <typename TType>
struct THasTrivialDTor<TPoint<TType>> : THasTrivialDTor<TType>
{
};
template <typename TType>
struct THasTrivialCopy<TPoint<TType>> : THasTrivialCopy<TType>
{
};
template <typename TType>
struct THasTrivialSetZero<TPoint<TType>> : THasTrivialSetZero<TType>
{
};

//---------------------------------------------------------------------
/// @dspec{1642}     The SW component "vfc" shall add the coordinates of two given points and
///                  return a new point instance with the resulting coordinates.
/// Returns the sum of two points. @relatesalso TPoint
/// @par Reference (CS-CRM / DOORS ID / EA):
/// @tparam ValueType data type of TPoint
/// @param f_op1 TPoint value on the left-hand-side of expression
/// @param f_op2 TPoint value on the right-hand-side of expression
/// @return const value TPoint of sum
/// $Source: vfc_trect.hpp $

/// @ingroup vfc_group_core_types
//---------------------------------------------------------------------
template <class ValueType>
const TPoint<ValueType> operator+(const TPoint<ValueType>& f_op1, const TPoint<ValueType>& f_op2);

//---------------------------------------------------------------------
/// @dspec{1643}     The SW component "vfc" shall subtract the coordinates of the second given
///                  point from the first given point and return a new point instance with the
///                  resulting coordinates.
/// Returns the difference of two points. @relatesalso TPoint
/// @par Reference (CS-CRM / DOORS ID / EA):
/// @tparam ValueType data type of TPoint
/// @param f_op1 TPoint value on the left-hand-side of expression
/// @param f_op2 TPoint value on the right-hand-side of expression
/// @return const value TPoint of difference
/// $Source: vfc_trect.hpp $

/// @ingroup vfc_group_core_types
//---------------------------------------------------------------------
template <class ValueType>
const TPoint<ValueType> operator-(const TPoint<ValueType>& f_op1, const TPoint<ValueType>& f_op2);

//=========================================================================
// TSize
//-------------------------------------------------------------------------
/// The TSize class implements a relative coordinate or extent (eg the
/// width and height of a rectangle).
/// @sa TRect
/// @par Reference (CS-CRM / DOORS ID / EA):
/// $Source: vfc_trect.hpp $

/// @ingroup vfc_group_core_types
/// @tparam ValueType data type for member and interfaces
//=========================================================================
template <class ValueType>
class TSize
{
  public:
    using value_type = ValueType;

    //---------------------------------------------------------------------
    /// Default constructor
    /// default c'tor, initializes cx and cy member variables with zero
    /// @dspec{1639}     The SW component "vfc" shall create and return a size instance where
    ///                  both dimensions are zero.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    TSize(void);

    //---------------------------------------------------------------------
    /// Default constructor
    /// default c'tor, initializes cx and cy member variables.
    /// @dspec{799}     The SW component "vfc" shall create and return a size instance
    ///                 from two given values which represent the x and y dimensions.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @param f_cx value for x-axis
    /// @param f_cy value for y-axis
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    TSize(value_type f_cx, value_type f_cy);

    //---------------------------------------------------------------------
    /// operator +=
    /// Adds a size to TSize.
    /// @dspec{1309}     The SW component "vfc" shall add two given size instances component-wise
    ///                  and store the resulting dimension in the first given instance.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @param f_rhs TSize instance that will be added to this
    /// @return const reference to modified TSize
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    const TSize& operator+=(const TSize& f_rhs);

    //---------------------------------------------------------------------
    /// operator -=
    /// Subtracts a size to TSize.
    /// @dspec{1308}     The SW component "vfc" shall subtract from the first given size
    ///                  instance another given size instance component-wise and store the
    ///                  resulting dimensions in the first given instance.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @param f_rhs TSize instance that will be subtracted from this
    /// @return const reference to modified TSize
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    const TSize& operator-=(const TSize& f_rhs);

    //---------------------------------------------------------------------
    /// Return value of x axis
    /// @dspec{1307}     The SW component "vfc" shall return the size dimension parameters
    ///                  of a given size instance by means of cx and cy.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return value by reference for x-axis
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    value_type& cx(void) { return m_cx; } // PRQA S 4024 # Technical debt L2 id=00c1fd97310a8b384c75-6aa3e79ebb1b035b

    //---------------------------------------------------------------------
    /// Return value of x axis. const interface
    /// @dspecref{1307}
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return value for x-axis
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    value_type cx(void) const { return m_cx; }

    //---------------------------------------------------------------------
    /// Return value of y axis.
    /// @dspecref{1307}
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return value by reference for y-axis
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    value_type& cy(void) { return m_cy; } // PRQA S 4024 # Technical debt L2 id=00c1c864d7a599224be4-36b0de0ee9c51e50

    //---------------------------------------------------------------------
    /// Return value of y axis. const interface
    /// @dspecref{1307}
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return value for y-axis
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    value_type cy(void) const { return m_cy; }

  private:
    value_type m_cx; ///< extent in x-direction
    value_type m_cy; ///< extent in y-direction
};

// traits specialization
template <typename TType>
struct THasTrivialDTor<TSize<TType>> : THasTrivialDTor<TType>
{
};
template <typename TType>
struct THasTrivialCopy<TSize<TType>> : THasTrivialCopy<TType>
{
};
template <typename TType>
struct THasTrivialSetZero<TSize<TType>> : THasTrivialSetZero<TType>
{
};

//---------------------------------------------------------------------
/// @dspec{1640}     The SW component "vfc" shall add two given size instances
///                  component-wise and return a new size instance with the resulting dimensions.
/// returns the sum of two sizes. @relatesalso TSize
/// @par Reference (CS-CRM / DOORS ID / EA):
/// @tparam ValueType data type of TSize
/// @param f_op1 TSize value on the left-hand-side of expression
/// @param f_op2 TSize value on the right-hand-side of expression
/// @return const value TSize of sum
/// $Source: vfc_trect.hpp $

/// @ingroup vfc_group_core_types
//---------------------------------------------------------------------
template <class ValueType>
const TSize<ValueType> operator+(const TSize<ValueType>& f_op1, const TSize<ValueType>& f_op2);

//---------------------------------------------------------------------
/// @dspec{1641}     The SW component "vfc" shall subtract from the first given size
///                  instance another given size instance component-wise and return a
///                  new size instance with the resulting dimensions.
/// returns the difference of two sizes. @relatesalso TSize
/// @par Reference (CS-CRM / DOORS ID / EA):
/// @tparam ValueType data type of TSize
/// @param f_op1 TSize value on the left-hand-side of expression
/// @param f_op2 TSize value on the right-hand-side of expression
/// @return const value TSize of difference
/// $Source: vfc_trect.hpp $

/// @ingroup vfc_group_core_types
//---------------------------------------------------------------------
template <class ValueType>
const TSize<ValueType> operator-(const TSize<ValueType>& f_op1, const TSize<ValueType>& f_op2);

//=========================================================================
// TRect
//-------------------------------------------------------------------------
/// A TRect defines the coordinates of the (inside) upper-left and (outside)
/// lower-right corners of a rectangle.
/// Keep in mind that the upper left corner is inside the rectangle, while
/// the lower right is 1 pixel to the right and below the last pixel in the
/// rectangle and therefore outside of the rectangle area.
/// @par
/// @image html core-rect.png
///
/// When specifying a TRect, you must be careful to construct it so that it
/// is normalized - in other words, such that the value of the left coordinate
/// is less than the right and the top is less than the bottom.
/// For example, a top left of (10,10) and bottom right of (20,20) defines a
/// normalized rectangle but a top left of (20,20) and bottom right of (10,10)
/// defines a non-normalized rectangle.
/// If the rectangle is not normalized, many TRect member functions may
/// return incorrect results.
/// Especially an isEmpty() for a not normalized TRect will return true, as
/// a not normalized rect is interpreted as "not existing" aka "a hole".
/// Before you call a function that requires normalized rectangles, you can
/// normalize non-normalized rectangles by calling the normalize() function.
/// @sa
/// - TPoint
/// - TSize
/// @par Reference (CS-CRM / DOORS ID / EA):
/// $Source: vfc_trect.hpp $

/// @ingroup vfc_group_core_types
/// @tparam ValueType data type for member and interfaces
//=========================================================================
template <class ValueType>
class TRect
{
  public:
    using value_type = ValueType;

    using area_type = typename TRectAreaTypeTraits<ValueType>::area_type;

    //---------------------------------------------------------------------
    /// Default constructor
    /// default, c'tor - sets top-left to origin and width and height to zero.
    /// @dspec{1599}     The SW component "vfc" shall create a new rectangle instance
    ///                  with zero origin coordinates and zero height and width.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    TRect(void);

    //---------------------------------------------------------------------
    /// Constructs
    /// constructs TRect with top-left corner and specified extent.
    /// @dspec{766}     The SW component "vfc" shall create a rectangle instance with
    ///                 a given left position, a given top position, a given width and
    ///                 a given height.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @param  f_left      Specifies the left position of TRect
    /// @param  f_top       Specifies the top of TRect.
    /// @param  f_width     Specifies the extent in x-direction.
    /// @param  f_height    Specifies the extent in y-direction.
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    TRect(value_type f_left, value_type f_top, value_type f_width, value_type f_height);

    //---------------------------------------------------------------------
    /// Constructs
    /// constructs TRect with top-left corner and specified extent.
    /// @dspec{767}     The SW component "vfc" shall create a rectangle instance with a
    ///                 given point and size.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @param  f_topLeft   Specifies the origin point for the rectangle.
    ///                     Corresponds to the top-left corner.
    /// @param  f_size      Specifies the displacement from the top-left
    ///                     corner to the bottom-right corner.
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    TRect(const TPoint<ValueType>& f_topLeft, const TSize<ValueType>& f_size);

    //---------------------------------------------------------------------
    /// Constructs
    /// constructs TRect with top-left (inside) and bottom-right (outside)
    /// @dspec{1248}     The SW component "vfc" shall create a rectangle instance from a
    ///                  given point that specifies the top left corner and a given point
    ///                  that specifies the bottom right corner of the rectangle.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @param  f_topLeft       Specifies the top-left (inside) position
    ///                         of TRect.
    /// @param  f_bottomRight   Specifies the bottom-right (outside)
    ///                         position of TRect.
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    TRect(const TPoint<ValueType>& f_topLeft, const TPoint<ValueType>& f_bottomRight);

    //---------------------------------------------------------------------
    /// Returns the left position (inside) of TRect (read only). const interface.
    /// @dspec{1247}     The SW component "vfc" shall return the positional parameters of a
    ///                  given rectangle by means of left, top, right, bottom.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return value of the left position
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    value_type left(void) const { return m_left; }

    //---------------------------------------------------------------------
    /// Returns the left position (inside) of TRect (read/write).
    /// @dspecref{1247}
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return value by reference of the left position
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    value_type& left(void)
    {
        return m_left;
    } // PRQA S 4024 # Technical debt L2 id=00c1f11ed9cfdc6043ef-0900c05f6cd7b66f

    //---------------------------------------------------------------------
    /// returns the top (inside) of TRect (read only). const interface.
    /// @dspecref{1247}
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return value of the top position
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    value_type top(void) const { return m_top; }

    //---------------------------------------------------------------------
    /// returns the top (inside) of TRect (read/write).
    /// @dspecref{1247}
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return value by reference of the top position
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    value_type& top(void) { return m_top; } // PRQA S 4024 # Technical debt L2 id=00c1c8eadc4b65964d1e-c6bc3f6d5e3f24a2

    //---------------------------------------------------------------------
    /// returns the width of TRect (read only).
    /// @dspec{1245}     The SW component "vfc" shall return the values of the dimension
    ///                  parameters of a given rectangle by means of width, and height.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return value of the width
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    value_type width(void) const { return m_width; }

    //---------------------------------------------------------------------
    /// returns the width of TRect (read/write).
    /// @dspecref{1245}
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return value by reference of the width
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    value_type& width(void)
    {
        return m_width;
    } // PRQA S 4024 # Technical debt L2 id=00c14c7bc45288e845d9-a896697f878a8955

    //---------------------------------------------------------------------
    /// returns the height of TRect (read only).
    /// @dspecref{1245}
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return value of the height
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    value_type height(void) const { return m_height; }

    //---------------------------------------------------------------------
    /// returns the height of TRect (read/write).
    /// @dspecref{1245}
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return value by reference of the height
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    value_type& height(void)
    {
        return m_height;
    } // PRQA S 4024 # Technical debt L2 id=00c182cc2705252e4301-78008d49068017ab

    //---------------------------------------------------------------------
    /// returns the top-left position of TRect (read only).
    /// @dspec{1633}     The SW component "vfc" shall return the corner points of the
    ///                  rectangle by means of  topLeft and bottomRight.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return        Specifies the top-left position of TRect
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    TPoint<ValueType> topLeft(void) const;

    //---------------------------------------------------------------------
    /// returns the bottom-right position of TRect (read only).
    /// @dspecref{1633}
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return        Specifies the bottom-right position of TRect
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    TPoint<ValueType> bottomRight(void) const;

    //---------------------------------------------------------------------
    /// returns the centerpoint C((right+left)/2,(bottom+top)/2) of the
    /// rectangle.
    /// @dspec{1246}     The SW component "vfc" shall return the point at the center position.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return     Object of TPoint which specify center of rect
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    TPoint<ValueType> center(void) const;

    //---------------------------------------------------------------------
    /// returns the right position (outside) of TRect.
    /// @dspecref{1247}
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return     right position (outside) of TRect.
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    value_type right(void) const
    {
        return m_left + m_width;
    } // PRQA S 3010 # Technical debt L2 id=00c12844b4a43c774704-7529e7257a487ef3

    //---------------------------------------------------------------------
    /// returns the bottom (outside) of TRect.
    /// @dspecref{1247}
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return     bottom position (outside) of TRect.
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    value_type bottom(void) const
    {
        return m_top + m_height;
    } // PRQA S 3010 # Technical debt L2 id=00c196bb0fe65ed94815-7f008c3e4662b889

    //---------------------------------------------------------------------
    /// returns the size of the rect.
    /// @dspec{1634}     The SW component "vfc" shall return the Size instance describing
    ///                  the dimensions of a given rectangle.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return     Object of TSize which specify size of the rect.
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    TSize<ValueType> size(void) const;

    //---------------------------------------------------------------------
    /// returns the rect's area. area = width*height.
    /// Do not use area() to check if the rect is valid.
    /// @dspec{1244}     The SW component "vfc" shall compute and return the area
    ///                  of a given rectangle instance
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return     area of the rect.
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    area_type area(void) const;

    //---------------------------------------------------------------------
    /// determines whether TRect is empty. TRect is empty if the
    /// width and/or height are 0, or if the rect is not normalized,
    /// i.e. width and/or height are negative.
    /// @dspec{1243}     The SW component "vfc" shall return the boolean value "true" if
    ///                  a given rectangle is empty otherwise it shall return the boolean value "false".
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @return     true to indicate rect is empty and false otherwise.
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    bool isEmpty(void) const;

    //---------------------------------------------------------------------
    /// determines whether the specified rect lies inside this rect,
    /// identical coordinates are treated as inside.
    /// @dspec{1242}     The SW component "vfc" shall return the boolean value "true"
    ///                  if a given rectangle contains another given rectangle or point
    ///                  otherwise it shall return the boolean value "false".
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @param f_otherRect Specifies the TRect instance
    /// @return     true to indicate rect contains rect and false otherwise.
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    bool contains(const TRect& f_otherRect) const;

    //---------------------------------------------------------------------
    /// determines whether the specified point lies inside or outside
    /// of this rect.
    /// @dspecref{1242}
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @param f_point Specifies the TPoint instance
    /// @return     true to indicate rect contains point and false otherwise.
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    bool contains(const TPoint<ValueType>& f_point) const;

    //---------------------------------------------------------------------
    /// determines whether the specified point p(x,y) lies inside or
    /// outside of this rect.
    /// @dspecref{1242}
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @param  f_px     Specifies the extent in x-direction.
    /// @param  f_py     Specifies the extent in y-direction.
    /// @return     true to indicate rect contains point and false otherwise.
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    bool contains(value_type f_px, value_type f_py) const;

    //---------------------------------------------------------------------
    /// changes the size of the rect without changing the position of the
    /// top-left corner.
    /// @dspec{1241}     The SW component "vfc" shall resize a given rectangle to the
    ///                  given dimensions without changing it's top-left position.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @param  f_cx     Specifies the extent in x-direction.
    /// @param  f_cy     Specifies the extent in y-direction.
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    void resize(value_type f_cx, value_type f_cy);

    //---------------------------------------------------------------------
    /// changes the size of the rect without changing the position of the
    /// top-left corner.
    /// @dspecref{1241}
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @param f_point Specifies the TSize instance
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    void resize(const TSize<ValueType>& f_newSize);

    //---------------------------------------------------------------------
    /// Standardizes the height and width of TRect.
    /// NormalizeRect adds m_width to m_left and subtracts m_with fromt itself
    /// if m_width is less than zero.
    /// Similarly, adds m_height to m_top and subtracts m_height fromt itself
    /// if m_height is less than zero.
    /// This function is useful when dealing with inverted rectangles.
    /// @dspec{1240}     The SW component "vfc" shall normalize a given rectangle so that
    ///                  the value for the bottom position is greater than/equals the top
    ///                  value and the right value is greater than/equals the left value and
    ///                  store the modified parameters in the given rectangle.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    void normalize(void);

    //---------------------------------------------------------------------
    /// Inflate rectangle's width and height by dx units to the left and
    /// right ends of the rectangle and dy units to the top and bottom.
    /// @dspec{1239}     The SW component "vfc" shall inflate, i.e. enlarge a given rectangle
    ///                  by moving the sides by given increments away from the center.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @param  f_dx    Specifies the number of units to inflate the left and
    ///                 right sides of TRect.
    /// @param  f_dy    Specifies the number of units to inflate the top and
    ///                 bottom of TRect.
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    void inflate(value_type f_dx, value_type f_dy);

    //---------------------------------------------------------------------
    /// Inflate rectangle's width and height by moving individual sides.
    /// Left side is moved to the left, right side is moved to the right,
    /// top is moved up and bottom is moved down.
    /// @dspecref{1239}
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @param  f_dleft     Specifies the number of units to inflate the
    ///                     left side of TRect.
    /// @param  f_dtop      Specifies the number of units to inflate the
    ///                     top side of TRect.
    /// @param  f_dright    Specifies the number of units to inflate the
    ///                     right side of TRect.
    /// @param  f_dbottom   Specifies the number of units to inflate the
    ///                     bottom side of TRect.
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    void inflate(value_type f_dleft, value_type f_dtop, value_type f_dright, value_type f_dbottom);

    //---------------------------------------------------------------------
    /// Deflate rectangle's width and height by dx units to the left and
    /// right ends of the rectangle and dy units to the top and bottom.
    /// @dspec{1238}     The SW component "vfc" shall deflate, i.e. shrink a given rectangle
    ///                  by moving the sides by given increments towards the center.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @param  f_dx    Specifies the number of units to deflate the left
    ///                 and right sides of TRect.
    /// @param  f_dy    Specifies the number of units to deflate the top
    ///                 and bottom of TRect.
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    void deflate(value_type f_dx, value_type f_dy);

    //---------------------------------------------------------------------
    /// Deflate rectangle's width and height by moving individual sides.
    /// Left side is moved to the right, right side is moved to the left,
    /// top is moved down and bottom is moved up.
    /// @dspecref{1238}
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @param  f_dleft     Specifies the number of units to deflate the
    ///                     left side of TRect.
    /// @param  f_dtop      Specifies the number of units to deflate the
    ///                     top side of TRect.
    /// @param  f_dright    Specifies the number of units to deflate the
    ///                     right side of TRect.
    /// @param  f_dbottom   Specifies the number of units to deflate the
    ///                     bottom side of TRect.
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    void deflate(value_type f_dleft, value_type f_dtop, value_type f_dright, value_type f_dbottom);

    //---------------------------------------------------------------------
    /// Moves TRect by the specified offsets.
    /// @dspec{1237}     The SW component "vfc" shall move a given rectangle by given
    ///                  offset coordinate values.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @param  f_dx      Specifies the number of units to shift in x dir
    /// @param  f_dy      Specifies the number of units to shift in y dir
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    void offset(value_type f_dx, value_type f_dy);

    //---------------------------------------------------------------------
    /// Moves TRect to the specified x- and y-coordinates.
    /// @dspec{1236}     The SW component "vfc" shall move the upper left corner of a
    ///                  given rectangle to a given absolute position.
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @param  f_x The absolute x-coordinate for the upper-left corner
    ///             of the rectangle.
    /// @param  f_y The absolute y-coordinate for the upper-left corner
    ///             of the rectangle.
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    void moveTo(value_type f_x, value_type f_y);

    //---------------------------------------------------------------------
    /// Moves TRect to the specified TPoint.
    /// @dspecref{1236}
    /// @par Reference (CS-CRM / DOORS ID / EA):
    /// @param   f_point    A TPoint specifying the absolute upper-left
    ///                     corner of the rectangle.
    /// $Source: vfc_trect.hpp $

    /// @ingroup vfc_group_core_types
    //---------------------------------------------------------------------
    void moveTo(const TPoint<ValueType>& f_point);

  private:
    value_type m_left;
    value_type m_top;
    value_type m_width;
    value_type m_height;
};

// traits specialization
template <typename TType>
struct THasTrivialDTor<TRect<TType>> : THasTrivialDTor<TType>
{
};
template <typename TType>
struct THasTrivialCopy<TRect<TType>> : THasTrivialCopy<TType>
{
};
template <typename TType>
struct THasTrivialSetZero<TRect<TType>> : THasTrivialSetZero<TType>
{
};

//---------------------------------------------------------------------
/// Returns a rect which is equal to the union of the two specified
/// rectangles.
/// The union is the smallest rectangle that contains both source
/// rectangles.
/// @dspec{1235}     The SW component "vfc" shall create and return the smallest rectangle
///                  which contains the union of two given rectangles.
/// @par Reference (CS-CRM / DOORS ID / EA):
/// @tparam  ValueType data type of TRect
/// @param   f_op1    Object of first TRect
/// @param   f_op2    Object of second TRect
/// @return  Returns a rect which is equal to the union
/// $Source: vfc_trect.hpp $

/// @ingroup vfc_group_core_types
//---------------------------------------------------------------------
template <class ValueType>
TRect<ValueType> union_rect(const TRect<ValueType>& f_op1, const TRect<ValueType>& f_op2);

//---------------------------------------------------------------------
/// Returns a rect which is equal to the intersection of the two
/// specified rectangles.
/// The intersection is the largest rectangle contained in both existing
/// rectangles.
/// If the two specified rectangles don't intersect, the resulting
/// rectangle may become denormalized.
/// The validity of the result can only be checked with isEmpty().
/// Do not use area() to check validity as the result from a denormalized
/// rectangle is undetermined (might be <,>,= 0,
/// depending on signs of width and height).
/// qdspec{1234}     The SW component "vfc" shall create and return the largest rectangle
///                  which is contained in two given rectangles.
/// @par Reference (CS-CRM / DOORS ID / EA):
/// @tparam  ValueType data type of TRect
/// @param   f_op1    Object of first TRect
/// @param   f_op2    Object of second TRect
/// @return     Returns a rect which is equal to the intersection
/// $Source: vfc_trect.hpp $

/// @ingroup vfc_group_core_types
//---------------------------------------------------------------------
template <class ValueType>
TRect<ValueType> intersect_rect(const TRect<ValueType>& f_op1, const TRect<ValueType>& f_op2);

//---------------------------------------------------------------------
/// returns true if every of the four values (top, left, width, height)
/// of one rectangle is equal the its corresponding value in the other
/// rectangle.
/// @dspec{1233}     The SW component "vfc" shall return the boolean value "true" if two
///                  given rectangles have the same top, the same left, the same width, and
///                  the same height values otherwise it shall return the boolean value "false".
/// @par Reference (CS-CRM / DOORS ID / EA):
/// @tparam  ValueType data type of TRect
/// @param f_lhs TRect value on the left-hand-side of expression
/// @param f_rhs TRect value on the right-hand-side of expression
/// @return     Returns true if rects are equal and false otherwise.
/// $Source: vfc_trect.hpp $

/// @ingroup vfc_group_core_types
//---------------------------------------------------------------------
template <class ValueType>
bool operator==(const TRect<ValueType>& f_lhs, const TRect<ValueType>& f_rhs);

//---------------------------------------------------------------------
/// returns true if any of the four values (top, left, width, height) of
/// one of the rectangles is not equal to the corresponding value in
/// the other rectangle.
/// @dspec{1232}     The SW component "vfc" shall return the boolean value "false" if two given
///                  rectangles have the same top, the same left, the same width, and the same
///                  height values otherwise it shall return the boolean value "true".
/// @par Reference (CS-CRM / DOORS ID / EA):
/// @tparam  ValueType data type of TRect
/// @param f_lhs TRect value on the left-hand-side of expression
/// @param f_rhs TRect value on the right-hand-side of expression
/// @return     Returns true if rects are NOT equal and false otherwise.
/// $Source: vfc_trect.hpp $

/// @ingroup vfc_group_core_types
//---------------------------------------------------------------------
template <class ValueType>
bool operator!=(const TRect<ValueType>& f_lhs, const TRect<ValueType>& f_rhs);

} // namespace vfc

#include "vfc/core/vfc_trect.inl"

#endif // VFC_TRECT_HPP_INCLUDED

