//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_FUNCTIONATTRIBUTES_HPP_INCLUDED
#define ZX_VFC_FUNCTIONATTRIBUTES_HPP_INCLUDED

// vfc/core includes
#include "vfc/core/vfc_config.hpp" // may define ZX_VFC_VARIATION_SW_QUAL_INCL
// Include of the variation point
#include "vfc/variation_point/vfc_sw_quality_level.inl"

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
namespace vfc
{
namespace attributes
{

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// SoftwareQualificationLevel
//
/// Enum describing the qualification level of the software.
///
/// Purpose is to enable checks at compile time that only certain functionality with a high enough rating is used.
/// It also has documentary value. This is very important for software developed according to ISO26262, but is
/// extended to cover also desktop software (e.g. usage of heap) and prototypes (which might not be fully functional
/// but are already in the repository).
///
/// Enum identifiers are avoiding names with all-caps due to potential macro name collisions.
///
/// * Desktop: Software has no limitations, e.g. may use heap and exceptions. This is useful for developing tools
///   etc. using well-known VFC functions or the same structure definitions as on target builds.
/// * Prototype: Software is aiming for being used in qualified target builds, but doesn't meet the quality
///   standards yet (e.g. missing test coverage).
/// * QM, ASIL_A through ASIL_D: This are qualification levels of the software (not the processed data) according to
///   ISO26262.
///
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
enum class SoftwareQualificationLevel
{ // PRQA S 2437 # Technical debt L7 id=00c13d41285693a64b51-8e675ac573fba4d4
    Desktop,
    Prototype,
    Qm,
    AsilA,
    AsilB,
    AsilC,
    AsilD
};

/// Simple template struct with the intention to create a type for each enum value of SoftwareQualificationLevel.
///
/// @tparam level An enum value of type SoftwareQualificationLevel.
template <SoftwareQualificationLevel level>
struct QualLevel2Type
{
};

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// g_softwareQualificationLevel
//
/// Global enum constant to define the qualification level active for this compilation.
///
/// This is defined via a Flux variation point. It defines an alias pointing to an inline header defining a macro,
/// which is used here.
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
constexpr SoftwareQualificationLevel g_softwareQualificationLevel =
    SoftwareQualificationLevel::ZX_VFC_VARIATION_ACTIVATION_SW_QUAL;

namespace intern
{
/// Implementation class for `static_assert`s, used by SoftwareQualification.
template <SoftwareQualificationLevel LevelValue = g_softwareQualificationLevel>
struct SwQual
{
    static inline void Desktop()
    {
        static_assert(
            LevelValue <= SoftwareQualificationLevel::Desktop,
            "The caller of this function has no ASIL rating ('Desktop') and was called from higher rated context.");
    }

    static inline void Prototype()
    {
        static_assert(
            LevelValue <= SoftwareQualificationLevel::Prototype,
            "The caller of this function has no ASIL rating ('Prototype') and was called from higher rated context.");
    }

    static inline void Qm()
    {
        static_assert(
            LevelValue <= SoftwareQualificationLevel::Qm,
            "The caller of this function has 'QM' ASIL rating and was called from higher rated context.");
    }

    static inline void Asil_A()
    {
        static_assert(
            LevelValue <= SoftwareQualificationLevel::AsilA,
            "The caller of this function has ASIL A rating and was called from higher rated context.");
    }

    static inline void Asil_B()
    {
        static_assert(
            LevelValue <= SoftwareQualificationLevel::AsilB,
            "The caller of this function has ASIL B rating and was called from higher rated context.");
    }

    static inline void Asil_C()
    {
        static_assert(
            LevelValue <= SoftwareQualificationLevel::AsilC,
            "The caller of this function has ASIL C rating and was called from higher rated context.");
    }

    static inline void Asil_D()
    {
        // Strictly speaking not needed since it's the highest level, but left in to be future proof.
        static_assert(
            LevelValue <= SoftwareQualificationLevel::AsilD,
            "The caller of this function has ASIL D rating and was called from higher rated context.");
    }
};
} // namespace intern

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// vfc::attributes::SoftwareQualification::*()
//
/// Helper functions to document and ensure a certain ASIL qualification rating, i.e. a function with QM quality
/// level can ensure not to be called in a build with ASIL-B quality level.
///
/// Note: It's mandatory to be in a template context, otherwise the assertion is evaluated too eager and compilation
/// fails, while it should only fail on actual usage of the function.
///
/// Example:
///
///     template< class T >
///     bool isNegative(const T& val)
///     {
///         vfc::attributes::SoftwareQualification::Asil_B();
///         // .... code
///         // Can call ASIL-D functions here, but not QM functions, when building for ASIL-B.
///     }
///
/// The using declaration enables to use the functions:
///
///     vfc::attributes::SoftwareQualification::Desktop()
///     vfc::attributes::SoftwareQualification::Prototype()
///     vfc::attributes::SoftwareQualification::Qm()
///     vfc::attributes::SoftwareQualification::Asil_A()
///     vfc::attributes::SoftwareQualification::Asil_B()
///     vfc::attributes::SoftwareQualification::Asil_C()
///     vfc::attributes::SoftwareQualification::Asil_D()
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
using SoftwareQualification = intern::SwQual<>;

namespace intern
{
/// Implementation class for `static_assert`s, used by SoftwareQualifiedType.
template <typename SomeType, SoftwareQualificationLevel LevelValue = g_softwareQualificationLevel>
struct SwQualT
{
    struct Desktop
    {
        static_assert(
            LevelValue <= SoftwareQualificationLevel::Desktop,
            "The instantiation was caused by a type which has no ASIL rating ('Desktop'), but a higher rating is "
            "required.");
        struct type_t
        {
        };
    };

    struct Prototype
    {
        static_assert(
            LevelValue <= SoftwareQualificationLevel::Prototype,
            "The instantiation was caused by a type which has no ASIL rating ('Prototype'), but a higher rating is "
            "required.");
        struct type_t
        {
        };
    };

    struct Qm
    {
        static_assert(
            LevelValue <= SoftwareQualificationLevel::Qm,
            "The instantiation was caused by a type which has 'QM' ASIL rating, but a higher rating is required.");
        struct type_t
        {
        };
    };

    struct Asil_A
    {
        static_assert(
            LevelValue <= SoftwareQualificationLevel::AsilA,
            "The instantiation was caused by a type which has ASIL-A rating, but a higher rating is required.");
        struct type_t
        {
        };
    };

    struct Asil_B
    {
        static_assert(
            LevelValue <= SoftwareQualificationLevel::AsilB,
            "The instantiation was caused by a type which has ASIL-B rating, but a higher rating is required.");
        struct type_t
        {
        };
    };

    struct Asil_C
    {
        static_assert(
            LevelValue <= SoftwareQualificationLevel::AsilC,
            "The instantiation was caused by a type which has ASIL-C rating, but a higher rating is required.");
        struct type_t
        {
        };
    };

    struct Asil_D
    {
        // Strictly speaking not needed since it's the highest level, but left in to be future proof.
        static_assert(
            LevelValue <= SoftwareQualificationLevel::AsilD,
            "The instantiation was caused by a type which has ASIL-D rating, but a higher rating is required.");
        struct type_t
        {
        };
    };
};
} // namespace intern

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// vfc::attibutes::SoftwareQualifiedType::*
//
/// Helper structs to document and ensure a certain ASIL qualification rating, i.e. a class designed with QM quality
/// level in mind can ensure not to be used (instantiated) in a build with ASIL-B quality level.
///
/// Note: It's mandatory to be in a template context, otherwise the assertion is evaluated too eager and compilation
/// fails, while it should only fail on actual usage of the class. Also, for this reason some arbitrary type, where
/// the actual class template is depended on, needs to be passed in as template argument to SoftwareQualifiedType.
///
/// Example:
///
///     template< class T >
///     class MyManagerClass
///     {
///     public:
///         // .... code like ctor, dtor etc.
///         MyManagerClass() {}
///         // The assertion, that this template is only instantiated in builds with at least ASIL-B rating.
///         using assertLevel = typename vfc::attributes::SoftwareQualifiedType<T>::Asil_B::type_t;
///     }
///
/// @tparam SomeType        Any arbitrary type which prevents early template evaluation.
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
template <typename SomeType>
using SoftwareQualifiedType = intern::SwQualT<SomeType>;

} // namespace attributes
} // namespace vfc
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
#endif // ZX_VFC_FUNCTIONATTRIBUTES_HPP_INCLUDED

