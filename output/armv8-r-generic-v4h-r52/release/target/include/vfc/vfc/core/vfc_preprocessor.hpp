//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_PREPROCESSOR_HPP_INCLUDED
#define VFC_PREPROCESSOR_HPP_INCLUDED

//=============================================================================
//  VFC_JOIN
//-----------------------------------------------------------------------------
/// helper macro, joins two macro arguments together.
/// The following piece of macro magic joins the two
/// arguments together, even when one of the arguments is
/// itself a macro (see 16.3.1 in C++ standard).  The key
/// is that macro expansion of macro arguments does not
/// occur in VFC_DO_JOIN2 but does in VFC_DO_JOIN.
/// @sa VFC_DO_JOIN
/// @sa VFC_DO_JOIN2
/// @ingroup vfc_group_core_misc
//=============================================================================
#define VFC_JOIN(ARG1, ARG2) VFC_DO_JOIN(ARG1, ARG2)
/// @sa VFC_JOIN
#define VFC_DO_JOIN(ARG1, ARG2)                                                                                        \
    VFC_DO_JOIN2(ARG1, ARG2) // PRQA S 0024 # Technical debt L8 id=00c1be7aeb309b074605-152bdfad0eff9195
/// @sa VFC_JOIN
#define VFC_DO_JOIN2(ARG1, ARG2) ARG1##ARG2

//=============================================================================
//  VFC_STRINGIZE
//-----------------------------------------------------------------------------
/// helper macro, converts the parameter X to a string after macro replacement
/// on X has been performed.
/// @sa VFC_DO_STRINGIZE
/// @ingroup vfc_group_core_misc
//=============================================================================
#define VFC_STRINGIZE(ARG) VFC_DO_STRINGIZE(ARG)
/// @sa VFC_STRINGIZE
#define VFC_DO_STRINGIZE(ARG) #ARG

//=============================================================================
//  VFC_NOCOMPILE_TEST
//-----------------------------------------------------------------------------
// Macro definitions for nocompile tests
#ifndef VFC_NOCOMPILE_TEST_NUMBER
#define VFC_NOCOMPILE_TEST_NUMBER 0
#endif

/// @def VFC_NOCOMPILE_TEST(N)
/// @brief Macro for code that should fail to compile (with any message).
/// @param N    running number, appears in test report, must be unique in compilation unit
#define VFC_NOCOMPILE_TEST(N) (VFC_NOCOMPILE_TEST_NUMBER == (N))

/// @def VFC_NOCOMPILE_TEST_MSG(N,M)
/// @brief Macro for code that should fail to compile with a given message.
/// @param N    running number, appears in test report, must be unique in compilation unit
/// @param M    Expected message which must be an exact substring of the compilation error message.
#define VFC_NOCOMPILE_TEST_MSG(N, M) (VFC_NOCOMPILE_TEST_NUMBER == (N))

/// @def VFC_NOCOMPILE_TYPE
/// @brief placeholder for multi-type no-compile tests, will be set as a #define
/// through the build system @sa VFC_NOCOMPILE_TEST_TYPES

/// @def VFC_NOCOMPILE_TEST_TYPES(N,...)
/// @brief Macro for code that should fail to compile with all of the given types.
/// @details For every combination of N (Number) and a type from the type list
/// given by the ellipsis' parameters a compilation unit will be created.
/// In every of these compilation units the macro VFC_NO_COMPILE_TYPE will be
/// available and that macro expands to a single of the given types.
/// Each of those compilation units is expected to fail.
/// @param N    running number, appears in test report, must be unique in compilation unit
/// @param ...  list of types the test is instantiated with
#define VFC_NOCOMPILE_TEST_TYPES(N, ...) (VFC_NOCOMPILE_TEST_NUMBER == (N))

#endif // VFC_PREPROCESSOR_HPP_INCLUDED

