//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_MATH_INL_INCLUDED
#define ZX_VFC_MATH_INL_INCLUDED

// stdlib includes
#include <algorithm>

#if defined(VFC_COMPILER_ARMRVCT)
#if defined(VFC_ARM_DETECTED) && (__ARMCC_VERSION >= 410713) && defined(VFC_HAS_ARM_NEON)
#include <arm_neon.h> // used for intrinsic functions macros from RVCT
#endif
#elif defined(VFC_COMPILER_VISUALC)
#if !defined(VFC_CLR_DETECTED)
#include <intrin.h>
#endif
#endif

// vfc/core includes
#include "vfc/core/vfc_assert.hpp"      // used for VFC_REQUIRE
#include "vfc/core/vfc_type_traits.hpp" // used for TIsFloating<>
#include "vfc/core/vfc_metaprog.hpp"    // used for TInt2Boolean<>
#include "vfc/core/vfc_builtin_types.hpp"
#include "vfc/core/vfc_limits.hpp" // CHAR_BIT

#include VFC_INCLUDE_INTERN_MATH_IMPL

namespace vfc
{ // namespace vfc opened

//---------------------------------------------------------------------
// conditional doxygen documentation
/// @cond VFC_DOXY_INTERN
//---------------------------------------------------------------------

namespace intern
{ // namespace intern opened
template <class T>
inline T clampValueToMinMax(const T& f_min, const T& f_value, const T& f_max, false_t)
{
    static_assert(
        !TIsFloating<T>::value || TIsValidFloating<T>::value,
        "invalid type used for comparison in clampValueToMinMax (generic case)");
    return (f_value < f_min) ? (f_min) : ((f_max < f_value) ? (f_max) : (f_value));
}

template <class T>
inline T clampValueToMinMax(const T& f_min, const T& f_value, const T& f_max, true_t)
{
    static_assert(
        TIsValidArithmetic<T>::value, "invalid type used for comparison in clampValueToMinMax (float32_t case)");
#if (ZX_BUILD_SCA() == false) && defined(VFC_COMPILER_ARMRVCT) && defined(VFC_ARM_DETECTED) &&                         \
    (__ARMCC_VERSION >= 410713) &&                                                                                     \
    defined(VFC_HAS_ARM_NEON) // PRQA S 1036 # Technical debt L7 id=00c15e7e705713d24f56-45aa8bd757f0637c
    vfc::float32_t f_result_f32 = 0.F;
    // if (f_min > f_value) ? f_min : f_value
    f_result_f32 = vget_lane_f32(vmax_f32(vdup_n_f32(f_value), vdup_n_f32(f_min)), 0);
    // if (f_max < f_value) ? f_max : f_value
    f_result_f32 = vget_lane_f32(vmin_f32(vdup_n_f32(f_result_f32), vdup_n_f32(f_max)), 0);
    return (f_result_f32);
#else
    return (f_value < f_min) ? (f_min) : ((f_max < f_value) ? (f_max) : (f_value));
#endif
}

// unsigned implementation just returns value
template <class T>
inline T integer_abs(const T& f_value, true_t)
{
    return f_value;
}

/// branch free abs implementation for signed integer types
template <class T>
inline T integer_abs(const T& f_value, false_t)
{
    static_assert(TIsInteger<T>::value && !TIsUnsignedArithmetic<T>::value, "Value type must be a signed integer");
    static_assert(numeric_limits<T>::is_specialized, "Value type must have proper specialization of numeric limits");
    static_assert(-2 >> 1 == -1, "vfc::abs needs arithmetic right shift to work");
    static_assert(8 == CHAR_BIT, "CHAR_BIT must be 8 bits");

    VFC_REQUIRE2(
        (f_value != numeric_limits<T>::min()),
        "f_value is equal to the minimum normalized value(numeric_limits<T>::min()). vfc::abs not possible for this "
        "value");

    // In case f_value is positive, even a big (numeric_limits<T>::max()) value gets right-shifted logically and the
    // mask is 0. In case f_value is negative, the mask is -1. In case f_value is smaller than int, it gets promoted to
    // int, with leading bits filled with ones if negative. Then shifted by e.g. 31 for ints or 15 for shorts. Using
    // 'auto' to catch integer promotion.
    const auto maskP = f_value >> (sizeof(T) * CHAR_BIT -
                                   1U); // PRQA S 3003 # Technical debt L2 id=00c18890f1a4b2104a59-7b7ced978efa2d36 //
                                        // PRQA S 2427 # Technical debt L2 id=00c1d40c4e3145fd491e-cda5da017315187f

    // Converting the type of `maskP` to unsigned type. As `maskP` is declared using `auto`, its type might be
    // the original type `T`, but it also might be the type after integer promotion, as no calculations can
    // happen in C++ with types smaller than `int`. On our platforms `int` is `int32_t`, so the resulting
    // mapping will be: `int8_t`-> `uint32_t`, `int16_t`-> `uint32_t`, `int32_t`-> `uint32_t`, `int64_t`->
    // `uint64_t`.
    using uintT = typename vfc::TSigned2Unsigned<decltype(maskP)>::type;

    // Converting the signed value to unsigned, negative values become positive. `uintT` may have more bits than
    // `T`!
    const uintT valUint = static_cast<uintT>(f_value);

    // Converting from signed mask to unsigned mask.
    const uintT maskU = static_cast<uintT>(maskP);

    // In case of positive f_value: mask is 0 and the original f_value leaves unchanged. In case of negative
    // f_value: mask is fully occupied with ones, which means a '-1' in two's complement. Adding this in
    // unsigned arithmetic (which is doing modulo arithmetic) is equivalent to subtracting one. Inverting the
    // bit pattern (xor) is then completing the inversion (abs) in two's complement.
    const auto res =
        (valUint + maskU) ^ maskU; // PRQA S 3003 # Technical debt L2 id=00c1fcc8af74c1d64573-f172a35a26bd6570

    // Casting finally the unsigned (and promoted) type to the resulting signed type. There should be no data
    // loss in this truncation when above calculation is the abs(f_value).
    T ret = static_cast<T>(res); // PRQA S 2427 # Technical debt L2 id=00c11e0dabebd53a4c06-f5e9a4cdf757fd7d

    // Ensure that the 'traditional' branching implementation would have the same result.
    VFC_ENSURE2(ret == ((f_value >= 0) ? f_value : -f_value), "Result has to match traditional calculation.");
    return ret;
}

template <class T>
inline T abs(const T& f_value, true_t)
{
    static_assert(TIsValidFloating<T>::value, "invalid type used for abs");
    return vfc_intern_math_alias::fabs_o(f_value);
}

template <class T>
inline T abs(const T& f_value, false_t)
{
    return static_cast<T>(integer_abs(
        f_value, // PRQA S 2427 # Technical debt L2 id=00c187fc3fd044a24223-5419eb34b3ebcc10
        typename TInt2Boolean<vfc::TIsUnsignedArithmetic<T>::value>::type()));
}

template <class T>
inline bool isINF(const T& f_value, true_t)
{
    static_assert(TIsValidFloating<T>::value, "invalid floating type used for isINF");
#if (ZX_BUILD_SCA() == false) &&                                                                                       \
    defined(VFC_COMPILER_ARMRVCT) // PRQA S 1036 # Technical debt L7 id=00c1a8cee252431a4dbf-6c6edde2265dd835
    return isinf(f_value);
#else
    static_assert(numeric_limits<T>::is_specialized, "value type must have proper specialization of numeric limits");
    return (
        (numeric_limits<T>::infinity() ==
         f_value) // PRQA S 3270 # Technical debt L2 id=00c1f2c8c6d62b8e40fc-4c5cb5c3a697abd5
        || (numeric_limits<T>::infinity() ==
            -f_value)); // PRQA S 3270 # Technical debt L2 id=00c10a7495a3dbbc44c8-dfe1a3433d95c266
#endif
}

template <class T>
inline bool isINF(const T&, false_t)
{
    return false;
}

template <class T>
inline bool isZero(const T& f_value, true_t)
{
    static_assert(TIsValidFloating<T>::value, "invalid floating type used for isZero");
    static_assert(numeric_limits<T>::is_specialized, "value type must have proper specialization of numeric limits");
    return ((numeric_limits<T>::epsilon() > -f_value) && (numeric_limits<T>::epsilon() > f_value));
}

template <class T>
inline bool isZero(const T& f_value, false_t)
{
    return (static_cast<T>(0) == f_value); // PRQA S 2427 # Technical debt L2 id=00c1c8bf9c5d39ad4e0f-c523ba8e39d7975f
}

template <class T>
inline constexpr bool isPositive(const T& f_value, true_t)
{
    static_assert(TIsValidFloating<T>::value, "invalid floating type used for isPositive");
    static_assert(numeric_limits<T>::is_specialized, "value type must have proper specialization of numeric limits");
    return (numeric_limits<T>::epsilon() <= f_value);
}

template <class T>
inline constexpr bool isPositive(const T& f_value, false_t)
{
    return (static_cast<T>(0) < f_value); // PRQA S 2427 # Technical debt L2 id=00c19f97d5857ef046a5-c4e1c3e46cb6bf04
}

template <class T>
inline constexpr bool isNegative(const T& f_value, true_t, false_t)
{
    static_assert(TIsValidFloating<T>::value, "invalid floating type used for isNegative");
    static_assert(numeric_limits<T>::is_specialized, "value type must have proper specialization of numeric limits");
    return (numeric_limits<T>::epsilon() <= -f_value);
}

template <class T>
inline constexpr bool isNegative(const T& f_value, false_t, false_t)
{
    return (static_cast<T>(0) > f_value); // PRQA S 2427 # Technical debt L2 id=00c11a44d509f33545ca-e38596f60c4bb916 //
                                          // PRQA S 4895 # Technical debt L4 id=00c159fec1e0fd0c46b5-784387a85578a7dc
}

template <class T>
inline constexpr bool isNegative(const T&, false_t, true_t)
{
    return false;
}

template <class T>
inline bool isEqual(const T& f_value1, const T& f_value2, false_t)
{
    return (f_value1 == f_value2);
}

// function overload if T is a built-in floating point type
template <class T>
inline bool isEqual(const T& f_value1, const T& f_value2, true_t)
{
    static_assert(TIsValidFloating<T>::value, "invalid floating type used for isEqual");
    static_assert(numeric_limits<T>::is_specialized, "value type must have proper specialization of numeric limits");

    const T oneT = static_cast<T>(1); // PRQA S 2427 # Technical debt L2 id=00c1bb0b57508f8c46f2-0b73b97d242d3107

    const T abs1 = vfc_intern_math_alias::fabs_o(f_value1);
    const T abs2 = vfc_intern_math_alias::fabs_o(f_value2);

    const T maxabs12 = (stlalias::max)(abs1, abs2);

    // invert the comparison and negate the result to force
    // the maxabs12 is NaN case into the "if" clause
    if (!(oneT < maxabs12))
    {
        // absolute error for small values
        return (vfc_intern_math_alias::fabs_o(f_value1 - f_value2) < numeric_limits<T>::epsilon());
    }
    else
    {
        // relative error for great values
        // In case of infinite values, we get a NaN on the lhs and the
        // comparison is false. Therefore we use ">=" and negate the result.
        return !((vfc_intern_math_alias::fabs_o(f_value1 - f_value2) >= (numeric_limits<T>::epsilon() * maxabs12)));
    }
}

/// numeric_cast specialization for floating to floating type
template <class ReturnType, class ArgumentType>
inline ReturnType numeric_cast(const ArgumentType& f_value, true_t, true_t)
{
    static_assert(TIsValidFloating<ArgumentType>::value, "invalid floating type used for source of numeric_cast");
    static_assert(TIsValidFloating<ReturnType>::value, "invalid floating type used for return value of numeric_cast");
    return static_cast<ReturnType>(f_value);
}

/// numeric_cast specialization for integral to floating type
template <class ReturnType, class ArgumentType>
inline ReturnType numeric_cast(const ArgumentType& f_value, true_t, false_t)
{
    return static_cast<ReturnType>(f_value);
}

/// numeric_cast specialization for floating to integral type
template <class ReturnType, class ArgumentType>
inline ReturnType numeric_cast(const ArgumentType& f_value, false_t, true_t)
{
    return static_cast<ReturnType>(::vfc::nint(f_value));
}

/// numeric_cast specialization for integral to integral type
template <class ReturnType, class ArgumentType>
inline ReturnType numeric_cast(const ArgumentType& f_value, false_t, false_t)
{
    return static_cast<ReturnType>(f_value); // PRQA S 2856 # Technical debt L5 id=00c1d29cc76b15474895-be2fff9c1abadb0c
}

/// used for type pruning.  Allows "conversion" \c float32_t <->
/// \c int32_t without violating strict aliasing rules.
union UFastRsqrtAliasingHelper
{ // PRQA S 2176 # Technical debt L2 id=00c1429dfedf7cb542f9-d72d13e33d99dc64 // PRQA S 2406 # Technical debt L4
  // id=00c1d5ba83a5bfd04a05-3e7fb027c7a88223
    vfc::float32_t m_val_f32;
    vfc::int32_t   m_val_i32;
};

#if !defined(VFC_NO_INT64) && (defined(VFC_ENABLE_FLOAT64) || defined(VFC_ENABLE_QM_MATH))
/// used for type pruning. Allows "conversion" \c float64_t <->
/// \c int64_t without violating strict aliasing rules.
union UFastRsqrtAliasing64Helper
{ // PRQA S 2176 # Technical debt L2 id=00c1ad56fe11b78f48da-36d268c527b28538 // PRQA S 2406 # Technical debt L4
  // id=00c1b49f8efd2c4541d2-1b07f4f7d1a36cfd
    vfc::float64_t m_val_f64;
    vfc::int64_t   m_val_i64;
};
#endif

// helper template funtion to check multiple predicates of types
// This is the base template
template <typename CheckType>
constexpr inline bool isTypeAmongAllowed()
{
    return false;
}

// variadic specialization that evalutes first predicate and recurses further.
template <typename CheckType, template <typename> class Pred, template <typename> class... Preds>
constexpr inline bool isTypeAmongAllowed()
{
    return (Pred<CheckType>::value || isTypeAmongAllowed<CheckType, Preds...>());
}

// Fancy wrapper for static_cast that performs another
// explicit static_cast when an intermediate type is needed.
// This is the base template that does not use an intermediate type.
template <typename OutputType, typename = void>
struct ValueTypeCaster
{
    template <typename InputType>
    static constexpr inline auto castValue(InputType val) -> OutputType
    {
        return static_cast<OutputType>(val);
    }
};

// Specialization for types with a `value_type`:
// Will cast to the intermediate type first.
// This avoids the implicit conversions when casting ints
// to SIUnits like vfc::CSI::metre_f32_t.
template <typename OutputType>
struct ValueTypeCaster<OutputType, TCheckDependentTypesAreValid_t<typename OutputType::value_type>>
{
    using IntermediateType = typename OutputType::value_type;

    template <typename InputType>
    static constexpr inline auto castValue(InputType val) -> OutputType
    {
        return static_cast<OutputType>(static_cast<IntermediateType>(val));
    }
};

} // namespace intern

//---------------------------------------------------------------------
/// @endcond
// of VFC_DOXY_INTERN
//---------------------------------------------------------------------

//=============================================================================
// implementations for typed constants
//=============================================================================

template <class ValueType>
constexpr inline ValueType typedZero()
{
    static_assert(
        intern::isTypeAmongAllowed<ValueType, TIsSIUnitType, TIsValidFloating, TIsInteger, TIsBoolType, TIsPointer>(),
        "Function typedZero() is usable only with SIUnits, integers, floats, pointers or bool.");
    return intern::ValueTypeCaster<ValueType>::castValue(0);
}

template <class ValueType>
constexpr inline ValueType typedOne()
{
    static_assert(
        intern::isTypeAmongAllowed<ValueType, TIsSIUnitType, TIsValidFloating, TIsInteger, TIsBoolType>(),
        "Function typedOne() is usable only with SIUnits, integers, floats or bool.");
    return intern::ValueTypeCaster<ValueType>::castValue(1);
}

template <class ValueType>
constexpr inline ValueType typedTwo()
{
    static_assert(
        intern::isTypeAmongAllowed<ValueType, TIsSIUnitType, TIsValidFloating, TIsInteger>(),
        "Function typedTwo() is usable only with SIUnits, integers or floats.");
    return intern::ValueTypeCaster<ValueType>::castValue(2);
}

template <class ValueType>
constexpr inline ValueType typedPi()
{
    static_assert(
        intern::isTypeAmongAllowed<ValueType, TIsValidFloating>(),
        "Function typedPi() is usable only with floating points.");
    // Return the literal for PI here. Even GHS can perfectly place a float32_t-casted literal in the
    // code, no specialization needed. Inlining is done well.
    return intern::ValueTypeCaster<ValueType>::castValue(3.1415926535897932384626433832795);
}

template <class ValueType>
constexpr inline ValueType typedDegree2Radian()
{
    static_assert(
        intern::isTypeAmongAllowed<ValueType, TIsValidFloating>(),
        "Function typedDegree2Radian() is usable only with floating points.");
    // See comment in typedPi()
    return typedPi<ValueType>() / static_cast<ValueType>(180.0);
}

template <class ValueType>
constexpr inline ValueType typedRadian2Degree()
{
    static_assert(
        intern::isTypeAmongAllowed<ValueType, TIsValidFloating>(),
        "Function typedRadian2Degree() is usable only with floating points.");
    // See comment in typedPi()
    return static_cast<ValueType>(180.0) / typedPi<ValueType>();
}

} // namespace vfc

//=============================================================================
// test for NAN
//=============================================================================

template <class T>
inline constexpr bool vfc::isNAN(const T& f_value)
{
    static_assert(
        intern::isTypeAmongAllowed<T, TIsIntegral, TIsFloating>(),
        "Function isNAN() is usable only with integers, floats or bool.");

#if (ZX_BUILD_SCA() == false) && (defined(VFC_COMPILER_ARMRVCT) || defined(VFC_COMPILER_AC6)) &&                       \
    !defined(VFC_PLATFORM_LINUX) // PRQA S 1036 # Technical debt L7 id=00c1465c34455d8e40c6-f4870a817534e65d
    return isnan(f_value);
#else
    // source: Appendix of IEEE 754 floating point standard
    return (f_value != f_value);       // PRQA S 3270 # Technical debt L2 id=00c103bd634ed3da430e-d698a9c1989f4944
#endif
}

//=============================================================================
// test for +INF, -INF
//=============================================================================

template <class T>
inline bool vfc::isINF(const T& f_value)
{
    return intern::isINF(f_value, typename TInt2Boolean<TIsFloating<T>::value>::type());
}

//=============================================================================
// tests for ZERO
//=============================================================================

template <class T>
inline bool vfc::isZero(const T& f_value)
{
    return intern::isZero(f_value, typename TInt2Boolean<TIsFloating<T>::value>::type());
}

template <class T>
inline bool vfc::notZero(const T& f_value)
{
    return !isZero(f_value);
}

template <class T>
inline constexpr bool vfc::isPositive(const T& f_value)
{
    return intern::isPositive(f_value, typename TInt2Boolean<TIsFloating<T>::value>::type());
}

template <class T>
inline constexpr bool vfc::notPositive(const T& f_value)
{
    return !isPositive(f_value);
}

template <class T>
inline constexpr bool vfc::isNegative(const T& f_value)
{
    return intern::isNegative(
        f_value,
        typename TInt2Boolean<TIsFloating<T>::value>::type(),
        typename TInt2Boolean<TIsUnsignedArithmetic<T>::value>::type());
}

template <class T>
inline constexpr bool vfc::notNegative(const T& f_value)
{
    return !isNegative(f_value);
}

//=============================================================================
// tests for EQUALITY
//=============================================================================

template <class T>
inline bool vfc::isEqual(const T& f_value1, const T& f_value2)
{
    const bool result = vfc::TIsFloating<T>::value && !vfc::TIsSIUnitType<T>::value;

    using IsSIUnit_t = typename vfc::TIf<result, true_t, false_t>::type;
    return intern::isEqual(f_value1, f_value2, IsSIUnit_t());
}

template <class T>
inline bool vfc::isEqual(const T& f_value1, const T& f_value2, const T& f_epsilon)
{
    static_assert(TIsFloating<T>::value, "type for isEqual with epsilon parameter must be a floating type");
    const T l1norm = abs(f_value1 - f_value2);
    return (l1norm <= f_epsilon);
}

template <class T>
inline bool vfc::notEqual(const T& f_value1, const T& f_value2)
{
    return !isEqual(f_value1, f_value2);
}

//=============================================================================
// checked divide
//=============================================================================

template <class T>
inline T vfc::divide(const T& f_num, const T& f_denom)
{
    VFC_REQUIRE2(!isZero(f_denom), "DivisionByZero");
    return f_num / f_denom; // PRQA S 3010 # Technical debt L2 id=00c1ff60c19961204942-59724c3200f8cb44
}

//=============================================================================
// more mathematical functions
//=============================================================================
inline vfc::float32_t vfc::floor(vfc::float32_t f_value) { return vfc_intern_math_alias::floor_f(f_value); }

inline vfc::float32_t vfc::ceil(vfc::float32_t f_value) { return vfc_intern_math_alias::ceil_f(f_value); }

inline vfc::float32_t vfc::round(vfc::float32_t f_value) { return vfc_intern_math_alias::round_f(f_value); }

inline vfc::int64_t vfc::lround(vfc::float32_t f_value)
{
    // For float values larger than the maximal mantissa value, the float32_t value cannot
    // represent a fractional part and we can cast to int64_t without explicit rounding.
    if (intern::abs(f_value, true_t()) > static_cast<float32_t>(numeric_limits<vfc::int32_t>::max()))
    {
        return static_cast<int64_t>(
            f_value); // PRQA S 3016 # Technical debt L2 id=00c154a232224b67431a-52fdef558e63269b
    }
    return vfc_intern_math_alias::llround_f(f_value);
}

#if defined(VFC_COMPILER_ARMRVCT) && defined(VFC_ARM_DETECTED) && (__ARMCC_VERSION >= 410713)
#pragma push
#pragma arm
#endif
inline vfc::int32_t
vfc::clampNegValueToZero(int32_t f_value) // PRQA S 3800 # Technical debt L4 id=00c133f5005407694cad-64879bcffa47f3ce
{
#if (ZX_BUILD_SCA() == false) && defined(VFC_COMPILER_ARMRVCT) && defined(VFC_ARM_DETECTED) &&                         \
    (__ARMCC_VERSION >= 410713) // PRQA S 1036 # Technical debt L7 id=00c152af3a9c04884329-54f385638d457a7a
    vfc::int32_t l_result_i32 = f_value;
    __asm
    {
            CMN f_value, 0
            MOVMI  l_result_i32, 0
    }
    return l_result_i32;
#else
    return f_value & ~(f_value >> 31); // PRQA S 3003 # Technical debt L2 id=00c11acfac6346724a69-6468a7094c8a1353
#endif
}
#if defined(VFC_COMPILER_ARMRVCT) && defined(VFC_ARM_DETECTED) && (__ARMCC_VERSION >= 410713)
#pragma pop
#endif

inline vfc::float32_t vfc::clampNegValueToZero(
    vfc::float32_t f_value) // PRQA S 3800 # Technical debt L4 id=00c17e535707937746c2-8314740941404c80
{
#if (ZX_BUILD_SCA() == false) && defined(VFC_COMPILER_ARMRVCT) && defined(VFC_ARM_DETECTED) &&                         \
    (__ARMCC_VERSION >= 410713) &&                                                                                     \
    defined(VFC_HAS_ARM_NEON) // PRQA S 1036 # Technical debt L7 id=00c157c93492e74045e0-45aa8bd757f0637c
    return vget_lane_f32(vmax_f32(vdup_n_f32(0.0F), vdup_n_f32(f_value)), 0); // Pass vectors to VMAX instruction
#else
    return (0 > f_value) ? static_cast<vfc::float32_t>(0) : f_value;
#endif
}

template <class T>
inline T vfc::clampNegValueToZero(const T& f_value)
{
    return (static_cast<T>(0) > f_value) ? static_cast<T>(0) : f_value;
}

template <class T>
inline T vfc::abs(const T& f_value)
{
    return intern::abs(f_value, typename TInt2Boolean<TIsFloating<T>::value>::type());
}

inline vfc::int32_t vfc::nint(vfc::float32_t f_value)
{
    // upper boundary for overflow free calculation: 2147483520
    //                (std::nextafter(static_cast<vfc::float32_t>(INT_MAX), 0.0F))
    // lower boundary for overflow free calculation: -2147483648
    //                (vfc::numeric_limits<vfc::int64_t>::min())
    VFC_REQUIRE2(
        2147483520.0F >= f_value && -2147483648.0F <= f_value, "The value is outside of the supported value range.");

    vfc::int32_t l_ret_i32 = 0;

    // specialized versions for some platforms or compilers
#if (ZX_BUILD_SCA() == false) && defined(VFC_COMPILER_VISUALC) &&                                                      \
    !defined(VFC_CLR_DETECTED) // PRQA S 1036 # Technical debt L7 id=00c132a237c70dfb4fb9-eb08da0edcb7912e
    // ms visual c++ with x86 and disabled CLR
    l_ret_i32 = _mm_cvtss_si32(_mm_set_ss(f_value));
#elif (ZX_BUILD_SCA() == false) && defined(VFC_COMPILER_MWERKS) && defined(VFC_PROCESSOR_IX86)
    // metrowerks codewarrior with x86
    asm
    {
            fld f_value
            fistp l_ret_i32
    }
#elif (ZX_BUILD_SCA() == false) && defined(VFC_COMPILER_ARMRVCT) && defined(VFC_ARM_DETECTED) &&                       \
    (__ARMCC_VERSION >= 410713) && defined(VFC_HAS_ARM_NEON)
    // ARM RVCT 4.1 (patch 4, build 713) (and up, hopefully) finally understands VFP in inline assembly
    vfc::float32_t l_rounded_f32;
    __asm
    {
            vcvtr.s32.f32 l_rounded_f32, f_value
            vmov          l_ret_i32, l_rounded_f32
    }
#else
    // std c fallback
    const vfc::float32_t l_rounded_f32 = (f_value >= 0.0F) ? (f_value + 0.5F) : (f_value - 0.5F);
    l_ret_i32                          = static_cast<vfc::int32_t>(
        l_rounded_f32); // PRQA S 3016 # Technical debt L2 id=00dd82944425e791448b-0d6d9ef59556a055
#endif

    return l_ret_i32;
}

inline vfc::float32_t vfc::nextafter(vfc::float32_t f_start, vfc::float32_t f_toward)
{
    return vfc_intern_math_alias::nextafter_f(f_start, f_toward);
}

inline vfc::EFloatClassification vfc::floatClassify(vfc::float32_t f_value)
{
    EFloatClassification l_result = FloatClass_Normal;
    switch (vfc_intern_math_alias::fpclassify_f(f_value))
    {
    case FP_INFINITE:
    {
        l_result = FloatClass_Infinite;
        break;
    }
    case FP_NAN:
    {
        l_result = FloatClass_NaN;
        break;
    }
    case FP_SUBNORMAL:
    {
        l_result = FloatClass_SubNormal;
        break;
    }
    case FP_ZERO:
    {
        l_result = FloatClass_Zero;
        break;
    }
    case FP_NORMAL:
    {
        // l_result is set to FloatClass_Normal at method entry.
    }
    default:
    {
        break;
    }
    }
    return l_result;
}

inline vfc::int32_t vfc::isqrt(vfc::int32_t f_value)
{
    VFC_REQUIRE(0 <= f_value);

    if (f_value <= 1)
    {
        return f_value;
    }

    vfc::uint32_t l_shift_u32     = 1U;
    vfc::uint32_t l_valueTemp_u32 = static_cast<vfc::uint32_t>(f_value) - 1U;

    // Find greatest shift
    if (l_valueTemp_u32 > 65535U)
    {
        l_shift_u32     = l_shift_u32 + 8U;
        l_valueTemp_u32 = l_valueTemp_u32 >> 16U;
    }
    if (l_valueTemp_u32 > 255U)
    {
        l_shift_u32     = l_shift_u32 + 4U;
        l_valueTemp_u32 = l_valueTemp_u32 >> 8U;
    }
    if (l_valueTemp_u32 > 15U)
    {
        l_shift_u32     = l_shift_u32 + 2U;
        l_valueTemp_u32 = l_valueTemp_u32 >> 4U;
    }
    if (l_valueTemp_u32 > 3U)
    {
        ++l_shift_u32;
    }

    // Calculate start value
    vfc::uint32_t l_g0_u32 = 1U << l_shift_u32;
    vfc::uint32_t l_g1_u32 = (l_g0_u32 + (static_cast<vfc::uint32_t>(f_value) >> l_shift_u32)) >> 1U;

    // Newton iteration
    while (l_g1_u32 < l_g0_u32)
    {
        l_g0_u32 = l_g1_u32;
        l_g1_u32 = (l_g0_u32 + (static_cast<vfc::uint32_t>(f_value) / l_g0_u32)) >> 1U;
    }

    return static_cast<vfc::int32_t>(l_g0_u32);
}

inline vfc::float32_t vfc::fast_rsqrt(vfc::float32_t f_value)
{
    VFC_REQUIRE(0.F <= f_value);
    const vfc::float32_t halfValue = 0.5F * f_value;
    // bit magic, relies on IEEE 32bit floating point representation,
    // original constant from Quake3 was 0x5f3759df, see paper cited above.
    vfc::intern::UFastRsqrtAliasingHelper fi;
    fi.m_val_f32 = f_value;
    fi.m_val_i32 =
        0x5f375a86 - (fi.m_val_i32 >> 1); // PRQA S 4422 # Technical debt L7 id=00c157dffe280e1d4f0b-e82fc1d6f6296af6 //
                                          // PRQA S 3003 # Technical debt L2 id=00c118dd3af527464ce3-89b650637d3b26cb

    // Newton step to increase accuracy.
    return fi.m_val_f32 * (1.5F - halfValue * fi.m_val_f32 * fi.m_val_f32);
}

template <class ReturnType, class ArgumentType>
inline ReturnType vfc::numeric_cast(const ArgumentType& f_value)
{
    return intern::numeric_cast<ReturnType>(
        f_value,
        typename TInt2Boolean<TIsFloating<ReturnType>::value>::type(),
        typename TInt2Boolean<TIsFloating<ArgumentType>::value>::type());
}

template <class ValueType>
inline typename vfc::TSqrtTraits<ValueType>::ArgType vfc::sqrt(ValueType f_value)
{
    VFC_REQUIRE(typedZero<ValueType>() <= f_value);
    return vfc::internSqrt(
        f_value,
        typename TInt2Boolean<
            !vfc::TIsSameType<typename vfc::TSqrtTraits<ValueType>::ArgType, vfc::float32_t>::value>::type());
}

template <class ValueType>
inline typename vfc::TSqrtTraits<ValueType>::ArgType vfc::internSqrt(ValueType f_value, false_t)
{
    const vfc::float32_t l_value = static_cast<vfc::float32_t>(f_value);
#if (ZX_BUILD_SCA() == false) && defined(VFC_COMPILER_ARMRVCT) && defined(VFC_ARM_DETECTED) &&                         \
    (__ARMCC_VERSION >= 410713) &&                                                                                     \
    defined(VFC_HAS_ARM_NEON) // PRQA S 1036 # Technical debt L7 id=00c11bd6d6ec72c44cc0-45aa8bd757f0637c
    __asm
    {
            vsqrt.f32 l_value, l_value
    }
    return l_value;
#else
    return vfc_intern_math_alias::sqrt_f(l_value);
#endif
}

template <class T>
inline T vfc::clampValueToMinMax(const T& f_min, const T& f_value, const T& f_max)
{

    return (vfc::intern::clampValueToMinMax(
        f_min, f_value, f_max, typename TInt2Boolean<vfc::TIsSameType<T, vfc::float32_t>::value>::type()));
}

inline vfc::float32_t vfc::exp(vfc::float32_t f_exponent) { return vfc_intern_math_alias::exp_f(f_exponent); }

inline vfc::float32_t vfc::exp2(vfc::float32_t f_exponent) { return vfc_intern_math_alias::exp2_f(f_exponent); }

inline vfc::float32_t vfc::pow(float32_t f_base, float32_t f_exponent)
{
    return vfc_intern_math_alias::pow_f(f_base, f_exponent);
}

inline vfc::float32_t vfc::fmod(vfc::float32_t f_dividend, vfc::float32_t f_divisor)
{
    return vfc_intern_math_alias::fmod_f(f_dividend, f_divisor);
}

inline vfc::float32_t vfc::log(float32_t f_value)
{
    VFC_REQUIRE(0.F < f_value);
    return vfc_intern_math_alias::log_f(f_value);
}

inline vfc::float32_t vfc::log2(float32_t f_value)
{
    VFC_REQUIRE(0.F < f_value);
    return vfc_intern_math_alias::log2_f(f_value);
}

inline vfc::float32_t vfc::log10(float32_t f_value)
{
    VFC_REQUIRE(0.F < f_value);
    return vfc_intern_math_alias::log10_f(f_value);
}

inline vfc::float32_t vfc::curt(float32_t f_value)
{
    const float32_t l_1_3_f32 = static_cast<float32_t>(G_1_3);
    return (f_value >= 0.F) ? vfc_intern_math_alias::pow_f(f_value, l_1_3_f32)
                            : -vfc_intern_math_alias::pow_f(-f_value, l_1_3_f32);
}

inline vfc::float32_t vfc::erf(float32_t f_value) { return vfc_intern_math_alias::erf_f(f_value); }

template <class T>
inline vfc::int32_t vfc::signum(const T& f_value)
{
    if (static_cast<T>(0) < f_value)
    {
        return 1;
    }
    else if (static_cast<T>(0) > f_value)
    {
        return -1;
    }
    else
    {
        return 0;
    }
}

template <class T>
inline vfc::float32_t vfc::heaviside(const T& f_value)
{
    return 0.5F * static_cast<float32_t>(vfc::signum(f_value)) + 0.5F;
}

template <class T>
inline T vfc::sqr(const T& f_value)
{
    return f_value * f_value; // PRQA S 3010 # Technical debt L2 id=00c108afdf9fee7744c1-3e306c086d1e0cce
}

//=============================================================================
// alignment
//=============================================================================

inline bool vfc::isPow2(size_t f_value) { return ((0 == (f_value & (f_value - 1))) && (0 != f_value)); }

inline vfc::size_t vfc::alignUp(size_t f_value, size_t f_alignment)
{
    // check precondition: alignment is power of two
    VFC_REQUIRE(isPow2(f_alignment));
    return (f_value + (f_alignment - 1)) & (~(f_alignment - 1));
}

inline vfc::size_t vfc::alignDown(size_t f_value, size_t f_alignment)
{
    // check precondition: alignment is power of two
    VFC_REQUIRE(isPow2(f_alignment));
    return f_value & ~(f_alignment - 1);
}

//=============================================================================
// float64_t overloads
//=============================================================================
#ifdef VFC_ENABLE_FLOAT64_TYPE

inline vfc::float64_t vfc::clampNegValueToZero(
    vfc::float64_t f_value) // PRQA S 3800 # Technical debt L4 id=00c1e1be9a421c9b47d6-afdab1495fd49b32
{
    return (0.0 > f_value) ? static_cast<vfc::float64_t>(0.0) : f_value;
}

inline vfc::int32_t vfc::nint(vfc::float64_t f_value)
{
    // upper boundary for overflow free calculation: 2147483647
    //                (vfc::numeric_limits<vfc::int32_t>::max())
    // lower boundary for underflow free calculation: -2147483648
    //                (vfc::numeric_limits<vfc::int32_t>::min())
    VFC_REQUIRE2(
        2147483647.0 >= f_value && -2147483648.0 <= f_value, "The value is outside of the supported value range.");

    vfc::int32_t l_ret_i32 = 0;

    // specialized versions for some platforms or compilers
#if (ZX_BUILD_SCA() == false) && defined(VFC_COMPILER_VISUALC) &&                                                      \
    !defined(VFC_CLR_DETECTED) // PRQA S 1036 # Technical debt L7 id=00c187674cbb9dc74527-eb08da0edcb7912e
    // ms visual c++ with x86 and disabled CLR
    l_ret_i32 = _mm_cvtsd_si32(_mm_set_sd(f_value));
#elif (ZX_BUILD_SCA() == false) && defined(VFC_COMPILER_MWERKS) && defined(VFC_PROCESSOR_IX86)
    // metrowerks codewarrior with x86
    asm
    {
            fld f_value
            fistp l_ret_i32
    }
#elif (ZX_BUILD_SCA() == false) && defined(VFC_COMPILER_ARMRVCT) && defined(VFC_ARM_DETECTED) &&                       \
    (__ARMCC_VERSION >= 410713) && defined(VFC_HAS_ARM_NEON)
    // ARM RVCT 4.1 (patch 4, build 713) (and up, hopefully) finally understands VFP in inline assembly
    vfc::float32_t l_rounded_f32;
    __asm
    {
            vcvtr.s32.f64 l_rounded_f32, f_value
            vmov          l_ret_i32, l_rounded_f32
    }
#else
    // std c fallback
    const vfc::float64_t l_rounded_f64 = (f_value >= 0.) ? (f_value + 0.5) : (f_value - 0.5);
    l_ret_i32                          = static_cast<vfc::int32_t>(
        l_rounded_f64); // PRQA S 3016 # Technical debt L2 id=00dddb817436be814fe5-29a2441910fa4bb6
#endif

    return l_ret_i32;
}

inline vfc::int64_t vfc::nint64(vfc::float64_t f_value)
{
    // upper boundary for overflow free calculation: 9223372036854774784
    //                (std::nextafter(static_cast<vfc::float64_t>(LLONG_MAX), 0.0))
    // lower boundary for underflow free calculation: -9223372036854775808
    //                (vfc::numeric_limits<vfc::int64_t>::min())
    VFC_REQUIRE2(
        9223372036854774784.0 >= f_value && -9223372036854775808.0 <= f_value,
        "The value is outside of the supported value range.");

    const vfc::float64_t l_rounded_f64 = (f_value >= 0.0) ? (f_value + 0.5) : (f_value - 0.5);
    return static_cast<vfc::int64_t>(
        l_rounded_f64); // PRQA S 3016 # Technical debt L2 id=00c1bc5dc683ef5a43aa-7770d241d6a461bd
}

template <class ValueType>
inline typename vfc::TSqrtTraits<ValueType>::ArgType vfc::internSqrt(ValueType f_value, true_t)
{
#if (ZX_BUILD_SCA() == false) && defined(VFC_COMPILER_ARMRVCT) && defined(VFC_ARM_DETECTED) &&                         \
    (__ARMCC_VERSION >= 410713) &&                                                                                     \
    defined(VFC_HAS_ARM_NEON) // PRQA S 1036 # Technical debt L7 id=00c1eafe0ad68fce4a19-45aa8bd757f0637c
    vfc::float64_t l_value = static_cast<vfc::float64_t>(f_value);
    __asm
    {
            vsqrt.f64 l_value, l_value
    }
    return l_value;
#else
    return vfc_intern_math_alias::sqrt_d(f_value);
#endif
}
#endif // VFC_ENABLE_FLOAT64_TYPE

#if (defined(VFC_ENABLE_FLOAT64) || defined(VFC_ENABLE_QM_MATH))

#ifndef VFC_NO_INT64
inline vfc::float64_t vfc::fast_rsqrt(vfc::float64_t f_value)
{
    VFC_REQUIRE(static_cast<vfc::float64_t>(0.0) <= f_value);
    const vfc::float64_t halfValue = static_cast<vfc::float64_t>(0.5) * f_value;
    // constant 0x5fe6eb50c7b537a9, see link cited above.
    vfc::intern::UFastRsqrtAliasing64Helper fi;
    fi.m_val_f64 = f_value;
    fi.m_val_i64 = 0x5fe6eb50c7b537a9ll -
                   (fi.m_val_i64 >> 1); // PRQA S 4422 # Technical debt L7 id=00c1f96bdb934e6e4070-ac4fcb8eb18830f6 //
                                        // PRQA S 4425 # Technical debt L7 id=00c1f288cb7a6a414964-6a030b02647fe985 //
                                        // PRQA S 3003 # Technical debt L2 id=00c16fa4202e7a7345d7-b11e950c18077e55

    // Newton step to increase accuracy.
    return fi.m_val_f64 * (static_cast<vfc::float64_t>(1.5) - halfValue * fi.m_val_f64 * fi.m_val_f64);
}
#endif
#endif

#ifdef VFC_ENABLE_QM_MATH
inline vfc::float64_t vfc::only_qm::pow(float64_t f_base, float64_t f_exponent)
{
    return vfc_intern_math_alias::pow_d(f_base, f_exponent);
}

inline vfc::float64_t vfc::only_qm::log(float64_t f_value)
{
    VFC_REQUIRE(0. < f_value);
    return vfc_intern_math_alias::log_d(f_value);
}

inline vfc::float64_t vfc::only_qm::log10(float64_t f_value)
{
    VFC_REQUIRE(0. < f_value);
    return vfc_intern_math_alias::log10_d(f_value);
}

inline vfc::float64_t vfc::only_qm::curt(float64_t f_value)
{
    return (f_value >= 0.) ? vfc_intern_math_alias::pow_d(f_value, G_1_3)
                           : -vfc_intern_math_alias::pow_d(-f_value, G_1_3);
}

inline vfc::float64_t vfc::only_qm::erf(float64_t f_value) { return vfc_intern_math_alias::erf_d(f_value); }
#endif

#endif // ZX_VFC_MATH_INL_INCLUDED

