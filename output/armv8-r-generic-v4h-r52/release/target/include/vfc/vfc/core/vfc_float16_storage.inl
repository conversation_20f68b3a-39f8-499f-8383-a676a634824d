//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_FLOAT16_STORAGE_INL_INCLUDED
#define VFC_FLOAT16_STORAGE_INL_INCLUDED

#include "vfc/core/vfc_math.hpp" // for vfc::isNAN

namespace vfc
{ // namespace vfc opened

/// construct from float32_t
inline CFloat16Storage::CFloat16Storage(float32_t f_value) : CFloat16Storage(f_value, NearestToEven)
{
    // intentionally left blank
}

/// construct from float32_t
inline CFloat16Storage::CFloat16Storage(float32_t f_value, EcfRound f_roundMode)
{
    uint16_t l_val = 0;

    Ufui32_t l_conv_instance;
    l_conv_instance.m_float32 = f_value;
    const uint32_t l_x        = l_conv_instance.m_uint32;

    if ((l_x & as<uint32_t>(EConstant::NoSignMask32)) == 0) // Signed zero
    {
        l_val = static_cast<uint16_t>(l_x >> as<uint32_t>(EConstant::SignShift)); // Return the signed zero
    }
    else // Not zero
    {
        const uint32_t l_xs = l_x & as<uint32_t>(EConstant::SignMask32);     // Pick off sign bit
        const uint32_t l_xe = l_x & as<uint32_t>(EConstant::ExpMask32);      // Pick off exponent bits
        uint32_t       l_xm = l_x & as<uint32_t>(EConstant::MantissaMask32); // Pick off mantissa bits
        const uint16_t l_hs = static_cast<uint16_t>(l_xs >> as<uint32_t>(EConstant::SignShift));
        if (0 == l_xe)
        { // Denormal will underflow, return a signed zero
            l_val = l_hs;
        }
        else if (as<uint32_t>(EConstant::ExpMask32) == l_xe)
        { // Inf or NaN (all the exponent bits are set)
            if (0U == l_xm)
            { // If mantissa is zero ...
                // Signed Inf
                l_val = (l_hs | as<uint16_t>(EConstant::InfBits16));
            }
            else
            {
                l_val = (l_hs | as<uint16_t>(EConstant::NaN16)); // NaN, only 1st mantissa bit set
            }
        }
        else
        { // Normalized number
            // Exponent unbias the single, then bias the halfp
            const uint32_t l_xe_rs = l_xe >> as<uint16_t>(EConstant::ExpShift32);
            const int16_t  l_hes =
                static_cast<int16_t>(l_xe_rs) -
                as<int16_t>(
                    EConstant::ExpBiasDiff); // PRQA S 3010 # Technical debt L2 id=00c1e1d9f8f467ef497c-d41f4b65edd0ba90
            uint16_t l_he = 0U;              // exponent
            uint16_t l_hm = 0U;              // mantissa
            if (as<int16_t>(EConstant::MaxExp16) <= l_hes)
            {                                                        // Overflow
                l_val = (l_hs | as<uint16_t>(EConstant::InfBits16)); // Signed Inf
            }
            else if (0 >= l_hes)
            { // Underflow
                if (-as<int16_t>(EConstant::ExpShift16) > l_hes)
                {                                     // Mantissa shifted all the way off & no rounding possibility
                    l_hm = static_cast<uint16_t>(0U); // Set mantissa to zero
                }
                else
                {
                    l_xm |= as<uint32_t>(EConstant::ImplicitMask32); // Add the hidden leading bit
                    // Mantissa
                    l_hm = static_cast<uint16_t>(
                        l_xm >> (as<int16_t>(EConstant::ExpShiftDiff) + 1 -
                                 l_hes)); // PRQA S 3003 # Technical debt L2 id=00c11430d48473b04fd9-b52efa4109146003
                    // Check for rounding
                    const int32_t l_rm_rs =
                        as<int32_t>(EConstant::RoundMaskUnderflow) >>
                        (as<int32_t>(EConstant::ExpShift16) +
                         l_hes); // PRQA S 3003 # Technical debt L2 id=00c1d222d67b3226453b-d64b995cf8078f25
                    const int32_t l_tc_rs =
                        as<int32_t>(EConstant::TieCheckUnderflow) >>
                        (as<int32_t>(EConstant::ExpShift16) +
                         l_hes); // PRQA S 3003 # Technical debt L2 id=00c1fd67345a2dc7404d-c9a8ea49be83c396
                    const int32_t l_rc =
                        (l_xm & l_rm_rs) -
                        l_tc_rs;  // PRQA S 3003 # Technical debt L2 id=00c133adb5420a074ff2-fac285e0b6c43000 // PRQA S
                                  // 3000 # Technical debt L2 id=00c17abc64b0c8a84be6-da0a753bfcf93a69
                    if (0 < l_rc) // check rounding
                    {
                        // Round, might overflow into exp bit, but this is OK
                        l_hm += static_cast<uint16_t>(1U);
                    }
                    else if (0 > l_rc)
                    {
                        // round down
                    }
                    else // tie case check 14th Bit for roundNearestToEven
                    {
                        l_hm += static_cast<uint16_t>(calcRoundOffset(
                            l_hm,
                            as<uint32_t>(EConstant::EvenBitUnderflow),
                            f_roundMode)); // PRQA S 3010 # Technical debt L2 id=00c1e867b5efb5444eec-379ad2113e68f162
                    }
                }
                // Combine sign bit and mantissa bits, biased exponent is zero
                l_val = (l_hs | l_hm);
            }
            else
            {
                // Exponent
                l_he               = static_cast<uint16_t>(l_hes) << as<uint16_t>(EConstant::ExpShift16);
                l_hm               = static_cast<uint16_t>(l_xm >> (as<uint16_t>(EConstant::ExpShiftDiff))); // Mantissa
                const int16_t l_rc = static_cast<int16_t>(static_cast<uint32_t>(
                    (l_xm & as<uint32_t>(EConstant::RoundMask)) - as<uint16_t>(EConstant::TieCheck)));
                l_val =
                    (l_hs | l_he | l_hm); // PRQA S 3010 # Technical debt L2 id=00c1081018ede3f04e45-a44a3555c4d23385
                if (0 < l_rc)             // check rounding
                {
                    // Round, might overflow to inf, this is OK
                    l_val += static_cast<uint16_t>(1U);
                }
                else if (0 > l_rc)
                {
                    // round down
                }
                else // tie case check 14th Bit for roundNearestToEven
                {
                    l_val += static_cast<uint16_t>(calcRoundOffset(
                        l_xm,
                        as<uint32_t>(EConstant::EvenBit),
                        f_roundMode)); // PRQA S 3010 # Technical debt L2 id=00c10616d11a644e4caf-273f10be134f61b9
                }
            }
        }
    }
    m_value = l_val;
}

inline auto CFloat16Storage::fromUint16(const uint16_t& f_value) -> CFloat16Storage
{
    CFloat16Storage l_res;
    l_res.m_value = f_value;
    return l_res;
}

inline auto CFloat16Storage::toFloat32() const -> float32_t
{
    uint32_t       l_x = 0U;
    const uint16_t l_h = m_value;

    if (0 == (l_h & as<uint16_t>(EConstant::NoSignMask16)))
    {                                                                           // Signed zero
        l_x = static_cast<uint32_t>(l_h) << as<uint16_t>(EConstant::SignShift); // Return the signed zero
    }
    else
    { // Not zero
        const uint16_t l_hs =
            l_h & as<uint16_t>(EConstant::SignMask16); // Pick off sign bit // PRQA S 3010 # Technical debt L2
                                                       // id=00c145d7744e3c5f4cb7-5bb0f39b2b314d48
        const uint16_t l_he =
            l_h & as<uint16_t>(EConstant::ExpMask16); // Pick off exponent bits // PRQA S 3010 # Technical debt L2
                                                      // id=00c12fdae74f889d461c-d1c2144b007f6fe8
        uint16_t l_hm =
            l_h & as<uint16_t>(EConstant::MantissaMask16); // Pick off mantissa bits // PRQA S 3010 # Technical debt L2
                                                           // id=00c10371de4601a84597-dc989fca64feecbd

        if (0 == l_he)
        {                     // Denormal will convert to normalized
            int32_t l_e = -1; // The following loop figures out how much extra to adjust the exponent
            do
            {
                ++l_e;
                l_hm <<= 1;
            } while (0 ==
                     (l_hm &
                      as<uint16_t>(EConstant::ImplicitMask16))); // Shift until leading bit overflows into exponent bit
            const uint32_t l_xs = static_cast<uint16_t>(l_hs) << as<uint16_t>(EConstant::SignShift); // Sign bit
            // Exponent unbias the halfp, then bias the single
            const uint16_t l_xes =
                (l_he >> as<uint16_t>(EConstant::ExpShift16)) + as<uint16_t>(EConstant::ExpBiasDiff) -
                static_cast<uint16_t>(l_e); // PRQA S 3010 # Technical debt L2 id=00c1de56e394d89b4d33-02256629e2c25b4e
            const uint32_t l_xe = static_cast<uint32_t>(l_xes) << as<uint16_t>(EConstant::ExpShift32); // Exponent
            const uint32_t l_xm = (static_cast<uint32_t>(l_hm) & as<uint32_t>(EConstant::MantissaMask16))
                                  << as<uint16_t>(EConstant::ExpShiftDiff); // Mantissa
            l_x = (l_xs | l_xe | l_xm); // Combine sign bit, exponent bits, and mantissa bits
        }
        else if (as<uint32_t>(EConstant::ExpMask16) == l_he)
        { // Inf or NaN (all the exponent bits are set)
            const uint32_t l_xs = static_cast<uint16_t>(l_hs) << as<uint16_t>(EConstant::SignShift); // Sign bit
            if (0 == l_hm)
            {                                                      // If mantissa is zero ...
                l_x = (l_xs | as<uint32_t>(EConstant::ExpMask32)); // Signed Inf
            }
            else
            {
                l_x = (l_xs | as<uint32_t>(EConstant::NaN32)); // NaN, only 1st mantissa bit set
            }
        }
        else
        { // Normalized number
            const uint32_t l_xs = static_cast<uint32_t>(l_hs) << as<uint16_t>(EConstant::SignShift); // Sign bit
            // Exponent unbias the halfp, then bias the single
            const uint16_t l_hes =
                l_he >>
                as<uint16_t>(
                    EConstant::ExpShift16); // PRQA S 3010 # Technical debt L2 id=00c1c4f7a289da3c4634-8b2394b1b3c08aaf
            const uint16_t l_xes =
                l_hes +
                as<uint16_t>(
                    EConstant::ExpBiasDiff); // PRQA S 3010 # Technical debt L2 id=00c1c62dc7f78be04e4b-1d49f76f2bd4a8b1
            // Exponent
            const uint32_t l_xe = l_xes << as<uint16_t>(EConstant::ExpShift32);
            const uint32_t l_xm = static_cast<uint32_t>(l_hm) << as<uint16_t>(EConstant::ExpShiftDiff); // Mantissa
            l_x                 = (l_xs | l_xe | l_xm); // Combine sign bit, exponent bits, and mantissa bits
        }
    }

    Ufui32_t l_conv_instance;
    l_conv_instance.m_uint32 = l_x;

    return l_conv_instance.m_float32;
}

inline CFloat16Storage::operator float32_t() const { return toFloat32(); }

inline bool CFloat16Storage::isNegative() const { return (0 != (m_value & as<uint16_t>(EConstant::SignMask16))); }

inline int32_t CFloat16Storage::calcRoundOffset(uint32_t f_check_pattern, uint32_t f_check_bit, EcfRound f_roundMode)
{
    switch (f_roundMode)
    {
    case ToInfinity:
    {
        return int32_t(1); // PRQA S 3082 # Technical debt L2 id=00c1dcde0bfb58b24eaa-346cb899b777bd27
    }
    case NearestToEven:
    {
        if (0 != (f_check_pattern & f_check_bit))
        {
            return int32_t(1); // PRQA S 3082 # Technical debt L2 id=00c1d5280a99ab5147c2-346cb899b777bd27
        }
        break;
    }
    default:
    {
        VFC_ASSERT2(false, "Unknown round mode.");
    }
    }

    return int32_t(0); // PRQA S 3082 # Technical debt L2 id=00c12fde12797dde4087-6e8b586e99e8f7b7
}

inline bool
isNAN(const float16_storage_t& f_value) // PRQA S 3800 # Technical debt L4 id=00c174cbe6c833484e80-fd7f56babef137da
{
    // maybe improved  by looking at the bit pattern
    return vfc::isNAN(f_value.toFloat32()); // cal vfc_math function
}

} // namespace vfc

#endif // VFC_FLOAT16_STORAGE_INL_INCLUDED

//=============================================================================

