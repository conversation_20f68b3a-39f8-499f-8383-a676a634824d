//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_LIMITS_HPP_INCLUDED
#define ZX_VFC_LIMITS_HPP_INCLUDED

#include <limits> // used for numeric_limits<>

namespace vfc
{ // namespace vfc opened

//=============================================================================
//  DOXYGEN DEFGROUP vfc_group_core_algorithms_limits BEGIN
//-----------------------------------------------------------------------------
/// @defgroup vfc_group_core_algorithms_limits Math
/// @ingroup vfc_group_core_algorithms
/// @brief Limit Functions.
/// @{
//=============================================================================

//=============================================================================
//  numeric_limits
//-------------------------------------------------------------------------
/// The numeric_limits class template provides a standardized way to query various properties of arithmetic
/// types (e.g. the largest possible value for type int is std::numeric_limits<int>::max()).
/// @tparam ValueType    A type to retrieve numeric properties for
//-------------------------------------------------------------------------
using std::numeric_limits;

//=============================================================================
//  DOXYGEN DEFGROUP vfc_group_core_algorithms_limits END
//-------------------------------------------------------------------------
/// @}
//=============================================================================

} // namespace vfc

#endif // ZX_VFC_LIMITS_HPP_INCLUDED

