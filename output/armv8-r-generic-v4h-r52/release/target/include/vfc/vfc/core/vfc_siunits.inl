//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef SIUNITS_INL_INCLUDED
#define SIUNITS_INL_INCLUDED

#include "vfc/core/vfc_siunits_helper.hpp" // For UnitInfoType & conversions
#include "vfc/core/vfc_math.hpp"           // For sqr, abs
#include <cmath>                           // For floor, sin, cos

#include VFC_INCLUDE_INTERN_MATH_IMPL

/// define all functions which potentially will be used in only_qm in the internal namespace
/// The internal functions do not perform float type checks and can be used for QM environments.
namespace vfc
{

namespace intern
{

template <
    class ValueType,
    class UnitInfo1Type,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitSinCosPromote<UnitInfo1Type>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
tan(vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfoType, UserType, ConvertPolicyType> f_val);

template <
    class ValueType,
    class UnitInfo1Type,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitSinCosPromote<UnitInfo1Type>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
sin(vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfoType, UserType, ConvertPolicyType> f_val);

template <
    class ValueType,
    class UnitInfo1Type,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitSinCosPromote<UnitInfo1Type>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
cos(vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfoType, UserType, ConvertPolicyType> f_val);

template <
    class ValueType,
    class UnitInfo1Type,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitAtanPromote<UnitInfo1Type>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
atan(vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfoType, UserType, ConvertPolicyType> f_val);

template <
    class ValueType,
    class UnitInfo1Type,
    class UnitInfo2Type,
    class RBInfo1Type,
    class RBInfo2Type,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitAtan2Promote<UnitInfo1Type, UnitInfo2Type>::unit_info_type,
    typename vfc::TSIUnitRBDivisionPromote<RBInfo1Type, RBInfo2Type>::unit_info_type,
    UserType,
    ConvertPolicyType>
atan2(
    vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfo1Type, UserType, ConvertPolicyType> f_y_val,
    vfc::TSIUnits<ValueType, UnitInfo2Type, RBInfo2Type, UserType, ConvertPolicyType> f_x_val);

template <
    class ValueType,
    class UnitInfo1Type,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitAtanPromote<UnitInfo1Type>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
asin(vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfoType, UserType, ConvertPolicyType> f_val);

template <
    class ValueType,
    class UnitInfo1Type,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitAtanPromote<UnitInfo1Type>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
acos(vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfoType, UserType, ConvertPolicyType> f_val);

} // namespace intern
#ifdef VFC_ENABLE_QM_MATH
// For QM software the unrestricted versions are available in the namespace only_qm
namespace only_qm
{
using intern::acos;
using intern::asin;
using intern::atan;
using intern::atan2;
using intern::cos;
using intern::sin;
using intern::tan;
} // namespace only_qm
#endif // VFC_ENABLE_QM_MATH
} // namespace vfc

// tan function for si-unit
template <
    class ValueType,
    class UnitInfo1Type,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitSinCosPromote<UnitInfo1Type>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
vfc::intern::tan(vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfoType, UserType, ConvertPolicyType> f_val)
{
    // Only rb-unit or valid angle dominant unit allowed to detect accidental errors
    VFC_STATIC_ASSERT(((vfc::TIsValidAngleDominant<UnitInfo1Type>::value) || (UnitInfo1Type::SI_UNIT == 0)));
    vfc::TBasicTypeConvert<typename UnitInfo1Type::angle_unit_type, vfc::CAngleType>::performConversion(f_val.value());
    return (vfc::TSIUnits<
            ValueType,
            typename vfc::TSIUnitSinCosPromote<UnitInfo1Type>::unit_info_type,
            RBInfoType,
            UserType,
            ConvertPolicyType>(vfc_intern_math_alias::tan_o(f_val.value())));
}

template <
    class ValueType,
    class UnitInfo1Type,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitSinCosPromote<UnitInfo1Type>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
vfc::tan(vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfoType, UserType, ConvertPolicyType> f_val)
{
    static_assert(TIsValidFloatingForFunction<ValueType>::value, "invalid value type used for tan");
    return vfc::intern::tan(f_val);
}

// sin function for si-unit
template <
    class ValueType,
    class UnitInfo1Type,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitSinCosPromote<UnitInfo1Type>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
vfc::intern::sin(vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfoType, UserType, ConvertPolicyType> f_val)
{
    // Only rb-unit or valid angle dominant unit allowed to detect accidental errors
    VFC_STATIC_ASSERT(((vfc::TIsValidAngleDominant<UnitInfo1Type>::value) || (UnitInfo1Type::SI_UNIT == 0)));
    vfc::TBasicTypeConvert<typename UnitInfo1Type::angle_unit_type, vfc::CAngleType>::performConversion(f_val.value());
    return (vfc::TSIUnits<
            ValueType,
            typename vfc::TSIUnitSinCosPromote<UnitInfo1Type>::unit_info_type,
            RBInfoType,
            UserType,
            ConvertPolicyType>(vfc_intern_math_alias::sin_o(f_val.value())));
}

template <
    class ValueType,
    class UnitInfo1Type,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitSinCosPromote<UnitInfo1Type>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
vfc::sin(vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfoType, UserType, ConvertPolicyType> f_val)
{
    static_assert(TIsValidFloatingForFunction<ValueType>::value, "invalid value type used for sin");
    return vfc::intern::sin(f_val);
}

// cos function for si-unit
template <
    class ValueType,
    class UnitInfo1Type,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitSinCosPromote<UnitInfo1Type>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
vfc::intern::cos(vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfoType, UserType, ConvertPolicyType> f_val)
{
    // Only rb-unit or valid angle dominant unit allowed to detect accidental errors
    VFC_STATIC_ASSERT(((vfc::TIsValidAngleDominant<UnitInfo1Type>::value) || (UnitInfo1Type::SI_UNIT == 0)));
    vfc::TBasicTypeConvert<typename UnitInfo1Type::angle_unit_type, vfc::CAngleType>::performConversion(f_val.value());
    return (vfc::TSIUnits<
            ValueType,
            typename vfc::TSIUnitSinCosPromote<UnitInfo1Type>::unit_info_type,
            RBInfoType,
            UserType,
            ConvertPolicyType>(vfc_intern_math_alias::cos_o(f_val.value())));
}

template <
    class ValueType,
    class UnitInfo1Type,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitSinCosPromote<UnitInfo1Type>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
vfc::cos(vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfoType, UserType, ConvertPolicyType> f_val)
{
    static_assert(TIsValidFloatingForFunction<ValueType>::value, "invalid value type used for cos");
    return vfc::intern::cos(f_val);
}

// atan function for si-unit
template <
    class ValueType,
    class UnitInfo1Type,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitAtanPromote<UnitInfo1Type>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
vfc::intern::atan(vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfoType, UserType, ConvertPolicyType> f_val)
{
    // Only not angle dominant unit and unit with not positive power allowed to detect accidental errors
    VFC_STATIC_ASSERT(((UnitInfo1Type::ANGLE_POWER_VALUE == 0) && (vfc::TIsNegativePower<UnitInfo1Type>::value)));
    using l_arc_info_type  = typename vfc::TSIUnitAtanPromote<UnitInfo1Type>::unit_info_type;
    using l_arc_angle_type = typename l_arc_info_type::angle_unit_type;
    ValueType result       = vfc_intern_math_alias::atan_o(f_val.value()); // atan always returnes radian

    // convert value from radian (CAngleType) to target angle type:
    vfc::TBasicTypeConvert<vfc::CAngleType, l_arc_angle_type>::performConversion(result);

    // now wrap the value into the SI type:
    return vfc::TSIUnits<ValueType, l_arc_info_type, RBInfoType, UserType, ConvertPolicyType>(result);
}

template <
    class ValueType,
    class UnitInfo1Type,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitAtanPromote<UnitInfo1Type>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
vfc::atan(vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfoType, UserType, ConvertPolicyType> f_val)
{
    static_assert(TIsValidFloatingForFunction<ValueType>::value, "invalid value type used for atan");
    return vfc::intern::atan(f_val);
}

// atan2 function for two differtent si-unit-types
template <
    class ValueType,
    class UnitInfo1Type,
    class UnitInfo2Type,
    class RBInfo1Type,
    class RBInfo2Type,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitAtan2Promote<UnitInfo1Type, UnitInfo2Type>::unit_info_type,
    typename vfc::TSIUnitRBDivisionPromote<RBInfo1Type, RBInfo2Type>::unit_info_type,
    UserType,
    ConvertPolicyType>
vfc::intern::atan2(
    vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfo1Type, UserType, ConvertPolicyType> f_y_val,
    vfc::TSIUnits<ValueType, UnitInfo2Type, RBInfo2Type, UserType, ConvertPolicyType> f_x_val)
{
    // Only unit with not positive power allowed to detect accidental errors
    VFC_STATIC_ASSERT(UnitInfo1Type::ANGLE_POWER_VALUE == 0);
    using l_arc_info_type  = typename vfc::TSIUnitAtan2Promote<UnitInfo1Type, UnitInfo2Type>::unit_info_type;
    using l_arc_angle_type = typename l_arc_info_type::angle_unit_type;
    using rb_res_type      = typename vfc::TSIUnitRBDivisionPromote<RBInfo1Type, RBInfo2Type>::unit_info_type;
    vfc::TSIUnits<ValueType, l_arc_info_type, rb_res_type, UserType, ConvertPolicyType> l_res(
        vfc_intern_math_alias::atan2_o(f_y_val.value(), f_x_val.value()));
    vfc::TBasicTypeConvert<vfc::CAngleType, l_arc_angle_type>::performConversion(l_res.value());
    return l_res;
}

template <
    class ValueType,
    class UnitInfo1Type,
    class UnitInfo2Type,
    class RBInfo1Type,
    class RBInfo2Type,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitAtan2Promote<UnitInfo1Type, UnitInfo2Type>::unit_info_type,
    typename vfc::TSIUnitRBDivisionPromote<RBInfo1Type, RBInfo2Type>::unit_info_type,
    UserType,
    ConvertPolicyType>
vfc::atan2(
    vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfo1Type, UserType, ConvertPolicyType> f_y_val,
    vfc::TSIUnits<ValueType, UnitInfo2Type, RBInfo2Type, UserType, ConvertPolicyType> f_x_val)
{
    static_assert(TIsValidFloatingForFunction<ValueType>::value, "invalid value type used for atan2");
    return vfc::intern::atan2(f_y_val, f_x_val);
}

// asin function for si-unit
template <
    class ValueType,
    class UnitInfo1Type,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitAtanPromote<UnitInfo1Type>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
vfc::intern::asin(vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfoType, UserType, ConvertPolicyType> f_val)
{
    validate_range(f_val.value(), static_cast<ValueType>(-1.0), static_cast<ValueType>(1.0));

    // Only not angle dominant unit and unit with not positive power allowed to detect accidental errors
    VFC_STATIC_ASSERT(((UnitInfo1Type::ANGLE_POWER_VALUE == 0) && (vfc::TIsNegativePower<UnitInfo1Type>::value)));
    using l_arc_info_type  = typename vfc::TSIUnitAtanPromote<UnitInfo1Type>::unit_info_type;
    using l_arc_angle_type = typename l_arc_info_type::angle_unit_type;
    vfc::TSIUnits<ValueType, l_arc_info_type, RBInfoType, UserType, ConvertPolicyType> l_res(
        vfc_intern_math_alias::asin_o(f_val.value()));
    vfc::TBasicTypeConvert<vfc::CAngleType, l_arc_angle_type>::performConversion(l_res.value());
    return l_res;
}

template <
    class ValueType,
    class UnitInfo1Type,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitAtanPromote<UnitInfo1Type>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
vfc::asin(vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfoType, UserType, ConvertPolicyType> f_val)
{
    static_assert(TIsValidFloatingForFunction<ValueType>::value, "invalid value type used for asin");
    return vfc::intern::asin(f_val);
}

// acos function for si-unit
template <
    class ValueType,
    class UnitInfo1Type,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitAtanPromote<UnitInfo1Type>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
vfc::intern::acos(vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfoType, UserType, ConvertPolicyType> f_val)
{
    validate_range(f_val.value(), static_cast<ValueType>(-1.0), static_cast<ValueType>(1.0));

    // Only not angle dominant unit and unit with not positive power allowed to detect accidental errors
    VFC_STATIC_ASSERT(((UnitInfo1Type::ANGLE_POWER_VALUE == 0) && (vfc::TIsNegativePower<UnitInfo1Type>::value)));
    using l_arc_info_type  = typename vfc::TSIUnitAtanPromote<UnitInfo1Type>::unit_info_type;
    using l_arc_angle_type = typename l_arc_info_type::angle_unit_type;
    vfc::TSIUnits<ValueType, l_arc_info_type, RBInfoType, UserType, ConvertPolicyType> l_res(
        vfc_intern_math_alias::acos_o(f_val.value()));
    vfc::TBasicTypeConvert<vfc::CAngleType, l_arc_angle_type>::performConversion(l_res.value());
    return l_res;
}

template <
    class ValueType,
    class UnitInfo1Type,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitAtanPromote<UnitInfo1Type>::unit_info_type,
    RBInfoType,
    UserType,
    ConvertPolicyType>
vfc::acos(vfc::TSIUnits<ValueType, UnitInfo1Type, RBInfoType, UserType, ConvertPolicyType> f_val)
{
    static_assert(TIsValidFloatingForFunction<ValueType>::value, "invalid value type used for acos");
    return vfc::intern::acos(f_val);
}

// floor function for si-unit
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>
vfc::floor(vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> f_val)
{
    static_assert(
        TIsIntegral<ValueType>::value || TIsValidFloatingForFunction<ValueType>::value,
        "invalid value type used for floor");
    return (vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>(
        vfc_intern_math_alias::floor_o(f_val.value())));
}

// sqr function for si-unit
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitSqrPromote<UnitInfoType>::unit_info_type,
    typename vfc::TSIUnitRBSqrPromote<RBInfoType>::unit_info_type,
    UserType,
    ConvertPolicyType>
vfc::sqr(const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_val)
{

    return (vfc::TSIUnits<
            ValueType,
            typename vfc::TSIUnitSqrPromote<UnitInfoType>::unit_info_type,
            typename vfc::TSIUnitRBSqrPromote<RBInfoType>::unit_info_type,
            UserType,
            ConvertPolicyType>(vfc::sqr(f_val.value())));
}

// sqrt function for si-unit
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<
    ValueType,
    typename vfc::TSIUnitSqrtPromote<UnitInfoType>::unit_info_type,
    typename vfc::TSIUnitRBSqrtPromote<RBInfoType>::unit_info_type,
    UserType,
    ConvertPolicyType>
vfc::sqrt(vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> f_val)
{
    return (vfc::TSIUnits<
            ValueType,
            typename vfc::TSIUnitSqrtPromote<UnitInfoType>::unit_info_type,
            typename vfc::TSIUnitRBSqrtPromote<RBInfoType>::unit_info_type,
            UserType,
            ConvertPolicyType>(vfc::sqrt(f_val.value())));
}

// abs function for si-unit
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>
vfc::abs(const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_val)
{
    return (vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>(vfc::abs(f_val.value())));
}

// isNAN function for si-unit
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool vfc::isNAN(const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_nanValue_r)
{
    const ValueType l_nanValue = f_nanValue_r.value();
    return vfc::isNAN(l_nanValue);
}

// isINF function for si-unit
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool vfc::isINF(const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_infValue_r)
{
    VFC_STATIC_ASSERT(1 == vfc::TIsFloating<ValueType>::value); // Only float are supported
    const ValueType l_infValue = f_infValue_r.value();
    return vfc::isINF(l_infValue);
}

// isZero function for si-unit
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool vfc::isZero(const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_zeroValue_r)
{
    const ValueType l_zeroValue = f_zeroValue_r.value();
    return vfc::isZero(l_zeroValue);
}

// notZero function for si-unit
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool vfc::notZero(
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_notzeroValue_r)
{
    const ValueType l_notzeroValue = f_notzeroValue_r.value();
    return vfc::notZero(l_notzeroValue);
}

// ispositive function for si-unit
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool vfc::isPositive(
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_positiveValue_r)
{
    const ValueType l_positiveValue = f_positiveValue_r.value();
    return vfc::isPositive(l_positiveValue);
}

// notPositive function for si-unit
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool vfc::notPositive(
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_notpositiveValue_r)
{
    const ValueType l_notpositiveValue = f_notpositiveValue_r.value();
    return vfc::notPositive(l_notpositiveValue);
}

// isNegative function for si-unit
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool vfc::isNegative(
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_negativeValue_r)
{
    const ValueType l_negativeValue = f_negativeValue_r.value();
    return vfc::isNegative(l_negativeValue);
}

// notNegative function for si-unit
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool vfc::notNegative(
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_notnegativeValue_r)
{
    const ValueType l_notnegativeValue = f_notnegativeValue_r.value();
    return vfc::notNegative(l_notnegativeValue);
}

// isEqual function for si-unit
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool vfc::isEqual(
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_testValueA_r,
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_testValueB_r)
{
    const ValueType l_valueA = f_testValueA_r.value();
    const ValueType l_valueB = f_testValueB_r.value();
    return vfc::isEqual(l_valueA, l_valueB);
}

// isEqual function for si-unit
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool vfc::isEqual(
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_testValueA_r,
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_testValueB_r,
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_testEpsilonValue_r)
{
    const ValueType l_valueA        = f_testValueA_r.value();
    const ValueType l_valueB        = f_testValueB_r.value();
    const ValueType l_epilisonValue = f_testEpsilonValue_r.value();
    return vfc::isEqual(l_valueA, l_valueB, l_epilisonValue);
}

// notEqual function for si-unit
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
bool vfc::notEqual(
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_testValueA_r,
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& f_testValueB_r)
{
    const ValueType l_valueA = f_testValueA_r.value();
    const ValueType l_valueB = f_testValueB_r.value();
    return vfc::notEqual(l_valueA, l_valueB);
}

// minNaNPropagate function for si-unit
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> vfc::minNaNPropagate(
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& a,
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& b)
{
    static_assert(vfc::TIsFloating<ValueType>::value, "Function minNaNPropagate() is usable only with floats.");
    return vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>(
        vfc::minNaNPropagate(a.value(), b.value()));
}

// maxNaNPropagate function for si-unit
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType> vfc::maxNaNPropagate(
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& a,
    const vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>& b)
{
    static_assert(vfc::TIsFloating<ValueType>::value, "Function maxNaNPropagate() is usable only with floats.");
    return vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>(
        vfc::maxNaNPropagate(a.value(), b.value()));
}

// Specialization of numeric_limits
namespace std
{
//=============================================================================
//  numeric_limits<>
//-----------------------------------------------------------------------------
/// @brief Specialization of std:numeric_limits for siunit types,
/// to provide the properties of the used arithmetic types.
/// @param ValueType            Defines the DataType
/// @param UnitInfoType         Defines the type of SIUnit
/// @param RBInfoType           New specific Info type
/// @param UserType             Defines a specific UserType SIUnit
/// @ingroup                    vfc_core
//=============================================================================
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
class numeric_limits<vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>>
    : public numeric_limits<ValueType>
{

    using SIUnit_t = vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>;

  public:
#ifndef VFC_COMPILER_VISUALC
    // will produce compiler warning C4516
    VFC_STATIC_ASSERT((numeric_limits<ValueType>::is_specialized));
#endif
    static constexpr SIUnit_t min() VFC_NOEXCEPT { return SIUnit_t(vfc::numeric_limits<ValueType>::min()); }
    static constexpr SIUnit_t max() VFC_NOEXCEPT { return SIUnit_t(vfc::numeric_limits<ValueType>::max()); }
    static constexpr SIUnit_t lowest() VFC_NOEXCEPT { return SIUnit_t(vfc::numeric_limits<ValueType>::lowest()); }
    static constexpr SIUnit_t epsilon() VFC_NOEXCEPT { return SIUnit_t(vfc::numeric_limits<ValueType>::epsilon()); }
    static constexpr SIUnit_t round_error() VFC_NOEXCEPT
    {
        return SIUnit_t(vfc::numeric_limits<ValueType>::round_error());
    }
};
} // namespace std

#endif // SIUNITS_INL_INCLUDED

//=============================================================================

