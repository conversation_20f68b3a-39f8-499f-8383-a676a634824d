//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_TUPLE_INL_INCLUDED
#define VFC_TUPLE_INL_INCLUDED

///////////////////////////////////////////////////////////////////////////////
// TPair
///////////////////////////////////////////////////////////////////////////////

template <class FirstType, class SecondType>
inline vfc::TPair<FirstType, SecondType>::TPair(void) : m_first(first_type()), m_second(second_type())
{
    // intentionally left blank
}

template <class FirstType, class SecondType>
inline vfc::TPair<FirstType, SecondType>::TPair(const FirstType& f_first, const SecondType& f_second)
    : m_first(f_first), m_second(f_second)
{
    // intentionally left blank
}

template <class FirstType, class SecondType>
inline vfc::TPair<FirstType, SecondType>::TPair(const TPair<FirstType, SecondType>& f_other)
    : m_first(f_other.first()), m_second(f_other.second())
{
}

// To be conformal with the std::pair implementation, the test for self-assignment is skipped
template <class FirstType, class SecondType>
inline vfc::TPair<FirstType, SecondType>& vfc::TPair<FirstType, SecondType>::operator=(
    const TPair<FirstType, SecondType>&
        f_other) // PRQA S 4072 # Technical debt L2 id=00c1abb47ce294a24fc7-93ebd2e4014f8eb5
{
    m_first  = f_other.first();
    m_second = f_other.second();
    return *this;
}

template <class FirstType, class SecondType>
inline vfc::TPair<FirstType, SecondType> vfc::make_pair(const FirstType& f_first, const SecondType& f_second)
{
    return TPair<FirstType, SecondType>(f_first, f_second);
}

template <class FirstType, class SecondType>
inline vfc::TPair<FirstType, SecondType> vfc::make_tuple(const FirstType& f_first, const SecondType& f_second)
{
    return TPair<FirstType, SecondType>(f_first, f_second);
}

template <class FirstType, class SecondType>
inline bool vfc::operator==(const TPair<FirstType, SecondType>& f_lhs, const TPair<FirstType, SecondType>& f_rhs)
{
    return (
        (f_lhs.first() == f_rhs.first()) // PRQA S 3270 # Technical debt L2 id=00c1c229f76f479b45ea-5bc191263dd1db88
        &&
        (f_lhs.second() == f_rhs.second())); // PRQA S 3270 # Technical debt L2 id=00c10298d321b76d4cf2-55a953c0c063bc4b
}

template <class FirstType, class SecondType>
inline bool vfc::operator!=(const TPair<FirstType, SecondType>& f_lhs, const TPair<FirstType, SecondType>& f_rhs)
{
    return (!(f_lhs == f_rhs));
}

template <class FirstType, class SecondType>
inline bool vfc::operator<(const TPair<FirstType, SecondType>& f_lhs, const TPair<FirstType, SecondType>& f_rhs)
{
    // the return value is true if the first element of x is less than the first element of y,
    // and false if the first element of y is less than the first element of x.
    // If neither of these is the case, then operator< returns the result of comparing the second elements of x and y.
    // This operator may only be used if both T1 and T2 are LessThanComparable
    return (
        (f_lhs.first() < f_rhs.first()) || ((!(f_rhs.first() < f_lhs.first())) && (f_lhs.second() < f_rhs.second())));
}

template <class FirstType, class SecondType>
inline bool vfc::operator>(const TPair<FirstType, SecondType>& f_lhs, const TPair<FirstType, SecondType>& f_rhs)
{
    return (f_rhs < f_lhs);
}

template <class FirstType, class SecondType>
inline bool vfc::operator<=(const TPair<FirstType, SecondType>& f_lhs, const TPair<FirstType, SecondType>& f_rhs)
{
    return (!(f_rhs < f_lhs));
}

template <class FirstType, class SecondType>
inline bool vfc::operator>=(const TPair<FirstType, SecondType>& f_lhs, const TPair<FirstType, SecondType>& f_rhs)
{
    return (!(f_lhs < f_rhs));
}

///////////////////////////////////////////////////////////////////////////////
// TTriple
///////////////////////////////////////////////////////////////////////////////

template <class FirstType, class SecondType, class ThirdType>
inline vfc::TTriple<FirstType, SecondType, ThirdType>::TTriple(void)
    : m_first(FirstType()), m_second(SecondType()), m_third(ThirdType())
{
    // intentionally left blank
}

template <class FirstType, class SecondType, class ThirdType>
inline vfc::TTriple<FirstType, SecondType, ThirdType>::TTriple(
    const FirstType&  f_first,
    const SecondType& f_second,
    const ThirdType&  f_third)
    : m_first(f_first), m_second(f_second), m_third(f_third)
{
    // intentionally left blank
}

template <class FirstType, class SecondType, class ThirdType>
inline vfc::TTriple<FirstType, SecondType, ThirdType>::TTriple(const TTriple<FirstType, SecondType, ThirdType>& f_other)
    : m_first(f_other.first()), m_second(f_other.second()), m_third(f_other.third())
{
    // intentionally left blank
}

// To be conformal with the std::pair implementation, the test for self-assignment is skipped
template <class FirstType, class SecondType, class ThirdType>
inline vfc::TTriple<FirstType, SecondType, ThirdType>& vfc::TTriple<FirstType, SecondType, ThirdType>::operator=(
    const TTriple<FirstType, SecondType, ThirdType>&
        f_other) // PRQA S 4072 # Technical debt L2 id=00c14c0454c12cd4429c-4cd753013c2d8864
{
    m_first  = f_other.first();
    m_second = f_other.second();
    m_third  = f_other.third();
    return *this;
}

template <class FirstType, class SecondType, class ThirdType>
inline vfc::TTriple<FirstType, SecondType, ThirdType>
vfc::make_triple(const FirstType& f_first, const SecondType& f_second, const ThirdType& f_third)
{
    return TTriple<FirstType, SecondType, ThirdType>(f_first, f_second, f_third);
}

template <class FirstType, class SecondType, class ThirdType>
inline vfc::TTriple<FirstType, SecondType, ThirdType>
vfc::make_tuple(const FirstType& f_first, const SecondType& f_second, const ThirdType& f_third)
{
    return TTriple<FirstType, SecondType, ThirdType>(f_first, f_second, f_third);
}

template <class FirstType, class SecondType, class ThirdType>
inline bool vfc::operator==(
    const TTriple<FirstType, SecondType, ThirdType>& f_lhs,
    const TTriple<FirstType, SecondType, ThirdType>& f_rhs)
{
    return (
        (f_lhs.first() == f_rhs.first()) // PRQA S 3270 # Technical debt L2 id=00c15550beffeb7d4536-5bc191263dd1db88
        &&
        (f_lhs.second() == f_rhs.second()) // PRQA S 3270 # Technical debt L2 id=00c18558542478b546e0-260b1caa8b6741e2
        &&
        (f_lhs.third() == f_rhs.third())); // PRQA S 3270 # Technical debt L2 id=00c16a31211c72de45f3-f99990c8cb52765b
}

template <class FirstType, class SecondType, class ThirdType>
inline bool vfc::operator!=(
    const TTriple<FirstType, SecondType, ThirdType>& f_lhs,
    const TTriple<FirstType, SecondType, ThirdType>& f_rhs)
{
    return (!(f_lhs == f_rhs));
}

template <class FirstType, class SecondType, class ThirdType>
inline bool vfc::operator<(
    const TTriple<FirstType, SecondType, ThirdType>& f_lhs,
    const TTriple<FirstType, SecondType, ThirdType>& f_rhs)
{
    if (f_lhs.first() == f_rhs.first()) // PRQA S 3270 # Technical debt L2 id=00c1ad9d06c0a3e643c1-6942ce1ff9b0f36a
    {
        if (f_lhs.second() ==
            f_rhs.second()) // PRQA S 3270 # Technical debt L2 id=00c1c24802be61ab4c7e-0fccc8767c2cd887
        {
            return (f_lhs.third() < f_rhs.third());
        }
        else
        {
            return (f_lhs.second() < f_rhs.second());
        }
    }
    else
    {
        return (f_lhs.first() < f_rhs.first());
    }
}

template <class FirstType, class SecondType, class ThirdType>
inline bool vfc::operator>(
    const TTriple<FirstType, SecondType, ThirdType>& f_lhs,
    const TTriple<FirstType, SecondType, ThirdType>& f_rhs)
{
    return (f_rhs < f_lhs);
}

template <class FirstType, class SecondType, class ThirdType>
inline bool vfc::operator<=(
    const TTriple<FirstType, SecondType, ThirdType>& f_lhs,
    const TTriple<FirstType, SecondType, ThirdType>& f_rhs)
{
    return (!(f_rhs < f_lhs));
}

template <class FirstType, class SecondType, class ThirdType>
inline bool vfc::operator>=(
    const TTriple<FirstType, SecondType, ThirdType>& f_lhs,
    const TTriple<FirstType, SecondType, ThirdType>& f_rhs)
{
    return (!(f_lhs < f_rhs));
}

///////////////////////////////////////////////////////////////////////////////
// TQuadruple
///////////////////////////////////////////////////////////////////////////////

template <class FirstType, class SecondType, class ThirdType, class FourthType>
inline vfc::TQuadruple<FirstType, SecondType, ThirdType, FourthType>::TQuadruple(void)
    : m_first(FirstType()), m_second(SecondType()), m_third(ThirdType()), m_fourth(FourthType())
{
    // intentionally left blank
}

template <class FirstType, class SecondType, class ThirdType, class FourthType>
inline vfc::TQuadruple<FirstType, SecondType, ThirdType, FourthType>::TQuadruple(
    const FirstType&  f_first,
    const SecondType& f_second,
    const ThirdType&  f_third,
    const FourthType& f_fourth)
    : m_first(f_first), m_second(f_second), m_third(f_third), m_fourth(f_fourth)
{
    // intentionally left blank
}

template <class FirstType, class SecondType, class ThirdType, class FourthType>
inline vfc::TQuadruple<FirstType, SecondType, ThirdType, FourthType>::TQuadruple(
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_other)
    : m_first(f_other.first()), m_second(f_other.second()), m_third(f_other.third()), m_fourth(f_other.fourth())
{
    // intentionally left blank
}

// To be conformal with the std::pair implementation, the test for self-assignment is skipped
template <class FirstType, class SecondType, class ThirdType, class FourthType>
inline vfc::TQuadruple<FirstType, SecondType, ThirdType, FourthType>&
vfc::TQuadruple<FirstType, SecondType, ThirdType, FourthType>::operator=(
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>&
        f_other) // PRQA S 4072 # Technical debt L2 id=00c11fae632e5d464ada-01397cb28f199f7d
{

    m_first  = f_other.first();
    m_second = f_other.second();
    m_third  = f_other.third();
    m_fourth = f_other.fourth();
    return *this;
}

template <class FirstType, class SecondType, class ThirdType, class FourthType>
inline vfc::TQuadruple<FirstType, SecondType, ThirdType, FourthType> vfc::make_quadruple(
    const FirstType&  f_first,
    const SecondType& f_second,
    const ThirdType&  f_third,
    const FourthType& f_fourth)
{
    return TQuadruple<FirstType, SecondType, ThirdType, FourthType>(f_first, f_second, f_third, f_fourth);
}

template <class FirstType, class SecondType, class ThirdType, class FourthType>
inline vfc::TQuadruple<FirstType, SecondType, ThirdType, FourthType> vfc::make_tuple(
    const FirstType&  f_first,
    const SecondType& f_second,
    const ThirdType&  f_third,
    const FourthType& f_fourth)
{
    return TQuadruple<FirstType, SecondType, ThirdType, FourthType>(f_first, f_second, f_third, f_fourth);
}

template <class FirstType, class SecondType, class ThirdType, class FourthType>
inline bool vfc::operator==(
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_lhs,
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_rhs)
{
    return (
        (f_lhs.first() == f_rhs.first()) // PRQA S 3270 # Technical debt L2 id=00c178299ab8d7a9490f-5bc191263dd1db88
        &&
        (f_lhs.second() == f_rhs.second())  // PRQA S 3270 # Technical debt L2 id=00c16ca5c8f85de540b9-260b1caa8b6741e2
        && (f_lhs.third() == f_rhs.third()) // PRQA S 3270 # Technical debt L2 id=00c111201693bc254096-0cac14177bfde149
        &&
        (f_lhs.fourth() == f_rhs.fourth())); // PRQA S 3270 # Technical debt L2 id=00c1c95058314f20469a-e61841d0400fa154
}

template <class FirstType, class SecondType, class ThirdType, class FourthType>
inline bool vfc::operator!=(
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_lhs,
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_rhs)
{
    return (!(f_lhs == f_rhs));
}

template <class FirstType, class SecondType, class ThirdType, class FourthType>
inline bool vfc::operator<(
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_lhs,
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_rhs)
{
    if (f_lhs.first() == f_rhs.first()) // PRQA S 3270 # Technical debt L2 id=00c1b437f67be69d4578-6942ce1ff9b0f36a
    {
        if (f_lhs.second() ==
            f_rhs.second()) // PRQA S 3270 # Technical debt L2 id=00c1a00c2b86870a4055-0fccc8767c2cd887
        {
            if (f_lhs.third() ==
                f_rhs.third()) // PRQA S 3270 # Technical debt L2 id=00c16b3f594c54494033-aeb7fd601bea1ad5
            {
                return (f_lhs.fourth() < f_rhs.fourth());
            }
            else
            {
                return (f_lhs.third() < f_rhs.third());
            }
        }
        else
        {
            return (f_lhs.second() < f_rhs.second());
        }
    }
    else
    {
        return (f_lhs.first() < f_rhs.first());
    }
}

template <class FirstType, class SecondType, class ThirdType, class FourthType>
inline bool vfc::operator>(
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_lhs,
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_rhs)
{
    return (f_rhs < f_lhs);
}

template <class FirstType, class SecondType, class ThirdType, class FourthType>
inline bool vfc::operator<=(
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_lhs,
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_rhs)
{
    return (!(f_rhs < f_lhs));
}

template <class FirstType, class SecondType, class ThirdType, class FourthType>
inline bool vfc::operator>=(
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_lhs,
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_rhs)
{
    return (!(f_lhs < f_rhs));
}

#endif // VFC_TUPLE_INL_INCLUDED

//=============================================================================

