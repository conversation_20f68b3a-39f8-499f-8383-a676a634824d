//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_ITERATOR_HPP_INCLUDED
#define VFC_ITERATOR_HPP_INCLUDED

#include <iterator>

namespace vfc
{ // namespace vfc opened

// Provide stl reverse_iterator
// reverse_iterator is an iterator adaptor that reverses the direction of a given iterator.
// For example, when provided with a bidirectional iterator, std::reverse_iterator produces
// a new iterator that moves from the end to the beginning of the sequence defined by the
// underlying bidirectional iterator.
using std::reverse_iterator;

// Iterator category tags carry information that can be used to select the most efficient
// algorithms for the specific requirement set that is implied by the category.

// Provide stl input_iterator_tag
using std::input_iterator_tag;

// Provide stl output_iterator_tag
using std::output_iterator_tag;

// Provide stl forward_iterator_tag
using std::forward_iterator_tag;

// Provide stl bidirectional_iterator_tag
using std::bidirectional_iterator_tag;

// Provide stl random_access_iterator_tag
using std::random_access_iterator_tag;

} // namespace vfc

#endif // VFC_ITERATOR_HPP_INCLUDED

