//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_TRECT_INL_INCLUDED
#define VFC_TRECT_INL_INCLUDED

#include <algorithm>                 // used for ::std::min(), ::std::max()
#include "vfc/core/vfc_math.hpp"     // used for vfc::isZero
#include "vfc/core/vfc_metaprog.hpp" // used for TEnableIf

namespace vfc
{
namespace intern
{ // namespace intern opened

// the center functions have been moved from the TRect class to be free internal functions
// to avoid compilation issues when the class functions are instantiated.

/// center function overload for non-SIUnitTypes
template <class ValueType>
inline vfc::TPoint<ValueType> center(const vfc::TRect<ValueType>& f_rect, vfc::true_t);

/// center function overload for SIUnitTypes
template <class ValueType>
inline vfc::TPoint<ValueType> center(const vfc::TRect<ValueType>& f_rect, vfc::false_t);
} // namespace intern
} // namespace vfc

template <class ValueType>
inline vfc::TPoint<ValueType>::TPoint(void) : m_x(static_cast<ValueType>(0)), m_y(static_cast<ValueType>(0))
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in TPoint::TPoint");
}

template <class ValueType>
inline vfc::TPoint<ValueType>::TPoint(
    typename vfc::TPoint<ValueType>::value_type f_x,
    typename vfc::TPoint<ValueType>::value_type f_y)
    : m_x(f_x), m_y(f_y)
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in TPoint::TPoint");
}

template <class ValueType>
inline const vfc::TPoint<ValueType>& vfc::TPoint<ValueType>::operator+=(
    const TPoint<ValueType>& f_rhs) // PRQA S 2093 # Technical debt L7 id=00c19f5391a890734aea-e15254bfa3014c1b
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in TPoint::operator+=");
    m_x += f_rhs.m_x;
    m_y += f_rhs.m_y;
    return *this;
}

template <class ValueType>
inline const vfc::TPoint<ValueType>& vfc::TPoint<ValueType>::operator-=(
    const TPoint<ValueType>& f_rhs) // PRQA S 2093 # Technical debt L7 id=00c1b6d919bc61b141b0-da633b18404e2ec9
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in TPoint::operator-=");
    m_x -= f_rhs.m_x;
    m_y -= f_rhs.m_y;
    return *this;
}

template <class ValueType>
inline const vfc::TPoint<ValueType> vfc::operator+(const TPoint<ValueType>& f_op1, const TPoint<ValueType>& f_op2)
{
    return TPoint<ValueType>(f_op1).operator+=(f_op2);
}

template <class ValueType>
inline const vfc::TPoint<ValueType> vfc::operator-(const TPoint<ValueType>& f_op1, const TPoint<ValueType>& f_op2)
{
    return TPoint<ValueType>(f_op1).operator-=(f_op2);
}

template <class ValueType>
inline vfc::TSize<ValueType>::TSize(void)
    : m_cx(static_cast<typename vfc::TPoint<ValueType>::value_type>(0)),
      m_cy(static_cast<typename vfc::TPoint<ValueType>::value_type>(0))
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in TSize::TSize");
}

template <class ValueType>
inline vfc::TSize<ValueType>::TSize(
    typename vfc::TSize<ValueType>::value_type f_cx,
    typename vfc::TSize<ValueType>::value_type f_cy)
    : m_cx(f_cx), m_cy(f_cy)
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in TSize::TSize");
}

template <class ValueType>
inline const vfc::TSize<ValueType>& vfc::TSize<ValueType>::operator+=(
    const TSize<ValueType>& f_rhs) // PRQA S 2093 # Technical debt L7 id=00c1ac39879429344303-011938d6c0b4c428
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in TSize::operator+=");
    m_cx += f_rhs.m_cx;
    m_cy += f_rhs.m_cy;
    return *this;
}

template <class ValueType>
inline const vfc::TSize<ValueType>& vfc::TSize<ValueType>::operator-=(
    const TSize<ValueType>& f_rhs) // PRQA S 2093 # Technical debt L7 id=00c11b306ecdb89348e0-13cc9079047c2008
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in TSize::operator-=");
    m_cx -= f_rhs.m_cx;
    m_cy -= f_rhs.m_cy;
    return *this;
}

template <class ValueType>
inline const vfc::TSize<ValueType> vfc::operator+(const TSize<ValueType>& f_op1, const TSize<ValueType>& f_op2)
{
    return TSize<ValueType>(f_op1).operator+=(f_op2);
}

template <class ValueType>
inline const vfc::TSize<ValueType> vfc::operator-(const TSize<ValueType>& f_op1, const TSize<ValueType>& f_op2)
{
    return TSize<ValueType>(f_op1).operator-=(f_op2);
}

template <class ValueType>
inline vfc::TRect<ValueType>::TRect(void)
    : m_left(static_cast<typename vfc::TPoint<ValueType>::value_type>(0)),
      m_top(static_cast<typename vfc::TPoint<ValueType>::value_type>(0)),
      m_width(static_cast<typename vfc::TPoint<ValueType>::value_type>(0)),
      m_height(static_cast<typename vfc::TPoint<ValueType>::value_type>(0))
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in TRect::TRect");
}

template <class ValueType>
inline vfc::TRect<ValueType>::TRect(ValueType f_left, ValueType f_top, ValueType f_width, ValueType f_height)
    : m_left(static_cast<typename vfc::TRect<ValueType>::value_type>(f_left)),
      m_top(static_cast<typename vfc::TRect<ValueType>::value_type>(f_top)),
      m_width(static_cast<typename vfc::TRect<ValueType>::value_type>(f_width)),
      m_height(static_cast<typename vfc::TRect<ValueType>::value_type>(f_height))
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in TRect::TRect");
}

template <class ValueType>
inline vfc::TRect<ValueType>::TRect(const TPoint<ValueType>& f_topLeft, const TSize<ValueType>& f_size)
    : m_left(f_topLeft.x()), m_top(f_topLeft.y()), m_width(f_size.cx()), m_height(f_size.cy())
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in TRect::TRect");
}

template <class ValueType>
inline vfc::TRect<ValueType>::TRect(const TPoint<ValueType>& f_topLeft, const TPoint<ValueType>& f_bottomRight)
    : m_left(f_topLeft.x()),
      m_top(f_topLeft.y()),
      m_width(
          f_bottomRight.x() -
          f_topLeft.x()), // PRQA S 3010 # Technical debt L2 id=00c15d512b4e534e4706-ca309321449ea4b7
      m_height(
          f_bottomRight.y() - f_topLeft.y()) // PRQA S 3010 # Technical debt L2 id=00c1a9a2975cdb1d4b75-2d3ed46de673b41f
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in TRect::TRect");
}

template <class ValueType>
inline vfc::TPoint<ValueType> vfc::TRect<ValueType>::topLeft(void) const
{
    return TPoint<ValueType>(left(), top());
}

template <class ValueType>
inline vfc::TPoint<ValueType> vfc::TRect<ValueType>::bottomRight(void) const
{
    return TPoint<ValueType>(right(), bottom());
}

template <class ValueType>
inline vfc::TPoint<ValueType> vfc::TRect<ValueType>::center(void) const
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in TRect::center");
    using IsSIUnit_t = typename vfc::TIf<vfc::TIsSIUnitType<ValueType>::value, false_t, true_t>::type;
    return vfc::intern::center(*this, IsSIUnit_t());
}

template <class ValueType>
inline vfc::TPoint<ValueType> vfc::intern::center(const vfc::TRect<ValueType>& f_rect, vfc::true_t)
{
    return vfc::TPoint<ValueType>(
        static_cast<ValueType>(f_rect.left() + f_rect.right()) /
            static_cast<ValueType>(2), // PRQA S 3010 # Technical debt L2 id=00c19eaa83c7cf3240b7-cb9544516854a70a
        static_cast<ValueType>(f_rect.top() + f_rect.bottom()) /
            static_cast<ValueType>(2)); // PRQA S 3010 # Technical debt L2 id=00c112a8e93104854f1b-c3bb65b3a246e3e2
}

template <class ValueType>
inline vfc::TPoint<ValueType> vfc::intern::center(const vfc::TRect<ValueType>& f_rect, vfc::false_t)
{
    return vfc::TPoint<ValueType>(
        static_cast<ValueType>(f_rect.left() + f_rect.right()) / static_cast<typename ValueType::value_type>(2),
        static_cast<ValueType>(f_rect.top() + f_rect.bottom()) / static_cast<typename ValueType::value_type>(2));
}

template <class ValueType>
inline vfc::TSize<ValueType> vfc::TRect<ValueType>::size(void) const
{
    return TSize<ValueType>(width(), height());
}

template <class ValueType>
inline typename vfc::TRect<ValueType>::area_type vfc::TRect<ValueType>::area(void) const
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in TRect::area");
    return (m_width * m_height); // PRQA S 3010 # Technical debt L2 id=00c179134b2c56514337-86815b2279f05e53
}

template <class ValueType>
inline bool vfc::TRect<ValueType>::isEmpty(void) const
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in TRect::isEmpty");
    return ((static_cast<ValueType>(0) >= width()) || (static_cast<ValueType>(0) >= height()));
}

template <class ValueType>
inline bool vfc::TRect<ValueType>::contains(const TRect<ValueType>& f_otherRect) const
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in TRect::contains");
    return (f_otherRect.left() >= left()) && (f_otherRect.top() >= top()) && (f_otherRect.right() <= right()) &&
           (f_otherRect.bottom() <= bottom());
}

template <class ValueType>
inline bool vfc::TRect<ValueType>::contains(const TPoint<ValueType>& f_point) const
{
    return contains(f_point.x(), f_point.y());
}

template <class ValueType>
inline bool vfc::TRect<ValueType>::contains(ValueType f_px, ValueType f_py) const
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in TRect::contains");
    return ((f_px >= left()) && (f_px < right()) && (f_py >= top()) && (f_py < bottom()));
}

template <class ValueType>
inline void vfc::TRect<ValueType>::resize(ValueType f_cx, ValueType f_cy)
{
    m_width  = f_cx;
    m_height = f_cy;
}

template <class ValueType>
inline void vfc::TRect<ValueType>::resize(const TSize<ValueType>& f_newSize)
{
    resize(f_newSize.cx(), f_newSize.cy());
}

template <class ValueType>
inline void vfc::TRect<ValueType>::normalize(void)
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in TRect::normalize");
    if (static_cast<ValueType>(0) > m_width)
    {
        m_left += m_width;
        m_width = -m_width; // PRQA S 3010 # Technical debt L2 id=00c11ed8f3bb0ce84627-480f0599aa0ac9b1
    }
    if (static_cast<ValueType>(0) > m_height)
    {
        m_top += m_height;
        m_height = -m_height; // PRQA S 3010 # Technical debt L2 id=00c16bbb03fd9bf440e6-7ff7695604b7c988
    }
}

template <class ValueType>
inline void vfc::TRect<ValueType>::inflate(ValueType f_dx, ValueType f_dy)
{
    inflate(f_dx, f_dy, f_dx, f_dy);
}

template <class ValueType>
inline void vfc::TRect<ValueType>::inflate(ValueType f_dleft, ValueType f_dtop, ValueType f_dright, ValueType f_dbottom)
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in TRect::inflate");
    m_left -= f_dleft;
    m_top -= f_dtop;

    m_width += f_dleft + f_dright;
    m_height += f_dtop + f_dbottom;
}

template <class ValueType>
inline void vfc::TRect<ValueType>::deflate(ValueType f_dx, ValueType f_dy)
{
    inflate(-f_dx, -f_dy, -f_dx, -f_dy); // PRQA S 3010 # Technical debt L2 id=00c1136d27d64a94436a-711ecfb7f118e33f
}

template <class ValueType>
inline void vfc::TRect<ValueType>::deflate(ValueType f_dleft, ValueType f_dtop, ValueType f_dright, ValueType f_dbottom)
{
    inflate(
        -f_dleft,
        -f_dtop,
        -f_dright,
        -f_dbottom); // PRQA S 3010 # Technical debt L2 id=00c170721a1406be4030-8d554a4659de1775
}

template <class ValueType>
inline void vfc::TRect<ValueType>::offset(ValueType f_dx, ValueType f_dy)
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in TRect::offset");
    m_left += f_dx;
    m_top += f_dy;
}

template <class ValueType>
inline void vfc::TRect<ValueType>::moveTo(ValueType f_x, ValueType f_y)
{
    m_left = f_x;
    m_top  = f_y;
}

template <class ValueType>
inline void vfc::TRect<ValueType>::moveTo(const TPoint<ValueType>& f_point)
{
    moveTo(f_point.x(), f_point.y());
}

template <class ValueType>
inline vfc::TRect<ValueType> vfc::union_rect(const TRect<ValueType>& f_op1, const TRect<ValueType>& f_op2)
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in union_rect");
    return TRect<ValueType>(
        TPoint<ValueType>((stlalias::min)(f_op1.left(), f_op2.left()), (stlalias::min)(f_op1.top(), f_op2.top())),
        TPoint<ValueType>(
            (stlalias::max)(f_op1.right(), f_op2.right()), (stlalias::max)(f_op1.bottom(), f_op2.bottom())));
}

template <class ValueType>
inline vfc::TRect<ValueType> vfc::intersect_rect(const TRect<ValueType>& f_op1, const TRect<ValueType>& f_op2)
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used intersect_rect");
    return TRect<ValueType>(
        TPoint<ValueType>((stlalias::max)(f_op1.left(), f_op2.left()), (stlalias::max)(f_op1.top(), f_op2.top())),
        TPoint<ValueType>(
            (stlalias::min)(f_op1.right(), f_op2.right()), (stlalias::min)(f_op1.bottom(), f_op2.bottom())));
}

template <class ValueType>
inline bool vfc::operator==(const TRect<ValueType>& f_lhs, const TRect<ValueType>& f_rhs)
{
    static_assert(TIsValidArithmetic<ValueType>::value, "invalid value type used in operator==(TRect,TRect)");
    return (
        vfc::isZero(f_lhs.width() - f_rhs.width()) && vfc::isZero(f_lhs.height() - f_rhs.height()) &&
        vfc::isZero(f_lhs.left() - f_rhs.left()) && vfc::isZero(f_lhs.top() - f_rhs.top()));
}

template <class ValueType>
inline bool vfc::operator!=(const TRect<ValueType>& f_lhs, const TRect<ValueType>& f_rhs)
{
    return !(f_lhs == f_rhs);
}

#endif // VFC_TRECT_INL_INCLUDED

//=============================================================================

