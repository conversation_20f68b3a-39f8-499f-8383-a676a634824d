//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

//=============================================================================

#ifndef VFC_INTERN_MATH_IMPL_HPP
#define VFC_INTERN_MATH_IMPL_HPP

#include <cmath>

#include "vfc/core/vfc_builtin_types.hpp"

/// @file vfc internal math
/// @brief vfc math file calling the C-lib math functions
/// Instructions how to create your customer math impl file:
/// Create your customer file by initial copying this file. In your customer file:
/// -rename the "package macro" (line 26 and 27)
/// -include your customer math header file (line 29)
/// -define another unique customer namespace (line 47-51)
///         e.g. instead of "::vfc::intern::math_impl" use "::customXY_math_impl" or "::customXY::math_impl"
/// -final step only change the function bodies and call the desired math functions
///         e.g. instead of calling "::ceil" call "::customXY::ceil"
namespace vfc
{
namespace intern
{
namespace math_impl
{
// Suffix _d for double, _f for float, _o for overload
inline built_in::double_t fabs_o(built_in::double_t x) { return ::std::fabs(x); }
inline built_in::float_t  fabs_o(built_in::float_t x) { return ::fabsf(x); }

inline built_in::double_t floor_d(built_in::double_t x) { return ::std::floor(x); }
inline built_in::float_t  floor_f(built_in::float_t x) { return ::floorf(x); }
inline built_in::double_t floor_o(built_in::double_t x) { return floor_d(x); }
inline built_in::float_t  floor_o(built_in::float_t x) { return floor_f(x); }

inline built_in::double_t pow_d(built_in::double_t x, built_in::double_t y) { return ::std::pow(x, y); }
inline built_in::float_t  pow_f(built_in::float_t x, built_in::float_t y) { return ::powf(x, y); }

inline built_in::double_t log_d(built_in::double_t x) { return ::std::log(x); }
inline built_in::float_t  log_f(built_in::float_t x) { return ::logf(x); }

inline built_in::double_t log10_d(built_in::double_t x) { return ::std::log10(x); }
inline built_in::float_t  log10_f(built_in::float_t x) { return ::log10f(x); }

inline built_in::float_t log2_f(built_in::float_t x) { return ::std::log2f(x); }

inline built_in::double_t sin_d(built_in::double_t x) { return ::std::sin(x); }
inline built_in::float_t  sin_f(built_in::float_t x) { return ::sinf(x); }
inline built_in::double_t sin_o(built_in::double_t x) { return ::std::sin(x); }
inline built_in::float_t  sin_o(built_in::float_t x) { return ::sinf(x); }

inline built_in::double_t cos_d(built_in::double_t x) { return ::std::cos(x); }
inline built_in::float_t  cos_f(built_in::float_t x) { return ::cosf(x); }
inline built_in::double_t cos_o(built_in::double_t x) { return ::std::cos(x); }
inline built_in::float_t  cos_o(built_in::float_t x) { return ::cosf(x); }

inline built_in::double_t tan_d(built_in::double_t x) { return ::std::tan(x); }
inline built_in::float_t  tan_f(built_in::float_t x) { return ::tanf(x); }
inline built_in::double_t tan_o(built_in::double_t x) { return ::std::tan(x); }
inline built_in::float_t  tan_o(built_in::float_t x) { return ::tanf(x); }

inline built_in::double_t asin_d(built_in::double_t x) { return ::std::asin(x); }
inline built_in::float_t  asin_f(built_in::float_t x) { return ::asinf(x); }
inline built_in::double_t asin_o(built_in::double_t x) { return ::std::asin(x); }
inline built_in::float_t  asin_o(built_in::float_t x) { return ::asinf(x); }

inline built_in::double_t acos_d(built_in::double_t x) { return ::std::acos(x); }
inline built_in::float_t  acos_f(built_in::float_t x) { return ::acosf(x); }
inline built_in::double_t acos_o(built_in::double_t x) { return ::std::acos(x); }
inline built_in::float_t  acos_o(built_in::float_t x) { return ::acosf(x); }

inline built_in::double_t atan_d(built_in::double_t x) { return ::std::atan(x); }
inline built_in::float_t  atan_f(built_in::float_t x) { return ::atanf(x); }
inline built_in::double_t atan_o(built_in::double_t x) { return ::std::atan(x); }
inline built_in::float_t  atan_o(built_in::float_t x) { return ::atanf(x); }

inline built_in::double_t atan2_d(built_in::double_t x, built_in::double_t y) { return ::std::atan2(x, y); }
inline built_in::float_t  atan2_f(built_in::float_t x, built_in::float_t y) { return ::atan2f(x, y); }
inline built_in::double_t atan2_o(built_in::double_t x, built_in::double_t y) { return ::std::atan2(x, y); }
inline built_in::float_t  atan2_o(built_in::float_t x, built_in::float_t y) { return ::atan2f(x, y); }

inline built_in::double_t sinh_d(built_in::double_t x) { return ::std::sinh(x); }
inline built_in::float_t  sinh_f(built_in::float_t x) { return ::sinhf(x); }

inline built_in::double_t cosh_d(built_in::double_t x) { return ::std::cosh(x); }
inline built_in::float_t  cosh_f(built_in::float_t x) { return ::coshf(x); }

inline built_in::double_t tanh_d(built_in::double_t x) { return ::std::tanh(x); }
inline built_in::float_t  tanh_f(built_in::float_t x) { return ::tanhf(x); }

inline built_in::double_t erf_d(built_in::double_t x) { return ::std::erf(x); }
inline built_in::float_t  erf_f(built_in::float_t x) { return ::std::erff(x); }

inline built_in::double_t sqrt_d(built_in::double_t x) { return ::std::sqrt(x); }
inline built_in::float_t  sqrt_f(built_in::float_t x) { return ::sqrtf(x); }

inline built_in::float_t ceil_f(built_in::float_t x) { return ::ceilf(x); }

inline built_in::float_t exp_f(built_in::float_t x) { return ::expf(x); }
inline built_in::float_t exp2_f(built_in::float_t x) { return ::std::exp2f(x); }
inline built_in::float_t fmod_f(built_in::float_t x, built_in::float_t y) { return ::fmodf(x, y); }
inline built_in::float_t round_f(built_in::float_t x) { return ::std::roundf(x); }
inline built_in::float_t nextafter_f(built_in::float_t x, built_in::float_t y) { return ::std::nextafterf(x, y); }

inline built_in::int_t      fpclassify_f(built_in::float_t x) { return ::std::fpclassify(x); }
inline built_in::longlong_t llround_f(built_in::float_t x) { return ::std::llroundf(x); }
} // namespace math_impl
} // namespace intern
} // namespace vfc
#endif

