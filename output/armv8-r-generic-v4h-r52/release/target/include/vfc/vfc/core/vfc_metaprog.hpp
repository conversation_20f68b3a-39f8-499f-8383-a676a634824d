//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_METAPROG_HPP_INCLUDED
#define ZX_VFC_METAPROG_HPP_INCLUDED

// vfc includes
#include "vfc/core/vfc_types.hpp"         // used for int32_t
#include "vfc/core/vfc_static_assert.hpp" // used for VFC_STATIC_ASSERT()
#include "vfc/core/vfc_limits.hpp"
#include "vfc/core/vfc_builtin_types.hpp"
#include <type_traits> // used for add_const, is_enum

namespace vfc
{ // namespace vfc opened

/// @brief Template metaprogramming with VFC.
/// @defgroup vfc_group_core_generic_metaprogram Template Metaprograms
/// @ingroup vfc_group_core_generic
///
/// Metaprogramming consists of "programming a program." In other words, we lay out code that the programming system
/// executes to generate new code that implements the functionality we really want. Usually the term metaprogramming
/// implies a reflexive attribute: The metaprogramming component is part of the program for which it generates a bit
/// of code/program.
///
/// Why would metaprogramming be desirable? As with most other programming techniques, the goal is to achieve more
/// functionality with less effort, where effort can be measured as code size, maintenance cost, and so forth. What
/// characterizes metaprogramming is that some user-defined computation happens at translation time. The underlying
/// motivation is often performance (things computed at translation time can frequently be optimized away) or
/// interface simplicity (a metapro-gram is generally shorter than what it expands to) or both.
///
/// Metaprogramming often relies on the concepts of traits and type functions, see also @ref
/// vfc_group_core_generic_typetraits. We therefore recommend getting familiar with this technique prior to delving
/// into this one.
///
/// In 1994 during a meeting of the C++ standardization committee, Erwin Unruh discovered that templates can be used
/// to compute something at compile time. He wrote a program that produced prime numbers. The intriguing part of
/// this exercise, however, was that the production of the prime numbers was performed by the compiler during the
/// compilation process and not at run time. Specifically, the compiler produced a sequence of error messages with
/// all prime numbers from two up to a certain configurable value. Although this program wasn't strictly portable
/// (error messages aren't standardized), the program did show that the template instantiation mechanism is a
/// primitive recursive language that can perform nontrivial computations at compile time. This sort of compile-time
/// computation that occurs through template instantiation is commonly called template metaprogramming.
//  Doxygen defgroup vfc_group_core_generic_metaprogram:
/// @{

//=============================================================================
//  TIf<>
/// Compile time if-then-else statement.
/// @tparam PredicateValue predicate, evaluates to true or false
/// @tparam ThenType type selected if PredicateValue evaluates to true
/// @tparam ElseType type selected if PredicateValue evaluates to false
template <bool PredicateValue, class ThenType, class ElseType>
struct TIf
{
    using type = ElseType;
};

/// Specialization for `true`.
template <class ThenType, class ElseType>
struct TIf<true, ThenType, ElseType>
{
    using type = ThenType;
};

//=============================================================================
//  TOr<>
/// Compile time 'or' statement. Is `true_t` when one of the arguments is `true_t`.
/// In future to be improved with variadic template.
/// @tparam LhsType Left hand side of the OR statement.
/// @tparam RhsType Right hand side of the OR statement.
template <typename LhsType, typename RhsType>
struct TOr : TIf<LhsType::value, LhsType, RhsType>::type
{
};

//=============================================================================
//  TAnd<>
/// Compile time 'and' statement. Is `true_t` when both arguments is `true_t`.
/// In future to be improved with variadic template.
/// @tparam LhsType Left hand side of the OR statement.
/// @tparam RhsType Right hand side of the OR statement.
template <typename LhsType, typename RhsType>
struct TAnd : TIf<LhsType::value, RhsType, LhsType>::type
{
};

//=============================================================================
//  TEvalIf<>
/// Compile time if-then-else statement with extra indirection. @sa TIf.
/// @tparam PredicateValue Predicate, evaluates to true or false.
/// @tparam ThenType ThenType::type is selected if PredicateValue evaluates to true
/// @tparam ElseType ElseType::type is selected if PredicateValue evaluates to false
template <bool PredicateValue, class ThenType, class ElseType>
struct TEvalIf
{
    using type = typename ElseType::type;
};

/// Specialization for `true`.
template <class ThenType, class ElseType>
struct TEvalIf<true, ThenType, ElseType>
{
    using type = typename ThenType::type;
};

//=============================================================================
//  TContainsType<>
/// Compile time check of parameter list. TContainsType<>::value = true will be returned in case of type SearchType
/// is contained in the passed paramter list.
/// @tparam SearchType Type to find in the pack paramter list.
/// @tparam ParameterTypes List of parameters to check for type.
template <typename SearchType, typename... ParameterTypes>
struct TContainsType;

/// Specialization for one type (end of recursion).
template <typename SearchType>
struct TContainsType<SearchType> : false_t
{
};

/// Specialization when first two arguments are same.
template <typename SearchType, typename... ParameterTypes>
struct TContainsType<SearchType, SearchType, ParameterTypes...> : true_t
{
};

/// Specialization taking out one argument.
template <typename SearchType, typename FieldType, typename... ParameterTypes>
struct TContainsType<SearchType, FieldType, ParameterTypes...> : TContainsType<SearchType, ParameterTypes...>::type
{
};

//=============================================================================
//  TEnableIf<>
/// Compile time type definition, depending on the predicate value. Used to add or remove function overloads, e.g.
/// depending on type traits evaluating to true or false.
/// @tparam PredicateValue Predicate, evaluates to `true` or `false`.
/// @tparam ValueType Used for a nested type, but only if the predicate is true.
template <bool PredicateValue, class ValueType>
struct TEnableIf
{
};

/// Specialization for true.
template <class ValueType>
struct TEnableIf<true, ValueType>
{
    using type = ValueType;
};

//=============================================================================
// TCheckDependentTypesAreValid_t<>
/// Metaprogramming type that enables specializations
/// to be disabled through SFINAE.
/// If it can be instantiated in the using context the resulting type
/// will be void otherwise it will cause the corresponding
/// specialization to be disregarded for the specific lookup.
/// @tparam TypesToCheck dependent types that are to be checked
template <typename... TypesToCheck>
using TCheckDependentTypesAreValid_t = void;

//=============================================================================
//  TAddConst<>
/// Template Metaprogram for adding const qualifier to the specified type.
/// @tparam TType Type whose qualifiers should be extended.
template <class TType>
struct TAddConst : std::add_const<TType>
{
};

//=============================================================================
//  TRemoveConst<>
/// Template Metaprogram for removing const qualifier from the specified type.
/// @tparam TType Type whose qualifiers should be removed.
template <class TType>
struct TRemoveConst : std::remove_const<TType>
{
};

//=============================================================================
//  TIsConst<>
/// Template Metaprogram to check, if the given type is const.
/// @tparam TType The given type.
template <class TType>
struct TIsConst : std::is_const<TType>
{
};

//=============================================================================
//  TRemoveCV<>
/// Template Metaprogram for removing const and volatile qualifiers of specified type.
/// @tparam TType Type whose qualifiers should be removed.
template <class TType>
struct TRemoveCV : std::remove_cv<TType>
{
};

//=============================================================================
//  TIsLvalueReference<>
/// Template Metaprogram for checking reference type. Checks whether the passed template parameter is of type
/// reference.
/// @tparam TType Type to check.
template <class TType>
struct TIsLvalueReference : false_t
{
};

template <class TType>
struct TIsLvalueReference<TType&> : true_t
{
};

//=============================================================================
//  TRemoveReference<>
/// Template Metaprogram for remove reference of the type. Returns the type of the reference.
/// @tparam TType Reference type.
template <class TType>
struct TRemoveReference
{
    using type = TType;
};

template <class TType>
struct TRemoveReference<TType&>
{
    using type = TType; // PRQA S 2427 # Technical debt L2 id=00c1938cae7791cb4161-973ed3f1ad43af9a
};

template <class TType>
struct TRemoveReference<TType&&>
{
    using type = TType;
};

//=============================================================================
//  TRemovePointer<>
/// Template Metaprogram for remove pointer of the type. Returns the type of the pointer.
/// @tparam TType Pointer type.
template <class TType>
struct TRemovePointer
{
    using type = TType;
};

template <class TType>
struct TRemovePointer<TType*>
{
    using type = TType;
};

template <class TType>
struct TRemovePointer<TType* const>
{
    using type = TType;
};

//=============================================================================
//  move<T>
/// Template Metaprogram for prefect moving. Is used for moving parameters.
/// @tparam TType Type for forwarding.
template <class TType>
auto move(TType&& t) VFC_NOEXCEPT -> typename vfc::TRemoveReference<TType>::type&&
{
    return static_cast<typename vfc::TRemoveReference<TType>::type&&>(
        t); // PRQA S 4028 # Technical debt L2 id=00c1a81a1c3cae2b4016-725e55fb835d4191
}

//=============================================================================
//  TForward<>
/// Template Metaprogram for prefect forwarding. Is used for forwardings lvalue and rvalue.
/// @tparam TType Type for forwarding.
template <class TType>
auto forward(typename TRemoveReference<TType>::type& t) VFC_NOEXCEPT -> TType&&
{
    return static_cast<TType&&>(t); // PRQA S 2427 # Technical debt L2 id=00c1f4967fc6f41a4b89-4b364b9931e08f96 // PRQA
                                    // S 4028 # Technical debt L2 id=00c13d556b2c2755412c-1f3655767c516e41 // PRQA S
                                    // 4624 # Technical debt L2 id=00c141e22b31379b41e6-4740283374a14770
}

template <class TType>
auto forward(typename TRemoveReference<TType>::type&& t) VFC_NOEXCEPT -> TType&&
{
    /// protection against misusage -> forward<lvalueType>(Type&&)
    static_assert(!TIsLvalueReference<TType>::value, "TType should not be an lvalue reference.");
    return static_cast<TType&&>(t); // PRQA S 4028 # Technical debt L2 id=00c120eb0c1f9fa04128-1f3655767c516e41
}

//=============================================================================
//  TIsSameTypeCV<>
/// Template Metaprogram for comparing two types for equality. If both types are equal TIsSameTypeCV<>::value
/// evaluates to true. @note Result depends on qualifiers as well, e.g. TIsSameTypeCV<const int32_t, int32_t>::value
/// evaluates to `false`.
/// @tparam FirstType First type for comparison.
/// @tparam SecondType Second type for comparison.
template <class FirstType, class SecondType>
struct TIsSameTypeCV : false_t
{
};

/// Specialization for FirstType == SecondType.
template <class SameType>
struct TIsSameTypeCV<SameType, SameType> : true_t
{
};

//=============================================================================
//  TIsSameType<>
/// Template Metaprogram for comparing two types for equality. If both types are equal TIsSameType<>::value
/// evaluates to true. @note result @b doesn't depend on qualifiers, e.g. TIsSameType<const int32_t, int32_t>::value
/// evaluates to @b true.
/// @tparam FirstType First type for comparison.
/// @tparam SecondType Second type for comparison.
template <class FirstType, class SecondType>
struct TIsSameType : TIsSameTypeCV<typename TRemoveCV<FirstType>::type, typename TRemoveCV<SecondType>::type>::type
{
};

//=============================================================================
//  TIsEnumType<>
/// Template Metaprogram to check whether the passed template parameter is of type enum. TIsEnumType follows the
/// same behavior as std::is_enum.
/// @tparam Type Type to check for enum.
template <typename Type>
struct TIsEnumType : bool_constant_t<std::is_enum<Type>::value>::type
{
};

//=============================================================================
//  TIsFunctionOfType<>
/// Template Metaprogram to check if a given function type can be matched to an overload of a given function symbol.
/// Since we want to handle overloaded functions, we need to resolve the function in a context that expects a
/// specific type and this is realized through a static constexpr check() function overload.
///
/// @note Currently, this is not very usefull for negative tests, i.e. matching a function against a wrong
/// signature, and expecting a false result as this generates a compiler error when overloads need to be resolved or
/// template types need to be inferred.
/// @tparam FuncType expected function type.
template <typename FuncType>
struct TIsFunctionOfType
{
    /// overload for type to be checked
    static constexpr true_t check(FuncType) { return true_t(); }
    /// fallthrough overload in case the type does not match
    static constexpr false_t check(...)
    {
        return false_t();
    } // PRQA S 2012 # Technical debt L2 id=00c19591d81445df4db2-e28e8eee4b101b30
};

//=============================================================================
//  TIsBaseOf<>
/// Template Metaprogram to check whether the one class derives of dedicated base class. TIsBaseOf follows the same
/// behavior as std::is_base_of.
/// @tparam  BaseType Base class to check against.
/// @tparam  DerivedType Class to check whether it is dervied from BaseType.
template <class BaseType, class DerivedType>
struct TIsBaseOf : bool_constant_t<std::is_base_of<BaseType, DerivedType>::value>::type
{
};

//=============================================================================
//  TIsDerivedFrom<>
namespace internal
{
template <class DerivedType, class BaseType>
struct TIsDerivedFrom
{
    // Make sure that the two sizes are not accidentally identical because of alignment issues.
    class CYes
    {
        vfc::uint8_t m_a[1U];
    };
    class CNo
    {
        vfc::uint8_t m_a[10U];
    };

    static CYes test(BaseType*);
    // Deviation of coding rule 9.6,
    // We do not define the memberfunction and no code is actually called.
    static CNo test(...); // PRQA S 2012 # Technical debt L2 id=00c1d9f02e96c20d45d6-823c670733c59c14

    // If DerivedType* can be converted to BaseType*,
    // the first specialization of Test() is chosen
    using type = typename bool_constant_t<(sizeof(test(static_cast<DerivedType*>(0))) == sizeof(CYes))>::type;
};
} // namespace internal

/// Template Metaprogram for testing if one type is derived from another. If DerivedType is derived from BaseType,
/// TIsDerivedFrom<>::value evaluates to true.
/// @tparam DerivedType The type which is checked to be derived from base.
/// @tparam BaseType The base type to be checked.
template <class DerivedType, class BaseType>
struct TIsDerivedFrom
    : internal::TIsDerivedFrom<typename vfc::TRemoveCV<DerivedType>::type, typename vfc::TRemoveCV<BaseType>::type>::
          type
{
};

//=============================================================================
//  TType2Type<>
/// Simple Template Metaprogram for storing specified type in type definition. TType2Type<> is used for building larger
/// metaprograms.
/// @tparam TType Type to be aliased.
/// @sa TEvalIf
template <class TType>
struct TType2Type
{
    using type = TType;
};

//=============================================================================
//  TInt2Boolean<>
/// Simple Template Metaprogram for converting an integral value to a boolean type.
/// If `Value` equals zero, the nested type `type` evaluates to `false_t`. All other values evaluate to `true_t`.
/// @tparam Value Integral value for evaluation.
template <int32_t Value>
struct TInt2Boolean : true_t
{
};

/// Specialization for zero values.
template <>
struct TInt2Boolean<0> : false_t
{
};

//=============================================================================
//  TInt2Type<>
/// Simple Template Metaprogram for converting an integral value to a unique type. Same functionality as
/// `TIntegralConstant`.
/// @tparam Value  Integral value to be converted to unique type.
template <int32_t Value>
struct TInt2Type : TIntegralConstant<int32_t, Value>
{
};

//=============================================================================
//  TSigned2Unsigned<>
/// Simple Template Metaprogram for converting a signed type to its unsigned counterpart type.
/// @tparam ValueType  Signed integral type to be converted to unsigned.
template <class ValueType>
struct TSigned2Unsigned
{
    using type = ValueType;
};

/// Specializations for fundamental signed integral types.
template <>
struct TSigned2Unsigned<built_in::schar_t>
{
    using type = built_in::uchar_t;
};

template <>
struct TSigned2Unsigned<built_in::short_t>
{
    using type = built_in::ushort_t;
};

template <>
struct TSigned2Unsigned<built_in::int_t>
{
    using type = built_in::uint_t;
};

template <>
struct TSigned2Unsigned<built_in::long_t>
{
    using type = built_in::ulong_t;
};

template <>
struct TSigned2Unsigned<built_in::longlong_t>
{
    using type = built_in::ulonglong_t;
};

//=============================================================================
//  TAbs<>
/// Template Metaprogram which calculates at compile time the absolute value corresponding to the specified integer
/// parameter value.
/// @tparam Value  Integral type of which the absolute value is computed at compile time.
template <int32_t Value>
struct TAbs : TIntegralConstant<int32_t, ((Value < 0) ? (-Value) : Value)>
{
    static_assert(
        Value > std::numeric_limits<int32_t>::min(),
        "vfc::TAbs<std::numeric_limits<int32_t>::min()> is not possible.");
};

//=============================================================================
//  TMin<>
/// Template Metaprogram which calculates at compile time the minimum of two specified integer values.
/// @tparam Op1Value  The first operand of the min function.
/// @tparam Op2Value  The second operand of the min function.
template <int32_t Op1Value, int32_t Op2Value>
struct TMin : TIntegralConstant<int32_t, ((Op1Value < Op2Value) ? Op1Value : Op2Value)>
{
};

//=============================================================================
//  TMax<>
/// Template Metaprogram which calculates at compile time the maximum of two specified integer values.
/// @tparam Op1Value  The first operand of the max function.
/// @tparam Op2Value  The second operand of the max function.
template <int32_t Op1Value, int32_t Op2Value>
struct TMax : TIntegralConstant<int32_t, ((Op1Value > Op2Value) ? Op1Value : Op2Value)>
{
};

//=============================================================================
//  TNextPow2<>
namespace internal
{
template <uint32_t Value>
struct TNextPow2Impl
{
    static constexpr uint32_t temp0{Value - 1U};
    static constexpr uint32_t temp1{temp0 | (temp0 >> 1U)};
    static constexpr uint32_t temp2{temp1 | (temp1 >> 2U)};
    static constexpr uint32_t temp3{temp2 | (temp2 >> 4U)};
    static constexpr uint32_t temp4{temp3 | (temp3 >> 8U)};
    static constexpr uint32_t temp5{temp4 | (temp4 >> 16U)};
    static constexpr uint32_t temp6{temp5 + 1U};
    static constexpr uint32_t value{temp6 + ((temp6 == 0U) ? 1U : 0U)};
};

template <>
struct TNextPow2Impl<0>
{
    static constexpr uint32_t value = 1;
};
} // namespace internal

/// Template Metaprogram which calculates at compile time the next power-of-two value which is greater or equal to
/// specified integer value.
/// @tparam Value  Integral value of which the next power of 2 is computed at compile time.
template <int32_t Value>
struct TNextPow2 : TIntegralConstant<int32_t, internal::TNextPow2Impl<Value>::value>
{
    static_assert(Value >= 0 && Value <= 1073741824, "Value has to be not negative and smaller than 2^30+1.");
};

//=============================================================================
//  TIsPow2<>
/// Template Metaprogram for testing specified value to be a power-of-two.
/// @tparam Value  the value which is used in the power-of-two check
template <int32_t Value>
struct TIsPow2
    : bool_constant_t<(0 == (Value & (Value - 1))) && (0 != Value)>::type // PRQA S 3006 # Technical debt L2
                                                                          // id=00c1009f1ba440c848e5-fabccd8f57efbd96
{
    static_assert(Value >= 0, "Value has to be not-negative.");
};

//=============================================================================
//  TAlignUp<>
/// Template Metaprogram for up-aligning given integer value to a multiple of specified power-of-two value.
/// @tparam SizeValue  The integral value which is aligned up.
/// @tparam AlignValue  The value used alignment.
template <size_t SizeValue, size_t AlignValue>
struct TAlignUp : TIntegralConstant<int32_t, ((SizeValue + (AlignValue - 1U)) & ~(AlignValue - 1U))>
{
    static_assert(SizeValue >= 0, "Only non-negative sizes allowed");
    static_assert(TIsPow2<AlignValue>::value, "Alignment requirement must be a power of two.");
    static_assert(AlignValue > 0, "Alignment requirement must be greater than zero.");
};

//=============================================================================
//  TAlignDown<>
/// Template Metaprogram for down-aligning given integer value to a multiple of specified power-of-two value.
/// @tparam SizeValue  The integral value which is aligned down.
/// @tparam AlignValue  The value used alignment.
template <size_t SizeValue, size_t AlignValue>
struct TAlignDown : TIntegralConstant<int32_t, (SizeValue & ~(AlignValue - 1))>
{
    static_assert(SizeValue >= 0, "SizeValue has to be non-negative.");
    static_assert(TIsPow2<AlignValue>::value, "Alignment has to be a power of two.");
};

//=============================================================================
//  TLog2Floor<>
/// Template Metaprogram for calculating the floor of log2(X) (or ld). Calculates the floor of the logarithm dualis
/// (log2 or ld) of specified template value at compile time.
/// @tparam ArgValue Value whose logarithm is to be found.
/// @relatesalso TLog2Near
template <int32_t ArgValue>
struct TLog2Floor : TIntegralConstant<int32_t, (TLog2Floor<(ArgValue >> 1)>::value + 1)>
{
    static_assert(ArgValue >= 0, "ArgValue has to be non-negative.");
};

/// Specialization for ArgValue == 1 (recursion termination).
template <>
struct TLog2Floor<1> : TIntegralConstant<int32_t, 0>
{
};

/// Specialization for undefined case ArgValue == 0 (generates compiler error).
template <>
struct TLog2Floor<0>
{
};

//=============================================================================
//  vfc::TLog2Near<>
/// Template Metaprogram for calculating the nearest integer of log2(X).
/// Calculates the nearest integer of the logarithm dualis (log2 or ld)
/// of specified template value at compile time.
/// @tparam ArgValue Value whose logarithm is to be found.
/// @sa TLog2Floor
///
/// Algorithm:
///
///     // pseudocode:
///     y = log2(x)
///     // going to y.1 fixpoint by transforming eq. (y.1 = 2*y)
///     2y  = 2 log2(x)
///     y.1 = 2 log2(x)
///     y.1 = log2(x^2)
///     // go back and round
///     nint(y) = (y.1 + 1 ) / 2
///
template <int32_t ArgValue>
struct TLog2Near : TIntegralConstant<int32_t, ((TLog2Floor<(ArgValue * ArgValue)>::value + 1) >> 1)>
{
    static_assert(ArgValue >= 0, "ArgValue has to be non-negative.");
};

//=============================================================================
// vfc::TPower<>
namespace intern
{
/// constants used to check if casting is valid
struct CCastConstants
{
    static constexpr uint64_t getMaskI32() { return 0xFFFFFFFF80000000ULL; }
    static constexpr uint64_t getMaskUI32() { return 0xFFFFFFFF00000000ULL; }
};

/// @class TPower64
/// Compile-time power computation with 64bit integers
/// @tparam BaseValue int64_t base value of power expression
/// @tparam ExponentValue int64_t exponent value of power expression
/// @result the computed value is available in the member value_ui64
template <vfc::int64_t BaseValue, vfc::int64_t ExponentValue>
struct TPower64
{
    static_assert(BaseValue > 0, "only positive base values allowed for TPower");
    static_assert(ExponentValue > 0, "only non-negative bases values allowed for TPower");

    static_assert(
        (BaseValue * TPower64<BaseValue, ExponentValue - 1>::value_ui64 >=
         TPower64<BaseValue, ExponentValue - 1>::value_ui64),
        "overflow in TPower");
    static const uint64_t value_ui64 =
        static_cast<uint64_t>(BaseValue) * (TPower64<BaseValue, ExponentValue - 1>::value_ui64);
};

template <vfc::int64_t BaseValue>
struct TPower64<BaseValue, 0>
{
    static_assert(BaseValue > 0, "only positive base values allowed for TPower");

    static const uint64_t value_ui64 = 1U;
};

/// @class TPower32
/// Compile-time power computation with 64bit integers.
/// It is used a the base-class for vfc::TPower if the result of the computation fits in an int32.
/// @tparam BaseValue int64_t base value of power expression
/// @tparam ExponentValue int64_t exponent value of power expression
/// @result the computed value is available in the member value_i32
template <vfc::int64_t BaseValue, vfc::int64_t ExponentValue>
struct TPower32 : public TPower64<BaseValue, ExponentValue>
{
    static const int32_t value_i32 = static_cast<int32_t>(TPower64<BaseValue, ExponentValue>::value_ui64);
    VFC_ATR_DEPRECATED(static const int32_t VALUE) = value_i32;
};

} // namespace intern

//=============================================================================
//  TPower<>
/// Template Metaprogram for calculating the power(BaseValue,ExponentValue).
/// TPower returns "BaseValue" to the power of "ExponentValue", BaseValue and ExponentValue should be greater than
/// Zero. Asserts if the value is less than zero. Returns the template value at compile time. The result is stored
/// in TPower<>::value_ui64, if it fits in int32_t there will be a member value_i32 which holds the result.
/// @tparam BaseValue Basevalue of the power function.
/// @tparam ExponentValue ExponentValue of the power function.
/// @pre BaseValue is positive and ExponentValue is non-negative.
/// @post Result equals (BaseValue)^ExponentValue.
template <vfc::int64_t BaseValue, vfc::int64_t ExponentValue>
struct TPower
    : public TIf<
          (0 != (intern::TPower64<BaseValue, ExponentValue>::value_ui64 & intern::CCastConstants::getMaskI32())),
          intern::TPower64<BaseValue, ExponentValue>, // need 64 bits to represent result
          intern::TPower32<BaseValue, ExponentValue>  // result fits in int32_t
          >::type
{
    // all members are in the base class(es)
};

//  Doxygen defgroup vfc_group_core_generic_metaprogram:
/// @}
} // namespace vfc
#endif // ZX_VFC_METAPROG_HPP_INCLUDED

