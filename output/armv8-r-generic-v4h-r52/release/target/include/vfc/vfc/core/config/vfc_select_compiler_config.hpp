//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_SELECT_COMPILER_CONFIG_HPP_INCLUDED
#define ZX_VFC_SELECT_COMPILER_CONFIG_HPP_INCLUDED

#ifndef VFC_CONFIG_HPP_INCLUDED
static_assert(false, "vfc_select_compiler_config.hpp must not be included directly, include vfc_config.hpp instead");
#endif

// documentation of macros to be defined by the compiler specific includes

/// @def VFC_LINKER_SECT(sect_name)
/// @brief one-parameter macro for placing the object in a specific section

/// @def VFC_DECL_FORCEINLINE
/// @brief compiler specific attribute to enforce inlining

/// @def VFC_TYPE_ALIGNMENT_PREFIX(val)
/// @def VFC_TYPE_ALIGNMENT_SUFFIX(val)
/// @brief align instances of the type to multiples of val

namespace vfc
{
// forward declaration of TSemanticVersion so it can be for
// compiler versions without including vfc_version.hpp
using VersionInt = unsigned char;
template <VersionInt MajorValue, VersionInt MinorValue, VersionInt PatchValue>
struct TSemanticVersion;

namespace intern
{
// Each compiler shall define the following three types:
// CompilerVersionMin : Minimum supported compiler version
// CompilerVersionMax : Maximum supported compiler version
// CompilerVersion    : Currently detected compiler version

/// Accessor for version components of TSemanticVersion template.
/// Also works for incomplete types, hence the include is not needed.
template <typename TVersion>
struct GetVersionInfo;

/// partial specialization for TSemanticVersion
template <VersionInt majorParam, VersionInt minorParam, VersionInt patchParam>
struct GetVersionInfo<TSemanticVersion<majorParam, minorParam, patchParam>>
{
    static constexpr VersionInt major = majorParam;
    static constexpr VersionInt minor = minorParam;
};

/// Comparision function to check major and minor version only.
template <typename Vers1, typename Vers2>
constexpr bool majorMinorLessEqual()
{
    using Info1 = GetVersionInfo<Vers1>;
    using Info2 = GetVersionInfo<Vers2>;
    return (Info1::major < Info2::major) || ((Info1::major == Info2::major) && (Info1::minor <= Info2::minor));
}
} // namespace intern
} // namespace vfc

//  GNU C++:
#if defined(__GNUC__) && !defined(__ARMCC_VERSION) && !defined(__clang__) && !defined(__EDG__)
#define VFC_COMPILER_CONFIG "vfc/core/config/compiler/vfc_gcc.hpp"
#elif defined(__clang__) && !defined(__ARMCC_VERSION) && !defined(__llvm__)
#define VFC_COMPILER_CONFIG "vfc/core/config/compiler/vfc_clang.hpp"
//  Metrowerks CodeWarrior
#elif defined ANDROID
#define VFC_COMPILER_CONFIG "vfc/core/config/compiler/vfc_clang.hpp"
#elif defined __MWERKS__
#define VFC_COMPILER_CONFIG "vfc/core/config/compiler/vfc_mwerks.hpp"
//  WindRiver Diab Data Compiler
#elif defined __DCC__
#define VFC_COMPILER_CONFIG "vfc/core/config/compiler/vfc_diab.hpp"
// Ti Code Composer Studio
#elif defined __TI_COMPILER_VERSION__|| defined(__llvm__)
#define VFC_COMPILER_CONFIG "vfc/core/config/compiler/vfc_ticcs.hpp"
//   ARM RVDS compiler
#elif defined __ARMCC_VERSION
#define VFC_COMPILER_CONFIG "vfc/core/config/compiler/vfc_armrvct.hpp"
//  Microsoft Visual C++
//  Must remain the last #elif since some other vendors (Metrowerks, for
//  example) also #define _MSC_VER
#elif defined __EDG__ && !defined(_MSC_VER)
#define VFC_COMPILER_CONFIG "vfc/core/config/compiler/vfc_ghc.hpp"
#elif defined _MSC_VER
#define VFC_COMPILER_CONFIG "vfc/core/config/compiler/vfc_visualc.hpp"
#else
static_assert(false, "Unknown compiler!");
#endif

#endif // ZX_VFC_SELECT_COMPILER_CONFIG_HPP_INCLUDED

