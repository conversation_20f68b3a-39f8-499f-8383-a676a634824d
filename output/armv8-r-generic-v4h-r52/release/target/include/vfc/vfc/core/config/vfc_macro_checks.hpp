//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

// Do not change: autogenerated file
//
// macro like names check header
// generated by calling:
// # python3 scripts/python/find_macro_like_names.py -g vfc/include/vfc/core/config/vfc_macro_checks.hpp vfc/include
// daddy/inc daddy/config daddy/open_variation_point
//
// Every used macro is checked and generates a static_assert(false).

#ifndef VFC_MACRO_CHECKS_INCLUDED
#define VFC_MACRO_CHECKS_INCLUDED

#ifdef A
static_assert(false, "A must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef ALIGN
static_assert(false, "ALIGN must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef AMOUNTOFSUBSTANCE_POWER_VALUE
static_assert(
    false,
    "AMOUNTOFSUBSTANCE_POWER_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef AMOUNTOFSUBSTANCE_PREFIX_VALUE
static_assert(
    false,
    "AMOUNTOFSUBSTANCE_PREFIX_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef AMOUNT_OF_BITS
static_assert(false, "AMOUNT_OF_BITS must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef AMOUNT_OF_BYTES
static_assert(false, "AMOUNT_OF_BYTES must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef AMOUNT_OF_VALUES
static_assert(false, "AMOUNT_OF_VALUES must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef ANGLE_POWER_VALUE
static_assert(false, "ANGLE_POWER_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef ANGLE_PREFIX_VALUE
static_assert(false, "ANGLE_PREFIX_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef ATTO
static_assert(false, "ATTO must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef B
static_assert(false, "B must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef BASE
static_assert(false, "BASE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef BITMASK
static_assert(false, "BITMASK must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef BIT_POSITION
static_assert(false, "BIT_POSITION must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef BUFFER_SIZE
static_assert(false, "BUFFER_SIZE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef C
static_assert(false, "C must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef CENTI
static_assert(false, "CENTI must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef CLZ
static_assert(false, "CLZ must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef CMN
static_assert(false, "CMN must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef COMMON_ALIGN
static_assert(false, "COMMON_ALIGN must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef CONVENIENCE_TYPE
static_assert(false, "CONVENIENCE_TYPE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef CONVERSION_RATIONAL
static_assert(false, "CONVERSION_RATIONAL must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef CONVERT_FACTOR_DEN
static_assert(false, "CONVERT_FACTOR_DEN must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef CONVERT_FACTOR_NUM
static_assert(false, "CONVERT_FACTOR_NUM must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef COUNT
static_assert(false, "COUNT must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef CSI
static_assert(false, "CSI must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef CURRENT_POWER_VALUE
static_assert(false, "CURRENT_POWER_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef CURRENT_PREFIX_VALUE
static_assert(false, "CURRENT_PREFIX_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef D
static_assert(false, "D must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef D2
static_assert(false, "D2 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DATASIZE
static_assert(false, "DATASIZE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DAY
static_assert(false, "DAY must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DECA
static_assert(false, "DECA must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DECI
static_assert(false, "DECI must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DEN
static_assert(false, "DEN must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DENOMINATOR
static_assert(false, "DENOMINATOR must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DENOMINATOR_INTERNAL
static_assert(false, "DENOMINATOR_INTERNAL must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DEN_10
static_assert(false, "DEN_10 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DEN_2
static_assert(false, "DEN_2 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DEN_3
static_assert(false, "DEN_3 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DEN_4
static_assert(false, "DEN_4 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DEN_5
static_assert(false, "DEN_5 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DEN_6
static_assert(false, "DEN_6 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DEN_7
static_assert(false, "DEN_7 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DEN_8
static_assert(false, "DEN_8 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DEN_9
static_assert(false, "DEN_9 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DEN_IS_DIVISIBLE
static_assert(false, "DEN_IS_DIVISIBLE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DEN_SHR_VALUE
static_assert(false, "DEN_SHR_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DO_CONV
static_assert(false, "DO_CONV must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DO_DIVIDE
static_assert(false, "DO_DIVIDE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DO_MULTIPLY
static_assert(false, "DO_MULTIPLY must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DO_OFFSET_ADDITION
static_assert(false, "DO_OFFSET_ADDITION must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DO_OFFSET_SUBTRACTION
static_assert(false, "DO_OFFSET_SUBTRACTION must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef E
static_assert(false, "E must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef ECF_ROUND
static_assert(false, "ECF_ROUND must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef ELEMENT_SIZE
static_assert(false, "ELEMENT_SIZE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef EXA
static_assert(false, "EXA must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef EXP
static_assert(false, "EXP must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef F
static_assert(false, "F must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef FEMTO
static_assert(false, "FEMTO must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef FIFO_SIZE
static_assert(false, "FIFO_SIZE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef FIRST_BYTE_TO_READ
static_assert(false, "FIRST_BYTE_TO_READ must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef FLOATING_OFFSET
static_assert(false, "FLOATING_OFFSET must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef ZXGIGA
static_assert(false, "ZXGIGA must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef G_1_3
static_assert(false, "G_1_3 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef G_1_PI
static_assert(false, "G_1_PI must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef G_1_SQRT2
static_assert(false, "G_1_SQRT2 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef G_2PI
static_assert(false, "G_2PI must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef G_2_PI
static_assert(false, "G_2_PI must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef G_DEG2RAD
static_assert(false, "G_DEG2RAD must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef G_E
static_assert(false, "G_E must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef G_EPSILON_F32
static_assert(false, "G_EPSILON_F32 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef G_EPSILON_F64
static_assert(false, "G_EPSILON_F64 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef G_LN10
static_assert(false, "G_LN10 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef G_LN2
static_assert(false, "G_LN2 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef G_LOG10E
static_assert(false, "G_LOG10E must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef G_LOG2E
static_assert(false, "G_LOG2E must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef G_PI
static_assert(false, "G_PI must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef G_PI_2
static_assert(false, "G_PI_2 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef G_PI_4
static_assert(false, "G_PI_4 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef G_RAD2DEG
static_assert(false, "G_RAD2DEG must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef G_SQRT2
static_assert(false, "G_SQRT2 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef G_SQRT3
static_assert(false, "G_SQRT3 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef HECTO
static_assert(false, "HECTO must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef HOUR
static_assert(false, "HOUR must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef ID
static_assert(false, "ID must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef INTEGER_DATA_TYPE
static_assert(false, "INTEGER_DATA_TYPE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef INUNITPREFIXVALUE_INTERNAL
static_assert(false, "INUNITPREFIXVALUE_INTERNAL must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef IS_DIVISIBLE
static_assert(false, "IS_DIVISIBLE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef KILO
static_assert(false, "KILO must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef LENGTH_POWER_VALUE
static_assert(false, "LENGTH_POWER_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef LENGTH_PREFIX_VALUE
static_assert(false, "LENGTH_PREFIX_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef LENGTH_VALUE
static_assert(false, "LENGTH_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef LSB_BIT_POS_IN_BYTE
static_assert(false, "LSB_BIT_POS_IN_BYTE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef LUMINOUSINTENSITY_POWER_VALUE
static_assert(
    false,
    "LUMINOUSINTENSITY_POWER_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef LUMINOUSINTENSITY_PREFIX_VALUE
static_assert(
    false,
    "LUMINOUSINTENSITY_PREFIX_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef L_FOUR
static_assert(false, "L_FOUR must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef L_ONE
static_assert(false, "L_ONE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef L_TWO
static_assert(false, "L_TWO must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MASS_POWER_VALUE
static_assert(false, "MASS_POWER_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MASS_PREFIX_VALUE
static_assert(false, "MASS_PREFIX_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MAX_ALIGNED_READ_BYTES
static_assert(false, "MAX_ALIGNED_READ_BYTES must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MAX_CHUNK_COUNT
static_assert(false, "MAX_CHUNK_COUNT must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MAX_CHUNK_SIZE
static_assert(false, "MAX_CHUNK_SIZE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MAX_METAPROG_UNROLL_SIZE
static_assert(false, "MAX_METAPROG_UNROLL_SIZE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MAX_ROWS
static_assert(false, "MAX_ROWS must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MAX_SIZE
static_assert(false, "MAX_SIZE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MAX_SIZE_VAL
static_assert(false, "MAX_SIZE_VAL must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MAX_STORAGE_SIZE
static_assert(false, "MAX_STORAGE_SIZE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MAX_UINT16_VAL
static_assert(false, "MAX_UINT16_VAL must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MAX_UINT8_VAL
static_assert(false, "MAX_UINT8_VAL must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MAX_VALUE
static_assert(false, "MAX_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MEGA
static_assert(false, "MEGA must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MEMORY_ORDER_ACQUIRE
static_assert(false, "MEMORY_ORDER_ACQUIRE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MEMORY_ORDER_ACQ_REL
static_assert(false, "MEMORY_ORDER_ACQ_REL must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MEMORY_ORDER_CONSUME
static_assert(false, "MEMORY_ORDER_CONSUME must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MEMORY_ORDER_RELAXED
static_assert(false, "MEMORY_ORDER_RELAXED must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MEMORY_ORDER_RELEASE
static_assert(false, "MEMORY_ORDER_RELEASE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MEMORY_ORDER_SEQ_CST
static_assert(false, "MEMORY_ORDER_SEQ_CST must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MICRO
static_assert(false, "MICRO must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MICRO_SECOND
static_assert(false, "MICRO_SECOND must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MILLI
static_assert(false, "MILLI must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MILLI_SECOND
static_assert(false, "MILLI_SECOND must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MINUTE
static_assert(false, "MINUTE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MONTH
static_assert(false, "MONTH must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MONTH_IN_DAYS
static_assert(false, "MONTH_IN_DAYS must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MOVMI
static_assert(false, "MOVMI must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MSG
static_assert(false, "MSG must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef N
static_assert(false, "N must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NAME
static_assert(false, "NAME must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NANO
static_assert(false, "NANO must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NANO_SECOND
static_assert(false, "NANO_SECOND must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NB_COLUMNS
static_assert(false, "NB_COLUMNS must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NB_ROWS
static_assert(false, "NB_ROWS must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NEAREST_TO_EVEN
static_assert(false, "NEAREST_TO_EVEN must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NIL
static_assert(false, "NIL must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NUM
static_assert(false, "NUM must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NUM1
static_assert(false, "NUM1 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NUM2
static_assert(false, "NUM2 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NUMBER_OF_PARAMETERS
static_assert(false, "NUMBER_OF_PARAMETERS must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NUMERATOR
static_assert(false, "NUMERATOR must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NUMERATOR_INTERNAL
static_assert(false, "NUMERATOR_INTERNAL must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NUM_10
static_assert(false, "NUM_10 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NUM_2
static_assert(false, "NUM_2 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NUM_3
static_assert(false, "NUM_3 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NUM_4
static_assert(false, "NUM_4 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NUM_5
static_assert(false, "NUM_5 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NUM_6
static_assert(false, "NUM_6 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NUM_7
static_assert(false, "NUM_7 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NUM_8
static_assert(false, "NUM_8 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NUM_9
static_assert(false, "NUM_9 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NUM_BYTES_REMAINING_TO_READ
static_assert(
    false,
    "NUM_BYTES_REMAINING_TO_READ must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NUM_IS_DIVISIBLE
static_assert(false, "NUM_IS_DIVISIBLE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NUM_SHR_VALUE
static_assert(false, "NUM_SHR_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef OFFSET
static_assert(false, "OFFSET must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef ONE
static_assert(false, "ONE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef OUTUNITPREFIXVALUE_INTERNAL
static_assert(
    false,
    "OUTUNITPREFIXVALUE_INTERNAL must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef PERCENTAGE_POWER_VALUE
static_assert(false, "PERCENTAGE_POWER_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef PERCENTAGE_PREFIX_VALUE
static_assert(false, "PERCENTAGE_PREFIX_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef PETA
static_assert(false, "PETA must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef PICO
static_assert(false, "PICO must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef PIXEL_POWER_VALUE
static_assert(false, "PIXEL_POWER_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef PIXEL_PREFIX_VALUE
static_assert(false, "PIXEL_PREFIX_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef POWER_VALUE
static_assert(false, "POWER_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef RBINFO
static_assert(false, "RBINFO must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef RESULTANT_AMOUNTOFSUBSTANCE
static_assert(
    false,
    "RESULTANT_AMOUNTOFSUBSTANCE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef RESULTANT_ANGLE
static_assert(false, "RESULTANT_ANGLE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef RESULTANT_CURRENT
static_assert(false, "RESULTANT_CURRENT must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef RESULTANT_LENGTH
static_assert(false, "RESULTANT_LENGTH must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef RESULTANT_LUMINOUSINTENSITY
static_assert(
    false,
    "RESULTANT_LUMINOUSINTENSITY must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef RESULTANT_MASS
static_assert(false, "RESULTANT_MASS must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef RESULTANT_PERCENTAGE
static_assert(false, "RESULTANT_PERCENTAGE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef RESULTANT_PIXLE
static_assert(false, "RESULTANT_PIXLE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef RESULTANT_TEMPERATURE
static_assert(false, "RESULTANT_TEMPERATURE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef SECOND
static_assert(false, "SECOND must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef SHIFT_BYTES
static_assert(false, "SHIFT_BYTES must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef SHR_VALUE
static_assert(false, "SHR_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef SIZE
static_assert(false, "SIZE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef SIZE_
static_assert(false, "SIZE_ must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef SIZE_VALUE
static_assert(false, "SIZE_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef SI_FUNDAMENTAL
static_assert(false, "SI_FUNDAMENTAL must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef SI_UNIT
static_assert(false, "SI_UNIT must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef STORAGE_ALIGNMENT
static_assert(false, "STORAGE_ALIGNMENT must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T
static_assert(false, "T must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T1
static_assert(false, "T1 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T10
static_assert(false, "T10 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T11
static_assert(false, "T11 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T12
static_assert(false, "T12 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T13
static_assert(false, "T13 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T14
static_assert(false, "T14 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T15
static_assert(false, "T15 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T16
static_assert(false, "T16 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T17
static_assert(false, "T17 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T18
static_assert(false, "T18 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T19
static_assert(false, "T19 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T2
static_assert(false, "T2 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T20
static_assert(false, "T20 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T21
static_assert(false, "T21 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T22
static_assert(false, "T22 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T23
static_assert(false, "T23 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T24
static_assert(false, "T24 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T3
static_assert(false, "T3 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T4
static_assert(false, "T4 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T5
static_assert(false, "T5 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T6
static_assert(false, "T6 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T7
static_assert(false, "T7 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T8
static_assert(false, "T8 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef T9
static_assert(false, "T9 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef TEMP0
static_assert(false, "TEMP0 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef TEMP1
static_assert(false, "TEMP1 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef TEMP2
static_assert(false, "TEMP2 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef TEMP3
static_assert(false, "TEMP3 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef TEMP4
static_assert(false, "TEMP4 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef TEMP5
static_assert(false, "TEMP5 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef TEMP6
static_assert(false, "TEMP6 must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef TEMPERATURE_POWER_VALUE
static_assert(false, "TEMPERATURE_POWER_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef TEMPERATURE_PREFIX_VALUE
static_assert(false, "TEMPERATURE_PREFIX_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef TERA
static_assert(false, "TERA must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef TIME_POWER_VALUE
static_assert(false, "TIME_POWER_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef TIME_PREFIX_VALUE
static_assert(false, "TIME_PREFIX_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef TOTAL_NUM_BYTES_TO_READ
static_assert(false, "TOTAL_NUM_BYTES_TO_READ must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef TO_INFINITY
static_assert(false, "TO_INFINITY must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef TRAIT
static_assert(false, "TRAIT must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef TYPE
static_assert(false, "TYPE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef U
static_assert(false, "U must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef UNIT
static_assert(false, "UNIT must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef USE_CACHE
static_assert(false, "USE_CACHE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef V
static_assert(false, "V must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef VALUE
static_assert(false, "VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef VALUE_TYPE_ALIGNMENT
static_assert(false, "VALUE_TYPE_ALIGNMENT must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef WEEK
static_assert(false, "WEEK must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef WEEK_IN_DAYS
static_assert(false, "WEEK_IN_DAYS must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef YEAR
static_assert(false, "YEAR must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef YEAR_IN_DAYS
static_assert(false, "YEAR_IN_DAYS must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef YEAR_IN_MONTHS
static_assert(false, "YEAR_IN_MONTHS must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef YOCTO
static_assert(false, "YOCTO must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef YOTTA
static_assert(false, "YOTTA must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef Y_VALUE
static_assert(false, "Y_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef ZEPTO
static_assert(false, "ZEPTO must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef ZERO_COUNT
static_assert(false, "ZERO_COUNT must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef ZETTA
static_assert(false, "ZETTA must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef ABA_MIN_SIZE
static_assert(false, "ABA_MIN_SIZE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef ALLNEW
static_assert(false, "ALLNEW must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef ASSIGN_CAPACITY
static_assert(false, "ASSIGN_CAPACITY must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef ASSIGN_CONTAINER_SIZE
static_assert(false, "ASSIGN_CONTAINER_SIZE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef BASE_VERSION
static_assert(false, "BASE_VERSION must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef BUDGET
static_assert(false, "BUDGET must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef CALCULATION_BASE
static_assert(false, "CALCULATION_BASE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef COMMANDOUTPUT_FIFO_SIZE
static_assert(false, "COMMANDOUTPUT_FIFO_SIZE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef COMMAND_CAPACITY
static_assert(false, "COMMAND_CAPACITY must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef CONNECT
static_assert(false, "CONNECT must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DATA_CONTAINER_SIZE
static_assert(false, "DATA_CONTAINER_SIZE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DEFAULT_COMMAND_CAPACITY_RECEIVER
static_assert(
    false,
    "DEFAULT_COMMAND_CAPACITY_RECEIVER must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DEFAULT_MAX_SUBSCRIBER
static_assert(false, "DEFAULT_MAX_SUBSCRIBER must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef DISCONNECT
static_assert(false, "DISCONNECT must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef ENABLE_INTERVENTION
static_assert(false, "ENABLE_INTERVENTION must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef ENABLE_PROXY
static_assert(false, "ENABLE_PROXY must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef HSM_HANDLED
static_assert(false, "HSM_HANDLED must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef HSM_IGNORED
static_assert(false, "HSM_IGNORED must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef HSM_MAX_NEST_DEPTH
static_assert(false, "HSM_MAX_NEST_DEPTH must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef HSM_SUPER
static_assert(false, "HSM_SUPER must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef HSM_TOP_STATE
static_assert(false, "HSM_TOP_STATE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef HSM_TRAN
static_assert(false, "HSM_TRAN must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef HSM_USER_STATE
static_assert(false, "HSM_USER_STATE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef IMAGE_BASE_VERSION
static_assert(false, "IMAGE_BASE_VERSION must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef IMPRECISE
static_assert(false, "IMPRECISE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef INDEX_BIT_SIZE
static_assert(false, "INDEX_BIT_SIZE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef INDEX_BIT_SIZE_VALUE
static_assert(false, "INDEX_BIT_SIZE_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MAX_HOLD_SIZE
static_assert(false, "MAX_HOLD_SIZE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MAX_NUMBER_OF_SUPPORTED_SUBSCRIBER
static_assert(
    false,
    "MAX_NUMBER_OF_SUPPORTED_SUBSCRIBER must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MAX_SUBSCRIBERS
static_assert(false, "MAX_SUBSCRIBERS must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NO_COMMAND
static_assert(false, "NO_COMMAND must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NUMBER_OF_CHUNKS
static_assert(false, "NUMBER_OF_CHUNKS must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef OK
static_assert(false, "OK must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef PORT_DATA_SIZE
static_assert(false, "PORT_DATA_SIZE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef PORT_ID_VALUE
static_assert(false, "PORT_ID_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef REALTIME
static_assert(false, "REALTIME must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef RECEIVER
static_assert(false, "RECEIVER must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef RELEASE_CAPACITY
static_assert(false, "RELEASE_CAPACITY must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef RELEASE_SIZE_VALUE
static_assert(false, "RELEASE_SIZE_VALUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef RESERVED_ITEMS_HOLD
static_assert(false, "RESERVED_ITEMS_HOLD must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef SENDER
static_assert(false, "SENDER must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef TAPI
static_assert(false, "TAPI must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef BRANCH_FALSE
static_assert(false, "BRANCH_FALSE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef BRANCH_TRUE
static_assert(false, "BRANCH_TRUE must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef G_NUM_BRANCH_DECISIONS
static_assert(false, "G_NUM_BRANCH_DECISIONS must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef MAX_RECEIVERS
static_assert(false, "MAX_RECEIVERS must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef NO_LIMITATION
static_assert(false, "NO_LIMITATION must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef STRUCT_T
static_assert(false, "STRUCT_T must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#ifdef VERSION
static_assert(false, "VERSION must not be used as macro as it conflicts with usage in DADDY or VFC");
#endif

#endif // VFC_MACRO_CHECKS_INCLUDED

