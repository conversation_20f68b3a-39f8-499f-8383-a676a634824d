//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_TYPELIST_INL_INCLUDED
#define VFC_TYPELIST_INL_INCLUDED

#include "vfc/core/vfc_types.hpp"         // used for int32_t
#include "vfc/core/vfc_static_assert.hpp" // used for VFC_STATIC_ASSERT

namespace vfc
{
// exclude all specializations from doxygen
/// @cond VFC_DOXY_SPECIALIZATIONS

//=========================================================================
// TTypeListSize specializations
//=========================================================================

// terminator reached
template <>
struct TTypeListSize<CTypeListNullType>
{
    enum
    {
        value = 0
    };
};

// recursion
template <typename HeadType, typename TailType>
struct TTypeListSize<TTypeList<HeadType, TailType>>
{
    enum
    {
        value = 1 + TTypeListSize<TailType>::value
    };
};

//=========================================================================
// TTypeListIndexOf specializations
//=========================================================================

// terminator reached (element as not be found)
template <class SearchType>
struct TTypeListIndexOf<vfc::CTypeListNullType, SearchType>
{
    enum
    {
        value = -1
    };
};

// type was found
template <class SearchType, typename TailType>
struct TTypeListIndexOf<TTypeList<SearchType, TailType>, SearchType>
{
    enum
    {
        value = 0
    };
};

// If the the type was found (value == 0) then count up
template <typename HeadType, typename TailType, typename SearchType>
struct TTypeListIndexOf<TTypeList<HeadType, TailType>, SearchType>
{
  private:
    enum
    {
        temp = TTypeListIndexOf<TailType, SearchType>::value
    };

  public:
    enum
    {
        value = ((temp == -1) ? (-1) : (1 + temp))
    };
};

//=========================================================================
// TTypeListTypeAt specializations
//=========================================================================

// recursion with argument validation
template <typename HeadType, typename TailType, int32_t IndexValue>
struct TTypeListTypeAt<TTypeList<HeadType, TailType>, IndexValue>
{
    using type = typename TTypeListTypeAt<TailType, IndexValue - 1>::type;

    // check if index is positive
    VFC_STATIC_ASSERT(0 < IndexValue);
};

// results in an error if NullType is reached before Index goes Zero
template <typename HeadType, int32_t IndexValue>
struct TTypeListTypeAt<TTypeList<HeadType, CTypeListNullType>, IndexValue>;
// intentionally no definition here!!

// terminate if index goes zero
template <typename HeadType>
struct TTypeListTypeAt<TTypeList<HeadType, CTypeListNullType>, 0>
{
    using type = HeadType;
};

// terminate if index goes zero
template <typename HeadType, typename TailType>
struct TTypeListTypeAt<TTypeList<HeadType, TailType>, 0>
{
    using type = HeadType;
};

//=========================================================================
// TTypeListAppend specializations
//=========================================================================

// add terminator to terminator
template <>
struct TTypeListAppend<CTypeListNullType, CTypeListNullType>
{
    using type = CTypeListNullType;
};

// append type specialization for typelist terminator, appending single type
template <class AppendType>
struct TTypeListAppend<CTypeListNullType, AppendType>
{
    using type = TTypeList<AppendType, CTypeListNullType>;
};

// Append type specialization for typelist terminator, appending typelist
template <class HeadType, class TailType>
struct TTypeListAppend<CTypeListNullType, TTypeList<HeadType, TailType>>
{
    using type = TTypeList<HeadType, TailType>;
};

// Append type for appending a type or another typelist to an existing typelist,
// resulting typelist is Append<TList,T>::result

template <class HeadType, class TailType, class AppendType>
struct TTypeListAppend<TTypeList<HeadType, TailType>, AppendType>
{
    using type = TTypeList<HeadType, typename TTypeListAppend<TailType, AppendType>::type>;
};

/// @endcond
//  of VFC_DOXY_SPECIALIZATIONS

} // namespace vfc

#endif // VFC_TYPELIST_INL_INCLUDED

//=============================================================================

