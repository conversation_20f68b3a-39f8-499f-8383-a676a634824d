//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_ATOMIC_MSVC_INL_INCLUDED
#define ZX_VFC_ATOMIC_MSVC_INL_INCLUDED

#if !defined(VFC_COMPILER_VISUALC)
static_assert(false, "A _msvc.inl included not using Visual Studio.");
#endif

#include <intrin.h>

namespace vfc
{
namespace intern
{
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

template <>
inline bool AtomicOps<uint32_t>::compare_exchange_weak(
    uint32_t&         memory,
    uint32_t&         expected,
    uint32_t          desired,
    vfc::EMemoryOrder successOrder,
    vfc::EMemoryOrder failureOrder)
{
    // https://docs.microsoft.com/en-us/cpp/intrinsics/interlockedcompareexchange-intrinsic-functions?view=vs-2015
    // long _InterlockedCompareExchange(long volatile * Destination, long Exchange, long Comparand);

    static_assert(sizeof(volatile long) == sizeof(uint32_t), "_InterlockedCompareExchange() memory size mismatch.");

    // We cast because we really want to just have bitpatterns, no conversions!
    volatile long* volatileLong  = reinterpret_cast<volatile long*>(&memory);
    long           desired_long  = static_cast<long>(desired);
    long           expected_long = static_cast<long>(expected);

    // NOTE: According to Microsoft's documention for this API, the memory ordering is not a parameter that can be
    // passed directly to this method, but there are rather different APIs with suffixes to specify the different memory
    // orderings. Furthermore, some suffixes are meant for ARM architecture, while others are meant for Intel if some
    // hardware feature is present. Overall, because this compiler is not used for production, for the moment we leave
    // here the API that uses the sequential consistent ordering and ignore the memory orderings passed as parameters.
    long mem_before = _InterlockedCompareExchange(volatileLong, desired_long, expected_long);

    if (mem_before == expected_long)
    {
        return true;
    }
    else
    {
        expected = static_cast<uint32_t>(mem_before);
        return false;
    }
}

template <>
inline bool AtomicOps<uint64_t>::compare_exchange_weak(
    uint64_t&         memory,
    uint64_t&         expected,
    uint64_t          desired,
    vfc::EMemoryOrder successOrder,
    vfc::EMemoryOrder failureOrder)
{
    // https://docs.microsoft.com/en-us/cpp/intrinsics/interlockedcompareexchange-intrinsic-functions?view=vs-2015
    // __int64 _InterlockedCompareExchange64(__int64 volatile * Destination, __int64 Exchange, __int64 Comparand);

    static_assert(
        sizeof(volatile __int64) == sizeof(uint64_t), "_InterlockedCompareExchange64() memory size mismatch.");

    // We cast because we really want to just have bitpatterns, no conversions!
    volatile __int64* volatileLong64 = reinterpret_cast<volatile __int64*>(&memory);
    __int64           desired_64     = static_cast<__int64>(desired);
    __int64           expected_64    = static_cast<__int64>(expected);

    // NOTE: According to Microsoft's documention for this API, the memory ordering is not a parameter that can be
    // passed directly to this method, but there are rather different APIs with suffixes to specify the different memory
    // orderings. Furthermore, some suffixes are meant for ARM architecture, while others are meant for Intel if some
    // hardware feature is present. Overall, because this compiler is not used for production, for the moment we leave
    // here the API that uses the sequential consistent ordering and ignore the memory orderings passed as parameters.
    __int64 mem_before = _InterlockedCompareExchange64(volatileLong64, desired_64, expected_64);

    if (mem_before == expected_64)
    {
        return true;
    }
    else
    {
        expected = static_cast<uint64_t>(mem_before);
        return false;
    }
}

template <>
inline uint32_t AtomicOps<uint32_t>::exchange(uint32_t& memory, uint32_t desired, vfc::EMemoryOrder /*order*/)
{
    // https://docs.microsoft.com/en-us/cpp/intrinsics/interlockedcompareexchange-intrinsic-functions?view=vs-2015
    // long _InterlockedExchange(long volatile * Target, long Value);

    static_assert(sizeof(volatile long) == sizeof(uint32_t), "_InterlockedExchange() memory size mismatch.");

    // We cast because we really want to just have bitpatterns, no conversions!
    volatile long* volatileLong = reinterpret_cast<volatile long*>(&memory);
    long           desired_long = static_cast<long>(desired);

    long ret = _InterlockedExchange(volatileLong, desired_long);

    return static_cast<uint32_t>(ret);
}

#if defined(VFC_PROCESSOR_X64)
// The intrinsic function _InterlockedExchange64() is only available on real 64-bit builds!

template <>
inline uint64_t AtomicOps<uint64_t>::exchange(uint64_t& memory, uint64_t desired, vfc::EMemoryOrder /*order*/)
{
    // https://docs.microsoft.com/en-us/cpp/intrinsics/interlockedcompareexchange-intrinsic-functions?view=vs-2015
    // __int64 _InterlockedExchange64(__int64 volatile * Target, __int64 Value);

    static_assert(sizeof(volatile __int64) == sizeof(uint64_t), "Atomic memory size mismatch.");

    volatile __int64* volatileLong64 = reinterpret_cast<volatile __int64*>(&memory);
    __int64           desired_64     = static_cast<__int64>(desired);

    __int64 ret = _InterlockedExchange64(volatileLong64, desired_64);

    return static_cast<uint64_t>(ret);
}
#endif // VFC_PROCESSOR_X64

template <>
inline void Fence<DummyType>::seqCst()
{
    // https://software.intel.com/sites/landingpage/IntrinsicsGuide/#text=_mm_mfence&expand=3678 void _mm_mfence (void)
    // "Perform a serializing operation on all load-from-memory and store-to-memory instructions that were issued prior
    // to this instruction. Guarantees that every memory access that precedes, in program order, the memory fence
    // instruction is globally visible before any memory instruction which follows the fence in program order."
    //
    // Note also: https://docs.microsoft.com/en-us/cpp/intrinsics/faststorefence?view=vs-2019 "void
    // __faststorefence();"" "The effect is comparable to but faster than the _mm_mfence intrinsic on all x64
    // platforms."
    _mm_mfence();
}

template <>
inline void Fence<DummyType>::acqRel()
{
    // No barrier necessary on x86 target for acq/rel semantics.
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
} // namespace intern
} // namespace vfc

#endif

