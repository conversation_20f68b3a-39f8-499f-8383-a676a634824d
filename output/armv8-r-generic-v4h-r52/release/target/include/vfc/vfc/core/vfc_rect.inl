//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_RECT_INL_INCLUDED
#define ZX_VFC_RECT_INL_INCLUDED

#include <algorithm>             // used for ::std::min(), ::std::max()
#include "vfc/core/vfc_math.hpp" // used for vfc::isZero

inline vfc::CPoint::CPoint(void) : m_x(0), m_y(0) {}

inline vfc::CPoint::CPoint(int32_t f_x_i32, int32_t f_y_i32) : m_x(f_x_i32), m_y(f_y_i32) {}

inline vfc::CPoint& vfc::CPoint::operator+=(const CPoint& f_rhs)
{
    m_x += f_rhs.m_x;
    m_y += f_rhs.m_y;
    return *this;
}

inline vfc::CPoint& vfc::CPoint::operator-=(const CPoint& f_rhs)
{
    m_x -= f_rhs.m_x;
    m_y -= f_rhs.m_y;
    return *this;
}

inline const vfc::CPoint vfc::operator+(const CPoint& f_op1, const CPoint& f_op2) { return CPoint(f_op1) += f_op2; }

inline const vfc::CPoint vfc::operator-(const CPoint& f_op1, const CPoint& f_op2) { return CPoint(f_op1) -= f_op2; }

inline vfc::CSize::CSize(void) : m_cx(0), m_cy(0) {}

inline vfc::CSize::CSize(int32_t f_cx_i32, int32_t f_cy_i32) : m_cx(f_cx_i32), m_cy(f_cy_i32) {}

inline vfc::CSize& vfc::CSize::operator+=(const CSize& f_rhs)
{
    m_cx += f_rhs.m_cx;
    m_cy += f_rhs.m_cy;
    return *this;
}

inline vfc::CSize& vfc::CSize::operator-=(const CSize& f_rhs)
{
    m_cx -= f_rhs.m_cx;
    m_cy -= f_rhs.m_cy;
    return *this;
}

inline const vfc::CSize vfc::operator+(const CSize& f_op1, const CSize& f_op2) { return CSize(f_op1) += f_op2; }

inline const vfc::CSize vfc::operator-(const CSize& f_op1, const CSize& f_op2) { return CSize(f_op1) -= f_op2; }

inline vfc::CRect::CRect(void) : m_left(0), m_top(0), m_width(0), m_height(0) {}

inline vfc::CRect::CRect(int32_t f_left, int32_t f_top, int32_t f_width, int32_t f_height)
    : m_left(f_left), m_top(f_top), m_width(f_width), m_height(f_height)
{
}

inline vfc::CRect::CRect(const CPoint& f_topLeft, const CSize& f_size)
    : m_left(f_topLeft.x()), m_top(f_topLeft.y()), m_width(f_size.cx()), m_height(f_size.cy())
{
}

inline vfc::CRect::CRect(const CPoint& f_topLeft, const CPoint& f_bottomRight)
    : m_left(f_topLeft.x()),
      m_top(f_topLeft.y()),
      m_width(f_bottomRight.x() - f_topLeft.x()),
      m_height(f_bottomRight.y() - f_topLeft.y())
{
}

inline vfc::CPoint vfc::CRect::topLeft(void) const { return CPoint(left(), top()); }

inline vfc::CPoint vfc::CRect::bottomRight(void) const { return CPoint(right(), bottom()); }

inline vfc::CPoint vfc::CRect::center(void) const { return CPoint((left() + right()) / 2, (top() + bottom()) / 2); }

inline vfc::CSize vfc::CRect::size(void) const { return CSize(width(), height()); }

inline vfc::int32_t vfc::CRect::area(void) const { return width() * height(); }

inline bool vfc::CRect::isEmpty(void) const { return ((0 >= width()) || (0 >= height())); }

inline bool vfc::CRect::contains(const CRect& f_otherRect) const
{
    return (contains(f_otherRect.topLeft()) && contains(f_otherRect.right() - 1, f_otherRect.bottom() - 1));
}

inline bool vfc::CRect::contains(const CPoint& f_point) const { return contains(f_point.x(), f_point.y()); }

inline bool vfc::CRect::contains(int32_t f_px, int32_t f_py) const
{
    return ((f_px >= left()) && (f_px < right()) && (f_py >= top()) && (f_py < bottom()));
}

inline void vfc::CRect::resize(int32_t f_cx, int32_t f_cy)
{
    m_width  = f_cx;
    m_height = f_cy;
}

inline void vfc::CRect::resize(const CSize& f_newSize) { resize(f_newSize.cx(), f_newSize.cy()); }

inline void vfc::CRect::normalize(void)
{
    if (0 > m_width)
    {
        m_left += m_width;
        m_width = -m_width;
    }
    if (0 > m_height)
    {
        m_top += m_height;
        m_height = -m_height;
    }
}

inline void vfc::CRect::inflate(int32_t f_dx, int32_t f_dy) { inflate(f_dx, f_dy, f_dx, f_dy); }

inline void vfc::CRect::inflate(int32_t f_dleft, int32_t f_dtop, int32_t f_dright, int32_t f_dbottom)
{
    m_left -= f_dleft;
    m_top -= f_dtop;

    m_width += f_dleft + f_dright;
    m_height += f_dtop + f_dbottom;
}

inline void vfc::CRect::deflate(int32_t f_dx, int32_t f_dy) { inflate(-f_dx, -f_dy, -f_dx, -f_dy); }

inline void vfc::CRect::deflate(int32_t f_dleft, int32_t f_dtop, int32_t f_dright, int32_t f_dbottom)
{
    inflate(-f_dleft, -f_dtop, -f_dright, -f_dbottom);
}

inline void vfc::CRect::offset(int32_t f_dx, int32_t f_dy)
{
    m_left += f_dx;
    m_top += f_dy;
}

inline void vfc::CRect::moveTo(int32_t f_x, int32_t f_y)
{
    m_left = f_x;
    m_top  = f_y;
}

inline void vfc::CRect::moveTo(const CPoint& f_point) { moveTo(f_point.x(), f_point.y()); }

inline vfc::CRect vfc::union_rect(const CRect& f_op1, const CRect& f_op2)
{
    return CRect(
        CPoint((stlalias::min)(f_op1.left(), f_op2.left()), (stlalias::min)(f_op1.top(), f_op2.top())),
        CPoint((stlalias::max)(f_op1.right(), f_op2.right()), (stlalias::max)(f_op1.bottom(), f_op2.bottom())));
}

inline vfc::CRect vfc::intersect_rect(const CRect& f_op1, const CRect& f_op2)
{
    return CRect(
        CPoint((stlalias::max)(f_op1.left(), f_op2.left()), (stlalias::max)(f_op1.top(), f_op2.top())),
        CPoint((stlalias::min)(f_op1.right(), f_op2.right()), (stlalias::min)(f_op1.bottom(), f_op2.bottom())));
}

inline bool vfc::operator==(const CRect& f_lhs, const CRect& f_rhs)
{
    return (
        (f_lhs.width() == f_rhs.width()) && (f_lhs.height() == f_rhs.height()) && (f_lhs.left() == f_rhs.left()) &&
        (f_lhs.top() == f_rhs.top()));
}

inline bool vfc::operator!=(const CRect& f_lhs, const CRect& f_rhs) { return !(f_lhs == f_rhs); }

#endif // ZX_VFC_RECT_INL_INCLUDED

