//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_GHC_HPP_INCLUDED
#define VFC_GHC_HPP_INCLUDED

#include "vfc/core/vfc_preprocessor.hpp"
//////////////////////////////
// versions check
//////////////////////////////

// we don't know GHC prior to version 202214:
#if (__GHS_VERSION_NUMBER < 202214)
static_assert(false, "Greenhills compiler version not supported or configured");
#endif

// last known and checked version is 202214:
#if (__GHS_VERSION_NUMBER > 202214)
static_assert(false, "Unknown compiler version");
#endif

///////////////////////////////
//  GreenHills C++ compiler setup
///////////////////////////////

#define VFC_COMPILER_VERSION VFC_STRINGIZE(__GHS_VERSION_NUMBER)
#define VFC_COMPILER_STRING "GreenHills version " VFC_COMPILER_VERSION

#if defined(__RH850__)
#define VFC_RH850_DETECTED
#elif defined(__V850)
#define VFC_V850_DETECTED
#elif defined(__ppc__)
#define VFC_EPPC_DETECTED
#elif defined(__Tricore)
#define VFC_TRICORE_DETECTED
#elif defined(__ARM__)
#define VFC_ARM_DETECTED
#elif defined(__ARM64__)
#define VFC_ARM64_DETECTED
#endif

#ifndef VFC_COMPILER_GHS
#define VFC_COMPILER_GHS
#endif

//////////////////////////////
// exception handling
//////////////////////////////

#ifndef __EXCEPTIONS
#define VFC_NO_EXCEPTIONS
#endif

//////////////////////////////
// stdint.h support
//////////////////////////////

#define __STDC_LIMIT_MACROS
#define VFC_HAS_STDINT_H

//////////////////////////////
// long long support
//////////////////////////////

#ifndef __NO_LONGLONG
#define VFC_HAS_LONG_LONG
#endif

////////////////////////////////////
// processor identification
////////////////////////////////////

#if defined(__RH850__)
#define VFC_PROCESSOR_RH850
#elif defined(__V800)
#define VFC_PROCESSOR_V8XX
#elif defined(__ppc)
#define VFC_EPPC_DETECTED
#elif defined(__Tricore)
#define VFC_PROCESSOR_TRICORE
#elif defined(__ARM__)
#define VFC_PROCESSOR_ARM
#elif defined(__ARM64__)
#define VFC_PROCESSOR_ARM64
#else
static_assert(false, "processor not supported");
#endif

////////////////////////////////////
// VFC needs namespace support
////////////////////////////////////

#ifndef __NAMESPACES
static_assert(false, "VFC needs namespace support, see build_{v800,ppc}.{pdf,chm}");
#endif

////////////////////////////////////
// floating point support
////////////////////////////////////

#ifdef __NoFloat
static_assert(false, "Compiler support for floats needed.");
#endif

// ghs for Tricore has float16 support as of 201713_testversion1i
// when the compiler option -half_precision_type is given
#if defined(VFC_TRICORE_DETECTED) && (__GHS_VERSION_NUMBER >= 201713) && defined(__FP16_BIT)
#define VFC_NATIVE_FLOAT16_TYPE __fp16
#endif

// macro abstraction for linker statements
#define VFC_LINKER_SECT1 __attribute__
#define VFC_LINKER_SECT(sect_name) __attribute__((used, section(sect_name)))

#ifndef VFC_DECL_FORCEINLINE
#define VFC_DECL_FORCEINLINE inline
#endif

#define VFC_TYPE_ALIGNMENT_PREFIX(val)
#define VFC_TYPE_ALIGNMENT_SUFFIX(val) __attribute__((aligned(val)))

#ifndef VFC_ATR_DEPRECATED
#define VFC_ATR_DEPRECATED(EXP) __attribute__((deprecated)) EXP
#endif

#ifndef VFC_ATR_DEPRECATED2
#define VFC_ATR_DEPRECATED2(EXP, MSG) __attribute__((deprecated(MSG))) EXP
#endif

#ifndef VFC_ATR_NODISCARD
#define VFC_ATR_NODISCARD __attribute__((warn_unused_result))
#endif

// To enable the no-inline feature on ghs the compiler option "--enable_noinline" has
// to be used during compiler invocation
#ifndef VFC_ATR_NOINLINE_REQUEST
#define VFC_ATR_NOINLINE_REQUEST __attribute__((noinline))
#endif

#endif // VFC_GHC_HPP_INCLUDED

//=============================================================================

