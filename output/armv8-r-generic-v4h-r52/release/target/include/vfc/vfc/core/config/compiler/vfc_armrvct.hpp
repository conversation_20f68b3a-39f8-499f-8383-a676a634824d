//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_ARMRVCT_HPP_INCLUDED
#define VFC_ARMRVCT_HPP_INCLUDED

#include "vfc/core/vfc_preprocessor.hpp"

//////////////////////////////
// versions check
//////////////////////////////

// we do not support versions prior 5.04 build 82
#if (__ARMCC_VERSION < 5040081)
static_assert(false, "Compiler not supported or configured");
#endif

// we do not support versions newer than 5.04 build 82 except 6.4, 6.5, 6.6 (upto 6.6.4)
#if (((__ARMCC_VERSION > 5040081) && (__ARMCC_VERSION < 6040000)) || (__ARMCC_VERSION > 6060401))
static_assert(false, "Unknown compiler version");
#endif

///////////////////////////////
//  ARM RVCT C++ compiler setup
///////////////////////////////

// use predefined macros,
// see DUI0348A_rvct_compiler_ref_guide.pdf section 4.8

#if (__ARMCC_VERSION < 6000000)
#define VFC_COMPILER_STRING VFC_JOIN("ARM RVCT version ", VFC_STRINGIZE(__ARMCC_VERSION))

#ifndef VFC_COMPILER_ARMRVCT
#define VFC_COMPILER_ARMRVCT
#endif
#else
#define VFC_COMPILER_STRING VFC_STRINGIZE(__ARMCC_VERSION)

#ifndef VFC_COMPILER_AC6
#define VFC_COMPILER_AC6
#endif
// Disables __promise intrinsic, else functions calling assert() need to be declared with
// __attribute__((const)) or __attribute__((pure)) to prevent throw of "Wassume" warning
#ifndef __DO_NOT_LINK_PROMISE_WITH_ASSERT
#define __DO_NOT_LINK_PROMISE_WITH_ASSERT
#endif
#endif

//////////////////////////////
// exception handling
//////////////////////////////

#ifndef __EXCEPTIONS
#define VFC_NO_EXCEPTIONS
#endif

////////////////////////////////////
// RTTI support
////////////////////////////////////

#ifndef __RTTI
#define VFC_NO_RTTI
#endif

//////////////////////////////
// stdint.h support
//////////////////////////////

#define VFC_HAS_STDINT_H

//////////////////////////////
// endianess
//////////////////////////////

#if defined(VFC_COMPILER_AC6)
#if defined(__ARM_BIG_ENDIAN)
#define VFC_BIG_ENDIAN
#endif
#else
#if defined(__BIG_ENDIAN)
#define VFC_BIG_ENDIAN
#endif
#endif

//////////////////////////////
// long long support
//
// - with C++03, only allow if no
//   strict ansi conformance is requested
// - since C++11, allow all the time
//////////////////////////////

#if !defined(__STRICT_ANSI__) || (__cplusplus >= 201103L)
#define VFC_HAS_LONG_LONG
#endif

////////////////////////////////////
// processor identification
////////////////////////////////////

#if defined(__arm__)
#define VFC_ARM_DETECTED
#define VFC_PROCESSOR_ARM
#if defined(__TARGET_CPU_CORTEX_A8)
#define VFC_PROCESSOR_ARM_CORTEXA8
#elif defined(__TARGET_CPU_CORTEX_A9)
#define VFC_PROCESSOR_ARM_CORTEXA9
#elif defined(__TARGET_CPU_CORTEX_R4)
#define VFC_PROCESSOR_ARM_CORTEXR4
#elif defined(__ARM_ARCH_PROFILE)
#if (__ARM_ARCH_PROFILE == 'A')
#define VFC_PROCESSOR_ARM_CORTEXA
#elif (__ARM_ARCH_PROFILE == 'R')
#define VFC_PROCESSOR_ARM_CORTEXR
#else
static_assert(false, "Processor not supported");
#endif
#else
static_assert(false, "processor not supported");
#endif
#elif defined(__aarch64__)
#define VFC_ARM64_DETECTED
#define VFC_PROCESSOR_ARM64
// Target cpu detection not possbile with AC6, so we only
#if defined(__ARM_ARCH_PROFILE)
#if (__ARM_ARCH_PROFILE == 'A')
#define VFC_PROCESSOR_ARM_CORTEXA
#elif (__ARM_ARCH_PROFILE == 'R')
#define VFC_PROCESSOR_ARM_CORTEXR
#else
static_assert(false, "Processor not supported");
#endif
#else
static_assert(false, "Processor not supported");
#endif
#endif

////////////////////////////////////
// floating point support
////////////////////////////////////

#ifdef __TARGET_FPU_NONE
static_assert(false, "Compiler support for floats needed.");
#endif

////////////////////////////////////
// neon support
////////////////////////////////////

#if defined(__ARM_NEON__) || defined(__ARM_NEON)
#define VFC_HAS_ARM_NEON
#endif

// macro abstraction for linker statements
#define VFC_LINKER_SECT1 __attribute__
#if !defined(VFC_COMPILER_AC6)
#define VFC_LINKER_SECT(sect_name) VFC_JOIN(VFC_LINKER_SECT1, ((used, section##(sect_name)##)))
#else
// AC6 uses GCC style:
#define VFC_LINKER_SECT(sect_name) __attribute__((section(sect_name)))
#endif

#ifndef VFC_DECL_FORCEINLINE
#if !defined(VFC_COMPILER_AC6)
#define VFC_DECL_FORCEINLINE __forceinline
#else
// AC6 uses GCC style:
#define VFC_DECL_FORCEINLINE inline __attribute__((always_inline))
#endif
#endif

#ifndef VFC_ATR_DEPRECATED
#define VFC_ATR_DEPRECATED(EXP) __attribute__((deprecated)) EXP
#endif

#ifndef VFC_ATR_DEPRECATED2
#define VFC_ATR_DEPRECATED2(EXP, MSG) __attribute__((deprecated(MSG))) EXP
#endif

#ifndef VFC_ATR_POST_DEPRECATED
#define VFC_ATR_POST_DEPRECATED(EXP) EXP __attribute__((deprecated))
#endif

#ifndef VFC_ATR_POST_DEPRECATED2
#define VFC_ATR_POST_DEPRECATED2(EXP, MSG) EXP __attribute__((deprecated(MSG)))
#endif

#ifndef VFC_ATR_DEPRECATED_ENUM_VALUE
#define VFC_ATR_DEPRECATED_ENUM_VALUE(EXP) VFC_ATR_POST_DEPRECATED(EXP)
#endif

#ifndef VFC_ATR_DEPRECATED_ENUM_VALUE2
#define VFC_ATR_DEPRECATED_ENUM_VALUE2(EXP, MSG) VFC_ATR_POST_DEPRECATED2(EXP, MSG)
#endif

#ifndef VFC_ATR_NODISCARD
#define VFC_ATR_NODISCARD __attribute__((warn_unused_result))
#endif

#endif // VFC_ARMRVCT_HPP_INCLUDED

//=============================================================================

