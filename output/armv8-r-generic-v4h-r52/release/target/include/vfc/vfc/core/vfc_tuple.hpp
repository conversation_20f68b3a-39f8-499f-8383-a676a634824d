//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_TUPLE_HPP_INCLUDED
#define VFC_TUPLE_HPP_INCLUDED

namespace vfc
{ // namespace vfc opened

//=========================================================================
//  TPair
//-------------------------------------------------------------------------
/// A class that provides for the ability to treat two objects of arbitrary
/// types as a single object.
/// The template class stores a pair of objects of type FirstType and SecondType,
/// respectively.
/// The type first_type is the same as the template parameter FirstType and
/// the type second_type is the same as the template parameter SecondType.
/// FirstType and SecondType each need supply only a default constructor,
/// a single-argument constructor, and a destructor.
///
/// The most common use for a TPair is as return types for functions that
/// return two values.
///
/// @tparam FirstType   type of first member of pair
/// @tparam SecondType  type of second member of pair
///
/// @sa
/// - TTriple
/// - TQuadruple
/// @ingroup vfc_group_core_types

//=========================================================================

template <class FirstType, class SecondType = FirstType>
class TPair
{
  public:
    using tuple_type = TPair<FirstType, SecondType>;

    using first_type  = FirstType;
    using second_type = SecondType;

  public:
    /// c'tor, initializes all members with their default c'tor
    /// @dspec{1650}     The SW component "vfc" shall create an empty pair where all
    ///                  entries are initialized with their respective default constructors.
    TPair(void);

    ~TPair() = default;

    /// c'tor, initializes members with specified values
    /// @dspec{821}     The SW component "vfc" shall create a pair from two given values and
    ///                 return the newly created pair.
    /// @param f_first  instance of first_type
    /// @param f_second instance of second_type
    TPair(const first_type& f_first, const second_type& f_second);
    /// Copy Constructor
    /// @dspec{822}     The SW component "vfc" shall create a pair by copying contents of a
    ///                 given pair.
    /// @param f_other  TPair that serves as the source of the copy
    TPair(const TPair<FirstType, SecondType>& f_other);
    /// Copy Assignment Operator
    /// @dspec{1333}     The SW component "vfc" shall copy contents of a given pair.
    /// @param f_other  TPair that serves as the source of the assignment
    TPair& operator=(const TPair& f_other);

    /// returns the first object (read-only)
    /// @dspec{1332}     The SW component "vfc" shall access contents of a given pair and
    ///                  return the value of the requested entry.
    /// @return const reference to the first item of type first_type
    const first_type& first(void) const { return m_first; }
    /// returns the second object (read-only)
    /// @dspecref{1332}
    /// @return const reference to the second item of type second_type
    const second_type& second(void) const { return m_second; }

    /// returns the first object (read-write)
    /// @dspecref{1332}
    /// @return reference to the first item of type first_type
    first_type& first(void)
    {
        return m_first;
    } // PRQA S 4024 # Technical debt L2 id=00c1bdf710509d0941cd-e666fb38c2581e04
    /// returns the second object (read-write)
    /// @dspecref{1332}
    /// @return reference to the second item of type second_type
    second_type& second(void)
    {
        return m_second;
    } // PRQA S 4024 # Technical debt L2 id=00c18cd991a33d714acf-1183364d7bd3ef58

  private:
    first_type  m_first;  /// first object
    second_type m_second; /// second object
};

// creation functions

//-------------------------------------------------------------------------
/// A template helper function used to construct objects of type TPair<T1,T2>,
/// where the component types are based on the data types passed as parameters.
/// @dspecref{821}
/// @relatesalso TPair
/// @tparam FirstType   type of first member of pair
/// @tparam SecondType  type of second member of pair
/// @param f_first      instance of FirstType
/// @param f_second     instance of SecondType
/// @return TPair instance with the specified entries
//-------------------------------------------------------------------------
template <class FirstType, class SecondType>
TPair<FirstType, SecondType> make_pair(const FirstType& f_first, const SecondType& f_second);

//-------------------------------------------------------------------------
/// A template helper function used to construct objects of type TPair<T1,T2>,
/// where the component types are based on the data types passed as parameters.
/// @dspecref{821}
/// @relatesalso TPair
/// @tparam FirstType   type of first member of pair
/// @tparam SecondType  type of second member of pair
/// @param f_first      instance of FirstType
/// @param f_second     instance of SecondType
/// @return TPair instance with the specified entries
//-------------------------------------------------------------------------
template <class FirstType, class SecondType>
TPair<FirstType, SecondType> make_tuple(const FirstType& f_first, const SecondType& f_second);

// template logical operators

//-------------------------------------------------------------------------
/// returns true if two TPair objects are identical.
/// @dspec{1331}     The SW component "vfc" shall return the boolean value "true" if two
///                  given pairs are equivalent otherwise it shall return "false".
/// @relatesalso TPair
/// @tparam FirstType   type of first member of pair
/// @tparam SecondType  type of second member of pair
/// @param f_lhs        TPair instance on left hand side of comparison
/// @param f_rhs        TPair instance on right hand side of comparison
/// @return true if the given instances are identical, else return false
//-------------------------------------------------------------------------
template <class FirstType, class SecondType>
bool operator==(const TPair<FirstType, SecondType>& f_lhs, const TPair<FirstType, SecondType>& f_rhs);

//-------------------------------------------------------------------------
/// returns true if two TPair objects are not identical.
/// @dspec{1330}     The SW component "vfc" shall return the boolean value "true" if two given
///                  pairs are NOT equivalent otherwise it shall return "false".
/// @relatesalso TPair
/// @tparam FirstType   type of first member of pair
/// @tparam SecondType  type of second member of pair
/// @param f_lhs        TPair instance on left hand side of comparison
/// @param f_rhs        TPair instance on right hand side of comparison
/// @return true if the given instances are different, else return false
//-------------------------------------------------------------------------
template <class FirstType, class SecondType>
bool operator!=(const TPair<FirstType, SecondType>& f_lhs, const TPair<FirstType, SecondType>& f_rhs);

//-------------------------------------------------------------------------
/// returns true for (A < B) if TPair object A is less than TPair object B.
/// @dspec{1329}     The SW component "vfc" shall compare two given pairs lexicographically
///                  and check if the first given pair is smaller than, larger than, smaller or
///                  equal, or larger or equan than the other given pair and return the boolean
///                  value "true" if the checked relation holds otherwise if shall return the
///                  boolean value "false".
/// @relatesalso TPair
/// @tparam FirstType   type of first member of pair
/// @tparam SecondType  type of second member of pair
/// @param f_lhs        TPair instance on left hand side of comparison
/// @param f_rhs        TPair instance on right hand side of comparison
/// @return true if the left-hand-side is lexicographically smaller the
///         right-hand-side, else return false
//-------------------------------------------------------------------------
template <class FirstType, class SecondType>
bool operator<(const TPair<FirstType, SecondType>& f_lhs, const TPair<FirstType, SecondType>& f_rhs);

//-------------------------------------------------------------------------
/// returns true for (A > B) if TPair object A is greater than TPair object B.
/// @dspecref{1329}
/// @relatesalso TPair
/// @tparam FirstType   type of first member of pair
/// @tparam SecondType  type of second member of pair
/// @param f_lhs        TPair instance on left hand side of comparison
/// @param f_rhs        TPair instance on right hand side of comparison
/// @return true if the left-hand-side is lexicographically greater than the
///         right-hand-side, else return false
//-------------------------------------------------------------------------
template <class FirstType, class SecondType>
bool operator>(const TPair<FirstType, SecondType>& f_lhs, const TPair<FirstType, SecondType>& f_rhs);

//-------------------------------------------------------------------------
/// returns true for (A <= B) if TPair object A is less than or equal to TPair object B.
/// @dspecref{1329}
/// @relatesalso TPair
/// @tparam FirstType   type of first member of pair
/// @tparam SecondType  type of second member of pair
/// @param f_lhs        TPair instance on left hand side of comparison
/// @param f_rhs        TPair instance on right hand side of comparison
/// @return true if the left-hand-side is lexicographically less-or-equal
///         to the right-hand-side, else return false
//-------------------------------------------------------------------------
template <class FirstType, class SecondType>
bool operator<=(const TPair<FirstType, SecondType>& f_lhs, const TPair<FirstType, SecondType>& f_rhs);

//-------------------------------------------------------------------------
/// returns true for (A >= B) if TPair object A is greater than or equal to TPair object B.
/// @dspecref{1329}
/// @relatesalso TPair
/// @tparam FirstType   type of first member of pair
/// @tparam SecondType  type of second member of pair
/// @param f_lhs        TPair instance on left hand side of comparison
/// @param f_rhs        TPair instance on right hand side of comparison
/// @return true if the left-hand-side is lexicographically greater-or-equal
///         to the right-hand-side, else return false
//-------------------------------------------------------------------------
template <class FirstType, class SecondType>
bool operator>=(const TPair<FirstType, SecondType>& f_lhs, const TPair<FirstType, SecondType>& f_rhs);

//=========================================================================
//  TTriple
//-------------------------------------------------------------------------
/// a class that provides for the ability to treat three objects of
/// arbitrary types as a single object.
/// The template class stores a triple of objects of type FirstType,
/// SecondType and ThirdType, respectively.
/// The type first_type is the same as the template parameter FirstType and
/// the type second_type is the same as the template parameter SecondType
/// and so on.
/// Each specified type needs to supply only a default constructor,
/// a single-argument constructor, and a destructor.
///
/// The most common use for a TTriple is as return types for functions that
/// return three values.
///
/// @tparam FirstType   type of first  member of triple
/// @tparam SecondType  type of second member of triple
/// @tparam ThirdType   type of third  member of triple
///
/// @sa
/// - TPair
/// - TQuadruple
/// @ingroup vfc_group_core_types

//=========================================================================

template <class FirstType, class SecondType = FirstType, class ThirdType = FirstType>
class TTriple
{
  public:
    using tuple_type = TTriple<FirstType, SecondType, ThirdType>;

    using first_type  = FirstType;
    using second_type = SecondType;
    using third_type  = ThirdType;

  public:
    /// c'tor, initializes all members with their default c'tor
    /// @dspec{1646}     The SW component "vfc" shall create an empty triple instance where
    ///                  all entries are initialized with their respective default constructors.
    TTriple(void);

    ~TTriple() = default;

    /// c'tor, initializes members with specified values
    /// @dspec{1389}     The SW component "vfc" shall create a triple instance from three given
    ///                  values and return the created instance.
    /// @param f_first  instance of first_type
    /// @param f_second instance of second_type
    /// @param f_third  instance of third_type
    TTriple(const first_type& f_first, const second_type& f_second, const third_type& f_third);
    /// copy constructor
    /// @dspec{1390}     The SW component "vfc" shall create a triple where each entry is identical
    ///                  to the corresponding entry of a given triple.
    /// @param f_other  TTriple that serves as the source of the copy
    TTriple(const TTriple<FirstType, SecondType, ThirdType>& f_other);
    /// Copy Assignment Operator
    /// @dspec{1391}     The SW component "vfc" shall copy the contents of a given triple.
    /// @param f_other  TTriple that serves as the source of the assignment
    TTriple& operator=(const TTriple& f_other);

    /// returns the first object (read-only)
    /// @dspec{1392}     The SW component "vfc" shall access the contents of a given triple and
    ///                  return the value of the requested entry.
    /// @return const reference to the first item of type first_type
    const first_type& first(void) const { return m_first; }
    /// returns the second object (read-only)
    /// @dspecref{1392}
    /// @return const reference to the second item of type second_type
    const second_type& second(void) const { return m_second; }
    /// returns the third object (read-only)
    /// @dspecref{1392}
    /// @return const reference to the third item of type third_type
    const third_type& third(void) const { return m_third; }

    /// returns the first object (read-write)
    /// @dspecref{1392}
    /// @return reference to the first item of type first_type
    first_type& first(void)
    {
        return m_first;
    } // PRQA S 4024 # Technical debt L2 id=00c1b5211e8bfce64b6d-e666fb38c2581e04
    /// returns the second object (read-write)
    /// @dspecref{1392}
    /// @return reference to the second item of type second_type
    second_type& second(void)
    {
        return m_second;
    } // PRQA S 4024 # Technical debt L2 id=00c176c38bf6879345ef-1183364d7bd3ef58
    /// returns the third object (read-write)
    /// @dspecref{1392}
    /// @return reference to the third item of type third_type
    third_type& third(void)
    {
        return m_third;
    } // PRQA S 4024 # Technical debt L2 id=00c1a341f326d9874e90-f79c2d9b610eda3e

  private:
    first_type  m_first;  /// first object
    second_type m_second; /// second object
    third_type  m_third;  /// third object
};

// creation functions

//-------------------------------------------------------------------------
/// A template helper function used to construct objects of type TTriple<T1,T2,T3>,
/// where the component types are based on the data types passed as parameters.
/// @dspecref{1389}
/// @relatesalso TTriple
/// @tparam FirstType   type of first member of triple
/// @tparam SecondType  type of second member of triple
/// @tparam ThirdType   type of third member of triple
/// @param f_first      instance of FirstType
/// @param f_second     instance of SecondType
/// @param f_third      instance of ThirdType
/// @return TTriple instance with the specified entries
//-------------------------------------------------------------------------
template <class FirstType, class SecondType, class ThirdType>
TTriple<FirstType, SecondType, ThirdType>
make_triple(const FirstType& f_first, const SecondType& f_second, const ThirdType& f_third);

//-------------------------------------------------------------------------
/// A template helper function used to construct objects of type TTriple<T1,T2,T3>,
/// where the component types are based on the data types passed as parameters.
/// @dspecref{1389}
/// @relatesalso TTriple
/// @tparam FirstType   type of first member of triple
/// @tparam SecondType  type of second member of triple
/// @tparam ThirdType   type of third member of triple
/// @param f_first      instance of FirstType
/// @param f_second     instance of SecondType
/// @param f_third      instance of ThirdType
/// @return TTriple instance with the specified entries
//-------------------------------------------------------------------------
template <class FirstType, class SecondType, class ThirdType>
TTriple<FirstType, SecondType, ThirdType>
make_tuple(const FirstType& f_first, const SecondType& f_second, const ThirdType& f_third);

// template comparison operators

//-------------------------------------------------------------------------
/// returns true if two TTriple objects are identical.
/// @dspec{1393}     The SW component "vfc" shall return the boolean value "true" if two
///                  given triple instances are equivalent otherwise it shall return the
///                  boolean value "false".
/// @relatesalso TTriple
/// @tparam FirstType   type of first member of triple
/// @tparam SecondType  type of second member of triple
/// @tparam ThirdType   type of third member of triple
/// @param f_lhs        TTriple instance on left hand side of comparison
/// @param f_rhs        TTriple instance on right hand side of comparison
/// @return true if the given instances are identical, else return false
//-------------------------------------------------------------------------
template <class FirstType, class SecondType, class ThirdType>
bool operator==(
    const TTriple<FirstType, SecondType, ThirdType>& f_lhs,
    const TTriple<FirstType, SecondType, ThirdType>& f_rhs);

//-------------------------------------------------------------------------
/// returns true if two TTriple objects are not identical.
/// @dspec{1394}     The SW component "vfc" shall shall return the boolean value "true" if
///                  two given triple instances are NOT equivalent otherwise it shall return
///                  the boolean value "false".
/// @relatesalso TTriple
/// @tparam FirstType   type of first member of triple
/// @tparam SecondType  type of second member of triple
/// @tparam ThirdType   type of third member of triple
/// @param f_lhs        TTriple instance on left hand side of comparison
/// @param f_rhs        TTriple instance on right hand side of comparison
/// @return true if the given instances are different, else return false
//-------------------------------------------------------------------------
template <class FirstType, class SecondType, class ThirdType>
bool operator!=(
    const TTriple<FirstType, SecondType, ThirdType>& f_lhs,
    const TTriple<FirstType, SecondType, ThirdType>& f_rhs);

//-------------------------------------------------------------------------
/// returns true for (A < B) if TTriple object A is less than TTriple object B.
/// @dspec{1395}     The SW component "vfc" shall compare two given triples lexicographically
///                  and check if the first given triple is smaller-than (<), larger-than (>),
///                  smaller-or-equal (<=), or larger-or-equal (>=) than the other given triple
///                  and return the boolean value "true" if the checked relation holds otherwise
///                  it shall return the boolean value "false".
/// @relatesalso TTriple
/// @tparam FirstType   type of first member of triple
/// @tparam SecondType  type of second member of triple
/// @tparam ThirdType   type of third member of triple
/// @param f_lhs        TTriple instance on left hand side of comparison
/// @param f_rhs        TTriple instance on right hand side of comparison
/// @return true if the left-hand-side is lexicographically smaller the
///         right-hand-side, else return false
//-------------------------------------------------------------------------
template <class FirstType, class SecondType, class ThirdType>
bool operator<(
    const TTriple<FirstType, SecondType, ThirdType>& f_lhs,
    const TTriple<FirstType, SecondType, ThirdType>& f_rhs);

//-------------------------------------------------------------------------
/// returns true for (A > B) if TTriple object A is greater than TTriple object B.
/// @dspecref{1395}
/// @relatesalso TTriple
/// @tparam FirstType   type of first member of triple
/// @tparam SecondType  type of second member of triple
/// @tparam ThirdType   type of third member of triple
/// @param f_lhs        TTriple instance on left hand side of comparison
/// @param f_rhs        TTriple instance on right hand side of comparison
/// @return true if the left-hand-side is lexicographically greater than the
///         right-hand-side, else return false
//-------------------------------------------------------------------------
template <class FirstType, class SecondType, class ThirdType>
bool operator>(
    const TTriple<FirstType, SecondType, ThirdType>& f_lhs,
    const TTriple<FirstType, SecondType, ThirdType>& f_rhs);

//-------------------------------------------------------------------------
/// returns true for (A <= B) if TTriple object A is less than or equal to TTriple object B.
/// @dspecref{1395}
/// @relatesalso TTriple
/// @tparam FirstType   type of first member of triple
/// @tparam SecondType  type of second member of triple
/// @tparam ThirdType   type of third member of triple
/// @param f_lhs        TTriple instance on left hand side of comparison
/// @param f_rhs        TTriple instance on right hand side of comparison
/// @return true if the left-hand-side is lexicographically less-or-equal
///         to the right-hand-side, else return false
//-------------------------------------------------------------------------
template <class FirstType, class SecondType, class ThirdType>
bool operator<=(
    const TTriple<FirstType, SecondType, ThirdType>& f_lhs,
    const TTriple<FirstType, SecondType, ThirdType>& f_rhs);

//-------------------------------------------------------------------------
/// returns true for (A >= B) if TTriple object A is greater than or equal to TTriple object B.
/// @dspecref{1395}
/// @relatesalso TTriple
/// @tparam FirstType   type of first member of triple
/// @tparam SecondType  type of second member of triple
/// @tparam ThirdType   type of third member of triple
/// @param f_lhs        TTriple instance on left hand side of comparison
/// @param f_rhs        TTriple instance on right hand side of comparison
/// @return true if the left-hand-side is lexicographically greater-or-equal
///         to the right-hand-side, else return false
//-------------------------------------------------------------------------
template <class FirstType, class SecondType, class ThirdType>
bool operator>=(
    const TTriple<FirstType, SecondType, ThirdType>& f_lhs,
    const TTriple<FirstType, SecondType, ThirdType>& f_rhs);

//=========================================================================
//  TQuadruple
//-------------------------------------------------------------------------
/// a class that provides for the ability to treat four objects of
/// arbitrary types as a single object.
/// The template class stores a quadruple of objects of type FirstType,
/// SecondType, ThirdType and FourthType, respectively.
/// The type first_type is the same as the template parameter FirstType and
/// the type second_type is the same as the template parameter SecondType
/// and so on.
/// Each specified type needs to supply only a default constructor,
/// a single-argument constructor, and a destructor.
///
/// The most common use for a TQuadruple is as return types for functions that
/// return four values.
///
/// @tparam FirstType   type of first  member of quadruple
/// @tparam SecondType  type of second member of quadruple
/// @tparam ThirdType   type of third  member of quadruple
/// @tparam FourthType  type of fourth member of quadruple
///
/// @sa
/// - TPair
/// - TTriple
/// @ingroup vfc_group_core_types

//=========================================================================

template <class FirstType, class SecondType = FirstType, class ThirdType = FirstType, class FourthType = FirstType>
class TQuadruple
{
  public:
    using tuple_type = TQuadruple<FirstType, SecondType, ThirdType, FourthType>;

    using first_type  = FirstType;
    using second_type = SecondType;
    using third_type  = ThirdType;
    using fourth_type = FourthType;

  public:
    /// c'tor, initializes all members with their default c'tor
    /// @dspec{1648}     The SW component "vfc" shall create an empty quadruple where all
    ///                  entries are initialized with their respective default constructors.
    TQuadruple(void);

    ~TQuadruple() = default;

    /// c'tor, initializes members with specified values
    /// @dspec{1405}     The SW component "vfc" shall create a quadruple instance from four
    ///                  given values and return the created quadruple.
    /// @param f_first  instance of first_type
    /// @param f_second instance of second_type
    /// @param f_third  instance of third_type
    /// @param f_fourth instance of fourth_type
    TQuadruple(
        const first_type&  f_first,
        const second_type& f_second,
        const third_type&  f_third,
        const fourth_type& f_fourth);
    /// copy constructor
    /// @dspec{1406}     The SW component "vfc" shall create a quadruple by copying contents
    ///                  of a given quadruple.
    /// @param f_other  TQuadruple that serves as the source of the copy
    TQuadruple(const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_other);
    /// Copy Assignment Operator
    /// @dspec{1407}     The SW component "vfc" shall copy contents of a given quadruple.
    /// @param f_other  TTriple that serves as the source of the assignment
    TQuadruple& operator=(const TQuadruple& f_other);

    /// returns the first object (read-only)
    /// @dspec{1408}     The SW component "vfc" shall access contents of a given quadruple
    ///                  and return the value of the requested entry.
    /// @return const reference to the first item of type first_type
    const first_type& first(void) const { return m_first; }
    /// returns the second object (read-only)
    /// @dspecref{1408}
    /// @return const reference to the second item of type second_type
    const second_type& second(void) const { return m_second; }
    /// returns the third object (read-only)
    /// @dspecref{1408}
    /// @return const reference to the third item of type third_type
    const third_type& third(void) const { return m_third; }
    /// returns the fourth object (read-only)
    /// @dspecref{1408}
    /// @return const reference to the fourth item of type fourth_type
    const fourth_type& fourth(void) const { return m_fourth; }

    /// returns the first object (read-write)
    /// @dspecref{1408}
    /// @return reference to the first item of type first_type
    first_type& first(void)
    {
        return m_first;
    } // PRQA S 4024 # Technical debt L2 id=00c1f0eeb5e36a2b4bfa-e666fb38c2581e04
    /// returns the second object (read-write)
    /// @dspecref{1408}
    /// @return reference to the second item of type second_type
    second_type& second(void)
    {
        return m_second;
    } // PRQA S 4024 # Technical debt L2 id=00c14f7d45a63bdd4d22-1183364d7bd3ef58
    /// returns the third object (read-write)
    /// @dspecref{1408}
    /// @return reference to the third item of type third_type
    third_type& third(void)
    {
        return m_third;
    } // PRQA S 4024 # Technical debt L2 id=00c195459ec7f53746e0-f79c2d9b610eda3e
    /// returns the fourth object (read-write)
    /// @dspecref{1408}
    /// @return reference to the fourth item of type fourth_type
    fourth_type& fourth(void)
    {
        return m_fourth;
    } // PRQA S 4024 # Technical debt L2 id=00c1cfdc83adbb234f67-357aad13cbec2e25

  private:
    first_type  m_first;  /// first object
    second_type m_second; /// second object
    third_type  m_third;  /// third object
    fourth_type m_fourth; /// fourth object
};

// creation functions

//-------------------------------------------------------------------------
/// A template helper function used to construct objects of type TQuadruple<T1,T2,T3,T4>,
/// where the component types are based on the data types passed as parameters.
/// @dspecref{1405}
/// @relatesalso TQuadruple
/// @tparam FirstType   type of first  member of quadruple
/// @tparam SecondType  type of second member of quadruple
/// @tparam ThirdType   type of third  member of quadruple
/// @tparam FourthType  type of fourth member of quadruple
/// @param f_first      instance of FirstType
/// @param f_second     instance of SecondType
/// @param f_third      instance of ThirdType
/// @param f_fourth     instance of FourthType
/// @return TQuadruple instance with the specified entries
//-------------------------------------------------------------------------
template <class FirstType, class SecondType, class ThirdType, class FourthType>
TQuadruple<FirstType, SecondType, ThirdType, FourthType> make_quadruple(
    const FirstType&  f_first,
    const SecondType& f_second,
    const ThirdType&  f_third,
    const FourthType& f_fourth);

//-------------------------------------------------------------------------
/// A template helper function used to construct objects of type TQuadruple<T1,T2,T3,T4>,
/// where the component types are based on the data types passed as parameters.
/// @dspecref{1405}
/// @relatesalso TQuadruple
/// @tparam FirstType   type of first  member of quadruple
/// @tparam SecondType  type of second member of quadruple
/// @tparam ThirdType   type of third  member of quadruple
/// @tparam FourthType  type of fourth member of quadruple
/// @return TQuadruple instance with the specified entries
//-------------------------------------------------------------------------
template <class FirstType, class SecondType, class ThirdType, class FourthType>
TQuadruple<FirstType, SecondType, ThirdType, FourthType>
make_tuple(const FirstType& f_first, const SecondType& f_second, const ThirdType& f_third, const FourthType& f_fourth);

// template comparison operators

//-------------------------------------------------------------------------
/// returns true if two TQuadruple objects are identical.
/// @dspec{1409}     The SW component "vfc" shall return the boolean value "true" if
///                  two given quadruple are equivalent otherwise it shall return the
///                  boolean value "false".
/// @relatesalso TQuadruple
/// @tparam FirstType   type of first  member of quadruple
/// @tparam SecondType  type of second member of quadruple
/// @tparam ThirdType   type of third  member of quadruple
/// @tparam FourthType  type of fourth member of quadruple
/// @param f_lhs        TQuadruple instance on left hand side of comparison
/// @param f_rhs        TQuadruple instance on right hand side of comparison
/// @return true if the given instances are identical, else return false
//-------------------------------------------------------------------------
template <class FirstType, class SecondType, class ThirdType, class FourthType>
bool operator==(
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_lhs,
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_rhs);

//-------------------------------------------------------------------------
/// returns true if two TQuadruple objects are not identical.
/// @dspec{1410}     The SW component "vfc" shall return the boolean value "true" if two
///                  given quadruples are NOT equivalent otherwise it shall return the boolean
///                  value "false".
/// @relatesalso TQuadruple
/// @tparam FirstType   type of first  member of quadruple
/// @tparam SecondType  type of second member of quadruple
/// @tparam ThirdType   type of third  member of quadruple
/// @tparam FourthType  type of fourth member of quadruple
/// @param f_lhs        TQuadruple instance on left hand side of comparison
/// @param f_rhs        TQuadruple instance on right hand side of comparison
/// @return true if the given instances are different, else return false
//-------------------------------------------------------------------------
template <class FirstType, class SecondType, class ThirdType, class FourthType>
bool operator!=(
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_lhs,
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_rhs);

//-------------------------------------------------------------------------
/// returns true for (A < B) if TQuadruple object A is less than TQuadruple object B.
/// @dspec{1411}     The SW component "vfc" shall compare two given quadruples lexicographically
///                  and check if the first given quadruple is smaller than, larger than, smaller
///                  or equal, or larger or equan than the other given quadruple and return the
///                  boolean value "true" if the checked relation holds otherwise if shall return
///                  the boolean value "false".
/// @relatesalso TQuadruple
/// @tparam FirstType   type of first  member of quadruple
/// @tparam SecondType  type of second member of quadruple
/// @tparam ThirdType   type of third  member of quadruple
/// @tparam FourthType  type of fourth member of quadruple
/// @return true if the left-hand-side is lexicographically smaller then the
///         right-hand-side, else return false
//-------------------------------------------------------------------------
template <class FirstType, class SecondType, class ThirdType, class FourthType>
bool operator<(
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_lhs,
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_rhs);

//-------------------------------------------------------------------------
/// returns true for (A > B) if TQuadruple object A is greater than TQuadruple object B.
/// @dspecref{1411}
/// @relatesalso TQuadruple
/// @tparam FirstType   type of first  member of quadruple
/// @tparam SecondType  type of second member of quadruple
/// @tparam ThirdType   type of third  member of quadruple
/// @tparam FourthType  type of fourth member of quadruple
/// @return true if the left-hand-side is lexicographically greater than the
///         right-hand-side, else return false
//-------------------------------------------------------------------------
template <class FirstType, class SecondType, class ThirdType, class FourthType>
bool operator>(
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_lhs,
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_rhs);

//-------------------------------------------------------------------------
/// returns true for (A <= B) if TQuadruple object A is less than or equal to TQuadruple object B.
/// @dspecref{1411}
/// @relatesalso TQuadruple
/// @tparam FirstType   type of first  member of quadruple
/// @tparam SecondType  type of second member of quadruple
/// @tparam ThirdType   type of third  member of quadruple
/// @tparam FourthType  type of fourth member of quadruple
/// @return true if the left-hand-side is lexicographically less-or-equal
///         to the right-hand-side, else return false
//-------------------------------------------------------------------------
template <class FirstType, class SecondType, class ThirdType, class FourthType>
bool operator<=(
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_lhs,
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_rhs);

//-------------------------------------------------------------------------
/// returns true for (A >= B) if TQuadruple object A is greater than or equal to TQuadruple object B.
/// @dspecref{1411}
/// @relatesalso TQuadruple
/// @tparam FirstType   type of first  member of quadruple
/// @tparam SecondType  type of second member of quadruple
/// @tparam ThirdType   type of third  member of quadruple
/// @tparam FourthType  type of fourth member of quadruple
/// @return true if the left-hand-side is lexicographically greater-or-equal
///         to the right-hand-side, else return false
//-------------------------------------------------------------------------
template <class FirstType, class SecondType, class ThirdType, class FourthType>
bool operator>=(
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_lhs,
    const TQuadruple<FirstType, SecondType, ThirdType, FourthType>& f_rhs);

} // namespace vfc

#include "vfc/core/vfc_tuple.inl"

#endif // VFC_TUPLE_HPP_INCLUDED

//=============================================================================

