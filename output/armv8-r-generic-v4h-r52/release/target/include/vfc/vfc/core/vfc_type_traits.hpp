//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_TYPE_TRAITS_HPP_INCLUDED
#define ZX_VFC_TYPE_TRAITS_HPP_INCLUDED

#include "vfc/core/vfc_types.hpp"
#include "vfc/core/vfc_static_assert.hpp"
#include "vfc/core/vfc_builtin_types.hpp"
#include "vfc/core/vfc_metaprog.hpp" // TRemoveCV

namespace vfc
{ // namespace vfc opened

/// @brief VFC type traits.
/// @defgroup vfc_group_core_generic_typetraits Type Traits
/// @ingroup vfc_group_core_generic
///
/// Generic programming
/// -------------------
/// -- that is, writing code that works with any data type meeting a set of requirements -- has
/// become the method of choice for delivering reusable code.
///
/// However, there are times in generic programming when generic just isn't good enough -- sometimes the differences
/// between types are too great for an efficient generic implementation.
///
/// **This is when the traits technique becomes important.**
///
/// By encapsulating those properties that need to be considered on a type-by-type basis inside a traits class, you
/// can minimize the amount of code that has to differ from one type to another, and maximize the amount of generic
/// code.
///
/// For example, when working with character strings, one common operation is to determine the length of a
/// null-terminated string. Clearly, it's possible to write generic code that can do this, but it turns out that
/// there are much more efficient methods available. The C library functions `strlen` and `wcslen`, for instance,
/// are usually written in assembler, and with suitable hardware support can be considerably faster than a generic
/// version written in C++. The authors of the C++ Standard Library realized this, and abstracted the properties of
/// `char` and `wchar_t` into the class `char_ traits`. Generic code that works with character strings can simply
/// use `char_traits<>::length` to determine the length of a null-terminated string, safe in the knowledge that
/// specializations of char_traits will use the most appropriate method available to them.
///
/// Type Traits
/// -----------
///
/// Class `char_traits` is a classic example of a collection of type-specific properties wrapped up in a single
/// class -- what Nathan Myers terms a "baggage class" (see "Traits," by Nathan Myers, C++ Report, June 1995). In
/// the vfc type-traits module we have written a set of very specific traits classes, each of which encapsulate a
/// single trait from the C++ type system. For example, is a type a pointer or a reference type, or does a type have
/// a trivial constructor, or a const qualifier? The type-traits classes share a unified design. Each class has a
/// single member called `value` -- a compile-time constant that is true if the type has the specified property, and
/// false otherwise. These classes can be used in generic programming to determine the properties of a given type
/// and introduce optimizations that are appropriate for that case.
///
/// Implementation
/// --------------
///
/// There are far too many separate classes contained in the type-traits library to give a full implementation here
/// (see the source code in the vfc library for full details). However, in this article we will give you a flavor of
/// a typical implementation of a 'trait'. Beginning with possibly the simplest example, below is a class `TIsVoid`
/// providing `value` as a compile-time constant, which is true if the template argument is `void`. Note: the real
/// implementation is different from this example!
///
/// ```
/// template <class T>
/// struct TIsVoid
/// {
///     enum : bool { value = false };
/// };
///
/// template <>
/// struct TIsVoid<void>
/// {
///     enum : bool { value = true };
/// };
/// ```
///
/// Here the primary template of the class `TIsVoid` defines `value` as `false`, but a full specialization when `T`
/// is `void` defines it as `true`. While full specialization of a template class is an important technique, you
/// sometimes need a solution that is halfway between a fully generic solution, and a full specialization. This is
/// exactly the situation for which the standards committee defined partial template-class specialization. To
/// illustrate, consider the class `TIsPointer`. Here we needed a primary version that handles all the cases where
/// `T` is not a pointer, and a partial specialization to handle all the cases where `T` is a pointer of any type.
///
/// Optimized copy
/// --------------
///
/// As an **example** of how the type-traits classes can be used, consider the standard library algorithm copy:
///
/// ```
/// template< typename Iter1, typename Iter2 >
/// Iter2 copy(Iter1 first, Iter1 last, Iter2 out);
/// ```
///
/// Obviously, there's no problem writing a generic version of copy that works for all iterator types Iter1 and
/// Iter2; however, there are some circumstances when the copy operation can best be performed by a call to
/// `memcpy`.
///
/// To implement `copy` in terms of `memcpy`, all of the following conditions need to be met:
///
/// - Both of the iterator types Iter1 and Iter2 must be pointers.
/// - Both Iter1 and Iter2 must point to the same type, excluding const and volatile qualifiers.
/// - The type pointed to by Iter1 must have a trivial assignment operator. By "trivial assignment operator," we
///   mean that the type is either a scalar type (that is, an arithmetic type, enumeration type, pointer, pointer to
///   member, or const- or volatile-qualified version of one of these types) or:
/// - The type has no user-defined assignment operator.
/// - The type does not have any data members that are references.
/// - All base classes, and all data member objects must have trivial assignment operators.
///
/// If all these conditions are met, then a type can be copied using memcpy rather than using a compiler-generated
/// assignment operator.
///
/// The type-traits library provides a class `THasTrivialCopy`, such that `THasTrivialCopy::value` is true only if
/// `T` has a trivial assignment operator. This class "just works" for scalar types, but has to be explicitly
/// specialized for class/struct types that also happen to have a trivial assignment operator. In other words, if
/// `THasTrivialCopy` gives the wrong answer, it will give the safe wrong answer -- that trivial assignment is not
/// allowable.
//  Doxygen defgroup vfc_group_core_generic_typetraits:
/// @{

//=========================================================================
// TIsVoid<>
/// Is the argument `void` (possibly CV qualified)?
/// @tparam TType Type argument of trait.
template <typename TType>
struct TIsVoid : TIsSameType<TType, void>::type
{
};

//=========================================================================
//  TIsC99Integral<>
/// Is the argument an integral type according to C99 standard?
/// @tparam TType Type argument of trait.
template <typename TType>
struct TIsC99Integral : TContainsType<
                            typename TRemoveCV<TType>::type,
                            int8_t,
                            int16_t,
                            int32_t,
                            uint8_t,
                            uint16_t,
                            uint32_t,
                            int64_t,
                            uint64_t>::type
{
};

//=========================================================================
//  TIsCPPIntegral<>
/// Is the argument an integral type according to C++11 standard?
/// @tparam TType Type argument of trait.
template <typename TType>
struct TIsCPPIntegral : TContainsType<
                            typename TRemoveCV<TType>::type,
                            bool,
                            wchar_t, // PRQA S 2440 # Technical debt L2 id=00c1f5920c76fdcb4223-2f5b30af9aa1b7a3
                            built_in::char_t,
                            built_in::schar_t,
                            built_in::short_t,
                            built_in::int_t,
                            built_in::long_t,
                            built_in::longlong_t,
                            built_in::uchar_t,
                            built_in::ushort_t,
                            built_in::uint_t,
                            built_in::ulong_t,
                            built_in::ulonglong_t>::type
{
};

//=========================================================================
//  TIsIntegral<>
/// Is the argument an integral type? Note, contains also bool, char and wchar_t.
/// @tparam TType Type argument of trait.
template <class TType>
struct TIsIntegral : TOr<TIsCPPIntegral<TType>, TIsC99Integral<TType>>::type
{
};

//=========================================================================
//  TIsFloating<>
/// Is the argument an floating point type?
/// @note There are specializations in `vfc_siunits.hpp` and `vfc_trig.hpp`!
/// @tparam TType Type argument of trait.
template <typename TType>
struct TIsFloating
    : TContainsType<typename TRemoveCV<TType>::type, built_in::float_t, built_in::double_t, built_in::longdouble_t>::
          type
{
};

//=========================================================================
//  TIsValidFloating<>
/// Is the argument an floating point type, which is legal to use in our software?
/// Depending on the hardware and compiler qualifications some C++ standard types may not be supported by the VFC.
/// With TIsValidFloating generic template functions and classes may check if the given type may be used.
/// @note There are specializations in `vfc_siunits.hpp` and `vfc_trig.hpp`!
/// @tparam TType Type argument of trait.
template <typename TType>
struct TIsValidFloating : TContainsType<
                              typename TRemoveCV<TType>::type,
                              built_in::float_t
#ifdef VFC_ENABLE_FLOAT64_TYPE
                              ,
                              built_in::double_t
#endif // VFC_ENABLE_FLOAT64_TYPE
                              >::type
{
};

//=========================================================================
//  TIsValidFloatingForFunction<>
/// Check if the floating type is legal for use with std functions.
/// Depending on compiler qualifications not all overloads of some C++ standard functions may be certified to use.
/// With TIsValidFloatingForFunction generic template functions and classes may check if the given type may be used.
/// @tparam TType Type argument of trait.
template <typename TType>
struct TIsValidFloatingForFunction : TContainsType<
                                         typename TRemoveCV<TType>::type,
                                         built_in::float_t
#ifdef VFC_ENABLE_FLOAT64_TYPE
                                         /// Currently only float32_t functions are qualified sufficiently.
                                         /// `float64_t` functions are only valid with VFC_ENABLE_FLOAT64 is enabled.
                                         ,
                                         built_in::double_t
#endif // VFC_ENABLE_QM_MATH
                                         >::type
{
};

//=========================================================================
//  TIsArithmetic<> = Floating || Integral
/// Is the argument an arithmetic type, so either floating or integral?
/// @tparam TType Type argument of trait.
template <typename TType>
struct TIsArithmetic : TOr<TIsFloating<TType>, TIsIntegral<TType>>::type
{
};

//=========================================================================
//  TIsValidArithmetic<>
/// Check if the type is a legal floating type or a integral type.
/// This trait can be used to check if a generic function can perform arithmetic operations using this type.
/// @note There are specializations in `vfc_siunits.hpp`!
/// @tparam TType Type argument of trait.
template <typename TType>
struct TIsValidArithmetic : TOr<TIsValidFloating<TType>, TIsIntegral<TType>>::type
{
};

//=========================================================================
//  TIsCPPUnsignedArithmetic<>
/// Is the argument an unsigned arithmetic type in C++11?
/// @tparam TType Type argument of trait.
template <typename TType>
struct TIsCPPUnsignedArithmetic : TContainsType<
                                      typename TRemoveCV<TType>::type,
                                      built_in::uchar_t,
                                      built_in::ushort_t,
                                      built_in::uint_t,
                                      built_in::ulong_t,
                                      built_in::ulonglong_t>::type
{
};

//=========================================================================
//  TIsC99UnsignedArithmetic<>
/// Is the argument an unsigned arithmetic type in C99?
/// @tparam TType Type argument of trait.
template <typename TType>
struct TIsC99UnsignedArithmetic
    : TContainsType<typename TRemoveCV<TType>::type, uint8_t, uint16_t, uint32_t, uint64_t>::type
{
};

//=========================================================================
//  TIsUnsignedArithmetic<>
/// Is the argument an unsigned arithmetic type?
/// @tparam TType Type argument of trait.
template <typename TType>
struct TIsUnsignedArithmetic : TOr<TIsCPPUnsignedArithmetic<TType>, TIsC99UnsignedArithmetic<TType>>::type
{
};

//=========================================================================
//  TIsFundamental<>  = Arithmetic || void
/// Is the argument a fundamental type?
/// @note There are specializations in `vfc_siunits.hpp`!
/// @tparam TType Type argument of trait.
template <typename TType>
struct TIsFundamental : TOr<TIsArithmetic<TType>, TIsVoid<TType>>::type
{
};

//=========================================================================
//  TIsPointer<>
namespace internal
{
template <typename TType>
struct TIsPointer : false_t
{
};
template <typename TType>
struct TIsPointer<TType*> : true_t
{
};
} // namespace internal

/// Is the argument a pointer?
/// @tparam TType Type argument of trait.
template <typename TType>
struct TIsPointer : internal::TIsPointer<typename TRemoveCV<TType>::type>::type
{
};

//=========================================================================
//  TIsBoolType<>
/// True iff template argument is 'bool'.
/// @tparam TType Type argument of trait.
template <typename TType>
struct TIsBoolType : TIsSameType<TType, bool>::type
{
};

//=========================================================================
//  TIsInteger<>
/// True if template argument is any integral type (@sa TIsIntegral) but not 'bool' (@sa TIsBoolType).
/// @tparam TType Type argument of trait.
template <typename TType>
struct TIsInteger : bool_constant_t<TIsIntegral<TType>::value && !TIsBoolType<TType>::value>::type
{
};

//=========================================================================
//  TIsPOD<>  = Fundamental || Pointer
/// Is the argument a plain-old-datatype (POD)?
/// @note There are specializations in `vfc_trig.hpp`!
/// @tparam TType Type argument of trait.
template <typename TType>
struct TIsPOD : TOr<TIsFundamental<TType>, TIsPointer<TType>>::type
{
};

//=========================================================================
//  THasTrivialCTor<>
/// Has the type a trivial constructor, not involving code execution?
/// @note Distributed specializations for user types exist.
/// @tparam TType Type argument of trait.
template <typename TType>
struct THasTrivialCTor : TIsPOD<TType>::type
{
};

//=========================================================================
//  THasTrivialDTor<>
/// Has the type a trivial destructor, not involving code execution?
/// @note Distributed specializations for user types exist.
/// @tparam TType Type argument of trait.
template <typename TType>
struct THasTrivialDTor : TIsPOD<TType>::type
{
};

//=========================================================================
//  THasTrivialCopy<>   = memcpy succeeds
/// Has the type a trivial copy constructor? That means it is possible to `memcpy` the object.
/// @note Distributed specializations for user types exist.
/// @tparam TType Type argument of trait.
template <typename TType>
struct THasTrivialCopy : TIsPOD<TType>::type
{
};

//=========================================================================
//  THasTrivialSetZero<>   = memset(0) succeeds
/// Is the type compatible with a `memset(0)` on its data?
/// @note Distributed specializations for user types exist.
/// @tparam TType Type argument of trait.
template <typename TType>
struct THasTrivialSetZero : TIsPOD<TType>::type
{
};

//=========================================================================
//  THasTrivialSet<>   = memset(val) succeeds
/// Is the type compatible with a `memset(val)` of some value 'val' on its data?
/// @tparam TType Type argument of trait.
template <typename TType>
struct THasTrivialSet
    : TContainsType<
          TType,
          built_in::char_t,
          built_in::schar_t,
          built_in::uchar_t,
          built_in::char_t volatile,  // PRQA S 5219 # Technical debt L2 id=00c1f9c91387ce2d47bd-d9308c600592c868
          built_in::schar_t volatile, // PRQA S 5219 # Technical debt L2 id=00c17bc6c5f19342481d-1cb5062e2810066d
          built_in::uchar_t volatile  // PRQA S 5219 # Technical debt L2 id=00c11e925863d339404a-7c4cca02a370b9bd
          >::type
{
};

//=========================================================================
//  TIsArray<>
/// Is the type a c-style array?
/// @tparam TType Type argument of trait.
template <class ValueType>
struct TIsArray : false_t
{
};

/// Specialization for bounded arrays.
///@tparam ValueType Type of value.
///@tparam SizeValue Size of array of given ValueType.
template <class ValueType, vfc::int32_t SizeValue>
struct TIsArray<ValueType[SizeValue]> : true_t
{
};

/// Specialization for unbounded arrays.
///@tparam ValueType Type of value.
template <class ValueType>
struct TIsArray<ValueType[]> : true_t
{
};

//=========================================================================
//  TIsSIUnitType
/// Trait to identify SIUnit types.
/// Specialization is in `vfc_siunits.hpp`.
/// @param ValueType Type to be checked if it is a SIUnit type.
/// @ingroup vfc_group_core_types
template <class ValueType>
struct TIsSIUnitType : false_t
{
};

//=========================================================================
//  TAlignmentOf<>
/// Traits for computing memory alignment restrictions for specified type T.
/// For an in-depth documentation see @ref page_core_memory_alignment.
/// @tparam TType Type for which the memory alignment should be determined.
template <class TType>
struct TAlignmentOf : TIntegralConstant<size_t, alignof(TType)>
{
};

//=========================================================================
//  TRectAreaTypeTraits<>
/// Trait to identify the type promotion.
/// ValueType is used to pass the unit in case of siunit or simple POD.
/// @tparam ValueType Type for which the area has to be calculated.
/// @ingroup vfc_group_core_types
template <class ValueType>
struct TRectAreaTypeTraits
{
    static_assert(!vfc::TIsUnsignedArithmetic<ValueType>::value, "static assertion failed");
    using area_type = ValueType;
};

//=========================================================================
/// @} Doxygen defgroup vfc_group_core_generic_typetraits
} // namespace vfc
#endif // ZX_VFC_TYPE_TRAITS_HPP_INCLUDED

