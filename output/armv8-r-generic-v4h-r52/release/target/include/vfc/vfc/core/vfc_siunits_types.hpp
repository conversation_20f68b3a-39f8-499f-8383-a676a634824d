//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_SIUNITS_TYPES_HPP_INCLUDED
#define VFC_SIUNITS_TYPES_HPP_INCLUDED

#include "vfc/core/vfc_siunits.hpp"

namespace vfc
{

//=============================================================================
// Base quantity length
//=============================================================================
using info_nano_metre_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::NANO,
    1, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_micro_metre_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::MICRO,
    1, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_milli_metre_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::MILLI,
    1, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_centi_metre_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::CENTI,
    1, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_deci_metre_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::DECI,
    1, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_metre_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    1, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_kilo_metre_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::KILO,
    1, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_mega_metre_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::MEGA,
    1, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_giga_metre_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::ZXGIGA,
    1, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_tera_metre_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::TERA,
    1, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_per_metre_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    -1, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_per_metre_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    -1, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    -1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_per_square_metre_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    -2, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_per_square_metre_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    -2, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    -1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_square_metre_per_square_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    2, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    -2, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_metre_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    1, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

struct CMileType
{
    using si_base_unit_type = CLength;
    enum
    {
        CONVERSION_RATIONAL = 0,
        FLOATING_OFFSET     = 0,
        NUM                 = 201168,
        DEN                 = 125,
        OFFSET              = 0
    };

    //=============================================================================
    /// @tparam ValueType  the type holding metre information
    /// @param f_value_r  the value in metre to be converted to mile
    //=============================================================================
    template <class ValueType>
    inline static void doMultiply(ValueType& f_value_r)
    {
        f_value_r *= static_cast<ValueType>(1609.344);
    }

    //=============================================================================
    /// @tparam ValueType  the type holding mile information
    /// @param f_value_r  the value in miles to be converted to metre
    //=============================================================================
    template <class ValueType>
    inline static void doDivide(ValueType& f_value_r)
    {
        f_value_r /= static_cast<ValueType>(1609.344);
    }
};

using info_mile_t = vfc::TUnitInfoType<
    vfc::CMileType,
    vfc::BASE,
    1, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

//=============================================================================
// Base quantity area
//=============================================================================
using info_square_metre_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    2, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

//=============================================================================
// Base quantity volume
//=============================================================================
using info_cubic_metre_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    3, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

// KiliMtr 2
using info_square_kilo_metre_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::KILO,
    2, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

// KiliMtr 3
using info_cubic_kilo_metre_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::KILO,
    3, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

// cubic decimetre = litre
using info_cubic_deci_metre_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::DECI,
    3, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

// litre = dm ^ 3
using info_litre_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::DECI,
    3, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

//=============================================================================
// Base quantity time
//=============================================================================

using info_nano_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::NANO_SECOND,
    1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_micro_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::MICRO_SECOND,
    1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_milli_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::MILLI_SECOND,
    1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::SECOND,
    1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_square_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::SECOND,
    2, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_minute_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::MINUTE,
    1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_hour_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::HOUR,
    1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_day_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::DAY,
    1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_week_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::WEEK,
    1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_month_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::MONTH,
    1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_year_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::YEAR,
    1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

//=============================================================================
// Base quantity 1/time - hertz
//=============================================================================

using info_per_nano_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::NANO_SECOND,
    -1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_per_micro_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::MICRO_SECOND,
    -1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_per_milli_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::MILLI_SECOND,
    -1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_per_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::SECOND,
    -1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_per_square_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::SECOND,
    -2, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_per_minute_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::MINUTE,
    -1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_per_hour_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::HOUR,
    -1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_per_square_hour_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::HOUR,
    -2, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

//=============================================================================
// Base quantity angle
//=============================================================================
struct CSIDegreeType
{
    using si_base_unit_type = CAngle;

    enum
    {
        CONVERSION_RATIONAL = 0,
        FLOATING_OFFSET     = 0
    };

    // Good numerical approximation for Pi. The 355/113 approximation gives 7digits of precision which covers the
    // float32 type (3.141593). For float64 16digits are required, the value should be 3.141592653589793 (245850922 /
    // 78256779) Unfortunately 78256779 * 180 overflows, the smallest value possible is then 1725033 which gives only
    // 13digits of precision (5419351 / 1725033)
    static const vfc::int32_t NUM    = 355;
    static const vfc::int32_t DEN    = (113 * 180);
    static const vfc::int32_t OFFSET = 0;

    //=============================================================================
    /// @tparam ValueType  the type holding degree information
    /// @param f_value_r  the value in degree to be converted to radian
    //=============================================================================
    template <class ValueType>
    inline static void doMultiply(ValueType& f_value_r)
    {
        // degree to radian conversion of non float types is not supported due to extreme rounding errors
        VFC_STATIC_ASSERT(TIsFloating<ValueType>::value);
        f_value_r *= static_cast<ValueType>(G_DEG2RAD);
    }

    //=============================================================================
    /// @tparam ValueType  the type holding radian information
    /// @param f_value_r  the value in radian to be converted to metre
    //=============================================================================
    template <class ValueType>
    inline static void doDivide(ValueType& f_value_r)
    {
        // radian to degree conversion of non float types is not supported due to extreme rounding errors
        VFC_STATIC_ASSERT(TIsFloating<ValueType>::value);
        f_value_r /= static_cast<ValueType>(G_DEG2RAD);
    }
};

using info_radian_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    1, // Angle
    vfc::CBasicType>;

using info_degree_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CSIDegreeType,
    vfc::BASE,
    1, // Angle
    vfc::CBasicType>;

using info_degree_per_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    -1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CSIDegreeType,
    vfc::BASE,
    1, // Angle
    vfc::CBasicType>;

using info_square_radian_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    2, // Angle
    vfc::CBasicType>;

using info_per_radian_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    -1, // Angle
    vfc::CBasicType>;

using info_per_square_radian_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    -2, // Angle
    vfc::CBasicType>;

using info_radian_per_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    -1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    1, // Angle
    vfc::CBasicType>;

using info_radian_per_square_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    -2, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    1, // Angle
    vfc::CBasicType>;

using info_square_radian_per_square_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    -2, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    2, // Angle
    vfc::CBasicType>;

//=============================================================================
// Base quantity mass
//=============================================================================
using info_gram_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    1, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_kilo_gram_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::KILO,
    1, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_ton_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::MEGA,
    1, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_milli_gram_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::MILLI,
    1, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_micro_gram_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::MICRO,
    1, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

//=============================================================================
// Base quantity temperature
//=============================================================================
struct CCelsiusType
{
    using si_base_unit_type = CTemperature;

    enum
    {
        CONVERSION_RATIONAL = 1,
        FLOATING_OFFSET     = 0
    };

    static const vfc::int32_t NUM    = 1;
    static const vfc::int32_t DEN    = 1;
    static const vfc::int32_t OFFSET = 273;
};

using info_kelvin_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    1, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_celsius_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CCelsiusType,
    vfc::BASE,
    1, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

//=============================================================================
// Base quantity Ampere
//=============================================================================

using info_nano_ampere_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::NANO,
    1, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_micro_ampere_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::MICRO,
    1, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_milli_ampere_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::MILLI,
    1, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_centi_ampere_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::CENTI,
    1, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_deci_ampere_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::DECI,
    1, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_ampere_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    1, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_kilo_ampere_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::KILO,
    1, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_mega_ampere_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::MEGA,
    1, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_giga_ampere_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::ZXGIGA,
    1, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_tera_ampere_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::TERA,
    1, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_per_ampere_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    -1, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

//=============================================================================
// Base quantity Candela ( cd )
//=============================================================================

using info_nano_candela_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::NANO,
    1, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_micro_candela_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::MICRO,
    1, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_milli_candela_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::MILLI,
    1, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_centi_candela_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::CENTI,
    1, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_deci_candela_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::DECI,
    1, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_candela_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    1, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_kilo_candela_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::KILO,
    1, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_mega_candela_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::MEGA,
    1, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_giga_candela_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::ZXGIGA,
    1, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_tera_candela_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::TERA,
    1, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

// 1 / cd
using info_per_candela_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    -1, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

// Newton = mass * acceleration
// Newton = Kg * m/s2
using info_kilo_gram_metre_per_square_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    1, // Length
    vfc::CMassType,
    vfc::KILO,
    1, // Mass
    vfc::CTimeType,
    vfc::sitime::SECOND,
    -2, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

// Newton = mass * acceleration
// Newton = Kg * m/s2
using info_newton_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    1, // Length
    vfc::CMassType,
    vfc::KILO,
    1, // Mass
    vfc::CTimeType,
    vfc::sitime::SECOND,
    -2, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

// same as newton
using info_force_of_gravity_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    1, // Length
    vfc::CMassType,
    vfc::KILO,
    1, // Mass
    vfc::CTimeType,
    vfc::sitime::SECOND,
    -2, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

// Killo Newton = 10^3 Kg m/s2  = Mg.m/s2
using info_kilo_newton_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    1, // Length
    vfc::CMassType,
    vfc::MEGA,
    1, // Mass
    vfc::CTimeType,
    vfc::sitime::SECOND,
    -2, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

// joule ( KG * (m2/s2) ) == N M
using info_joule_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    2, // Length
    vfc::CMassType,
    vfc::KILO,
    1, // Mass
    vfc::CTimeType,
    vfc::sitime::SECOND,
    -2, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;
// J = Nm
using info_newton_metre_t = info_joule_t;

// watt = Joule/second = N m / s
// watt = Kg * m2 / s3
using info_watt_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    2, // Length
    vfc::CMassType,
    vfc::KILO,
    1, // Mass
    vfc::CTimeType,
    vfc::sitime::SECOND,
    -3, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

// kilowatt = kilo* Kg * m2 / s3
// kilowatt = Mg * m2 / s3
using info_kilo_watt_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    2, // Length
    vfc::CMassType,
    vfc::MEGA,
    1, // Mass
    vfc::CTimeType,
    vfc::sitime::SECOND,
    -3, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

// pascal=force/area= kg / (m s2)
using info_pascal_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    -1, // Length
    vfc::CMassType,
    vfc::KILO,
    1, // Mass
    vfc::CTimeType,
    vfc::BASE,
    -2, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

// 10^5Kg/(m.s^2) = 10^9g/(dm.s^2) = Bar
using info_bar_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::DECA,
    -1, // Length
    vfc::CMassType,
    vfc::ZXGIGA,
    1, // Mass
    vfc::CTimeType,
    vfc::BASE,
    -2, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

// bar = 10^9g/(dm.s^2)
// milli bar = 10^-3*bar = 10^6g/(dm.s^2)
using info_milli_bar_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::DECA,
    -1, // Length
    vfc::CMassType,
    vfc::MEGA,
    1, // Mass
    vfc::CTimeType,
    vfc::BASE,
    -2, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

// 1 lx = 1 lm*m-2 = 1 cd* sr*m-2
// sr = steradian
// The steradian is dimensionless because 1 sr = m2*m-2 = 1
// assumed steradian = 1
// http://en.wikipedia.org/wiki/Lux
// http://en.wikipedia.org/wiki/Steradian

using info_lux_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    -2, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    1, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_metre_per_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    1, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::SECOND,
    -1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_kilometre_per_hour_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::KILO,
    1, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::HOUR,
    -1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_miles_per_hour_t = vfc::TUnitInfoType<
    vfc::CMileType,
    vfc::BASE,
    1, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::HOUR,
    -1, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_metre_per_square_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    1, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::SECOND,
    -2, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_kilometre_per_square_hour_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::KILO,
    1, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::HOUR,
    -2, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

using info_metre_per_cubic_second_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    1, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::sitime::SECOND,
    -3, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

//=============================================================================
// Base quantity slope
//=============================================================================
using info_slope_t = vfc::TUnitInfoType<
    vfc::CLengthType,
    vfc::BASE,
    0, // Length
    vfc::CMassType,
    vfc::BASE,
    0, // Mass
    vfc::CTimeType,
    vfc::BASE,
    0, // Time
    vfc::CCurrentType,
    vfc::BASE,
    0, // Current
    vfc::CTemperatureType,
    vfc::BASE,
    0, // Temperature
    vfc::CAmountOfSubstanceType,
    vfc::BASE,
    0, // AmountOfSubstance
    vfc::CLuminousIntensityType,
    vfc::BASE,
    0, // LuminousIntensity
    vfc::CAngleType,
    vfc::BASE,
    0, // Angle
    vfc::CBasicType>;

// the word scalar should to be more intuitive for most developers
using info_scalar_t = info_slope_t;

//=============================================================================
// Base quantity pixel
//=============================================================================
using info_pixel_t = vfc::TRBInfoType<
    vfc::CPixelType,
    vfc::BASE,
    1, // Pixel
    vfc::CPercentageType,
    vfc::BASE,
    0, // Percentage
    vfc::CBasicType>;

using info_square_pixel_t = vfc::TRBInfoType<
    vfc::CPixelType,
    vfc::BASE,
    2, // Pixel
    vfc::CPercentageType,
    vfc::BASE,
    0, // Percentage
    vfc::CBasicType>;

using info_per_pixel_t = vfc::TRBInfoType<
    vfc::CPixelType,
    vfc::BASE,
    -1, // Pixel
    vfc::CPercentageType,
    vfc::BASE,
    0, // Percentage
    vfc::CBasicType>;

using info_per_square_pixel_t = vfc::TRBInfoType<
    vfc::CPixelType,
    vfc::BASE,
    -2, // Pixel
    vfc::CPercentageType,
    vfc::BASE,
    0, // Percentage
    vfc::CBasicType>;

using info_percentage_t = vfc::TRBInfoType<
    vfc::CPixelType,
    vfc::BASE,
    0, // Pixel
    vfc::CPercentageType,
    vfc::BASE,
    1, // Percentage
    vfc::CBasicType>;

} // namespace vfc

namespace vfc
{ // open namespace vfc

//============================================================
// SIUnits common typedefs
/// @tparam ValueType  the type used to store the SI unit information
/// @tparam UserType  custom type definable by the user
/// @tparam ConvertPolicyType  template template argument for converting different SIUnits
//============================================================

template <
    class ValueType,
    class UserType                                         = vfc::CDefaultType,
    template <class, class, class> class ConvertPolicyType = vfc::TConvert>
struct TSIUnitType
{

    ///////////////////////////////////////////////
    ///////////////// length /////////////////////
    ///////////////////////////////////////////////

    // power = 1

    using si_metre_t = vfc::TSIUnits<ValueType, info_metre_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_kilo_metre_t = vfc::TSIUnits<ValueType, info_kilo_metre_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_mega_metre_t = vfc::TSIUnits<ValueType, info_mega_metre_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_giga_metre_t = vfc::TSIUnits<ValueType, info_giga_metre_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_tera_metre_t = vfc::TSIUnits<ValueType, info_tera_metre_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_deci_metre_t = vfc::TSIUnits<ValueType, info_deci_metre_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_centi_metre_t = vfc::TSIUnits<ValueType, info_centi_metre_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_milli_metre_t = vfc::TSIUnits<ValueType, info_milli_metre_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_micro_metre_t = vfc::TSIUnits<ValueType, info_micro_metre_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_nano_metre_t = vfc::TSIUnits<ValueType, info_nano_metre_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_metre_second_t =
        vfc::TSIUnits<ValueType, info_metre_second_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_mile_t = vfc::TSIUnits<ValueType, info_mile_t, info_rbnil_type, UserType, ConvertPolicyType>;

    // power = 2

    using si_square_metre_t =
        vfc::TSIUnits<ValueType, info_square_metre_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_square_kilo_metre_t =
        vfc::TSIUnits<ValueType, info_square_kilo_metre_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_cubic_metre_t = vfc::TSIUnits<ValueType, info_cubic_metre_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_cubic_kilo_metre_t =
        vfc::TSIUnits<ValueType, info_cubic_kilo_metre_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_cubic_deci_metre_t =
        vfc::TSIUnits<ValueType, info_cubic_deci_metre_t, info_rbnil_type, UserType, ConvertPolicyType>;

    // litre = cubic decimetre
    using si_litre_t = vfc::TSIUnits<ValueType, info_litre_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_square_metre_per_square_second_t =
        vfc::TSIUnits<ValueType, info_square_metre_per_square_second_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_per_metre_second_t =
        vfc::TSIUnits<ValueType, info_per_metre_second_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_per_square_metre_t =
        vfc::TSIUnits<ValueType, info_per_square_metre_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_per_square_metre_second_t =
        vfc::TSIUnits<ValueType, info_per_square_metre_second_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_per_metre_t = vfc::TSIUnits<ValueType, info_per_metre_t, info_rbnil_type, UserType, ConvertPolicyType>;

    ///////////////////////////////////////////////
    ///////////////// time /////////////////////
    ///////////////////////////////////////////////

    // power = 1

    using si_second_t = vfc::TSIUnits<ValueType, info_second_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_minute_t = vfc::TSIUnits<ValueType, info_minute_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_hour_t = vfc::TSIUnits<ValueType, info_hour_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_milli_second_t =
        vfc::TSIUnits<ValueType, info_milli_second_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_micro_second_t =
        vfc::TSIUnits<ValueType, info_micro_second_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_nano_second_t = vfc::TSIUnits<ValueType, info_nano_second_t, info_rbnil_type, UserType, ConvertPolicyType>;

    // 1 / s
    using si_per_second_t = vfc::TSIUnits<ValueType, info_per_second_t, info_rbnil_type, UserType, ConvertPolicyType>;

    //  1 / hr
    using si_per_hour_t = vfc::TSIUnits<ValueType, info_per_hour_t, info_rbnil_type, UserType, ConvertPolicyType>;

    //  1 / s2
    using si_per_square_second_t =
        vfc::TSIUnits<ValueType, info_per_square_second_t, info_rbnil_type, UserType, ConvertPolicyType>;

    //  s2
    using si_square_second_t =
        vfc::TSIUnits<ValueType, info_square_second_t, info_rbnil_type, UserType, ConvertPolicyType>;

    //  1 / hr2
    using si_per_square_hour_t =
        vfc::TSIUnits<ValueType, info_per_square_hour_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_day_t = vfc::TSIUnits<ValueType, info_day_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_week_t = vfc::TSIUnits<ValueType, info_week_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_month_t = vfc::TSIUnits<ValueType, info_month_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_year_t = vfc::TSIUnits<ValueType, info_year_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_per_minute_t = vfc::TSIUnits<ValueType, info_per_minute_t, info_rbnil_type, UserType, ConvertPolicyType>;

    ///////////////////////////////////////////////
    ///////////////// Degree /////////////////////
    ///////////////////////////////////////////////

    using si_degree_t = vfc::TSIUnits<ValueType, info_degree_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_degree_per_second_t =
        vfc::TSIUnits<ValueType, info_degree_per_second_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_radian_t = vfc::TSIUnits<ValueType, info_radian_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_square_radian_t =
        vfc::TSIUnits<ValueType, info_square_radian_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_per_square_radian_t =
        vfc::TSIUnits<ValueType, info_per_square_radian_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_per_radian_t = vfc::TSIUnits<ValueType, info_per_radian_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_radian_per_second_t =
        vfc::TSIUnits<ValueType, info_radian_per_second_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_radian_per_square_second_t =
        vfc::TSIUnits<ValueType, info_radian_per_square_second_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_square_radian_per_square_second_t =
        vfc::TSIUnits<ValueType, info_square_radian_per_square_second_t, info_rbnil_type, UserType, ConvertPolicyType>;

    ///////////////////////////////////////////////
    ///////////////// Mass /////////////////////
    ///////////////////////////////////////////////

    using si_gram_t = vfc::TSIUnits<ValueType, info_gram_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_kilo_gram_t = vfc::TSIUnits<ValueType, info_kilo_gram_t, info_rbnil_type, UserType, ConvertPolicyType>;

    ///////////////////////////////////////////////
    ///////////////// temperature /////////////////////
    ///////////////////////////////////////////////

    //  temperature based
    using si_kelvin_t = vfc::TSIUnits<ValueType, info_kelvin_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_celsius_t = vfc::TSIUnits<ValueType, info_celsius_t, info_rbnil_type, UserType, ConvertPolicyType>;

    ///////////////////////////////////////////////
    ///////////////// ampere /////////////////////
    ///////////////////////////////////////////////

    using si_ampere_t = vfc::TSIUnits<ValueType, info_ampere_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_kilo_ampere_t = vfc::TSIUnits<ValueType, info_kilo_ampere_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_mega_ampere_t = vfc::TSIUnits<ValueType, info_mega_ampere_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_giga_ampere_t = vfc::TSIUnits<ValueType, info_giga_ampere_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_tera_ampere_t = vfc::TSIUnits<ValueType, info_tera_ampere_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_deci_ampere_t = vfc::TSIUnits<ValueType, info_deci_ampere_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_centi_ampere_t =
        vfc::TSIUnits<ValueType, info_centi_ampere_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_milli_ampere_t =
        vfc::TSIUnits<ValueType, info_milli_ampere_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_micro_ampere_t =
        vfc::TSIUnits<ValueType, info_micro_ampere_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_nano_ampere_t = vfc::TSIUnits<ValueType, info_nano_ampere_t, info_rbnil_type, UserType, ConvertPolicyType>;

    // 1 / A
    using si_per_ampere_t = vfc::TSIUnits<ValueType, info_per_ampere_t, info_rbnil_type, UserType, ConvertPolicyType>;

    ///////////////////////////////////////////////
    ///////////////// candela /////////////////////
    ///////////////////////////////////////////////

    using si_candela_t = vfc::TSIUnits<ValueType, info_candela_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_kilo_candela_t =
        vfc::TSIUnits<ValueType, info_kilo_candela_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_mega_candela_t =
        vfc::TSIUnits<ValueType, info_mega_candela_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_giga_candela_t =
        vfc::TSIUnits<ValueType, info_giga_candela_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_tera_candela_t =
        vfc::TSIUnits<ValueType, info_tera_candela_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_deci_candela_t =
        vfc::TSIUnits<ValueType, info_deci_candela_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_centi_candela_t =
        vfc::TSIUnits<ValueType, info_centi_candela_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_milli_candela_t =
        vfc::TSIUnits<ValueType, info_milli_candela_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_micro_candela_t =
        vfc::TSIUnits<ValueType, info_micro_candela_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_nano_candela_t =
        vfc::TSIUnits<ValueType, info_nano_candela_t, info_rbnil_type, UserType, ConvertPolicyType>;

    // 1 / cd
    using si_per_candela_t = vfc::TSIUnits<ValueType, info_per_candela_t, info_rbnil_type, UserType, ConvertPolicyType>;

    ///////////////////////////////////////////////////////////////////////////
    //////////////////// Commonly used types //////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////

    // Hz
    using si_hertz_t = si_per_second_t;

    using si_kilo_gram_metre_per_square_second_t = vfc::
        TSIUnits<ValueType, info_kilo_gram_metre_per_square_second_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_newton_t = vfc::TSIUnits<ValueType, info_newton_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_force_of_gravity_t =
        vfc::TSIUnits<ValueType, info_force_of_gravity_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_kilo_newton_t = vfc::TSIUnits<ValueType, info_kilo_newton_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_joule_t = vfc::TSIUnits<ValueType, info_joule_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_watt_t = vfc::TSIUnits<ValueType, info_watt_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_kilo_watt_t = vfc::TSIUnits<ValueType, info_kilo_watt_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_bar_t = vfc::TSIUnits<ValueType, info_bar_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_milli_bar_t = vfc::TSIUnits<ValueType, info_milli_bar_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_pascal_t = vfc::TSIUnits<ValueType, info_pascal_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_lux_t = vfc::TSIUnits<ValueType, info_lux_t, info_rbnil_type, UserType, ConvertPolicyType>;

    // newton metre
    using si_newton_metre_t = si_joule_t;

    //  velocity
    using si_metre_per_second_t =
        vfc::TSIUnits<ValueType, info_metre_per_second_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_kilometre_per_hour_t =
        vfc::TSIUnits<ValueType, info_kilometre_per_hour_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_miles_per_hour_t =
        vfc::TSIUnits<ValueType, info_miles_per_hour_t, info_rbnil_type, UserType, ConvertPolicyType>;

    //  acceleration
    using si_metre_per_square_second_t =
        vfc::TSIUnits<ValueType, info_metre_per_square_second_t, info_rbnil_type, UserType, ConvertPolicyType>;

    using si_kilometre_per_square_hour_t =
        vfc::TSIUnits<ValueType, info_kilometre_per_square_hour_t, info_rbnil_type, UserType, ConvertPolicyType>;

    // metre_per_cubic_second
    using si_metre_per_cubic_second_t =
        vfc::TSIUnits<ValueType, info_metre_per_cubic_second_t, info_rbnil_type, UserType, ConvertPolicyType>;

    // wave number
    using si_wavenumber_t = vfc::TSIUnits<ValueType, info_per_metre_t, info_rbnil_type, UserType, ConvertPolicyType>;

    ///////////////////////////////////////////////
    ///////////////// slope /////////////////////
    ///////////////////////////////////////////////

    using si_slope_t = vfc::TSIUnits<ValueType, info_slope_t, info_rbnil_type, UserType, ConvertPolicyType>;

    // the word scalar should to be more intuitive for most developers
    using si_scalar_t = si_slope_t;

    ///////////////////////////////////////////////
    ///////////////// pixel /////////////////////
    ///////////////////////////////////////////////

    using si_pixel_t = vfc::TSIUnits<ValueType, info_nil_type, info_pixel_t, UserType, ConvertPolicyType>;

    using si_square_pixel_t = vfc::TSIUnits<ValueType, info_nil_type, info_square_pixel_t, UserType, ConvertPolicyType>;

    using si_per_pixel_t = vfc::TSIUnits<ValueType, info_nil_type, info_per_pixel_t, UserType, ConvertPolicyType>;

    using si_per_square_pixel_t =
        vfc::TSIUnits<ValueType, info_nil_type, info_per_square_pixel_t, UserType, ConvertPolicyType>;

    using si_pixel_per_radian_t =
        vfc::TSIUnits<ValueType, vfc::info_per_radian_t, vfc::info_pixel_t, UserType, ConvertPolicyType>;

    using si_percent_t = vfc::TSIUnits<ValueType, info_nil_type, info_percentage_t, UserType, ConvertPolicyType>;

    using si_percent_per_second_t =
        vfc::TSIUnits<ValueType, info_per_second_t, info_percentage_t, UserType, ConvertPolicyType>;

    using si_percent_of_force_of_gravity_t =
        vfc::TSIUnits<ValueType, info_force_of_gravity_t, info_percentage_t, UserType, ConvertPolicyType>;

    // nil type
    using si_nil_t = vfc::TSIUnits<ValueType, vfc::info_nil_type, info_rbnil_type, UserType, ConvertPolicyType>;
};

//=========================================================================
// TSIUnitType_i32
//-------------------------------------------------------------------------
/// Specialization of trait to identify the type promotion for SIunits.
//=========================================================================
template <class UserType = vfc::CDefaultType, template <class, class, class> class ConvertPolicyType = vfc::TConvert>
class TSIUnitType_i32 : public TSIUnitType<vfc::int32_t, UserType, ConvertPolicyType>
{
};

//=========================================================================
// TSIUnitType_f32
//-------------------------------------------------------------------------
/// Specialization of trait to identify the type promotion for SIunits.
//=========================================================================
template <class UserType = vfc::CDefaultType, template <class, class, class> class ConvertPolicyType = vfc::TConvert>
class TSIUnitType_f32 : public TSIUnitType<vfc::float32_t, UserType, ConvertPolicyType>
{
};

#ifdef VFC_ENABLE_FLOAT64_TYPE
template <class UserType = vfc::CDefaultType, template <class, class, class> class ConvertPolicyType = vfc::TConvert>
class TSIUnitType_f64 : public TSIUnitType<vfc::float64_t, UserType, ConvertPolicyType>
{
};
#endif // VFC_ENABLE_FLOAT64_TYPE

//=========================================================================
// TRectAreaTypeTraits
//-------------------------------------------------------------------------
/// Specialization of trait to identify the type promotion for SIunits.

/// @ingroup vfc_group_core_types
//=========================================================================
template <
    class ValueType,
    class UnitInfoType,
    class RBInfoType,
    class UserType,
    template <class, class, class>
    class ConvertPolicyType>
struct TRectAreaTypeTraits<vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>>
{
    VFC_STATIC_ASSERT(!(vfc::TIsUnsignedArithmetic<ValueType>::value));

    using area_type =
        typename vfc::TSIUnits<ValueType, UnitInfoType, RBInfoType, UserType, ConvertPolicyType>::sqr_promoted_type;
};

} // namespace vfc

#endif // VFC_SIUNITS_TYPES_HPP_INCLUDED

//=============================================================================

