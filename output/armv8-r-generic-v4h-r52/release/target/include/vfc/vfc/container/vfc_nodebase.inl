//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_NODEBASE_INL_INCLUDED
#define VFC_NODEBASE_INL_INCLUDED

#include "vfc/core/vfc_assert.hpp" //VFC_REQUIRE, VFC_ENSURE

///////////////////////////////////////////////////////////////////////////////////////////////
// TNodeBase implementation
///////////////////////////////////////////////////////////////////////////////////////////////

template <bool UsePointer>
inline vfc::TNodeBase<UsePointer>::TNodeBase()
    : // PRQA S 2628 # Technical debt L2 id=00c187210e0a65aa4fb6-e1d23c4d9962a275
      Base_t()
{
}

template <bool UsePointer>
inline vfc::TNodeBase<UsePointer>::TNodeBase(TNodeBase<UsePointer> const& f_rhs) : Base_t(f_rhs)
{
}

template <bool UsePointer>
inline void vfc::TNodeBase<UsePointer>::init(const CBaseInfo& f_baseInfo_r)
{
    Base_t::m_next_idx = f_baseInfo_r.getLinkTo(this);
    Base_t::m_prev_idx = f_baseInfo_r.getLinkTo(this);
}

template <bool UsePointer>
inline typename vfc::TNodeBase<UsePointer>* vfc::TNodeBase<UsePointer>::next(const CBaseInfo& f_baseInfo_r) const
{
    return static_cast<TNodeBase*>(f_baseInfo_r.getAddressOf(
        Base_t::m_next_idx)); // PRQA S 3103 # Technical debt L2 id=00c10a757468fbe94993-755271f44814f6ff
}

template <bool UsePointer>
inline void vfc::TNodeBase<UsePointer>::setNext(const CBaseInfo& f_baseInfo_r, vfc::TNodeBase<UsePointer>* f_next)
{
    Base_t::m_next_idx = f_baseInfo_r.getLinkTo(f_next);
}

template <bool UsePointer>
inline typename vfc::TNodeBase<UsePointer>* vfc::TNodeBase<UsePointer>::prev(const CBaseInfo& f_baseInfo_r) const
{
    return static_cast<TNodeBase*>(f_baseInfo_r.getAddressOf(
        Base_t::m_prev_idx)); // PRQA S 3103 # Technical debt L2 id=00c1a9c5d1700d274831-3b7b192bb317deb4
}

template <bool UsePointer>
inline void vfc::TNodeBase<UsePointer>::setPrev(const CBaseInfo& f_baseInfo_r, vfc::TNodeBase<UsePointer>* f_prev)
{
    Base_t::m_prev_idx = f_baseInfo_r.getLinkTo(f_prev);
}

template <bool UsePointer>
inline vfc::TNodeBase<UsePointer>* vfc::TNodeBase<UsePointer>::linkNode(
    const CBaseInfo&       f_baseInfo_r,
    TNodeBase<UsePointer>* f_node_p,
    TNodeBase<UsePointer>* f_prev_p,
    TNodeBase<UsePointer>* f_next_p)
{
    VFC_ENSURE2(f_node_p != f_prev_p, "Can not link node to itself as previous");
    VFC_ENSURE2(f_node_p != f_next_p, "Can not link node to itself as next");

    f_node_p->setPrev(f_baseInfo_r, f_prev_p);
    f_node_p->setNext(f_baseInfo_r, f_next_p);
    f_prev_p->setNext(f_baseInfo_r, f_node_p);
    f_next_p->setPrev(f_baseInfo_r, f_node_p);
    return f_node_p;
}

template <bool UsePointer>
inline vfc::TNodeBase<UsePointer>* vfc::TNodeBase<UsePointer>::unlinkNode(
    const CBaseInfo&       f_baseInfo_r,
    TNodeBase<UsePointer>* f_prev_p,
    TNodeBase<UsePointer>* f_next_p)
{
    f_prev_p->setNext(f_baseInfo_r, f_next_p);
    f_next_p->setPrev(f_baseInfo_r, f_prev_p);
    return f_next_p;
}

template <bool UsePointer>
inline void vfc::TNodeBase<UsePointer>::swapNodeBase(
    const CBaseInfo&       f_baseInfo_r,
    TNodeBase<UsePointer>& f_node1_r,
    TNodeBase<UsePointer>& f_node2_r)
{
    TNodeBase<UsePointer>* temp_p = f_node1_r.prev(f_baseInfo_r);
    f_node1_r.setPrev(f_baseInfo_r, f_node2_r.prev(f_baseInfo_r));
    f_node2_r.setPrev(f_baseInfo_r, temp_p);
    temp_p = f_node1_r.next(f_baseInfo_r);
    f_node1_r.setNext(f_baseInfo_r, f_node2_r.next(f_baseInfo_r));
    f_node2_r.setNext(f_baseInfo_r, temp_p);
    f_node1_r.next(f_baseInfo_r)->setPrev(f_baseInfo_r, &f_node1_r);
    f_node1_r.prev(f_baseInfo_r)->setNext(f_baseInfo_r, &f_node1_r);
    f_node2_r.next(f_baseInfo_r)->setPrev(f_baseInfo_r, &f_node2_r);
    f_node2_r.prev(f_baseInfo_r)->setNext(f_baseInfo_r, &f_node2_r);
}

template <bool UsePointer>
inline void vfc::TNodeBase<UsePointer>::relinkNodeBase(
    const CBaseInfo&       f_baseInfo_r,
    TNodeBase<UsePointer>* f_pos_p,
    TNodeBase<UsePointer>* f_first_p,
    TNodeBase<UsePointer>* f_last_p)
{
    TNodeBase<UsePointer>* l_prevToLastNode = f_last_p->prev(f_baseInfo_r);
    f_first_p->prev(f_baseInfo_r)->setNext(f_baseInfo_r, f_last_p);
    f_last_p->setPrev(f_baseInfo_r, f_first_p->prev(f_baseInfo_r));

    // relink in the current list
    f_first_p->setPrev(f_baseInfo_r, f_pos_p->prev(f_baseInfo_r));
    l_prevToLastNode->setNext(f_baseInfo_r, f_pos_p);

    f_pos_p->prev(f_baseInfo_r)->setNext(f_baseInfo_r, f_first_p);
    f_pos_p->setPrev(f_baseInfo_r, l_prevToLastNode);
}

#endif // VFC_NODEBASE_INL_INCLUDED

//=============================================================================

