//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_CONTAINER_HASH_SUPPORT_HPP_INCLUDED
#define VFC_CONTAINER_HASH_SUPPORT_HPP_INCLUDED

#include "vfc/core/vfc_hash.hpp"

#include "vfc/core/vfc_types.hpp"

#include "vfc/container/vfc_carray.hpp"
#include "vfc/container/vfc_fixedvector.hpp"
#include "vfc/container/vfc_fixedlist.hpp"
#include "vfc/container/vfc_fixedcircularbuffer.hpp"

namespace vfc
{

//=========================================================================
// Hash function specializations for vfc types and vfc container classes.
//=========================================================================

// vfc types specializations
//-------------------------------------------------------------------------
//  for vfc/container

namespace intern
{
//=========================================================================
// hashValue(TCArray<ValueType, CapacityValue>)
//-------------------------------------------------------------------------
/// Calculation of the hash value for TCArray<ValueType, CapacityValue>.
/// @tparam V      value type of containter
/// @tparam N      capacity value type of containter
/// @param f_val   TCArray for which the hash shall be computed
/// @return        hash value of type hash_type
//=========================================================================
template <class V, vfc::int32_t N>
hash_type hashValue(const vfc::TCArray<V, N>& f_val)
{
    const vfc::intern::TGenericContainerHash<vfc::TCArray<V, N>> l_gch;
    return l_gch(f_val);
}

//=========================================================================
// hashValue(TFixedVector<ValueType, CapacityValue>)
//-------------------------------------------------------------------------
/// Calculation of the hash value for TFixedVector<ValueType, CapacityValue>.
/// @tparam V      value type of containter
/// @tparam N      capacity value type of containter
/// @param f_val   TFixedVector for which the hash shall be computed
/// @return        hash value of type hash_type
//=========================================================================
template <class V, vfc::int32_t N>
hash_type hashValue(const vfc::TFixedVector<V, N>& f_val)
{
    const vfc::intern::TGenericContainerHash<vfc::TFixedVector<V, N>> l_gch;
    return l_gch(f_val);
}

//=========================================================================
// hashValue(TFixedList<ValueType, SizeValue>)
//-------------------------------------------------------------------------
/// Calculation of the hash value for TFixedList<ValueType, SizeValue>.
/// @tparam V      value type of containter
/// @tparam N      capacity value type of containter
/// @param f_val   TFixedList for which the hash shall be computed
/// @return        hash value of type hash_type
//=========================================================================
template <class V, vfc::int32_t N>
hash_type hashValue(const vfc::TFixedList<V, N>& f_val)
{
    const vfc::intern::TGenericContainerHash<vfc::TFixedList<V, N>> l_gch;
    return l_gch(f_val);
}

//=========================================================================
// hashValue(TFixedCircularBuffer<ValueType, CapacityValue>)
//-------------------------------------------------------------------------
/// Calculation of the hash value for TFixedCircularBuffer<ValueType, CapacityValue>.
/// @tparam V      value type of containter
/// @tparam N      capacity value type of containter
/// @param f_val   TFixedCircularBuffer for which the hash shall be computed
/// @return        hash value of type hash_type
//=========================================================================
template <class V, vfc::int32_t N>
hash_type hashValue(const vfc::TFixedCircularBuffer<V, N>& f_val)
{
    const vfc::intern::TGenericContainerHash<vfc::TFixedCircularBuffer<V, N>> l_gch;
    return l_gch(f_val);
}
} // namespace intern

//=========================================================================
// THash<TCArray>
//-------------------------------------------------------------------------
/// @dspec{1856}     The SW component "vfc" shall compute and return the hash value of an given CArray instance.
/// @tparam V      value type of containter
/// @tparam N      capacity value type of containter
/// Specialization of a hash function object.
//=========================================================================
template <class V, vfc::int32_t N>
struct THash<vfc::TCArray<V, N>>
{
    using local_hash_type = vfc::TCArray<V, N>;

    /// @param f_val   value of local_hash_type (TCArray) to be hashed
    /// @return        hash value of type hash_type
    vfc::hash_type operator()(const local_hash_type& f_val) const { return vfc::intern::hashValue(f_val); }
};

//=========================================================================
// THash<TFixedVector>
//-------------------------------------------------------------------------
/// @dspec{1857}     The SW component "vfc" shall compute and return the hash value of an given Fixed vector instance.
/// @tparam V      value type of containter
/// @tparam N      capacity value type of containter
/// Specialization of a hash function object.
//=========================================================================
template <class V, vfc::int32_t N>
struct THash<vfc::TFixedVector<V, N>>
{
    using local_hash_type = vfc::TFixedVector<V, N>;

    /// @param f_val   value of local_hash_type (TFixedVector) to be hashed
    /// @return        hash value of type hash_type
    vfc::hash_type operator()(const local_hash_type& f_val) const { return vfc::intern::hashValue(f_val); }
};

//=========================================================================
// THash<TFixedList>
//-------------------------------------------------------------------------
/// @dspec{1858}     The SW component "vfc" shall compute and return the hash value of an given Fixed list instance.
/// @tparam V      value type of containter
/// @tparam N      capacity value type of containter
/// Specialization of a hash function object.
//=========================================================================
template <class V, vfc::int32_t N>
struct THash<vfc::TFixedList<V, N>>
{
    using local_hash_type = vfc::TFixedList<V, N>;

    /// @param f_val   value of local_hash_type (TFixedLIst) to be hashed
    /// @return        hash value of type hash_type
    vfc::hash_type operator()(const local_hash_type& f_val) const { return vfc::intern::hashValue(f_val); }
};

//=========================================================================
// THash<TFixedCircularBuffer>
//-------------------------------------------------------------------------
/// @dspec{1859}     The SW component "vfc" shall compute and return the hash value of an given Fixed circular buffer
/// instance.
/// @tparam V      value type of containter
/// @tparam N      capacity value type of containter
/// Specialization of a hash function object.
//=========================================================================
template <class V, vfc::int32_t N>
struct THash<vfc::TFixedCircularBuffer<V, N>>
{
    using local_hash_type = vfc::TFixedCircularBuffer<V, N>;

    /// @param f_val   value of local_hash_type (TFixedCircularBuffer) to be hashed
    /// @return        hash value of type hash_type
    vfc::hash_type operator()(const local_hash_type& f_val) const { return vfc::intern::hashValue(f_val); }
};

} // namespace vfc

#endif // VFC_CONTAINER_HASH_SUPPORT_HPP_INCLUDED

//=============================================================================

