//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_CARRAY_INL_INCLUDED
#define ZX_VFC_CARRAY_INL_INCLUDED

#include "vfc/core/vfc_assert.hpp"
#include "vfc/core/vfc_util.hpp"
#include "vfc/core/vfc_metaprog.hpp"
#include "vfc/core/vfc_math.hpp" //notNegative, numeric_limits

template <class ValueType, vfc::int32_t CapacityValue>
inline auto vfc::TCArray<ValueType, CapacityValue>::begin() -> iterator
{
    return intern::make_checkable_iterator(&m_value[0], *this);
}

template <class ValueType, vfc::int32_t CapacityValue>
inline auto vfc::TCArray<ValueType, CapacityValue>::begin() const -> const_iterator
{
    return intern::make_checkable_iterator(&m_value[0], *this);
}

template <class ValueType, vfc::int32_t CapacityValue>
inline auto vfc::TCArray<ValueType, CapacityValue>::end() -> iterator
{
    return intern::make_checkable_iterator(
        &m_value[0] + CapacityValue, *this); // PRQA S 3705 # Technical debt L2 id=00c1a578c4f5169541e2-b58c600a3e0fd82d
}

template <class ValueType, vfc::int32_t CapacityValue>
inline auto vfc::TCArray<ValueType, CapacityValue>::end() const -> const_iterator
{
    return intern::make_checkable_iterator(
        &m_value[0] + CapacityValue, *this); // PRQA S 3705 # Technical debt L2 id=00c132c5dd21afa04ddc-b58c600a3e0fd82d
}

template <class ValueType, vfc::int32_t CapacityValue>
inline auto vfc::TCArray<ValueType, CapacityValue>::rbegin() -> reverse_iterator
{
    return static_cast<reverse_iterator>(end());
}

template <class ValueType, vfc::int32_t CapacityValue>
inline auto vfc::TCArray<ValueType, CapacityValue>::rbegin() const -> const_reverse_iterator
{
    return static_cast<const_reverse_iterator>(end());
}

template <class ValueType, vfc::int32_t CapacityValue>
inline auto vfc::TCArray<ValueType, CapacityValue>::rend() -> reverse_iterator
{
    return static_cast<reverse_iterator>(begin());
}

template <class ValueType, vfc::int32_t CapacityValue>
inline auto vfc::TCArray<ValueType, CapacityValue>::rend() const -> const_reverse_iterator
{
    return static_cast<const_reverse_iterator>(begin());
}

template <class ValueType, vfc::int32_t CapacityValue>
template <typename IndexType>
inline auto vfc::TCArray<ValueType, CapacityValue>::operator[](IndexType f_pos) -> reference
{
    static_assert(
        TIsEnumType<IndexType>::value || TIsIntegral<IndexType>::value, "IndexType must be an integer or enum type");
    static_assert(
        TIsEnumType<IndexType>::value || numeric_limits<IndexType>::is_specialized,
        "numeric_limits for integer IndexType must be specialized");
    static_assert(
        TIsEnumType<IndexType>::value || (CapacityValue - 1) <= numeric_limits<IndexType>::max(),
        "IndexType must cover all possible index values");
    VFC_REQUIRE(notNegative(f_pos) && (static_cast<size_type>(f_pos) < CapacityValue));
    return m_value[f_pos]; // Warning qacpp-2841: Please check your code, the warning only occurs in the use of carray.
}

template <class ValueType, vfc::int32_t CapacityValue>
template <typename IndexType>
inline auto vfc::TCArray<ValueType, CapacityValue>::operator[](IndexType f_pos) const -> const_reference
{
    static_assert(
        TIsEnumType<IndexType>::value || TIsIntegral<IndexType>::value, "IndexType must be an integer or enum type");
    static_assert(
        TIsEnumType<IndexType>::value || numeric_limits<IndexType>::is_specialized,
        "numeric_limits for integer IndexType must be specialized");
    static_assert(
        TIsEnumType<IndexType>::value || (CapacityValue - 1) <= numeric_limits<IndexType>::max(),
        "IndexType must cover all possible index values");
    VFC_REQUIRE(notNegative(f_pos) && (static_cast<size_type>(f_pos) < CapacityValue));
    return m_value[f_pos]; // Warning qacpp-2841: Please check your code, the warning only occurs in the use of carray.
}

// qacpp-4212-R3: This method is part of a conceptual API across several classes and not static for consistency.
template <class ValueType, vfc::int32_t CapacityValue>
inline constexpr typename vfc::TCArray<ValueType, CapacityValue>::size_type
vfc::TCArray<ValueType, CapacityValue>::capacity() const // PRQA S 4212 # R3
{
    return static_cast<size_type>(CapacityValue);
}

template <class ValueType, vfc::int32_t CapacityValue>
inline constexpr typename vfc::TCArray<ValueType, CapacityValue>::size_type
vfc::TCArray<ValueType, CapacityValue>::size() const // PRQA S 4212 # R3
{
    return static_cast<size_type>(CapacityValue);
}

template <class ValueType, vfc::int32_t CapacityValue>
inline constexpr typename vfc::TCArray<ValueType, CapacityValue>::size_type
vfc::TCArray<ValueType, CapacityValue>::max_size() const // PRQA S 4212 # R3
{
    return static_cast<size_type>(CapacityValue);
}

template <class ValueType, vfc::int32_t CapacityValue>
inline constexpr bool vfc::TCArray<ValueType, CapacityValue>::empty() const // PRQA S 4212 # R3
{
    return false;
}

#endif // ZX_VFC_CARRAY_INL_INCLUDED

