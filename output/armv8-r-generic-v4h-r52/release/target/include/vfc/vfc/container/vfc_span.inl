//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_SPAN_INL_INCLUDED
#define ZX_VFC_SPAN_INL_INCLUDED

#include "vfc/core/vfc_assert.hpp"

template <typename ValueType>
inline vfc::TSpan<ValueType>::TSpan() : m_begin_p(nullptr), m_end_p(nullptr)
{
    // intentionally left blank
}

template <typename ValueType>
inline vfc::TSpan<ValueType> vfc::TSpan<ValueType>::from_pointer_and_size(ValueType* f_begin_p, size_t f_size)
{
    if (nullptr == f_begin_p)
    {
        VFC_REQUIRE2(0 == f_size, "Non-empty spans with nullptr are not allowed");
        return TSpan();
    }
    return TSpan(
        f_begin_p,
        f_begin_p + f_size); // PRQA S 3705 # Technical debt L2 id=00c119c1803eb4094684-7d237e4ea530fc4d
}

/// private constructor
template <typename ValueType>
inline vfc::TSpan<ValueType>::TSpan(ValueType* f_begin_p, ValueType* f_end_p) : m_begin_p(f_begin_p), m_end_p(f_end_p)
{
    // intentionally left blank
}

template <typename ValueType>
inline vfc::TSpan<typename vfc::TAddConst<ValueType>::type> vfc::TSpan<ValueType>::as_const() const
{
    return TSpan<typename vfc::TAddConst<ValueType>::type>::from_pointer_and_size(
        m_begin_p, size()); // PRQA S 3000 # Technical debt L2 id=00c1ba56ae0998634b7d-3e79f49d74156962
}

template <class ValueType>
inline typename vfc::TSpan<ValueType>::iterator
vfc::TSpan<ValueType>::begin() // PRQA S 4211 # Technical debt L4 id=00c148a6e71103d3464e-d5c714b302898a4b
{
    return intern::make_checkable_iterator(m_begin_p, *this);
}

template <class ValueType>
inline typename vfc::TSpan<ValueType>::const_iterator vfc::TSpan<ValueType>::begin() const
{
    return cbegin();
}

template <class ValueType>
inline typename vfc::TSpan<ValueType>::const_iterator vfc::TSpan<ValueType>::cbegin() const
{
    return intern::make_checkable_iterator(m_begin_p, *this);
}

template <class ValueType>
inline typename vfc::TSpan<ValueType>::iterator
vfc::TSpan<ValueType>::end() // PRQA S 4211 # Technical debt L4 id=00c1a6c7b048229b42d3-b273385495b282a5
{
    return intern::make_checkable_iterator(m_end_p, *this);
}

template <class ValueType>
inline typename vfc::TSpan<ValueType>::const_iterator vfc::TSpan<ValueType>::end() const
{
    return cend();
}

template <class ValueType>
inline typename vfc::TSpan<ValueType>::const_iterator vfc::TSpan<ValueType>::cend() const
{
    return intern::make_checkable_iterator(m_end_p, *this);
    ; // PRQA S 2880 # Technical debt L2 id=00c11803a28570ae4aa8-c0dfe202f9263045 // PRQA S 4500 # Technical debt L7
      // id=00c1a5d7a3a0a6f544e5-b67dd33865a6285d
}

template <class ValueType>
inline typename vfc::TSpan<ValueType>::reverse_iterator vfc::TSpan<ValueType>::rbegin()
{
    return static_cast<reverse_iterator>(end());
}

template <class ValueType>
inline typename vfc::TSpan<ValueType>::const_reverse_iterator vfc::TSpan<ValueType>::rbegin() const
{
    return crbegin();
}

template <class ValueType>
inline typename vfc::TSpan<ValueType>::const_reverse_iterator vfc::TSpan<ValueType>::crbegin() const
{
    return static_cast<const_reverse_iterator>(end());
}

template <class ValueType>
inline typename vfc::TSpan<ValueType>::reverse_iterator vfc::TSpan<ValueType>::rend()
{
    return static_cast<reverse_iterator>(begin());
}

template <class ValueType>
inline typename vfc::TSpan<ValueType>::const_reverse_iterator vfc::TSpan<ValueType>::rend() const
{
    return crend();
}

template <class ValueType>
inline typename vfc::TSpan<ValueType>::const_reverse_iterator vfc::TSpan<ValueType>::crend() const
{
    return static_cast<const_reverse_iterator>(begin());
}

template <class ValueType>
template <typename IndexType>
inline typename vfc::TSpan<ValueType>::reference vfc::TSpan<ValueType>::operator[](
    IndexType f_pos) // PRQA S 4211 # Technical debt L4 id=00c10d3e65e94fef4ff4-921ee9f2d30535e1
{
    static_assert(
        TIsEnumType<IndexType>::value || TIsIntegral<IndexType>::value, "IndexType must be an integer or enum type");

    VFC_REQUIRE(
        notNegative(f_pos) &&
        ((m_begin_p + f_pos) < m_end_p)); // PRQA S 3705 # Technical debt L2 id=00c1767b7af5b9a34634-4d69accaadb9c815

    return *(m_begin_p + f_pos); // PRQA S 3705 # Technical debt L2 id=00c1deabda2f9e4b480a-80781557ecfe25da
}

template <class ValueType>
template <typename IndexType>
inline typename vfc::TSpan<ValueType>::const_reference vfc::TSpan<ValueType>::operator[](IndexType f_pos) const
{
    static_assert(
        TIsEnumType<IndexType>::value || TIsIntegral<IndexType>::value, "IndexType must be an integer or enum type");

    VFC_REQUIRE(
        notNegative(f_pos) &&
        ((m_begin_p + f_pos) < m_end_p)); // PRQA S 3705 # Technical debt L2 id=00c1af6d12f9bc2d4e89-4d69accaadb9c815

    return *(m_begin_p + f_pos); // PRQA S 3705 # Technical debt L2 id=00c1c77521971ee941b3-80781557ecfe25da
}

template <class ValueType>
inline typename vfc::TSpan<ValueType>::size_type vfc::TSpan<ValueType>::size() const
{
    return static_cast<size_type>(
        m_end_p - m_begin_p); // PRQA S 2821 # Technical debt L8 id=00c151e336e37ab04a8d-0698f9afb227e98c // PRQA S 3705
                              // # Technical debt L2 id=00c192e8862aeff94da1-a0f033b6aa43a80b
}

template <class ValueType>
inline bool vfc::TSpan<ValueType>::empty() const
{
    return (m_begin_p == m_end_p);
}

template <typename ValueType, vfc::int32_t CapacityValue>
vfc::TSpan<ValueType> vfc::as_span(vfc::TCArray<ValueType, CapacityValue>& f_carray)
{
    return vfc::TSpan<ValueType>::from_pointer_and_size(
        &*f_carray.begin(),
        f_carray.size()); // PRQA S 3000 # Technical debt L2 id=00c199da30bf544e40fa-633ca4a2a325c746
}

template <typename ValueType, vfc::int32_t CapacityValue>
vfc::TSpan<const ValueType> vfc::as_span(const vfc::TCArray<ValueType, CapacityValue>& f_carray)
{
    return vfc::TSpan<const ValueType>::from_pointer_and_size(
        &*f_carray.begin(),
        f_carray.size()); // PRQA S 3000 # Technical debt L2 id=00c190c602b96cb049ee-ce08e2d88352d383
}

template <typename ValueType, vfc::int32_t CapacityValue, typename AllocatorType>
vfc::TSpan<ValueType> vfc::as_span(vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>& f_fixedVector)
{
    return vfc::TSpan<ValueType>::from_pointer_and_size(
        &*f_fixedVector.begin(),
        f_fixedVector.size()); // PRQA S 3000 # Technical debt L2 id=00c19e7361adbf754af8-340c17799e1c7fe4
}

template <typename ValueType, vfc::int32_t CapacityValue, typename AllocatorType>
vfc::TSpan<const ValueType>
vfc::as_span(const vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>& f_fixedVector)
{
    return vfc::TSpan<const ValueType>::from_pointer_and_size(
        &*f_fixedVector.begin(),
        f_fixedVector.size()); // PRQA S 3000 # Technical debt L2 id=00c1f109ccaf7cdf4ae0-56b4ad11835776a4
}

template <typename IterType1, typename IterType2, typename ValueType1, typename ValueType2>
struct vfc::intern::TContainerCheck<
    vfc::intern::TCheckedIterator<IterType1, vfc::TSpan<ValueType1>>,
    vfc::intern::TCheckedIterator<IterType2, vfc::TSpan<ValueType2>>>
{
    static inline bool isSame(
        TCheckedIterator<IterType1, TSpan<ValueType1>>
            it1, // PRQA S 2009 # Technical debt L2 id=00c15b51809faec943e5-b9e169db8b7a4a57
        TCheckedIterator<IterType2, TSpan<ValueType2>>
            it2) // PRQA S 2009 # Technical debt L2 id=00c1984149205e95497e-c63d6539a10c29e0
    {
        const auto rawIt1 = getUncheckedIterator(it1);
        const auto rawIt2 = getUncheckedIterator(it2);
        if (rawIt1 == rawIt2)
        {
            return true;
        }
        const auto rawBegin1 = getUncheckedIterator(getContainer(it1)->begin());
        const auto rawEnd1   = getUncheckedIterator(getContainer(it1)->end());
        const auto rawBegin2 = getUncheckedIterator(getContainer(it2)->begin());
        const auto rawEnd2   = getUncheckedIterator(getContainer(it2)->end());
        if (rawBegin2 < rawEnd1 && rawBegin1 < rawEnd2)
        {
            return true;
        }
        return false; // no overlap
    }
};

#endif // ZX_VFC_SPAN_INL_INCLUDED

