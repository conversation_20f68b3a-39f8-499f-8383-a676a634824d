//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_FIFO_HPP_INCLUDED
#define VFC_FIFO_HPP_INCLUDED

// vfc/core includes
#include "vfc/core/vfc_types.hpp"
#include "vfc/core/vfc_static_assert.hpp"
#include "vfc/core/vfc_atomic.hpp"
#include "vfc/container/vfc_carray.hpp" //TCArray

namespace vfc
{ //  namespace vfc opened

//=============================================================================
//  TFifo<>
//-----------------------------------------------------------------------------
/// Thread safe producer and consumer queue without using locks.
/// TFIFO class is used for synchronized data exchange with exactly one reader and
/// one writer thread.
/// The class internally allocates memory for CapacityValue + 1 , that
/// extra memory location at the end is used for end of Memory Location.
/// both reader and writer thread can work simultaneously without any data lose.
/// if Queue is full then happens to call push() returns false else true
/// if Queue is empty then happens to call pop() returns false else true
/// @note Be aware, that only one writer context calling push() and one reader context
/// calling pop() is allowed. Any 3rd context usage is illegal.
/// $Source: vfc_fifo.hpp $
/// @see
/// http://msmvps.com/blogs/vandooren/archive/2007/01/05/creating-a-thread-safe-producer-consumer-queue-in-c-without-using-locks.aspx
/// @par Description:
/// A template class representing a TFifo.
/// Implementation of FIFO ( First In First Out ) Queue.
/// @param ValueType        DataType to be stored
/// @param CapacityValue    Capacity of the Fifo
/// @par Sequence Diagrams:
/// \startuml
// participant Writer_thread
// participant Reader_thread
// participant No_3rd_context_allowed
// group write->read
// group write
// [-> Writer_thread
// activate Writer_thread
// hnote over Writer_thread
// TFifo::push()
// endnote
// Writer_thread --> Reader_thread : "commit"
// deactivate Writer_thread
// end
// == atomic sync: it is guaranteed now that all data is pushed ==
// group read
// Reader_thread --> Writer_thread : "request"
// activate Reader_thread
// hnote over Reader_thread
// TFifo::pop()
// endnote
// Writer_thread -> Reader_thread : "read data"
// Reader_thread ->] : true
// deactivate Reader_thread
// end
// end
// group concurrency
// [-> Writer_thread
// activate Writer_thread
// hnote over Writer_thread
// TFifo::push()
// endnote
// Reader_thread --> Writer_thread : "request"
// activate Reader_thread
// hnote over Reader_thread
// TFifo::pop()
// endnote
// Reader_thread ->] : false
// deactivate Reader_thread
// Writer_thread --> Reader_thread : "commit"
// deactivate Writer_thread
// end
// group empty
// Reader_thread --> Writer_thread : "empty"
// activate Reader_thread
// hnote over Reader_thread
// TFifo::empty()
// endnote
// Reader_thread ->] : true/false
// deactivate Reader_thread
// Writer_thread --> Writer_thread : "empty"
// activate Writer_thread
// hnote over Writer_thread
// TFifo::empty()
// endnote
// [<- Writer_thread : true/false
// deactivate Writer_thread
// end
// group illegal
// Reader_thread x<- No_3rd_context_allowed : not allowed
// activate No_3rd_context_allowed
// Writer_thread x<- No_3rd_context_allowed : not allowed
// deactivate No_3rd_context_allowed
// end
/// \enduml
/// @ingroup                vfc_containers

//=============================================================================

template <class ValueType, vfc::int32_t CapacityValue>
class TFifo
{
    static_assert(0 <= CapacityValue, "Capacity must not be negative");

  public:
    enum
    {
        FifoSize = CapacityValue, ///< The static TFifo size defined at compile time
        VFC_ATR_DEPRECATED_ENUM_VALUE2(FIFO_SIZE, "Use 'FifoSize' instead") = FifoSize
    };

    /// A type that represent the TFifo Type
    using fifo_type = TFifo<ValueType, CapacityValue>;

    /// A type that represent the data type stored in a TFifo
    using value_type = ValueType;
    using pointer    = value_type*;

    //---------------------------------------------------------------------
    /// Default constructor, takes no arguments.
    /// $Source: vfc_fifo.hpp $

    /// @dspec{218}     The SW component "vfc" shall create a new empty
    ///                 fifo instance.
    //---------------------------------------------------------------------
    TFifo();

    //---------------------------------------------------------------------
    /// Default Destructor.
    /// $Source: vfc_fifo.hpp $

    //---------------------------------------------------------------------
    ~TFifo() VFC_NOEXCEPT;

    //=====================================================================================
    /// Push the element into FIFO.
    /// @dspec{221}     The SW component "vfc" shall append data at the end of the
    ///                 given fifo instance and return the boolean value "true" if data
    ///                 could be appended or otherwise return the boolean value "false".
    /// @par Description:
    /// push() Function returns false when FIFO is Full and Data won't be pushed into FIFO
    /// else push data into FIFO and returns true.
    /// write_ptr is pointing to next writable memory location unless queue is Full.
    /// $Source: vfc_fifo.hpp $
    /// @code
    /// vfc::int32_t value ;
    /// bool status = push(value);
    /// @endcode
    /// @param f_param_r value to be pushed
    /// @return return true if push was successfull else false.
    /// @code
    /// (if FIFO is FULL , calling push() No change)
    ///     Start|-------------|
    ///                       |-------------|
    ///                                      |-------------|
    ///                                                    |-------------|
    ///                                                      (NO Change)
    ///
    /// (if FIFO is not FULL , calling push() add new data)
    ///     Start|-------------|
    ///                        |-------------|  ( Initial FIFO )
    ///  (push() Called two times)
    ///
    ///                                      |-------------|
    ///                                      (New Data)
    ///                                                     |-------------|
    ///                                                      (New Data)
    /// @endcode

    //====================================================================================
    bool push(const value_type& f_param_r);

    //====================================================================================
    /// Pop the element from FIFO.
    /// dspec{222}     The SW component "vfc" shall copy the first data item of the given fifo
    ///                instance to a given data reference and remove it from the fifo and return
    ///                the boolean value "true" if such a value exists, otherwise it shall return
    ///                the boolean value "false".
    /// @par Description:
    /// pop() Function returns false when queue is empty and read_ptr is not updated , else
    /// data is poped out and returns true.
    /// read_ptr is pointing to next readable memory location unless queue is empty.
    /// $Source: vfc_fifo.hpp $
    /// @code
    /// vfc::int32_t f_Value ;
    /// bool status = pop(f_Value);
    /// @endcode
    /// @param[out] f_param_r reference where removed value is copied to
    /// @return return true if pop was successfull else false.
    /// @code
    /// (FIFO With Data)
    ///     Start|-------------|
    ///                        |-------------|
    ///                                      |-------------|
    ///                                                    |-------------|  ( 4 elements )
    /// ( Calling pop() on the FIFO )
    ///
    ///                        |-------------|
    ///                                      |-------------|
    ///                                                    |-------------|  ( 3 elements )
    /// @endcode

    //====================================================================================
    bool pop(value_type& f_param_r);

    //====================================================================================
    /// Check if the FIFO is empty.
    /// @dspec{219}     The SW component "vfc" shall return the boolean value "true" if the
    ///                 given fifo instance is empty otherwise it shall return the boolean
    ///                 value "false".
    /// @par Description:
    /// empty() function returns true when queue is empty at the moment, that this
    /// function is called. Otherwise there exists data in the queue and false will be
    /// returned. It is allowed to be called from writer and reader side.
    /// @note: Never call empty() outside of either writer or reader context, because
    /// calling from a 3rd context causes data races.
    /// $Source: vfc_fifo.hpp $
    /// @return  true if the FIFO is empty; false if the FIFO is nonempty.

    //====================================================================================
    bool empty() const;

  private:
    /// Internal size needs to be one element bigger than
    /// demanded, to come around the pointer equality == buffer empty
    /// problem.
    /// Due to this, we waste one element of memory space.
    enum
    {
        InternalFifoSize = CapacityValue + 1 ///< Internal FIFO size for internal purpose ,
                                             ///< one greater than CapacityValue
    };

    /// The start/end pointers are "const pointers" as they are not modified after the ctor.
    /// the write/read pointers are "atomic pointers" so that they are not reordered (read or written too late)
    TCArray<value_type, InternalFifoSize> m_data;      ///<    internal datastructure
    vfc::TAtomicInt<uint32_t>             m_write_pos; ///<    write pointer, depends on fifo fill level
    vfc::TAtomicInt<uint32_t>             m_read_pos;  ///<    read pointer, depends on fifo fill level

    //---------------------------------------------------------------------
    /// Copy constructor.
    /// $Source: vfc_fifo.hpp $

    //---------------------------------------------------------------------
    TFifo(const TFifo& f_fifoobj);

    //---------------------------------------------------------------------
    /// Assignment Operator.
    /// $Source: vfc_fifo.hpp $

    //---------------------------------------------------------------------
    TFifo& operator=(const TFifo& f_fifoobj);
};

} // namespace vfc

#include "vfc/container/vfc_fifo.inl"

#endif // VFC_FIFO_HPP_INCLUDED

//=============================================================================

