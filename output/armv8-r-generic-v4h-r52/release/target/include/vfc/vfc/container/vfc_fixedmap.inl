//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================
#ifndef ZX_VFC_FIXEDMAP_INL_INCLUDED
#define ZX_VFC_FIXEDMAP_INL_INCLUDED

#include <new> // operator new()

#include "vfc/core/vfc_assert.hpp"             // For VFC_REQUIRE()
#include "vfc/core/vfc_algorithm.hpp"          // For vfc::min and vfc::max
#include "vfc/container/vfc_carray.hpp"        // For TCarray
#include "vfc/memory/vfc_objectstorage.hpp"    // For TObjectStorage
#include "vfc/core/vfc_functionattributes.hpp" // For SoftwareQualificationLevel

namespace vfc
{
namespace intern
{
enum balance : vfc::int32_t
{
    veryLeftHeavy  = -2,
    leftHeavy      = -1,
    balanced       = 0,
    rightHeavy     = +1,
    veryRightHeavy = +2
};

// Storage size shouldn't matter since only used as function argument.
enum class LeftRight : vfc::uint8_t
{
    left,
    right
};

template <vfc::int32_t CapacityValue>
struct TFixedMapBase
{
    using index_type   = vfc::int32_t;
    using balance_type = vfc::int8_t;
    using size_type    = vfc::size_t;
    using MaxSize      = TIntegralConstant<vfc::int32_t, CapacityValue>;
    using assertLevel  = typename attributes::SoftwareQualifiedType<MaxSize>::Prototype::type_t;

    // qacpp-4206-R1: Member `m_parent,m_balance,m_left,m_right` not initialized by intention.
    TFixedMapBase() // PRQA S 4206 # R1
        : m_root{-1}, m_next{0}
    {
        // Intentionally left blank.
    }

    /// Clear the map.
    void clear()
    {
        m_root = -1;
        m_next = 0;
    }

    /// Return size of the container.
    auto size() const -> size_type { return static_cast<size_type>(m_next); }

    /// Return Capacity of the container.
    static constexpr auto max_size() -> size_type { return MaxSize::value; }

    /// Check the map is empty or not.
    bool empty() const
    {
        VFC_ASSERT2(
            ((-1 != m_root) || (0 == m_next)), "For map to be empty, root should be -1 and next node should be 0");
        return (-1 == m_root);
    }

    /// To check the valid index of the container.
    static bool isInvalidIdx(const index_type f_idx)
    {
        VFC_ASSERT2((f_idx >= -1) && (f_idx < CapacityValue), "Index out of range");
        return (f_idx == -1);
    }

    /// To check if the current node is root node.
    bool isRoot(const index_type f_idx) const
    {
        VFC_ASSERT(!isInvalidIdx(f_idx));
        VFC_ASSERT2(
            (f_idx == m_root) || !isInvalidIdx(m_parent[f_idx]), "For root node, parent node should be invalid index");
        return ((f_idx == m_root) && (isInvalidIdx(m_parent[f_idx])));
    }

    /// To check if the current node is leaf node.
    bool isLeaf(const index_type f_idx) const
    {
        VFC_ASSERT(!isInvalidIdx(f_idx));
        return ((isInvalidIdx(m_left[f_idx])) && (isInvalidIdx(m_right[f_idx])));
    }

    /// To check if the current node is left child.
    bool isLeftChild(const index_type f_idx) const
    {
        VFC_ASSERT(!isInvalidIdx(f_idx));
        VFC_ASSERT2(
            f_idx != m_left[m_parent[f_idx]] || !isInvalidIdx(m_parent[f_idx]),
            "For left child node, parent node must be valid node");
        return ((!isInvalidIdx(m_parent[f_idx])) && (f_idx == m_left[m_parent[f_idx]]));
    }

    /// To check if the current node is right child.
    bool isRightChild(const index_type f_idx) const
    {
        VFC_ASSERT(!isInvalidIdx(f_idx));
        VFC_ASSERT2(
            f_idx != m_right[m_parent[f_idx]] || !isInvalidIdx(m_parent[f_idx]),
            "For right child node, parent node must be valid node");
        return ((!isInvalidIdx(m_parent[f_idx])) && (f_idx == m_right[m_parent[f_idx]]));
    }

    /// Find out the validity of the index.
    static void assertValidIdx(index_type idx)
    {
        VFC_REQUIRE2((idx >= -1) && (idx < MaxSize::value), "Index out of bound.");
    }

    /// To calculate balance after all operations.
    void changeBalance(index_type f_idx, vfc::int32_t f_offset)
    {
        VFC_ASSERT2(!isInvalidIdx(f_idx), "Node must be valid to calculate new balance");
        vfc::int32_t balanceInt = static_cast<vfc::int32_t>(m_balance[f_idx]);
        VFC_ASSERT2(static_cast<vfc::int32_t>(veryLeftHeavy) <= balanceInt, "Resulting balance is too left-heavy");
        VFC_ASSERT2(static_cast<vfc::int32_t>(veryRightHeavy) >= balanceInt, "Resulting balance is too right-heavy");
        m_balance[f_idx] = static_cast<balance_type>(balanceInt + f_offset);
    }

    /// Create and link new node with index f_idx.
    /// @param f_idx is created node index, f_parentIdx is parent node index.
    void create(index_type f_idx, index_type f_parentIdx)
    {
        assertValidIdx(f_idx);
        m_parent[f_idx]  = f_parentIdx;
        m_left[f_idx]    = -1;
        m_right[f_idx]   = -1;
        m_balance[f_idx] = balanced;
    }

    /// Insert an index at the right or left position of the parent index.
    /// This function is only called and instantiated from tests, not production code.
    /// @param f_parent is parent index of the new index to be inserted.
    /// @param f_position will decides, on which position(left or right) the new index to be inserted.
    auto insert_for_test(index_type f_parent, LeftRight f_position) -> index_type
    {
        VFC_ASSERT2(MaxSize::value > size(), "Container must not be full when insert called");
        VFC_ASSERT2(((f_parent >= -1) && (f_parent < CapacityValue)), "Container must not be empty when insert called");
        const index_type l_new = m_next;
        m_next += 1;

        if (isInvalidIdx(f_parent))
        {
            // empty map => initialize root
            m_root = l_new;
            create(l_new, f_parent);
        }
        else
        {
            create(l_new, f_parent);

            switch (f_position)
            {
            // right is right of f_parent.
            case LeftRight::right:
                m_right[f_parent] = l_new;
                changeBalance(f_parent, 1);
                break;
            // left is left of f_parent.
            case LeftRight::left:
                m_left[f_parent] = l_new;
                changeBalance(f_parent, -1);
                break;

            default:
                // do nothing
                break;
            }
        }
        return l_new;
    }

    /// Initialize node to insert an element.
    auto insert(index_type f_parent) -> index_type { return insert(f_parent, true); }

    auto insert(index_type f_parent, bool f_left) -> index_type
    {
        VFC_REQUIRE2(MaxSize::value > size(), "Container must not be full when insert called");
        const index_type l_new = m_next;
        m_next += 1;

        if (isInvalidIdx(f_parent))
        {
            // empty map => initialize root
            m_root = l_new;
            create(l_new, f_parent);
        }
        else
        {
            const balance_type l_currentBalance = m_balance[f_parent];
            create(l_new, f_parent);
            changeBalance(f_parent, f_left ? leftHeavy : rightHeavy);
            bool needUpin = false;

            if (f_left)
            {
                // Initialize left node to insert an element.
                VFC_REQUIRE2(isInvalidIdx(m_left[f_parent]), "left node of the current index must not be Invalid node");

                m_left[f_parent] = l_new;
                needUpin         = (l_currentBalance <= balanced);
            }
            else
            {
                // Initialize right node to insert an element.
                VFC_REQUIRE2(
                    isInvalidIdx(m_right[f_parent]), "right node of the current index must not be Invalid node");

                m_right[f_parent] = l_new;
                needUpin          = (l_currentBalance >= balanced);
            }
            if (needUpin)
            {
                upin(f_parent);
            }
        }
        return l_new;
    }

    /// Reestablish balance of tree structure after an insertion.
    /// For details see Ottmann page 266+ for details.
    /// @param f_idx Index of current node.
    void upin(index_type f_idx)
    {
        while (!isInvalidIdx(f_idx))
        {
            if (isInvalidIdx(m_parent[f_idx]))
            {
                return;
            }

            // figure out if f_idx is left or right child of its parent
            else if (isLeftChild(f_idx))
            {
                const index_type   l_parent        = m_parent[f_idx];
                const balance_type l_parentBalance = m_balance[l_parent];
                changeBalance(l_parent, -1);

                if (l_parentBalance == rightHeavy)
                {
                    return;
                }
                else if (l_parentBalance == balanced)
                {
                    f_idx = l_parent;
                }
                else
                {
                    if (m_balance[f_idx] == leftHeavy)
                    {
                        rotate_right(l_parent);
                        return;
                    }
                    else
                    {
                        rotate_leftright(l_parent);
                        return;
                    }
                }
            }

            else if (isRightChild(f_idx))
            {
                const index_type   l_parent        = m_parent[f_idx];
                const balance_type l_parentBalance = m_balance[l_parent];
                changeBalance(l_parent, 1);

                if (l_parentBalance == leftHeavy)
                {
                    return;
                }
                else if (l_parentBalance == balanced)
                {
                    f_idx = l_parent;
                }
                else
                {
                    if (m_balance[f_idx] == rightHeavy)
                    {
                        rotate_left(l_parent);
                        return;
                    }
                    else
                    {
                        rotate_rightleft(l_parent);
                        return;
                    }
                }
            }
            else
            {
                // do nothing
            }
        }
    }

    /// Set nodes(parent, left, right) after rotation of tree.
    void setNodes(const index_type f_parent, const index_type f_idx1, const index_type f_idx2)
    {
        // re-link parent links
        const index_type l_parent = m_parent[f_parent];
        m_parent[f_parent]        = f_idx1;
        m_parent[f_idx1]          = l_parent;
        if (!isInvalidIdx(f_idx2))
        {
            m_parent[f_idx2] = f_parent;
        }

        // update root node/left node/right node correctly
        if (isInvalidIdx(l_parent))
        {
            m_root = f_idx1;
        }
        else if (f_parent == m_left[l_parent])
        {
            m_left[l_parent] = f_idx1;
        }
        else if (f_parent == m_right[l_parent])
        {
            m_right[l_parent] = f_idx1;
        }
        else
        {
            // do nothing
        }
    }

    /// Perform left rotation on current node.
    ///
    /// With @f$ \phi = @f$ `f_parent`, the following rotation is performed:
    ///
    /// ~~~
    ///     phi                     p              "" |
    ///    /   \                   / \             "" |
    ///  T1     p       =>      phi   T3           "" |
    ///        / \             /   \               "" |
    ///      T2   T3         T1     T2             "" |
    /// ~~~
    ///
    /// Since the balance calculation is not given in the book the
    /// following assumptions are taken:
    ///
    /// For a given node @f$ p  @f$, its parent @f$ parent(p) = \phi @f$, the height
    /// function @f$ h @f$ and the balance function @f$ b @f$ the following
    /// node balances are defined:
    ///
    /// @f{align*}{
    ///     b(p)    &= h(right(p)) - h(left(p)) \equiv h_3 - h_2 \\                          "" |
    ///     b(\phi) &= h(right(\phi)) - h(left(\phi)) \equiv (1 + \max(h_2, h_3)) - h_1      "" |
    /// @f}
    ///
    /// With these balance definitions the following heights can be calculated:
    ///
    /// @f{align*}{
    ///     h_3     &\equiv h \\                           "" |
    ///     h_2     &= h - b(p) \\                         "" |
    ///     h_1     &= 1 + h - \min(0, b(p)) - b(\phi)     "" |
    /// @f}
    ///
    /// With these definitions the total height of the map before the rotation is:
    ///
    /// @f{align*}{
    ///     h(\phi) &= 1 + \max(h_1, 1 + \max(h_2, h_3)) \\      "" |
    ///             &= 2 + h - \min(0, b(p)) - \min(0, b(\phi))  "" |
    /// @f}
    ///
    /// After left rotation the balances are defined as:
    ///
    /// @f{align*}{
    ///     b(\phi') &= h_2 - h_1 \\                                 "" |
    ///              &= b(\phi) - b(p) - 1 + \min(0, b(p)) \\        "" |
    ///     b(p')    &= h_3 - (1 + \max(h_1, h_2)) \\                "" |
    ///              &= \min(b(p), b(\phi) - 1 + \min(0, b(p))) - 1  "" |
    /// @f}
    ///
    /// For the total height of the new map @f$ h(p') @f$ it is:
    ///
    /// @f{align*}{
    ///     h(p') &= 1 + \max(1 + \max(h_1, h_2), h_3) \\                            "" |
    ///           &= 1 + h + \max(0, 1 + \max(1 - \min(0, b(p)) - b(\phi), -b(p))    "" |
    /// @f}
    ///
    /// @param [in] f_parent Parent node index
    /// @pre The following nodes must be defined: `f_parent` and `right(f_parent)`
    void rotate_left(const index_type f_parent)
    {
        VFC_ASSERT2(!isInvalidIdx(f_parent), "parent node must be a valid node");
        VFC_ASSERT2(!isInvalidIdx(m_right[f_parent]), "right node of the f_parent index must be a valid node.");

        // current relationships of f_parent
        const index_type l_right     = m_right[f_parent];
        const index_type l_rightleft = m_left[l_right];

        // current balance
        const balance_type l_balanceParent = m_balance[f_parent];
        const balance_type l_balanceRight  = m_balance[l_right];

        // re-link subtrees
        m_right[f_parent] = l_rightleft;
        m_left[l_right]   = f_parent;

        // re-link parent and set root/left/right node
        setNodes(f_parent, l_right, l_rightleft);

        // fix balance
        const balance_type l_min0bp = static_cast<balance_type>(vfc::min(0, static_cast<vfc::int32_t>(l_balanceRight)));

        changeBalance(f_parent, -l_balanceRight - 1 + l_min0bp);
        m_balance[l_right] = static_cast<balance_type>(
            vfc::min(
                static_cast<vfc::int32_t>(l_balanceRight), static_cast<vfc::int32_t>(l_balanceParent) - 1 + l_min0bp) -
            1);
    }

    /// Perform left-right double rotation on current node.
    ///
    /// The double rotation consist of two steps:
    ///
    /// * First the left child of `f_parent` is rotated to the left
    /// * Second the node `f_parent` is rotated to the right
    ///
    /// ~~~
    ///         f_parent                  f_parent                  __p__                 "" |
    ///        /        \                /        \                /     \                "" |
    ///     phi          T4     =>      p          T4    =>     phi       f_parent        "" |
    ///    /   \                       / \                     /   \     /        \       "" |
    ///  T1     p                   phi   T3                 T1     T2 T3          T4     "" |
    ///        / \                 /   \                                                  "" |
    ///      T2   T3             T1     T2                                                "" |
    /// ~~~
    ///
    /// Since the balance calculation is not given in the book the
    /// following assumptions are taken:
    ///
    /// After the first left rotation on node @f$ \phi = left(@f$ `f_parent `@f$) @f$
    /// and its right child @f$ p = right(\phi) @f$ the total height difference
    /// of the rotated subgraph is (see height calculations in rotate_left()):
    ///
    /// @f{align*}{
    ///     h(p') - h(\phi) &= -1 + \max(0, 1 + \max(1 - \min(0, b(p)) - b(\phi), -b(p))    "" |
    ///                           + \min(0, b(p)) + \min(0, b(\phi))                        "" |
    /// @f}
    ///
    /// Before performing the second right rotation on `f_parent`, the balance
    /// of `f_parent` is adjusted accordingly by subtracting the height difference
    /// from the balance @f$ b( @f$ `f_parent` @f$ ) @f$.
    ///
    /// @param [in] f_parent Parent node index
    /// @pre The following nodes must be defined: `f_parent`, `left(f_parent)` and `right(left(f_parent))`
    void rotate_leftright(const index_type f_parent)
    {
        const balance_type l_balanceLeft      = m_balance[m_left[f_parent]];
        const balance_type l_balanceLeftRight = m_balance[m_right[m_left[f_parent]]];

        // perform left rotation on left subtree
        rotate_left(m_left[f_parent]);

        // calculate new balance
        const balance_type l_min0bp =
            static_cast<balance_type>(vfc::min(0, static_cast<vfc::int32_t>(l_balanceLeftRight)));
        const balance_type l_min0bphi = static_cast<balance_type>(vfc::min(0, static_cast<int32_t>(l_balanceLeft)));

        // subtract height difference from rotated subtree
        changeBalance(
            f_parent,
            -(-1 + vfc::max(0, 1 + vfc::max(1 - l_min0bp - l_balanceLeft, -l_balanceLeftRight)) + l_min0bp +
              l_min0bphi));

        // perform right rotation on parent
        rotate_right(f_parent);
    }

    /// Perform right rotation on current node.
    ///
    /// With @f$ \phi = @f$ `f_parent`, the following rotation is performed:
    ///
    /// ~~~
    ///      phi                     p                         "" |
    ///     /   \                   / \                        "" |
    ///    p     T3       =>      T1   phi                     "" |
    ///   / \                         /   \                    "" |
    /// T1   T2                     T2     T3                  "" |
    /// ~~~
    ///
    /// Since the balance calculation is not given in the book the
    /// following assumptions are taken:
    ///
    /// For a given node @f$ p  @f$, its parent @f$ parent(p) = \phi @f$, the height
    /// function @f$ h @f$ and the balance function @f$ b @f$ the following
    /// node balances are defined:
    ///
    /// @f{align*}{
    ///     b(p)    &= h(right(p)) - h(left(p)) \equiv h_2 - h_1 \\  "" |
    ///     b(\phi) &= h(right(\phi)) - h(left(\phi)) \equiv h_3 - (1 + \max(h_1, h_2))  "" |
    /// @f}
    ///
    /// With these balance definitions the following heights can be calculated:
    ///
    /// @f{align*}{
    ///     h_1     &\equiv h \\                         "" |
    ///     h_2     &= h + b(p) \\                       "" |
    ///     h_3     &= 1 + h + \max(0, b(p)) + b(\phi)   "" |
    /// @f}
    ///
    /// The total height of the map before rotation is:
    ///
    /// @f{align*}{
    ///     h(\phi) &= 1 + \max(1 + \max(h_1, h_2), h_3) \\      "" |
    ///             &= 2 + h + \max(0, b(p)) + \max(0, b(\phi))  "" |
    /// @f}
    ///
    /// After right rotation the balances are defined as:
    ///
    /// @f{align*}{
    ///     b(\phi') &= h_3 - h_2 \\                                   "" |
    ///              &= b(\phi) - b(p) + 1 + \max(0, b(p)) \\          "" |
    ///     b(p')    &= (1 + \max(h_2, h_3)) - h_1 \\                  "" |
    ///              &= \max(b(p), b(\phi) + 1 + \max(0, b(p))) + 1    "" |
    /// @f}
    ///
    /// For the total height of the new map @f$ h(p') @f$ it is:
    ///
    /// @f{align*}{
    ///     h(p') &= 1 + \max(h_1, 1 + \max(h_2, h_3)) \\                            "" |
    ///           &= 1 + h + \max(0, 1 + \max(b(p), 1 + \max(0, b(p)) + b(\phi)))    "" |
    /// @f}
    ///
    /// @param [in] f_parent Parent node index
    /// @pre The following nodes must be defined: `f_parent` and `left(f_parent)`
    void rotate_right(const index_type f_parent)
    {
        VFC_ASSERT2(!isInvalidIdx(f_parent), "parent node must be a valid node");
        VFC_ASSERT2(!isInvalidIdx(m_left[f_parent]), "left node of the f_parent index must be a valid node");

        // current relationships of f_parent
        const index_type l_left      = m_left[f_parent];
        const index_type l_leftright = m_right[l_left];

        // current balance
        const balance_type l_balanceParent = m_balance[f_parent];
        const balance_type l_balanceLeft   = m_balance[l_left];

        // re-link subtrees
        m_left[f_parent] = l_leftright;
        m_right[l_left]  = f_parent;

        // re-link parent and set root/left/right node
        setNodes(f_parent, l_left, l_leftright);

        // fix balance:
        const balance_type l_max0bp = static_cast<balance_type>(vfc::max(0, static_cast<vfc::int32_t>(l_balanceLeft)));
        changeBalance(f_parent, -l_balanceLeft + 1 + l_max0bp);

        m_balance[l_left] = static_cast<balance_type>(
            vfc::max(
                static_cast<vfc::int32_t>(l_balanceLeft), static_cast<vfc::int32_t>(l_balanceParent) + 1 + l_max0bp) +
            1);
    }

    /// Perform right-left double rotation on current node.
    ///
    /// The double rotation consist of two steps:
    ///
    /// * First the right child of `f_parent` is rotated to the right
    /// * Second the node `f_parent` is rotated to the left
    ///
    /// ~~~
    ///    f_parent                     f_parent                             __p__              "" |
    ///   /        \                   /        \                           /     \             "" |
    /// T1          phi       =>     T1          p          =>      f_parent       phi          "" |
    ///            /   \                        / \                /        \     /   \         "" |
    ///           p     T4                    T2   phi           T1          T2 T3     T4       "" |
    ///          / \                              /   \                                         "" |
    ///        T2   T3                          T3     T4                                       "" |
    /// ~~~
    ///
    /// Since the balance calculation is not given in the book the
    /// following assumptions are taken:
    ///
    /// After the first right rotation on node @f$ \phi = right(@f$ `f_parent `@f$) @f$
    /// and its left child @f$ p = left(\phi) @f$ the total height difference
    /// of the rotated subgraph is (see height calculations in rotate_right()):
    ///
    /// @f{align*}{
    ///     h(p') - h(\phi) &= -1 + \max(0, 1 + \max(b(p), 1 + \max(0, b(p)) + b(\phi))) - \max(0, b(p)) - max(0,
    ///     b(\phi))    "" |
    /// @f}
    ///
    /// Before performing the second left rotation on `f_parent`, the balance
    /// of `f_parent` is adjusted accordingly by adding the height difference
    /// from the balance @f$ b( @f$ `f_parent` @f$ ) @f$.
    ///
    /// @param [in] f_parent Parent node index
    /// @pre The following nodes must be defined: `f_parent`, `right(f_parent)` and `left(right(f_parent))`
    void rotate_rightleft(const index_type f_parent)
    {
        const balance_type l_balanceRight     = m_balance[m_right[f_parent]];
        const balance_type l_balanceRightLeft = m_balance[m_left[m_right[f_parent]]];

        // perform right rotation on left subtree
        rotate_right(m_right[f_parent]);

        // calculate new balance
        const balance_type l_max0bp =
            static_cast<balance_type>(vfc::max(0, static_cast<vfc::int32_t>(l_balanceRightLeft)));
        const balance_type l_max0bphi =
            static_cast<balance_type>(vfc::max(0, static_cast<vfc::int32_t>(l_balanceRight)));

        // add height difference from rotated subtree
        changeBalance(
            f_parent,
            -1 +
                vfc::max(
                    0,
                    1 + vfc::max(
                            static_cast<vfc::int32_t>(l_balanceRightLeft),
                            1 + static_cast<vfc::int32_t>(l_max0bp) + static_cast<vfc::int32_t>(l_balanceRight))) -
                l_max0bp - l_max0bphi);

        // perform right rotation on parent
        rotate_left(f_parent);
    }

    /// Reestablish map balance of map structure after a removal.
    /// For details see Ottmann page 270+ for details.
    /// @param f_idx Node index of modified subtree.
    /// @param f_child1 true when it needs to perform upout() on previous parent of successor-f_idx, otherwise false.
    /// @param f_child2 true when it needs to perform upout() on previous parent of predecessor-f_idx, otherwise false.
    void upout(index_type f_idx, bool f_child1, bool f_child2)
    {
        while (!isInvalidIdx(f_idx))
        {
            if ((f_child1 == false) && (f_child2 == false))
            {
                if ((m_balance[f_idx] != balanced) || (isRoot(f_idx)))
                {
                    return; // do nothing
                }
            }

            index_type l_parent = m_parent[f_idx];

            // case 1: p is left child of parent
            if ((isLeftChild(f_idx) && (f_child2 == false)) || (f_child1 == true))
            {
                if (f_child1 == true)
                {
                    l_parent = f_idx;
                    f_child1 = false;
                }
                const balance_type l_balanceParent = m_balance[l_parent];
                changeBalance(l_parent, 1);

                if (l_balanceParent == rightHeavy)
                {
                    // Get right child of parent (node `q` in the book)
                    const index_type l_parentRight = m_right[l_parent];

                    const balance_type l_balanceParentRight = m_balance[l_parentRight];

                    if (l_balanceParentRight == balanced)
                    {
                        rotate_left(l_parent);
                        return;
                    }
                    else if (l_balanceParentRight == rightHeavy)
                    {
                        rotate_left(l_parent);

                        // perform upout() on new root node
                        f_idx = l_parentRight;
                    }
                    else if (l_balanceParentRight == leftHeavy)
                    {
                        // get left child of q => must exist since balance is -1
                        const index_type l_parentRightLeft = m_left[l_parentRight];

                        rotate_rightleft(l_parent);
                        // perform upout() on new root node
                        f_idx = l_parentRightLeft;
                    }
                    else
                    {
                        // do nothing
                    }
                }
                else
                {
                    f_idx = l_parent;
                }
            }
            // case 2: p is right child of parent
            else if ((isRightChild(f_idx) && (f_child1 == false)) || (f_child2 == true))
            {
                if (f_child2 == true)
                {
                    l_parent = f_idx;
                    f_child2 = false;
                }

                const balance_type l_balanceParent = m_balance[l_parent];

                changeBalance(l_parent, -1);
                if (l_balanceParent == leftHeavy)
                {
                    // Get left child of parent (node `q` in the book)
                    const index_type l_parentLeft = m_left[l_parent];

                    const balance_type l_balanceParentLeft = m_balance[l_parentLeft];

                    if (l_balanceParentLeft == balanced)
                    {
                        rotate_right(l_parent);
                        return;
                    }
                    else if (l_balanceParentLeft == leftHeavy)
                    {
                        rotate_right(l_parent);

                        // perform upout() on new root node
                        f_idx = l_parentLeft;
                    }
                    else if (l_balanceParentLeft == rightHeavy)
                    {
                        // get right child of q => must exist since balance is +1
                        const index_type l_parentLeftRight = m_right[l_parentLeft];

                        rotate_leftright(l_parent);

                        // perform upout() on new root node
                        f_idx = l_parentLeftRight;
                    }
                    else
                    {
                        // do nothing
                    }
                }
                else
                {
                    f_idx = l_parent;
                }
            }
            else
            {
                // do nothing
            }
        }
    }

    /// Relink the node after swaping the last node with removed element node.
    /// @param f_idx removed element node, f_lastIdx index of last node.
    void moveLastNode(index_type f_idx, index_type f_lastIdx)
    {
        // Update the references of the swaped node.
        m_balance[f_idx] = m_balance[f_lastIdx];
        m_left[f_idx]    = m_left[f_lastIdx];
        m_right[f_idx]   = m_right[f_lastIdx];
        m_parent[f_idx]  = m_parent[f_lastIdx];

        // Fix left child node reference.
        const index_type l_left = m_left[f_lastIdx];
        if (!isInvalidIdx(l_left))
        {
            m_parent[l_left] = f_idx;
        }

        // Fix right child node reference.
        const index_type l_right = m_right[f_lastIdx];
        if (!isInvalidIdx(l_right))
        {
            m_parent[l_right] = f_idx;
        }

        // Fix parent node reference.
        const index_type l_parent = m_parent[f_lastIdx];
        if (!isInvalidIdx(l_parent))
        {
            if (m_right[l_parent] == f_lastIdx)
            {
                m_right[l_parent] = f_idx;
            }
            else if (m_left[l_parent] == f_lastIdx)
            {
                m_left[l_parent] = f_idx;
            }
            else
            {
                // do nothing
            }
        }

        // Fix root node reference.
        if (isRoot(f_lastIdx))
        {
            m_root = f_idx;
        }
    }

    /// Find symmetric successor of current node.
    /// The symmetric successor is the lowest value on the right subtree of the current node.
    /// @param f_idx Current node index to find successor for.
    /// @return Index of successor or `-1` in case of no successor found.
    auto symSuccessor(const index_type f_idx) -> index_type
    {
        index_type l_idx = m_right[f_idx];
        index_type l_res = l_idx;

        while (!isInvalidIdx(l_idx))
        {
            l_res = l_idx;
            l_idx = m_left[l_idx];
        }

        return l_res;
    }

    /// Find symmetric predecessor of current node.
    /// The symmetric predecessor is the highest value on the left subtree of the current node.
    /// @param f_idx Current node index to find predecessor for.
    /// @return Index of predecessor or `-1` in case of no predecessor found.
    auto symPredecessor(const index_type f_idx) -> index_type
    {
        index_type l_idx = m_left[f_idx];
        index_type l_res = l_idx;

        while (!isInvalidIdx(l_idx))
        {
            l_res = l_idx;
            l_idx = m_right[l_idx];
        }

        return l_res;
    }

    /// Relink nodes(parent, left, right, root) after removing a node from the tree.
    void linkNodesAfterDelete(const index_type f_idx, const index_type f_IndexPredSucc)
    {
        const index_type l_parent = m_parent[f_idx];
        const index_type l_left   = m_left[f_idx];
        const index_type l_right  = m_right[f_idx];

        // set new children links
        m_left[f_IndexPredSucc]  = l_left;
        m_right[f_IndexPredSucc] = l_right;
        // set new parent of predecessor/successor
        m_parent[f_IndexPredSucc] = l_parent;
        // set new parent ID on children
        if (!isInvalidIdx(l_right))
        {
            m_parent[l_right] = f_IndexPredSucc;
        }
        if (!isInvalidIdx(l_left))
        {
            m_parent[l_left] = f_IndexPredSucc;
        }
        // set balance => will be adjusted by upout()
        m_balance[f_IndexPredSucc] = m_balance[f_idx];

        // replace `f_idx` with predecessor/successor
        if (isRoot(f_idx))
        {
            // reassign root node
            m_root = f_IndexPredSucc;
        }
        else if (isLeftChild(f_idx))
        {
            m_left[m_parent[f_idx]] = f_IndexPredSucc;
        }
        else if (isRightChild(f_idx))
        {
            m_right[m_parent[f_idx]] = f_IndexPredSucc;
        }
        else
        {
            // do nothing
        }
    }

    /// Replace current node with predecessor/successor and fix balance.
    void replace_currentNodeWithPredSucc(const index_type f_idx, const index_type f_IndexPredSucc)
    {
        const index_type l_parent = m_parent[f_idx];
        // replace `f_idx` with predecessor/successor
        if (isRoot(f_idx))
        {
            // reassign root node
            m_root = f_IndexPredSucc;
        }
        else
        {
            if (isLeftChild(f_idx))
            {
                m_left[l_parent] = f_IndexPredSucc;
            }
            else if (isRightChild(f_idx))
            {
                m_right[l_parent] = f_IndexPredSucc;
            }
            else
            {
                // do nothing
            }
            // fix balance
            upout(f_IndexPredSucc, false, false);
        }
    }

    /// Move/replace the current node with its symmetric successor.
    /// @param f_idx Current node to replace with successor.
    void moveSuccessor(const index_type f_idx)
    {
        VFC_ASSERT2(!isInvalidIdx(f_idx), "index node must be a valid node");

        // get symmetric successor of current node
        const index_type l_succ = symSuccessor(f_idx);

        // make sure successor is available
        VFC_ASSERT2(!isInvalidIdx(l_succ), "successor node must be a valid node");

        // get relatives of current node
        const index_type l_parent = m_parent[f_idx];
        const index_type l_left   = m_left[f_idx];

        // get relatives of successor
        const index_type l_succParent = m_parent[l_succ];
        const index_type l_succRight  = m_right[l_succ];

        // parent of successor should always be available
        VFC_ASSERT2(!isInvalidIdx(l_succParent), "Parent of successor must be a valid index");

        // get balances
        const balance_type l_balance = m_balance[f_idx];

        // make sure balance of parent node is "right-heavy"
        VFC_ASSERT2(l_balance >= balanced, "parent node must be balanced or right heavy");

        // check if parent of successor `f_predParent` is current node `f_idx`
        if (f_idx == l_succParent)
        {
            // in this case `l_succ` should always be the right child
            VFC_ASSERT2(isRightChild(l_succ), "l_succ must be a right child");

            m_left[l_succ] = l_left;

            // update parent link on successor
            m_parent[l_succ] = l_parent;

            // set new parent ID on left child
            if (!isInvalidIdx(l_left))
            {
                m_parent[l_left] = l_succ;
            }

            // decrement balance by one
            m_balance[l_succ] = static_cast<balance_type>(static_cast<vfc::int32_t>(m_balance[f_idx]) - 1);

            replace_currentNodeWithPredSucc(f_idx, l_succ);
        }
        else
        {
            // in this case successor should always be a left child
            VFC_ASSERT2(isLeftChild(l_succ), "l_succ must be a left child");

            // check if successor has a right child which needs relinking
            if (!isInvalidIdx(l_succRight))
            {
                // set proper parent link of child
                m_parent[l_succRight] = m_parent[l_succ];

                // set proper left/right link of parent
                m_left[l_succParent] = l_succRight;
            }
            else
            {
                // unlink successor from its parent
                m_left[l_succParent] = -1;
            }

            linkNodesAfterDelete(f_idx, l_succ);

            bool l_node = true;
            // perform upout() on previous parent of predecessor
            upout(l_succParent, l_node, false);
        }
    }

    /// Move/replace the current node with its symmetric predecessor.
    /// Does not preserce balancing of tree.
    /// @param f_idx Current node to replace with predecessor.
    void movePredecessor(const index_type f_idx)
    {
        VFC_ASSERT2(!isInvalidIdx(f_idx), "index node must be a valid node");

        // get symmetric predecessor of current node
        const index_type l_pred = symPredecessor(f_idx);

        // make sure predecessor is available
        VFC_ASSERT2(!isInvalidIdx(l_pred), "predecessor node must be a valid node");

        // get relatives of current node
        const index_type l_parent = m_parent[f_idx];
        const index_type l_right  = m_right[f_idx];

        // get relatives of predecessor
        const index_type l_predParent = m_parent[l_pred];
        const index_type l_predLeft   = m_left[l_pred];

        // parent of predecessor should always be available
        VFC_ASSERT2(!isInvalidIdx(l_predParent), "parent of predecessor must be a valid index");

        // get balances
        const balance_type l_balance = m_balance[f_idx];

        // make sure balance of parent node is "left-heavy"
        VFC_ASSERT2(l_balance <= balanced, "parent node must be balanced or left heavy");

        // check if parent of predecessor `f_predParent` is current node `f_idx`
        if (f_idx == l_predParent)
        {
            // in this case `l_pred` should always be the left child
            VFC_ASSERT2(isLeftChild(l_pred), "l_pred must be a left child");

            m_right[l_pred] = l_right;

            // update parent link on predecessor
            m_parent[l_pred] = l_parent;

            // set new parent ID on right child
            if (!isInvalidIdx(l_right))
            {
                m_parent[l_right] = l_pred;
            }

            // increment balance by one
            m_balance[l_pred] = static_cast<balance_type>(static_cast<vfc::int32_t>(m_balance[f_idx]) + 1);

            replace_currentNodeWithPredSucc(f_idx, l_pred);
        }
        else
        {
            // in this case predecessor should always be a right child
            VFC_REQUIRE2(isRightChild(l_pred), "l_pred must be a right child");

            // check if predecessor has a left child which needs relinking
            if (!isInvalidIdx(l_predLeft))
            {
                // set proper parent link of child
                m_parent[l_predLeft] = m_parent[l_pred];

                // set proper left/right link of parent
                m_right[l_predParent] = l_predLeft;
            }
            else
            {
                m_right[l_predParent] = -1;
            }
            linkNodesAfterDelete(f_idx, l_pred);

            bool l_node = true;
            // perform upout() on previous parent of predecessor
            upout(l_predParent, false, l_node);
        }
    }

    /// Swap predecessor/successor based on the tree balance.
    void movePredSucc(const index_type f_idx)
    {
        if (m_balance[f_idx] <= balanced)
        {
            movePredecessor(f_idx);
        }
        else
        {
            moveSuccessor(f_idx);
        }
    }

    // Decrement the size by one after deleting an element
    // Fix node after swapping with last node.
    void resize(const index_type f_idx)
    {
        if (m_next == 0)
        {
            return;
        }
        VFC_ASSERT(!isInvalidIdx(f_idx));
        VFC_ASSERT(!empty());

        // decrement `next` index and get its index
        m_next -= 1;
        const index_type l_last = m_next;

        moveLastNode(f_idx, l_last);
    }
    /// Remove root node from the map.
    void remove_root()
    {
        const index_type l_idx = m_root;

        VFC_ASSERT2(m_next > l_idx, "Next empty node index should be greater than the root node index");

        // check if index is last node
        if (isLeaf(l_idx))
        {
            clear();
        }
        else
        {
            movePredSucc(l_idx);
        }
        resize(l_idx);
    }

    /// Remove left child of `f_parent` node
    /// @param f_parent is the Parent node index
    void remove_left(const index_type f_parent)
    {
        VFC_ASSERT2(f_parent != -1, "f_parent index must be a non empty node");
        VFC_ASSERT2(m_left[f_parent] != -1, "left node of f_parent must be a valid index");

        const index_type l_idx = m_left[f_parent];

        // case 1: current node is a leaf (has no children)
        if (isLeaf(l_idx))
        {
            // unlink node from parent and increment balance
            m_left[f_parent] = -1;
            changeBalance(f_parent, 1);

            // the node to perform upout() on:
            index_type l_upout = f_parent;

            if (m_balance[f_parent] > rightHeavy)
            {
                // with p removed (marked as `[ ]`) the map looks like this:
                //
                // ~~~
                //        phi                     phi                   phi       ""|
                //       /   \                   /   \                 /   \      ""|
                //     [ ]    x       or       [ ]    x       or     [ ]    x     ""|
                //           / \                     /                       \    ""|
                //          y   z                   y                         z   ""|
                // ~~~
                //
                // In the second case a double right-left rotation is required to
                // get back to balance, in both other cases a single left rotation
                // on `phi` is sufficient

                const index_type   l_parentRight        = m_right[f_parent];
                const balance_type l_balanceParentRight = m_balance[l_parentRight];

                if (l_balanceParentRight == leftHeavy)
                {
                    // new root will be parent->right->left
                    l_upout = m_left[l_parentRight];

                    rotate_rightleft(f_parent);
                }
                else
                {
                    // new root will be parent->right
                    l_upout = l_parentRight;
                    rotate_left(f_parent);
                }
            }

            // fix balance of parent maps
            upout(l_upout, false, false);
        }

        // case 2: p only has one child q
        else if (isInvalidIdx(m_left[l_idx]) != isInvalidIdx(m_right[l_idx]))
        {
            index_type l_q = -1;
            if (m_left[l_idx] == -1)
            {
                VFC_ASSERT(isLeaf(m_right[l_idx]));
                l_q = m_right[l_idx];
            }
            else
            {
                VFC_ASSERT2(m_right[l_idx] == -1, "Node most not have a right child");
                VFC_ASSERT(isLeaf(m_left[l_idx]));
                l_q = m_left[l_idx];
            }

            VFC_ASSERT2(!isInvalidIdx(l_q), "l_q must be either left node or right node");
            VFC_ASSERT2(m_balance[l_q] == balanced, "l_q should have same no of left and right nodes");

            // replace p with q
            m_left[f_parent] = l_q;
            m_parent[l_q]    = f_parent;

            // call upout()
            upout(l_q, false, false);
        }

        // case 3: both children of p are inner nodes
        else
        {
            movePredSucc(l_idx);
        }
        resize(l_idx);
    }

    /// Remove right child of `f_parent` node.
    /// @param f_parent is the Parent node index.
    void remove_right(const index_type f_parent)
    {
        VFC_ASSERT2(f_parent != -1, "f_parent index must be a non empty node");
        VFC_ASSERT2(m_right[f_parent] != -1, "right node of f_parent must be a valid index");

        const index_type l_idx = m_right[f_parent];

        // case 1: current node is a leaf (has no children)
        if (isLeaf(m_right[f_parent]))
        {
            // unlink node from parent and increment balance
            m_right[f_parent] = -1;
            changeBalance(f_parent, -1);

            // the node to perform upout() on:
            index_type l_upout = f_parent;

            if (m_balance[f_parent] < leftHeavy)
            {
                // with p removed (marked as `[ ]`) the map looks like this:
                //
                // ~~~
                //        phi                     phi                   phi       ""|
                //       /   \                   /   \                 /   \      ""|
                //      x    [ ]      or        x    [ ]      or      x    [ ]    ""|
                //     / \                     /                       \          ""|
                //    y   z                   y                         z         ""|
                // ~~~
                //
                // In the third case a double left-right rotation is required to
                // get back to balance, in both other cases a single right rotation
                // on `phi` is sufficient

                const index_type   l_parentLeft        = m_left[f_parent];
                const balance_type l_balanceParentLeft = m_balance[l_parentLeft];

                if (l_balanceParentLeft == rightHeavy)
                {
                    // new root will be parent->right->left
                    l_upout = m_right[l_parentLeft];

                    rotate_leftright(f_parent);
                }
                else
                {
                    // new root will be parent->right
                    l_upout = l_parentLeft;
                    rotate_right(f_parent);
                }
            }

            // fix balance of parent maps
            upout(l_upout, false, false);
        }

        // case 2: p only has one child q
        else if (isInvalidIdx(m_left[l_idx]) != isInvalidIdx(m_right[l_idx]))
        {
            index_type l_q = -1;
            if (m_left[l_idx] == -1)
            {
                VFC_ASSERT(isLeaf(m_right[l_idx]));
                l_q = m_right[l_idx];
            }
            else
            {
                VFC_ASSERT2(m_right[l_idx] == -1, "Node most not have a right child");
                VFC_ASSERT(isLeaf(m_left[l_idx]));
                l_q = m_left[l_idx];
            }

            VFC_ASSERT2(!isInvalidIdx(l_q), "l_q should be either left node or right node");
            VFC_ASSERT2(m_balance[l_q] == balanced, "l_q should have same no of left and right nodes");

            // replace p with q
            m_right[f_parent] = l_q;
            m_parent[l_q]     = f_parent;

            // call upout()
            upout(l_q, false, false);
        }

        // case 3: both children of p are inner nodes
        else
        {
            movePredSucc(l_idx);
        }
        resize(l_idx);
    }

    /// Index pointing to current root node
    index_type m_root;
    /// Index pointing to the next empty node
    index_type m_next;

    template <class Type>
    using Storage = TCArray<Type, CapacityValue>;
    Storage<index_type>   m_parent;  //< Parent node storage
    Storage<index_type>   m_left;    //< Left node storage
    Storage<index_type>   m_right;   //< Right node storage
    Storage<balance_type> m_balance; //< Balance value of the respective node
};                                   // struct TFixedMapBase

// ==================================================================================
template <class KeyType, class MappedType, vfc::int32_t CapacityValue>
struct TConstMapIterator;

template <class KeyType, class MappedType, vfc::int32_t CapacityValue>
struct TFixedMapImpl
{
    static_assert(CapacityValue >= 0, "Capacity value of TFixedMap must be non-negative");
    using assertLevel    = typename vfc::attributes::SoftwareQualifiedType<KeyType>::Prototype::type_t;
    using Base           = TFixedMapBase<CapacityValue>;
    using const_iterator = TConstMapIterator<KeyType, MappedType, CapacityValue>;
    using MaxSize        = typename Base::MaxSize;
    using size_type      = typename Base::size_type;

    /// Type used for indices in the map structure.
    using index_type = typename Base::index_type;
    /// Type used for balance storage in the map structure.
    using balance_type = typename Base::balance_type;
    /// Type used for key definitions in the map structure.
    using key_type = KeyType;
    /// Type used for mapped values in the map structure.
    using mapped_type = MappedType;

    /// A type that provides a key value pair of KeyType and MappedType type.
    using value_type = std::pair<key_type, mapped_type>;

    /// A return type when iterator and a bool indicating insertion is returned.
    using ret_pair = std::pair<bool, bool>;

    /// Default constructor.
    TFixedMapImpl() = default;

    /// Copy constructor.
    /// @param f_obj values to be copied to map.
    TFixedMapImpl(const TFixedMapImpl& f_obj) = default;

    /// Copy assignment operator.
    /// @param f_otherMap replaces the contents with a copy of the contents of f_otherMap.
    /// @return pointer to the current object.
    TFixedMapImpl& operator=(const TFixedMapImpl& f_otherMap)
    {
        vfc::nop(f_otherMap);
        VFC_ASSERT(false);
        return *this;
    }

    /// Move assignment operator.
    /// @param f_otherMap replaces the contents with those of f_otherMap using move semantics.
    /// @return pointer to the current object.
    TFixedMapImpl& operator=(TFixedMapImpl&& f_otherMap)
    {
        vfc::nop(f_otherMap);
        VFC_ASSERT(false);
        return *this;
    }

    /// Default destructor.
    ~TFixedMapImpl() { VFC_ASSERT(false); }

    /// Clear the map.
    void clear() { VFC_ASSERT(false); }

    /// Check the map is empty or not.
    bool empty() const { return m_base.empty(); }

    /// Return size of the map.
    auto size() const -> size_type { return m_base.size(); }

    /// Return Capacity of the map.
    static constexpr auto max_size() -> size_type { return MaxSize::value; }

    /// Return root node of the map.
    auto root() const -> index_type { return m_base.m_root; }

    /// Return left node of the current index.
    auto left(const index_type f_idx) const -> index_type { return m_base.m_left[f_idx]; }

    /// Return right node of the current index.
    auto right(const index_type f_idx) const -> index_type { return m_base.m_right[f_idx]; }

    /// Return reference of the key element.
    auto key(const index_type f_idx) -> key_type& { return m_key[f_idx].getObj(); }

    /// Return constant reference of the key element.
    auto key(const index_type f_idx) const -> const key_type& { return m_key[f_idx].getObj(); }

    /// Return reference of the mapped element.
    auto val(const index_type f_idx) -> mapped_type& { return m_val[f_idx].getObj(); }

    /// Return constant reference of the mapped element.
    auto val(const index_type f_idx) const -> const mapped_type& { return m_val[f_idx].getObj(); }

    /// Return balance of the current index.
    auto balance(const index_type f_idx) const -> balance_type { return m_base.m_balance[f_idx]; }

    /// Return parent node of the current index.
    auto parent(const index_type f_idx) const -> index_type { return m_base.m_parent[f_idx]; }

    static bool isInvalidIdx(const index_type f_idx) { return Base::isInvalidIdx(f_idx); }

    bool isRoot(const key_type f_key) const { return m_base.isRoot(lookupIndex(f_key)); }

    bool isLeaf(const key_type f_key) const { return m_base.isLeaf(lookupIndex(f_key)); }

    bool isLeftChild(const key_type f_key) const { return m_base.isLeftChild(lookupIndex(f_key)); }

    bool isRightChild(const key_type f_key) const { return m_base.isRightChild(lookupIndex(f_key)); }

    /// Lookup the index of a key.
    /// @param f_key Key to be looked up.
    /// @return Index of key in map or `-1` if not found.
    auto lookupIndex(const key_type& f_key) const -> index_type
    {
        vfc::nop(f_key);
        VFC_ASSERT(false);
        return -1;
    }

    void insert(std::initializer_list<value_type> f_args) // we need to configure qacpp-2008 differently
    {
        for (auto it : f_args)
        {
            ignoreReturn(insert(it, false));
        }
    }

    /// Insert a new key.
    /// @param f_pair The key to insert, ref bool parameter to decide to use move or copy of elements.
    /// @return a pair consisting of an iterator to the inserted element or
    ///         to the element that prevented the insertion
    ///         and a bool denoting whether the insertion took place.
    // Consider to remove the hack with 'ref'!
    auto insert(value_type f_pair, bool ref) -> ret_pair
    {
        vfc::nop(f_pair);
        vfc::nop(ref);
        VFC_ASSERT(false);
        return ret_pair{false, false};
    }

    template <typename MappedValType>
    auto insert_or_assign(const key_type& f_key, MappedValType&& f_obj) -> ret_pair
    {
        vfc::nop(f_key);
        vfc::nop(f_obj);
        VFC_ASSERT(false);
        return ret_pair{false, false};
    }

    /// Swap last element with current index.
    /// @param f_idx Index of empty node.
    void swapLast(const index_type f_idx)
    {
        vfc::nop(f_idx);
        VFC_ASSERT(false);
    }

    /// Destruct a node after deleting an element.
    /// Destructs last node after swapping with deleted element.
    void destruct() { VFC_ASSERT(false); }

    /// Remove node by key and reestablish tree balance.
    /// @warning This invalidates all key/value/index references.
    /// @param f_key Key to be removed.
    void remove(const key_type& f_key)
    {
        vfc::nop(f_key);
        VFC_ASSERT(false);
    }

    /// Erase node by key and reestablish tree balance.
    /// @warning This invalidates all key/value/index references.
    /// @param f_key Key to be removed.
    auto erase(const key_type& f_key) -> size_type
    {
        vfc::nop(f_key);
        VFC_ASSERT(false);
        return 0;
    }

    /// Counts the frequency of key present in the cotainer.
    /// @return 1 if the container contains an element whose key is equivalent to f_key,
    /// or zero otherwise.
    /// @param f_key Key to be count.
    auto count(const key_type& f_key) const -> size_type
    {
        vfc::nop(f_key);
        VFC_ASSERT(false);
        return 0;
    }

    /// Checks if there is an element with key equivalent to f_key in the container.
    /// @param f_key Key to be looked up.
    /// @returns true if there is such an element, otherwise false.
    bool contains(const key_type& f_key) const
    {
        vfc::nop(f_key);
        VFC_ASSERT(false);
        return false;
    }

    /// To swap the contents of two container.
    /// @param f_otherMap object of the container which swaps with the current object of the same class.
    template <typename map_type>
    void swap(map_type& f_otherMap)
    {
        vfc::nop(f_otherMap);
    }

    Base m_base;

    template <class Type>
    using Storage = TCArray<Type, CapacityValue>;

    using key_storage_type = TCArray<TObjectStorage<key_type>, CapacityValue>;
    key_storage_type m_key; // For key type storage
    using val_storage_type = TCArray<TObjectStorage<mapped_type>, CapacityValue>;
    val_storage_type m_val; // For val type storage
};

//=============================================================================
//  TConstMapIterator
//-----------------------------------------------------------------------------
/// TConstMapIterator provides a bidirectional iterator that
/// can read a const element in a balanced map.
/// $Source:        vfc_fixedmap.inl $
/// @ingroup:       vfc_containers
//=============================================================================
template <class KeyType, class MappedType, vfc::int32_t CapacityValue>
struct TConstMapIterator
{
  public:
    using self_type   = TConstMapIterator;
    using mapped_type = MappedType;
    using key_type    = KeyType;
    using Impl        = TFixedMapImpl<KeyType, MappedType, CapacityValue>;
    using index_type  = typename Impl::index_type;
    // using value_type = typename Impl::value_type;
    using value_type = std::pair<key_type&, mapped_type&>;

    /// Default constructor.
    TConstMapIterator() = delete;

    /// Default destructor.
    ~TConstMapIterator() = default;

    /// Copy constructor.
    /// @param f_iter values to be filled for map - index, map pointer, key, value.
    TConstMapIterator(const TConstMapIterator& f_iter)
    {
        vfc::nop(f_iter);
        VFC_ASSERT(false);
    }

    /// Assignment operator.
    /// @param f_iter values to be filled for map - index, map pointer, key, value.
    TConstMapIterator& operator=(TConstMapIterator const& f_iter)
    {
        vfc::nop(f_iter);
        VFC_ASSERT(false);
        return *this;
    }

    /// Returns a pointer to the object.
    auto operator->() -> value_type*
    {
        VFC_ASSERT(false);
        return nullptr;
    }

    /// Prefix increment operator.
    /// @return reference to the object.
    auto operator++() -> self_type&
    {
        VFC_ASSERT(false);
        return *this;
    }

    /// Postfix increment operator
    /// @return reference to the object.
    auto operator++(vfc::int32_t) -> self_type&
    {
        ++(*this);
        return *this;
    }

    /// Prefix decrement operator.
    /// @return reference to the object.
    auto operator--() -> self_type&
    {
        VFC_ASSERT(false);
        return *this;
    }

    /// Postfix decrement operator.
    /// @return reference to the object.
    auto operator--(vfc::int32_t) -> self_type&
    {
        --(*this);
        return *this;
    }

  private:
    /// @brief `TFixedMapImpl` can access private members of TConstMapIterator.
    // qacpp-2107-R2: Friend Declaration needed for limited, specific access to members.
    friend vfc::intern::TFixedMapImpl<KeyType, MappedType, CapacityValue>; // PRQA S 2107 # R2

    /// Parameterised constructor.
    /// @param f_index index  and f_mapimpl_p pointer to TFixedMap map.
    TConstMapIterator(vfc::int32_t f_index, const Impl* f_mapimpl_p)
    {
        vfc::nop(f_index);
        vfc::nop(f_mapimpl_p);
        VFC_ASSERT(false);
    }
};

/// Equality comparision operator.
/// @param [f_iter] values to be compared for map.
/// @return true if containers are equal, false otherwise.
template <class KeyType, class MappedType, vfc::int32_t CapacityValue>
bool operator==(
    TConstMapIterator<KeyType, MappedType, CapacityValue> const& f_liter,
    TConstMapIterator<KeyType, MappedType, CapacityValue> const& f_riter)
{
    vfc::nop(f_liter);
    vfc::nop(f_riter);
    VFC_ASSERT(false);
    return false;
}

/// Inequality comparision operator.
/// @param [f_iter] values to be compared for map.
/// @return true if containers are not equal, false otherwise.
template <class KeyType, class MappedType, vfc::int32_t CapacityValue>
bool operator!=(
    TConstMapIterator<KeyType, MappedType, CapacityValue> const& f_liter,
    TConstMapIterator<KeyType, MappedType, CapacityValue> const& f_riter)
{
    return !operator==(f_liter, f_riter);
}
} // namespace intern
} // namespace vfc

#endif

