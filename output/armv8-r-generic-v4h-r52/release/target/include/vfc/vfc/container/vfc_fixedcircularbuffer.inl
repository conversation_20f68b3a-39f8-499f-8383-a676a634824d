//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_FIXEDCIRCULARBUFFER_INL_INCLUDED
#define ZX_VFC_FIXEDCIRCULARBUFFER_INL_INCLUDED

#include <algorithm>               // lexicographical_compare
#include "vfc/core/vfc_types.hpp"  // used for int32_t
#include "vfc/core/vfc_assert.hpp" // used for VFC_ASSERT()

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::TFixedCircularBuffer()
    : m_storage(), m_readIndex(0), m_writeIndex(0), m_size(0)
{
}

// Copy Constructor
template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::TFixedCircularBuffer(
    const circularBuffer_type& f_param_r)
    : m_storage(), m_readIndex(0), m_writeIndex(0), m_size(0)
{
    operator=(f_param_r);
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::TFixedCircularBuffer(
    const AllocatorType& f_alloc)
    : m_storage(), m_readIndex(0), m_writeIndex(0), m_size(0)
{
    vfc::nop(f_alloc);
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>&
vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::operator=(
    const TFixedCircularBuffer<value_type, CapacityValue, AllocatorType>& f_param_r)
{
    if (this != &f_param_r)
    {
        //  clear the circular buffer
        clear();

        const ValueType* const readBase = f_param_r.m_storage.typed_begin();

        if (!f_param_r.empty())
        {
            // if readindex < writeindex , push elements to the Destination
            // circularBuffer from readindex to the writeindex
            if (f_param_r.m_readIndex < f_param_r.m_writeIndex)
            {
                int32_t readIndex = f_param_r.m_readIndex;

                while (readIndex != f_param_r.m_writeIndex)
                {
                    const ValueType& readItem = readBase[readIndex];
                    this->push(readItem);
                    readIndex++;
                }
            }

            // if readindex >= writeindex , push elements to the Destination
            // from readindex till end of circularBuffer and from starting index
            // till writeindex
            else
            {
                for (int32_t readIndex = f_param_r.m_readIndex; readIndex <= BufferSize - 1; readIndex++)
                {
                    const ValueType& readItem = readBase[readIndex];
                    this->push(readItem);
                }
                for (int32_t readIndex = 0; readIndex < f_param_r.m_writeIndex; readIndex++)
                {
                    const ValueType& readItem = readBase[readIndex];
                    this->push(readItem);
                }
            }
        }
    }
    return *this;
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::~TFixedCircularBuffer() VFC_NOEXCEPT
{
    clear();

    m_readIndex  = 0;
    m_writeIndex = 0;
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline void vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::push_intern(const value_type* f_item_p)
{
    ValueType* const storageBase = m_storage.typed_begin();
    ValueType* const item        = &storageBase[m_writeIndex];
    if (BufferSize == m_size)
    {
        item->~ValueType(); // PRQA S 3803 # Technical debt L2 id=00c1fcfb8e2b80174e5d-a40bed5743edcfed // PRQA S 3804 #
                            // Technical debt L2 id=00c19ecb4f17017e4eb7-2ff2cc4e0516d95a
    }

    if (nullptr != f_item_p)
    {
        // construct the value
        const value_type* const retPtr = new (item) value_type(*f_item_p);
        vfc::nop(retPtr);
    }
    // else is null pointer, leave newnode_p's payload completely uninitialized

    if (true == IsOverFlow())
    {
        m_readIndex = incrementIndex(m_readIndex);
    }
    else
    {
        ++m_size;
    }

    m_writeIndex = incrementIndex(m_writeIndex);
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline void vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::push(const value_type& f_item_r)
{
    this->push_intern(&f_item_r);
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline auto vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::push_uninitialized() -> reference
{
    this->push_intern(nullptr);
    return this->back();
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline void vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::pop()
{
    VFC_REQUIRE2(m_size > 0, "size must not be zero when calling pop");

    ValueType* const storageBase  = m_storage.typed_begin();
    ValueType* const destructItem = &storageBase[m_readIndex];
    destructItem->~ValueType(); // PRQA S 3803 # Technical debt L2 id=00c164b6e4bf6c9f49ce-a5803e204c0f607e // PRQA S
                                // 3804 # Technical debt L2 id=00c11693fa365fc34feb-deb837de3e67c704
    --m_size;
    m_readIndex = incrementIndex(m_readIndex);
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline auto vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::get_allocator() const
    -> allocator_type // PRQA S 4212 # Technical debt L2 id=00c1fa3387c7eaa74ccf-12eb9c00ce083dfc
{
    return allocator_type();
}

// qacpp-4211: This non const function is needed due to overload resolution
template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline auto vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::back() -> reference
{
    VFC_REQUIRE2(m_size > 0, "size must not be zero when calling back");
    ValueType* const storageBase = m_storage.typed_begin();
    int32_t          backIndex   = getDecrementIndex(m_writeIndex);
    return storageBase[backIndex];
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline auto vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::back() const -> const_reference
{
    VFC_REQUIRE2(m_size > 0, "size must not be zero when calling back");
    const ValueType* const storageBase = m_storage.typed_begin();
    int32_t                backIndex   = getDecrementIndex(m_writeIndex);
    return storageBase[backIndex];
}

// qacpp-4211: This non const function is needed due to overload resolution
template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline auto vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::front() -> reference
{
    VFC_REQUIRE2(m_size > 0, "size must not be zero when calling front");
    ValueType* const storageBase = m_storage.typed_begin();
    return storageBase[m_readIndex];
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline auto vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::front() const -> const_reference
{
    VFC_REQUIRE2(m_size > 0, "size must not be zero when calling front");
    const ValueType* const storageBase = m_storage.typed_begin();
    return storageBase[m_readIndex];
}

// qacpp-4211: This non const function is needed due to overload resolution
template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline auto vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::operator[](int32_t f_index) -> reference
{
    VFC_REQUIRE2((f_index < m_size) && (0 <= f_index), "index out of range");
    ValueType* const storageBase = m_storage.typed_begin();

    int32_t readIndex = m_readIndex + f_index;

    if (BufferSize <= readIndex)
    {
        readIndex -= BufferSize;
    }

    return storageBase[readIndex];
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline auto vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::operator[](int32_t f_index) const
    -> const_reference
{
    VFC_REQUIRE2((f_index < m_size) && (0 <= f_index), "index out of range");

    const ValueType* const storageBase = m_storage.typed_begin();

    int32_t readIndex = m_readIndex + f_index;

    if (BufferSize <= readIndex)
    {
        readIndex -= BufferSize;
    }

    return storageBase[readIndex];
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline bool vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::empty() const
{
    return (0 == m_size);
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline auto vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::size() const -> size_type
{
    return m_size;
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline auto vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::capacity() const
    -> size_type // PRQA S 4212 # Technical debt L2 id=00c1c8f5801900154dd0-7f3ef8a7157b2dce
{
    return BufferSize;
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline void vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::clear()
{
    const vfc::int32_t size = m_size;
    for (vfc::int32_t i = 0; i < size; ++i)
    {
        this->pop();
    }

    VFC_ENSURE(0 == m_size);
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline auto vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::begin() -> iterator
{
    return iterator(this, 0); // 0(zero) signify the index
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline auto vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::begin() const -> const_iterator
{
    return const_iterator(this, 0); // 0(zero) signify the index
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline auto vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::end() -> iterator
{
    return iterator(this, size()); // size() signify the index
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline auto vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::end() const -> const_iterator
{
    return const_iterator(this, size()); // size() signify the index
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline auto vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::rbegin() -> reverse_iterator
{
    return static_cast<reverse_iterator>(end());
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline auto vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::rbegin() const -> const_reverse_iterator
{
    return static_cast<const_reverse_iterator>(end());
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline auto vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::rend() -> reverse_iterator
{
    return static_cast<reverse_iterator>(begin());
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline auto vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::rend() const -> const_reverse_iterator
{
    return static_cast<const_reverse_iterator>(begin());
}

// CConstIterator Functions

template <typename FriendClass>
inline vfc::CConstIterator<FriendClass>&
vfc::CConstIterator<FriendClass>::operator=(const vfc::CConstIterator<FriendClass>& f_rhs)
{
    if (this != &f_rhs)
    {
        m_index      = f_rhs.m_index;
        m_cirBuff_cp = f_rhs.m_cirBuff_cp;
    }
    return *this;
}

template <typename FriendClass>
inline vfc::CConstIterator<FriendClass>& vfc::CConstIterator<FriendClass>::operator++()
{
    VFC_REQUIRE2(m_index < m_cirBuff_cp->size(), "must not increment beyond end()");
    m_index++;
    return *this;
}

template <typename FriendClass>
inline vfc::CConstIterator<FriendClass> vfc::CConstIterator<FriendClass>::operator++(postfix_operator_tag)
{
    VFC_REQUIRE2(m_index < m_cirBuff_cp->size(), "must not increment beyond end()");
    const self_type temp = *this;
    m_index++;
    return temp;
}

template <typename FriendClass>
inline vfc::CConstIterator<FriendClass>& vfc::CConstIterator<FriendClass>::operator--()
{
    VFC_REQUIRE2(m_index > 0, "must not decrement beyond begin()");
    --m_index;
    return *this;
}

template <typename FriendClass>
inline vfc::CConstIterator<FriendClass> vfc::CConstIterator<FriendClass>::operator--(postfix_operator_tag)
{
    VFC_REQUIRE2(m_index > 0, "must not decrement beyond begin()");
    const self_type temp = *this;
    --m_index;
    return temp;
}

template <typename FriendClass>
inline const vfc::CConstIterator<FriendClass> vfc::CConstIterator<FriendClass>::operator+(vfc::int32_t f_val) const
{
    self_type iterTemp = *this;
    iterTemp += f_val;
    return iterTemp;
}

template <typename FriendClass>
inline vfc::CConstIterator<FriendClass>& vfc::CConstIterator<FriendClass>::operator+=(vfc::int32_t f_val)
{
    VFC_REQUIRE2(f_val >= 0, "must not use negative increment with operator+=");
    VFC_REQUIRE2(m_index + f_val <= m_cirBuff_cp->size(), "must not increment beyond end()");
    m_index += f_val;
    return *this;
}

template <typename FriendClass>
inline const vfc::CConstIterator<FriendClass> vfc::CConstIterator<FriendClass>::operator-(vfc::int32_t f_val) const
{
    self_type iterTemp = *this;
    iterTemp -= f_val;
    return iterTemp;
}

template <typename FriendClass>
inline vfc::CConstIterator<FriendClass>& vfc::CConstIterator<FriendClass>::operator-=(vfc::int32_t f_val)
{
    VFC_REQUIRE2(f_val > 0, "must not use negative increment with operator-=");
    VFC_REQUIRE2(m_index - f_val >= 0, "must not decrement beyond begin()");
    m_index -= f_val;
    return *this;
}

template <typename ContainerType>
inline bool vfc::CConstIterator<ContainerType>::operator<(const self_type& f_self_type) const
{
    return (m_index < f_self_type.m_index);
}

template <typename ContainerType>
inline bool vfc::CConstIterator<ContainerType>::operator<=(const self_type& f_self_type) const
{
    return (m_index <= f_self_type.m_index);
}

template <typename ContainerType>
inline bool vfc::CConstIterator<ContainerType>::operator>(const self_type& f_self_type) const
{
    return (m_index > f_self_type.m_index);
}

template <typename ContainerType>
inline bool vfc::CConstIterator<ContainerType>::operator>=(const self_type& f_self_type) const
{
    return (m_index >= f_self_type.m_index);
}

template <typename FriendClass>
inline typename vfc::CConstIterator<FriendClass>::reference vfc::CConstIterator<FriendClass>::operator*() const
{
    VFC_REQUIRE2(this->m_index < this->m_cirBuff_cp->size(), "dereference beyond end attempted");
    return (m_cirBuff_cp->operator[](m_index));
}

template <typename FriendClass>
inline typename vfc::CConstIterator<FriendClass>::pointer vfc::CConstIterator<FriendClass>::operator->() const
{
    VFC_REQUIRE2(this->m_index < this->m_cirBuff_cp->size(), "dereference beyond end attempted");
    return &(m_cirBuff_cp->operator[](m_index));
}

template <typename ContainerType>
inline bool vfc::CConstIterator<ContainerType>::operator!=(const self_type& f_self_type) const
{
    return !(*this == f_self_type);
}

template <typename ContainerType>
inline bool vfc::CConstIterator<ContainerType>::operator==(const self_type& f_self_type) const
{
    return (m_index == f_self_type.m_index);
}

// CIterator Functions

template <typename FriendClass>
inline vfc::CIterator<FriendClass>& vfc::CIterator<FriendClass>::operator++()
{
    VFC_REQUIRE2(this->m_index < this->m_cirBuff_cp->size(), "must not increment beyond end()");
    this->m_index++;
    return *this;
}

template <typename FriendClass>
inline vfc::CIterator<FriendClass> vfc::CIterator<FriendClass>::operator++(postfix_operator_tag)
{
    VFC_REQUIRE2(this->m_index < this->m_cirBuff_cp->size(), "must not increment beyond end()");
    self_type temp = *this;
    this->m_index++;
    return temp;
}

template <typename FriendClass>
inline vfc::CIterator<FriendClass>& vfc::CIterator<FriendClass>::operator--()
{
    VFC_REQUIRE2(this->m_index > 0, "must not decrement beyond begin()");
    --(this->m_index);
    return *this;
}

template <typename FriendClass>
inline vfc::CIterator<FriendClass> vfc::CIterator<FriendClass>::operator--(postfix_operator_tag)
{
    VFC_REQUIRE2(this->m_index > 0, "must not decrement beyond begin()");
    self_type temp = *this;
    --(this->m_index);
    return temp;
}

template <typename FriendClass>
inline typename vfc::CIterator<FriendClass>::reference vfc::CIterator<FriendClass>::operator*() const
{
    VFC_REQUIRE2(this->m_index < this->m_cirBuff_cp->size(), "dereference beyond end attempted");
    return (const_cast<circularBuffer_type*>(this->m_cirBuff_cp))
        ->
        operator[](this->m_index); // PRQA S 3066 # Technical debt L4 id=00c179dcc38fd4d24b5c-48f1aca9bd550ecf
}

template <typename FriendClass>
inline typename vfc::CIterator<FriendClass>::pointer vfc::CIterator<FriendClass>::operator->() const
{
    VFC_REQUIRE2(this->m_index < this->m_cirBuff_cp->size(), "dereference beyond end attempted");
    return &((const_cast<circularBuffer_type*>(this->m_cirBuff_cp))
                 ->
                 operator[](this->m_index)); // PRQA S 3066 # Technical debt L4 id=00c14ba39f786a5b487c-f2652997bdb2f9be
}

template <typename FriendClass>
inline const vfc::CIterator<FriendClass> vfc::CIterator<FriendClass>::operator+(vfc::int32_t f_val) const
{
    self_type iterTemp = *this;
    iterTemp += f_val;
    return iterTemp;
}

template <typename FriendClass>
inline vfc::CIterator<FriendClass>& vfc::CIterator<FriendClass>::operator+=(vfc::int32_t f_val)
{
    VFC_REQUIRE2(f_val >= 0, "must not use negative increment with operator+=");
    VFC_REQUIRE2(this->m_index + f_val <= this->m_cirBuff_cp->size(), "must not increment beyond end()");

    this->m_index += f_val;
    return *this;
}

template <typename FriendClass>
inline const vfc::CIterator<FriendClass> vfc::CIterator<FriendClass>::operator-(vfc::int32_t f_val) const
{
    self_type iterTemp = *this;
    iterTemp -= f_val;
    return iterTemp;
}

template <typename FriendClass>
inline vfc::CIterator<FriendClass>& vfc::CIterator<FriendClass>::operator-=(vfc::int32_t f_val)
{
    VFC_REQUIRE2(f_val > 0, "must not use negative increment with operator-=");
    VFC_REQUIRE2(this->m_index - f_val >= 0, "must not decrement beyond begin()");

    this->m_index -= f_val;
    return *this;
}

// Global Functions

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline bool vfc::operator==(
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer1_r,
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer2_r)
{
    if (f_cirBuffer1_r.size() != f_cirBuffer2_r.size())
    {
        return false;
    }
    else
    {
        typename vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::const_iterator l_it1 =
            f_cirBuffer1_r.begin();
        typename vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::const_iterator l_it2 =
            f_cirBuffer2_r.begin();
        // Dubious QACPP-3804: vfc::ignoreReturn is used to avoid QACPP warning 3804 for ignoring return value.
        // With ignoreReturn, the increment of the iterators cannot be done inside a for-loop statement or we would get
        // QACPP warning 3369 about having a post-increment used as a sub-expression in a for loop
        // Therfore, we use a while loop instead.
        while (l_it1 != f_cirBuffer1_r.end())
        {
            if (*l_it1 != *l_it2) // PRQA S 3270 # Technical debt L2 id=00c1f0cf0bb9109a45b2-a74baa6b592123de
            {
                return false;
            }
            vfc::ignoreReturn(++l_it1); // PRQA S 3360 # Technical debt L2 id=00c1bffbf0a833464e1a-dd0aeaf2dad05096
            vfc::ignoreReturn(++l_it2); // PRQA S 3360 # Technical debt L2 id=00c10511932a302f4f59-e27d1d71d53462d8
        }
    }
    return true;
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline bool vfc::operator!=(
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer1_r,
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer2_r)
{
    return !(f_cirBuffer1_r == f_cirBuffer2_r);
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline bool vfc::operator<(
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer1_r,
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer2_r)
{
    return stlalias::lexicographical_compare(
        f_cirBuffer1_r.begin(), f_cirBuffer1_r.end(), f_cirBuffer2_r.begin(), f_cirBuffer2_r.end());
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline bool vfc::operator>(
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer1_r,
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer2_r)
{
    return f_cirBuffer2_r < f_cirBuffer1_r;
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline bool vfc::operator<=(
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer1_r,
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer2_r)
{
    return !(f_cirBuffer2_r < f_cirBuffer1_r);
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline bool vfc::operator>=(
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer1_r,
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer2_r)
{
    return !(f_cirBuffer1_r < f_cirBuffer2_r);
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline bool vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::IsOverFlow() const
{
    // check if we reach the last read element
    if ((m_size + 1) > BufferSize)
    {
        return true;
    }
    else
    {
        return false;
    }
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline vfc::int32_t vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::incrementIndex(int32_t f_index)
{
    if (f_index == BufferSize - 1)
    {
        return 0;
    }
    return f_index + 1;
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline vfc::int32_t
vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>::getDecrementIndex(int32_t f_writeIndex)
{
    if (f_writeIndex == 0)
    {
        return BufferSize - 1;
    }
    else
    {
        return f_writeIndex - 1;
    }
}

#endif // ZX_VFC_FIXEDCIRCULARBUFFER_INL_INCLUDED

