//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_CARRAY_HPP_INCLUDED
#define ZX_VFC_CARRAY_HPP_INCLUDED

#include "vfc/core/vfc_types.hpp"            //for vfc::int32_t
#include "vfc/core/vfc_static_assert.hpp"    //for VFC_STATIC_ASSERT
#include "vfc/core/vfc_checked_iterator.hpp" //checkable_iterator
#include "vfc/core/vfc_iterator.hpp"         //reverse_iterator

namespace vfc
{
//=============================================================================
//  TCArray<>
//-----------------------------------------------------------------------------
/// TCArray is a simple wrapper for a c-array for cases where the TFixedVector
/// is too big.
/// @par Description:
/// A template class representing a TCArray.
/// @tparam ValueType       DataType to be stored
/// @tparam CapacityValue   Capacity of the Array

/// @ingroup                vfc_group_containers
//=============================================================================
template <class ValueType, vfc::int32_t CapacityValue>
class TCArray
{
  public:
    VFC_STATIC_ASSERT(CapacityValue > 0);

    enum
    {
        ArraySize = CapacityValue /// The static array size defined at compile time.
    };

    enum
    {
        VFC_ATR_DEPRECATED_ENUM_VALUE2(ARRAY_SIZE, "According to PJIF-18790 - Use ArraySize instead of ARRAY_SIZE") =
            CapacityValue
    };

    /// A type that represent the data type stored in a TCArray
    using value_type = ValueType;

    /// A type that provides a iterator for TCArray which is used for traversing
    /// the elements.
    using iterator = intern::checkable_iterator<ValueType*, TCArray>;

    /// A type that provides a const iterator for TCArray which is used for traversing
    /// the elements.
    using const_iterator = intern::checkable_iterator<const ValueType*, TCArray>;

    /// A type that provides a iterator for TCArray which is used for traversing
    /// the elements.
    using reverse_iterator = vfc::reverse_iterator<iterator>;

    /// A type that provides a const iterator for TCArray which is used for traversing
    /// the elements.
    using const_reverse_iterator = vfc::reverse_iterator<const_iterator>;

    /// A type that provides a reference to a element stored in a TCArray for reading
    /// and performing operations.
    using reference = value_type&;

    /// A type that provides a reference to a const element stored in a TCArray for
    /// reading and performing const operations.
    using const_reference = const value_type&;

    /// A type that counts the number of elements in a TCArray.
    using size_type = vfc::int32_t;

    //---------------------------------------------------------------------
    /// Default constructor
    /// $Source: vfc_carray.hpp $
    //---------------------------------------------------------------------
    TCArray() = default;

    ~TCArray() = default;

    //---------------------------------------------------------------------
    /// Copy constructor
    /// $Source: vfc_carray.hpp $
    //---------------------------------------------------------------------
    TCArray(const TCArray&) = default;

    //---------------------------------------------------------------------
    /// Assignment operator
    /// Assigns only TCArray types which have equal template arguments.
    /// $Source: vfc_carray.hpp $
    //---------------------------------------------------------------------
    TCArray& operator=(const TCArray&) = default;

    //---------------------------------------------------------------------
    /// Returns pointer to the first stored element.
    /// $Source: vfc_carray.hpp $
    /// @return  returns pointer

    //---------------------------------------------------------------------
    iterator begin();

    //---------------------------------------------------------------------
    /// Returns const pointer to the first stored element.
    /// $Source: vfc_carray.hpp $
    /// @return  returns const pointer

    //---------------------------------------------------------------------
    const_iterator begin() const;

    //---------------------------------------------------------------------
    /// Returns pointer after the last stored element.
    /// $Source: vfc_carray.hpp $
    /// @return  returns  pointer

    //---------------------------------------------------------------------
    iterator end();

    //---------------------------------------------------------------------
    /// Returns const pointer after the last stored element.
    /// $Source: vfc_carray.hpp $
    /// @return  returns const pointer

    //---------------------------------------------------------------------
    const_iterator end() const;

    //---------------------------------------------------------------------
    /// Returns pointer after the last stored element.
    /// $Source: vfc_carray.hpp $
    /// @return returns pointer

    //---------------------------------------------------------------------
    reverse_iterator rbegin();

    //---------------------------------------------------------------------
    /// Returns const pointer after the last stored element.
    /// $Source: vfc_carray.hpp $
    /// @return returns const pointer

    //---------------------------------------------------------------------
    const_reverse_iterator rbegin() const;

    //---------------------------------------------------------------------
    /// Returns pointer to the first stored element.
    /// $Source: vfc_carray.hpp $
    /// @return returns pointer

    //---------------------------------------------------------------------
    reverse_iterator rend();

    //---------------------------------------------------------------------
    /// Returns const pointer to the first stored element.
    /// $Source: vfc_carray.hpp $
    /// @return returns const pointer

    //---------------------------------------------------------------------
    const_reverse_iterator rend() const;

    //---------------------------------------------------------------------
    /// Returns a reference to the element at position f_pos in TCArray.
    /// Overloaded for all integral index types : gives static error
    /// when max value is smaller than CapacityValue.
    /// @tparam IndexType type to use for indexing values
    /// @param f_pos position of element to access
    /// @return returns reference to element at specified position

    /// @note This operator[] is could be declared constexpr for c++14 and higher versions
    //---------------------------------------------------------------------
    template <typename IndexType>
    reference operator[](IndexType f_pos);

    //---------------------------------------------------------------------
    /// Returns a const reference to the element at position f_pos in TCArray.
    /// Overloaded for all integral index types : gives static error
    /// when max value is smaller than CapacityValue.
    /// @tparam IndexType type to use for indexing values
    /// $Source: vfc_carray.hpp $
    /// @param f_pos position of element to access
    /// @return returns const reference to element at specified position

    /// @note This operator[] is could be declared constexpr for c++14 and higher versions
    //---------------------------------------------------------------------
    template <typename IndexType>
    const_reference operator[](IndexType f_pos) const;

    //---------------------------------------------------------------------
    /// Returns the number of elements the array can hold
    /// $Source: vfc_carray.hpp $
    /// @return returns size of array in elements, not bytes

    //---------------------------------------------------------------------
    constexpr size_type capacity() const;

    //---------------------------------------------------------------------
    /// Returns the number of elements the array can hold
    /// Same functionality as capacity(), is there for interface compatibility with std::array
    /// $Source: vfc_carray.hpp $
    /// @return returns size of array in elements, not bytes

    //---------------------------------------------------------------------
    constexpr size_type size() const;

    //---------------------------------------------------------------------
    /// Returns the number of elements the array can hold
    /// Same functionality as capacity(), is there for interface compatibility with std::array
    /// $Source: vfc_carray.hpp $
    /// @return returns size of array in elements, not bytes

    //---------------------------------------------------------------------
    constexpr size_type max_size() const;

    //---------------------------------------------------------------------
    /// Checks whether the container is empty(Capacity value == 0)
    /// $Source: vfc_carray.hpp $
    /// @return returns true if container is empty, else it returns false
    /// @note For the non-specialized base template class the value is always false(because Capacity value cannot be 0)

    //---------------------------------------------------------------------
    constexpr bool empty() const;

  public:
    // This Datastructure is made Public as no constuctors are used
    // for member initialization.The reason being the possibility to
    // initialize at compile time shall be kept like with plain C
    // arrays because only then the code is ROMable.
    ValueType m_value[ArraySize]; ///<    internal Datastructure.
};
} // namespace vfc

#include "vfc/container/vfc_carray.inl"

#endif // ZX_VFC_CARRAY_HPP_INCLUDED

