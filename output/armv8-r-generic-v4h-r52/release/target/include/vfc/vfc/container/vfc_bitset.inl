//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_BITSET_IMPL_INL_INCLUDED
#define ZX_VFC_BITSET_IMPL_INL_INCLUDED

// vfc/core includes
#include "vfc/core/vfc_assert.hpp"
#include <algorithm>

///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
namespace vfc
{
namespace intern
{

/// Future improvement: move this more central, maybe even replace vfc::countOnes()
template <typename IntType>
struct TBitCountHelper
{
    // Solution inspired by "Bit Twiddling" page of Sean Eron Anderson (public domain)
    static_assert(sizeof(IntType) <= 16U, "Only up to 128 bits supported");
    static constexpr IntType Zero       = static_cast<IntType>(0U);
    static constexpr IntType One        = static_cast<IntType>(1U);
    static constexpr IntType PatternFFs = static_cast<IntType>(~Zero);
    static constexpr IntType Pattern5s  = PatternFFs / 3U;
    static constexpr IntType Pattern3s  = PatternFFs / 15U * 3U;
    static constexpr IntType Pattern0Fs = PatternFFs / 255U * 15U;
    static constexpr IntType Pattern01s = PatternFFs / 255U;

    /// Count bits set in an int
    static inline IntType countBits(IntType v)
    {
        // Reuse input as temporary.
        // A lot of casts due to QAC.
        // Count inside pairs of bits
        v = v - static_cast<IntType>(static_cast<IntType>(v >> 1U) & Pattern5s);
        // Add result of neighbouring pairs to form nibbles
        v = static_cast<IntType>(v & Pattern3s) + static_cast<IntType>(static_cast<IntType>(v >> 2U) & Pattern3s);
        // Same to form bytes, result can only take space of a nibble, so masking out rest is ok.
        v = static_cast<IntType>((v + static_cast<IntType>(v >> 4U)) & Pattern0Fs);
        // Current result is in bytes. Build sum in highest byte: Multiplication with 2^n is like <<n.
        // Each bit represents a 2^n. The 01s pattern means that each byte is shifted to the highest and added there.
        // After, shift right to get result.
        v = static_cast<IntType>(
            v * Pattern01s); // PRQA S 2906 # Technical debt L3 id=00e4f71202abbedf4713-36076ec46b37d03e
        v = static_cast<IntType>(v >> ((sizeof(IntType) - 1U) * static_cast<IntType>(CHAR_BIT)));
        return v;
    }

    /// For component tests only, won't be instantiated otherwise
    static inline bool testCountFunc(IntType v)
    {
        // Count the slow way
        IntType temp       = v;
        IntType resultSlow = 0U;
        while (true) // Not using temp in while because of QAC
        {
            if (0U == temp)
            {
                break;
            }
            resultSlow += (temp & 1U); // PRQA S 3010 # Technical debt L2 id=00c10195fb434935425b-e36b3098990655d3
            temp >>= 1U;
        }
        return resultSlow == countBits(v);
    }

    /// Count bits in an int at compile time.
    static inline constexpr uint32_t countBitsCTime(IntType v)
    {
        // Recurse with shifted value, terminate when zero: with unsigned guaranteed to terminate.
        return (0U == v) ? 0U
                         : ((v & 1U) +
                            countBitsCTime(
                                v >> 1)); // PRQA S 3010 # Technical debt L2 id=00c1063060324a9a48b7-0b3625a98353f9d1 //
                                          // PRQA S 1521 # Technical debt L6 id=00c14073bb782b5a4328-8ef9d4617c7aaafa
    }
};

/// Count an array recursively at compile time.
template <typename BitSetType>
inline constexpr uint32_t countBitsArrCTime(const typename BitSetType::ArrayType& arr, uint32_t idx)
{
    // Count last element, recurse into one before, terminate with idx==0.
    return (TBitCountHelper<typename BitSetType::StorageType>::countBitsCTime(arr.arr[idx])) +
           ((0U < idx) ? (countBitsArrCTime<BitSetType>(arr, idx - 1U))
                       : 0U); // PRQA S 1521 # Technical debt L6 id=00e526276a1d65c643d5-88dad5cd519c682c
}

template <typename BitSetType>
inline constexpr bool
compareArrCTime(const typename BitSetType::ArrayType& lhs, const typename BitSetType::ArrayType& rhs, uint32_t idx)
{
    return (lhs.arr[idx] == rhs.arr[idx]) &&
           ((0U < idx) ? (compareArrCTime<BitSetType>(lhs, rhs, idx - 1U))
                       : true); // PRQA S 1521 # Technical debt L6 id=00e553cf7fad7222487f-267d0cb9c2108be0
}

} // namespace intern

template <uint32_t SizeValue>
inline constexpr typename TBitSet<SizeValue>::StorageType TBitSet<SizeValue>::maskForLastElement()
{
    return (0U == (Size % BitsPerElement))
               ? intern::TBitCountHelper<StorageType>::PatternFFs
               : static_cast<StorageType>(
                     static_cast<StorageType>(intern::TBitCountHelper<StorageType>::One << (Size % BitsPerElement)) -
                     1U);
}

template <uint32_t SizeValue>
inline constexpr typename TBitSet<SizeValue>::StorageType
TBitSet<SizeValue>::storageElementMask(uint32_t absoluteBitPos)
{
    return intern::TBitCountHelper<StorageType>::One
           << (absoluteBitPos %
               BitsPerElement); // PRQA S 3010 # Technical debt L2 id=00c142f66ae94f634b1b-c0a7849fb0474e53
}

template <uint32_t SizeValue>
inline constexpr typename TBitSet<SizeValue>::StorageType
TBitSet<SizeValue>::storageElementMask(uint32_t absoluteBitPos, bool value)
{
    return (static_cast<StorageType>(value) << (absoluteBitPos % BitsPerElement));
}

template <uint32_t SizeValue>
inline typename TBitSet<SizeValue>::StorageType& TBitSet<SizeValue>::getStorageElement(uint32_t absoluteBitPos)
{
    VFC_REQUIRE(absoluteBitPos < Size);
    return m_storage.arr[absoluteBitPos / BitsPerElement];
}

template <uint32_t SizeValue>
inline constexpr const typename TBitSet<SizeValue>::StorageType&
TBitSet<SizeValue>::getStorageElementC(uint32_t absoluteBitPos) const
{
    // Assertion in test() function since this is constexpr.
    return m_storage.arr[absoluteBitPos / BitsPerElement];
}

template <uint32_t SizeValue>
constexpr TBitSet<SizeValue>::TBitSet() // PRQA S 2628 # Technical debt L2 id=00c1a7197e7e3cad435b-63313389d3c5e592
    : m_storage()
{
}

template <uint32_t SizeValue>
TBitSet<SizeValue>::TBitSet(
    const TBitSet& rhs) // PRQA S 2634 # Technical debt L7 id=00c1009c0eeb6ddd400f-bd679db2acf711ef
    : m_storage(rhs.m_storage)
{
}

template <uint32_t SizeValue>
constexpr TBitSet<SizeValue>::TBitSet(const ArrayType& rhs) : m_storage(rhs)
{
}

template <uint32_t SizeValue>
TBitSet<SizeValue>& TBitSet<SizeValue>::operator=(const TBitSet& rhs)
{
    // QAC complains about not doing copy+swap :(
    if (this != &rhs)
    {
        m_storage = rhs.m_storage;
    }
    return *this;
}

template <uint32_t SizeValue>
constexpr uint32_t TBitSet<SizeValue>::size()
{
    return Size;
}

template <uint32_t SizeValue>
void TBitSet<SizeValue>::resetAll()
{
    m_storage = ArrayType();
}

template <uint32_t SizeValue>
void TBitSet<SizeValue>::reset(uint32_t pos)
{
    getStorageElement(pos) &= ~storageElementMask(pos);
}

template <uint32_t SizeValue>
void TBitSet<SizeValue>::setAll()
{
    for (auto& elem : m_storage.arr) // PRQA S 3705 # Technical debt L2 id=01588b8f3ebd82e64871-4a87196ace10e195
    {
        elem = intern::TBitCountHelper<StorageType>::PatternFFs;
    }
    // Mask out trailing bits since they became 1s.
    m_storage.arr[NumElements - 1U] &= maskForLastElement();
}

template <uint32_t SizeValue>
void TBitSet<SizeValue>::set(uint32_t pos)
{
    getStorageElement(pos) |= storageElementMask(pos);
}

template <uint32_t SizeValue>
void TBitSet<SizeValue>::set(uint32_t pos, bool value)
{
    getStorageElement(pos) &= ~storageElementMask(pos);
    getStorageElement(pos) |= storageElementMask(pos, value);
}

template <uint32_t SizeValue>
void TBitSet<SizeValue>::flipAll()
{
    // QAC complains "4117 @for-end is not modified by the function"
    for (auto& elem : m_storage.arr) // PRQA S 3705 # Technical debt L2 id=0158067894d8450c4e5c-4a87196ace10e195
    {
        elem ^= intern::TBitCountHelper<StorageType>::PatternFFs;
    }
    // Mask out trailing bits since they became 1s.
    m_storage.arr[NumElements - 1U] &= maskForLastElement();
}

template <uint32_t SizeValue>
void TBitSet<SizeValue>::flip(uint32_t pos)
{
    getStorageElement(pos) ^= storageElementMask(pos);
}

template <uint32_t SizeValue>
constexpr bool TBitSet<SizeValue>::test(uint32_t pos) const
{
    return (
        (VFC_ASSERT(pos < Size)),
        (0U != (getStorageElementC(pos) &
                storageElementMask(pos)))); // PRQA S 3340 # Technical debt L2 id=00c1c451aa8fc0f241b1-bbee813664757aa7
}

template <uint32_t SizeValue>
template <uint32_t PosVal>
constexpr bool TBitSet<SizeValue>::test() const
{
    static_assert(PosVal < Size, "Out-of-bounds access");
    return 0U != (getStorageElementC(PosVal) & storageElementMask(PosVal));
}

template <uint32_t SizeValue>
bool TBitSet<SizeValue>::all() const
{
    uint32_t p =
        0U; // QAC warns either about 1. not initializing in 'for' 2. not initializing here or 3. double initialization
    for (; NumElements > p; ++p)
    {
        if (intern::TBitCountHelper<StorageType>::PatternFFs != m_storage.arr[p])
        {
            break;
        }
    }

    return ((NumElements - 1U) <= p) && (maskForLastElement() == m_storage.arr[NumElements - 1U]);
}

template <uint32_t SizeValue>
bool TBitSet<SizeValue>::any() const
{
    return !none();
}

template <uint32_t SizeValue>
bool TBitSet<SizeValue>::none() const
{
    // QAC complains "4117 @for-end is not modified by the function"
    for (auto&& elem : m_storage.arr) // PRQA S 3705 # Technical debt L2 id=01588b02108faec8431d-b4d10dc9e8ae61c1
    {
        if (0U != elem)
        {
            return false;
        }
    }
    return true;
}

template <uint32_t SizeValue>
constexpr uint32_t TBitSet<SizeValue>::count(bool isconst) const
{
    return (isconst) ? countCTime() : countRuntime();
}

template <uint32_t SizeValue>
void TBitSet<SizeValue>::andWith(const TBitSet& oth)
{
    for (uint32_t p = 0U; NumElements > p;
         ++p) // PRQA S 4687 # Technical debt L2 id=00c19557a655f14f492d-422459355a6c0585
    {
        m_storage.arr[p] &= oth.m_storage.arr[p];
    }
    // No masking needed, 0&0==0.
}

template <uint32_t SizeValue>
void TBitSet<SizeValue>::orWith(const TBitSet& oth)
{
    for (uint32_t p = 0U; NumElements > p;
         ++p) // PRQA S 4687 # Technical debt L2 id=00c1ebd6688450f24a71-422459355a6c0585
    {
        m_storage.arr[p] |= oth.m_storage.arr[p];
    }
    // No masking needed, 0|0==0.
}

template <uint32_t SizeValue>
void TBitSet<SizeValue>::xorWith(const TBitSet& oth)
{
    for (uint32_t p = 0U; NumElements > p;
         ++p) // PRQA S 4687 # Technical debt L2 id=00c1b4a50f4b3a3c4bfe-422459355a6c0585
    {
        m_storage.arr[p] ^= oth.m_storage.arr[p];
    }
    // No masking needed, 0^0==0.
}

template <uint32_t SizeValue>
constexpr bool TBitSet<SizeValue>::isEqual(const TBitSet& rhs, bool isconst) const
{
    return (isconst) ? isEqualCTime(rhs) : isEqualRuntime(rhs);
}

template <uint32_t SizeValue>
constexpr auto TBitSet<SizeValue>::getStorage() const -> const ArrayType&
{
    return m_storage;
}

template <typename T, T V>
constexpr T ct()
{
    return V;
}

template <uint32_t SizeValue>
constexpr uint32_t TBitSet<SizeValue>::countCTime() const
{
    return intern::countBitsArrCTime<TBitSet<SizeValue>>(m_storage, NumElements - 1U);
}

template <uint32_t SizeValue>
uint32_t TBitSet<SizeValue>::countRuntime() const
{
    uint32_t result = 0U;
    // QAC complains "4117 @for-end is not modified by the function"
    for (auto&& elem : m_storage.arr) // PRQA S 3705 # Technical debt L2 id=015807c1f03175254e13-b4d10dc9e8ae61c1
    {
        result += intern::TBitCountHelper<StorageType>::countBits(
            elem); // PRQA S 3010 # Technical debt L2 id=00c156fe83ab8c374e71-62cee8db2401334b
    }

    return result;
}

template <uint32_t SizeValue>
constexpr bool TBitSet<SizeValue>::isEqualCTime(const TBitSet& rhs) const
{
    return intern::compareArrCTime<TBitSet<SizeValue>>(m_storage, rhs.m_storage, NumElements - 1U);
}

template <uint32_t SizeValue>
bool TBitSet<SizeValue>::isEqualRuntime(const TBitSet& rhs) const
{
    for (uint32_t p = 0U; NumElements > p;
         ++p) // PRQA S 4687 # Technical debt L2 id=00c1ea4c900cf56a41de-422459355a6c0585
    {
        if (m_storage.arr[p] != rhs.m_storage.arr[p])
        {
            return false;
        }
    }
    return true;
}

template <uint32_t SizeValue>
bool operator==(const TBitSet<SizeValue>& lhs, const TBitSet<SizeValue>& rhs)
{
    return lhs.isEqual(rhs);
}

template <uint32_t SizeValue>
bool operator!=(const TBitSet<SizeValue>& lhs, const TBitSet<SizeValue>& rhs)
{
    return !(lhs == rhs);
}

} // namespace vfc
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
#endif // ZX_VFC_BITSET_IMPL_INL_INCLUDED

