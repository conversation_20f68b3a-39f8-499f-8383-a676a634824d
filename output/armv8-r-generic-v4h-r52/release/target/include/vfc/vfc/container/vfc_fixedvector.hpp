//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_FIXEDVECTOR_HPP_INCLUDED
#define ZX_VFC_FIXEDVECTOR_HPP_INCLUDED

// vfc/core includes
#include "vfc/core/vfc_types.hpp"       // used for size_t
#include "vfc/core/vfc_type_traits.hpp" // used for hasTrivialD/Ctor_t
#include "vfc/core/vfc_metaprog.hpp"    // used for TInt2Boolean, TIf, TEnableIf
#include "vfc/core/vfc_namedtype.hpp"   // used for namedtype, SizeType
#include "vfc/core/vfc_aligned_storage.hpp"
#include "vfc/core/vfc_checked_iterator.hpp" //checkable_iterator
#include "vfc/core/vfc_iterator.hpp"
#include "vfc/core/vfc_limits.hpp"
#include "vfc/core/vfc_functionattributes.hpp" // SoftwareQualificationLevel
#include "vfc/memory/vfc_fixedblock_allocator.hpp"
#include <initializer_list> // std::initializer_list<>

namespace vfc_unit_test
{
template <class DataType>
class vfc_fixedvector_basic;
}

namespace vfc
{
//=============================================================================
/// @ingroup vfc_group_container_template
/// @brief  A container in the style of `std::vector`.
/// @details The TFixedVector has a similar functionality as `std::vector`, but doesn't use heap memory. Instead,
/// the memory provided is contained in the class. As a consequence, the capacity is fixed, hence the name. As a
/// vector, it provides modifiers like `push_back()` and `resize()` and a logical size, which may not exceed the
/// capacity. If the number of used elements is known beforehand, consider using TCArray<> instead.
/// @tparam ValueType           The type of the data the vector holds.
/// @tparam CapacityValue       The static maximum size of the vector.
/// @tparam UnusedAllocatorType (unused) Used to be the allocator type, but this flexibility is taken now. Argument
///                             left in for compatibility.
template <
    class ValueType,
    vfc::int32_t CapacityValue,
    class UnusedAllocatorType = TFixedBlockAllocator<ValueType, CapacityValue>>
class TFixedVector
{
    static_assert(0 <= CapacityValue, "CapacityValue must not be negative!");

    // allocators are not supported by TFixedVector anymore.
    // * For default allocator the correct behaviour of TFixedVector is ensured.
    //   But if a custom allocator is specified here, then an error is reported.
    // * For a custom allocator the correct behaviour of TFixedVector cannot be guaranteed
    //   anymore. Thatswhy an error is reported here.
    static_assert(
        (TIsSameType<UnusedAllocatorType, vfc::TFixedBlockAllocator<ValueType, CapacityValue>>::value == true),
        "Allocators are not supported by TFixedVector anymore");

    static_assert(
        (TIsSameType<ValueType, vfc::SizeType>::value == false),
        "vfc::SizeType as template parameter ValueType is not supported.");

    /// The class TFixedVector in general is certified up to ASIL B. Selected methods can have a lower rating.
    using assertLevel = typename vfc::attributes::SoftwareQualifiedType<ValueType>::Asil_B::type_t;

  public:
    enum
    {
        MAX_SIZE = CapacityValue ///< The maximum size of the vector (defined at compile time).
    };

    // type definitions

    using value_type             = ValueType;
    using iterator               = intern::checkable_iterator<ValueType*, TFixedVector>;
    using const_iterator         = intern::checkable_iterator<const ValueType*, TFixedVector>;
    using reverse_iterator       = vfc::reverse_iterator<iterator>;
    using const_reverse_iterator = vfc::reverse_iterator<const_iterator>;
    using reference              = ValueType&;
    using const_reference        = const ValueType&;
    using size_type              = int32_t;
    using difference_type        = ptrdiff_t;

    using hasTrivialCTor_t = typename TInt2Boolean<THasTrivialCTor<ValueType>::value>::type;
    using hasTrivialDTor_t = typename TInt2Boolean<THasTrivialDTor<ValueType>::value>::type;

    //======================================================
    // constructors, destructor
    //======================================================

    /// @dspec{1440} The SW component "vfc" shall create a new empty Fixed vector instance.
    TFixedVector();

    /// @dspecref{1440}
    /// @details        Constructor takes one argument: the reserved size of the vector.
    ///                 The maximum allowed reserved size is the vectors static capacity (template
    ///                 parameter CapacityValue).
    ///                 Elements stored from zero to f_n-1 are default constructed. Reserved POD types
    ///                 are uninitialized.
    /// @param[in]      f_n  number of elements to be constructed
    /// @pre            0 <= f_n <= CapacityValue
    VFC_ATR_DEPRECATED2(
        explicit TFixedVector(size_type f_n),
        "Use new constructor with strong type vfc::SizeType as argument");

    /// @dspecref{1440}
    /// @details        Constructor takes one argument: the reserved size of the vector.
    ///                 The maximum allowed reserved size is the vectors static capacity (template
    ///                 parameter CapacityValue).
    ///                 Elements stored from zero to f_n-1 are default constructed. Reserved POD types
    ///                 are uninitialized.
    /// @param[in]      f_n  number of elements to be constructed
    /// @pre            0 <= f_n <= CapacityValue
    explicit TFixedVector(vfc::SizeType f_n);

    /// @dspec{1442}    The SW component "vfc" shall be able to initialize a Fixed vector instance
    ///                 with a given number of copies of a given item value.
    /// @details        Constructor takes two arguments: the size of the vector and default value.
    ///                 The maximum allowed reserved size is the vectors static capacity (template
    ///                 parameter CapacityValue).
    ///                 Elements stored from zero to f_n-1 are default constructed. Reserved POD types
    ///                 are uninitialized.
    /// @param[in]      f_n  number of elements to be constructed
    /// @pre            0 <= f_n <= CapacityValue
    VFC_ATR_DEPRECATED2(
        TFixedVector(size_type f_n, const ValueType& f_default),
        "Use new constructor with strong type vfc::SizeType as first argument");

    /// @dspec{1442}    The SW component "vfc" shall be able to initialize a Fixed vector instance
    ///                 with a given number of copies of a given item value.
    /// @details        Constructor takes two arguments: the size of the vector and default value.
    ///                 The maximum allowed reserved size is the vectors static capacity (template
    ///                 parameter CapacityValue).
    ///                 Elements stored from zero to f_n-1 are default constructed. Reserved POD types
    ///                 are uninitialized.
    /// @param[in]      f_n  number of elements to be constructed
    /// @pre            0 <= f_n <= CapacityValue
    TFixedVector(vfc::SizeType f_n, const ValueType& f_default);

    /// Constructor taking an `std::initializer_list<>`. This allows for list initialization of the TFixedVector
    /// with given elements, such as `TFixedVector<int32_t> vec { 3, 5, 7 };`.
    /// @note When using curly-braces initialization (`{..}`) with one or two values, this constructor is preferred
    /// by the compiler. When you intend to construct using the size, or size and value (see above), then use round
    /// parentheses (`(..)`) instead.
    /// @pre                The initializer list may not have more elements than the capacity CapacityValue of the
    /// vector.
    /// @param list         The proxy object `std::initializer_list<>`, possibly implicitly generated by the
    ///                     compiler.
    TFixedVector(std::initializer_list<ValueType> list);

    /// @dspec{1441}    The SW component "vfc" shall be able to initialize a given Fixed vector
    ///                 instance from another given Fixed vector instance of its own type.
    /// @details        Copy constructor.
    /// @param [in]     rhs  TFixedVector that serves as the source of the copy
    TFixedVector(const TFixedVector& f_rhs);

    /// Destructor, destructs stored elements (if any) and frees memory.
    ~TFixedVector() VFC_NOEXCEPT;

    /// @dspec{1454}    The SW component "vfc" shall be able to provide the possibility to assign the
    ///                 items of a given Fixed vector instance to another given Fixed vector instance.
    /// @details        Assigns only TFixedVector types which have equal template arguments.
    /// @param[in]      f_n  TFixedVector that serves as the source of the assignment.
    /// @return         reference to modified TFixedVector
    TFixedVector& operator=(const TFixedVector& f_rhs);

    //======================================================
    // methods
    //======================================================

    /// @dspec{1453}    The SW component "vfc" shall return the iterator to the first item of the
    ///                 given Fixed vector instance.
    /// @note           Attention: If the container is empty, you get an iterator to uninitialized memory.
    /// @details        begin() returns pointer to the first stored element.
    /// @return         A random-access iterator addressing the first element in the TFixedVector
    iterator begin();

    /// @dspecref{1453}
    /// @note           Attention: If the container is empty, you get an iterator to uninitialized memory.
    /// @details        begin() returns const pointer to the first stored element.
    /// @return         A const random-access iterator addressing the first element in the TFixedVector
    const_iterator begin() const;

    /// @dspec{1452}    The SW component "vfc" shall return the iterator after the last item of
    ///                 the given Fixed vector instance.
    /// @note           Attention: The end iterator always points to unallocated and potentially
    ///                 uninitialized memory, therefore it shall never be dereferenced.
    /// @details        end() returns pointer after the last stored element.
    /// @return         A random-access iterator to the end of the TFixedVector object.
    iterator end();

    /// @dspecref{1452}
    /// @note           Attention: The end iterator always points to unallocated and potentially
    ///                 uninitialized memory, therefore it shall never be dereferenced.
    /// @details        end() returns const pointer after the last stored element.
    /// @return         A const random-access iterator to the end of the TFixedVector object.
    const_iterator end() const;

    /// @dspec{1451}    The SW component "vfc" shall return the reverse iterator to the last
    ///                 item of the given Fixed vector instance.
    /// @note           Attention: If the container is empty, you get an iterator to uninitialized memory.
    /// @details        rbegin() returns pointer to the first stored element of the reversed vector.
    /// @return         A reverse random-access iterator to the last element of the TFixedVector object.
    reverse_iterator rbegin();

    /// @dspecref{1451}
    /// @note           Attention: If the container is empty, you get an iterator to uninitialized memory.
    /// @details        begin() returns const pointer to the first stored element of the reversed vector.
    /// @return         A const reverse random-access iterator to the last element of the TFixedVector object.
    const_reverse_iterator rbegin() const;

    /// @dspec{1450}    The SW component "vfc" shall return the reverse iterator pointing to the
    ///                 position before the first item of the given Fixed vector instance.
    /// @note           Attention: The end iterator always points to unallocated and potentially
    ///                 uninitialized memory, therefore it shall never be dereferenced.
    /// @return         A reverse random-access iterator marking the end of the TFixedVector object
    ///                 in reverse order.
    reverse_iterator rend();

    /// @dspecref{1450}
    /// @note           Attention: The end iterator always points to unallocated and potentially
    //                  uninitialized memory, therefore it shall never be dereferenced.
    /// @return         A const reverse random-access iterator marking the end of the TFixedVector
    //                  object in reverse order.
    const_reverse_iterator rend() const;

    /// @dspec{1455}    The SW component "vfc" shall return a reference to an item at a given
    ///                 index of a given Fixed vector instance.
    /// @details        operator[] returns reference to the element at position `idx` in vector.
    /// @param[in]      idx  Index of element to access.
    /// @return         Reference to specified element.
    /// @pre            `idx` must not be negative and `idx` must be less than size().
    auto operator[](size_type idx) -> reference;

    /// @dspecref{1455}
    /// @details        Same as `operator[]` taking `size_type`, but taking a template argument.
    /// @tparam         IndexType Type of the function argument.
    /// @param[in]      idx  Index of element to access.
    /// @return         Reference to specified element.
    /// @pre            `idx` must not be negative and `idx` must be less than size().
    /// @pre            `IndexType` must be integral and big enough to cover all possible indices.
    template <typename IndexType>
    auto operator[](IndexType idx) -> reference;

    /// @dspecref{1455}
    /// @details        Same as `operator[]` taking `size_type`, but const method.
    /// @param[in]      idx  Index of element to access.
    /// @return         Constant reference to specified element.
    /// @pre            `idx` must not be negative and `idx` must be less than size().
    auto operator[](size_type idx) const -> const_reference;

    /// @dspecref{1455}
    /// @details        Same as `operator[]` taking a template argument, but const method.
    /// @tparam         IndexType Type of the function argument.
    /// @param[in]      idx  Index of element to access.
    /// @return         Constant reference to specified element.
    /// @pre            `idx` must not be negative and `idx` must be less than size().
    /// @pre            `IndexType` must be integral and big enough to cover all possible indices.
    template <typename IndexType>
    auto operator[](IndexType idx) const -> const_reference;

    /// @dspec{1456}    The SW component "vfc" shall return reference to first
    ///                 item in a given Fixed vector instance.
    /// @details        front() returns reference to the first stored element.
    /// @return         reference to first element
    /// @pre            number of elements must not be zero
    reference front();

    /// @dspecref{1456}
    /// @details        front() returns const reference to the first stored element.
    /// @return         const reference to first element
    /// @pre            number of elements must not be zero
    const_reference front() const;

    /// @dspec{1462}    The SW component "vfc" shall return reference to the
    ///                 last item in a given Fixed vector instance.
    /// @details        back() returns reference to the last stored element.
    /// @return         reference to last element
    /// @pre            number of elements must not be zero
    reference back();

    /// @dspecref{1462}
    /// @details        back() returns const reference to the last stored element.
    /// @return         const reference to last element
    /// @pre            number of elements must not be zero
    const_reference back() const;

    /// @dspec{1448}    The SW component "vfc" shall return the current size of
    ///                 the given Fixed vector instance.
    /// @details        size() returns the number of used elements in the vector.
    /// @return         current number of elements in this TFixedVector
    size_type size() const;

    /// @dspec{1449}    The SW component "vfc" shall return the boolean value "true"
    ///                 if the given Fixed vector instance is empty otherwise it shall
    ///                 return the boolean value "false".
    /// @details        empty() returns true if the vector holds no elements. Otherwise
    ///                 returns false.
    /// @return         true if this TFixedVector has zero elements, else return false
    bool empty() const;

    /// @dspec{1446}    The SW component "vfc" shall return its currently allocated
    ///                 size of the Fixed vector instance.
    /// @details        capacity() returns the maximum number of elements (defined
    ///                 at compile time) the vector can store.
    /// @return         maximum number of elements this TFixedVector can hold
    //  qacpp-4212: Adding `static` keyword to method would change the API. Waiting for new deviation before suppressing
    size_type capacity() const;

    /// @dspec{1447}    The SW component "vfc" shall return the maximum size of
    ///                 the given Fixed vector instance.
    /// @details        max_size() returns the maximum possible sequence length.
    ///                 Same functionality as capacity(), is there for interface
    //                  compatibility with std::vector.
    /// @return         maximum number of elements this TFixedVector can hold
    //  qacpp-4212: Adding `static` keyword to method would change the API. Waiting for new deviation before suppressing
    size_type max_size() const;

    /// @dspec{1445}    The SW component "vfc" shall append a given item instance
    ///                 at the end of the given Fixed vector instance.
    /// @details        push_back() constructs a copy of f_elem after the last stored
    ///                 element of the vector.
    /// @param[in]      f_elem  element to be inserted at the end of this TFixedVector
    /// @pre            The user has to ensure that this function is not called on
    ///                 a full vector.
    void push_back(const ValueType& f_elem);

    /// @dspec{3001}    The SW component "vfc" shall append a given item instance
    ///                 at the end of the fixed vector instance and call the given error function
    ///                 if and only if the operation could not be completed.
    /// @details        push_back() constructs a copy of f_elem after the last stored
    ///                 element of the vector.
    ///                 If this function is called on a full vector, then the fixed vector is not
    ///                 modified and the functor error_handler is called.
    /// @tparam         ErrorHandlerType    Type of error handler functor.
    /// @param[in]      f_elem              Element to be inserted at the end of this TFixedVector.
    /// @param[in]      error_handler       Error handler functor.
    template <class ErrorHandlerType>
    void push_back(const ValueType& f_elem, ErrorHandlerType error_handler);

    /// @dspec{3002}    The SW component "vfc" shall append a given item instance
    ///                 at the end of the fixed vector instance.
    ///                 The function shall return the bool true if the append succeeded,
    ///                 or it shall return the bool false otherwise.
    /// @details        If this function is called on a non-full vector, push_back() constructs a
    ///                 copy of f_elem after the last stored element of the vector, and returns
    ///                 true to signal success.
    ///                 If this function is called on a full vector, then the fixed vector is not
    ///                 modified and the function returns false to signal failure.
    /// @param[in]      f_elem              Element to be inserted at the end of this TFixedVector.
    /// @return         true if the push_back is successful, false otherwise.
    bool push_back_checked(const ValueType& f_elem);

    /// @dspec{1444}    The SW component "vfc" shall remove the item instance at
    ///                 the end of the given Fixed vector instance.
    /// @details        pop_back() destructs the last element of the vector.
    /// @pre            The user has to ensure that this function is not called on
    ///                 an empty vector.
    void pop_back();

    /// @dspec{1443}    The SW component "vfc" shall erase the item at specified
    ///                 iterator from the given Fixed vector instance and return an
    ///                 iterator pointing to the item after the erased item.
    /// @note           After erase() the vector is one element shorter than before.
    /// @note           Attention: This is very expensive as it moves elements around,
    ///                 de- and re-constructing each of them!
    ///                 Also notice that if the elements are pointers, the pointed-to
    ///                 entities are not freed.
    ///                 All iterators pointing to elements after f_it are invalid
    ///                 after erase().
    /// @details        erase() erases the element the iterator points to and compacts
    ///                 the vector by moving down all others
    /// @param[in]      f_it  Iterator pointing to element to be erased.
    /// @return         Iterator pointing to the next element (or end()).
    /// @pre            number of elements must not be zero
    /// @pre            f_it must be greater or equal than the pointer to the first element
    /// @pre            f_it must be smaller or equal than the pointer to the last element
    iterator erase(iterator f_it);

    /// @dspec{1461}    The SW component "vfc" shall erase the items starting at
    ///                 the given start iterator upto and not including the given end
    ///                 iterator from the given Fixed vector instance and return an
    ///                 interator pointing to the item after the last erased item.
    /// @details        erase() erases the elements in [f_first, f_last) and compacts
    ///                 the vector by moving down all others
    /// @param[in]      f_first  Iterator pointing to first element being erased.
    /// @param[in]      f_last   Iterator pointing after(!) last element being erased.
    /// @return         Iterator pointing to the first element after f_last (or end()).
    /// @note           Attention: This is very expensive as it moves elements around,
    ///                 de- and re-constructing each of them!
    ///                 Also notice that if the elements are pointers, the pointed-to
    //                  entities are not freed.
    ///                 All iterators pointing to elements after f_first are invalid
    ///                 after erase().
    /// @pre            number of elements must not be zero
    /// @pre            f_first must be greater or equal than the pointer to the first element
    /// @pre            f_last must be smaller or equal than the pointer to the last element
    /// @pre            f_first must be smaller or equal than f_last
    iterator erase(iterator f_first, iterator f_last);

    /// @dspec{1460}    The SW component "vfc" shall remove all data from a given
    ///                 Fixed vector instance.
    /// @details        clear() destructs all elements of the vector. The vector is
    ///                 empty after calling clear().
    void clear();

    /// @dspec{1459}    The SW component "vfc" shall change the size of the given
    ///                 Fixed vector instance to the given number and initialize new
    ///                 entries with the given default item value.
    /// @details        resize() changes the number of stored elements. If f_newsize<oldsize,
    ///                 the elements from f_newsize to oldsize-1 are destructed.
    ///                 if f_newsize>oldsize, the elements from oldsize to f_newsize-1 are
    ///                 default constructed, POD types are not initialized.
    /// @param[in]      f_newsize  number of elements this TFixedVector shall hold after
    //                  resize
    /// @pre            The user has to ensure that the newsize does not exceed the static
    ///                 capacity of the vector.
    void resize(size_type f_newsize);

    /// @dspec{1459}    The SW component "vfc" shall change the size of the given
    ///                 Fixed vector instance to the given number and initialize new
    ///                 entries with the given default item value.
    /// @details        resize() changes the number of stored elements. If f_newsize<oldsize,
    ///                 the elements from f_newsize to oldsize-1 are destructed.
    ///                 if f_newsize>oldsize, the elements from oldsize to f_newsize-1 are
    ///                 constructed with f_default.
    /// @param[in]      f_newsize  number of elements this TFixedVector shall hold after resize
    /// @param[in]      f_default  value to initialize new elements with
    /// @pre            The user has to ensure that the newsize does not exceed the static
    ///                 capacity of the vector.
    void resize(size_type f_newsize, const ValueType& f_default);

    /// @dspec{1521}    The SW component "vfc" shall ensure that the given Fixed vector
    ///                 instance can hold at least the given number of items.
    /// @details        reserve() is there for interface compatibility with std::vector,
    ///                 it is basically a NOP and only asserts that f_reserved_size <= CapacityValue
    /// @param[in]      f_reserved_size  number of elements this TFixedVector shall potentially hold
    /// @pre            f_reserved_size needs to be a value between 0 and the max. capacity of the vector.
    void reserve(size_type f_reserved_size);

    /// @dspec{1458}    The SW component "vfc" shall return a C-pointer to the first
    ///                 item value of the given Fixed vector instance.
    /// @note           c_array() is there for compatibility with the c standard.
    /// @details        Use TFixedVector as C array. Direct read/write access to data, returns
    ///                 pointer to the first element.
    /// @return         pointer to first member (allocated or not)
    /// @note           Attention: If the container is empty, you get a pointer to uninitialized
    ///                 memory.
    ValueType* c_array();

    /// @dspecref{1458}
    /// @details        Use TFixedVector as const C array. Direct readonly access to data, returns
    ///                 const pointer to the first element.
    /// @note           c_array() is there for compatibility with the c standard.
    /// @return         const pointer to first member (allocated or not)
    /// @note           Attention: If the container is empty, you get a pointer to uninitialized memory.
    const ValueType* c_array() const;

    /// @dspec{1518}    The SW component "vfc" shall set all current items of a given Fixed
    ///                 vector instance to a given value.
    /// @details        erase all the elements in the vector and copies the specified
    //                  element in the empty vector
    /// @param[in]      f_value  f_value is the element to be set to the FixedVector
    void set(const ValueType& f_value);

    /// @dspec{1457}    The SW component "vfc" shall allocate memory at the end for an item
    ///                 without initializing this memory of the given Fixed vector instance.
    /// @note           Potentially dangerous(!) adding of an allocated, but not initialized
    ///                 element at the end.
    ///                 Immediately construct it like this:
    ///                 @code new (myFixedVector.insert_uninitialized_back()) myPayload(ctorArgs); @endcode
    /// @return         iterator to newly allocated but uninitialized element
    /// @pre            Must not be used if vector is already full.
    iterator insert_uninitialized_back();

    iterator insert(const_iterator pos, ValueType const& value)
    {
        if (m_usedElements + 1 < CapacityValue)
        {
            if (pos != end())
            {
                iterator from = end();
                iterator to   = end() + 1;
                while (from != pos)
                {
                    (void)new (&(*--to)) value_type(std::move(*--from));
                    from->~value_type();
                }
            }
            (void)new (const_cast<ValueType*>(pos)) value_type(value);
            m_usedElements += 1;
        }
        throw std::bad_alloc();
    }

  private:
    enum
    {
        VALUE_TYPE_ALIGNMENT = TAlignmentOf<ValueType>::value,
        MAX_UINT16_VAL       = (1 << 16) - 1, // @FIXME2011: std::numeric_limits<uint16_t>::max())
        MAX_UINT8_VAL        = (1 << 8) - 1,  // @FIXME2011: std::numeric_limits<uint8_t>::max())
        MAX_SIZE_VAL         = MAX_SIZE       //< duplicated to cleanly compare to other enums
    };

    using storage_type = TAlignedTypeStorage<ValueType, CapacityValue>;

    static_assert(MAX_UINT16_VAL == std::numeric_limits<uint16_t>::max(), "The numeric limit is not correct!");
    static_assert(MAX_UINT8_VAL == std::numeric_limits<uint8_t>::max(), "The numeric limit is not correct!");

    /// counter_t should be as small as possible as long as the alignment would imply padding anyway
    using counter_t = typename TIf<
        (MAX_SIZE_VAL <= MAX_UINT16_VAL) && 2 >= VALUE_TYPE_ALIGNMENT,
        typename TIf<(MAX_SIZE_VAL <= MAX_UINT8_VAL) && 1 >= VALUE_TYPE_ALIGNMENT, uint8_t, uint16_t>::type,
        uint32_t>::type;

    // sanity check for chosen counter_t
    static_assert(
        CapacityValue <= std::numeric_limits<counter_t>::max(),
        "counter_t not suitable for given CapacityValue");

    // memory shall be the first member, s.t. the TFixedVector's data can be memory
    // aligned by aligning the whole TFixedVector itself
    storage_type m_memory;
    counter_t    m_usedElements; ///< Counter: how much elements are actually used

    /// Destroys objects, f_newsize < m_usedElements.
    void shrink(size_type f_newsize);

    /// Check if a number of elements fits in the vectors capacity.
    static bool capacity_check(size_type f_numElements);

    /// Construct build-in type objects, hence does nothing.
    static void construct(iterator, iterator, true_t);

    /// Construct objects with default ctor.
    /// Does nothing for built-in types. C++11 5.3.4-15 with 8.5-6
    /// But Greenhills compiler didn't optimize out the empty loop, so the above overload is necessary.
    static void construct(iterator f_start, iterator f_end, false_t);

    /// Constructs objects with copy-constructor.
    static void copy_construct(iterator f_start, iterator f_end, const ValueType& elem);

    /// Destroy objects. Version for built-in types which does nothing.
    static void destruct(iterator f_start, iterator f_end, true_t);

    /// Destroy objects.
    /// Does nothing for built-in types. C++11 12.4-15.
    /// But Greenhills compiler didn't optimize out the empty loop, so the above overload is necessary.
    static void destruct(iterator f_start, iterator f_end, false_t);

    void set(const ValueType& f_value, vfc::true_t);
    void set(const ValueType& f_value, vfc::false_t);

    template <class DataType>
    friend class ::vfc_unit_test::vfc_fixedvector_basic; // PRQA S 2107 # Technical debt L2
                                                         // id=00c12fda6088535c43bd-a5aa96dab42ba225
};

} // namespace vfc

#include "vfc/container/vfc_fixedvector.inl"

#endif // ZX_VFC_FIXEDVECTOR_HPP_INCLUDED

