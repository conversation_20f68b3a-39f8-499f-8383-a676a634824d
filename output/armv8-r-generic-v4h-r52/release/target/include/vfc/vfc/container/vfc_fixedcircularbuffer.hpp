//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_FIXEDCIRCULARBUFFER_HPP_INCLUDED
#define ZX_VFC_FIXEDCIRCULARBUFFER_HPP_INCLUDED

#include "vfc/core/vfc_types.hpp"
#include "vfc/core/vfc_aligned_storage.hpp"        // used for TAlignedStorage
#include "vfc/core/vfc_iterator.hpp"               // reverse_iterator & bidirectional_iterator_tag
#include "vfc/memory/vfc_fixedblock_allocator.hpp" // used for legacy AllocatorType TFixedBlockAllocator
#include "vfc/core/vfc_builtin_types.hpp"

namespace vfc
{

//=============================================================================
//  CConstIterator
//-----------------------------------------------------------------------------
/// A type that provides a bidirectional iterator that
/// can read a const element in a TFixedCircularBuffer.

/// @ingroup                vfc_containers
//=============================================================================

template <typename ContainerType>
class CConstIterator
{
  public:
    using circularBuffer_type = ContainerType;
    using self_type           = CConstIterator;
    using value_type          = typename circularBuffer_type::value_type;
    using pointer             = const value_type*;
    using reference           = const value_type&;
    using iterator_category   = vfc::bidirectional_iterator_tag;
    using difference_type     = ptrdiff_t;

    //---------------------------------------------------------------------
    /// Default constructor, takes no arguments.

    //---------------------------------------------------------------------
    CConstIterator() : m_index(0), m_cirBuff_cp(nullptr) {}

    //---------------------------------------------------------------------
    /// Destructor.

    //---------------------------------------------------------------------
    ~CConstIterator() = default;

    //---------------------------------------------------------------------
    /// Copy constructor.
    /// @param f_rhs  CConstIterator that serves as the source of the copy

    //---------------------------------------------------------------------
    CConstIterator(const CConstIterator& f_rhs)
        : m_index(f_rhs.m_index),
          m_cirBuff_cp(f_rhs.m_cirBuff_cp) // PRQA S 2634 # Technical debt L7 id=00c13efd0e7e281a4dbc-78d5a92874524467
    {
    }

    //---------------------------------------------------------------------
    /// Assignment operator.
    /// @param f_rhs  CConstIterator that serves as the source of the assignment
    /// @return const reference to the modified iterator

    //--------------------------------------------------------------------
    CConstIterator& operator=(const CConstIterator& f_rhs);

    //====================================================================
    /// prefix increment iterator.
    /// @return an iterator to the incremented position

    //====================================================================
    self_type& operator++();

    //====================================================================
    /// post increment iterator.
    /// @return an iterator to the previous, non-incremented position

    //====================================================================
    self_type operator++(postfix_operator_tag);

    //====================================================================
    /// prefix decrement iterator.
    /// @return an iterator to the decremented position

    //====================================================================
    self_type& operator--();

    //====================================================================
    /// postfix decrement iterator.
    /// @return an iterator to the previous, non-decremented position

    //====================================================================
    self_type operator--(postfix_operator_tag);

    //====================================================================
    /// operator + , add f_index to m_index.
    /// @param f_val    number of steps to advance
    /// @return         iterator advanced by f_val steps

    //====================================================================
    const self_type operator+(vfc::int32_t f_val) const;

    /// @param f_val    number of steps to advance
    /// @return         iterator advanced by f_val steps
    self_type& operator+=(vfc::int32_t f_val);

    //====================================================================
    /// operator - , decrement f_index from m_index.
    /// @param f_val    number of steps to decrease
    /// @return         iterator decreased by f_val steps

    //====================================================================
    const self_type operator-(vfc::int32_t f_val) const;

    /// @param f_val    number of steps to decrease
    /// @return         iterator decreased by f_val steps
    self_type& operator-=(vfc::int32_t f_val);

    //====================================================================
    /// tests if the object on the left side of the operator is
    /// less than the object on the right side.
    /// @param f_self_type  right hand side object for comparison
    /// @return  Returns true when object on the left side of the operator
    ///          is less than the object on the right side else false.

    //====================================================================
    bool operator<(
        const self_type& f_self_type) const; // PRQA S 2066 # Technical debt L4 id=00c1b509099d9e504a5a-3e79c1fda3ee69f7

    //========================================================================
    /// tests if the object on the left side of the operator is
    /// less than or equal to the object on the right side.
    /// @param f_self_type  right hand side object for comparison
    /// @return  Returns true when object on the left side of the operator is
    ///          less than or equal to the object on the right side else false.

    //========================================================================
    bool operator<=(
        const self_type& f_self_type) const; // PRQA S 2066 # Technical debt L4 id=00c104034931df284d6f-1af7f803ff1e6d97

    //========================================================================
    /// tests if the object on the left side of the operator is
    /// greater than the object on the right side.
    /// @param f_self_type  right hand side object for comparison
    /// @return  Returns true when object on the left side of the operator is
    ///          greater than the object on the right side else false.

    //========================================================================
    bool operator>(
        const self_type& f_self_type) const; // PRQA S 2066 # Technical debt L4 id=00c163b01c1709c941da-64bd07c7860e42da

    //========================================================================
    /// tests if the object on the left side of the operator is
    /// greater than the object on the right side.
    /// @param f_self_type  right hand side object for comparison
    /// @return  Returns true when object on the left side of the operator is
    ///          greater than the object on the right side else false.

    //========================================================================
    bool operator>=(
        const self_type& f_self_type) const; // PRQA S 2066 # Technical debt L4 id=00c15282635fe9274542-8f7213a19a902530

    //========================================================================
    /// tests if the object on the left side of the operator is
    /// equal to the object on the right side.
    /// @param f_self_type  right hand side object for comparison
    /// @return  Returns true when object on the left side of the operator is
    ///          equal to the object on the right side else false.

    //========================================================================
    bool operator==(
        const self_type& f_self_type) const; // PRQA S 2066 # Technical debt L4 id=00c113ec74fca3f34e9c-892e6c89ff02f29c

    //========================================================================
    /// tests if the object on the left side of the operator is
    /// not equal to the object on the right side.
    /// @param f_self_type  right hand side object for comparison
    /// @return  Returns true when object on the left side of the operator is
    ///          not equal to the object on the right side else false.

    //========================================================================
    bool operator!=(
        const self_type& f_self_type) const; // PRQA S 2066 # Technical debt L4 id=00c191da8aa1dbe9437b-f8c3f9ab1958bc0c

    //========================================================================
    /// Returns reference to the object.
    /// @return  Returns reference to the object

    //========================================================================
    reference operator*() const;

    //========================================================================
    /// Returns pointer to the object.
    /// @return  Returns pointer to the object.

    //========================================================================
    pointer operator->() const;

  protected:
    //---------------------------------------------------------------------
    /// Parameterised Constructor takes circularBuffer_type and f_index.
    /// @param f_value_p pointer to circular buffer value
    /// @param f_index   index number the iterator shall point to

    //---------------------------------------------------------------------
    CConstIterator(const circularBuffer_type* f_value_p, vfc::int32_t f_index)
        : m_index(f_index), m_cirBuff_cp(f_value_p)
    {
    }

    vfc::int32_t               m_index;
    const circularBuffer_type* m_cirBuff_cp;
    friend circularBuffer_type; ///< circularBuffer_type can access private members // PRQA S 2107 # Technical debt L2
                                ///< id=00c1ba6addc205474854-e7b1a7e118979811 as well call the private constructor
};

//=============================================================================
//  CIterator
//-----------------------------------------------------------------------------
/// A type that provides a bidirectional iterator that can
/// read or modify any element in a ContainerType.

/// @ingroup                vfc_containers
//=============================================================================
template <typename ContainerType>
class CIterator : public CConstIterator<ContainerType>
{
  public:
    using circularBuffer_type = ContainerType;
    using base_type           = CConstIterator<circularBuffer_type>;
    using self_type           = CIterator<circularBuffer_type>;
    using value_type          = typename circularBuffer_type::value_type;
    using pointer             = value_type*;
    using reference           = value_type&;
    using iterator_category   = vfc::bidirectional_iterator_tag;
    using difference_type     = ptrdiff_t;

    //---------------------------------------------------------------------
    /// Default constructor, takes no arguments.

    //---------------------------------------------------------------------
    CIterator() : base_type() // PRQA S 2628 # Technical debt L2 id=00c17ea2f4929bb142b9-9cf7ed97081c7c1a
    {
    }

    //========================================================================
    /// prefix increment iterator.
    /// @return an iterator to the incremented position

    //========================================================================
    self_type& operator++();

    //========================================================================
    /// post increment iterator.
    /// @return an iterator to the previous, non-incremented position

    //========================================================================
    self_type operator++(postfix_operator_tag);

    //========================================================================
    /// prefix decrement iterator.
    /// @return an iterator to the decremented position

    //========================================================================
    self_type& operator--();

    //========================================================================
    /// postfix decrement iterator.
    /// @return an iterator to the previous, non-decremented position

    //========================================================================
    self_type operator--(postfix_operator_tag);

    //========================================================================
    /// Returns reference to the object.
    /// @return  Returns reference to the object.

    //========================================================================
    reference operator*() const;

    //========================================================================
    /// Returns pointer to the object.
    /// @return  Returns pointer to the object.

    //========================================================================
    pointer operator->() const;

    //====================================================================
    /// operator + , add f_index to m_index.
    /// @param f_val    number of steps to advance
    /// @return         iterator advanced by f_val steps

    //====================================================================
    const self_type operator+(vfc::int32_t f_val) const;

    /// @param f_val    number of steps to advance
    /// @return         iterator advanced by f_val steps
    self_type& operator+=(vfc::int32_t f_val);

    //====================================================================
    /// operator - , decrement f_index from m_index.
    /// @param f_val    number of steps to decrease
    /// @return         iterator decreased by f_val steps

    //====================================================================
    const self_type operator-(vfc::int32_t f_val) const;

    /// @param f_val    number of steps to decrease
    /// @return         iterator decreased by f_val steps
    self_type& operator-=(vfc::int32_t f_val);

  private:
    //---------------------------------------------------------------------
    /// constructor takes CConstIterator as input parameter.

    //---------------------------------------------------------------------
    explicit CIterator(const base_type& f_baseObj_r);

    //---------------------------------------------------------------------
    /// Parameterised Constructor takes circularBuffer_type and f_index.

    //---------------------------------------------------------------------
    CIterator(circularBuffer_type* f_value_p, vfc::int32_t f_index) : base_type(f_value_p, f_index) {}

    friend circularBuffer_type; ///< ContainerType can access private variables // PRQA S 2107 # Technical debt L2
                                ///< id=00c1f5c699ac9ab2465e-e7b1a7e118979811 as well call the private constructor
};

/// Forward declare TCircularSpan for friend functions
template <class ValueType>
class TCircularSpan;

/// Forward declare TCircularBuffer to forward declare as_span functions
template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
class TFixedCircularBuffer;

/// Forward declare friend conversion methods
template <typename ValueType, vfc::int32_t CapacityValue, class AllocatorType>
TCircularSpan<ValueType> as_span(vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_circularBuffer);

template <typename ValueType, vfc::int32_t CapacityValue, class AllocatorType>
TCircularSpan<const ValueType>
as_span(const vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_circularBuffer);

//=============================================================================
//  TFixedCircularBuffer
//-----------------------------------------------------------------------------
/// TFixedCircularBuffer refers to an area in memory which is used to store
/// incoming data.
/// When the buffer is filled, new data is written starting at the beginning
/// of the buffer and overwriting the old.The TFixedCircularBuffer is
/// especially designed to provide fixed capacity storage. When its capacity
/// is exhausted, newly inserted elements will cause elements either at the
/// beginning to be overwritten. The TFixedCircularBuffer has a fixed static
/// memory footprint. Its dynamic size is adjusted on insertion and removal
/// of items. Implemented in FIFO manner.
/// @tparam ValueType       DataType to be stored
/// @tparam CapacityValue   Capacity of the TFixedCircularBuffer
/// @tparam UnusedAllocatorType   Dummy AllocatorType not used anymore.

/// @ingroup                vfc_containers
/// @dspec{3007} The SW component "vfc" shall provide a Fixed circular buffer
///              type whose objects can be relocated by copying their memory.
//=============================================================================
template <
    class ValueType,
    vfc::int32_t CapacityValue,
    class UnusedAllocatorType = vfc::TFixedBlockAllocator<ValueType, CapacityValue>>
class TFixedCircularBuffer
{
    static_assert(0 <= CapacityValue, "Capacity value of TFixedCircularBuffer must not be negative");

    /// allocators are not supported by TFixedCircularBuffer anymore.
    /// * For default allocator the correct behaviour of TFixedCircularBuffer is ensured.
    ///   But if a custom allocator is specified here, then an error is reported.
    /// * For a custom allocator the correct behaviour of TFixedCircularBuffer cannot be guaranteed
    ///   anymore. That's why an error is reported here.
    using InternalAllocatorCheck =
        TIsSameType<UnusedAllocatorType, vfc::TFixedBlockAllocator<ValueType, CapacityValue>>;
    static_assert((InternalAllocatorCheck::value == true), "Allocators are not supported by TFixedVector anymore");

  public:
    enum : int32_t
    {
        BufferSize = CapacityValue, ///< The static TFixedCircularBuffer size
                                    ///< (defined at compile time).
        BUFFER_SIZE = BufferSize    ///< Deprecated: Use BufferSize now.
    };

    /// typedefs

    /// A type that represent the TFixedCircularBuffer Type
    using circularBuffer_type = TFixedCircularBuffer<ValueType, CapacityValue, UnusedAllocatorType>;

    ///  A type that represent the data type stored in a TFixedCircularBuffer
    using value_type = ValueType;

    ///  A type that represents the allocator class for a TFixedCircularBuffer object.
    using allocator_type = UnusedAllocatorType;

    /// A type that provides a pointer to an element in a TFixedCircularBuffer.
    using pointer = typename allocator_type::pointer;

    /// A type that provides a pointer to a const element in a TFixedCircularBuffer.
    using const_pointer = typename allocator_type::const_pointer;

    /// A type that provides a reference to a element stored in a TFixedCircularBuffer for reading
    /// and performing operations.
    using reference = typename allocator_type::reference;

    /// A type that provides a reference to a const element stored in a TFixedCircularBuffer for
    /// reading and performing const operations.
    using const_reference = typename allocator_type::const_reference;

    /// A type that counts the number of elements in a TFixedCircularBuffer.
    using size_type = vfc::int32_t;

    /// A type that provides the difference between two iterators that refer to
    /// elements within the same TFixedCircularBuffer.
    using difference_type = ptrdiff_t;

    using iterator_category = vfc::bidirectional_iterator_tag;

    /// A type that provides bidirectional iterator that can read and modify
    /// any element in the TFixedCircularBuffer
    using iterator = CIterator<circularBuffer_type>;

    /// A type that provides a bidirectional iterator that can read a const element
    /// in a TFixedCircularBuffer
    using const_iterator = CConstIterator<circularBuffer_type>;

    /// A type that provides a bidirectional reverse iterator that can read or modify
    /// an element in a TFixedCircularBuffer
    using reverse_iterator = vfc::reverse_iterator<iterator>;

    /// A type that provides a bidirectional reverse iterator that can read any const element
    /// in a TFixedCircularBuffer
    using const_reverse_iterator = vfc::reverse_iterator<const_iterator>;

    //---------------------------------------------------------------------
    /// Default constructor, takes no arguments.

    /// @dspec{226}     The SW component "vfc" shall create a new empty Fixed
    ///                 circular buffer instance.
    //---------------------------------------------------------------------
    TFixedCircularBuffer();

    //---------------------------------------------------------------------
    /// Copy constructor.
    /// @dspec{227}     The SW component "vfc" shall be able to initialize a
    ///                 new Fixed circular buffer instance from another given
    ///                 Fixed circular buffer instance of its own type.
    /// @param f_param_r  circularBuffer that serves as the source of the copy

    //---------------------------------------------------------------------
    TFixedCircularBuffer(const circularBuffer_type& f_param_r);

    //---------------------------------------------------------------------
    /// Allocator constructor.
    /// @dspec{228}     The SW component "vfc" shall create a new Fixed circular
    ///                 buffer instance with a given Allocator instance.
    /// @param f_alloc  allocator instance that manages memory

    //---------------------------------------------------------------------
    VFC_ATR_DEPRECATED2(
        explicit TFixedCircularBuffer(const UnusedAllocatorType& f_alloc),
        "The constructor taking an AllocatorType is deprecated "
        "because custom allocators are no longer supported."
        "Use the default constructor instead.");

    //---------------------------------------------------------------------
    /// Overloaded assignment operator.
    /// @dspec{231}     The SW component "vfc" shall be able to provide the possibility
    ///                 to assign the items of the given Fixed circular buffer instance
    ///                 to another given Fixed circular buffer instance.
    /// @param - TFixedCircularBuffer that serves as the source of the assignment
    /// @return  const reference to modified TFixedCircularBuffer

    //---------------------------------------------------------------------
    TFixedCircularBuffer& operator=(const TFixedCircularBuffer&);

    //---------------------------------------------------------------------
    /// Destructor clears and deletes all remaining elements in the buffer.

    //---------------------------------------------------------------------
    ~TFixedCircularBuffer() VFC_NOEXCEPT;

    //=============================================================================
    /// Returns the copy of the allocator object used to construct the TFixedCircularBuffer.
    /// @dspec{229}     The SW component "vfc" shall return the allocator of a given Fixed
    ///                 circular buffer instance.
    /// @return  copy of the allocator object

    //=============================================================================
    allocator_type VFC_ATR_DEPRECATED2(
        get_allocator(),
        "Please don't use get_allocator() any longer. The allocator "
        "type is still available by the public type definition allocator_type, "
        "which can be used to manually construct a new allocator instance.") const;

    //=============================================================================
    /// Adds an element to the back of the Circularbuffer by calling its
    /// copy constructor.
    /// If the buffer is full, the "oldest", i.e. logically first element is
    /// destroyed by calling its destructor and removed. That means that
    /// this operation can not fail due to a lack of memory.
    /// @dspec{239}     The SW component "vfc" shall append a given item at the end of
    ///                 the given Fixed circular buffer instance.
    /// @param   f_item_r   The element added to the top of the Circularbuffer.
    /// @return  Returns nothing.

    //=============================================================================
    void push(const value_type& f_item_r);

    //-----------------------------------------------------------------------------
    /// Adds an UNINITIALIZED element to the back of the Circularbuffer.
    /// If the buffer is full, push_uninitialized overwrites the oldest element
    /// Use in conjunction with placement new, if you have buffer elements that
    /// are very costly to copy and you still want to use a Circularbuffer container:
    /// @dspec{240}     The SW component "vfc" shall allocate memory for an item without
    ///                 initializing this memory of the given Fixed circular buffer instance
    ///                 and return a reference to allocated but uninitialized item.
    /// @return  Returns a reference to the inserted element
    /// @code new (&*myCircBuff.push_uninitialized()) myElement(ctorArgs); @endcode

    //=============================================================================
    reference push_uninitialized();

    //=============================================================================
    /// Remove the oldest available element.
    /// @dspec{241}     The SW component "vfc" shall deallocate and remove the first
    ///                 item of the given Fixed circular buffer instance.
    /// @pre     The TFixedCircularBuffer must not be empty when calling
    ///          this method.
    /// @return  Returns nothing.

    //=============================================================================
    void pop();

    //=============================================================================
    /// Reference to the oldest element in the TFixedCircularBuffer.
    /// @dspec{242}     The SW component "vfc" shall return a reference to the first
    ///                 item in the given Fixed circular buffer instance.
    /// @pre     The TFixedCircularBuffer must not be empty when calling
    ///          this method.
    /// @return  Returns reference to oldest element.

    //=============================================================================
    reference front();

    //=============================================================================
    /// Const reference to the oldest element in the TFixedCircularBuffer.
    /// @dspecref{242}
    /// @pre     The TFixedCircularBuffer must not be empty when calling
    ///          this method.
    /// @return  Returns const reference to oldest element.

    //=============================================================================
    const_reference front() const;

    //=============================================================================
    /// reference to the last added element.
    /// @dspec{243}     The SW component "vfc" shall return a reference to the last
    ///                 item in a given Fixed circular buffer instance.
    /// @pre     The TFixedCircularBuffer must not be empty when calling
    ///          this method.
    /// @return  reference to the newly added element.

    //=============================================================================
    reference back();

    //=============================================================================
    /// Const reference to the last added element.
    /// @dspecref{243}
    /// @pre     The TFixedCircularBuffer must not be empty when calling
    ///          this method.
    /// @return  Const reference to the newly added element.

    //=============================================================================
    const_reference back() const;

    //=================================================================================
    /// Returns a reference to the element at position f_index in circularBuffer.
    /// @dspec{230}     The SW component "vfc" shall return a reference to an item at a
    ///                 given index of a given Fixed circular buffer instance.
    /// @pre        It must be ensured that 0 <= f_index < size().
    /// @note       zero is the front() element
    /// @param      index of the TFixedCircularBuffer.
    /// @return     reference to a history element with specified index.

    //==================================================================================
    reference operator[](int32_t f_index);

    //===================================================================================
    /// Returns a const reference to the element at position f_index in circularBuffer.
    /// @dspecref{230}
    /// @pre        It must be ensured that 0 <= f_index < size().
    /// @note       The index of element (Ex:- zero is the front() element).
    /// @param      index of the TFixedCircularBuffer.
    /// @return     Const reference to a history element with specified index.

    //====================================================================================
    const_reference operator[](int32_t f_index) const;

    //=============================================================================
    /// Tests if a TFixedCircularBuffer is empty.
    /// @dspec{236}     The SW component "vfc" shall return the boolean value "true"
    ///                 if the given Fixed circular buffer instance is empty otherwise
    ///                 it shall return the boolean value "false".
    /// @return  true if the TFixedCircularBuffer is empty; false if the TFixedCircularBuffer is nonempty.

    //=============================================================================
    bool empty() const;

    //=============================================================================
    /// Returns the number of elements in the TFixedCircularBuffer.
    /// @dspec{237}     The SW component "vfc" shall return the current size of the given
    ///                 Fixed circular buffer instance.
    /// @return  current length of TFixedCircularBuffer.

    //=============================================================================
    size_type size() const;

    //=============================================================================
    /// Returns the maximum length of the TFixedCircularBuffer.
    /// @dspec{238}     The SW component "vfc" shall return the maximum size of the given
    ///                 Fixed circular buffer instance.
    /// @return  Maximum length of TFixedCircularBuffer.

    //=============================================================================
    //  qacpp-4212: Adding `static` keyword to method would change the API. Waiting for new deviation before suppressing
    size_type capacity() const;

    //=============================================================================
    /// Erases the elements of the TFixedCircularBuffer.

    /// @dspec{244}     The SW component "vfc" shall remove all items from a given Fixed
    ///                 circular buffer instance.
    //=============================================================================
    void clear();

    //===========================================================================================
    /// Returns a random-access iterator to the first element in the TFixedCircularBuffer.
    /// @dspec{232}     The SW component "vfc" shall return an iterator to the first item of the
    ///                 given Fixed circular buffer instance.
    /// @return  A random-access iterator addressing the first element in the TFixedCircularBuffer.

    //===========================================================================================
    iterator begin();

    //=================================================================================================
    /// Returns a random-access Const iterator to the first element in the TFixedCircularBuffer.
    /// @dspecref{232}
    /// @return  A random-access Const iterator addressing the first element in the TFixedCircularBuffer.

    //=================================================================================================
    const_iterator begin() const;

    //========================================================================================
    /// Returns a random-access iterator that points just beyond the end of the TFixedCircularBuffer.
    /// @dspec{233}     The SW component "vfc" shall return an iterator after the last item of the
    ///                 given Fixed circular buffer instance.
    /// @return  A random-access iterator to the end of the TFixedCircularBuffer object.

    //=========================================================================================
    iterator end();

    //==============================================================================================
    /// Returns a random-access Const iterator that points just beyond the end of the TFixedCircularBuffer.
    /// @dspecref{233}
    /// @return  A random-access Const iterator to the end of the TFixedCircularBuffer object.

    //==============================================================================================
    const_iterator end() const;

    //=====================================================================================
    /// Returns an iterator to the first element in a reversed TFixedCircularBuffer.
    /// @dspec{234}     The SW component "vfc" shall return an reverse iterator to the last item
    ///                 of the given Fixed circular buffer instance.
    /// @return  A reverse random-access iterator addressing the first element in a reversed
    ///          TFixedCircularBuffer.

    //=====================================================================================
    reverse_iterator rbegin();

    //===========================================================================================
    /// Returns an const iterator to the first element in a reversed TFixedCircularBuffer.
    /// @dspecref{234}
    /// @return  A reverse random-access const iterator addressing the first element in a reversed
    ///          TFixedCircularBuffer.

    //============================================================================================
    const_reverse_iterator rbegin() const;

    //=============================================================================================
    /// Returns an iterator to the end of a reversed TFixedCircularBuffer.
    /// @dspec{235}     The SW component "vfc" shall return a reverse iterator to the position before
    ///                 the first item of the given Fixed circular buffer instance.
    /// @return  A reverse random-access iterator that addresses the location succeeding the last
    ///          element in a reversed TFixedCircularBuffer.

    //=============================================================================================
    reverse_iterator rend();

    //=============================================================================================
    /// Returns an const iterator to the end of a reversed TFixedCircularBuffer.
    /// @dspecref{235}
    /// @return  A reverse random-access const iterator that addresses the location succeeding the
    ///          last element in a reversed TFixedCircularBuffer.

    //==============================================================================================
    const_reverse_iterator rend() const;

  private:
    /// Storage type that avoids needless clearing of memory.
    using storage_type = TAlignedTypeStorage<ValueType, BufferSize>;

    storage_type m_storage; ///< internal uninitialized properly aligned storage

    int32_t   m_readIndex;  ///< Read pointer
    int32_t   m_writeIndex; ///< Write pointer
    size_type m_size;       ///< size of the CircularBuffer

    bool IsOverFlow() const; ///< check for TFixedCircularBuffer Overflow

    static int32_t incrementIndex(int32_t f_index); ///< Increment pointer , if overflow
                                                    ///< since it is TFixedCircularBuffer , not
                                                    ///< possible just ++

    static int32_t getDecrementIndex(int32_t f_writeIndex); ///< function called when back()
                                                            ///< called , since m_writeIndex is
                                                            ///< pointing to next memory location
                                                            ///< and circular in nature , can't
                                                            ///< do blind decrement

    void push_intern(const value_type* f_item_p); ///< pushes the item value in the buffer.
                                                  ///< used in order to avoid code duplicates and
                                                  ///< prevent from modifying public functions declaration

    template <typename ValueTypeForAsSpan, vfc::int32_t CapacityValueForAsSpan, typename AllocatorTypeForAsSpan>
    friend vfc::TCircularSpan<ValueTypeForAsSpan>
    vfc::as_span(vfc::TFixedCircularBuffer<ValueTypeForAsSpan, CapacityValueForAsSpan, AllocatorTypeForAsSpan>&
                     f_circularBuffer); // PRQA S 2107 # Technical debt L2 id=00c1a27960a1197b48c8-26691b937ad03c94

    template <typename ValueTypeForAsSpan, vfc::int32_t CapacityValueForAsSpan, typename AllocatorTypeForAsSpan>
    friend vfc::TCircularSpan<const ValueTypeForAsSpan>
    vfc::as_span(const vfc::TFixedCircularBuffer<ValueTypeForAsSpan, CapacityValueForAsSpan, AllocatorTypeForAsSpan>&
                     f_circularBuffer); // PRQA S 2107 # Technical debt L2 id=00c144232c91649a461d-deeb6752f20d7851
};

//====================================================================================
/// TFixedCircularBuffer equality comparison.
/// This is an equivalence relation.  It is linear in the size of the TFixedCircularBuffer.
/// TFixedCircularBuffer are considered equivalent if their sizes are  equal,
/// and if corresponding elements compare equal.
/// @tparam  ValueType       DataType to be stored
/// @tparam  CapacityValue   Capacity of the TFixedCircularBuffer
/// @tparam  AllocatorType   Allocator type used
/// @param   f_cirBuffer1_r  TFixedCircularBuffer on right hand side of expression.
/// @param   f_cirBuffer2_r  TFixedCircularBuffer on left hand side of expression.
/// @return  True if the size and elements of the TFixedCircularBuffer are equal else return false

/// @ingroup vfc_containers
//====================================================================================
template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
bool operator==(
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer1_r,
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer2_r);

/// @tparam  ValueType       DataType to be stored
/// @tparam  CapacityValue   Capacity of the TFixedCircularBuffer
/// @tparam  AllocatorType   Allocator type used
/// @param   f_cirBuffer1_r  TFixedCircularBuffer on right hand side of expression.
/// @param   f_cirBuffer2_r  TFixedCircularBuffer on left hand side of expression.
/// @return  True if the size and elements of the TFixedCircularBuffer are not equal, else return false.
template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
bool operator!=(
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer1_r,
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer2_r);

//==========================================================================
/// TFixedCircularBuffer ordering relation.
/// This is a total ordering relation.  It is linear in the size of the
/// TFixedCircularBuffer.  The elements must be comparable with <.
/// @tparam  ValueType       DataType to be stored
/// @tparam  CapacityValue   Capacity of the TFixedCircularBuffer
/// @tparam  AllocatorType   Allocator type used
/// @param   f_cirBuffer1_r  TFixedCircularBuffer on right hand side of expression.
/// @param   f_cirBuffer2_r  TFixedCircularBuffer on left hand side of expression.
/// @return  True if f_cirBuffer1_r is lexicographically less than f_cirBuffer2_r.

/// @ingroup vfc_containers
//==========================================================================
template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
bool operator<(
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer1_r,
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer2_r);

/// @tparam  ValueType       DataType to be stored
/// @tparam  CapacityValue   Capacity of the TFixedCircularBuffer
/// @tparam  AllocatorType   Allocator type used
/// @param   f_cirBuffer1_r  TFixedCircularBuffer on right hand side of expression.
/// @param   f_cirBuffer2_r  TFixedCircularBuffer on left hand side of expression.
/// @return  True if f_cirBuffer1_r is lexicographically greater than f_cirBuffer2_r.
template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
bool operator>(
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer1_r,
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer2_r);

/// @tparam  ValueType       DataType to be stored
/// @tparam  CapacityValue   Capacity of the TFixedCircularBuffer
/// @tparam  AllocatorType   Allocator type used
/// @param   f_cirBuffer1_r  TFixedCircularBuffer on right hand side of expression.
/// @param   f_cirBuffer2_r  TFixedCircularBuffer on left hand side of expression.
/// @return  True if f_cirBuffer1_r is lexicographically less-or-equal than f_cirBuffer2_r.
template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
bool operator<=(
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer1_r,
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer2_r);

/// @tparam  ValueType       DataType to be stored
/// @tparam  CapacityValue   Capacity of the TFixedCircularBuffer
/// @tparam  AllocatorType   Allocator type used
/// @param   f_cirBuffer1_r  TFixedCircularBuffer on right hand side of expression.
/// @param   f_cirBuffer2_r  TFixedCircularBuffer on left hand side of expression.
/// @return  True if f_cirBuffer1_r is lexicographically greater-or-equal than f_cirBuffer2_r.
template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
bool operator>=(
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer1_r,
    const TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_cirBuffer2_r);

} // namespace vfc

#include "vfc/container/vfc_fixedcircularbuffer.inl"

#endif // ZX_VFC_FIXEDCIRCULARBUFFER_HPP_INCLUDED

