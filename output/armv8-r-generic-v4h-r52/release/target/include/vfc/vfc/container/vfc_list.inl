//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_LIST_INL_INCLUDED
#define VFC_LIST_INL_INCLUDED

#include <new>                        //placement new
#include <functional>                 //equal_to
#include "vfc/core/vfc_algorithm.hpp" //distance
#include "vfc/core/vfc_assert.hpp"    //VFC_REQUIRE, VFC_ENSURE
#include "vfc/core/vfc_limits.hpp"    //used for numeric_limits

namespace vfc
{
namespace intern
{
template <class ValueType>
class TRemovePredicate
{
  public:
    // constructor
    explicit TRemovePredicate(const ValueType& f_lhs) : m_lhs(f_lhs) {}
    bool operator()(const ValueType& f_rhs) const
    {
        return (m_lhs == f_rhs); // PRQA S 3270 # Technical debt L2 id=00c1e75964d0de184361-6ad2a0a382a0ae23
    }

  private:
    const ValueType& m_lhs;
};

template <class AllocatorType>
struct IterHelper
{
    static void checkIteratorPointsToValidNode(const void*, const void*, vfc::uint32_t) {}
};

/// Specialization for TFixedMemPoolAllocator
template <class ValueType, int32_t SizeValue>
struct IterHelper<TFixedMemPoolAllocator<ValueType, SizeValue>>
{
    /// Check if node points inside the storage. Otherwise it's 'end' or beyond.
    /// This applies only to TFixedMemPoolAllocator, not TFreeStoreAllocator.
    static void
    checkIteratorPointsToValidNode(const TNodeBase<false>* base, const TNodeBase<false>* node, vfc::uint32_t sizeOfNode)
    {
        VFC_ASSERT2(base != nullptr, "Internal error: nullptr used as base pointer");
        VFC_ASSERT2(node != nullptr, "Internal error: illegal node pointer");
        // qacpp-2822: The data-flow analysis is not correct here:
        // The condition where the pointer is checked is inside a macro that expands
        // to a throw expression. Hence the pointer will always be non-zero
        // at the point of computation.
        // If the assertion is replaced with the __qacpp_assert macro this warning
        // also vanishes.
        const uint8_t* const end =
            static_cast<const uint8_t*>(static_cast<const void*>(base)) +
            (SizeValue *
             sizeOfNode); // PRQA S 2822 // PRQA S 3103 # Technical debt L2 id=00c1ae3106dda9554861-8fdae033ad5a8589 //
                          // PRQA S 3705 # Technical debt L2 id=00c1b6cd97205a2243fb-4c9bd4b4405a7b0a
        nop(node);
        nop(end);
        VFC_ASSERT(
            static_cast<const uint8_t*>(static_cast<const void*>(node)) <
            end); // PRQA S 3103 # Technical debt L2 id=00c1e1b038155cf9454f-8560149b2ac56780 // PRQA S 2669 # Technical
                  // debt L8 id=00e4a515abdeb0d749a6-09f790f58bf0104f
    }
};
} // namespace intern
} // namespace vfc

// maxSizePolicy Definition
inline vfc::int32_t vfc::intern::CListMaxsizePolicy::maxSizePolicy()
{
    return stlalias::numeric_limits<vfc::int32_t>::max();
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::TList() : m_numElements(0), m_nodeAlloc(), m_head()
{
    const CBaseInfo l_baseInfo(getBasePointer(), sizeof(node_type));
    getHead()->init(l_baseInfo);
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::TList(const nodealloc_type& f_alloc_r)
    : m_numElements(0), m_nodeAlloc(f_alloc_r), m_head()
{
    CBaseInfo l_baseInfo(getBasePointer(), sizeof(node_type));
    getHead()->init(l_baseInfo);
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::TList(size_type f_count) : TList(vfc::SizeType(f_count))
{
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::TList(vfc::SizeType f_count)
    : m_numElements(0), m_nodeAlloc(), m_head()
{
    CBaseInfo l_baseInfo(getBasePointer(), sizeof(node_type));
    getHead()->init(l_baseInfo);

    VFC_REQUIRE(max_size() >= f_count.get());

    insert(end(), f_count.get(), value_type());

    VFC_ENSURE(size() == f_count.get());
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::TList( // PRQA S 3800 # Technical debt L4
                                                                       // id=00c1d2ebecc6be7244a9-0ca28c5de1e95985
    size_type         f_count,
    const value_type& f_value_r)
    : TList(vfc::SizeType(f_count), f_value_r)
{
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::TList(
    vfc::SizeType     f_count,
    const value_type& f_value_r)
    : m_numElements(0), m_nodeAlloc(), m_head()
{
    const CBaseInfo l_baseInfo(getBasePointer(), sizeof(node_type));
    getHead()->init(l_baseInfo);

    VFC_REQUIRE(max_size() >= f_count.get());

    insert(end(), f_count.get(), f_value_r);

    VFC_ENSURE(size() == f_count.get());
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::TList( // PRQA S 3800 # Technical debt L4
                                                                       // id=00c14af465e700574eb2-0ca28c5de1e95985
    size_type             f_count,
    const value_type&     f_value_r,
    const nodealloc_type& f_alloc_r)
    : m_numElements(0), m_nodeAlloc(f_alloc_r), m_head()
{
    CBaseInfo l_baseInfo(getBasePointer(), sizeof(node_type));
    getHead()->init(l_baseInfo);

    VFC_REQUIRE(max_size() >= f_count);

    insert(end(), f_count, f_value_r);

    VFC_ENSURE(size() == f_count);
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::TList(const list_type& f_rhs_r)
    : m_numElements(0), m_nodeAlloc(), m_head()
{
    const CBaseInfo l_baseInfo(getBasePointer(), sizeof(node_type));
    getHead()->init(l_baseInfo);

    // insert each of the element available in the other list
    insert(end(), f_rhs_r.begin(), f_rhs_r.end());
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
template <class InIteratorType>
inline vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::TList(InIteratorType f_first, InIteratorType f_last)
    : m_numElements(0), m_head() // PRQA S 2009 # Technical debt L2 id=00c17b96e15426924f96-d989263e15e137e1 // PRQA S
                                 // 4050 # Technical debt L7 id=00c18ec3d47b774846e6-d24f2a0d05fdcb2f
{
    CBaseInfo l_baseInfo(getBasePointer(), sizeof(node_type));
    getHead()->init(l_baseInfo);

    insert(end(), f_first, f_last);
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
template <class InIteratorType>
inline vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::TList(
    InIteratorType        f_first,
    InIteratorType        f_last,
    const nodealloc_type& f_alloc_r)
    : // PRQA S 2009 # Technical debt L2 id=00c1e8651eea59d54b1e-497d872ff7b94e89
      m_numElements(0),
      m_nodeAlloc(f_alloc_r),
      m_head()
{
    CBaseInfo l_baseInfo(getBasePointer(), sizeof(node_type));
    getHead()->init(l_baseInfo);

    insert(end(), f_first, f_last);
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>&
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::operator=(const list_type& f_rhs_r)
{
    if (this != &f_rhs_r)
    {
        assign(f_rhs_r.begin(), f_rhs_r.end());
    }
    return *this;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::~TList() VFC_NOEXCEPT
{
    clear();
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::iterator
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::begin()
{
    // Create CBaseInfo for next(): base-pointer is either nullptr or first memory slot.
    node_type* const l_base_p = getBasePointer();
    const CBaseInfo  l_baseInfo(l_base_p, sizeof(node_type));
    // Creates an iterator with base-pointer and the first node. The head (either member m_head or beyond the fixed
    // storage) has the first node as 'next'-pointer.
    return CListIterator(l_base_p, getHead()->next(l_baseInfo));
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::const_iterator
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::begin() const
{
    node_type* const l_base_p = getBasePointer();
    const CBaseInfo  l_baseInfo(l_base_p, sizeof(node_type));
    return CListConstIterator(l_base_p, getHead()->next(l_baseInfo));
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::iterator
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::end()
{
    // Creates an iterator with base-pointer (nullptr or first memory slot) and the pointer to head (either member
    // m_head or beyond the fixed storage). The last node in the list points to this head by definition.
    return CListIterator(getBasePointer(), getHead());
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::const_iterator
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::end() const
{
    return CListConstIterator(
        getBasePointer(),
        const_cast<position_type*>(
            getHead())); // PRQA S 3066 # Technical debt L4 id=00c1a3f3b0a61be24e19-ec7c3c70cb55922c
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::reverse_iterator
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::rbegin()
{
    return reverse_iterator(end());
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::const_reverse_iterator
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::rbegin() const
{
    return const_reverse_iterator(end());
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::reverse_iterator
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::rend()
{
    return reverse_iterator(begin());
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::const_reverse_iterator
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::rend() const
{
    return const_reverse_iterator(begin());
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline bool vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::empty() const
{
    return (0 == m_numElements);
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::size_type
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::size() const
{
    return m_numElements;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::size_type
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::max_size()
    const // PRQA S 4212 # Technical debt L2 id=00c152c26f7ed79c479d-5cf43db185d11e03
{
    return MaxSizePolicyType::maxSizePolicy();
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::size_type
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::capacity() const
{
    return max_size();
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::resize(size_type f_newSize)
{
    resize(f_newSize, ValueType());
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::resize(size_type f_newSize, const value_type& f_default)
{
    VFC_REQUIRE((f_newSize <= max_size()) && (0 <= f_newSize));

    if (f_newSize < m_numElements)
    {
        size_type diffSize = m_numElements - f_newSize;
        for (size_type count = 0; count < diffSize; count++)
        {
            pop_back();
        }
    }
    else if (f_newSize > m_numElements)
    {
        iterator  iterEnd  = end();
        size_type diffSize = f_newSize - m_numElements;
        insert(iterEnd, diffSize, f_default);
    } // PRQA S 4070 # Technical debt L7 id=00c1253e11d09a634753-95698beb1eff61d3

    VFC_ENSURE(m_numElements == f_newSize);
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::allocator_type
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::get_allocator() const
{
    return allocator_type(m_nodeAlloc);
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::push_back(const value_type& f_value_r)
{
    VFC_REQUIRE(max_size() > m_numElements);
    vfc::ignoreReturn(insert(end(), f_value_r));
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::iterator
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::insert_uninitialized_back()
{
    VFC_REQUIRE(max_size() > m_numElements);
    return CListIterator(getBasePointer(), insertElement(end(), nullptr));
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::pop_back()
{
    VFC_REQUIRE2(0 != m_numElements, "pop_back requires non-empty list");

    node_type* const     l_base_p = getBasePointer();
    const CBaseInfo      l_baseInfo(l_base_p, sizeof(node_type));
    position_type* const lastItem = getHead()->prev(l_baseInfo);
    VFC_ASSERT2(nullptr != lastItem, "Internal linkage of TList broken");

    vfc::ignoreReturn(removeElement(lastItem));
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::push_front(const value_type& f_value_r)
{
    VFC_REQUIRE(max_size() > m_numElements);
    vfc::ignoreReturn(insert(begin(), f_value_r));
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::iterator
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::insert_uninitialized_front()
{
    VFC_REQUIRE(max_size() > m_numElements);
    return CListIterator(getBasePointer(), insertElement(begin(), nullptr));
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::pop_front()
{
    VFC_REQUIRE2(0 != m_numElements, "pop_front requires non-empty list");
    vfc::ignoreReturn(erase(begin()));
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::iterator
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::insert(
    iterator          f_pos,
    const value_type& f_value_r) // PRQA S 2008 # Technical debt L2 id=00c176aa32681b6447af-dda1f0bfda8be4b2
{
    VFC_REQUIRE(max_size() > m_numElements);
    VFC_REQUIRE2(nullptr != f_pos.m_node_p, "Illegal iterator passed to insert");

    const CListIterator iter(getBasePointer(), insertElement(f_pos, &f_value_r));
    return iter;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::insert( // PRQA S 3800 # Technical debt L4
                                                                             // id=00c160bc6d8d3cb8404a-7a34bd565ced2edc
    iterator          f_pos,
    size_type         f_count,
    const value_type& f_value_r) // PRQA S 2008 # Technical debt L2 id=00c1f01301750bc248bb-f7d64711264673f8
{
    VFC_REQUIRE(f_count >= 0);
    VFC_REQUIRE(max_size() >= (m_numElements + f_count));
    VFC_REQUIRE2(nullptr != f_pos.m_node_p, "Illegal iterator passed to insert");
    for (size_type count = f_count; count > 0; --count)
    {
        insert(f_pos, f_value_r); // PRQA S 3803 # Technical debt L2 id=00c1d44087f6039d43ed-6a4fab66612eb989 // PRQA S
                                  // 3804 # Technical debt L2 id=00c1cd842096c44f4ae5-1405b036a1fb2674
    }
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
template <class InIteratorType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::insert(
    iterator       f_pos,
    InIteratorType f_first,
    InIteratorType f_last) // PRQA S 2008 # Technical debt L2 id=00c14fe639e1a30d4633-7d2361d421b2fbb7 // PRQA S 2009 #
                           // Technical debt L2 id=00c1407fc790b249445c-c5544c9d00c9e231
{
    VFC_REQUIRE(max_size() >= (m_numElements + vfc::distance(f_first, f_last)));
    VFC_REQUIRE2(nullptr != f_pos.m_node_p, "Illegal iterator passed to insert");
    for (InIteratorType first = f_first; first != f_last; first++)
    {
        vfc::ignoreReturn(insert(f_pos, (*first)));
    }
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::iterator
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::insert_uninitialized(
    iterator f_pos) // PRQA S 2008 # Technical debt L2 id=00c10f13aea83c4543c9-cda853d81fe41c60
{
    VFC_REQUIRE(max_size() > m_numElements);
    VFC_REQUIRE2(nullptr != f_pos.m_node_p, "Illegal iterator passed to insert_uninitialized");
    return CListIterator(getBasePointer(), insertElement(f_pos, nullptr));
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::assign( // PRQA S 3800 # Technical debt L4
                                                                             // id=00c158edb370ae674b44-85b6d97304f84669
    size_type         f_count,
    const value_type& f_value_r)
{
    VFC_REQUIRE(f_count >= 0);
    VFC_REQUIRE(max_size() >= f_count);

    iterator iter = begin();
    // replace the existing values with the new values
    for (; iter != end() && f_count > 0; ++iter, --f_count)
    {
        *iter = f_value_r;
    }

    if (f_count > 0)
    {
        // if the input count is greater than the existing size of the list
        // then add more values
        insert(end(), f_count, f_value_r);
    }
    else
    {
        // remove the values
        erase(iter, end()); // PRQA S 3804 # Technical debt L2 id=00c185aa71899cac45bf-825593ac215726dd
    }
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
template <typename InIteratorType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::assign(
    InIteratorType f_first,
    InIteratorType f_last) // PRQA S 2008 # Technical debt L2 id=00c1addae33dd0a44bc3-9ee645bb0465dd52 // PRQA S 2009 #
                           // Technical debt L2 id=00c1c4b2e60444ab4b8c-1f98c17135e9b20a
{
    VFC_REQUIRE(max_size() >= vfc::distance(f_first, f_last));
    iterator source_it = begin();
    iterator end_it    = end();
    for (; source_it != end_it && f_first != f_last; ++source_it, ++f_first)
    {
        *source_it = *f_first;
    }
    if (f_first == f_last)
    {
        erase(source_it, end_it); // PRQA S 3804 # Technical debt L2 id=00c1c721aa3901944730-0056a56e913e3446
    }
    else
    {
        insert(end_it, f_first, f_last);
    }
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::iterator
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::erase(
    iterator f_pos) // PRQA S 2008 # Technical debt L2 id=00c1dd63543a45a34f19-cda853d81fe41c60
{
    VFC_REQUIRE2(0 != m_numElements, "erase requires non-empty list");
    VFC_REQUIRE2(f_pos != end(), "Cannot erase end() iterator");
    VFC_REQUIRE2(nullptr != f_pos.m_node_p, "Illegal iterator passed to erase");
    return CListIterator(getBasePointer(), removeElement(f_pos.m_node_p));
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::iterator
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::erase(
    iterator f_first,
    iterator f_last) // PRQA S 2008 # Technical debt L2 id=00c1ae0b73b918ee4d70-e49494ab0fd32802
{
    VFC_REQUIRE(
        (vfc::distance(f_first, f_last) <= m_numElements) && (nullptr != f_first.m_node_p) &&
        (nullptr != f_last.m_node_p));

    const CListIterator iter(getBasePointer(), removeRange(f_first.m_node_p, f_last.m_node_p));

    return iter;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::remove(const value_type& f_value_r)
{
    remove_if(vfc::intern::TRemovePredicate<value_type>(f_value_r));
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
template <class PredicateType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::remove_if(PredicateType f_predicate)
{
    iterator first = begin();
    iterator last  = end();
    while (first != last)
    {
        if (f_predicate(*first))
        {
            first = erase(first);
        }
        else
        {
            vfc::ignoreReturn(++first); // Dubious QACPP-3804 // PRQA S 3360 # Technical debt L2
                                        // id=00c1b6d8f34e3b6a4ee7-42f70090ee620a62
        }
    }
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::reverse()
{
    // for Fundamental datatypes, use stlalias::reverse
    // for UserDefined Data types, use TList::reverse function
    reverse(typename TIf<TIsFundamental<value_type>::value, true_t, false_t>::type());
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::swap(list_type& f_rhs_r)
{
    // dont do anything if the list specified is same as this list
    if (this == &f_rhs_r)
    {
        return;
    }

    swapIntern(f_rhs_r, typename AllocatorType::inplace_storage_flag{});
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::unique()
{
    unique(stlalias::equal_to<ValueType>());
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
template <class BinaryPredicateType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::unique(BinaryPredicateType f_binaryPredicate)
{
    // return if there are not elements in the list
    if (empty())
    {
        return;
    }

    iterator first = begin();
    iterator last  = end();

    iterator next = first;
    while (++next != last) // PRQA S 4290 # Technical debt L2 id=00c1dd975429f4eb4004-eea4f1cc26d6c293 // PRQA S 3360 #
                           // Technical debt L2 id=00c1d5639cc29a8a4c68-6274ff7983c243d9
    {
        if (f_binaryPredicate(*first, *next))
        {
            erase(next); // PRQA S 3804 # Technical debt L2 id=00c1abb18f63a3ae40bb-aea26fae495aa648
        }
        else
        {
            first = next;
        }
        next = first;
    }
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::splice(
    iterator   f_pos,
    list_type& f_list_r) // PRQA S 2009 # Technical debt L2 id=00c1d83e13a77a0e4b35-01bf7a30055ee089
{
    splice(f_pos, f_list_r, f_list_r.begin(), f_list_r.end());
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::splice(
    iterator   f_pos,
    list_type& f_list_r,
    iterator   f_iter) // PRQA S 2009 # Technical debt L2 id=00c18d2e69b891cb4d9a-9d48452b3840a542
{
    iterator next = f_iter;
    ++next;
    splice(f_pos, f_list_r, f_iter, next);
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::splice(
    iterator   f_pos,
    list_type& f_list_r,
    iterator   f_first,
    iterator   f_last) // PRQA S 2008 # Technical debt L2 id=00c18eb9b7d0c68d46b5-826777485b4ef71b
{
    VFC_REQUIRE(
        0 != f_pos.m_node_p && // PRQA S 3124 # Technical debt L7 id=00c10bb481ba9bc84101-bd5aafa1cbacf742
        0 != f_first.m_node_p && 0 != f_last.m_node_p);

    // The distance between f_first and f_last must be both:
    // * Smaller or equal to the size of f_list_r.
    // * Smaller or equal to the remaining entries in this list (max_size - m_numElements)
    VFC_REQUIRE(
        static_cast<size_type>(vfc::distance(f_first, f_last)) <=
        vfc::min(f_list_r.size(), max_size() - m_numElements));

    if (f_first != f_last)
    {
        spliceIntern(f_pos, f_list_r, f_first, f_last, typename AllocatorType::inplace_storage_flag{});
    }
    else
    {
        // intentionally left blank
    }
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::spliceIntern(
    iterator   f_pos,
    list_type& f_list_r,
    iterator   f_first,
    iterator   f_last,
    true_t) // PRQA S 2009 # Technical debt L2 id=00c1be0c874389a44d8a-4bc827fd2f36669d // PRQA S 2008 # Technical debt
            // L2 id=00c116fde491cde8469c-5f3c22018cb74598
{
    VFC_REQUIRE(f_first != f_last);

    insert(f_pos, f_first, f_last);
    f_list_r.erase(f_first, f_last); // PRQA S 3804 # Technical debt L2 id=00c1910b5defd4ec4641-f559322faa29286f
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::spliceIntern(
    iterator   f_pos,
    list_type& f_list_r,
    iterator   f_first,
    iterator   f_last,
    false_t) // PRQA S 2009 # Technical debt L2 id=00c198f55d21d6fa4e9c-bf96671cd9c4be69 // PRQA S 2008 # Technical debt
             // L2 id=00c199a190ab1b4c4455-8e4fc6bb71782568
{
    const size_type count = static_cast<size_type>(vfc::distance(f_first, f_last));

    VFC_REQUIRE(f_first != f_last);

    relinkNode(f_pos, f_first, f_last);
    f_list_r.m_numElements -= count;
    m_numElements += count;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::merge(list_type& f_list_r)
{
    merge(f_list_r, stlalias::less<ValueType>());
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
template <class OrderingType>
inline void
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::merge(list_type& f_list_r, OrderingType f_orderType)
{
    VFC_REQUIRE(max_size() >= (m_numElements + f_list_r.size()));

    if (this != &f_list_r)
    {
        mergeIntern(f_list_r, f_orderType, typename AllocatorType::inplace_storage_flag{});
    }

    VFC_ENSURE(f_list_r.empty());
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
template <class OrderingType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::mergeIntern(
    list_type&   f_list_r,
    OrderingType f_orderType,
    vfc::true_t)
{
    iterator first1 = begin();
    iterator last1  = end();
    iterator first2 = f_list_r.begin();
    iterator last2  = f_list_r.end();

    while (first1 != last1 && first2 != last2)
    {
        if (f_orderType(*first2, *first1))
        {
            iterator next = first2;
            ++next;
            splice(first1, f_list_r, first2);
            first2 = next;
        }
        else
        {
            ++first1;
        }
    }
    if (first2 != last2)
    {
        splice(last1, f_list_r, first2, last2);
    }
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
template <class OrderingType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::mergeIntern(
    list_type&   f_list_r,
    OrderingType f_orderType,
    vfc::false_t)
{
    iterator first1 = begin();
    iterator last1  = end();
    iterator first2 = f_list_r.begin();
    iterator last2  = f_list_r.end();

    // relink nodes
    while (first1 != last1 && first2 != last2)
    {
        if (f_orderType(*first2, *first1))
        {
            iterator next = first2;
            relinkNode(
                first1, first2, ++next); // PRQA S 3360 # Technical debt L2 id=00c15a14bec0a01d44ce-6e3f8c18a2a091c9
            first2 = next;
        }
        else
        {
            ++first1;
        }
    }
    if (first2 != last2)
    {
        relinkNode(last1, first2, last2);
    }
    m_numElements += f_list_r.m_numElements;
    f_list_r.m_numElements = 0;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::sort()
{
    sort(stlalias::less<ValueType>());
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
template <class ComparisonPredicateType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::sort(ComparisonPredicateType f_predicate)
{
    // Nothing to do if there arent 2 items
    if (2 > m_numElements)
    {
        return;
    }

    // take the current list
    iterator curr = begin();
    iterator iter = curr;
    // point to the second element
    ++curr;
    while (curr != end())
    {
        if (f_predicate(*curr, *iter))
        {
            // do the swap and continue
            iterator next = curr;
            relinkNode(iter, curr, ++next); // PRQA S 3360 # Technical debt L2 id=00c14466e61b33dd4386-9c7cabe79ad7e4c1
            curr = next;
            iter = begin();
        }
        else
        {
            ++iter;
        }
        if (iter == curr)
        {
            ++curr;
            iter = begin();
        }
    }
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::clear()
{
    vfc::ignoreReturn(erase(begin(), end()));
    VFC_ENSURE2(0 == m_numElements, "List not empty after clear!");
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::reference
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::front()
{
    VFC_REQUIRE(!empty());
    return *begin();
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::const_reference
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::front() const
{
    VFC_REQUIRE(!empty());
    return *begin();
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::reference
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::back()
{
    VFC_REQUIRE(!empty());
    return *(--end()); // PRQA S 3362 # Technical debt L2 id=00c141fe12c3d81e4760-a04c2cce065b3030
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::const_reference
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::back() const
{
    VFC_REQUIRE(!empty());
    return *(--end()); // PRQA S 3362 # Technical debt L2 id=00c1687d3aa819b949da-a04c2cce065b3030
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::node_type*
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::getBasePointer() const
{
    // Dispatch pointer vs indicies.
    return getBasePointer(UsePointer_t());
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::node_type*
    vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::getBasePointer(
        true_t) const // PRQA S 4212 # Technical debt L2 id=00c125729fb2ddec422f-167207dd868eaa8f
{
    return static_cast<node_type*>(0);
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::node_type*
    vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::getBasePointer(false_t) const
{
    return const_cast<node_type*>(
        m_nodeAlloc.getBasePointer()); // PRQA S 3066 # Technical debt L4 id=00c14fd74f768f014d0c-92622cb7cd11ed19
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::position_type*
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::getHead()
{
    // Dispatch pointer vs indicies.
    return getHead(UsePointer_t());
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline const typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::position_type*
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::getHead() const
{
    // Dispatch pointer vs indicies.
    return getHead(UsePointer_t());
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::position_type*
    // qacpp-4211: This non const function is needed due to overload resolution
    vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::getHead(false_t) // PRQA S 4211
{
    // Take the base-pointer (exists only with TFixedMemPoolAllocator) and add size, thus point behind memory. This
    // assumes that there is a CNodeBase stored 0 bytes behind the memory of the allocator and max_size() considers
    // alignment. Return as CNodeBase*.
    return static_cast<position_type*>(
        getBasePointer() +
        m_nodeAlloc.max_size()); // PRQA S 2841 # Technical debt L5 id=00c1e0e3713167914f9b-fc1e2fc6549e222e // PRQA S
                                 // 3705 # Technical debt L2 id=00c1cb2781df2e5c4726-ad93c9878c62c9f7
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline const typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::position_type*
    vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::getHead(false_t) const
{
    return static_cast<const position_type*>(
        getBasePointer() +
        m_nodeAlloc.max_size()); // PRQA S 2841 # Technical debt L5 id=00c13b2a893bdae64044-b5bd14123cade9d6 // PRQA S
                                 // 3705 # Technical debt L2 id=00c19ae3e94f3d364670-e94188963c000fe4
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::position_type*
    vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::getHead(true_t)
{
    // Return our member as CNodeBase*.
    return &m_head; // PRQA S 4024 # Technical debt L2 id=00c1ff701555b1fc4d8c-d268a60717275544
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline const typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::position_type*
    vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::getHead(true_t) const
{
    return &m_head;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::node_type*
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::insertElement(
    iterator          f_pos, // PRQA S 2008 # Technical debt L2 id=00c158153f610eec4668-d0c0fb54b410eb8a
    const value_type* f_value_p)
{
    VFC_REQUIRE(max_size() > m_numElements);

    node_type* const l_base_p = getBasePointer();
    const CBaseInfo  l_baseInfo(l_base_p, sizeof(node_type));

    position_type* const prevNode_p = f_pos.m_node_p->prev(l_baseInfo);
    position_type* const nextNode_p = f_pos.m_node_p;

    // allocate memory for the node
    node_type* const newnode_p = m_nodeAlloc.allocate(1);

    VFC_REQUIRE(nullptr != newnode_p);

    if (nullptr != newnode_p)
    {
        if (nullptr != f_value_p)
        {
            // construct the value by placement new
            static_cast<void>(new (newnode_p) CNode(*f_value_p));
        }
        // else leave newnode_p's payload completely uninitialized

        // set the linked list
        vfc::ignoreReturn(position_type::linkNode(l_baseInfo, newnode_p, prevNode_p, nextNode_p));
        ++m_numElements;
    }
    return newnode_p;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::position_type*
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::removeElement(position_type* f_node_p)
{
    VFC_ASSERT2(nullptr != f_node_p, "Internal error: trying to remove null node");
    VFC_ASSERT2(f_node_p != getHead(), "Internal error: trying to remove end()");

    node_type* const node_p =
        static_cast<node_type*>(f_node_p); // PRQA S 3070 # Technical debt L4 id=00c11d46983bc13342ad-f28623abe8dbec81

    node_type* const l_base_p = getBasePointer();
    const CBaseInfo  l_baseInfo(l_base_p, sizeof(node_type));

    // set the linked list
    // qacpp-2812-asserted: The data-flow analysis is not correct here:
    // The condition where the pointer is checked is inside a macro that expands
    // to a throw expression. Hence the point of dereference is not reached.
    // If the assertion is replaced with the __qacpp_assert macro this warning
    // also vanishes.
    CNodeBase* const     l_prev_p = f_node_p->prev(l_baseInfo); // PRQA S 2812 # asserted
    CNodeBase* const     l_next_p = f_node_p->next(l_baseInfo); // PRQA S 2812 # asserted
    position_type* const retNode  = position_type::unlinkNode(l_baseInfo, l_prev_p, l_next_p);

    // calls the destructor of the value_type class
    node_p->~node_type(); // PRQA S 3803 # Technical debt L2 id=00c1ab06938a884d4935-a797d7c8507d8657

    // remove memory allocated for the node
    m_nodeAlloc.deallocate(node_p, 1);

    --m_numElements;

    return retNode;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::position_type*
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::removeRange(
    position_type* f_startNode_p,
    position_type* f_endNode_p)
{
    VFC_REQUIRE((nullptr != f_startNode_p) && (nullptr != f_endNode_p));

    node_type* current_p = static_cast<node_type*>(
        f_startNode_p); // PRQA S 3070 # Technical debt L4 id=00c16ccc627af37348a8-9abf12627f9b7a47
    position_type*   next_p   = nullptr;
    node_type* const l_base_p = getBasePointer();
    const CBaseInfo  l_baseInfo(l_base_p, sizeof(node_type));

    position_type* retNode = f_endNode_p;
    // do unlinking only if the there are nodes to remove
    if (f_startNode_p != f_endNode_p)
    {
        // set the linked list
        retNode = position_type::unlinkNode(l_baseInfo, current_p->prev(l_baseInfo), f_endNode_p);
    }
    while (current_p != f_endNode_p)
    {
        next_p = current_p->next(l_baseInfo);
        current_p->~node_type(); // PRQA S 3803 # Technical debt L2 id=00c1f815b42b71a14fc2-e33c57692a400977
        m_nodeAlloc.deallocate(current_p, 1);
        --m_numElements;
        current_p =
            static_cast<node_type*>(next_p); // PRQA S 3070 # Technical debt L4 id=00c1691b365813f6427d-342859801d662f67
    }
    return retNode;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::reverse(true_t)
{
    stlalias::reverse(begin(), end());
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::reverse(false_t)
{
    // how does this logic work
    // assume list = 1,2,3,4
    // iter 1: 2,1,3,4
    // iter 2: 3,2,1,4
    // iter 3: 4,3,2,1
    if (2 <= m_numElements)
    { // no point in reversing if the count there is only 1 element
        iterator last = end();
        for (iterator next = ++begin();
             next != last;) // PRQA S 4235 # Technical debt L4 id=00c1d125aafd6cc644e4-e189e7af29315ed7 // PRQA S 3360 #
                            // Technical debt L2 id=00c1d7297beb5caa4461-3959117f306dfba5
        {                   // move next element to beginning
            iterator curr = next;
            relinkNode(
                begin(), curr, ++next); // PRQA S 3360 # Technical debt L2 id=00c11cd084b93a7f46fb-87755dd76e0963dd
        }
    }
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::swapIntern(list_type& f_rhs_r, vfc::true_t)
{
    // loop the list

    iterator lhsFirst = begin();
    iterator lhsLast  = end();
    iterator rhsFirst = f_rhs_r.begin();
    iterator rhsLast  = f_rhs_r.end();
    for (; rhsFirst != rhsLast && lhsFirst != lhsLast; ++lhsFirst, ++rhsFirst)
    {
        // exchange the items
        stlalias::swap(*lhsFirst, *rhsFirst);
    }
    if (lhsFirst == lhsLast)
    {
        // remove from right and add to left
        insert(lhsLast, rhsFirst, rhsLast);
        f_rhs_r.erase(rhsFirst, rhsLast); // PRQA S 3804 # Technical debt L2 id=00c13e48980e548045b4-89964ee86cc42418
    }
    else
    {
        // remove from left and add to right
        f_rhs_r.insert(rhsLast, lhsFirst, lhsLast);
        erase(lhsFirst, lhsLast); // PRQA S 3804 # Technical debt L2 id=00c1e3facff317444b36-0e5ee035642f91aa
    }
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::swapIntern(list_type& f_rhs_r, vfc::false_t)
{
    // this list is empty. so only relink the nodes from the other list.
    if (empty())
    {
        relinkNode(end(), f_rhs_r.begin(), f_rhs_r.end());
    }
    // RHS list is empty. so only relink the nodes from the this list.
    else if (f_rhs_r.empty())
    {
        relinkNode(f_rhs_r.end(), begin(), end());
    }
    // Both lists have data. so swap the nodes
    else
    {
        node_type* l_base_p = getBasePointer();
        CBaseInfo  l_baseInfo(l_base_p, sizeof(node_type));

        position_type::swapNodeBase(l_baseInfo, *getHead(), *f_rhs_r.getHead());
    }
    // swap the counts
    size_type temp        = m_numElements;
    m_numElements         = f_rhs_r.m_numElements;
    f_rhs_r.m_numElements = temp;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::relinkNode( // PRQA S 4211 # Technical debt L4
                                                                     // id=00c16bbbe5032baf4665-b886f3475e350e8d
    iterator f_pos,
    iterator f_first,
    iterator f_last) // PRQA S 2008 # Technical debt L2 id=00c1cb6da05d30354cf7-f6b69d7af3d5c90a
{
    node_type* l_base_p = getBasePointer();
    CBaseInfo  l_baseInfo(l_base_p, sizeof(node_type));

    position_type::relinkNodeBase(l_baseInfo, f_pos.m_node_p, f_first.m_node_p, f_last.m_node_p);
}

///////////////////////////////////////////////////////////////////////////////////////////////
// Global functions
///////////////////////////////////////////////////////////////////////////////////////////////

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline bool vfc::operator==(
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list1_r,
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list2_r)
{
    if (f_list1_r.size() != f_list2_r.size())
    {
        return false;
    }

    using const_iterator = typename TList<ValueType, AllocatorType, MaxSizePolicyType>::const_iterator;

    const_iterator end1  = f_list1_r.end();
    const_iterator iter1 = f_list1_r.begin();

    const_iterator iter2 = f_list2_r.begin();

    while ((iter1 != end1) &&
           (*iter1 == *iter2)) // PRQA S 3270 # Technical debt L2 id=00c188e0c996535a4413-74ef9af8f5fd58c9
    {
        ++iter1;
        ++iter2;
    }
    return (iter1 == end1);
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline bool vfc::operator<(
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list1_r,
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list2_r)
{
    return stlalias::lexicographical_compare(f_list1_r.begin(), f_list1_r.end(), f_list2_r.begin(), f_list2_r.end());
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline bool vfc::operator!=(
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list1_r,
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list2_r)
{
    return !(f_list1_r == f_list2_r);
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline bool vfc::operator>(
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list1_r,
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list2_r)
{
    return f_list2_r < f_list1_r;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline bool vfc::operator<=(
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list1_r,
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list2_r)
{
    return !(f_list2_r < f_list1_r);
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline bool vfc::operator>=(
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list1_r,
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list2_r)
{
    return !(f_list1_r < f_list2_r);
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void vfc::swap(
    TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list1_r,
    TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list2_r)
{
    f_list1_r.swap(f_list2_r);
}

///////////////////////////////////////////////////////////////////////////////////////////////
// CListIterator implementation
///////////////////////////////////////////////////////////////////////////////////////////////

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListIterator::CListIterator(
    const typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListIterator& f_iter_r)
    : CListConstIterator()
{
    operator=(f_iter_r);
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListIterator::reference
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListIterator::operator*() const
{
    return ((static_cast<node_type*>(this->m_node_p))
                ->value()); // PRQA S 3070 # Technical debt L4 id=00c19bdfa08de733413b-0450d713ae22d5c9
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListIterator::pointer
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListIterator::operator->() const
{
    return &static_cast<node_type*>(this->m_node_p)
                ->value(); // PRQA S 3070 # Technical debt L4 id=00c12faa1d349c544df5-c645b319e037a27b
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListIterator&
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListIterator::operator++()
{
    this->checkIteratorPointsToValidNode(); // Incrementing beyond 'end'?
    // Calls the next() method on a CNodeBase we are pointing to. It provides via the CBaseInfo the base address of the
    // fixed memory, since in the case of indices a transformation from memory address to index has to be done. In case
    // of a pointer-based container the base-pointer is nullptr.
    const CBaseInfo l_baseInfo(this->m_base_p, sizeof(node_type));
    this->m_node_p = this->m_node_p->next(l_baseInfo);
    return *this;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListIterator
    vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListIterator::operator++(postfix_operator_tag)
{
    this->checkIteratorPointsToValidNode(); // Incrementing beyond 'end'?
    const self_type temp = *this;
    const CBaseInfo l_baseInfo(this->m_base_p, sizeof(node_type));
    this->m_node_p = this->m_node_p->next(l_baseInfo);
    return temp;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListIterator&
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListIterator::operator--()
{
    const CBaseInfo l_baseInfo(this->m_base_p, sizeof(node_type));
    this->m_node_p =
        this->m_node_p->prev(l_baseInfo);   // PRQA S 2811 # Technical debt L8 id=00e469d8c026358a467a-5ccda23ac0f79c64
    this->checkIteratorPointsToValidNode(); // If pointing to 'end' now, we decremented beyond 'begin'.
    return *this;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListIterator
    vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListIterator::operator--(postfix_operator_tag)
{
    self_type temp = *this;
    CBaseInfo l_baseInfo(this->m_base_p, sizeof(node_type));
    this->m_node_p = this->m_node_p->prev(l_baseInfo);
    this->checkIteratorPointsToValidNode(); // If pointing to 'end' now, we decremented beyond 'begin'.
    return temp;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListIterator&
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListIterator::operator=(
    const typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListIterator& f_iter_r)
{
    if (this != &f_iter_r)
    {
        this->m_base_p = f_iter_r.m_base_p;
        this->m_node_p = f_iter_r.m_node_p;
    }
    return *this;
}

///////////////////////////////////////////////////////////////////////////////////////////////
// CListConstIterator implementation
///////////////////////////////////////////////////////////////////////////////////////////////

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListConstIterator&
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListConstIterator::operator=(
    const typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListConstIterator& f_iter_r)
{
    if (this != &f_iter_r)
    {
        m_base_p = f_iter_r.m_base_p;
        m_node_p = f_iter_r.m_node_p;
    }
    return *this;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListConstIterator::reference
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListConstIterator::operator*() const
{
    return ((static_cast<const node_type*>(m_node_p))
                ->value()); // PRQA S 3070 # Technical debt L4 id=00c16dc1ce11ace34451-50b054c62201e801
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListConstIterator::pointer
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListConstIterator::operator->() const
{
    return &static_cast<const node_type*>(m_node_p)
                ->value(); // PRQA S 3070 # Technical debt L4 id=00c1fd997f591ac54a94-5a2061900db0d13f
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListConstIterator&
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListConstIterator::operator++()
{
    checkIteratorPointsToValidNode(); // Incrementing beyond 'end'?
    const CBaseInfo l_baseInfo(m_base_p, sizeof(node_type));
    m_node_p = m_node_p->next(l_baseInfo);
    return *this;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListConstIterator
    vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListConstIterator::operator++(built_in::int_t)
{
    checkIteratorPointsToValidNode(); // Incrementing beyond 'end'?
    const self_type temp = *this;
    const CBaseInfo l_baseInfo(m_base_p, sizeof(node_type));
    m_node_p = m_node_p->next(l_baseInfo);
    return temp;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListConstIterator&
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListConstIterator::operator--()
{
    CBaseInfo l_baseInfo(m_base_p, sizeof(node_type));
    m_node_p = m_node_p->prev(l_baseInfo);
    checkIteratorPointsToValidNode(); // If pointing to 'end' now, we decremented beyond 'begin'.
    return *this;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListConstIterator
    vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListConstIterator::operator--(postfix_operator_tag)
{
    self_type temp = *this;
    CBaseInfo l_baseInfo(m_base_p, sizeof(node_type));
    m_node_p = m_node_p->prev(l_baseInfo);
    checkIteratorPointsToValidNode(); // If pointing to 'end' now, we decremented beyond 'begin'.
    return temp;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline bool vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListConstIterator::
            operator==( // PRQA S 2066 # Technical debt L4 id=00c131b92a7c69bc4812-782a3ffbf424fc72
    const typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListConstIterator& f_rhs_r) const
{
    return m_node_p == f_rhs_r.m_node_p;
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline bool vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListConstIterator::
            operator!=( // PRQA S 2066 # Technical debt L4 id=00c1a9eeb4baea514a72-2f3fc851a8c4e532
    const typename vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListConstIterator& f_rhs_r) const
{
    return !(*this == f_rhs_r);
}

template <class ValueType, class AllocatorType, class MaxSizePolicyType>
inline void
vfc::TList<ValueType, AllocatorType, MaxSizePolicyType>::CListConstIterator::checkIteratorPointsToValidNode() const
{
    // Assert that we don't point at 'end' currently. The pointer-based containers are not asserted.
    intern::IterHelper<AllocatorType>::checkIteratorPointsToValidNode(
        static_cast<const CNodeBase*>(m_base_p), m_node_p, sizeof(node_type));
}

#endif // VFC_LIST_INL_INCLUDED

//=============================================================================

