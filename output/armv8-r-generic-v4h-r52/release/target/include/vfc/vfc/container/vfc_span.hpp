//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_SPAN_HPP_INCLUDED
#define ZX_VFC_SPAN_HPP_INCLUDED

#include "vfc/core/vfc_types.hpp"            //for vfc::int32_t
#include "vfc/core/vfc_metaprog.hpp"         //for TAddConst
#include "vfc/core/vfc_static_assert.hpp"    //for VFC_STATIC_ASSERT
#include "vfc/core/vfc_checked_iterator.hpp" //checkable_iterator
#include "vfc/core/vfc_iterator.hpp"         //reverse_iterator
#include "vfc/container/vfc_carray.hpp"      //for TCArray
#include "vfc/container/vfc_fixedvector.hpp" //for TFixedVector

namespace vfc
{
//=============================================================================
//  TSpan<>
//-----------------------------------------------------------------------------
/// TSpan provides a view to 0 to n instances that live in contiguous memory.
/// It is useful as an interface class because it does not introduce a non-type
/// template parameter and can therefore work with different statically sized
/// containers including TCArray, TFixedVector and plain C pointers.
///
/// @par Description:
///   A template class representing a with to typed object instances in memoty.
/// @tparam ValueType       DataType to be accessed (not stored!)

/// @ingroup                vfc_group_containers
//=============================================================================
template <typename ValueType>
class TSpan
{
  public:
    //======================================================
    // constructors
    //======================================================
    /// Default constructor, takes no arguments, construct empty span
    /// @dspec{301}     The SW component "vfc" shall create a new empty Span instance.
    TSpan();

    /// constructor method from a pointer and a size.
    /// Not implemented as constructor and given an ugly name to make reviews
    /// easier.
    /// @dspec{306}     The SW component "vfc" shall be able to initialize and return a new
    ///                 Span instance from a given pointer to an item and a given size number.
    /// @param f_begin_p  pointer to the begin of the view
    /// @param f_size     size of view in number of items, not bytes
    /// @return TSpan instance representing a view to the given range
    /// @pre If f_begin_p is a nullptr then f_size must be 0
    static TSpan from_pointer_and_size(ValueType* f_begin_p, size_t f_size);

    /// A type that represent the data type accessed by a TSpan
    using value_type = ValueType;

    /// A type that provides a iterator for TSpan which is used for traversing
    /// the items.
    using iterator = intern::checkable_iterator<ValueType*, TSpan>;

    /// A type that provides a const iterator for TSpan which is used for traversing
    /// the items.
    using const_iterator = intern::checkable_iterator<const ValueType*, TSpan>;

    /// A type that provides a iterator for TSpan which is used for traversing
    /// the items.
    using reverse_iterator = vfc::reverse_iterator<iterator>;

    /// A type that provides a const iterator for TSpan which is used for traversing
    /// the items.
    using const_reverse_iterator = vfc::reverse_iterator<const_iterator>;

    /// A type that provides a reference to a item stored in a TSpan for reading
    /// and performing operations.
    using reference = value_type&;

    /// A type that provides a reference to a const item stored in a TSpan for
    /// reading and performing const operations.
    using const_reference = const value_type&;

    /// A type that counts the number of items in a TSpan.
    using size_type = vfc::int32_t;

    //---------------------------------------------------------------------
    /// Returns a span to the same data treated as const
    ///
    /// @dspec{1742}     The SW component "vfc" shall return a Span instance to the same
    ///                  data as a given Span instance where the resulting Span will only
    ///                  provide read-only access to the data.
    /// @return returns new TSpan instance to same const data
    //---------------------------------------------------------------------
    TSpan<typename TAddConst<ValueType>::type> as_const() const;

    //---------------------------------------------------------------------
    /// Returns iterator to the first stored item.
    /// Attention: If the TSpan is empty this will be the same as end() and
    /// may not be dereferenced.
    ///
    /// @dspec{310}     The SW component "vfc" shall return an iterator to the first
    ///                 item of the given Span instance.
    /// @return returns iterator to first stored item
    //---------------------------------------------------------------------
    iterator begin();

    //---------------------------------------------------------------------
    /// Returns const_iterator to the first stored item.
    /// Attention: If the TSpan is empty this will be the same as end() and
    /// may not be dereferenced.
    ///
    /// @dspecref{310}
    /// @return returns const iterator to first stored item
    //---------------------------------------------------------------------
    const_iterator begin() const;

    //---------------------------------------------------------------------
    /// Returns const_iterator to the first stored item.
    /// Attention: If the TSpan is empty this will be the same as cend() and
    /// may not be dereferenced.
    ///
    /// @dspecref{310}
    /// @return returns const iterator to first stored item
    //---------------------------------------------------------------------
    const_iterator cbegin() const;

    //---------------------------------------------------------------------
    /// Returns iterator pointing after the last stored item.
    /// Attention: This iterator must never be dereferenced.
    ///
    /// @dspec{311}     The SW component "vfc" shall return an iterator after the last
    ///                 item of the given Span instance.
    /// @return returns iterator marking the end of this container
    //---------------------------------------------------------------------
    iterator end();

    //---------------------------------------------------------------------
    /// Returns const_iterator pointing after the last stored item.
    /// Attention: This iterator must never be dereferenced.
    ///
    /// @dspecref{311}
    /// @return returns const_iterator marking the end of this container
    //---------------------------------------------------------------------
    const_iterator end() const;

    //---------------------------------------------------------------------
    /// Returns const_iterator pointing after the last stored item.
    /// Attention: This iterator must never be dereferenced.
    ///
    /// @dspecref{311}
    /// @return returns const_iterator marking the end of this container
    //---------------------------------------------------------------------
    const_iterator cend() const;

    //---------------------------------------------------------------------
    /// Returns reverse_iterator pointing to the last item in the view.
    /// Attention: If the TSpan is empty this will be the same as rend() and
    /// may not be dereferenced.
    ///
    /// @dspec{312}     The SW component "vfc" shall return an reverse iterator to the
    ///                 last item of the given Span instance.
    /// @return returns reverse_iterator to the last item of this container
    //---------------------------------------------------------------------
    reverse_iterator rbegin();

    //---------------------------------------------------------------------
    /// Returns const_reverse_iterator pointing to the last item in the view.
    /// Attention: If the TSpan is empty this will be the same as rend() and
    /// may not be dereferenced.
    ///
    /// @dspecref{312}
    /// @return returns const_reverse_iterator to the last item of this container
    //---------------------------------------------------------------------
    const_reverse_iterator rbegin() const;

    //---------------------------------------------------------------------
    /// Returns const_reverse_iterator pointing to the last item in the view.
    /// Attention: If the TSpan is empty this will be the same as crend() and
    /// may not be dereferenced.
    ///
    /// @dspecref{312}
    /// @return returns const_reverse_iterator to the last item of this container
    //---------------------------------------------------------------------
    const_reverse_iterator crbegin() const;

    //---------------------------------------------------------------------
    /// Returns reverse_iterator pointing after the first item in the view.
    /// Attention: This iterator must never be dereferenced.
    /// @dspec{313}     The SW component "vfc" shall return an reverse iterator to the
    ///                 position before the first item of the given Span instance.
    /// @return returns reverse_iterator marking the end of this container in reverse order
    //---------------------------------------------------------------------
    reverse_iterator rend();

    //---------------------------------------------------------------------
    /// Returns const_reverse_iterator pointing after the first item in the
    /// view.
    /// Attention: This iterator must never be dereferenced.
    /// @dspecref{313}
    /// @return returns const_reverse_iterator marking the end of this container in reverse order
    //---------------------------------------------------------------------
    const_reverse_iterator rend() const;

    //---------------------------------------------------------------------
    /// Returns const_reverse_iterator pointing after the first item in the
    /// view.
    /// Attention: This iterator must never be dereferenced.
    /// @dspecref{313}
    /// @return returns const_reverse_iterator marking the end of this container in reverse order
    //---------------------------------------------------------------------
    const_reverse_iterator crend() const;

    //---------------------------------------------------------------------
    /// Returns a reference to the item at position f_pos in TSpan.
    /// It is not legal to call this operator when the TSpan is empty.
    /// @dspec{309}     The SW component "vfc" shall return a reference to an item at a given
    ///                 index of a given Span instance.
    /// \pre
    ///   0 <= f_pos and f_pos < size()
    /// @param f_pos    position number of item
    /// @return returns reference to item at specified position
    //---------------------------------------------------------------------
    template <typename IndexType>
    reference operator[](IndexType f_pos);

    //---------------------------------------------------------------------
    /// Returns a const reference to the item at position f_pos in TSpan.
    /// It is not legal to call this operator when the TSpan is empty.
    /// @dspecref{309}
    /// \pre
    ///   0 <= f_pos and f_pos < size()
    /// @param f_pos    position number of item
    /// @return returns const reference to item at specified position
    //---------------------------------------------------------------------
    template <typename IndexType>
    const_reference operator[](IndexType f_pos) const;

    //---------------------------------------------------------------------
    /// Returns the number of items the array can hold
    /// @dspec{303}     The SW component "vfc" shall return the current size of the
    ///                 given Span instance.
    /// @return returns size of array in items, not bytes
    //---------------------------------------------------------------------
    size_type size() const;

    //---------------------------------------------------------------------
    /// Checks whether the span is empty.
    /// @dspec{302}     The SW component "vfc" shall return the boolean value "true" if
    ///                 the given Span instance is empty otherwise it shall return the boolean
    ///                 value "false".
    /// @return returns true iff no items are in this view
    //---------------------------------------------------------------------
    bool empty() const;

  private:
    /// private constructor from pointers
    TSpan(ValueType* f_begin_p, ValueType* f_end_p);

    /// Points to the first valid element in the span.
    ValueType* m_begin_p; ///< start of view

    /// Attention: Points after the last valid element in the span.
    ValueType* m_end_p; ///<  end of view
};

/// conversion methods

/// Construct TSpan from TCArray instance.
/// The ownership of the data remains with the TCArray instance.
/// @dspec{307}     The SW component "vfc" shall be able to initialize a new Span instance
///                 from a given CArray instance.
/// @tparam ValueType       DataType to be accessed
/// @tparam CapacityValue   Capacity of TCArray
/// @param f_carray         array owning the data items
/// @return                 span representing the whole TCArray range
template <typename ValueType, vfc::int32_t CapacityValue>
TSpan<ValueType> as_span(vfc::TCArray<ValueType, CapacityValue>& f_carray);

/// Construct a const TSpan from a const TCArray instance.
/// The ownership of the data remains with the TCArray instance.
/// @dspecref{307}
/// @tparam ValueType       DataType to be accessed
/// @tparam CapacityValue   Capacity of TCArray
/// @param f_carray         array owning the data items
/// @return                 const span representing the whole TCArray range
template <typename ValueType, vfc::int32_t CapacityValue>
TSpan<const ValueType> as_span(const vfc::TCArray<ValueType, CapacityValue>& f_carray);

/// Construct TSpan from TFixedVector instance, taking dynamic size.
/// This will construct a span that views the current size of the
/// vector.
/// Attention: Size changes of the vector after the creation of the
///  span will not be reflected in the size of the span.
///  If the size of the vector is reduced, the span will point to
///  invalid (e.g. erased or cleared memory)
/// @dspec{308}     The SW component "vfc" shall be able to initialize a new Span instance
///                 from a given FixedVector instance.
/// @tparam ValueType       DataType to be accessed
/// @tparam CapacityValue   Capacity of TFixedVector
/// @tparam AllocatorType   Allocator type used by TFixedVector
/// @param f_fixedVector    TFixedVector owning the data items
/// @return                 span representing the currently allocated item of the vector
template <typename ValueType, vfc::int32_t CapacityValue, class AllocatorType>
TSpan<ValueType> as_span(vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>& f_fixedVector);

/// Construct const TSpan from const TFixedVector instance, taking dynamic size.
/// This will construct a const TSpan that views the current size of the
/// vector.
/// Attention: Size changes of the vector after the creation of the
///  span will not be reflected in the size of the span.
///  If the size of the vector is reduced, the span will point to
///  invalid (e.g. erased or cleared memory)
/// @dspecref{308}
/// @tparam ValueType       DataType to be accessed
/// @tparam CapacityValue   Capacity of TFixedVector
/// @tparam AllocatorType   Allocator type used by TFixedVector
/// @param f_fixedVector    TFixedVector owning the data items
/// @return                 const span representing the currently allocated item of the vector
template <typename ValueType, vfc::int32_t CapacityValue, class AllocatorType>
TSpan<const ValueType> as_span(const vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>& f_fixedVector);

namespace intern
{
/// As TSpan is a "view" container we can not require the container to be
/// the same. But we can require that the ranges overlap.
/// This special handling for views is implemented in this specialization.
template <typename IterType1, typename IterType2, typename ValueType1, typename ValueType2>
struct TContainerCheck<
    intern::TCheckedIterator<IterType1, TSpan<ValueType1>>,
    intern::TCheckedIterator<IterType2, TSpan<ValueType2>>>;
} // namespace intern

} // namespace vfc

#include "vfc/container/vfc_span.inl"

#endif // ZX_VFC_SPAN_HPP_INCLUDED

