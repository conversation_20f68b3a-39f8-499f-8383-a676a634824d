//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_NODEBASE_HPP_INCLUDED
#define ZX_VFC_NODEBASE_HPP_INCLUDED

// vfc/core includes
#include "vfc/core/vfc_types.hpp"
#include "vfc/core/vfc_static_assert.hpp"
#include "vfc/core/vfc_metaprog.hpp"
#include "vfc/core/vfc_util.hpp"

namespace vfc
{ // namespace vfc opened

/// forward declare base indexing helper class
struct CBaseInfo;

/// forward declare node base used for node links
template <bool UsePointer>
class TNodeBase;

namespace intern
{
/// TNodeLinks is used as a helper class for the vfc::TNodeBase supporting the full specialization between pointer
/// and non-pointer based link_types which have to be initialized in a different way.
///
/// @tparam UsePointer    Flag if pointer based link_type are to be used or non-pointer based link_type uint32_t
///                       are to be used forindices
/// @ingroup vfc_group_memory_containers

template <bool UsePointer>
class TNodeLinks;

/// Specialization for pointer based link_types.
template <>
class TNodeLinks<true>
{
  protected:
    using link_type = TNodeBase<true>*;

    TNodeLinks() : m_prev_idx(nullptr), m_next_idx(nullptr) {}

    TNodeLinks(TNodeLinks const& f_rhs) = default;

    ~TNodeLinks() = default;

    /// Stores the previous node ptr.
    link_type m_prev_idx;
    /// Stores the next node ptr.
    link_type m_next_idx;
};

/// Specialization for non-pointer based link_types.
template <>
class TNodeLinks<false>
{
  protected:
    using link_type = uint32_t;

    TNodeLinks() : m_prev_idx(0U), m_next_idx(0U) {}

    TNodeLinks(TNodeLinks const& f_rhs) = default;

    ~TNodeLinks() = default;

    /// Stores the previous node.
    link_type m_prev_idx;
    /// Stores the next node.
    link_type m_next_idx;
};
} // namespace intern

//=============================================================================
//  TNodeBase
//-----------------------------------------------------------------------------
/// TNodeBase is the base class and used by TList as a datastructure
/// for linked list implementation.
/// Each node has a link to both left and right, to facilitate the mechanism of
/// bi-directional list.
/// next() , prev() interfaces enable access the previous and next nodes
/// respectively.
/// The link_type can be a pointer or an index relative to an (external)
/// base pointer.
/// @tparam  UsePointer    flag if pointer are to be used, else use uint32_t indices
/// $Source: vfc_list.hpp $

/// @ingroup        vfc_group_containers
//=============================================================================

template <bool UsePointer>
class TNodeBase : public intern::TNodeLinks<UsePointer>
{
  public:
    using Base_t    = intern::TNodeLinks<UsePointer>;
    using link_type = typename Base_t::link_type;
    using CBaseInfo = vfc::CBaseInfo;

    //---------------------------------------------------------------------
    /// Default constructor.
    /// $Source: vfc_list.hpp $

    /// @dspec{1899}     The SW component "vfc" shall create a new node base instance
    ///                  which is initialized with zero values.
    //---------------------------------------------------------------------
    TNodeBase();

    //---------------------------------------------------------------------
    /// Copy constructor.
    /// @dspec{1900}     The SW component "vfc" shall create a new node base instance as
    ///                  a copy of a given node base instance.
    /// @param f_rhs TNodeBase that serves as the source of the copy
    /// $Source: vfc_list.hpp $

    //---------------------------------------------------------------------
    TNodeBase(TNodeBase const& f_rhs);

    //---------------------------------------------------------------------
    /// Destructor.
    /// $Source: vfc_list.hpp $

    //---------------------------------------------------------------------
    ~TNodeBase() = default;

    //---------------------------------------------------------------------
    /// Initialize node from CBaseInfo
    /// @dspec{1901}     The SW component "vfc" shall initialize a node base instance
    ///                  with a given base info instance such that the node base instance's
    ///                  previous link and its next link will link to itself.
    /// @param f_baseInfo_r CBaseInfo instance with base information
    /// $Source: vfc_list.hpp $

    //---------------------------------------------------------------------
    void init(const CBaseInfo& f_baseInfo_r);

    //---------------------------------------------------------------------
    /// Returns the next node.
    /// @dspec{1902}     The SW component "vfc" shall compute the next node base
    ///                  instance for a given start node base instance using a  given
    ///                  base info instance and return a pointer to the next node base
    ///                  instance.
    /// @param f_baseInfo_r CBaseInfo instance used to compute the next node
    /// @return TNodeBase pointer to the next node
    /// $Source: vfc_list.hpp $
    /// @return returns pointer to TNodeBase

    //---------------------------------------------------------------------
    TNodeBase* next(const CBaseInfo& f_baseInfo_r) const;

    //---------------------------------------------------------------------
    /// Returns a reference to the next node.
    /// @dspec{1903}     The SW component "vfc" shall set a given start node base
    ///                  instance's next link to a given destination node base instance
    ///                  using the given node base info.
    /// @param f_baseInfo_r CBaseInfo instance used to compute the next node
    /// @param f_next       TNodeBase pointer that shall become the next node
    /// $Source: vfc_list.hpp $
    /// @return returns reference to TNodeBase.

    //---------------------------------------------------------------------
    void setNext(const CBaseInfo& f_baseInfo_r, TNodeBase* f_next);

    //---------------------------------------------------------------------
    /// Returns the previous node.
    /// @dspec{1904}     The SW component "vfc" shall compute the previous node base
    ///                  instance for a given start node base instance using a  given base
    ///                  info instance and return a pointer to the previous node base instance.
    /// @param f_baseInfo_r CBaseInfo instance used to compute the previous node
    /// @return TNodeBase pointer to the previous node
    /// $Source: vfc_list.hpp $
    /// @return return pointer to previous node.

    //---------------------------------------------------------------------
    TNodeBase* prev(const CBaseInfo& f_baseInfo_r) const;

    //---------------------------------------------------------------------
    /// Returns a reference to the previous node.
    /// @dspec{1905}     The SW component "vfc" shall set a given start node base
    ///                  instance's previous link to a given destination node base
    ///                  instance using the given node base info.
    /// @param f_baseInfo_r CBaseInfo instance used to compute the previous node
    /// @param f_prev       TNodeBase pointer that shall become the previous node
    /// $Source: vfc_list.hpp $

    //---------------------------------------------------------------------
    void setPrev(const CBaseInfo& f_baseInfo_r, TNodeBase* f_prev);

    //---------------------------------------------------------------------
    /// Links f_node_p inbetween f_prev_p & f_next_p.
    /// $Source: vfc_list.hpp $
    /// @dspec{1906}     The SW component "vfc" shall insert the first given node base
    ///                  instance between the second and third given node base instances
    ///                  using the given base info instance by changing the respective next
    ///                  and previous links and return the inserted node base instance.
    /// @param f_baseInfo_r CBaseInfo instance used to compute the next/previous nodes
    /// @param  f_node_p    node to be linked between f_prev_p and f_next_p.
    /// @param  f_prev_p    pointer to previous node.
    /// @param  f_next_p    pointer to next node.
    /// @return returns pointer to the TNodeBase class.

    //---------------------------------------------------------------------
    static TNodeBase*
    linkNode(const CBaseInfo& f_baseInfo_r, TNodeBase* f_node_p, TNodeBase* f_prev_p, TNodeBase* f_next_p);

    //---------------------------------------------------------------------
    /// Unlinks a node inbetween f_prev_p & f_next_p
    /// and sets the linked list in the proper order.
    /// $Source: vfc_list.hpp $
    /// @dspec{1907}     The SW component "vfc" shall link the first given node base
    ///                  instance to appear directly before the second given node base
    ///                  instance by changing the respective next and previous links using
    ///                  the given base info instance and return a pointer to the second node
    ///                  base instance.
    /// @param f_baseInfo_r CBaseInfo instance used to compute the next/previous nodes
    /// @param  f_prev_p    pointer to previous node.
    /// @param  f_next_p    pointer to next node.
    /// @return returns pointer to the next node.

    //---------------------------------------------------------------------
    static TNodeBase* unlinkNode(const CBaseInfo& f_baseInfo_r, TNodeBase* f_prev_p, TNodeBase* f_next_p);

    //---------------------------------------------------------------------
    /// Swaps a nodes f_node1_r & f_node2_r.
    /// $Source: vfc_list.hpp $
    /// @dspec{1908}     The SW component "vfc" shall swap the logical positions of two
    ///                  given node base instances such that the next and previous links
    ///                  of both given instances are swapped by using the given base info instance.
    /// @param f_baseInfo_r  CBaseInfo instance used to compute the next/previous nodes
    /// @param  f_node1_r    reference to TNodeBase.
    /// @param  f_node2_r    reference to TNodeBase.

    //---------------------------------------------------------------------
    static void swapNodeBase(const CBaseInfo& f_baseInfo_r, TNodeBase& f_node1_r, TNodeBase& f_node2_r);

    //---------------------------------------------------------------------
    /// sequence from f_first_p to one before f_last_p is inserted before f_pos_p.
    /// $Source: vfc_list.hpp $
    /// @dspec{1909}     The SW component "vfc" shall remove the sequence of linked node
    ///                  base instances defined by the second and third given node base
    ///                  instance from their original sequence and insert the removed sequence
    ///                  at the position given by the first given node base instance by changing
    ///                  the respective next and previous links using the given base info instance.
    /// @param f_baseInfo_r CBaseInfo instance used to compute the next/previous nodes
    /// @param  f_pos_p     position to be inserted.
    /// @param  f_first_p   position of the first element to be relinked.
    /// @param  f_last_p    position just beyond the last element to be relinked.

    //---------------------------------------------------------------------
    static void
    relinkNodeBase(const CBaseInfo& f_baseInfo_r, TNodeBase* f_pos_p, TNodeBase* f_first_p, TNodeBase* f_last_p);

  private:
    //---------------------------------------------------------------------
    /// Declare assignment operator as private.
    /// $Source: vfc_list.hpp $

    //---------------------------------------------------------------------
    TNodeBase& operator=(TNodeBase const& f_rhs);
};

/// CBaseInfo performs pointer/index abstraction for TNodeBase.
/// The overloaded methods getAddressOf() and getLinkTo() handle the pointer/index cases
struct CBaseInfo
{
    /// @dspec{1910}     The SW component "vfc" shall create a new base info instance
    ///                  with a given base pointer and a given number of bytes per item.
    /// @param f_base_p         void pointer to base address
    /// @param f_numItemBytes   number of bytes per item
    inline CBaseInfo(void* f_base_p, uint32_t f_numItemBytes)
        : m_base_p(static_cast<char*>(f_base_p)), m_numItemBytes(f_numItemBytes)
    {
        // intentionally left blank
    }

    /// overload for index case
    /// @dspecref{1911}
    /// @param f_index  index of item to return address of
    /// @return void pointer to memory of item in position f_index
    inline void* getAddressOf(uint32_t f_index) const
    {
        return (
            m_base_p +
            f_index * m_numItemBytes); // PRQA S 3705 # Technical debt L2 id=00c1a8f4e1a5af784a99-ca5a641b7021cc0f //
                                       // PRQA S 2821 # Technical debt L8 id=00e417485e6b6be14e45-7615b5cb0e9a13fa
    }

    /// overload for pointer case
    /// @dspec{1911}     The SW component "vfc" shall compute the effective address
    ///                  from a given base info instance and a given node base instance
    ///                  or a given index and return the computed address.
    /// @param f_node  pointer to TNodeBase<true>
    /// @return void pointer to the memory of the specified item
    static void* getAddressOf(TNodeBase<true>* f_node) { return f_node; }

    /// overload for index case
    /// @dspec{1912}        The SW component "vfc" shall compute a link for a given base
    ///                     info instance and a given node base instance and return the link index.
    /// @param f_node   TNodeBase pointer to generate link to
    /// @return link index to TNodeBase
    inline uint32_t getLinkTo(TNodeBase<false>* f_node) const
    {
        char* const l_char_pointer = static_cast<char*>(
            static_cast<void*>(f_node)); // PRQA S 3103 # Technical debt L2 id=00c136cae58a343c483d-1e357d024ba3e9a2
        return static_cast<uint32_t>(
            (l_char_pointer - m_base_p) /
            m_numItemBytes); // PRQA S 3135 # Technical debt L7 id=00c17c41cf521e7948bc-2cf8f2a295dce8c8 // PRQA S 2668
                             // # Technical debt L5 id=00c182f2835308c44e5e-d4a30039c338670e // PRQA S 3705 # Technical
                             // debt L2 id=00c1a81d19551a354cb5-cfdcc799417396df
    }

    /// overload for pointer case
    /// @dspec{1956}     The SW component "vfc" shall compute a link for a given base
    ///                  info instance and a given node base instance and return the link index.
    /// @param f_node   TNodeBase pointer to generate link to
    /// @return link pointer to TNodeBase
    static TNodeBase<true>* getLinkTo(TNodeBase<true>* f_node) { return f_node; }

    char*    m_base_p;
    uint32_t m_numItemBytes;
};

/// export convenient name for indexed TNodeBase
using CIndexNodeBase = TNodeBase<false>;

/// export convenient name for pointerfull TNodeBase
using CPointerNodeBase = TNodeBase<true>;

} // namespace vfc

#include "vfc/container/vfc_nodebase.inl"

#endif // VFC_NODEBASE_HPP_INCLUDED

