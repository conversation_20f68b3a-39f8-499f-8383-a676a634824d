//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_BITSET_HPP_INCLUDED
#define ZX_VFC_BITSET_HPP_INCLUDED

// vfc/core includes
#include "vfc/core/vfc_config.hpp"
#include "vfc/core/vfc_types.hpp"
#include "vfc/core/vfc_version.hpp"
#include <type_traits> // for std::is_standard_layout etc

///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
namespace vfc
{
namespace intern
{
// Helper to select TBitSet::StorageType.
template <uint8_t N>
struct StorageSelect;
template <>
struct StorageSelect<0>
{
    using Type = uint8_t;
};
template <>
struct StorageSelect<1>
{
    using Type = uint16_t;
};
template <>
struct StorageSelect<2>
{
    using Type = uint32_t;
};
template <>
struct StorageSelect<3>
{
    using Type = uint64_t;
};
} // namespace intern

//=============================================================================
//  TBitSet

/// Provides an array of bits with a fixed size @p SizeValue.
///
/// Similar to an array of bools, but more efficient in space. Provides very efficient AND, OR and XOR operations on
/// large data. Is constexpr-able so data can be used at compile time. Further benefits are assertions upon
/// out-of-range access. The size of the array is always fixed at compile time.
///
/// This array of bits can also be seen as efficient implementation of a mathematical sets of numbers with their
/// union (OR), intersection (AND) or set-difference (XOR). When dealing with large date, note that it is most
/// efficient when the number of contained elements is high but the number range is not too big. For example storing
/// just 5 numbers out of the range of 0 - 2^32 is better done in an explicit list, while the TBitSet would use
/// 512MB. But ~50 numbers out of 0 - 1000 is more compact in a TBitSet.
///
/// Example to convert code from bool-array:
///
///     // old:
///     bool m_numbers[1000];
///     if( m_numbers[x] == true )....
///     // new:
///     TBitSet<1000> m_numbers;
///     if( m_numbers.test(x) )....
///
/// @tparam SizeValue       Size of the set, in number of bits.

/// @ingroup                vfc_group_containers.

template <uint32_t SizeValue>
class TBitSet
{
  public:
    /// Current version of the class.
    using Version = TSemanticVersion<1, 0, 0>;

    static constexpr uint32_t Size = SizeValue;
    static_assert(Size > 0, "Cannot have 0 bits."); // There is a specialization below.
    /// Choosing the right storage type when bitset gets small, to save space.
    static constexpr uint32_t StorageTypeID = (Size > 32U ? 3U : (Size > 16U ? 2U : (Size > 8U ? 1U : 0U)));
    /// Type of storage elements, must be unsigned so that bit shifts work.
    using StorageType = typename intern::StorageSelect<StorageTypeID>::Type;
    /// How many bits are stored in one element (int)?
    static constexpr uint32_t BitsPerElement = sizeof(StorageType) * static_cast<uint32_t>(CHAR_BIT);
    /// Number of elements needed.
    static constexpr uint32_t NumElements = (Size + BitsPerElement - 1U) / BitsPerElement;
    /// Type of the array. The struct allows for assignment.
    struct ArrayType
    {
        StorageType arr[NumElements];
    };

    /// Constructs a bitset with all set to 0.
    /// @dspec{2011}     The SW component "vfc" shall construct a new Bitset
    ///                  instance where all bits are initialized to zero.
    constexpr TBitSet();

    /// Destructor
    ~TBitSet() = default;

    /// Copy-constructor.
    /// @param rhs  TBitSet that serves as the source of the copy
    TBitSet(const TBitSet& rhs);

    /// Constructs from plain array of ints.
    /// @dspec{2012}     The SW component "vfc" shall construct a new Bitset which
    ///                  is initialized from a given array.
    /// @param rhs  TBitSet that serves as the source of the copy
    explicit constexpr TBitSet(const ArrayType& rhs);

    /// Assignment operator.
    /// @param rhs  TBitSet that serves as the source of the assignment
    /// @return     reference to modified bitset
    TBitSet& operator=(const TBitSet& rhs);

    /// The size of the container in bits.
    /// @dspec{2013}     The SW component "vfc" shall return the number of bits in the
    ///                  given Bitset instance.
    /// @return     total number of bits of this TBitSet
    static constexpr uint32_t size();

    /// Reset all bits to 0.
    /// @dspec{2014}     The SW component "vfc" shall set all bits of the given
    ///                  Bitset instance to zero.
    void resetAll();

    /// Reset bit @p pos to 0.
    /// @dspec{2015}     The SW component "vfc" shall set the bit at the given position
    ///                  of the given Bitset instance to zero.
    /// @param pos  index number of position to be reset
    void reset(uint32_t pos);

    /// Set all bits to 1.
    /// @dspec{2016}     The SW component "vfc" shall set all bits of the given Bitset
    ///                  instance to the value one.
    void setAll();

    /// Set bit @p pos to 1.
    /// @dspec{2017}     The SW component "vfc" shall set the bit at the given position
    ///                  of the given Bitset instance to the value one.
    /// @param pos  index number of position to be set
    void set(uint32_t pos);

    /// Set bit @p pos to value @p value (true==1, false==0).
    /// @param pos    index number of position to be set
    /// @param value  value to be set at position
    void set(uint32_t pos, bool value);

    /// Flips all bits.
    /// @dspec{2018}     "The SW component ""vfc"" shall flip all bits of the given Bitset instance.
    ///                  Note: By ""Flip"" we mean the operation that transforms a bit to ""one"" if
    ///                  it was previously ""zero"" and to ""zero"" it it was previously ""one""."
    void flipAll();

    /// Flips bit @pos.
    /// @dspec{2019}     "The SW component ""vfc"" shall flip the bit at the given position of the
    ///                  given Bitset instance.
    ///                  Note: By ""Flip"" we mean the operation that transforms a bit to ""one""
    ///                  if it was previously ""zero"" and to ""zero"" it it was previously ""one""."
    /// @param pos  index number of position to be changed
    void flip(uint32_t pos);

    /// Tests bit @p pos.
    /// @dspec{2020}     The SW component "vfc" shall return the boolean value "true" if the bit
    ///                  at the given position of the given Bitset instance is set to the value one,
    ///                  otherwise it shall return the boolean value "false".
    /// @param pos  index number of position to be tested
    /// @return     value at the specified position (true==1, false==0)
    constexpr bool test(uint32_t pos) const;

    /// Tests bit @p pos at compile time.
    /// @dspecref{2020}
    /// @tparam PosVal  index number of position to be tested
    /// @return         value at the specified position (true==1, false==0)
    template <uint32_t PosVal>
    constexpr bool test() const;

    /// Are all bits set to 1?
    /// @dspec{2021}     The SW component "vfc" shall return the boolean value "true" if all
    ///                  bits in the given Bitset instance are set to the value one, otherwise
    ///                  it shall return the boolean value "false".
    /// @return     true if all bits are set to 1, else false is returned
    bool all() const;

    /// Is any bit set to 1?
    /// @dspec{2022}     The SW component "vfc" shall return the boolean value "true" if there
    ///                  exists a bit in the given Bitset instance which is set to the value one,
    ///                  otherwise it shall return the boolean value "false".
    /// @return     true if any bit is set to 1, else false is returned
    bool any() const;

    /// Are all bits set to 0?
    /// @dspec{2023}     The SW component "vfc" shall return the boolean value "true" if all bits
    ///                  in the given Bitset instance are set to the value zero, otherwise it shall
    ///                  return the boolean value "false".
    /// @return     true if all bits are set to 0, else false is returned
    bool none() const;

    /// Returns the number of elements which are set.
    /// @dspec{2024}     The SW component "vfc" shall compute and return the number of the bits in
    ///                  the given Bitset instance that are set to the value one.
    /// @param[in] isconst  Bool indicating if executed in constexpr context to enable compile-time
    ///                     counting of bits.
    /// @return             The number of bits that are set to 1
    constexpr uint32_t count(bool isconst = false) const;

    /// Combining this bitset with another with AND, OR or XOR, just as you would expect.

    /// @dspec{2026}     The SW component "vfc" shall compute and return a new Bitset instance
    ///                  where each resulting bit is the result of the logical "and" operation
    ///                  of the corresponding bits of two given TBitSet instances.
    /// @param oth  other bitset operand to perform "and" computation with
    void andWith(const TBitSet& oth);
    /// @dspec{2027}     The SW component "vfc" shall compute and return a new Bitset instance
    ///                  where each resulting bit is the result of the logical "or" operation
    ///                  of the corresponding bits of two given TBitSet instances.
    /// @param oth  other bitset operand to perform "or" computation with
    void orWith(const TBitSet& oth);
    /// @dspec{2028}     The SW component "vfc" shall compute and return a new Bitset instance
    ///                  where each resulting bit is the result of the logical "xor" operation
    ///                  of the corresponding bits of two given TBitSet instances.
    /// @param oth  other bitset operand to perform "xor" computation with
    void xorWith(const TBitSet& oth);

    /// Comparing two bitsets.
    /// @param[in] rhs      Right bitset to compare.
    /// @param[in] isconst  Bool indicating if executed in constexpr context to enable compile-time comparison.
    /// @return             True iff bitsets are containing same data, false otherwise.
    constexpr bool isEqual(const TBitSet& rhs, bool isconst = false) const;

    /// Access to the internal storage for serialization purposes.
    /// @return Const-reference to the storage.
    /// @dspec{xx} The TBitSet shall give read-only access to its internal storage.
    constexpr auto getStorage() const -> const ArrayType&;

    /// Upcomming: Iterator, ConstIterator
  private:
    /// The mask for the last storage element.
    static constexpr StorageType maskForLastElement();
    static constexpr StorageType storageElementMask(uint32_t absoluteBitPos);
    static constexpr StorageType storageElementMask(uint32_t absoluteBitPos, bool value);
    StorageType&                 getStorageElement(uint32_t absoluteBitPos);
    constexpr const StorageType& getStorageElementC(uint32_t absoluteBitPos) const;
    /// @dspecref{2024}
    constexpr uint32_t countCTime() const;
    uint32_t           countRuntime() const;
    constexpr bool     isEqualCTime(const TBitSet& rhs) const;
    bool               isEqualRuntime(const TBitSet& rhs) const;

    /// Tests: The underlaying type is trivial:
    static_assert(std::is_trivial<ArrayType>::value, "Not trivial");
    static_assert(std::is_standard_layout<ArrayType>::value, "Not standard layout");

    /// Actual storage, the C-array of ints.
    /// If there are unused bits in the last element, they remain 0 to
    /// simplify bitset comparison and bit counting.
    ArrayType m_storage;
};

/// Comparing two bitsets - operator on namespace level.
/// @param[in] lhs Left bitset to compare.
/// @param[in] rhs Right bitset to compare.
/// @return True iff bitsets are containing same data, false otherwise.
template <uint32_t SizeValue>
bool operator==(const TBitSet<SizeValue>& lhs, const TBitSet<SizeValue>& rhs);

/// Comparing two bitsets - unequal.
/// @param[in] lhs Left bitset to compare.
/// @param[in] rhs Right bitset to compare.
/// @return The invers of the @sa operator==().
template <uint32_t SizeValue>
bool operator!=(const TBitSet<SizeValue>& lhs, const TBitSet<SizeValue>& rhs);

/// Specialization for 0 elements.
/// For API see template above.
template <>
class TBitSet<0>
{
  public:
    static constexpr uint32_t Size = 0;
    static constexpr uint32_t size() { return 0; }

    // qacpp-4212-R3: This method is part of a conceptual API across several classes and not static for consistency.

    void resetAll() {} // PRQA S 4212 # R3

    void setAll() {} // PRQA S 4212 # R3

    void flipAll() {} // PRQA S 4212 # R3

    /// Although zero capacity, we assume an empty set (all 0).
    bool all() const { return false; } // PRQA S 4212 # R3

    bool any() const { return false; } // PRQA S 4212 # R3

    bool none() const { return true; } // PRQA S 4212 # R3

    uint32_t count() const { return 0; } // PRQA S 4212 # R3

    constexpr uint32_t countCTime() const { return 0; } // PRQA S 4212 # R3

    void andWith(const TBitSet&) {} // PRQA S 4212 # R3

    void orWith(const TBitSet&) {} // PRQA S 4212 # R3

    void xorWith(const TBitSet&) {} // PRQA S 4212 # R3
};

} // namespace vfc

#include "vfc/container/vfc_bitset.inl"
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
#endif // ZX_VFC_BITSET_HPP_INCLUDED

