//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_FIXEDLIST_HPP_INCLUDED
#define ZX_VFC_FIXEDLIST_HPP_INCLUDED

#include "vfc/container/vfc_list.hpp"                // used for TList class
#include "vfc/memory/vfc_fixedmempool_allocator.hpp" // used for TFixedMemPoolAllocator Allocator class
#include "vfc/core/vfc_functionattributes.hpp"       // SoftwareQualificationLevel
#include "vfc/core/vfc_namedtype.hpp"                // used for namedtype, SizeType
#include <initializer_list>                          // std::initializer_list<>

namespace vfc
{ // namespace vfc opened

namespace intern
{ // namespace intern opened

template <vfc::int32_t SizeValue>
struct TFixedListMaxsizePolicy
{
    static vfc::int32_t maxSizePolicy();
};

} // namespace intern

/// The list class is a template class of sequence containers that maintain
/// their elements in a linear arrangement and allow efficient insertions and
/// deletions at any location within the sequence. The sequence is stored as a
/// bidirectional linked list of elements, each containing a member of some type Type.
/// @dspec{1837}     The SW component "vfc" shall provide all non-constructing functionality
///                  available for List instances also for Fixed list instances
/// @tparam ValueType       DataType to be stored.
/// @tparam SizeValue       size of the fixedList.
/// @ingroup                vfc_group_containers.
template <class ValueType, vfc::int32_t SizeValue>
class TFixedList : public vfc::TList<
                       ValueType,
                       vfc::TFixedMemPoolAllocator<ValueType, SizeValue>,
                       vfc::intern::TFixedListMaxsizePolicy<SizeValue>>
{
    static_assert(
        (TIsSameType<ValueType, vfc::SizeType>::value == false),
        "vfc::SizeType as template parameter ValueType is not supported.");

  public:
    using fixed_list_type = TFixedList<ValueType, SizeValue>;
    using base_type       = vfc::TList<
        ValueType,
        vfc::TFixedMemPoolAllocator<ValueType, SizeValue>,
        vfc::intern::TFixedListMaxsizePolicy<SizeValue>>;

    /// A type that represents the data type stored in a list.
    using value_type = typename base_type::value_type;
    /// A type that counts the number of elements in a list.
    using size_type = typename base_type::size_type;
    /// node allocate type
    using nodealloc_type = typename base_type::nodealloc_type;
    /// A type that provides a pointer to an element in a list.
    using pointer = typename base_type::pointer;
    /// A type that provides a pointer to a const element in a list.
    using const_pointer = typename base_type::const_pointer;
    /// A type that provides a reference to a element stored in a list for reading
    /// and performing operations.
    using reference = typename base_type::reference;
    /// A type that provides a reference to a const element stored in a list for
    /// reading and performing const operations.
    using const_reference = typename base_type::const_reference;
    /// A type that provides a bidirectional iterator that can read a const element in a list.
    using const_iterator = typename base_type::const_iterator;
    /// A type that provides a bidirectional iterator that can read or modify
    /// any element in a list.
    using iterator = typename base_type::iterator;
    /// A type that provides a bidirectional iterator that can read any const element
    /// in a list.
    using const_reverse_iterator = typename base_type::const_reverse_iterator;
    /// A type that provides a bidirectional iterator that can read or modify an element in
    /// a reversed list.
    using reverse_iterator = typename base_type::reverse_iterator;
    /// Expose the push_back methods from the base class
    using base_type::push_back;
    /// Expose the insert methods from the base class
    using base_type::insert;

    /// @dspec{255}    The SW component "vfc" shall create a new empty Fixed list instance.
    /// Default constructor.
    TFixedList(void);

    ~TFixedList() = default;

    /// @dspecref{255}
    /// Creates the list with a specific allocator.
    /// @param rhs TFixedList that serves as the source of the copy
    explicit TFixedList(const nodealloc_type& f_alloc_r);

    /// @dspecref{255}
    /// Creates a FixedList with default elements.
    /// Constructor fills the list with the specified number
    /// copies of default-constructed element.
    /// @param  f_count     The number of elements to initially create.
    VFC_ATR_DEPRECATED2(
        explicit TFixedList(size_type f_count),
        "Use new constructor with strong type vfc::SizeType as argument");

    /// @dspecref{255}
    /// Creates a FixedList with default elements.
    /// Constructor fills the list with the specified number
    /// copies of default-constructed element.
    /// @param  f_count     The number of elements to initially create.
    explicit TFixedList(vfc::SizeType f_count);

    /// @dspec{257} The SW component "vfc" shall be able to initialize a Fixed list instance with a given
    ///             number of copies of a given item value using a given allocator instance.
    /// Creates a FixedList with specified element value.
    /// Constructor fills the list with the specified number
    /// copies of element with the given value.
    /// @param  f_count     The number of elements to initially create.
    /// @param  f_value_r   value to be filled.
    VFC_ATR_DEPRECATED2(
        TFixedList(size_type f_count, const value_type& f_value_r),
        "Use new constructor with strong type vfc::SizeType as first argument");

    /// @dspecref{257}
    /// Creates a FixedList with specified element value.
    /// Constructor fills the list with the specified number
    /// copies of element with the given value.
    /// @param  f_count     The number of elements to initially create.
    /// @param  f_value_r   value to be filled.
    TFixedList(vfc::SizeType f_count, const value_type& f_value_r);

    /// Creates a TFixedList with specified element value.
    /// Constructor fills the list with the specified number
    /// copies of element with the given value.
    /// @param  f_count     The number of elements to initially create.
    /// @param  f_value_r   value to be filled.
    /// @param  f_alloc_r   node type allocator.
    TFixedList(size_type f_count, const value_type& f_value_r, const nodealloc_type& f_alloc_r);

    /// Constructor taking an `std::initializer_list<>`. This allows for
    /// list initialization of the TFixedList with given elements, such as
    /// `TFixedList<int32_t,5> vec { 3, 5, 7 };`.
    /// @dspec{3010} The SW component "vfc" shall be able to initialize a Fixed list instance
    ///              with the elements of a given initialization list such that both lists
    ///              contain the identical elements in the same order.
    /// @note When using curly-braces initialization (`{..}`) with one or
    /// two values, this constructor is preferred by the compiler. When you
    /// intend to construct using the size, or size and value (see above),
    /// then use round parentheses (`(..)`) instead.
    /// @pre The initializer list may not have more elements than the
    /// capacity CapacityValue of the list.
    /// @param f_list The proxy object `std::initializer_list<>`, possibly
    /// implicitly generated by the compiler.
    explicit TFixedList(std::initializer_list<ValueType> f_list);

    /// Copy constructor.
    /// @dspec{256}     The SW component "vfc" shall be able to initialize a new Fixed list
    ///                 instance from another given Fixed list instance of its own type.
    /// @param  f_rhs_r  A list with identical element type, size value and allocator type.
    TFixedList(const fixed_list_type& f_rhs_r);

    /// Builds a list from the given range.
    /// Create a list consisting of copies of the elements in the other list.
    /// @param  f_first  Position of the first element in the range of elements to be copied.
    /// @param  f_last   Position of the first element beyond the range of elements to be copied.
    template <class InIteratorType>
    TFixedList(InIteratorType f_first, InIteratorType f_last);

    /// Builds a list from the given range.
    /// Create a list consisting of copies of the elements in the other list.
    /// @dspec{258}     The SW component "vfc" shall be able to initialize a Fixed list instance
    ///                 with a given begin iterator, a given end iterator and a given allocator
    ///                 instance.
    /// @param  f_first  Position of the first element in the range of elements to be copied.
    /// @param  f_last   Position of the first element beyond the range of elements to be copied.
    /// @param  f_alloc_r   node type allocator.
    template <class InIteratorType>
    TFixedList(InIteratorType f_first, InIteratorType f_last, const nodealloc_type& f_alloc_r);

    /// assignment operator.
    /// @dspec{1514}     The SW component "vfc" shall be able to assign the items of a given
    ///                  Fixed list instance from the item of another given Fixed list instance
    ///                  of its own type.
    /// @param  f_rhs_r  A list with identical element type, size value and allocator type.
    TFixedList& operator=(const TFixedList& f_rhs_r);

    /// @dspec{3003}    The SW component "vfc" will append a given item at the end of the fixed list instance
    ///                 and call the given error function if and only if the operation could not be completed.
    /// @details        The function creates an element at the end of the fixed list and
    ///                 assigns the given data to it. If the fixed list is full, the list is not modified
    ///                 and the error_handler functor is called to deal with that situation.
    /// @tparam         ErrorHandlerType    Type of error handler functor.
    /// @param[in]      f_value             The element added to the end of the list.
    /// @param[in]      error_handler       Error handler functor.
    template <class ErrorHandlerType>
    void push_back(const value_type& f_value, ErrorHandlerType error_handler);

    /// @dspec{3004}    The SW component "vfc" will append a given item at the end of the fixed list instance.
    ///                 The function shall return the bool true if the append succeeded,
    ///                 or it shall return the bool false otherwise.
    /// @details        If the fixed list is not full, the function creates an element at the end of the
    ///                 fixed list, assigns the given data to it, and returns true to signal success.
    ///                 If the fixed list is full, the list is not modified and the function returns false
    ///                 to signal failure.
    /// @param[in]      f_value             The element added to the end of the list.
    /// @return         true if the push_back is successful, false otherwise.
    bool push_back_checked(const value_type& f_value);

    /// @dspec{3005}    The SW component "vfc" will insert the given item into the fixed list instance before the
    /// specified position
    ///                 and call the given error function if and only if the operation could not be completed.
    /// @details        The function creates an element before the specified position of the fixed list and
    ///                 assigns the given data to it. If the fixed list is full, insert() calls
    ///                 the error_handler functor to deal with that situation.
    /// @tparam         ErrorHandlerType    Type of error handler functor.
    /// @param[in]      f_pos               The position that will become the next element to the inserted element.
    /// @param[in]      f_value_r           The value of the element being inserted into the list.
    /// @param[in]      error_handler       Error handler functor.
    /// @return         Returns an iterator that points to the position where the new element was inserted.
    template <class ErrorHandlerType>
    iterator insert(iterator f_pos, const value_type& f_value, ErrorHandlerType error_handler);
};

} // namespace vfc

#include "vfc/container/vfc_fixedlist.inl"

#endif // ZX_VFC_FIXEDLIST_HPP_INCLUDED

