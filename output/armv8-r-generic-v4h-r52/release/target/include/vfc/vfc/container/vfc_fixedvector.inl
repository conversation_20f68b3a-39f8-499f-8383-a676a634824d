//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_FIXEDVECTOR_INL_INCLUDED
#define ZX_VFC_FIXEDVECTOR_INL_INCLUDED

#include "vfc/core/vfc_type_traits.hpp"                            //
#include "vfc/core/vfc_math.hpp"                                   // used for notNegative, numeric_limits
#include "vfc/core/vfc_algorithm.hpp"                              // used for fill_n(), contiguous_fill_n(), copy_n
#include "vfc/variation_point/vfc_curly_braces_initialization.hpp" // Flux variation about std::initializer_list

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::TFixedVector() : m_memory(), m_usedElements(0)
{
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::TFixedVector(size_type f_n)
    : TFixedVector(vfc::SizeType(f_n))
{
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::TFixedVector(vfc::SizeType f_n)
    : m_memory(), m_usedElements(0)
{
    VFC_REQUIRE((f_n.get() <= MAX_SIZE) && (0 <= f_n.get()));

    m_usedElements = static_cast<counter_t>(f_n.get());
    construct(
        begin(),
        begin() + f_n.get(),
        hasTrivialCTor_t()); // PRQA S 3705 # Technical debt L2 id=00c10edd20cf5e004d7d-244d34db3242edea

    VFC_ENSURE2(f_n.get() == static_cast<vfc::SizeType::type>(m_usedElements), "m_usedElements is not correctly set!");
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::TFixedVector(
    size_type        f_n,
    const ValueType& f_default)
    : TFixedVector(vfc::SizeType(f_n), f_default)
{
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::TFixedVector(
    vfc::SizeType    f_n,
    const ValueType& f_default)
    : m_memory(), m_usedElements(0)
{
    VFC_REQUIRE((f_n.get() <= MAX_SIZE) && (0 <= f_n.get()));

    m_usedElements = static_cast<counter_t>(f_n.get());
    copy_construct(
        begin(),
        begin() + f_n.get(),
        f_default); // PRQA S 3705 # Technical debt L2 id=00c11ff65c9a82584fc3-521223ba293a5320

    VFC_ENSURE2(f_n.get() == static_cast<vfc::SizeType::type>(m_usedElements), "m_usedElements is not correctly set!");
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::TFixedVector(const TFixedVector& f_rhs)
    : m_usedElements(0) // PRQA S 4050 # Technical debt L7 id=00c125d19c4745db4473-7b451940d269e86f
{
    this->operator=(f_rhs);

    VFC_ENSURE2(size() == f_rhs.size(), "incorrect size in copy!");
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::TFixedVector(
    std::initializer_list<ValueType>
        list) // PRQA S 2180 # Technical debt L2 id=00c1f91e618f0a834798-c0ba5b4392c953a0 // PRQA S 2008 # Technical
              // debt L2 id=00c1d7ceaea631904942-c6dce8c7e0eaeb7c
    : m_usedElements{0} // PRQA S 4050 # Technical debt L7 id=00c174b876c86bb84e49-22bc0b1ad6dccae4
{
    // Until a clear statement of qualification of the std::initializer_list<> is available, this constructor is tagged
    // as a QM-level function:
    vfc::attributes::SoftwareQualification::Qm();

    // In order to use this constructor, you have to explicitly opt-in at the moment. The problem is the ambiguity with
    // other constructors when using curly braces initialization with just one or two elements. There is a Flux
    // variation deciding this, defaulting to 'disabled'. In that case, using this constructor will break compilation. A
    // fix of eventual curly-braces initialization with the intention of using the constructors taking the size and
    // possibly value is easy: use round parentheses instead. Once fixed, the ambiguity is resolved and one can enable
    // the usage of this constructor safely.
    static_assert(
        vfc::config::isCurlyBracesInitializationActive && sizeof(ValueType),
        "Constructor taking std::initializer_list<> used but disabled. Please refer to comment in file.");

    VFC_REQUIRE2(
        list.size() <= static_cast<size_t>(capacity()), "Initializer-list may not be longer than our capacity.");
    for (const auto& elem : list)
    {
        push_back(elem);
    }
    VFC_ENSURE2(static_cast<size_t>(size()) == list.size(), "Vector size should now match initializer list.");
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::~TFixedVector() VFC_NOEXCEPT
{
    destruct(begin(), end(), hasTrivialDTor_t());
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>&
vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::operator=(const TFixedVector& f_rhs)
{
    if (this != &f_rhs)
    {
        clear();
        for (const_iterator l_iter = f_rhs.begin(); l_iter != f_rhs.end();
             ++l_iter) // PRQA S 4687 # Technical debt L2 id=00c19f0de3d482f04819-68a3653c77208a62
        {
            push_back(*l_iter);
        }
    }

    VFC_ENSURE2((size() == f_rhs.size()), "incorrect size after assignment!");

    return *this;
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::iterator
vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::begin()
{
    return intern::make_checkable_iterator(
        m_memory.typed_begin(), *this); // Warning qacpp-3085 - if warning is triggered here check calling sites //
                                        // Warning qacpp-3090  - if warning is triggered here check calling sites
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::const_iterator
vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::begin() const
{
    return intern::make_checkable_iterator(
        m_memory.typed_begin(), *this); // Warning qacpp-3085 - if warning is triggered here check calling sites //
                                        // Warning qacpp-3090  - if warning is triggered here check calling sites
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::iterator
vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::end()
{
    return intern::make_checkable_iterator(
        m_memory.typed_begin() + m_usedElements,
        *this); // Warning qacpp-3085 - if warning is triggered here check calling sites // Warning qacpp-3090  - if
                // warning is triggered here check calling sites // PRQA S 3705 # Technical debt L2
                // id=00c1afffc5104be64a72-c340599bea599c82 // PRQA S 2821 # Technical debt L8
                // id=01588e694ba03a764d66-8f10cf243fd7460a // PRQA S 2932 # Technical debt L5
                // id=0158bb436845015b4dfd-ed8c2068556437b5
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::const_iterator
vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::end() const
{
    return intern::make_checkable_iterator(
        m_memory.typed_begin() + m_usedElements,
        *this); // Warning qacpp-3085 - if warning is triggered here check calling sites // Warning qacpp-3090  - if
                // warning is triggered here check calling sites // PRQA S 3705 # Technical debt L2
                // id=00c117d5c1f1f12641dd-c340599bea599c82 // PRQA S 2821 # Technical debt L8
                // id=01586511697d298f4ec8-8f10cf243fd7460a
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::reverse_iterator
vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::rbegin()
{
    return static_cast<reverse_iterator>(end());
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::const_reverse_iterator
vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::rbegin() const
{
    return static_cast<const_reverse_iterator>(end());
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::reverse_iterator
vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::rend()
{
    return static_cast<reverse_iterator>(begin());
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::const_reverse_iterator
vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::rend() const
{
    return static_cast<const_reverse_iterator>(begin());
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline auto vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::operator[](size_type idx)
    -> reference // PRQA S 3800 # Technical debt L4 id=00c12f597e0f79614ad8-9f4fec3b69d0b59a
{
    VFC_REQUIRE(notNegative(idx) && (idx < size()));
    return (m_memory.typed_begin()[idx]);
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
template <typename IndexType>
inline auto vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::operator[](IndexType idx) -> reference
{
    // static_assert(
    //     numeric_limits<IndexType>::is_specialized, "numeric_limits for integer IndexType must be specialized");
    // static_assert(TIsIntegral<IndexType>::value, "IndexType must be an integer type");
    // static_assert(
    //     (CapacityValue - 1) <= numeric_limits<IndexType>::max(), "IndexType must cover all possible index values");
    VFC_REQUIRE(notNegative(idx) && (idx < static_cast<IndexType>(size())));

    return (m_memory.typed_begin()[idx]);
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline auto vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::operator[](size_type idx) const
    -> const_reference // PRQA S 3800 # Technical debt L4 id=00c1c70b4c048aed499a-bd5238f3d7aac50c
{
    VFC_REQUIRE(notNegative(idx) && (idx < size()));

    return (m_memory.typed_begin()[idx]);
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
template <typename IndexType>
inline auto vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::operator[](IndexType idx) const
    -> const_reference
{
    // static_assert(
    //     numeric_limits<IndexType>::is_specialized, "numeric_limits for integer IndexType must be specialized");
    // static_assert(TIsIntegral<IndexType>::value, "IndexType must be an integer type");
    // static_assert(
    //     (CapacityValue - 1) <= numeric_limits<IndexType>::max(), "IndexType must cover all possible index values");
    VFC_REQUIRE(notNegative(idx) && (idx < static_cast<IndexType>(size())));

    return (m_memory.typed_begin()[idx]);
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::reference
vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::front()
{
    VFC_REQUIRE(!empty());
    return *begin(); // PRQA S 2811 # Technical debt L8 id=015873d7fd5921454a78-da0023aadd9ced54
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::const_reference
vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::front() const
{
    VFC_REQUIRE(!empty());
    return *begin(); // PRQA S 2811 # Technical debt L8 id=01580002b0ba34a74a7d-da0023aadd9ced54
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::reference
vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::back()
{
    VFC_REQUIRE(!empty());
    return *(end() - 1); // PRQA S 3705 # Technical debt L2 id=00c192437f669e294315-c621416d0af0ab36
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::const_reference
vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::back() const
{
    VFC_REQUIRE(!empty());
    return *(end() - 1); // PRQA S 3705 # Technical debt L2 id=00c179c134c50d7340db-c621416d0af0ab36
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::size_type
vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::size() const
{
    return static_cast<size_type>(m_usedElements);
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline bool vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::empty() const
{
    return (0 == m_usedElements);
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::size_type
vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::capacity()
    const // PRQA S 4212 # Technical debt L2 id=00c1d0c4356724cb4a5f-0b63583b7021c798
{
    return MAX_SIZE;
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::size_type
vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::max_size()
    const // PRQA S 4212 # Technical debt L2 id=00c136c6274ee4ed4ac4-4851f8ddd4cff643
{
    return MAX_SIZE;
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline void vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::push_back(const ValueType& f_elem)
{
    VFC_REQUIRE(
        true ==
        capacity_check(m_usedElements + 1)); // PRQA S 3000 # Technical debt L2 id=00c1c385bbd9a2cf40f8-dfc196127558059a

    ValueType* placementPos =
        m_memory.typed_begin() +
        m_usedElements; // PRQA S 3705 # Technical debt L2 id=00c1fb5cf2217fc947b1-9214388d30487d3b

    new (placementPos) ValueType(f_elem);
    ++m_usedElements;
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
template <class ErrorHandlerType>
inline void vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::push_back(
    const ValueType& f_elem,
    ErrorHandlerType error_handler)
{
    // If the fixed vector is full, then call the error handler functor
    if (!capacity_check(m_usedElements + 1)) // PRQA S 3000 # Technical debt L2 id=00c16e8bb92907bb41c6-38ccee75fd1db79f
    {
        error_handler();
        return;
    }
    push_back(f_elem);
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline bool vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::push_back_checked(const ValueType& f_elem)
{
    // If the fixed vector is full, then return false
    if (!capacity_check(m_usedElements + 1)) // PRQA S 3000 # Technical debt L2 id=00c1c80c40f65a5c490d-38ccee75fd1db79f
    {
        return false;
    }
    push_back(f_elem);
    return true;
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline void vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::pop_back()
{
    VFC_REQUIRE(!empty());
    back().~ValueType(); // PRQA S 3803 # Technical debt L2 id=00c1330296759ad44256-ddc1fee9de20dd61
    --m_usedElements;
}

// qacpp-2008-R3: Size of argument unknown in template context.
template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::iterator
vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::erase(
    typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::iterator f_it) // PRQA S 2008 # R3
{
    VFC_REQUIRE(!empty());
    VFC_REQUIRE((f_it < end()) && (begin() <= f_it));

    const_iterator const next_it = f_it + 1; // PRQA S 3705 # Technical debt L2 id=00c15537251873d243ca-5dd93ad39fd1b4a8

    // if last element is erased, don't move around anything, otherwise
    if (next_it != end())
    {
        // copy remaining elements after erased one "one place to the left"
        vfc::copy_n(
            next_it, f_it, end() - next_it); // PRQA S 3705 # Technical debt L2 id=00c1b32a9cc7e89648d5-e48db991a118f3a5
    }
    pop_back();  // destroy last element and adjust size
    return f_it; // return iterator to the element that took over in place of the erased one.
}

// qacpp-2008-R3: Size of argument unknown in template context.
// qacpp-2009-R2: Size of argument unknown in template context.
template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::iterator
vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::erase( // PRQA S 2755 # Technical debt L6
                                                                   // id=00c1ab7ebb0c688243ab-fa39e1e1277d7189
    typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::iterator f_first, // PRQA S 2008 # R3
    typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::iterator f_last)  // PRQA S 2009 # R2
{
    VFC_REQUIRE((f_last <= end()) && (begin() <= f_first) && (f_first <= f_last));
    // by transitivity we also know begin() <= f_last and f_first <= end() (and begin() <= end())

    if (f_first != f_last)
    {
        const typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::difference_type num_elements_erased =
            f_last - f_first; // PRQA S 3705 # Technical debt L2 id=00c10a29158bac004415-44b18e52f0787d75

        const typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::difference_type num_elements_copied =
            end() - f_last; // PRQA S 3705 # Technical debt L2 id=00c1d9c282d347564cc7-83a01857a1e332c0

        // num_elements_erased is a type of difference_type, is signed and can hold maximum 64 bits
        // m_usedElements is a type of counter_t, unsigned and can hold maximum 32 bits
        // Converting to int64 does not result in an overflow
        VFC_REQUIRE2(
            static_cast<vfc::int64_t>(m_usedElements) >= static_cast<vfc::int64_t>(num_elements_erased),
            "Tries to delete more items than the container has available!");

        if (0 != num_elements_copied)
        {
            vfc::copy_n(f_last, f_first, num_elements_copied);
        }

        const iterator first_to_destruct =
            end() - num_elements_erased; // PRQA S 3705 # Technical debt L2 id=00c1389eca600bd54ac7-0c582ac58c2ebfa6
        destruct(first_to_destruct, end(), hasTrivialDTor_t());
        // Make sure, following cast succeeds.
        // If ever somebody used a larger type than vfc::int32_t for CapacityValue,
        // m_usedElements would need to be changed accordingly and the cast here as well.
        static_assert(sizeof(vfc::int32_t) >= sizeof(m_usedElements), "Wrong type used for 'm_usedElements'!");
        // The difference_type may be a 64bit type but since we require that
        // both iterators must be from this vector their difference is bounded by
        // CapacityValue.
        m_usedElements -= static_cast<counter_t>(
            num_elements_erased); // PRQA S 3010 # Technical debt L2 id=00c1579e994cce2542b4-8ca5f5b4ba3c8391
    }
    return f_first;
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline void vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::clear()
{
    // destruct the previous used elements
    destruct(begin(), end(), hasTrivialDTor_t());
    m_usedElements = static_cast<counter_t>(0);

    VFC_ENSURE2(0 == m_usedElements, "number of element is not zero after clear()!");
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline void vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::resize(size_type f_newsize)
{
    VFC_REQUIRE((f_newsize <= MAX_SIZE) && (0 <= f_newsize));
    const size_type oldSize = static_cast<size_type>(m_usedElements);

    if (oldSize == f_newsize)
        return; // bail out, nothing to do // PRQA S 4060 # Technical debt L7 id=00c1970b63d32b724b05-1ae75beae7bd3ed6

    if (f_newsize < oldSize)
    {
        shrink(f_newsize);
    }
    else
    {
        // construct the new elements
        m_usedElements = static_cast<counter_t>(f_newsize);
        construct(
            begin() + oldSize,
            begin() + f_newsize,
            hasTrivialCTor_t()); // PRQA S 3705 # Technical debt L2 id=00c1dd5bc9a90ce946b7-106991b3e842b8d2
    }

    VFC_ENSURE2(f_newsize == static_cast<size_type>(m_usedElements), "the vectors number of elements is not correct!");
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline void
vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::resize(size_type f_newsize, const ValueType& f_default)
{
    VFC_REQUIRE((f_newsize <= MAX_SIZE) && (0 <= f_newsize));
    const size_type oldSize = static_cast<size_type>(m_usedElements);

    if (oldSize == f_newsize)
        return; // bail out, nothing to do // PRQA S 4060 # Technical debt L7 id=00c12c8785a47e644e62-1ae75beae7bd3ed6

    if (f_newsize < oldSize)
    {
        shrink(f_newsize);
    }
    else
    {
        // construct the new elements
        m_usedElements = static_cast<counter_t>(f_newsize);
        copy_construct(
            begin() + oldSize,
            begin() + f_newsize,
            f_default); // PRQA S 3705 # Technical debt L2 id=00c1f929c95144254593-838363cb081c5ad0
    }

    VFC_ENSURE2(f_newsize == static_cast<size_type>(m_usedElements), "the vectors number of elements is not correct!");
}

//=============================================================================
//  vfc::set(const ValueType& f_value)
//-----------------------------------------------------------------------------
/// @par Description:
/// erase all the elements in the vector and copies the specified
/// element in the empty vector
/// incase of objects calls it's destructor.

/// @ingroup vfc_group_containers
/// @par Requirements:
/// - vfc_fixedvector.hpp
//=============================================================================
template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline void vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::set(const ValueType& f_value)
{
    set(f_value, typename TIf<vfc::TIsPOD<ValueType>::value, true_t, false_t>::type());
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline void vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::set(const ValueType& f_value, true_t)
{
    vfc::contiguous_fill_n(begin(), static_cast<uint32_t>(m_usedElements), f_value);
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline void vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::set(const ValueType& f_value, false_t)
{
    vfc::fill_n(begin(), m_usedElements, f_value);
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline typename vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::iterator
vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::insert_uninitialized_back()
{
    VFC_REQUIRE(
        true ==
        capacity_check(m_usedElements + 1)); // PRQA S 3000 # Technical debt L2 id=00c1a8f837c1668c438f-dfc196127558059a
    // do nothing, allocation is long done, construction must take place outside after this call
    ++m_usedElements;
    return begin() + (m_usedElements - 1); // PRQA S 3705 # Technical debt L2 id=00c17e7bdfbece854ad8-099cebee86affcc8
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline void vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::shrink(size_type f_newsize)
{
    const size_type oldSize = static_cast<size_type>(m_usedElements);
    VFC_REQUIRE(f_newsize < oldSize);

    // destruct the previous used elements
    destruct(
        begin() + f_newsize, // PRQA S 3705 # Technical debt L2 id=00c10367412c174349fa-72b494863391ebae
        begin() + oldSize,
        hasTrivialDTor_t()); // PRQA S 3705 # Technical debt L2 id=00c155f99d31c8a94583-11026571fc841d63

    m_usedElements = static_cast<counter_t>(f_newsize);
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline void vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::reserve(
    size_type f_reserved_size) // PRQA S 4212 # Technical debt L2 id=00c1cc7ad12832ef497d-6c80ce7021b010bc
{
    // Since we own our memory from construction on, just check that the request does not exceed it.
    // No need to do anything else.
    VFC_REQUIRE((0 <= f_reserved_size) && (f_reserved_size <= MAX_SIZE));
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline ValueType* vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::c_array()
{
    return m_memory.typed_begin();
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline const ValueType* vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::c_array() const
{
    return m_memory.typed_begin();
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline bool vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::capacity_check(size_type f_numElements)
{
    if ((0 <= f_numElements) && (f_numElements <= MAX_SIZE))
    {
        return true;
    }
    else
    {
        return false;
    }
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline void vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::construct(iterator, iterator, true_t)
{
}

// qacpp-2009-R2: Size of argument unknown in template context.
template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline void vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::construct(
    iterator f_start, // PRQA S 2009 # R2
    iterator f_end,   // PRQA S 2009 # R2
    false_t)
{
    VFC_REQUIRE(f_start <= f_end);
    for (iterator iter = f_start; iter != f_end; ++iter)
    {
        ValueType* elemPtr = &*iter;
        // Placement-new, ignore return value. Note it's not "ValueType()".
        static_cast<void>(new (elemPtr) ValueType);
    }
}

// qacpp-2009-R2: Size of argument unknown in template context.
template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline void vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::copy_construct(
    iterator         f_start, // PRQA S 2009 # R2
    iterator         f_end,   // PRQA S 2009 # R2
    const ValueType& elem)
{
    VFC_REQUIRE(f_start <= f_end);
    for (iterator iter = f_start; iter != f_end; ++iter)
    {
        ValueType* elemPtr = &*iter;
        // Placement-new, ignore return value.
        static_cast<void>(new (elemPtr) ValueType(elem));
    }
}

template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline void vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::destruct(iterator, iterator, true_t)
{
    // nothing
}

// qacpp-2009-R2: Size of argument unknown in template context.
template <class ValueType, vfc::int32_t CapacityValue, class AllocatorType>
inline void vfc::TFixedVector<ValueType, CapacityValue, AllocatorType>::destruct(
    iterator f_start, // PRQA S 2009 # R2
    iterator f_end,   // PRQA S 2009 # R2
    false_t)
{
    VFC_ASSERT(f_start <= f_end);
    for (iterator iter = f_start; iter != f_end; ++iter)
    {
        iter->~ValueType(); // PRQA S 3803 # Technical debt L2 id=00c1160dfe3b70be4e55-ceb160ae6b1fecaa // PRQA S 3804 #
                            // Technical debt L2 id=00c12bdfd8eb81bc4d73-f47f8447aa104496
    }
}

#endif // ZX_VFC_FIXEDVECTOR_INL_INCLUDED

