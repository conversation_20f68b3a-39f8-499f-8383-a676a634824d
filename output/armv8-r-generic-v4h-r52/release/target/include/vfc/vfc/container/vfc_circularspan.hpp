//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_CIRCULAR_SPAN_HPP_INCLUDED
#define VFC_CIRCULAR_SPAN_HPP_INCLUDED

#include "vfc/core/vfc_iterator.hpp"                 // reverse_iterator
#include "vfc/container/vfc_span.hpp"                // for TSpan
#include "vfc/container/vfc_fixedcircularbuffer.hpp" // for TFixedCircularBuffer

namespace vfc
{
//=============================================================================
//  TCircularSpan<>
//-----------------------------------------------------------------------------
/// TCircularSpan provides a view to 0 to n instances that live in contiguous memory.
/// It is useful as an interface class because it does not introduce a non-type
/// template parameter and can therefore work with different statically sized
/// containers including TCircularBuffer.
///
/// @par Description:
///   A template class representing a with to typed object instances in memoty.
/// @tparam ValueType       DataType to be accessed (not stored!)

/// @ingroup                vfc_group_containers
//=============================================================================
template <typename ValueType>
class TCircularSpan
{
  public:
    /// A type that represent the data type accessed by a TCircularSpan
    using value_type = ValueType;

    /// A type that provides a iterator for TCircularSpan which is used for traversing
    /// the items.
    using iterator = CIterator<TCircularSpan>;

    /// A type that provides a const iterator for TCircularSpan which is used for traversing
    /// the items.
    using const_iterator = CConstIterator<TCircularSpan>;

    /// A type that provides a iterator for TCircularSpan which is used for traversing
    /// the items.
    using reverse_iterator = vfc::reverse_iterator<iterator>;

    /// A type that provides a const iterator for TCircularSpan which is used for traversing
    /// the items.
    using const_reverse_iterator = vfc::reverse_iterator<const_iterator>;

    /// A type that provides a reference to a item stored in a TCircularSpan for reading
    /// and performing operations.
    using reference = value_type&;

    /// A type that provides a reference to a const item stored in a TCircularSpan for
    /// reading and performing const operations.
    using const_reference = const value_type&;

    /// A type that provides reference to volatile item.
    using volatile_reference =
        volatile value_type&; // PRQA S 5219 # Technical debt L2 id=00c163d113414a6443da-59a2a0547829b7c2

    /// A type that provides const reference to volatile item.
    using const_volatile_reference =
        const volatile value_type&; // PRQA S 5219 # Technical debt L2 id=00c15d9885988a344399-368a07650806fefd

    /// A type that counts the number of items in a TCircularSpan.
    using size_type = vfc::int32_t;

    //======================================================
    // constructors
    //======================================================
    /// Default constructor, takes no arguments, construct empty span
    /// @dspec{202}     The SW component "vfc" shall create a new empty circular
    ///                 span instance.
    TCircularSpan();

    ~TCircularSpan() = default;

    /// Copy constructor.
    /// @dspec{204}     The SW component "vfc" shall create a new circular span
    ///                 instance from another given circular span instance of its
    ///                 own type.
    /// @param f_other TCircularSpan that serves as the source of the copy
    TCircularSpan(const TCircularSpan<value_type>& f_other);

    /// Assignment operator.
    /// @dspec{209}     The SW component "vfc" shall be able to provide the possibility
    ///                 to assign the items of a given circular span instance to another
    ///                 given circular span instance.
    /// @param f_other TCircularSpan that serves as the source of the assignment
    /// @return     reference to modified circular span
    TCircularSpan& operator=(const TCircularSpan<value_type>& f_other);

    /// Return a circularspan to the same data treated as const
    /// @dspec{1887}     The SW component "vfc" shall return a circular span instance
    ///                  to the same data as a given circular span instance where the
    ///                  resulting circular span will only provide read-only access to the data.
    /// @return returns new TCircularSpan instance to same const data
    TCircularSpan<typename vfc::TAddConst<value_type>::type> as_const() const;

    /// constructor method from a pointer and a size.
    /// Not implemented as constructor and given an ugly name to make reviews
    /// easier.
    /// @dspec{205}     The SW component "vfc" shall create and return a new circular
    ///                 span instance from a given pointer to an item and a given size number.
    /// @param f_begin_p1  pointer to the begin of the first view
    /// @param f_size1     size of first view in number of items, not bytes
    /// @return TCircularSpan instance representing a view to the given range
    /// @pre f_size1 must be non-negative
    /// @pre If f_begin_p1 is a nullptr then f_size1 must be 0
    static TCircularSpan from_pointer_and_size(value_type* f_begin_p1, size_type f_size1);

    /// constructor method from a pointer and a size.
    /// Not implemented as constructor and given an ugly name to make reviews
    /// easier.
    /// @dspec{206}     The SW component "vfc" shall create and return a new circular
    ///                 span instance from two given pointers to two items and two given
    ///                 size numbers.
    /// @param f_begin_p1  pointer to the begin of the first view
    /// @param f_size1     size of first view in number of items, not bytes
    /// @param f_begin_p2  pointer to the begin of the second view
    /// @param f_size2     size of second view in number of items, not bytes
    /// @return TCircularSpan instance representing a view to the given range
    /// @pre f_size1 and f_size2 must both be non-negative
    /// @pre f_size1 is 0 then f_size2 must also be 0
    /// @pre If f_begin_p1 is a nullptr then f_size1 must be 0
    /// @pre If f_begin_p2 is a nullptr then f_size2 must be 0
    static TCircularSpan
    from_pointer_and_size(value_type* f_begin_p1, size_type f_size1, value_type* f_begin_p2, size_type f_size2);

    //---------------------------------------------------------------------
    /// Returns iterator to the first stored item.
    /// Attention: If the TCircularSpan is empty this will be the same as end() and
    /// may not be dereferenced.
    ///
    /// @dspec{210}     The SW component "vfc" shall return an iterator to the first
    ///                 item of the given circular span instance.
    /// @return returns iterator
    //---------------------------------------------------------------------
    iterator begin();

    //---------------------------------------------------------------------
    /// Returns const_iterator to the first stored item.
    /// Attention: If the TCircularSpan is empty this will be the same as end() and
    /// may not be dereferenced.
    ///
    /// @dspecref{210}
    /// @return returns const iterator
    //---------------------------------------------------------------------
    const_iterator begin() const;

    //---------------------------------------------------------------------
    /// Returns const_iterator to the first stored item.
    /// Attention: If the TCircularSpan is empty this will be the same as cend() and
    /// may not be dereferenced.
    ///
    /// @dspecref{210}
    /// @return returns const iterator
    //---------------------------------------------------------------------
    const_iterator cbegin() const;

    //---------------------------------------------------------------------
    /// Returns iterator pointing after the last stored item.
    /// Attention: This iterator must never be dereferenced.
    ///
    /// @dspec{211}     The SW component "vfc" shall return an iterator after the
    ///                 last item of the given circular span instance.
    /// @return returns iterator
    //---------------------------------------------------------------------
    iterator end();

    //---------------------------------------------------------------------
    /// Returns const_iterator pointing after the last stored item.
    /// Attention: This iterator must never be dereferenced.
    ///
    /// @dspecref{211}
    /// @return returns const_iterator
    //---------------------------------------------------------------------
    const_iterator end() const;

    //---------------------------------------------------------------------
    /// Returns const_iterator pointing after the last stored item.
    /// Attention: This iterator must never be dereferenced.
    ///
    /// @dspecref{211}
    /// @return returns const_iterator
    //---------------------------------------------------------------------
    const_iterator cend() const;

    //---------------------------------------------------------------------
    /// Returns reverse_iterator pointing to the last item in the view.
    /// Attention: If the TCircularSpan is empty this will be the same as rend() and
    /// may not be dereferenced.
    ///
    /// @dspec{212}     The SW component "vfc" shall return an reverse iterator to the
    ///                 last item of the given circular span instance.
    /// @return returns reverse_iterator
    //---------------------------------------------------------------------
    reverse_iterator rbegin();

    //---------------------------------------------------------------------
    /// Returns const_reverse_iterator pointing to the last item in the view.
    /// Attention: If the TCircularSpan is empty this will be the same as rend() and
    /// may not be dereferenced.
    ///
    /// @dspecref{212}
    /// @return returns const_reverse_iterator to last item
    //---------------------------------------------------------------------
    const_reverse_iterator rbegin() const;

    //---------------------------------------------------------------------
    /// Returns const_reverse_iterator pointing to the last item in the view.
    /// Attention: If the TCircularSpan is empty this will be the same as crend() and
    /// may not be dereferenced.
    ///
    /// @dspecref{212}
    /// @return returns const_reverse_iterator to last item
    //---------------------------------------------------------------------
    const_reverse_iterator crbegin() const;

    //---------------------------------------------------------------------
    /// Returns reverse_iterator pointing after the first item in the view.
    /// Attention: This iterator must never be dereferenced.
    /// @dspec{213}     The SW component "vfc" shall return an reverse iterator to the
    ///                 position before the first item of the given circular span instance.
    /// @return returns reverse_iterator behind the first item
    //---------------------------------------------------------------------
    reverse_iterator rend();

    //---------------------------------------------------------------------
    /// Returns const_reverse_iterator pointing after the first item in the
    /// view.
    /// Attention: This iterator must never be dereferenced.
    /// @dspecref{213}
    /// @return returns const_reverse_iterator behind the first item
    //---------------------------------------------------------------------
    const_reverse_iterator rend() const;

    //---------------------------------------------------------------------
    /// Returns const_reverse_iterator pointing after the first item in the
    /// view.
    /// Attention: This iterator must never be dereferenced.
    /// @dspecref{213}
    /// @return returns const_reverse_iterator behind the first item
    //---------------------------------------------------------------------
    const_reverse_iterator crend() const;

    //=============================================================================
    /// Reference to the oldest element in the TCircularSpan.
    /// @dspec{1522}     The SW component "vfc" shall return a reference to the first
    ///                  item in a given circular span instance.
    /// @return  Returns reference to oldest element.
    //=============================================================================
    reference front();

    //=============================================================================
    /// Const reference to the oldest element in the TCircularSpan.
    /// @dspecref{1522}
    /// @return  Returns const reference to oldest element.
    //=============================================================================
    const_reference front() const;

    //=============================================================================
    /// reference to the last added element.
    /// @dspec{1523}     The SW component "vfc" shall return a reference to the last
    ///                  item in a given circular span instance.
    /// @return  reference to the newly added element.
    //=============================================================================
    reference back();

    //=============================================================================
    /// Const reference to the last added element.
    /// @dspecref{1523}
    /// @return  Const reference to the newly added element.
    //=============================================================================
    const_reference back() const;

    //---------------------------------------------------------------------
    /// Returns a reference to the item at position f_pos in TCircularSpan.
    /// It is not legal to call this operator when the TCircularSpan is empty.
    /// @dspec{208}     The SW component "vfc" shall return a reference to an item at
    ///                 a given, valid index of a given circular span instance.
    /// \pre
    ///   0 <= f_pos and f_pos < size()
    /// @param f_pos    position number of item
    /// @return returns reference to item at specified position
    //---------------------------------------------------------------------
    reference operator[](size_type f_pos);

    //---------------------------------------------------------------------
    /// Returns a volatile reference to the item at position f_pos in TCircularSpan.
    /// It is not legal to call this operator when the TCircularSpan is empty.
    /// @dspecref{208}
    /// \pre
    ///   0 <= f_pos and f_pos < size()
    /// @param f_pos    position number of item
    /// @return returns volatile reference to item at specified position
    //---------------------------------------------------------------------
    volatile_reference
    operator[](size_type f_pos) volatile; // PRQA S 5219 # Technical debt L2 id=00c18a0b96028e3c4698-443511b655846a53

    //---------------------------------------------------------------------
    /// Returns a const reference to the item at position f_pos in TCircularSpan.
    /// It is not legal to call this operator when the TCircularSpan is empty.
    /// @dspecref{208}
    /// \pre
    ///   0 <= f_pos and f_pos < size()
    /// @param f_pos    position number of item
    /// @return returns const reference to item at specified position
    //---------------------------------------------------------------------
    const_reference operator[](size_type f_pos) const;

    //---------------------------------------------------------------------
    /// Returns a const volatile reference to the item at position f_pos in
    /// TCircularSpan.
    /// It is not legal to call this operator when the TCircularSpan is empty.
    /// @dspecref{208}
    /// \pre
    ///   0 <= f_pos and f_pos < size()
    /// @param f_pos    position number of item
    /// @return returns const volatile reference to item at specified position
    //---------------------------------------------------------------------
    const_volatile_reference operator[](size_type f_pos) const
        volatile; // PRQA S 5219 # Technical debt L2 id=00c1a4c40f5eda7e43a6-9cfe0f5e18977b4b

    //---------------------------------------------------------------------
    /// Returns the number of items the array can hold
    /// @dspec{215}     The SW component "vfc" shall return the current size of
    ///                 the given circular span instance.
    /// @return returns size of array in items, not bytes
    //---------------------------------------------------------------------
    size_type size() const;

    //---------------------------------------------------------------------
    /// Checks whether the circularspan is empty.
    /// @dspec{214}     The SW component "vfc" shall return the boolean value
    ///                 "true" if the given circular span instance is empty otherwise
    ///                 it shall return the boolean value "false".
    /// @return returns true iff no items are in this view
    //---------------------------------------------------------------------
    bool empty() const;

  private:
    /// private constructor from pointers
    TCircularSpan(value_type* f_begin, value_type* f_end);
    TCircularSpan(value_type* f_begin1, value_type* f_end1, value_type* f_begin2, value_type* f_end2);

    /// Constructor that explicitly assings values to m_span1 and m_span2. Deleted as we do not want to expose it.
    TCircularSpan(const TSpan<value_type>& span1, const TSpan<value_type>& span2) = delete;

    TSpan<value_type> m_span1; ///< first span for broken memory block
    TSpan<value_type> m_span2; ///< second span for broken memory block

    template <typename ValueTypeForAsSpan, vfc::int32_t CapacityValueForAsSpan, typename AllocatorTypeForAsSpan>
    friend vfc::TCircularSpan<ValueTypeForAsSpan>
    vfc::as_span(vfc::TFixedCircularBuffer<ValueTypeForAsSpan, CapacityValueForAsSpan, AllocatorTypeForAsSpan>&
                     f_circularBuffer); // PRQA S 2107 # Technical debt L2 id=00c1e613e25ea0dd4078-26691b937ad03c94

    template <typename ValueTypeForAsSpan, vfc::int32_t CapacityValueForAsSpan, typename AllocatorTypeForAsSpan>
    friend vfc::TCircularSpan<const ValueTypeForAsSpan>
    vfc::as_span(const vfc::TFixedCircularBuffer<ValueTypeForAsSpan, CapacityValueForAsSpan, AllocatorTypeForAsSpan>&
                     f_circularBuffer); // PRQA S 2107 # Technical debt L2 id=00c10c2a13f981254021-deeb6752f20d7851

    friend vfc::TCircularSpan<typename vfc::TRemoveConst<ValueType>::type>; // PRQA S 2107 # Technical debt L2
                                                                            // id=00c19bcb6d03f0d94806-8132e17b39807c18
};

/// conversion methods

/// Construct TCircularSpan from TCircularBuffer instance, taking dynamic size.
/// This will construct a circularspan that views the current size of the
/// vector.
/// Attention: Size changes of the vector after the creation of the
///  circularspan will not be reflected in the size of the circularspan.
///  If the size of the vector is reduced, the circularspan will point to
///  invalid (e.g. erased or cleared memory)
/// @dspec{207}     The SW component "vfc" shall be able to initialize a new circular
///                 span instance from a given Fixed circular buffer instance and return
///                 this new circular span.
/// @tparam ValueType        DataType to be accessed
/// @tparam CapacityValue    Capacity of TFixedCircularBuffer
/// @tparam AllocatorType    Allocator type used by TFixedCircularBuffer
/// @param f_circularBuffer  TCircularBuffer owning the data items
/// @return                  circularspan representing the currently allocated item of the vector
template <typename ValueType, vfc::int32_t CapacityValue, class AllocatorType>
TCircularSpan<ValueType> as_span(vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_circularBuffer);

/// Construct const TCircularSpan from const TCircularBuffer instance, taking dynamic size.
/// This will construct a const TCircularSpan that views the current size of the
/// vector.
/// Attention: Size changes of the vector after the creation of the
///  circularspan will not be reflected in the size of the circularspan.
///  If the size of the vector is reduced, the circularspan will point to
///  invalid (e.g. erased or cleared memory)
/// @tparam ValueType        DataType to be accessed
/// @tparam CapacityValue    Capacity of TFixedCircularBuffer
/// @tparam AllocatorType    Allocator type used by TFixedCircularBuffer
/// @param f_circularBuffer  TCircularBuffer owning the data items
/// @return                   const circularspan representing the currently allocated item of the vector
template <typename ValueType, vfc::int32_t CapacityValue, class AllocatorType>
TCircularSpan<const ValueType>
as_span(const vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_circularBuffer);
} // namespace vfc

#include "vfc/container/vfc_circularspan.inl"

#endif // VFC_CIRCULAR_SPAN_HPP_INCLUDED

