//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef ZX_VFC_FIXEDLIST_INL_INCLUDED
#define ZX_VFC_FIXEDLIST_INL_INCLUDED

#include "vfc/variation_point/vfc_curly_braces_initialization.hpp" // Flux variation about std::initializer_list

// maxSizePolicy Definition
template <vfc::int32_t SizeValue>
inline vfc::int32_t vfc::intern::TFixedListMaxsizePolicy<SizeValue>::maxSizePolicy()
{
    return SizeValue;
}

// TFixedList

template <class ValueType, vfc::int32_t SizeValue>
vfc::TFixedList<ValueType, SizeValue>::TFixedList() // PRQA S 2628 # Technical debt L2
                                                    // id=00c1a26b86e8d6b946a9-83df6816ae6226b0
    : TList<
          ValueType,
          vfc::TFixedMemPoolAllocator<ValueType, SizeValue>,
          vfc::intern::TFixedListMaxsizePolicy<SizeValue>>()
{
    // intentionally left blank because it's implemented in tlist
}

template <class ValueType, vfc::int32_t SizeValue>
vfc::TFixedList<ValueType, SizeValue>::TFixedList(const nodealloc_type& f_alloc_r)
    : TList<
          ValueType,
          vfc::TFixedMemPoolAllocator<ValueType, SizeValue>,
          vfc::intern::TFixedListMaxsizePolicy<SizeValue>>(f_alloc_r)
{
    // intentionally left blank because it's implemented in tlist
}

template <class ValueType, vfc::int32_t SizeValue>
vfc::TFixedList<ValueType, SizeValue>::TFixedList(size_type f_count)
    : TList<
          ValueType,
          vfc::TFixedMemPoolAllocator<ValueType, SizeValue>,
          vfc::intern::TFixedListMaxsizePolicy<SizeValue>>(f_count)
{
    // intentionally left blank because it's implemented in tlist
}

template <class ValueType, vfc::int32_t SizeValue>
vfc::TFixedList<ValueType, SizeValue>::TFixedList(vfc::SizeType f_count)
    : TList<
          ValueType,
          vfc::TFixedMemPoolAllocator<ValueType, SizeValue>,
          vfc::intern::TFixedListMaxsizePolicy<SizeValue>>(f_count)
{
    // intentionally left blank because it's implemented in tlist
}

template <class ValueType, vfc::int32_t SizeValue>
vfc::TFixedList<ValueType, SizeValue>::TFixedList( // PRQA S 3800 # Technical debt L4
                                                   // id=00c1bb77d9d28d704b6b-cc663399b68a54cc
    size_type         f_count,
    const value_type& f_value_r)
    : TList<
          ValueType,
          vfc::TFixedMemPoolAllocator<ValueType, SizeValue>,
          vfc::intern::TFixedListMaxsizePolicy<SizeValue>>(f_count, f_value_r)
{
    // intentionally left blank because it's implemented in tlist
}

template <class ValueType, vfc::int32_t SizeValue>
vfc::TFixedList<ValueType, SizeValue>::TFixedList(vfc::SizeType f_count, const value_type& f_value_r)
    : TList<
          ValueType,
          vfc::TFixedMemPoolAllocator<ValueType, SizeValue>,
          vfc::intern::TFixedListMaxsizePolicy<SizeValue>>(f_count, f_value_r)
{
    // intentionally left blank because it's implemented in tlist
}

template <class ValueType, vfc::int32_t SizeValue>
vfc::TFixedList<ValueType, SizeValue>::TFixedList( // PRQA S 3800 # Technical debt L4
                                                   // id=00c1eb4d6ff071dc42dc-cc663399b68a54cc
    size_type             f_count,
    const value_type&     f_value_r,
    const nodealloc_type& f_alloc_r)
    : TList<
          ValueType,
          vfc::TFixedMemPoolAllocator<ValueType, SizeValue>,
          vfc::intern::TFixedListMaxsizePolicy<SizeValue>>(f_count, f_value_r, f_alloc_r)
{
    // intentionally left blank because it's implemented in tlist
}

// qacpp-2008-R3: Size of argument unknown in template context.
template <class ValueType, vfc::int32_t SizeValue>
vfc::TFixedList<ValueType, SizeValue>::TFixedList(std::initializer_list<ValueType> f_list) // PRQA S 2008 # R3
    : TList<
          ValueType,
          vfc::TFixedMemPoolAllocator<ValueType, SizeValue>,
          vfc::intern::TFixedListMaxsizePolicy<SizeValue>>()
{
    // Until a clear statement of qualification of the std::initializer_list<> is available, this constructor is tagged
    // as a QM-level function:
    vfc::attributes::SoftwareQualification::Qm();

    // In order to use this constructor, you have to explicitly opt-in at the moment. The problem is the ambiguity with
    // other constructors when using curly braces initialization with just one or two elements. There is a Flux
    // variation deciding this, defaulting to 'disabled'. In that case, using this constructor will break compilation. A
    // fix of eventual curly-braces initialization with the intention of using the constructors taking the size and
    // possibly value is easy: use round parentheses instead. Once fixed, the ambiguity is resolved and one can enable
    // the usage of this constructor safely.
    static_assert(
        vfc::config::isCurlyBracesInitializationActive && sizeof(ValueType),
        "Constructor taking std::initializer_list<> used but disabled. Please refer to comment in file.");

    VFC_REQUIRE2(
        static_cast<vfc::int32_t>(f_list.size()) <= SizeValue, "Initializer-list may not be longer than our capacity.");
    for (const auto& elem : f_list)
    {
        push_back(elem);
    }
    VFC_ENSURE2(
        static_cast<std::size_t>(this->size()) == f_list.size(), "List size should now match initializer list.");
}

template <class ValueType, vfc::int32_t SizeValue>
vfc::TFixedList<ValueType, SizeValue>::TFixedList(
    const fixed_list_type& f_rhs_r) // PRQA S 2634 # Technical debt L7 id=00c1d698af516fea41ac-d585e68ef8975a1a
    : TList<
          ValueType,
          vfc::TFixedMemPoolAllocator<ValueType, SizeValue>,
          vfc::intern::TFixedListMaxsizePolicy<SizeValue>>(f_rhs_r)
{
    // intentionally left blank because it's implemented in tlist
}

template <class ValueType, vfc::int32_t SizeValue>
template <class InIteratorType>
vfc::TFixedList<ValueType, SizeValue>::TFixedList(
    InIteratorType f_first,
    InIteratorType f_last) // PRQA S 2009 # Technical debt L2 id=00c17839a5b6b4fd44e3-ee0c530730ef8520
    : TList<
          ValueType,
          vfc::TFixedMemPoolAllocator<ValueType, SizeValue>,
          vfc::intern::TFixedListMaxsizePolicy<SizeValue>>(f_first, f_last)
{
    // intentionally left blank because it's implemented in tlist
}

template <class ValueType, vfc::int32_t SizeValue>
template <class InIteratorType>
vfc::TFixedList<ValueType, SizeValue>::TFixedList(
    InIteratorType        f_first,
    InIteratorType        f_last, // PRQA S 2009 # Technical debt L2 id=00c135529305e6da4d0a-58ad4324bfffb544
    const nodealloc_type& f_alloc_r)
    : TList<
          ValueType,
          vfc::TFixedMemPoolAllocator<ValueType, SizeValue>,
          vfc::intern::TFixedListMaxsizePolicy<SizeValue>>(f_first, f_last, f_alloc_r)
{
    // intentionally left blank because it's implemented in tlist
}

template <class ValueType, vfc::int32_t SizeValue>
inline vfc::TFixedList<ValueType, SizeValue>&
vfc::TFixedList<ValueType, SizeValue>::operator=(const TFixedList& f_rhs_r)
{
    if (this != &f_rhs_r)
    {
        base_type::operator=(f_rhs_r);
    }
    return *this;
}

template <class ValueType, vfc::int32_t SizeValue>
template <class ErrorHandlerType>
inline void vfc::TFixedList<ValueType, SizeValue>::push_back(const value_type& f_value, ErrorHandlerType error_handler)
{
    // If the list is full, then call the error handler functor
    if (base_type::max_size() <= this->size())
    {
        error_handler();
        return;
    }
    push_back(f_value);
}

template <class ValueType, vfc::int32_t SizeValue>
inline bool vfc::TFixedList<ValueType, SizeValue>::push_back_checked(const value_type& f_value)
{
    // If the list is full, then return false
    if (base_type::max_size() <= this->size())
    {
        return false;
    }
    push_back(f_value);
    return true;
}

template <class ValueType, vfc::int32_t SizeValue>
template <class ErrorHandlerType>
inline typename vfc::TFixedList<ValueType, SizeValue>::iterator vfc::TFixedList<ValueType, SizeValue>::insert(
    iterator          f_pos,
    const value_type& f_value,
    ErrorHandlerType  error_handler) // PRQA S 2009 # Technical debt L2 id=00c1d3fda024f5754e84-c1a23ac0607a135c
{
    // If the list is full, then call the error handler functor
    if (base_type::max_size() <= this->size())
    {
        error_handler();
        return this->end();
    }
    return insert(f_pos, f_value);
}
#endif // ZX_VFC_FIXEDLIST_INL_INCLUDED

