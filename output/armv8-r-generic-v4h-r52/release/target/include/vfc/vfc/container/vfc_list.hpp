//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_LIST_HPP_INCLUDED
#define VFC_LIST_HPP_INCLUDED

// stdlib includes
#include <algorithm> //used std::reverse

// vfc/core includes
#include "vfc/core/vfc_types.hpp"
#include "vfc/core/vfc_type_traits.hpp" // used for hasTrivialD/Ctor_t
#include "vfc/core/vfc_metaprog.hpp"    // used for TInt2Boolean
#include "vfc/core/vfc_static_assert.hpp"
#include "vfc/core/vfc_iterator.hpp"
#include "vfc/core/vfc_builtin_types.hpp"
#include "vfc/core/vfc_namedtype.hpp" // used for namedtype, SizeType

// vfc/memory includes
#include "vfc/memory/vfc_freestore_allocator.hpp" // TFreeScaleAllocator

// vfc/container includes
#include "vfc/container/vfc_nodebase.hpp" // used for TNodeBase

namespace vfc
{ // namespace vfc opened

// Forward declaration.
template <class ValueType, int32_t SizeValue>
class TFixedMemPoolAllocator;

namespace intern
{ // namespace intern opened

struct CListMaxsizePolicy
{
    static vfc::int32_t maxSizePolicy();
};

} // namespace intern

//=============================================================================
//  TList<>
//-----------------------------------------------------------------------------
/// Linked List class.
/// The list class is a template class of sequence containers that maintain
/// their elements in a linear arrangement and allow efficient insertions and
/// deletions at any location within the sequence. The sequence is stored as a
/// bidirectional linked list of elements, each containing a member of some type
/// ValueType.
/// @note The most notable use is as a base class of `TFixedList`, which uses `TFixedMemPoolAllocator` instead of
/// `TFreeStoreAllocator`. As this is using integers instead of pointers for 'links', `UsePointer` will be `false`
/// and the operations of `TList` change drastically. As a result, `m_head` will not be used and empty (nevertheless
/// taking same space). The helper `CBaseInfo` calculates pointers given the index, provided by `TNodeBase<false>`.
/// The head (provided by `getHead()`) needs to be stored in the allocator instead of `m_head`, which only works
/// with the `TFixedMemPoolAllocator`. The access is actually behind `max_size()`, thus the allocator has to provide
/// more memory than he reports.
/// @tparam ValueType           DataType to be stored.
/// @tparam AllocatorType       AllocatorType to store the values.
/// @tparam MaxSizePolicyType   PolicyType specifying behavior of max_size

/// @ingroup                vfc_group_containers
//=============================================================================
template <
    class ValueType,
    class AllocatorType     = vfc::TFreeStoreAllocator<ValueType>,
    class MaxSizePolicyType = intern::CListMaxsizePolicy>
class TList
{
  public:
    // typdefes
    using list_type = TList<ValueType, AllocatorType, MaxSizePolicyType>;
    /// A type that represents the data type stored in a list.
    using value_type = ValueType;
    /// A type that counts the number of elements in a list.
    using size_type = vfc::int32_t;
    /// A type that provides the difference between two iterators that refer to
    /// elements within the same list.
    using difference_type = typename AllocatorType::difference_type;
    /// A type that represents the allocator class for a list object.
    using allocator_type = AllocatorType;

    using iterator_category = vfc::bidirectional_iterator_tag;
    using hasTrivialCTor_t  = typename TInt2Boolean<THasTrivialCTor<value_type>::value>::type;
    using hasTrivialDTor_t  = typename TInt2Boolean<THasTrivialDTor<value_type>::value>::type;

  private:
    // AllocatorType defines whether pointers or indexes are used.
    enum
    {
        UsePointer = TIsPointer<typename AllocatorType::link_type>::value
    };
    using UsePointer_t = typename TIf<UsePointer, true_t, false_t>::type;
    using CNodeBase    = TNodeBase<UsePointer>;

    //=============================================================================
    //  CNode
    //-----------------------------------------------------------------------------
    /// CNode class is used for storing the value added to the list.
    /// $Source: vfc_list.hpp $

    /// @ingroup                vfc_group_containers
    //=============================================================================
    class CNode : public CNodeBase
    {
      public:
        using value_type = ValueType;

        //---------------------------------------------------------------------
        /// Constructor takes value_type parameter.
        /// @param f_value  value to initialize the node value with
        /// $Source: vfc_list.hpp $

        //---------------------------------------------------------------------
        explicit CNode(const value_type& f_value) : CNodeBase(), m_value(f_value) {}

        //---------------------------------------------------------------------
        /// Returns the reference of the contained value.
        /// $Source: vfc_list.hpp $
        /// @return returns reference of the internally stored value.

        //---------------------------------------------------------------------
        inline value_type& value()
        {
            return m_value;
        } // PRQA S 4024 # Technical debt L2 id=00c1b383822b030145f6-faf4f3aae2db0a07

        //---------------------------------------------------------------------
        /// Returns the constant reference of the contained value.
        /// $Source: vfc_list.hpp $
        /// @return returns const reference of the internally stored value.

        //---------------------------------------------------------------------
        inline const value_type& value() const { return m_value; }

      private:
        value_type m_value; ///< Stores the value the node
    };

  public:
    using node_type     = CNode;
    using position_type = CNodeBase;

  protected:
    // The AllocatorType has also a seperate template argument ValueType,
    // which may differ to the ValueType of TList.
    // But the rebind changes the allocator to manage CNode<ValueType>, where
    // ValueType is defined by the TList's ValueType
    using nodealloc_type = typename allocator_type::template rebind<node_type>::other;
    ///////////////////////////////////////////////////////////////////////////////////////////////
    // iterators
    ///////////////////////////////////////////////////////////////////////////////////////////////

    //=============================================================================
    //  CListConstIterator
    //-----------------------------------------------------------------------------
    /// CListConstIterator provides a bidirectional iterator that
    /// can read a const element in a list.
    /// $Source: vfc_list.hpp $

    /// @ingroup        vfc_group_containers
    //=============================================================================
  private:
    class CListConstIterator // PRQA S 2609 # Technical debt L7 id=00c18080da4f5b4c43cf-f42f59ef9a09d75e // PRQA S 2610
                             // # Technical debt L7 id=00c11944880e0d5241db-1764c6cb423eaca0
    {
      public:
        using self_type         = CListConstIterator;
        using difference_type   = ptrdiff_t;
        using iterator_category = vfc::bidirectional_iterator_tag;
        using value_type        = ValueType;
        using pointer           = const ValueType*;
        using reference         = const ValueType&;

        /// Default c'tor.
        CListConstIterator() : m_base_p(), m_node_p(nullptr) {}

        /// Microsoft visual studio compilerbug leads to stack corruption during reverse() call. Therefore
        /// this d'tor is disabled for the microsoft visual studio build.
#ifndef VFC_MSVC
        /// Default d'tor.
        ~CListConstIterator() = default;
#endif

        /// Copy constructor.
        /// @param f_iter_r  CListConstIterator that serves as the source of the copy
        CListConstIterator(const CListConstIterator&
                               f_iter_r) // PRQA S 2634 # Technical debt L7 id=00c1ba86827ce6494517-172726840448385b
            : m_base_p(f_iter_r.m_base_p), m_node_p(f_iter_r.m_node_p)
        {
            // intentionally left blank
        }

        /// Constructor taking position type
        /// @param f_base   node_type pointer to the base address
        /// @param f_pos_p  index relative to base address
        CListConstIterator(node_type* f_base, position_type* f_pos_p) : m_base_p(f_base), m_node_p(f_pos_p)
        {
            // intentionally left blank
        }

        /// Assignment operator.
        /// @param f_iter_r  CListConstIterator that serves as the source of the assignment
        CListConstIterator& operator=(const CListConstIterator& f_iter_r);

        /// Returns a const reference to the object.
        /// @return reference to externally stored value
        reference operator*() const;

        /// Returns a const pointer to the object.
        /// @return pointer to externally stored value
        pointer operator->() const;

        /// Prefix increment iterator.
        /// @return an iterator to the incremented position
        self_type& operator++();

        /// Postfix increment iterator.
        /// @return an iterator to the previous, non-incremented position
        self_type operator++(postfix_operator_tag);

        /// Prefix decrement iterator.
        /// @return an iterator to the decremented position
        self_type& operator--();

        /// Postfix decrement iterator.
        /// @return an iterator to the previous, non-decremented position
        self_type operator--(postfix_operator_tag);

        /// Equality comparison operator.
        /// @param f_rhs_r  CListConstIterator on right hand side of comparision
        /// @return true if both this iterator and the given iterator point to the
        ///         same position otherwise return false
        bool operator==(
            const self_type& f_rhs_r) const; // PRQA S 2066 # Technical debt L4 id=00c149eb91174a4e4550-48faeeee36d5101d

        /// Inequality comparison operator.
        /// @param f_rhs_r  CListConstIterator on right hand side of comparision
        /// @return true if this iterator and the given iterator point to
        ///         different positions otherwise return false
        bool operator!=(
            const self_type& f_rhs_r) const; // PRQA S 2066 # Technical debt L4 id=00c1531b26a397ca4819-a0a9893d8974bf03

      private:
        /// Helper function to assert that the iterator is currently not the 'end' interator.
        void checkIteratorPointsToValidNode() const;

      protected:
        node_type*     m_base_p; ///< base pointer
        position_type* m_node_p; /// Current node pointer
        friend class TList;      /// TList class can access private variables // PRQA S 2107 # Technical debt L2
                                 /// id=00c1229c6f824f57452d-1502c2d002832841
    };

    //=======================================================================
    //  CListIterator
    //-----------------------------------------------------------------------
    /// A type that provides a bidirectional iterator that can
    /// read or modify any element in a list.
    /// $Source: vfc_list.hpp $

    /// @ingroup        vfc_group_containers
    //=======================================================================
    class CListIterator : public CListConstIterator
    {
      public:
        using base_type         = CListConstIterator;
        using self_type         = CListIterator;
        using difference_type   = ptrdiff_t;
        using iterator_category = vfc::bidirectional_iterator_tag;
        using value_type        = ValueType;
        using pointer           = ValueType*;
        using reference         = ValueType&;

        /// Default constructor
        CListIterator() = default;

        ~CListIterator() = default;

        /// Constructor taking position type
        /// @param f_base   node_type pointer to the base address
        /// @param f_pos_p  index relative to base address
        CListIterator(node_type* f_base, position_type* f_pos_p) : base_type(f_base, f_pos_p)
        {
            // intentionally left blank
        }

        /// copy constructor
        /// @param f_iter_r  CListIterator that serves as the source of the copy
        CListIterator(const CListIterator& f_iter_r);

        /// Returns a reference to the object.
        /// @return reference to externally stored value
        reference operator*() const;

        /// Returns a pointer to the object.
        /// @return pointer to externally stored value
        pointer operator->() const;

        /// Prefix increment iterator
        /// @return an iterator to the incremented position
        self_type& operator++();

        /// Post increment iterator.
        /// @return an iterator to the previous, non-incremented position
        self_type operator++(postfix_operator_tag);

        /// Prefix decrement iterator
        /// @return an iterator to the decremented position
        self_type& operator--();

        /// Postfix decrement iterator.
        /// @return an iterator to the previous, non-decremented position
        self_type operator--(postfix_operator_tag);

        /// Assignment operator
        /// @param f_iter_r  CListIterator that serves as the source of the assignment
        CListIterator& operator=(const CListIterator& f_iter_r);

      private:
        explicit CListIterator(const base_type& f_iter_r);

        CListIterator(nodealloc_type& f_alloc, position_type* f_pos_p) : base_type(f_alloc, f_pos_p)
        {
            // intentionally left blank
        }

        friend class TList; /// TList class can access private members // PRQA S 2107 # Technical debt L2
                            /// id=00c1d7b1f170f7974507-1502c2d002832841
    };

  public:
    /// A type that provides a pointer to an element in a list.
    using pointer = typename allocator_type::pointer;
    /// A type that provides a pointer to a const element in a list.
    using const_pointer = typename allocator_type::const_pointer;
    /// A type that provides a reference to a element stored in a list for reading
    /// and performing operations.
    using reference = typename allocator_type::reference;
    /// A type that provides a reference to a const element stored in a list for
    /// reading and performing const operations.
    using const_reference = typename allocator_type::const_reference;
    /// A type that provides a bidirectional iterator that can read a const element in a list.
    using const_iterator = CListConstIterator;
    /// A type that provides a bidirectional iterator that can read or modify
    /// any element in a list.
    using iterator = CListIterator;
    /// A type that provides a bidirectional iterator that can read any const element
    /// in a list.
    using const_reverse_iterator = vfc::reverse_iterator<const_iterator>;
    /// A type that provides a bidirectional iterator that can read or modify an element in
    /// a reversed list.
    using reverse_iterator = vfc::reverse_iterator<iterator>;

  public:
    //---------------------------------------------------------------------
    /// Default constructor.
    /// $Source: vfc_list.hpp $

    /// @dspec{283}     The SW component "vfc" shall create a new empty List instance.
    //---------------------------------------------------------------------
    TList(void);

    //---------------------------------------------------------------------
    /// Creates the list with a specific allocator.
    /// @dspec{1748}     The SW component "vfc" shall create a new empty List instance
    ///                  with a given nodealloc instance.
    /// @param  f_alloc_r   node type allocator instance
    /// $Source: vfc_list.hpp $

    //---------------------------------------------------------------------
    explicit TList(const nodealloc_type& f_alloc_r);

    //---------------------------------------------------------------------
    /// Creates a list with default elements.
    /// Constructor fills the list with the specified number
    /// copies of default-constructed element.
    /// $Source: vfc_list.hpp $
    /// @param  f_count     The number of elements to initially create.

    //---------------------------------------------------------------------
    VFC_ATR_DEPRECATED2(
        explicit TList(size_type f_count),
        "Use new constructor with strong type vfc::SizeType as argument");

    //---------------------------------------------------------------------
    /// Creates a list with default elements.
    /// Constructor fills the list with the specified number
    /// copies of default-constructed element.
    /// $Source: vfc_list.hpp $
    /// @dspec{1764}     The SW component "vfc" shall create a new List instance containing
    ///                  a given number of default constructed items.
    /// @param  f_count     The number of elements to initially create.

    //---------------------------------------------------------------------
    explicit TList(vfc::SizeType f_count);

    //-----------------------------------------------------------------------------
    /// Creates a list with specified element value.
    /// Constructor fills the list with the specified number
    /// copies of element with the given value.
    /// $Source: vfc_list.hpp $
    /// @param  f_count     The number of elements to initially create.
    /// @param  f_value_r   value to be filled.

    //=============================================================================
    VFC_ATR_DEPRECATED2(
        TList(size_type f_count, const value_type& f_value_r),
        "Use new constructor with strong type vfc::SizeType as first argument");

    //-----------------------------------------------------------------------------
    /// Creates a list with specified element value.
    /// Constructor fills the list with the specified number
    /// copies of element with the given value.
    /// $Source: vfc_list.hpp $
    /// @dspec{1765}     The SW component "vfc" shall create a new List instance containing
    ///                  a given number of copies of a given item.
    /// @param  f_count     The number of elements to initially create.
    /// @param  f_value_r   value to be filled.

    //=============================================================================
    TList(vfc::SizeType f_count, const value_type& f_value_r);

    //-----------------------------------------------------------------------------
    /// Creates a list with specified element value.
    /// Constructor fills the list with the specified number
    /// copies of element with the given value.
    /// $Source: vfc_list.hpp $
    /// @dspec{1766}     The SW component "vfc" shall create a new List instance containing
    ///                  a given number of copies of a given item using a given nodealloc instance.
    /// @param  f_count     The number of elements to initially create.
    /// @param  f_value_r   value to be filled.
    /// @param  f_alloc_r   node type allocator.

    //=============================================================================
    TList(size_type f_count, const value_type& f_value_r, const nodealloc_type& f_alloc_r);

    //-----------------------------------------------------------------------------
    /// Copy constructor.
    /// $Source: vfc_list.hpp $
    /// @dspec{287}     The SW component "vfc" shall be able to initialize as a List instance
    ///                 from another given List instance of its own type.
    /// @param  f_rhs_r  A list with identical element type, size value and allocator type.

    //=============================================================================
    TList(const list_type& f_rhs_r);

    //-----------------------------------------------------------------------------
    /// Builds a list from the given range.
    /// Create a list consisting of copies of the elements in the other list.
    /// $Source: vfc_list.hpp $
    /// @dspec{1767}     The SW component "vfc" shall create a new List instance that
    ///                  contains the copies of the items in the sequence specified by two
    ///                  given iterators.
    /// @param  f_first  Position of the first element in the range of elements to be copied.
    /// @param  f_last   Position of the first element beyond the range of elements to be copied.

    //=============================================================================
    template <class InIteratorType>
    TList(InIteratorType f_first, InIteratorType f_last);

    //-----------------------------------------------------------------------------
    /// Builds a list from the given range.
    /// Create a list consisting of copies of the elements in the other list.
    /// $Source: vfc_list.hpp $
    /// @dspec{1768}     The SW component "vfc" shall create a new List instance using the
    ///                  given nodealloc instance that contains the copies of the items in the
    ///                  sequence specified by two given iterators.
    /// @param  f_first  Position of the first element in the range of elements to be copied.
    /// @param  f_last   Position of the first element beyond the range of elements to be copied.
    /// @param  f_alloc_r   node type allocator.

    //=============================================================================
    template <class InIteratorType>
    TList(InIteratorType f_first, InIteratorType f_last, const nodealloc_type& f_alloc_r);

    //-----------------------------------------------------------------------------
    /// Assignment operator.
    /// $Source: vfc_list.hpp $
    /// @dspec{288}     The SW component "vfc" shall be able to provide the possibility to
    ///                 assign the items a given List instance to another given List instance.
    /// @param  f_rhs_r  A list with identical element type, size value and allocator type.
    /// @return reference to this instance after the assignment

    //=============================================================================
    list_type& operator=(const list_type& f_rhs_r);

    //-----------------------------------------------------------------------------
    /// Destructor.
    /// $Source: vfc_list.hpp $

    //=============================================================================
    ~TList() VFC_NOEXCEPT;

    ///////////////////////////////////////////////////////////////////////////////////////////////
    // iterator funcs
    ///////////////////////////////////////////////////////////////////////////////////////////////

    //-----------------------------------------------------------------------------
    /// Returns an iterator addressing the first element in a list.
    /// $Source: vfc_list.hpp $
    /// @dspec{261}     The SW component "vfc" shall return an iterator to the first item
    ///                 of the given List instance.
    /// @return Returns a bidirectional iterator addressing the first element in the list
    ///         or to the location succeeding an empty list.

    //=============================================================================
    iterator begin();

    //-----------------------------------------------------------------------------
    /// Returns a const iterator addressing the first element in a list..
    /// $Source: vfc_list.hpp $
    /// @dspecref{261}
    /// @return Returns a bidirectional const iterator addressing the first element in the list
    ///         or to the location succeeding an empty list

    //=============================================================================
    const_iterator begin() const;

    //-----------------------------------------------------------------------------
    /// Returns an iterator that addresses the location succeeding the last element in a list.
    /// $Source: vfc_list.hpp $
    /// @dspec{262}     The SW component "vfc" shall return an iterator after the last item of
    ///                 the given List instance.
    /// @return Returns a bidirectional iterator that addresses the location succeeding the last
    ///         element in a list. If the list is empty, then list::end == list::begin.

    //=============================================================================
    iterator end();

    //-----------------------------------------------------------------------------
    /// Returns a const iterator that addresses the location succeeding the last element in a list.
    /// @dspecref{262}
    /// @note If the list is empty, then list.end() == list.begin().
    /// $Source: vfc_list.hpp $
    /// @return Returns a const bidirectional iterator that addresses the location succeeding the last
    ///         element in a list. If the list is empty, then list::end == list::begin.

    //=============================================================================
    const_iterator end() const;

    //-----------------------------------------------------------------------------
    /// Returns an iterator addressing the first element in a reversed list.
    /// $Source: vfc_list.hpp $
    /// @dspec{263}     The SW component "vfc" shall return an reverse iterator to the last
    ///                 item of the given List instance.
    /// @return Returns a reverse bidirectional iterator addressing the first element in a
    ///         reversed list (or addressing what had been the last element in the unreversed list).

    //=============================================================================
    reverse_iterator rbegin();

    //-----------------------------------------------------------------------------
    /// Returns a const iterator addressing the first element in a reversed list.
    /// $Source: vfc_list.hpp $
    /// @dspecref{263}
    /// @return Returns a const reverse bidirectional iterator addressing the first element in a
    ///         reversed list (or addressing what had been the last element in the unreversed list).

    //=============================================================================
    const_reverse_iterator rbegin() const;

    //-----------------------------------------------------------------------------
    /// Returns an iterator that addresses the location succeeding the last element in a reversed list.
    /// $Source: vfc_list.hpp $
    /// @dspec{264}     The SW component "vfc" shall return an reverse iterator to position
    ///                 after first in reverse order first item of the given List instance.
    /// @return Returns a reverse bidirectional iterator that addresses the location succeeding the
    ///         last element in a reversed list (the location that had preceded the first
    ///         element in the unreversed list).

    //=============================================================================
    reverse_iterator rend();

    //-----------------------------------------------------------------------------
    /// Returns a const iterator that addresses the location succeeding the last element in a reversed list.
    /// $Source: vfc_list.hpp $
    /// @dspecref{264}
    /// @return Returns a const reverse bidirectional iterator that addresses the location succeeding the
    ///         last element in a reversed list (the location that had preceded the first
    ///         element in the unreversed list).

    //=============================================================================
    const_reverse_iterator rend() const;

    ///////////////////////////////////////////////////////////////////////////////////////////////
    // size related funcs
    ///////////////////////////////////////////////////////////////////////////////////////////////

    //-----------------------------------------------------------------------------
    /// Tests if a list is empty.
    /// $Source: vfc_list.hpp $
    /// @dspec{284}     The SW component "vfc" shall return a boolean value if the given
    ///                 List instance is empty.
    /// @return Retruns true if the list is empty; false if the list is not empty.

    //=============================================================================
    bool empty() const;

    //-----------------------------------------------------------------------------
    /// Returns the number of elements in the list.
    /// $Source: vfc_list.hpp $
    /// @dspec{289}    The SW component "vfc" shall return the current size of the given
    ///                List instance.
    /// @return Returns the number of elements in the list.

    //=============================================================================
    size_type size() const;

    //-----------------------------------------------------------------------------
    /// Returns the length of the longest sequence that the object can control.
    /// $Source: vfc_list.hpp $
    /// @dspec{286}     The SW component "vfc" shall return the maximum size of the given
    ///                 List instance.
    /// @return Returns the length of the longest sequence that the object can control.

    //=============================================================================
    //  qacpp-4212: Adding `static` keyword to method would change the API. Waiting for new deviation before suppressing
    size_type max_size() const;

    //-----------------------------------------------------------------------------
    /// Returns the length of the longest sequence that the object can control.
    /// $Source: vfc_list.hpp $
    /// @dspecref{286}
    /// @return Returns the length of the longest sequence that the object can control.

    //=============================================================================
    //  qacpp-4212: Adding `static` keyword to method would change the API. Waiting for new deviation before suppressing
    size_type capacity() const;

    //-----------------------------------------------------------------------------
    /// Specifies a new size for a list.
    /// $Source: vfc_list.hpp $
    /// @dspec{285}     The SW component "vfc" shall be able to resize the given List instance
    ///                 and initialize new items with the given default item value.
    /// @param  f_newSize   The new size of the list.
    /// @param  f_default   The value of the new elements to be added to the list.
    /// @note   The value of the new elements will be added if the new size is larger
    ///         that the original size

    //=============================================================================
    void resize(size_type f_newSize, const value_type& f_default);

    //-----------------------------------------------------------------------------
    /// Specifies a new size for a list.
    /// $Source: vfc_list.hpp $
    /// @param  f_newSize   The new size of the list.

    //=============================================================================
    void resize(size_type f_newSize);

    ///////////////////////////////////////////////////////////////////////////////////////////////
    // funcs
    ///////////////////////////////////////////////////////////////////////////////////////////////

    //-----------------------------------------------------------------------------
    /// Returns a copy of the allocator object used to construct a list.
    /// $Source: vfc_list.hpp $
    /// @dspec{1769}     The SW component "vfc" shall return the allocator of the given
    ///                  List instance.
    /// @return The allocator used by the list.

    //=============================================================================
    allocator_type VFC_ATR_DEPRECATED2(
        get_allocator(),
        "Please don't use get_allocator() any longer. The allocator "
        "type is still available by the public type definition allocator_type, "
        "which can be used to manually construct a new allocator instance.") const;

    //-----------------------------------------------------------------------------
    /// Adds an element to the end of a list.
    /// It increases the list size by one.
    /// The function creates an element at the end of the list and
    /// assigns the given data to it. If the list is full, push_back does
    /// not do anything but simply returns.
    /// $Source: vfc_list.hpp $
    /// @dspec{259}     The SW component "vfc" shall append a given item at the end of the
    ///                 given List instance.
    /// @param  f_value_r  The element added to the end of the list.

    //=============================================================================
    void push_back(const value_type& f_value_r);

    //-----------------------------------------------------------------------------
    /// Adds an UNINITIALIZED element to the end of a list.
    /// If the (TFixed...) list is full, insert_uninitialized_back does not do anything but simply returns.
    /// Use in conjunction with placement new, if you have list elements that
    /// are very costly to copy and you still want to use a value container:
    /// @dspec{260}     The SW component "vfc" shall be able to allocate an uninitialized
    ///                 item at the end of the given List instance and return an iterator
    ///                 to the allocated item.
    /// @code new (&*myList.insert_uninitialized_back()) myElement(ctorArgs); @endcode
    /// @return iterator to newly allocated but uninitialized element
    /// $Source: vfc_list.hpp $

    //=============================================================================
    iterator insert_uninitialized_back();

    //-----------------------------------------------------------------------------
    /// Deletes the element at the end of a list.
    /// It reduces the list by one.
    /// Note that no data is returned, and if the last element's data
    /// is needed, it should be retrieved before pop_back() is called.
    /// The user has to ensure that this function is not called on an empty list.
    /// $Source: vfc_list.hpp $

    /// @dspec{265}     The SW component "vfc" shall remove the last item of the given
    ///                 List instance.
    //=============================================================================
    void pop_back();

    //-----------------------------------------------------------------------------
    /// Adds an element to the beginning of a list.
    /// It increases the list by one.
    /// The function creates an element at the front of the list and
    /// assigns the given data to it. If the list is full, push_front does
    /// not do anything but simply returns.
    /// $Source: vfc_list.hpp $
    /// @dspec{266}     The SW component "vfc" shall prepend a given item at the begin of
    ///                 the given List instance.
    /// @param  f_value_r  The element added to the begining of the list.

    //=============================================================================
    void push_front(const value_type& f_value_r);

    //-----------------------------------------------------------------------------
    /// Adds an UNINITIALIZED element to the beginning of a list.
    /// If the (TFixed...) list is full, insert_uninitialized_front does not do anything but simply returns.
    /// Use in conjunction with placement new, if you have list elements that
    /// are very costly to copy and you still want to use a value container:
    /// @dspec{267}     The SW component "vfc" shall prepend an unitialized item at the begin
    ///                 of the given List instance.
    /// @code new (&*myList.insert_uninitialized_front()) myElement(ctorArgs); @endcode
    /// @return iterator to newly allocated but uninitialized front element
    /// $Source: vfc_list.hpp $

    //=============================================================================
    iterator insert_uninitialized_front();

    //-----------------------------------------------------------------------------
    /// Deletes the element at the begining of a list.
    /// It reduces the list by one.
    /// Note that no data is returned, and if the first element's data
    /// is needed, it should be retrieved before pop_front() is called.
    /// If the list is empty, pop_front does not do anything but simply returns.
    /// $Source: vfc_list.hpp $

    /// @dspec{268}     The SW component "vfc" shall remove the first item of the given
    ///                 List instance.
    //=============================================================================
    void pop_front();

    //-----------------------------------------------------------------------------
    /// Inserts a element into list before specified position.
    /// $Source: vfc_list.hpp $
    /// @dspec{291}     The SW component "vfc" shall insert a copy of a given item at the
    ///                 position before the given iterator inside of the given List instance.
    /// @param  f_pos  The position that will become the next element to the inserted element.
    /// @param  f_value_r  The value of the element being inserted into the list.
    /// @return returns an iterator that points to the position where the new element was inserted.

    //=============================================================================
    iterator insert(iterator f_pos, const value_type& f_value_r);

    //-----------------------------------------------------------------------------
    /// Inserts a number of elements into the list before specified position.
    /// $Source: vfc_list.hpp $
    /// @dspec{1770}     The SW component "vfc" shall insert a given number of copies of a
    ///                  given item at the position before the given iterator inside of the
    ///                  given List instance.
    /// @param  f_pos  The position that will become the next element to the inserted element.
    /// @param  f_count  Number of elements to be inserted.
    /// @param  f_value_r  The value of the element being inserted into the list.

    //=============================================================================
    void insert(iterator f_pos, size_type f_count, const value_type& f_value_r);

    //-----------------------------------------------------------------------------
    /// Inserts a range of elements into the list before specified position.
    /// $Source: vfc_list.hpp $
    /// @dspec{1771}     The SW component "vfc" shall insert copies of a sequence of items
    ///                  specified by a given start and a given end iterator at the position
    ///                  before the given destination iterator inside of the given List instance.
    /// @tparam InIteratorType type of source iterators
    /// @param  f_pos  The position in the list where the first element is inserted.
    /// @param  f_first  Position of the first element in the range of elements in the
    ///                  argument list to be copied.
    /// @param  f_last   Position of the first element beyond the range of elements in
    ///                  the argument list to be copied.

    //=============================================================================
    template <class InIteratorType>
    void insert(iterator f_pos, InIteratorType f_first, InIteratorType f_last);

    //-----------------------------------------------------------------------------
    /// Inserts an UNINITIALIZED element after the given position.
    /// If the (TFixed...) list is full, insert_uninitialized simply returns.
    /// Use in conjunction with placement new, if you have list elements that
    /// are very costly to copy and you still want to use a value container:
    /// @dspec{1772}     The SW component "vfc" shall allocate an entry for an item that
    ///                  is not initialized at the position specified by a given iterator
    ///                  pointing to an item in the given list instance and return an iterator
    ///                  to the allocated entry.
    /// @code new (&(*myList.insert_uninitialized())) myElement(ctorArgs); @endcode
    /// @param f_pos    iterator to position where insertion shall be performed
    /// @return iterator to newly allocated but uninitialized front element
    /// $Source: vfc_list.hpp $

    //=============================================================================
    iterator insert_uninitialized(iterator f_pos);

    //-----------------------------------------------------------------------------
    /// Assigns a given value to a list.
    /// Function fills a list with the specified number of copies
    /// Note that the assignment completely changes the list
    /// and that the resulting list's size is the same as the count
    /// $Source: vfc_list.hpp $
    /// @dspec{1773}     The SW component "vfc" shall replace all content of the given
    ///                  List instance with a given number of items with a given value.
    /// @param  f_count  The number of copies of an element being inserted into the list.
    /// @param  f_value_r  The value of the element being inserted into the list.

    //=============================================================================
    void assign(size_type f_count, const value_type& f_value_r);

    //-----------------------------------------------------------------------------
    /// Assigns a range to a list.
    /// Function fills a list with the specified range
    /// Note that the assignment completely changes the list and
    /// that the resulting list's size is the same as the number of
    /// elements assigned.  Old data is lost.
    /// $Source: vfc_list.hpp $
    /// @dspec{1778}     The SW component "vfc" shall replace all content of the given List
    ///                  instance with copies of the items in the sequence specified by a given
    ///                  start iterator and a given end iterator.
    /// @tparam InIteratorType type of source iterators
    /// @param  f_first  Position of the first element in the range of elements to be copied
    ///                  from the argument list.
    /// @param  f_last   Position of the first element just beyond the range of elements to be copied from the argument
    /// list.

    //=============================================================================
    template <typename InIteratorType>
    void assign(InIteratorType f_first, InIteratorType f_last);

    //-----------------------------------------------------------------------------
    /// Removes an element in a list from specified position.
    /// beyond any elements removed, or a pointer to the end of the list if no such element exists.
    /// Function will erase the element at the given position and thus
    /// shorten the list by one.
    /// The user is also cautioned that this function only erases the element,
    /// and that if the element is itself a pointer, the pointed-to memory is not touched
    /// Managing the pointer is the user's responsibilty.
    /// $Source: vfc_list.hpp $
    /// @dspec{270}     The SW component "vfc" shall erase an item at given iterator from the
    ///                 given List instance and return an iterator to the position after the
    ///                 erased element.
    /// @param  f_pos  Position of the element to be removed from the list.
    /// @return  A bidirectional iterator that designates the first element remaining

    //=============================================================================
    iterator erase(iterator f_pos);

    //-----------------------------------------------------------------------------
    /// Removes a range of elements in a list from specified position.
    /// beyond any elements removed, or a pointer to the end of the list if no such element exists.
    /// Function will erase the element at the given position and thus
    /// shorten the list by one.
    /// Function will erase the specified range of elements
    /// and shorten the list accordingly.
    /// The user is also cautioned that this function only erases the elements, and that if the
    /// elements themselves are pointers, the pointed-to memory is not
    /// touched .  Managing the pointer is the user's responsibilty.
    /// $Source: vfc_list.hpp $
    /// @dspec{271}     The SW component "vfc" shall erase items starting from a given start
    ///                 iterator upto not including a given end iterator from the given List
    ///                 instance and return an iterator pointing to the position atfer the last
    ///                 erased element.
    /// @param  f_first  Position of the first element removed from the list.
    /// @param  f_last  Position just beyond the last element removed from the list.
    /// @return  A bidirectional iterator that designates the first element remaining

    //=============================================================================
    iterator erase(iterator f_first, iterator f_last);

    //-----------------------------------------------------------------------------
    /// Erases elements in a list that match a specified value.
    /// The order of the elements remaining is not affected.
    /// Note that this function only erases the elements, and
    /// that if the elements themselves are pointers, the  pointed-to memory
    /// is not touched. Managing the pointer is the user's responsibilty.
    /// $Source: vfc_list.hpp $
    /// @dspec{272}     The SW component "vfc" shall remove and deallocate all items comparing
    ///                 equal to a given item from the given List instance.
    /// @param  f_value_r  The value which, if held by an element,
    /// will result in that element's removal from the list.

    //=============================================================================
    void remove(const value_type& f_value_r);

    //-----------------------------------------------------------------------------
    /// Erases elements from a list for which a specified predicate is satisfied.
    /// Erases every element in the list for which the predicate
    /// returns true.  The order of the elements remaining is not affected.
    /// Note that this function only erases the elements, and that if the
    /// elements themselves are pointers, the pointed-to memory is
    /// not touched .  Managing the pointer is the user's responsibilty.
    /// $Source: vfc_list.hpp $
    /// @dspec{273}     The SW component "vfc" shall remove and deallocate all items satisifying
    ///                 a given predicate from the given List instance.
    /// @tparam PredicateType unary predicate type applicable to value type
    /// @param  f_predicate The unary predicate which, if satisfied by an
    /// element, results in the deletion of that element from the list.

    //=============================================================================
    template <class PredicateType>
    void remove_if(PredicateType f_predicate);

    //-----------------------------------------------------------------------------
    /// Reverses the order in which the elements occur in a list.
    /// $Source: vfc_list.hpp $

    /// @dspec{1789}     The SW component "vfc" shall reorder all items of a given List
    ///                  instance such that the items appear in the reverse order than they
    //                   did before.
    //=============================================================================
    void reverse();

    //-----------------------------------------------------------------------------
    /// Exchanges the elements of two lists.
    /// $Source: vfc_list.hpp $
    /// @dspec{1570}     The SW component "vfc" shall swap the all items of a given List
    ///                  instance wih the all items of another given List instance.
    /// @param  f_rhs_r  A list of the same element and allocator types.

    //=============================================================================
    void swap(list_type& f_rhs_r);

    //-----------------------------------------------------------------------------
    /// Removes adjacent elements that are equal in the list.
    /// For each consecutive set of elements with the same value,
    /// remove all but the first one.  Remaining elements stay in
    /// list order.  Note that this function only erases the
    /// elements, and that if the elements themselves are pointers,
    /// the pointed-to memory is not touched in any way.  Managing
    /// the pointer is the user's responsibilty.
    /// $Source: vfc_list.hpp $

    /// @dspec{1791}     The SW component "vfc" shall remove all items from a given List
    ///                  instance that compare equal to their respective predecessor.
    //=============================================================================
    void unique();

    //-----------------------------------------------------------------------------
    /// Removes adjacent elements that satisfy some other binary predicate from the list.
    /// This function assumes that the list is sorted, so that all duplicate elements
    /// are adjacent. Duplicates that are not adjacent will not be deleted. Remaining
    /// elements stay in list order.
    /// Note that this function only erases the elements, and that if the
    /// elements themselves are pointers, the pointed-to memory is not
    /// touched in any way. Managing the pointer is the user's responsibilty.
    /// $Source: vfc_list.hpp $
    /// @dspec{1793}     The SW component "vfc" shall remove all items from a given List
    ///                  instance that return the boolean value "true" when being called with
    ///                  a given binary predicate with their respective predecessor item.
    /// @tparam BinaryPredicateType binary predicate applicable to two value type instances
    /// @param  f_binaryPredicate  The binary predicate used to compare successive elements.

    //=============================================================================
    template <class BinaryPredicateType>
    void unique(BinaryPredicateType f_binaryPredicate);

    //-----------------------------------------------------------------------------
    /// Removes all elements from the argument list and inserts them into this list.
    /// The elements of given list are inserted in front of the element specified.
    /// The source list becomes an empty list at the end of the operation.
    /// Note If the number of elements to spliced exceed the capacity of the list,
    /// then the function leaves both the lists unaltered
    /// $Source: vfc_list.hpp $
    /// @dspec{1795}     The SW component "vfc" shall remove all items from a given source
    ///                  List instance and insert the removed items in a given destination
    ///                  List instance at the position specified by a given destination iterator.
    /// @param  f_pos  The position in the target list before which the elements of the
    /// argument list are to be inserted.
    /// @param  f_list_r  The argument list that is to be inserted into this list.

    //=============================================================================
    void splice(iterator f_pos, list_type& f_list_r);

    //-----------------------------------------------------------------------------
    /// Removes one element from the argument list and inserts them into this list.
    /// Removes the element in given list referenced by iterator and
    /// inserts it into the current list before specified position.
    /// Note If the number of elements to spliced exceed the capacity of the list,
    /// then the function leaves both the lists unaltered
    /// $Source: vfc_list.hpp $
    /// @dspec{1796}     The SW component "vfc" shall remove one item from a given source
    ///                  List instance and insert the removed item in a given destination List
    ///                  instance at the position specified by a given destination iterator.
    /// @param  f_pos  The position in the target list before which the elements of the
    /// argument list are to be inserted.
    /// @param  f_list_r  The argument list that is to be inserted into this list.
    /// @param  f_iter  Iterator referencing the element to be moved.

    //=============================================================================
    void splice(iterator f_pos, list_type& f_list_r, iterator f_iter);

    //-----------------------------------------------------------------------------
    /// Removes a range of elements from the argument list and inserts them into this list.
    /// Removes elements in the given range and inserts them before position specified position.
    /// Note if f_pos is in between the specified f_first & f_last the
    /// output cannot be predicted.
    /// Note If the number of elements to spliced exceed the capacity of the list,
    /// then the function leaves both the lists unaltered.
    /// $Source: vfc_list.hpp $
    /// @dspec{1797}     The SW component "vfc" shall remove a sequence of items specified by a
    ///                  given source start iterator and a given source end iterator from a given
    ///                  source List instance and insert the removed items in a given destination
    ///                  List instance at the position specified by a given destination iterator.
    /// @param  f_pos  The position in the target list before which the elements of the
    /// argument list are to be inserted.
    /// @param  f_list_r  The argument list that is to be inserted into this list.
    /// @param  f_first  Iterator referencing the start of range in the argument list.
    /// @param  f_last  Iterator referencing the end of range in the argument list.

    //=============================================================================
    void splice(iterator f_pos, list_type& f_list_r, iterator f_first, iterator f_last);

    //-----------------------------------------------------------------------------
    /// Removes the elements from the argument list, inserts them into the target list,
    /// and orders the new, combined set of elements in ascending order.
    /// Assumes that both lists are sorted according to operator < ().
    /// Merges elements of f_list_r into this list in sorted order,
    /// leaving f_list_r empty when complete. Elements in this list precede elements in
    /// f_list_r they are equal.
    /// Note If the number of elements to merged exceed the capacity of the list,
    /// then the function leaves both the lists unaltered.
    /// $Source: vfc_list.hpp $
    /// @dspec{1802}     The SW component "vfc" shall remove all items of the given source
    ///                  List instance and insert these source items in the given destination
    ///                  List instance such that the resulting destination list items will be
    ///                  sorted with respect to the operator< of the item type under the precondition
    ///                  that both source and destination items were sorted with respect to the operator<
    ///                  of the item type before.
    /// @param  f_list_r  Sorted list to merge.

    //=============================================================================
    void merge(list_type& f_list_r);

    //-----------------------------------------------------------------------------
    /// Removes the elements from the argument list, inserts them into the target list,
    /// and orders the new, combined set of elements in some other specified order.
    /// Assumes that both the lists are sorted according to
    /// defined Comparison function.  Merges elements of f_list_r
    /// into this list in sorted order, leaving f_list_r empty when complete.
    /// Elements in this list precede elements in f_list_r that are equivalent
    /// according to the Comparison function.
    /// Note If the number of elements to merged exceed the capacity of the list,
    /// then the function leaves both the lists unaltered.
    /// $Source: vfc_list.hpp $
    /// @dspec{1805}     The SW component "vfc" shall remove all items of the given source List
    ///                  instance and insert these source items in the given destination List instance
    ///                  such that the resulting destination list items will be sorted with respect to
    ///                  the given binary predicate of the item type under the precondition that both
    ///                  source and destination items were sorted with respect to the given binary predicate
    ///                  of the item type before.
    /// @tparam OrderingType binary predicate applicable to two value type instances
    /// @param  f_list_r  Sorted list to merge.
    /// @param  f_orderType Comparison function definining sort order.

    //=============================================================================
    template <class OrderingType>
    void merge(list_type& f_list_r, OrderingType f_orderType);

    //-----------------------------------------------------------------------------
    /// Arranges the elements of a list in ascending order.
    /// Equivalent elements remain in list order.
    /// Inserstion sort technique used.
    /// @dspec{1807}     The SW component "vfc" shall reorder the items of the given list such
    ///                  that they are sorted with respect to the operator< of the item type.
    /// @note   Time Complexity of the sort function (O (N2))
    /// $Source: vfc_list.hpp $

    /// @ingroup vfc_group_containers
    //=============================================================================
    void sort();

    //-----------------------------------------------------------------------------
    /// Arranges the elements of a list with respect to some
    /// user-specified order relation.
    /// Equivalent elements remain in list order.
    /// Inserstion sort technique used.
    /// @dspec{1808}     The SW component "vfc" shall reorder the items of the given list such
    ///                  that they are sorted with respect to the given binary predicate taking
    ///                  two item type instances of the item type.
    /// @tparam ComparisonPredicateType binary predicate applicable to two value type instances
    /// @param  f_predicate instance of binary predicate specifying the order relation of the values
    /// $Source: vfc_list.hpp $

    //=============================================================================
    template <class ComparisonPredicateType>
    void sort(ComparisonPredicateType f_predicate);

    //-----------------------------------------------------------------------------
    /// Erases all the elements of a list.
    /// Note that this function only erases the elements,
    /// and that if the elements themselves are pointers,
    /// Managing the pointer is the user's responsibilty.
    /// $Source: vfc_list.hpp $

    /// @dspec{1810}     The SW component "vfc" shall remove all items from the given
    ///                  List instance.
    //=============================================================================
    void clear();

    ///////////////////////////////////////////////////////////////////////////////////////////////
    // element access
    ///////////////////////////////////////////////////////////////////////////////////////////////

    //-----------------------------------------------------------------------------
    /// Returns a reference to the first element in a list.
    /// $Source: vfc_list.hpp $
    /// @dspec{1811}     The SW component "vfc" shall return a reference to the first item
    ///                  in the given List instance.
    /// @return     Returns a reference to the first element in a list,
    /// If the list is empty, the return is undefined.

    //=============================================================================
    reference front();

    //-----------------------------------------------------------------------------
    /// Returns a constant reference to the first element in a list
    /// $Source: vfc_list.hpp $
    /// @dspecref{1811}
    /// @return     Returns a constant reference to the first element in a list.
    /// If the list is empty, the return is undefined

    //=============================================================================
    const_reference front() const;

    //-----------------------------------------------------------------------------
    /// Returns a reference to the last element of a list.
    /// $Source: vfc_list.hpp $
    /// @dspec{1812}     The SW component "vfc" shall return a reference to the last item in
    ///                  the given List instance.
    /// @return     Returns a reference to the last element of a list.
    /// If the list is empty, the return value is undefined.

    //=============================================================================
    reference back();

    //-----------------------------------------------------------------------------
    /// constant reference to the last element of a list
    /// $Source: vfc_list.hpp $
    /// @dspecref{1812}
    /// @return     Returns a constant reference to the last element of a list.
    /// If the list is empty, the return value is undefined.

    //=============================================================================
    const_reference back() const;

  private:
    //-----------------------------------------------------------------------------
    /// Returns the base pointer to the continuous memory of the allocator or null
    /// @return the pointer the first memory slot of the allocator

    //=============================================================================
    node_type* getBasePointer() const;

    /// Returns null for allocators using pointers
    /// @return the null pointer

    //=============================================================================
    node_type* getBasePointer(true_t) const;

    /// Returns the base pointer to the continuous memory of the allocator
    /// @return the pointer the first memory slot of the allocator

    //=============================================================================
    node_type* getBasePointer(false_t) const;

    //-----------------------------------------------------------------------------
    /// The head is always linked to the last and first element in the list, evaluates pointer case.
    /// @return the pointer the head node
    //=============================================================================
    position_type* getHead();

    //-----------------------------------------------------------------------------
    /// The head is always linked to the last and first element in the list, evaluates pointer case.
    /// @return the const pointer the head node
    //=============================================================================
    const position_type* getHead() const;

    //-----------------------------------------------------------------------------
    /// The head is always linked to the last and first element in the list, pointerless case.
    /// @return the pointer the head node
    //=============================================================================
    position_type* getHead(false_t);

    //-----------------------------------------------------------------------------
    /// The head is always linked to the last and first element in the list, pointerless case.
    /// @return the const pointer the head node
    //=============================================================================
    const position_type* getHead(false_t) const;

    //-----------------------------------------------------------------------------
    /// The head is always linked to the last and first element in the list, pointer case.
    /// @return the pointer the head node
    //=============================================================================
    position_type* getHead(true_t);

    //-----------------------------------------------------------------------------
    /// The head is always linked to the last and first element in the list, pointer case.
    /// @return the const pointer the head node
    //=============================================================================
    const position_type* getHead(true_t) const;

    //-----------------------------------------------------------------------------
    /// function creates a node with the specified value and inserts the
    /// node and returns pointer to CNode else returns a zero CNode pointer
    /// if the list is full.
    /// $Source: vfc_list.hpp $
    /// @param f_pos                iterator of the inserted element.
    /// @param f_value_p            points to element that will be inserted or 0,
    ///                             if you want to insert an uninitialized element
    ///                             that needs to be constructed by the user!
    /// @return return a pointer to newly created node.

    //=============================================================================
    node_type* insertElement(iterator f_pos, const value_type* f_value_p);

    /// swap when memory is inplace
    template <class OrderingType>
    void mergeIntern(list_type& f_list_r, OrderingType f_orderType, true_t);

    /// swap when memory is not inplace
    template <class OrderingType>
    void mergeIntern(list_type& f_list_r, OrderingType f_orderType, false_t);

    /// function removed the specified element from the list and relinks the chain
    position_type* removeElement(position_type* f_node_p);

    /// function removed the specified range of elements from the list and relinks the chain
    position_type* removeRange(position_type* f_startNode_p, position_type* f_endNode_p);

    // reverse for PODs
    void reverse(true_t);

    /// reverse for UDTs (Non PODs)
    void reverse(false_t);

    /// splice when memory is inplace
    void spliceIntern(iterator f_pos, list_type& f_list_r, iterator f_first, iterator f_last, true_t);

    /// splice when memory not inplace
    void spliceIntern(iterator f_pos, list_type& f_list_r, iterator f_first, iterator f_last, false_t);

    /// Swap when memory is inplace.
    /// This will work by swapping the values.
    inline void swapIntern(list_type& f_rhs_r, true_t);

    /// Swap when memory is not inplace.
    /// The will be performed by node swapping.
    inline void swapIntern(list_type& f_rhs_r, false_t);

    //-----------------------------------------------------------------------------
    /// sequence from f_first to one before f_last is inserted before f_pos.
    /// $Source: vfc_list.hpp $
    /// @param f_pos                position to be inserted.
    /// @param f_first              position of the first element to be relinked.
    /// @param f_last               position just beyond the last element to be relinked.

    //=============================================================================
    void relinkNode(iterator f_pos, iterator f_first, iterator f_last);

  private:
    /// Counter: how much elements are actually contained. Future improvement: In case of the TFixedMemPoolAllocator
    /// there is also the m_usedChunks member which is redundant to m_numElements.
    size_type      m_numElements;
    nodealloc_type m_nodeAlloc;

    /// The head of the list. A doubled-linked list needs a pointer to the first and to the last node, hence a
    /// TNodeBase can be reused (has prev+next). In case of list with indices instead of pointers the calculation of
    /// head and tail is done differently (@sa getHead()) and this is an empty class. Note and future improvement: in
    /// that case m_head is likely to still consume 4 bytes.
    using head_type = typename TIf<UsePointer, position_type, false_t>::type;
    head_type m_head;
};

//-----------------------------------------------------------------------------
/// List equality comparison.
/// This is an equivalence relation.  It is linear in the size of
/// the lists.  Lists are considered equivalent if their sizes are
/// equal, and if corresponding elements compare equal.
/// $Source: vfc_list.hpp $
/// @tparam ValueType           DataType to be stored.
/// @tparam AllocatorType       AllocatorType used.
/// @tparam MaxSizePolicyType   PolicyType specifying behavior of max_size
/// @param  f_list1_r  list.
/// @param  f_list2_r  list of the same type as f_list1_r.
/// @return  True if the size and elements of the lists are equal else return False

/// @ingroup vfc_containers
//=============================================================================
template <class ValueType, class AllocatorType, class MaxSizePolicyType>
bool operator==(
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list1_r,
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list2_r);

//-----------------------------------------------------------------------------
/// List ordering relation.
/// This is a total ordering relation.  It is linear in the size of the
/// lists.  The elements must be comparable with <.
/// $Source: vfc_list.hpp $
/// @tparam ValueType           DataType to be stored.
/// @tparam AllocatorType       AllocatorType used.
/// @tparam MaxSizePolicyType   PolicyType specifying behavior of max_size
/// @param  f_list1_r  list.
/// @param  f_list2_r  list of the same type as f_list1_r.
/// @return  True if f_list1_r is lexicographically less than f_list2_r else return False

/// @ingroup vfc_containers
//=============================================================================
template <class ValueType, class AllocatorType, class MaxSizePolicyType>
bool operator<(
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list1_r,
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list2_r);

//-----------------------------------------------------------------------------
/// List in equality comparison.
/// This is an equivalence relation.  It is linear in the size of
/// the lists.  Lists are considered equivalent if their sizes are
/// equal, and if corresponding elements compare equal.
/// $Source: vfc_list.hpp $
/// @tparam ValueType           DataType to be stored.
/// @tparam AllocatorType       AllocatorType used.
/// @tparam MaxSizePolicyType   PolicyType specifying behavior of max_size
/// @param  f_list1_r  list.
/// @param  f_list2_r  list of the same type as f_list1_r.
/// @return  False if the size and elements of the lists are equal else return True

/// @ingroup vfc_containers
//=============================================================================
template <class ValueType, class AllocatorType, class MaxSizePolicyType>
bool operator!=(
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list1_r,
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list2_r);

//-----------------------------------------------------------------------------
/// List ordering relation.
/// This is a total ordering relation.  It is linear in the size of the
/// lists.  The elements must be comparable with >.
/// $Source: vfc_list.hpp $
/// @tparam ValueType           DataType to be stored.
/// @tparam AllocatorType       AllocatorType used.
/// @tparam MaxSizePolicyType   PolicyType specifying behavior of max_size
/// @param  f_list1_r  list.
/// @param  f_list2_r  list of the same type as f_list1_r.
/// @return  True if f_list1_r is lexicographically greater than f_list2_r else return False

/// @ingroup vfc_containers
//=============================================================================
template <class ValueType, class AllocatorType, class MaxSizePolicyType>
bool operator>(
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list1_r,
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list2_r);

//-----------------------------------------------------------------------------
/// List ordering relation.
/// This is a total ordering relation.  It is linear in the size of the
/// lists.  The elements must be comparable with <=.
/// $Source: vfc_list.hpp $
/// @tparam ValueType           DataType to be stored.
/// @tparam AllocatorType       AllocatorType used.
/// @tparam MaxSizePolicyType   PolicyType specifying behavior of max_size
/// @param  f_list1_r  list.
/// @param  f_list2_r  list of the same type as f_list1_r.
/// @return  True if f_list1_r is lexicographically less than or equal to f_list2_r else return False

/// @ingroup vfc_containers
//=============================================================================
template <class ValueType, class AllocatorType, class MaxSizePolicyType>
bool operator<=(
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list1_r,
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list2_r);

//-----------------------------------------------------------------------------
/// List ordering relation.
/// This is a total ordering relation.  It is linear in the size of the
/// lists.  The elements must be comparable with >=.
/// $Source: vfc_list.hpp $
/// @tparam ValueType           DataType to be stored.
/// @tparam AllocatorType       AllocatorType used.
/// @tparam MaxSizePolicyType   PolicyType specifying behavior of max_size
/// @param  f_list1_r  list.
/// @param  f_list2_r  list of the same type as f_list1_r.
/// @return  True if f_list1_r is lexicographically greater than or equal to f_list2_r else return False

/// @ingroup vfc_containers
//=============================================================================
template <class ValueType, class AllocatorType, class MaxSizePolicyType>
bool operator>=(
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list1_r,
    const TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list2_r);

//---------------------------------------------------------------------
/// Swaps exchanges the elements of two lists f_list1_r & f_list2_r.
/// $Source: vfc_list.hpp $
/// @tparam ValueType           DataType to be stored.
/// @tparam AllocatorType       AllocatorType used.
/// @tparam MaxSizePolicyType   PolicyType specifying behavior of max_size
/// @param  f_list1_r    The list providing the elements to be swapped, or the
///                      list whose elements are to be exchanged with those of the list f_list2_r.
/// @param  f_list2_r    A list whose elements are to be exchanged with those of the list f_list1_r.

/// @ingroup vfc_containers
//---------------------------------------------------------------------
template <class ValueType, class AllocatorType, class MaxSizePolicyType>
void swap(
    TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list1_r,
    TList<ValueType, AllocatorType, MaxSizePolicyType>& f_list2_r);

} // namespace vfc

#include "vfc/container/vfc_list.inl"

#endif // VFC_LIST_HPP_INCLUDED

//=============================================================================

