//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_FIFO_INL_INCLUDED
#define VFC_FIFO_INL_INCLUDED

#include "vfc/core/vfc_types.hpp"     // used for int32_t
#include "vfc/core/vfc_assert.hpp"    // used for VFC_ASSERT()
#include "vfc/core/vfc_algorithm.hpp" // used for contiguous_copy_n()

template <class ValueType, vfc::int32_t CapacityValue>
inline vfc::TFifo<ValueType, CapacityValue>::TFifo() : m_data(), m_write_pos(0), m_read_pos(0)
{
}

template <class ValueType, vfc::int32_t CapacityValue>
inline vfc::TFifo<ValueType, CapacityValue>::~TFifo()
    VFC_NOEXCEPT // PRQA S 2635 # Technical debt L7 id=00c14a2167ce30a84452-674524d1728adf94
{
}

template <class ValueType, vfc::int32_t CapacityValue>
inline bool vfc::TFifo<ValueType, CapacityValue>::push(const value_type& f_param_r)
{
    const uint32_t l_write_pos      = m_write_pos.load(vfc::MemoryOrderRelaxed);
    uint32_t       l_next_write_pos = l_write_pos + 1U;
    if (static_cast<uint32_t>(InternalFifoSize) == l_next_write_pos) // wraparound
    {
        l_next_write_pos = 0U;
    }

    // buffer overflow detection
    if (l_next_write_pos == m_read_pos.load(vfc::MemoryOrderAcquire))
    {
        return false;
    }
    else
    {
        // assing the user data
        using idx_type                             = typename TCArray<value_type, InternalFifoSize>::size_type;
        m_data[static_cast<idx_type>(l_write_pos)] = f_param_r;

        // Assign m_write_p to next writable location
        m_write_pos.store(l_next_write_pos, vfc::MemoryOrderRelease);

        return true;
    }
}

template <class ValueType, vfc::int32_t CapacityValue>
inline bool vfc::TFifo<ValueType, CapacityValue>::empty() const
{
    return m_read_pos.load(vfc::MemoryOrderAcquire) == m_write_pos.load(vfc::MemoryOrderAcquire);
}

template <class ValueType, vfc::int32_t CapacityValue>
inline bool vfc::TFifo<ValueType, CapacityValue>::pop(value_type& f_param_r)
{
    const uint32_t l_read_pos  = m_read_pos.load(vfc::MemoryOrderRelaxed);
    const uint32_t l_write_pos = m_write_pos.load(vfc::MemoryOrderAcquire);

    // buffer empty detection
    if (l_read_pos == l_write_pos)
    {
        return false;
    }
    else
    {
        uint32_t l_next_read_pos = l_read_pos + 1U;
        if (static_cast<uint32_t>(InternalFifoSize) == l_next_read_pos) // wraparound
        {
            l_next_read_pos = 0U;
        }

        // assign the user data
        using idx_type = typename TCArray<value_type, InternalFifoSize>::size_type;
        f_param_r      = m_data[static_cast<idx_type>(l_read_pos)];

        // Assign m_read_p to next readable location
        m_read_pos.store(l_next_read_pos, vfc::MemoryOrderRelease);

        return true;
    }
}

#endif // VFC_FIFO_INL_INCLUDED

//=============================================================================

