//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2023 iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: iMotion Driving Computer
//  Target systems: X86_64, ARM, IMP-X7+
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Xu Ronnie
//  Department: EA2
//=============================================================================

#ifndef VFC_CIRCULAR_SPAN_INL_INCLUDED
#define VFC_CIRCULAR_SPAN_INL_INCLUDED

#include "vfc/core/vfc_assert.hpp"

template <typename ValueType>
inline vfc::TCircularSpan<ValueType>::TCircularSpan()
    : // PRQA S 2628 # Technical debt L2 id=00c16add77d6ef0347e2-ec1789618f6d5caf
      m_span1(),
      m_span2()
{
    // intentionally left blank
}

template <typename ValueType>
inline vfc::TCircularSpan<ValueType>::TCircularSpan(
    const vfc::TCircularSpan<value_type>&
        f_other) // PRQA S 4054 # Technical debt L4 id=00c1f57b12d17ce34354-f02efd0256c20812
{
    m_span1 = f_other.m_span1;
    m_span2 = f_other.m_span2;
}

template <typename ValueType>
inline vfc::TCircularSpan<ValueType>& vfc::TCircularSpan<ValueType>::operator=(
    const vfc::TCircularSpan<value_type>&
        f_other) // PRQA S 4073 # Technical debt L2 id=00c1df7495fe71514b65-01edd32d8b329baf
{
    m_span1 = f_other.m_span1;
    m_span2 = f_other.m_span2;

    return (*this);
}

template <typename ValueType>
inline vfc::TCircularSpan<ValueType>
vfc::TCircularSpan<ValueType>::from_pointer_and_size(value_type* f_begin_p1, size_type f_size1)
{
    VFC_REQUIRE2(0 <= f_size1, "No negative size value allowed.");
    if (nullptr == f_begin_p1)
    {
        VFC_REQUIRE2(0 == f_size1, "Non-empty circular spans with nullptr are not allowed");
        return TCircularSpan();
    }
    return TCircularSpan(
        f_begin_p1,
        f_begin_p1 + f_size1); // PRQA S 3705 # Technical debt L2 id=00c1d862a450534d4ab0-978f387223c681e2
}

template <typename ValueType>
inline vfc::TCircularSpan<ValueType> vfc::TCircularSpan<ValueType>::from_pointer_and_size(
    value_type* f_begin_p1,
    size_type   f_size1,
    value_type* f_begin_p2,
    size_type   f_size2)
{
    VFC_REQUIRE2(0 <= f_size1, "No negative size value allowed.");
    VFC_REQUIRE2(0 <= f_size2, "No negative size value allowed.");
    VFC_REQUIRE2(
        !(0 != f_size2 && 0 == f_size1), "First circular span range may only be empty when second range is also empty");
    VFC_REQUIRE2(
        nullptr != f_begin_p1 || 0 == f_size1, "Non-empty first circular span range with nullptr is not allowed");
    VFC_REQUIRE2(
        nullptr != f_begin_p2 || 0 == f_size2, "Non-empty second circular span range with nullptr is not allowed");
    // avoid pointer arithmetic for empty ranges
    value_type* end_p1 =
        (0 == f_size1
             ? f_begin_p1
             : f_begin_p1 + f_size1); // PRQA S 3705 # Technical debt L2 id=00c1bab537728c4e4848-46dfda10d93a30d3
    value_type* end_p2 =
        (0 == f_size2
             ? f_begin_p2
             : f_begin_p2 + f_size2); // PRQA S 3705 # Technical debt L2 id=00c1c1b28be4e269490b-c9b8eee73d571209
    return TCircularSpan(f_begin_p1, end_p1, f_begin_p2, end_p2);
}

/// private constructor
template <typename ValueType>
inline vfc::TCircularSpan<ValueType>::TCircularSpan(
    value_type* f_begin,
    value_type* f_end) // PRQA S 4054 # Technical debt L4 id=00c180ba8617e1914a96-99fe360c177a165b
{
    VFC_REQUIRE2(f_begin <= f_end, "Wrong order of pointer parameters.");
    vfc::ptrdiff_t const diff =
        f_end - f_begin; // PRQA S 3705 # Technical debt L2 id=00c1cd27621bafb6497d-4623c347c1631155
    m_span1 = TSpan<value_type>::from_pointer_and_size(f_begin, static_cast<size_t>(diff));
    m_span2 = TSpan<value_type>();
}

/// private constructor
template <typename ValueType>
inline vfc::TCircularSpan<ValueType>::TCircularSpan(
    value_type* f_begin1,
    value_type* f_end1,
    value_type* f_begin2,
    value_type* f_end2) // PRQA S 4054 # Technical debt L4 id=00c14370be6e0ce34c93-ac7c59ccbbd9debe
{
    VFC_REQUIRE2(f_begin1 <= f_end1, "Wrong order of pointer parameters.");
    vfc::ptrdiff_t const diff1 =
        f_end1 - f_begin1; // PRQA S 3705 # Technical debt L2 id=00c1b16267526b4845b6-8aa15db3a9e3a770
    m_span1 = TSpan<value_type>::from_pointer_and_size(f_begin1, static_cast<size_t>(diff1));

    VFC_REQUIRE2(f_begin2 <= f_end2, "Wrong order of pointer parameters.");
    vfc::ptrdiff_t const diff2 =
        f_end2 - f_begin2; // PRQA S 3705 # Technical debt L2 id=00c1be344ace186d42f1-e0a054eac6a99a45
    m_span2 = TSpan<value_type>::from_pointer_and_size(f_begin2, static_cast<size_t>(diff2));
}

template <typename ValueType>
inline vfc::TCircularSpan<typename vfc::TAddConst<ValueType>::type> vfc::TCircularSpan<ValueType>::as_const() const
{
    TCircularSpan<typename TAddConst<value_type>::type> retValue;
    retValue.m_span1 = m_span1.as_const();
    retValue.m_span2 = m_span2.as_const();
    return retValue;
}

template <class ValueType>
inline typename vfc::TCircularSpan<ValueType>::iterator vfc::TCircularSpan<ValueType>::begin()
{
    return iterator(this, 0);
}

template <class ValueType>
inline typename vfc::TCircularSpan<ValueType>::const_iterator vfc::TCircularSpan<ValueType>::begin() const
{
    return cbegin();
}

template <class ValueType>
inline typename vfc::TCircularSpan<ValueType>::const_iterator vfc::TCircularSpan<ValueType>::cbegin() const
{
    return const_iterator(this, 0);
}

template <class ValueType>
inline typename vfc::TCircularSpan<ValueType>::iterator vfc::TCircularSpan<ValueType>::end()
{
    return iterator(this, size());
}

template <class ValueType>
inline typename vfc::TCircularSpan<ValueType>::const_iterator vfc::TCircularSpan<ValueType>::end() const
{
    return cend();
}

template <class ValueType>
inline typename vfc::TCircularSpan<ValueType>::const_iterator vfc::TCircularSpan<ValueType>::cend() const
{
    return const_iterator(this, size());
}

template <class ValueType>
inline typename vfc::TCircularSpan<ValueType>::reverse_iterator vfc::TCircularSpan<ValueType>::rbegin()
{
    return static_cast<reverse_iterator>(end());
}

template <class ValueType>
inline typename vfc::TCircularSpan<ValueType>::const_reverse_iterator vfc::TCircularSpan<ValueType>::rbegin() const
{
    return crbegin();
}

template <class ValueType>
inline typename vfc::TCircularSpan<ValueType>::const_reverse_iterator vfc::TCircularSpan<ValueType>::crbegin() const
{
    return static_cast<const_reverse_iterator>(end());
}

template <class ValueType>
inline typename vfc::TCircularSpan<ValueType>::reverse_iterator vfc::TCircularSpan<ValueType>::rend()
{
    return static_cast<reverse_iterator>(begin());
}

template <class ValueType>
inline typename vfc::TCircularSpan<ValueType>::const_reverse_iterator vfc::TCircularSpan<ValueType>::rend() const
{
    return crend();
}

template <class ValueType>
inline typename vfc::TCircularSpan<ValueType>::const_reverse_iterator vfc::TCircularSpan<ValueType>::crend() const
{
    return static_cast<const_reverse_iterator>(begin());
}

template <class ValueType>
inline typename vfc::TCircularSpan<ValueType>::reference vfc::TCircularSpan<ValueType>::back()
{
    VFC_REQUIRE(0 < size());

    if (0 < m_span2.size())
    {
        return *(m_span2.end() - 1); // PRQA S 3705 # Technical debt L2 id=00c19be42d5b9e4848b2-84341aee13e249cd
    }
    return *(m_span1.end() - 1); // PRQA S 3705 # Technical debt L2 id=00c1fe534ad663284c64-7b8b9fcaafb00d6f
}

template <class ValueType>
inline typename vfc::TCircularSpan<ValueType>::const_reference vfc::TCircularSpan<ValueType>::back() const
{
    VFC_REQUIRE(0 < size());

    if (0 < m_span2.size())
    {
        return *(m_span2.end() - 1); // PRQA S 3705 # Technical debt L2 id=00c196db64fbfe984b0a-84341aee13e249cd
    }
    return *(m_span1.end() - 1); // PRQA S 3705 # Technical debt L2 id=00c18e09b852193a4ea3-7b8b9fcaafb00d6f
}

template <class ValueType>
inline typename vfc::TCircularSpan<ValueType>::reference vfc::TCircularSpan<ValueType>::front()
{
    VFC_REQUIRE(0 < size());
    return *(m_span1.begin());
}

template <class ValueType>
inline typename vfc::TCircularSpan<ValueType>::const_reference vfc::TCircularSpan<ValueType>::front() const
{
    VFC_REQUIRE(0 < size());
    return *(m_span1.begin());
}

template <class ValueType>
inline typename vfc::TCircularSpan<ValueType>::reference vfc::TCircularSpan<ValueType>::operator[](size_type f_pos)
{
    if (f_pos < m_span1.size())
    {
        return m_span1[f_pos];
    }
    return m_span2[f_pos - m_span1.size()];
}

template <class ValueType>
inline typename vfc::TCircularSpan<ValueType>::volatile_reference vfc::TCircularSpan<ValueType>::operator[](
    size_type f_pos) volatile // PRQA S 5219 # Technical debt L2 id=00c112825ab36f6748fc-32747a12dc02eb25
{
    if (f_pos < m_span1.size())
    {
        return m_span1[f_pos];
    }
    return m_span2[f_pos - m_span1.size()];
}

template <class ValueType>
inline typename vfc::TCircularSpan<ValueType>::const_reference
vfc::TCircularSpan<ValueType>::operator[](size_type f_pos) const
{
    if (f_pos < m_span1.size())
    {
        return m_span1[f_pos];
    }
    return m_span2[f_pos - m_span1.size()];
}

template <class ValueType>
inline typename vfc::TCircularSpan<ValueType>::const_volatile_reference
vfc::TCircularSpan<ValueType>::operator[](size_type f_pos) const
    volatile // PRQA S 5219 # Technical debt L2 id=00c1aa521a8f15924916-c0ba6ff6a58b9390
{
    if (f_pos < m_span1.size())
    {
        return m_span1[f_pos];
    }
    return m_span2[f_pos - m_span1.size()];
}

template <class ValueType>
inline typename vfc::TCircularSpan<ValueType>::size_type vfc::TCircularSpan<ValueType>::size() const
{
    return m_span1.size() + m_span2.size();
}

template <class ValueType>
inline bool vfc::TCircularSpan<ValueType>::empty() const
{
    return (m_span1.empty());
}

template <typename ValueType, vfc::int32_t CapacityValue, typename AllocatorType>
vfc::TCircularSpan<ValueType>
vfc::as_span(vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_circularBuffer)
{
    if (0 == f_circularBuffer.size())
    {
        return vfc::TCircularSpan<ValueType>();
    }
    ValueType* const storageBase = f_circularBuffer.m_storage.typed_begin();
    ValueType* const firstItemPtr =
        storageBase +
        f_circularBuffer.m_readIndex; // PRQA S 3705 # Technical debt L2 id=00c1c570b43ab0f24d33-a27ee4f2e9eab66e
    // Creates one contiguous memory ranges
    if (f_circularBuffer.m_readIndex < f_circularBuffer.m_writeIndex)
    {
        return vfc::TCircularSpan<ValueType>::from_pointer_and_size(firstItemPtr, f_circularBuffer.m_size);
    }
    ValueType* const endItemPtr =
        storageBase +
        f_circularBuffer.m_writeIndex; // PRQA S 3705 # Technical debt L2 id=00c1641055297ea04fef-0b14371617de2849
    // Creates two contiguous memory ranges
    return vfc::TCircularSpan<ValueType>(
        firstItemPtr,
        storageBase + CapacityValue, // PRQA S 3705 # Technical debt L2 id=00c1670aa347037b4230-eac4ff9626cd9007
        storageBase,
        endItemPtr);
}

template <typename ValueType, vfc::int32_t CapacityValue, typename AllocatorType>
vfc::TCircularSpan<const ValueType>
vfc::as_span(const vfc::TFixedCircularBuffer<ValueType, CapacityValue, AllocatorType>& f_circularBuffer)
{
    if (0 == f_circularBuffer.size())
    {
        return vfc::TCircularSpan<const ValueType>();
    }
    const ValueType* const storageBase = f_circularBuffer.m_storage.typed_begin();
    const ValueType* const firstItemPtr =
        storageBase +
        f_circularBuffer.m_readIndex; // PRQA S 3705 # Technical debt L2 id=00c1a7ead55e084c48e9-e2dc73f8ba20fdb5
    // Creates one contiguous memory ranges
    if (f_circularBuffer.m_readIndex < f_circularBuffer.m_writeIndex)
    {
        return vfc::TCircularSpan<const ValueType>::from_pointer_and_size(firstItemPtr, f_circularBuffer.m_size);
    }
    // Creates two contiguous memory ranges
    const ValueType* const endItemPtr =
        storageBase +
        f_circularBuffer.m_writeIndex; // PRQA S 3705 # Technical debt L2 id=00c17e79001f83184285-619783055af8b342
    return vfc::TCircularSpan<const ValueType>(
        firstItemPtr,
        storageBase + CapacityValue, // PRQA S 3705 # Technical debt L2 id=00c1401579c40e7849fb-795bddf8f7474aba
        storageBase,
        endItemPtr);
}

#endif // VFC_CIRCULAR_SPAN_INL_INCLUDED

