//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
//  Copyright (c) by iMotion AI. All rights reserved.
//=============================================================================
//  D E S C R I P T I O N
//-----------------------------------------------------------------------------
//  Project Name: imo_com
//  Target System(s):
//  Compiler(s):
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//  Name: xiang.li
//=============================================================================
#ifndef IMO_COM_DDK_MONITOR_HPP_
#define IMO_COM_DDK_MONITOR_HPP_

#if !defined(ZX_COMPUTE_TARGET_RPU)
#include <map>
#include <iostream>
#include "ddk_utils.hpp"

namespace imo_com
{
namespace ddk
{

class CDdkMonitor
{
  public:
    static CDdkMonitor& getInstance()
    {
        static CDdkMonitor instance;
        return instance;
    }

    void registerPort(void* f_senderPort, void* f_receiverPort);

    void printPortsInfo();

  private:
    CDdkMonitor() = default;

  private:
    std::map<void*, std::vector<void*>> m_mapPorts;
    CSpinLock                           m_lock;
};

} // namespace ddk
} // namespace imo_com

#endif // !defined(ZX_COMPUTE_TARGET_RPU)

#endif // IMO_COM_DDK_MONITOR_HPP_
