//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
//  Copyright (c) by iMotion AI. All rights reserved.
//=============================================================================
//  D E S C R I P T I O N
//-----------------------------------------------------------------------------
//  Project Name: imo_com
//  Target System(s):
//  Compiler(s):
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//  Name: xiang.li
//=============================================================================
#ifndef IMO_COM_DDK_UTILS_HPP_
#define IMO_COM_DDK_UTILS_HPP_
#include <stdint.h>
#include <stddef.h>
#include <atomic>
#include <array>
#include <list>
#include <vector>
#include <stdarg.h>
#include <string>

#if defined(ZX_COMPUTE_TARGET_RPU)
#define ddk_log(level, format, args...) {}
template<typename type>
static const char* GET_CLASS_NAME()
{
    return "undefined";
}
#else

#include <stdexcept>
#include <cxxabi.h>
#include "zos_log_c.h"
#define unlikely(x) __builtin_expect(!!(x), 0)
#define ddk_log(level, format, args...) ({ zos_log_##level(g_com_logger, "[DDK] " format "\n", ##args); })
template<typename type>
static std::string GET_CLASS_NAME()
{
    char* port_data_type = abi::__cxa_demangle(typeid(type).name(), 0, 0, 0);
    std::string result = port_data_type;
    std::free(port_data_type);
    return result;
}
#endif

#define ddk_log_debug(format, args...) ({ ddk_log(debug, format, ##args); })
#define ddk_log_info(format, args...) ({ ddk_log(info, format, ##args); })
#define ddk_log_warn(format, args...) ({ ddk_log(warn, format, ##args); })
#define ddk_log_error(format, args...) ({ ddk_log(error, format, ##args); })
#define ddk_log_fatal(format, args...) ({ ddk_log(fatal, format, ##args); })

namespace imo_com
{
namespace ddk
{
static void __attribute__((unused)) handleErrorNeverReturn()
{
    while (true)
    {
        // do nothing
    }
}

static void __attribute__((unused)) setError(const char* fmt, ...)
{
    char buffer[256];
    buffer[sizeof(buffer) - 1] = '\0';
    va_list args;
    va_start(args, fmt);
    vsnprintf(buffer, sizeof(buffer) - 1, fmt, args);
    va_end(args);
    ddk_log_fatal("%s", buffer);
#if defined(ZX_COMPUTE_TARGET_RPU)
    handleErrorNeverReturn();
#else
    throw std::runtime_error(buffer);
#endif
}

class CSpinLock
{
  public:
    CSpinLock() {}
    ~CSpinLock() {}

    void lock()
    {
        while (m_lock.test_and_set())
        {
        }
    }
    void release() { m_lock.clear(); }

  private:
    std::atomic_flag m_lock = ATOMIC_FLAG_INIT;
};

template <class ValueType, int32_t CapacityValue>
class TFifo
{
  public:
    enum
    {
        InternalFifoSize = CapacityValue + 1
    };

    TFifo() : m_data(), m_write_pos(0), m_read_pos(0) {}

    bool push(const ValueType& f_param_r)
    {
        const uint32_t l_write_pos      = m_write_pos.load(std::memory_order_relaxed);
        uint32_t       l_next_write_pos = l_write_pos + 1U;
        if (static_cast<uint32_t>(InternalFifoSize) == l_next_write_pos)
        {
            l_next_write_pos = 0U;
        }
        if (l_next_write_pos == m_read_pos.load(std::memory_order_acquire))
        {
            return false;
        }
        else
        {
            m_data[static_cast<int32_t>(l_write_pos)] = f_param_r;
            m_write_pos.store(l_next_write_pos, std::memory_order_release);

            return true;
        }
    }

    bool pop(ValueType& f_param_r)
    {
        const uint32_t l_read_pos  = m_read_pos.load(std::memory_order_relaxed);
        const uint32_t l_write_pos = m_write_pos.load(std::memory_order_acquire);
        if (l_read_pos == l_write_pos)
        {
            return false;
        }
        else
        {
            uint32_t l_next_read_pos = l_read_pos + 1U;
            if (static_cast<uint32_t>(InternalFifoSize) == l_next_read_pos)
            {
                l_next_read_pos = 0U;
            }
            f_param_r = m_data[static_cast<int32_t>(l_read_pos)];
            m_read_pos.store(l_next_read_pos, std::memory_order_release);
            return true;
        }
    }

    bool empty() const
    {
        return m_read_pos.load(std::memory_order_acquire) == m_write_pos.load(std::memory_order_acquire);
    }

  private:
    std::array<ValueType, InternalFifoSize> m_data;
    std::atomic_uint32_t                    m_write_pos;
    std::atomic_uint32_t                    m_read_pos;
};

template <typename ValueType>
class PortDataContainer
{
  public:
    PortDataContainer() { m_value[0] = nullptr; }

    PortDataContainer(const ValueType f_param_r) { m_value[0] = f_param_r; }

    ValueType data() const { return m_value[0]; }

    operator ValueType() const { return m_value[0]; }

    ValueType operator->() const { return m_value[0]; }

  private:
    ValueType m_value[1];
};

template <typename VType, VType Value>
struct TIntegralConstant
{
    using ValueType                  = VType;
    static constexpr ValueType value = Value;
    using type                       = TIntegralConstant<VType, Value>;
};

template <typename VType, VType Value>
constexpr VType TIntegralConstant<VType, Value>::value;

template <class TType>
struct TAlignmentOf : TIntegralConstant<size_t, alignof(TType)>
{
};

template <int32_t Op1Value, int32_t Op2Value>
struct TMax : TIntegralConstant<int32_t, ((Op1Value > Op2Value) ? Op1Value : Op2Value)>
{
};

template <uint32_t ByteAlignment>
struct ParamByteAlign
{
    enum
    {
        ALIGNMENT_IN_BYTE = ByteAlignment
    };
};
enum
{
    default_alignment = TAlignmentOf<void*>::value
};

template <size_t SizeValue, size_t AlignValue>
struct TAlignUp : TIntegralConstant<int32_t, ((SizeValue + (AlignValue - 1U)) & ~(AlignValue - 1U))>
{
};

template <size_t SizeValue, size_t AlignValue = TAlignmentOf<max_align_t>::value>
class TAlignedStorage
{
  public:
    TAlignedStorage() {}

    enum
    {
        ALIGN = AlignValue,
        SIZE  = SizeValue
    };

    size_t size(void) const { return SIZE; }

    void* begin(void) { return static_cast<void*>(m_data); }

    const void* begin(void) const { return static_cast<const void*>(m_data); }

    const void* cbegin(void) const { return static_cast<const void*>(m_data); }

    void* end(void) { return static_cast<void*>(m_data + SIZE); }

    const void* end(void) const { return static_cast<const void*>(m_data + SIZE); }

    const void* cend(void) const { return static_cast<const void*>(m_data + SIZE); }

  private:
    union alignas(AlignValue)
    {
        char m_data[TAlignUp<SizeValue, AlignValue>::value];
    };
};

template <class ValueType, size_t NumItems>
union TAlignStorageHelper
{
    TAlignStorageHelper()                                      = delete;
    TAlignStorageHelper(const TAlignStorageHelper&)            = delete;
    TAlignStorageHelper& operator=(const TAlignStorageHelper&) = delete;
    ~TAlignStorageHelper()                                     = delete;
    static_assert(NumItems > 0, "Items number must not be zero or less!");
    ValueType m_items[NumItems];
};

template <class ValueType>
union TAlignStorageHelper<ValueType, 0>
{
    TAlignStorageHelper()                                      = delete;
    TAlignStorageHelper(const TAlignStorageHelper&)            = delete;
    TAlignStorageHelper& operator=(const TAlignStorageHelper&) = delete;
    ~TAlignStorageHelper()                                     = delete;
};

template <typename ValueType, size_t NumItems, size_t AlignValue = alignof(TAlignStorageHelper<ValueType, NumItems>)>
union alignas(AlignValue) TAlignedTypeStorage
{
  public:
    enum
    {
        ALIGN = AlignValue,
        NUM   = NumItems,
        SIZE  = NumItems * sizeof(ValueType),
    };

    size_t size(void) const { return SIZE; }

    void* begin(void) { return typed_begin(); }

    const void* begin(void) const { return typed_begin(); }

    const void* cbegin(void) const { return typed_begin(); }

    void* end(void) { return typed_begin() + NUM; }

    const void* end(void) const { return typed_begin() + NUM; }

    const void* cend(void) const { return typed_begin() + NUM; }

    ValueType* typed_begin() { return &m_values[0]; }

    const ValueType* typed_begin() const { return &m_values[0]; }

    ValueType* typed_end() { return typed_begin() + NUM; }

    const ValueType* typed_end() const { return typed_begin() + NUM; }

  private:
    ValueType m_values[NUM];
};

template <class ValueType, int32_t CapacityValue>
class TFixedCircularBuffer
{
  public:
    TFixedCircularBuffer() : m_storage(), m_readIndex(0), m_writeIndex(0), m_size(0) {}

    int32_t size() const { return m_size; }

    int32_t capacity() const { return CapacityValue; }

    bool empty() const { return 0 == m_size; }

    ValueType& front()
    {
        ValueType* const storageBase = m_storage.typed_begin();
        return storageBase[m_readIndex];
    }

    const ValueType& front() const
    {
        const ValueType* const storageBase = m_storage.typed_begin();
        return storageBase[m_readIndex];
    }

    void push(const ValueType& f_item_r) { this->push_intern(&f_item_r); }

    void pop()
    {
        ValueType* const storageBase  = m_storage.typed_begin();
        ValueType* const destructItem = &storageBase[m_readIndex];
        destructItem->~ValueType();
        --m_size;
        m_readIndex = incrementIndex(m_readIndex);
    }

    ValueType& operator[](int32_t f_index)
    {
        ValueType* const storageBase = m_storage.typed_begin();
        int32_t          readIndex   = m_readIndex + f_index;
        if (capacity() <= readIndex)
        {
            readIndex -= capacity();
        }
        return storageBase[readIndex];
    }

    void clear()
    {
        const int32_t size = m_size;
        for (int32_t i = 0; i < size; ++i)
        {
            this->pop();
        }
    }

    void push_intern(const ValueType* f_item_p)
    {
        ValueType* const storageBase = m_storage.typed_begin();
        ValueType* const item        = &storageBase[m_writeIndex];
        if (capacity() == m_size)
        {
            item->~ValueType();
        }

        if (nullptr != f_item_p)
        {
            new (item) ValueType(*f_item_p);
        }

        if (true == IsOverFlow())
        {
            m_readIndex = incrementIndex(m_readIndex);
        }
        else
        {
            ++m_size;
        }

        m_writeIndex = incrementIndex(m_writeIndex);
    }

    bool IsOverFlow() const
    {
        if (m_size + 1 > capacity())
            return true;
        return false;
    }

    int32_t incrementIndex(int32_t f_index)
    {
        if (f_index == capacity() - 1)
        {
            return 0;
        }
        return f_index + 1;
    }

  private:
    TAlignedTypeStorage<ValueType, CapacityValue> m_storage;
    int32_t                                       m_readIndex;
    int32_t                                       m_writeIndex;
    int32_t                                       m_size;
};

} // namespace ddk
} // namespace imo_com

#endif // IMO_COM_DDK_UTILS_HPP_
