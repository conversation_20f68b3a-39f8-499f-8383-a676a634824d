//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
//  Copyright (c) by iMotion AI. All rights reserved.
//=============================================================================
//  D E S C R I P T I O N
//-----------------------------------------------------------------------------
//  Project Name: imo_com
//  Target System(s):
//  Compiler(s):
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//  Name: xiang.li
//=============================================================================
#ifndef IMO_COM_DDK_SENDERPORT_BASE_HPP_
#define IMO_COM_DDK_SENDERPORT_BASE_HPP_
#include "ddk_mempool.hpp"
#include "ddk_porttype.hpp"

namespace imo_com
{
namespace ddk
{
class CSenderPortBase
{
  public:
    CSenderPortBase() : m_lastDelivery_p(nullptr), m_pool_p(nullptr) {}

    virtual ~CSenderPortBase() {}

    inline void setLastDelivery(const void* currentStorage) { m_lastDelivery_p = currentStorage; }

    inline const void* getLastDelivery() const { return m_lastDelivery_p; }

    inline void setPortName(const std::string &port_name) { m_portType.setPortName(port_name); }

    inline const char *getPortName() const { return m_portType.c_str(); }

    inline void setCoolFwdID(uint32_t fwdID) { m_coolFwdID = fwdID; }

    inline uint32_t getCoolFwdID() const { return m_coolFwdID; }

    inline void setPortID(uint32_t portID) { m_portID = portID; }

    inline uint32_t getPortID() { return m_portID; }

    void connectMemPool(CChangeableMemPoolBase* pool_p)
    {
        if (m_pool_p)
        {
            ddk_log_error(
                "mempool already connected, please check duplicated bind mempool, port name: %s", m_portType.c_str());
            return;
        }
        m_pool_p = pool_p;
    }

    inline CChangeableMemPoolBase* getMemPool() const { return m_pool_p; }

    void* getChunk()
    {
        void* chunk = static_cast<void*>(m_pool_p->getChunk());
        if (nullptr == chunk)
        {
            setError("getChunk is nullptr, FwdID=%ld, Port=%s", getCoolFwdID(), m_portType.c_str());
        }
        return chunk;
    }

    void* cloneChunk(void* const chunk2Clone_p) { return static_cast<void*>(m_pool_p->cloneChunk(chunk2Clone_p)); }

  private:
    CDdkPortType            m_portType;
    const void*             m_lastDelivery_p;
    CChangeableMemPoolBase* m_pool_p;
    uint32_t                m_coolFwdID{0xFFFFFFFF};
    uint32_t                m_portID{0xFFFFFFFF};
};

} // namespace ddk
} // namespace imo_com

#endif // IMO_COM_DDK_SENDERPORT_BASE_HPP_
