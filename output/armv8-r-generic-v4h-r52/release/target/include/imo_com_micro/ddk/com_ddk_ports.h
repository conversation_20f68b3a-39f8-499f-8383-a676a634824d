//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
//  Copyright (c) by iMotion AI. All rights reserved.
//=============================================================================
//  D E S C R I P T I O N
//-----------------------------------------------------------------------------
//  Project Name: imo_com
//  Target System(s):
//  Compiler(s):
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//  Name: xiang.li
//=============================================================================
#ifndef IMO_COM_DDK_COM_DDK_PORTS_HPP_
#define IMO_COM_DDK_COM_DDK_PORTS_HPP_
#include "detail/ddk_senderport.hpp"
#include "detail/ddk_receiverport.hpp"

namespace imo_com
{
namespace ddk
{
template <
    typename PortDataType,
    uint16_t   MemPoolSize = 0U,
    EAllocType AllocType   = EAllocType_Normal,
    typename               = typename std::enable_if<
                      (EAllocType_Normal == AllocType) ||
                      (std::is_trivially_copyable<PortDataType>::value || std::is_standard_layout<PortDataType>::value)>::type>
class TSenderPort : public TSenderPortBase<PortDataType>
{
  public:
    TSenderPort() : TSenderPortBase<PortDataType>(), m_memPool(0U) { this->connectMemPool(m_memPool); }

  private:
    TMemPool<PortDataType, MemPoolSize, AllocType> m_memPool;
};

template <typename PortDataType>
class TSenderPort<PortDataType> : public TSenderPortBase<PortDataType>
{
  public:
    TSenderPort() : TSenderPortBase<PortDataType>() {}
};

} // namespace ddk
} // namespace imo_com

#endif // IMO_COM_DDK_COM_DDK_PORTS_HPP_
