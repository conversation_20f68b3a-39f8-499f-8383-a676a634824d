//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
//  Copyright (c) by iMotion AI. All rights reserved.
//=============================================================================
//  D E S C R I P T I O N
//-----------------------------------------------------------------------------
//  Project Name: imo_com
//  Target System(s):
//  Compiler(s):
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//  Name: xiang.li
//=============================================================================
#ifndef IMO_COM_DDK_COM_DDK_WRAPPER_H
#define IMO_COM_DDK_COM_DDK_WRAPPER_H
#include "detail/ddk_senderport.hpp"
#include "detail/ddk_receiverport.hpp"
#include "com_ddk_ports.h"
#include "com_rtps_cpp.h"

namespace imo_com
{
namespace ddk
{
template <
    typename PortDataType,
    uint16_t MemPoolSize = 0U,
    typename             = typename std::enable_if<(
        std::is_trivially_copyable<PortDataType>::value || std::is_standard_layout<PortDataType>::value)>::type>
class TProcessInterSenderPort : public TSenderPortBase<PortDataType>
{
  public:
    TProcessInterSenderPort(uint32_t measurementID = 0)
        : TSenderPortBase<PortDataType>(),
          m_measurementID(measurementID),
          m_memPoolPtr(nullptr),
          m_nodePtr(nullptr),
          m_puberPtr(nullptr)
    {
    }

    bool bindNodeAndTopic(const std::string& node_name, const std::string& topic_name)
    {
        m_memPoolPtr = std::make_shared<TMemPool<PortDataType, MemPoolSize, EAllocType_FixedCool>>(m_measurementID);
        this->connectMemPool(*m_memPoolPtr);

        m_nodePtr = std::make_shared<imo_com::Node>(node_name);
        // 检查Node是否注册成功
        if (!m_nodePtr->operator bool())
        {
            ddk_log_fatal("Node register failed. node_name: %s, topic_name: %s", node_name.c_str(), topic_name.c_str());
            return false;
        }
        // 注册进程间通信的发布者，入参为话题名称
        m_puberPtr = m_nodePtr->NewPublisher<PortDataType>(topic_name);
        if (nullptr == m_puberPtr)
        {
            ddk_log_fatal(
                "Publisher register failed. node_name: %s, topic_name: %s", node_name.c_str(), topic_name.c_str());
            return false;
        }
        return true;
    }

    void deliver()
    {
        if (m_puberPtr)
        {
            auto* data = TSenderPortBase<PortDataType>::getReserveData();
            m_puberPtr->Deliver(*data, com_osal_get_boot_time_us());
        }
        TSenderPortBase<PortDataType>::deliver();
    }

  private:
    uint32_t                                                                   m_measurementID;
    std::shared_ptr<TMemPool<PortDataType, MemPoolSize, EAllocType_FixedCool>> m_memPoolPtr;
    imo_com::Node::SharedPtr                                                   m_nodePtr;
    imo_com::Publisher::SharedPtr                                              m_puberPtr;
};

template <
    typename PortDataType,
    typename = typename std::enable_if<(
        std::is_trivially_copyable<PortDataType>::value || std::is_standard_layout<PortDataType>::value)>::type>
class TProcessInterForwardSenderPort : public TSenderPortBase<PortDataType>
{
  public:
    TProcessInterForwardSenderPort(uint32_t measurementID = 0)
        : TSenderPortBase<PortDataType>(), m_memPool(measurementID), m_nodePtr(nullptr), m_suberPtr(nullptr)
    {
        this->connectMemPool(m_memPool);
    }

    bool bindNodeAndTopic(const std::string& node_name, const std::string& topic_name)
    {
        m_nodePtr = std::make_shared<imo_com::Node>(node_name);
        if (!m_nodePtr->operator bool())
        {
            ddk_log_fatal("Node register failed. node_name: %s, topic_name: %s", node_name.c_str(), topic_name.c_str());
            return false;
        }
        m_suberPtr = m_nodePtr->NewSubscription<PortDataType>(
            topic_name,
            std::bind(
                &TProcessInterForwardSenderPort<PortDataType>::sub_cbk,
                this,
                std::placeholders::_1,
                std::placeholders::_2));
        if (nullptr == m_suberPtr)
        {
            ddk_log_fatal(
                "Subscriber register failed. node_name: %s, topic_name: %s", node_name.c_str(), topic_name.c_str());
            return false;
        }
        return true;
    }

    void sub_cbk(std::shared_ptr<PortDataType const>& msg, imo_com::MsgMetaIface* info)
    {
        TSenderPortBase<PortDataType>::deliver(msg.get());
    }

  private:
    TMemPool<PortDataType, 0, EAllocType_FixedCool_Forwarder> m_memPool;
    imo_com::Node::SharedPtr                                  m_nodePtr;
    imo_com::Subscription::SharedPtr                          m_suberPtr;
};

} // namespace ddk
} // namespace imo_com

#endif // #ifndef IMO_COM_DDK_COM_DDK_WRAPPER_H
