#ifndef	__USS_PFENV_H__
#define	__USS_PFENV_H__

#include "stdint.h"
#ifdef __cplusplus
extern "C" 
{
#endif
typedef const char *pchar; 
extern void Log_FunSync(pchar FuncName, int Line, pchar fmt, ...);
#define imoLogInfo(...) Log_FunSync(__func__, __LINE__, ##__VA_ARGS__);

typedef enum
{
    BSW_INTERFACE_SPI_CHANNEL_1_enm = 0,
    BSW_INTERFACE_SPI_CHANNEL_2_enm,
    BSW_INTERFACE_SPI_CHANNEL_3_enm,
    BSW_INTERFACE_SPI_CHANNEL_MAX_enm

} imo_Type_bsw_mp_interface_Spi_Channel_en;

typedef enum
{
    // Master1 RESB
    BSW_INTERFACE_MASTER1_RESET_enm = 0,
    BSW_INTERFACE_MASTER1_DCR1_enm,
    BSW_INTERFACE_MASTER1_DCR2_enm,

    // Master2 RESB
    BSW_INTERFACE_MASTER2_RESET_enm,
    BSW_INTERFACE_MASTER2_DCR1_enm,
    BSW_INTERFACE_MASTER2_DCR2_enm,

    // Master3 RESB
    BSW_INTERFACE_MASTER3_RESET_enm,
    BSW_INTERFACE_MASTER3_DCR1_enm,
    BSW_INTERFACE_MASTER3_DCR2_enm,

    // Side Power
    BSW_INTERFACE_MASTER_POWER1_enm,
    // Frone Power
    BSW_INTERFACE_MASTER_POWER2_enm,
    // Rear Power
    BSW_INTERFACE_MASTER_POWER3_enm,
    BSW_INTERFACE_TPS4H160_5_ST1,
    BSW_INTERFACE_TPS4H160_5_ST2,
    BSW_INTERFACE_TPS4H160_5_ST3,
    BSW_INTERFACE_MON_PWR_USS_1,
    BSW_INTERFACE_MON_PWR_USS_2,
    BSW_INTERFACE_MON_PWR_USS_3,

    BSW_INTERFACE_IOPIN_MAX,

} imo_Type_bsw_mp_interface_IOPin_en;

typedef enum
{
    BSW_INTERFACE_IO_PIN_UNKNOWN_enm = 0,
    BSW_INTERFACE_IO_PIN_LOW_enm,
    BSW_INTERFACE_IO_PIN_HIGH_enm

} imo_Type_bsw_mp_interface_IOPin_Level_en;

typedef struct 
{
    uint32_t sec;
    uint32_t nsec;
} imo_Type_Rtc_BinaryTime;

typedef enum 
{
    GEARBOX_NONE      = 0,
    GEARBOX_MANUAL    = 1,
    GEARBOX_AUTOMATIC = 2,
    GEARBOX_ERROR     = 255
} EVHM_GearBoxType;

typedef enum 
{
    GEAR_PARK     = 0,
    GEAR_REVERSE  = 1,
    GEAR_NEUTRAL  = 2,
    GEAR_DRIVE    = 3,
    GEAR_NOSIGNAL = 4,
} EVHM_GearStatus;

typedef enum 
{
    FWA_HIGH_PRECISION = 0,
    FWA_LOW_PRECISION  = 1,
    FWA_NOT_INIT       = 2
} EVHM_FrontWheelAngleStatus;
 
// GPS Rx Signals
typedef enum 
{
    NO_FIX = 0,
    FIX_2D = 1,
    FIX_3D = 2
} EVHM_PosFix;

typedef enum 
{
    WHEEL_DRV_DIR_FWD     = 0,
    WHEEL_DRV_DIR_BWD     = 1,
    WHEEL_DRV_DIR_UNKNOWN = 2,
    WHEEL_DRV_DIR_STOP    = 3
} EVHM_WheelDrvDir;
 
typedef enum 
{
    Q_WHEEL_ROTATION_VALID   = 0,
    Q_WHEEL_ROTATION_INVALID = 15
} EVHM_WheelRotationQualifier;

typedef enum 
{
    Q_LATERAL_ACC_VALID   = 0,
    Q_LATERAL_ACC_INVALID = 15
} EVHM_LateralAccQualifier;

typedef enum 
{
    Q_YAW_VELOCITY_VEH_VALID   = 0,
    Q_YAW_VELOCITY_VEH_INVALID = 15
} EVHM_QualifierYawVelocityVehicle;

typedef enum 
{
    Q_LONG_ACC_VALID   = 0,
    Q_LONG_ACC_INVALID = 15
} EVHM_LongAccQualifier;

typedef enum 
{
    ABS_ASR_ESP_CTRL_ACTIVE   = 0,
    ABS_ASR_ESP_CTRL_INACTIVE = 1
} EVHM_EspOpMode;

typedef enum 
{
    PRKG_BRK_NOTAPPLIED,
    PRKG_BRK_APPLIED_STATIC,
    PRKG_BRK_APPLIED_DYNAMIC
}EVHM_PrkgBrkState;

typedef enum 
{
    ENGINE_NO_START    = 0,
    ENGINE_FIRST_START = 1,
    ENGINE_RESTART     = 2,
    ENGINE_START_SNA   = 255
} EVHM_EngineStart;

typedef enum 
{
    REAR_STEERING_ANGLE_INVALID = 0,
    REAR_STEERING_ANGLE_VALID   = 1,
    REAR_STEERING_ANGLE_ERR     = 2
} EVHM_QualifierRearAngle;
 

typedef struct 
{
    EVHM_GearBoxType m_gearBoxType;
    EVHM_GearStatus  m_gearStatus; // active gear
} CVHM_PfGear;

typedef enum 
{
    STEERING_ANGLE_VALID = 0,
    STEERING_ANGLE_INVALID  = 15,
} EVHM_QualifierSteeringAngle;

typedef enum 
{
    EPS_NOT_PRESENT,
    EPS_INACTIVE,
    EPS_READY,
    EPS_ACTIVE
} EVHM_EpsStatus;

typedef struct 
{
    float m_vehicleVelocity;//  m/s
    float m_longitudinalAcceleration;
    float m_lateralAcceleration;
    float m_yawRate;
    uint32_t  m_wheelImpCtrFLTimestamp;
    uint32_t  m_wheelImpCtrFRTimestamp;
    uint32_t  m_wheelImpCtrRLTimestamp;
    uint32_t  m_wheelImpCtrRRTimestamp;
    uint16_t  m_wheelImpCtrFL;
    uint16_t  m_wheelImpCtrFR;
    uint16_t  m_wheelImpCtrRL;
    uint16_t  m_wheelImpCtrRR;

    EVHM_WheelDrvDir                 m_wheelDrvDirFL;
    EVHM_WheelDrvDir                 m_wheelDrvDirFR;
    EVHM_WheelDrvDir                 m_wheelDrvDirRL;
    EVHM_WheelDrvDir                 m_wheelDrvDirRR;
    EVHM_WheelRotationQualifier      m_wheelRotationFLQualifier;
    EVHM_WheelRotationQualifier      m_wheelRotationFRQualifier;
    EVHM_WheelRotationQualifier      m_wheelRotationRLQualifier;
    EVHM_WheelRotationQualifier      m_wheelRotationRRQualifier;
    EVHM_LateralAccQualifier         m_lateralAccelerationQualifier;
    EVHM_LongAccQualifier            m_longitudinalAccelerationQualifier;
    EVHM_QualifierYawVelocityVehicle m_qualifierYawVelocityVehicle;

} CVHM_PfOdometry;

typedef enum 
{
VEHICLE_STOP_STATE_NOT_DETECTED = 0,
VEHICLE_STOP_STATE_DETECTED = 1,
VEHICLE_STOP_STATE_NOT_UNKNOWN = 15
} EVHM_VehicleStopState;

typedef struct 
{
    uint16_t  m_vehSpeed; //0.1km/h
    float m_steeringWheelAngle;
    uint16_t  m_wheelRotationFL;
    uint16_t  m_wheelRotationFR;
    uint16_t  m_wheelRotationRL;
    uint16_t  m_wheelRotationRR;
    float m_wheelSpeedFL;
    float m_wheelSpeedFR;
    float m_wheelSpeedRL;
    float m_wheelSpeedRR;
    int8_t m_outsideTemp;
    EVHM_VehicleStopState m_vehicleStopState;
} CVHM_PfPmaFixedPoint;

typedef struct 
{
    float m_frontWheelAngle;
    float m_frontWheelAngleOffset;
    float m_steeringWheelAngle;
    float m_rearAxleSteeringAngle;

    uint8_t                m_frontWheelAngleValid;
    uint8_t                m_frontWheelAngleOffsetValid;
    EVHM_QualifierSteeringAngle m_qualifierSteeringAngle;
    EVHM_FrontWheelAngleStatus m_frontWheelAngleStatus;
    float             m_steerWhlTrqHys;
    EVHM_EpsStatus          m_epsStatus;//TODO
    uint32_t              m_frontWheelAngleTimestamp;
    EVHM_QualifierRearAngle m_rearWheelAngleStatus;
} CVHM_PfSteering;

typedef struct 
{
    float m_gnssAltitude;
    float m_gnssHeading;
    int32_t  m_gnssLongitude;
    int32_t  m_gnssLatitude;
    int32_t  m_gnssLongitudeDeadReck;
    int32_t  m_gnssLatitudeDeadReck;
    float m_gnssVelOvrGrnd;
    int32_t  m_gnssSampleRate;
    uint8_t   m_gnssVsblSat;
    uint8_t   m_gnssTrackedSat;
    float m_gnssVdop;
    float m_gnssHdop;
    float m_gnssPdop;
    EVHM_PosFix m_gnssPosFix;
} CVHM_PfGnssLocalization;

typedef struct 
{
    float m_tirePressFL;
    float m_tirePressFR;
    float m_tirePressRL;
    float m_tirePressRR;
} CVHM_PfTirePress;

typedef struct 
{
    EVHM_EspOpMode m_espOpMode;
    EVHM_PrkgBrkState m_prkgBrkState;
} CVHM_PfBrakes;
typedef struct 
{

    EVHM_EngineStart m_engineStart;

} CVHM_PfVehicleInfo;

typedef struct 
{
    CVHM_PfGear             m_pfGear;
    CVHM_PfOdometry         m_pfOdometry;
    CVHM_PfPmaFixedPoint    m_pfPmaFixedPoint;
    CVHM_PfSteering         m_pfSteering;
    CVHM_PfVehicleInfo      m_pfVehicleInfo;
    CVHM_PfGnssLocalization m_pfGnssLocalization;
    CVHM_PfTirePress        m_pfTirePress;
    CVHM_PfBrakes           m_pfBrakes;

    uint64_t m_senderCallTimestamp;
}CVHM_ValInOutputPf;


extern CVHM_ValInOutputPf vhm_valinOutputPf;

/* get rtc tick */
extern uint8_t imo_bsw_mp_interface_Rtc_ReadBinTime(uint8_t Instance, imo_Type_Rtc_BinaryTime * pRtcBinTime);

/* get os tick 1us*/
extern uint64_t imo_bsw_mp_interface_1usOsTickTime();

/* enable timer*/
extern void imo_bsw_mp_interface_VariableTimerEnable_vd(void);

/* disable timer*/
extern void imo_bsw_mp_interface_VariableTimerDisable_vd(void);

/* set timer value us*/
extern void imo_bsw_mp_interface_SetVariableTimer_vd(uint16_t f_SetVal_u16);

/* Set IO pin power/resb,etc*/
extern uint8_t imo_bsw_mp_interface_SetIOPinLevel_b(imo_Type_bsw_mp_interface_IOPin_en f_IOPin_en,
                                             imo_Type_bsw_mp_interface_IOPin_Level_en f_Level_en);

/* set cs pin*/
extern void imo_bsw_mp_interface_SetSpixCSPin_vd(imo_Type_bsw_mp_interface_Spi_Channel_en f_Spix_en,
                                          imo_Type_bsw_mp_interface_IOPin_Level_en f_PinLevel_en);

/* set spi data*/
extern uint8_t imo_bsw_mp_interface_SpiTX_Send_b(imo_Type_bsw_mp_interface_Spi_Channel_en f_SpiChannel_en,
                                          uint8_t * f_SpiTxBuffer_pu8,uint8_t * f_SpiRxBuffer_pu8,uint8_t f_Length_u8);



// HCT Call the function
/* sensl init */
extern void imo_mp_bsw_uss_init_vd(void);

/* 1ms task*/      
extern void imo_mp_bsw_interface_1msTask_vd(void);

/* apt task*/  
extern void imo_mp_bsw_interface_AptTask_vd(void);

/* 10ms task*/   
extern void imo_mp_bsw_interface_10msTask_vd(void);

/* 100ms task*/   
extern void imo_mp_bsw_interface_100msTask_vd(void);

/* timer interupt callback*/
extern void imo_mp_bsw_interface_VariableTimer_vd(void);

/* master1 DCR1 interupt callback*/
extern void imo_mp_bsw_interface_Master1_DCR1ISR_vd(void);

/* master1 DCR2 interupt callback*/
extern void imo_mp_bsw_interface_Master1_DCR2ISR_vd(void);

/* master2 DCR1 interupt callback*/
extern void imo_mp_bsw_interface_Master2_DCR1ISR_vd(void);

/* master2 DCR2 interupt callback*/
extern void imo_mp_bsw_interface_Master2_DCR2ISR_vd(void);

/* master3 DCR1 interupt callback*/
extern void imo_mp_bsw_interface_Master3_DCR1ISR_vd(void);

/* master3 DCR2 interupt callback*/
extern void imo_mp_bsw_interface_Master3_DCR2ISR_vd(void);

/* SPI1 Tx complete interupt callback*/
extern void imo_mp_bsw_interface_Spi1RX_CompleteISR_vd(void);

/* SPI2 Tx complete interupt callback*/
extern void imo_mp_bsw_interface_Spi2RX_CompleteISR_vd(void);

/* SPI3 Tx complete interupt callback*/
extern void imo_mp_bsw_interface_Spi3RX_CompleteISR_vd(void);

#ifdef __cplusplus
}
#endif

#endif /*__imo_MP_INTERFACE_HDL_H__*/	


/*========================= EoF (mp_interface_hdl.h) =========================*/
