#ifndef	__USS_PERIPHERAL_DRIVER_H__
#define	__USS_PERIPHERAL_DRIVER_H__

#include "stdint.h"
#include "Spi.h"
#include "Spi_Cfg.h"

#include "Gpt.h"
#include "Gpt_Cfg.h"
#include "Gpt_PBcfg.h"

#ifndef NULL
#define NULL ((void*)0)
#endif

#define PRINT_FLAG_CNT  32

extern uint8_t m_flagCnt;
extern uint32_t m_flag[PRINT_FLAG_CNT];


/** GPIO */

#define DIO_E52142A_DCR1B   DioName_DioChannel_J6E_E52142A_DCR1B
#define DIO_E52142A_DCR2B   DioName_DioChannel_J6E_E52142A_DCR2B

#define DIO_E52142B_DCR1B   DioName_DioChannel_J6E_E52142B_DCR1B
#define DIO_E52142B_DCR2B   DioName_DioChannel_J6E_E52142B_DCR2B

#define DIO_PWR_USS_1       DioName_DioChannel_J6E_BOOT_MD9_TPS4H160_5_IN1 
#define DIO_PWR_USS_2       DioName_DioChannel_J6E_BOOT_MD8_TPS4H160_5_IN2   
#define DIO_PWR_USS_3       DioName_DioChannel_J6E_TPS4H160_5_IN3

#define DIO_MON_PWR_USS_1   DioName_DioChannel_J6E_MON_PWR_USS_1
#define DIO_MON_PWR_USS_2   DioName_DioChannel_J6E_MON_PWR_USS_2 
#define DIO_MON_PWR_USS_3   DioName_DioChannel_J6E_MON_PWR_USS_3


extern void j6e_uss_gpio_init(void);

/** SPI2 &SPI5 */

#define E52142_MAX_CHANNEL      2

#define E52142A_SPI_CHANNEL     SpiConf_SpiChannel_0
#define E52142B_SPI_CHANNEL     SpiConf_SpiChannel_2

#define E52142A_SPI_SEQUENCE    SpiConf_SpiSequence_0
#define E52142B_SPI_SEQUENCE    SpiConf_SpiSequence_2


/* TCA6408 */

#define TCA6408_IIC_ADDR (0x20)


typedef enum
{
    E52142A_RFC_PIN_INDEX   = 0,
    E52142A_RESB_PIN_INDEX  = 1,
    E52142B_RFC_PIN_INDEX,
    E52142B_RESB_PIN_INDEX,
    TPS4H160_5_ST1_PIN_INDEX,
    TPS4H160_5_ST2_PIN_INDEX,
    TPS4H160_5_ST3_PIN_INDEX,
    TPS4H160_5_DIAG_EN_PIN_INDEX,
    TCA6408_PIN_MAX,
}TCA6408_PIN_ENM;

#define TCA6408_READ_REG        (0x00)
#define TCA6408_WRITE_REG       (0x01)
#define TCA6408_INVERSION_REG   (0x02)
#define TCA6408_CONFIG_REG      (0x03)

#define TCA6408_INPUT           (1)
#define TCA6408_OUTPUT          (0)
#define TCA6408_CONFIG_VAL      (0x75)

extern void tca6408_init(void);
extern uint8_t tca6408_read_pin(TCA6408_PIN_ENM p_pinIdx_enm);
extern void tca6408_write_pin(TCA6408_PIN_ENM p_pinIdx_enm , uint8_t p_pinLvl_u8);
extern uint64_t imo_bsw_mp_interface_1usOsTickTime(void);
extern void imo_uss_1us_timer_init(void);
extern void imo_uss_1us_timer_cb(void);

/** GPT */

#define USS_TIMER_CHANNEL   GptConf_GptChannelConfiguration_GptChannelConfiguration_5
#define USS_TIMER_IRS_SOURCE    87
#define USS_1US_TIMER_CHANNEL   GptConf_GptChannelConfiguration_GptChannelConfiguration_6
#define USS_1US_TIMER_IRS_SOURCE    88

#endif /*__USS_PERIPHERAL_DRIVER_H__*/	


/*========================= EoF (mp_interface_hdl.h) =========================*/
