#ifndef	__USS_PFAPP_H__
#define	__USS_PFAPP_H__

#include "stdint.h"

#ifdef __cplusplus
extern "C" 
{
#endif

#define  imo_ROD_MEASOBJLISTSIZE  (uint8_t)(17)
#define  imo_ECHO_SENSORS_NUM     (uint8_t)(12)
#define  imo_ECHO_MAX_NUM         (uint8_t)(8)
#define  imo_SPL_NumPSSides_du8   (uint8_t)(2)
#define  imo_SPL_PSRM_SIZE_du8    (uint8_t)(2)
#define  imo_MAP_SIZE_OUT (uint8_t)(80)
/* PDC */
typedef struct
{
    // unit : mm
    // right rear side
    uint16_t IDM_PDC_RHSRSensorDis_Dis_u16;
    // left  rear side
    uint16_t IDM_PDC_LHSRSensorDis_Dis_u16;
    // right front side 
    uint16_t IDM_PDC_RHSFSensorDis_Dis_u16;
    // left  front side 
    uint16_t IDM_PDC_LHSFSensorDis_Dis_u16;
    
    // right  rear
    uint16_t IDM_PDC_RHRSSensorDis_Dis_u16;
    // left   rear
    uint16_t IDM_PDC_LHRSSensorDis_Dis_u16;
    // ringht front
    uint16_t IDM_PDC_RHFSSensorDis_Dis_u16;
    // left   front
    uint16_t IDM_PDC_LHFSSensorDis_Dis_u16;
    
    // right middle rear
    uint16_t IDM_PDC_RHMRSensorDis_Dis_u16;
    // left  middle rear
    uint16_t IDM_PDC_LHMRSensorDis_Dis_u16;
    // right middle front
    uint16_t IDM_PDC_RHMFSensorDis_Dis_u16;
    // left  middle front
    uint16_t IDM_PDC_LHMFSensorDis_Dis_u16;
    
    // rear radar warn beep rate
    uint8_t  IDM_PDC_WarnBpRate_R_u8;
    // front radar warn beep rate
    uint8_t  IDM_PDC_WarnBpRate_F_u8;
    
    // PDC work state
    uint8_t  IDM_PDC_SysSts_u8;
    
    // USS detect state
    uint8_t  IDM_PDC_DetecSts_u8;

    // reserve
    uint32_t IDM_PDC_Reserve1;
} imo_PDCOutput_st;

/* Object Location*/
/**
 * Point or Vector in a Euclidean 2D space.
 */
typedef struct
{
    /**
     * !< x-coordinate or x-component of vector
     */
    int16_t X_s16;
    /**
     * !< y-coordinate or y-component of vector
     */
    int16_t Y_s16;
} imo_Point_st;

/**
ROD object position flags
*/
typedef struct
{

    uint8_t  reserved_u8;
    uint8_t  VehInside_u8; /**< indicates that ROD object is at least partly located inside vehicle contour  */
    uint8_t  VehRight_u8;  /**< indicates that ROD object has a location point at vehicle right side  */
    uint8_t  VehLeft_u8;   /**< indicates that ROD object has a location point at vehicle left side   */
    uint8_t  VehRear_u8;   /**< indicates that ROD object has a location point at vehicle rear side   */
    uint8_t  VehFront_u8;  /**< indicates that ROD object has a location point at vehicle front side  */
} imo_rod_ObjectPositionFlags_st;
// object type
typedef enum
{
    imo_NONE_enm       = 0, // Object Type: no object
    imo_DE_enm         = 1, // Object Type: Direct-Echo Object
    imo_DE_DE_enm      = 2, // Object Type: Double Direct-Echo Object
    imo_CE_enm         = 3, // Object Type: Cross-Echo Object
    imo_DE_CE_enm      = 4, // Object Type: Direct-Echo-Cross-Echo Object
    imo_ROUND_POST_enm = 5, // Object Type: Round Post
    imo_WALL_enm       = 6  // Object Type: Wall
} imo_rod_ObjType_en;
/**
 * A line segment described by two points. Points can have arbitrary scaling (cm, mm, 2^-10 m)
 */
typedef struct
{
    /**
     * < Position (coordinates)
     */
    imo_Point_st P1_st;
    /**
     * < Position (coordinates)
     */
    imo_Point_st P2_st;
} imo_LineSegment_st;
// sensor source of measurement object (tx, rx)
typedef struct
{
    uint8_t TxSensorID_u8; /**< tx sensor (id-1) of measurement object [0...15] */
    uint8_t RxSensorID_u8; /**< rx sensor (id-1) of measurement object [0...15]*/
} imo_rod_SensorSource_st;
// rod measurement object data type
typedef struct
{
    // measurement object coordinates (P1, P2)
    imo_LineSegment_st Coords_st;
    // left direct echo which leads to measurement object
    uint16_t DETx_u16;
    // right direct echo which leads to measurement object
    uint16_t DERx_u16;
    // cross echo which leads to measurement object
    uint16_t CE_u16;
    // sensor source of measurement object (tx, rx)
    imo_rod_SensorSource_st SensorSource_st;
    // type of measurement object
    imo_rod_ObjType_en measObjType_en;
} imo_rod_measObjectMainInfo_st;
/* Height Information of rod measurement objects
 * (Output data of ROD Height Classification Algorithm)
 */
typedef struct
{
    uint8_t Probability_u8; //!< Height probability of measurement object (0=LOW, 128=UNKNOWN, 255=HIGH)
    uint8_t Certainty_u8;   //!< Certainty of the height probability (0=LOW, 255=HIGH)
    uint8_t Estimation_u8;  //!< Estimated height of object in cm (255=INVALID)
    uint8_t MaxError_u8;    //!< Uncertainty of height estimation in cm (255=INVALID) [optional value, might be removed
                          //!< during dev.]
    uint8_t CeilingBeamProb_u8; //!< Probability that measurement object is a ceiling beam (0=Low probability, 255=High
                              //!< probability)
    uint8_t CeilingBeamProbUncertain_b; //!< True if CeilingBeamProb is uncertain
} imo_rod_measObjHeightInfo_st;

// rod measurement object data type
typedef struct
{

    // main object information of measurement object
    imo_rod_measObjectMainInfo_st measObjectMainInfo_st;
    // height information of measurement object
    imo_rod_measObjHeightInfo_st Height_st;
    // ecu time stamp of measurement object
    uint32_t  ECUTimeL_u32;
    // ecu time stamp of measurement object
    uint32_t  ECUTimeH_u32;
    // sensor distance
    uint16_t   SensDist_u16;
    // Length of object
    uint16_t   ObjLength_u16;
    // existence probability of measurement object
    uint8_t   ExistProb_u8;
    // position flags of measured object
    imo_rod_ObjectPositionFlags_st ObjPosFlags_st;
} imo_rod_measObject_st;

typedef struct
{
    int16_t   DrivenDistanceCM_s16;
    int16_t   XPositionCM_s16;
    int16_t   YPositionCM_s16;
    uint16_t  YawAngleRAD_F10_u16;
    int16_t   YawAngleRAD_F12_s16;
} imo_VehPos16_st;
/**
 * Container structure to group the measurement objects
 */
typedef struct
{
    imo_rod_measObject_st MeasObjLst_pst[imo_ROD_MEASOBJLISTSIZE];
    /// number of objects in MeasObjLst_pst
    uint16_t  numValidObjs_u16;
    /// vehicle position for the measurement
    imo_VehPos16_st refVehPos_st;
    // reserve1
    uint32_t Olo_Reserve1;
} imo_rod_measObjMonitor_st;

/* Echo Infomation*/
typedef enum
{
    imo_EchoType_None_en      = 0, // none of the echoes type
    imo_EchoType_Fixed_en     = 1, // fix of  the echoes type
    imo_EchoType_Chirp_en     = 2, // chirp of the echoes type 
    imo_EchoType_Max_en       = 3  // max of the echoes type
} imo_EchoType_FreqMode_en;

typedef enum
{
    imo_EchoTransmit_None_en     = 0, // none  of the echoes Transmit
    imo_EchoTransmit_Normal_en   = 1, // 15/26/14/36 of the echoes Transmit
    imo_EchoTransmit_Parallel_en = 2, // 135/246     of the echoes Transmit
    imo_EchoTransmit_Max_en      = 3  // max   of the echoes Transmit
} imo_EchoTransmit_Comb_en;

typedef struct
{
    uint16_t  EchoDist_u16;
    uint8_t   EchoAmp_u8;
    uint8_t   EchoProb_u8;
} imo_EchoInfo_st;

typedef struct
{
    imo_EchoType_FreqMode_en EchoType_FreqMode_en;
    imo_EchoTransmit_Comb_en EchoTransmit_Comb_en;
    uint32_t                      TransmitStartTime_u32;
    uint64_t                      TransmitStartTime_u64;
    uint16_t                      mpTransmitter_u16;      
    uint16_t                      mpReceiver_u16;   
    imo_EchoInfo_st          DE_pst[imo_ECHO_SENSORS_NUM][imo_ECHO_MAX_NUM];       
    imo_EchoInfo_st          CE_left_pst[imo_ECHO_SENSORS_NUM][imo_ECHO_MAX_NUM]; 
    imo_EchoInfo_st          CE_right_pst[imo_ECHO_SENSORS_NUM][imo_ECHO_MAX_NUM];          
} imo_Echo_measObjMonitor_st;

/* Parking  Space*/
typedef enum
{
    imo_PS_None_enm     = 0,
    imo_PS_Parallel_enm = 1,
    imo_PS_Cross_enm    = 2,
    imo_PS_Diagonal_enm = 3
} imo_PSType_en;

typedef enum
{
    /**
     * PS on the left side of the road
     */
    imo_PS_LeftSide_enm = 0,
    /**
     * The right side of the road
     */
    imo_PS_RightSide_enm = 1,
    /**
     * Both Sides (used in MPS for parking space selection, or as "uninitialized" value)
     */
    imo_PS_BothSides_enm = 2
} imo_Side_en;

typedef struct
{
    float X_f32;
    float Y_f32;
} imo_PointFl_st;

typedef enum
{
    /**
     * No real reference object is present. Used for virtual parking spaces limited only on one side by a
     * real object.
     */
    imo_RefVirtual_enm = 0,
    /**
     * reference is based on a real object.
     * CPSC: Reference is based on a long object (to be considered for alignment).
     * This is also the default Ref type until the type can be qualified better
     */
    imo_RefReal_enm = 1,
    /**
     * OMA remeasurement of reference is based on a sole short object. This could be anything, but it's
     * probably not a car. Short or unstructured object will be treated differently in the CPSC alignment
     * rules.
     */
    imo_RefShort_enm = 2,
    /**
     * The reference is based on a short object with a larger object behind it. This is a typical ROD
     * object constellation for a pillar with a parking car behind. The reference will be placed on the
     * closer (short) object to avoid danger of collision.  NOTE: Currently unsupported.
     */
    imo_RefPillar_enm = 3
} imo_RefType_en;
typedef enum
{
    imo_DepthVirtual_enm = 0,
    imo_DepthLow_enm     = 1,
    imo_DepthHigh_enm    = 2,
    imo_DepthCurb_enm    = 3,
    /**
     * not traversable, but possibly relevant for target-orientation
     */
    imo_DepthWall_enm      = 4,
    imo_DepthVideoLine_enm = 5,
    imo_DepthUnknown_enm   = 6
} imo_DepthRefType_en;
typedef struct 
{
    imo_PointFl_st P1_st;
    imo_PointFl_st P2_st;
} imo_LineSegmentFl_st;
typedef struct
{
    /**
     * Coordinates of corner and length in m
     */
    imo_PointFl_st    ObjCorner_st;  // Aligned to 4 bytes
    float                  ObjLength_f32; // Aligned to 4 bytes
    imo_RefType_en    ObjType_en;
    int16_t                Orientation_s16;
} imo_Obj_st; // Aligned to 4 bytes
typedef struct 
{
    /**
     * Coordinates of the line segment in m
     */
    imo_LineSegmentFl_st   LineSegment_st; // Aligned to 4 bytes
    imo_DepthRefType_en    DepthRefType_en;
} imo_DepthRef_st; // Aligned to 4 bytes
typedef struct
{
        /**
     * object affecting the depthwise positioning in the parking space and/or defining its orientation
     * for perpendicular parking here is the 'main' curbstone
     */
    imo_DepthRef_st DepthRef1_st;   // Aligned to 4 bytes
                                            /**
                                             * object affecting the depthwise positioning in the parking space and/or defining its orientation
                                             * for parallel parking spaces here is placed the main curbstone/obstacle in the scene limiting the
                                             * depth - OCP0, OCP2 cases
                                             */
    imo_DepthRef_st DepthRef2_st;   // Aligned to 4 bytes
                                            /**
                                             * object affecting the depthwise positioning in the parking space and/or defining its orientation
                                             * obstacle object
                                             */
    imo_DepthRef_st DepthRef3_st;   // Aligned to 4 bytes
                                            /**
                                             * 1st object (in driving by direction) limiting the parking space length/width (as opposed to depth)
                                             */
    imo_DepthRef_st DepthRef4_st;   // Aligned to 4 bytes
                                            /**
                                             * 1st object (in driving by direction) limiting the parking space length/width (as opposed to depth)
                                             */
    imo_Obj_st Obj1_st;             // Aligned to 4 bytes
                                            /**
                                             * 2nd object (in driving by direction) limiting the parking space length/width (as opposed to depth)
                                             */
    imo_Obj_st Obj2_st;             // Aligned to 4 bytes
                                            /**
                                             * Reference circle identifying the scene as a scene in a curve. The curvatures and exact position in
                                             * space of the curb-DepthRefs can be calculated from this circle. Alternatively, a curvature could be
                                             * associated to the line-segments representing the curbs.
                                             * However, for perpendicular slots in a curve the latter approach does not work as in this case curbs
                                             * are not necessarily identified while having a circle-information might still be desirable.
                                             * An alternative to using this circle here, is to use the circle in the target-position struct as a
                                             * reference circle for the depth-references. In perpendicular scenes, the target-pos-circle would
                                             * only be used for the target position and angle.
                                             * Coordinates of center and radius in m
                                             */
} imo_PSObjInfo_st;                 // Aligned to 4 bytes

/**
 * Basic parking-space structure containing information about
 * 1. the object layout
 * 3. meta-information about the side, the type and the OCP (on-curb-parking)-type.
 * The OCP-type is relevant for the path planning.
 */
typedef struct
{
    /**
     * object-layout containing all objects relevant for the target position
     */
    imo_PSObjInfo_st PSObj_st; // Aligned to 4 dbytes
                                        /**
                                        * length of parking space in cm
                                        */
    uint16_t Length_u16;
        /**
     * This contains the relative orientation of the street to the parking space. The variable is only
     * needed for angled parking space detection. For parallel parking spaces, it's always 0 and for cross
     * PS always 90 degrees (-90 degrees for left-hand side parking spaces). For diagonal parking spaces,
     * it's between 0 and 180 degrees (0 and -180 degrees for left-hand side parking spaces).
     * If the relative orientation cannot be estimated by SPL, it may be set in MPS based on driver
     * triggering diagonal parking. Unit: [F12 rad]
     */
    int16_t StreetOrient_s16;
    /**
     * parking space unique identifier. ID 0 is reserved and means "none". All parking space IDs above
     * imo_minOMAPSID_u8 are reserved for AIM or POC initialized parking spaces. Odd IDs for parking
     * spaces on the left, even IDs for parking spaces on the right.
     */
    uint8_t ID_u8;

    /**
     * Parking space id in Uint64 format, used for function to save parking slots
     */
    uint64_t ID_u64;

    /**
     * Parking space target oritention, used for function to identify the same ps during trainning and gudiance phase, Unit: [F12 rad], range (-pi...+pi)
     */
    int16_t Angle_s16;
    /**
     * Parking space side - left or right of the street. Left side parking spaces are usually mapped to
     * the right side in path planning and guidance, but the parking space side will be kept to restore
     * the original coordinates.
     */
    imo_Side_en Side_en;
    /**
     * type of the parking space
     */
    imo_PSType_en         Type_en;
} imo_PSInfo_st; // Aligned to 4 bytes

typedef struct
{
    imo_PSInfo_st PsInfo_st[imo_SPL_NumPSSides_du8][imo_SPL_PSRM_SIZE_du8];
} imo_ParkingSpace_st;


/**
 * map object type
 */
typedef enum
{
    imo_MAP_None_enm = 0,
    /**
     * point shaped object
     */
    imo_MAP_Point_enm = 1,
    /**
     * open wall
     */
    imo_MAP_Straight_0Corner_enm = 2,
    /**
     * one sided closed wall
     */
    imo_MAP_Straight_1Corner_enm = 3,
    /**
     * both sided closed wall
     */
    imo_MAP_Straight_2Corner_enm = 4,
    /**
     * detected curbstone
     */
    imo_MAP_Curbstone_enm = 5,
    /**
     * Blind area e.g. gap in measurement
     */
    imo_MAP_Blind_enm = 6,

    /**
     * A "road" or "ground" marking is a traversable object, detected by imaging sensors like cameras,
     * which can be any of the following: Paint lines, paint marking, lines by pavement stones with distinct
     * coloring, raised pavement markers etc., which usually indicate parking space boundaries. Not available with
     * ultrasonic-sensor-only system.
     */
    imo_MAP_RoadMarking_enm = 7,
    /**
     * General Obstacle (used by MAP/SOB for marking some specific objects detected by ESOT)
     */
    imo_MAP_Obstacle_enm = 8

} imo_ObjType_en;

/**
 * Type do describe the height status of an object.
 */
typedef enum
{

    /**
     * Object is surely traversable (ground markings, very low curb stones, ...) or has already
     * been traversed by the ego vehicle's wheels.
     */
    imo_MAP_Traversable_enm = 0,

    /**
     * Object is low with a high probability
     * Can be a curb stone or some other low object. Height not exactly determined, but less than the
     * height-level of the built-in ultrasonic sensors.
     */
    imo_MAP_Low_enm = 1,

    /**
     * Height Status is unknown
     */
    imo_MAP_Unknown_enm = 2,

    /**
     * Object is high with a high probability. High means: Object height almost on the height level of the
     * built-in ultrasonic sensor levels or higher.
     */
    imo_MAP_High_enm = 3,

    /**
     * Object is surely traversable with the car's body (i.e., it is less than 11 cm high, or whatever height for
     * body-traversable has been configured).
     * Possible objects in this category: curbstones, wheel-stoppers.
     * HS_BodyTraversable is more specific than HS_Low. In an ultrasonic-only system,
     * objects will not be marked as "HS_BodyTraversable", as exact height is not measurable, but will be marked as
     * "HS_Low" instead.
     */
    imo_MAP_BodyTraversable_enm = 4

} imo_HeightStatus_en;
typedef struct
{
    /**
     * Object coordinates. Border Points for P1/P2 for walls. Equal coords for points.
     * Endpoint coordinates of LineSegment (X/Y pairs) are in millimeters !!!
     */
    imo_LineSegment_st Coords_st;
    /**
     * Length of the object (millimeters).
     */
    uint16_t Length_u16;

    /**
     * ID of the object within the map (0 means invalid object)
     */
    uint16_t ObjID_u16;
    /**
     * Probability that object is high.
    */
    uint8_t HeightProb_u8;
    /**
     * Provides whether object is high, low, traversable or unknown
     */
    imo_HeightStatus_en HeightStatus_en;
    /**
     * Type of the object (None, point, opem wall, single-ended-wall, double-ended-wall, ...)
     */
    imo_ObjType_en ObjType_en;
    /**
     * Probability that Object really exists. Indication for the update rate of the object.
    */
    uint8_t ExistProb_u8;

} imo_Object_st;
typedef struct
{
    imo_Object_st MapObjs_pst[imo_MAP_SIZE_OUT];
    // the actual number of valid objects currently in MAP output
    uint16_t numMapObjs_u16;
    // ecu time stamp of measurement object
    uint32_t  ECUTimeL_u32;
    // ecu time stamp of measurement object
    uint32_t  ECUTimeH_u32;
    // reserve1
    uint32_t  numSideObjects_u16;
    // reserve2
    uint32_t  Map_Reserve2_u32;
    // reserve3
    uint32_t  Map_Reserve3_u32;
} imo_map_MonitorData_st;
typedef struct
{
    imo_ParkingSpace_st        ParkingSpace_st;
    imo_PDCOutput_st           PDCOutput_st;
    imo_rod_measObjMonitor_st  rod_MeasObjMonitor_st;
    imo_Echo_measObjMonitor_st Echo_measObjMonitor_st;
    imo_map_MonitorData_st     Map_MeasObjMonitor_st;
    // uint32_t imo_Debug1_u32[50];
    // uint32_t imo_Debug2_u32[50];
    // uint32_t imo_Debug3_u32[50];
    // uint32_t imo_Debug4_u32[50];
    // uint32_t imo_Debug5_u32[50];
    // uint32_t imo_Debug6_u32[50];
}imo_UssAppOutputSample_st ;

extern imo_UssAppOutputSample_st __attribute__((aligned(16))) imo_UssAppOutput_st;
// extern imo_UssAppOutputSample_st __attribute__((aligned(16))) imo_UssAppOutput_st;
//A core send to R core about control information
typedef struct
{
    uint8_t veh_Mode;	//车辆状态 0--行车，1--泊车
	uint8_t reserved0;
	uint16_t reserved1;
	uint8_t reserved2[200];
} imo_Type_UssControlInfo_st;
extern imo_Type_UssControlInfo_st	   imo_UssControlInfo_st;
#ifdef __cplusplus
}
#endif

#endif /*__imo_USS_PFAPP_H__*/	


