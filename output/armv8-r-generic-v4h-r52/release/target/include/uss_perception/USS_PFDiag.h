#ifndef	__USS_PFDiag_H__
#define	__USS_PFDiag_H__

#include "stdint.h"

#define imo_mp_diag_SYSTEMFaultNUM                  (uint8_t)1
#define imo_mp_diag_42CHIPFaultNUM                  (uint8_t)3
#define imo_mp_diag_SnsFaultNUM                     (uint8_t)12

#ifdef __cplusplus
extern "C" 
{
#endif


typedef union
{
  struct
  {
      uint16_t SystemUssPower1OVFlag_bf1 : 1;
      uint16_t SystemUssPower1UVFlag_bf1 : 1;
      uint16_t SystemUssPower2OVFlag_bf1 : 1;
      uint16_t SystemUssPower2UVFlag_bf1 : 1;
      uint16_t SystemUssPower3OVFlag_bf1 : 1;
      uint16_t SystemUssPower3UVFlag_bf1 : 1;
      uint16_t SystemTask1msTimeoutFlag_bf1 : 1;//0:untrigger   1:trigger
      uint16_t SystemTask10msTimeoutFlag_bf1 : 1;
      uint16_t SystemTaskMpTimeout_bf1 : 1;
      uint16_t SystemReservedFlag_bf13 : 13;
  } SystemFaultBitList_st;

  uint16_t SystemFaultWord_u16;
}imo_Type_mp_Diag_SystemFaultList_un;

typedef union
{
  struct
  {
      uint16_t SpiDataCrcFlag_bf1 : 1;//0:untrigger   1:trigger
      uint16_t SpiDataTimeoutFlag_bf1 : 1;
      uint16_t SpiDCRTimeoutFlag_bf1 : 1;
      uint16_t SpiReservedFlag_bf1 : 1;
      uint16_t SpiReservedFlag_bf12 : 12;
  } SpiFaultBitList_st;
  uint16_t SpiFaultWord_u16;
}imo_Type_mp_Diag_SpiFaultList_un;

typedef union
{
  struct
  {
      uint16_t Chip42OTFlag_bf1 : 1;//0:untrigger   1:trigger
      uint16_t Chip42VCCUV_RFCBFlag_bf1 : 1;
      uint16_t Chip42CLKREF_ErrorFlag_bf1 : 1;
      uint16_t Chip42CMD_INCFlag_bf1 : 1;
      uint16_t Chip42CRC_ErrorFlag_bf1 : 1;
      uint16_t Chip42UND_CMDFlag_bf1 : 1;
      uint16_t Chip42RFCErrFlag_bf1 : 1;
      uint16_t Chip42ReservedFlag_bf1 : 1;
      uint16_t Chip42ReservedFlag_bf8 : 8;
  } E524_42ChipFaultBitList_st;
  uint16_t E524_42ChipFaultWord_u16;
}imo_Type_mp_Diag_42ChipFaultList_un;

typedef union
{
  struct
  {
      uint16_t DSI0_UVFlag_bf1 : 1;//0:untrigger   1:trigger
      uint16_t DSI0_OCFlag_bf1 : 1;
      uint16_t DSI0_CMD_OVRFlag_bf1 : 1;
      uint16_t DSI0_PDCMRECFlag_bf1 : 1;
      uint16_t DSI0_CRMRECFlag_bf1 : 1;
      uint16_t DSI0_TDMA_SCHEME_DEFINEFlag_bf1 : 1;
      uint16_t DSI1_UVFlag_bf1 : 1;//0:untrigger   1:trigger
      uint16_t DSI1_OCFlag_bf1 : 1;
      uint16_t DSI1_CMD_OVRFlag_bf1 : 1;
      uint16_t DSI1_PDCMRECFlag_bf1 : 1;
      uint16_t DSI1_CRMRECFlag_bf1 : 1;
      uint16_t DSI1_TDMA_SCHEME_DEFINEFlag_bf1 : 1;
      uint16_t DSI_ReservedFlag_bf4 : 4;
  } DSIFaultBitList_st;
  uint16_t DSIFaultWord_u16;
}imo_Type_mp_Diag_DSIFaultList_un;

typedef union
{
  struct
  {
      uint32_t SnsOpenFlag_bf1 : 1;//0:untrigger   1:trigger
      uint32_t SnsShortGround_bf1 : 1;
      uint32_t SnsShortPower_bf1 : 1;
      uint32_t SnsParaInitFail_bf1 : 1;
      uint32_t SnsIDInitFail_bf1 : 1;
      uint32_t SnsDisturb_bf1 : 1;
      uint32_t SnsBlock_bf1 : 1;
      uint32_t SnsNoSignal_bf1 : 1;
      uint32_t SnsRingTimeShort_bf1 : 1;
      uint32_t SnsRingTimeLong_bf1 : 1;
      uint32_t SnsTempHigh_bf1 : 1;
      uint32_t SnsTempLow_bf1 : 1;
      uint32_t SnsVSUPHigh_bf1 : 1;
      uint32_t SnsVSUPLow_bf1 : 1;
      uint32_t SnsHwErr_bf1 : 1;
      uint32_t SnsComErr_bf1 : 1;
      uint32_t SnsRingFreqHigh_bf1 : 1;
      uint32_t SnsRingFreqLow_bf1 : 1;
      uint32_t SnsCrcErr_bf1 : 1;
      uint32_t SnsSymbolCodingErr_bf1 : 1;
      uint32_t SnsReservedFlag_bf12 : 12;

  } SnsFaultBitList_st;

  uint32_t SnsFaultWord_u32;
}imo_Type_mp_Diag_SnsFaultList_un;

typedef struct
{
    imo_Type_mp_Diag_SystemFaultList_un SystemFaultCode_pun[imo_mp_diag_SYSTEMFaultNUM];//system
    imo_Type_mp_Diag_SpiFaultList_un SpiFaultCode_pun[imo_mp_diag_42CHIPFaultNUM];//single spi
    imo_Type_mp_Diag_42ChipFaultList_un Chip42FaultCode_pun[imo_mp_diag_42CHIPFaultNUM];//single 42 chip 
    imo_Type_mp_Diag_DSIFaultList_un DSIFaultCode_pun[imo_mp_diag_42CHIPFaultNUM];//single DSI
    imo_Type_mp_Diag_SnsFaultList_un SnsFaultCode_pun[imo_mp_diag_SnsFaultNUM];//single sns

} imo_Type_mp_Diag_SnsFaultDiagnose_st;

typedef struct
{
    uint8_t Event_Ak2Fault_List[81];
    uint8_t Event_SnsFault_List[216];
} imo_Type_mp_Diag_SnsFaultDiagnoseList_st;

extern imo_Type_mp_Diag_SnsFaultDiagnose_st imo_mp_diag_SnsInsideFaultDiagnose_st;
extern imo_Type_mp_Diag_SnsFaultDiagnoseList_st imo_mp_diag_SnsInsideFaultDiagnoseList_st;
#ifdef __cplusplus
}
#endif

#endif /*__imo_USS_PFAPP_H__*/	


