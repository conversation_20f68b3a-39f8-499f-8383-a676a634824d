#ifndef BASIC_TYPES_TYPES_HPP_
#define BASIC_TYPES_TYPES_HPP_

#include <cstddef>
#include <cstdint>

namespace zos
{

using int8_t     = std::int8_t;
using uint8_t    = std::uint8_t;
using int16_t    = std::int16_t;
using uint16_t   = std::uint16_t;
using int32_t    = std::int32_t;
using uint32_t   = std::uint32_t;
using int64_t    = std::int64_t;
using uint64_t   = std::uint64_t;
using float32_t  = float;
using float64_t  = double;
// using float128_t = long double;
using size_t     = std::size_t;
using ptrdiff_t  = std::ptrdiff_t;
using char_t     = char;
using uintptr_t  = std::uintptr_t;
using bool_t     = bool;

}  // namespace zos

static_assert(1 == sizeof(zos::uint8_t), "sizeof zos::uint8_t != 1");
static_assert(1 == sizeof(zos::int8_t), "sizeof zos::int8_t != 1");
static_assert(2 == sizeof(zos::uint16_t), "sizeof zos::uint16_t != 2");
static_assert(2 == sizeof(zos::int16_t), "sizeof zos::int16_t != 2");
static_assert(4 == sizeof(zos::uint32_t), "sizeof zos::uint32_t != 4");
static_assert(4 == sizeof(zos::int32_t), "sizeof zos::int32_t != 4");
static_assert(8 == sizeof(zos::uint64_t), "sizeof zos::uint64_t != 8");
static_assert(8 == sizeof(zos::int64_t), "sizeof zos::int64_t != 8");
static_assert(4 == sizeof(zos::float32_t), "sizeof zos::float32_t != 4");
static_assert(8 == sizeof(zos::float64_t), "sizeof zos::float64_t != 8");
// static_assert(16 == sizeof(zos::float128_t), "sizeof zos::float128_t != 8");
static_assert(1 == sizeof(zos::bool_t), "sizeof zos::bool_t != 1");
static_assert(1 == sizeof(zos::char_t), "sizeof zos::char_t != 1");

#endif
