#ifndef BASIC_TYPES_SERIALIZATION_SERIALIZATION_TRAITS_HPP_
#define BASIC_TYPES_SERIALIZATION_SERIALIZATION_TRAITS_HPP_

#include <basic_types/container/array.hpp>
#include <basic_types/container/string.hpp>
#include <basic_types/container/vector.hpp>
#include <basic_types/types.hpp>

namespace zos
{
namespace serialization
{

struct StdLayoutTag
{
};

struct StdLayoutTruncTag
{
};

template <typename T, typename TAG = StdLayoutTruncTag>
struct SerializationTraits
{
    template <typename = typename std::enable_if<std::is_trivially_copyable<T>::value ||
                                                 std::is_standard_layout<T>::value>::type>
    static size_t GetSerializedSize(T const&)
    {
        return sizeof(T);
    }
};

// SerializationTraits for basic types
template <>
struct SerializationTraits<zos::int8_t, StdLayoutTag>
{
    using ValueType = zos::int8_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <>
struct SerializationTraits<zos::uint8_t, StdLayoutTag>
{
    using ValueType = zos::uint8_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <>
struct SerializationTraits<zos::int16_t, StdLayoutTag>
{
    using ValueType = zos::int16_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <>
struct SerializationTraits<zos::uint16_t, StdLayoutTag>
{
    using ValueType = zos::uint16_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <>
struct SerializationTraits<zos::int32_t, StdLayoutTag>
{
    using ValueType = zos::int32_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <>
struct SerializationTraits<zos::uint32_t, StdLayoutTag>
{
    using ValueType = zos::uint32_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <>
struct SerializationTraits<zos::int64_t, StdLayoutTag>
{
    using ValueType = zos::int64_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <>
struct SerializationTraits<zos::float32_t, StdLayoutTag>
{
    using ValueType = zos::float32_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <>
struct SerializationTraits<zos::float64_t, StdLayoutTag>
{
    using ValueType = zos::float64_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <>
struct SerializationTraits<zos::char_t, StdLayoutTag>
{
    using ValueType = zos::char_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <>
struct SerializationTraits<zos::bool_t, StdLayoutTag>
{
    using ValueType = zos::bool_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <typename T, size_t SIZE>
struct SerializationTraits<zos::FixedVector<T, SIZE>, StdLayoutTag>
{
    using ValueType = zos::FixedVector<T, SIZE>;

    static size_t GetSerializedSize(ValueType const& sample)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <typename T, size_t SIZE>
struct SerializationTraits<zos::FixedArray<T, SIZE>, StdLayoutTag>
{
    using ValueType = zos::FixedArray<T, SIZE>;

    static size_t GetSerializedSize(ValueType const& sample)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <size_t SIZE>
struct SerializationTraits<zos::FixedString<SIZE>, StdLayoutTag>
{
    using ValueType = zos::FixedString<SIZE>;

    static size_t GetSerializedSize(ValueType const& sample)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

// std layout trunc tag SerializationTraits for basic types
template <>
struct SerializationTraits<zos::int8_t, StdLayoutTruncTag>
{
    using ValueType = zos::int8_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <>
struct SerializationTraits<zos::uint8_t, StdLayoutTruncTag>
{
    using ValueType = zos::uint8_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <>
struct SerializationTraits<zos::int16_t, StdLayoutTruncTag>
{
    using ValueType = zos::int16_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <>
struct SerializationTraits<zos::uint16_t, StdLayoutTruncTag>
{
    using ValueType = zos::uint16_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <>
struct SerializationTraits<zos::int32_t, StdLayoutTruncTag>
{
    using ValueType = zos::int32_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <>
struct SerializationTraits<zos::uint32_t, StdLayoutTruncTag>
{
    using ValueType = zos::uint32_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <>
struct SerializationTraits<zos::int64_t, StdLayoutTruncTag>
{
    using ValueType = zos::int64_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <>
struct SerializationTraits<zos::float32_t, StdLayoutTruncTag>
{
    using ValueType = zos::float32_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <>
struct SerializationTraits<zos::float64_t, StdLayoutTruncTag>
{
    using ValueType = zos::float64_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <>
struct SerializationTraits<zos::char_t, StdLayoutTruncTag>
{
    using ValueType = zos::char_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <>
struct SerializationTraits<zos::bool_t, StdLayoutTruncTag>
{
    using ValueType = zos::bool_t;

    static size_t GetSerializedSize(ValueType)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <typename T, size_t SIZE>
struct SerializationTraits<zos::FixedVector<T, SIZE>, StdLayoutTruncTag>
{
    using ValueType = zos::FixedVector<T, SIZE>;

    static size_t GetSerializedSize(ValueType const& sample)
    {
        return sizeof(uint32_t) + sample.size() * sizeof(T);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        uint32_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size > ser_size)
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        uint32_t ele_size;
        uint32_t des_size;
        std::memcpy(std::addressof(ele_size), buffer, sizeof(ele_size));
        des_size = sizeof(ele_size) + ele_size * sizeof(T);
        if (std::addressof(target) == nullptr)
        {
            return des_size;
        }
        else
        {
            if (size >= des_size)
            {
                std::memcpy(std::addressof(target), buffer, des_size);
                return des_size;
            }
            else
            {
                return 0;
            }
        }
    }
};

template <typename T, size_t SIZE>
struct SerializationTraits<zos::FixedArray<T, SIZE>, StdLayoutTruncTag>
{
    using ValueType = zos::FixedArray<T, SIZE>;

    static size_t GetSerializedSize(ValueType const& sample)
    {
        return sizeof(ValueType);
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size > sizeof(ValueType))
            {
                std::memcpy(buffer, std::addressof(sample), sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        if (std::addressof(target) == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size >= sizeof(ValueType))
            {
                std::memcpy(std::addressof(target), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
            else
            {
                return 0;
            }
        }
    }
};

template <size_t SIZE>
struct SerializationTraits<zos::FixedString<SIZE>, StdLayoutTruncTag>
{
    using ValueType = zos::FixedString<SIZE>;

    static size_t GetSerializedSize(ValueType const& sample)
    {
        return sizeof(uint32_t) + sample.size();
    }

    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        uint32_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size > ser_size)
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
            else
            {
                return 0;
            }
        }
    }

    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& target)
    {
        uint32_t str_size;
        uint32_t des_size;
        std::memcpy(std::addressof(str_size), buffer, sizeof(str_size));
        des_size = sizeof(str_size) + str_size;
        if (std::addressof(target) == nullptr)
        {
            return des_size;
        }
        else
        {
            if (size >= des_size)
            {
                std::memcpy(std::addressof(target), buffer, des_size);
                return des_size;
            }
            else
            {
                return 0;
            }
        }
    }
};

}  // namespace serialization
}  // namespace zos

#endif
