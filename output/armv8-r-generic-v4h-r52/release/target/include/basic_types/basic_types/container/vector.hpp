#ifndef BASIC_TYPES_CONTAINER_VECTOR_HPP_
#define BASIC_TYPES_CONTAINER_VECTOR_HPP_

#include <basic_types/container/traits.hpp>
#include <basic_types/types.hpp>
#include <nlohmann/json.hpp>
#include <iterator>

namespace zos
{

/**
 * @brief FixedSequence is base class for sequence container with predefined maximum element size.
 *
 * @tparam T Type of the elements
 * @tparam SIZE Max size of the vector
 */
template <typename T, size_t SIZE>
class FixedSequence
{
public:
    static size_t const SIZE_VALUE = SIZE;

    using value_type      = T;
    using size_type       = std::size_t;
    using difference_type = std::ptrdiff_t;
    using reference       = value_type&;
    using const_reference = const value_type&;
    using pointer         = value_type*;
    using const_pointer   = const value_type*;
    template <bool_t CONST = false>
    class Iterator;
    using iterator               = Iterator<false>;
    using const_iterator         = Iterator<true>;
    using reverse_iterator       = std::reverse_iterator<iterator>;
    using const_reverse_iterator = std::reverse_iterator<const_iterator>;

    /**
     * @brief Default constructor
     */
    FixedSequence() : size_{0}, buf_{}
    {
    }

    /**
     * @brief Constructs the container with count default-inserted instance of T.
     *
     * @param count Number of elements.
     *
     * @throws std::bad_alloc if count > SIZE.
     */
    explicit FixedSequence(size_type count) : size_{count}, buf_{}
    {
        if (count <= SIZE)
        {
            for (size_type i = 0; i < count; ++i)
            {
                (void)new (reinterpret_cast<pointer>(buf_) + i) value_type();
            }
        }
        else
        {
#ifdef __EXCEPTIONS
            throw std::bad_alloc();
#else
            std::terminate();
#endif
        }
    }

    /**
     * @brief Constructs the container with count copies of elements with value
     *
     * @param count Number of elements
     * @param value value to initialize elements of the container
     *
     * @throws std::bad_alloc if count > SIZE
     */
    FixedSequence(size_type count, T const& value) : size_{static_cast<uint32_t>(count)}, buf_{}
    {
        if (count <= SIZE)
        {
            for (size_type i = 0; i < count; ++i)
            {
                (void)new (reinterpret_cast<pointer>(buf_) + i) value_type(value);
            }
        }
        else
        {
#ifdef __EXCEPTIONS
            throw std::bad_alloc();
#else
            std::terminate();
#endif
        }
    }

    /**
     * @brief Constructs the container with the contents of the range [first, last)
     *
     * @tparam InputIt Input iterator type. Must satisfy c++ named requirements: LegacyInputIterator
     * @param first Start of range to copy elements
     * @param last End of range to copy elements
     *
     * @throws std::bad_alloc if std::distance(first, last) > SIZE
     */
    template <typename InputIt, typename = typename std::enable_if<IsInputIterator<InputIt>::value>::type>
    FixedSequence(InputIt first, InputIt last) : size_{0}, buf_{}
    {
        size_t count = last - first;
        if (count <= SIZE)
        {
            for (size_type i = 0; i < count; ++i)
            {
                (void)new (reinterpret_cast<pointer>(buf_) + i) value_type(*first++);
            }
            size_ = count;
        }
        else
        {
#ifdef __EXCEPTIONS
            throw std::bad_alloc();
#else
            std::terminate();
#endif
        }
    }

    /**
     * @brief Copy constructor
     *
     * @param other The other container.
     */
    FixedSequence(FixedSequence const& other) : FixedSequence(other.begin(), other.end())
    {
    }

    /**
     * @brief Move constructor
     *
     * @param other The other container
     */
    FixedSequence(FixedSequence&& other) : FixedSequence(other.begin(), other.end())
    {
    }

    /**
     * @brief Constructs with the contents of the initializer list
     *
     * @param ilist The initializer list
     */
    FixedSequence(std::initializer_list<T> ilist) : FixedSequence(ilist.begin(), ilist.end())
    {
    }

    /**
     * @brief Construct with other FixedSequence of different SIZE
     *
     * @tparam SIZE_ Size of the other FixedSequence object.
     * @param other The other FixedSequence object.
     */
    template <size_t SIZE_>
    FixedSequence(FixedSequence<T, SIZE_> const& other) : FixedSequence(other.begin(), other.end())
    {
    }

    /**
     * @brief Copy assign operator
     *
     * @param other The other container
     *
     * @return Reference to this container
     */
    FixedSequence& operator=(FixedSequence const& other)
    {
        assign(other.begin(), other.end());
        return *this;
    }

    /**
     * @brief Replace the contents with those identified by initializer list
     *
     * @param ilist initializer list
     *
     * @return Reference to this container
     */
    FixedSequence& operator=(std::initializer_list<T> ilist)
    {
        assign(ilist.begin(), ilist.end());
        return *this;
    }

    /**
     * @brief Assign from other FixedSizeBuffer of different SIZE
     *
     * @tparam SIZE_ Size of the other FixedSequence object.
     * @param other The other FixedSequence object.
     *
     * @throws std::bad_alloc if other.size() > SIZE
     */
    template <size_t SIZE_>
    FixedSequence& operator=(FixedSequence<T, SIZE_> const& other)
    {
        assign(other.begin(), other.end());
        return *this;
    }

    /**
     * @brief Replace the contents with count copies of value
     *
     * @param count Number of elements
     * @param value Element value
     *
     * @throws std::bad_alloc if count > SIZE
     */
    void assign(size_type count, T const& value)
    {
        if (count <= SIZE)
        {
            clear();
            for (size_type i = 0; i < count; ++i)
            {
                (void)new (reinterpret_cast<pointer>(buf_) + i) value_type(value);
            }
            size_ = count;
        }
        else
        {
#ifdef __EXCEPTIONS
            throw std::bad_alloc();
#else
            std::terminate();
#endif
        }
    }

    /**
     * @brief Replace the contents with copies of those in the range [first, last)
     *
     * @tparam InputIt Input iterator type. Must satisfy c++ named requirements: LegacyInputIterator
     * @param first Start of range to copy elements
     * @param last End of range to copy elements
     *
     * @throws std::bad_alloc if std::distance(first, last) > SIZE
     */
    template <typename InputIt, typename = typename std::enable_if<IsInputIterator<InputIt>::value>::type>
    void assign(InputIt first, InputIt last)
    {
        size_t count = last - first;
        if (count <= SIZE)
        {
            clear();
            for (size_type i = 0; i < count; ++i)
            {
                (void)new (reinterpret_cast<pointer>(buf_) + i) value_type(*first++);
            }
            size_ = count;
        }
        else
        {
#ifdef __EXCEPTIONS
            throw std::bad_alloc();
#else
            std::terminate();
#endif
        }
    }

    /**
     * @brief Replace the contents with the those identified by initializer list
     *
     * @param ilist The initailizer list
     */
    void assign(std::initializer_list<T> ilist)
    {
        assign(ilist.begin(), ilist.end());
    }

    /**
     * @brief Access specified element with bounds check.
     *
     * @param pos Element index
     *
     * @return reference to the element
     *
     * @throws std::out_of_range if pos > size()
     */
    reference at(size_type pos)
    {
        if (pos < size_)
        {
            return reinterpret_cast<pointer>(buf_)[pos];
        }
#ifdef __EXCEPTIONS
        throw std::out_of_range("");
#else
            std::terminate();
#endif
    }

    /**
     * @brief Access specified element with bounds check.
     *
     * @param pos Element index
     *
     * @return reference to the element
     *
     * @throws std::out_of_range if pos >= size()
     */
    const_reference at(size_type pos) const
    {
        if (pos < size_)
        {
            return reinterpret_cast<const_pointer>(buf_)[pos];
        }
#ifdef __EXCEPTIONS
        throw std::out_of_range("");
#else
            std::terminate();
#endif
    }

    /**
     * @brief Access specified element without bounds check.
     *
     * @param pos Element index
     *
     * @return reference to the element
     */
    reference operator[](size_type pos)
    {
        return reinterpret_cast<pointer>(buf_)[pos];
    }

    /**
     * @brief Access specified element without bounds check.
     *
     * @param pos Element index
     *
     * @return reference to the element
     */
    const_reference operator[](size_type pos) const
    {
        return reinterpret_cast<const_pointer>(buf_)[pos];
    }

    /**
     * @brief Access the first element
     *
     * @return Reference to first element
     */
    reference front()
    {
        return *begin();
    }

    /**
     * @brief Access the first element
     *
     * @return Reference to first element
     */
    const_reference front() const
    {
        return *begin();
    }

    /**
     * @brief Access the last element
     *
     * @return Reference to last element
     */
    reference back()
    {
        return *(end() - 1);
    }

    /**
     * @brief Access the last element
     *
     * @return Reference to last element
     */
    const_reference back() const
    {
        return *(end() - 1);
    }

    /**
     * @brief Direct access to the underlying array.
     *
     * @return Pointer to underlying array
     */
    T* data() noexcept
    {
        return reinterpret_cast<pointer>(buf_);
    }

    /**
     * @brief Direct access to the underlying array.
     *
     * @return Pointer to underlying array
     */
    T const* data() const noexcept
    {
        return reinterpret_cast<const_pointer>(buf_);
    }

    /**
     * @brief Get iterator to the beginning
     *
     * @return Iterator to beginning
     */
    iterator begin()
    {
        return iterator(reinterpret_cast<pointer>(buf_));
    }

    /**
     * @brief Get iterator to the beginning
     *
     * @return Iterator to beginning
     */
    const_iterator begin() const
    {
        return const_iterator(reinterpret_cast<const_pointer>(buf_));
    }

    /**
     * @brief Get iterator to the beginning
     *
     * @return Iterator to beginning
     */
    const_iterator cbegin() const
    {
        return const_iterator(reinterpret_cast<const_pointer>(buf_));
    }

    /**
     * @brief Get reverse iterator to the beginning
     *
     * @return Reverse iterator to beginning
     */
    reverse_iterator rbegin()
    {
        return reverse_iterator(end());
    }

    /**
     * @brief Get reverse iterator to the beginning
     *
     * @return Reverse iterator to beginning
     */
    const_reverse_iterator rbegin() const
    {
        return const_reverse_iterator(end());
    }

    /**
     * @brief Get reverse iterator to the beginning
     *
     * @return Reverse iterator to beginning
     */
    const_reverse_iterator crbegin() const
    {
        return const_reverse_iterator(end());
    }

    /**
     * @brief Get iterator to the end
     *
     * @return Iterator to end
     */
    iterator end()
    {
        return iterator(reinterpret_cast<pointer>(buf_) + size_);
    }

    /**
     * @brief Get iterator to the end
     *
     * @return Iterator to end
     */
    const_iterator end() const
    {
        return const_iterator(reinterpret_cast<const_pointer>(buf_) + size_);
    }

    /**
     * @brief Get iterator to the end
     *
     * @return Iterator to end
     */
    const_iterator cend() const
    {
        return const_iterator(reinterpret_cast<const_pointer>(buf_) + size_);
    }

    /**
     * @brief Get reverse iterator to the end
     *
     * @return Reverse iterator to end
     */
    reverse_iterator rend()
    {
        return reverse_iterator(begin());
    }

    /**
     * @brief Get reverse iterator to the end
     *
     * @return Reverse iterator to end
     */
    const_reverse_iterator rend() const
    {
        return const_reverse_iterator(begin());
    }

    /**
     * @brief Get reverse iterator to the end
     *
     * @return Reverse iterator to end
     */
    const_reverse_iterator crend() const
    {
        return const_reverse_iterator(begin());
    }

    /**
     * @brief Checks whether the container is empty
     *
     * @return true if container is empty otherwise false
     */
    bool empty() const noexcept
    {
        return size_ == 0;
    }

    /**
     * @brief Get number of elements in container
     *
     * @return Number of elements.
     */
    size_type size() const noexcept
    {
        return size_;
    }

    /**
     * @brief Get maximum possible number of elements
     *
     * @return Maximum possible number of elements
     */
    size_type max_size() const noexcept
    {
        return SIZE;
    }

    /**
     * @brief Reserve storage to hold new_cap elements
     *
     * @param new_cap new element capacity
     */
    void reserve(size_type new_cap)
    {
        if (new_cap > SIZE)
        {
#ifdef __EXCEPTIONS
            throw std::bad_alloc();
#else
            std::terminate();
#endif
        }
    }

    /**
     * @brief Get number of elements that can be held in currently allocated storage
     *
     * @return Number of elements
     */
    size_t capacity() const noexcept
    {
        return SIZE;
    }

    /**
     * @brief This function does nothing. Only to keep consistance with STL vector
     */
    void shrink_to_fit()
    {
    }

    /**
     * @brief Clears the content
     */
    void clear() noexcept
    {
        for (size_type idx = 0; idx < size_; ++idx)
        {
            reinterpret_cast<pointer>(buf_)[idx].~value_type();
        }
        size_ = 0;
    }

    /**
     * @brief Insert value before pos
     *
     * @param pos Iterator to insert value before
     * @param value The value to insert
     *
     * @return iterator to inserted element
     *
     * @throws std::bad_alloc if container size exceeded the maximum size.
     */
    iterator insert(const_iterator pos, T const& value)
    {
        if (size_ < SIZE)
        {
            iterator insert_pos(pos.base());
            if (pos == cend())
            {
                // construct at end
                ::new (insert_pos.base()) value_type(value);
                ++size_;
            }
            else
            {
                // 1. construct value_type at end with end() - 1
                ::new (end().base()) value_type(std::move(*(end() - 1)));
                // 2. move [pos, end -1) to [pos + 1, end) backward
                std::move_backward(insert_pos, end() - 1, end());
                // 3. assign value to pos
                *insert_pos = value;
                ++size_;
            }
            return insert_pos;
        }
#ifdef __EXCEPTIONS
        throw std::bad_alloc();
#else
            std::terminate();
#endif
    }

    /**
     * @brief Insert value before pos
     *
     * @param pos Iterator to insert value before
     * @param value The value to insert
     *
     * @return iterator to inserted element
     *
     * @throws std::bad_alloc if container size exceeded the maximum size.
     */
    iterator insert(const_iterator pos, T&& value)
    {
        if (size_ < SIZE)
        {
            iterator insert_pos(pos.base());
            if (pos == cend())
            {
                // construct at end
                ::new (insert_pos.base()) value_type(std::move(value));
                ++size_;
            }
            else
            {
                // 1. construct value_type at end with end() - 1
                ::new (end().base()) value_type(std::move(*(end() - 1)));
                // 2. move [pos, end -1) to [pos + 1, end) backward
                std::move_backward(insert_pos, end() - 1, end());
                // 3. assign value to pos
                *insert_pos = std::move(value);
                ++size_;
            }
            return insert_pos;
        }
#ifdef __EXCEPTIONS
        throw std::bad_alloc();
#else
            std::terminate();
#endif
    }

    /**
     * @brief Insert count copies of the value before pos
     *
     * @param pos Iterator to insert value before
     * @param count Number of elements
     * @param value The value to insert
     *
     * @return iterator pointing to the first element inserted or pos if count == 0
     *
     * @throws std::bad_alloc if container size + count > SIZE.
     */
    iterator insert(const_iterator pos, size_type count, T const& value)
    {
        if (size_ + count <= SIZE)
        {
            size_type elems_after = end() - pos;
            iterator  insert_pos(pos.base());
            iterator  old_finish = end();
            if (elems_after > count)
            {
                // 1. move last count elements to uninitialized memory [end(), end() + count]
                this->uninitialized_move(old_finish - count, old_finish, old_finish);
                this->size_ += count;
                // 2. move [pos, old_finish - count) to [pos + count, old_finish) backward
                std::move_backward(insert_pos, old_finish - count, old_finish);
                // 3. fill values in range [pos, pos + count)
                std::fill(insert_pos, insert_pos + count, value);
            }
            else
            {
                // 1. fill uninitialized memory [old_finish, old_finish + count) with value
                this->uninitialized_fill(old_finish, old_finish + count, value);
                this->size_ += count;
                // 2. move [pos, old_finish) to [pos + count, old_finish + count)
                std::move(insert_pos, old_finish, insert_pos + count);
                // 3. fill values in range [pos, old_finish)
                std::fill(insert_pos, old_finish, value);
            }
            return insert_pos;
        }
#ifdef __EXCEPTIONS
        throw std::bad_alloc();
#else
            std::terminate();
#endif
    }

    /**
     * @brief Inserts elements from range [first, last) before pos
     *
     * @tparam InputIt Input iterator type. Must satisfy c++ named requirements: LegacyInputIterator
     * @param pos Iterator to insert value before
     * @param first Start of range to inserted elements
     * @param last End of range to inserted elements
     *
     * @return iterator pointing to the first element inserted or pos if first == last
     *
     * @throws std::bad_alloc if container size + std::distance(first, last) > SIZE.
     */
    template <typename InputIt, typename = typename std::enable_if<IsInputIterator<InputIt>::value>::type>
    iterator insert(const_iterator pos, InputIt first, InputIt last)
    {
        size_t count = last - first;
        if (size_ + count <= SIZE)
        {
            size_type elems_after = end() - pos;
            iterator  insert_pos(pos.base());
            iterator  old_finish = end();
            if (elems_after > count)
            {
                // 1. move last count elements to uninitialized memory [end(), end() + count]
                this->uninitialized_move(old_finish - count, old_finish, old_finish);
                this->size_ += count;
                // 2. move [pos, old_finish - count) to [pos + count, old_finish) backward
                std::move_backward(insert_pos, old_finish - count, old_finish);
                // 3. copy [first, last) to pos
                std::copy(first, last, insert_pos);
            }
            else
            {
                InputIt mid = first;
                std::advance(mid, elems_after);
                // 1. copy uninitialized memory [old_finish, old_finish + count - elems_after) with value range [first +
                // elems_after, last)
                this->uninitialized_copy(mid, last, old_finish);
                this->size_ = size_ + count - elems_after;
                // 2. move [pos, old_finish) to uninitialized memory [old_finish + count - elems_after, old_finish +
                // count)
                this->uninitialized_move(insert_pos, old_finish, end());
                this->size_ = size_ + elems_after;
                // 3. copy [first, first + elems_after) to pos
                std::copy(first, mid, insert_pos);
            }
            return insert_pos;
        }
#ifdef __EXCEPTIONS
        throw std::bad_alloc();
#else
            std::terminate();
#endif
    }

    /**
     * @brief Inserts elements from initializer list before pos
     *
     * @param pos Iterator to insert value before
     * @param ilist The initailizer list
     *
     * @return iterator pointing to the first element inserted or pos if ilist is empty
     *
     * @throws std::bad_alloc if container size + ilist.size() > SIZE.
     */
    iterator insert(const_iterator pos, std::initializer_list<T> ilist)
    {
        if (size_ + ilist.size() <= SIZE)
        {
            return insert(pos, ilist.begin(), ilist.end());
        }
#ifdef __EXCEPTIONS
        throw std::bad_alloc();
#else
            std::terminate();
#endif
    }

    /**
     * @brief Construct element in-place
     *
     * @tparam Args Types of arguments
     * @param pos Iterator to insert value before
     * @param args arguments to forward to the constructor of the element
     *
     * @return iterator pointing to the emplaced element
     */
    template <typename... Args>
    iterator emplace(const_iterator pos, Args&&... args)
    {
        if (size_ < SIZE)
        {
            iterator insert_pos(pos.base());
            if (pos == cend())
            {
                // construct at end
                ::new (insert_pos.base()) value_type(std::forward<Args>(args)...);
                ++size_;
            }
            else
            {
                // 1. construct value_type at end with end() - 1
                ::new (end().base()) value_type(std::move(*(end() - 1)));
                // 2. move [pos, end -1) to [pos + 1, end) backward
                std::move_backward(insert_pos, end() - 1, end());
                // 3. assign value to pos
                *insert_pos = value_type(std::forward<Args>(args)...);
                ++size_;
            }
            return insert_pos;
        }
#ifdef __EXCEPTIONS
        throw std::bad_alloc();
#else
            std::terminate();
#endif
    }

    /**
     * @brief Remove element at pos
     *
     * @param pos Iterator pointing to the element to remove
     *
     * @return Iterator following the last removed element. if pos refers to last element, then the end() iterator is
     * returned.
     */
    iterator erase(const_iterator pos)
    {
        auto erase_pos = iterator(pos.base());
        if (erase_pos + 1 != end())
        {
            std::move(erase_pos + 1, end(), erase_pos);
        }
        (end() - 1)->~value_type();
        size_ -= 1;
        return erase_pos;
    }

    /**
     * @brief Remove elements in the range [first, last)
     *
     * @param first Start of range to remove elements
     * @param last End of range to remove elements
     *
     * @return Iterator following the last removed element. If last == end(), then the updated end() iterator is
     * returned. If [first, last) is an empty range, then last is returned.
     */
    iterator erase(const_iterator first, const_iterator last)
    {
        size_t count = last - first;
        if (first != last)
        {
            if (last != end())
            {
                std::move(iterator(last.base()), end(), iterator(first.base()));
            }
            iterator new_end(first.base() + count);
            for (; new_end != end(); ++new_end)
            {
                new_end->~value_type();
            }
            size_ -= count;
        }
        return iterator(first.base());
    }

    /**
     * @brief Adds an element to the end.
     *
     * @param value The element to add at the end.
     */
    void push_back(T const& value)
    {
        if (size_ < SIZE)
        {
            // construct at end
            ::new (reinterpret_cast<pointer>(buf_) + size_) value_type(value);
            ++size_;
            return;
        }
#ifdef __EXCEPTIONS
        throw std::bad_alloc();
#else
            std::terminate();
#endif
    }

    /**
     * @brief Adds an element to the end.
     *
     * @param value The element to add at the end.
     */
    void push_back(T&& value)
    {
        if (size_ < SIZE)
        {
            // construct at end
            ::new (reinterpret_cast<pointer>(buf_) + size_) value_type(std::move(value));
            ++size_;
            return;
        }
#ifdef __EXCEPTIONS
        throw std::bad_alloc();
#else
            std::terminate();
#endif
    }

    /**
     * @brief Construct an element in-place at the end
     *
     * @tparam Args Types of arguments
     * @param args arguments to forward to the constructor of the element
     */
    template <typename... Args>
    void emplace_back(Args&&... args)
    {
        if (size_ < SIZE)
        {
            // construct at end
            ::new (reinterpret_cast<pointer>(buf_) + size_) value_type(std::forward<Args>(args)...);
            ++size_;
            return;
        }
#ifdef __EXCEPTIONS
        throw std::bad_alloc();
#else
            std::terminate();
#endif
    }

    /**
     * @brief Removes the last element.
     */
    void pop_back()
    {
        (iterator(end().base()) - 1)->~value_type();
        --size_;
    }

    /**
     * @brief Change the number of elements stored
     *
     * @param count Number of elements
     */
    void resize(size_type count)
    {
        if (count <= SIZE)
        {
            if (count > static_cast<size_t>(size_))
            {
                // allocate at end
                value_type tmp{};
                this->uninitialized_fill(end(), begin() + count, tmp);
                size_ = static_cast<uint32_t>(count);
            }
            else
            {
                iterator it = begin() + count;
                for (; it != end(); ++it)
                {
                    it->~value_type();
                }
                size_ = static_cast<uint32_t>(count);
            }
            return;
        }
#ifdef __EXCEPTIONS
        throw std::bad_alloc();
#else
            std::terminate();
#endif
    }

    /**
     * @brief Change the number of elements stored
     *
     * @param count Number of elements
     * @param value The value to initialize the new elements
     */
    void resize(size_type count, value_type const& value)
    {
        if (count <= SIZE)
        {
            if (static_cast<uint32_t>(count) > size_)
            {
                // allocate at end
                this->uninitialized_fill(end(), begin() + count, value);
                size_ = static_cast<uint32_t>(count);
            }
            else
            {
                iterator it = begin() + count;
                for (; it != end(); ++it)
                {
                    it->~value_type();
                }
                size_ = static_cast<uint32_t>(count);
            }
            return;
        }
#ifdef __EXCEPTIONS
        throw std::bad_alloc();
#else
            std::terminate();
#endif
    }

    /**
     * @brief Swaps the contents
     *
     * @param other The other container
     */
    void swap(FixedSequence& other) noexcept
    {
        iterator it1 = this->begin();
        iterator it2 = other.begin();
        if (other.size() > size_)
        {
            for (; it1 != end(); ++it1, ++it2)
            {
                std::swap(*it1, *it2);
            }
            uninitialized_move(it2, other.end(), it1);
            other.erase(it2, other.end());
        }
        else
        {
            for (; it2 != other.end(); ++it1, ++it2)
            {
                std::swap(*it1, *it2);
            }
            uninitialized_move(it1, this->end(), it2);
            other.erase(it1, this->end());
        }
        std::swap(size_, other.size_);
    }

    /**
     * @brief Swaps the contents with FixedSequence of different size.
     *
     * @tparam SIZE_ Size of the other FixedSequence
     * @param other The other FixedSequence object.
     *
     * @throws std::bad_alloc if size() > SIZE_.
     * @throws std::bad_alloc if other.size() > SIZE.
     */
    template <size_t SIZE_, typename = typename std::enable_if<(SIZE_ > SIZE)>::type>
    void swap(FixedSequence<T, SIZE_>& other)
    {
        if ((this->size() > SIZE_) || other.size() > SIZE)
        {
#ifdef __EXCEPTIONS
            throw std::bad_alloc();
#else
            std::terminate();
#endif
        }
        iterator                                   it1 = this->begin();
        typename FixedSequence<T, SIZE_>::iterator it2 = other.begin();
        if (other.size() > size_)
        {
            for (; it1 != end(); ++it1, ++it2)
            {
                std::swap(*it1, *it2);
            }
            uninitialized_move(it2, other.end(), it1);
            other.erase(it2, other.end());
        }
        else
        {
            for (; it2 != other.end(); ++it1, ++it2)
            {
                std::swap(*it1, *it2);
            }
            uninitialized_move(it1, this->end(), it2);
            this->erase(it1, this->end());
        }
        uint32_t s = other.size();
        other.resize(size_);
        size_ = s;
    }

private:
    /**
     * @brief move [first, last) to uninitialized memory started from d_first
     */
    template <typename InputIt, typename NoThrowForwardIt>
    NoThrowForwardIt uninitialized_move(InputIt first, InputIt last, NoThrowForwardIt d_first)
    {
        NoThrowForwardIt current = d_first;
        for (; first != last; ++first, (void)++current)
        {
            auto addr = static_cast<void*>(std::addressof(*current));
            ::new (addr) value_type(std::move(*first));
        }
        return current;
    }

    /**
     * @brief fill uninitialied memory in range [first, last)
     */
    template <typename InputIt>
    InputIt uninitialized_fill(InputIt first, InputIt last, value_type const& value)
    {
        InputIt current = first;
        for (; current != last; ++current)
        {
            ::new (static_cast<void*>(std::addressof(*current))) value_type(value);
        }
        return current;
    }

    /**
     * @brief copy [first, last) to uninitialied memory d_first
     */
    template <typename InputIt, typename NoThrowForwardIt>
    NoThrowForwardIt uninitialized_copy(InputIt first, InputIt last, NoThrowForwardIt d_first)
    {
        NoThrowForwardIt current = d_first;
        for (; first != last; ++first, (void)++current)
        {
            ::new (static_cast<void*>(std::addressof(*current))) value_type(*first);
        }
        return current;
    }

private:
    uint32_t size_;
    uint8_t  buf_[sizeof(T) * SIZE];  // FIXME: buffer alignment?
};

template <typename T, size_t SIZE>
template <bool_t CONST>
class FixedSequence<T, SIZE>::Iterator
{
public:
    using difference_type   = typename FixedSequence<T, SIZE>::difference_type;
    using value_type        = typename FixedSequence<T, SIZE>::value_type;
    using reference         = typename std::conditional<CONST, typename FixedSequence<T, SIZE>::const_reference,
                                                typename FixedSequence<T, SIZE>::reference>::type;
    using pointer           = typename std::conditional<CONST, typename FixedSequence<T, SIZE>::const_pointer,
                                              typename FixedSequence<T, SIZE>::pointer>::type;
    using iterator_category = std::random_access_iterator_tag;

    Iterator() : base_(nullptr)
    {
    }

    Iterator(const pointer i) : base_(const_cast<pointer>(i))
    {
    }

    Iterator(Iterator<!CONST> const& other) : base_(const_cast<pointer>(other.base()))
    {
    }

    template <bool_t InputConst>
    bool_t operator==(Iterator<InputConst> const& other) const
    {
        return base_ == other.base();
    }

    template <bool_t InputConst>
    bool_t operator!=(Iterator<InputConst> const& other) const
    {
        return !this->operator==(other);
    }

    template <bool_t InputConst>
    bool_t operator<(Iterator<InputConst> const& other) const
    {
        return base_ < other.base();
    }

    template <bool_t InputConst>
    bool_t operator>(Iterator<InputConst> const& other) const
    {
        return base_ > other.base();
    }

    template <bool_t InputConst>
    bool_t operator<=(Iterator<InputConst> const& other) const
    {
        return !this->operator>(other);
    }

    template <bool_t InputConst>
    bool_t operator>=(Iterator<InputConst> const& other) const
    {
        return !this->operator<(other);
    }

    Iterator& operator++()
    {
        ++(this->base_);
        return *this;
    }

    Iterator operator++(int)
    {
        Iterator tmp(*this);
        ++(this->base_);
        return tmp;
    }

    Iterator& operator--()
    {
        --(this->base_);
        return *this;
    }

    Iterator operator--(int)
    {
        Iterator tmp(*this);
        --(this->base_);
        return tmp;
    }

    Iterator& operator+=(difference_type rhs)
    {
        this->base_ += rhs;
        return *this;
    }

    Iterator operator+(difference_type rhs) const
    {
        Iterator tmp(*this);
        tmp.base_ += rhs;
        return tmp;
    }

    friend Iterator operator+(difference_type lhs, const Iterator& rhs)
    {
        Iterator tmp(rhs);
        tmp.base_ += lhs;
        return tmp;
    }

    Iterator& operator-=(difference_type rhs)
    {
        this->base_ -= rhs;
        return *this;
    }

    Iterator operator-(difference_type rhs) const
    {
        Iterator tmp(*this);
        tmp.base_ -= rhs;
        return tmp;
    }

    difference_type operator-(Iterator rhs) const
    {
        return this->base_ - rhs.base();
    }

    reference operator*() const
    {
        return *base_;
    }

    pointer operator->() const
    {
        return base_;
    }

    reference operator[](difference_type idx) const
    {
        return base_[idx];
    }

    pointer base() const
    {
        return base_;
    }

private:
    pointer base_;
};

/**
 * @brief Checks if the contents of lhs and rhs are equal.
 *
 * @tparam T Type of elements
 * @tparam SIZE Maximum size of lhs
 * @tparam SIZE_ Maximum size of rhs
 * @param lhs Left hand side
 * @param rhs Right hand side
 *
 * @return true if lhs and rhs are equal otherwise false.
 */
template <typename T, size_t SIZE, size_t SIZE_>
inline bool operator==(FixedSequence<T, SIZE> const& lhs, FixedSequence<T, SIZE_> const& rhs)
{
    return (lhs.size() == rhs.size()) && std::equal(lhs.begin(), lhs.end(), rhs.begin());
}

/**
 * @brief Checks if the contents of lhs and rhs are not equal.
 *
 * @tparam T Type of elements
 * @tparam SIZE Maximum size of lhs
 * @tparam SIZE_ Maximum size of rhs
 * @param lhs Left hand side
 * @param rhs Right hand side
 *
 * @return true if lhs and rhs are not equal otherwise false.
 */
template <typename T, size_t SIZE, size_t SIZE_>
inline bool operator!=(FixedSequence<T, SIZE> const& lhs, FixedSequence<T, SIZE_> const& rhs)
{
    return !(lhs == rhs);
}

/**
 * @brief Compares the contents of lhs and rhs lexicographically
 *
 * @tparam T Type of elements
 * @tparam SIZE Maximum size of lhs
 * @tparam SIZE_ Maximum size of rhs
 * @param lhs Left hand side
 * @param rhs Right hand side
 *
 * @return true if lhs < rhs otherwise false
 */
template <typename T, size_t SIZE, size_t SIZE_>
inline bool operator<(FixedSequence<T, SIZE> const& lhs, FixedSequence<T, SIZE_> const& rhs)
{
    return std::lexicographical_compare(lhs.begin(), lhs.end(), rhs.begin(), rhs.end());
}

/**
 * @brief Compares the contents of lhs and rhs lexicographically
 *
 * @tparam T Type of elements
 * @tparam SIZE Maximum size of lhs
 * @tparam SIZE_ Maximum size of rhs
 * @param lhs Left hand side
 * @param rhs Right hand side
 *
 * @return true if lhs > rhs otherwise false
 */
template <typename T, size_t SIZE, size_t SIZE_>
inline bool operator>(FixedSequence<T, SIZE> const& lhs, FixedSequence<T, SIZE_> const& rhs)
{
    return rhs < lhs;
}

/**
 * @brief Compares the contents of lhs and rhs lexicographically
 *
 * @tparam T Type of elements
 * @tparam SIZE Maximum size of lhs
 * @tparam SIZE_ Maximum size of rhs
 * @param lhs Left hand side
 * @param rhs Right hand side
 *
 * @return true if lhs <= rhs otherwise false
 */
template <typename T, size_t SIZE, size_t SIZE_>
inline bool operator<=(FixedSequence<T, SIZE> const& lhs, FixedSequence<T, SIZE_> const& rhs)
{
    return !(rhs < lhs);
}

/**
 * @brief Compares the contents of lhs and rhs lexicographically
 *
 * @tparam T Type of elements
 * @tparam SIZE Maximum size of lhs
 * @tparam SIZE_ Maximum size of rhs
 * @param lhs Left hand side
 * @param rhs Right hand side
 *
 * @return true if lhs >= rhs otherwise false
 */
template <typename T, size_t SIZE, size_t SIZE_>
inline bool operator>=(FixedSequence<T, SIZE> const& lhs, FixedSequence<T, SIZE_> const& rhs)
{
    return !(lhs < rhs);
}

template <typename ValueType, size_t CapacityValue>
inline void to_json(nlohmann::json& j, FixedSequence<ValueType, CapacityValue> const& v)
{
    for (auto it = v.begin(); it != v.end(); ++it)
    {
        j.push_back(*it);
    }
}

template <typename ValueType, size_t CapacityValue>
inline void from_json(nlohmann::json const& j, FixedSequence<ValueType, CapacityValue>& v)
{
    if (j.is_array())
    {
        for (auto const& element : j)
        {
            v.push_back(element);
        }
    }
}

template <typename T, size_t SIZE>
using FixedVector = FixedSequence<T, SIZE>;

}  // namespace zos

#endif
