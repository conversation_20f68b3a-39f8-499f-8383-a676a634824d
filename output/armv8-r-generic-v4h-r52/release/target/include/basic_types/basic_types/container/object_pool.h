#ifndef BASIC_TYPES_CONTAINER_OBJECT_POOL_HPP_
#define BASIC_TYPES_CONTAINER_OBJECT_POOL_HPP_

#include <memory>
#include <stack>

namespace zos
{

/**
 * @brief Object pool class 
 *
 * @tparam T element type
 */
template <typename T>
class ObjectPool : public std::enable_shared_from_this<ObjectPool<T>>
{
private:
    struct Deleter
    {
    public:
        Deleter(std::weak_ptr<ObjectPool<T>> pool) : pool_(pool)
        {
        }

        void operator()(T* ptr)
        {
            auto pool_ptr = pool_.lock();
            if (pool_ptr)
            {
                std::lock_guard<std::mutex>(pool_ptr->objects_mtx_);
                pool_ptr->objects_.push(ptr);
            }
            else
            {
                delete ptr;
            }
        }

    private:
        std::weak_ptr<ObjectPool<T>> pool_;
    };

    friend class Deleter;

public:
    /**
     * @brief Constructor with pool size and element construct arguments 
     *
     * @tparam Args element construct argument types
     * @param size pool capacity
     * @param args element construct arguments
     */
    template <typename... Args>
    ObjectPool(size_t size, Args&&... args) : objects_{}, capacity_(size), objects_mtx_{}
    {
        for (size_t i = 0; i < size; ++i)
        {
            objects_.push(new T(std::forward<Args>(args)...));
        }
    }

    ~ObjectPool()
    {
        std::lock_guard<std::mutex> lck(objects_mtx_);
        while (objects_.size())
        {
            delete objects_.top();
            objects_.pop();
        }
    }

    /**
     * @brief Get an element
     *
     * @returns  shared ptr to element object
     */
    std::shared_ptr<T> Get()
    {
        std::lock_guard<std::mutex> lck(objects_mtx_);
        if (objects_.size() == 0)
        {
            throw std::out_of_range("Cannot acquire object from object pool");
        }
        T* ptr = objects_.top();
        objects_.pop();
        return std::shared_ptr<T>(ptr, Deleter(this->shared_from_this()));
    }

    /**
     * @brief Remaining element size
     *
     * @returns  Remaining size
     */
    size_t Size()
    {
        std::lock_guard<std::mutex> lck(objects_mtx_);
        return objects_.size();
    }

    /**
     * @brief Pool capacity
     *
     * @returns  pool capacity 
     */
    size_t Capacity()
    {
        return capacity_;
    }

    /**
     * @brief Used elements 
     *
     * @returns used elements   
     */
    size_t UseCount()
    {
        std::lock_guard<std::mutex> lck(objects_mtx_);
        return capacity_ - objects_.size();
    }

private:
    std::stack<T*> objects_;
    size_t         capacity_;
    std::mutex     objects_mtx_;
};
}  // namespace zos

#endif
