#ifndef BASIC_TYPES_CONTAINER_STRING_HPP_
#define BASIC_TYPES_CONTAINER_STRING_HPP_

#include <algorithm>
#include <basic_types/types.hpp>
#include <cstring>
#include <iterator>
#include <stdexcept>
#include <utility>
#include <nlohmann/json.hpp>

namespace zos
{
#if __cplusplus >= 201402L
template <bool B, class T = void>
using enable_if_t = std::enable_if_t<B, T>;
#else
template <bool B, class T = void>
using enable_if_t = typename ::std::enable_if<B, T>::type;
#endif

/**
 * @brief Fixed-size string container with stack-allocated storage
 * @tparam SIZE Maximum character capacity (excluding null terminator)
 * @details
 * - Provides STL-like interface with constexpr capabilities
 * - Ensures null-termination in all operations
 * - Designed for memory-constrained environments avoiding heap allocations
 */
template <size_t SIZE, typename CharT = char, typename Traits = std::char_traits<CharT>>
class FixedString
{
    static_assert(SIZE != 0, "FixedString SIZE must be non-zero");

public:
    // region Type Aliases
    using traits_type     = Traits;
    using value_type      = CharT;
    using size_type       = size_t;
    using difference_type = ptrdiff_t;

    using reference       = CharT&;
    using const_reference = const CharT&;
    using pointer         = CharT*;
    using const_pointer   = const CharT*;

    using iterator               = CharT*;
    using const_iterator         = const CharT*;
    using reverse_iterator       = std::reverse_iterator<iterator>;
    using const_reverse_iterator = std::reverse_iterator<const_iterator>;
    // endregion

    static constexpr size_type npos = size_type(-1);

    // region Constructors/Destructor

    FixedString(const FixedString&) = default;

    FixedString(std::initializer_list<value_type> il)
    {
        if (il.size() > SIZE)
        {
            throw_bad_alloc();
        }
        else
        {
            auto it = il.begin();
            for (size_ = 0; size_ < il.size(); ++size_, ++it)
            {
                data_[size_] = *it;
            }
            terminate();
        }
    }
    /**
     * @brief Default constructor (empty string)
     * @details
     * - Initializes buffer with double null terminator
     * - Sets size to zero
     * @note Constexpr-capable for compile-time initialization
     */
    FixedString() noexcept : size_(0), data_{0}
    {
        terminate();
    }

    /**
     * @brief Constructs from single character
     * @param c Source character to initialize
     * @throws std::length_error if SIZE < 1
     * @details
     * - Creates temporary NTBS (Null-Terminated Byte String)
     * - Validates capacity through assign()
     * - Updates size after successful assignment
     */
    FixedString(size_type count, value_type c) : size_(count)
    {
        if (count > SIZE)
        {
            throw_bad_alloc();
        }
        else
        {
            for (size_type i = 0; i < size_; ++i)
            {
                data_[i] = c;
            }
            terminate();
        }
    }

    /**
     * @brief Constructs from C-style string
     * @param str Source null-terminated string
     * @throws std::length_error if input exceeds capacity
     * @details
     * - Copies characters until null terminator or capacity
     * - Ensures proper null-termination
     */
    FixedString(const_pointer str)
    {
        assign(str, traits_type::length(str));
    }
    FixedString(const_pointer str, size_type count)
    {
        assign(str, count);
    }
    FixedString(const std::string& str)
    {
        assign(str.data(), str.length());
    }

    // /**
    // * @brief Copy constructor (defaulted)
    // * @param other Source FixedString to copy
    // * @note Safe due to fixed-size buffer and trivial copy
    // */
    // FixedString(const FixedString&) = default;

    /**
     * @brief Constructs from different-sized FixedString
     * @tparam OTHER_SIZE Source string capacity
     * @param other Source FixedString to copy
     * @details
     * - Copies up to min(SIZE, OTHER_SIZE) characters
     * - Maintains null termination regardless of size
     */
    template <size_t OTHER_SIZE>
    FixedString(const FixedString<OTHER_SIZE>& other) noexcept
    {
        if (other.size() > SIZE)
        {
            throw_bad_alloc();
        }
        else
        {
            size_ = other.size();
            traits_type::copy(data_, other.c_str(), size_);
            terminate();
        }
    }

    /**
     * @brief Copy assignment operator
     * @param rhs Source FixedString
     * @return Reference to this object
     * @throws std::bad_alloc If memory allocation fails
     */
    template <size_t S2>
    FixedString& operator=(const FixedString<S2>& rhs)
    {
        if (static_cast<void*>(this) != static_cast<void*>(&rhs))
        {
            if (SIZE < rhs.size())
            {
                throw_bad_alloc();
            }
            traits_type::copy(data_, rhs.data_, rhs.size());
            size_ = rhs.size();
            terminate();
        }
        return *this;
    }

    // remove noexcept, cause it's not constexpr, it is different from standard
    template <size_t S2>
    FixedString& operator=(FixedString<S2>&& rhs)
    {
        if (static_cast<void*>(this) != static_cast<void*>(&rhs))
        {
            if (SIZE < rhs.size())
            {
                throw_bad_alloc();
            }
            traits_type::copy(data_, rhs.data(), rhs.size());
            size_ = rhs.size();
            terminate();
            rhs.clear();
        }
        return *this;
    }

    FixedString& operator=(const_pointer s)
    {
        if (this->data() != s)
        {
            if (SIZE < traits_type::length(s))
            {
                throw_bad_alloc();
            }
            assign(s);
        }
        return *this;
    }
    template <size_t N>
    FixedString& operator=(const char (&str)[N])
    {
        return assign(str);
    }
    FixedString& operator=(const std::string& str)
    {
        return operator=(str.c_str());
    }

    FixedString& operator=(value_type c)
    {
        clear();
        push_back(c);
        return *this;
    }

    FixedString& operator=(std::initializer_list<value_type> ilist)
    {
        return assign(ilist);
    }
    // remove noexcept, cause it's not constexpr, it is different from standard
    template <size_t S2>
    FixedString& assign(const FixedString<S2>& rhs)
    {
        return *this = rhs;
    }
    template <size_t S2>
    FixedString& assign(FixedString<S2>&& rhs)
    {
        return *this = std::move(rhs);
    }
    FixedString& assign(size_type count, value_type c)
    {
        if (count > SIZE)
        {
            throw_bad_alloc();
        }
        else
        {
            size_ = count;
            std::fill_n(data_, size_, c);
            terminate();
        }
        return *this;
    }
    /**
     * @brief Replaces contents with C-string
     * @param str Null-terminated source string
     * @throws std::length_error If strlen(str) > capacity()
     */
    // count is in str, not in this
    FixedString& assign(const_pointer str, size_type count)
    {
        const size_type len = traits_type::length(str);
        count               = std::min(count, len);
        validate_capacity(count);
        traits_type::copy(data_, str, count);
        size_ = count;
        terminate();
        return *this;
    }
    FixedString& assign(const_pointer str)
    {
        return assign(str, traits_type::length(str));
    }
    FixedString& assign(const std::string& str)
    {
        return assign(str.c_str(), str.length());
    }
    template <size_t S2>
    FixedString& assign(const FixedString<S2>& rhs, size_type pos, size_type count)
    {
        if (pos > rhs.size())
        {
            throw std::out_of_range("pos out of range");
        }
        const size_type len = std::min(rhs.size() - pos, count);
        validate_capacity(len);
        traits_type::copy(data_, rhs.c_str() + pos, len);
        size_ = len;
        terminate();
        return *this;
    }
    template <class InputIt>
    FixedString& assign(InputIt first, InputIt last)
    {
        const size_type len = std::distance(first, last);
        validate_capacity(len);
        traits_type::copy(data_, &(*first), len);
        size_ = len;
        terminate();
        return *this;
    }
    FixedString& assign(std::initializer_list<value_type> ilist)
    {
        const size_type len = ilist.size();
        validate_capacity(len);
        traits_type::copy(data_, &(*ilist.begin()), len);
        size_ = len;
        terminate();
        return *this;
    }
    //////////////// region element access
    /**
     * @brief Checked element access
     * @param pos Character position
     * @return Reference to character at pos
     * @throws std::out_of_range If pos >= size()
     */
    reference at(size_type pos)
    {
        check_bounds(pos);
        return data_[pos];
    }

    /**
     * @brief Checked const element access
     * @param pos Character position
     * @return Const reference to character at pos
     * @throws std::out_of_range If pos >= size()
     */
    const_reference at(size_type pos) const
    {
        check_bounds(pos);
        return data_[pos];
    }

    /**
     * @brief Subscript access without bounds checking
     * @param pos Character position (0 <= pos < size())
     * @return Reference to character at position
     * @warning No bounds checking - undefined behavior for pos >= size()
     */
    reference operator[](size_type pos)
    {
        check_bounds(pos);
        return data_[pos];
    }

    /**
     * @brief Subscript access const element without bounds checking
     * @param pos Character position (0 <= pos < size())
     * @return Reference to character at position
     * @warning No bounds checking - undefined behavior for pos >= size()
     */
    const_reference operator[](size_type pos) const
    {
        check_bounds(pos);
        return data_[pos];
    }

    /**
     * @brief Returns pointer to character data
     * @return Pointer to mutable/immutable character buffer
     */
    const_pointer data() const noexcept
    {
        return data_;
    }

    /// @brief Access mutable data buffer
    value_type* data() noexcept
    {
        return data_;
    }

    /**
     * @brief Returns pointer to null-terminated data
     * @return const CharT* compatible with C-string functions
     */
    const_pointer c_str() const noexcept
    {
        return data();
    }
    // endregion

    //////////////// region Iterators
    /**
     * @brief Returns iterator to beginning
     * @return Mutable iterator
     */
    iterator begin() noexcept
    {
        return iterator(data_);
    }

    /**
     * @brief Returns const iterator to beginning
     * @return Immutable iterator
     */
    const_iterator begin() const noexcept
    {
        return const_iterator(data_);
    }
    const_iterator cbegin() const noexcept
    {
        return const_iterator(data_);
    }

    /**
     * @brief Returns iterator to end
     * @return Mutable end iterator
     */
    iterator end() noexcept
    {
        return iterator(data_ + size_);
    }

    /**
     * @brief Returns const iterator to end
     * @return Immutable end iterator
     */
    const_iterator end() const noexcept
    {
        return const_iterator(data_ + size_);
    }
    const_iterator cend() const noexcept
    {
        return const_iterator(data_ + size_);
    }

    /**
     * @brief Returns reverse iterator to beginning
     * @return Mutable reverse iterator
     */
    reverse_iterator rbegin() noexcept
    {
        return reverse_iterator(end());
    }
    reverse_iterator crbegin() noexcept
    {
        return reverse_iterator(end());
    }

    /**
     * @brief Returns const reverse iterator to beginning
     * @return Immutable reverse iterator
     */
    const_reverse_iterator rbegin() const noexcept
    {
        return const_reverse_iterator(end());
    }
    const_reverse_iterator crbegin() const noexcept
    {
        return const_reverse_iterator(end());
    }

    /**
     * @brief Returns reverse iterator to end
     * @return Mutable reverse end iterator
     */
    reverse_iterator rend() noexcept
    {
        return reverse_iterator(begin());
    }
    reverse_iterator crend() noexcept
    {
        return reverse_iterator(begin());
    }

    /**
     * @brief Returns const reverse iterator to end
     * @return Immutable reverse end iterator
     */
    const_reverse_iterator rend() const noexcept
    {
        return const_reverse_iterator(begin());
    }
    const_reverse_iterator crend() const noexcept
    {
        return const_reverse_iterator(begin());
    }
    // endregion

    //////////////// region capacity
    /**
     * @brief Checks if string is empty
     * @return true if size() == 0, false otherwise
     */
    bool empty() const noexcept
    {
        return size_ == 0;
    }

    /**
     * @brief Gets current string length
     * @return Number of characters (excluding null terminator)
     * @note Constant time complexity O(1)
     */
    size_type size() const noexcept
    {
        return size_;
    }

    /**
     * @brief Synonym for size()
     * @return Current size in characters
     */
    size_type length() const noexcept
    {
        return size_;
    }

    size_type max_size() const noexcept
    {
        return SIZE;
    }

    /**
     * @brief Returns maximum allowed characters
     * @return Configured capacity
     */
    size_type capacity() const noexcept
    {
        return SIZE;
    }
    // endregion

    //////////////// region modifiers
    /**
     * @brief Clears string contents
     * @post size() == 0, data() == '\0'
     */
    void clear() noexcept
    {
        size_ = 0;
        terminate();
    }

    // TODO: implement insert and erase
    iterator insert(const_iterator pos, value_type ch)
    {
        return insert(pos, 1, ch);
    }
    iterator insert(const_iterator pos, size_type count, value_type ch)
    {
        size_type offset = pos - begin();
        if (count == 0)
        {
            return begin() + offset;
        }

        if (size_ + count > SIZE)
        {
            throw_bad_alloc();
        }

        std::memmove(static_cast<void*>(data_ + offset + count), static_cast<void*>(data_ + offset),
                     (size_ - offset) * sizeof(value_type));

        std::fill_n(data_ + offset, count, ch);

        size_ += count;
        terminate();
        return begin() + offset;
    }
    template <class InputIt>
    iterator insert(const_iterator pos, InputIt first, InputIt last)
    {
        size_type offset = pos - begin();
        if (first == last)
        {
            return begin() + offset;
        }
        size_type count = std::distance(first, last);
        if (size_ + count > SIZE)
        {
            throw_bad_alloc();
        }
        std::memmove(static_cast<void*>(data_ + offset + count), static_cast<void*>(data_ + offset),
                     (size_ - offset) * sizeof(value_type));

        std::copy(first, last, data_ + offset);
        size_ += count;
        terminate();
        return begin() + offset;
    }
    iterator insert(const_iterator pos, std::initializer_list<value_type> ilist)
    {
        return insert(pos, ilist.begin(), ilist.end());
    }
    /**
     * @brief Appends single character
     * @param ch Character to append
     * @throws std::length_error If remaining capacity == 0
     */
    void push_back(value_type ch)
    {
        static_assert(SIZE > 0, "Zero-capacity FixedString cannot hold characters");

        if (size_ >= SIZE)
        {
            throw_length_error("FixedString capacity exhausted");
        }

        data_[size_]   = ch;
        data_[++size_] = '\0';
    }

    /**
     * @brief Removes last character
     * @post If not empty, size() decreases by 1
     */
    void pop_back() noexcept
    {
        if (!empty())
        {
            size_--;
            terminate();
        }
    }

    /**
     * @brief Appends C-string to the end
     * @param str Null-terminated string to append
     * @throws std::length_error If remaining capacity < strlen(str)
     */
    void append(const_pointer str)
    {
        const size_type len = traits_type::length(str);
        validate_available(len);
        traits_type::copy(data_ + size_, str, len);
        size_ += len;
        terminate();
    }
    void append(const_pointer str, size_type count)
    {
        size_type len = traits_type::length(str);
        if (count < len)
        {
            len = count;
        }
        validate_available(len);
        traits_type::copy(data_ + size_, str, len);
        size_ += len;
        terminate();
    }
    void append(size_type count, value_type ch)
    {
        validate_available(count);
        for (size_t i = 0; i < count; i++)
        {
            data_[size_ + i] = ch;
        }
        size_ += count;
        terminate();
    }

    template <size_t S2>
    FixedString& operator+=(const FixedString<S2>& rhs)
    {
        append(rhs.c_str(), rhs.size());
        return *this;
    }
    FixedString& operator+=(value_type ch)
    {
        append(&ch, 1);
        return *this;
    }
    FixedString& operator+=(const_pointer str)
    {
        append(str);
        return *this;
    }

    FixedString& replace(size_type pos, size_type count, const_pointer str)
    {
        if (pos > size_)
        {
            throw_out_of_range("FixedString::replace pos");
        }
        else
        {
            // std function: first delete pos->pos+count, then copy rhs to pos
            const size_t copy_len   = traits_type::length(str);
            const size_t left_len   = (size_ - pos > count) ? (size_ - pos - count) : 0;
            const size_t new_length = pos + left_len + copy_len;

            if (new_length > SIZE)
            {
                throw_length_error("FixedString::replace new_length > max_size()");
            }
            else
            {
                if (left_len && copy_len != count)
                {  // has data left
                    std::memmove(static_cast<void*>(data_ + pos + copy_len), static_cast<void*>(data_ + pos + count),
                                 left_len * sizeof(value_type));
                }
                traits_type::copy(data_ + pos, str, copy_len);
                size_ = new_length;
                terminate();
            }
        }
        return *this;
    }
    template <size_t S2>
    FixedString& replace(size_type pos, size_type count, const FixedString<S2>& rhs)
    {
        return replace(pos, count, rhs.c_str());
    }

    FixedString& replace(size_type pos, size_type count, const_pointer str, size_type pos2, size_type count2)
    {
        const size_t str_len = traits_type::length(str);
        if (pos > size())
        {
            throw_out_of_range("FixedString::replace pos");
        }

        if (pos2 > str_len)
        {
            throw_out_of_range("FixedString::replace pos2");
        }

        // std function: first delete pos->pos+count, then copy rhs pos2->count2 to pos
        const size_t copy_len   = (count2 > (str_len - pos2)) ? (str_len - pos2) : count2;
        const size_t left_len   = (size_ - pos > count) ? (size_ - pos - count) : 0;
        const size_t new_length = pos + left_len + copy_len;

        if (new_length > SIZE)
        {
            throw_length_error("FixedString::replace new_length > max_size()");
        }
        else
        {
            if (left_len && copy_len != count)
            {  // has data left
                std::memmove(static_cast<void*>(data_ + pos + copy_len), static_cast<void*>(data_ + pos + count),
                             left_len * sizeof(value_type));
            }
            traits_type::copy(data_ + pos, str + pos2, copy_len);
            size_ = new_length;
            terminate();
        }

        return *this;
    }
    template <size_t S2>
    FixedString& replace(size_type pos, size_type count, const FixedString<S2>& rhs, size_type pos2, size_type count2)
    {
        return replace(pos, count, rhs.c_str(), pos2, count2);
    }

    size_type copy(pointer dest, size_type count, size_type pos = 0) const
    {
        if (pos > size())
        {
            throw_out_of_range("FixedString::copy pos");
        }
        const size_t copy_len = (count > (size_ - pos)) ? (size_ - pos) : count;
        traits_type::copy(dest, data_ + pos, copy_len);
        return copy_len;
    }

    void resize(size_type count)
    {
        if (count > SIZE)
        {
            throw_length_error("FixedString::resize count > max_size()");
        }
        if (count > size_)
        {
            std::memset(data_ + size_, 0, (count - size_) * sizeof(value_type));
        }
        size_ = count;
        terminate();
    }

    void resize(size_type count, value_type ch)
    {
        if (count > SIZE)
        {
            throw_length_error("FixedString::resize count > max_size()");
        }
        if (count > size_)
        {
            std::memset(data_ + size_, ch, (count - size_) * sizeof(value_type));
        }
        size_ = count;
        terminate();
    }

    template <size_t S1, typename = enable_if_t<SIZE == S1>>
    void swap(FixedString<S1>& rhs)
    {
        if (this != &rhs)
        {
            FixedString<S1> temp(rhs);
            rhs.assign(c_str());

            assign(temp.c_str());
        }
    }

    template <size_t S1, size_t S2>
    friend auto swap(FixedString<S1>& lhs, FixedString<S2>& rhs) noexcept -> enable_if_t<S1 == S2, void>;

    // endregion

    //////////////// region find
    template <size_t S2>
    size_type find(const FixedString<S2>& rhs, size_type pos = 0) const
    {
        return find(rhs.c_str(), pos);
    }
    size_type find(const_pointer s, size_type pos = 0) const
    {
        return find(s, pos, traits_type::length(s));
    }
    size_type find(const_pointer s, size_type pos, size_type count) const
    {
        if (s == nullptr)
        {
            return npos;
        }

        if (count == 0)
        {
            return npos;
        }

        if (pos > size_ || size_ - pos < count)
        {
            return npos;
        }

        for (size_type i = pos; i <= size_ - count; ++i)
        {
            if (traits_type::compare(data_ + i, s, count) == 0)
            {
                return i;
            }
        }
        return npos;
    }
    size_type find(value_type ch, size_type pos = 0) const
    {
        if (pos > size_)
        {
            return npos;
        }

        for (size_type i = pos; i < size_; ++i)
        {
            if (traits_type::eq(data_[i], ch))
            {
                return i;
            }
        }

        return npos;
    }
    template <size_t S2>
    size_type rfind(const FixedString<S2>& rhs, size_type pos = npos) const
    {
        return rfind(rhs.c_str(), pos);
    }
    size_type rfind(const_pointer s, size_type pos = npos) const
    {
        return rfind(s, pos, s == nullptr ? 0 : traits_type::length(s));
    }
    size_type rfind(const_pointer s, size_type pos, size_type count) const
    {
        const size_type rhs_size = (s == nullptr) ? 0 : traits_type::length(s);

        if (s == nullptr || rhs_size == 0 || count == 0)
        {
            return (pos <= size_) ? pos : size_;
        }
        else if (size_ == 0)
        {
            return npos;
        }

        if (size_ < count)
        {
            return npos;
        }

        pos = (pos >= size_) ? size_ : pos;
        for (size_type i = std::min(pos, static_cast<size_type>(size_ - count)); i != npos; --i)
        {
            if (traits_type::compare(data_ + i, s, count) == 0)
            {
                return i;
            }
        }
        return npos;
    }
    size_type rfind(value_type ch, size_type pos = npos) const
    {
        if (size_ == 0)
            return npos;

        pos = (pos >= size_) ? size_ - 1 : pos;
        for (size_type i = pos; i != npos; --i)
        {
            if (traits_type::eq(data_[i], ch))
            {
                return i;
            }
        }

        return npos;
    }

    template <size_t S2>
    size_type find_first_of(const FixedString<S2>& str, size_type pos = 0) const
    {
        return find_first_of(str.c_str(), pos);
    }

    size_type find_first_of(const_pointer s, size_type pos = 0) const
    {
        return find_first_of(s, pos, s == nullptr ? 0 : traits_type::length(s));
    }
    size_type find_first_of(const_pointer s, size_type pos, size_type count) const
    {
        const size_type rhs_size = (s == nullptr) ? 0 : std::min(traits_type::length(s), count);
        if (s == nullptr || rhs_size == 0)
        {
            return npos;
        }
        for (size_type i = pos; i < size_; ++i)
        {
            for (size_type j = 0; j < rhs_size; ++j)
            {
                if (traits_type::eq(data_[i], s[j]))
                {
                    return i;
                }
            }
        }
        return npos;
    }
    size_type find_first_of(value_type ch, size_type pos = 0) const
    {
        return find(ch, pos);
    }

    template <size_t S2>
    size_type find_first_not_of(const FixedString<S2>& str, size_type pos = 0) const
    {
        return find_first_not_of(str.c_str(), pos);
    }

    size_type find_first_not_of(const_pointer s, size_type pos = 0) const
    {
        return find_first_not_of(s, pos, s == nullptr ? 0 : traits_type::length(s));
    }
    // pos is in this string, count is in rhs string
    // find first position in this string, which is not in rhs string
    size_type find_first_not_of(const_pointer s, size_type pos, size_type count) const
    {
        const size_type rhs_size = (s == nullptr) ? 0 : std::min(traits_type::length(s), count);
        if (s == nullptr || rhs_size == 0)
        {
            if (pos < size_)
            {
                return pos;
            }
            return npos;
        }

        size_type j;
        for (size_type i = pos; i < size_; ++i)
        {
            // Check against all characters in search set
            for (j = 0; j < rhs_size; ++j)
            {
                if (traits_type::eq(data_[i], s[j]))
                {
                    break;
                }
            }

            // Return first position where no match was found
            if (j == rhs_size)
            {
                return i;
            }
        }
        return npos;
    }
    size_type find_first_not_of(value_type ch, size_type pos = 0) const
    {
        // Handle invalid position ‌:ml-citation{ref="3,4" data="citationList"}
        if (pos >= size_)
            return npos;

        // Linear scan from pos ‌:ml-citation{ref="1,3" data="citationList"}
        for (size_type i = pos; i < size_; ++i)
        {
            if (!traits_type::eq(data_[i], ch))
            {
                return i;  // Found mismatch
            }
        }
        return npos;  // All characters match ‌:ml-citation{ref="1,4" data="citationList"}
    }

    template <size_t S2>
    size_type find_last_of(const FixedString<S2>& str, size_type pos = npos) const
    {
        return find_last_of(str.c_str(), pos);
    }

    size_type find_last_of(const_pointer s, size_type pos = npos) const
    {
        return find_last_of(s, pos, s == nullptr ? 0 : traits_type::length(s));
    }
    // pos is in this string, count is in rhs string
    // find [0, pos], which is in rhs [s, s+count) string
    size_type find_last_of(const_pointer s, size_type pos, size_type count) const
    {
        // Handle empty source string or invalid search parameters
        if (size_ == 0 || s == nullptr || count == 0)
        {
            return npos;
        }

        // Calculate valid search range
        const size_type actual_count = std::min(count, static_cast<size_type>(traits_type::length(s)));

        // Adjust pos to valid range [0, len-1]
        pos = (pos >= size_) ? size_ - 1 : pos;

        // Reverse search from pos towards beginning
        for (size_type i = pos; i != npos; --i)
        {
            // Check against each character in search set
            for (size_type j = 0; j < actual_count; ++j)
            {
                if (traits_type::eq(data_[i], s[j]))
                {
                    return i;  // Found matching character
                }
            }
        }

        return npos;
    }
    size_type find_last_of(value_type ch, size_type pos = npos) const
    {
        if (size_ == 0)
            return npos;

        pos = (pos >= size_) ? size_ - 1 : pos;
        for (size_type i = pos; i != npos; --i)
        {
            if (traits_type::eq(data_[i], ch))
            {
                return i;
            }
        }
        return npos;
    }

    template <size_t S2>
    size_type find_last_not_of(const FixedString<S2>& str, size_type pos = npos) const
    {
        return find_last_not_of(str.c_str(), pos);
    }

    size_type find_last_not_of(const_pointer s, size_type pos = npos) const
    {
        return find_last_not_of(s, pos, s == nullptr ? 0 : traits_type::length(s));
    }
    // pos is in this string, count is in rhs string
    // find [0, pos], which is not in rhs [s, s+count) string
    size_type find_last_not_of(const_pointer s, size_type pos, size_type count) const
    {
        // Handle empty source string or invalid search parameters
        if (size_ == 0 || s == nullptr || count == 0)
        {
            return npos;
        }

        // Calculate valid search range
        const size_type actual_count = std::min(count, static_cast<size_type>(traits_type::length(s)));

        // Adjust pos to valid range [0, len-1]
        pos = (pos >= size_) ? size_ - 1 : pos;

        // Reverse search from pos towards beginning
        for (size_type i = pos; i != npos; --i)
        {
            bool found_match = false;
            // Check against each character in search set
            for (size_type j = 0; j < actual_count; ++j)
            {
                if (traits_type::eq(data_[i], s[j]))
                {
                    found_match = true;
                    break;
                }
            }

            // Return first position where no match was found
            if (!found_match)
            {
                return i;
            }
        }

        return npos;
    }
    size_type find_last_not_of(value_type ch, size_type pos = npos) const
    {
        if (size_ == 0)
            return npos;

        pos = (pos >= size_) ? size_ - 1 : pos;
        for (size_type i = pos; i != npos; --i)
        {
            if (!traits_type::eq(data_[i], ch))
            {
                return i;
            }
        }
        return npos;
    }

    template <size_t S2>
    int compare(const FixedString<S2>& rhs) const
    {
        return compare(0, size(), rhs.c_str(), 0, rhs.size());
    }
    template <size_t S2>
    int compare(size_type pos1, size_type count1, const FixedString<S2>& rhs) const
    {
        return compare(pos1, count1, rhs.c_str(), 0, rhs.size());
    }
    template <size_t S2>
    int compare(size_type pos1, size_type count1, const FixedString<S2>& rhs, size_type pos2,
                size_type count2 = npos) const
    {
        return compare(pos1, count1, rhs.c_str(), pos2, count2);
    }
    int compare(const_pointer s) const
    {
        return compare(0, size(), s, 0, s == nullptr ? 0 : traits_type::length(s));
    }
    int compare(size_type pos1, size_type count1, const_pointer s) const
    {
        return compare(pos1, count1, s, 0, s == nullptr ? 0 : traits_type::length(s));
    }
    // compare [pos1, pos1 + count1) in this, [pos2 or s, pos2 or s + count2) in s
    int compare(size_type pos1, size_type count1, const_pointer s, size_type count2) const
    {
        return compare(pos1, count1, s, 0, count2);
    }
    int compare(size_type pos1, size_type count1, const_pointer s, size_type pos2, size_type count2) const
    {
        // 1. Parameter validation and adjustment
        if (pos1 > size_)
        {
            throw_out_of_range("FixedString::compare pos1");
        }

        // Adjust count1 to not exceed string length
        const size_type max_count1 = size_ - pos1;
        count1                     = std::min(count1, max_count1);
        if (pos2 != 0 && pos2 != npos)
        {
            const size_type max_count2 = traits_type::length(s) - pos2;
            count2                     = std::min(count2, max_count2);
        }

        // 2. Handle empty comparisons early
        const_pointer data_ptr = data_ + pos1;

        // 3. Get comparison length as the minimum of both ranges
        const size_type cmp_len = std::min(count1, count2);

        // 4. Perform lexicographical comparison
        int result = traits_type::compare(data_ptr, s, cmp_len);

        // 5. Determine final comparison result:
        //    - Different characters found: return traits_type::compare result
        //    - Equal prefixes: shorter string is considered smaller
        return result != 0 ? result : (count1 == count2 ? 0 : (count1 < count2 ? -1 : 1));
    }

    template <size_t S2>
    FixedString<S2> substr(size_type pos, size_type count = npos) const
    {
        if (pos > size_)
        {
            throw_out_of_range("FixedString::substr pos");
        }

        // Adjust count to not exceed string length
        const size_type max_count = size_ - pos;
        count                     = std::min(count, max_count);
        return FixedString<S2>(data_ + pos, count);
    }

    friend std::ostream& operator<<(std::ostream& os, const FixedString& fs)
    {
        return os << fs.data_;  // Directly output null-terminated content
    }

    friend std::istream& operator>>(std::istream& is, FixedString& fs)
    {
        // Preserve original stream state
        std::ios_base::iostate state = is.exceptions();
        is.exceptions(std::ios_base::goodbit);

        // Skip leading whitespace unless noskipws is set
        if (is.flags() & std::ios::skipws)
        {
            is >> std::ws;
        }

        // Read characters until capacity or whitespace
        fs.size_ = 0;
        value_type ch;
        while (fs.size_ < SIZE && is.get(ch) && !std::isspace(ch))
        {
            fs.data_[fs.size_++] = ch;
        }
        fs.data_[fs.size_] = '\0';

        // Handle full buffer case
        if (fs.size_ == SIZE && !std::isspace(ch) && is.good())
        {
            is.clear();  // Clear failbit if we filled buffer
        }

        // Restore original stream state
        is.exceptions(state);
        return is;
    }

    /**
     * @brief Concatenates with C-style string
     * @tparam N Automatic deduction for rhs array size
     * @param rhs Right-hand side NTBS
     * @return New FixedString with combined contents
     * @throws std::length_error if combined size exceeds capacity
     * @note Returns new object - original strings remain unchanged
     */
    template <size_t N>
    FixedString<N - 1 + SIZE> operator+(const value_type (&rhs)[N])
    {
        FixedString<N - 1 + SIZE> result(data_);
        result.append(rhs);
        return result;
    }

    template <size_t N, size_t S2>
    friend FixedString<N - 1 + S2> operator+(const value_type (&lhs)[N], const FixedString<S2>& rhs);

    template <size_t S1, size_t S2>
    friend FixedString<S1 + S2> operator+(const FixedString<S1>& lhs, const FixedString<S2>& rhs);

    // endregion

    // region Comparison Operators

    /**
     * @brief Equality comparison
     * @param rhs Right-hand side FixedString
     * @return true if contents are identical
     */
    template <size_t OTHER_SIZE>
    bool operator==(const FixedString<OTHER_SIZE>& rhs) const noexcept
    {
        return (size_ == rhs.size()) && traits_type::compare(c_str(), rhs.c_str(), size_) == 0;
    }
    bool operator==(const_pointer str) const noexcept
    {
        return (traits_type::length(str) == size_) && (traits_type::compare(c_str(), str, size_) == 0);
    }

    /**
     * @brief Inequality comparison
     * @param rhs Right-hand side FixedString
     * @return true if contents differ
     */
    template <size_t OTHER_SIZE, typename = enable_if_t<SIZE == OTHER_SIZE>>
    bool operator!=(const FixedString<OTHER_SIZE>& rhs) const noexcept
    {
        return !(*this == rhs);
    }

    /**
     * @brief Lexicographical less-than comparison
     * @param rhs Right-hand side FixedString
     * @return true if this string is lexicographically smaller
     */
    template <size_t OTHER_SIZE, typename = enable_if_t<SIZE == OTHER_SIZE>>
    bool operator<(const FixedString<OTHER_SIZE>& rhs) const noexcept
    {
        size_t  left_len  = size_;
        size_t  right_len = rhs.size();
        int32_t ret       = traits_type::compare(c_str(), rhs.c_str(), std::min(left_len, right_len));
        // sometimes uninitialized memory may be compared, so we need to compare the length
        if (ret == 0)
        {
            return (left_len < right_len);
        }
        else
        {
            return (ret < 0);
        }
    }

    /// @copydoc operator<
    template <size_t OTHER_SIZE, typename = enable_if_t<SIZE == OTHER_SIZE>>
    bool operator<=(const FixedString<OTHER_SIZE>& rhs) const noexcept
    {
        return (rhs < *this) || (*this == rhs);
    }

    /// @copydoc operator<
    template <size_t OTHER_SIZE, typename = enable_if_t<SIZE == OTHER_SIZE>>
    bool operator>(const FixedString<OTHER_SIZE>& rhs) const noexcept
    {
        return rhs < *this;
    }

    /// @copydoc operator<
    template <size_t OTHER_SIZE, typename = enable_if_t<SIZE == OTHER_SIZE>>
    bool operator>=(const FixedString<OTHER_SIZE>& rhs) const noexcept
    {
        return !(*this < rhs);
    }
    // endregion

    /// @brief Ensures null-termination
    void terminate() noexcept
    {
        data_[size_] = '\0';
    }
    // endregion

private:
    // region Validation

    /**
     * @brief Validates position bounds
     * @param pos Position to check
     * @throws std::out_of_range If pos >= size()
     */
    void check_bounds(size_type pos) const
    {
        if (pos >= size_)
        {
            throw_out_of_range("FixedString::at");
        }
    }

    /**
     * @brief Validates required capacity
     * @param required Minimum required capacity
     * @throws std::length_error If required > capacity()
     */
    void validate_capacity(size_type required) const
    {
        if (required > SIZE)
        {
            throw_length_error("FixedString capacity exceeded");
        }
    }

    /**
     * @brief Validates available space
     * @param need Additional space needed
     * @throws std::length_error If size() + need > capacity()
     */
    void validate_available(size_type need) const
    {
        if (size_ + need > SIZE)
        {
            throw_length_error("FixedString capacity exceeded");
        }
    }
    // endregion
private:
    /**
     * @brief Current string length
     */
    uint32_t size_;
    /**
     * @brief Internal buffer storage (+1 for null terminator)
     */
    value_type data_[SIZE + 1];  ///< Character buffer with space for null terminator

    inline void throw_bad_alloc() const
    {
#ifdef __EXCEPTIONS
        throw std::bad_alloc();
#else
        std::terminate();
#endif
    }
    inline void throw_length_error(const char* msg) const
    {
#ifdef __EXCEPTIONS
        throw std::length_error(msg);
#else
        std::terminate();
#endif
    }
    inline void throw_out_of_range(const char* msg) const
    {
#ifdef __EXCEPTIONS
        throw std::out_of_range(msg);
#else
        std::terminate();
#endif
    }
};

// region Non-member Functions

/**
 * @brief Swaps two FixedString instances
 * @param lhs First string to swap
 * @param rhs Second string to swap
 * @post Contents and capacities are exchanged
 */
template <size_t S1, size_t S2>
auto swap(FixedString<S1>& lhs, FixedString<S2>& rhs) noexcept -> enable_if_t<S1 == S2, void>
{
    if (lhs == rhs)
    {
        return;
    }

    std::swap(lhs.size_, rhs.size_);
    size_t maxSize = std::max(lhs.size(), rhs.size());
    for (size_t i = 0; i <= maxSize + 1; ++i)
    {
        std::swap(lhs.data_[i], rhs.data_[i]);  // 逐个元素交换
    }
}

template <size_t N, size_t S2>
FixedString<N - 1 + S2> operator+(const char (&lhs)[N], const FixedString<S2>& rhs)
{
    FixedString<N - 1 + S2> result(lhs);
    result.append(rhs.c_str());
    return result;
}

template <size_t S1, size_t S2>
FixedString<S1 + S2> operator+(const FixedString<S1>& lhs, const FixedString<S2>& rhs)
{
    FixedString<S1 + S2> result(lhs);
    result.append(rhs.c_str());
    return result;
}

template <size_t SIZE, typename CharT, typename Traits>
constexpr typename FixedString<SIZE, CharT, Traits>::size_type FixedString<SIZE, CharT, Traits>::npos;

template <size_t CapacityValue>
inline void to_json(nlohmann::json& j, FixedString<CapacityValue> const& v)
{
    j = v.c_str();
}

template <size_t CapacityValue>
inline void from_json(nlohmann::json const& j, FixedString<CapacityValue>& v)
{
    if (j.is_string())
    {
        std::string s = j;
        v = FixedString<CapacityValue>(s.c_str());
    }
}

}  // namespace zos

#endif
