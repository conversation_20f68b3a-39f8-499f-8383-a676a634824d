#ifndef BASIC_TYPES_CONTAINER_ARRAY_HPP_
#define BASIC_TYPES_CONTAINER_ARRAY_HPP_

#include <basic_types/types.hpp>
#include <nlohmann/json.hpp>
#include <array>

namespace zos
{
template <typename ValueType, zos::int32_t CapacityValue>
using FixedArray = std::array<ValueType, CapacityValue>;

template <typename ValueType, zos::int32_t CapacityValue>
using Array = std::array<ValueType, CapacityValue>;

}

#endif
