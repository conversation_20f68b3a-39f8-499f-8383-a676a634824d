#ifndef BASIC_TYPES_CONTAINER_TRAITS_HPP_
#define BASIC_TYPES_CONTAINER_TRAITS_HPP_

#include <basic_types/types.hpp>
#include <type_traits>
#include <iterator>
namespace zos
{
template <typename Iter>
using IsInputIterator =
    std::integral_constant<zos::bool_t, std::is_convertible<typename std::iterator_traits<Iter>::iterator_category,
                                                             std::input_iterator_tag>::value>;
}

#endif
