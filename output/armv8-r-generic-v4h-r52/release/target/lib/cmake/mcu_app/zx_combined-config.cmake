

include(CMakeFindDependencyMacro)

# 查找必要的依赖项
find_dependency(imo_com_micro REQUIRED)

# 设置库和头文件路径
get_filename_component(ZX_SDK_ROOT_DIR "${CMAKE_CURRENT_LIST_DIR}/../.." ABSOLUTE)
set(ZX_SDK_LIB_DIR "${ZX_SDK_ROOT_DIR}/lib")
set(ZX_SDK_INCLUDE_DIR "${ZX_SDK_ROOT_DIR}/include")

# 检查组合库是否存在
set(ZX_COMBINED_LIB_PATH "${ZX_SDK_LIB_DIR}/libzx_combined.a")

if(EXISTS "${ZX_COMBINED_LIB_PATH}")
    # 创建组合库目标
    if(NOT TARGET zx::combined)
        add_library(zx::combined STATIC IMPORTED)
        set_target_properties(zx::combined PROPERTIES
            IMPORTED_LOCATION "${ZX_COMBINED_LIB_PATH}"
            INTERFACE_INCLUDE_DIRECTORIES "${ZX_SDK_INCLUDE_DIR}"
        )
    endif()

    # 主要的链接函数 - 使用组合库
    function(target_link_zx_components target_name)
        message(STATUS "Linking ${target_name} with ZX combined library")
        target_link_libraries(${target_name}
            PUBLIC
            imo_com_micro::imo_com_ddk
            -Wl,--start-group
            -Wl,--whole-archive
            zx::combined
            -Wl,--no-whole-archive
            -Wl,--end-group
        )
    endfunction()

    # 简化的链接函数
    function(target_link_zx_combined target_name)
        target_link_libraries(${target_name}
            PUBLIC
            zx::combined
        )
    endfunction()

else()
    # 如果组合库不存在，回退到单独库的方式
    message(WARNING "Combined library not found at ${ZX_COMBINED_LIB_PATH}, falling back to individual libraries")

    # 导入单独的目标（如果存在）
    if(EXISTS "${CMAKE_CURRENT_LIST_DIR}/zx-targets.cmake")
        include("${CMAKE_CURRENT_LIST_DIR}/zx-targets.cmake")
    endif()

    # 回退的链接函数
    function(target_link_zx_components target_name)
        message(STATUS "Linking ${target_name} with individual ZX libraries")
        target_link_libraries(${target_name}
            PUBLIC
            imo_com_micro::imo_com_ddk
            -Wl,--start-group
            -Wl,--whole-archive
            # 这里需要根据实际可用的目标进行调整
            -Wl,--no-whole-archive
            -Wl,--end-group
        )
    endfunction()

    function(target_link_zx_combined target_name)
        target_link_zx_components(${target_name})
    endfunction()
endif()

# 提供版本信息
set(ZX_SDK_VERSION "1.0.0")
set(ZX_SDK_VERSION_MAJOR "1")
set(ZX_SDK_VERSION_MINOR "0")
set(ZX_SDK_VERSION_PATCH "0")
