# ZX MCU SDK 快速开始指南

## 🚀 快速开始

### 第一步：构建和打包 SDK

```bash
# 在项目根目录运行
./build_and_package_sdk.sh
```

这将创建：
- `zx_sdk_output/` - SDK 安装目录
- `zx_mcu_sdk_*.tar.gz` - SDK 压缩包

### 第二步：测试 SDK

```bash
# 使用示例项目测试 SDK
cd example_project
./build_with_sdk.sh ../zx_sdk_output
```

### 第三步：在您的项目中使用

1. **解压 SDK 到目标位置**
```bash
tar xzf zx_mcu_sdk_*.tar.gz -C /your/sdk/location/
```

2. **在您的 CMakeLists.txt 中添加**
```cmake
find_package(mcu_app REQUIRED PATHS /your/sdk/location/zx_sdk_output)
target_link_zx_components(your_target)
```

3. **构建您的项目**
```bash
mkdir build && cd build
cmake .. -DCMAKE_PREFIX_PATH=/your/sdk/location/zx_sdk_output
make
```

## 📁 SDK 内容

构建完成后，SDK 包含：

```
zx_sdk_output/
├── lib/
│   ├── libzx_combined.a          # 组合库（主要）
│   ├── lib*.a                    # 单独的库文件（兼容性）
│   └── cmake/mcu_app/            # CMake 配置文件
├── include/                      # 头文件
├── bin/                          # 可执行文件
└── SDK_INFO.txt                  # SDK 信息
```

## 🔧 高级用法

### 自定义链接

```cmake
# 方法1：使用完整的组件链接（推荐）
target_link_zx_components(your_target)

# 方法2：仅链接组合库
target_link_zx_combined(your_target)

# 方法3：手动链接（高级用户）
target_link_libraries(your_target PUBLIC zx::combined)
```

### 检查 SDK 版本

```cmake
find_package(mcu_app REQUIRED)
message(STATUS "ZX SDK Version: ${ZX_SDK_VERSION}")
```

## ⚠️ 注意事项

1. **确保工具链兼容**: SDK 和您的项目需要使用相同的工具链
2. **静态链接**: 所有库都是静态链接的
3. **内存需求**: 组合库可能较大，确保有足够的链接内存

## 🐛 故障排除

### 常见错误及解决方案

**错误**: `Could not find a package configuration file provided by "mcu_app"`
```bash
# 解决方案：设置正确的 CMAKE_PREFIX_PATH
cmake .. -DCMAKE_PREFIX_PATH=/path/to/zx_sdk_output
```

**错误**: `undefined reference to ...`
```bash
# 解决方案：确保使用了正确的链接函数
target_link_zx_components(your_target)  # 而不是 target_link_libraries
```

**错误**: 组合库未找到
```bash
# 检查组合库是否存在
ls -la /path/to/zx_sdk_output/lib/libzx_combined.a
# 如果不存在，重新运行 build_and_package_sdk.sh
```

## 📞 获取帮助

1. 查看详细文档：`ZX_MCU_SDK_README.md`
2. 检查 SDK 信息：`cat zx_sdk_output/SDK_INFO.txt`
3. 查看示例项目：`example_project/`

## 🎯 最佳实践

1. **版本管理**: 为每个 SDK 版本创建标签
2. **测试**: 使用示例项目验证 SDK 功能
3. **文档**: 保持 SDK 使用文档更新
4. **备份**: 定期备份 SDK 构建产物

---

**恭喜！** 您现在可以在任何项目中重用 ZX MCU 组件库了！ 🎉
