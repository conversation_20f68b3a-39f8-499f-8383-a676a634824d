COMMAND: cmake -E remove_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/idc_rte_mcu

WORKING_DIRECTORY: /home/<USER>/code/mcu_app_YT_workspace

ENVIRONMENT: {'AR': '/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-ar',
 'ARCH': 'armv8-r',
 'AUTOTOOL_HOST': 'arm-none-eabi',
 'CC': '/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc',
 'CFLAGS': '-I/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include '
           '-marm -mcpu=cortex-r52 -gdwarf-2 -std=c99 -fverbose-asm -O3 '
           '-DNDEBUG -Werror=return-type -fno-omit-frame-pointer ',
 'CMAKE_FIND_ROOT_PATH': '/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target:/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/3rdparty:/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/tmp:',
 'CMAKE_PREFIX_PATH': '/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target:/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/3rdparty:',
 'CXX': '/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++',
 'CXXFLAGS': '-I/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include '
             '-marm -mcpu=cortex-r52 -gdwarf-2 -std=c++14 -fverbose-asm '
             '-fno-exceptions -fno-threadsafe-statics -fno-rtti -Wno-reorder '
             '-O3 -std=c++14 -Werror=return-type -fno-omit-frame-pointer '
             '-fexceptions ',
 'FC': '',
 'HOME': '/home/<USER>',
 'IMOBUILDER_HOST_OUTPUT': '/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/host',
 'IMOBUILDER_TARGET_OUTPUT': '/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target',
 'IMOBUILDER_TMP_OUTPUT': '/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/tmp',
 'LANG': 'zh_CN.UTF-8',
 'LD': '/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-ld',
 'LDFLAGS': '-Wl,-rpath-link,/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/3rdparty/lib:/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/tmp/lib:/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/lib '
            '-L/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib '
            '-Wl,-rpath-link,/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib '
            '-Wl,--entry=Start -Wl,--gc-sections -Wl,--cref ',
 'LD_LIBRARY_PATH': '/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/host/lib:',
 'NM': '/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc-nm',
 'OBJDUMP': '/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-objdump',
 'PATH': '/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/host/bin:/home/<USER>/code/mcu_app_YT_workspace/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin:/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/home/<USER>/.local/bin',
 'PKG_CONFIG_LIBDIR': '',
 'PKG_CONFIG_PATH': '/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/3rdparty/share/pkgconfig:/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/3rdparty/lib/pkgconfig:',
 'PWD': '/home/<USER>/code/mcu_app_YT_workspace',
 'RANLIB': '/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc-ranlib',
 'SHELL': '/usr/bin/zsh',
 'STRIP': '/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-strip',
 'TERM': 'xterm-256color',
 'USER': 'lucas'}

RETCODE: 0

