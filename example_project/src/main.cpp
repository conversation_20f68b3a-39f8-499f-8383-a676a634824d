#include <iostream>
#include "app_main.h"

int main(int argc, char* argv[]) {
    std::cout << "Starting Example MCU Application..." << std::endl;
    
    // 初始化应用程序
    if (app_initialize() != 0) {
        std::cerr << "Failed to initialize application" << std::endl;
        return -1;
    }
    
    std::cout << "Application initialized successfully" << std::endl;
    
    // 运行主循环
    int result = app_run();
    
    // 清理资源
    app_cleanup();
    
    std::cout << "Application finished with result: " << result << std::endl;
    return result;
}
