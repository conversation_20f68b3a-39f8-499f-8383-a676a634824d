cmake_minimum_required(VERSION 3.8.2)

project(ExampleMcuApp 
    VERSION 1.0.0
    DESCRIPTION "Example project using ZX MCU SDK"
    LANGUAGES C CXX
)

# 查找 ZX MCU SDK
find_package(mcu_app REQUIRED 
    PATHS ${ZX_SDK_INSTALL_PATH}/lib/cmake/mcu_app
    NO_DEFAULT_PATH
)

# 创建可执行文件
add_executable(${PROJECT_NAME}
    src/main.cpp
    src/app_main.cpp
)

# 链接 ZX 组件库
target_link_zx_components(${PROJECT_NAME})

# 设置编译选项
target_compile_features(${PROJECT_NAME} PRIVATE cxx_std_14)

# 设置输出目录
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 安装可执行文件
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)

# 打印信息
message(STATUS "Example project configured successfully")
message(STATUS "ZX SDK Version: ${ZX_SDK_VERSION}")
