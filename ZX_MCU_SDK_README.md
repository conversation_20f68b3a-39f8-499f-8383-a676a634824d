# ZX MCU SDK 解决方案

## 概述

这个解决方案将 `ZX_TARGET_BIN_NAME` 链接时使用的所有库打包成一个可重用的 SDK，让其他项目可以通过 `find_package` 导入并重新链接生成可执行文件。

## 主要特性

1. **组合库打包**: 将所有依赖库合并成一个 `libzx_combined.a` 文件
2. **CMake 集成**: 提供完整的 `find_package` 支持
3. **向后兼容**: 支持单独库文件的链接方式
4. **简化使用**: 提供简单的 `target_link_zx_components()` 函数
5. **头文件管理**: 自动收集和安装必要的头文件

## 文件结构

```
src/mcu_app/
├── cmake/
│   ├── install_func.cmake          # 库收集和打包函数
│   ├── zx-config.cmake.in          # 原有的 CMake 配置模板
│   └── zx_combined-config.cmake.in # 新的组合库配置模板
├── CMakeLists.txt                  # 主 CMake 文件（已修改）
└── ...

example_project/                    # 示例项目
├── CMakeLists.txt                  # 示例项目配置
├── src/
│   ├── main.cpp
│   ├── app_main.cpp
│   └── app_main.h
└── build_with_sdk.sh              # 使用 SDK 构建脚本

build_and_package_sdk.sh           # SDK 构建和打包脚本
```

## 使用方法

### 1. 构建和打包 SDK

```bash
# 运行 SDK 构建脚本
./build_and_package_sdk.sh
```

这将：
- 构建整个 MCU 应用程序
- 收集所有链接的库文件
- 创建组合库 `libzx_combined.a`
- 复制必要的头文件
- 生成 CMake 配置文件
- 创建完整的 SDK 包

### 2. 在其他项目中使用 SDK

#### 方法一：使用组合库（推荐）

```cmake
cmake_minimum_required(VERSION 3.8.2)
project(MyMcuApp)

# 查找 ZX MCU SDK
find_package(mcu_app REQUIRED)

# 创建可执行文件
add_executable(MyMcuApp src/main.cpp)

# 链接 ZX 组件（使用组合库）
target_link_zx_components(MyMcuApp)
```

#### 方法二：使用简化函数

```cmake
# 只链接组合库，不包含链接器标志
target_link_zx_combined(MyMcuApp)
```

### 3. 构建使用 SDK 的项目

```bash
cd example_project
./build_with_sdk.sh /path/to/zx_sdk_output
```

## 技术实现

### 1. 库收集机制

`install_func.cmake` 中的 `install_zx_libs()` 函数：
- 分析目标的链接库
- 在多个可能路径中搜索库文件
- 收集所有找到的静态库

### 2. 组合库创建

`create_zx_combined_library()` 函数：
- 提取所有静态库中的对象文件
- 使用 `ar` 工具重新打包成单一库文件
- 避免符号冲突和重复定义

### 3. CMake 配置

`zx_combined-config.cmake.in` 提供：
- 自动依赖查找
- 灵活的库路径配置
- 向后兼容支持
- 版本信息管理

## 链接的组件

当前解决方案包含以下组件：

- **PDM Configuration** - 参数数据管理配置
- **IPC Configuration** - 进程间通信配置
- **Network Configuration** - 网络配置
- **OS Configuration** - 操作系统配置
- **System Communication** - 系统通信
- **DDK** - 设备驱动开发包
- **Cool Framework** - Cool 框架
- **VFC** - 车辆功能组件
- **Application Configuration** - 应用配置
- **USS Sensors** - 超声波传感器
- **Microsar Components** - Microsar 组件

## 优势

1. **简化部署**: 单一库文件，减少依赖管理复杂性
2. **提高性能**: 减少链接时间和运行时开销
3. **易于维护**: 统一的版本管理和更新机制
4. **跨项目重用**: 标准的 CMake find_package 接口
5. **灵活配置**: 支持多种链接方式

## 注意事项

1. 确保所有依赖库都是静态库（.a 文件）
2. 注意符号冲突，特别是全局变量和函数
3. 组合库可能较大，注意存储空间
4. 定期更新 SDK 以包含最新的组件变更

## 故障排除

### 常见问题

1. **找不到库文件**: 检查 `install_func.cmake` 中的搜索路径
2. **链接错误**: 确认所有依赖项都已正确安装
3. **符号未定义**: 可能需要调整链接顺序或添加缺失的库

### 调试方法

```bash
# 查看组合库内容
ar -t /path/to/libzx_combined.a

# 检查符号
nm /path/to/libzx_combined.a | grep symbol_name

# 验证 CMake 配置
cmake --find-package -DNAME=mcu_app -DCOMPILER_ID=GNU -DLANGUAGE=C -DMODE=EXIST
```
