#!/bin/bash

# ZX MCU SDK 构建和打包脚本
# 用于将 ZX_TARGET_BIN_NAME 链接的所有库打包成 SDK

set -e  # 遇到错误立即退出

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="${SCRIPT_DIR}/src/mcu_app"
BUILD_DIR="${PROJECT_ROOT}/build_sdk"
INSTALL_DIR="${SCRIPT_DIR}/zx_sdk_output"
VERSION=$(git describe --tags --always 2>/dev/null || echo "1.0.0")

echo "=========================================="
echo "ZX MCU SDK 构建和打包工具"
echo "=========================================="
echo "项目根目录: ${PROJECT_ROOT}"
echo "构建目录: ${BUILD_DIR}"
echo "安装目录: ${INSTALL_DIR}"
echo "版本: ${VERSION}"
echo "=========================================="

# 清理之前的构建
echo "清理之前的构建..."
rm -rf "${BUILD_DIR}"
rm -rf "${INSTALL_DIR}"

# 创建构建目录
mkdir -p "${BUILD_DIR}"
cd "${BUILD_DIR}"

# 配置 CMake
echo "配置 CMake..."
cmake "${PROJECT_ROOT}" \
    -DCMAKE_BUILD_TYPE=Release \
    -DCMAKE_INSTALL_PREFIX="${INSTALL_DIR}" \
    -DCMAKE_EXPORT_COMPILE_COMMANDS=ON

# 构建项目
echo "构建项目..."
make -j$(nproc)

# 创建组合库
echo "创建组合库..."
make zx_combined_lib

# 安装 SDK
echo "安装 SDK..."
make install

# 创建头文件目录并复制必要的头文件
echo "复制头文件..."
mkdir -p "${INSTALL_DIR}/include"

# 复制公共接口头文件
if [ -d "${PROJECT_ROOT}/idc_interfaces" ]; then
    cp -r "${PROJECT_ROOT}/idc_interfaces"/* "${INSTALL_DIR}/include/" 2>/dev/null || true
fi

# 复制组件头文件
COMPONENT_DIRS=(
    "components"
    "idc_rte"
    "idc_infrastructure"
)

for dir in "${COMPONENT_DIRS[@]}"; do
    if [ -d "${PROJECT_ROOT}/${dir}" ]; then
        find "${PROJECT_ROOT}/${dir}" -name "*.h" -o -name "*.hpp" | while read -r header; do
            # 计算相对路径
            rel_path=$(realpath --relative-to="${PROJECT_ROOT}" "${header}")
            target_dir="${INSTALL_DIR}/include/$(dirname "${rel_path}")"
            mkdir -p "${target_dir}"
            cp "${header}" "${target_dir}/"
        done
    fi
done

# 验证组合库是否创建成功
COMBINED_LIB="${INSTALL_DIR}/lib/libzx_combined.a"
if [ -f "${COMBINED_LIB}" ]; then
    echo "✓ 组合库创建成功: ${COMBINED_LIB}"
    echo "  库大小: $(du -h "${COMBINED_LIB}" | cut -f1)"
else
    echo "✗ 警告: 组合库未找到"
fi

# 创建 SDK 信息文件
cat > "${INSTALL_DIR}/SDK_INFO.txt" << EOF
ZX MCU SDK Information
======================

Version: ${VERSION}
Build Date: $(date)
Build Host: $(hostname)
Build User: $(whoami)

Components Included:
- PDM Configuration
- IPC Configuration  
- Network Configuration
- OS Configuration
- System Communication
- DDK (Device Driver Kit)
- Cool Framework
- VFC (Vehicle Function Components)
- Application Configuration
- USS Sensors
- Microsar Components

Usage:
------
In your CMakeLists.txt:

    find_package(mcu_app REQUIRED)
    target_link_zx_components(your_target)

Or for simplified usage:

    find_package(mcu_app REQUIRED)
    target_link_zx_combined(your_target)

EOF

# 创建压缩包
echo "创建 SDK 压缩包..."
cd "$(dirname "${INSTALL_DIR}")"
tar czf "zx_mcu_sdk_${VERSION}.tar.gz" "$(basename "${INSTALL_DIR}")"

echo "=========================================="
echo "SDK 构建完成!"
echo "=========================================="
echo "安装目录: ${INSTALL_DIR}"
echo "压缩包: $(dirname "${INSTALL_DIR}")/zx_mcu_sdk_${VERSION}.tar.gz"
echo ""
echo "使用方法:"
echo "1. 解压 SDK 到目标位置"
echo "2. 在项目中设置 CMAKE_PREFIX_PATH 或 mcu_app_DIR"
echo "3. 使用 find_package(mcu_app REQUIRED)"
echo "4. 调用 target_link_zx_components(your_target)"
echo "=========================================="
