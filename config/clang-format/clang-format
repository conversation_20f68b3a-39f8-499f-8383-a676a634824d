{
BasedOnStyle: Google,
AccessModifierOffset: -4,
ConstructorInitializerIndentWidth: 2,
AlignEscapedNewlinesLeft: false,
AlignTrailingComments: true,
AllowAllParametersOfDeclarationOnNextLine: false,
AllowShortIfStatementsOnASingleLine: false,
AllowShortFunctionsOnASingleLine: None,
AllowShortLoopsOnASingleLine: false,
AlwaysBreakTemplateDeclarations: true,
AlwaysBreakBeforeMultilineStrings: false,
BreakBeforeBinaryOperators: false,
BreakBeforeTernaryOperators: false,
BreakConstructorInitializersBeforeComma: true,
BinPackParameters: true,
ColumnLimit: 120,
ConstructorInitializerAllOnOneLineOrOnePerLine: true,
DerivePointerBinding: false,
PointerAlignment: Left,
ExperimentalAutoDetectBinPacking: false,
IndentCaseLabels: true,
MaxEmptyLinesToKeep: 1,
NamespaceIndentation: None,
ObjCSpaceBeforeProtocolList: true,
PenaltyBreakBeforeFirstCallParameter: 19,
PenaltyBreakComment: 60,
PenaltyBreakString: 10000,
PenaltyBreakFirstLessLess: 1000,
PenaltyExcessCharacter: 1000,
PenaltyReturnTypeOnItsOwnLine: 90,
SpacesBeforeTrailingComments: 2,
Cpp11BracedListStyle: true,
Standard:        Auto,
IndentWidth:     4,
TabWidth:        4,
UseTab:          Never,
IndentFunctionDeclarationAfterType: false,
SpacesInParentheses: false,
SpacesInAngles:  false,
SpaceInEmptyParentheses: false,
SpacesInCStyleCastParentheses: false,
SpaceAfterControlStatementKeyword: true,
SpaceBeforeAssignmentOperators: true,
ContinuationIndentWidth: 4,
SortIncludes: true,
IncludeBlocks: Preserve,
SpaceAfterCStyleCast: false,
AlignConsecutiveDeclarations: true,
AlignConsecutiveAssignments: true,
BreakBeforeBraces: Custom,
BraceWrapping:
{
  AfterClass:      true,
  AfterControlStatement: true,
  AfterEnum:       true,
  AfterFunction:   true,
  AfterNamespace:  true,
  AfterObjCDeclaration: false,
  AfterStruct:     true,
  AfterUnion:      true,
  AfterCaseLabel:  true,
  AfterExternBlock: false,
  BeforeCatch:     true,
  BeforeElse:      true,
  IndentBraces:    false,
  SplitEmptyFunction: true,
  SplitEmptyRecord: true,
  SplitEmptyNamespace: true,
}
}
