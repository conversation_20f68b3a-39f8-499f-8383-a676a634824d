toolchain: gcc-ubuntu-11.4.0-hobot-x86_64-aarch64-linux-gnu
rootfs: rootfs-sdk-focal_0.0.1.20240530_all
packages:
- aarch64-linux_j6_tros_aarch64-20241126
- aarch64-linux_j6_hbrootfs-sdk_0.0.1.20240530_all
- aarch64-linux_sercoder-0.13.3
- aarch64-linux_serialize-0.13.3
- aarch64-linux_j6_ucp_aarch64-20250206
- tools_cmake_cmake-3.30.5-linux-x86_64
- tools_cmake_toolchain_files
- tools_imotion_env
- aarch64-linux_onnxruntime-linux-aarch64-1.15.1

var:
  CFLAGS: "-DAPU=1 -DRPU=2 -DSIL=3 -DZX_COMPUTE_TARGET=APU ${CFLAGS}"
  CXXFLAGS: "-DAPU=1 -DRPU=2 -DSIL=3 -DZX_COMPUTE_TARGET=APU ${CXXFLAGS}"
env:
  CFLAGS: ${CFLAGS}
  CXXFLAGS: ${CXXFLAGS}
