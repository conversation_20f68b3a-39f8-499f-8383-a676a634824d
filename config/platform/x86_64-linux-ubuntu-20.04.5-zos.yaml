toolchain: x86_64-linux-gcc-9.5.0
rootfs: ubuntu-base-20.04.5-base-amd64
packages:
- x86_64-linux_sercoder-0.13.3
- x86_64-linux_serialize-0.13.3
- x86_64-linux_tros_x86_64-20241029
- x86_64-linux_j6_ucp_x86_64-20250206
- tools_tros_tools-20240925
- tools_cmake_cmake-3.30.5-linux-x86_64
- tools_cmake_toolchain_files
- tools_imotion_env
- x86_64-linux_onnxruntime-linux-x64-1.15.1

var:
  CFLAGS: "-DAPU=1 -DRPU=2 -DSIL=3 -DZX_COMPUTE_TARGET=SIL ${CFLAGS}"
  CXXFLAGS: "-DAPU=1 -DRPU=2 -DSIL=3 -DZX_COMPUTE_TARGET=SIL ${CXXFLAGS}"
env:
  CFLAGS: ${CFLAGS}
  CXXFLAGS: ${CXXFLAGS}
