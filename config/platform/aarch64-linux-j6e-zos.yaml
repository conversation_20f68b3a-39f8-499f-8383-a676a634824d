toolchain: arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-linux-gnu
rootfs: null
packages: 
- aarch64-linux_sercoder-0.13.3
- aarch64-linux_serialize-0.13.3
- aarch64-linux_j6_tros_aarch64-20241126
- aarch64-linux_j6_hbrootfs-sdk_0.0.1.20250218_all
- aarch64-linux_j6_ucp_aarch64-20250206
- aarch64-linux_j6_gpu-0.0.1.20240530
- aarch64-linux_onnxruntime-linux-aarch64-1.15.1
- rootfs-sdk-bookworm_0.0.1.20250218_all
- x86_64-linux_ccache_ccache-4.10.2
- tools_cmake_cmake-3.30.5-linux-x86_64
- tools_cmake_toolchain_files
- tools_imotion_env
- tool_xpack-meson-build-1.6.1-1-linux-x64
- tool_xpack-ninja-build-1.12.1-1-linux-x64

var:
  CFLAGS: "-DAPU=1 -DRPU=2 -DSIL=3 -DZX_COMPUTE_TARGET=APU ${CFLAGS}"
  CXXFLAGS: "-DAPU=1 -DRPU=2 -DSIL=3 -DZX_COMPUTE_TARGET=APU ${CXXFLAGS}"
env:
  CFLAGS: ${CFLAGS}
  CXXFLAGS: ${CXXFLAGS}
