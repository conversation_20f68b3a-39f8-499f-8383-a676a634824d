base:
  - ZOS/mcu/release/X040
  - XPeng/D047/bsw/release/X040

projects:
  mcu_app:
    dependencies: [idc_rte_mcu, idc_interfaces_mcu, idc_infrastructure_mcu, vfc, n<PERSON><PERSON>_json, version_did, hobot_j6e_mcu, zos_msg]
    commands:
      configure:
        arguments:
        - -DBUILD_TESTING:BOOL=ON
        - -DENABLE_TESTING:BOOL=ON
        - -DBENCHMARK_ENABLE_TESTING:BOOL=OFF
        - -DBENCHMARK_ENABLE_GTEST_TESTS:BOOL=OFF
        - -DHAVE_STD_REGEX=0
        - -DTESTING_TOOL:STRING=googletest
        - -DCMAKE_EXPORT_COMPILE_COMMANDS=1
        - -DCPJ_FOLDER_NAME=cpj_xpht
        - -DZX_CUSTOMER_PROJECT:STRING=cpj_xpht
        - -DZX_PROJECT_TARGET_CMAKE=${PROJECT_SOURCE_DIR}/components/cmake_build/d510p_rpu.cmake
        - -DZX_PROJECT_OPTIONS_CMAKE=${PROJECT_SOURCE_DIR}/components/cmake_build/d510p/compile_options_rpu.cmake
        - -DCMAKE_BUILD_TYPE=Release
        - -DZX_SWID_JENKINS=developer
        - -DZX_PRM_TOOLCHAIN_MODE=off
        - -DZX_PROJECT_DEVICE=d510p
        - -DZX_PROJECT_VARIANT=h2c1
        - -DZX_HARDWARE=h2
        - -DZX_SENSOR=c1
        - -DZX_PROJECT_TARGET=rpu
        - -DZX_VEH_VARIANT=x3
        - -DZX_PROJECT_WHICH=arm
        - -DZX_PROJECT_BUILD_DIR=${PROJECT_SOURCE_DIR}/build/cpj_xpht_x3_d510p_h2c1/rpu_arm_gcc
        - -DZX_PROJECT_NORFLASH=m35xu512
        - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
        - -B ${PROJECT_SOURCE_DIR}/build/cpj_xpht_x3_d510p_h2c1/rpu_arm_gcc
        - ${PROJECT_SOURCE_DIR}
      build:
          command: "cmake --build ${PROJECT_SOURCE_DIR}/build/cpj_xpht_x3_d510p_h2c1/rpu_arm_gcc"
      clean_binary:
          command: '[ -d "${PROJECT_SOURCE_DIR}/.git" ]'
          arguments:
            - "&& rm -rf ${PROJECT_SOURCE_DIR}/build/cpj_xpht_x3_d510p_h2c1/rpu_arm_gcc"
            - "&& rm -rf ${PROJECT_SOURCE_DIR}/cpj_xpht/microsar_cfg/x3_d510p/build/SipAddon/Appl/obj"
            - "&& rm -rf ${PROJECT_SOURCE_DIR}/cpj_xpht/microsar_cfg/x3_d510p/build/SipAddon/Appl/err"
            - "&& rm -rf ${PROJECT_SOURCE_DIR}/cpj_xpht/microsar_cfg/x3_d510p/build/SipAddon/Appl/lst"
            - "&& find ${PROJECT_SOURCE_DIR}/components/microsar/src/_AR_ThirdParty/ThirdParty/hobot_j6e_v0.8.0_mcu/output/inc -name \"*.h\" -type f -delete"
            - "&& rm -rf ${PROJECT_SOURCE_DIR}/components/microsar/src/_AR_ThirdParty/ThirdParty/hobot_j6e_v0.8.0_mcu/output/lib"
            - "&& rm -rf ${PROJECT_SOURCE_DIR}/components/microsar/src/_AR_ThirdParty/ThirdParty/hobot_j6e_v0.8.0_mcu/output/objs"
          working_directory: ${PROJECT_SOURCE_DIR}
          retcode: []
      install:  
        command: "cmake --install ${PROJECT_SOURCE_DIR}/build/cpj_xpht_x3_d510p_h2c1/rpu_arm_gcc"