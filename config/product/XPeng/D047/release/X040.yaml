base:
- XPeng/D047/thirdparty/release/X040
- BSP/j6/release/20250408
- XPeng/D047/bsw/release/X040
- XPeng/D047/mcu/release/X040

package:
  runtime:
    manifest: null
    files:
    - ${IMOBUILDER_TARGET_OUTPUT}/imo_mdw_init.bash
    - ${IMOBUILDER_TARGET_OUTPUT}/run_asan_lsan_tsan_ubsan.sh
    - ${IMOBUILDER_TARGET_OUTPUT}/run_gtest.sh
    - ${IMOBUILDER_TARGET_OUTPUT}/target_start.sh
    directories:
    - ${IMOBUILDER_TARGET_OUTPUT}/bin
    - ${IMOBUILDER_TARGET_OUTPUT}/lib
    - ${IMOBUILDER_TARGET_OUTPUT}/config
    - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/bin
    - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib
    - ${IMOBUILDER_TARGET_OUTPUT}/etc
    - ${IMOBUILDER_TARGET_OUTPUT}/scripts

projects:
  ZOS_MCU:
    url: null
    branch: null
    dependencies:
      - MCU
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  ZOS_BSP:
    url: null
    branch: null
    dependencies:
      - hobot_j6e_v1_0_0_pack
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  ZOS:
    url: null
    branch: null
    dependencies:
      - BSW
      - Driving
      - Parking
      - Iuni
      - Perception
      - Prediction
      - LocalizationMapping
      - DataLoop
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  # ============== BSP/j6 ============== #

  hbre:
    branch: release/X040

  kernel:
    branch: release/X040

  uboot:
    branch: release/X040

  hobot_drivers:
    branch: release/X040

  hobot_j6e_v1_0_0_pack:
    var:
      debug_lunch_args: "j6m_debug_xpx3_defconfig"
      coverage_lunch_args: "j6m_debug_xpx3_defconfig"
      release_lunch_args: "j6m_release_xpx3_defconfig"
      relwithdebinfo_lunch_args: "j6m_release_xpx3_defconfig"
      minsizerel_lunch_args: "j6m_release_xpx3_defconfig"
    branch: release/X040

  # ============== XPeng/bsw ============== #
  vfc:
    branch: release/X040

  version_did:
    branch: release/X040

  zos_scripts:
    branch: release/X040

  basic_types:
    branch: release/X040

  zos_msg:
    branch: release/X040

  d047_ota_app:
    branch: release/X040

  d047_system_manager_app:
    branch: release/X040

  # ============== XPeng/mcu ============== #
  hobot_j6e_mcu:
    branch: release/X040

  idc_rte_mcu:
    branch: release/X040

  idc_interfaces_mcu:
    branch: release/X040
    
  idc_infrastructure_mcu:
    branch: release/X040
    
  mcu_app:
    branch: release/X040