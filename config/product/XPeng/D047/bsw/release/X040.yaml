base:
  - XPeng/D047/thirdparty/release/X040
  - ZOS/bsw/release/X040

projects:
  version_did:
    commands:
      configure:
        arguments:
        - -DZX_CUSTOMER_PROJECT=cpj_xpht
        - -DZX_VEH_VARIANT=x3
        - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
        - ${PROJECT_SOURCE_DIR}

  d047_system_manager_app:
    url: ${git_url}/d047/d047_system_manager_app.git
    branch: develop
    dependencies: [jsoncpp]
    commands:
      configure:
        arguments:
        - -DZX_CUSTOMER_PROJECT=cpj_chery
        - -DZX_VEH_VARIANT=x3
        - -DZX_PROJECT_NO=d047
        - -DZX_TIME_SYNC=ON
        - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
        - ${PROJECT_SOURCE_DIR}

  d047_ota_app:
    url: ${git_url}/d047/d047_ota_app.git
    branch: develop
    dependencies: [protobuf, protobuf_host, version_did, libzmq, otalib, cjson, jsoncpp, nlohmann_json]
    commands:
      configure:
        arguments:
        - -DZX_DOIP_UPDATE=ON
        - -DZX_SOMEIP_UPDATE=ON
        - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
        - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  BSW:
    dependencies:
    - arch_pattern: "x86_64-linux-ubuntu-*"
      dependencies:
      - BSW_ARCH
      - imo_com_examples
      - Inter5NoConvertDemo_ImoCom
      - Inter6NoConvertDemo_ImoCom
      - Inter5NoConvertHeroDemo_ImoCom
      - ipc
      - sensor_manager_app
    - arch_pattern: "*"
      dependencies:
      - BSW_ARCH
      - imo_com_examples
      - Inter5NoConvertDemo_ImoCom
      - Inter6NoConvertDemo_ImoCom
      - Inter5NoConvertHeroDemo_ImoCom
      - ipc
      - sensor_manager_app
      - d047_system_manager_app
      - d047_ota_app