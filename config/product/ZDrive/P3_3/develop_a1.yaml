base:
  - ZDrive/P3_3/develop

var:
  C_CXX_FLAGS : "-DAPU=1 \ 
    -DRPU=2 \
    -DSIL=3 \
    -DZX_COMPUTE_TARGET=RPU \
    -G \
    -cpu=cortexr52 \
    -thumb \
    -thumb_lib \
    -g \
    -dwarf2 \
    -Ogeneral \
    -D__CC_ARM \
    -Onone \
    -Xgvared \
    --no_vla \
    --gnu_asm \
    --no_commons \
    -no_discard_zero_initializers \
    -preprocess_assembly_files \
    -split_data_sections_by_alignment \
    -individual_data_sections \
    -individual_pragma_data_sections \
    -individual_function_sections \
    -individual_pragma_function_sections \
    -individual_attribute_data_sections \
    -individual_attribute_function_sections \
    -individual_section_name_extra_dot \
    -nostartfiles \
    -align8 \
    --unknown_pragma_errors \
    --incorrect_pragma_errors \
    -fpu=vfpv3_d16 \
    -fsingle \
    -floatsingle \
    -passsource \
    -c \
    -g \
    --diag_suppress=223 \
    -DBUILD_BIP"
  CFLAGS: "${C_CXX_FLAGS} -c11 ${CFLAGS}"
  CXXFLAGS: "${C_CXX_FLAGS} --c++14 ${CXXFLAGS}"
  LDFLAGS: "-Ogeneral -G ${LDFLAGS}"

env:
  CFLAGS: ${CFLAGS}
  CXXFLAGS: ${CXXFLAGS}
  LDFLAGS: ${LDFLAGS}