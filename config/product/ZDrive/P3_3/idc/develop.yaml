base: 
- Infra/thirdparty/develop
- Infra/idc/develop

projects:
  idc_infrastructure:
    branch: feature/PDCU-25448-dev_dz_feature_strripping

  idc_rte:
    branch: feature/refactor_modules

  idc_interfaces:
    branch: feature/PDCU-25448-dev_dz_feature_strripping

  mem:
    dependencies: [idc_infrastructure, pubif_services]

  ipc:
    dependencies: [idc_rte, vfc, cpj_interface, mem, trace, ddk, pubif_services, pubif_ipark, pubif_viper, pubif_vial, pubif_iwalle, pubif_ipilot, pubif_ego_motion, pubif_sv3d]

  cpj_interface:
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - -DENABLE_ZX_DEVICE_D100=OFF
          - -DENABLE_ZX_DEVICE_D100A=OFF
          - -DENABLE_ZX_DEVICE_D100E=OFF
          - -DENABLE_ZX_DEVICE_D300=OFF
          - -DENABLE_ZX_DEVICE_D300E=OFF
          - -DENABLE_ZX_DEVICE_D500=OFF
          - -DENABLE_ZX_DEVICE_D510=ON
          - -DENABLE_ZX_VEHICLE_E0X=OFF
          - -DENABLE_ZX_VEHICLE_M3X=OFF
          - -DENABLE_ZX_VEHICLE_M3XH=OFF
          - -DENABLE_ZX_VEHICLE_PF1=OFF
          - -DENABLE_ZX_VEHICLE_PF2=ON
          - -DENABLE_ZX_VEHICLE_T1GC=OFF
          - -DENABLE_ZX_VEHICLE_T2X=OFF
          - -DENABLE_ZX_VEHICLE_V2X=OFF
          - -DENABLE_ZX_DEVICE_APK_COMMON=OFF
          - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  idc_prm:
    url: ${git_url}/idc2/idc_prm.git
    branch: feature/PDCU-22978-refactor_modules
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  cpj_prm:
    url: null
    path: idc_prm/cpj_chery
    dependencies: [idc_prm, cpj_interface, pubif_ipark]


  idc_pma_ref:
    url: ${git_url}/idc2/idc_pma_ref.git
    branch: feature/PDCU-22978-refactor_modules
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  pma_ref:
    url: null
    path: idc_pma_ref
    dependencies: [idc_prm, cpj_interface, pubif_ipark]
