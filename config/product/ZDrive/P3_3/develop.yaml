base:
  - Infra/base/cmake_project
  - Infra/thirdparty/develop
  - Infra/framework/base/develop
  - ZDrive/P3_3/idc/develop

projects:
  boost:
    dependencies:
    - arch_pattern: "aarch64-linux-j6e*"
      dependencies: [zlib, lz4, zstd]
    - arch_pattern: "*"
      dependencies: [zlib, lz4, zstd, icu]
  tic:
    dependencies:
    - arch_pattern: "armv8-r*"
      dependencies: [idc_infrastructure, vfc, os, trace, zdrive_bsw]
    - arch_pattern: "*"
      dependencies: [idc_infrastructure, vfc, os, trace]

  imoapp_bin:
    var:
      IMOAPP_VERSION: 0.0.18

  swarch_config_ZDrive_P3_3:
    url: ${git_url}/zd_p33/swarch_config_zdrive_p3_3.git
    branch: develop
    dependencies: [imoapp_bin]
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  protobuf:
    branch: v3.19.3

  protobuf_host:
    branch: v3.19.3

  eigen:
    branch: 3.4.0

  ipark_planning:
    url: ${git_url}/zd_p33/ipark_planning.git
    branch: develop
    dependencies: [swarch_framework, swarch_config_ZDrive_P3_3, eigen, boost, protobuf_host, protobuf, fmt, ddk, vfc, pubif_sv3d, pubif_ipark, pubif_services, cpj_interface, pubif_ego_motion, pubif_iwalle, pubif_viper]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config_ZDrive_P3_3
        - --app
        - ipark_planning
        - --bus_type
        - tros
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  ipark_fusion:
    url: ${git_url}/zd_p33/ipark_fusion.git
    branch: develop
    dependencies: [swarch_framework, swarch_config_ZDrive_P3_3, eigen, boost, protobuf_host, protobuf, fmt, ddk, vfc, pubif_sv3d, pubif_ipark, pubif_services, cpj_interface, pubif_ego_motion, pubif_iwalle, pubif_viper]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config_ZDrive_P3_3
        - --app
        - ipark_fusion
        - --bus_type
        - tros
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  test_ipark_planning:
    # url: ${git_url}/zd_p33/ipark_planning.git
    # branch: develop
    # dependencies: [swarch_framework, swarch_config_ZDrive_P3_3, eigen, boost, protobuf_host, protobuf, fmt, ddk, vfc, pubif_sv3d, pubif_ipark, pubif_services, cpj_interface, pubif_ego_motion, pubif_iwalle, pubif_viper]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config_ZDrive_P3_3
        - --app
        - test_ipark_planning
        - --bus_type
        - tros
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  zdrive_asw:
    url: ${git_url}/zd_p33/zdrive_asw.git
    branch: develop
    dependencies: [swarch_config_ZDrive_P3_3, zdrive_bsw, zdrive_ipccomm, ddk, pubif_ipark, cpj_interface, cpj_prm, pubif_services, pubif_ego_motion, pubif_sv3d, pubif_iwalle]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config_ZDrive_P3_3
        - --app
        - ZdriveAsw
        - --bus_type
        - rte
        - --platform
        - j6e-r-core
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []
  zdrive_ipccomm:
    url: null
    branch: null
    path: zdrive_mcu/ASW/ipc_comm
    dependencies: [zdrive_mcu]
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: "cmake -E copy_directory"
        arguments:
        - ${PROJECT_SOURCE_DIR}/inc
        - ${IMOBUILDER_TARGET_OUTPUT}/include/ipc_common
  zdrive_bsw:
    url: null
    branch: null
    path: zdrive_mcu/BasicSoftware
    dependencies: [zdrive_mcu]
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: "cmake -E copy_directory"
        arguments:
        - ${PROJECT_SOURCE_DIR}/inc
        - ${IMOBUILDER_TARGET_OUTPUT}/include/zdrive_bsw
  zdrive_mcu_build:
    url: null
    branch: null
    path: zdrive_mcu
    dependencies: [zdrive_asw, zdrive_mcu]
    commands:
      configure:
        command: ""
      build:
        command: "scons"
        working_directory: ${PROJECT_SOURCE_DIR}
        retcode: [0]
      install:
        command: ""
  zdrive_mcu_merge:
    url: null
    branch: null
    path: zdrive_mcu
    dependencies: [zdrive_mcu_build]
    commands:
      configure:
        command: ""
      build:
        command: "ImgGenerate.bat"
        working_directory: ${PROJECT_SOURCE_DIR}
        retcode: [0]
      install:
        command: "cmake -E copy_directory"
        arguments:
        - ${PROJECT_SOURCE_DIR}/output/hex
        - ${IMOBUILDER_TARGET_OUTPUT}/bin/hex
        - " && "
        - cmake -E copy_directory
        - ${PROJECT_SOURCE_DIR}/output/dbg
        - ${IMOBUILDER_TARGET_OUTPUT}/bin/dbg
  zdrive_mcu:
    url: "${git_url}/zd_p33/zdrive_mcu.git"
    branch: develop
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""
  MCU:
    url: null
    branch: null
    dependencies: [zdrive_asw, zdrive_mcu_merge]
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""
