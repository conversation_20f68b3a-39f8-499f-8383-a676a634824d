base:
  - ZDrive/P3_3/develop

var:
  MISRA_RULES_NUMBER: "R11.3,R11.4,R11.5,R14.1,R14.2,R14.3,R8.5,R8.7,R10.1,R10.4,R8.12,R8.13,R20.1"
  MISRA_FLAGS: "--misra_2012=${MISRA_RULES_NUMBER} --diag_warning=2099,2100,1833,236,1750"
  C_CXX_FLAGS: "-DAPU=1 \
    -DRPU=2 \
    -DSIL=3 \
    -DZX_COMPUTE_TARGET=RPU \
    -cpu=cortexr52 \
    -thumb \
    -thumb_lib \
    -g \
    -dwarf2 \
    -D__CC_ARM \
    -Ospeed \
    -Xgvared \
    --no_vla \
    --gnu_asm \
    --no_commons \
    -no_discard_zero_initializers \
    -preprocess_assembly_files \
    -split_data_sections_by_alignment \
    -individual_data_sections \
    -individual_pragma_data_sections \
    -individual_function_sections \
    -individual_pragma_function_sections \
    -individual_attribute_data_sections \
    -individual_attribute_function_sections \
    -individual_section_name_extra_dot \
    -nostartfiles \
    -align8 \
    --unknown_pragma_errors \
    --incorrect_pragma_errors \
    -fpu=vfpv3_d16 \
    -fsingle \
    -nofloatsingle \
    -passsource \
    -c \
    -g \
    --diag_suppress=223 \
    -DBUILD_BIP \
    ${MISRA_FLAGS}"
  CFLAGS: "${C_CXX_FLAGS} -c11 ${CFLAGS}"
  CXXFLAGS: "${C_CXX_FLAGS} --c++14 ${CXXFLAGS}"
  LDFLAGS: "-G ${LDFLAGS}"

env:
  CFLAGS: ${CFLAGS}
  CXXFLAGS: ${CXXFLAGS}
  LDFLAGS: ${LDFLAGS}