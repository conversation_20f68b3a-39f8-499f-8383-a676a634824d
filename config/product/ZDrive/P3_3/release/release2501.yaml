base:
  - Infra/base/cmake_project
  - Infra/thirdparty/develop
  - Infra/framework/base/develop
  - Infra/idc/develop

projects:
  boost:
    branch: 1.83.0
    dependencies:
    - arch_pattern: "aarch64-linux-j6e*"
      dependencies: [zlib, lz4, zstd]
    - arch_pattern: "*"
      dependencies: [zlib, lz4, zstd, icu]
  tic:
    dependencies:
    - arch_pattern: "armv8-r*"
      dependencies: [idc_infrastructure, vfc, os, trace, zdrive_bsw]
    - arch_pattern: "*"
      dependencies: [idc_infrastructure, vfc, os, trace]

  idc_infrastructure:
    branch: 5c5618fd00584ba33b7febc51c22cbf6be728b5a
    # branch: feature/PDCU-25448-dev_dz_feature_strripping

  idc_rte:
    branch: 166bdd78d359bf5502e3bc8b157092da00a2fbe7
    # branch: feature/refactor_modules

  idc_interfaces:
    branch: 710614f80c16a3348c4639f06c0b23ed4776500f
    # branch: feature/PDCU-25448-dev_dz_feature_strripping

  mem:
    dependencies: [idc_infrastructure, pubif_services]

  ipc:
    dependencies: [idc_rte, vfc, cpj_interface, mem, trace, ddk, pubif_services, pubif_ipark, pubif_viper, pubif_vial, pubif_iwalle, pubif_ipilot, pubif_ego_motion, pubif_sv3d]

  cpj_interface:
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - -DENABLE_ZX_DEVICE_D100=OFF
          - -DENABLE_ZX_DEVICE_D100A=OFF
          - -DENABLE_ZX_DEVICE_D100E=OFF
          - -DENABLE_ZX_DEVICE_D300=OFF
          - -DENABLE_ZX_DEVICE_D300E=OFF
          - -DENABLE_ZX_DEVICE_D500=OFF
          - -DENABLE_ZX_DEVICE_D510=ON
          - -DENABLE_ZX_VEHICLE_E0X=OFF
          - -DENABLE_ZX_VEHICLE_M3X=OFF
          - -DENABLE_ZX_VEHICLE_M3XH=OFF
          - -DENABLE_ZX_VEHICLE_PF1=OFF
          - -DENABLE_ZX_VEHICLE_PF2=ON
          - -DENABLE_ZX_VEHICLE_T1GC=OFF
          - -DENABLE_ZX_VEHICLE_T2X=OFF
          - -DENABLE_ZX_VEHICLE_V2X=OFF
          - -DENABLE_ZX_DEVICE_APK_COMMON=OFF
          - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  idc_prm:
    url: ${git_url}/idc2/idc_prm.git
    branch: feature/PDCU-22978-refactor_modules
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  cpj_prm:
    url: null
    path: idc_prm/cpj_chery
    dependencies: [idc_prm, cpj_interface, pubif_ipark]


  idc_pma_ref:
    url: ${git_url}/idc2/idc_pma_ref.git
    branch: feature/PDCU-22978-refactor_modules
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  pma_ref:
    url: null
    path: idc_pma_ref
    dependencies: [idc_prm, cpj_interface, pubif_ipark]

  imoapp_bin:
    var:
      IMOAPP_VERSION: 0.0.18

  swarch_config_ZDrive_P3_3:
    url: ${git_url}/zd_p33/swarch_config_zdrive_p3_3.git
    branch: 38e3f37198ea37154c34c2f0029d105306be3c73
    # branch: develop
    dependencies: [imoapp_bin]
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  protobuf:
    branch: v3.19.3

  protobuf_host:
    branch: v3.19.3

  eigen:
    branch: 3.4.0

  fmt:
    branch: 9.0.0

  yaml_cpp:
    branch: 0.8.0

  ipark_planning:
    url: ${git_url}/zd_p33/ipark_planning.git
    branch: release/release2501
    dependencies: [swarch_framework, swarch_config_ZDrive_P3_3, eigen, boost, protobuf_host, protobuf, fmt, ddk, vfc, pubif_sv3d, pubif_ipark, pubif_services, cpj_interface, pubif_ego_motion, pubif_iwalle, pubif_viper]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config_ZDrive_P3_3
        - --app
        - ipark_planning
        - --bus_type
        - tros
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  ipark_fusion:
    url: ${git_url}/zd_p33/ipark_fusion.git
    branch: develop
    dependencies: [swarch_framework, swarch_config_ZDrive_P3_3, eigen, boost, protobuf_host, protobuf, fmt, ddk, vfc, pubif_sv3d, pubif_ipark, pubif_services, cpj_interface, pubif_ego_motion, pubif_iwalle, pubif_viper]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config_ZDrive_P3_3
        - --app
        - ipark_fusion
        - --bus_type
        - tros
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  test_ipark_planning:
    # url: ${git_url}/zd_p33/ipark_planning.git
    # branch: develop
    # dependencies: [swarch_framework, swarch_config_ZDrive_P3_3, eigen, boost, protobuf_host, protobuf, fmt, ddk, vfc, pubif_sv3d, pubif_ipark, pubif_services, cpj_interface, pubif_ego_motion, pubif_iwalle, pubif_viper]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config_ZDrive_P3_3
        - --app
        - test_ipark_planning
        - --bus_type
        - tros
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  zdrive_asw:
    url: ${git_url}/zd_p33/zdrive_asw.git
    branch: features/ywh/release_to_zdrive
    dependencies: [swarch_config_ZDrive_P3_3, zdrive_bsw, zdrive_ipccomm, ddk, pubif_ipark, cpj_prm, cpj_interface, pubif_services, pubif_ego_motion, pubif_sv3d, pubif_iwalle]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config_ZDrive_P3_3
        - --app
        - ZdriveAsw
        - --bus_type
        - rte
        - --platform
        - j6e-r-core
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []
  zdrive_ipccomm:
    url: null
    branch: null
    path: zdrive_mcu/ASW/ipc_comm
    dependencies: [zdrive_mcu]
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: "cmake -E copy_directory"
        arguments:
        - ${PROJECT_SOURCE_DIR}/inc
        - ${IMOBUILDER_TARGET_OUTPUT}/include/ipc_common
  zdrive_bsw:
    url: null
    branch: null
    path: zdrive_mcu/BasicSoftware
    dependencies: [zdrive_mcu]
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: "cmake -E copy_directory"
        arguments:
        - ${PROJECT_SOURCE_DIR}/inc
        - ${IMOBUILDER_TARGET_OUTPUT}/include/zdrive_bsw
  zdrive_mcu_build:
    url: null
    branch: null
    path: zdrive_mcu
    dependencies: [zdrive_asw, zdrive_mcu]
    commands:
      configure:
        command: ""
      build:
        command: "scons"
        working_directory: ${PROJECT_SOURCE_DIR}
        retcode: [0]
      install:
        command: ""
  zdrive_mcu_merge:
    url: null
    branch: null
    path: zdrive_mcu
    dependencies: [zdrive_mcu_build]
    commands:
      configure:
        command: ""
      build:
        command: "ImgGenerate.bat"
        working_directory: ${PROJECT_SOURCE_DIR}
        retcode: [0]
      install:
        command: "cmake -E copy_directory"
        arguments:
        - ${PROJECT_SOURCE_DIR}/output/hex
        - ${IMOBUILDER_TARGET_OUTPUT}/bin/hex
        - " && "
        - cmake -E copy_directory
        - ${PROJECT_SOURCE_DIR}/output/dbg
        - ${IMOBUILDER_TARGET_OUTPUT}/bin/dbg
  zdrive_mcu:
    url: "${git_url}/zd_p33/zdrive_mcu.git"
    branch: 90cf9bf891a395d2517a3a240a513fc88077b068
    # branch: develop
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""
  MCU:
    url: null
    branch: null
    dependencies: [zdrive_asw, zdrive_mcu_merge]
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""
