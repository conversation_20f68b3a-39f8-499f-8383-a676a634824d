base:
  - ZOS/thirdparty/develop
  - BSP/j6/develop
  - Z<PERSON>/mcu/develop
  - Z<PERSON>/driving/develop
  - ZOS/parking/develop
  - ZOS/iuni/develop
  - ZOS/perception/develop
  - ZOS/prediction/develop
  - ZOS/localization_mapping/develop
  - ZOS/dataloop/develop

package:
  runtime:
    manifest: null
    files:
    - ${IMOBUILDER_TARGET_OUTPUT}/imo_mdw_init.bash
    - ${IMOBUILDER_TARGET_OUTPUT}/run_asan_lsan_tsan_ubsan.sh
    - ${IMOBUILDER_TARGET_OUTPUT}/run_gtest.sh
    - ${IMOBUILDER_TARGET_OUTPUT}/target_start.sh
    directories:
    - ${IMOBUILDER_TARGET_OUTPUT}/bin
    - ${IMOBUILDER_TARGET_OUTPUT}/lib
    - ${IMOBUILDER_TARGET_OUTPUT}/config
    - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/bin
    - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib
    - ${<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_TARGET_OUTPUT}/etc
    - ${IMOBUILDER_TARGET_OUTPUT}/scripts

projects:
  hobot_j6e_v1_0_0_pack:
    var:
      debug_lunch_args: "j6m_debug_platform_defconfig"
      coverage_lunch_args: "j6m_debug_platform_defconfig"
      release_lunch_args: "j6m_release_platform_defconfig"
      relwithdebinfo_lunch_args: "j6m_release_platform_defconfig"
      minsizerel_lunch_args: "j6m_release_platform_defconfig"
    branch: develop

  ZOS_MCU:
    url: null
    branch: null
    dependencies:
      - MCU
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  ZOS_BSP:
    url: null
    branch: null
    dependencies:
      - hobot_j6e_v1_0_0_pack
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  ZOS:
    url: null
    branch: null
    dependencies:
      - BSW
      - Driving
      - Parking
      - Iuni
      - Perception
      - Prediction
      - LocalizationMapping
      - DataLoop
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""
