base:
  - ZOS/develop


projects:
  algorithm_base:
    branch: feature/lwg/test_compile

  prediction_lib:
    branch: feature/zhangdarui/add_math

  system_manager_app:
    # dependencies: [swarch_config_zos, state_management, execution_management, platform_health_management]
    dependencies: [swarch_config_zos]
  
  BSW_APP:
    dependencies: 
      - BSW_ARCH
      - imo_com_examples
      - Inter5NoConvertDemo_ImoCom
      - Inter6NoConvertDemo_ImoCom
      - Inter5NoConvertHeroDemo_ImoCom
      - sensor_manager_app
      - system_manager_app
      - ipc
      - dataloop_app

  Driving:
    dependencies: 
      - BSW_ARCH
      - driving_apps
      - driving_control
      - driving_planning
      - activesafety_evaluator
      - driving_assist_func
  
  Perception:
    url: null
    branch: null
    dependencies: 
      - BSW_ARCH
      - perception_app

  
  LocalizationMapping:
    url: null
    branch: null
    dependencies: 
      - BSW_ARCH
      - ipolaris_base
      - ipolaris_map_base
      - ipolaris_map_constructor
      - ipolaris_map_manager
      - ipolaris_localization
      - ipolaris_app