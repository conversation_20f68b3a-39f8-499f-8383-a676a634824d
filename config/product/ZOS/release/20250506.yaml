base:
- ZOS/thirdparty/release/20250506
- BSP/j6/release/20250408
- ZOS/bsw/release/20250506
- ZOS/dataloop/release/20250506
- ZOS/driving/release/20250506
- ZOS/iuni/release/20250506
- ZOS/localization_mapping/release/20250506
- ZOS/mcu/release/20250506
- ZOS/parking/release/20250506
- ZOS/perception/release/20250506
- ZOS/prediction/release/20250506
- ZOS/thirdparty/release/20250506

package:
  runtime:
    manifest: null
    files:
    - ${IMOBUILDER_TARGET_OUTPUT}/imo_mdw_init.bash
    - ${IMOBUILDER_TARGET_OUTPUT}/run_asan_lsan_tsan_ubsan.sh
    - ${IMOBUILDER_TARGET_OUTPUT}/run_gtest.sh
    - ${IMOBUILDER_TARGET_OUTPUT}/target_start.sh
    directories:
    - ${IMOBUILDER_TARGET_OUTPUT}/bin
    - ${IMOBUILDER_TARGET_OUTPUT}/lib
    - ${I<PERSON>BUILDER_TARGET_OUTPUT}/config
    - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/bin
    - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib
    - ${IMOBUILDER_TARGET_OUTPUT}/etc
    - ${IMOBUILDER_TARGET_OUTPUT}/scripts

projects:
  ZOS_MCU:
    url: null
    branch: null
    dependencies:
      - MCU
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  ZOS_BSP:
    url: null
    branch: null
    dependencies:
      - hobot_j6e_v1_0_0_pack
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  ZOS:
    url: null
    branch: null
    dependencies:
      - BSW
      - Driving
      - Parking
      - Iuni
      - Perception
      - Prediction
      - LocalizationMapping
      - DataLoop
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  # ============== BSP/j6 ============== #
  hbre:
    branch: develop

  kernel:
    branch: develop

  uboot:
    branch: develop

  hobot_drivers:
    branch: develop

  hobot_j6e_v1_0_0_pack:
    var:
      debug_lunch_args: "j6m_debug_platform_defconfig"
      coverage_lunch_args: "j6m_debug_platform_defconfig"
      release_lunch_args: "j6m_release_platform_defconfig"
      relwithdebinfo_lunch_args: "j6m_release_platform_defconfig"
      minsizerel_lunch_args: "j6m_release_platform_defconfig"
    branch: develop

  # ============== ZOS/bsw ============== #
  vfc:
    branch: feature/zhangjiannan/add_insert_to_fixed_vector

  idc_rte:
    branch: feature/refactor_modules

  version_did:
    branch: develop

  zos_scripts:
    branch: develop

  basic_types:
    branch: develop

  zos_msg:
    branch: develop

  zos_osal:
    branch: develop

  zos_log:
    branch: develop

  imo_com:
    branch: develop

  imo_com_examples:
    branch: develop

  imo_com_tools:
    branch: develop

  swarch_config:
    branch: develop

  swarch_framework:
    branch: develop

  swarch_codegen_demo:
    branch: develop

  time_synchronization:
    branch: develop

  imosar_lib:
    branch: develop

  state_management:
    branch: develop

  execution_management:
    branch: develop

  platform_health_management:
    branch: develop

  algorithm_base:
    branch: develop

  com_gateway:
    branch: develop

  sensor_manager_app:
    branch: develop

  system_manager_app:
    branch: develop

  otalib:
    branch: develop

  zos_hal:
    branch: develop

  ota_app:
    branch: develop

  # ============== ZOS/dataloop ============== #
  datasim_app:
    branch: develop

  dataloop_app:
    branch: develop

  dataloop_base:
    branch: develop

  dalo_protocol:
    branch: develop

  xmt2:
    branch: develop

  xmt2_plugins:
    branch: develop

  mcap_sdk:
    branch: develop

  dalo_protocol_server_example:
    branch: develop
  
  # ============== ZOS/driving ============== #
  driving_planning_base:
    branch: develop

  driving_control_base:
    branch: develop

  driving_assist_base:
    branch: develop

  driving_corner_radar_base:
    branch: develop

  activesafety_evaluator_base:
    branch: develop

  activesafety_evaluator_app:
    branch: develop

  driving_planning_app:
    branch: develop

  driving_control_app:
    branch: develop

  driving_assist_func:
    branch: develop

  driving_apps:
    branch: develop
  
  # ============== ZOS/iuni ============== #
  iuni:
    branch: develop

  # ============== ZOS/localization_mapping  ============== #
  ipolaris_base:
    branch: develop

  ipolaris_map_base:
    branch: develop

  ipolaris_map_constructor:
    branch: develop

  ipolaris_map_manager:
    branch: develop

  ipolaris_localization:
    branch: develop

  ipolaris_mapping:
    branch: develop

  ipolaris_tools:
    branch: develop

  ipolaris_app:
    branch: develop

  # ============== ZOS/mcu ============== #
  hobot_j6e_mcu:
    branch: develop

  idc_rte_mcu:
    branch: feature/IDC510-184-j6p-platform-adaptation-imobuild-compiler-tool

  idc_interfaces_mcu:
    branch: feature/IDC510-184-j6p-platform-adaptation-imobuild-compiler-tool

  idc_infrastructure_mcu:
    branch: feature/IDC510-184-j6p-platform-adaptation-imobuild-compiler-tool

  mcu_app:
    branch: develop

  # ============== ZOS/parking ============== #
  park_app:
    branch: develop

  parking_planning_app:
    branch: develop

  park_control_app:
    branch: develop

  pma_pf:
    branch: feature/parkdr_base
  
  park_dr:
    branch: develop

  # ============== ZOS/perception ============== #
  perception_base:
    branch: develop

  perception_common:
    branch: develop

  # common
  perception_lib:
    branch: develop_chery_pf1

  perception_common_onboard:
    branch: develop_chery_pf1

  # preprocess
  perception_preprocess:
    branch: develop/j6

  perception_preprocess_onboard:
    branch: develop/j6

  # calibration
  perception_calibration:
    branch: develop

  perception_calibration_onboard:
    branch: develop

  # detection
  perception_inference:
    branch: develop/j6

  perception_detection:
    branch: develop/j6

  perception_detection_onboard:
    branch: develop/j6

  # fusion
  perception_fusion:
    branch: develop_chery_pf1

  perception_fusion_onboard:
    branch: develop_chery_pf1

  # app
  perception_app:
    branch: product/ZOS/develop
    
  # ============== ZOS/prediction ============== #
  prediction_lib:
    branch: develop

  prediction_app:
    branch: develop