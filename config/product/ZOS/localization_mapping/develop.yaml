base:
  - ZOS/bsw/develop

projects:
  ipolaris_base:
    url: ${git_url}/ipolaris/ipolaris_base.git
    branch: develop
    dependencies: [vfc, zos_scripts,algorithm_base, gtsam, opencv, zos_log]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  ipolaris_map_base:
    url: ${git_url}/ipolaris/ipolaris_map_base.git
    branch: develop
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - -DIMOBUILDER_HOST_OUTPUT=${IMOBUILDER_HOST_OUTPUT}
          - -DBUILD_SHARED_LIBS=ON
          - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
          - -DCMAKE_INSTALL_RPATH="\$ORIGIN/../lib:\$ORIGIN/../3rdparty/lib"
          - ${PROJECT_SOURCE_DIR}
    dependencies: [protobuf, protobuf_host, sqlite, sqlite_host, ipolaris_base]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  ipolaris_map_sdk:
    url: ${git_url}/ipolaris/ipolaris_map_sdk.git
    branch: develop
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - -DIMOBUILDER_HOST_OUTPUT=${IMOBUILDER_HOST_OUTPUT}
          - -DBUILD_SHARED_LIBS=ON
          - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
          - -DCMAKE_INSTALL_RPATH="\$ORIGIN/../lib:\$ORIGIN/../3rdparty/lib"
          - -DIPOLARIS_BUILD_WITH_MAP_SDK_AMAP_HD_MAP_API=ON
          - -DIPOLARIS_BUILD_WITH_MAP_SDK_TENCENT_MAP_NERD_API=OFF
          - ${PROJECT_SOURCE_DIR}
    dependencies: [curl, ipolaris_base, ipolaris_map_base]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  ipolaris_map_constructor:
    url: ${git_url}/ipolaris/ipolaris_map_constructor.git
    branch: develop
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - -DIMOBUILDER_HOST_OUTPUT=${IMOBUILDER_HOST_OUTPUT}
          - -DBUILD_SHARED_LIBS=ON
          - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
          - -DCMAKE_INSTALL_RPATH="\$ORIGIN/../lib:\$ORIGIN/../3rdparty/lib"
          - -DIPOLARIS_BUILD_WITH_MAP_SDK_AMAP_HD_MAP_API=ON
          - -DIPOLARIS_BUILD_WITH_MAP_SDK_TENCENT_MAP_NERD_API=OFF
          - ${PROJECT_SOURCE_DIR}
    dependencies: [ipolaris_map_sdk]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  ipolaris_map_manager:
    url: ${git_url}/ipolaris/ipolaris_map_manager.git
    branch: develop
    dependencies: [ipolaris_map_constructor]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  ipolaris_localization:
    url: ${git_url}/ipolaris/ipolaris_localization.git
    branch: develop
    dependencies: [vfc, zos_scripts, ipolaris_map_manager]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  ipolaris_navigation:
    url: ${git_url}/ipolaris/ipolaris_navigation.git
    branch: develop
    dependencies: [ipolaris_map_manager]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  ipolaris_mapping:
    url: ${git_url}/ipolaris/ipolaris_mapping.git
    branch: develop
    dependencies: [osqp, ipolaris_map_manager, ipolaris_navigation]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  ipolaris_tools:
    url: ${git_url}/ipolaris/ipolaris_tools.git
    branch: develop
    dependencies: [rapidjson, ipolaris_map_manager]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  ipolaris_app:
    url: ${git_url}/zos/ipolaris_app.git
    branch: develop
    dependencies: [swarch_config_zos, ipolaris_localization, ipolaris_mapping]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
        - --app
        - LocalizationMapping
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  LocalizationMapping:
    url: null
    branch: null
    dependencies: 
      - BSW_ARCH
      - ipolaris_base
      - ipolaris_map_base
      - ipolaris_map_constructor
      - ipolaris_map_manager
      - ipolaris_localization
      - ipolaris_navigation
      - ipolaris_app
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""