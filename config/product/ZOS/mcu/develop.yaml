base:
  - ZOS/bsw/develop

projects:
  hobot_j6e_mcu:
    url: ${git_url}/sdksoc/hobot_j6e_v0.8.0_mcu.git
    branch: develop
    commands:
      # patch:
      #   command: ln
      #   arguments:
      #   - -sfn
      #   - ${PROJECT_SOURCE_DIR}
      #   - ${PROJECT_SOURCE_DIR}/../mcu_app/components/microsar/src/_AR_ThirdParty/ThirdParty/hobot_j6e_v0.8.0_mcu
      #   working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        command: null
      build:
        command: null
          # command: python3
          # arguments: 
          # - build_freertos.py
          # - --build_type=matrix_sip_A_lib 
          # - --config=Release
          # - --custom_folder=cpj_chery
          # - --device=d510p
          # - --core=mcu10
          # - --vehicle=e0x
          # - --sensor=c1
          # - --hardware=h2
          # - --lib_path=${PROJECT_SOURCE_DIR}/../mcu_app/build/cpj_chery_e0x_d510p_h2c1/rpu_arm_gcc/libJ6_MCU_MCAL.a
          # working_directory: ${PROJECT_SOURCE_DIR}/../mcu_app/components/microsar/src/_AR_ThirdParty/ThirdParty/hobot_j6e_v0.8.0_mcu/zx_build
      install:
          command: ""

  idc_rte_mcu:
    url: ${git_url}/idc2/idc_rte.git
    branch: feature/IDC510-184-j6p-platform-adaptation-imobuild-compiler-tool
    commands:
      configure:
        command: null
      build:
        command: null
      install: 
        command: null

  idc_interfaces_mcu:
    url: ${git_url}/idc2/idc_interfaces.git
    branch: feature/IDC510-184-j6p-platform-adaptation-imobuild-compiler-tool
    commands:
      configure:
        command: null
      build:
        command: null
      install: 
        command: null

  idc_infrastructure_mcu:
    url: ${git_url}/idc2/idc_infrastructure.git
    branch: feature/IDC510-184-j6p-platform-adaptation-imobuild-compiler-tool
    commands:
      configure:
        command: null
      build:
        command: null
      install: 
        command: null
  uss_perception:
    url: ${git_url}/zos/uss_perception.git
    branch: develop
    commands:
      configure:
          arguments:
          - -DZX_VEH_VARIANT=e0x
          - -DZX_PROJECT_DEVICE=d510p
          - -DCPJ_FOLDER_NAME=cpj_chery
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - ${PROJECT_SOURCE_DIR}

  uss_sil:
    url: null
    path: uss_perception/uss_sil
    branch: develop
    dependencies: [swarch_config_zos, uss_perception]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
        - --app
        - UssSil
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []
      
  mcu_app:
    url: ${git_url}/zos/mcu_app.git
    branch: develop
    dependencies: [idc_rte_mcu, idc_interfaces_mcu, idc_infrastructure_mcu, vfc, nlohmann_json, version_did, hobot_j6e_mcu, zos_msg, uss_perception, imo_com_micro]
    commands:
      patch:
        command: ln
        arguments:
        - -sfn
        - ${PROJECT_SOURCE_DIR}/../hobot_j6e_mcu
        - ${PROJECT_SOURCE_DIR}/components/microsar/src/_AR_ThirdParty/ThirdParty/hobot_j6e_v0.8.0_mcu
        - "&&"
        - ln
        - -sfn
        - ${PROJECT_SOURCE_DIR}/../idc_interfaces_mcu
        - ${PROJECT_SOURCE_DIR}/idc_interfaces
        - "&&"
        - ln
        - -sfn
        - ${PROJECT_SOURCE_DIR}/../idc_infrastructure_mcu
        - ${PROJECT_SOURCE_DIR}/idc_infrastructure
        - "&&"
        - ln
        - -sfn
        - ${PROJECT_SOURCE_DIR}/../idc_rte_mcu
        - ${PROJECT_SOURCE_DIR}/idc_rte
        working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        arguments:
        - -DBUILD_TESTING:BOOL=ON
        - -DENABLE_TESTING:BOOL=ON
        - -DBENCHMARK_ENABLE_TESTING:BOOL=OFF
        - -DBENCHMARK_ENABLE_GTEST_TESTS:BOOL=OFF
        - -DHAVE_STD_REGEX=0
        - -DTESTING_TOOL:STRING=googletest
        - -DCMAKE_EXPORT_COMPILE_COMMANDS=1
        - -DCPJ_FOLDER_NAME=cpj_chery
        - -DZX_CUSTOMER_PROJECT:STRING=cpj_chery
        - -DZX_PROJECT_TARGET_CMAKE=${PROJECT_SOURCE_DIR}/components/cmake_build/d510p_rpu.cmake
        - -DZX_PROJECT_OPTIONS_CMAKE=${PROJECT_SOURCE_DIR}/components/cmake_build/d510p/compile_options_rpu.cmake
        - -DCMAKE_BUILD_TYPE=Release
        - -DZX_SWID_JENKINS=developer
        - -DZX_PRM_TOOLCHAIN_MODE=off
        - -DZX_PROJECT_DEVICE=d510p
        - -DZX_PROJECT_VARIANT=h2c1
        - -DZX_HARDWARE=h2
        - -DZX_SENSOR=c1
        - -DZX_PROJECT_TARGET=rpu
        - -DZX_VEH_VARIANT=e0x
        - -DZX_PROJECT_WHICH=arm
        - -DZX_PROJECT_BUILD_DIR=${PROJECT_SOURCE_DIR}/build/cpj_chery_e0x_d510p_h2c1/rpu_arm_gcc
        - -DZX_PROJECT_NORFLASH=m35xu512
        - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
        - -B ${PROJECT_SOURCE_DIR}/build/cpj_chery_e0x_d510p_h2c1/rpu_arm_gcc
        - ${PROJECT_SOURCE_DIR}
      build:
          command: "cmake --build ${PROJECT_SOURCE_DIR}/build/cpj_chery_e0x_d510p_h2c1/rpu_arm_gcc"
      clean_binary:
          command: '[ -d "${PROJECT_SOURCE_DIR}/.git" ]' 
          arguments: 
          - " && rm -rf ${PROJECT_SOURCE_DIR}/build/cpj_chery_e0x_d510p_h2c1/rpu_arm_gcc"
          - " && rm -rf ${PROJECT_SOURCE_DIR}/cpj_chery/microsar_cfg/e0x_d510p/build/SipAddon/Appl/obj"
          - " && rm -rf ${PROJECT_SOURCE_DIR}/cpj_chery/microsar_cfg/e0x_d510p/build/SipAddon/Appl/err"
          - " && rm -rf ${PROJECT_SOURCE_DIR}/cpj_chery/microsar_cfg/e0x_d510p/build/SipAddon/Appl/lst"
          - " && find ${PROJECT_SOURCE_DIR}/components/microsar/src/_AR_ThirdParty/ThirdParty/hobot_j6e_v0.8.0_mcu/output/inc -name \"*.h\" -type f -delete"
          - " && rm -rf ${PROJECT_SOURCE_DIR}/components/microsar/src/_AR_ThirdParty/ThirdParty/hobot_j6e_v0.8.0_mcu/output/lib"
          - " && rm -rf ${PROJECT_SOURCE_DIR}/components/microsar/src/_AR_ThirdParty/ThirdParty/hobot_j6e_v0.8.0_mcu/output/objs"
          working_directory: ${PROJECT_SOURCE_DIR}
          retcode: []
      install:
          command: "cmake --install  ${PROJECT_SOURCE_DIR}/build/cpj_chery_e0x_d510p_h2c1/rpu_arm_gcc"
      # install:
      #     command: cp
      #     arguments:
      #       - -r
      #       - ${PROJECT_SOURCE_DIR}/build/cpj_chery_e0x_d510p_h2c1/rpu_arm_gcc/../bin
      #       - ${IMOBUILDER_TARGET_OUTPUT}

  MCU:
    url: null
    branch: null
    dependencies: 
      - mcu_app
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""
