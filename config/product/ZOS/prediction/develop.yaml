base:
  - ZOS/bsw/develop

projects:
  prediction_lib:
    url: ${git_url}/zos/prediction_lib.git
    branch: develop
    dependencies: [basic_types,eigen,boost,zos_msg,algorithm_base,perception_inference]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []
  prediction_app:
    url: ${git_url}/zos/prediction_app.git
    branch: develop
    dependencies: [swarch_config_zos,prediction_lib]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
        - --app
        - prediction_app
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]

  Prediction:
    url: null
    branch: null
    dependencies: 
      - BSW_ARCH
      - prediction_app
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""
