base:
  - ZOS/thirdparty/release/20250506

projects:
  vfc:
    url: ${git_url}/zos/vfc.git
    branch: feature/zhangjiannan/add_insert_to_fixed_vector
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  idc_rte:
    url: ${git_url}/idc2/idc_rte.git
    branch: feature/refactor_modules
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  ffc:
    url: null
    path: idc_rte/ffc
    dependencies: [idc_rte, vfc]

  version_did:
    url: ${git_url}/zos/version_did.git
    branch: develop
    commands:
      configure:
        arguments:
        - -DZX_CUSTOMER_PROJECT=cpj_chery
        - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
        - ${PROJECT_SOURCE_DIR}

  zos_scripts:
    url: ${git_url}/zos/zos_scripts.git
    branch: develop
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  basic_types:
    url: ${git_url}/zos/basic_types.git
    branch: develop
    dependencies: [vfc, zos_scripts, nlohmann_json]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  zos_msg:
    url: ${git_url}/zos/zos_msg.git
    branch: develop
    dependencies: [basic_types]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  zos_osal:
    url: ${git_url}/zos/zos_osal.git
    branch: develop
    dependencies:
    - arch_pattern: "aarch64-linux-renesas*"
      dependencies: [basic_types, nlohmann_json, json_schema_validator]
    - arch_pattern: "*"
      dependencies: [basic_types]
    commands:
      configure:
        arguments:
        - arch_pattern: "aarch64-linux-renesas*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - -DBUILD_SHARED_LIBS=OFF
          - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
          - -DCMAKE_INSTALL_RPATH="\$ORIGIN/../lib:\$ORIGIN/../3rdparty/lib"
          - ${PROJECT_SOURCE_DIR}
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - -DBUILD_SHARED_LIBS=ON
          - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
          - -DCMAKE_INSTALL_RPATH="\$ORIGIN/../lib:\$ORIGIN/../3rdparty/lib"
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  zos_log:
    url: ${git_url}/zos/zos_log.git
    branch: develop
    dependencies: [zlog, yaml_cpp]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - -DCMAKE_INSTALL_RPATH="\$ORIGIN/../lib:\$ORIGIN/../3rdparty/lib"
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  imo_com:
    url: ${git_url}/inf_sw_arch/imo_com.git
    branch: develop
    dependencies:
    - arch_pattern: "arm*"
      dependencies: [boost]
    - arch_pattern: "*"
      dependencies: [zos_log, readline, termcap, gtest, protobuf, protobuf_host, mcap_builder]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - -DCMAKE_INSTALL_RPATH="\$ORIGIN/../lib:\$ORIGIN/../3rdparty/lib:\$ORIGIN/../../../lib:\$ORIGIN/../../../3rdparty/lib"
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  imo_com_examples:
    url: ${git_url}/inf_sw_arch/imo_com_examples.git
    branch: develop
    dependencies: [imo_com, zos_osal, zos_msg]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - -DCMAKE_INSTALL_RPATH="\$ORIGIN/../lib:\$ORIGIN/../3rdparty/lib"
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  imo_com_tools:
    url: ${git_url}/inf_sw_arch/imo_com_tools.git
    branch: develop
    dependencies: [imo_com, mcap_builder, zos_msg]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - -DCMAKE_INSTALL_RPATH="\$ORIGIN/../lib:\$ORIGIN/../3rdparty/lib"
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  imoapp_bin:
    url: null
    branch: null
    var:
      IMOAPP_VERSION: 0.0.24
    commands:
      configure:
        command: "cmake -E"
        arguments:
        - make_directory
        - ${PROJECT_BINARY_DIR}
        - ${IMOBUILDER_HOST_OUTPUT}/bin
      build:
        command: curl
        arguments:
        - arch_pattern: "*x86_64-linux*"
          arguments:
          - -o
          - imoapp_v${IMOAPP_VERSION}.tar.gz.cache
          - "${artifactory_url}/repository/imoapp_resource/v${IMOAPP_VERSION}/imoapp_v${IMOAPP_VERSION}_x86_64-linux.tar.gz"
          - " && cmake -E rename imoapp_v${IMOAPP_VERSION}.tar.gz.cache imoapp_v${IMOAPP_VERSION}.tar.gz "
        - arch_pattern: "armv8-r-*j6e*"
          arguments:
          - -o
          - imoapp_v${IMOAPP_VERSION}.tar.gz.cache
          - "${artifactory_url}/repository/imoapp_resource/v${IMOAPP_VERSION}/imoapp_v${IMOAPP_VERSION}_win10.tar.gz"
          - " && cmake -E rename imoapp_v${IMOAPP_VERSION}.tar.gz.cache imoapp_v${IMOAPP_VERSION}.tar.gz "
        - arch_pattern: "*"
          arguments:
          - -o
          - imoapp_v${IMOAPP_VERSION}.tar.gz.cache
          - "${artifactory_url}/repository/imoapp_resource/v${IMOAPP_VERSION}/imoapp_v${IMOAPP_VERSION}_x86_64-linux.tar.gz"
          - " && cmake -E rename imoapp_v${IMOAPP_VERSION}.tar.gz.cache imoapp_v${IMOAPP_VERSION}.tar.gz "
        parallel_arg: ""
        signatures: ["${PROJECT_BINARY_DIR}/imoapp_v${IMOAPP_VERSION}.tar.gz"]
      install:
        command: "cmake -E"
        arguments:
        - tar
        - xvf
        - ${PROJECT_BINARY_DIR}/imoapp_v${IMOAPP_VERSION}.tar.gz
        working_directory: ${IMOBUILDER_HOST_OUTPUT}/bin

  swarch_config:
    url: ${git_url}/inf_sw_arch/swarch_config.git
    branch: develop
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  swarch_framework:
    url: ${git_url}/inf_sw_arch/swarch_framework.git
    branch: develop
    dependencies: [yaml_cpp]
    commands:
      gtest:
        command: make
        arguments: [UT_gtest]
        parallel_arg: -j
        retcode: [0]
      test:
        env:
          SOURCE_ROOT: ${PROJECT_SOURCE_DIR}
          BUILD_ROOT: ${BUILD_ROOT}
          INSTALL_ROOT: ${INSTALL_ROOT}
          LD_LIBRARY_PATH: ${TARGET_LD_LIBRARY_PATH}
        command: ctest
        arguments:
        - --output-junit ctest_result.xml
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  swarch_config_Demo:
    url: null
    path: swarch_config/Demo
    dependencies: [swarch_config, imoapp_bin]
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  swarch_codegen_demo:
    url: ${git_url}/inf_sw_arch/swarch_codegen_demo.git
    branch: develop
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  Inter5NoConvertDemo_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter5NoConvertDemo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter5NoConvertDemo
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter5NoConvertHeroDemo_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter5NoConvertHeroDemo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter5NoConvertHeroDemo
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter6NoConvertDemo_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter6NoConvertDemo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter6NoConvertDemo
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter6NoConvertWithCallbackDemo_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter6NoConvertWithCallbackDemo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter6NoConvertWithCallbackDemo
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  time_synchronization:
    url: ${git_url}/zos/time_synchronization.git
    branch: develop
    dependencies: [basic_types]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  imosar_lib:
    url: ${git_url}/zos/imosar_lib.git
    branch: develop
    dependencies: [boost, zos_log]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  sm_dpt_lib:
    path: imosar_lib/sm_dpt_lib
    dependencies: [swarch_framework, swarch_config_zos, imo_com, zos_msg, imosar_lib]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
        - --app
        - SmDptLib
        - --bus_type
        - imo_com
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  state_management:
    url: ${git_url}/zos/state_management.git
    branch: develop
    dependencies: [execution_management, boost, libzmq, protobuf, protobuf_host, imosar_lib, sm_dpt_lib]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  execution_management:
    url: ${git_url}/zos/execution_management.git
    branch: develop
    dependencies: [boost, rapidjson, gtest, libcgroup, libzmq, imosar_lib, openssl]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  platform_health_management:
    url: ${git_url}/zos/platform_health_management.git
    branch: develop
    dependencies: [basic_types, execution_management, state_management]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  algorithm_base:
    url: ${git_url}/zos/algorithm_base.git
    branch: develop
    dependencies: [basic_types, eigen]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  swarch_config_zos:
    url: null
    path: swarch_config/ZOS
    dependencies: [protobuf_host, protobuf, fmt, swarch_framework, imoapp_bin, swarch_config, zos_msg, zos_osal, imo_com]
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  j3_gw:
    url: null
    path: com_gateway/j3_gw
    dependencies: [swarch_config_zos, com_gateway, libzmq]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
        - --app
        - j3_gw
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  ipc:
    path: com_gateway/ipc
    dependencies: [swarch_config_zos, com_gateway]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_RPATH="\$ORIGIN/../lib:\$ORIGIN/../3rdparty/lib"
          - -DIMOBUILDER_PRODUCT=${IMOBUILDER_PRODUCT}
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - -DBUILD_SHARED_LIBS=ON
          - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
          - ${PROJECT_SOURCE_DIR}
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
        - --app
        - Ipc
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  hmi_socket:
    path: com_gateway/hmi_socket
    dependencies: [swarch_config_zos, com_gateway, nlohmann_json]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
        - --app
        - hmi_socket
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  vsomeip_base:
    path: com_gateway/vsomeip_base
    dependencies: [swarch_config_zos, com_gateway, vsomeip,vfc]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
        - --app
        - vsomeip_base
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  com_gateway:
    url: ${git_url}/inf_sw_arch/com_gateway.git
    branch: develop
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []
    dependencies: [zos_osal, zos_msg]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DPRODUCT_CODE=
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - -DBUILD_SHARED_LIBS=ON
          - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
          - ${PROJECT_SOURCE_DIR}

  sensor_manager_app:
    url: ${git_url}/zos/sensor_manager_app.git
    branch: develop
    dependencies: [basic_types,eigen,openssl,swarch_framework, swarch_config_zos, zos_osal, zos_msg, protobuf_host, protobuf, algorithm_base, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
        - --app
        - sensor_manager_app
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  system_manager_app:
    url: ${git_url}/zos/system_manager_app.git
    branch: develop
    dependencies: [swarch_config_zos, jsoncpp,execution_management, otalib]
    commands:
        patch:
          command: imoapp
          arguments:
          - generate
          - --project_dir
          - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
          - --app
          - system_manager_app
          - --bus_type
          - imo_com
          - --target_type
          - executable
          - --output
          - ${PROJECT_SOURCE_DIR}
          - --yes
          retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  otalib:
    url: ${git_url}/zos/otalib.git
    branch: develop
    dependencies: [rapidjson, protobuf, protobuf_host, leveldb, backward, openssl, libzmq, imosar_lib]
    commands:
      configure:
        arguments:
        - -DTLS_SUPPORT=NOTLS
        - -DTESTER_PRIORITY_SUPPORT=PRIO
        - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
        - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
 
  sensorlib:
    url: ${git_url}/zos/sensorlib.git
    branch: develop          
    dependencies: [eigen,openssl]
    commands:
      configure:
        arguments:
        - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
        - ${PROJECT_SOURCE_DIR}

  zos_hal:
    url: ${git_url}/zos/zos_hal.git
    branch: develop
    dependencies: []
    commands:
      configure:
        arguments:
        - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
        - ${PROJECT_SOURCE_DIR}

  ota_app:
    url: ${git_url}/zos/ota_app.git
    branch: develop
    dependencies: [protobuf, protobuf_host, version_did, libzmq, otalib, nlohmann_json, cjson, jsoncpp]
    commands:
      configure:
        arguments:
        - -DZX_DOIP_UPDATE=ON
        - -DZX_SOMEIP_UPDATE=OFF
        - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
        - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  BSW_ARCH:
    url: null
    branch: null
    dependencies:
      - swarch_config_zos
      - version_did
      - imo_com_tools
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  BSW:
    url: null
    branch: null
    dependencies:
    - arch_pattern: "x86_64-linux-ubuntu-*"
      dependencies:
      - BSW_ARCH
      - imo_com_examples
      - ipc
      - j3_gw
      - hmi_socket
    - arch_pattern: "*"
      dependencies:
      - BSW_ARCH
      - imo_com_examples
      - ipc
      - j3_gw
      - hmi_socket
      - sensor_manager_app
      - system_manager_app
      - ota_app
      - state_management
      - execution_management
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  BSW_DEMO:
    url: null
    branch: null
    dependencies:
    - BSW_ARCH
    - imo_com_examples
    - Inter5NoConvertDemo_ImoCom
    - Inter6NoConvertDemo_ImoCom
    - Inter5NoConvertHeroDemo_ImoCom
    - Inter6NoConvertWithCallbackDemo_ImoCom
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""