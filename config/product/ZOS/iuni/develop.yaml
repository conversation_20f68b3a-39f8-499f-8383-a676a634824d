base:
  - ZOS/bsw/develop

projects:
  iuni:
    url: ${git_url}/zos/iuni_app.git
    dependencies: [swarch_config_zos]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
        - --app
        - iuni_app
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Iuni:
    url: null
    branch: null
    dependencies: 
      - BSW_ARCH
      - iuni
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""