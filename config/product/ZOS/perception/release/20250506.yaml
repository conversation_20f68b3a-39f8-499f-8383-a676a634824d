base:
  - ZOS/bsw/release/20250506

projects:
  # base
  perception_base:
    url: ${git_url}/per/perception_base.git
    branch: develop
    dependencies: [eigen, imo_com, swarch_framework, zos_log, gtest]

  perception_common:
    url: ${git_url}/per/perception_common.git
    branch: develop
    dependencies: [perception_base, zos_osal]

  # common
  perception_lib:
    url: ${git_url}/per/perception_lib.git
    branch: develop_chery_pf1
    dependencies: [perception_base, boost, yaml_cpp]

  perception_common_onboard:
    url: ${git_url}/per/perception_common_onboard.git
    branch: develop_chery_pf1
    dependencies: [perception_common, perception_lib, zos_msg]

  # preprocess
  perception_preprocess:
    url: ${git_url}/per/perception_preprocess.git
    branch: develop/j6
    dependencies: [perception_common, perception_lib, cjson, zos_msg]

  perception_preprocess_onboard:
    url: ${git_url}/per/perception_preprocess_onboard.git
    branch: develop/j6
    dependencies:
      - perception_preprocess
      - perception_common_onboard
      - boost
      - jsoncpp
      - glog
      - gtest
      - yaml_cpp
      - zos_msg

  # calibration
  perception_calibration:
    url: ${git_url}/per/perception_calibration.git
    branch: develop
    dependencies:
      - perception_base
      - perception_lib
      - ceres_solver
      - cjson
      - eigen
      - jsoncpp
      - glog
      - opencv
      - pcl
      - vfc
      - yaml_cpp
      - basic_types

  perception_calibration_onboard:
    url: ${git_url}/per/perception_calibration_onboard.git
    branch: develop
    dependencies: [perception_calibration, zos_msg]

  # detection
  perception_inference:
    url: ${git_url}/per/perception_inference.git
    branch: develop/j6
    dependencies:
      [perception_base, boost, cjson, eigen, jsoncpp, glog, gtest, opencv, mnn]

  perception_detection:
    url: ${git_url}/per/perception_detection.git
    branch: develop/j6
    dependencies: [perception_inference]

  perception_detection_onboard:
    url: ${git_url}/per/perception_detection_onboard.git
    branch: develop/j6
    dependencies: [perception_detection, zos_msg]

  # fusion
  perception_fusion:
    url: ${git_url}/per/perception_fusion.git
    branch: develop_chery_pf1
    dependencies: [perception_base, perception_common, perception_lib, opencv]

  perception_fusion_onboard:
    url: ${git_url}/per/perception_fusion_onboard.git
    branch: develop_chery_pf1
    dependencies: [perception_fusion, perception_common_onboard]

  # app
  perception_app:
    url: ${git_url}/per/perception_app.git
    branch: product/ZOS/develop
    dependencies:
          - perception_calibration
          - perception_preprocess_onboard
          - perception_detection_onboard
          - perception_fusion_onboard
          - swarch_config
          - imo_com
          - imoapp_bin
    commands:
      patch:
        command: imoapp
        arguments:
          - generate
          - --project_dir
          - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
          - --app
          - perception_app
          - --bus_type
          - imo_com
          - --target_type
          - executable
          - --output
          - ${PROJECT_SOURCE_DIR}
          - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Perception:
    dependencies:
      - BSW_ARCH
      - perception_app
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""
