base:
  - ZOS/perception/develop

projects:
  # base
  perception_base:
    branch: release/chery_pf1-v0.1.0
  perception_common:
    branch: release/chery_pf1-v0.1.0
  # commmon
  perception_lib:
    branch: release/chery_pf1-v0.1.0
  perception_common_onboard:
    branch: release/chery_pf1-v0.1.0
  # preprocess
  perception_preprocess:
    branch: release/chery_pf1-v0.1.0
  perception_preprocess_onboard:
    branch: release/chery_pf1-v0.1.0
  # calibration
  perception_calibration:
    branch: release/chery_pf1-v0.1.0
  perception_calibration_onboard:
    branch: release/chery_pf1-v0.1.0
  # detection
  perception_inference:
    branch: release/chery_pf1-v0.1.0
  perception_detection:
    branch: release/chery_pf1-v0.1.0
  perception_detection_onboard:
    branch: release/chery_pf1-v0.1.0
  # fusion
  perception_fusion:
    branch: release/chery_pf1-v0.1.0
  perception_fusion_onboard:
    branch: release/chery_pf1-v0.1.0
  # app
  perception_app:
    branch: release/chery_pf1-v0.1.0
