base:
  - ZOS/bsw/develop

projects:
  # base
  perception_base:
    url: ${git_url}/per/perception_base.git
    branch: develop
    dependencies: [eigen, imo_com, swarch_framework, zos_log, gtest]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  perception_lib:
    url: ${git_url}/per/perception_lib.git
    branch: develop_chery_pf1
    dependencies: [perception_base, boost, yaml_cpp]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  # common
  perception_common:
    url: ${git_url}/per/perception_common.git
    branch: develop
    dependencies: [perception_lib, zos_osal]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  perception_common_onboard:
    url: ${git_url}/per/perception_common_onboard.git
    branch: develop_chery_pf1
    dependencies: [perception_common, zos_msg]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  # preprocess
  perception_preprocess:
    url: ${git_url}/per/perception_preprocess.git
    branch: develop/j6
    dependencies: [perception_common, cjson, zos_msg, opencv]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  perception_preprocess_onboard:
    url: ${git_url}/per/perception_preprocess_onboard.git
    branch: develop/j6
    dependencies:
      - perception_preprocess
      - perception_common_onboard
      - glog
      - jsoncpp
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  # calibration
  perception_calibration:
    url: ${git_url}/per/perception_calibration.git
    branch: develop
    dependencies:
      - perception_base
      - perception_lib
      - basic_types
      - ceres_solver
      - cjson
      - glog
      - jsoncpp
      - opencv
      - pcl
      - vfc
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  perception_calibration_onboard:
    url: ${git_url}/per/perception_calibration_onboard.git
    branch: develop
    dependencies: [perception_calibration, zos_msg]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  # detection
  perception_inference:
    url: ${git_url}/per/perception_inference.git
    branch: develop/j6
    dependencies:
      - perception_base
      - boost
      - cjson
      - glog
      - half
      - jsoncpp
      - mnn
      - opencl_headers
      - opencv
      - rapidxml
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  perception_detection:
    url: ${git_url}/per/perception_detection.git
    branch: develop/j6
    dependencies: [perception_inference]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  perception_detection_onboard:
    url: ${git_url}/per/perception_detection_onboard.git
    branch: develop/j6
    dependencies: [perception_detection, zos_msg]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  # fusion
  perception_fusion:
    url: ${git_url}/per/perception_fusion.git
    branch: develop_chery_pf1
    dependencies: [perception_common, opencv, zos_msg]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  perception_fusion_onboard:
    url: ${git_url}/per/perception_fusion_onboard.git
    branch: develop_chery_pf1
    dependencies: [perception_fusion, perception_common_onboard]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  # app
  perception_app:
    url: ${git_url}/per/perception_app.git
    branch: product/ZOS/develop
    dependencies:
      - perception_calibration
      - perception_preprocess_onboard
      - perception_detection_onboard
      - perception_fusion_onboard
      - swarch_config
      - imo_com
      - imoapp_bin
    commands:
      patch:
        command: imoapp
        arguments:
          - generate
          - --project_dir
          - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
          - --app
          - perception_app
          - --bus_type
          - imo_com
          - --target_type
          - executable
          - --output
          - ${PROJECT_SOURCE_DIR}
          - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Perception:
    dependencies:
      - BSW_ARCH
      - perception_app
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""
