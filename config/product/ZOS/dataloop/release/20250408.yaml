base:
  - ZOS/bsw/release/20250408

projects:
  datasim_app:
    url: ${git_url}/zos/datasim_app.git
    branch: develop
    dependencies: [imo_com, dalo_protocol, nlohmann_json]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  dataloop_app:
    url: ${git_url}/zos/dataloop_app.git
    branch: develop
    dependencies: [imo_com, mcap_builder, dalo_protocol, mcap_sdk, nlohmann_json]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - -DCMAKE_INSTALL_RPATH="\$ORIGIN/../lib:\$ORIGIN/../3rdparty/lib"
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  dataloop_base:
    url: ${git_url}/infdalo/dataloop_base.git
    branch: develop
    dependencies: 
    - arch_pattern: "*"
      dependencies: [nlohmann_json, gtest]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  dalo_protocol:
    url: ${git_url}/infdalo/dalo_protocol.git
    branch: develop
    dependencies: 
    - arch_pattern: "*"
      dependencies: []
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  xmt2:
    url: ${git_url}/infdalo/xmt2.git
    branch: develop
    dependencies: 
    - arch_pattern: "*"
      dependencies: [nlohmann_json, qt5, dataloop_base, websocketpp, swarch_framework, mcap_sdk, dalo_protocol, foxglove_studio, vfc]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  xmt2_plugins:
    url: ${git_url}/infdalo/xmt2_plugins.git
    branch: develop
    dependencies: 
    - arch_pattern: "*"
      dependencies: [nlohmann_json,dataloop_base]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  mcap_sdk:
    url: ${git_url}/infdalo/mcap_sdk.git
    branch: develop
    dependencies: [mcap_builder]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  dalo_protocol_server_example:
    url: ${git_url}/infdalo/dalo_protocol_server_example.git
    branch: develop
    dependencies: [dalo_protocol]
    commands:
      install:
        command: ""

  DataLoop:
    url: null
    branch: null
    dependencies: 
      - BSW_ARCH
      - datasim_app
      - dataloop_app
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""
