base:
  - ZOS/bsw/release/20250506

projects:
  park_app:
    url: ${git_url}/zos/park_app.git
    dependencies: [swarch_config_zos]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
        - --app
        - park_app
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  parking_planning_app:
    url: ${git_url}/zos/parking_planning_app.git
    dependencies: [swarch_config_zos, opencv, eigen, boost]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
        - --app
        - parking_planning
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  park_control_app:
    url: ${git_url}/zos/park_control_app.git
    dependencies: [swarch_config_zos]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
        - --app
        - park_control
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  pma_pf:
    url: ${git_url}/idc2/idc_pma_pf.git
    branch: feature/parkdr_base
    dependencies: [zos_msg,eigen,boost]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
  
  park_dr:
    url: ${git_url}/zos/park_dr.git
    branch: develop
    dependencies: [swarch_config_zos, eigen, boost, pma_pf]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
        - --app
        - park_dr
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_SOURCE_DIR}/install_manifest.txt
      files: []
      directories: []


  Parking:
    url: null
    branch: null
    dependencies: 
      - BSW_ARCH
      - park_control_app
      - parking_planning_app
      - park_app
      - park_dr
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""