base:
  - ZOS/bsw/release/20250506

projects:
  driving_planning_base:
    url: ${git_url}/dev/driving_planning_base.git
    branch: develop
    dependencies: [protobuf_host, protobuf, abseil_cpp, boost, glog, 
                   gflags, osqp, eigen, yaml_cpp, jsoncpp, lbfgspp, cereal, zos_log]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  driving_control_base:
    url: ${git_url}/dev/driving_control_base.git
    branch: develop
    dependencies: [zos_msg,vfc,ffc,eigen,nlohmann_json,zos_scripts,basic_types,algorithm_base,qpoases_e]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  driving_assist_base:
    url: ${git_url}/dev/driving_assist_base.git
    branch: develop
    dependencies: [zos_msg,vfc,ffc,n<PERSON><PERSON>_json,swarch_config_zos]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  driving_corner_radar_base:
    url: ${git_url}/dev/driving_corner_radar_base.git
    branch: develop
    dependencies: [swarch_config_zos]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  activesafety_evaluator_base:
    url: ${git_url}/dev/activesafety_evaluator_base.git
    branch: develop
    dependencies: [swarch_config_zos]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  activesafety_evaluator_app:
    url: ${git_url}/zos/activesafety_evaluator_app.git
    branch: develop
    dependencies: [activesafety_evaluator_base, swarch_config_zos]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
        - --app
        - AsEvaluatorApp
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  driving_planning_app:
    url: ${git_url}/zos/driving_planning_app.git
    branch: develop
    dependencies: [driving_planning_base, swarch_config_zos, zos_log]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
        - --app
        - DrivingPlanning
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  driving_control_app:
    url: ${git_url}/zos/driving_control_app.git
    branch: develop
    dependencies: [driving_control_base, swarch_config_zos]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
        - --app
        - DrivingControl
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  driving_assist_func:
    url: ${git_url}/zos/driving_assist_func.git
    branch: develop
    dependencies: [driving_assist_base, driving_corner_radar_base, swarch_config_zos]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
        - --app
        - DrivingAssist
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  driving_apps:
    url: ${git_url}/zos/driving_apps.git
    branch: develop
    dependencies: [swarch_config_zos, zos_log]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/ZOS
        - --app
        - DrivingApps
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Driving:
    url: null
    branch: null
    dependencies: 
    - arch_pattern: "*"
      dependencies:
      - BSW_ARCH
      - driving_apps
      - driving_control_app
      - driving_planning_app
      - activesafety_evaluator_app
      - driving_assist_func
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""
