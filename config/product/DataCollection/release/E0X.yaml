base:
- ZOS/release/20250408

package:
  runtime:
    manifest: null
    files:
    - ${IMOBUILDER_TARGET_OUTPUT}/imo_mdw_init.bash
    - ${IMOBUILDER_TARGET_OUTPUT}/run_asan_lsan_tsan_ubsan.sh
    - ${IMOBUILDER_TARGET_OUTPUT}/run_gtest.sh
    - ${IMOBUILDER_TARGET_OUTPUT}/target_start.sh
    directories:
    - ${IMOBUILDER_TARGET_OUTPUT}/bin
    - ${IMOBUILDER_TARGET_OUTPUT}/lib
    - ${IMOBUILDER_TARGET_OUTPUT}/config
    - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/bin
    - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib
    - ${IMOBUILDER_TARGET_OUTPUT}/etc
    - ${IMOBUILDER_TARGET_OUTPUT}/scripts

projects:
  ZOS_MCU:
    url: null
    branch: null
    dependencies:
      - MCU
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  ZOS_BSP:
    url: null
    branch: null
    dependencies:
      - hobot_j6e_v1_0_0_pack
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  ZOS:
    url: null
    branch: null
    dependencies:
      - BSW
      - Driving
      - Parking
      - Iuni
      - Perception
      - Prediction
      - LocalizationMapping
      - DataLoop
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  hobot_j6e_v1_0_0_pack:
    var:
      debug_lunch_args: "j6m_debug_datacollection_defconfig"
      coverage_lunch_args: "j6m_debug_datacollection_defconfig"
      release_lunch_args: "j6m_release_datacollection_defconfig"
      relwithdebinfo_lunch_args: "j6m_release_datacollection_defconfig"
      minsizerel_lunch_args: "j6m_release_datacollection_defconfig"
    branch: release/Data_Collection_E0X

  hbre:
    branch: release/Data_Collection_E0X

  kernel:
    branch: release/Data_Collection_E0X

  uboot:
    branch: release/Data_Collection_E0X

  hobot_drivers:
    branch: release/Data_Collection_E0X
  
  basic_types:
    branch: release/Data_Collection_E0X
  
  version_did:
    branch: release/Data_Collection_E0X

  zos_hal:
    branch: release/Data_Collection_E0X

  otalib:
    branch: release/Data_Collection_E0X

  hobot_j6e_mcu:
    branch: release/Data_Collection_E0X

  idc_rte_mcu:
    branch: release/Data_Collection_E0X

  idc_interfaces_mcu:
    branch: release/Data_Collection_E0X
  
  idc_infrastructure_mcu:
    branch: release/Data_Collection_E0X

  mcu_app:
    branch: release/Data_Collection_E0X

  d055_system_manager_app:
    url: ${git_url}/d055/d055_system_manager_app.git
    branch: release/Data_Collection_E0X
    dependencies: [jsoncpp, otalib, zos_hal]
    commands:
      configure:
        arguments:
        - -DZX_CUSTOMER_PROJECT=cpj_chery
        - -DZX_VEH_VARIANT=icar05
        - -DZX_PROJECT_NO=d055
        - -DZX_ETH_MONITOR=ON
        - -DZX_TIME_SYNC=ON
        - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
        - ${PROJECT_SOURCE_DIR}