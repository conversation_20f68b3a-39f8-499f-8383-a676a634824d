base:
- Infra/base/cmake_project

projects:
  abseil_cpp:
    url: ${git_url}/thdpty/abseil-cpp.git
    branch: lts_2025_01_27
    commands:
      configure:
        arguments:
          - -DBUILD_SHARED_LIBS=ON
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - -DCMAKE_CXX_STANDARD=17
          - -DCMAKE_CXX_STANDARD_REQUIRED=ON
          - -DABSL_PROPAGATE_CXX_STD=ON
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  apriltag:
    url: ${git_url}/thdpty/apriltag.git
    branch: v3.4.3
    dependencies: []
    commands:
      configure:
        arguments:
          - -<PERSON><PERSON><PERSON>_SHARED_LIBS=ON
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - -DCMAKE_CXX_STANDARD=14
          - -DBUILD_EXAMPLES=OFF
          - -DBUILD_PYTHON_WRAPPER=OFF
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  backward:
    url: ${git_url}/thdpty/backward-cpp.git
    branch: v1.6
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBACKWARD_SHARED=ON
          - -DBACKWARD_TESTS=OFF
          - -DREGISTER_INSTALL_PREFIX=OFF
          - -DCMAKE_EXPORT_NO_PACKAGE_REGISTRY=ON
          - -DCMAKE_CXX_FLAGS="${CMAKE_CXX_FLAGS} -Wno-pedantic" # [fix compile error]
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  boost:
    url: ${git_url}/thdpty/boost.git
    branch: 1.83.0
    dependencies:
    - arch_pattern: "arm*"
      dependencies: []
    - arch_pattern: "*"
      dependencies: [zlib, lz4, zstd]
    commands:
      clean_binary:
        command: '[ -d "${PROJECT_SOURCE_DIR}/.git" ]'
        arguments:
        - " && git clean -xfd && git reset --hard && cmake -E remove_directory ${PROJECT_BINARY_DIR}"
        working_directory: ${PROJECT_SOURCE_DIR}
        retcode: []
      configure:
        env:
          CC: null
          CXX: null
          CFLAGS: null
          CXXFLAGS: null
        command: ./bootstrap.sh
        arguments:
        - arch_pattern: "native"
          arguments:
          - --with-libraries=atomic,serialization,system,filesystem,thread,program_options,date_time,timer,chrono,regex,iostreams,log
          - --prefix=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
        - arch_pattern: "*qnx*"
          arguments:
          - --with-libraries=atomic,serialization,system,filesystem,thread,program_options,date_time,timer,chrono,regex,iostreams,log
          - --prefix=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
        - arch_pattern: "*"
          arguments:
          - --with-libraries=atomic,serialization,system,filesystem,thread,program_options,date_time,timer,chrono,regex,iostreams,log
          - --prefix=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - "&&"
          - sed
          - -i
          - "\"s/using gcc/using gcc : cross : ${TOOLCHAIN_PREFIX}g++/g\""
          - project-config.jam
        working_directory: ${PROJECT_SOURCE_DIR}
      build:
        command: ./b2
        arguments:
        - arch_pattern: "*qnxnto*"
          arguments:
          - toolset=qcc
          - target-os=qnxnto
          - architecture=arm
          - address-model=64
          - abi=aapcs
          - threading=multi
          - cxxflags="-Vgcc_ntoaarch64le_cxx -DQNX_SOURCE"
          - linkflags="-Vgcc_ntoaarch64le_cxx"
          - link=shared
          - install
          - -sNO_COMPRESSION=1
        - arch_pattern: "native"
          arguments:
          - link=shared
          - install
          - -sNO_COMPRESSION=1
        - arch_pattern: "*"
          arguments:
          - toolset=gcc-cross
          - cxxflags="--sysroot=${SYSROOT}"
          - linkflags="--sysroot=${SYSROOT}"
          - link=shared
          - install
          - -sNO_COMPRESSION=1
        parallel_arg: '-j'
        working_directory: ${PROJECT_SOURCE_DIR}
      install:
        command: ""
    package:
      manifest: null
      files:
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libboost_*
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/cmake/boost_*
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/cmake/Boost*
      directories:
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/boost

  cereal:
    url: ${git_url}/thdpty/cereal.git
    branch: v1.3.2
    dependencies: [boost]
    commands:
      configure:
        arguments:
          - -DBUILD_SHARED_LIBS=ON
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - -DTHREAD_SAFE=ON
          - -DBUILD_DOC=OFF
          - -DWITH_WERROR=OFF
          - -DBUILD_TESTS=OFF
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  ceres_solver:
    url: ${git_url}/thdpty/ceres-solver.git
    branch: 2.1.0
    path: ceres-solver
    dependencies: [eigen, glog]
      #patch: [patch/fix_FindSuiteSparse, patch/no-commit-hook]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_EXAMPLES=OFF
          - -DBUILD_TESTING=OFF
          - -DBUILD_SHARED_LIBS=ON
          - -DEXPORT_BUILD_DIR=OFF
          - -DBUILD_DOCUMENTAGION=OFF
          - -DCMAKE_EXPORT_NO_PACKAGE_REGISTRY=ON
          - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  cjson:
    url: ${git_url}/thdpty/cjson.git
    branch: v1.7.18
    dependencies: []
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  cppcheck_host:
    env:
      CC: null
      CXX: null
      RANLIB: null
      AR: null
      LD: null
      NM: null
      OBJDUMP: null
      STRIP: null
      CFLAGS: null
      CXXFLAGS: null
      PKG_CONFIG_PATH: null
      PKG_CONFIG_LIBDIR: null
      PKG_CONFIG_SYSROOT_DIR: null
    url: ${git_url}/thdpty/cppcheck.git
    branch: 2.13.x
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_HOST_OUTPUT}
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DCMAKE_EXPORT_NO_PACKAGE_REGISTRY=ON
          - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  cpython_host:
    env:
      CC: null
      CXX: null
      RANLIB: null
      AR: null
      LD: null
      NM: null
      OBJDUMP: null
      STRIP: null
      CFLAGS: null
      CXXFLAGS: null
      PKG_CONFIG_PATH: null
      PKG_CONFIG_LIBDIR: null
      PKG_CONFIG_SYSROOT_DIR: null
    url: ${git_url}/thdpty/cpython.git
    branch: v3.8.13
    commands:
      configure:
        command: ${PROJECT_SOURCE_DIR}/configure
        arguments:
        - arch_pattern: "*"
          arguments:
          - "--prefix=${IMOBUILDER_HOST_OUTPUT}"
          - "--enable-shared"
      build:
        command: make
        arguments: []
      install:
        command: make
        arguments: [install]
  
  curl:
    url: ${git_url}/thdpty/curl.git
    branch: curl-8_11_1
    dependencies: [openssl]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - -DBUILD_SHARED_LIBS=ON
          - -DBUILD_STATIC_CURL=OFF
          - -DBUILD_STATIC_LIBS=OFF
          - -DBUILD_TESTING=OFF
          - -DCURL_ENABLE_SSL=ON
          - -DBUILD_CURL_EXE=OFF
          - -DBUILD_EXAMPLES=OFF
          - -DBUILD_LIBCURL_DOCS=OFF
          - -DBUILD_MISC_DOCS=OFF
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  easy_profiler:
    url: ${git_url}/thdpty/easy_profiler.git
    branch: v2.1.0
    commands:
      configure:
        arguments:
          - -DBUILD_SHARED_LIBS=ON
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  eigen:
    url: ${git_url}/thdpty/eigen.git
    branch: 3.3.7
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DEIGEN_TEST_NO_OPENGL=1
          - -DBUILD_TESTING=OFF
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  ffmpeg:
    url: ${git_url}/thdpty/ffmpeg.git
    branch: release/3.4
    dependencies: [x264, x265]
    env:
      PKG_CONFIG_SYSROOT_DIR: null
    commands:
      configure:
        signatures: ["${PROJECT_BINARY_DIR}/ffbuild/config.log"]
        command: ${PROJECT_SOURCE_DIR}/configure
        arguments:
        - arch_pattern: "native"
          arguments:
          - --prefix=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - --enable-shared
          - --disable-static
          - --disable-x86asm
          - --disable-programs
          - --disable-doc
          - --enable-libx264
          - --enable-libx265
          - --enable-gpl
          - --pkg-config-flags=--define-prefix
        - arch_pattern: "*qnx*"
          arguments:
          - --prefix=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - --enable-shared
          - --enable-cross-compile
          - --arch=${ARCH}
          - --target-os=qnx
          - --enable-shared
          - --disable-x86asm
          - --disable-programs
          - --disable-doc
          - --disable-static
          - --enable-pic
          - --enable-libx264
          - --enable-libx265
          - --enable-gpl
          - --cc="${CC}"
          - --cxx="${CXX}"
          - --strip="${STRIP}"
          - --pkg-config=pkg-config
          - --pkg-config-flags=--define-prefix
        - arch_pattern: "*"
          arguments:
          - --prefix=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - --enable-cross-compile
          - --cross-prefix=${TOOLCHAIN_PREFIX}
          - --sysroot=${SYSROOT}
          - --arch=${ARCH}
          - --target-os=linux
          - --enable-shared
          - --disable-x86asm
          - --disable-programs
          - --disable-doc
          - --disable-static
          - --enable-pic
          - --enable-libx264
          - --enable-libx265
          - --enable-gpl
          - --pkg-config=pkg-config
          - --pkg-config-flags=--define-prefix
          - --cc="${CC}"
          - --cxx="${CXX}"
      build:
        command: make
        arguments: []
      install:
        command: make
        arguments: [install]
    package:
      manifest: null
      files:
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libavcodec*
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libavdevice*
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libavfilter*
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libavformat*
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libavutil*
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libswresample*
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libswscale*
      directories:
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/libavcodec
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/libavdevice
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/libavfilter
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/libavformat
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/libavutil
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/libswresample
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/libswscale

  flann:
    url: ${git_url}/thdpty/flann.git
    branch: "1.9.2"
    dependencies: [lz4]
    env:
      CFLAGS: "-I${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include ${CFLAGS}"
      CXXFLAGS: "-I${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include ${CXXFLAGS}"
      LDFLAGS: "-L${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib ${LDFLAGS}"
    commands:
      configure:
        arguments:
        - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
        - -DBUILD_SHARED_LIBS=ON
        - -DCMAKE_BUILD_TYPE=RELEASE
        - -DBUILD_DOC=OFF
        - -DBUILD_EXAMPLES=OFF
        - -DBUILD_TESTS=OFF
        - -DBUILD_PYTHON_BINDINGS=OFF
        - -DBUILD_MATLAB_BINDINGS=OFF
        - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  fmt:
    url: ${git_url}/thdpty/fmt.git
    branch: 9.0.0
    commands:
      configure:
        arguments:
        - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
        - -DFMT_DOC=OFF
        - -DFMT_TEST=OFF
        - -DBUILD_SHARED_LIBS=ON
        - -DCMAKE_BUILD_TYPE=RELEASE
        - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  foxglove_studio:
    url: ${git_url}/thdpty/foxglove_studio.git
    branch: release/x86_64-linux/2.17.0
    commands:
      configure:
        command: ""
      build:
        command: "cmake -E"
        arguments:
        - make_directory
        - "${IMOBUILDER_TARGET_OUTPUT}/foxglove-studio"
      install:
        command: dpkg
        arguments:
        - arch_pattern: "x86_64-linux*"
          arguments:
          - -x
          - "${PROJECT_SOURCE_DIR}/*.deb"
          - "${IMOBUILDER_TARGET_OUTPUT}/foxglove-studio"

  gtsam:
    url: ${git_url}/thdpty/gtsam.git
    branch: 4.2.0
    dependencies: [eigen, boost]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DGTSAM_BUILD_EXAMPLES_ALWAYS=OFF
          - -DGTSAM_BUILD_TESTS=OFF
          - -DGTSAM_USE_SYSTEM_EIGEN=ON
          - -DGTSAM_BUILD_WITH_MARCH_NATIVE=OFF
          - -DGTSAM_WITH_TBB=OFF
          - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  gflags:
    url: ${git_url}/thdpty/gflags.git
    branch: v2.2.2
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - -DBUILD_TESTING=OFF
          - -DREGISTER_INSTALL_PREFIX=OFF
          - -DCMAKE_EXPORT_NO_PACKAGE_REGISTRY=ON
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  glog:
    url: ${git_url}/thdpty/glog.git
    branch: v0.6.0
    dependencies: [gflags]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - -DBUILD_TESTING=OFF
          - -DCMAKE_EXPORT_NO_PACKAGE_REGISTRY=ON
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  gtest:
    url: ${git_url}/thdpty/gtest.git
    branch: v1.15.2
    dependencies: [gflags]
    commands:
      configure:
        arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - -DCMAKE_EXPORT_NO_PACKAGE_REGISTRY=ON
          - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []
  
  graphite:
    url: ${git_url}/thdpty/graphite.git
    branch: 1.3.14
    dependencies: []
    commands:
      configure:
        arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  harfbuzz:
    url: ${git_url}/thdpty/harfbuzz.git
    branch: 11.0.0
    dependencies: []
    commands:
      configure:
        arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  icu:
    url: ${git_url}/thdpty/icu.git
    branch: release-76-1
    commands:
      configure:
        command: ${PROJECT_SOURCE_DIR}/icu4c/source/configure
        arguments: 
        - arch_pattern: "*"
          arguments:
          - --host=${AUTOTOOL_HOST}
          - --prefix=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
      build:
        command: make
        arguments: []
      install:
        command: make
        arguments: [install]
    package:
      manifest: null
      files:
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libicu*
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/pkgconfig/icu-*.pc
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/icu/*
      directories:
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/unicode

  jsoncpp:
    url: ${git_url}/thdpty/jsoncpp.git
    branch: 1.9.5
    commands:
      configure:
        arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - -DJSONCPP_WITH_TESTS=OFF
          - -DBUILD_SHARED_LIBS=ON
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  json_schema_validator:
    url: ${git_url}/thdpty/json-schema-validator.git
    branch: e1cef0b58b1a89915577ca163fc491c2e84d4b34
    dependencies: [nlohmann_json]
    commands:
      configure:
        arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - -DJSON_VALIDATOR_BUILD_TESTS=OFF
          - -DJSON_VALIDATOR_BUILD_EXAMPLES=OFF
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  leveldb:
    url: ${git_url}/thdpty/leveldb.git
    branch: "1.23"
    dependencies: [gtest]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - -DLEVELDB_BUILD_TESTS=OFF
          - -DLEVELDB_BUILD_BENCHMARKS=OFF
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  libepoxy:
    url: ${git_url}/thdpty/libepoxy.git
    branch: 1.5.4
    dependencies: []
    env:
      NOCONFIGURE: "1"
    commands:
      patch:
        command: ./autogen.sh
        arguments: []
        working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        command: ${PROJECT_SOURCE_DIR}/configure
        arguments:
        - arch_pattern: "*"
          arguments:
          - --prefix=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - --host=${AUTOTOOL_HOST}
          - --enable-shared=yes
          - --enable-static=no
          - --enable-egl=no
          - --enable-glx=no
      build:
        command: make
        arguments: []
      install:
        command: make
        arguments: [install]
    package:
      manifest: null
      files:
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libepoxy*
      directories:
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/epoxy

  libjpeg:
    url: ${git_url}/thdpty/libjpeg.git
    branch: 9f
    dependencies: []
    commands:
      configure:
        command: ${PROJECT_SOURCE_DIR}/configure
        arguments:
        - arch_pattern: "*"
          arguments:
          - --prefix=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - --host=${AUTOTOOL_HOST}
          - --enable-shared=yes
          - --enable-static=no
      build:
        command: make
        arguments: []
      install:
        command: make
        arguments: [install]
    package:
      manifest: null
      files:
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/jpeglib.h
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/jerror.h
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/jmorecfg.h
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/jpeglib.h
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libjpeg*
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/bin/cjpeg
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/bin/djpeg
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/bin/jpegtran
      directories: []

  libpng:
    url: ${git_url}/thdpty/libpng.git
    branch: v1.6.46
    dependencies: [zlib]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - -DBUILD_TESTING=OFF
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - -DCMAKE_EXE_LINKER_FLAGS=-lm
          - -DCMAKE_SHARED_LINKER_FLAGS=-lm
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  lz4_host:
    env:
      CC: null
      CXX: null
      RANLIB: null
      AR: null
      LD: null
      NM: null
      OBJDUMP: null
      STRIP: null
      CFLAGS: null
      CXXFLAGS: null
      LDFLAGS: null
      PKG_CONFIG_PATH: null
      PKG_CONFIG_SYSROOT_DIR: null
    url: ${git_url}/thdpty/lz4.git
    branch: v1.9.4
    commands:
      configure:
        arguments:
        - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_HOST_OUTPUT}
        - -DCMAKE_BUILD_TYPE=RELEASE
        - ${PROJECT_SOURCE_DIR}/build/cmake
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  lz4:
    url: ${git_url}/thdpty/lz4.git
    branch: v1.9.4
    commands:
      configure:
        arguments:
        - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
        - -DCMAKE_BUILD_TYPE=RELEASE
        - ${PROJECT_SOURCE_DIR}/build/cmake
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  libzmq:
    url: ${git_url}/thdpty/libzmq.git
    branch: v4.3.4
    commands:
      configure:
        arguments:
          - -DWITH_PERF_TOOL=OFF
          - -DZMQ_BUILD_TESTS=OFF
          - -DENABLE_CPACK=OFF
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_EXPORT_NO_PACKAGE_REGISTRY=ON
          - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []
  
  log4cplus:
    url: ${git_url}/thdpty/log4cplus.git
    branch: 2.0.x
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DLOG4CPLUS_BUILD_TESTING=OFF
          - -DWITH_UNIT_TESTS=OFF
          - -DLOG4CPLUS_BUILD_LOGGINGSERVER=OFF
          - -DLOG4CPLUS_ENABLE_THREAD_POOL=OFF
          - -DLOG4CPLUS_SINGLE_THREADED=ON
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - -DBUILD_SHARED_LIBS=ON
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  lbfgspp:
    url: ${git_url}/thdpty/lbfgspp.git
    branch: v0.3.0
    dependencies: [eigen]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - -DBUILD_SHARED_LIBS=ON
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  libssh:
    url: ${git_url}/thdpty/libssh.git
    branch: stable-0.11
    dependencies: [zlib, openssl]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - -DBUILD_SHARED_LIBS=ON
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  libxml2:
    url: ${git_url}/thdpty/libxml2.git
    branch: v2.12.10
    dependencies: [zlib]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - -DBUILD_SHARED_LIBS=ON
          - -DLIBXML2_WITH_PYTHON=OFF
          - -DLIBXML2_WITH_TESTS=OFF
          - -DLIBXML2_WITH_LZMA=OFF
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  libcgroup:
    url: ${git_url}/thdpty/libcgroup.git
    branch: "release-3.1"
    patch: [bugfix/release_3.1_fix_download_gtest]
    dependencies: []
    commands:
      patch:
        command: ./bootstrap.sh
        arguments: []
        working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        command: ${PROJECT_SOURCE_DIR}/configure
        arguments:
        - arch_pattern: "*"
          arguments:
          - --prefix=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - --host=${AUTOTOOL_HOST}
          - --enable-shared=yes
          - --enable-static=no
          - --enable-tests=no
          - --enable-pam=no
          - --enable-systemd=no
          - --enable-tests=no
      build:
        command: make
        arguments: []
      install:
        command: make
        arguments: [install]
    package:
      manifest: null
      files:
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/libcgroup
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/libcgroup.h
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/pkgconfig/libcgroup.pc
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/sbin/cgconfigparser
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/sbin/cgrulesengd
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libcgroup*
      directories: []

  mcap:
    url: ${git_url}/thdpty/mcap.git
    branch: releases/cpp/v1.4.1
    dependencies: [lz4, zstd]
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  mcap_builder:
    url: ${git_url}/thdpty/mcap_builder.git
    branch: main
    patch: [patch/cmake]
    dependencies: [mcap]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - -Dmcap_SOURCE_DIR=${PROJECT_SOURCE_DIR}/../mcap
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  mnn:
    url: ${git_url}/thdpty/mnn.git
    branch: 3.1.2
    patch: [bugfix/3.1.2_support_find_package]
    commands:
      configure:
        arguments:
          - -DBUILD_SHARED_LIBS=ON
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  nasm_host:
    url: ${git_url}/thdpty/nasm.git
    branch: nasm-2.16.03
    env:
      CC: null
      CXX: null
      RANLIB: null
      AR: null
      LD: null
      NM: null
      OBJDUMP: null
      STRIP: null
      CFLAGS: null
      CXXFLAGS: null
      LDFLAGS: null
      PKG_CONFIG_PATH: null
      PKG_CONFIG_SYSROOT_DIR: null
    dependencies: []
    commands:
      patch:
        command: ./autogen.sh
        arguments: []
        working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        command: ./configure
        working_directory: ${PROJECT_SOURCE_DIR}
        arguments: []
      build:
        command: make
        working_directory: ${PROJECT_SOURCE_DIR}
        arguments: []
      install:
        command: install
        arguments:
        - -D
        - ${PROJECT_SOURCE_DIR}/nasm
        - ${IMOBUILDER_HOST_OUTPUT}/bin/nasm
  
  nlohmann_json:
    url: ${git_url}/thdpty/nlohmann_json.git
    branch: v3.9.1
    patch: [patch/nlohmann_define]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - -DJSON_BuildTests=OFF
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  opencv:
    url: ${git_url}/thdpty/opencv.git
    branch: 3.4.18
    patch:
    - arch_pattern: "*"
      patch: [cherry-pick:bugfix/fix_resource_download_3.4.18]
    dependencies: 
    - arch_pattern: "*qnx*"
      dependencies: [opencv_contrib, eigen, ffmpeg, mozjpeg, libjpeg, libpng]
    - arch_pattern: "*"
      dependencies: [opencv_contrib, eigen, ffmpeg, ceres_solver, libjpeg, libpng]
        #patch: [xfeatures2d-cache, ippicv-cache, fisheye_undistortpoints-fix]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_DOCS=OFF
          - -DBUILD_EXAMPLES=OFF
          - -DBUILD_TESTS=OFF
          - -DBUILD_PERF_TESTS=OFF
          - -DBUILD_WITH_DEBUG_INFO=OFF
          - -DBUILD_PACKAGE=OFF
          - -DWITH_CUDA=OFF
          - -DWITH_OPENCL=OFF
          - -DWITH_GTK=OFF
          - -DWITH_1394=OFF
          - -DWITH_GSTREAMER=OFF
          - -DWITH_GPHOTO2=OFF
          - -DBUILD_FAT_JAVA_LIB=OFF
          - -DENABLE_CXX11=ON
          - -DENABLE_PRECOMPILED_HEADERS=OFF
          - -DOPENCV_EXTRA_MODULES_PATH=${PROJECT_SOURCE_DIR}/../opencv_contrib/modules
          - -DCMAKE_INSTALL_PREFIX:PATH=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_EXPORT_NO_PACKAGE_REGISTRY=ON
          - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
          # following options for opencv > 3.4
          - -DBUILD_LIST=calib3d,core,features2d,flann,highgui,imgcodecs,imgproc,xfeatures2d,aruco,video,videoio,objdetect,tracking
          # following options for opencv 3.3
          #- -DBUILD_opencv_aruco=ON
          #- -DBUILD_opencv_bgsegm=OFF
          #- -DBUILD_opencv_bioinspired=OFF
          #- -DBUILD_opencv_ccalib=OFF
          #- -DBUILD_opencv_cnn_3dobj=OFF
          #- -DBUILD_opencv_contrib_world=ON
          #- -DBUILD_opencv_cvv=OFF
          #- -DBUILD_opencv_datasets=OFF
          #- -DBUILD_opencv_dnn_modern=OFF
          #- -DBUILD_opencv_dnns_easily_fooled=OFF
          #- -DBUILD_opencv_dpm=OFF
          #- -DBUILD_opencv_face=OFF
          #- -DBUILD_opencv_freetype=OFF
          #- -DBUILD_opencv_fuzzy=OFF
          #- -DBUILD_opencv_hdf=OFF
          #- -DBUILD_opencv_img_hash=OFF
          #- -DBUILD_opencv_line_descriptor=OFF
          #- -DBUILD_opencv_matlab=OFF
          #- -DBUILD_opencv_optflow=OFF
          #- -DBUILD_opencv_phase_unwrapping=OFF
          #- -DBUILD_opencv_plot=OFF
          #- -DBUILD_opencv_reg=OFF
          #- -DBUILD_opencv_rgbd=OFF
          #- -DBUILD_opencv_saliency=OFF
          #- -DBUILD_opencv_sfm=ON
          #- -DBUILD_opencv_stereo=OFF
          #- -DBUILD_opencv_structured_light=OFF
          #- -DBUILD_opencv_surface_matching=OFF
          #- -DBUILD_opencv_text=OFF
          #- -DBUILD_opencv_tracking=OFF
          #- -DBUILD_opencv_xfeatures2d=ON
          #- -DBUILD_opencv_ximgproc=OFF
          #- -DBUILD_opencv_xobjdetect=OFF
          #- -DBUILD_opencv_xphoto=OFF
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  opencv_contrib:
    url: ${git_url}/thdpty/opencv_contrib.git
    branch: 3.4.18
    patch:
    - arch_pattern: "*"
      patch: [cherry-pick:bugfix/fix_resource_download_3.4.18]
    dependencies: []
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  openssl:
    url: ${git_url}/thdpty/openssl.git
    branch: OpenSSL_1_1_1o
    dependencies: []
    commands:
      configure:
        command: ${PROJECT_SOURCE_DIR}/Configure
        arguments:
        - arch_pattern: "aarch64-linux-*"
          arguments:
          - shared
          - --prefix=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - --openssldir=${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/ssl
          - linux-aarch64
          - "&&"
          - perl
          - configdata.pm
          - -d
        - arch_pattern: "aarch64-qnxnto-*"
          arguments:
          - shared
          - --prefix=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - --openssldir=${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/ssl
          - linux-aarch64
          - "&&"
          - perl
          - configdata.pm
          - -d
        - arch_pattern: "x86_64-linux-*"
          arguments:
          - shared
          - --prefix=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - --openssldir=${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/ssl
          - linux-x86_64
          - "&&"
          - perl
          - configdata.pm
          - -d
        - arch_pattern: "*"
          arguments:
          - shared
          - --prefix=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - --openssldir=${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/ssl
          - linux-x86_64
          - "&&"
          - perl
          - configdata.pm
          - -d
      build:
        command: make
        arguments: []
      install:
        command: make
        arguments: [install]

    package:
      manifest: null
      files:
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libcrypto*
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libssl*
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/pkgconfig/openssl.pc
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/pkgconfig/libcrypto.pc
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/pkgconfig/libssl.pc
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/bin/openssl
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/bin/c_rehash
      directories:
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/engines-1.1
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/ssl
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/openssl

  opendds:
    url: ${git_url}/thdpty/opendds.git
    branch: branch-DDS-3.21
      #patch: [branch-DDS-3.21-patch]
    env:
      CC: null
      CXX: null
      RANLIB: null
      AR: null
      LD: null
      NM: null
      OBJDUMP: null
      STRIP: null
      CFLAGS: null
      CXXFLAGS: null
    commands:
      patch:
        command: printf
        arguments:
        - arch_pattern: "*qnx*"
          arguments:
          - "'#!/bin/bash -e \nq++ -Vgcc_ntoaarch64le -D_QNX_SOURCE $*'"
          - ">"
          - "cross-g++"
          - "&&"
          - "chmod"
          - "u+x"
          - "cross-g++"
          - "&&"
          - "printf"
          - "'#!/bin/bash -e \nqcc -Vgcc_ntoaarch64le -D_QNX_SOURCE $*'"
          - ">"
          - "cross-gcc"
          - "&&"
          - "chmod"
          - "u+x"
          - "cross-gcc"
        - arch_pattern: "*"
          arguments:
          - "'#!/bin/bash -e \n${TOOLCHAIN_PREFIX}g++ --sysroot=${SYSROOT} $*'"
          - ">"
          - "cross-g++"
          - "&&"
          - "chmod"
          - "u+x"
          - "cross-g++"
          - "&&"
          - "printf"
          - "'#!/bin/bash -e \n${TOOLCHAIN_PREFIX}gcc --sysroot=${SYSROOT} $*'"
          - ">"
          - "cross-gcc"
          - "&&"
          - "chmod"
          - "u+x"
          - "cross-gcc"
        working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        command: ./configure
        arguments:
        - arch_pattern: "*"
          arguments:
          - --optimize
          - --no-tests
          - --std=c++14
          - --host=linux
          - --compiler=g++
          - --target=linux-cross
          - --target-compiler=${PROJECT_SOURCE_DIR}/cross-g++
        - arch_pattern: "*qnx*"
          arguments:
          - --optimize
          - --no-tests
          - --std=c++14
          - --host=linux
          - --compiler=g++
          - --target=qnx
          - --target-compiler=${PROJECT_SOURCE_DIR}/cross-g++
        working_directory: ${PROJECT_SOURCE_DIR}
      build:
        command: make
        working_directory: ${PROJECT_SOURCE_DIR}
      install:
        command: 'pushd build/host && make install INSTALL_PREFIX=${IMOBUILDER_HOST_OUTPUT} && popd && pushd build/target && make install INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty && popd'
        arguments: []
        parallel_arg: ""
        working_directory: ${PROJECT_SOURCE_DIR}
    package:
      manifest: null
      files:
        - ${IMOBUILDER_HOST_OUTPUT}/lib/libACE*
        - ${IMOBUILDER_HOST_OUTPUT}/bin/ace_gperf
        - ${IMOBUILDER_HOST_OUTPUT}/bin/opendds_idl
        - ${IMOBUILDER_HOST_OUTPUT}/bin/tao_idl
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libACE*
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libOpenDDS*
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libTAO*
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/bin/inspect
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/bin/DCPSInfoRepo
      directories:
        - ${IMOBUILDER_HOST_OUTPUT}/include/ace
        - ${IMOBUILDER_HOST_OUTPUT}/share/ace
        - ${IMOBUILDER_HOST_OUTPUT}/share/dds
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/ace
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/dds
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/FACE
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/model
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/orbsvcs
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/tao
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/share/ace
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/share/cmake/OpenDDS
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/share/dds
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/share/tao

  qdldl:
    url: ${git_url}/thdpty/qdldl.git
    branch: v0.1.7
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  osqp:
    url: ${git_url}/thdpty/osqp.git
    branch: v0.6.3
    dependencies: [qdldl]
    commands:
      clean:
        command: git
        arguments: [
          "clean -xfd", 
          "&& git reset --hard"
        ]
      patch:
        command: rm
        arguments:
        - -rf
        - ${PROJECT_SOURCE_DIR}/qdldl
        - "&&"
        - cp 
        - -r
        - ${PROJECT_SOURCE_DIR}/../qdldl/*
        - ${PROJECT_SOURCE_DIR}/lin_sys/direct/qdldl/qdldl_sources
        working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  pangolin:
    url: ${git_url}/thdpty/pangolin.git
    branch: v0.9.3
    patch: [bugfix/v0.9.3_fix_exported_cmake_config]
    dependencies: [eigen, libepoxy]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - -DBUILD_EXAMPLES=OFF
          - -DBUILD_TOOLS=OFF
          - -DBUILD_PANGOLIN_PYTHON=OFF
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  pcl:
    url: ${git_url}/thdpty/pcl.git
    branch: pcl-1.14.1
    dependencies: [boost, eigen, flann]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  pcre:
    url: ${git_url}/thdpty/pcre.git
    branch: "8.45"
    dependencies: []
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - -DREGISTER_INSTALL_PREFIX=OFF
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - -DPCRE_SUPPORT_LIBREADLINE=OFF
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  pcre2:
    url: ${git_url}/thdpty/pcre2.git
    branch: pcre2-10.45
    dependencies: []
    commands:
      patch:
        command: ./autogen.sh
        arguments: []
        working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        command: ${PROJECT_SOURCE_DIR}/configure
        arguments:
        - arch_pattern: "*"
          arguments:
          - --prefix=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - --host=${AUTOTOOL_HOST}
          - --enable-shared=yes
          - --enable-static=no
      build:
        command: make
        arguments: []
      install:
        command: make
        arguments: [install]
    package:
      manifest: null
      files:
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/pcre2.h
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/pcre2posix.h
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libpcre2-8*
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libpcre2-posix*
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/bin/pcre2-config
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/bin/pcre2grep
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/bin/pcre2test
      directories: []

  poco:
    url: ${git_url}/thdpty/poco.git
    branch: poco-1.14.0-release
    dependencies: []
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - -DCMAKE_EXPORT_NO_PACKAGE_REGISTRY=ON
          - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  protobuf_host:
    url: ${git_url}/thdpty/protobuf.git
    branch: v3.18.0
    env:
      CC: null
      CXX: null
      RANLIB: null
      AR: null
      LD: null
      NM: null
      OBJDUMP: null
      STRIP: null
      CFLAGS: null
      CXXFLAGS: null
      LDFLAGS: null
      PKG_CONFIG_PATH: null
      PKG_CONFIG_SYSROOT_DIR: null
    dependencies: [zlib_host]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -Dprotobuf_BUILD_TESTS=OFF
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_HOST_OUTPUT}
          - ${PROJECT_SOURCE_DIR}/cmake
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  protobuf:
    url: ${git_url}/thdpty/protobuf.git
    branch: v3.18.0
    dependencies: [zlib]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - -DTHREADS_PREFER_PTHREAD_FLAG=TRUE
          - -Dprotobuf_BUILD_TESTS=OFF
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - ${PROJECT_SOURCE_DIR}/cmake
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  qpoases:
    url: ${git_url}/thdpty/qpoases.git
    branch: "releases/3.2.2"
    commands:
      configure:
        arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - -DBUILD_SHARED_LIBS=ON
          - -DQPOASES_BUILD_EXAMPLES=OFF
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  qpoases_e:
    url: ${git_url}/thdpty/qpoases.git
    branch: qpOASES_e
    commands:
      clean_binary:
        command: '[ -d "${PROJECT_SOURCE_DIR}/.git" ]'
        arguments:
        - " && git clean -xfd && git reset --hard && cmake -E remove_directory ${PROJECT_BINARY_DIR}"
        working_directory: ${PROJECT_SOURCE_DIR}
        retcode: []
      configure:
        command: ""
      build:
        command: mkdir
        arguments:
          - -p
          - bin
          - "&&"
          - make
          - MAKE_STATIC_LIB=1
          - CC="${CC}"
          - CPP="${CXX}"
          - AR=${AR}
        working_directory: ${PROJECT_SOURCE_DIR}/releases/3.1.2
      install:
        command: mkdir
        arguments:
          - -p
          - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/qpOASES_e
          - "&&"
          - mkdir
          - -p
          - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib
          - "&&"
          - cmake
          - -E
          - copy
          - "bin/libqpOASES_e.*"
          - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib
          - "&&"
          - cmake
          - -E
          - copy_directory
          - include/
          - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/qpOASES_e
        working_directory: ${PROJECT_SOURCE_DIR}/releases/3.1.2
        parallel_arg: ""
    package:
      manifest: null
      files:
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libqpOASES_e*
      directories:
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/qpOASES_e

  rapidjson:
    url: ${git_url}/thdpty/rapidjson.git
    branch: v1.1.0
    commands:
      configure:
        arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - -DBUILD_SHARED_LIBS=ON
          - -DRAPIDJSON_BUILD_DOC=OFF
          - -DRAPIDJSON_BUILD_EXAMPLES=OFF
          - -DRAPIDJSON_BUILD_TESTS=OFF
          - -DRAPIDJSON_BUILD_THIRDPARTY_GTEST=OFF
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  qt5:
    url: ${git_url}/thdpty/qt5.git
    branch: '5.15.15'
    patch: [patch/5.15.15/xplatform]
    dependencies: []
      #env:
      #  CC: null
      #  CXX: null
      #  RANLIB: null
      #  AR: null
      #  LD: null
      #  NM: null
      #  OBJDUMP: null
      #  STRIP: null
      #  CFLAGS: null
      #  CXXFLAGS: null
    commands:
      #patch:
      #  command: for
      #  arguments:
      #    - 'f in `cat .gitmodules |grep path|cut -d = -f 2`; do mkdir -p $f/.git ; touch $f/.git/.gitignore; done'
      #  working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        command: '${PROJECT_SOURCE_DIR}/configure'
        arguments:
        - arch_pattern: "aarch64-linux-*"
          arguments:
          - -xplatform
          - linux-aarch64-gnu-g++
          - -sysroot
          - ${SYSROOT}
          - --enable-shared
          - --disable-static
          - -release
          - -extprefix
          - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -hostprefix
          - ${IMOBUILDER_HOST_OUTPUT}
          - -confirm-license
          - -opensource
          - -nomake
          - examples
          - -nomake
          - tests
          - -opengl
          - -fontconfig
          - es2
          - --freetype=qt
          - -skip qtdeclarative
          - -skip qtactiveqt
          - -skip qtandroidextras
          - -skip qtcanvas3d
          - -skip qtconnectivity
          - -skip qtdatavis3d
          - -skip qtdoc
          - -skip qtgamepad
          - -skip qtgraphicaleffects
          - -skip qtlocation
          - -skip qtmacextras
          - -skip qtmultimedia
          - -skip qtnetworkauth
          - -skip qtpurchasing
          - -skip qtquickcontrols
          - -skip qtquickcontrols2
          - -skip qtremoteobjects
          - -skip qtscript
          - -skip qtscxml
          - -skip qtsensors
          - -skip qtserialbus
          - -skip qtserialport
          - -skip qtspeech
          - -skip qttools
          - -skip qttranslations
          - -skip qtwebchannel
          - -skip qtwebengine
          - -skip qtwebview
          - -skip qtwinextras
          - -skip qtx11extras
          - -skip qtxmlpatterns
          - -skip qtvirtualkeyboard
          - -skip qtwebsockets
        - arch_pattern: "x86_64-linux-*"
          arguments:
          - -xplatform
          - linux-x86_64-gnu-g++
          - -sysroot
          - ${SYSROOT}
          - --enable-shared
          - --disable-static
          - -release
          - -extprefix
          - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -hostprefix
          - ${IMOBUILDER_HOST_OUTPUT}
          - -confirm-license
          - -opensource
          - -nomake
          - examples
          - -nomake
          - tests
          - -xcb
          - -fontconfig
          - -skip qtactiveqt
          - -skip qtandroidextras
          - -skip qtcanvas3d
          - -skip qtconnectivity
          - -skip qtdatavis3d
          - -skip qtdoc
          - -skip qtgamepad
          - -skip qtgraphicaleffects
          - -skip qtlocation
          - -skip qtmacextras
          - -skip qtmultimedia
          - -skip qtnetworkauth
          - -skip qtpurchasing
          - -skip qtquickcontrols
          - -skip qtquickcontrols2
          - -skip qtremoteobjects
          - -skip qtscript
          - -skip qtscxml
          - -skip qtsensors
          - -skip qtserialbus
          - -skip qtserialport
          - -skip qtspeech
          - -skip qttools
          - -skip qttranslations
          - -skip qtwayland
          - -skip qtwebchannel
          - -skip qtwebengine
          - -skip qtwebview
          - -skip qtwinextras
          - -skip qtx11extras
          - -skip qtxmlpatterns
          - -skip qtvirtualkeyboard
          - -skip qtwebsockets
        - arch_pattern: "*qnx*"
          arguments:
          - -xplatform
          - qnx-aarch64le-qcc
          - -release
          - -prefix
          - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -hostprefix
          - ${IMOBUILDER_HOST_OUTPUT}
          - -confirm-license
          - -opensource
          - -nomake
          - examples
          - -nomake
          - tests
          - -skip
          - qtmultimedia
      build:
        command: make
        arguments: []
      install:
        command: make
        arguments: [install]
    package:
      manifest: null
      files:
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libQt5*
        - ${IMOBUILDER_HOST_OUTPUT}/lib/libQt5*
        - ${IMOBUILDER_HOST_OUTPUT}/bin/qmake
        - ${IMOBUILDER_HOST_OUTPUT}/bin/rcc
        - ${IMOBUILDER_HOST_OUTPUT}/bin/moc
        - ${IMOBUILDER_HOST_OUTPUT}/bin/uic
      directories:
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/cmake/Qt5*
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/Qt*
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/plugins
        - ${IMOBUILDER_HOST_OUTPUT}/mkspecs
  
  readline:
    url: ${git_url}/thdpty/readline.git
    branch: readline-8.2
    commands:
      configure:
        command: ${PROJECT_SOURCE_DIR}/configure
        arguments: 
        - arch_pattern: "*"
          arguments:
          - --host=${AUTOTOOL_HOST}
          - --prefix=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - --enable-shared
          - --enable-static
          - --disable-curses
      build:
        command: make
        arguments: []
      install:
        command: make
        arguments: [install]

  sqlite:
    url: ${git_url}/thdpty/sqlite.git
    branch: version-3.39.2
    dependencies: [zlib]
    env:
      CFLAGS: "-I${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include ${CFLAGS}"
      CXXFLAGS: "-I${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include ${CXXFLAGS}"
      LDFLAGS: "-L${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib ${LDFLAGS}"
    commands:
      configure:
        command: ${PROJECT_SOURCE_DIR}/configure
        arguments:
        - arch_pattern: "native"
          arguments:
          - --prefix=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - --enable-shared=yes
          - --enable-static=no
          - --disable-tcl
          - --disable-readline
          - --disable-editline
        - arch_pattern: "*"
          arguments:
          - --prefix=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - --host=${AUTOTOOL_HOST}
          - --enable-shared=yes
          - --enable-static=no
          - --disable-tcl
          - --disable-readline
          - --disable-editline
      build:
        command: make
        arguments: []
      install:
        command: make
        arguments: [install]
    package:
      manifest: null
      files:
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/sqlite3.h
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/sqlite3ext.h
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libsqlite3*
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/bin/sqlite3
      directories: []

  sqlite_host:
    url: ${git_url}/thdpty/sqlite.git
    branch: version-3.39.2
    env:
      CC: null
      CXX: null
      RANLIB: null
      AR: null
      LD: null
      NM: null
      OBJDUMP: null
      STRIP: null
      CFLAGS: null
      CXXFLAGS: null
      LDFLAGS: null
      PKG_CONFIG_PATH: null
      PKG_CONFIG_SYSROOT_DIR: null
    commands:
      configure:
        command: ${PROJECT_SOURCE_DIR}/configure
        arguments:
        - arch_pattern: "*"
          arguments:
          - --prefix=${IMOBUILDER_HOST_OUTPUT}
          - --enable-shared=yes
          - --enable-static=no
          - --disable-tcl
          - --disable-readline
          - --disable-editline
      build:
        command: make
        arguments: []
      install:
        command: make
        arguments: [install]
    package:
      manifest: null
      files:
        - ${IMOBUILDER_HOST_OUTPUT}/include/sqlite3.h
        - ${IMOBUILDER_HOST_OUTPUT}/include/sqlite3ext.h
        - ${IMOBUILDER_HOST_OUTPUT}/lib/libsqlite3*
        - ${IMOBUILDER_HOST_OUTPUT}/bin/sqlite3
      directories: []

  termcap:
    url: ${git_url}/thdpty/termcap.git
    branch: 10fbafd438cc3fc6e19d94a3541e51c2b204e662
    patch:
    - arch_pattern: "*"
      patch: [bugfix/fix_expose_header_dir]
    commands:
      configure:
        command: ${PROJECT_SOURCE_DIR}/configure
        arguments: 
        - arch_pattern: "*"
          arguments:
          - --host=${AUTOTOOL_HOST}
          - --srcdir=${PROJECT_SOURCE_DIR}
          - --prefix=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
      build:
        command: make
        arguments: []
      install:
        command: make
        arguments: [install]
    package:
      manifest: null
      files:
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/termcap.h
      directories:
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libtermcap*

  tinyxml2:
    url: ${git_url}/thdpty/tinyxml2.git
    branch: "10.0.0"
    dependencies: []
    commands:
      configure:
        arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - -DBUILD_TESTING=OFF
          - -Dtinyxml2_BUILD_TESTING=OFF
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []      

  vsomeip:
    url: ${git_url}/thdpty/vsomeip.git
    branch: ********
    dependencies: [boost]
    commands:
      configure:
        arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DDEFAULT_CONFIGURATION_FOLDER=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  websocketpp:
    url: ${git_url}/thdpty/websocketpp.git
    branch: 0.8.2
    dependencies: [boost, zlib, openssl]
    commands:
      configure:
        arguments:
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_EXPORT_NO_PACKAGE_REGISTRY=ON
          - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  ws_protocol:
    url: ${git_url}/thdpty/ws-protocol.git
    branch: releases/cpp/v1.3.1
    dependencies: [websocketpp]
    commands:
      configure:
        arguments:
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_EXPORT_NO_PACKAGE_REGISTRY=ON
          - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}/cpp/foxglove-websocket
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  x264:
    url: ${git_url}/thdpty/x264.git
    branch: stable
    dependencies:
    - arch_pattern: "x86_64-linux*"
      dependencies: [nasm_host]
    - arch_pattern: "native"
      dependencies: [nasm_host]
    commands:
      configure:
        command: ${PROJECT_SOURCE_DIR}/configure
        arguments:
        - arch_pattern: native
          arguments:
          - --prefix=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - --enable-static
          - --enable-shared
          - --disable-cli
          - --enable-pic
        - arch_pattern: "*"
          arguments:
           - --prefix=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
           - --host=${AUTOTOOL_HOST}
           - --enable-static
           - --enable-shared
           - --disable-cli
           - --enable-pic
      build:
        command: make
        arguments:
        - arch_pattern: "*qnx*"
          arguments:
            - -j
            - "||"
            - "true"
            - make
      install:
        command: make
        arguments: [install]
    package:
      manifest: null
      files:
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/x264.h
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/include/x264_config.h
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libx264.a
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libx264.so
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/libx264.so.164
        - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/pkgconfig/x264.pc
      directories: []

  x265:
    url: ${git_url}/thdpty/x265.git
    branch: "3.4"
    commands:
      configure:
        arguments:
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_CXX_STANDARD=14
          - -DCMAKE_EXPORT_NO_PACKAGE_REGISTRY=ON
          - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - -DENABLE_ASSEMBLY=OFF
          - ${PROJECT_SOURCE_DIR}/source
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  xerces_c:
    url: ${git_url}/thdpty/xerces-c.git
    branch: v3.2.3
    path: xerces-c
    commands:
      configure:
        arguments:
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_CXX_STANDARD=14
          - -DCMAKE_EXPORT_NO_PACKAGE_REGISTRY=ON
          - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  yaml_cpp:
    url: ${git_url}/thdpty/yaml_cpp.git
    branch: master # NOTE: only master branch can pass compile with gcc8 or greater
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - -DYAML_CPP_BUILD_TESTS=OFF
          - -DYAML_CPP_BUILD_TOOLS=OFF
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  zlib:
    url: ${git_url}/thdpty/zlib.git
    branch: v1.2.12
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  zlib_host:
    url: ${git_url}/thdpty/zlib.git
    branch: v1.2.12
    env:
      CC: null
      CXX: null
      RANLIB: null
      AR: null
      LD: null
      NM: null
      OBJDUMP: null
      STRIP: null
      CFLAGS: null
      CXXFLAGS: null
      LDFLAGS: null
      PKG_CONFIG_PATH: null
      PKG_CONFIG_SYSROOT_DIR: null
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_HOST_OUTPUT}
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - -DBUILD_SHARED_LIBS=ON
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []
  
  zlog:
    url: ${git_url}/thdpty/zlog.git
    branch: 1.2.18
    patch: [bugfix/1.2.18_fix_cmake_install]
    commands:
      configure:
        arguments: 
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - -DUNIT_TEST=OFF
          - -DCMAKE_INSTALL_RPATH=$ORIGIN
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  zstd:
    url: ${git_url}/thdpty/zstd.git
    branch: v1.5.6
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}/3rdparty
          - -DCMAKE_BUILD_TYPE=RELEASE
          - -DBUILD_SHARED_LIBS=ON
          - ${PROJECT_SOURCE_DIR}/build/cmake
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []
