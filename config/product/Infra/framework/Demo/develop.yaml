base: Infra/framework/base/develop

projects:
  imoapp_bin:
    var:
      IMOAPP_VERSION: 0.0.22

  swarch_config_Demo:
    url: null
    path: swarch_config/Demo
    dependencies: [swarch_config, imoapp_bin]
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  swarch_codegen_demo:
    url: ${git_url}/inf_sw_arch/swarch_codegen_demo.git
    branch: develop
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  zos_scripts:
    url: ${git_url}/zos/zos_scripts.git
    branch: develop
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  zos_log:
    url: ${git_url}/zos/zos_log.git
    branch: develop
    dependencies: [zos_scripts, zlog, yaml_cpp]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - -DCMAKE_INSTALL_RPATH="\$ORIGIN/../lib:\$ORIGIN/../3rdparty/lib"
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  StateMachineDemo_Tros:
    url: null
    path: swarch_codegen_demo/Com/StateMachineDemo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - StateMachineDemo
        - --bus_type
        - tros
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  StateMachineDemo_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/StateMachineDemo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - StateMachineDemo
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  StateMachineDemo1_Tros:
    url: null
    path: swarch_codegen_demo/Com/StateMachineDemo1
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - StateMachineDemo1
        - --bus_type
        - tros
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  StateMachineDemo1_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/StateMachineDemo1
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - StateMachineDemo1
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  StatePublisherDemo_Tros:
    url: null
    path: swarch_codegen_demo/Com/StatePublisherDemo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - StatePublisherDemo
        - --bus_type
        - tros
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  StatePublisherDemo_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/StatePublisherDemo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - StatePublisherDemo
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  SenderDemo_Tros:
    url: null
    path: swarch_codegen_demo/Com/SenderDemo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - SenderDemo
        - --bus_type
        - tros
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  SenderDemo_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/SenderDemo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - SenderDemo
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  ReceiverDemo_Tros:
    url: null
    path: swarch_codegen_demo/Com/ReceiverDemo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - ReceiverDemo
        - --bus_type
        - tros
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  ReceiverDemo_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/ReceiverDemo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - ReceiverDemo
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inner1Demo_Tros:
    url: null
    path: swarch_codegen_demo/Com/Inner1Demo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inner1Demo
        - --bus_type
        - tros
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inner1Demo_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inner1Demo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inner1Demo
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter1Demo_Tros:
    url: null
    path: swarch_codegen_demo/Com/Inter1Demo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter1Demo
        - --bus_type
        - tros
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter1Demo_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter1Demo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter1Demo
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter2Demo_Tros:
    url: null
    path: swarch_codegen_demo/Com/Inter2Demo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter2Demo
        - --bus_type
        - tros
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter2Demo_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter2Demo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter2Demo
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter3Demo_Tros:
    url: null
    path: swarch_codegen_demo/Com/Inter3Demo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter3Demo
        - --bus_type
        - tros
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter3Demo_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter3Demo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter3Demo
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter3Lib_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter3Lib
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter3Lib
        - --bus_type
        - imo_com
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter3AliasLib_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter3AliasLib
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt, imo_com, Inter3Lib_ImoCom]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter3AliasLib
        - --bus_type
        - imo_com
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter3Demo_Link_Inter3Lib_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter3Demo_Link_Inter3Lib
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt, imo_com, Inter3Lib_ImoCom]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter3Demo_Link_Inter3Lib
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []
    
  Inter4Demo_Tros:
    url: null
    path: swarch_codegen_demo/Com/Inter4Demo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter4Demo
        - --bus_type
        - tros
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter4Demo_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter4Demo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter4Demo
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter5Demo_Tros:
    url: null
    path: swarch_codegen_demo/Com/Inter5Demo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter5Demo
        - --bus_type
        - tros
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter5Demo_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter5Demo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter5Demo
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []
        
  Inter5NoConvertDemo_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter5NoConvertDemo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter5NoConvertDemo
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter5NoConvertHeroDemo_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter5NoConvertHeroDemo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter5NoConvertHeroDemo
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter5DemoWithBaseComponent_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter5DemoWithBaseComponent
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter5DemoWithBaseComponent
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter5DemoWithBaseComponent_Tros:
    url: null
    path: swarch_codegen_demo/Com/Inter5DemoWithBaseComponent
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter5DemoWithBaseComponent
        - --bus_type
        - tros
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter5DemoWithExecutableModel_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter5DemoWithExecutableModel
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter5DemoWithExecutableModel
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter5DemoWithExecutableModel_Tros:
    url: null
    path: swarch_codegen_demo/Com/Inter5DemoWithExecutableModel
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter5DemoWithExecutableModel
        - --bus_type
        - tros
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter6Demo_Tros:
    url: null
    path: swarch_codegen_demo/Com/Inter6Demo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter6Demo
        - --bus_type
        - tros
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter6Demo_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter6Demo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter6Demo
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter6NoConvertDemo_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter6NoConvertDemo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter6NoConvertDemo
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter6NoConvertWithCallbackDemo_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter6NoConvertWithCallbackDemo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt, imo_com]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter6NoConvertWithCallbackDemo
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter6DemoWithBagService_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter6DemoWithBagService
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter6DemoWithBagService
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter6DemoWithBagService_Tros:
    url: null
    path: swarch_codegen_demo/Com/Inter6DemoWithBagService
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter6DemoWithBagService
        - --bus_type
        - tros
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter6DemoWithCallback_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter6DemoWithCallback
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter6DemoWithCallback
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter6DemoWithCallback_Tros:
    url: null
    path: swarch_codegen_demo/Com/Inter6DemoWithCallback
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter6DemoWithCallback
        - --bus_type
        - tros
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter6DemoWithEasyProfiler_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter6DemoWithEasyProfiler
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter6DemoWithEasyProfiler
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter6DemoWithEasyProfiler_Tros:
    url: null
    path: swarch_codegen_demo/Com/Inter6DemoWithEasyProfiler
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter6DemoWithEasyProfiler
        - --bus_type
        - tros
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter6DemoWithBaseComponent_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter6DemoWithBaseComponent
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter6DemoWithBaseComponent
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter6DemoWithBaseComponent_Tros:
    url: null
    path: swarch_codegen_demo/Com/Inter6DemoWithBaseComponent
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter6DemoWithBaseComponent
        - --bus_type
        - tros
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter6DemoWithExecutableModel_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter6DemoWithExecutableModel
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter6DemoWithExecutableModel
        - --bus_type
        - imo_com
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter6DemoWithExecutableModel_Tros:
    url: null
    path: swarch_codegen_demo/Com/Inter6DemoWithExecutableModel
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - Inter6DemoWithExecutableModel
        - --bus_type
        - tros
        - --target_type
        - shared_library
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  UssDemo_Rte:
    url: null
    path: swarch_codegen_demo/Com/UssDemo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - UssDemo
        - --bus_type
        - rte
        - --target_type
        - executable
        - --platform
        - j6e-r-core
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  SimpleArgs:
    url: null
    path: swarch_codegen_demo/Arguments/SimpleArgs
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - SimpleArgs
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  LogServiceDemo:
    url: null
    path: swarch_codegen_demo/Log/LogServiceDemo
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt, zos_log]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - LogServiceDemo
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  fileconfig_args:
    url: null
    path: swarch_codegen_demo/Arguments/FileConfigArgs
    dependencies: [swarch_framework, swarch_config_Demo, swarch_codegen_demo, protobuf_host, protobuf, fmt]
    commands:
      patch:
        command: imoapp
        arguments:
        - generate
        - --project_dir
        - ${WORKSPACE_ROOT}/src/swarch_config/Demo
        - --app
        - FileConfigArgs
        - --target_type
        - executable
        - --output
        - ${PROJECT_SOURCE_DIR}
        - --yes
        retcode: [0]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  Inter3LibApp_ImoCom:
    url: null
    path: swarch_codegen_demo/Com/Inter3LibApp
    dependencies: [Inter3Lib_ImoCom]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []
