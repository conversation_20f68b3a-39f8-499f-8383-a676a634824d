base:
  - Infra/base/cmake_project
  - Infra/thirdparty/develop

projects:
  imoapp_bin:
    url: null
    branch: null
    var:
      IMOAPP_VERSION: 0.0.18
    commands:
      configure:
        command: "cmake -E"
        arguments:
        - make_directory
        - ${PROJECT_BINARY_DIR}
        - ${IMOBUILDER_HOST_OUTPUT}/bin
      build:
        command: curl
        arguments:
        - arch_pattern: "*x86_64-linux*"
          arguments:
          - -o
          - imoapp_v${IMOAPP_VERSION}.tar.gz.cache
          - "${artifactory_url}/repository/imoapp_resource/v${IMOAPP_VERSION}/imoapp_v${IMOAPP_VERSION}_x86_64-linux.tar.gz"
          - " && cmake -E rename imoapp_v${IMOAPP_VERSION}.tar.gz.cache imoapp_v${IMOAPP_VERSION}.tar.gz "
        - arch_pattern: "armv8-r-*j6e*"
          arguments:
          - -o
          - imoapp_v${IMOAPP_VERSION}.tar.gz.cache
          - "${artifactory_url}/repository/imoapp_resource/v${IMOAPP_VERSION}/imoapp_v${IMOAPP_VERSION}_win10.tar.gz"
          - " && cmake -E rename imoapp_v${IMOAPP_VERSION}.tar.gz.cache imoapp_v${IMOAPP_VERSION}.tar.gz "
        - arch_pattern: "*"
          arguments:
          - -o
          - imoapp_v${IMOAPP_VERSION}.tar.gz.cache
          - "${artifactory_url}/repository/imoapp_resource/v${IMOAPP_VERSION}/imoapp_v${IMOAPP_VERSION}_x86_64-linux.tar.gz"
          - " && cmake -E rename imoapp_v${IMOAPP_VERSION}.tar.gz.cache imoapp_v${IMOAPP_VERSION}.tar.gz "
        parallel_arg: ""
        signatures: ["${PROJECT_BINARY_DIR}/imoapp_v${IMOAPP_VERSION}.tar.gz"]
      install:
        command: "cmake -E"
        arguments:
        - tar
        - xvf
        - ${PROJECT_BINARY_DIR}/imoapp_v${IMOAPP_VERSION}.tar.gz
        working_directory: ${IMOBUILDER_HOST_OUTPUT}/bin

  swarch_framework:
    url: ${git_url}/inf_sw_arch/swarch_framework.git
    branch: develop
    dependencies: [yaml_cpp]
    commands:
      gtest:
        command: make
        arguments: [UT_gtest]
        parallel_arg: -j
        retcode: [0]
      test:
        env:
          SOURCE_ROOT: ${PROJECT_SOURCE_DIR}
          BUILD_ROOT: ${BUILD_ROOT}
          INSTALL_ROOT: ${INSTALL_ROOT}
          LD_LIBRARY_PATH: ${TARGET_LD_LIBRARY_PATH}
        command: ctest
        arguments:
        - --output-junit ctest_result.xml
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  swarch_config:
    url: ${git_url}/inf_sw_arch/swarch_config.git
    branch: develop
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  tros_demo:
    url: ${git_url}/~jianrong.zeng/tros_demo.git
    branch: develop
    dependencies: [protobuf_host, protobuf, fmt]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - -DCMAKE_BUILD_TYPE=RELEASE
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  gtest:
    branch: release-1.8.0

  swarch_osal:
    url: ${git_url}/inf_sw_arch/swarch_osal.git
    branch: develop
    dependencies:
    - arch_pattern: "aarch64-linux-renesas*"
      dependencies: [nlohmann_json, json_schema_validator]
    commands:
      configure:
        arguments:
        - arch_pattern: "aarch64-linux-renesas*"
          arguments:
          - -DENABLE_RCAR_XOS_HERO=ON
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - ${PROJECT_SOURCE_DIR}
        - arch_pattern: "*"
          arguments:
          - -DENABLE_RCAR_XOS_HERO=OFF
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []
  imo_com:
    url: ${git_url}/inf_sw_arch/imo_com.git
    branch: develop
    dependencies:
    - arch_pattern: "arm*"
      dependencies: []
    - arch_pattern: "*"
      dependencies: [zos_log, readline, termcap, gtest, protobuf, protobuf_host, mcap_builder, basic_types]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - -DCMAKE_INSTALL_RPATH="\$ORIGIN/../lib:\$ORIGIN/../3rdparty/lib:\$ORIGIN/../../../lib:\$ORIGIN/../../../3rdparty/lib"
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  imo_com_examples:
    url: ${git_url}/inf_sw_arch/imo_com_examples.git
    branch: develop
    dependencies: [imo_com, swarch_osal]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - -DCMAKE_INSTALL_RPATH="\$ORIGIN/../lib:\$ORIGIN/../3rdparty/lib"
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  imo_com_tools:
    url: ${git_url}/inf_sw_arch/imo_com_tools.git
    branch: develop
    dependencies: [imo_com, mcap_builder]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - -DCMAKE_INSTALL_RPATH="\$ORIGIN/../lib:\$ORIGIN/../3rdparty/lib"
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []

  zos_scripts:
    url: ${git_url}/zos/zos_scripts.git
    branch: develop
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt

  zos_log:
    url: ${git_url}/zos/zos_log.git
    branch: develop
    dependencies: [zlog, yaml_cpp, zos_scripts]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - -DCMAKE_INSTALL_RPATH="\$ORIGIN/../lib:\$ORIGIN/../3rdparty/lib"
          - ${PROJECT_SOURCE_DIR}
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []
