base: null

commands:
  configure:
    command: cmake
    arguments:
    - arch_pattern: "*"
      arguments:
      - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
      - -DBUILD_SHARED_LIBS=ON
      - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
      - -DCMAKE_INSTALL_RPATH="\$ORIGIN/../lib:\$ORIGIN/../3rdparty/lib:\$ORIGIN/../../../lib:\$ORIGIN/../../../3rdparty/lib"
      - ${PROJECT_SOURCE_DIR}
    working_directory: ${PROJECT_BINARY_DIR}
    retcode: [0]
    signatures: ${PROJECT_BINARY_DIR}/Makefile
  build:
    command: "cmake --build ${PROJECT_BINARY_DIR}"
    arguments:
    - arch_pattern: "*"
      arguments: []
    working_directory: ${PROJECT_BINARY_DIR}
    retcode: [0]
    parallel_arg: "-j"
  install:
    command: "cmake --install ${PROJECT_BINARY_DIR}"
    arguments:
    - arch_pattern: "*"
      arguments: []
    working_directory: ${PROJECT_BINARY_DIR}
    retcode: [0]
  clean_binary:
    command: "cmake -E remove_directory ${PROJECT_BINARY_DIR}"
    arguments:
    - arch_pattern: "*"
      arguments: []
    working_directory: ${WORKSPACE_ROOT}
    retcode: [0]
  clean_cache:
    command: "cmake -E remove ${IMOBUILDER_OUTPUT}/download/${PROJECT_NAME}.tar.gz"
    arguments:
    - arch_pattern: "*"
      arguments: []
    working_directory: ${WORKSPACE_ROOT}
    retcode: [0]
  gtest_build:
    command: "cmake --build ${PROJECT_BINARY_DIR} --target UT_gtest"
    arguments:
    - arch_pattern: "*"
      arguments: []
    working_directory: ${PROJECT_BINARY_DIR}
    parallel_arg: "-j"
  gtest_test:
    env:
      SOURCE_ROOT: ${PROJECT_SOURCE_DIR}
      BUILD_ROOT: ${PROJECT_BINARY_DIR}
      INSTALL_ROOT: ${IMOBUILDER_OUTPUT}
      LD_LIBRARY_PATH: ${TARGET_LD_LIBRARY_PATH}
    command: ctest
    arguments:
    - arch_pattern: "*"
      arguments:
      - --output-junit ctest_result.xml
    working_directory: ${PROJECT_BINARY_DIR}
  gtest_coverage:
    command: make
    arguments:
      - coverage
    working_directory: ${PROJECT_BINARY_DIR}
  gtest_collect:
    command: "cmake -E remove_directory ${WORKSPACE_ROOT}/report/gtest/test_case/${PROJECT_NAME}"
    arguments:
    - " && cmake -E remove_directory ${WORKSPACE_ROOT}/report/gtest/coverage/${PROJECT_NAME}"
    - " && cmake -E remove_directory ${WORKSPACE_ROOT}/report/gtest/ctest/${PROJECT_NAME}"
    - " && cmake -E remove ${WORKSPACE_ROOT}/log/test/${PROJECT_NAME}_LastTest.log"
    - " && cmake -E copy_directory ${PROJECT_BINARY_DIR}/gtest ${WORKSPACE_ROOT}/report/gtest/test_case/${PROJECT_NAME}/ 2>/dev/null || echo 'Warning: ${PROJECT_BINARY_DIR}/gtest does not exist, skipping copy.'"
    - " && cmake -E copy_directory ${PROJECT_BINARY_DIR}/coverage/${PROJECT_NAME} ${WORKSPACE_ROOT}/report/gtest/coverage/${PROJECT_NAME}/ 2>/dev/null || echo 'Warning: ${PROJECT_BINARY_DIR}/coverage does not exist, skipping copy.'"
    - " && cmake -E copy ${PROJECT_BINARY_DIR}/ctest_result.xml ${WORKSPACE_ROOT}/report/gtest/ctest/${PROJECT_NAME}/ctest_result.xml 2>/dev/null || echo 'Warning: ${PROJECT_BINARY_DIR}/ctest_result.xml does not exist, skipping copy.'"
    - " && cmake -E copy ${PROJECT_BINARY_DIR}/Testing/Temporary/LastTest.log ${WORKSPACE_ROOT}/log/test/${PROJECT_NAME}_LastTest.log 2>/dev/null || echo 'Warning: ${PROJECT_BINARY_DIR}/Testing/Temporary/LastTest.log does not exist, skipping copy.'"
    working_directory: ${WORKSPACE_ROOT}
  cppcheck:
    command: mkdir
    arguments:
      - -p ${PROJECT_BINARY_DIR}/cppcheck-build-dir
      - "&&"
      - "python3 ${WORKSPACE_ROOT}/config/cppcheck/scripts/gen_cfg.py"
      - "--output"
      - ${PROJECT_BINARY_DIR}/cppcheck-build-dir/config.cfg
      - "&&"
      - cppcheck
      - --cppcheck-build-dir=${PROJECT_BINARY_DIR}/cppcheck-build-dir
      - --project=${PROJECT_BINARY_DIR}/compile_commands.json -i ${IMOBUILDER_OUTPUT}
      - --checkers-report=${PROJECT_BINARY_DIR}/cppcheck-build-dir/checkers_report.txt
      - --std=c++14
      - --platform=unix64
      - --enable=all
      - --suppress=missingIncludeSystem
      - --library=${PROJECT_BINARY_DIR}/cppcheck-build-dir/config.cfg
    working_directory: ${WORKSPACE_ROOT}

build_steps: 
- patch
- configure
- build
- install

build_complete_step: install

clean_steps:
- clean_binary
clean_complete_step: clean_binary 

test_steps:
- gtest_build
- gtest_test
test_complete_step: gtest_build

package:
  runtime:
    manifest: null
    files: []
    directories:
    - ${IMOBUILDER_TARGET_OUTPUT}/bin
    - ${IMOBUILDER_TARGET_OUTPUT}/lib
    - ${IMOBUILDER_TARGET_OUTPUT}/config
    - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/bin
    - ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib
  target:
    manifest: null
    files: []
    directories:
    - ${IMOBUILDER_TARGET_OUTPUT}
    
projects: {}
