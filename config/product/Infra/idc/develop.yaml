base: Infra/thirdparty/develop

projects:
  idc_infrastructure:
    url: ${git_url}/idc2/idc_infrastructure.git
    branch: feature/refactor_modules
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  idc_rte:
    url: ${git_url}/idc2/idc_rte.git
    branch: feature/refactor_modules
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  idc_interfaces:
    url: ${git_url}/idc2/idc_interfaces.git
    branch: feature/refactor_modules
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""
  dnn:
    url: ${git_url}/inflibs/dnn.git
    branch: develop
    dependencies: [glog]
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []
  vfc:
    url: ${git_url}/inflibs/vfc.git
    branch: develop
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
  ddk:
    url: null
    path: idc_rte/ddk
    dependencies: [idc_rte, vfc, trace, tic, os]
  dmac:
    url: null
    path: idc_rte/dmac
    dependencies: [idc_rte, vfc]
  trace:
    url: null
    path: idc_infrastructure/trace
    dependencies:
    - arch_pattern: "armv8-r*"
      dependencies: [vfc]
    - arch_pattern: "armv7-r*"
      dependencies: [vfc]
    - arch_pattern: "*"
      dependencies: [dmac, vfc]
  tic:
    url: null
    path: idc_infrastructure/tic
    dependencies: [idc_infrastructure, vfc, os, trace]
  os:
    url: null
    path: idc_infrastructure/os
    dependencies: [idc_infrastructure, trace]
  eth:
    url: null
    path: idc_infrastructure/eth
    dependencies: [idc_infrastructure, vfc]
  daddons:
    url: null
    path: idc_infrastructure/daddons
    dependencies: [idc_infrastructure, vfc, vaddons_core, ddk]
  vaddons_core:
    url: null
    path: idc_infrastructure/vaddons_core
    dependencies: [idc_infrastructure, vfc]
  imgutil:
    url: null
    path: idc_infrastructure/imgutil
    dependencies: [idc_infrastructure, vfc]
  mem:
    url: null
    path: idc_infrastructure/mem
    dependencies: [idc_infrastructure]
  r2dtwoCommon:
    url: null
    path: idc_infrastructure/r2dtwoCommon
    dependencies: [idc_infrastructure, vfc]
  cnl_core:
    url: null
    path: idc_infrastructure/cnl_libs/cnl_core
    dependencies: [idc_infrastructure, vfc, vaddons_core]
  cnl_lsq:
    url: null
    path: idc_infrastructure/cnl_libs/cnl_lsq
    dependencies: [idc_infrastructure, vfc, vaddons_core, cnl_core]
  cnl_pgo:
    url: null
    path: idc_infrastructure/cnl_libs/cnl_pgo
    dependencies: [idc_infrastructure, vfc, vaddons_core, cnl_core]
  cnl_spl:
    url: null
    path: idc_infrastructure/cnl_libs/cnl_spl
    dependencies: [idc_infrastructure, vfc, vaddons_core, cnl_core]
  cnl_trf:
    url: null
    path: idc_infrastructure/cnl_libs/cnl_trf
    dependencies: [idc_infrastructure, vfc, vaddons_core, cnl_core]
  version:
    url: null
    path: idc_infrastructure/version
    dependencies: [idc_infrastructure, vfc]
  prm:
    url: null
    path: idc_infrastructure/prm
    dependencies: [idc_infrastructure, vfc, ddk]
  zxEvm:
    url: null
    path: idc_infrastructure/zxEvm
    dependencies: [idc_infrastructure, vfc]
  dag:
    url: null
    path: idc_rte/dag
    dependencies: [idc_rte, vfc, play, tic]
  ffc:
    url: null
    path: idc_rte/ffc
    dependencies: [idc_rte, vfc]
  ipc:
    url: null
    path: idc_rte/ipc
    dependencies: [idc_rte, vfc, cpj_interface, trace, ddk, pubif_services, pubif_ipark, pubif_viper, pubif_vial, pubif_iwalle, pubif_ipilot, pubif_ego_motion, pubif_sv3d]
  play:
    url: null
    path: idc_rte/play
    dependencies: [idc_rte, vfc, cool, tic, pubif_services]
  bsv_common:
    url: null
    path: idc_rte/bsv_common
    dependencies: [idc_rte, vfc, tic, pubif_services]
  had:
    url: null
    path: idc_infrastructure/had
    dependencies: [vfc]
  cool:
    url: null
    path: idc_rte/cool
    dependencies: [ddk, had, vfc]

  pubif_iasf:
    url: null
    path: idc_interfaces/iasf
    dependencies: [idc_interfaces, ddk, vfc]
  pubif_sv3d:
    url: null
    path: idc_interfaces/sv3d
    dependencies: [idc_interfaces, ddk, vfc]
  pubif_services:
    url: null
    path: idc_interfaces/services
    dependencies: [idc_interfaces, ddk, vfc, tic]
  pubif_ego_motion:
    url: null
    path: idc_interfaces/ego_motion
    dependencies: [idc_interfaces, vfc]
  pubif_ipark:
    url: null
    path: idc_interfaces/ipark
    dependencies: [idc_interfaces, vfc]
  pubif_ipilot:
    url: null
    path: idc_interfaces/ipilot
    dependencies: [idc_interfaces, vfc]
  pubif_ipola:
    url: null
    path: idc_interfaces/ipola
    dependencies: [idc_interfaces, vfc]
  pubif_iwalle:
    url: null
    path: idc_interfaces/iwalle
    dependencies: [idc_interfaces, vfc]
  pubif_vial:
    url: null
    path: idc_interfaces/vial
    dependencies: [idc_interfaces, vfc, daddons, pubif_services]
  pubif_viper:
    url: null
    path: idc_interfaces/viper
    dependencies: [idc_interfaces, vfc, bsv_common, pubif_vial]
  cpj_interface:
    url: null
    path: idc_interfaces/cpj_chery
    dependencies: [idc_interfaces, vfc]
  ddk_demo:
    url: ${git_url}/~jianrong.zeng/ddk_demo.git
    path: ddk_demo
    dependencies: [ddk]
