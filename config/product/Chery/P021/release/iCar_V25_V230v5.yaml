base: 
  - ZOS/thirdparty/release/20250506
  - BSP/j3/release/20250509
  - Infra/framework/Demo/develop
  - Infra/idc/develop

env:
  CFLAGS: "-I${IMOBUILDER_TMP_OUTPUT}/appuser/include ${CFLAGS}"
  CXXFLAGS: "-I${IMOBUILDER_TMP_OUTPUT}/appuser/include ${CXXFLAGS}"
  LDFLAGS: "-L${IMOBUILDER_TMP_OUTPUT}/appuser/lib ${LDFLAGS}"

projects:
  hbre:
    branch: release/iCar_V25_V230v5

  kernel:
    branch: release/iCar_V25_V230v5

  perception_app:
    branch: release/iCar_V25_V230v5

  uboot:
    branch: release/iCar_V25_V230v5

  sirius_s3_mono3_3_host:
    url: ${git_url}/sdksoc/sirius_s3_mono3.3.git
    path: sirius_s3_mono3.3
    branch: release/iCar_V25_V230v5
    dependencies: []
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: "cmake -E"
        arguments:
        - copy_directory
        - ${PROJECT_SOURCE_DIR}/appuser/include
        - ${IMOBUILDER_TMP_OUTPUT}/appuser/include
        - "&&"
        - cmake
        - -E
        - copy_directory
        - ${PROJECT_SOURCE_DIR}/appuser/lib
        - ${IMOBUILDER_TMP_OUTPUT}/appuser/lib
        - "&&"
        - cmake
        - -E
        - copy_directory
        - ${PROJECT_SOURCE_DIR}/appuser/middleware/include
        - ${IMOBUILDER_TMP_OUTPUT}/appuser/include
        - "&&"
        - cmake
        - -E
        - copy_directory
        - ${PROJECT_SOURCE_DIR}/appuser/middleware/lib
        - ${IMOBUILDER_TMP_OUTPUT}/appuser/lib
    package:
      files: []
      directories: ["${IMOBUILDER_TMP_OUTPUT}/appuser"]

  p021_ota:
    url: ${git_url}/p021/p021_ota.git
    branch: release/iCar_V25_V230v5
    dependencies: [sirius_s3_mono3_3_host,hbre,protobuf,protobuf_host,libzmq]
  boost:
    branch: 1.72.0
  vsomeip:
    branch: ********
  com_gateway:
    url: ${git_url}/inf_sw_arch/com_gateway.git
    branch: release/iCar_V25_V230v5
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []
    dependencies: [vfc, vsomeip]
    commands:
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - -DPRODUCT_CODE=P021
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
          - -DBUILD_SHARED_LIBS=ON
          - -DCMAKE_POSITION_INDEPENDENT_CODE=ON
          - ${PROJECT_SOURCE_DIR}

  version_did:
    url: ${git_url}/zos/version_did.git
    branch: release/iCar_V25_V230v5
    commands:
      configure:
        arguments:
        - -DZX_CUSTOMER_PROJECT=cpj_chery
        - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
        - ${PROJECT_SOURCE_DIR}
