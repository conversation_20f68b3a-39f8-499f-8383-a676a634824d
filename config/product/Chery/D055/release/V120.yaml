base: Chery/D055/develop

projects:
  hobot_j6e_v1_0_0_pack:
    branch: release/V120
  
  hbre:
    branch: release/V120

  kernel:
    branch: release/V120

  uboot:
    branch: release/V120

  hobot_drivers:
    branch: release/V120

  ota_app:
    url: ${git_url}/zos/ota_app.git
    branch: release/V120
    dependencies: [protobuf, protobuf_host, version_did, libzmq]
    commands:
      configure:
        arguments:
        - -DZX_CUSTOMER_PROJECT=cpj_chery
        - -DZX_VEH_VARIANT=icar05
        - -DZX_PROJECT_NO=d055
        - -DZX_ETH_MONITOR=ON
        - -DZX_TIME_SYNC=ON
        - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
        - ${PROJECT_SOURCE_DIR}
  
  version_did:
    url: ${git_url}/zos/version_did.git
    branch: release/V120
    commands:
      configure:
        arguments:
        - -DZX_CUSTOMER_PROJECT=cpj_chery
        - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
        - ${PROJECT_SOURCE_DIR}