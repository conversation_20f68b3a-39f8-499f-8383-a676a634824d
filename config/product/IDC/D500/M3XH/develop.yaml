base: 
  - Infra/base/cmake_project

projects:
  zx_idc_sdk_general:
    url: ${git_url}/sdksoc/zx_idc_sdk_general.git
    branch: develop
    commands:
      patch:
        command: mkdir
        arguments:
        - "-p $ENV{HOME}/zx"
        - " && ln -sf $(readlink -f ${PROJECT_SOURCE_DIR}) $ENV{HOME}/zx/${PROJECT_NAME}"
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  idc_infrastructure:
    url: ${git_url}/idc2/idc_infrastructure.git
    branch: develop
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""
  
  idc_interfaces:
    url: ${git_url}/idc2/idc_interfaces.git
    branch: develop
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  idc_rte:
    url: ${git_url}/idc2/idc_rte.git
    branch: develop
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  idc_prm:
    url: ${git_url}/idc2/idc_prm.git
    branch: develop
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""
  
  idc_dmf:
    url: ${git_url}/idc2/idc_dmf.git
    branch: develop
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  idc_pma_pf:
    url: ${git_url}/idc2/idc_pma_pf.git
    branch: develop
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  idc_pma_ref:
    url: ${git_url}/idc2/idc_pma_ref.git
    branch: develop
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  idc_compute_pf:
    url: ${git_url}/idc2/idc_compute_pf.git
    branch: develop
    dependencies: [zx_idc_sdk_general, idc_infrastructure, idc_interfaces, idc_rte, idc_prm]
    commands:
      clean_binary:
        command: "rm -rf ${PROJECT_SOURCE_DIR}/build"
        working_directory: ${PROJECT_SOURCE_DIR}
        retcode: []
      patch:
        command: "rm -rf"
        arguments:
        - "${PROJECT_SOURCE_DIR}/idc_infrastructure && cp -ar ${PROJECT_SOURCE_DIR}/../idc_infrastructure ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_interfaces && cp -ar ${PROJECT_SOURCE_DIR}/../idc_interfaces ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_rte && cp -ar ${PROJECT_SOURCE_DIR}/../idc_rte ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_prm && cp -ar ${PROJECT_SOURCE_DIR}/../idc_prm ${PROJECT_SOURCE_DIR}"
        working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        command: ""
      build:
        command: "./idc.sh"
        arguments: 
        - "arm -t apu -p cpj_chery -d d500 -v h2c1 -vh m3xh -c Release -m full"
        working_directory: ${PROJECT_SOURCE_DIR}
      install:
        command: ""
        
  idc_data_loop:
    url: ${git_url}/idc2/idc_data_loop.git
    branch: develop
    dependencies: [zx_idc_sdk_general, idc_infrastructure, idc_interfaces, idc_rte, idc_dmf]
    commands:
      clean_binary:
        command: "rm -rf ${PROJECT_SOURCE_DIR}/build"
        working_directory: ${PROJECT_SOURCE_DIR}
        retcode: []
      patch:
        command: "rm -rf"
        arguments:
        - "${PROJECT_SOURCE_DIR}/idc_infrastructure && cp -ar ${PROJECT_SOURCE_DIR}/../idc_infrastructure ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_interfaces && cp -ar ${PROJECT_SOURCE_DIR}/../idc_interfaces ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_rte && cp -ar ${PROJECT_SOURCE_DIR}/../idc_rte ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_dmf && cp -ar ${PROJECT_SOURCE_DIR}/../idc_dmf ${PROJECT_SOURCE_DIR}"
        working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        command: ""
      build:
        command: "./idc.sh"
        arguments: 
        - "arm -t apu -p cpj_chery -d d500 -v h2c1 -vh m3xh -c Release -m full"
        working_directory: ${PROJECT_SOURCE_DIR}
      install:
        command: ""
  
  idc_flasher:
    url: ${git_url}/idc2/idc_flasher.git
    branch: develop
    dependencies: [zx_idc_sdk_general, idc_infrastructure, idc_interfaces, idc_rte]
    commands:
      clean_binary:
        command: "rm -rf ${PROJECT_SOURCE_DIR}/build"
        working_directory: ${PROJECT_SOURCE_DIR}
        retcode: []
      patch:
        command: "rm -rf"
        arguments:
        - "${PROJECT_SOURCE_DIR}/idc_infrastructure && cp -ar ${PROJECT_SOURCE_DIR}/../idc_infrastructure ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_interfaces && cp -ar ${PROJECT_SOURCE_DIR}/../idc_interfaces ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_rte && cp -ar ${PROJECT_SOURCE_DIR}/../idc_rte ${PROJECT_SOURCE_DIR}"
        working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        command: ""
      build:
        command: "./idc.sh"
        arguments: 
        - "arm -t apu -p cpj_chery -d d500 -v h2c1 -vh m3xh -c Release -m full"
        working_directory: ${PROJECT_SOURCE_DIR}
      install:
        command: ""

  idc_ipark:
    url: ${git_url}/idc2/idc_ipark.git
    branch: develop
    dependencies: [zx_idc_sdk_general, idc_infrastructure, idc_interfaces, idc_rte, idc_dmf, idc_prm, idc_pma_pf, idc_pma_ref]
    commands:
      clean_binary:
        command: "rm -rf ${PROJECT_SOURCE_DIR}/build"
        working_directory: ${PROJECT_SOURCE_DIR}
        retcode: []
      patch:
        command: "rm -rf"
        arguments:
        - "${PROJECT_SOURCE_DIR}/idc_infrastructure && cp -ar ${PROJECT_SOURCE_DIR}/../idc_infrastructure ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_interfaces && cp -ar ${PROJECT_SOURCE_DIR}/../idc_interfaces ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_rte && cp -ar ${PROJECT_SOURCE_DIR}/../idc_rte ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_dmf && cp -ar ${PROJECT_SOURCE_DIR}/../idc_dmf ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_prm && cp -ar ${PROJECT_SOURCE_DIR}/../idc_prm ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/components/pma_pf && cp -ar ${PROJECT_SOURCE_DIR}/../idc_pma_pf ${PROJECT_SOURCE_DIR}/components/pma_pf"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/components/pma_ref && cp -ar ${PROJECT_SOURCE_DIR}/../idc_pma_ref ${PROJECT_SOURCE_DIR}/components/pma_ref"
        working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        command: ""
      build:
        command: "./idc.sh"
        arguments: 
        - "arm -t apu -p cpj_chery -d d500 -v h2c1 -vh m3xh -c Release -m full"
        working_directory: ${PROJECT_SOURCE_DIR}
      install:
        command: ""

  idc_ipilot:
    url: ${git_url}/idc2/idc_ipilot.git
    branch: develop
    dependencies: [zx_idc_sdk_general, idc_infrastructure, idc_interfaces, idc_rte, idc_dmf]
    commands:
      clean_binary:
        command: "rm -rf ${PROJECT_SOURCE_DIR}/build"
        working_directory: ${PROJECT_SOURCE_DIR}
        retcode: []
      patch:
        command: "rm -rf"
        arguments:
        - "${PROJECT_SOURCE_DIR}/idc_infrastructure && cp -ar ${PROJECT_SOURCE_DIR}/../idc_infrastructure ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_interfaces && cp -ar ${PROJECT_SOURCE_DIR}/../idc_interfaces ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_rte && cp -ar ${PROJECT_SOURCE_DIR}/../idc_rte ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_dmf && cp -ar ${PROJECT_SOURCE_DIR}/../idc_dmf ${PROJECT_SOURCE_DIR}"
        working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        command: ""
      build:
        command: "./idc.sh"
        arguments: 
        - "arm -t apu -p cpj_chery -d d500 -v h2c1 -vh m3xh -c Release -m full"
        working_directory: ${PROJECT_SOURCE_DIR}
      install:
        command: ""

  idc_ipola:
    url: ${git_url}/idc2/idc_ipola.git
    branch: develop
    dependencies: [zx_idc_sdk_general, idc_infrastructure, idc_interfaces, idc_rte, idc_dmf]
    commands:
      clean_binary:
        command: "rm -rf ${PROJECT_SOURCE_DIR}/build"
        working_directory: ${PROJECT_SOURCE_DIR}
        retcode: []
      patch:
        command: "rm -rf"
        arguments:
        - "${PROJECT_SOURCE_DIR}/idc_infrastructure && cp -ar ${PROJECT_SOURCE_DIR}/../idc_infrastructure ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_interfaces && cp -ar ${PROJECT_SOURCE_DIR}/../idc_interfaces ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_rte && cp -ar ${PROJECT_SOURCE_DIR}/../idc_rte ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_dmf && cp -ar ${PROJECT_SOURCE_DIR}/../idc_dmf ${PROJECT_SOURCE_DIR}"
        working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        command: ""
      build:
        command: "./idc.sh"
        arguments: 
        - "arm -t apu -p cpj_chery -d d500 -v h2c1 -vh m3xh -c Release -m full"
        working_directory: ${PROJECT_SOURCE_DIR}
      install:
        command: ""

  idc_iuni:
    url: ${git_url}/idc2/idc_iuni.git
    branch: develop
    dependencies: [zx_idc_sdk_general, idc_infrastructure, idc_interfaces, idc_rte]
    commands:
      clean_binary:
        command: "rm -rf ${PROJECT_SOURCE_DIR}/build"
        working_directory: ${PROJECT_SOURCE_DIR}
        retcode: []
      patch:
        command: "rm -rf"
        arguments:
        - "${PROJECT_SOURCE_DIR}/idc_infrastructure && cp -ar ${PROJECT_SOURCE_DIR}/../idc_infrastructure ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_interfaces && cp -ar ${PROJECT_SOURCE_DIR}/../idc_interfaces ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_rte && cp -ar ${PROJECT_SOURCE_DIR}/../idc_rte ${PROJECT_SOURCE_DIR}"
        working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        command: ""
      build:
        command: "./idc.sh"
        arguments: 
        - "arm -t apu -p cpj_chery -d d500 -v h2c1 -vh m3xh -c Release -m full"
        working_directory: ${PROJECT_SOURCE_DIR}
      install:
        command: ""

  idc_iwalle:
    url: ${git_url}/idc2/idc_iwalle.git
    branch: develop
    dependencies: [zx_idc_sdk_general, idc_infrastructure, idc_interfaces, idc_rte]
    commands:
      clean_binary:
        command: "rm -rf ${PROJECT_SOURCE_DIR}/build"
        working_directory: ${PROJECT_SOURCE_DIR}
        retcode: []
      patch:
        command: "rm -rf"
        arguments:
        - "${PROJECT_SOURCE_DIR}/idc_infrastructure && cp -ar ${PROJECT_SOURCE_DIR}/../idc_infrastructure ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_interfaces && cp -ar ${PROJECT_SOURCE_DIR}/../idc_interfaces ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_rte && cp -ar ${PROJECT_SOURCE_DIR}/../idc_rte ${PROJECT_SOURCE_DIR}"
        working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        command: ""
      build:
        command: "./idc.sh"
        arguments: 
        - "arm -t apu -p cpj_chery -d d500 -v h2c1 -vh m3xh -c Release -m full"
        working_directory: ${PROJECT_SOURCE_DIR}
      install:
        command: ""
  
  idc_viper:
    url: ${git_url}/idc2/idc_viper.git
    branch: develop
    dependencies: [zx_idc_sdk_general, idc_infrastructure, idc_interfaces, idc_rte, idc_dmf]
    commands:
      clean_binary:
        command: "rm -rf ${PROJECT_SOURCE_DIR}/build"
        working_directory: ${PROJECT_SOURCE_DIR}
        retcode: []
      patch:
        command: "rm -rf"
        arguments:
        - "${PROJECT_SOURCE_DIR}/idc_infrastructure && cp -ar ${PROJECT_SOURCE_DIR}/../idc_infrastructure ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_interfaces && cp -ar ${PROJECT_SOURCE_DIR}/../idc_interfaces ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_rte && cp -ar ${PROJECT_SOURCE_DIR}/../idc_rte ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_dmf && cp -ar ${PROJECT_SOURCE_DIR}/../idc_dmf ${PROJECT_SOURCE_DIR}"
        working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        command: ""
      build:
        command: "./idc.sh"
        arguments: 
        - "arm -t apu -p cpj_chery -d d500 -v h2c1 -vh m3xh -c Release -m full"
        working_directory: ${PROJECT_SOURCE_DIR}
      install:
        command: ""

  idc_vial:
    url: ${git_url}/idc2/idc_vial.git
    branch: develop
    dependencies: [zx_idc_sdk_general, idc_infrastructure, idc_interfaces, idc_rte]
    commands:
      clean_binary:
        command: "rm -rf ${PROJECT_SOURCE_DIR}/build"
        working_directory: ${PROJECT_SOURCE_DIR}
        retcode: []
      patch:
        command: "rm -rf"
        arguments:
        - "${PROJECT_SOURCE_DIR}/idc_infrastructure && cp -ar ${PROJECT_SOURCE_DIR}/../idc_infrastructure ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_interfaces && cp -ar ${PROJECT_SOURCE_DIR}/../idc_interfaces ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_rte && cp -ar ${PROJECT_SOURCE_DIR}/../idc_rte ${PROJECT_SOURCE_DIR}"
        working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        command: ""
      build:
        command: "./idc.sh"
        arguments: 
        - "arm -t apu -p cpj_chery -d d500 -v h2c1 -vh m3xh -c Release -m full"
        working_directory: ${PROJECT_SOURCE_DIR}
      install:
        command: ""

  idc_mcu_app:
    url: ${git_url}/idc2/idc_mcu_app.git
    branch: develop
    dependencies: [zx_idc_sdk_general, idc_infrastructure, idc_interfaces, idc_rte, idc_dmf, idc_prm]
    commands:
      clean_binary:
        command: "rm -rf ${PROJECT_SOURCE_DIR}/build"
        working_directory: ${PROJECT_SOURCE_DIR}
        retcode: []
      patch:
        command: "rm -rf"
        arguments:
        - "${PROJECT_SOURCE_DIR}/idc_infrastructure && cp -ar ${PROJECT_SOURCE_DIR}/../idc_infrastructure ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_interfaces && cp -ar ${PROJECT_SOURCE_DIR}/../idc_interfaces ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_rte && cp -ar ${PROJECT_SOURCE_DIR}/../idc_rte ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_dmf && cp -ar ${PROJECT_SOURCE_DIR}/../idc_dmf ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/idc_prm && cp -ar ${PROJECT_SOURCE_DIR}/../idc_prm ${PROJECT_SOURCE_DIR}"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/components/pmasensl/idc_pma_pf && cp -ar ${PROJECT_SOURCE_DIR}/../idc_pma_pf ${PROJECT_SOURCE_DIR}/components/pmasensl"
        - "&& rm -rf ${PROJECT_SOURCE_DIR}/components/pmasensl/idc_pma_ref && cp -ar ${PROJECT_SOURCE_DIR}/../idc_pma_ref ${PROJECT_SOURCE_DIR}/components/pmasensl"
        working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        command: ""
      build:
        command: "./idc.sh"
        arguments: 
        - "arm -t rpu -cr 10 -p cpj_chery -d d500 -v h2c1 -vh m3xh -c Release -m full"
        working_directory: ${PROJECT_SOURCE_DIR}
      install:
        command: ""