base:
- ZOS/thirdparty/develop

projects:
  hbre:
    url: ${git_url}/sdksoc/sirius_s3_mono3.3_hbre.git
    branch: feature/iCar05_fcm
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: "cmake -E"
        arguments:
        - copy
        - ${PROJECT_SOURCE_DIR}/hbutils/veeprom.h
        - ${IMOBUILDER_TMP_OUTPUT}/appuser/include/veeprom.h
        - "&&"
        - cmake
        - -E
        - copy
        - ${PROJECT_SOURCE_DIR}/hbutils/ota_utility/updatedll/include/update.h
        - ${IMOBUILDER_TMP_OUTPUT}/appuser/include/update.h
    package:
      files: []
      directories: ["${IMOBUILDER_TMP_OUTPUT}/appuser"]

  kernel:
    url: ${git_url}/sdksoc/sirius_s3_mono3.3_kernel.git
    branch: feature/iCar05_fcm
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  perception_app:
    url: ${git_url}/sdksoc/sirius_s3_mono3.3_perception_app.git
    branch: feature/iCar05_fcm
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  uboot:
    url: ${git_url}/sdksoc/sirius_s3_mono3.3_uboot.git
    branch: feature/iCar05_fcm
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  sirius_s3_mono3_3_pack:
    var:
      debug_IDX: "1"
      coverage_IDX: "1"
      release_IDX: "2"
      relwithdebinfo_IDX: "2"
    env:
      CC: null
      CXX: null
      RANLIB: null
      AR: null
      LD: null
      NM: null
      OBJDUMP: null
      STRIP: null
      CFLAGS: null
      CXXFLAGS: null
      PKG_CONFIG_PATH: null
      PKG_CONFIG_LIBDIR: null
      PKG_CONFIG_SYSROOT_DIR: null
    url: null
    path: sirius_s3_mono3.3
    branch: null
    dependencies: [hbre, kernel, perception_app, uboot, lz4_host, sirius_s3_mono3_3_host]
    commands:
      clean:
        command: git
        arguments: [
          "clean -xfd", 
          "&& git reset --hard"
        ]
      patch:
        command: rm
        arguments:
        - -rf
        - ${PROJECT_SOURCE_DIR}/hbre
        - ${PROJECT_SOURCE_DIR}/kernel
        - ${PROJECT_SOURCE_DIR}/perception_app
        - ${PROJECT_SOURCE_DIR}/uboot
        - ${PROJECT_SOURCE_DIR}/sirius_flasher
        - "&&"
        - cp 
        - -r
        - ${PROJECT_SOURCE_DIR}/../hbre
        - ${PROJECT_SOURCE_DIR}/../kernel
        - ${PROJECT_SOURCE_DIR}/../perception_app
        - ${PROJECT_SOURCE_DIR}/../uboot
        - ${PROJECT_SOURCE_DIR}
        - "&&"
        - 'find ${PROJECT_SOURCE_DIR}/prebuilts/root/usr/zx_ota -type f -name "*.so*" -exec aarch64-linux-gnu-strip {} \;'
        - "&&"
        - 'find ${PROJECT_SOURCE_DIR}/prebuilts/root/usr/3rdparty -type f -name "*.so*" -exec aarch64-linux-gnu-strip {} \;'
        working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        command: ""
      build:
        env:
          TROS: "1"
          SUB_BOARD: "one"
        command: source
        arguments: 
        - envsetup.sh
        - "&&"
        - lunch
        - ${${IMOBUILDER_BUILD_TYPE}_IDX}
        - "&&"
        - ./build.sh
        - -b 
        - j3dvb_emmc 
        - -s 
        - secure 
        - -m 
        - hynix 
        - -f 
        - "3200" 
        - -a 
        - "2" 
        - -d 
        - dual_boot 
        - -e 
        - ft
        working_directory: ${PROJECT_SOURCE_DIR}/build
        parallel_arg: ""
        retcode: [0]
      install:
        command: ""
    package:
      files: []
      directories: []

  sirius_s3_mono3_3_host:
    url: ${git_url}/sdksoc/sirius_s3_mono3.3.git
    path: sirius_s3_mono3.3
    branch: feature/iCar05_fcm
    dependencies: []
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: "cmake -E"
        arguments:
        - copy_directory
        - ${PROJECT_SOURCE_DIR}/appuser/include
        - ${IMOBUILDER_TMP_OUTPUT}/appuser/include
        - "&&"
        - cmake
        - -E
        - copy_directory
        - ${PROJECT_SOURCE_DIR}/appuser/lib
        - ${IMOBUILDER_TMP_OUTPUT}/appuser/lib
        - "&&"
        - cmake
        - -E
        - copy_directory
        - ${PROJECT_SOURCE_DIR}/appuser/middleware/include
        - ${IMOBUILDER_TMP_OUTPUT}/appuser/include
        - "&&"
        - cmake
        - -E
        - copy_directory
        - ${PROJECT_SOURCE_DIR}/appuser/middleware/lib
        - ${IMOBUILDER_TMP_OUTPUT}/appuser/lib
    package:
      files: []
      directories: ["${IMOBUILDER_TMP_OUTPUT}/appuser"]
