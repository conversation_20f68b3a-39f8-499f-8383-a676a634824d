base:
- ZOS/thirdparty/release/20250408

projects:
  hbre:
    url: ${git_url}/sdksoc/hobot_j6e_v1.0.0_hbre.git
    branch: develop
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  kernel:
    url: ${git_url}/sdksoc/hobot_j6e_v1.0.0_kernel.git
    branch: develop
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  uboot:
    url: ${git_url}/sdksoc/hobot_j6e_v1.0.0_uboot.git
    branch: develop
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  hobot_drivers:
    url: ${git_url}/sdksoc/hobot_j6e_v1.0.0_hobot-drivers.git
    branch: develop
    path: hobot-drivers
    commands:
      configure:
        command: ""
      build:
        command: ""
      install:
        command: ""

  hobot_j6e_v1_0_0_pack:
    var:
      # Below settings should be override by different projects themself.
      debug_lunch_args: "j6e_debug_defconfig"
      coverage_lunch_args: "j6e_debug_defconfig"
      release_lunch_args: "j6e_release_defconfig"
      relwithdebinfo_lunch_args: "j6e_release_defconfig"
      minsizerel_lunch_args: "j6e_release_defconfig"
    env:
      CC: null
      CXX: null
      RANLIB: null
      AR: null
      LD: null
      NM: null
      OBJDUMP: null
      STRIP: null
      CFLAGS: null
      CXXFLAGS: null
      LDFLAGS: null
      PKG_CONFIG_PATH: null
      PKG_CONFIG_LIBDIR: null
      PKG_CONFIG_SYSROOT_DIR: null
    url: ${git_url}/sdksoc/hobot_j6e_v1.0.0.git
    path: hobot_j6e_v1.0.0
    branch: develop
    dependencies: [hbre, kernel, uboot, hobot_drivers]
    commands:
      patch:
        command: rm
        arguments:
        - -rf
        - ${PROJECT_SOURCE_DIR}/hbre
        - ${PROJECT_SOURCE_DIR}/kernel
        - ${PROJECT_SOURCE_DIR}/uboot
        - ${PROJECT_SOURCE_DIR}/hobot-drivers
        - "&&"
        - cp 
        - -r
        - ${PROJECT_SOURCE_DIR}/../hbre
        - ${PROJECT_SOURCE_DIR}/../kernel
        - ${PROJECT_SOURCE_DIR}/../uboot
        - ${PROJECT_SOURCE_DIR}/../hobot-drivers
        - ${PROJECT_SOURCE_DIR}
        - "&&"
        - 'find ${PROJECT_SOURCE_DIR}/hbbin/imotion_app -type f -name "*.so*" -exec aarch64-none-linux-gnu-strip {} \;'
        working_directory: ${PROJECT_SOURCE_DIR}
      configure:
        command: "make"
        arguments:
        - CHIP_DIR=j6
        - ${${IMOBUILDER_BUILD_TYPE}_lunch_args}
        working_directory: ${PROJECT_SOURCE_DIR}
      build:
        env:
          SERVER_HOST_BUILD: y
        command: source
        arguments: 
        - envsetup.sh
        - "&&" 
        - "./build.sh all" # build image
        - "&&" 
        - "./build.sh otapackage all" # build ota packet
        working_directory: ${PROJECT_SOURCE_DIR}
        parallel_arg: ""
      install:
        command: ""