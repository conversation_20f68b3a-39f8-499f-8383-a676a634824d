base: 
- Infra/thirdparty/develop
projects:
  dpro_one:
    url: ${git_url}/dat/dpro_one.git 
    branch: master 
    package:
      manifest: ${PROJECT_BINARY_DIR}/install_manifest.txt
      files: []
      directories: []
    commands: 
      configure:
        arguments:
        - arch_pattern: "*"
          arguments:
          - ${PROJECT_SOURCE_DIR}
          - -DCMAKE_INSTALL_PREFIX=${IMOBUILDER_TARGET_OUTPUT}
