var: 
  IMOBUILDER_BUILD_TYPE: minsizerel
arguments:
- tool_pattern: "*cmake"
  arguments: 
  - -DCMA<PERSON>_EXPORT_NO_PACKAGE_REGISTRY=ON
  - -DCMAKE_FIND_PACKAGE_NO_PACKAGE_REGISTRY=ON
  - -DCMAKE_BUILD_TYPE=MINSIZEREL
  - -DCMAKE_EXPORT_COMPILE_COMMANDS=ON
  - -DCMAKE_VERBOSE_MAKEFILE=${IMOBUILDER_BUILD_VERBOSE}
  - -DIMOBUILDER_PLATFORM=${IMOBUILDER_PLATFORM}
  - -DIMOBUILDER_PRODUCT=${IMOBUILDER_PRODUCT}
  - -D<PERSON><PERSON><PERSON>UILDER_BUILD_ID=${IMOBUILDER_BUILD_ID}
