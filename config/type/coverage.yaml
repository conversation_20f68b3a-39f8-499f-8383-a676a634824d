var: 
  IMOBUILDER_BUILD_TYPE: coverage
arguments:
- tool_pattern: "*cmake"
  arguments: 
  - -DCMAKE_EXPORT_NO_PACKAGE_REGISTRY=ON
  - -DCMAKE_FIND_PACKAGE_NO_PACKAGE_REGISTRY=ON
  - -DCMAKE_BUILD_TYPE=DEBUG
  - -DCMAKE_EXPORT_COMPILE_COMMANDS=ON
  - -DCMAKE_VERBOSE_MAKEFILE=${IMOBUILDER_BUILD_VERBOSE}
  - -DIMOBUILDER_PLATFORM=${IMOBUILDER_PLATFORM}
  - -DIMOBUILDER_PRODUCT=${IMOBUILDER_PRODUCT}
  - -DIMOBUILDER_BUILD_TYPE=${IMOBUILDER_BUILD_TYPE}
  - -DIMOBUILDER_BUILD_ID=${IMOBUILDER_BUILD_ID}
