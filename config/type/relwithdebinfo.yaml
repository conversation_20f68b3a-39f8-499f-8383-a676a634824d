var: 
  IMOBUILDER_BUILD_TYPE: relwithdebinfo
arguments:
- tool_pattern: "*cmake"
  arguments: 
  - -DCMAKE_EXPORT_NO_PACKAGE_REGISTRY=ON
  - -DCMAKE_FIND_PACKAGE_NO_PACKAGE_REGISTRY=ON
  - -DCMAKE_BUILD_TYPE=RELWITHDEBINFO
  - -DCMAKE_EXPORT_COMPILE_COMMANDS=ON
  - -DCMAKE_VERBOSE_MAKEFILE=${IMOBUILDER_BUILD_VERBOSE}
  - -DIMOBUILDER_PLATFORM=${IMOBUILDER_PLATFORM}
  - -DIMOBUILDER_PRODUCT=${IMOBUILDER_PRODUCT}
  - -D<PERSON><PERSON><PERSON><PERSON>LDER_BUILD_ID=${IMOBUILDER_BUILD_ID}
