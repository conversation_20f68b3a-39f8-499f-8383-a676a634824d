url: package/x86_64-linux/python/python-3.8.20.tar.gz
md5: null
var:
  PATH: "${CROSSTOOL_ROOT}/package/x86_64-linux/python/python-3.8.20/bin:${PATH}"
  CMAKE_PREFIX_PATH: "${CROSSTOOL_ROOT}/package/x86_64-linux/python/python-3.8.20:\
                      ${CMAKE_PREFIX_PATH}"
  LDFLAGS: "-Wl,-rpath-link,\
           ${CROSSTOOL_ROOT}/package/x86_64-linux/python/python-3.8.20/lib:\
           ${LDFLAGS}"
  LD_LIBRARY_PATH: ${CROSSTOOL_ROOT}/package/x86_64-linux/python/python-3.8.20/lib:${LD_LIBRARY_PATH}

env:
  PATH: ${PATH}
  CMAKE_PREFIX_PATH: ${CMAKE_PREFIX_PATH}
  LDFLAGS: ${LDFLAGS}
  LD_LIBRARY_PATH: ${LD_LIBRARY_PATH}
