url: package/x86_64-linux/ubuntu-22.04/cuda-12.4.tar.gz
var:
  CMAKE_FIND_ROOT_PATH: ${CROSSTOOL_ROOT}/package/x86_64-linux/ubuntu-22.04/cuda-12.4:${CMAKE_FIND_ROOT_PATH}
  LDFLAGS: "-Wl,-rpath-link,${CROSSTOOL_ROOT}/package/x86_64-linux/ubuntu-22.04/cuda-12.4/lib64 ${LDFLAGS}"
  TARGET_LD_LIBRARY_PATH: "${CROSSTOOL_ROOT}/package/x86_64-linux/ubuntu-22.04/cuda-12.4/targets/x86_64-linux/lib:${TARGET_LD_LIBRARY_PATH}"
env:
  CMAKE_FIND_ROOT_PATH: ${CMAKE_FIND_ROOT_PATH}
  CUDA_TOOLKIT_ROOT: ${CROSSTOOL_ROOT}/package/x86_64-linux/ubuntu-22.04/cuda-12.4
  CUDAToolkit_ROOT: ${CROSSTOOL_ROOT}/package/x86_64-linux/ubuntu-22.04/cuda-12.4
  LDFLAGS: ${LDFLAGS}
