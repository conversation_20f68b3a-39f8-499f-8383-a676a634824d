url: package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all.tar.gz
md5: null
var:
  CMAKE_FIND_ROOT_PATH: "${CROSSTOOL_ROOT}/package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all:\
                        ${CROSSTOOL_ROOT}/package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all/usr:\
                        ${CROSSTOOL_ROOT}/package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all/usr/hobot:\
                        ${CROSSTOOL_ROOT}/package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all/usr/lib/aarch64-linux-gnu:\
                        ${CMAKE_FIND_ROOT_PATH}"
  CFLAGS: "-isystem ${CROSSTOOL_ROOT}/package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all/include \
          -isystem ${CROSSTOOL_ROOT}/package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all/usr/include \
          -isystem ${CROSSTOOL_ROOT}/package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all/usr/hobot/include \
          -isystem ${CROSSTOOL_ROOT}/package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all/usr/hobot/include/aarch64-linux-gnu \
          ${CFLAGS}"
  CXXFLAGS: "-isystem ${CROSSTOOL_ROOT}/package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all/include \
          -isystem ${CROSSTOOL_ROOT}/package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all/usr/include \
          -isystem ${CROSSTOOL_ROOT}/package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all/usr/hobot/include \
          -isystem ${CROSSTOOL_ROOT}/package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all/usr/hobot/include/aarch64-linux-gnu \
          ${CXXFLAGS}"
  LDFLAGS: "-L${CROSSTOOL_ROOT}/package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all/usr/lib \
           -L${CROSSTOOL_ROOT}/package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all/usr/hobot/lib \
           -L${CROSSTOOL_ROOT}/package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all/usr/lib/aarch64-linux-gnu \
           -L${CROSSTOOL_ROOT}/package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all/usr/hobot/lib/aarch64-linux-gnu \
          -Wl,-rpath-link,\
           ${CROSSTOOL_ROOT}/package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all/lib:\
           ${CROSSTOOL_ROOT}/package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all/usr/lib:\
           ${CROSSTOOL_ROOT}/package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all/usr/hobot/lib:\
           ${CROSSTOOL_ROOT}/package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all/usr/lib/aarch64-linux-gnu:\
           ${CROSSTOOL_ROOT}/package/aarch64-linux/j6/hbrootfs-sdk_0.0.1.20250218_all/usr/hobot/lib/aarch64-linux-gnu \
           ${LDFLAGS}"
env:
  CMAKE_FIND_ROOT_PATH: ${CMAKE_FIND_ROOT_PATH}
  CFLAGS: ${CFLAGS}
  CXXFLAGS: ${CXXFLAGS}
  LDFLAGS: ${LDFLAGS}
