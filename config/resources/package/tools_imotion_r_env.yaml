url: null
md5: null
var:
  PATH: "${IMOBUILDER_HOST_OUTPUT}/bin:${PATH}:$ENV{HOME}/.local/bin"
  LDFLAGS: "-Wl,-rpath-link,${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib:${IMOBUILDER_TMP_OUTPUT}/lib:${IMOBUILDER_TARGET_OUTPUT}/lib ${LDFLAGS}"
  LD_LIBRARY_PATH: "${IMOBUILDER_HOST_OUTPUT}/lib:${LD_LIBRARY_PATH}"
  CMAKE_FIND_ROOT_PATH: "${IMOBUILDER_TARGET_OUTPUT}:${IMOBUILDER_TARGET_OUTPUT}/3rdparty:${IMOBUILDER_TMP_OUTPUT}:${CMAKE_FIND_ROOT_PATH}"
  CMAKE_PREFIX_PATH: "${IMOBUILDER_TARGET_OUTPUT}:${IMOBUILDER_TARGET_OUTPUT}/3rdparty:${CMAKE_PREFIX_PATH}"
  PKG_CONFIG_PATH: ${IMOBUILDER_TARGET_OUTPUT}/3rdparty/share/pkgconfig:${IMOBUILDER_TARGET_OUTPUT}/3rdparty/lib/pkgconfig:${PKG_CONFIG_PATH}
  artifactory_url: https://nexus.imotion.ai
env:
  IMOBUILDER_HOST_OUTPUT: ${IMOBUILDER_HOST_OUTPUT}
  IMOBUILDER_TARGET_OUTPUT: ${IMOBUILDER_TARGET_OUTPUT}
  IMOBUILDER_TMP_OUTPUT: ${IMOBUILDER_TMP_OUTPUT}
  PATH: ${PATH}
  # LDFLAGS: ${LDFLAGS}
  LD_LIBRARY_PATH: ${LD_LIBRARY_PATH}
  PKG_CONFIG_PATH: ${PKG_CONFIG_PATH}
  CMAKE_FIND_ROOT_PATH: ${CMAKE_FIND_ROOT_PATH}
  CMAKE_PREFIX_PATH: ${CMAKE_PREFIX_PATH}
