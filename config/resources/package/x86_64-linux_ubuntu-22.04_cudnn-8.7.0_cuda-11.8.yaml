url: package/x86_64-linux/ubuntu-22.04/cudnn-8.7.0_cuda-11.8.tar.gz
var:
  CMAKE_FIND_ROOT_PATH: ${CROSSTOOL_ROOT}/package/x86_64-linux/ubuntu-22.04/cudnn-8.7.0_cuda-11.8:${CMAKE_FIND_ROOT_PATH}
  LDFLAGS: "-Wl,-rpath-link,${CROSSTOOL_ROOT}/package/x86_64-linux/ubuntu-22.04/cudnn-8.7.0_cuda-11.8/lib ${LDFLAGS}"
  TARGET_LD_LIBRARY_PATH: "${CROSSTOOL_ROOT}/package/x86_64-linux/ubuntu-22.04/cudnn-8.7.0_cuda-11.8/lib:${TARGET_LD_LIBRARY_PATH}"
env:
  CMAKE_FIND_ROOT_PATH: ${CMAKE_FIND_ROOT_PATH}
  LDFLAGS: ${LDFLAGS}
