url: package/x86_64-linux/python/python2.7.tar.gz
md5: null
var:
  PATH: "${CROSSTOOL_ROOT}/package/x86_64-linux/python/python2.7/bin:${PATH}"
  CMAKE_PREFIX_PATH: "${CROSSTOOL_ROOT}/package/x86_64-linux/python/python2.7:\
                      ${CMAKE_PREFIX_PATH}"
  LDFLAGS: "-Wl,-rpath-link,\
           ${CROSSTOOL_ROOT}/package/x86_64-linux/python/python2.7/lib:\
           ${LDFLAGS}"

env:
  PATH: ${PATH}
  CMAKE_PREFIX_PATH: ${CMAKE_PREFIX_PATH}
  LDFLAGS: ${LDFLAGS}
