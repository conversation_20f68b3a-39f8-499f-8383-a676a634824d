url: rootfs/rootfs-sdk-bookworm_0.0.1.20250218_all.tar.gz
md5: null
var:
  PKG_CONFIG_LIBDIR: ${CROSSTOOL_ROOT}/rootfs/rootfs-sdk-bookworm_0.0.1.20250218_all/usr/lib/pkgconfig:${CROSSTOOL_ROOT}/rootfs/rootfs-sdk-bookworm_0.0.1.20250218_all/usr/share/pkgconfig:${CROSSTOOL_ROOT}/rootfs/rootfs-sdk-bookworm_0.0.1.20250218_all/usr/lib/aarch64-linux-gnu/pkgconfig
  CFLAGS: "-isystem ${CROSSTOOL_ROOT}/rootfs/rootfs-sdk-bookworm_0.0.1.20250218_all/usr/include \
    -isystem ${CROSSTOOL_ROOT}/rootfs/rootfs-sdk-bookworm_0.0.1.20250218_all/usr/include/aarch64-linux-gnu \
    ${CFLAGS}"
  CXXFLAGS: "-isystem ${CROSSTOOL_ROOT}/rootfs/rootfs-sdk-bookworm_0.0.1.20250218_all/usr/include \
    -isystem ${CROSSTOOL_ROOT}/rootfs/rootfs-sdk-bookworm_0.0.1.20250218_all/usr/include/aarch64-linux-gnu \
    ${CXXFLAGS}"
  LDFLAGS: "-Wl,-rpath-link,\
            ${CROSSTOOL_ROOT}/rootfs/rootfs-sdk-bookworm_0.0.1.20250218_all/usr/lib/aarch64-linux-gnu \
            -L${CROSSTOOL_ROOT}/rootfs/rootfs-sdk-bookworm_0.0.1.20250218_all/usr/lib/aarch64-linux-gnu \
            ${LDFLAGS} "
  TARGET_LD_LIBRARY_PATH: "${CROSSTOOL_ROOT}/rootfs/rootfs-sdk-bookworm_0.0.1.20250218_all/usr/lib:\
                           ${CROSSTOOL_ROOT}/rootfs/rootfs-sdk-bookworm_0.0.1.20250218_all/usr/lib/aarch64-linux-gnu"
env:
  PKG_CONFIG_LIBDIR: ${PKG_CONFIG_LIBDIR}
  PKG_CONFIG_SYSROOT_DIR: ${CROSSTOOL_ROOT}/rootfs/rootfs-sdk-bookworm_0.0.1.20250218_all
  CFLAGS: ${CFLAGS}
  CXXFLAGS: ${CXXFLAGS}
  LDFLAGS: ${LDFLAGS}