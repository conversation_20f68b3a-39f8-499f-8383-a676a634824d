url: package/aarch64-linux/j3/ddk-1.10.7.tar.gz
var:
  CMAKE_FIND_ROOT_PATH: "${CROSSTOOL_ROOT}/package/aarch64-linux/j3/ddk-1.10.7/appsdk/appuser:\
                        ${CROSSTOOL_ROOT}/package/aarch64-linux/j3/ddk-1.10.7/bpu_predict:\
                        ${CROSSTOOL_ROOT}/package/aarch64-linux/j3/ddk-1.10.7/dnn:${CMAKE_FIND_ROOT_PATH}"
  LDFLAGS: "-L${CROSSTOOL_ROOT}/package/aarch64-linux/j3/ddk-1.10.7/appsdk/appuser/lib \
            -Wl,-rpath-link,${CROSSTOOL_ROOT}/package/aarch64-linux/j3/ddk-1.10.7/appsdk/appuser/lib:\
            ${CROSSTOOL_ROOT}/package/aarch64-linux/j3/ddk-1.10.7/bpu_predict/lib:\
            ${CROSSTOOL_ROOT}/package/aarch64-linux/j3/ddk-1.10.7/dnn/lib ${LDFLAGS}"
  PKG_CONFIG_PATH: ${PKG_CONFIG_PATH}:${CROSSTOOL_ROOT}/package/aarch64-linux/j3/ddk-1.10.7/appsdk/appuser/lib/pkgconfig
  CFLAGS: "-isystem ${CROSSTOOL_ROOT}/package/aarch64-linux/j3/ddk-1.10.7/appsdk/appuser/include ${CFLAGS}"
  CXXFLAGS: "-isystem ${CROSSTOOL_ROOT}/package/aarch64-linux/j3/ddk-1.10.7/appsdk/appuser/include ${CXXFLAGS}"
env:
  CMAKE_FIND_ROOT_PATH: ${CMAKE_FIND_ROOT_PATH}
  LDFLAGS: ${LDFLAGS}
  PKG_CONFIG_PATH: ${PKG_CONFIG_PATH}
  CFLAGS: ${CFLAGS}
  CXXFLAGS: ${CXXFLAGS}