url: package/x86_64-linux/ubuntu-22.04/opengl-22.04.tar.gz
md5: null
var:
  CMAKE_FIND_ROOT_PATH: "${CROSSTOOL_ROOT}/package/x86_64-linux/ubuntu-22.04/opengl-22.04:\
                        ${CMAKE_FIND_ROOT_PATH}"
  CFLAGS: "-isystem ${CROSSTOOL_ROOT}/package/x86_64-linux/ubuntu-22.04/opengl-22.04/usr/include ${CFLAGS}"
  CXXFLAGS: "-isystem ${CROSSTOOL_ROOT}/package/x86_64-linux/ubuntu-22.04/opengl-22.04/usr/include ${CXXFLAGS}"                        
  LDFLAGS: "-Wl,-rpath-link,\
           ${CROSSTOOL_ROOT}/package/x86_64-linux/ubuntu-22.04/opengl-22.04/lib/x86_64-linux-gnu:\
           ${CROSSTOOL_ROOT}/package/x86_64-linux/ubuntu-22.04/opengl-22.04/lib64:\
           ${CROSSTOOL_ROOT}/package/x86_64-linux/ubuntu-22.04/opengl-22.04/usr/lib/x86_64-linux-gnu \
           ${LDFLAGS}"
env:
  CMAKE_FIND_ROOT_PATH: ${CMAKE_FIND_ROOT_PATH}
  CFLAGS: ${CFLAGS}
  CXXFLAGS: ${CXXFLAGS}
  LDFLAGS: ${LDFLAGS}
