url: package/x86_64-linux/onnxruntime-linux-x64-gpu-cuda12-1.18.0.tar.gz
md5: null
var:
  CMAKE_FIND_ROOT_PATH: "${CROSSTOOL_ROOT}/package/x86_64-linux/onnxruntime-linux-x64-gpu-1.18.0:\
                        ${CMAKE_FIND_ROOT_PATH}"
  LDFLAGS: "-Wl,-rpath-link,\
           ${CROSSTOOL_ROOT}/package/x86_64-linux/onnxruntime-linux-x64-gpu-1.18.0/lib:\
           ${LDFLAGS}"
env:
  CMAKE_FIND_ROOT_PATH: ${CMAKE_FIND_ROOT_PATH}
  LDFLAGS: ${LDFLAGS}
