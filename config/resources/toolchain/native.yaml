url: null
var:
  PATH: /usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
  LDFLAGS: ""
  LD_LIBRARY_PATH: ""
  PKG_CONFIG_PATH: ""
  CMAKE_PREFIX_PATH: ""
  TOOLCHAIN_PREFIX: ''
env:
  PATH: ${PATH}
  LANG: $ENV{LANG}
  TERM: $ENV{TERM}
  PWD: $ENV{PWD}
  SHELL: $ENV{SHELL}
  USER: $ENV{USER}
  HOME: $ENV{HOME}
  LDFLAGS: ${LDFLAGS}
  PKG_CONFIG_PATH: ${PKG_CONFIG_PATH}
  CMAKE_PREFIX_PATH: ${CMAKE_PREFIX_PATH}
