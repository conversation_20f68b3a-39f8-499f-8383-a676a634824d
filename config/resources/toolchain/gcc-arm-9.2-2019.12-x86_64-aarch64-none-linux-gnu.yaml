url: toolchain/aarch64-linux/gcc-arm-9.2-2019.12-x86_64-aarch64-none-linux-gnu.tar.gz
md5: null
var:
  PATH: ${CROSSTOOL_ROOT}/toolchain/aarch64-linux/gcc-arm-9.2-2019.12-x86_64-aarch64-none-linux-gnu/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
  TOOLCHAIN_PREFIX: aarch64-none-linux-gnu-
  CC: aarch64-none-linux-gnu-gcc
  CXX: aarch64-none-linux-gnu-g++
  FC: aarch64-none-linux-gnu-gfortran
  RANLIB: aarch64-none-linux-gnu-ranlib
  AR: aarch64-none-linux-gnu-ar
  LD: aarch64-none-linux-gnu-ld
  NM: aarch64-none-linux-gnu-nm
  OBJDUMP: aarch64-none-linux-gnu-objdump
  STRIP: aarch64-none-linux-gnu-strip
  LDFLAGS: ""
  LD_LIBRARY_PATH: ""
  PKG_CONFIG_PATH: ""
  CMAKE_PREFIX_PATH: ""
  SYSROOT: $SHELL{${CROSSTOOL_ROOT}/toolchain/aarch64-linux/gcc-arm-9.2-2019.12-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc -print-sysroot}
  AUTOTOOL_HOST: aarch64-none-linux-gnu
  ARCH: aarch64
env:
  PATH: ${PATH}
  LANG: $ENV{LANG}
  TERM: $ENV{TERM}
  PWD: $ENV{PWD}
  SHELL: $ENV{SHELL}
  USER: $ENV{USER}
  HOME: $ENV{HOME}

  TOOLCHAIN_PREFIX: ${TOOLCHAIN_PREFIX}

  CC: ${CC}
  CXX: ${CXX}
  FC: ${FC}
  RANLIB: ${RANLIB}
  AR: ${AR}
  LD: ${LD}
  NM: ${NM}
  OBJDUMP: ${OBJDUMP}
  STRIP: ${STRIP}
  LDFLAGS: ${LDFLAGS}
  PKG_CONFIG_PATH: ${PKG_CONFIG_PATH}
  PKG_CONFIG_LIBDIR: ${PKG_CONFIG_LIBDIR}
  CMAKE_PREFIX_PATH: ${CMAKE_PREFIX_PATH}
  SYSROOT: ${SYSROOT}

arguments:
- tool_pattern: "cmake"
  arguments:
  - -DCMAKE_TOOLCHAIN_FILE=${CROSSTOOL_ROOT}/package/tools/cmake/toolchain_files/aarch64-linux-gcc.cmake
