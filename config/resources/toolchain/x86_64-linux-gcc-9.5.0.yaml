url: toolchain/x86_64-linux/gcc-9.5.0.tar.gz
md5: null
var:
  PATH: ${CROSSTOOL_ROOT}/toolchain/x86_64-linux/gcc-9.5.0/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
  TOOLCHAIN_PREFIX: x86_64-linux-gnu-
  CC: x86_64-linux-gnu-gcc
  CXX: x86_64-linux-gnu-g++
  FC: x86_64-linux-gnu-gfortran
  RANLIB: x86_64-linux-gnu-ranlib
  AR: x86_64-linux-gnu-ar
  LD: x86_64-linux-gnu-ld
  NM: x86_64-linux-gnu-nm
  OBJDUMP: x86_64-linux-gnu-objdump
  STRIP: x86_64-linux-gnu-strip
  LDFLAGS: ""
  LD_LIBRARY_PATH: ""
  PKG_CONFIG_PATH: ""
  CMAKE_PREFIX_PATH: ""
  SYSROOT: $SHELL{${CROSSTOOL_ROOT}/toolchain/x86_64-linux/gcc-9.5.0/bin/x86_64-linux-gnu-gcc -print-sysroot}
  AUTOTOOL_HOST: x86_64-linux-gnu
  ARCH: x86_64
env:
  PATH: ${PATH}
  LANG: $ENV{LANG}
  TERM: $ENV{TERM}
  PWD: $ENV{PWD}
  SHELL: $ENV{SHELL}
  USER: $ENV{USER}
  HOME: $ENV{HOME}

  TOOLCHAIN_PREFIX: ${TOOLCHAIN_PREFIX}

  CC: ${CC}
  CXX: ${CXX}
  FC: ${FC}
  RANLIB: ${RANLIB}
  AR: ${AR}
  LD: ${LD}
  NM: ${NM}
  OBJDUMP: ${OBJDUMP}
  STRIP: ${STRIP}
  LDFLAGS: ${LDFLAGS}
  PKG_CONFIG_PATH: ${PKG_CONFIG_PATH}
  PKG_CONFIG_LIBDIR: ${PKG_CONFIG_LIBDIR}
  CMAKE_PREFIX_PATH: ${CMAKE_PREFIX_PATH}
  SYSROOT: ${SYSROOT}

arguments:
- tool_pattern: "cmake"
  arguments:
  - -DCMAKE_TOOLCHAIN_FILE=${CROSSTOOL_ROOT}/package/tools/cmake/toolchain_files/x86_64-linux-gcc.cmake
