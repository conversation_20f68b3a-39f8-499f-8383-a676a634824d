url: toolchain/aarch64-linux/gcc-arm-9.3.0-x86_64-aarch64-poky-linux-v3.18.0.tar.gz
md5: null
var:
  PATH: ${CROSSTOOL_ROOT}/toolchain/aarch64-linux/gcc-arm-9.3.0-x86_64-aarch64-poky-linux-v3.18.0/usr/bin/aarch64-poky-linux:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
  TOOLCHAIN_PREFIX: aarch64-poky-linux-
  CC: aarch64-poky-linux-gcc
  CXX: aarch64-poky-linux-g++
  FC: aarch64-poky-linux-gfortran
  RANLIB: aarch64-poky-linux-ranlib
  AR: aarch64-poky-linux-ar
  LD: aarch64-poky-linux-ld
  NM: aarch64-poky-linux-nm
  OBJDUMP: aarch64-poky-linux-objdump
  STRIP: aarch64-poky-linux-strip
  LDFLAGS: ""
  LD_LIBRARY_PATH: ""
  PKG_CONFIG_PATH: ""
  CMAKE_PREFIX_PATH: ""
  SYSROOT: $SHELL{${CROSSTOOL_ROOT}/toolchain/aarch64-linux/gcc-arm-9.3.0-x86_64-aarch64-poky-linux-v3.18.0/usr/bin/aarch64-poky-linux/aarch64-poky-linux-gcc -print-sysroot}
  AUTOTOOL_HOST: aarch64-poky-linux
  ARCH: aarch64
env:
  PATH: ${PATH}
  LANG: $ENV{LANG}
  TERM: $ENV{TERM}
  PWD: $ENV{PWD}
  SHELL: $ENV{SHELL}
  USER: $ENV{USER}
  HOME: $ENV{HOME}

  TOOLCHAIN_PREFIX: ${TOOLCHAIN_PREFIX}

  CC: ${CC}
  CXX: ${CXX}
  FC: ${FC}
  RANLIB: ${RANLIB}
  AR: ${AR}
  LD: ${LD}
  NM: ${NM}
  OBJDUMP: ${OBJDUMP}
  STRIP: ${STRIP}
  LDFLAGS: ${LDFLAGS}
  PKG_CONFIG_PATH: ${PKG_CONFIG_PATH}
  PKG_CONFIG_LIBDIR: ${PKG_CONFIG_LIBDIR}
  CMAKE_PREFIX_PATH: ${CMAKE_PREFIX_PATH}
  SYSROOT: ${SYSROOT}

arguments:
- tool_pattern: "cmake"
  arguments:
  - -DCMAKE_TOOLCHAIN_FILE=${CROSSTOOL_ROOT}/package/tools/cmake/toolchain_files/aarch64-linux-gcc.cmake
