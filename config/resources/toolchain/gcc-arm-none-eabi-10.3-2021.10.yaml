url: toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10.tar.gz
var:
  PATH: ${CROSSTOOL_ROOT}/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
  CC: ${CROSSTOOL_ROOT}/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc
  CXX: ${CROSSTOOL_ROOT}/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++
  FC: ""
  RANLIB: ${CROSSTOOL_ROOT}/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc-ranlib
  AR: ${CROSSTOOL_ROOT}/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-ar
  LD: ${CROSSTOOL_ROOT}/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-ld
  NM: ${CROSSTOOL_ROOT}/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc-nm
  OBJDUMP: ${CROSSTOOL_ROOT}/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-objdump
  STRIP: ${CROSSTOOL_ROOT}/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-strip
  LDFLAGS: "-L${CROSSTOOL_ROOT}/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib \
    -Wl,-rpath-link,${CROSSTOOL_ROOT}/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib \
    -Wl,--entry=Start \
    -Wl,--gc-sections \
    -Wl,--cref \
    ${LDFLAGS}"
  LD_LIBRARY_PATH: ""
  PKG_CONFIG_PATH: ""
  CMAKE_PREFIX_PATH: ""
  AUTOTOOL_HOST: arm-none-eabi
  ARCH: armv8-r
  CFLAGS: "-I${CROSSTOOL_ROOT}/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include \
    -marm \
    -mcpu=cortex-r52 \
    -gdwarf-2 \
    -std=c99 \
    -fverbose-asm \
    -O3 \
    -DNDEBUG \
    -Werror=return-type \
    -fno-omit-frame-pointer \
    ${CFLAGS}"
  CXXFLAGS: "-I${CROSSTOOL_ROOT}/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include \
    -marm \
    -mcpu=cortex-r52 \
    -gdwarf-2 \
    -std=c++14 \
    -fverbose-asm \
    -fno-exceptions \
    -fno-threadsafe-statics \
    -fno-rtti \
    -Wno-reorder \
    -O3 \
    -std=c++14 \
    -Werror=return-type \
    -fno-omit-frame-pointer \
    -fexceptions \
    ${CXXFLAGS}"

env:
  PATH: ${PATH}
  LANG: $ENV{LANG}
  TERM: $ENV{TERM}
  PWD: $ENV{PWD}
  SHELL: $ENV{SHELL}
  USER: $ENV{USER}
  HOME: $ENV{HOME}

  CC: ${CC}
  CXX: ${CXX}
  FC: ${FC}
  RANLIB: ${RANLIB}
  AR: ${AR}
  LD: ${LD}
  NM: ${NM}
  OBJDUMP: ${OBJDUMP}
  STRIP: ${STRIP}
  LDFLAGS: ${LDFLAGS}
  PKG_CONFIG_PATH: ${PKG_CONFIG_PATH}
  PKG_CONFIG_LIBDIR: ${PKG_CONFIG_LIBDIR}
  CMAKE_PREFIX_PATH: ${CMAKE_PREFIX_PATH}
  AUTOTOOL_HOST: ${AUTOTOOL_HOST}
  ARCH: ${ARCH}
  CFLAGS: ${CFLAGS}
  CXXFLAGS: ${CXXFLAGS}

arguments:
- tool_pattern: "cmake"
  arguments:
  - -DCMAKE_TOOLCHAIN_FILE=${CROSSTOOL_ROOT}/package/tools/cmake/toolchain_files/armv8-r-generic-gcc.cmake
  - -DZX_SOC_TOOLCHAIN=${CROSSTOOL_ROOT}/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10