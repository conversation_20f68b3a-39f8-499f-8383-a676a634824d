url: toolchain/armv7-r/ti-cgt-arm_20.2.5.LTS.tar.gz
var:
  PATH: ${CROSSTOOL_ROOT}/toolchain/armv7-r/ti-cgt-arm_20.2.5.LTS/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
  CC: armcl
  CXX: armcl
  FC: ""
  RANLIB: ""
  AR: ""
  LD: ""
  NM: ""
  OBJDUMP: ""
  STRIP: ""
  LDFLAGS: "-a \
    -cinit_compression=rle \
    --copy_compression=rle \
    --no_demangle \
    --compress_dwarf=on \
    --rom_model \
    --reread_libs \
    ${LDFLAGS}"
  LD_LIBRARY_PATH: ""
  PKG_CONFIG_PATH: ""
  CMAKE_PREFIX_PATH: ""
  AUTOTOOL_HOST: arm-none-eabi
  ARCH: armv7-r
  CFLAGS: "-I${CROSSTOOL_ROOT}/toolchain/armv7-r/ti-cgt-arm_20.2.5.LTS/include \
    -mv7r5 \
    --code_state=32 \
    --float_support=VFPv3D16 \
    -me \
    --abi=eabi \
    --c99 \
    --symdebug:none \
    --opt_level=3 \
    ${CFLAGS}"
  CXXFLAGS: "-I${CROSSTOOL_ROOT}/toolchain/armv7-r/ti-cgt-arm_20.2.5.LTS/include \
    -mv7r5 \
    --code_state=32 \
    --float_support=VFPv3D16 \
    -me \
    --abi=eabi \
    --c++14 \
    --symdebug:none \
    --opt_level=3 \
    ${CXXFLAGS}"

env:
  PATH: ${PATH}
  LANG: $ENV{LANG}
  TERM: $ENV{TERM}
  PWD: $ENV{PWD}
  SHELL: $ENV{SHELL}
  USER: $ENV{USER}
  HOME: $ENV{HOME}

  CC: ${CC}
  CXX: ${CXX}
  FC: ${FC}
  RANLIB: ${RANLIB}
  AR: ${AR}
  LD: ${LD}
  NM: ${NM}
  OBJDUMP: ${OBJDUMP}
  STRIP: ${STRIP}
  LDFLAGS: ${LDFLAGS}
  PKG_CONFIG_PATH: ${PKG_CONFIG_PATH}
  PKG_CONFIG_LIBDIR: ${PKG_CONFIG_LIBDIR}
  CMAKE_PREFIX_PATH: ${CMAKE_PREFIX_PATH}
  AUTOTOOL_HOST: ${AUTOTOOL_HOST}
  ARCH: ${ARCH}
  CFLAGS: ${CFLAGS}
  CXXFLAGS: ${CXXFLAGS}

arguments:
- tool_pattern: "cmake"
  arguments:
  - -DCMAKE_TOOLCHAIN_FILE=${CROSSTOOL_ROOT}/package/tools/cmake/toolchain_files/armv7-r-generic-gcc.cmake