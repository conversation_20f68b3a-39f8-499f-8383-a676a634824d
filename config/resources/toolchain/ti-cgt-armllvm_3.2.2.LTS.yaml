url: toolchain/armv7-r/ti-cgt-armllvm_3.2.2.LTS.tar.gz
var:
  PATH: ${CROSSTOOL_ROOT}/toolchain/armv7-r/ti-cgt-armllvm_3.2.2.LTS/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
  CC: tiarmclang
  CXX: tiarmclang
  FC: ""
  RANLIB: ""
  AR: ""
  LD: ""
  NM: ""
  OBJDUMP: ""
  STRIP: ""
  LDFLAGS: "-Wl,--cinit_compression=rle \
    -Wl,--compress_dwarf=on \
    -Wl,--copy_compression=rle \
    -Wl,--display_error_number \
    -Wl,--diag_suppress=10068 \
    -Wl,--absolute_exe \
    -Wl,--use_memcpy=fast \
    -Wl,--use_memset=fast \
    -Wl,--reread_libs \
    -Wl,--zero_init=on \
    -Wl,--rom_model \
    -Wl,--unused_section_elimination=on \
    ${LDFLAGS}"
  LD_LIBRARY_PATH: ""
  PKG_CONFIG_PATH: ""
  CMAKE_PREFIX_PATH: ""
  AUTOTOOL_HOST: arm-none-eabi
  ARCH: armv7-r
  CFLAGS: "-I${CROSSTOOL_ROOT}/toolchain/armv7-r/ti-cgt-armllvm_3.2.2.LTS/include \
    -marm \
    -mlittle-endian \
    -mfloat-abi=hard \
    -mfpu=vfpv3-d16 \
    -mcpu=cortex-r5 \
    -march=armv7-r \
    -gdwarf-3 \
    -std=gnu99 \
    ${CFLAGS}"
  CXXFLAGS: "-I${CROSSTOOL_ROOT}/toolchain/armv7-r/ti-cgt-armllvm_3.2.2.LTS/include \
    -marm \
    -mlittle-endian \
    -mfloat-abi=hard \
    -mfpu=vfpv3-d16 \
    -mcpu=cortex-r5 \
    -march=armv7-r \
    -gdwarf-3 \
    -std=c++14 \
    ${CXXFLAGS}"

env:
  PATH: ${PATH}
  LANG: $ENV{LANG}
  TERM: $ENV{TERM}
  PWD: $ENV{PWD}
  SHELL: $ENV{SHELL}
  USER: $ENV{USER}
  HOME: $ENV{HOME}

  CC: ${CC}
  CXX: ${CXX}
  FC: ${FC}
  RANLIB: ${RANLIB}
  AR: ${AR}
  LD: ${LD}
  NM: ${NM}
  OBJDUMP: ${OBJDUMP}
  STRIP: ${STRIP}
  LDFLAGS: ${LDFLAGS}
  PKG_CONFIG_PATH: ${PKG_CONFIG_PATH}
  PKG_CONFIG_LIBDIR: ${PKG_CONFIG_LIBDIR}
  CMAKE_PREFIX_PATH: ${CMAKE_PREFIX_PATH}
  AUTOTOOL_HOST: ${AUTOTOOL_HOST}
  ARCH: ${ARCH}
  CFLAGS: ${CFLAGS}
  CXXFLAGS: ${CXXFLAGS}

arguments:
- tool_pattern: "cmake"
  arguments:
  - -DCMAKE_TOOLCHAIN_FILE=${CROSSTOOL_ROOT}/package/tools/cmake/toolchain_files/armv7-r-generic-gcc.cmake