url: toolchain/armv8-r/ghs-202214.tar.gz
md5: null
var:
  PATH: ${CROSSTOOL_ROOT}/toolchain/armv8-r/ghs-202214/comp_202214;$ENV{PATH}
  TOOLCHAIN_PREFIX: ""
  CC: ccarm.exe
  CXX: ccarm.exe
  FC: ""
  RANLIB: ""
  AR: ccarm.exe
  ASM: ccarm.exe
  LD: ccarm.exe
  SREC: gsrec.exe
  NM: ""
  OBJDUMP: ""
  STRIP: ""
  LDFLAGS: ""
  LD_LIBRARY_PATH: ""
  PKG_CONFIG_PATH: ""
  CMAKE_PREFIX_PATH: ""
  SYSROOT: ""
  AUTOTOOL_HOST: armv8-r
  ARCH: armv8-r
env:
  PATH: ${PATH}
  LANG: $ENV{LANG}
  TERM: $ENV{TERM}
  PWD: $ENV{PWD}
  SHELL: $ENV{SHELL}
  USER: $ENV{USER}
  HOME: $ENV{HOME}

  TOOLCHAIN_PREFIX: ${TOOLCHAIN_PREFIX}

  CC: ${CC}
  CXX: ${CXX}
  FC: ${FC}
  RANLIB: ${RANLIB}
  AR: ${AR}
  ASM: ${ASM}
  LD: ${LD}
  SREC: ${SREC}
  NM: ${NM}
  OBJDUMP: ${OBJDUMP}
  STRIP: ${STRIP}
  LDFLAGS: ${LDFLAGS}
  PKG_CONFIG_PATH: ${PKG_CONFIG_PATH}
  PKG_CONFIG_LIBDIR: ${PKG_CONFIG_LIBDIR}
  CMAKE_PREFIX_PATH: ${CMAKE_PREFIX_PATH}
  SYSROOT: ${SYSROOT}
  PROCESSOR_ARCHITECTURE: $ENV{PROCESSOR_ARCHITECTURE}
  COMPUTERNAME: $ENV{COMPUTERNAME}
  SystemRoot: $ENV{SystemRoot}
  TEMP: $ENV{TEMP}
  TMP: $ENV{TMP}
  COMPILER_ROOT: ${CROSSTOOL_ROOT}/toolchain/armv8-r/ghs-202214/comp_202214

arguments:
- tool_pattern: "cmake"
  arguments:
  - -G 
  - "\"MinGW Makefiles\""
  - -DCMAKE_TOOLCHAIN_FILE=${CROSSTOOL_ROOT}/package/tools/cmake/toolchain_files/armv8-r-ghs.cmake
  - -DCMAKE_FIND_ROOT_PATH=${IMOBUILDER_TARGET_OUTPUT}
  - -DCMAKE_PREFIX_PATH=${IMOBUILDER_TARGET_OUTPUT}
  - -DBUILD_SHARED_LIBS=OFF