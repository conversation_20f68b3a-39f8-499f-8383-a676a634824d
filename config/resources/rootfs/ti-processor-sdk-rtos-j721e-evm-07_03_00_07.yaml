url: rootfs/ti-processor-sdk-rtos-j721e-evm-07_03_00_07.tar.gz
md5: null
var:
  SYSROOT: ${CROSSTOOL_ROOT}/rootfs/ti-processor-sdk-rtos-j721e-evm-07_03_00_07
  PKG_CONFIG_LIBDIR: "${CROSSTOOL_ROOT}/rootfs/ti-processor-sdk-rtos-j721e-evm-07_03_00_07/usr/lib/pkgconfig:\
  		     ${CROSSTOOL_ROOT}/rootfs/ti-processor-sdk-rtos-j721e-evm-07_03_00_07/usr/share/pkgconfig:\
  		     ${CROSSTOOL_ROOT}/rootfs/ti-processor-sdk-rtos-j721e-evm-07_03_00_07/usr/ext/lib/pkgconfig"
  LDFLAGS: "${LDFLAGS} \
            -Wl,-rpath-link,\
            ${CROSSTOOL_ROOT}/rootfs/ti-processor-sdk-rtos-j721e-evm-07_03_00_07/usr/lib"
  TARGET_LD_LIBRARY_PATH: "${CROSSTOOL_ROOT}/rootfs/ti-processor-sdk-rtos-j721e-evm-07_03_00_07/usr/lib:\
                           ${CROSSTOOL_ROOT}/rootfs/ti-processor-sdk-rtos-j721e-evm-07_03_00_07/usr/ext/lib"
  CC: ${CC} --sysroot=${CROSSTOOL_ROOT}/rootfs/ti-processor-sdk-rtos-j721e-evm-07_03_00_07
  CXX: ${CXX} --sysroot=${CROSSTOOL_ROOT}/rootfs/ti-processor-sdk-rtos-j721e-evm-07_03_00_07
env:
  PKG_CONFIG_LIBDIR: ${PKG_CONFIG_LIBDIR}
  PKG_CONFIG_SYSROOT_DIR: ${SYSROOT}
  LDFLAGS: ${LDFLAGS}
  SYSROOT: ${SYSROOT}
  CC: ${CC}
  CXX: ${CXX}
