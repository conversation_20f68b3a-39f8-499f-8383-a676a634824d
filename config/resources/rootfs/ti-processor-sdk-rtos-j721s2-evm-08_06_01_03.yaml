url: rootfs/ti-processor-sdk-rtos-j721s2-evm-08_06_01_03.tar.gz
md5: null
var:
  SYSROOT: ${CROSSTOOL_ROOT}/rootfs/ti-processor-sdk-rtos-j721s2-evm-08_06_01_03
  PKG_CONFIG_LIBDIR: "${CROSSTOOL_ROOT}/rootfs/ti-processor-sdk-rtos-j721s2-evm-08_06_01_03/usr/lib/pkgconfig:\
  		     ${CROSSTOOL_ROOT}/rootfs/ti-processor-sdk-rtos-j721s2-evm-08_06_01_03/usr/share/pkgconfig:\
  		     ${CROSSTOOL_ROOT}/rootfs/ti-processor-sdk-rtos-j721s2-evm-08_06_01_03/usr/ext/lib/pkgconfig"
  LDFLAGS: "${LDFLAGS} \
            -Wl,-rpath-link,\
            ${CROSSTOOL_ROOT}/rootfs/ti-processor-sdk-rtos-j721s2-evm-08_06_01_03/usr/lib"
  TARGET_LD_LIBRARY_PATH: "${CROSSTOOL_ROOT}/rootfs/ti-processor-sdk-rtos-j721s2-evm-08_06_01_03/usr/lib:\
                           ${CROSSTOOL_ROOT}/rootfs/ti-processor-sdk-rtos-j721s2-evm-08_06_01_03/usr/ext/lib"
  CC: ${CC} --sysroot=${CROSSTOOL_ROOT}/rootfs/ti-processor-sdk-rtos-j721s2-evm-08_06_01_03
  CXX: ${CXX} --sysroot=${CROSSTOOL_ROOT}/rootfs/ti-processor-sdk-rtos-j721s2-evm-08_06_01_03
env:
  PKG_CONFIG_LIBDIR: ${PKG_CONFIG_LIBDIR}
  PKG_CONFIG_SYSROOT_DIR: ${SYSROOT}
  LDFLAGS: ${LDFLAGS}
  SYSROOT: ${SYSROOT}
  CC: ${CC}
  CXX: ${CXX}
