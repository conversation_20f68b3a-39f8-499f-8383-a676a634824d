url: rootfs/rootfs-sdk-focal_0.0.1.20240530_all.tar.gz
md5: null
var:
  SYSROOT: ${CROSSTOOL_ROOT}/rootfs/rootfs-sdk-focal_0.0.1.20240530_all
  PKG_CONFIG_LIBDIR: ${CROSSTOOL_ROOT}/rootfs/rootfs-sdk-focal_0.0.1.20240530_all/usr/lib/pkgconfig:${CROSSTOOL_ROOT}/rootfs/rootfs-sdk-focal_0.0.1.20240530_all/usr/share/pkgconfig:${CROSSTOOL_ROOT}/rootfs/rootfs-sdk-focal_0.0.1.20240530_all/usr/lib/aarch64-linux-gnu/pkgconfig
  LDFLAGS: "${LDFLAGS} \
            -Wl,-rpath-link,\
            ${CROSSTOOL_ROOT}/rootfs/rootfs-sdk-focal_0.0.1.20240530_all/usr/lib/aarch64-linux-gnu"
  TARGET_LD_LIBRARY_PATH: "${CROSSTOOL_ROOT}/rootfs/rootfs-sdk-focal_0.0.1.20240530_all/usr/lib:\
                           ${CROSSTOOL_ROOT}/rootfs/rootfs-sdk-focal_0.0.1.20240530_all/usr/lib/aarch64-linux-gnu"
  CC: ${CC} --sysroot=${CROSSTOOL_ROOT}/rootfs/rootfs-sdk-focal_0.0.1.20240530_all
  CXX: ${CXX} --sysroot=${CROSSTOOL_ROOT}/rootfs/rootfs-sdk-focal_0.0.1.20240530_all
env:
  PKG_CONFIG_LIBDIR: ${PKG_CONFIG_LIBDIR}
  PKG_CONFIG_SYSROOT_DIR: ${SYSROOT}
  LDFLAGS: ${LDFLAGS}
  SYSROOT: ${SYSROOT}
  CC: ${CC}
  CXX: ${CXX}
