url: rootfs/aarch64-poky-linux_rootfs_v3.33.0.tar.gz
md5: null
var:
  SYSROOT: ${CROSSTOOL_ROOT}/rootfs/aarch64-poky-linux_rootfs_v3.33.0
  PKG_CONFIG_LIBDIR: "${CROSSTOOL_ROOT}/rootfs/aarch64-poky-linux_rootfs_v3.33.0/usr/lib/pkgconfig:\
  		     ${CROSSTOOL_ROOT}/rootfs/aarch64-poky-linux_rootfs_v3.33.0/usr/share/pkgconfig:\
  		     ${CROSSTOOL_ROOT}/rootfs/aarch64-poky-linux_rootfs_v3.33.0/usr/ext/lib/pkgconfig"
  LDFLAGS: "${LDFLAGS} \
            -Wl,-rpath-link,\
            ${CROSSTOOL_ROOT}/rootfs/aarch64-poky-linux_rootfs_v3.33.0/usr/lib"
  TARGET_LD_LIBRARY_PATH: "${CROSSTOOL_ROOT}/rootfs/aarch64-poky-linux_rootfs_v3.33.0/usr/lib:\
                           ${CROSSTOOL_ROOT}/rootfs/aarch64-poky-linux_rootfs_v3.33.0/usr/ext/lib"
  CC: ${CC} --sysroot=${CROSSTOOL_ROOT}/rootfs/aarch64-poky-linux_rootfs_v3.33.0
  CXX: ${CXX} --sysroot=${CROSSTOOL_ROOT}/rootfs/aarch64-poky-linux_rootfs_v3.33.0
env:
  PKG_CONFIG_LIBDIR: ${PKG_CONFIG_LIBDIR}
  PKG_CONFIG_SYSROOT_DIR: ${SYSROOT}
  LDFLAGS: ${LDFLAGS}
  SYSROOT: ${SYSROOT}
  CC: ${CC}
  CXX: ${CXX}
