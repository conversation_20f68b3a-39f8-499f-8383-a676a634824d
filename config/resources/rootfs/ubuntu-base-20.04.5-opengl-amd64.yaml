url: rootfs/ubuntu-base-20.04.5-opengl-amd64.tar.gz
md5: null
var:
  SYSROOT: ${CROSSTOOL_ROOT}/rootfs/ubuntu-base-20.04.5-opengl-amd64
  PKG_CONFIG_LIBDIR: ${CROSSTOOL_ROOT}/rootfs/ubuntu-base-20.04.5-opengl-amd64/usr/lib/pkgconfig:${CROSSTOOL_ROOT}/rootfs/ubuntu-base-20.04.5-opengl-amd64/usr/share/pkgconfig:${CROSSTOOL_ROOT}/rootfs/ubuntu-base-20.04.5-opengl-amd64/usr/lib/x86_64-linux-gnu/pkgconfig
  LDFLAGS: "${LDFLAGS} \
            -Wl,-rpath-link,\
            ${CROSSTOOL_ROOT}/rootfs/ubuntu-base-20.04.5-opengl-amd64/usr/lib/x86_64-linux-gnu:\
            ${CROSSTOOL_ROOT}/rootfs/ubuntu-base-20.04.5-opengl-amd64/lib/x86_64-linux-gnu"
  TARGET_LD_LIBRARY_PATH: "${CROSSTOOL_ROOT}/rootfs/ubuntu-base-20.04.5-opengl-amd64/usr/lib:\
                           ${CROSSTOOL_ROOT}/rootfs/ubuntu-base-20.04.5-opengl-amd64/usr/lib/x86_64-linux-gnu:\
                           ${CROSSTOOL_ROOT}/rootfs/ubuntu-base-20.04.5-opengl-amd64/lib:\
                           ${CROSSTOOL_ROOT}/rootfs/ubuntu-base-20.04.5-opengl-amd64/lib/x86_64-linux-gnu"
  CC: ${CC} --sysroot=${CROSSTOOL_ROOT}/rootfs/ubuntu-base-20.04.5-opengl-amd64
  CXX: ${CXX} --sysroot=${CROSSTOOL_ROOT}/rootfs/ubuntu-base-20.04.5-opengl-amd64
env:
  PKG_CONFIG_LIBDIR: ${PKG_CONFIG_LIBDIR}
  PKG_CONFIG_SYSROOT_DIR: ${SYSROOT}
  LDFLAGS: ${LDFLAGS}
  SYSROOT: ${SYSROOT}
  CC: ${CC}
  CXX: ${CXX}
