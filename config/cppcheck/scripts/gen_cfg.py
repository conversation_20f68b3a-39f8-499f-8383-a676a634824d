import os
import sys
import argparse
import subprocess

def extract_string(input_string):
    if len(input_string) >= 2 and input_string[0] == '"' and input_string[-1] == '"':
        return input_string[1:-1]
    else:
        return input_string

def parse_macro_definition(macro_str):
    parts = macro_str.split()
    name = parts[1]
    value = " ".join(parts[2:]).strip()
    return '<define name="{}" value="{}"/>'.format(extract_string(name), extract_string(value))

def generate_cfg():
    parser = argparse.ArgumentParser(description='generate cfg file')
    parser.add_argument('--output', help='Specify the config file path')
    args = parser.parse_args()
    output_file = args.output

    if os.path.exists(output_file):
        sys.exit(0)

    # Get macro definition
    (retcode, value) = subprocess.getstatusoutput("$CC -E -dM - < /dev/null")
    macro_definitions = value if retcode == 0 else None 

    # Process macro definition
    if macro_definitions:
        output_lines = []
        for macro_str in macro_definitions.split('\n'):
            output_lines.append(parse_macro_definition(macro_str))

        # Writes the result to the output file
        with open(output_file, 'w') as file:
            file.write(f'<?xml version="1.0"?>\n')
            file.write(f'<def format="2">\n')
            for line in output_lines:
                file.write(f"  {line}\n")
            file.write(f'</def>\n')

if __name__ == "__main__":
    generate_cfg()
