# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.30

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake

# The command to remove a file.
RM = /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/code/mcu_app_YT_workspace/src/imo_com_micro

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro

# Include any dependencies generated for this target.
include ddk/CMakeFiles/imo_com_ddk.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include ddk/CMakeFiles/imo_com_ddk.dir/compiler_depend.make

# Include the progress variables for this target.
include ddk/CMakeFiles/imo_com_ddk.dir/progress.make

# Include the compile flags for this target's objects.
include ddk/CMakeFiles/imo_com_ddk.dir/flags.make

ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_mempool.cpp.obj: ddk/CMakeFiles/imo_com_ddk.dir/flags.make
ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_mempool.cpp.obj: /home/<USER>/code/mcu_app_YT_workspace/src/imo_com_micro/ddk/src/ddk_mempool.cpp
ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_mempool.cpp.obj: ddk/CMakeFiles/imo_com_ddk.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_mempool.cpp.obj"
	cd /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro/ddk && /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_mempool.cpp.obj -MF CMakeFiles/imo_com_ddk.dir/src/ddk_mempool.cpp.obj.d -o CMakeFiles/imo_com_ddk.dir/src/ddk_mempool.cpp.obj -c /home/<USER>/code/mcu_app_YT_workspace/src/imo_com_micro/ddk/src/ddk_mempool.cpp

ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_mempool.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/imo_com_ddk.dir/src/ddk_mempool.cpp.i"
	cd /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro/ddk && /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code/mcu_app_YT_workspace/src/imo_com_micro/ddk/src/ddk_mempool.cpp > CMakeFiles/imo_com_ddk.dir/src/ddk_mempool.cpp.i

ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_mempool.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/imo_com_ddk.dir/src/ddk_mempool.cpp.s"
	cd /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro/ddk && /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code/mcu_app_YT_workspace/src/imo_com_micro/ddk/src/ddk_mempool.cpp -o CMakeFiles/imo_com_ddk.dir/src/ddk_mempool.cpp.s

ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_monitor.cpp.obj: ddk/CMakeFiles/imo_com_ddk.dir/flags.make
ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_monitor.cpp.obj: /home/<USER>/code/mcu_app_YT_workspace/src/imo_com_micro/ddk/src/ddk_monitor.cpp
ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_monitor.cpp.obj: ddk/CMakeFiles/imo_com_ddk.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_monitor.cpp.obj"
	cd /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro/ddk && /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_monitor.cpp.obj -MF CMakeFiles/imo_com_ddk.dir/src/ddk_monitor.cpp.obj.d -o CMakeFiles/imo_com_ddk.dir/src/ddk_monitor.cpp.obj -c /home/<USER>/code/mcu_app_YT_workspace/src/imo_com_micro/ddk/src/ddk_monitor.cpp

ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_monitor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/imo_com_ddk.dir/src/ddk_monitor.cpp.i"
	cd /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro/ddk && /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code/mcu_app_YT_workspace/src/imo_com_micro/ddk/src/ddk_monitor.cpp > CMakeFiles/imo_com_ddk.dir/src/ddk_monitor.cpp.i

ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_monitor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/imo_com_ddk.dir/src/ddk_monitor.cpp.s"
	cd /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro/ddk && /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code/mcu_app_YT_workspace/src/imo_com_micro/ddk/src/ddk_monitor.cpp -o CMakeFiles/imo_com_ddk.dir/src/ddk_monitor.cpp.s

# Object files for target imo_com_ddk
imo_com_ddk_OBJECTS = \
"CMakeFiles/imo_com_ddk.dir/src/ddk_mempool.cpp.obj" \
"CMakeFiles/imo_com_ddk.dir/src/ddk_monitor.cpp.obj"

# External object files for target imo_com_ddk
imo_com_ddk_EXTERNAL_OBJECTS =

ddk/libimo_com_ddk.a: ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_mempool.cpp.obj
ddk/libimo_com_ddk.a: ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_monitor.cpp.obj
ddk/libimo_com_ddk.a: ddk/CMakeFiles/imo_com_ddk.dir/build.make
ddk/libimo_com_ddk.a: ddk/CMakeFiles/imo_com_ddk.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX static library libimo_com_ddk.a"
	cd /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro/ddk && $(CMAKE_COMMAND) -P CMakeFiles/imo_com_ddk.dir/cmake_clean_target.cmake
	cd /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro/ddk && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/imo_com_ddk.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
ddk/CMakeFiles/imo_com_ddk.dir/build: ddk/libimo_com_ddk.a
.PHONY : ddk/CMakeFiles/imo_com_ddk.dir/build

ddk/CMakeFiles/imo_com_ddk.dir/clean:
	cd /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro/ddk && $(CMAKE_COMMAND) -P CMakeFiles/imo_com_ddk.dir/cmake_clean.cmake
.PHONY : ddk/CMakeFiles/imo_com_ddk.dir/clean

ddk/CMakeFiles/imo_com_ddk.dir/depend:
	cd /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/code/mcu_app_YT_workspace/src/imo_com_micro /home/<USER>/code/mcu_app_YT_workspace/src/imo_com_micro/ddk /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro/ddk /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro/ddk/CMakeFiles/imo_com_ddk.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : ddk/CMakeFiles/imo_com_ddk.dir/depend

