
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/code/mcu_app_YT_workspace/src/imo_com_micro/ddk/src/ddk_mempool.cpp" "ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_mempool.cpp.obj" "gcc" "ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_mempool.cpp.obj.d"
  "/home/<USER>/code/mcu_app_YT_workspace/src/imo_com_micro/ddk/src/ddk_monitor.cpp" "ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_monitor.cpp.obj" "gcc" "ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_monitor.cpp.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
