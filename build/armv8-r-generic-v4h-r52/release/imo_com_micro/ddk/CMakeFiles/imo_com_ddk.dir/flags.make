# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.30

# compile CXX with /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++
CXX_DEFINES = -DZX_COMPUTE_TARGET_RPU=1

CXX_INCLUDES = -I/home/<USER>/code/mcu_app_YT_workspace/src/imo_com_micro/ddk/inc

CXX_FLAGS = -I/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include -marm -mcpu=cortex-r52 -gdwarf-2 -std=c++14 -fverbose-asm -fno-exceptions -fno-threadsafe-statics -fno-rtti -Wno-reorder -O3 -std=c++14 -Werror=return-type -fno-omit-frame-pointer -fexceptions -O3 -DNDEBUG

