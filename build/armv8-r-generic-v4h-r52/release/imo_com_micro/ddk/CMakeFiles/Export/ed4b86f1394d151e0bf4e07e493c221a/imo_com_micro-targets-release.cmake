#----------------------------------------------------------------
# Generated CMake target import file for configuration "RELEASE".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "imo_com_micro::imo_com_ddk" for configuration "RELEASE"
set_property(TARGET imo_com_micro::imo_com_ddk APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(imo_com_micro::imo_com_ddk PROPERTIES
  IMPORTED_LINK_INTERFACE_LANGUAGES_RELEASE "CXX"
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/libimo_com_ddk.a"
  )

list(APPEND _cmake_import_check_targets imo_com_micro::imo_com_ddk )
list(APPEND _cmake_import_check_files_for_imo_com_micro::imo_com_ddk "${_IMPORT_PREFIX}/lib/libimo_com_ddk.a" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
