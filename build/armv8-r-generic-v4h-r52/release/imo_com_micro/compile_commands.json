[{"directory": "/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro/ddk", "command": "/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++ -DZX_COMPUTE_TARGET_RPU=1 -I/home/<USER>/code/mcu_app_YT_workspace/src/imo_com_micro/ddk/inc -I/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include -marm -mcpu=cortex-r52 -gdwarf-2 -std=c++14 -fverbose-asm -fno-exceptions -fno-threadsafe-statics -fno-rtti -Wno-reorder -O3 -std=c++14 -Werror=return-type -fno-omit-frame-pointer -fexceptions -O3 -DNDEBUG -o CMakeFiles/imo_com_ddk.dir/src/ddk_mempool.cpp.obj -c /home/<USER>/code/mcu_app_YT_workspace/src/imo_com_micro/ddk/src/ddk_mempool.cpp", "file": "/home/<USER>/code/mcu_app_YT_workspace/src/imo_com_micro/ddk/src/ddk_mempool.cpp", "output": "ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_mempool.cpp.obj"}, {"directory": "/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro/ddk", "command": "/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++ -DZX_COMPUTE_TARGET_RPU=1 -I/home/<USER>/code/mcu_app_YT_workspace/src/imo_com_micro/ddk/inc -I/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include -marm -mcpu=cortex-r52 -gdwarf-2 -std=c++14 -fverbose-asm -fno-exceptions -fno-threadsafe-statics -fno-rtti -Wno-reorder -O3 -std=c++14 -Werror=return-type -fno-omit-frame-pointer -fexceptions -O3 -DNDEBUG -o CMakeFiles/imo_com_ddk.dir/src/ddk_monitor.cpp.obj -c /home/<USER>/code/mcu_app_YT_workspace/src/imo_com_micro/ddk/src/ddk_monitor.cpp", "file": "/home/<USER>/code/mcu_app_YT_workspace/src/imo_com_micro/ddk/src/ddk_monitor.cpp", "output": "ddk/CMakeFiles/imo_com_ddk.dir/src/ddk_monitor.cpp.obj"}]