# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.30

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake

# The command to remove a file.
RM = /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/code/mcu_app_YT_workspace/src/imo_com_micro

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: ddk/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: ddk/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: ddk/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory ddk

# Recursive "all" directory target.
ddk/all: ddk/CMakeFiles/imo_com_ddk.dir/all
.PHONY : ddk/all

# Recursive "preinstall" directory target.
ddk/preinstall:
.PHONY : ddk/preinstall

# Recursive "clean" directory target.
ddk/clean: ddk/CMakeFiles/imo_com_ddk.dir/clean
.PHONY : ddk/clean

#=============================================================================
# Target rules for target ddk/CMakeFiles/imo_com_ddk.dir

# All Build rule for target.
ddk/CMakeFiles/imo_com_ddk.dir/all:
	$(MAKE) $(MAKESILENT) -f ddk/CMakeFiles/imo_com_ddk.dir/build.make ddk/CMakeFiles/imo_com_ddk.dir/depend
	$(MAKE) $(MAKESILENT) -f ddk/CMakeFiles/imo_com_ddk.dir/build.make ddk/CMakeFiles/imo_com_ddk.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro/CMakeFiles --progress-num=1,2,3 "Built target imo_com_ddk"
.PHONY : ddk/CMakeFiles/imo_com_ddk.dir/all

# Build rule for subdir invocation for target.
ddk/CMakeFiles/imo_com_ddk.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro/CMakeFiles 3
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ddk/CMakeFiles/imo_com_ddk.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/imo_com_micro/CMakeFiles 0
.PHONY : ddk/CMakeFiles/imo_com_ddk.dir/rule

# Convenience name for target.
imo_com_ddk: ddk/CMakeFiles/imo_com_ddk.dir/rule
.PHONY : imo_com_ddk

# clean rule for target.
ddk/CMakeFiles/imo_com_ddk.dir/clean:
	$(MAKE) $(MAKESILENT) -f ddk/CMakeFiles/imo_com_ddk.dir/build.make ddk/CMakeFiles/imo_com_ddk.dir/clean
.PHONY : ddk/CMakeFiles/imo_com_ddk.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

