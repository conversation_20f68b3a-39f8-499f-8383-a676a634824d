//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
//  Copyright (c) by iMotion AI. All rights reserved.
//=============================================================================
//  D E S C R I P T I O N
//-----------------------------------------------------------------------------
//  Project Name: IDC2
//  Target System(s):
//  Compiler(s):
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//  Name:
//=============================================================================

#pragma once

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <unistd.h>
#include <getopt.h>
#include <time.h>
#include <signal.h>
#include <string.h>
#include <pthread.h>
#include <sys/prctl.h>
#include <thread>

#ifndef ZX_PRJ_CPJ_ICAR_05
#define ZX_PRJ_CPJ_ICAR_05
#endif

#ifndef PREPROC_CAM_CFG_FOLDER
#if defined(ZX_PRJ_CPJ_XPHT_X3)
#define PREPROC_CAM_CFG_FOLDER "cfg/xpht_x3"
#elif defined(ZX_PRJ_CPJ_ICAR_05)
#define PREPROC_CAM_CFG_FOLDER "cfg/icar05_11V_X8B"
#elif defined(ZX_PRJ_CPJ_CHRY_PF1)
#define PREPROC_CAM_CFG_FOLDER "cfg/platform"
#else
#define PREPROC_CAM_CFG_FOLDER "cfg/xpht_x3"
#endif
#endif // PREPROC_CAM_CFG_FOLDER
