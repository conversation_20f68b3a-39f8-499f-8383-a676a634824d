# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.30

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake

# The command to remove a file.
RM = /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg

# Utility rule file for zos_msg_zosidl_gen.

# Include any custom commands dependencies for this target.
include CMakeFiles/zos_msg_zosidl_gen.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/zos_msg_zosidl_gen.dir/progress.make

CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/ccw/codingmgr_var_output.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/common/common.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/common/enumDemo.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/common/timestamp.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/dalo/image/gdc_image.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/dalo/system_monitor/system_monitor.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/dalo/version/system_version.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/driving/driving_activesafety/as_debugVariablesFca.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/driving/driving_activesafety/as_debugVariablesFcaEgoContext.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/driving/driving_activesafety/as_evaluationOutput.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/driving/driving_activesafety/as_fusedObjectInfo.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/driving/driving_activesafety/as_hostVehicleInfo.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/driving/driving_apps/driving_apps_interfaces.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/driving/driving_apps/driving_automaton_state.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/driving/driving_apps/driving_databack_trigger.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/driving/driving_apps/driving_hmi_command.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/driving/driving_assist/driving_assist_func.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/driving/driving_assist/driving_assist_func_evi.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/driving/driving_assist/driving_assist_func_lda.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/driving/driving_assist/driving_assist_func_pla.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/driving/driving_assist/driving_assist_func_sem.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/driving/driving_assist/driving_assist_func_tos.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/driving/driving_assist/driving_corner_radar.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/driving/driving_control/DrivingControl.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/driving/driving_control/DrivingLateralControl.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/driving/driving_control/DrivingLongitudinalControl.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/driving/driving_control/DrivingVehicleLongitudinalControl.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/driving/driving_planning/planning_result.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/geometry/box.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/geometry/line.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/geometry/point.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/geometry/pose.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/geometry/rectangle.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/geometry/rot.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/iEvm/_gen/zx_sys_evm_cfg_Ethcfg.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/iEvm/_gen/zx_sys_evm_cfg_HCTcfg.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/iEvm/_gen/zx_sys_evm_cfg_Mcucfg.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/iEvm/_gen/zx_sys_evm_cfg_Reservedcfg.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/iEvm/_gen/zx_sys_evm_cfg_SensAK2cfg.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/iEvm/_gen/zx_sys_evm_cfg_SensLcfg.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/iEvm/_gen/zx_sys_evm_cfg_cameracfg.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/iEvm/evm_cfg_FidCfg.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/iEvm/zx_sys_evm_cfg_fimcfg.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/iEvm/zx_sys_evm_events.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/iEvm/zx_sys_evm_interfaces.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/iEvm/zx_sys_evm_ipc_cfg_interface.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/ipc/imuInfo.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/ipc/ipcCommunicateData.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/ipc/ipc_calib_interface.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/ipc/ipc_cfg_interface.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/ipc/ipc_proto/ipcAdccFunctionProto.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/ipc/ipc_proto/ipcAdccLaneLinesProto.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/ipc/ipc_proto/ipcAdccNaviPathsProto.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/ipc/ipc_proto/ipcAdccObjectsProto.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/ipc/ipc_proto/ipcAdccObstaclesProto.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/ipc/ipc_proto/ipcAdccSettingProto.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/ipc/ipc_proto/ipcPadHeaderProto.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/ipc/radarObject.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/ipc/ussPointInfo.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/ipc/valInPilotChassis.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/ipc/valInPilotVehicle.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/ipc/valInoutputcpj.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/ipc/valInoutputpf.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/ipc/vhmAbst2d.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/iuni/iuniHmiOutput.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/localization/odometry.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/map/app_status.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/map/local_map_graph.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/map/local_map_graph_boundary.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/map/local_map_graph_common.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/map/local_map_graph_extension.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/map/local_map_graph_lane.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/map/local_map_graph_link.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/map/local_map_graph_objects.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/navigation/navigation.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/navigation/route_guidance.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/numerics/matrix.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/numerics/vector.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/park_dr/vhm_abst_output.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/parking/parkapp/parkAciOutput.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/parking/parkapp/parkBaseType.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/parking/parkapp/parkControlOutput.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/parking/parkapp/parkDbgOutput.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/parking/parkapp/parkHmiOutput.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/parking/parkapp/parkMrgrOutput.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/parking/parkcontrol/ParkCtrlCmd.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/parking/planning/path.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/perception/calibration/calibDcomReq.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/perception/calibration/calibDcomResult.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/perception/calibration/calibOfPoint.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/perception/calibration/calib_info.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/perception/calibration/calibration.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/perception/common/common.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/perception/common/ihbc.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/perception/common/laneline3d.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/perception/common/object3d.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/perception/common/occupancy3d.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/perception/common/parking_slot3d.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/perception/common/scene.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/perception/common/traffic_light.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/perception/common/traffic_sign.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/perception/inference/dnn_base.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/perception/inference/dnn_op.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/perception/inference/inference.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/perception/perception.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/perception/preprocess/cam_capture/cam_capture_common.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/perception/preprocess/cam_capture/cam_capture_data_type.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/perception/preprocess/preprocess.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/predicton/prediction.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/sensors/GnssIns.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/sensors/imuInfo.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/sensors/lidar.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/sync/sync_message.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/uss_perception/uss_data_backflow.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/uss_perception/uss_data_output.hpp
CMakeFiles/zos_msg_zosidl_gen: include/zos_msg/uss_perception/uss_hardware_input.hpp
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "building zos_msg"

include/zos_msg/ccw/codingmgr_var_output.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ccw/codingmgr_var_output.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ccw/codingmgr_var_output.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ccw
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ccw/codingmgr_var_output.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ccw -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/common/common.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/common/common.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/common/common.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/common
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/common/common.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/common -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/common/enumDemo.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/common/enumDemo.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/common/enumDemo.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/common
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/common/enumDemo.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/common -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/common/timestamp.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/common/timestamp.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/common/timestamp.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/common
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/common/timestamp.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/common -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/dalo/image/gdc_image.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/dalo/image/gdc_image.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/dalo/image/gdc_image.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/dalo/image
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/dalo/image/gdc_image.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/dalo/image -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/dalo/system_monitor/system_monitor.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/dalo/system_monitor/system_monitor.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/dalo/system_monitor/system_monitor.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/dalo/system_monitor
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/dalo/system_monitor/system_monitor.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/dalo/system_monitor -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/dalo/version/system_version.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/dalo/version/system_version.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/dalo/version/system_version.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/dalo/version
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/dalo/version/system_version.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/dalo/version -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/driving/driving_activesafety/as_debugVariablesFca.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_activesafety/as_debugVariablesFca.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_activesafety/as_debugVariablesFca.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_activesafety
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_activesafety/as_debugVariablesFca.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_activesafety -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/driving/driving_activesafety/as_debugVariablesFcaEgoContext.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_activesafety/as_debugVariablesFcaEgoContext.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_activesafety/as_debugVariablesFcaEgoContext.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_activesafety
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_activesafety/as_debugVariablesFcaEgoContext.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_activesafety -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/driving/driving_activesafety/as_evaluationOutput.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_activesafety/as_evaluationOutput.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_activesafety/as_evaluationOutput.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_activesafety
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_activesafety/as_evaluationOutput.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_activesafety -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/driving/driving_activesafety/as_fusedObjectInfo.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_activesafety/as_fusedObjectInfo.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_activesafety/as_fusedObjectInfo.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_activesafety
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_activesafety/as_fusedObjectInfo.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_activesafety -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/driving/driving_activesafety/as_hostVehicleInfo.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_activesafety/as_hostVehicleInfo.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_activesafety/as_hostVehicleInfo.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_activesafety
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_activesafety/as_hostVehicleInfo.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_activesafety -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/driving/driving_apps/driving_apps_interfaces.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_apps/driving_apps_interfaces.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_apps/driving_apps_interfaces.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_apps
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_apps/driving_apps_interfaces.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_apps -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/driving/driving_apps/driving_automaton_state.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_apps/driving_automaton_state.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_apps/driving_automaton_state.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_apps
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_apps/driving_automaton_state.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_apps -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/driving/driving_apps/driving_databack_trigger.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_apps/driving_databack_trigger.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_apps/driving_databack_trigger.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_apps
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_apps/driving_databack_trigger.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_apps -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/driving/driving_apps/driving_hmi_command.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_apps/driving_hmi_command.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_apps/driving_hmi_command.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_apps
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_apps/driving_hmi_command.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_apps -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/driving/driving_assist/driving_assist_func.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_assist/driving_assist_func.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_assist/driving_assist_func.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_assist
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_assist/driving_assist_func.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_assist -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/driving/driving_assist/driving_assist_func_evi.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_assist/driving_assist_func_evi.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_assist/driving_assist_func_evi.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_assist
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_assist/driving_assist_func_evi.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_assist -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/driving/driving_assist/driving_assist_func_lda.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_assist/driving_assist_func_lda.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_assist/driving_assist_func_lda.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_assist
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_assist/driving_assist_func_lda.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_assist -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/driving/driving_assist/driving_assist_func_pla.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_assist/driving_assist_func_pla.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_assist/driving_assist_func_pla.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_assist
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_assist/driving_assist_func_pla.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_assist -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/driving/driving_assist/driving_assist_func_sem.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_assist/driving_assist_func_sem.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_assist/driving_assist_func_sem.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_assist
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_assist/driving_assist_func_sem.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_assist -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/driving/driving_assist/driving_assist_func_tos.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_assist/driving_assist_func_tos.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_assist/driving_assist_func_tos.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_assist
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_assist/driving_assist_func_tos.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_assist -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/driving/driving_assist/driving_corner_radar.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_assist/driving_corner_radar.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_assist/driving_corner_radar.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_assist
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_assist/driving_corner_radar.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_assist -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/driving/driving_control/DrivingControl.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_control/DrivingControl.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_control/DrivingControl.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_control
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_control/DrivingControl.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_control -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/driving/driving_control/DrivingLateralControl.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_control/DrivingLateralControl.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_control/DrivingLateralControl.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_control
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_control/DrivingLateralControl.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_control -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/driving/driving_control/DrivingLongitudinalControl.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_control/DrivingLongitudinalControl.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_control/DrivingLongitudinalControl.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_control
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_control/DrivingLongitudinalControl.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_control -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/driving/driving_control/DrivingVehicleLongitudinalControl.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_control/DrivingVehicleLongitudinalControl.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_control/DrivingVehicleLongitudinalControl.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_control
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_control/DrivingVehicleLongitudinalControl.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_control -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/driving/driving_planning/planning_result.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_planning/planning_result.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_planning/planning_result.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_planning
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/driving/driving_planning/planning_result.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/driving/driving_planning -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/geometry/box.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/geometry/box.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/geometry/box.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/geometry
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/geometry/box.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/geometry -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/geometry/line.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/geometry/line.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/geometry/line.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/geometry
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/geometry/line.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/geometry -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/geometry/point.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/geometry/point.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/geometry/point.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/geometry
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/geometry/point.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/geometry -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/geometry/pose.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/geometry/pose.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/geometry/pose.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/geometry
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/geometry/pose.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/geometry -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/geometry/rectangle.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/geometry/rectangle.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/geometry/rectangle.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/geometry
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/geometry/rectangle.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/geometry -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/geometry/rot.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/geometry/rot.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/geometry/rot.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/geometry
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/geometry/rot.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/geometry -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/iEvm/_gen/zx_sys_evm_cfg_Ethcfg.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/_gen/zx_sys_evm_cfg_Ethcfg.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/_gen/zx_sys_evm_cfg_Ethcfg.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm/_gen
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/_gen/zx_sys_evm_cfg_Ethcfg.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm/_gen -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/iEvm/_gen/zx_sys_evm_cfg_HCTcfg.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/_gen/zx_sys_evm_cfg_HCTcfg.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/_gen/zx_sys_evm_cfg_HCTcfg.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm/_gen
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/_gen/zx_sys_evm_cfg_HCTcfg.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm/_gen -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/iEvm/_gen/zx_sys_evm_cfg_Mcucfg.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/_gen/zx_sys_evm_cfg_Mcucfg.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/_gen/zx_sys_evm_cfg_Mcucfg.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm/_gen
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/_gen/zx_sys_evm_cfg_Mcucfg.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm/_gen -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/iEvm/_gen/zx_sys_evm_cfg_Reservedcfg.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/_gen/zx_sys_evm_cfg_Reservedcfg.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_39) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/_gen/zx_sys_evm_cfg_Reservedcfg.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm/_gen
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/_gen/zx_sys_evm_cfg_Reservedcfg.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm/_gen -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/iEvm/_gen/zx_sys_evm_cfg_SensAK2cfg.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/_gen/zx_sys_evm_cfg_SensAK2cfg.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_40) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/_gen/zx_sys_evm_cfg_SensAK2cfg.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm/_gen
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/_gen/zx_sys_evm_cfg_SensAK2cfg.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm/_gen -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/iEvm/_gen/zx_sys_evm_cfg_SensLcfg.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/_gen/zx_sys_evm_cfg_SensLcfg.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_41) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/_gen/zx_sys_evm_cfg_SensLcfg.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm/_gen
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/_gen/zx_sys_evm_cfg_SensLcfg.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm/_gen -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/iEvm/_gen/zx_sys_evm_cfg_cameracfg.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/_gen/zx_sys_evm_cfg_cameracfg.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_42) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/_gen/zx_sys_evm_cfg_cameracfg.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm/_gen
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/_gen/zx_sys_evm_cfg_cameracfg.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm/_gen -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/iEvm/evm_cfg_FidCfg.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/evm_cfg_FidCfg.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_43) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/evm_cfg_FidCfg.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/evm_cfg_FidCfg.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/iEvm/zx_sys_evm_cfg_fimcfg.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/zx_sys_evm_cfg_fimcfg.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_44) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/zx_sys_evm_cfg_fimcfg.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/zx_sys_evm_cfg_fimcfg.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/iEvm/zx_sys_evm_events.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/zx_sys_evm_events.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_45) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/zx_sys_evm_events.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/zx_sys_evm_events.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/iEvm/zx_sys_evm_interfaces.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/zx_sys_evm_interfaces.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_46) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/zx_sys_evm_interfaces.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/zx_sys_evm_interfaces.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/iEvm/zx_sys_evm_ipc_cfg_interface.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/zx_sys_evm_ipc_cfg_interface.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_47) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/zx_sys_evm_ipc_cfg_interface.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iEvm/zx_sys_evm_ipc_cfg_interface.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iEvm -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/ipc/imuInfo.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/imuInfo.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_48) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/imuInfo.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/imuInfo.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/ipc/ipcCommunicateData.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipcCommunicateData.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_49) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipcCommunicateData.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipcCommunicateData.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/ipc/ipc_calib_interface.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_calib_interface.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_50) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_calib_interface.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_calib_interface.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/ipc/ipc_cfg_interface.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_cfg_interface.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_51) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_cfg_interface.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_cfg_interface.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/ipc/ipc_proto/ipcAdccFunctionProto.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_proto/ipcAdccFunctionProto.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_52) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_proto/ipcAdccFunctionProto.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc/ipc_proto
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_proto/ipcAdccFunctionProto.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc/ipc_proto -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/ipc/ipc_proto/ipcAdccLaneLinesProto.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_proto/ipcAdccLaneLinesProto.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_53) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_proto/ipcAdccLaneLinesProto.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc/ipc_proto
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_proto/ipcAdccLaneLinesProto.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc/ipc_proto -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/ipc/ipc_proto/ipcAdccNaviPathsProto.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_proto/ipcAdccNaviPathsProto.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_54) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_proto/ipcAdccNaviPathsProto.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc/ipc_proto
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_proto/ipcAdccNaviPathsProto.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc/ipc_proto -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/ipc/ipc_proto/ipcAdccObjectsProto.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_proto/ipcAdccObjectsProto.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_55) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_proto/ipcAdccObjectsProto.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc/ipc_proto
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_proto/ipcAdccObjectsProto.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc/ipc_proto -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/ipc/ipc_proto/ipcAdccObstaclesProto.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_proto/ipcAdccObstaclesProto.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_56) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_proto/ipcAdccObstaclesProto.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc/ipc_proto
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_proto/ipcAdccObstaclesProto.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc/ipc_proto -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/ipc/ipc_proto/ipcAdccSettingProto.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_proto/ipcAdccSettingProto.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_57) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_proto/ipcAdccSettingProto.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc/ipc_proto
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_proto/ipcAdccSettingProto.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc/ipc_proto -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/ipc/ipc_proto/ipcPadHeaderProto.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_proto/ipcPadHeaderProto.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_58) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_proto/ipcPadHeaderProto.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc/ipc_proto
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ipc_proto/ipcPadHeaderProto.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc/ipc_proto -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/ipc/radarObject.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/radarObject.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_59) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/radarObject.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/radarObject.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/ipc/ussPointInfo.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ussPointInfo.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_60) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ussPointInfo.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/ussPointInfo.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/ipc/valInPilotChassis.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/valInPilotChassis.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_61) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/valInPilotChassis.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/valInPilotChassis.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/ipc/valInPilotVehicle.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/valInPilotVehicle.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_62) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/valInPilotVehicle.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/valInPilotVehicle.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/ipc/valInoutputcpj.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/valInoutputcpj.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_63) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/valInoutputcpj.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/valInoutputcpj.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/ipc/valInoutputpf.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/valInoutputpf.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_64) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/valInoutputpf.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/valInoutputpf.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/ipc/vhmAbst2d.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/vhmAbst2d.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_65) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/vhmAbst2d.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/ipc/vhmAbst2d.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/ipc -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/iuni/iuniHmiOutput.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iuni/iuniHmiOutput.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_66) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iuni/iuniHmiOutput.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iuni
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/iuni/iuniHmiOutput.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/iuni -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/localization/odometry.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/localization/odometry.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_67) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/localization/odometry.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/localization
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/localization/odometry.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/localization -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/map/app_status.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/app_status.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_68) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/app_status.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/map
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/app_status.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/map -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/map/local_map_graph.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/local_map_graph.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_69) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/local_map_graph.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/map
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/local_map_graph.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/map -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/map/local_map_graph_boundary.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/local_map_graph_boundary.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_70) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/local_map_graph_boundary.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/map
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/local_map_graph_boundary.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/map -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/map/local_map_graph_common.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/local_map_graph_common.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_71) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/local_map_graph_common.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/map
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/local_map_graph_common.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/map -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/map/local_map_graph_extension.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/local_map_graph_extension.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_72) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/local_map_graph_extension.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/map
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/local_map_graph_extension.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/map -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/map/local_map_graph_lane.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/local_map_graph_lane.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_73) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/local_map_graph_lane.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/map
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/local_map_graph_lane.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/map -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/map/local_map_graph_link.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/local_map_graph_link.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_74) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/local_map_graph_link.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/map
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/local_map_graph_link.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/map -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/map/local_map_graph_objects.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/local_map_graph_objects.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_75) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/local_map_graph_objects.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/map
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/map/local_map_graph_objects.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/map -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/navigation/navigation.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/navigation/navigation.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_76) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/navigation/navigation.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/navigation
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/navigation/navigation.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/navigation -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/navigation/route_guidance.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/navigation/route_guidance.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_77) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/navigation/route_guidance.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/navigation
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/navigation/route_guidance.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/navigation -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/numerics/matrix.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/numerics/matrix.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_78) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/numerics/matrix.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/numerics
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/numerics/matrix.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/numerics -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/numerics/vector.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/numerics/vector.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_79) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/numerics/vector.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/numerics
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/numerics/vector.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/numerics -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/park_dr/vhm_abst_output.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/park_dr/vhm_abst_output.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_80) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/park_dr/vhm_abst_output.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/park_dr
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/park_dr/vhm_abst_output.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/park_dr -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/parking/parkapp/parkAciOutput.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/parkapp/parkAciOutput.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_81) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/parkapp/parkAciOutput.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/parking/parkapp
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/parkapp/parkAciOutput.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/parking/parkapp -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/parking/parkapp/parkBaseType.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/parkapp/parkBaseType.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_82) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/parkapp/parkBaseType.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/parking/parkapp
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/parkapp/parkBaseType.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/parking/parkapp -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/parking/parkapp/parkControlOutput.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/parkapp/parkControlOutput.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_83) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/parkapp/parkControlOutput.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/parking/parkapp
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/parkapp/parkControlOutput.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/parking/parkapp -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/parking/parkapp/parkDbgOutput.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/parkapp/parkDbgOutput.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_84) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/parkapp/parkDbgOutput.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/parking/parkapp
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/parkapp/parkDbgOutput.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/parking/parkapp -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/parking/parkapp/parkHmiOutput.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/parkapp/parkHmiOutput.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_85) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/parkapp/parkHmiOutput.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/parking/parkapp
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/parkapp/parkHmiOutput.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/parking/parkapp -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/parking/parkapp/parkMrgrOutput.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/parkapp/parkMrgrOutput.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_86) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/parkapp/parkMrgrOutput.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/parking/parkapp
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/parkapp/parkMrgrOutput.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/parking/parkapp -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/parking/parkcontrol/ParkCtrlCmd.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/parkcontrol/ParkCtrlCmd.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_87) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/parkcontrol/ParkCtrlCmd.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/parking/parkcontrol
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/parkcontrol/ParkCtrlCmd.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/parking/parkcontrol -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/parking/planning/path.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/planning/path.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_88) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/planning/path.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/parking/planning
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/parking/planning/path.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/parking/planning -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/perception/calibration/calibDcomReq.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/calibration/calibDcomReq.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_89) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/calibration/calibDcomReq.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/calibration
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/calibration/calibDcomReq.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/calibration -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/perception/calibration/calibDcomResult.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/calibration/calibDcomResult.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_90) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/calibration/calibDcomResult.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/calibration
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/calibration/calibDcomResult.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/calibration -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/perception/calibration/calibOfPoint.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/calibration/calibOfPoint.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_91) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/calibration/calibOfPoint.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/calibration
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/calibration/calibOfPoint.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/calibration -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/perception/calibration/calib_info.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/calibration/calib_info.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_92) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/calibration/calib_info.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/calibration
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/calibration/calib_info.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/calibration -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/perception/calibration/calibration.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/calibration/calibration.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_93) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/calibration/calibration.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/calibration
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/calibration/calibration.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/calibration -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/perception/common/common.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/common.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_94) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/common.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/common
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/common.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/common -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/perception/common/ihbc.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/ihbc.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_95) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/ihbc.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/common
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/ihbc.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/common -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/perception/common/laneline3d.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/laneline3d.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_96) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/laneline3d.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/common
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/laneline3d.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/common -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/perception/common/object3d.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/object3d.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_97) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/object3d.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/common
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/object3d.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/common -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/perception/common/occupancy3d.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/occupancy3d.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_98) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/occupancy3d.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/common
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/occupancy3d.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/common -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/perception/common/parking_slot3d.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/parking_slot3d.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_99) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/parking_slot3d.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/common
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/parking_slot3d.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/common -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/perception/common/scene.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/scene.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_100) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/scene.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/common
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/scene.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/common -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/perception/common/traffic_light.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/traffic_light.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_101) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/traffic_light.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/common
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/traffic_light.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/common -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/perception/common/traffic_sign.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/traffic_sign.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_102) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/traffic_sign.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/common
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/common/traffic_sign.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/common -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/perception/inference/dnn_base.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/inference/dnn_base.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_103) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/inference/dnn_base.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/inference
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/inference/dnn_base.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/inference -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/perception/inference/dnn_op.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/inference/dnn_op.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_104) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/inference/dnn_op.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/inference
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/inference/dnn_op.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/inference -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/perception/inference/inference.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/inference/inference.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_105) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/inference/inference.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/inference
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/inference/inference.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/inference -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/perception/perception.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/perception.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_106) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/perception.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/perception.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/perception/preprocess/cam_capture/cam_capture_common.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/preprocess/cam_capture/cam_capture_common.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_107) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/preprocess/cam_capture/cam_capture_common.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/preprocess/cam_capture
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/preprocess/cam_capture/cam_capture_common.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/preprocess/cam_capture -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/perception/preprocess/cam_capture/cam_capture_data_type.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/preprocess/cam_capture/cam_capture_data_type.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_108) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/preprocess/cam_capture/cam_capture_data_type.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/preprocess/cam_capture
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/preprocess/cam_capture/cam_capture_data_type.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/preprocess/cam_capture -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/perception/preprocess/preprocess.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/preprocess/preprocess.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_109) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/preprocess/preprocess.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/preprocess
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/perception/preprocess/preprocess.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/perception/preprocess -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/predicton/prediction.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/predicton/prediction.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_110) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/predicton/prediction.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/predicton
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/predicton/prediction.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/predicton -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/sensors/GnssIns.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/sensors/GnssIns.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_111) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/sensors/GnssIns.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/sensors
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/sensors/GnssIns.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/sensors -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/sensors/imuInfo.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/sensors/imuInfo.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_112) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/sensors/imuInfo.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/sensors
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/sensors/imuInfo.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/sensors -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/sensors/lidar.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/sensors/lidar.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_113) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/sensors/lidar.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/sensors
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/sensors/lidar.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/sensors -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/sync/sync_message.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/sync/sync_message.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_114) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/sync/sync_message.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/sync
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/sync/sync_message.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/sync -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/uss_perception/uss_data_backflow.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/uss_perception/uss_data_backflow.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_115) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/uss_perception/uss_data_backflow.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/uss_perception
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/uss_perception/uss_data_backflow.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/uss_perception -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/uss_perception/uss_data_output.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/uss_perception/uss_data_output.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_116) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/uss_perception/uss_data_output.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/uss_perception
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/uss_perception/uss_data_output.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/uss_perception -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

include/zos_msg/uss_perception/uss_hardware_input.hpp: /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/uss_perception/uss_hardware_input.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=$(CMAKE_PROGRESS_117) "Generating /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/uss_perception/uss_hardware_input.idl"
	/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E make_directory /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/uss_perception
	/home/<USER>/.local/bin/zosidl --language c++11 --idl /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message/zos_msg/uss_perception/uss_hardware_input.idl -o /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/include/zos_msg/uss_perception -I /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/message

zos_msg_zosidl_gen: CMakeFiles/zos_msg_zosidl_gen
zos_msg_zosidl_gen: include/zos_msg/ccw/codingmgr_var_output.hpp
zos_msg_zosidl_gen: include/zos_msg/common/common.hpp
zos_msg_zosidl_gen: include/zos_msg/common/enumDemo.hpp
zos_msg_zosidl_gen: include/zos_msg/common/timestamp.hpp
zos_msg_zosidl_gen: include/zos_msg/dalo/image/gdc_image.hpp
zos_msg_zosidl_gen: include/zos_msg/dalo/system_monitor/system_monitor.hpp
zos_msg_zosidl_gen: include/zos_msg/dalo/version/system_version.hpp
zos_msg_zosidl_gen: include/zos_msg/driving/driving_activesafety/as_debugVariablesFca.hpp
zos_msg_zosidl_gen: include/zos_msg/driving/driving_activesafety/as_debugVariablesFcaEgoContext.hpp
zos_msg_zosidl_gen: include/zos_msg/driving/driving_activesafety/as_evaluationOutput.hpp
zos_msg_zosidl_gen: include/zos_msg/driving/driving_activesafety/as_fusedObjectInfo.hpp
zos_msg_zosidl_gen: include/zos_msg/driving/driving_activesafety/as_hostVehicleInfo.hpp
zos_msg_zosidl_gen: include/zos_msg/driving/driving_apps/driving_apps_interfaces.hpp
zos_msg_zosidl_gen: include/zos_msg/driving/driving_apps/driving_automaton_state.hpp
zos_msg_zosidl_gen: include/zos_msg/driving/driving_apps/driving_databack_trigger.hpp
zos_msg_zosidl_gen: include/zos_msg/driving/driving_apps/driving_hmi_command.hpp
zos_msg_zosidl_gen: include/zos_msg/driving/driving_assist/driving_assist_func.hpp
zos_msg_zosidl_gen: include/zos_msg/driving/driving_assist/driving_assist_func_evi.hpp
zos_msg_zosidl_gen: include/zos_msg/driving/driving_assist/driving_assist_func_lda.hpp
zos_msg_zosidl_gen: include/zos_msg/driving/driving_assist/driving_assist_func_pla.hpp
zos_msg_zosidl_gen: include/zos_msg/driving/driving_assist/driving_assist_func_sem.hpp
zos_msg_zosidl_gen: include/zos_msg/driving/driving_assist/driving_assist_func_tos.hpp
zos_msg_zosidl_gen: include/zos_msg/driving/driving_assist/driving_corner_radar.hpp
zos_msg_zosidl_gen: include/zos_msg/driving/driving_control/DrivingControl.hpp
zos_msg_zosidl_gen: include/zos_msg/driving/driving_control/DrivingLateralControl.hpp
zos_msg_zosidl_gen: include/zos_msg/driving/driving_control/DrivingLongitudinalControl.hpp
zos_msg_zosidl_gen: include/zos_msg/driving/driving_control/DrivingVehicleLongitudinalControl.hpp
zos_msg_zosidl_gen: include/zos_msg/driving/driving_planning/planning_result.hpp
zos_msg_zosidl_gen: include/zos_msg/geometry/box.hpp
zos_msg_zosidl_gen: include/zos_msg/geometry/line.hpp
zos_msg_zosidl_gen: include/zos_msg/geometry/point.hpp
zos_msg_zosidl_gen: include/zos_msg/geometry/pose.hpp
zos_msg_zosidl_gen: include/zos_msg/geometry/rectangle.hpp
zos_msg_zosidl_gen: include/zos_msg/geometry/rot.hpp
zos_msg_zosidl_gen: include/zos_msg/iEvm/_gen/zx_sys_evm_cfg_Ethcfg.hpp
zos_msg_zosidl_gen: include/zos_msg/iEvm/_gen/zx_sys_evm_cfg_HCTcfg.hpp
zos_msg_zosidl_gen: include/zos_msg/iEvm/_gen/zx_sys_evm_cfg_Mcucfg.hpp
zos_msg_zosidl_gen: include/zos_msg/iEvm/_gen/zx_sys_evm_cfg_Reservedcfg.hpp
zos_msg_zosidl_gen: include/zos_msg/iEvm/_gen/zx_sys_evm_cfg_SensAK2cfg.hpp
zos_msg_zosidl_gen: include/zos_msg/iEvm/_gen/zx_sys_evm_cfg_SensLcfg.hpp
zos_msg_zosidl_gen: include/zos_msg/iEvm/_gen/zx_sys_evm_cfg_cameracfg.hpp
zos_msg_zosidl_gen: include/zos_msg/iEvm/evm_cfg_FidCfg.hpp
zos_msg_zosidl_gen: include/zos_msg/iEvm/zx_sys_evm_cfg_fimcfg.hpp
zos_msg_zosidl_gen: include/zos_msg/iEvm/zx_sys_evm_events.hpp
zos_msg_zosidl_gen: include/zos_msg/iEvm/zx_sys_evm_interfaces.hpp
zos_msg_zosidl_gen: include/zos_msg/iEvm/zx_sys_evm_ipc_cfg_interface.hpp
zos_msg_zosidl_gen: include/zos_msg/ipc/imuInfo.hpp
zos_msg_zosidl_gen: include/zos_msg/ipc/ipcCommunicateData.hpp
zos_msg_zosidl_gen: include/zos_msg/ipc/ipc_calib_interface.hpp
zos_msg_zosidl_gen: include/zos_msg/ipc/ipc_cfg_interface.hpp
zos_msg_zosidl_gen: include/zos_msg/ipc/ipc_proto/ipcAdccFunctionProto.hpp
zos_msg_zosidl_gen: include/zos_msg/ipc/ipc_proto/ipcAdccLaneLinesProto.hpp
zos_msg_zosidl_gen: include/zos_msg/ipc/ipc_proto/ipcAdccNaviPathsProto.hpp
zos_msg_zosidl_gen: include/zos_msg/ipc/ipc_proto/ipcAdccObjectsProto.hpp
zos_msg_zosidl_gen: include/zos_msg/ipc/ipc_proto/ipcAdccObstaclesProto.hpp
zos_msg_zosidl_gen: include/zos_msg/ipc/ipc_proto/ipcAdccSettingProto.hpp
zos_msg_zosidl_gen: include/zos_msg/ipc/ipc_proto/ipcPadHeaderProto.hpp
zos_msg_zosidl_gen: include/zos_msg/ipc/radarObject.hpp
zos_msg_zosidl_gen: include/zos_msg/ipc/ussPointInfo.hpp
zos_msg_zosidl_gen: include/zos_msg/ipc/valInPilotChassis.hpp
zos_msg_zosidl_gen: include/zos_msg/ipc/valInPilotVehicle.hpp
zos_msg_zosidl_gen: include/zos_msg/ipc/valInoutputcpj.hpp
zos_msg_zosidl_gen: include/zos_msg/ipc/valInoutputpf.hpp
zos_msg_zosidl_gen: include/zos_msg/ipc/vhmAbst2d.hpp
zos_msg_zosidl_gen: include/zos_msg/iuni/iuniHmiOutput.hpp
zos_msg_zosidl_gen: include/zos_msg/localization/odometry.hpp
zos_msg_zosidl_gen: include/zos_msg/map/app_status.hpp
zos_msg_zosidl_gen: include/zos_msg/map/local_map_graph.hpp
zos_msg_zosidl_gen: include/zos_msg/map/local_map_graph_boundary.hpp
zos_msg_zosidl_gen: include/zos_msg/map/local_map_graph_common.hpp
zos_msg_zosidl_gen: include/zos_msg/map/local_map_graph_extension.hpp
zos_msg_zosidl_gen: include/zos_msg/map/local_map_graph_lane.hpp
zos_msg_zosidl_gen: include/zos_msg/map/local_map_graph_link.hpp
zos_msg_zosidl_gen: include/zos_msg/map/local_map_graph_objects.hpp
zos_msg_zosidl_gen: include/zos_msg/navigation/navigation.hpp
zos_msg_zosidl_gen: include/zos_msg/navigation/route_guidance.hpp
zos_msg_zosidl_gen: include/zos_msg/numerics/matrix.hpp
zos_msg_zosidl_gen: include/zos_msg/numerics/vector.hpp
zos_msg_zosidl_gen: include/zos_msg/park_dr/vhm_abst_output.hpp
zos_msg_zosidl_gen: include/zos_msg/parking/parkapp/parkAciOutput.hpp
zos_msg_zosidl_gen: include/zos_msg/parking/parkapp/parkBaseType.hpp
zos_msg_zosidl_gen: include/zos_msg/parking/parkapp/parkControlOutput.hpp
zos_msg_zosidl_gen: include/zos_msg/parking/parkapp/parkDbgOutput.hpp
zos_msg_zosidl_gen: include/zos_msg/parking/parkapp/parkHmiOutput.hpp
zos_msg_zosidl_gen: include/zos_msg/parking/parkapp/parkMrgrOutput.hpp
zos_msg_zosidl_gen: include/zos_msg/parking/parkcontrol/ParkCtrlCmd.hpp
zos_msg_zosidl_gen: include/zos_msg/parking/planning/path.hpp
zos_msg_zosidl_gen: include/zos_msg/perception/calibration/calibDcomReq.hpp
zos_msg_zosidl_gen: include/zos_msg/perception/calibration/calibDcomResult.hpp
zos_msg_zosidl_gen: include/zos_msg/perception/calibration/calibOfPoint.hpp
zos_msg_zosidl_gen: include/zos_msg/perception/calibration/calib_info.hpp
zos_msg_zosidl_gen: include/zos_msg/perception/calibration/calibration.hpp
zos_msg_zosidl_gen: include/zos_msg/perception/common/common.hpp
zos_msg_zosidl_gen: include/zos_msg/perception/common/ihbc.hpp
zos_msg_zosidl_gen: include/zos_msg/perception/common/laneline3d.hpp
zos_msg_zosidl_gen: include/zos_msg/perception/common/object3d.hpp
zos_msg_zosidl_gen: include/zos_msg/perception/common/occupancy3d.hpp
zos_msg_zosidl_gen: include/zos_msg/perception/common/parking_slot3d.hpp
zos_msg_zosidl_gen: include/zos_msg/perception/common/scene.hpp
zos_msg_zosidl_gen: include/zos_msg/perception/common/traffic_light.hpp
zos_msg_zosidl_gen: include/zos_msg/perception/common/traffic_sign.hpp
zos_msg_zosidl_gen: include/zos_msg/perception/inference/dnn_base.hpp
zos_msg_zosidl_gen: include/zos_msg/perception/inference/dnn_op.hpp
zos_msg_zosidl_gen: include/zos_msg/perception/inference/inference.hpp
zos_msg_zosidl_gen: include/zos_msg/perception/perception.hpp
zos_msg_zosidl_gen: include/zos_msg/perception/preprocess/cam_capture/cam_capture_common.hpp
zos_msg_zosidl_gen: include/zos_msg/perception/preprocess/cam_capture/cam_capture_data_type.hpp
zos_msg_zosidl_gen: include/zos_msg/perception/preprocess/preprocess.hpp
zos_msg_zosidl_gen: include/zos_msg/predicton/prediction.hpp
zos_msg_zosidl_gen: include/zos_msg/sensors/GnssIns.hpp
zos_msg_zosidl_gen: include/zos_msg/sensors/imuInfo.hpp
zos_msg_zosidl_gen: include/zos_msg/sensors/lidar.hpp
zos_msg_zosidl_gen: include/zos_msg/sync/sync_message.hpp
zos_msg_zosidl_gen: include/zos_msg/uss_perception/uss_data_backflow.hpp
zos_msg_zosidl_gen: include/zos_msg/uss_perception/uss_data_output.hpp
zos_msg_zosidl_gen: include/zos_msg/uss_perception/uss_hardware_input.hpp
zos_msg_zosidl_gen: CMakeFiles/zos_msg_zosidl_gen.dir/build.make
.PHONY : zos_msg_zosidl_gen

# Rule to build all files generated by this target.
CMakeFiles/zos_msg_zosidl_gen.dir/build: zos_msg_zosidl_gen
.PHONY : CMakeFiles/zos_msg_zosidl_gen.dir/build

CMakeFiles/zos_msg_zosidl_gen.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/zos_msg_zosidl_gen.dir/cmake_clean.cmake
.PHONY : CMakeFiles/zos_msg_zosidl_gen.dir/clean

CMakeFiles/zos_msg_zosidl_gen.dir/depend:
	cd /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles/zos_msg_zosidl_gen.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/zos_msg_zosidl_gen.dir/depend

