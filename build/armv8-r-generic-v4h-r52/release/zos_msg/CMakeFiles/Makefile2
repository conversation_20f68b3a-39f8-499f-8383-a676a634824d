# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.30

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake

# The command to remove a file.
RM = /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/code/mcu_app_YT_workspace/src/zos_msg

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/zos_msg_zosidl_gen.dir/all
all: pub_if/all
all: utils/all
all: test/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: pub_if/preinstall
preinstall: utils/preinstall
preinstall: test/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/zos_msg_zosidl_gen.dir/clean
clean: pub_if/clean
clean: utils/clean
clean: test/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory pub_if

# Recursive "all" directory target.
pub_if/all:
.PHONY : pub_if/all

# Recursive "preinstall" directory target.
pub_if/preinstall:
.PHONY : pub_if/preinstall

# Recursive "clean" directory target.
pub_if/clean:
.PHONY : pub_if/clean

#=============================================================================
# Directory level rules for directory test

# Recursive "all" directory target.
test/all: test/gtest/all
.PHONY : test/all

# Recursive "preinstall" directory target.
test/preinstall: test/gtest/preinstall
.PHONY : test/preinstall

# Recursive "clean" directory target.
test/clean: test/gtest/clean
.PHONY : test/clean

#=============================================================================
# Directory level rules for directory test/gtest

# Recursive "all" directory target.
test/gtest/all:
.PHONY : test/gtest/all

# Recursive "preinstall" directory target.
test/gtest/preinstall:
.PHONY : test/gtest/preinstall

# Recursive "clean" directory target.
test/gtest/clean:
.PHONY : test/gtest/clean

#=============================================================================
# Directory level rules for directory utils

# Recursive "all" directory target.
utils/all:
.PHONY : utils/all

# Recursive "preinstall" directory target.
utils/preinstall:
.PHONY : utils/preinstall

# Recursive "clean" directory target.
utils/clean:
.PHONY : utils/clean

#=============================================================================
# Target rules for target CMakeFiles/zos_msg_zosidl_gen.dir

# All Build rule for target.
CMakeFiles/zos_msg_zosidl_gen.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/zos_msg_zosidl_gen.dir/build.make CMakeFiles/zos_msg_zosidl_gen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/zos_msg_zosidl_gen.dir/build.make CMakeFiles/zos_msg_zosidl_gen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100 "Built target zos_msg_zosidl_gen"
.PHONY : CMakeFiles/zos_msg_zosidl_gen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/zos_msg_zosidl_gen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles 100
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/zos_msg_zosidl_gen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles 0
.PHONY : CMakeFiles/zos_msg_zosidl_gen.dir/rule

# Convenience name for target.
zos_msg_zosidl_gen: CMakeFiles/zos_msg_zosidl_gen.dir/rule
.PHONY : zos_msg_zosidl_gen

# clean rule for target.
CMakeFiles/zos_msg_zosidl_gen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/zos_msg_zosidl_gen.dir/build.make CMakeFiles/zos_msg_zosidl_gen.dir/clean
.PHONY : CMakeFiles/zos_msg_zosidl_gen.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

